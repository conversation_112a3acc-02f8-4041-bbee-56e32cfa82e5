{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 8.32a7.43 7.43 0 0 1 0 7.36\",\n  key: \"9iaqei\"\n}], [\"path\", {\n  d: \"M9.46 6.21a11.76 11.76 0 0 1 0 11.58\",\n  key: \"1yha7l\"\n}], [\"path\", {\n  d: \"M12.91 4.1a15.91 15.91 0 0 1 .01 15.8\",\n  key: \"4iu2gk\"\n}], [\"path\", {\n  d: \"M16.37 2a20.16 20.16 0 0 1 0 20\",\n  key: \"sap9u2\"\n}]];\nconst Nfc = createLucideIcon(\"nfc\", __iconNode);\nexport { __iconNode, Nfc as default };\n//# sourceMappingURL=nfc.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}