{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 11a8 8 0 0 0-8-8\",\n  key: \"1lxwo5\"\n}], [\"path\", {\n  d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\",\n  key: \"1dv2y5\"\n}]];\nconst SquareRoundCorner = createLucideIcon(\"square-round-corner\", __iconNode);\nexport { __iconNode, SquareRoundCorner as default };\n//# sourceMappingURL=square-round-corner.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}