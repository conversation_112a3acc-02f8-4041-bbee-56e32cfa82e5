{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z\",\n  key: \"12oyoe\"\n}], [\"path\", {\n  d: \"M21 16v2a4 4 0 0 1-4 4h-5\",\n  key: \"1x7m43\"\n}]];\nconst Headset = createLucideIcon(\"headset\", __iconNode);\nexport { __iconNode, Headset as default };\n//# sourceMappingURL=headset.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}