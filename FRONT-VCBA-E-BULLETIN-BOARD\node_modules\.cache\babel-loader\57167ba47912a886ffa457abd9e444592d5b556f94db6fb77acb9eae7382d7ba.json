{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 7V5a2 2 0 0 1 2-2h2\",\n  key: \"aa7l1z\"\n}], [\"path\", {\n  d: \"M17 3h2a2 2 0 0 1 2 2v2\",\n  key: \"4qcy5o\"\n}], [\"path\", {\n  d: \"M21 17v2a2 2 0 0 1-2 2h-2\",\n  key: \"6vwrx8\"\n}], [\"path\", {\n  d: \"M7 21H5a2 2 0 0 1-2-2v-2\",\n  key: \"ioqczr\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"8\",\n  x: \"7\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"vys8me\"\n}]];\nconst Fullscreen = createLucideIcon(\"fullscreen\", __iconNode);\nexport { __iconNode, Fullscreen as default };\n//# sourceMappingURL=fullscreen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}