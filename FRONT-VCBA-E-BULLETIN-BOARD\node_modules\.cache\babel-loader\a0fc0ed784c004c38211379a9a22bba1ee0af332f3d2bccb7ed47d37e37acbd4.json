{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{useHolidayTypes}from'../../../hooks/useCalendar';import{useCalendarImageUpload}from'../../../hooks/useCalendarImageUpload';import CalendarImageUpload from'../CalendarImageUpload';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CalendarEventModal=_ref=>{let{isOpen,onClose,onSave,event,selectedDate,loading=false}=_ref;const{holidayTypes}=useHolidayTypes();const[formData,setFormData]=useState({title:'',description:'',event_date:'',end_date:'',holiday_type_id:'',is_recurring:false,recurrence_pattern:'',is_active:true,is_published:false});const[errors,setErrors]=useState({});const[selectedImages,setSelectedImages]=useState([]);const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');// Image upload hook\nconst{existingImages,loading:imageLoading,error:imageError,uploadImages,setPrimaryImage,refreshImages,clearError:clearImageError,// Pending operations\npendingDeletes,markForDeletion,unmarkForDeletion,applyPendingDeletes,clearPendingDeletes,// Clear all image state\nclearAllImageState}=useCalendarImageUpload({calendarId:event===null||event===void 0?void 0:event.calendar_id,onSuccess:message=>setSuccessMessage(message),onError:error=>setErrorMessage(error)});// Initialize form data when event or selectedDate changes\nuseEffect(()=>{if(event){var _event$holiday_type_i;// Helper function to extract date part without timezone issues\nconst extractDatePart=dateString=>{if(!dateString)return'';// If it's already in YYYY-MM-DD format, return as-is\nif(dateString.match(/^\\d{4}-\\d{2}-\\d{2}$/)){return dateString;}// Otherwise, extract the date part from ISO string\nreturn dateString.split('T')[0];};setFormData({title:event.title||'',description:event.description||'',event_date:extractDatePart(event.event_date),end_date:event.end_date?extractDatePart(event.end_date):'',holiday_type_id:((_event$holiday_type_i=event.holiday_type_id)===null||_event$holiday_type_i===void 0?void 0:_event$holiday_type_i.toString())||'',is_recurring:event.is_recurring||false,recurrence_pattern:event.recurrence_pattern||'',is_active:event.is_active!==false,is_published:event.is_published||false});}else{// Format selected date properly to avoid timezone issues\nconst formatLocalDate=date=>{const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day);};const dateString=selectedDate?formatLocalDate(selectedDate):'';setFormData({title:'',description:'',event_date:dateString,end_date:'',holiday_type_id:'',is_recurring:false,recurrence_pattern:'',is_active:true,is_published:false});// Clear image state for new events\nclearAllImageState();setSelectedImages([]);}setErrors({});setSuccessMessage('');setErrorMessage('');clearImageError();},[event,selectedDate,clearImageError,clearAllImageState]);// Separate effect to handle modal open/close state\nuseEffect(()=>{if(!isOpen){// Clear pending deletes when modal closes\nclearPendingDeletes();}},[isOpen,clearPendingDeletes]);// Enhanced close handler that ensures everything is cleared\nconst handleClose=useCallback(()=>{console.log('🚪 Closing calendar modal - clearing all data');// Clear pending deletes before closing\nclearPendingDeletes();// Clear other state\nsetErrors({});setSuccessMessage('');setErrorMessage('');clearImageError();// Call parent's onClose\nonClose();},[clearPendingDeletes,clearImageError,onClose]);// Handle Escape key to close modal and clear data\nuseEffect(()=>{const handleEscapeKey=event=>{if(event.key==='Escape'&&isOpen){handleClose();}};if(isOpen){document.addEventListener('keydown',handleEscapeKey);return()=>document.removeEventListener('keydown',handleEscapeKey);}},[isOpen,handleClose]);// Clear messages after 5 seconds\nuseEffect(()=>{if(successMessage||errorMessage){const timer=setTimeout(()=>{setSuccessMessage('');setErrorMessage('');},5000);return()=>clearTimeout(timer);}},[successMessage,errorMessage]);const validateForm=()=>{const newErrors={};if(!formData.title.trim()){newErrors.title='Title is required';}else if(formData.title.length>255){newErrors.title='Title must be less than 255 characters';}if(!formData.event_date){newErrors.event_date='Event date is required';}if(!formData.holiday_type_id){newErrors.holiday_type_id='Holiday type is required';}if(formData.end_date&&formData.event_date&&formData.end_date<formData.event_date){newErrors.end_date='End date cannot be before start date';}if(formData.is_recurring&&!formData.recurrence_pattern){newErrors.recurrence_pattern='Recurrence pattern is required for recurring events';}setErrors(newErrors);return Object.keys(newErrors).length===0;};const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{// Format dates to ensure they're sent as local dates without timezone conversion\nconst formatDateForSubmission=dateString=>{if(!dateString)return undefined;// Simply return the date string as-is if it's already in YYYY-MM-DD format\n// This prevents any timezone conversion issues\nreturn dateString;};const submitData=_objectSpread(_objectSpread({},formData),{},{holiday_type_id:parseInt(formData.holiday_type_id),event_date:formatDateForSubmission(formData.event_date),end_date:formData.end_date?formatDateForSubmission(formData.end_date):undefined,recurrence_pattern:formData.is_recurring&&formData.recurrence_pattern?formData.recurrence_pattern:undefined});// Create completion callback for additional operations\nconst onComplete=async()=>{// If we're editing and have images, upload them separately\nif(event&&selectedImages.length>0){try{await uploadImages(selectedImages);setSelectedImages([]);// Clear selected images after upload\n}catch(uploadError){console.error('Error uploading additional images:',uploadError);// Don't throw here as the main event was saved successfully\n}}// Refresh images to show updates immediately\nif(event!==null&&event!==void 0&&event.calendar_id){await refreshImages();}// Clear pending deletes after successful update\nclearPendingDeletes();};console.log('Submitting calendar event:',submitData);// Debug log\nconsole.log('📋 Pending deletes before save:',pendingDeletes);await onSave(submitData,pendingDeletes.length>0?applyPendingDeletes:undefined,onComplete);handleClose();}catch(error){console.error('Error saving event:',error);}};const handleInputChange=e=>{const{name,value,type}=e.target;if(type==='checkbox'){const checked=e.target.checked;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:checked}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));}// Clear error when user starts typing\nif(errors[name]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',padding:'2rem',width:'100%',maxWidth:'500px',maxHeight:'90vh',overflow:'auto',boxShadow:'0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{fontSize:'1.5rem',fontWeight:'700',color:'#2d5016',margin:0},children:event?'Edit Event':'Create New Event'}),/*#__PURE__*/_jsx(\"button\",{onClick:handleClose,style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Title *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"title\",value:formData.title,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.title?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease'},placeholder:\"Enter event title\"}),errors.title&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.title})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"description\",value:formData.description,onChange:handleInputChange,rows:3,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease',resize:'vertical'},placeholder:\"Enter event description (optional)\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Start Date *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",name:\"event_date\",value:formData.event_date,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.event_date?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none'}}),errors.event_date&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.event_date})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"End Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",name:\"end_date\",value:formData.end_date,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.end_date?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none'}}),errors.end_date&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.end_date})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Event Type *\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"holiday_type_id\",value:formData.holiday_type_id,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.holiday_type_id?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Event Type\"}),holidayTypes.map(type=>/*#__PURE__*/_jsx(\"option\",{value:type.type_id,children:type.type_name},type.type_id))]}),errors.holiday_type_id&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.holiday_type_id})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_recurring\",checked:formData.is_recurring,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Recurring Event\"]}),formData.is_recurring&&/*#__PURE__*/_jsxs(\"select\",{name:\"recurrence_pattern\",value:formData.recurrence_pattern,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.recurrence_pattern?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Recurrence Pattern\"}),/*#__PURE__*/_jsx(\"option\",{value:\"weekly\",children:\"Weekly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"monthly\",children:\"Monthly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"yearly\",children:\"Yearly\"})]}),errors.recurrence_pattern&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.recurrence_pattern})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_active\",checked:formData.is_active,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Active Event\"]})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#374151',margin:0},children:\"Event Images\"}),pendingDeletes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem 0.75rem',marginTop:'0.5rem',backgroundColor:'#fef2f2',borderRadius:'6px',border:'1px solid #fecaca',color:'#dc2626',fontSize:'0.875rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A0\\uFE0F\"}),pendingDeletes.length,\" image\",pendingDeletes.length>1?'s':'',\" will be deleted\"]})]}),/*#__PURE__*/_jsx(CalendarImageUpload,{onImagesChange:setSelectedImages,existingImages:existingImages,onSetPrimary:setPrimaryImage,maxImages:10,disabled:imageLoading,pendingDeletes:pendingDeletes,onMarkForDeletion:markForDeletion,onUnmarkForDeletion:unmarkForDeletion}),imageError&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc2626',fontSize:'0.875rem',marginTop:'0.5rem'},children:imageError})]}),successMessage&&/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'6px',padding:'0.75rem',marginBottom:'1rem',color:'#15803d',fontSize:'0.875rem'},children:successMessage}),errorMessage&&/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'6px',padding:'0.75rem',marginBottom:'1rem',color:'#dc2626',fontSize:'0.875rem'},children:errorMessage}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'flex-end',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleClose,style:{padding:'0.75rem 1.5rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem'},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,style:{padding:'0.75rem 1.5rem',background:loading?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:loading?'not-allowed':'pointer',fontWeight:'600',fontSize:'0.875rem'},children:loading?'Saving...':event?'Update':'Create'})]})]})]})});};export default CalendarEventModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}