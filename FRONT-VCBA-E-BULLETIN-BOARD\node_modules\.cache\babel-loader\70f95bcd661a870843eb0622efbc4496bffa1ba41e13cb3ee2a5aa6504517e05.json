{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"path\", {\n  d: \"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1\",\n  key: \"9jr5yi\"\n}]];\nconst Heading2 = createLucideIcon(\"heading-2\", __iconNode);\nexport { __iconNode, Heading2 as default };\n//# sourceMappingURL=heading-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}