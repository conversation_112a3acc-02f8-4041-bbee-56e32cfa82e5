{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"11.9\",\n  r: \"2\",\n  key: \"e8h31w\"\n}], [\"path\", {\n  d: \"M6.7 3.4c-.9 2.5 0 5.2 2.2 6.7C6.5 9 3.7 9.6 2 11.6\",\n  key: \"17bolr\"\n}], [\"path\", {\n  d: \"m8.9 10.1 1.4.8\",\n  key: \"15ezny\"\n}], [\"path\", {\n  d: \"M17.3 3.4c.9 2.5 0 5.2-2.2 6.7 2.4-1.2 5.2-.6 6.9 1.5\",\n  key: \"wtwa5u\"\n}], [\"path\", {\n  d: \"m15.1 10.1-1.4.8\",\n  key: \"1r0b28\"\n}], [\"path\", {\n  d: \"M16.7 20.8c-2.6-.4-4.6-2.6-4.7-5.3-.2 2.6-2.1 4.8-4.7 5.2\",\n  key: \"m7qszh\"\n}], [\"path\", {\n  d: \"M12 13.9v1.6\",\n  key: \"zfyyim\"\n}], [\"path\", {\n  d: \"M13.5 5.4c-1-.2-2-.2-3 0\",\n  key: \"1bi9q0\"\n}], [\"path\", {\n  d: \"M17 16.4c.7-.7 1.2-1.6 1.5-2.5\",\n  key: \"1rhjqw\"\n}], [\"path\", {\n  d: \"M5.5 13.9c.3.9.8 1.8 1.5 2.5\",\n  key: \"8gsud3\"\n}]];\nconst Biohazard = createLucideIcon(\"biohazard\", __iconNode);\nexport { __iconNode, Biohazard as default };\n//# sourceMappingURL=biohazard.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}