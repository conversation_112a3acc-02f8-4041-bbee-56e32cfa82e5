{"version": 3, "file": "VisitorBase.js", "sourceRoot": "", "sources": ["../../src/referencer/VisitorBase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,kEAA2E;AAO3E,SAAS,QAAQ,CAAC,GAAY;IAC5B,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,CAAC;AAChD,CAAC;AACD,SAAS,MAAM,CAAC,IAAa;IAC3B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AACzD,CAAC;AAMD,MAAe,WAAW;IAGxB,YAAY,OAAuB;;QAF1B,gDAA+B;QAC/B,iEAA4C;QAEnD,uBAAA,IAAI,iCAAqB,MAAA,OAAO,CAAC,gBAAgB,mCAAI,0BAAW,MAAA,CAAC;QACjE,uBAAA,IAAI,kDACF,MAAA,OAAO,CAAC,iCAAiC,mCAAI,KAAK,MAAA,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,IAA0B,EAC1B,aAA0B,EAAE;;QAE5B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;YACrC,OAAO;SACR;QAED,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAgB,CAAC;QACtE,MAAM,QAAQ,GAAG,MAAA,uBAAA,IAAI,qCAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,mCAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;YAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACpB,SAAS;aACV;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAA0B,CAAY,CAAC;YAC1D,IAAI,CAAC,KAAK,EAAE;gBACV,SAAS;aACV;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;oBAC5B,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE;wBACpB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;qBACtB;iBACF;aACF;iBAAM,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;aACnB;SACF;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAsC;QAC1C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;YACrC,OAAO;SACR;QAED,MAAM,OAAO,GAAI,IAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,uBAAA,IAAI,sDAAmC,EAAE;gBAC5C,OAAO;aACR;SACF;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAEQ,kCAAW"}