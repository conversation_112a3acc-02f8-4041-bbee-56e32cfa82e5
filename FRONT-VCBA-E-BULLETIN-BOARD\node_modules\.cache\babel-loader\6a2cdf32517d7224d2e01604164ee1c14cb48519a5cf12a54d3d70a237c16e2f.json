{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 9 5-5 5 5\",\n  key: \"9ctzwi\"\n}], [\"path\", {\n  d: \"M4 20h7a4 4 0 0 0 4-4V4\",\n  key: \"1plgdj\"\n}]];\nconst CornerRightUp = createLucideIcon(\"corner-right-up\", __iconNode);\nexport { __iconNode, CornerRightUp as default };\n//# sourceMappingURL=corner-right-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}