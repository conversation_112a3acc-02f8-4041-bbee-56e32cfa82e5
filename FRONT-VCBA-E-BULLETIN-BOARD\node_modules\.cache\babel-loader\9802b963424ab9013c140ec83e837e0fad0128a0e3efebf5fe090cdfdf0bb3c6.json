{"ast": null, "code": "/**\n * Professional Notification Navigation Service\n * Handles context-aware redirection based on notification metadata\n * Following Google/OpenAI software engineering practices\n */class NotificationNavigationService{constructor(){this.navigate=null;this.currentUserRole=null;}/**\n   * Initialize the navigation service with React Router navigate function\n   */initialize(navigate,userRole){this.navigate=navigate;this.currentUserRole=userRole;}/**\n   * Handle notification click with context-aware redirection\n   */async handleNotificationClick(notification){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};if(!this.navigate||!this.currentUserRole){console.error('NotificationNavigationService not initialized');return false;}try{// Parse context metadata\nconst context=this.parseNotificationContext(notification);// Mark as read if requested\nif(options.markAsRead!==false){await this.markNotificationAsRead(notification.notification_id);}// Determine target route\nconst targetRoute=this.buildTargetRoute(context,this.currentUserRole);if(!targetRoute){console.warn('Unable to determine target route for notification:',notification);return this.handleFallback(options.fallbackRoute);}// Navigate to target with state for highlighting and scrolling\nconst navigationState={fromNotification:true,notificationId:notification.notification_id,highlightTarget:options.highlightTarget!==false,scrollTo:context.scroll_to,scrollBehavior:options.scrollBehavior||'smooth'};this.navigate(targetRoute,{state:navigationState});console.log('🎯 Navigated to:',targetRoute,'with state:',navigationState);return true;}catch(error){console.error('Error handling notification click:',error);return this.handleFallback(options.fallbackRoute);}}/**\n   * Parse notification context metadata\n   */parseNotificationContext(notification){try{// Handle both string and object context_metadata\nlet contextData;if(typeof notification.context_metadata==='string'){contextData=JSON.parse(notification.context_metadata);}else if(typeof notification.context_metadata==='object'&&notification.context_metadata!==null){contextData=notification.context_metadata;}else{// Fallback: infer context from notification type and related IDs\ncontextData=this.inferContextFromNotification(notification);}return contextData;}catch(error){console.warn('Failed to parse notification context, using fallback:',error);return this.inferContextFromNotification(notification);}}/**\n   * Infer context from notification data when metadata is not available\n   */inferContextFromNotification(notification){const{type_name,related_announcement_id,related_comment_id}=notification;// Comment-related notifications\nif(type_name&&['comment_reply','comment_reaction','comment_flagged'].includes(type_name)){return{type:'comment',target_id:related_comment_id||null,announcement_id:related_announcement_id,scroll_to:related_comment_id?\"comment-\".concat(related_comment_id):undefined};}// Announcement-related notifications\nif(type_name&&['new_announcement','alert_announcement','announcement_reaction','pinned_post'].includes(type_name)){return{type:'announcement',target_id:related_announcement_id||null,announcement_id:related_announcement_id,scroll_to:related_announcement_id?\"announcement-\".concat(related_announcement_id):undefined};}// Calendar-related notifications\nif(type_name==='calendar_event'){return{type:'calendar',target_id:related_announcement_id||null,// Calendar events might use announcement_id field\nscroll_to:related_announcement_id?\"event-\".concat(related_announcement_id):undefined};}// General fallback\nreturn{type:'general',target_id:null};}/**\n   * Build target route based on context and user role\n   */buildTargetRoute(context,userRole){const baseRoute=userRole==='admin'?'/admin':'/student';switch(context.type){case'comment':if(context.announcement_id){// Navigate to newsfeed with announcement focus\nreturn\"\".concat(baseRoute,\"/newsfeed?focus=announcement&id=\").concat(context.announcement_id,\"&comment=\").concat(context.target_id);}return\"\".concat(baseRoute,\"/newsfeed\");case'announcement':if(context.target_id){// Navigate to newsfeed with announcement focus\nreturn\"\".concat(baseRoute,\"/newsfeed?focus=announcement&id=\").concat(context.target_id);}return\"\".concat(baseRoute,\"/newsfeed\");case'calendar':if(context.target_id){// Navigate to calendar with event focus\nreturn\"\".concat(baseRoute,\"/calendar?focus=event&id=\").concat(context.target_id);}return\"\".concat(baseRoute,\"/calendar\");case'general':default:// Navigate to dashboard or newsfeed as fallback\nreturn\"\".concat(baseRoute,\"/newsfeed\");}}/**\n   * Mark notification as read\n   */async markNotificationAsRead(notificationId){try{// Import notification service dynamically to avoid circular dependencies\nconst{studentNotificationService,adminNotificationService}=await import('./notificationService');const service=this.currentUserRole==='admin'?adminNotificationService:studentNotificationService;await service.markAsRead(notificationId);console.log('✅ Marked notification as read:',notificationId);}catch(error){console.error('Failed to mark notification as read:',error);// Don't throw - this shouldn't prevent navigation\n}}/**\n   * Handle fallback navigation when target route cannot be determined\n   */handleFallback(fallbackRoute){if(!this.navigate||!this.currentUserRole)return false;const defaultFallback=this.currentUserRole==='admin'?'/admin/dashboard':'/student/dashboard';const targetRoute=fallbackRoute||defaultFallback;this.navigate(targetRoute);console.log('🔄 Fallback navigation to:',targetRoute);return true;}/**\n   * Validate if target content still exists (for error handling)\n   */async validateTarget(context){try{// This would typically make API calls to verify content exists\n// For now, we'll implement basic validation\nif(context.type==='announcement'&&context.target_id){// Could check if announcement still exists and is accessible\nreturn true;}if(context.type==='comment'&&context.target_id){// Could check if comment still exists and is not deleted\nreturn true;}if(context.type==='calendar'&&context.target_id){// Could check if calendar event still exists\nreturn true;}return true;}catch(error){console.error('Error validating target:',error);return false;}}/**\n   * Get user-friendly error message for invalid targets\n   */getTargetErrorMessage(context){switch(context.type){case'announcement':return'This announcement is no longer available or has been removed.';case'comment':return'This comment is no longer available or has been removed.';case'calendar':return'This calendar event is no longer available or has been removed.';default:return'The content you\\'re looking for is no longer available.';}}/**\n   * Generate deep link URL for sharing notifications\n   */generateDeepLink(notification){let baseUrl=arguments.length>1&&arguments[1]!==undefined?arguments[1]:window.location.origin;const context=this.parseNotificationContext(notification);const userRole=this.currentUserRole||'student';const targetRoute=this.buildTargetRoute(context,userRole);if(targetRoute){return\"\".concat(baseUrl).concat(targetRoute,\"&notification=\").concat(notification.notification_id);}return\"\".concat(baseUrl,\"/\").concat(userRole,\"/dashboard\");}/**\n   * Handle browser back navigation after notification click\n   */handleBackNavigation(){// This could be enhanced to provide smart back navigation\n// For now, we'll let the browser handle it naturally\nif(window.history.length>1){window.history.back();}else{var _this$navigate;const fallbackRoute=this.currentUserRole==='admin'?'/admin/dashboard':'/student/dashboard';(_this$navigate=this.navigate)===null||_this$navigate===void 0?void 0:_this$navigate.call(this,fallbackRoute);}}}// Export singleton instance\nexport const notificationNavigationService=new NotificationNavigationService();export default notificationNavigationService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}