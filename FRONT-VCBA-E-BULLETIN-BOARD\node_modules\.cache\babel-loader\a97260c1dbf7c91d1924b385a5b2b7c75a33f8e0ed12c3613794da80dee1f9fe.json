{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '100px',\n          height: '100px',\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n          transition: 'border-color 0.2s ease',\n          cursor: 'pointer'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n        },\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(Camera, {\n            size: 24,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              border: '2px solid #e8f5e8',\n              borderTop: '2px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"anN87rU3+EZ9lZcP/0Ht9kDGQHU=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "_s", "preview", "setPreview", "selectedFile", "setSelectedFile", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showRemoveConfirm", "setShowRemoveConfirm", "fileInputRef", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "size", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "setTimeout", "err", "message", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemove", "displayImage", "hasImage", "Boolean", "console", "log", "children", "style", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "current", "click", "src", "alt", "onLoad", "onError", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "flexDirection", "_fileInputRef$current2", "disabled", "padding", "ref", "accept", "onChange", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Save, RotateCcw, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async (file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div\n          style={{\n            position: 'relative',\n            width: '100px',\n            height: '100px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n            transition: 'border-color 0.2s ease',\n            cursor: 'pointer'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: '2rem'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Upload Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(0, 0, 0, 0.5)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.2s ease'\n            }}\n          >\n            <Camera size={24} color=\"white\" />\n          </div>\n\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  border: '2px solid #e8f5e8',\n                  borderTop: '2px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.6 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAiC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpG,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAG7B,WAAW,CAAE8B,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,gBAAgB,GAAGpC,WAAW,CAAC,MAAO8B,IAAU,IAAK;IACzDP,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMY,eAAe,GAAGR,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIO,eAAe,EAAE;MACnBd,QAAQ,CAACc,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBzB,UAAU,EAAAyB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;IACxC,CAAC;IACDN,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBtB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDe,MAAM,CAACQ,aAAa,CAAChB,IAAI,CAAC;IAE1B,IAAI;MACF,MAAMnB,QAAQ,CAACmB,IAAI,CAAC;MACpBL,UAAU,CAAC,uCAAuC,CAAC;MACnDsB,UAAU,CAAC,MAAMtB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOuB,GAAQ,EAAE;MACjBzB,QAAQ,CAACyB,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3DhC,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACY,YAAY,EAAElB,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAMuC,iBAAiB,GAAIT,CAAsC,IAAK;IAAA,IAAAU,eAAA;IACpE,MAAMrB,IAAI,IAAAqB,eAAA,GAAGV,CAAC,CAACE,MAAM,CAACS,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIrB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIZ,CAAkB,IAAK;IAC7CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMkC,eAAe,GAAId,CAAkB,IAAK;IAC9CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMmC,UAAU,GAAIf,CAAkB,IAAK;IACzCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMS,IAAI,GAAGW,CAAC,CAACgB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAItB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BnC,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBR,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAML,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDsB,UAAU,CAAC,MAAMtB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOuB,GAAQ,EAAE;MACjBzB,QAAQ,CAACyB,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMU,YAAY,GAAG3C,OAAO,IAAIP,cAAc;EAC9C,MAAMmD,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;;EAEtC;EACAG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9CtD,cAAc;IACdO,OAAO;IACP2C,YAAY;IACZC;EACF,CAAC,CAAC;EAEF,oBACErD,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAkD,QAAA,gBACpDzD,OAAA;MAAK0D,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAEjEzD,OAAA;QACE0D,KAAK,EAAE;UACLI,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEtD,UAAU,GAAG,oBAAoB,GAAG,mBAAmB;UAC/DuD,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAExB,cAAe;QAC3ByB,WAAW,EAAEvB,eAAgB;QAC7BwB,MAAM,EAAEvB,UAAW;QACnBwB,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAMrD,YAAY,CAACsD,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;QAAA,CAAC;QAAAnB,QAAA,GAE5CJ,QAAQ,gBACPrD,OAAA;UACE6E,GAAG,EAAEzB,YAAa;UAClB0B,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,YAAY,CAAE;UACxE4B,OAAO,EAAG9C,CAAC,IAAKqB,OAAO,CAACxC,KAAK,CAAC,yBAAyB,EAAEqC,YAAY,EAAElB,CAAC,CAAE;UAC1EwB,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFrF,OAAA;UACE0D,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdsB,UAAU,EAAE,mDAAmD;YAC/D3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAjC,QAAA,EAEDtD;QAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGDrF,OAAA;UACE0D,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,oBAAoB;YAChC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAElF,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3BuD,UAAU,EAAE;UACd,CAAE;UAAAX,QAAA,eAEFzD,OAAA,CAACJ,MAAM;YAACgC,IAAI,EAAE,EAAG;YAAC4D,KAAK,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAEL/E,SAAS,iBACRN,OAAA;UACE0D,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,0BAA0B;YACtC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE;UAClB,CAAE;UAAA9B,QAAA,eAEFzD,OAAA;YACE0D,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,MAAM,EAAE,mBAAmB;cAC3B6B,SAAS,EAAE,mBAAmB;cAC9B/B,YAAY,EAAE,KAAK;cACnBgC,SAAS,EAAE;YACb;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrF,OAAA;QAAK0D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEuC,aAAa,EAAE,QAAQ;UAAErC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACpEzD,OAAA;UAAK0D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAC3CzD,OAAA;YACEyE,OAAO,EAAEA,CAAA;cAAA,IAAA0B,sBAAA;cAAA,QAAAA,sBAAA,GAAM9E,YAAY,CAACsD,OAAO,cAAAwB,sBAAA,uBAApBA,sBAAA,CAAsBvB,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CwB,QAAQ,EAAE9F,SAAU;YACpBoD,KAAK,EAAE;cACL4B,UAAU,EAAE,mDAAmD;cAC/DE,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBZ,UAAU,EAAE,KAAK;cACjBpB,MAAM,EAAE/D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CyF,OAAO,EAAEzF,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BqD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEFzD,OAAA,CAACN,MAAM;cAACkC,IAAI,EAAE;YAAG;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBhC,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,EAERhC,QAAQ,iBACPrD,OAAA;YACEyE,OAAO,EAAEtB,YAAa;YACtBiD,QAAQ,EAAE9F,SAAU;YACpBoD,KAAK,EAAE;cACL4B,UAAU,EAAE,MAAM;cAClBnB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBhC,MAAM,EAAE/D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CkF,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAEzF,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BqD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEFzD,OAAA,CAACL,CAAC;cAACiC,IAAI,EAAE;YAAG;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrF,OAAA;UACEsG,GAAG,EAAEjF,YAAa;UAClBM,IAAI,EAAC,MAAM;UACX4E,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAE7D,iBAAkB;UAC5Be,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGFrF,OAAA;UAAG0D,KAAK,EAAE;YACRgC,QAAQ,EAAE,UAAU;YACpBF,KAAK,EAAE,SAAS;YAChBiB,MAAM,EAAE;UACV,CAAE;UAAAhD,QAAA,EAAC;QAEH;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtE,KAAK,iBACJf,OAAA;MAAK0D,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAzD,OAAA,CAACH,WAAW;QAAC+B,IAAI,EAAE;MAAG;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBtE,KAAK;IAAA;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEApE,OAAO,iBACNjB,OAAA;MAAK0D,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAzD,OAAA,CAACF,WAAW;QAAC8B,IAAI,EAAE;MAAG;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBpE,OAAO;IAAA;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGDrF,OAAA;MAAAyD,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7E,EAAA,CAvUIP,oBAAyD;AAAA0G,EAAA,GAAzD1G,oBAAyD;AAyU/D,eAAeA,oBAAoB;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}