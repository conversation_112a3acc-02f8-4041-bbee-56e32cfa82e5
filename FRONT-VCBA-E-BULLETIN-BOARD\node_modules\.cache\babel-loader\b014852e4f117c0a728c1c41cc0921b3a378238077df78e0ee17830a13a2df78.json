{"ast": null, "code": "import { API_ENDPOINTS } from '../config/constants';\nimport AdminAuthService from './admin-auth.service';\nimport StudentAuthService from './student-auth.service';\n/**\n * Admin Profile Picture Service\n * Handles profile picture operations for admin users\n */\nexport class AdminProfilePictureService {\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return AdminAuthService.post(`${API_ENDPOINTS.ADMIN.PROFILE}/picture`, formData);\n  }\n\n  /**\n   * Delete admin profile picture\n   */\n  static async deleteProfilePicture() {\n    return AdminAuthService.delete(`${API_ENDPOINTS.ADMIN.PROFILE}/picture`);\n  }\n\n  /**\n   * Get profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath) {\n    if (!profilePicturePath) return null;\n\n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n\n    // Construct full URL from relative path\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return profilePicturePath.startsWith('/') ? `${baseUrl}${profilePicturePath}` : `${baseUrl}/${profilePicturePath}`;\n  }\n}\n\n/**\n * Student Profile Picture Service\n * Handles profile picture operations for student users\n */\nexport class StudentProfilePictureService {\n  /**\n   * Upload student profile picture\n   */\n  static async uploadProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return StudentAuthService.post(`${API_ENDPOINTS.STUDENT.PROFILE}/picture`, formData);\n  }\n\n  /**\n   * Delete student profile picture\n   */\n  static async deleteProfilePicture() {\n    return StudentAuthService.delete(`${API_ENDPOINTS.STUDENT.PROFILE}/picture`);\n  }\n\n  /**\n   * Get profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath) {\n    if (!profilePicturePath) return null;\n\n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n\n    // Construct full URL from relative path\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return profilePicturePath.startsWith('/') ? `${baseUrl}${profilePicturePath}` : `${baseUrl}/${profilePicturePath}`;\n  }\n}\n\n/**\n * Generic Profile Picture Service\n * Provides common utilities for profile picture handling\n */\nexport class ProfilePictureService {\n  /**\n   * Validate profile picture file\n   */\n  static validateFile(file, maxSize = 5 * 1024 * 1024) {\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return `Invalid file format. Accepted formats: ${allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n    if (file.size > maxSize) {\n      const maxSizeMB = Math.round(maxSize / (1024 * 1024));\n      return `File size too large. Maximum size: ${maxSizeMB}MB`;\n    }\n    return null;\n  }\n\n  /**\n   * Create image preview URL\n   */\n  static createPreviewUrl(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        return resolve((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  }\n\n  /**\n   * Get profile picture URL with fallback\n   */\n  static getProfilePictureUrl(profilePicturePath, userType) {\n    if (userType === 'admin') {\n      return AdminProfilePictureService.getProfilePictureUrl(profilePicturePath);\n    } else {\n      return StudentProfilePictureService.getProfilePictureUrl(profilePicturePath);\n    }\n  }\n\n  /**\n   * Generate initials from user name\n   */\n  static generateInitials(firstName, lastName) {\n    var _firstName$charAt, _lastName$charAt;\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName$charAt = firstName.charAt(0)) === null || _firstName$charAt === void 0 ? void 0 : _firstName$charAt.toUpperCase()) || '';\n    const last = (lastName === null || lastName === void 0 ? void 0 : (_lastName$charAt = lastName.charAt(0)) === null || _lastName$charAt === void 0 ? void 0 : _lastName$charAt.toUpperCase()) || '';\n    return `${first}${last}` || 'U';\n  }\n\n  /**\n   * Get avatar display props\n   */\n  static getAvatarProps(profilePicturePath, firstName, lastName, userType = 'student') {\n    const profilePictureUrl = this.getProfilePictureUrl(profilePicturePath, userType);\n    const initials = this.generateInitials(firstName, lastName);\n    return {\n      profilePictureUrl,\n      initials,\n      hasProfilePicture: !!profilePictureUrl\n    };\n  }\n}\nexport default ProfilePictureService;", "map": {"version": 3, "names": ["API_ENDPOINTS", "AdminAuthService", "StudentAuthService", "AdminProfilePictureService", "uploadProfilePicture", "file", "formData", "FormData", "append", "post", "ADMIN", "PROFILE", "deleteProfilePicture", "delete", "getProfilePictureUrl", "profilePicture<PERSON>ath", "startsWith", "baseUrl", "process", "env", "REACT_APP_API_URL", "StudentProfilePictureService", "STUDENT", "ProfilePictureService", "validateFile", "maxSize", "allowedTypes", "includes", "type", "map", "t", "split", "toUpperCase", "join", "size", "maxSizeMB", "Math", "round", "createPreviewUrl", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "userType", "generateInitials", "firstName", "lastName", "_firstName$charAt", "_lastName$charAt", "first", "char<PERSON>t", "last", "getAvatarProps", "profilePictureUrl", "initials", "hasProfilePicture"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/profilePictureService.ts"], "sourcesContent": ["import { API_ENDPOINTS } from '../config/constants';\nimport AdminAuthService from './admin-auth.service';\nimport StudentAuthService from './student-auth.service';\nimport { ApiResponse } from '../types';\n\nexport interface ProfilePictureUploadResponse {\n  profilePicture: {\n    filename: string;\n    path: string;\n    size: number;\n  };\n}\n\nexport interface ProfilePictureDeleteResponse {\n  message: string;\n}\n\n/**\n * Admin Profile Picture Service\n * Handles profile picture operations for admin users\n */\nexport class AdminProfilePictureService {\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<ApiResponse<ProfilePictureUploadResponse>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n\n    return AdminAuthService.post<ProfilePictureUploadResponse>(\n      `${API_ENDPOINTS.ADMIN.PROFILE}/picture`,\n      formData\n    );\n  }\n\n  /**\n   * Delete admin profile picture\n   */\n  static async deleteProfilePicture(): Promise<ApiResponse<ProfilePictureDeleteResponse>> {\n    return AdminAuthService.delete<ProfilePictureDeleteResponse>(\n      `${API_ENDPOINTS.ADMIN.PROFILE}/picture`\n    );\n  }\n\n  /**\n   * Get profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath: string | null | undefined): string | null {\n    if (!profilePicturePath) return null;\n    \n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n    \n    // Construct full URL from relative path\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return profilePicturePath.startsWith('/') \n      ? `${baseUrl}${profilePicturePath}` \n      : `${baseUrl}/${profilePicturePath}`;\n  }\n}\n\n/**\n * Student Profile Picture Service\n * Handles profile picture operations for student users\n */\nexport class StudentProfilePictureService {\n  /**\n   * Upload student profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<ApiResponse<ProfilePictureUploadResponse>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n\n    return StudentAuthService.post<ProfilePictureUploadResponse>(\n      `${API_ENDPOINTS.STUDENT.PROFILE}/picture`,\n      formData\n    );\n  }\n\n  /**\n   * Delete student profile picture\n   */\n  static async deleteProfilePicture(): Promise<ApiResponse<ProfilePictureDeleteResponse>> {\n    return StudentAuthService.delete<ProfilePictureDeleteResponse>(\n      `${API_ENDPOINTS.STUDENT.PROFILE}/picture`\n    );\n  }\n\n  /**\n   * Get profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath: string | null | undefined): string | null {\n    if (!profilePicturePath) return null;\n    \n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n    \n    // Construct full URL from relative path\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return profilePicturePath.startsWith('/') \n      ? `${baseUrl}${profilePicturePath}` \n      : `${baseUrl}/${profilePicturePath}`;\n  }\n}\n\n/**\n * Generic Profile Picture Service\n * Provides common utilities for profile picture handling\n */\nexport class ProfilePictureService {\n  /**\n   * Validate profile picture file\n   */\n  static validateFile(file: File, maxSize: number = 5 * 1024 * 1024): string | null {\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n    \n    if (!allowedTypes.includes(file.type)) {\n      return `Invalid file format. Accepted formats: ${allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n\n    if (file.size > maxSize) {\n      const maxSizeMB = Math.round(maxSize / (1024 * 1024));\n      return `File size too large. Maximum size: ${maxSizeMB}MB`;\n    }\n\n    return null;\n  }\n\n  /**\n   * Create image preview URL\n   */\n  static createPreviewUrl(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => resolve(e.target?.result as string);\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  }\n\n  /**\n   * Get profile picture URL with fallback\n   */\n  static getProfilePictureUrl(\n    profilePicturePath: string | null | undefined,\n    userType: 'admin' | 'student'\n  ): string | null {\n    if (userType === 'admin') {\n      return AdminProfilePictureService.getProfilePictureUrl(profilePicturePath);\n    } else {\n      return StudentProfilePictureService.getProfilePictureUrl(profilePicturePath);\n    }\n  }\n\n  /**\n   * Generate initials from user name\n   */\n  static generateInitials(firstName?: string, lastName?: string): string {\n    const first = firstName?.charAt(0)?.toUpperCase() || '';\n    const last = lastName?.charAt(0)?.toUpperCase() || '';\n    return `${first}${last}` || 'U';\n  }\n\n  /**\n   * Get avatar display props\n   */\n  static getAvatarProps(\n    profilePicturePath: string | null | undefined,\n    firstName?: string,\n    lastName?: string,\n    userType: 'admin' | 'student' = 'student'\n  ) {\n    const profilePictureUrl = this.getProfilePictureUrl(profilePicturePath, userType);\n    const initials = this.generateInitials(firstName, lastName);\n\n    return {\n      profilePictureUrl,\n      initials,\n      hasProfilePicture: !!profilePictureUrl\n    };\n  }\n}\n\nexport default ProfilePictureService;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,gBAAgB,MAAM,sBAAsB;AACnD,OAAOC,kBAAkB,MAAM,wBAAwB;AAevD;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,CAAC;EACtC;AACF;AACA;EACE,aAAaC,oBAAoBA,CAACC,IAAU,EAAsD;IAChG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOJ,gBAAgB,CAACQ,IAAI,CAC1B,GAAGT,aAAa,CAACU,KAAK,CAACC,OAAO,UAAU,EACxCL,QACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,aAAaM,oBAAoBA,CAAA,EAAuD;IACtF,OAAOX,gBAAgB,CAACY,MAAM,CAC5B,GAAGb,aAAa,CAACU,KAAK,CAACC,OAAO,UAChC,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOG,oBAAoBA,CAACC,kBAA6C,EAAiB;IACxF,IAAI,CAACA,kBAAkB,EAAE,OAAO,IAAI;;IAEpC;IACA,IAAIA,kBAAkB,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACzC,OAAOD,kBAAkB;IAC3B;;IAEA;IACA,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IACxE,OAAOL,kBAAkB,CAACC,UAAU,CAAC,GAAG,CAAC,GACrC,GAAGC,OAAO,GAAGF,kBAAkB,EAAE,GACjC,GAAGE,OAAO,IAAIF,kBAAkB,EAAE;EACxC;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMM,4BAA4B,CAAC;EACxC;AACF;AACA;EACE,aAAajB,oBAAoBA,CAACC,IAAU,EAAsD;IAChG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOH,kBAAkB,CAACO,IAAI,CAC5B,GAAGT,aAAa,CAACsB,OAAO,CAACX,OAAO,UAAU,EAC1CL,QACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,aAAaM,oBAAoBA,CAAA,EAAuD;IACtF,OAAOV,kBAAkB,CAACW,MAAM,CAC9B,GAAGb,aAAa,CAACsB,OAAO,CAACX,OAAO,UAClC,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOG,oBAAoBA,CAACC,kBAA6C,EAAiB;IACxF,IAAI,CAACA,kBAAkB,EAAE,OAAO,IAAI;;IAEpC;IACA,IAAIA,kBAAkB,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACzC,OAAOD,kBAAkB;IAC3B;;IAEA;IACA,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IACxE,OAAOL,kBAAkB,CAACC,UAAU,CAAC,GAAG,CAAC,GACrC,GAAGC,OAAO,GAAGF,kBAAkB,EAAE,GACjC,GAAGE,OAAO,IAAIF,kBAAkB,EAAE;EACxC;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,qBAAqB,CAAC;EACjC;AACF;AACA;EACE,OAAOC,YAAYA,CAACnB,IAAU,EAAEoB,OAAe,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAiB;IAChF,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACtB,IAAI,CAACuB,IAAI,CAAC,EAAE;MACrC,OAAO,0CAA0CF,YAAY,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IACpH;IAEA,IAAI5B,IAAI,CAAC6B,IAAI,GAAGT,OAAO,EAAE;MACvB,MAAMU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACZ,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;MACrD,OAAO,sCAAsCU,SAAS,IAAI;IAC5D;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,OAAOG,gBAAgBA,CAACjC,IAAU,EAAmB;IACnD,OAAO,IAAIkC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC;QAAA,IAAAC,SAAA;QAAA,OAAKN,OAAO,EAAAM,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;MAAA;MAC1DN,MAAM,CAACO,OAAO,GAAGR,MAAM;MACvBC,MAAM,CAACQ,aAAa,CAAC7C,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,OAAOS,oBAAoBA,CACzBC,kBAA6C,EAC7CoC,QAA6B,EACd;IACf,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAOhD,0BAA0B,CAACW,oBAAoB,CAACC,kBAAkB,CAAC;IAC5E,CAAC,MAAM;MACL,OAAOM,4BAA4B,CAACP,oBAAoB,CAACC,kBAAkB,CAAC;IAC9E;EACF;;EAEA;AACF;AACA;EACE,OAAOqC,gBAAgBA,CAACC,SAAkB,EAAEC,QAAiB,EAAU;IAAA,IAAAC,iBAAA,EAAAC,gBAAA;IACrE,MAAMC,KAAK,GAAG,CAAAJ,SAAS,aAATA,SAAS,wBAAAE,iBAAA,GAATF,SAAS,CAAEK,MAAM,CAAC,CAAC,CAAC,cAAAH,iBAAA,uBAApBA,iBAAA,CAAsBvB,WAAW,CAAC,CAAC,KAAI,EAAE;IACvD,MAAM2B,IAAI,GAAG,CAAAL,QAAQ,aAARA,QAAQ,wBAAAE,gBAAA,GAARF,QAAQ,CAAEI,MAAM,CAAC,CAAC,CAAC,cAAAF,gBAAA,uBAAnBA,gBAAA,CAAqBxB,WAAW,CAAC,CAAC,KAAI,EAAE;IACrD,OAAO,GAAGyB,KAAK,GAAGE,IAAI,EAAE,IAAI,GAAG;EACjC;;EAEA;AACF;AACA;EACE,OAAOC,cAAcA,CACnB7C,kBAA6C,EAC7CsC,SAAkB,EAClBC,QAAiB,EACjBH,QAA6B,GAAG,SAAS,EACzC;IACA,MAAMU,iBAAiB,GAAG,IAAI,CAAC/C,oBAAoB,CAACC,kBAAkB,EAAEoC,QAAQ,CAAC;IACjF,MAAMW,QAAQ,GAAG,IAAI,CAACV,gBAAgB,CAACC,SAAS,EAAEC,QAAQ,CAAC;IAE3D,OAAO;MACLO,iBAAiB;MACjBC,QAAQ;MACRC,iBAAiB,EAAE,CAAC,CAACF;IACvB,CAAC;EACH;AACF;AAEA,eAAetC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}