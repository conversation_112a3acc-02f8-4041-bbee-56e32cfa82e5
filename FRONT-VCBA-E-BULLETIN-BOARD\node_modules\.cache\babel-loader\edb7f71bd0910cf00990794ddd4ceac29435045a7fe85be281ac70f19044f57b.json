{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Archive.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Archive as ArchiveIcon, FileText, Calendar, Users } from 'lucide-react';\nimport { archiveService } from '../../services/archiveService';\nimport ArchivedAnnouncements from '../../components/admin/archive/ArchivedAnnouncements';\nimport ArchivedCalendarEvents from '../../components/admin/archive/ArchivedCalendarEvents';\nimport ArchivedStudents from '../../components/admin/archive/ArchivedStudents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Archive = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('announcements');\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadStatistics();\n  }, []);\n  const loadStatistics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await archiveService.getArchiveStatistics();\n      if (response.success && response.data) {\n        setStatistics(response.data);\n      } else {\n        setError('Failed to load archive statistics');\n        // Set default statistics to prevent UI errors\n        setStatistics({\n          announcements: 0,\n          calendar_events: 0,\n          students: 0,\n          total: 0\n        });\n      }\n    } catch (error) {\n      console.error('Error loading archive statistics:', error);\n      setError(error.message || 'Failed to load archive statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const tabs = [{\n    id: 'announcements',\n    label: 'Announcements',\n    icon: FileText,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.announcements) || 0,\n    color: '#3b82f6'\n  }, {\n    id: 'calendar',\n    label: 'Calendar Events',\n    icon: Calendar,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.calendar_events) || 0,\n    color: '#10b981'\n  }, {\n    id: 'students',\n    label: 'Students',\n    icon: Users,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.students) || 0,\n    color: '#f59e0b'\n  }];\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'announcements':\n        return /*#__PURE__*/_jsxDEV(ArchivedAnnouncements, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n      case 'calendar':\n        return /*#__PURE__*/_jsxDEV(ArchivedCalendarEvents, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n      case 'students':\n        return /*#__PURE__*/_jsxDEV(ArchivedStudents, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px',\n        color: '#6b7280'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), \"Loading archive data...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(ArchiveIcon, {\n          size: 24,\n          color: \"white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: 0,\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Archive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"View and manage archived records\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1.5rem',\n        color: '#dc2626'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this), statistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '12px',\n          padding: '1.5rem',\n          border: '1px solid #e5e7eb',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',\n          borderColor: activeTab === tab.id ? tab.color : '#e5e7eb'\n        },\n        onClick: () => setActiveTab(tab.id),\n        onMouseEnter: e => {\n          if (activeTab !== tab.id) {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n          }\n        },\n        onMouseLeave: e => {\n          if (activeTab !== tab.id) {\n            e.currentTarget.style.transform = 'none';\n            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              background: `${tab.color}20`,\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(tab.icon, {\n              size: 18,\n              color: tab.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#6b7280'\n            },\n            children: tab.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: tab.color\n          },\n          children: tab.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',\n          borderRadius: '12px',\n          padding: '1.5rem',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              background: 'rgba(255, 255, 255, 0.2)',\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ArchiveIcon, {\n              size: 18,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              opacity: 0.9\n            },\n            children: \"Total Archived\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700'\n          },\n          children: statistics.total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '2px solid #e5e7eb',\n        marginBottom: '2rem'\n      },\n      children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setActiveTab(tab.id),\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '1rem 1.5rem',\n          background: 'none',\n          border: 'none',\n          borderBottom: `3px solid ${activeTab === tab.id ? tab.color : 'transparent'}`,\n          color: activeTab === tab.id ? tab.color : '#6b7280',\n          fontWeight: activeTab === tab.id ? '600' : '500',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), tab.label, tab.count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: activeTab === tab.id ? tab.color : '#e5e7eb',\n            color: activeTab === tab.id ? 'white' : '#6b7280',\n            fontSize: '0.75rem',\n            fontWeight: '600',\n            padding: '0.25rem 0.5rem',\n            borderRadius: '12px',\n            minWidth: '20px',\n            textAlign: 'center'\n          },\n          children: tab.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: renderTabContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(Archive, \"8sA8jgfn0X6j3CyM0AqroqTfv40=\");\n_c = Archive;\nexport default Archive;\nvar _c;\n$RefreshReg$(_c, \"Archive\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Archive", "ArchiveIcon", "FileText", "Calendar", "Users", "archiveService", "ArchivedAnnouncements", "ArchivedCalendarEvents", "ArchivedStudents", "jsxDEV", "_jsxDEV", "_s", "activeTab", "setActiveTab", "statistics", "setStatistics", "loading", "setLoading", "error", "setError", "loadStatistics", "response", "getArchiveStatistics", "success", "data", "announcements", "calendar_events", "students", "total", "console", "message", "tabs", "id", "label", "icon", "count", "color", "renderTabContent", "onRestoreSuccess", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "minHeight", "children", "textAlign", "width", "height", "border", "borderTop", "borderRadius", "animation", "margin", "padding", "gap", "marginBottom", "background", "size", "fontSize", "fontWeight", "gridTemplateColumns", "map", "tab", "boxShadow", "cursor", "transition", "transform", "borderColor", "onClick", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "opacity", "borderBottom", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Archive.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Archive as ArchiveIcon, FileText, Calendar, Users, RotateCcw, Trash2, Search, Filter } from 'lucide-react';\nimport { archiveService, ArchiveStatistics } from '../../services/archiveService';\nimport ArchivedAnnouncements from '../../components/admin/archive/ArchivedAnnouncements';\nimport ArchivedCalendarEvents from '../../components/admin/archive/ArchivedCalendarEvents';\nimport ArchivedStudents from '../../components/admin/archive/ArchivedStudents';\n\ntype ArchiveTab = 'announcements' | 'calendar' | 'students';\n\nconst Archive: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<ArchiveTab>('announcements');\n  const [statistics, setStatistics] = useState<ArchiveStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadStatistics();\n  }, []);\n\n  const loadStatistics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await archiveService.getArchiveStatistics();\n      if (response.success && response.data) {\n        setStatistics(response.data);\n      } else {\n        setError('Failed to load archive statistics');\n        // Set default statistics to prevent UI errors\n        setStatistics({\n          announcements: 0,\n          calendar_events: 0,\n          students: 0,\n          total: 0\n        });\n      }\n    } catch (error: any) {\n      console.error('Error loading archive statistics:', error);\n      setError(error.message || 'Failed to load archive statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    {\n      id: 'announcements' as ArchiveTab,\n      label: 'Announcements',\n      icon: FileText,\n      count: statistics?.announcements || 0,\n      color: '#3b82f6'\n    },\n    {\n      id: 'calendar' as ArchiveTab,\n      label: 'Calendar Events',\n      icon: Calendar,\n      count: statistics?.calendar_events || 0,\n      color: '#10b981'\n    },\n    {\n      id: 'students' as ArchiveTab,\n      label: 'Students',\n      icon: Users,\n      count: statistics?.students || 0,\n      color: '#f59e0b'\n    }\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'announcements':\n        return <ArchivedAnnouncements onRestoreSuccess={loadStatistics} />;\n      case 'calendar':\n        return <ArchivedCalendarEvents onRestoreSuccess={loadStatistics} />;\n      case 'students':\n        return <ArchivedStudents onRestoreSuccess={loadStatistics} />;\n      default:\n        return null;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px',\n        color: '#6b7280'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{\n            width: '40px',\n            height: '40px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }} />\n          Loading archive data...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      }}>\n        <div style={{\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}>\n          <ArchiveIcon size={24} color=\"white\" />\n        </div>\n        <div>\n          <h1 style={{\n            margin: 0,\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Archive\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            View and manage archived records\n          </p>\n        </div>\n      </div>\n\n      {error && (\n        <div style={{\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          padding: '1rem',\n          marginBottom: '1.5rem',\n          color: '#dc2626'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      {statistics && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem',\n          marginBottom: '2rem'\n        }}>\n          {tabs.map((tab) => (\n            <div\n              key={tab.id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                padding: '1.5rem',\n                border: '1px solid #e5e7eb',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',\n                borderColor: activeTab === tab.id ? tab.color : '#e5e7eb'\n              }}\n              onClick={() => setActiveTab(tab.id)}\n              onMouseEnter={(e) => {\n                if (activeTab !== tab.id) {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (activeTab !== tab.id) {\n                  e.currentTarget.style.transform = 'none';\n                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                }\n              }}\n            >\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '0.5rem'\n              }}>\n                <div style={{\n                  width: '32px',\n                  height: '32px',\n                  background: `${tab.color}20`,\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <tab.icon size={18} color={tab.color} />\n                </div>\n                <span style={{\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#6b7280'\n                }}>\n                  {tab.label}\n                </span>\n              </div>\n              <div style={{\n                fontSize: '2rem',\n                fontWeight: '700',\n                color: tab.color\n              }}>\n                {tab.count}\n              </div>\n            </div>\n          ))}\n          \n          {/* Total Card */}\n          <div style={{\n            background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            color: 'white'\n          }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '0.5rem'\n            }}>\n              <div style={{\n                width: '32px',\n                height: '32px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                borderRadius: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <ArchiveIcon size={18} color=\"white\" />\n              </div>\n              <span style={{\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                opacity: 0.9\n              }}>\n                Total Archived\n              </span>\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700'\n            }}>\n              {statistics.total}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div style={{\n        display: 'flex',\n        borderBottom: '2px solid #e5e7eb',\n        marginBottom: '2rem'\n      }}>\n        {tabs.map((tab) => (\n          <button\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '1rem 1.5rem',\n              background: 'none',\n              border: 'none',\n              borderBottom: `3px solid ${activeTab === tab.id ? tab.color : 'transparent'}`,\n              color: activeTab === tab.id ? tab.color : '#6b7280',\n              fontWeight: activeTab === tab.id ? '600' : '500',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n          >\n            <tab.icon size={18} />\n            {tab.label}\n            {tab.count > 0 && (\n              <span style={{\n                background: activeTab === tab.id ? tab.color : '#e5e7eb',\n                color: activeTab === tab.id ? 'white' : '#6b7280',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '12px',\n                minWidth: '20px',\n                textAlign: 'center'\n              }}>\n                {tab.count}\n              </span>\n            )}\n          </button>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      <div>\n        {renderTabContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default Archive;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,IAAIC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,QAA2C,cAAc;AACnH,SAASC,cAAc,QAA2B,+BAA+B;AACjF,OAAOC,qBAAqB,MAAM,sDAAsD;AACxF,OAAOC,sBAAsB,MAAM,uDAAuD;AAC1F,OAAOC,gBAAgB,MAAM,iDAAiD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI/E,MAAMV,OAAiB,GAAGA,CAAA,KAAM;EAAAW,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAa,eAAe,CAAC;EACvE,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAA2B,IAAI,CAAC;EAC5E,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,QAAQ,GAAG,MAAMhB,cAAc,CAACiB,oBAAoB,CAAC,CAAC;MAC5D,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCT,aAAa,CAACM,QAAQ,CAACG,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLL,QAAQ,CAAC,mCAAmC,CAAC;QAC7C;QACAJ,aAAa,CAAC;UACZU,aAAa,EAAE,CAAC;UAChBC,eAAe,EAAE,CAAC;UAClBC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAACD,KAAK,CAACY,OAAO,IAAI,mCAAmC,CAAC;IAChE,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,eAA6B;IACjCC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAEhC,QAAQ;IACdiC,KAAK,EAAE,CAAArB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEW,aAAa,KAAI,CAAC;IACrCW,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,UAAwB;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE/B,QAAQ;IACdgC,KAAK,EAAE,CAAArB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEY,eAAe,KAAI,CAAC;IACvCU,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,UAAwB;IAC5BC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE9B,KAAK;IACX+B,KAAK,EAAE,CAAArB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,QAAQ,KAAI,CAAC;IAChCS,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQzB,SAAS;MACf,KAAK,eAAe;QAClB,oBAAOF,OAAA,CAACJ,qBAAqB;UAACgC,gBAAgB,EAAElB;QAAe;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpE,KAAK,UAAU;QACb,oBAAOhC,OAAA,CAACH,sBAAsB;UAAC+B,gBAAgB,EAAElB;QAAe;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrE,KAAK,UAAU;QACb,oBAAOhC,OAAA,CAACF,gBAAgB;UAAC8B,gBAAgB,EAAElB;QAAe;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAI1B,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKiC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,OAAO;QAClBX,KAAK,EAAE;MACT,CAAE;MAAAY,QAAA,eACAtC,OAAA;QAAKiC,KAAK,EAAE;UAAEM,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCtC,OAAA;UAAKiC,KAAK,EAAE;YACVO,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE,mBAAmB;YAC9BC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE;UACV;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAEP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAKiC,KAAK,EAAE;MAAEc,OAAO,EAAE;IAAO,CAAE;IAAAT,QAAA,gBAE9BtC,OAAA;MAAKiC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBY,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,gBACAtC,OAAA;QAAKiC,KAAK,EAAE;UACVO,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdS,UAAU,EAAE,mDAAmD;UAC/DN,YAAY,EAAE,MAAM;UACpBV,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE;QAClB,CAAE;QAAAG,QAAA,eACAtC,OAAA,CAACT,WAAW;UAAC4D,IAAI,EAAE,EAAG;UAACzB,KAAK,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACNhC,OAAA;QAAAsC,QAAA,gBACEtC,OAAA;UAAIiC,KAAK,EAAE;YACTa,MAAM,EAAE,CAAC;YACTM,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjB3B,KAAK,EAAE;UACT,CAAE;UAAAY,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhC,OAAA;UAAGiC,KAAK,EAAE;YACRa,MAAM,EAAE,CAAC;YACTpB,KAAK,EAAE,SAAS;YAChB0B,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxB,KAAK,iBACJR,OAAA;MAAKiC,KAAK,EAAE;QACViB,UAAU,EAAE,SAAS;QACrBR,MAAM,EAAE,mBAAmB;QAC3BE,YAAY,EAAE,KAAK;QACnBG,OAAO,EAAE,MAAM;QACfE,YAAY,EAAE,QAAQ;QACtBvB,KAAK,EAAE;MACT,CAAE;MAAAY,QAAA,EACC9B;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA5B,UAAU,iBACTJ,OAAA;MAAKiC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfoB,mBAAmB,EAAE,sCAAsC;QAC3DN,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,GACCjB,IAAI,CAACkC,GAAG,CAAEC,GAAG,iBACZxD,OAAA;QAEEiC,KAAK,EAAE;UACLiB,UAAU,EAAE,OAAO;UACnBN,YAAY,EAAE,MAAM;UACpBG,OAAO,EAAE,QAAQ;UACjBL,MAAM,EAAE,mBAAmB;UAC3Be,SAAS,EAAE,8BAA8B;UACzCC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3BC,SAAS,EAAE1D,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAG,kBAAkB,GAAG,MAAM;UAC7DuC,WAAW,EAAE3D,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAGkC,GAAG,CAAC9B,KAAK,GAAG;QAClD,CAAE;QACFoC,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACqD,GAAG,CAAClC,EAAE,CAAE;QACpCyC,YAAY,EAAGC,CAAC,IAAK;UACnB,IAAI9D,SAAS,KAAKsD,GAAG,CAAClC,EAAE,EAAE;YACxB0C,CAAC,CAACC,aAAa,CAAChC,KAAK,CAAC2B,SAAS,GAAG,kBAAkB;YACpDI,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACwB,SAAS,GAAG,gCAAgC;UACpE;QACF,CAAE;QACFS,YAAY,EAAGF,CAAC,IAAK;UACnB,IAAI9D,SAAS,KAAKsD,GAAG,CAAClC,EAAE,EAAE;YACxB0C,CAAC,CAACC,aAAa,CAAChC,KAAK,CAAC2B,SAAS,GAAG,MAAM;YACxCI,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACwB,SAAS,GAAG,8BAA8B;UAClE;QACF,CAAE;QAAAnB,QAAA,gBAEFtC,OAAA;UAAKiC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBY,GAAG,EAAE,SAAS;YACdC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBACAtC,OAAA;YAAKiC,KAAK,EAAE;cACVO,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdS,UAAU,EAAE,GAAGM,GAAG,CAAC9B,KAAK,IAAI;cAC5BkB,YAAY,EAAE,KAAK;cACnBV,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACAtC,OAAA,CAACwD,GAAG,CAAChC,IAAI;cAAC2B,IAAI,EAAE,EAAG;cAACzB,KAAK,EAAE8B,GAAG,CAAC9B;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNhC,OAAA;YAAMiC,KAAK,EAAE;cACXmB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjB3B,KAAK,EAAE;YACT,CAAE;YAAAY,QAAA,EACCkB,GAAG,CAACjC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNhC,OAAA;UAAKiC,KAAK,EAAE;YACVmB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjB3B,KAAK,EAAE8B,GAAG,CAAC9B;UACb,CAAE;UAAAY,QAAA,EACCkB,GAAG,CAAC/B;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA,GAzDDwB,GAAG,CAAClC,EAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0DR,CACN,CAAC,eAGFhC,OAAA;QAAKiC,KAAK,EAAE;UACViB,UAAU,EAAE,mDAAmD;UAC/DN,YAAY,EAAE,MAAM;UACpBG,OAAO,EAAE,QAAQ;UACjBrB,KAAK,EAAE;QACT,CAAE;QAAAY,QAAA,gBACAtC,OAAA;UAAKiC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBY,GAAG,EAAE,SAAS;YACdC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBACAtC,OAAA;YAAKiC,KAAK,EAAE;cACVO,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdS,UAAU,EAAE,0BAA0B;cACtCN,YAAY,EAAE,KAAK;cACnBV,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACAtC,OAAA,CAACT,WAAW;cAAC4D,IAAI,EAAE,EAAG;cAACzB,KAAK,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNhC,OAAA;YAAMiC,KAAK,EAAE;cACXmB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBc,OAAO,EAAE;YACX,CAAE;YAAA7B,QAAA,EAAC;UAEH;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNhC,OAAA;UAAKiC,KAAK,EAAE;YACVmB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EACClC,UAAU,CAACc;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhC,OAAA;MAAKiC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfkC,YAAY,EAAE,mBAAmB;QACjCnB,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,EACCjB,IAAI,CAACkC,GAAG,CAAEC,GAAG,iBACZxD,OAAA;QAEE8D,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACqD,GAAG,CAAClC,EAAE,CAAE;QACpCW,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBY,GAAG,EAAE,QAAQ;UACbD,OAAO,EAAE,aAAa;UACtBG,UAAU,EAAE,MAAM;UAClBR,MAAM,EAAE,MAAM;UACd0B,YAAY,EAAE,aAAalE,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAGkC,GAAG,CAAC9B,KAAK,GAAG,aAAa,EAAE;UAC7EA,KAAK,EAAExB,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAGkC,GAAG,CAAC9B,KAAK,GAAG,SAAS;UACnD2B,UAAU,EAAEnD,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAG,KAAK,GAAG,KAAK;UAChDoC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE;QACd,CAAE;QAAArB,QAAA,gBAEFtC,OAAA,CAACwD,GAAG,CAAChC,IAAI;UAAC2B,IAAI,EAAE;QAAG;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBwB,GAAG,CAACjC,KAAK,EACTiC,GAAG,CAAC/B,KAAK,GAAG,CAAC,iBACZzB,OAAA;UAAMiC,KAAK,EAAE;YACXiB,UAAU,EAAEhD,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAGkC,GAAG,CAAC9B,KAAK,GAAG,SAAS;YACxDA,KAAK,EAAExB,SAAS,KAAKsD,GAAG,CAAClC,EAAE,GAAG,OAAO,GAAG,SAAS;YACjD8B,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,KAAK;YACjBN,OAAO,EAAE,gBAAgB;YACzBH,YAAY,EAAE,MAAM;YACpByB,QAAQ,EAAE,MAAM;YAChB9B,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EACCkB,GAAG,CAAC/B;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACP;MAAA,GA/BIwB,GAAG,CAAClC,EAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCL,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhC,OAAA;MAAAsC,QAAA,EACGX,gBAAgB,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAxTIX,OAAiB;AAAAgF,EAAA,GAAjBhF,OAAiB;AA0TvB,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}