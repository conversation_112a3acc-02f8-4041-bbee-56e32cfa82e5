{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m4 5 8 8\",\n  key: \"1eunvl\"\n}], [\"path\", {\n  d: \"m12 5-8 8\",\n  key: \"1ah0jp\"\n}], [\"path\", {\n  d: \"M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07\",\n  key: \"e8ta8j\"\n}]];\nconst Subscript = createLucideIcon(\"subscript\", __iconNode);\nexport { __iconNode, Subscript as default };\n//# sourceMappingURL=subscript.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}