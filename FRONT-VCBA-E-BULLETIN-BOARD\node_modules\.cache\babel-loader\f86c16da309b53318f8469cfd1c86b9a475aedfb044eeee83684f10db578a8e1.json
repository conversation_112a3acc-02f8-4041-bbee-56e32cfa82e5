{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"rect\", {\n  x: \"9\",\n  y: \"9\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"1ssd4o\"\n}]];\nconst CircleStop = createLucideIcon(\"circle-stop\", __iconNode);\nexport { __iconNode, CircleStop as default };\n//# sourceMappingURL=circle-stop.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}