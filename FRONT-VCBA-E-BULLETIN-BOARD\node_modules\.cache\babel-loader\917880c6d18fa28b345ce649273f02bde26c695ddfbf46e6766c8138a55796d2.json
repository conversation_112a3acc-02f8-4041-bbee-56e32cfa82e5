{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z\",\n  key: \"e79jfc\"\n}], [\"circle\", {\n  cx: \"13.5\",\n  cy: \"6.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"1okk4w\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"10.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"f64h9f\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"12.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"qy21gx\"\n}], [\"circle\", {\n  cx: \"8.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"fotxhn\"\n}]];\nconst Palette = createLucideIcon(\"palette\", __iconNode);\nexport { __iconNode, Palette as default };\n//# sourceMappingURL=palette.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}