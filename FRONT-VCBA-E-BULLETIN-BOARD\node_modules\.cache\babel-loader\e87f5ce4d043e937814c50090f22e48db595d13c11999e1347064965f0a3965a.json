{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.929 18.629A1 1 0 0 1 19 20H9a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 11 13h6a1 1 0 0 1 .928.629z\",\n  key: \"u4w2d7\"\n}], [\"path\", {\n  d: \"M6 3a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z\",\n  key: \"15356w\"\n}], [\"path\", {\n  d: \"M8 6h4a2 2 0 0 1 2 2v5\",\n  key: \"1m6m7x\"\n}]];\nconst LampWallDown = createLucideIcon(\"lamp-wall-down\", __iconNode);\nexport { __iconNode, LampWallDown as default };\n//# sourceMappingURL=lamp-wall-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}