{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18H5a3 3 0 0 1-3-3v-1\",\n  key: \"ru65g8\"\n}], [\"path\", {\n  d: \"M14 2a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2\",\n  key: \"e30een\"\n}], [\"path\", {\n  d: \"M20 2a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2\",\n  key: \"2ahx8o\"\n}], [\"path\", {\n  d: \"m7 21 3-3-3-3\",\n  key: \"127cv2\"\n}], [\"rect\", {\n  x: \"14\",\n  y: \"14\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1b0bso\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"2\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1x09vl\"\n}]];\nconst Combine = createLucideIcon(\"combine\", __iconNode);\nexport { __iconNode, Combine as default };\n//# sourceMappingURL=combine.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}