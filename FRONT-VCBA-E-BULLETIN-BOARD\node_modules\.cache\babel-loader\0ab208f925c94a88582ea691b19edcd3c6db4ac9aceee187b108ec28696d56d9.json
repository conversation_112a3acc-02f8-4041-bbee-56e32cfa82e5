{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 10 3 3\",\n  key: \"fzmg1i\"\n}], [\"path\", {\n  d: \"M6.5 21A3.5 3.5 0 1 0 3 17.5a2.62 2.62 0 0 1-.708 1.792A1 1 0 0 0 3 21z\",\n  key: \"p4q2r7\"\n}], [\"path\", {\n  d: \"M9.969 17.031 21.378 5.624a1 1 0 0 0-3.002-3.002L6.967 14.031\",\n  key: \"wy6l02\"\n}]];\nconst Brush = createLucideIcon(\"brush\", __iconNode);\nexport { __iconNode, Brush as default };\n//# sourceMappingURL=brush.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}