{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5\",\n  key: \"x6cv4u\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}], [\"path\", {\n  d: \"M19 22v-6\",\n  key: \"qhmiwi\"\n}], [\"path\", {\n  d: \"m22 19-3-3-3 3\",\n  key: \"rn6bg2\"\n}], [\"path\", {\n  d: \"M6 12h.01\",\n  key: \"c2rlol\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}]];\nconst BanknoteArrowUp = createLucideIcon(\"banknote-arrow-up\", __iconNode);\nexport { __iconNode, BanknoteArrowUp as default };\n//# sourceMappingURL=banknote-arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}