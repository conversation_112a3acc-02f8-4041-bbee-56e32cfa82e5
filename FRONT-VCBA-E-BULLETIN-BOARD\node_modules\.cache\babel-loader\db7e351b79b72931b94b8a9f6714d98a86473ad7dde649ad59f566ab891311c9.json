{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10.5 8 13l2 2.5\",\n  key: \"m4t9c1\"\n}], [\"path\", {\n  d: \"m14 10.5 2 2.5-2 2.5\",\n  key: \"14w2eb\"\n}], [\"path\", {\n  d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2z\",\n  key: \"1u1bxd\"\n}]];\nconst FolderCode = createLucideIcon(\"folder-code\", __iconNode);\nexport { __iconNode, FolderCode as default };\n//# sourceMappingURL=folder-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}