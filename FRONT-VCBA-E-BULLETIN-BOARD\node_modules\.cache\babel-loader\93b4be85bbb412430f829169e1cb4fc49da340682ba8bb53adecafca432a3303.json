{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 14h-1.343\",\n  key: \"1jdnxi\"\n}], [\"path\", {\n  d: \"M9.128 3.47A9 9 0 0 1 21 12v3.343\",\n  key: \"6kipu2\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20.414 20.414A2 2 0 0 1 19 21h-1a2 2 0 0 1-2-2v-3\",\n  key: \"9x50f4\"\n}], [\"path\", {\n  d: \"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 2.636-6.364\",\n  key: \"1bkxnm\"\n}]];\nconst HeadphoneOff = createLucideIcon(\"headphone-off\", __iconNode);\nexport { __iconNode, HeadphoneOff as default };\n//# sourceMappingURL=headphone-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}