{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l3.644 1.822\",\n  key: \"1jmett\"\n}], [\"path\", {\n  d: \"M16 19h6\",\n  key: \"xwg31i\"\n}], [\"path\", {\n  d: \"M19 16v6\",\n  key: \"tddt3s\"\n}], [\"path\", {\n  d: \"M21.92 13.267a10 10 0 1 0-8.653 8.653\",\n  key: \"1u0osk\"\n}]];\nconst ClockPlus = createLucideIcon(\"clock-plus\", __iconNode);\nexport { __iconNode, ClockPlus as default };\n//# sourceMappingURL=clock-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}