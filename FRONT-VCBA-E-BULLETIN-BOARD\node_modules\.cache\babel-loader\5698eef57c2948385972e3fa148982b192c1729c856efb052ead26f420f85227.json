{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\",\n  key: \"cvxqlc\"\n}], [\"path\", {\n  d: \"M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\",\n  key: \"1ostrc\"\n}], [\"path\", {\n  d: \"M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12\",\n  key: \"hqx58h\"\n}], [\"path\", {\n  d: \"M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z\",\n  key: \"eykp1o\"\n}]];\nconst Cherry = createLucideIcon(\"cherry\", __iconNode);\nexport { __iconNode, Cherry as default };\n//# sourceMappingURL=cherry.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}