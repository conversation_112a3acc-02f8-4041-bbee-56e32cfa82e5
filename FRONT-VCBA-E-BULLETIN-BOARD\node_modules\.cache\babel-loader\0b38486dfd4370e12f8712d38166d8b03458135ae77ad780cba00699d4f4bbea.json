{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 17 17 2\",\n  key: \"18b09t\"\n}], [\"path\", {\n  d: \"m2 14 8 8\",\n  key: \"1gv9hu\"\n}], [\"path\", {\n  d: \"m5 11 8 8\",\n  key: \"189pqp\"\n}], [\"path\", {\n  d: \"m8 8 8 8\",\n  key: \"1imecy\"\n}], [\"path\", {\n  d: \"m11 5 8 8\",\n  key: \"ummqn6\"\n}], [\"path\", {\n  d: \"m14 2 8 8\",\n  key: \"1vk7dn\"\n}], [\"path\", {\n  d: \"M7 22 22 7\",\n  key: \"15mb1i\"\n}]];\nconst TrainTrack = createLucideIcon(\"train-track\", __iconNode);\nexport { __iconNode, TrainTrack as default };\n//# sourceMappingURL=train-track.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}