{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M12 9v11\",\n  key: \"1fnkrn\"\n}], [\"path\", {\n  d: \"M2 9h13a2 2 0 0 1 2 2v9\",\n  key: \"11z3ex\"\n}]];\nconst Proportions = createLucideIcon(\"proportions\", __iconNode);\nexport { __iconNode, Proportions as default };\n//# sourceMappingURL=proportions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}