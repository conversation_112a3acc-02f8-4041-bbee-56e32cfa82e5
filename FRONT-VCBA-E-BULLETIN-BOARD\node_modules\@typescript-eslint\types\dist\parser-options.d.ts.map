{"version": 3, "file": "parser-options.d.ts", "sourceRoot": "", "sources": ["../src/parser-options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAE1C,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAEjC,KAAK,UAAU,GAAG,OAAO,GAAG,CAAC,mBAAmB,GAAG,QAAQ,GAAG,YAAY,CAAC,EAAE,CAAC;AAC9E,KAAK,oBAAoB,GAAG,MAAM,GAAG,UAAU,CAAC;AAEhD,KAAK,WAAW,GACZ,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,CAAC;AAET,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAEtC,UAAU,aAAa;IACrB,YAAY,CAAC,EAAE;QACb,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB,GAAG,CAAC,EAAE,OAAO,CAAC;KACf,CAAC;IACF,WAAW,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC;IAGrC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IAGZ,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAGhC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,2CAA2C,CAAC,EAAE,OAAO,CAAC;IACtD,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,gDAAgD,CAAC,EAAE,OAAO,CAAC;IAC3D,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,GAAG,CAAC,EAAE,OAAO,CAAC;IACd,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC;IACnC,uBAAuB,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9C,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,kCAAkC,CAAC,EAAE,OAAO,CAAC;IAC7C,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,CAAC,EAAE;QACd,IAAI,CAAC,EAAE,oBAAoB,CAAC;KAC7B,CAAC;IAEF,CAAC,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC;CACzC;AAED,OAAO,EACL,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,aAAa,EACb,UAAU,GACX,CAAC"}