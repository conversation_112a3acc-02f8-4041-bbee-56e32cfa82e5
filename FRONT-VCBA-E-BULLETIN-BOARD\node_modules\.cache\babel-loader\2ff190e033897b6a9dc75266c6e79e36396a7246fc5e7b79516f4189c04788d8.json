{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 16.586V19a1 1 0 0 1-1 1H2L18.37 3.63a1 1 0 1 1 3 3l-9.663 9.663a1 1 0 0 1-1.414 0L8 14\",\n  key: \"1sllp5\"\n}]];\nconst Slice = createLucideIcon(\"slice\", __iconNode);\nexport { __iconNode, Slice as default };\n//# sourceMappingURL=slice.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}