{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21 17-2.156-1.868A.5.5 0 0 0 18 15.5v.5a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1c0-2.545-3.991-3.97-8.5-4a1 1 0 0 0 0 5c4.153 0 4.745-11.295 5.708-13.5a2.5 2.5 0 1 1 3.31 3.284\",\n  key: \"y32ogt\"\n}], [\"path\", {\n  d: \"M3 21h18\",\n  key: \"itz85i\"\n}]];\nconst Signature = createLucideIcon(\"signature\", __iconNode);\nexport { __iconNode, Signature as default };\n//# sourceMappingURL=signature.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}