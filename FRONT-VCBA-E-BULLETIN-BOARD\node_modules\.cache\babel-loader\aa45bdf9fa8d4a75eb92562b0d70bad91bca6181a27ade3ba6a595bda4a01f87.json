{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 16c5 0 7-8 12-8a4 4 0 0 1 0 8c-5 0-7-8-12-8a4 4 0 1 0 0 8\",\n  key: \"18ogeb\"\n}]];\nconst Infinity = createLucideIcon(\"infinity\", __iconNode);\nexport { __iconNode, Infinity as default };\n//# sourceMappingURL=infinity.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}