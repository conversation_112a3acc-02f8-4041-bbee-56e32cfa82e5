{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\Avatar.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Avatar = ({\n  imageUrl,\n  initials,\n  size = 40,\n  gradientColors = ['#22c55e', '#facc15'],\n  className = '',\n  style = {},\n  onClick\n}) => {\n  const avatarStyle = {\n    width: `${size}px`,\n    height: `${size}px`,\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    color: 'white',\n    fontWeight: '600',\n    fontSize: `${size * 0.4}px`,\n    // Scale font size with avatar size\n    cursor: onClick ? 'pointer' : 'default',\n    overflow: 'hidden',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    ...style\n  };\n  if (imageUrl) {\n    // Show actual profile picture\n    avatarStyle.background = `url(${imageUrl}) center/cover`;\n    avatarStyle.backgroundSize = 'cover';\n    avatarStyle.backgroundPosition = 'center';\n    avatarStyle.backgroundRepeat = 'no-repeat';\n  } else {\n    // Show gradient background with initials\n    avatarStyle.background = `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `avatar ${className}`,\n    style: avatarStyle,\n    onClick: onClick,\n    role: onClick ? 'button' : undefined,\n    tabIndex: onClick ? 0 : undefined,\n    children: !imageUrl && initials\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = Avatar;\nexport default Avatar;\nvar _c;\n$RefreshReg$(_c, \"Avatar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Avatar", "imageUrl", "initials", "size", "gradientColors", "className", "style", "onClick", "avatarS<PERSON>le", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontWeight", "fontSize", "cursor", "overflow", "border", "background", "backgroundSize", "backgroundPosition", "backgroundRepeat", "role", "undefined", "tabIndex", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/Avatar.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface AvatarProps {\n  imageUrl?: string;\n  initials: string;\n  size?: number; // in pixels\n  gradientColors?: [string, string];\n  className?: string;\n  style?: React.CSSProperties;\n  onClick?: () => void;\n}\n\nconst Avatar: React.FC<AvatarProps> = ({\n  imageUrl,\n  initials,\n  size = 40,\n  gradientColors = ['#22c55e', '#facc15'],\n  className = '',\n  style = {},\n  onClick\n}) => {\n  const avatarStyle: React.CSSProperties = {\n    width: `${size}px`,\n    height: `${size}px`,\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    color: 'white',\n    fontWeight: '600',\n    fontSize: `${size * 0.4}px`, // Scale font size with avatar size\n    cursor: onClick ? 'pointer' : 'default',\n    overflow: 'hidden',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    ...style\n  };\n\n  if (imageUrl) {\n    // Show actual profile picture\n    avatarStyle.background = `url(${imageUrl}) center/cover`;\n    avatarStyle.backgroundSize = 'cover';\n    avatarStyle.backgroundPosition = 'center';\n    avatarStyle.backgroundRepeat = 'no-repeat';\n  } else {\n    // Show gradient background with initials\n    avatarStyle.background = `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`;\n  }\n\n  return (\n    <div\n      className={`avatar ${className}`}\n      style={avatarStyle}\n      onClick={onClick}\n      role={onClick ? 'button' : undefined}\n      tabIndex={onClick ? 0 : undefined}\n    >\n      {!imageUrl && initials}\n    </div>\n  );\n};\n\nexport default Avatar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,QAAQ;EACRC,IAAI,GAAG,EAAE;EACTC,cAAc,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;EACvCC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAgC,GAAG;IACvCC,KAAK,EAAE,GAAGN,IAAI,IAAI;IAClBO,MAAM,EAAE,GAAGP,IAAI,IAAI;IACnBQ,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,GAAGd,IAAI,GAAG,GAAG,IAAI;IAAE;IAC7Be,MAAM,EAAEX,OAAO,GAAG,SAAS,GAAG,SAAS;IACvCY,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,oCAAoC;IAC5C,GAAGd;EACL,CAAC;EAED,IAAIL,QAAQ,EAAE;IACZ;IACAO,WAAW,CAACa,UAAU,GAAG,OAAOpB,QAAQ,gBAAgB;IACxDO,WAAW,CAACc,cAAc,GAAG,OAAO;IACpCd,WAAW,CAACe,kBAAkB,GAAG,QAAQ;IACzCf,WAAW,CAACgB,gBAAgB,GAAG,WAAW;EAC5C,CAAC,MAAM;IACL;IACAhB,WAAW,CAACa,UAAU,GAAG,2BAA2BjB,cAAc,CAAC,CAAC,CAAC,QAAQA,cAAc,CAAC,CAAC,CAAC,QAAQ;EACxG;EAEA,oBACEL,OAAA;IACEM,SAAS,EAAE,UAAUA,SAAS,EAAG;IACjCC,KAAK,EAAEE,WAAY;IACnBD,OAAO,EAAEA,OAAQ;IACjBkB,IAAI,EAAElB,OAAO,GAAG,QAAQ,GAAGmB,SAAU;IACrCC,QAAQ,EAAEpB,OAAO,GAAG,CAAC,GAAGmB,SAAU;IAAAE,QAAA,EAEjC,CAAC3B,QAAQ,IAAIC;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV,CAAC;AAACC,EAAA,GA/CIjC,MAA6B;AAiDnC,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}