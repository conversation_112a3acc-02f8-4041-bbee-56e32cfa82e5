{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user,\n    checkAuthStatus\n  } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile Settings',\n    icon: User\n  }, {\n    key: 'system',\n    label: 'System Settings',\n    icon: SettingsIcon\n  }, {\n    key: 'security',\n    label: 'Security',\n    icon: Lock\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }];\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async file => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 2rem 0',\n            color: '#2d5016',\n            fontSize: '1.5rem',\n            fontWeight: '600'\n          },\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-layout\",\n          style: {\n            display: 'flex',\n            gap: '2rem',\n            alignItems: 'flex-start',\n            marginBottom: '2rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n              currentPicture: user !== null && user !== void 0 && user.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined,\n              userInitials: `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`,\n              onUpload: handleProfilePictureUpload,\n              onRemove: handleProfilePictureRemove,\n              isLoading: isUploadingPicture,\n              size: 140\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-details\",\n            style: {\n              flex: 1,\n              minWidth: '300px',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem',\n              paddingTop: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#6b7280',\n                  marginBottom: '0.25rem'\n                },\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#111827',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  marginBottom: '0.5rem'\n                },\n                children: `${(user === null || user === void 0 ? void 0 : user.firstName) || ''} ${user !== null && user !== void 0 && user.middleName ? user.middleName + ' ' : ''}${(user === null || user === void 0 ? void 0 : user.lastName) || ''}${user !== null && user !== void 0 && user.suffix ? ' ' + user.suffix : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"role-status-grid\",\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    marginBottom: '0.25rem'\n                  },\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#16a34a',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    textTransform: 'capitalize',\n                    marginBottom: '0.5rem'\n                  },\n                  children: (user === null || user === void 0 ? void 0 : user.role) || 'Not assigned'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    marginBottom: '0.25rem'\n                  },\n                  children: \"Account Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: user !== null && user !== void 0 && user.isActive ? '#16a34a' : '#dc2626',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      background: user !== null && user !== void 0 && user.isActive ? '#16a34a' : '#dc2626'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 19\n                  }, this), user !== null && user !== void 0 && user.isActive ? 'Active' : 'Inactive']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 13\n            }, this), ((user === null || user === void 0 ? void 0 : user.department) || (user === null || user === void 0 ? void 0 : user.position) || (user === null || user === void 0 ? void 0 : user.phoneNumber)) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-grid\",\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '1rem'\n              },\n              children: [(user === null || user === void 0 ? void 0 : user.department) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    marginBottom: '0.25rem'\n                  },\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#111827',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    marginBottom: '0.5rem'\n                  },\n                  children: user.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), (user === null || user === void 0 ? void 0 : user.position) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    marginBottom: '0.25rem'\n                  },\n                  children: \"Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#111827',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    marginBottom: '0.5rem'\n                  },\n                  children: user.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), (user === null || user === void 0 ? void 0 : user.phoneNumber) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    marginBottom: '0.25rem'\n                  },\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#111827',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    marginBottom: '0.5rem'\n                  },\n                  children: user.phoneNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#2d5016',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '2rem',\n            background: '#f9fafb',\n            borderRadius: '12px',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Lock, {\n              size: 48,\n              color: \"#6b7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#374151',\n              fontSize: '1.125rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Password Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280',\n              marginBottom: '1.5rem',\n              lineHeight: '1.5'\n            },\n            children: \"Password change functionality will be available soon. For now, please contact your system administrator if you need to update your password.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            disabled: true,\n            style: {\n              background: '#e8f5e8',\n              color: '#9ca3af',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'not-allowed',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              margin: '0 auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 13\n            }, this), \"Change Password (Coming Soon)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 5\n    }, this);\n  };\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"General Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Maintenance Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Enable maintenance mode to restrict access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Auto-approve Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Automatically approve new posts without review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Email Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Send email notifications for important events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              defaultChecked: true,\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"System Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"System Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Last Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"June 28, 2025\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Database Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this), \"Connected\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Server Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), \"Online\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Lock, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Security Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Security settings panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this);\n      case 'notifications':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bell, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Notification Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Notification preferences panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-container\",\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"tab-button\",\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.3s ease',\n            transform: activeTab === tab.key ? 'translateY(-1px)' : 'translateY(0)',\n            boxShadow: activeTab === tab.key ? '0 4px 15px rgba(34, 197, 94, 0.3)' : 'none'\n          },\n          onMouseEnter: e => {\n            if (activeTab !== tab.key) {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.background = '#f9fafb';\n              e.currentTarget.style.transform = 'translateY(-1px)';\n            }\n          },\n          onMouseLeave: e => {\n            if (activeTab !== tab.key) {\n              e.currentTarget.style.borderColor = '#e8f5e8';\n              e.currentTarget.style.background = 'transparent';\n              e.currentTarget.style.transform = 'translateY(0)';\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 540,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"1HCAwwITHjjLDMKCsayOsMO0B+M=\", false, function () {\n  return [useAdminAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useAdminAuth", "User", "Settings", "SettingsIcon", "Lock", "Bell", "CheckCircle", "ProfilePictureUpload", "AdminAuthService", "jsxDEV", "_jsxDEV", "_s", "user", "checkAuthStatus", "activeTab", "setActiveTab", "isUploadingPicture", "setIsUploadingPicture", "tabs", "key", "label", "icon", "handleProfilePictureUpload", "file", "console", "log", "result", "uploadProfilePicture", "error", "Error", "message", "handleProfilePictureRemove", "removeProfilePicture", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "className", "background", "borderRadius", "padding", "boxShadow", "border", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alignItems", "marginBottom", "flexWrap", "flexShrink", "currentPicture", "profilePicture", "undefined", "userInitials", "firstName", "char<PERSON>t", "lastName", "onUpload", "onRemove", "isLoading", "size", "flex", "min<PERSON><PERSON><PERSON>", "paddingTop", "middleName", "suffix", "gridTemplateColumns", "textTransform", "role", "isActive", "width", "height", "department", "position", "phoneNumber", "textAlign", "lineHeight", "disabled", "cursor", "renderSystemSettings", "justifyContent", "type", "opacity", "top", "left", "right", "bottom", "transition", "defaultChecked", "renderContent", "map", "tab", "onClick", "transform", "onMouseEnter", "e", "currentTarget", "borderColor", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\n\nconst Settings: React.FC = () => {\n  const { user, checkAuthStatus } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'system' | 'security' | 'notifications'>('profile');\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile Settings', icon: User },\n    { key: 'system', label: 'System Settings', icon: SettingsIcon },\n    { key: 'security', label: 'Security', icon: Lock },\n    { key: 'notifications', label: 'Notifications', icon: Bell }\n  ];\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async (file: File) => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error: any) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Information Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 2rem 0',\n          color: '#2d5016',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        }}>\n          Profile Information\n        </h3>\n\n        {/* Horizontal Layout: Profile Picture + Profile Details */}\n        <div className=\"profile-layout\" style={{\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start',\n          marginBottom: '2rem',\n          flexWrap: 'wrap'\n        }}>\n          {/* Profile Picture (Left Side) */}\n          <div style={{ flexShrink: 0 }}>\n            <ProfilePictureUpload\n              currentPicture={user?.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined}\n              userInitials={`${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`}\n              onUpload={handleProfilePictureUpload}\n              onRemove={handleProfilePictureRemove}\n              isLoading={isUploadingPicture}\n              size={140}\n            />\n          </div>\n\n          {/* Profile Details (Right Side) */}\n          <div className=\"profile-details\" style={{\n            flex: 1,\n            minWidth: '300px',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem',\n            paddingTop: '0.5rem'\n          }}>\n            {/* Name */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#6b7280',\n                marginBottom: '0.25rem'\n              }}>\n                Full Name\n              </label>\n              <div style={{\n                color: '#111827',\n                fontSize: '1.125rem',\n                fontWeight: '600',\n                marginBottom: '0.5rem'\n              }}>\n                {`${user?.firstName || ''} ${user?.middleName ? user.middleName + ' ' : ''}${user?.lastName || ''}${user?.suffix ? ' ' + user.suffix : ''}`}\n                \n              </div>\n            </div>\n\n            {/* Role and Status Row */}\n            <div className=\"role-status-grid\" style={{\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem'\n            }}>\n              {/* Role */}\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#6b7280',\n                  marginBottom: '0.25rem'\n                }}>\n                  Role\n                </label>\n                <div style={{\n                  color: '#16a34a',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  textTransform: 'capitalize',\n                  marginBottom: '0.5rem'\n                }}>\n                  {user?.role || 'Not assigned'}\n                </div>\n              </div>\n\n              {/* Status */}\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#6b7280',\n                  marginBottom: '0.25rem'\n                }}>\n                  Account Status\n                </label>\n                <div style={{\n                  color: user?.isActive ? '#16a34a' : '#dc2626',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <div style={{\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    background: user?.isActive ? '#16a34a' : '#dc2626'\n                  }} />\n                  {user?.isActive ? 'Active' : 'Inactive'}\n                </div>\n              </div>\n            </div>\n\n            {/* Additional Information */}\n            {(user?.department || user?.position || user?.phoneNumber) && (\n              <div className=\"profile-grid\" style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '1rem'\n              }}>\n                {user?.department && (\n                  <div>\n                    <label style={{\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      marginBottom: '0.25rem'\n                    }}>\n                      Department\n                    </label>\n                    <div style={{\n                      color: '#111827',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      marginBottom: '0.5rem'\n                    }}>\n                      {user.department}\n                    </div>\n                  </div>\n                )}\n\n                {user?.position && (\n                  <div>\n                    <label style={{\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      marginBottom: '0.25rem'\n                    }}>\n                      Position\n                    </label>\n                    <div style={{\n                      color: '#111827',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      marginBottom: '0.5rem'\n                    }}>\n                      {user.position}\n                    </div>\n                  </div>\n                )}\n\n                {user?.phoneNumber && (\n                  <div>\n                    <label style={{\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      marginBottom: '0.25rem'\n                    }}>\n                      Phone Number\n                    </label>\n                    <div style={{\n                      color: '#111827',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      marginBottom: '0.5rem'\n                    }}>\n                      {user.phoneNumber}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Password Change Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Security Settings\n        </h3>\n\n        <div style={{\n          padding: '2rem',\n          background: '#f9fafb',\n          borderRadius: '12px',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        }}>\n          <div style={{ marginBottom: '1rem' }}>\n            <Lock size={48} color=\"#6b7280\" />\n          </div>\n          <h4 style={{\n            color: '#374151',\n            fontSize: '1.125rem',\n            fontWeight: '600',\n            marginBottom: '0.5rem'\n          }}>\n            Password Management\n          </h4>\n          <p style={{\n            color: '#6b7280',\n            marginBottom: '1.5rem',\n            lineHeight: '1.5'\n          }}>\n            Password change functionality will be available soon. For now, please contact your system administrator if you need to update your password.\n          </p>\n          <button\n            disabled\n            style={{\n              background: '#e8f5e8',\n              color: '#9ca3af',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'not-allowed',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              margin: '0 auto'\n            }}\n          >\n            <Lock size={16} />\n            Change Password (Coming Soon)\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* General Settings */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          General Settings\n        </h3>\n        \n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Maintenance Mode\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Enable maintenance mode to restrict access\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Auto-approve Posts\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Automatically approve new posts without review\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Email Notifications\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Send email notifications for important events\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* System Information */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          System Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              System Version\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              v1.0.0\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Last Updated\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              June 28, 2025\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Database Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Connected\n              </span>\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Server Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Online\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Lock size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Security Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Security settings panel coming soon\n            </p>\n          </div>\n        );\n      case 'notifications':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Bell size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Notification Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Notification preferences panel coming soon\n            </p>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div>\n      {/* CSS for responsive design and animations */}\n      <style>{`\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `}</style>\n\n      {/* Tabs */}\n      <div className=\"fade-in\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div className=\"tab-container\" style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              className=\"tab-button\"\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key\n                  ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.3s ease',\n                transform: activeTab === tab.key ? 'translateY(-1px)' : 'translateY(0)',\n                boxShadow: activeTab === tab.key ? '0 4px 15px rgba(34, 197, 94, 0.3)' : 'none'\n              }}\n              onMouseEnter={(e) => {\n                if (activeTab !== tab.key) {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (activeTab !== tab.key) {\n                  e.currentTarget.style.borderColor = '#e8f5e8';\n                  e.currentTarget.style.background = 'transparent';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                }\n              }}\n            >\n              <tab.icon size={16} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"fade-in\">\n        {renderContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,IAAI,EAAEC,QAAQ,IAAIC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,QAAQ,cAAc;AACtF,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMR,QAAkB,GAAGA,CAAA,KAAM;EAAAS,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGb,YAAY,CAAC,CAAC;EAChD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAsD,SAAS,CAAC;EAC1G,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMmB,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEpB;EAAK,CAAC,EACzD;IAAEkB,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAElB;EAAa,CAAC,EAC/D;IAAEgB,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEjB;EAAK,CAAC,EAClD;IAAEe,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAEhB;EAAK,CAAC,CAC7D;;EAED;EACA,MAAMiB,0BAA0B,GAAG,MAAOC,IAAU,IAAK;IACvDN,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACFO,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,MAAMC,MAAM,GAAG,MAAMlB,gBAAgB,CAACmB,oBAAoB,CAACJ,IAAI,CAAC;MAChEC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,MAAM,CAAC;;MAEnD;MACAF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMZ,eAAe,CAAC,CAAC;MACvBW,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEb,IAAI,CAAC;IACrE,CAAC,CAAC,OAAOgB,KAAU,EAAE;MACnBJ,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRb,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMc,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7Cd,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMT,gBAAgB,CAACwB,oBAAoB,CAAC,CAAC;MAC7C;MACA,MAAMnB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOe,KAAU,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRb,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMgB,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5BzB,OAAA;MAAK0B,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpE9B,OAAA;QAAK+B,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,gBACA9B,OAAA;UAAI0B,KAAK,EAAE;YACTW,MAAM,EAAE,YAAY;YACpBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGL5C,OAAA;UAAK+B,SAAS,EAAC,gBAAgB;UAACL,KAAK,EAAE;YACrCC,OAAO,EAAE,MAAM;YACfE,GAAG,EAAE,MAAM;YACXgB,UAAU,EAAE,YAAY;YACxBC,YAAY,EAAE,MAAM;YACpBC,QAAQ,EAAE;UACZ,CAAE;UAAAjB,QAAA,gBAEA9B,OAAA;YAAK0B,KAAK,EAAE;cAAEsB,UAAU,EAAE;YAAE,CAAE;YAAAlB,QAAA,eAC5B9B,OAAA,CAACH,oBAAoB;cACnBoD,cAAc,EAAE/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgD,cAAc,GAAG,wBAAwBhD,IAAI,CAACgD,cAAc,EAAE,GAAGC,SAAU;cACjGC,YAAY,EAAE,GAAG,CAAAlD,IAAI,aAAJA,IAAI,wBAAAsB,eAAA,GAAJtB,IAAI,CAAEmD,SAAS,cAAA7B,eAAA,uBAAfA,eAAA,CAAiB8B,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAApD,IAAI,aAAJA,IAAI,wBAAAuB,cAAA,GAAJvB,IAAI,CAAEqD,QAAQ,cAAA9B,cAAA,uBAAdA,cAAA,CAAgB6B,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAG;cACtFE,QAAQ,EAAE5C,0BAA2B;cACrC6C,QAAQ,EAAEpC,0BAA2B;cACrCqC,SAAS,EAAEpD,kBAAmB;cAC9BqD,IAAI,EAAE;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5C,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAACL,KAAK,EAAE;cACtCkC,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjBlC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,GAAG,EAAE,QAAQ;cACbiC,UAAU,EAAE;YACd,CAAE;YAAAhC,QAAA,gBAEA9B,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAO0B,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBY,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBQ,YAAY,EAAE;gBAChB,CAAE;gBAAAhB,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5C,OAAA;gBAAK0B,KAAK,EAAE;kBACVY,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBM,YAAY,EAAE;gBAChB,CAAE;gBAAAhB,QAAA,EACC,GAAG,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,SAAS,KAAI,EAAE,IAAInD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6D,UAAU,GAAG7D,IAAI,CAAC6D,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,QAAQ,KAAI,EAAE,GAAGrD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8D,MAAM,GAAG,GAAG,GAAG9D,IAAI,CAAC8D,MAAM,GAAG,EAAE;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5C,OAAA;cAAK+B,SAAS,EAAC,kBAAkB;cAACL,KAAK,EAAE;gBACvCC,OAAO,EAAE,MAAM;gBACfsC,mBAAmB,EAAE,SAAS;gBAC9BpC,GAAG,EAAE;cACP,CAAE;cAAAC,QAAA,gBAEA9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAO0B,KAAK,EAAE;oBACZC,OAAO,EAAE,OAAO;oBAChBY,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE,SAAS;oBAChBQ,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAK0B,KAAK,EAAE;oBACVY,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE,KAAK;oBACjB0B,aAAa,EAAE,YAAY;oBAC3BpB,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EACC,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,IAAI,KAAI;gBAAc;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5C,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAO0B,KAAK,EAAE;oBACZC,OAAO,EAAE,OAAO;oBAChBY,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE,SAAS;oBAChBQ,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAK0B,KAAK,EAAE;oBACVY,KAAK,EAAEpC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkE,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC7C7B,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE,KAAK;oBACjBb,OAAO,EAAE,MAAM;oBACfkB,UAAU,EAAE,QAAQ;oBACpBhB,GAAG,EAAE,QAAQ;oBACbiB,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,gBACA9B,OAAA;oBAAK0B,KAAK,EAAE;sBACV2C,KAAK,EAAE,KAAK;sBACZC,MAAM,EAAE,KAAK;sBACbrC,YAAY,EAAE,KAAK;sBACnBD,UAAU,EAAE9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkE,QAAQ,GAAG,SAAS,GAAG;oBAC3C;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACJ1C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkE,QAAQ,GAAG,QAAQ,GAAG,UAAU;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAAC,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,UAAU,MAAIrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,QAAQ,MAAItE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,WAAW,mBACvDzE,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAACL,KAAK,EAAE;gBACnCC,OAAO,EAAE,MAAM;gBACfsC,mBAAmB,EAAE,sCAAsC;gBAC3DpC,GAAG,EAAE;cACP,CAAE;cAAAC,QAAA,GACC,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,UAAU,kBACfvE,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAO0B,KAAK,EAAE;oBACZC,OAAO,EAAE,OAAO;oBAChBY,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE,SAAS;oBAChBQ,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAK0B,KAAK,EAAE;oBACVY,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBM,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EACC5B,IAAI,CAACqE;gBAAU;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,QAAQ,kBACbxE,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAO0B,KAAK,EAAE;oBACZC,OAAO,EAAE,OAAO;oBAChBY,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE,SAAS;oBAChBQ,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAK0B,KAAK,EAAE;oBACVY,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBM,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EACC5B,IAAI,CAACsE;gBAAQ;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,WAAW,kBAChBzE,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAO0B,KAAK,EAAE;oBACZC,OAAO,EAAE,OAAO;oBAChBY,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE,SAAS;oBAChBQ,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAK0B,KAAK,EAAE;oBACVY,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBM,YAAY,EAAE;kBAChB,CAAE;kBAAAhB,QAAA,EACC5B,IAAI,CAACuE;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAK+B,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,gBACA9B,OAAA;UAAI0B,KAAK,EAAE;YACTW,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL5C,OAAA;UAAK0B,KAAK,EAAE;YACVQ,OAAO,EAAE,MAAM;YACfF,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpBG,MAAM,EAAE,mBAAmB;YAC3BsC,SAAS,EAAE;UACb,CAAE;UAAA5C,QAAA,gBACA9B,OAAA;YAAK0B,KAAK,EAAE;cAAEoB,YAAY,EAAE;YAAO,CAAE;YAAAhB,QAAA,eACnC9B,OAAA,CAACN,IAAI;cAACiE,IAAI,EAAE,EAAG;cAACrB,KAAK,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACN5C,OAAA;YAAI0B,KAAK,EAAE;cACTY,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBM,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAG0B,KAAK,EAAE;cACRY,KAAK,EAAE,SAAS;cAChBQ,YAAY,EAAE,QAAQ;cACtB6B,UAAU,EAAE;YACd,CAAE;YAAA7C,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ5C,OAAA;YACE4E,QAAQ;YACRlD,KAAK,EAAE;cACLM,UAAU,EAAE,SAAS;cACrBM,KAAK,EAAE,SAAS;cAChBF,MAAM,EAAE,MAAM;cACdH,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,gBAAgB;cACzBM,UAAU,EAAE,KAAK;cACjBqC,MAAM,EAAE,aAAa;cACrBlD,OAAO,EAAE,MAAM;cACfkB,UAAU,EAAE,QAAQ;cACpBhB,GAAG,EAAE,QAAQ;cACbQ,MAAM,EAAE;YACV,CAAE;YAAAP,QAAA,gBAEF9B,OAAA,CAACN,IAAI;cAACiE,IAAI,EAAE;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iCAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAMkC,oBAAoB,GAAGA,CAAA,kBAC3B9E,OAAA;IAAK0B,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEpE9B,OAAA;MAAK0B,KAAK,EAAE;QACVM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,gBACA9B,OAAA;QAAI0B,KAAK,EAAE;UACTW,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EAAC;MAEH;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5C,OAAA;QAAK0B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACtE9B,OAAA;UAAK0B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEoD,cAAc,EAAE,eAAe;YAAElC,UAAU,EAAE;UAAS,CAAE;UAAAf,QAAA,gBACrF9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK0B,KAAK,EAAE;gBAAEc,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEQ,YAAY,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAAC;YAE9E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA;cAAK0B,KAAK,EAAE;gBAAEY,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,EAAC;YAExD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAO0B,KAAK,EAAE;cAAE8C,QAAQ,EAAE,UAAU;cAAE7C,OAAO,EAAE,cAAc;cAAE0C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAxC,QAAA,gBAC7F9B,OAAA;cAAOgF,IAAI,EAAC,UAAU;cAACtD,KAAK,EAAE;gBAAEuD,OAAO,EAAE,CAAC;gBAAEZ,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE5C,OAAA;cAAM0B,KAAK,EAAE;gBACX8C,QAAQ,EAAE,UAAU;gBACpBK,MAAM,EAAE,SAAS;gBACjBK,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACTrD,UAAU,EAAE,MAAM;gBAClBsD,UAAU,EAAE,MAAM;gBAClBrD,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5C,OAAA;UAAK0B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEoD,cAAc,EAAE,eAAe;YAAElC,UAAU,EAAE;UAAS,CAAE;UAAAf,QAAA,gBACrF9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK0B,KAAK,EAAE;gBAAEc,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEQ,YAAY,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAAC;YAE9E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA;cAAK0B,KAAK,EAAE;gBAAEY,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,EAAC;YAExD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAO0B,KAAK,EAAE;cAAE8C,QAAQ,EAAE,UAAU;cAAE7C,OAAO,EAAE,cAAc;cAAE0C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAxC,QAAA,gBAC7F9B,OAAA;cAAOgF,IAAI,EAAC,UAAU;cAACtD,KAAK,EAAE;gBAAEuD,OAAO,EAAE,CAAC;gBAAEZ,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE5C,OAAA;cAAM0B,KAAK,EAAE;gBACX8C,QAAQ,EAAE,UAAU;gBACpBK,MAAM,EAAE,SAAS;gBACjBK,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACTrD,UAAU,EAAE,SAAS;gBACrBsD,UAAU,EAAE,MAAM;gBAClBrD,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5C,OAAA;UAAK0B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEoD,cAAc,EAAE,eAAe;YAAElC,UAAU,EAAE;UAAS,CAAE;UAAAf,QAAA,gBACrF9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAK0B,KAAK,EAAE;gBAAEc,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEQ,YAAY,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAAC;YAE9E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA;cAAK0B,KAAK,EAAE;gBAAEY,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,EAAC;YAExD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAO0B,KAAK,EAAE;cAAE8C,QAAQ,EAAE,UAAU;cAAE7C,OAAO,EAAE,cAAc;cAAE0C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAxC,QAAA,gBAC7F9B,OAAA;cAAOgF,IAAI,EAAC,UAAU;cAACO,cAAc;cAAC7D,KAAK,EAAE;gBAAEuD,OAAO,EAAE,CAAC;gBAAEZ,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpF5C,OAAA;cAAM0B,KAAK,EAAE;gBACX8C,QAAQ,EAAE,UAAU;gBACpBK,MAAM,EAAE,SAAS;gBACjBK,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACTrD,UAAU,EAAE,SAAS;gBACrBsD,UAAU,EAAE,MAAM;gBAClBrD,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAK0B,KAAK,EAAE;QACVM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,gBACA9B,OAAA;QAAI0B,KAAK,EAAE;UACTW,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EAAC;MAEH;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5C,OAAA;QAAK0B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEsC,mBAAmB,EAAE,SAAS;UAAEpC,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAC7E9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAK0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEO,YAAY,EAAE;YAAU,CAAE;YAAAhB,QAAA,EAAC;UAEjF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAK0B,KAAK,EAAE;cAAEc,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5C,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAK0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEO,YAAY,EAAE;YAAU,CAAE;YAAAhB,QAAA,EAAC;UAEjF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAK0B,KAAK,EAAE;cAAEc,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5C,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAK0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEO,YAAY,EAAE;YAAU,CAAE;YAAAhB,QAAA,EAAC;UAEjF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAK0B,KAAK,EAAE;cAAEc,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,eAClD9B,OAAA;cAAM0B,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEkB,UAAU,EAAE,QAAQ;gBAAEhB,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE9B,OAAA,CAACJ,WAAW;gBAAC+D,IAAI,EAAE,EAAG;gBAACrB,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5C,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAK0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEO,YAAY,EAAE;YAAU,CAAE;YAAAhB,QAAA,EAAC;UAEjF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAK0B,KAAK,EAAE;cAAEc,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,eAClD9B,OAAA;cAAM0B,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEkB,UAAU,EAAE,QAAQ;gBAAEhB,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE9B,OAAA,CAACJ,WAAW;gBAAC+D,IAAI,EAAE,EAAG;gBAACrB,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM4C,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQpF,SAAS;MACf,KAAK,SAAS;QACZ,OAAOmB,qBAAqB,CAAC,CAAC;MAChC,KAAK,QAAQ;QACX,OAAOuD,oBAAoB,CAAC,CAAC;MAC/B,KAAK,UAAU;QACb,oBACE9E,OAAA;UAAK0B,KAAK,EAAE;YACVM,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3BsC,SAAS,EAAE;UACb,CAAE;UAAA5C,QAAA,gBACA9B,OAAA;YAAK0B,KAAK,EAAE;cAAEoB,YAAY,EAAE;YAAO,CAAE;YAAAhB,QAAA,eACnC9B,OAAA,CAACN,IAAI;cAACiE,IAAI,EAAE,EAAG;cAACrB,KAAK,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACN5C,OAAA;YAAI0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEM,YAAY,EAAE;YAAS,CAAE;YAAAhB,QAAA,EAAC;UAEhG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAG0B,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEhC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV,KAAK,eAAe;QAClB,oBACE5C,OAAA;UAAK0B,KAAK,EAAE;YACVM,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3BsC,SAAS,EAAE;UACb,CAAE;UAAA5C,QAAA,gBACA9B,OAAA;YAAK0B,KAAK,EAAE;cAAEoB,YAAY,EAAE;YAAO,CAAE;YAAAhB,QAAA,eACnC9B,OAAA,CAACL,IAAI;cAACgE,IAAI,EAAE,EAAG;cAACrB,KAAK,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACN5C,OAAA;YAAI0B,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEM,YAAY,EAAE;YAAS,CAAE;YAAAhB,QAAA,EAAC;UAEhG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAG0B,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEhC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE5C,OAAA;IAAA8B,QAAA,gBAEE9B,OAAA;MAAA8B,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGV5C,OAAA;MAAK+B,SAAS,EAAC,SAAS;MAACL,KAAK,EAAE;QAC9BM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBY,YAAY,EAAE,MAAM;QACpBX,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,eACA9B,OAAA;QAAK+B,SAAS,EAAC,eAAe;QAACL,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEkB,QAAQ,EAAE;QAAO,CAAE;QAAAjB,QAAA,EACtFtB,IAAI,CAACiF,GAAG,CAACC,GAAG,iBACX1F,OAAA;UAEE+B,SAAS,EAAC,YAAY;UACtB4D,OAAO,EAAEA,CAAA,KAAMtF,YAAY,CAACqF,GAAG,CAACjF,GAAU,CAAE;UAC5CiB,KAAK,EAAE;YACLM,UAAU,EAAE5B,SAAS,KAAKsF,GAAG,CAACjF,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjB6B,KAAK,EAAElC,SAAS,KAAKsF,GAAG,CAACjF,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD2B,MAAM,EAAEhC,SAAS,KAAKsF,GAAG,CAACjF,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5DwB,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzB2C,MAAM,EAAE,SAAS;YACjBrC,UAAU,EAAE,KAAK;YACjBb,OAAO,EAAE,MAAM;YACfkB,UAAU,EAAE,QAAQ;YACpBhB,GAAG,EAAE,QAAQ;YACbyD,UAAU,EAAE,eAAe;YAC3BM,SAAS,EAAExF,SAAS,KAAKsF,GAAG,CAACjF,GAAG,GAAG,kBAAkB,GAAG,eAAe;YACvE0B,SAAS,EAAE/B,SAAS,KAAKsF,GAAG,CAACjF,GAAG,GAAG,mCAAmC,GAAG;UAC3E,CAAE;UACFoF,YAAY,EAAGC,CAAC,IAAK;YACnB,IAAI1F,SAAS,KAAKsF,GAAG,CAACjF,GAAG,EAAE;cACzBqF,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACsE,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACM,UAAU,GAAG,SAAS;cAC5C8D,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACkE,SAAS,GAAG,kBAAkB;YACtD;UACF,CAAE;UACFK,YAAY,EAAGH,CAAC,IAAK;YACnB,IAAI1F,SAAS,KAAKsF,GAAG,CAACjF,GAAG,EAAE;cACzBqF,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACsE,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACM,UAAU,GAAG,aAAa;cAChD8D,CAAC,CAACC,aAAa,CAACrE,KAAK,CAACkE,SAAS,GAAG,eAAe;YACnD;UACF,CAAE;UAAA9D,QAAA,gBAEF9B,OAAA,CAAC0F,GAAG,CAAC/E,IAAI;YAACgD,IAAI,EAAE;UAAG;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrB8C,GAAG,CAAChF,KAAK;QAAA,GApCLgF,GAAG,CAACjF,GAAG;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqCN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAK+B,SAAS,EAAC,SAAS;MAAAD,QAAA,EACrB0D,aAAa,CAAC;IAAC;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CArpBIT,QAAkB;EAAA,QACYF,YAAY;AAAA;AAAA4G,EAAA,GAD1C1G,QAAkB;AAupBxB,eAAeA,QAAQ;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}