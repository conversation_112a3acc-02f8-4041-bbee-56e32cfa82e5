{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 15-5.5 5.5L9 18\",\n  key: \"15q87x\"\n}], [\"path\", {\n  d: \"M5 17.743A7 7 0 1 1 15.71 10h1.79a4.5 4.5 0 0 1 1.5 8.742\",\n  key: \"9ho6ki\"\n}]];\nconst CloudCheck = createLucideIcon(\"cloud-check\", __iconNode);\nexport { __iconNode, CloudCheck as default };\n//# sourceMappingURL=cloud-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}