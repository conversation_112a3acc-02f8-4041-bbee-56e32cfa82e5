{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"18.5\",\n  cy: \"17.5\",\n  r: \"3.5\",\n  key: \"15x4ox\"\n}], [\"circle\", {\n  cx: \"5.5\",\n  cy: \"17.5\",\n  r: \"3.5\",\n  key: \"1noe27\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"19l28e\"\n}], [\"path\", {\n  d: \"M12 17.5V14l-3-3 4-3 2 3h2\",\n  key: \"1npguv\"\n}]];\nconst Bike = createLucideIcon(\"bike\", __iconNode);\nexport { __iconNode, Bike as default };\n//# sourceMappingURL=bike.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}