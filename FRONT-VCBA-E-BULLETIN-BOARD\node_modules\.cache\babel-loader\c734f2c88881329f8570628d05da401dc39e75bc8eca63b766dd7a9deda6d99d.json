{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 21a8 8 0 0 1 13.292-6\",\n  key: \"bjp14o\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"path\", {\n  d: \"M19 16v6\",\n  key: \"tddt3s\"\n}], [\"path\", {\n  d: \"M22 19h-6\",\n  key: \"vcuq98\"\n}]];\nconst UserRoundPlus = createLucideIcon(\"user-round-plus\", __iconNode);\nexport { __iconNode, UserRoundPlus as default };\n//# sourceMappingURL=user-round-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}