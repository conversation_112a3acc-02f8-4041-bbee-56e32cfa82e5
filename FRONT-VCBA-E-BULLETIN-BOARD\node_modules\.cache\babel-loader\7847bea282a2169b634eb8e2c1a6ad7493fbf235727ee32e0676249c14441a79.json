{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.162 3.167A10 10 0 0 0 2 13a2 2 0 0 0 4 0v-1a2 2 0 0 1 4 0v4a2 2 0 0 0 4 0v-4a2 2 0 0 1 4 0v1a2 2 0 0 0 4-.006 10 10 0 0 0-8.161-9.826\",\n  key: \"xi88qy\"\n}], [\"path\", {\n  d: \"M20.804 14.869a9 9 0 0 1-17.608 0\",\n  key: \"1r28rg\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"4\",\n  r: \"2\",\n  key: \"muu5ef\"\n}]];\nconst Dessert = createLucideIcon(\"dessert\", __iconNode);\nexport { __iconNode, Dessert as default };\n//# sourceMappingURL=dessert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}