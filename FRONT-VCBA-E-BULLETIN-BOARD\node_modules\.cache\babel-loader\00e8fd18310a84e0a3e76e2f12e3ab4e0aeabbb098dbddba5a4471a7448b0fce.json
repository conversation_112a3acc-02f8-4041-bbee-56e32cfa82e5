{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2\",\n  key: \"1vzg26\"\n}], [\"path\", {\n  d: \"M10 22H8\",\n  key: \"euku7a\"\n}], [\"path\", {\n  d: \"M16 22h-2\",\n  key: \"18d249\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"14cg06\"\n}], [\"path\", {\n  d: \"M9.414 9.414 12 12\",\n  key: \"qz4lzr\"\n}], [\"path\", {\n  d: \"M14.8 14.8 18 18\",\n  key: \"11flf1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1acxsx\"\n}], [\"path\", {\n  d: \"m18 6-8.586 8.586\",\n  key: \"11kzk1\"\n}]];\nconst SquareBottomDashedScissors = createLucideIcon(\"square-bottom-dashed-scissors\", __iconNode);\nexport { __iconNode, SquareBottomDashedScissors as default };\n//# sourceMappingURL=square-bottom-dashed-scissors.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}