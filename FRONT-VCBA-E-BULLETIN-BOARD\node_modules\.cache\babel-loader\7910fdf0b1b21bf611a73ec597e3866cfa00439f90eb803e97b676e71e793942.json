{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2Z\",\n  key: \"169p4p\"\n}], [\"path\", {\n  d: \"m9 10 2 2 4-4\",\n  key: \"1gnqz4\"\n}]];\nconst BookmarkCheck = createLucideIcon(\"bookmark-check\", __iconNode);\nexport { __iconNode, BookmarkCheck as default };\n//# sourceMappingURL=bookmark-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}