{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\",\n  key: \"1cjeqo\"\n}], [\"path\", {\n  d: \"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\",\n  key: \"19qd67\"\n}]];\nconst Link = createLucideIcon(\"link\", __iconNode);\nexport { __iconNode, Link as default };\n//# sourceMappingURL=link.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}