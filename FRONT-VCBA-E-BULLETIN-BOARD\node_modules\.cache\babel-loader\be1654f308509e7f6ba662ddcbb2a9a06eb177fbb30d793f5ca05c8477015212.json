{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 10V7l-5-5H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h4\",\n  key: \"1rdf37\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M16 14a2 2 0 0 0-2 2\",\n  key: \"ceaadl\"\n}], [\"path\", {\n  d: \"M20 14a2 2 0 0 1 2 2\",\n  key: \"1ny6zw\"\n}], [\"path\", {\n  d: \"M20 22a2 2 0 0 0 2-2\",\n  key: \"1l9q4k\"\n}], [\"path\", {\n  d: \"M16 22a2 2 0 0 1-2-2\",\n  key: \"1wqh5n\"\n}]];\nconst FileScan = createLucideIcon(\"file-scan\", __iconNode);\nexport { __iconNode, FileScan as default };\n//# sourceMappingURL=file-scan.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}