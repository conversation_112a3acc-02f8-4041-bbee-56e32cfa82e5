{"version": 3, "file": "inferSingleRun.js", "sourceRoot": "", "sources": ["../../src/parseSettings/inferSingleRun.ts"], "names": [], "mappings": ";;;AAAA,+BAAiC;AAIjC;;;;;;;;;;;GAWG;AACH,SAAgB,cAAc,CAAC,OAAoC;IACjE;IACE,2FAA2F;IAC3F,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,KAAI,IAAI;QACxB,8FAA8F;QAC9F,uDAAuD;QACvD,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,KAAI,IAAI,EACzB;QACA,OAAO,KAAK,CAAC;KACd;IAED,gHAAgH;IAChH,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,OAAO,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IACD,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,MAAM,EAAE;QAC9C,OAAO,IAAI,CAAC;KACb;IAED,8DAA8D;IAC9D,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gCAAgC,EAAE;QAC7C;QACE,2FAA2F;QAC3F,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,MAAM;YACzB,kGAAkG;YAClG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAA,gBAAS,EAAC,0BAA0B,CAAC,CAAC,EAC/D;YACA,OAAO,IAAI,CAAC;SACb;KACF;IAED;;;;;;OAMG;IACH,OAAO,KAAK,CAAC;AACf,CAAC;AAvCD,wCAuCC"}