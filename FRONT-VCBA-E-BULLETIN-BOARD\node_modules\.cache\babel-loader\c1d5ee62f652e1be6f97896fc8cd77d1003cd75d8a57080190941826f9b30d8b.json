{"ast": null, "code": "/**\n * Form utility functions for consistent form handling across the application\n */\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    if (key === 'category_id' && typeof value === 'string') {\n      formDataToSubmit.append(key, parseInt(value).toString());\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty\n      if (value.trim() !== '') {\n        formDataToSubmit.append(key, parseInt(value).toString());\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n    } else if (value !== null && value !== undefined) {\n      formDataToSubmit.append(key, value.toString());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "append", "parseInt", "toString", "trim", "status", "undefined", "length", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "join", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\n * Form utility functions for consistent form handling across the application\n */\n\nexport interface FormField {\n  [key: string]: string | number | boolean | null | undefined;\n}\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (\n  formData: FormField,\n  files: File[] = [],\n  options: {\n    skipScheduledDate?: boolean;\n    fileFieldName?: string;\n  } = {}\n): FormData => {\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    if (key === 'category_id' && typeof value === 'string') {\n      formDataToSubmit.append(key, parseInt(value).toString());\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty\n      if (value.trim() !== '') {\n        formDataToSubmit.append(key, parseInt(value).toString());\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n    } else if (value !== null && value !== undefined) {\n      formDataToSubmit.append(key, value.toString());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach((file) => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (\n  formData: FormField,\n  rules: {\n    required?: string[];\n    maxLength?: { [key: string]: number };\n    custom?: { [key: string]: (value: any) => string | null };\n  } = {}\n): Record<string, string> => {\n  const errors: Record<string, string> = {};\n  const { required = [], maxLength = {}, custom = {} } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || (typeof value === 'string' && !value.trim())) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: { title: 255 },\n  custom: {\n    scheduled_publish_at: (value: any, formData?: FormField) => {\n      if (formData?.status === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (\n  file: File,\n  options: {\n    maxSize?: number;\n    allowedTypes?: string[];\n  } = {}\n): string | null => {\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\n\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n\n  return null;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACAC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,CAACS,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD,IAAID,GAAG,KAAK,aAAa,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MACtDN,gBAAgB,CAACO,MAAM,CAACF,GAAG,EAAEG,QAAQ,CAACF,KAAK,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIJ,GAAG,KAAK,gBAAgB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvBV,gBAAgB,CAACO,MAAM,CAACF,GAAG,EAAEG,QAAQ,CAACF,KAAK,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAC;MAC1D;IACF,CAAC,MAAM,IAAIJ,GAAG,KAAK,sBAAsB,IAAIP,iBAAiB,IAAIH,QAAQ,CAACgB,MAAM,KAAK,WAAW,EAAE;MACjG;MACA;IACF,CAAC,MAAM,IAAI,OAAOL,KAAK,KAAK,SAAS,EAAE;MACrCN,gBAAgB,CAACO,MAAM,CAACF,GAAG,EAAEC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;IACjD,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKM,SAAS,EAAE;MAChDZ,gBAAgB,CAACO,MAAM,CAACF,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;IAChD;EACF,CAAC,CAAC;;EAEF;EACA,IAAIb,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;IACpBjB,KAAK,CAACQ,OAAO,CAAEU,IAAI,IAAK;MACtBd,gBAAgB,CAACO,MAAM,CAACR,aAAa,EAAEe,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAOd,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,kBAAkB,GAAGA,CAChCpB,QAAmB,EACnBqB,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACd,OAAO,CAACiB,KAAK,IAAI;IACxB,MAAMf,KAAK,GAAGX,QAAQ,CAAC0B,KAAK,CAAC;IAC7B,IAAI,CAACf,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACI,IAAI,CAAC,CAAE,EAAE;MAC1DO,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACApB,MAAM,CAACC,OAAO,CAACgB,SAAS,CAAC,CAACf,OAAO,CAAC,CAAC,CAACiB,KAAK,EAAEE,GAAG,CAAC,KAAK;IAClD,MAAMjB,KAAK,GAAGX,QAAQ,CAAC0B,KAAK,CAAC;IAC7B,IAAI,OAAOf,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACO,MAAM,GAAGU,GAAG,EAAE;MACnDN,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACArB,MAAM,CAACC,OAAO,CAACiB,MAAM,CAAC,CAAChB,OAAO,CAAC,CAAC,CAACiB,KAAK,EAAEG,SAAS,CAAC,KAAK;IACrD,MAAMlB,KAAK,GAAGX,QAAQ,CAAC0B,KAAK,CAAC;IAC7B,MAAMI,KAAK,GAAGD,SAAS,CAAClB,KAAK,CAAC;IAC9B,IAAImB,KAAK,EAAE;MACTR,MAAM,CAACI,KAAK,CAAC,GAAGI,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,2BAA2B,GAAG;EACzCR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEQ,KAAK,EAAE;EAAI,CAAC;EACzBP,MAAM,EAAE;IACNQ,oBAAoB,EAAEA,CAACtB,KAAU,EAAEX,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgB,MAAM,MAAK,WAAW,IAAI,CAACL,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuB,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACR,KAAK,CAAC,GAAGM,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,CAAC;EAEnD,OAAOO,UAAU,CAAC,CAACT,KAAK,GAAGM,IAAI,CAACI,GAAG,CAACR,CAAC,EAAEG,CAAC,CAAC,EAAEM,OAAO,CAACR,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,YAAY,GAAGA,CAC1B5B,IAAU,EACVjB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAE8C,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAG/C,OAAO;EAEpH,IAAI,CAAC+C,YAAY,CAACC,QAAQ,CAAC/B,IAAI,CAACgC,IAAI,CAAC,EAAE;IACrC,OAAO,aAAahC,IAAI,CAACgC,IAAI,qCAAqCF,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIjC,IAAI,CAACkC,IAAI,GAAGL,OAAO,EAAE;IACvB,OAAO,aAAad,cAAc,CAACf,IAAI,CAACkC,IAAI,CAAC,oCAAoCnB,cAAc,CAACc,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}