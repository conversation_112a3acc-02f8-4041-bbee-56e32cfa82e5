{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{ChevronRight,Users,BookOpen,Calendar,Award,ArrowRight}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WelcomePage=()=>{const navigate=useNavigate();const[isLoaded,setIsLoaded]=useState(false);const[scrollY,setScrollY]=useState(0);useEffect(()=>{setIsLoaded(true);const handleScroll=()=>{setScrollY(window.scrollY);};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const handleStudentLogin=()=>{navigate('/student/login');};const features=[{icon:/*#__PURE__*/_jsx(BookOpen,{size:24}),title:\"Academic Excellence\",description:\"Access to quality education and comprehensive learning resources\"},{icon:/*#__PURE__*/_jsx(Users,{size:24}),title:\"Vibrant Community\",description:\"Join a diverse community of students, faculty, and staff\"},{icon:/*#__PURE__*/_jsx(Calendar,{size:24}),title:\"Campus Events\",description:\"Stay updated with the latest announcements and campus activities\"},{icon:/*#__PURE__*/_jsx(Award,{size:24}),title:\"Achievement Recognition\",description:\"Celebrate academic and extracurricular accomplishments\"}];return/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-page\",children:[/*#__PURE__*/_jsxs(\"section\",{className:\"hero-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"hero-background\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"hero-overlay\"}),/*#__PURE__*/_jsx(\"img\",{src:\"/villamor-image/villamor-collge-BG-landscape.jpg\",alt:\"Villamor College Campus\",className:\"hero-image\",style:{transform:\"translateY(\".concat(scrollY*0.5,\"px)\")}})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hero-content\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hero-inner\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-section \".concat(isLoaded?'animate-fade-in':''),children:/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"Villamor College of Business and Arts\",className:\"college-logo\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"main-content \".concat(isLoaded?'animate-fade-in':''),children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"hero-title\",children:[\"Welcome to\",/*#__PURE__*/_jsx(\"span\",{className:\"title-highlight\",children:\"Villamor College\"}),\"of Business and Arts\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"hero-subtitle\",children:\"Your gateway to academic excellence and personal growth. Stay connected with the latest announcements, events, and opportunities that shape your educational journey.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"cta-section\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleStudentLogin,className:\"cta-button\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Student Portal\"}),/*#__PURE__*/_jsx(ArrowRight,{size:20})]}),/*#__PURE__*/_jsx(\"p\",{className:\"cta-subtitle\",children:\"Access your personalized dashboard, announcements, and campus updates\"})]})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"scroll-indicator\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"scroll-arrow\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Scroll to explore\"})]})]}),/*#__PURE__*/_jsx(\"section\",{className:\"features-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"features-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Why Choose Villamor College?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Discover what makes our institution a leader in business and arts education\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"features-grid\",children:features.map((feature,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"feature-card \".concat(isLoaded?'animate-fade-in':''),style:{animationDelay:\"\".concat(index*0.1,\"s\")},children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:feature.icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"feature-title\",children:feature.title}),/*#__PURE__*/_jsx(\"p\",{className:\"feature-description\",children:feature.description})]},index))})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"final-cta-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"final-cta-content\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Ready to Get Started?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Join thousands of students who trust Villamor College for their educational journey\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleStudentLogin,className:\"secondary-cta-button\",children:[\"Access Student Portal\",/*#__PURE__*/_jsx(ChevronRight,{size:20})]})]})})}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        .welcome-page {\\n          min-height: 100vh;\\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\\n        }\\n\\n        /* Hero Section */\\n        .hero-section {\\n          position: relative;\\n          min-height: 100vh;\\n          display: flex;\\n          align-items: center;\\n          overflow: hidden;\\n        }\\n\\n        .hero-background {\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          z-index: 1;\\n        }\\n\\n        .hero-image {\\n          width: 100%;\\n          height: 100%;\\n          object-fit: cover;\\n          object-position: center;\\n        }\\n\\n        .hero-overlay {\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background: linear-gradient(\\n            135deg,\\n            rgba(34, 197, 94, 0.9) 0%,\\n            rgba(21, 128, 61, 0.8) 50%,\\n            rgba(20, 83, 45, 0.9) 100%\\n          );\\n          z-index: 2;\\n        }\\n\\n        .hero-content {\\n          position: relative;\\n          z-index: 3;\\n          width: 100%;\\n          padding: 2rem 0;\\n        }\\n\\n        /* Scroll Indicator */\\n        .scroll-indicator {\\n          position: absolute;\\n          bottom: 2rem;\\n          left: 50%;\\n          transform: translateX(-50%);\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          gap: 0.5rem;\\n          color: rgba(255, 255, 255, 0.8);\\n          font-size: 0.875rem;\\n          z-index: 4;\\n          animation: bounce 2s infinite;\\n        }\\n\\n        .scroll-arrow {\\n          width: 24px;\\n          height: 24px;\\n          border: 2px solid rgba(255, 255, 255, 0.8);\\n          border-radius: 50%;\\n          position: relative;\\n        }\\n\\n        .scroll-arrow::after {\\n          content: '';\\n          position: absolute;\\n          top: 50%;\\n          left: 50%;\\n          transform: translate(-50%, -50%) rotate(45deg);\\n          width: 8px;\\n          height: 8px;\\n          border-right: 2px solid rgba(255, 255, 255, 0.8);\\n          border-bottom: 2px solid rgba(255, 255, 255, 0.8);\\n        }\\n\\n        @keyframes bounce {\\n          0%, 20%, 50%, 80%, 100% {\\n            transform: translateX(-50%) translateY(0);\\n          }\\n          40% {\\n            transform: translateX(-50%) translateY(-10px);\\n          }\\n          60% {\\n            transform: translateX(-50%) translateY(-5px);\\n          }\\n        }\\n\\n        .container {\\n          max-width: 1280px;\\n          margin: 0 auto;\\n          padding: 0 1rem;\\n        }\\n\\n        .hero-inner {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          text-align: center;\\n          gap: 3rem;\\n        }\\n\\n        /* Logo Section */\\n        .logo-section {\\n          opacity: 0;\\n          transform: translateY(30px);\\n          transition: all 0.8s ease-out;\\n        }\\n\\n        .logo-section.animate-fade-in {\\n          opacity: 1;\\n          transform: translateY(0);\\n        }\\n\\n        .college-logo {\\n          height: 120px;\\n          width: auto;\\n          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\\n        }\\n\\n        /* Main Content */\\n        .main-content {\\n          max-width: 800px;\\n          opacity: 0;\\n          transform: translateY(30px);\\n          transition: all 0.8s ease-out 0.2s;\\n        }\\n\\n        .main-content.animate-fade-in {\\n          opacity: 1;\\n          transform: translateY(0);\\n        }\\n\\n        .hero-title {\\n          font-size: 3.5rem;\\n          font-weight: 700;\\n          color: white;\\n          margin-bottom: 1.5rem;\\n          line-height: 1.1;\\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n        }\\n\\n        .title-highlight {\\n          display: block;\\n          color: #fbbf24;\\n          font-size: 4rem;\\n          margin: 0.5rem 0;\\n        }\\n\\n        .hero-subtitle {\\n          font-size: 1.25rem;\\n          color: rgba(255, 255, 255, 0.9);\\n          margin-bottom: 3rem;\\n          line-height: 1.6;\\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n        }\\n\\n        /* CTA Section */\\n        .cta-section {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          gap: 1rem;\\n        }\\n\\n        .cta-button {\\n          display: flex;\\n          align-items: center;\\n          gap: 0.75rem;\\n          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\\n          color: #1f2937;\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          padding: 1rem 2rem;\\n          border: none;\\n          border-radius: 50px;\\n          cursor: pointer;\\n          transition: all 0.3s ease;\\n          box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);\\n          text-transform: uppercase;\\n          letter-spacing: 0.5px;\\n        }\\n\\n        .cta-button:hover {\\n          transform: translateY(-2px);\\n          box-shadow: 0 8px 25px rgba(251, 191, 36, 0.6);\\n          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\\n        }\\n\\n        .cta-subtitle {\\n          color: rgba(255, 255, 255, 0.8);\\n          font-size: 0.95rem;\\n          text-align: center;\\n          max-width: 400px;\\n        }\\n\\n        /* Features Section */\\n        .features-section {\\n          padding: 6rem 0;\\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);\\n          position: relative;\\n          overflow: hidden;\\n        }\\n\\n        .features-section::before {\\n          content: '';\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background:\\n            radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),\\n            radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);\\n          pointer-events: none;\\n        }\\n\\n        .features-header {\\n          text-align: center;\\n          margin-bottom: 4rem;\\n        }\\n\\n        .features-header h2 {\\n          font-size: 2.5rem;\\n          font-weight: 700;\\n          color: #1f2937;\\n          margin-bottom: 1rem;\\n        }\\n\\n        .features-header p {\\n          font-size: 1.125rem;\\n          color: #6b7280;\\n          max-width: 600px;\\n          margin: 0 auto;\\n        }\\n\\n        .features-grid {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n          gap: 2rem;\\n          margin-top: 3rem;\\n        }\\n\\n        .feature-card {\\n          background: rgba(255, 255, 255, 0.95);\\n          backdrop-filter: blur(10px);\\n          padding: 2rem;\\n          border-radius: 20px;\\n          text-align: center;\\n          box-shadow:\\n            0 8px 32px rgba(0, 0, 0, 0.1),\\n            0 1px 0 rgba(255, 255, 255, 0.5) inset;\\n          border: 1px solid rgba(255, 255, 255, 0.2);\\n          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n          opacity: 0;\\n          transform: translateY(30px);\\n          position: relative;\\n          overflow: hidden;\\n        }\\n\\n        .feature-card::before {\\n          content: '';\\n          position: absolute;\\n          top: 0;\\n          left: -100%;\\n          width: 100%;\\n          height: 100%;\\n          background: linear-gradient(\\n            90deg,\\n            transparent,\\n            rgba(255, 255, 255, 0.2),\\n            transparent\\n          );\\n          transition: left 0.5s ease;\\n        }\\n\\n        .feature-card:hover::before {\\n          left: 100%;\\n        }\\n\\n        .feature-card.animate-fade-in {\\n          opacity: 1;\\n          transform: translateY(0);\\n        }\\n\\n        .feature-card:hover {\\n          transform: translateY(-8px) scale(1.02);\\n          box-shadow:\\n            0 20px 40px rgba(0, 0, 0, 0.15),\\n            0 1px 0 rgba(255, 255, 255, 0.6) inset;\\n          border-color: rgba(34, 197, 94, 0.3);\\n        }\\n\\n        .feature-icon {\\n          width: 60px;\\n          height: 60px;\\n          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\\n          border-radius: 16px;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          margin: 0 auto 1.5rem;\\n          color: white;\\n        }\\n\\n        .feature-title {\\n          font-size: 1.25rem;\\n          font-weight: 600;\\n          color: #1f2937;\\n          margin-bottom: 1rem;\\n        }\\n\\n        .feature-description {\\n          color: #6b7280;\\n          line-height: 1.6;\\n        }\\n\\n        /* Final CTA Section */\\n        .final-cta-section {\\n          padding: 4rem 0;\\n          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n        }\\n\\n        .final-cta-content {\\n          text-align: center;\\n          color: white;\\n        }\\n\\n        .final-cta-content h2 {\\n          font-size: 2.25rem;\\n          font-weight: 700;\\n          margin-bottom: 1rem;\\n          color: white;\\n        }\\n\\n        .final-cta-content p {\\n          font-size: 1.125rem;\\n          color: rgba(255, 255, 255, 0.8);\\n          margin-bottom: 2rem;\\n          max-width: 500px;\\n          margin-left: auto;\\n          margin-right: auto;\\n        }\\n\\n        .secondary-cta-button {\\n          display: inline-flex;\\n          align-items: center;\\n          gap: 0.5rem;\\n          background: transparent;\\n          color: #22c55e;\\n          font-size: 1.125rem;\\n          font-weight: 600;\\n          padding: 1rem 2rem;\\n          border: 2px solid #22c55e;\\n          border-radius: 50px;\\n          cursor: pointer;\\n          transition: all 0.3s ease;\\n        }\\n\\n        .secondary-cta-button:hover {\\n          background: #22c55e;\\n          color: white;\\n          transform: translateY(-2px);\\n        }\\n\\n        /* Responsive Design */\\n        @media (max-width: 1024px) {\\n          .hero-title {\\n            font-size: 3rem;\\n          }\\n\\n          .title-highlight {\\n            font-size: 3.5rem;\\n          }\\n\\n          .features-grid {\\n            grid-template-columns: repeat(2, 1fr);\\n          }\\n        }\\n\\n        @media (max-width: 768px) {\\n          .container {\\n            padding: 0 1rem;\\n          }\\n\\n          .hero-inner {\\n            gap: 2rem;\\n          }\\n\\n          .hero-title {\\n            font-size: 2.5rem;\\n          }\\n\\n          .title-highlight {\\n            font-size: 3rem;\\n          }\\n\\n          .hero-subtitle {\\n            font-size: 1.125rem;\\n            margin-bottom: 2rem;\\n          }\\n\\n          .college-logo {\\n            height: 80px;\\n          }\\n\\n          .features-grid {\\n            grid-template-columns: 1fr;\\n            gap: 1.5rem;\\n          }\\n\\n          .features-header h2 {\\n            font-size: 2rem;\\n          }\\n\\n          .final-cta-content h2 {\\n            font-size: 1.875rem;\\n          }\\n\\n          .features-section {\\n            padding: 4rem 0;\\n          }\\n\\n          .final-cta-section {\\n            padding: 3rem 0;\\n          }\\n        }\\n\\n        @media (max-width: 480px) {\\n          .hero-section {\\n            min-height: 100vh;\\n            padding: 1rem 0;\\n          }\\n\\n          .hero-inner {\\n            gap: 1.5rem;\\n          }\\n\\n          .hero-title {\\n            font-size: 2rem;\\n            line-height: 1.2;\\n          }\\n\\n          .title-highlight {\\n            font-size: 2.5rem;\\n            margin: 0.25rem 0;\\n          }\\n\\n          .hero-subtitle {\\n            font-size: 1rem;\\n            margin-bottom: 1.5rem;\\n          }\\n\\n          .cta-button {\\n            font-size: 1rem;\\n            padding: 0.875rem 1.5rem;\\n            width: 100%;\\n            max-width: 280px;\\n          }\\n\\n          .college-logo {\\n            height: 60px;\\n          }\\n\\n          .feature-card {\\n            padding: 1.5rem;\\n          }\\n\\n          .features-header {\\n            margin-bottom: 2rem;\\n          }\\n\\n          .features-header h2 {\\n            font-size: 1.75rem;\\n          }\\n\\n          .final-cta-content h2 {\\n            font-size: 1.5rem;\\n          }\\n\\n          .secondary-cta-button {\\n            width: 100%;\\n            max-width: 280px;\\n            justify-content: center;\\n          }\\n        }\\n\\n        @media (max-width: 360px) {\\n          .hero-title {\\n            font-size: 1.75rem;\\n          }\\n\\n          .title-highlight {\\n            font-size: 2.25rem;\\n          }\\n\\n          .hero-subtitle {\\n            font-size: 0.95rem;\\n          }\\n\\n          .college-logo {\\n            height: 50px;\\n          }\\n\\n          .cta-button {\\n            font-size: 0.95rem;\\n            padding: 0.75rem 1.25rem;\\n          }\\n        }\\n\\n        /* Enhanced animations and interactions */\\n        @keyframes float {\\n          0%, 100% { transform: translateY(0px); }\\n          50% { transform: translateY(-10px); }\\n        }\\n\\n        .college-logo {\\n          animation: float 6s ease-in-out infinite;\\n        }\\n\\n        @keyframes pulse {\\n          0%, 100% { box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4); }\\n          50% { box-shadow: 0 4px 25px rgba(251, 191, 36, 0.6); }\\n        }\\n\\n        .cta-button {\\n          animation: pulse 2s ease-in-out infinite;\\n        }\\n\\n        .cta-button:hover {\\n          animation: none;\\n        }\\n\\n        /* Improved accessibility */\\n        @media (prefers-reduced-motion: reduce) {\\n          .college-logo,\\n          .cta-button,\\n          .feature-card,\\n          .main-content,\\n          .logo-section {\\n            animation: none !important;\\n            transition: none !important;\\n          }\\n        }\\n\\n        /* High contrast mode support */\\n        @media (prefers-contrast: high) {\\n          .hero-overlay {\\n            background: rgba(0, 0, 0, 0.8);\\n          }\\n\\n          .cta-button {\\n            border: 2px solid #000;\\n          }\\n\\n          .feature-card {\\n            border: 2px solid #000;\\n          }\\n        }\\n      \"})]});};export default WelcomePage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}