{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"1pf5j1\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M2 13v-1h6v1\",\n  key: \"1dh9dg\"\n}], [\"path\", {\n  d: \"M5 12v6\",\n  key: \"150t9c\"\n}], [\"path\", {\n  d: \"M4 18h2\",\n  key: \"1xrofg\"\n}]];\nconst FileType2 = createLucideIcon(\"file-type-2\", __iconNode);\nexport { __iconNode, FileType2 as default };\n//# sourceMappingURL=file-type-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}