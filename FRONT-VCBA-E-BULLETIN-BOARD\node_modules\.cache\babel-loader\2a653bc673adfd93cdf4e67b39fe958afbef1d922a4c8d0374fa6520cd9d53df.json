{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2h8\",\n  key: \"1ssgc1\"\n}], [\"path\", {\n  d: \"M9 2v1.343M15 2v2.789a4 4 0 0 0 .672 2.219l.656.984a4 4 0 0 1 .672 2.22v1.131M7.8 7.8l-.128.192A4 4 0 0 0 7 10.212V20a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-3\",\n  key: \"y0ejgx\"\n}], [\"path\", {\n  d: \"M7 15a6.47 6.47 0 0 1 5 0 6.472 6.472 0 0 0 3.435.435\",\n  key: \"iaxqsy\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst MilkOff = createLucideIcon(\"milk-off\", __iconNode);\nexport { __iconNode, MilkOff as default };\n//# sourceMappingURL=milk-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}