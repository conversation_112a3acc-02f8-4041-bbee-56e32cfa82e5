{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 5a2 2 0 0 0-1.344.519l-6.328 5.74a1 1 0 0 0 0 1.481l6.328 5.741A2 2 0 0 0 10 19h10a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z\",\n  key: \"1yo7s0\"\n}], [\"path\", {\n  d: \"m12 9 6 6\",\n  key: \"anjzzh\"\n}], [\"path\", {\n  d: \"m18 9-6 6\",\n  key: \"1fp51s\"\n}]];\nconst Delete = createLucideIcon(\"delete\", __iconNode);\nexport { __iconNode, Delete as default };\n//# sourceMappingURL=delete.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}