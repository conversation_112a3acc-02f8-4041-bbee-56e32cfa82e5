{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 11v4\",\n  key: \"a6ujw6\"\n}], [\"path\", {\n  d: \"M14 13h-4\",\n  key: \"1pl8zg\"\n}], [\"path\", {\n  d: \"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2\",\n  key: \"1ksdt3\"\n}], [\"path\", {\n  d: \"M18 6v14\",\n  key: \"1mu4gy\"\n}], [\"path\", {\n  d: \"M6 6v14\",\n  key: \"1s15cj\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"i6l2r4\"\n}]];\nconst BriefcaseMedical = createLucideIcon(\"briefcase-medical\", __iconNode);\nexport { __iconNode, BriefcaseMedical as default };\n//# sourceMappingURL=briefcase-medical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}