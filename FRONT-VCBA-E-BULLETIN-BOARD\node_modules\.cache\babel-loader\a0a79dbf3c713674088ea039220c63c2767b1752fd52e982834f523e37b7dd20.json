{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\",\n  key: \"1pljnt\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}], [\"path\", {\n  d: \"M8 19h.01\",\n  key: \"puxtts\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}], [\"path\", {\n  d: \"M12 21h.01\",\n  key: \"h35vbk\"\n}], [\"path\", {\n  d: \"M16 15h.01\",\n  key: \"rnfrdf\"\n}], [\"path\", {\n  d: \"M16 19h.01\",\n  key: \"1vcnzz\"\n}]];\nconst CloudSnow = createLucideIcon(\"cloud-snow\", __iconNode);\nexport { __iconNode, CloudSnow as default };\n//# sourceMappingURL=cloud-snow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}