{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\PublicRoute.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Admin Public Route Component\nconst AdminPublicRoute = ({\n  children,\n  restricted = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAdminAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Student Public Route Component\n_s(AdminPublicRoute, \"zeGHgmRC+QMCcA/aIWqLB+h7J9Q=\", false, function () {\n  return [useAdminAuth];\n});\n_c = AdminPublicRoute;\nconst StudentPublicRoute = ({\n  children,\n  restricted = false\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useStudentAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Main Public Route Component\n_s2(StudentPublicRoute, \"qQO1MO9AEmjDbNk78wOdubWscbE=\", false, function () {\n  return [useStudentAuth];\n});\n_c2 = StudentPublicRoute;\nconst PublicRoute = props => {\n  _s3();\n  const location = useLocation();\n\n  // Determine which public route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return /*#__PURE__*/_jsxDEV(AdminPublicRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 12\n    }, this);\n  } else if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(StudentPublicRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 12\n    }, this);\n  }\n\n  // For other routes, just render children\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: props.children\n  }, void 0, false);\n};\n_s3(PublicRoute, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c3 = PublicRoute;\nexport default PublicRoute;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AdminPublicRoute\");\n$RefreshReg$(_c2, \"StudentPublicRoute\");\n$RefreshReg$(_c3, \"PublicRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdminAuth", "useStudentAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminPublicRoute", "children", "restricted", "_s", "isAuthenticated", "user", "isLoading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "StudentPublicRoute", "_s2", "_c2", "PublicRoute", "props", "_s3", "location", "pathname", "startsWith", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/PublicRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\n\ninterface PublicRouteProps {\n  children: React.ReactNode;\n  restricted?: boolean; // If true, authenticated users will be redirected\n}\n\n// Admin Public Route Component\nconst AdminPublicRoute: React.FC<PublicRouteProps> = ({\n  children,\n  restricted = false,\n}) => {\n  const { isAuthenticated, user, isLoading } = useAdminAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Student Public Route Component\nconst StudentPublicRoute: React.FC<PublicRouteProps> = ({\n  children,\n  restricted = false,\n}) => {\n  const { isAuthenticated, user, isLoading } = useStudentAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return <Navigate to=\"/student/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Main Public Route Component\nconst PublicRoute: React.FC<PublicRouteProps> = (props) => {\n  const location = useLocation();\n\n  // Determine which public route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return <AdminPublicRoute {...props} />;\n  } else if (location.pathname.startsWith('/student')) {\n    return <StudentPublicRoute {...props} />;\n  }\n\n  // For other routes, just render children\n  return <>{props.children}</>;\n};\n\nexport default PublicRoute;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQnE;AACA,MAAMC,gBAA4C,GAAGA,CAAC;EACpDC,QAAQ;EACRC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGZ,YAAY,CAAC,CAAC;;EAE3D;EACA,IAAIY,SAAS,EAAE;IACb,oBACET,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA;QAAKU,SAAS,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAIT,UAAU,IAAIE,eAAe,IAAIC,IAAI,EAAE;IACzC,oBAAOR,OAAA,CAACL,QAAQ;MAACoB,EAAE,EAAC,kBAAkB;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAE,EAAA,CAvBMH,gBAA4C;EAAA,QAIHN,YAAY;AAAA;AAAAoB,EAAA,GAJrDd,gBAA4C;AAwBlD,MAAMe,kBAA8C,GAAGA,CAAC;EACtDd,QAAQ;EACRC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAc,GAAA;EACJ,MAAM;IAAEZ,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;;EAE7D;EACA,IAAIW,SAAS,EAAE;IACb,oBACET,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA;QAAKU,SAAS,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAIT,UAAU,IAAIE,eAAe,IAAIC,IAAI,EAAE;IACzC,oBAAOR,OAAA,CAACL,QAAQ;MAACoB,EAAE,EAAC,oBAAoB;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAe,GAAA,CAvBMD,kBAA8C;EAAA,QAILpB,cAAc;AAAA;AAAAsB,GAAA,GAJvDF,kBAA8C;AAwBpD,MAAMG,WAAuC,GAAIC,KAAK,IAAK;EAAAC,GAAA;EACzD,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAI4B,QAAQ,CAACC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;IAC1C,oBAAO1B,OAAA,CAACG,gBAAgB;MAAA,GAAKmB;IAAK;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACxC,CAAC,MAAM,IAAIU,QAAQ,CAACC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACnD,oBAAO1B,OAAA,CAACkB,kBAAkB;MAAA,GAAKI;IAAK;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC1C;;EAEA;EACA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGkB,KAAK,CAAClB;EAAQ,gBAAG,CAAC;AAC9B,CAAC;AAACmB,GAAA,CAZIF,WAAuC;EAAA,QAC1BzB,WAAW;AAAA;AAAA+B,GAAA,GADxBN,WAAuC;AAc7C,eAAeA,WAAW;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}