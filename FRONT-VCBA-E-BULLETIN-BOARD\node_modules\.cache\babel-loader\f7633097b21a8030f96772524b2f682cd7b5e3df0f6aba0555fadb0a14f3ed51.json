{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 17.5a2.5 2.5 0 1 1-4 2.03V12\",\n  key: \"yd12zl\"\n}], [\"path\", {\n  d: \"M6 12H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\",\n  key: \"larmp2\"\n}], [\"path\", {\n  d: \"M6 8h12\",\n  key: \"6g4wlu\"\n}], [\"path\", {\n  d: \"M6.6 15.572A2 2 0 1 0 10 17v-5\",\n  key: \"1x1kqn\"\n}]];\nconst AirVent = createLucideIcon(\"air-vent\", __iconNode);\nexport { __iconNode, AirVent as default };\n//# sourceMappingURL=air-vent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}