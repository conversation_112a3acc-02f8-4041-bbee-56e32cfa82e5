{"ast": null, "code": "import React from'react';import{NavLink,useLocation}from'react-router-dom';import{BarChart3,Setting<PERSON>,BookOpen,Rss}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const navItems=[{path:'/student/dashboard',label:'Dashboard',icon:BarChart3,description:'Overview & Quick Actions'},{path:'/student/newsfeed',label:'Newsfeed',icon:Rss,description:'Latest Announcements & Events'},{path:'/student/settings',label:'Settings',icon:Settings,description:'Profile & Preferences'}];const StudentSidebar=_ref=>{let{isOpen,onToggle}=_ref;const location=useLocation();const isActive=path=>{return location.pathname===path;};return/*#__PURE__*/_jsxs(\"aside\",{style:{position:'fixed',left:0,top:0,height:'100vh',width:isOpen?'280px':'80px',background:'linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%)',transition:'width 0.3s ease',zIndex:1000,boxShadow:'4px 0 20px rgba(0, 0, 0, 0.1)',overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:isOpen?'1.5rem':'1rem',borderBottom:'1px solid rgba(255, 255, 255, 0.1)',display:'flex',alignItems:'center',gap:'1rem',minHeight:'80px'},children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",style:{width:'48px',height:'48px',objectFit:'contain',flexShrink:0}}),isOpen&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{style:{color:'white',margin:0,fontSize:'1.1rem',fontWeight:'700',lineHeight:'1.2'},children:\"Student Portal\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.7)',margin:0,fontSize:'0.75rem',lineHeight:'1.2'},children:\"E-Bulletin Board\"})]})]}),/*#__PURE__*/_jsx(\"nav\",{style:{padding:'2rem 0'},children:navItems.map(item=>/*#__PURE__*/_jsxs(NavLink,{to:item.path,style:{display:'flex',alignItems:'center',gap:'1rem',padding:isOpen?'1.5rem 1.5rem':'1.5rem',color:isActive(item.path)?'#fbbf24':'rgba(255, 255, 255, 0.8)',textDecoration:'none',background:isActive(item.path)?'linear-gradient(90deg, rgba(251, 191, 36, 0.2) 0%, transparent 100%)':'transparent',borderRight:isActive(item.path)?'3px solid #fbbf24':'3px solid transparent',transition:'all 0.2s ease',position:'relative',overflow:'hidden'},onMouseEnter:e=>{if(!isActive(item.path)){e.currentTarget.style.background='rgba(255, 255, 255, 0.05)';e.currentTarget.style.color='white';}},onMouseLeave:e=>{if(!isActive(item.path)){e.currentTarget.style.background='transparent';e.currentTarget.style.color='rgba(255, 255, 255, 0.8)';}},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'1.5rem',flexShrink:0,width:'24px',textAlign:'center'},children:/*#__PURE__*/_jsx(item.icon,{size:20,color:isActive(item.path)?'#facc15':'rgba(255, 255, 255, 0.8)'})}),isOpen&&/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:isActive(item.path)?'600':'500',fontSize:'1rem',marginBottom:'0.25rem'},children:item.label}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:isActive(item.path)?'rgba(251, 191, 36, 0.8)':'rgba(255, 255, 255, 0.6)',lineHeight:'1.2'},children:item.description})]})]},item.path))}),isOpen&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',bottom:'2rem',left:'1.5rem',right:'1.5rem',padding:'1.5rem',background:'rgba(255, 255, 255, 0.05)',borderRadius:'12px',border:'1px solid rgba(255, 255, 255, 0.1)',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'rgba(255, 255, 255, 0.9)',fontSize:'1rem',fontWeight:'600',marginBottom:'0.5rem'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(BookOpen,{size:18,color:\"rgba(255, 255, 255, 0.9)\"}),\"Welcome Student!\"]})}),/*#__PURE__*/_jsx(\"div\",{style:{color:'rgba(255, 255, 255, 0.7)',fontSize:'0.75rem',lineHeight:'1.4'},children:\"Stay updated with the latest announcements and manage your profile\"})]})]});};export default StudentSidebar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}