{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M3 15h18\",\n  key: \"5xshup\"\n}], [\"path\", {\n  d: \"m15 8-3 3-3-3\",\n  key: \"1oxy1z\"\n}]];\nconst PanelBottomClose = createLucideIcon(\"panel-bottom-close\", __iconNode);\nexport { __iconNode, PanelBottomClose as default };\n//# sourceMappingURL=panel-bottom-close.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}