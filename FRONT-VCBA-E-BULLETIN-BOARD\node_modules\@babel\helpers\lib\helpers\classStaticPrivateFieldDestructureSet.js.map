{"version": 3, "names": ["_classApplyDescriptorDestructureSet", "require", "_assert<PERSON>lassBrand", "_classCheckPrivateStaticFieldDescriptor", "_classStaticPrivateFieldDestructureSet", "receiver", "classConstructor", "descriptor", "assertClassBrand", "classCheckPrivateStaticFieldDescriptor", "classApplyDescriptorDestructureSet"], "sources": ["../../src/helpers/classStaticPrivateFieldDestructureSet.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorDestructureSet from \"classApplyDescriptorDestructureSet\";\nimport assertClassBrand from \"assertClassBrand\";\nimport classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\nexport default function _classStaticPrivateFieldDestructureSet(\n  receiver,\n  classConstructor,\n  descriptor,\n) {\n  assertClassBrand(classConstructor, receiver);\n  classCheckPrivateStaticFieldDescriptor(descriptor, \"set\");\n  return classApplyDescriptorDestructureSet(receiver, descriptor);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,mCAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,uCAAA,GAAAF,OAAA;AACe,SAASG,sCAAsCA,CAC5DC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACV;EACAC,iBAAgB,CAACF,gBAAgB,EAAED,QAAQ,CAAC;EAC5CI,uCAAsC,CAACF,UAAU,EAAE,KAAK,CAAC;EACzD,OAAOG,mCAAkC,CAACL,QAAQ,EAAEE,UAAU,CAAC;AACjE", "ignoreList": []}