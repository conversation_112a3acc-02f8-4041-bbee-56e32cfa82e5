{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12H5a2 2 0 0 0-2 2v5\",\n  key: \"7zsz91\"\n}], [\"circle\", {\n  cx: \"13\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"wjnkru\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"v8kfzx\"\n}], [\"path\", {\n  d: \"M8 19h3m5-17v17h6M6 12V7c0-1.1.9-2 2-2h3l5 5\",\n  key: \"13bk1p\"\n}]];\nconst Forklift = createLucideIcon(\"forklift\", __iconNode);\nexport { __iconNode, Forklift as default };\n//# sourceMappingURL=forklift.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}