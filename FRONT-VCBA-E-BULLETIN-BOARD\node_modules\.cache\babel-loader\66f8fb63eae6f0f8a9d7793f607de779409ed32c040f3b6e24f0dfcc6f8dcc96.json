{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useReducer,useCallback,useEffect}from'react';import{AdminAuthService}from'../services/admin-auth.service';// Admin Auth context interface\nimport{jsx as _jsx}from\"react/jsx-runtime\";// Initial state\nconst initialState={user:null,isAuthenticated:false,isLoading:true,error:null};// Action types\n// Reducer\nconst authReducer=(state,action)=>{switch(action.type){case'AUTH_START':return _objectSpread(_objectSpread({},state),{},{isLoading:true,error:null});case'AUTH_SUCCESS':return _objectSpread(_objectSpread({},state),{},{user:action.payload,isAuthenticated:true,isLoading:false,error:null});case'AUTH_ERROR':return _objectSpread(_objectSpread({},state),{},{user:null,isAuthenticated:false,isLoading:false,error:action.payload});case'AUTH_LOGOUT':return _objectSpread(_objectSpread({},state),{},{user:null,isAuthenticated:false,isLoading:false,error:null});case'SET_LOADING':return _objectSpread(_objectSpread({},state),{},{isLoading:action.payload});case'CLEAR_ERROR':return _objectSpread(_objectSpread({},state),{},{error:null});default:return state;}};// Create context\nconst AdminAuthContext=/*#__PURE__*/createContext(undefined);// Provider component\nexport const AdminAuthProvider=_ref=>{let{children}=_ref;const[state,dispatch]=useReducer(authReducer,initialState);// Clear error function\nconst clearError=useCallback(()=>{dispatch({type:'CLEAR_ERROR'});},[]);// Check authentication status\nconst checkAuthStatus=useCallback(async()=>{try{console.log('🔍 AdminAuth - Checking authentication status');dispatch({type:'SET_LOADING',payload:true});// First check local storage for authentication data\nconst storedUser=AdminAuthService.getStoredUser();const hasToken=AdminAuthService.isAuthenticated();// console.log('🔍 AdminAuth - Local auth data:', {\n//   hasUser: !!storedUser,\n//   hasToken: !!hasToken,\n//   userRole: storedUser?.role,\n//   userEmail: storedUser?.email\n// });\n// If we have local data and it's an admin user, authenticate immediately\nif(storedUser&&hasToken&&storedUser.role==='admin'){console.log('✅ AdminAuth - Admin user authenticated from local storage:',storedUser.email);dispatch({type:'AUTH_SUCCESS',payload:storedUser});return;}// If no local data or wrong role, logout\nconsole.log('❌ AdminAuth - No admin user found in local storage or wrong role');dispatch({type:'AUTH_LOGOUT'});}catch(error){console.error('❌ AdminAuth - Auth check failed:',error.message);dispatch({type:'AUTH_LOGOUT'});}},[]);// Login function\nconst login=async credentials=>{try{dispatch({type:'AUTH_START'});// Use AdminAuthService for login\nconst response=await AdminAuthService.login(credentials);// Verify the user is actually an admin\nif(response.data.user.role!=='admin'){throw new Error('Access denied: Admin privileges required');}console.log('✅ AdminAuth - Admin login successful:',response.data.user.email);dispatch({type:'AUTH_SUCCESS',payload:response.data.user});}catch(error){const errorMessage=error.message||'Admin login failed. Please try again.';console.error('❌ AdminAuth - Login failed:',errorMessage);dispatch({type:'AUTH_ERROR',payload:errorMessage});throw error;}};// Logout function\nconst logout=async()=>{try{console.log('🚪 AdminAuth - Starting admin logout process');await AdminAuthService.logout();console.log('✅ AdminAuth - Server logout successful');}catch(error){console.error('❌ AdminAuth - Logout error:',error);}finally{console.log('🧹 AdminAuth - Clearing admin state');dispatch({type:'AUTH_LOGOUT'});// Redirect to admin login\nwindow.location.href='/admin/login';}};// Register function\nconst register=async data=>{try{dispatch({type:'AUTH_START'});const response=await AdminAuthService.registerAdmin(data);dispatch({type:'SET_LOADING',payload:false});return response;}catch(error){const errorMessage=error.message||'Admin registration failed. Please try again.';dispatch({type:'AUTH_ERROR',payload:errorMessage});throw error;}};// Verify OTP function\nconst verifyOtp=async data=>{try{dispatch({type:'AUTH_START'});await AdminAuthService.verifyOtp(data);dispatch({type:'SET_LOADING',payload:false});}catch(error){const errorMessage=error.message||'OTP verification failed. Please try again.';dispatch({type:'AUTH_ERROR',payload:errorMessage});throw error;}};// Resend OTP function\nconst resendOtp=async email=>{try{dispatch({type:'AUTH_START'});await AdminAuthService.resendOtp(email);dispatch({type:'SET_LOADING',payload:false});}catch(error){const errorMessage=error.message||'Failed to resend OTP. Please try again.';dispatch({type:'AUTH_ERROR',payload:errorMessage});throw error;}};// Setup response interceptor for handling unauthorized requests - DISABLED\nuseEffect(()=>{console.log('🔧 AdminAuth - Response interceptor DISABLED to prevent automatic logouts');// setupResponseInterceptor(() => {\n//   console.log('🚨 AdminAuth - Unauthorized request detected, logging out admin');\n//   dispatch({ type: 'AUTH_LOGOUT' });\n// });\n},[]);// Check authentication status on mount\nuseEffect(()=>{console.log('🚀 AdminAuth - Component mounted, checking admin auth status');checkAuthStatus();},[checkAuthStatus]);// Context value\nconst value=_objectSpread(_objectSpread({},state),{},{login,logout,register,verifyOtp,resendOtp,clearError,checkAuthStatus,userType:'admin'});return/*#__PURE__*/_jsx(AdminAuthContext.Provider,{value:value,children:children});};// Hook to use admin auth context\nexport const useAdminAuth=()=>{const context=useContext(AdminAuthContext);if(context===undefined){throw new Error('useAdminAuth must be used within an AdminAuthProvider');}return context;};export default AdminAuthContext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}