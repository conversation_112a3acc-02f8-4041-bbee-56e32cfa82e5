{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 12h18\",\n  key: \"1i2n21\"\n}], [\"path\", {\n  d: \"M3 18h18\",\n  key: \"1h113x\"\n}], [\"path\", {\n  d: \"M3 6h18\",\n  key: \"d0wm0j\"\n}]];\nconst AlignJustify = createLucideIcon(\"align-justify\", __iconNode);\nexport { __iconNode, AlignJustify as default };\n//# sourceMappingURL=align-justify.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}