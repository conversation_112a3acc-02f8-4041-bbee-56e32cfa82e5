{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M14 15h1\",\n  key: \"171nev\"\n}], [\"path\", {\n  d: \"M19 15h2\",\n  key: \"1vnucp\"\n}], [\"path\", {\n  d: \"M3 15h2\",\n  key: \"8bym0q\"\n}], [\"path\", {\n  d: \"M9 15h1\",\n  key: \"1tg3ks\"\n}]];\nconst PanelBottomDashed = createLucideIcon(\"panel-bottom-dashed\", __iconNode);\nexport { __iconNode, PanelBottomDashed as default };\n//# sourceMappingURL=panel-bottom-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}