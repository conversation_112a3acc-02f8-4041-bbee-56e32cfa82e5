{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M15 14v1\",\n  key: \"ilsfch\"\n}], [\"path\", {\n  d: \"M15 19v2\",\n  key: \"1fst2f\"\n}], [\"path\", {\n  d: \"M15 3v2\",\n  key: \"z204g4\"\n}], [\"path\", {\n  d: \"M15 9v1\",\n  key: \"z2a8b1\"\n}]];\nconst PanelRightDashed = createLucideIcon(\"panel-right-dashed\", __iconNode);\nexport { __iconNode, PanelRightDashed as default };\n//# sourceMappingURL=panel-right-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}