{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 17H4a2 2 0 0 1-2-2V5c0-1.5 1-2 1-2\",\n  key: \"k0q8oc\"\n}], [\"path\", {\n  d: \"M22 15V5a2 2 0 0 0-2-2H9\",\n  key: \"cp1ac0\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst MonitorOff = createLucideIcon(\"monitor-off\", __iconNode);\nexport { __iconNode, MonitorOff as default };\n//# sourceMappingURL=monitor-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}