{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\NewsFeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useContext, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\n\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminAuthContext from '../../contexts/AdminAuthContext';\nimport StudentAuthContext from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart,\n// Edit, // Not used in unified component\nUsers, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut, Settings } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = (imagePath, userRole) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const currentBlobUrl = useRef(null);\n  useEffect(() => {\n    // Cleanup previous blob URL if it exists\n    if (currentBlobUrl.current) {\n      URL.revokeObjectURL(currentBlobUrl.current);\n      currentBlobUrl.current = null;\n    }\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n\n        // Store the blob URL for cleanup\n        currentBlobUrl.current = objectUrl;\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup function\n    return () => {\n      if (currentBlobUrl.current) {\n        URL.revokeObjectURL(currentBlobUrl.current);\n        currentBlobUrl.current = null;\n      }\n    };\n  }, [imagePath, userRole]); // Fixed: removed imageUrl to prevent infinite loop\n\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"hHzKct3MDLbHRCNtj2pkZxlcZjY=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath, userRole);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  userRole,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = () => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(),\n        userRole: userRole,\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(),\n            userRole: userRole,\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n\n// Props interface for the unified NewsFeed component\n_c2 = ImageGallery;\n// Main unified NewsFeed Component\nconst NewsFeed = ({\n  userRole\n}) => {\n  _s3();\n  var _currentUser$firstNam2, _currentUser$lastName2, _currentUser$firstNam4, _currentUser$lastName4;\n  const navigate = useNavigate();\n\n  // Safely get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useContext(AdminAuthContext);\n  const studentAuth = useContext(StudentAuthContext);\n\n  // Determine current user role and context\n  const currentRole = userRole || (adminAuth !== null && adminAuth !== void 0 && adminAuth.isAuthenticated ? 'admin' : studentAuth !== null && studentAuth !== void 0 && studentAuth.isAuthenticated ? 'student' : null);\n  const currentUser = currentRole === 'admin' ? adminAuth === null || adminAuth === void 0 ? void 0 : adminAuth.user : studentAuth === null || studentAuth === void 0 ? void 0 : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth === null || adminAuth === void 0 ? void 0 : adminAuth.logout : studentAuth === null || studentAuth === void 0 ? void 0 : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const {\n    categories\n  } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const [showProfileSettings, setShowProfileSettings] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = useCallback(async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const authToken = currentRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${authToken}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  }, [currentRole]);\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#ef4444',\n            marginBottom: '1rem'\n          },\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            marginBottom: '1.5rem'\n          },\n          children: \"Please log in to access the newsfeed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          style: {\n            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            padding: '0.75rem 1.5rem',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId, currentlyLiked) => {\n    const client = currentRole === 'admin' ? adminHttpClient : studentHttpClient;\n    const endpoint = `/api/calendar/${eventId}/like`;\n    console.log(`[DEBUG] ${currentRole} making direct API call to:`, endpoint);\n    console.log(`[DEBUG] Using ${currentRole} HTTP client`);\n    if (currentlyLiked) {\n      // Unlike the event\n      return await client.delete(endpoint);\n    } else {\n      // Like the event\n      return await client.post(endpoint, {});\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      const response = await toggleCalendarReaction(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        const newReactionState = !event.user_has_reacted;\n        const newReactionCount = event.user_has_reacted ? Math.max(0, (event.reaction_count || 0) - 1) : (event.reaction_count || 0) + 1;\n        console.log(`[DEBUG] ${currentRole} updating local state:`, {\n          eventId: event.calendar_id,\n          oldReactionState: event.user_has_reacted,\n          newReactionState,\n          oldCount: event.reaction_count,\n          newCount: newReactionCount,\n          userId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n          userRole: currentRole\n        });\n\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: newReactionState,\n          reaction_count: newReactionCount\n        } : e));\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      if (currentLogout) {\n        await currentLogout();\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' + String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' + String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search post\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 19\n                }, this), categories.filter(category =>\n                // Hide holiday categories from dropdown\n                !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 67\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                \"data-dropdown\": \"user-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowUserDropdown(!showUserDropdown),\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    padding: '0.75rem 1rem',\n                    background: 'white',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: [currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getImageUrl(currentUser.profilePicture) || '',\n                    alt: `${currentUser.firstName} ${currentUser.lastName}`,\n                    style: {\n                      width: '24px',\n                      height: '24px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '1px solid #e5e7eb'\n                    },\n                    onError: e => {\n                      const target = e.target;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        const userIcon = parent.querySelector('.user-icon');\n                        if (userIcon) {\n                          userIcon.style.display = 'block';\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(User, {\n                    size: 16,\n                    className: \"user-icon\",\n                    style: {\n                      display: currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? 'none' : 'block'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentRole === 'admin' ? 'Admin' : 'Student')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                    size: 14,\n                    style: {\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1101,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 19\n                }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '100%',\n                    right: 0,\n                    marginTop: '0.5rem',\n                    background: 'white',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '12px',\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                    minWidth: '200px',\n                    zIndex: 1000,\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem 1rem',\n                      borderBottom: '1px solid #f3f4f6',\n                      background: '#f9fafb'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: getImageUrl(currentUser.profilePicture) || '',\n                        alt: `${currentUser.firstName} ${currentUser.lastName}`,\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '50%',\n                          objectFit: 'cover',\n                          border: '2px solid #e5e7eb',\n                          flexShrink: 0\n                        },\n                        onError: e => {\n                          const target = e.target;\n                          target.style.display = 'none';\n                          const parent = target.parentElement;\n                          if (parent) {\n                            var _currentUser$firstNam, _currentUser$lastName;\n                            parent.innerHTML = `\n                                    <div style=\"\n                                      width: 40px;\n                                      height: 40px;\n                                      border-radius: 50%;\n                                      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                                      display: flex;\n                                      align-items: center;\n                                      justify-content: center;\n                                      color: white;\n                                      font-weight: 600;\n                                      font-size: 1rem;\n                                      flex-shrink: 0;\n                                    \">\n                                      ${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam = currentUser.firstName) === null || _currentUser$firstNam === void 0 ? void 0 : _currentUser$firstNam.charAt(0)) || ''}${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName = currentUser.lastName) === null || _currentUser$lastName === void 0 ? void 0 : _currentUser$lastName.charAt(0)) || ''}\n                                    </div>\n                                  `;\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1135,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '50%',\n                          background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                          fontWeight: '600',\n                          fontSize: '1rem',\n                          flexShrink: 0\n                        },\n                        children: [(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam2 = currentUser.firstName) === null || _currentUser$firstNam2 === void 0 ? void 0 : _currentUser$firstNam2.charAt(0)) || '', (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName2 = currentUser.lastName) === null || _currentUser$lastName2 === void 0 ? void 0 : _currentUser$lastName2.charAt(0)) || '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1172,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827'\n                          },\n                          children: [currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1190,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.75rem',\n                            color: '#6b7280'\n                          },\n                          children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1197,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1189,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1127,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.5rem 0'\n                    },\n                    children: [currentRole === 'admin' ?\n                    /*#__PURE__*/\n                    // Admin: Show Dashboard button\n                    _jsxDEV(\"button\", {\n                      onClick: () => {\n                        navigate('/admin/dashboard');\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#374151',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#f3f4f6';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1236,\n                        columnNumber: 29\n                      }, this), \"Dashboard\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1211,\n                      columnNumber: 27\n                    }, this) :\n                    /*#__PURE__*/\n                    // Student: Show Profile Settings button\n                    _jsxDEV(\"button\", {\n                      onClick: () => {\n                        setShowProfileSettings(true);\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#374151',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#f3f4f6';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Settings, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1266,\n                        columnNumber: 29\n                      }, this), \"Profile Settings\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1241,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        handleLogout();\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#ef4444',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#fef2f2';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1296,\n                        columnNumber: 27\n                      }, this), \"Logout\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1271,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1207,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1109,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map(announcement => {\n                  // Handle alert announcements with special styling\n                  const isAlert = announcement.is_alert;\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: isAlert ? '#fef2f2' : '#f8fafc',\n                      borderRadius: '12px',\n                      border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                      e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                      e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: isAlert ? '#ef4444' : categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1397,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1406,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1415,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1432,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1433,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1425,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1405,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1467,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1506,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1486,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                refreshAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1519,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1600,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1590,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1610,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1625,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1586,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1708,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1698,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.category_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1737,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1724,\n                          columnNumber: 33\n                        }, this);\n                      })(), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px',\n                          fontSize: '0.75rem',\n                          color: '#3b82f6'\n                        },\n                        children: [event.created_by_picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getImageUrl(event.created_by_picture) || '',\n                          alt: event.created_by_name,\n                          style: {\n                            width: '16px',\n                            height: '16px',\n                            borderRadius: '50%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            const target = e.target;\n                            target.style.display = 'none';\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1756,\n                          columnNumber: 35\n                        }, this) : /*#__PURE__*/_jsxDEV(User, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1771,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontWeight: '500'\n                          },\n                          children: event.created_by_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1773,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1745,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1786,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1779,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1712,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1796,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1711,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1692,\n                  columnNumber: 23\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = eventImageUrls.filter(Boolean);\n                        openLightboxWithUrls(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1827,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1826,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1842,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1868,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1869,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1862,\n                    columnNumber: 25\n                  }, this), event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.category_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1884,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1878,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1853,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCalendarLikeToggle(event),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: event.user_has_reacted ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1930,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.reaction_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1934,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1907,\n                    columnNumber: 25\n                  }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1966,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.comment_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1967,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1939,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1898,\n                  columnNumber: 23\n                }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1980,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1987,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1974,\n                  columnNumber: 25\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1671,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                id: `announcement-${announcement.announcement_id}`,\n                className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2053,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2038,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2078,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2068,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2097,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2087,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2127,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2114,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2150,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2137,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2158,\n                        columnNumber: 31\n                      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'rgba(34, 197, 94, 0.1)',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px',\n                          fontSize: '0.75rem',\n                          color: '#16a34a'\n                        },\n                        children: [announcement.author_picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getImageUrl(announcement.author_picture) || '',\n                          alt: announcement.author_name,\n                          style: {\n                            width: '16px',\n                            height: '16px',\n                            borderRadius: '50%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            const target = e.target;\n                            target.style.display = 'none';\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2183,\n                          columnNumber: 35\n                        }, this) : /*#__PURE__*/_jsxDEV(User, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2198,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontWeight: '500'\n                          },\n                          children: announcement.author_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2200,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2172,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2206,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2104,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2219,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2103,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2059,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  userRole: currentRole,\n                  onImageClick: index => {\n                    openLightbox(announcement.attachments || [], index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2233,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2244,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2292,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2296,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2269,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2328,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2329,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2301,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2263,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2349,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || announcement.author_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2350,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2344,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2353,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2337,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2254,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2375,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2382,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2369,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2005,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1311,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2430,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              if (selectedPinnedPost.is_alert) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2499,\n                    columnNumber: 25\n                  }, this), \"Alert\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2486,\n                  columnNumber: 23\n                }, this);\n              } else {\n                const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                const categoryStyle = getCategoryStyle(categoryName);\n                const IconComponent = categoryStyle.icon;\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: categoryStyle.background,\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2522,\n                    columnNumber: 25\n                  }, this), categoryName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2509,\n                  columnNumber: 23\n                }, this);\n              }\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2540,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2545,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              userRole: currentRole,\n              onImageClick: index => {\n                openLightbox(selectedPinnedPost.attachments, index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2558,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2557,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2569,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2593,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2587,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2596,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2476,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2418,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2403,\n      columnNumber: 9\n    }, this), showProfileSettings && currentRole === 'student' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n          maxWidth: '800px',\n          width: '100%',\n          maxHeight: '90vh',\n          overflow: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: 0,\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#1f2937'\n            },\n            children: \"Profile Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2638,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowProfileSettings(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280',\n              padding: '0.5rem',\n              borderRadius: '8px',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#f3f4f6';\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2646,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2631,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            display: 'flex',\n            gap: '2rem',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '200px'\n            },\n            children: [currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getImageUrl(currentUser.profilePicture) || '',\n              alt: `${currentUser.firstName} ${currentUser.lastName}`,\n              style: {\n                width: '150px',\n                height: '150px',\n                borderRadius: '50%',\n                objectFit: 'cover',\n                border: '4px solid #e5e7eb'\n              },\n              onError: e => {\n                const target = e.target;\n                target.style.display = 'none';\n                const parent = target.parentElement;\n                if (parent) {\n                  var _currentUser$firstNam3, _currentUser$lastName3;\n                  parent.innerHTML = `\n                          <div style=\"\n                            width: 150px;\n                            height: 150px;\n                            border-radius: 50%;\n                            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                            display: flex;\n                            align-items: center;\n                            justify-content: center;\n                            color: white;\n                            font-weight: 600;\n                            font-size: 3rem;\n                            border: 4px solid #e5e7eb;\n                          \">\n                            ${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam3 = currentUser.firstName) === null || _currentUser$firstNam3 === void 0 ? void 0 : _currentUser$firstNam3.charAt(0)) || ''}${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName3 = currentUser.lastName) === null || _currentUser$lastName3 === void 0 ? void 0 : _currentUser$lastName3.charAt(0)) || ''}\n                          </div>\n                        `;\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2688,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '150px',\n                height: '150px',\n                borderRadius: '50%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '600',\n                fontSize: '3rem',\n                border: '4px solid #e5e7eb'\n              },\n              children: [(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam4 = currentUser.firstName) === null || _currentUser$firstNam4 === void 0 ? void 0 : _currentUser$firstNam4.charAt(0)) || '', (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName4 = currentUser.lastName) === null || _currentUser$lastName4 === void 0 ? void 0 : _currentUser$lastName4.charAt(0)) || '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2724,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 0.5rem 0',\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#1f2937'\n                },\n                children: [currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2744,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2752,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2741,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2679,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  margin: '0 0 1rem 0',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1f2937',\n                  borderBottom: '2px solid #22c55e',\n                  paddingBottom: '0.5rem'\n                },\n                children: \"Personal Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2771,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: '1fr 1fr',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"First Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2788,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2797,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2787,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"Last Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2810,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2819,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2809,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2832,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2841,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2831,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"Student Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2854,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.studentNumber) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2863,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2853,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"Grade Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2876,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: [\"Grade \", (currentUser === null || currentUser === void 0 ? void 0 : currentUser.grade_level) || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2885,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2875,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      color: '#374151',\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2898,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem',\n                      backgroundColor: '#f9fafb',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      color: '#1f2937'\n                    },\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.phoneNumber) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2907,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2897,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2782,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2770,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  margin: '0 0 1rem 0',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1f2937',\n                  borderBottom: '2px solid #ef4444',\n                  paddingBottom: '0.5rem'\n                },\n                children: \"Change Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2923,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem',\n                  backgroundColor: '#fef2f2',\n                  border: '1px solid #fecaca',\n                  borderRadius: '8px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    fontSize: '0.875rem',\n                    color: '#7f1d1d'\n                  },\n                  children: \"For security reasons, password changes must be requested through your administrator.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2941,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#7f1d1d',\n                    fontWeight: '500'\n                  },\n                  children: \"Please contact the school administration to change your password.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2948,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2934,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2922,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2763,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2672,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderTop: '1px solid #e5e7eb',\n            display: 'flex',\n            justifyContent: 'flex-end'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowProfileSettings(false),\n            style: {\n              padding: '0.75rem 1.5rem',\n              backgroundColor: '#22c55e',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#16a34a';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = '#22c55e';\n            },\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2968,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2962,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2621,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2608,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      isOpen: lightboxOpen,\n      onClose: () => setLightboxOpen(false),\n      altPrefix: \"Announcement Image\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2996,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 811,\n    columnNumber: 5\n  }, this);\n};\n_s3(NewsFeed, \"hZ8KgraueEc2aCATEWOYIR5Z570=\", false, function () {\n  return [useNavigate, useCategories, useAnnouncements, useNotificationTarget];\n});\n_c3 = NewsFeed;\nexport default NewsFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"NewsFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useCallback", "useRef", "useNavigate", "adminHttpClient", "studentHttpClient", "useCategories", "useAnnouncements", "useNotificationTarget", "AdminAuthContext", "StudentAuthContext", "AdminCommentSection", "CommentSection", "NotificationBell", "StudentNotificationBell", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "Settings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "userRole", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "currentBlobUrl", "current", "URL", "revokeObjectURL", "loadImage", "fullUrl", "Error", "console", "log", "authToken", "localStorage", "getItem", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "createObjectURL", "err", "message", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "position", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "transition", "gap", "file_path", "currentTarget", "transform", "top", "left", "right", "bottom", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "_c2", "NewsFeed", "_s3", "_currentUser$firstNam2", "_currentUser$lastName2", "_currentUser$firstNam4", "_currentUser$lastName4", "navigate", "adminAuth", "studentAuth", "currentRole", "isAuthenticated", "currentUser", "user", "currentLogout", "logout", "categories", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "isFromNotification", "scrollTarget", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "showProfileSettings", "setShowProfileSettings", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "fetchCalendarEvents", "undefined", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "pinned", "filter", "ann", "is_pinned", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "minHeight", "background", "padding", "max<PERSON><PERSON><PERSON>", "openLightbox", "initialIndex", "imageUrls", "img", "Boolean", "openLightboxWithUrls", "getCategoryStyle", "categoryName", "styles", "icon", "getHolidayTypeStyle", "holidayTypeName", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "toggleCalendarReaction", "eventId", "currentlyLiked", "client", "endpoint", "delete", "post", "handleCalendarLikeToggle", "user_has_reacted", "newReactionState", "newReactionCount", "reaction_count", "oldReactionState", "oldCount", "newCount", "userId", "prevEvents", "handleLogout", "redirectPath", "href", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "event_date", "eventStartDateString", "eventEndDateString", "end_date", "endDate", "isEventActive", "isActive", "is_active", "displayAnnouncements", "displayEvents", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "size", "type", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "profilePicture", "firstName", "lastName", "onError", "parent", "parentElement", "userIcon", "querySelector", "flexShrink", "_currentUser$firstNam", "_currentUser$lastName", "innerHTML", "char<PERSON>t", "email", "<PERSON><PERSON><PERSON><PERSON>", "is_alert", "category_name", "toUpperCase", "categoryStyle", "substring", "created_at", "toLocaleDateString", "opacity", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "created_by_name", "created_by_picture", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "push", "maxVisible", "filteredImages", "paddingTop", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "flexWrap", "author_name", "author_picture", "posted_by_name", "announcementId", "maxHeight", "stopPropagation", "_currentUser$firstNam3", "_currentUser$lastName3", "paddingBottom", "gridTemplateColumns", "studentNumber", "phoneNumber", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/NewsFeed.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminAuthContext from '../../contexts/AdminAuthContext';\nimport StudentAuthContext from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  // Edit, // Not used in unified component\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut,\n  Settings\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nconst useImageLoader = (imagePath: string | null, userRole?: 'admin' | 'student') => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const currentBlobUrl = useRef<string | null>(null);\n\n  useEffect(() => {\n    // Cleanup previous blob URL if it exists\n    if (currentBlobUrl.current) {\n      URL.revokeObjectURL(currentBlobUrl.current);\n      currentBlobUrl.current = null;\n    }\n\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin'\n          ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n          : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n\n        // Store the blob URL for cleanup\n        currentBlobUrl.current = objectUrl;\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup function\n    return () => {\n      if (currentBlobUrl.current) {\n        URL.revokeObjectURL(currentBlobUrl.current);\n        currentBlobUrl.current = null;\n      }\n    };\n  }, [imagePath, userRole]); // Fixed: removed imageUrl to prevent infinite loop\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  userRole?: 'admin' | 'student';\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath, userRole);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  userRole?: 'admin' | 'student';\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, userRole, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle()}\n          userRole={userRole}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle()}\n                  userRole={userRole}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Props interface for the unified NewsFeed component\ninterface NewsFeedProps {\n  userRole?: 'admin' | 'student'; // Optional prop to override role detection\n}\n\n// Main unified NewsFeed Component\nconst NewsFeed: React.FC<NewsFeedProps> = ({ userRole }) => {\n  const navigate = useNavigate();\n\n  // Safely get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useContext(AdminAuthContext);\n  const studentAuth = useContext(StudentAuthContext);\n\n  // Determine current user role and context\n  const currentRole = userRole ||\n    (adminAuth?.isAuthenticated ? 'admin' :\n     studentAuth?.isAuthenticated ? 'student' : null);\n\n  const currentUser = currentRole === 'admin' ? adminAuth?.user : studentAuth?.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth?.logout : studentAuth?.logout;\n\n  // All hooks must be called before any early returns\n  const { categories } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, scrollTarget } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const [showProfileSettings, setShowProfileSettings] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = useCallback(async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const authToken = currentRole === 'admin'\n        ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n        : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${authToken}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  }, [currentRole]);\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      }}>\n        <div style={{\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        }}>\n          <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>Authentication Required</h2>\n          <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>\n            Please log in to access the newsfeed.\n          </p>\n          <button\n            onClick={() => navigate('/')}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '12px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId: number, currentlyLiked: boolean) => {\n    const client = currentRole === 'admin' ? adminHttpClient : studentHttpClient;\n    const endpoint = `/api/calendar/${eventId}/like`;\n\n    console.log(`[DEBUG] ${currentRole} making direct API call to:`, endpoint);\n    console.log(`[DEBUG] Using ${currentRole} HTTP client`);\n\n    if (currentlyLiked) {\n      // Unlike the event\n      return await client.delete(endpoint);\n    } else {\n      // Like the event\n      return await client.post(endpoint, {});\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      const response = await toggleCalendarReaction(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        const newReactionState = !event.user_has_reacted;\n        const newReactionCount = event.user_has_reacted\n          ? Math.max(0, (event.reaction_count || 0) - 1)\n          : (event.reaction_count || 0) + 1;\n\n        console.log(`[DEBUG] ${currentRole} updating local state:`, {\n          eventId: event.calendar_id,\n          oldReactionState: event.user_has_reacted,\n          newReactionState,\n          oldCount: event.reaction_count,\n          newCount: newReactionCount,\n          userId: currentUser?.id,\n          userRole: currentRole\n        });\n\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: newReactionState,\n                  reaction_count: newReactionCount\n                }\n              : e\n          )\n        );\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      if (currentLogout) {\n        await currentLogout();\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' +\n      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n      String(today.getDate()).padStart(2, '0');\n\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' +\n      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +\n      String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' +\n        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +\n        String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = (event as any).is_active !== 0;\n\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                 {/* ill comment this for now and uncomment it soon */}\n                  {/* {currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'} */}\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories\n                    .filter(category =>\n                      // Hide holiday categories from dropdown\n                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                    )\n                    .map(category => (\n                      <option key={category.category_id} value={category.category_id.toString()}>\n                        {category.name}\n                      </option>\n                    ))\n                  }\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell - Role-aware */}\n                {currentRole === 'admin' ? <NotificationBell /> : <StudentNotificationBell />}\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#22c55e';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    {/* Profile Picture */}\n                    {currentUser?.profilePicture ? (\n                      <img\n                        src={getImageUrl(currentUser.profilePicture) || ''}\n                        alt={`${currentUser.firstName} ${currentUser.lastName}`}\n                        style={{\n                          width: '24px',\n                          height: '24px',\n                          borderRadius: '50%',\n                          objectFit: 'cover',\n                          border: '1px solid #e5e7eb'\n                        }}\n                        onError={(e) => {\n                          const target = e.target as HTMLImageElement;\n                          target.style.display = 'none';\n                          const parent = target.parentElement;\n                          if (parent) {\n                            const userIcon = parent.querySelector('.user-icon');\n                            if (userIcon) {\n                              (userIcon as HTMLElement).style.display = 'block';\n                            }\n                          }\n                        }}\n                      />\n                    ) : null}\n                    <User\n                      size={16}\n                      className=\"user-icon\"\n                      style={{\n                        display: currentUser?.profilePicture ? 'none' : 'block'\n                      }}\n                    />\n                    <span>{currentUser?.firstName || (currentRole === 'admin' ? 'Admin' : 'Student')}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          marginBottom: '0.5rem'\n                        }}>\n                          {/* Profile Picture */}\n                          {currentUser?.profilePicture ? (\n                            <img\n                              src={getImageUrl(currentUser.profilePicture) || ''}\n                              alt={`${currentUser.firstName} ${currentUser.lastName}`}\n                              style={{\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '50%',\n                                objectFit: 'cover',\n                                border: '2px solid #e5e7eb',\n                                flexShrink: 0\n                              }}\n                              onError={(e) => {\n                                const target = e.target as HTMLImageElement;\n                                target.style.display = 'none';\n                                const parent = target.parentElement;\n                                if (parent) {\n                                  parent.innerHTML = `\n                                    <div style=\"\n                                      width: 40px;\n                                      height: 40px;\n                                      border-radius: 50%;\n                                      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                                      display: flex;\n                                      align-items: center;\n                                      justify-content: center;\n                                      color: white;\n                                      font-weight: 600;\n                                      font-size: 1rem;\n                                      flex-shrink: 0;\n                                    \">\n                                      ${currentUser?.firstName?.charAt(0) || ''}${currentUser?.lastName?.charAt(0) || ''}\n                                    </div>\n                                  `;\n                                }\n                              }}\n                            />\n                          ) : (\n                            <div style={{\n                              width: '40px',\n                              height: '40px',\n                              borderRadius: '50%',\n                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              color: 'white',\n                              fontWeight: '600',\n                              fontSize: '1rem',\n                              flexShrink: 0\n                            }}>\n                              {currentUser?.firstName?.charAt(0) || ''}{currentUser?.lastName?.charAt(0) || ''}\n                            </div>\n                          )}\n\n                          <div style={{ flex: 1 }}>\n                            <div style={{\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              color: '#111827'\n                            }}>\n                              {currentUser?.firstName} {currentUser?.lastName}\n                            </div>\n                            <div style={{\n                              fontSize: '0.75rem',\n                              color: '#6b7280'\n                            }}>\n                              {currentUser?.email}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        {/* Role-based menu items */}\n                        {currentRole === 'admin' ? (\n                          // Admin: Show Dashboard button\n                          <button\n                            onClick={() => {\n                              navigate('/admin/dashboard');\n                              setShowUserDropdown(false);\n                            }}\n                            style={{\n                              width: '100%',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.75rem',\n                              padding: '0.75rem 1rem',\n                              background: 'transparent',\n                              border: 'none',\n                              color: '#374151',\n                              fontSize: '0.875rem',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = '#f3f4f6';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'transparent';\n                            }}\n                          >\n                            <LayoutDashboard size={16} />\n                            Dashboard\n                          </button>\n                        ) : (\n                          // Student: Show Profile Settings button\n                          <button\n                            onClick={() => {\n                              setShowProfileSettings(true);\n                              setShowUserDropdown(false);\n                            }}\n                            style={{\n                              width: '100%',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.75rem',\n                              padding: '0.75rem 1rem',\n                              background: 'transparent',\n                              border: 'none',\n                              color: '#374151',\n                              fontSize: '0.875rem',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = '#f3f4f6';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'transparent';\n                            }}\n                          >\n                            <Settings size={16} />\n                            Profile Settings\n                          </button>\n                        )}\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement) => {\n                      // Handle alert announcements with special styling\n                      const isAlert = announcement.is_alert;\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: isAlert ? '#fef2f2' : '#f8fafc',\n                            borderRadius: '12px',\n                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  refreshAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) - Commented out for now */}\n          {/* Future feature: Recent student registrations for admin dashboard */}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.category_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            {/* Author Information - Show for all users */}\n                            {event.created_by_name && (\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px',\n                                fontSize: '0.75rem',\n                                color: '#3b82f6'\n                              }}>\n                                {event.created_by_picture ? (\n                                  <img\n                                    src={getImageUrl(event.created_by_picture) || ''}\n                                    alt={event.created_by_name}\n                                    style={{\n                                      width: '16px',\n                                      height: '16px',\n                                      borderRadius: '50%',\n                                      objectFit: 'cover'\n                                    }}\n                                    onError={(e) => {\n                                      const target = e.target as HTMLImageElement;\n                                      target.style.display = 'none';\n                                    }}\n                                  />\n                                ) : (\n                                  <User size={12} />\n                                )}\n                                <span style={{ fontWeight: '500' }}>\n                                  {event.created_by_name}\n                                </span>\n                              </div>\n                            )}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightboxWithUrls(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.category_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.category_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Interaction Section */}\n                      <div style={{\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      }}>\n                        {/* Like Button */}\n                        <button\n                          onClick={() => handleCalendarLikeToggle(event)}\n                          style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            background: 'none',\n                            border: 'none',\n                            color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                            cursor: 'pointer',\n                            padding: '0.5rem',\n                            borderRadius: '8px',\n                            transition: 'all 0.2s ease',\n                            fontSize: '0.875rem',\n                            fontWeight: '500'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'none';\n                          }}\n                        >\n                          <Heart\n                            size={18}\n                            fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                          />\n                          <span>{(event as any).reaction_count || 0}</span>\n                        </button>\n\n                        {/* Comments Button */}\n                        {(event as any).allow_comments && (\n                          <button\n                            onClick={() => setShowCalendarComments(\n                              showCalendarComments === event.calendar_id ? null : event.calendar_id\n                            )}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                            }}\n                          >\n                            <MessageSquare size={18} />\n                            <span>{(event as any).comment_count || 0}</span>\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Comments Section */}\n                      {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            {/* Author Information - Show for all users */}\n                            {announcement.author_name && (\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'rgba(34, 197, 94, 0.1)',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px',\n                                fontSize: '0.75rem',\n                                color: '#16a34a'\n                              }}>\n                                {announcement.author_picture ? (\n                                  <img\n                                    src={getImageUrl(announcement.author_picture) || ''}\n                                    alt={announcement.author_name}\n                                    style={{\n                                      width: '16px',\n                                      height: '16px',\n                                      borderRadius: '50%',\n                                      objectFit: 'cover'\n                                    }}\n                                    onError={(e) => {\n                                      const target = e.target as HTMLImageElement;\n                                      target.style.display = 'none';\n                                    }}\n                                  />\n                                ) : (\n                                  <User size={12} />\n                                )}\n                                <span style={{ fontWeight: '500' }}>\n                                  {announcement.author_name}\n                                </span>\n                              </div>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          userRole={currentRole}\n                          onImageClick={(index) => {\n                            openLightbox(announcement.attachments || [], index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {(announcement as any).posted_by_name || announcement.author_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    userRole={currentRole}\n                    onImageClick={(index) => {\n                      openLightbox(selectedPinnedPost.attachments, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Student Profile Settings Modal */}\n      {showProfileSettings && currentRole === 'student' && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '1rem'\n        }}>\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n            maxWidth: '800px',\n            width: '100%',\n            maxHeight: '90vh',\n            overflow: 'auto'\n          }}>\n            {/* Modal Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <h2 style={{\n                margin: 0,\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#1f2937'\n              }}>\n                Profile Settings\n              </h2>\n              <button\n                onClick={() => setShowProfileSettings(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  cursor: 'pointer',\n                  color: '#6b7280',\n                  padding: '0.5rem',\n                  borderRadius: '8px',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.backgroundColor = '#f3f4f6';\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.backgroundColor = 'transparent';\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Modal Content */}\n            <div style={{\n              padding: '1.5rem',\n              display: 'flex',\n              gap: '2rem',\n              alignItems: 'flex-start'\n            }}>\n              {/* Profile Picture Section */}\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem',\n                minWidth: '200px'\n              }}>\n                {/* Profile Picture */}\n                {currentUser?.profilePicture ? (\n                  <img\n                    src={getImageUrl(currentUser.profilePicture) || ''}\n                    alt={`${currentUser.firstName} ${currentUser.lastName}`}\n                    style={{\n                      width: '150px',\n                      height: '150px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '4px solid #e5e7eb'\n                    }}\n                    onError={(e) => {\n                      const target = e.target as HTMLImageElement;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        parent.innerHTML = `\n                          <div style=\"\n                            width: 150px;\n                            height: 150px;\n                            border-radius: 50%;\n                            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                            display: flex;\n                            align-items: center;\n                            justify-content: center;\n                            color: white;\n                            font-weight: 600;\n                            font-size: 3rem;\n                            border: 4px solid #e5e7eb;\n                          \">\n                            ${currentUser?.firstName?.charAt(0) || ''}${currentUser?.lastName?.charAt(0) || ''}\n                          </div>\n                        `;\n                      }\n                    }}\n                  />\n                ) : (\n                  <div style={{\n                    width: '150px',\n                    height: '150px',\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontWeight: '600',\n                    fontSize: '3rem',\n                    border: '4px solid #e5e7eb'\n                  }}>\n                    {currentUser?.firstName?.charAt(0) || ''}{currentUser?.lastName?.charAt(0) || ''}\n                  </div>\n                )}\n\n                <div style={{\n                  textAlign: 'center'\n                }}>\n                  <h3 style={{\n                    margin: '0 0 0.5rem 0',\n                    fontSize: '1.25rem',\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  }}>\n                    {currentUser?.firstName} {currentUser?.lastName}\n                  </h3>\n                  <p style={{\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  }}>\n                    Student\n                  </p>\n                </div>\n              </div>\n\n              {/* Information Section */}\n              <div style={{\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              }}>\n                {/* Personal Information */}\n                <div>\n                  <h4 style={{\n                    margin: '0 0 1rem 0',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937',\n                    borderBottom: '2px solid #22c55e',\n                    paddingBottom: '0.5rem'\n                  }}>\n                    Personal Information\n                  </h4>\n\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 1fr',\n                    gap: '1rem'\n                  }}>\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        First Name\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        {currentUser?.firstName || 'N/A'}\n                      </div>\n                    </div>\n\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        Last Name\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        {currentUser?.lastName || 'N/A'}\n                      </div>\n                    </div>\n\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        Email\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        {currentUser?.email || 'N/A'}\n                      </div>\n                    </div>\n\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        Student Number\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        {currentUser?.studentNumber || 'N/A'}\n                      </div>\n                    </div>\n\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        Grade Level\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        Grade {currentUser?.grade_level || 'N/A'}\n                      </div>\n                    </div>\n\n                    <div>\n                      <label style={{\n                        display: 'block',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        color: '#374151',\n                        marginBottom: '0.25rem'\n                      }}>\n                        Phone Number\n                      </label>\n                      <div style={{\n                        padding: '0.75rem',\n                        backgroundColor: '#f9fafb',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem',\n                        color: '#1f2937'\n                      }}>\n                        {currentUser?.phoneNumber || 'N/A'}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Password Change Section */}\n                <div>\n                  <h4 style={{\n                    margin: '0 0 1rem 0',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937',\n                    borderBottom: '2px solid #ef4444',\n                    paddingBottom: '0.5rem'\n                  }}>\n                    Change Password\n                  </h4>\n\n                  <div style={{\n                    padding: '1rem',\n                    backgroundColor: '#fef2f2',\n                    border: '1px solid #fecaca',\n                    borderRadius: '8px',\n                    textAlign: 'center'\n                  }}>\n                    <p style={{\n                      margin: '0 0 1rem 0',\n                      fontSize: '0.875rem',\n                      color: '#7f1d1d'\n                    }}>\n                      For security reasons, password changes must be requested through your administrator.\n                    </p>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '0.875rem',\n                      color: '#7f1d1d',\n                      fontWeight: '500'\n                    }}>\n                      Please contact the school administration to change your password.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Modal Footer */}\n            <div style={{\n              padding: '1.5rem',\n              borderTop: '1px solid #e5e7eb',\n              display: 'flex',\n              justifyContent: 'flex-end'\n            }}>\n              <button\n                onClick={() => setShowProfileSettings(false)}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: '#22c55e',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.backgroundColor = '#16a34a';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.backgroundColor = '#22c55e';\n                }}\n              >\n                Close\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Announcement Image\"\n      />\n    </div>\n  );\n};\n\nexport default NewsFeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACnF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C;;AAEA,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC/E,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,uBAAuB,MAAM,6BAA6B;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAG3C,SAASC,WAAW,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAChH,OAAO,wCAAwC;AAC/C,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK;AACL;AACAC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,QAAQ,QACH,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAwB,EAAEC,QAA8B,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM2D,cAAc,GAAGvD,MAAM,CAAgB,IAAI,CAAC;EAElDH,SAAS,CAAC,MAAM;IACd;IACA,IAAI0D,cAAc,CAACC,OAAO,EAAE;MAC1BC,GAAG,CAACC,eAAe,CAACH,cAAc,CAACC,OAAO,CAAC;MAC3CD,cAAc,CAACC,OAAO,GAAG,IAAI;IAC/B;IAEA,IAAI,CAACV,SAAS,EAAE;MACdI,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMS,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAMM,OAAO,GAAG7C,WAAW,CAAC+B,SAAS,CAAC;QACtC,IAAI,CAACc,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,SAAS,GAAGjB,QAAQ,KAAK,OAAO,GAClCkB,YAAY,CAACC,OAAO,CAACjD,oBAAoB,CAAC,GAC1CgD,YAAY,CAACC,OAAO,CAAChD,sBAAsB,CAAC;;QAEhD;QACA,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAACR,OAAO,EAAE;UACpCS,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,SAAS,EAAE;YACtC,QAAQ,EAAEO,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAId,KAAK,CAAC,QAAQM,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGtB,GAAG,CAACuB,eAAe,CAACF,IAAI,CAAC;;QAE3C;QACAvB,cAAc,CAACC,OAAO,GAAGuB,SAAS;QAClC7B,WAAW,CAAC6B,SAAS,CAAC;QAEtBjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOkB,GAAG,EAAE;QACZnB,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAE4B,GAAG,CAAC;QAC3C3B,QAAQ,CAAC2B,GAAG,YAAYpB,KAAK,GAAGoB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACR9B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIJ,cAAc,CAACC,OAAO,EAAE;QAC1BC,GAAG,CAACC,eAAe,CAACH,cAAc,CAACC,OAAO,CAAC;QAC3CD,cAAc,CAACC,OAAO,GAAG,IAAI;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACV,SAAS,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE3B,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CAhFMH,cAAc;AA4FpB,MAAMsC,YAAyC,GAAGA,CAAC;EACjDrC,SAAS;EACTsC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTvC,QAAQ;EACRwC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAEzC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGR,cAAc,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAExE,IAAII,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBtD,OAAA;QAAK2C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCtD,OAAA;UAAK2C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE7D,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlD,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEP,OAAA;MAAK2C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBtD,OAAA;QAAK2C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCtD,OAAA;UAAK2C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrE7D,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/ElD,KAAK,iBACJX,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzE3C;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7D,OAAA;IACEiE,GAAG,EAAE1D,QAAS;IACdmC,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CAtEMP,YAAyC;EAAA,QAURtC,cAAc;AAAA;AAAAgE,EAAA,GAV/C1B,YAAyC;AA8E/C,MAAM2B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEjE,QAAQ;EAAEkE;AAAa,CAAC,KAAK;EACnG,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEd,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIR,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAA2B;IAC/C,OAAO;MACLF,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACE1F,OAAA;IAAK2C,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf0C,GAAG,EAAE,KAAK;MACVL,KAAK,EAAE,MAAM;MACb9B,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAtD,OAAA;MAAK2C,KAAK,EAAEmC,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAAlB,QAAA,gBACrDtD,OAAA,CAACyC,YAAY;QACXrC,SAAS,EAAEqE,aAAa,CAAC,CAAC,CAAC,CAACmB,SAAU;QACtClD,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAE6C,aAAa,CAAC,CAAE;QACvBnF,QAAQ,EAAEA,QAAS;QACnByC,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;QACjD,CAAE;QACF/C,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDU,YAAY,iBACXvE,OAAA;QACE2C,KAAK,EAAE;UACLuC,QAAQ,EAAE,UAAU;UACpBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAAC;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLY,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBxE,OAAA;MAAK2C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE3B,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DmB,GAAG,EAAE,KAAK;QACVL,KAAK,EAAEb,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAlB,QAAA,EACCmB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK/B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE3E,OAAA;UAEE2C,KAAK,EAAE;YACL,GAAGmC,iBAAiB,CAAC0B,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAC;YACvDU,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBAEFtD,OAAA,CAACyC,YAAY;YACXrC,SAAS,EAAEkG,KAAK,CAACV,SAAU;YAC3BlD,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAE6C,aAAa,CAAC,CAAE;YACvBnF,QAAQ,EAAEA,QAAS;YACnByC,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;YACjD,CAAE;YACF/C,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD4C,MAAM,IAAI9B,cAAc,GAAG,CAAC,iBAC3B3E,OAAA;YAAK2C,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT9C,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdI,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,GACA,EAACqB,cAAc;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAU,YAAY,iBACXvE,OAAA;YACE2C,KAAK,EAAE;cACLuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACiC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAnDI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA6C,GAAA,GAzJMtC,YAAyC;AA8J/C;AACA,MAAMuC,QAAiC,GAAGA,CAAC;EAAEtG;AAAS,CAAC,KAAK;EAAAuG,GAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC1D,MAAMC,QAAQ,GAAG1J,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM2J,SAAS,GAAG9J,UAAU,CAACS,gBAAgB,CAAC;EAC9C,MAAMsJ,WAAW,GAAG/J,UAAU,CAACU,kBAAkB,CAAC;;EAElD;EACA,MAAMsJ,WAAW,GAAG/G,QAAQ,KACzB6G,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEG,eAAe,GAAG,OAAO,GACpCF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC;EAEnD,MAAMC,WAAW,GAAGF,WAAW,KAAK,OAAO,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,IAAI,GAAGJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI;EACjF,MAAMC,aAAa,GAAGJ,WAAW,KAAK,OAAO,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,MAAM,GAAGN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,MAAM;;EAEvF;EACA,MAAM;IAAEC;EAAW,CAAC,GAAGhK,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IACJiK,aAAa;IACblH,OAAO;IACPE,KAAK;IACLiH,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAGpK,gBAAgB,CAAC;IACnBuE,MAAM,EAAE,WAAW;IACnB8F,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAEf,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAM;IAAEgB,kBAAkB;IAAEC;EAAa,CAAC,GAAGzK,qBAAqB,CAAC,CAAC;;EAEpE;EACA,MAAM,CAAC0K,cAAc,EAAEC,iBAAiB,CAAC,GAAGrL,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACsL,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvL,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACwL,UAAU,EAAEC,aAAa,CAAC,GAAGzL,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAAC0L,YAAY,EAAEC,eAAe,CAAC,GAAG3L,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC4L,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7L,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAAC8L,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/L,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAACgM,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjM,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnM,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACoM,YAAY,EAAEC,eAAe,CAAC,GAAGrM,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsM,cAAc,EAAEC,iBAAiB,CAAC,GAAGvM,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACwM,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzM,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC0M,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3M,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAAC4M,cAAc,EAAEC,iBAAiB,CAAC,GAAG7M,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAAC8M,eAAe,EAAEC,kBAAkB,CAAC,GAAG/M,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgN,aAAa,EAAEC,gBAAgB,CAAC,GAAGjN,QAAQ,CAAqB,CAAC;EACxE;;EAEA;EACA,MAAMkN,mBAAmB,GAAG/M,WAAW,CAAC,YAAY;IAClD,IAAI;MACF4M,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACE,SAAS,CAAC;MAE3B,MAAM/I,SAAS,GAAG8F,WAAW,KAAK,OAAO,GACrC7F,YAAY,CAACC,OAAO,CAACjD,oBAAoB,CAAC,GAC1CgD,YAAY,CAACC,OAAO,CAAChD,sBAAsB,CAAC;MAEhD,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGpD,YAAY,0DAA0D,EAAE;QACtGsD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUN,SAAS,EAAE;UACtC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMgJ,IAAI,GAAG,MAAM7I,QAAQ,CAAC8I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAACpE,GAAG,CAAC,MAAOyE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMrJ,KAAK,CAAC,GAAGpD,YAAY,iBAAiBwM,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FpJ,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUN,SAAS,EAAE;gBACtC,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAM2J,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACzG,MAAM,GAAG4G,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACzG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO8G,MAAM,EAAE;YACf/J,OAAO,CAACgK,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACzG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOyG,KAAK;QACd,CAAC,CACH,CAAC;QAEDf,iBAAiB,CAACY,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLR,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAO5H,GAAQ,EAAE;MACjBnB,OAAO,CAACT,KAAK,CAAC,iCAAiC,EAAE4B,GAAG,CAAC;MACrD4H,gBAAgB,CAAC5H,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRyH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAAC7C,WAAW,CAAC,CAAC;;EAEjB;EACAjK,SAAS,CAAC,MAAM;IACd,MAAMkO,MAAM,GAAG1D,aAAa,CAAC2D,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtE3B,sBAAsB,CAACwB,MAAM,CAAC;EAChC,CAAC,EAAE,CAAC1D,aAAa,CAAC,CAAC;;EAEnB;EACAxK,SAAS,CAAC,MAAM;IACdiN,mBAAmB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAChD,WAAW,EAAEgD,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExC;EACAjN,SAAS,CAAC,MAAM;IACd,MAAMsO,kBAAkB,GAAIX,KAAiB,IAAK;MAChD,IAAI5B,gBAAgB,EAAE;QACpB,MAAMwC,MAAM,GAAGZ,KAAK,CAACY,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtDxC,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAEDyC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACvC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,IAAI,CAAC9B,WAAW,EAAE;IAChB,oBACEpH,OAAA;MAAK2C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpB6I,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE;MACd,CAAE;MAAA1I,QAAA,eACAtD,OAAA;QAAK2C,KAAK,EAAE;UACVqJ,UAAU,EAAE,OAAO;UACnBC,OAAO,EAAE,MAAM;UACf7G,YAAY,EAAE,MAAM;UACpBtB,MAAM,EAAE,mBAAmB;UAC3BP,SAAS,EAAE,QAAQ;UACnB2I,QAAQ,EAAE;QACZ,CAAE;QAAA5I,QAAA,gBACAtD,OAAA;UAAI2C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF7D,OAAA;UAAG2C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7D,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAMc,QAAQ,CAAC,GAAG,CAAE;UAC7BtE,KAAK,EAAE;YACLqJ,UAAU,EAAE,mDAAmD;YAC/D3I,KAAK,EAAE,OAAO;YACdS,MAAM,EAAE,MAAM;YACdsB,YAAY,EAAE,MAAM;YACpB6G,OAAO,EAAE,gBAAgB;YACzBxI,QAAQ,EAAE,UAAU;YACpBM,UAAU,EAAE,KAAK;YACjBsB,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMsI,YAAY,GAAGA,CAAC9H,MAAgC,EAAE+H,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAGhI,MAAM,CAACgC,GAAG,CAACiG,GAAG,IAAIjO,WAAW,CAACiO,GAAG,CAAC1G,SAAS,CAAC,CAAC,CAAC0F,MAAM,CAACiB,OAAO,CAAa;IAC3F9C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiD,oBAAoB,GAAGA,CAACH,SAAmB,EAAED,YAAoB,KAAK;IAC1E3C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE3N;MACR,CAAC;MACD,SAAS,EAAE;QACT+M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE7N;MACR,CAAC;MACD,QAAQ,EAAE;QACRiN,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE1N;MACR,CAAC;MACD,WAAW,EAAE;QACX8M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEzN;MACR,CAAC;MACD,QAAQ,EAAE;QACR6M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEvN;MACR,CAAC;MACD,WAAW,EAAE;QACX2M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAExN;MACR;IACF,CAAC;IAED,OAAOuN,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMH,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEpN;MACR,CAAC;MACD,cAAc,EAAE;QACdwM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAErN;MACR,CAAC;MACD,gBAAgB,EAAE;QAChByM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEnN;MACR,CAAC;MACD,cAAc,EAAE;QACduM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEvN;MACR,CAAC;MACD,YAAY,EAAE;QACZ2M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAElN;MACR,CAAC;MACD,SAAS,EAAE;QACTsM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEtN;MACR;IACF,CAAC;IAED,OAAOqN,MAAM,CAACG,eAAe,CAAwB,IAAIH,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;;EAEA;EACA,MAAMI,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF5L,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,sCAAsC,EAAE4F,YAAY,CAACC,eAAe,CAAC;MACvG7L,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2L,YAAY,CAACE,aAAa,CAAC;MACzE9L,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,gBAAgB,EAAE;QAAE+F,EAAE,EAAE7F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAE;QAAEC,IAAI,EAAEhG;MAAY,CAAC,CAAC;MAE/F,IAAI4F,YAAY,CAACE,aAAa,EAAE;QAC9B;QACA9L,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,uBAAuB,CAAC;QAC1D,MAAMS,kBAAkB,CAACmF,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACA7L,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,qBAAqB,CAAC;QACxD,MAAMQ,gBAAgB,CAACoF,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEA7L,OAAO,CAACC,GAAG,CAAC,aAAa+F,WAAW,gCAAgC,CAAC;IACvE,CAAC,CAAC,OAAOzG,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,WAAWyG,WAAW,uBAAuB,EAAEzG,KAAK,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM0M,sBAAsB,GAAG,MAAAA,CAAOC,OAAe,EAAEC,cAAuB,KAAK;IACjF,MAAMC,MAAM,GAAGpG,WAAW,KAAK,OAAO,GAAG5J,eAAe,GAAGC,iBAAiB;IAC5E,MAAMgQ,QAAQ,GAAG,iBAAiBH,OAAO,OAAO;IAEhDlM,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,6BAA6B,EAAEqG,QAAQ,CAAC;IAC1ErM,OAAO,CAACC,GAAG,CAAC,iBAAiB+F,WAAW,cAAc,CAAC;IAEvD,IAAImG,cAAc,EAAE;MAClB;MACA,OAAO,MAAMC,MAAM,CAACE,MAAM,CAACD,QAAQ,CAAC;IACtC,CAAC,MAAM;MACL;MACA,OAAO,MAAMD,MAAM,CAACG,IAAI,CAACF,QAAQ,EAAE,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMG,wBAAwB,GAAG,MAAO9C,KAAU,IAAK;IACrD,IAAI;MACF1J,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,wCAAwC,EAAE0D,KAAK,CAACE,WAAW,CAAC;MAC9F5J,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyJ,KAAK,CAAC+C,gBAAgB,CAAC;MACxEzM,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,gBAAgB,EAAE;QAAE+F,EAAE,EAAE7F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAE;QAAEC,IAAI,EAAEhG;MAAY,CAAC,CAAC;MAE/F,MAAM3F,QAAQ,GAAG,MAAM4L,sBAAsB,CAACvC,KAAK,CAACE,WAAW,EAAEF,KAAK,CAAC+C,gBAAgB,IAAI,KAAK,CAAC;MAEjG,IAAIpM,QAAQ,CAAC+I,OAAO,EAAE;QACpB,MAAMsD,gBAAgB,GAAG,CAAChD,KAAK,CAAC+C,gBAAgB;QAChD,MAAME,gBAAgB,GAAGjD,KAAK,CAAC+C,gBAAgB,GAC3CjJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACiG,KAAK,CAACkD,cAAc,IAAI,CAAC,IAAI,CAAC,CAAC,GAC5C,CAAClD,KAAK,CAACkD,cAAc,IAAI,CAAC,IAAI,CAAC;QAEnC5M,OAAO,CAACC,GAAG,CAAC,WAAW+F,WAAW,wBAAwB,EAAE;UAC1DkG,OAAO,EAAExC,KAAK,CAACE,WAAW;UAC1BiD,gBAAgB,EAAEnD,KAAK,CAAC+C,gBAAgB;UACxCC,gBAAgB;UAChBI,QAAQ,EAAEpD,KAAK,CAACkD,cAAc;UAC9BG,QAAQ,EAAEJ,gBAAgB;UAC1BK,MAAM,EAAE9G,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAE;UACvB9M,QAAQ,EAAE+G;QACZ,CAAC,CAAC;;QAEF;QACA2C,iBAAiB,CAACsE,UAAU,IAC1BA,UAAU,CAAChI,GAAG,CAACnC,CAAC,IACdA,CAAC,CAAC8G,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAG9G,CAAC;UACJ2J,gBAAgB,EAAEC,gBAAgB;UAClCE,cAAc,EAAED;QAClB,CAAC,GACD7J,CACN,CACF,CAAC;QACD9C,OAAO,CAACC,GAAG,CAAC,aAAa+F,WAAW,+CAA+C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOzG,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,WAAWyG,WAAW,gCAAgC,EAAEzG,KAAK,CAAC;IAC9E;EACF,CAAC;;EAED;EACA,MAAM2N,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI9G,aAAa,EAAE;QACjB,MAAMA,aAAa,CAAC,CAAC;MACvB;IACF,CAAC,CAAC,OAAO7G,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACA,MAAM4N,YAAY,GAAGnH,WAAW,KAAK,OAAO,GAAG,cAAc,GAAG,gBAAgB;MAChFvF,MAAM,CAACC,QAAQ,CAAC0M,IAAI,GAAGD,YAAY;IACrC;EACF,CAAC;;EAED;;EAIA;EACA,MAAME,qBAAqB,GAAG9G,aAAa,CAAC2D,MAAM,CAAC0B,YAAY,IAAI;IAAA,IAAA0B,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAAClG,UAAU,IAC/BsE,YAAY,CAAC6B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAC,IACnE9B,YAAY,CAACgC,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAAC3G,cAAc,IACrC,EAAAoG,qBAAA,GAAA1B,YAAY,CAACkC,WAAW,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BS,QAAQ,CAAC,CAAC,MAAK7G,cAAc;IAEzD,MAAM8G,iBAAiB,GAAG,CAAC5G,gBAAgB,IACzC,EAAAmG,qBAAA,GAAA3B,YAAY,CAACqC,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAK3G,gBAAgB;IAE3D,OAAOoG,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGxF,cAAc,CAACwB,MAAM,CAACR,KAAK,IAAI;IAC5D,MAAM8D,aAAa,GAAG,CAAClG,UAAU,IAC/BoC,KAAK,CAAC+D,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC3DhE,KAAK,CAACyE,WAAW,IAAIzE,KAAK,CAACyE,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACJ,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1C,MAAME,cAAc,GAAG,IAAIP,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC;IACjD,MAAMC,oBAAoB,GAAGF,cAAc,CAACL,WAAW,CAAC,CAAC,GAAG,GAAG,GAC7DC,MAAM,CAACI,cAAc,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAC5DF,MAAM,CAACI,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMK,kBAAkB,GAAGrF,KAAK,CAACsF,QAAQ,GAAG,CAAC,MAAM;MACjD,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAAC3E,KAAK,CAACsF,QAAQ,CAAC;MACxC,OAAOC,OAAO,CAACV,WAAW,CAAC,CAAC,GAAG,GAAG,GAChCC,MAAM,CAACS,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACrDF,MAAM,CAACS,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9C,CAAC,EAAE,CAAC,GAAGI,oBAAoB;;IAE3B;IACA,MAAMI,aAAa,GAAGZ,eAAe,IAAIQ,oBAAoB,IAAIR,eAAe,IAAIS,kBAAkB;;IAEtG;IACA,MAAMI,QAAQ,GAAIzF,KAAK,CAAS0F,SAAS,KAAK,CAAC;IAE/C,OAAO5B,aAAa,IAAI0B,aAAa,IAAIC,QAAQ;EACnD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGhC,qBAAqB;EAClD,MAAMiC,aAAa,GAAGpB,sBAAsB;;EAI5C;EACA;EACA;EACA;EACA;;EAEA,oBACEtP,OAAA;IAAK2C,KAAK,EAAE;MACVoJ,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/D9G,QAAQ,EAAE;IACZ,CAAE;IAAA5B,QAAA,gBAEAtD,OAAA;MAAK2C,KAAK,EAAE;QACVuC,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTyK,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAlN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL7D,OAAA;MAAK2C,KAAK,EAAE;QAAEuC,QAAQ,EAAE,UAAU;QAAE2L,MAAM,EAAE;MAAE,CAAE;MAAAvN,QAAA,gBAE9CtD,OAAA;QAAQ2C,KAAK,EAAE;UACbqJ,UAAU,EAAE,OAAO;UACnB8E,YAAY,EAAE,mBAAmB;UACjC5L,QAAQ,EAAE,QAAQ;UAClBa,GAAG,EAAE,CAAC;UACN8K,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAAzN,QAAA,eACAtD,OAAA;UAAK2C,KAAK,EAAE;YACVsJ,OAAO,EAAE,QAAQ;YACjB1G,MAAM,EAAE,MAAM;YACdtC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBAEAtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,QAAQ;cACbqL,QAAQ,EAAE;YACZ,CAAE;YAAA1N,QAAA,gBACAtD,OAAA;cACEiE,GAAG,EAAC,iBAAiB;cACrBvB,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACL2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7D,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAI2C,KAAK,EAAE;kBACTsO,MAAM,EAAE,CAAC;kBACTxN,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChB6N,UAAU,EAAE;gBACd,CAAE;gBAAA5N,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRsO,MAAM,EAAE,CAAC;kBACTxN,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE,SAAS;kBAChB6N,UAAU,EAAE;gBACd;cAAE;gBAAAxN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAK2C,KAAK,EAAE;cACVwO,IAAI,EAAE,CAAC;cACPjF,QAAQ,EAAE,OAAO;cACjB+E,MAAM,EAAE;YACV,CAAE;YAAA3N,QAAA,eACAtD,OAAA;cAAK2C,KAAK,EAAE;gBAAEuC,QAAQ,EAAE;cAAW,CAAE;cAAA5B,QAAA,gBACnCtD,OAAA,CAACtB,MAAM;gBACL0S,IAAI,EAAE,EAAG;gBACTzO,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBc,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVD,SAAS,EAAE,kBAAkB;kBAC7BzC,KAAK,EAAE;gBACT;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7D,OAAA;gBACEqR,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,aAAa;gBACzBC,KAAK,EAAE7I,UAAW;gBAClB8I,QAAQ,EAAGtN,CAAC,IAAKyE,aAAa,CAACzE,CAAC,CAACwH,MAAM,CAAC6F,KAAK,CAAE;gBAC/C5O,KAAK,EAAE;kBACL2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd0G,OAAO,EAAE,eAAe;kBACxBnI,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,MAAM;kBACpB4G,UAAU,EAAE,SAAS;kBACrB3I,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgO,OAAO,EAAE,MAAM;kBACf/L,UAAU,EAAE;gBACd,CAAE;gBACFgM,OAAO,EAAGxN,CAAC,IAAK;kBACdA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;kBAC7CzN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,OAAO;kBAC1C9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFa,MAAM,EAAG1N,CAAC,IAAK;kBACbA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;kBAC7CzN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;kBAC5C9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAArN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXqL,QAAQ,EAAE,OAAO;cACjB7N,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAGAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbsG,OAAO,EAAE,QAAQ;gBACjBD,UAAU,EAAE,SAAS;gBACrB5G,YAAY,EAAE,MAAM;gBACpBtB,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACAtD,OAAA;gBACEuR,KAAK,EAAEjJ,cAAe;gBACtBkJ,QAAQ,EAAGtN,CAAC,IAAKqE,iBAAiB,CAACrE,CAAC,CAACwH,MAAM,CAAC6F,KAAK,CAAE;gBACnD5O,KAAK,EAAE;kBACLsJ,OAAO,EAAE,gBAAgB;kBACzBnI,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB4G,UAAU,EAAE,OAAO;kBACnB3I,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgO,OAAO,EAAE,MAAM;kBACfpM,MAAM,EAAE,SAAS;kBACjB2L,QAAQ,EAAE;gBACZ,CAAE;gBAAA1N,QAAA,gBAEFtD,OAAA;kBAAQuR,KAAK,EAAC,EAAE;kBAAAjO,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvC6D,UAAU,CACR4D,MAAM,CAACuG,QAAQ;gBACd;gBACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC9C,QAAQ,CAAC8C,QAAQ,CAACC,IAAI,CACjG,CAAC,CACAzL,GAAG,CAACwL,QAAQ,iBACX7R,OAAA;kBAAmCuR,KAAK,EAAEM,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAA7L,QAAA,EACvEuO,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC3C,WAAW;kBAAAxL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eAET7D,OAAA;gBACEuR,KAAK,EAAE/I,gBAAiB;gBACxBgJ,QAAQ,EAAGtN,CAAC,IAAKuE,mBAAmB,CAACvE,CAAC,CAACwH,MAAM,CAAC6F,KAAK,CAAE;gBACrD5O,KAAK,EAAE;kBACLsJ,OAAO,EAAE,gBAAgB;kBACzBnI,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB4G,UAAU,EAAE,OAAO;kBACnB3I,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgO,OAAO,EAAE,MAAM;kBACfpM,MAAM,EAAE,SAAS;kBACjB2L,QAAQ,EAAE;gBACZ,CAAE;gBAAA1N,QAAA,gBAEFtD,OAAA;kBAAQuR,KAAK,EAAC,EAAE;kBAAAjO,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7D,OAAA;kBAAQuR,KAAK,EAAC,IAAI;kBAAAjO,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7D,OAAA;kBAAQuR,KAAK,EAAC,IAAI;kBAAAjO,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAAC6E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDxI,OAAA;gBACEmG,OAAO,EAAEA,CAAA,KAAM;kBACbwC,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACF9F,KAAK,EAAE;kBACLsJ,OAAO,EAAE,gBAAgB;kBACzBnI,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB4G,UAAU,EAAE,SAAS;kBACrB3I,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBsB,MAAM,EAAE,SAAS;kBACjBK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACFjJ,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAA1I,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7D,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,GAEC8D,WAAW,KAAK,OAAO,gBAAGpH,OAAA,CAAC/B,gBAAgB;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG7D,OAAA,CAAC9B,uBAAuB;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG7E7D,OAAA;gBAAK2C,KAAK,EAAE;kBAAEuC,QAAQ,EAAE;gBAAW,CAAE;gBAAC,iBAAc,eAAe;gBAAA5B,QAAA,gBACjEtD,OAAA;kBACEmG,OAAO,EAAEA,CAAA,KAAMgD,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDvG,KAAK,EAAE;oBACLM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbsG,OAAO,EAAE,cAAc;oBACvBD,UAAU,EAAE,OAAO;oBACnBlI,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB/B,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE,eAAe;oBAC3BqL,SAAS,EAAE;kBACb,CAAE;kBACFjO,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;oBAC7CzN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,kCAAkC;kBACtE,CAAE;kBACFhO,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;oBAC7CzN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,8BAA8B;kBAClE,CAAE;kBAAAzN,QAAA,GAGDgE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyK,cAAc,gBAC1B/R,OAAA;oBACEiE,GAAG,EAAE5F,WAAW,CAACiJ,WAAW,CAACyK,cAAc,CAAC,IAAI,EAAG;oBACnDrP,GAAG,EAAE,GAAG4E,WAAW,CAAC0K,SAAS,IAAI1K,WAAW,CAAC2K,QAAQ,EAAG;oBACxDtP,KAAK,EAAE;sBACL2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdH,YAAY,EAAE,KAAK;sBACnBK,SAAS,EAAE,OAAO;sBAClB3B,MAAM,EAAE;oBACV,CAAE;oBACFoO,OAAO,EAAGhO,CAAC,IAAK;sBACd,MAAMwH,MAAM,GAAGxH,CAAC,CAACwH,MAA0B;sBAC3CA,MAAM,CAAC/I,KAAK,CAACM,OAAO,GAAG,MAAM;sBAC7B,MAAMkP,MAAM,GAAGzG,MAAM,CAAC0G,aAAa;sBACnC,IAAID,MAAM,EAAE;wBACV,MAAME,QAAQ,GAAGF,MAAM,CAACG,aAAa,CAAC,YAAY,CAAC;wBACnD,IAAID,QAAQ,EAAE;0BACXA,QAAQ,CAAiB1P,KAAK,CAACM,OAAO,GAAG,OAAO;wBACnD;sBACF;oBACF;kBAAE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACA,IAAI,eACR7D,OAAA,CAACJ,IAAI;oBACHwR,IAAI,EAAE,EAAG;oBACTxO,SAAS,EAAC,WAAW;oBACrBD,KAAK,EAAE;sBACLM,OAAO,EAAEqE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyK,cAAc,GAAG,MAAM,GAAG;oBAClD;kBAAE;oBAAArO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF7D,OAAA;oBAAAsD,QAAA,EAAO,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0K,SAAS,MAAK5K,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;kBAAC;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF7D,OAAA,CAACL,WAAW;oBAACyR,IAAI,EAAE,EAAG;oBAACzO,KAAK,EAAE;sBAC5BmD,SAAS,EAAEoD,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;sBAC/DxD,UAAU,EAAE;oBACd;kBAAE;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGRqF,gBAAgB,iBACflJ,OAAA;kBAAK2C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,CAAC;oBACRjC,SAAS,EAAE,QAAQ;oBACnBgI,UAAU,EAAE,OAAO;oBACnBlI,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB2L,SAAS,EAAE,iCAAiC;oBAC5CC,QAAQ,EAAE,OAAO;oBACjBH,MAAM,EAAE,IAAI;oBACZ1L,QAAQ,EAAE;kBACZ,CAAE;kBAAA7B,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,cAAc;sBACvB6E,YAAY,EAAE,mBAAmB;sBACjC9E,UAAU,EAAE;oBACd,CAAE;oBAAA1I,QAAA,eACAtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GAECgE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyK,cAAc,gBAC1B/R,OAAA;wBACEiE,GAAG,EAAE5F,WAAW,CAACiJ,WAAW,CAACyK,cAAc,CAAC,IAAI,EAAG;wBACnDrP,GAAG,EAAE,GAAG4E,WAAW,CAAC0K,SAAS,IAAI1K,WAAW,CAAC2K,QAAQ,EAAG;wBACxDtP,KAAK,EAAE;0BACL2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdH,YAAY,EAAE,KAAK;0BACnBK,SAAS,EAAE,OAAO;0BAClB3B,MAAM,EAAE,mBAAmB;0BAC3ByO,UAAU,EAAE;wBACd,CAAE;wBACFL,OAAO,EAAGhO,CAAC,IAAK;0BACd,MAAMwH,MAAM,GAAGxH,CAAC,CAACwH,MAA0B;0BAC3CA,MAAM,CAAC/I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC7B,MAAMkP,MAAM,GAAGzG,MAAM,CAAC0G,aAAa;0BACnC,IAAID,MAAM,EAAE;4BAAA,IAAAK,qBAAA,EAAAC,qBAAA;4BACVN,MAAM,CAACO,SAAS,GAAG;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,CAAApL,WAAW,aAAXA,WAAW,wBAAAkL,qBAAA,GAAXlL,WAAW,CAAE0K,SAAS,cAAAQ,qBAAA,uBAAtBA,qBAAA,CAAwBG,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAArL,WAAW,aAAXA,WAAW,wBAAAmL,qBAAA,GAAXnL,WAAW,CAAE2K,QAAQ,cAAAQ,qBAAA,uBAArBA,qBAAA,CAAuBE,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;AACxH;AACA,mCAAmC;0BACH;wBACF;sBAAE;wBAAAjP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEF7D,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdH,YAAY,EAAE,KAAK;0BACnB4G,UAAU,EAAE,mDAAmD;0BAC/D/I,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBE,KAAK,EAAE,OAAO;0BACdU,UAAU,EAAE,KAAK;0BACjBN,QAAQ,EAAE,MAAM;0BAChB8O,UAAU,EAAE;wBACd,CAAE;wBAAAjP,QAAA,GACC,CAAAgE,WAAW,aAAXA,WAAW,wBAAAT,sBAAA,GAAXS,WAAW,CAAE0K,SAAS,cAAAnL,sBAAA,uBAAtBA,sBAAA,CAAwB8L,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAE,CAAArL,WAAW,aAAXA,WAAW,wBAAAR,sBAAA,GAAXQ,WAAW,CAAE2K,QAAQ,cAAAnL,sBAAA,uBAArBA,sBAAA,CAAuB6L,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;sBAAA;wBAAAjP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BAAEwO,IAAI,EAAE;wBAAE,CAAE;wBAAA7N,QAAA,gBACtBtD,OAAA;0BAAK2C,KAAK,EAAE;4BACVc,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,GACCgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0K,SAAS,EAAC,GAAC,EAAC1K,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2K,QAAQ;wBAAA;0BAAAvO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C,CAAC,eACN7D,OAAA;0BAAK2C,KAAK,EAAE;4BACVc,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,EACCgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsL;wBAAK;0BAAAlP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEsJ,OAAO,EAAE;oBAAW,CAAE;oBAAA3I,QAAA,GAEjC8D,WAAW,KAAK,OAAO;oBAAA;oBACtB;oBACApH,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM;wBACbc,QAAQ,CAAC,kBAAkB,CAAC;wBAC5BkC,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFxG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdsG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzBlI,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACFjJ,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAA1I,QAAA,gBAEFtD,OAAA,CAAChB,eAAe;wBAACoS,IAAI,EAAE;sBAAG;wBAAA1N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAE/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;oBAAA;oBAET;oBACA7D,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM;wBACbkD,sBAAsB,CAAC,IAAI,CAAC;wBAC5BF,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFxG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdsG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzBlI,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACFjJ,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAA1I,QAAA,gBAEFtD,OAAA,CAACF,QAAQ;wBAACsR,IAAI,EAAE;sBAAG;wBAAA1N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,oBAExB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,eAED7D,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM;wBACbmI,YAAY,CAAC,CAAC;wBACdnF,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFxG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdsG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzBlI,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACFjJ,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAA1I,QAAA,gBAEFtD,OAAA,CAACH,MAAM;wBAACuR,IAAI,EAAE;sBAAG;wBAAA1N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT7D,OAAA;QAAK2C,KAAK,EAAE;UACVsJ,OAAO,EAAE,MAAM;UACfhJ,OAAO,EAAE,MAAM;UACf0C,GAAG,EAAE,MAAM;UACXzC,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEAtD,OAAA;UAAK2C,KAAK,EAAE;YACV2C,KAAK,EAAE,OAAO;YACdiN,UAAU,EAAE;UACd,CAAE;UAAAjP,QAAA,eACAtD,OAAA;YAAK2C,KAAK,EAAE;cACVqJ,UAAU,EAAE,OAAO;cACnB5G,YAAY,EAAE,MAAM;cACpBtB,MAAM,EAAE,mBAAmB;cAC3BqB,QAAQ,EAAE,QAAQ;cAClBD,QAAQ,EAAE,QAAQ;cAClBa,GAAG,EAAE;YACP,CAAE;YAAAzC,QAAA,gBAEAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVsJ,OAAO,EAAE,oBAAoB;gBAC7B6E,YAAY,EAAE;cAChB,CAAE;cAAAxN,QAAA,gBACAtD,OAAA;gBAAK2C,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE,SAAS;kBACdnC,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,gBACAtD,OAAA,CAACrB,GAAG;kBAACyS,IAAI,EAAE,EAAG;kBAACzO,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C7D,OAAA;kBAAI2C,KAAK,EAAE;oBACTsO,MAAM,EAAE,CAAC;oBACTxN,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRsO,MAAM,EAAE,CAAC;kBACTxN,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN7D,OAAA;cAAK2C,KAAK,EAAE;gBAAEsJ,OAAO,EAAE;cAAO,CAAE;cAAA3I,QAAA,EAC7BsG,mBAAmB,CAACpF,MAAM,GAAG,CAAC,gBAC7BxE,OAAA,CAAAE,SAAA;gBAAAoD,QAAA,GACGsG,mBAAmB,CAAClF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAE2G,YAAY,IAAK;kBACrD;kBACA,MAAM6F,OAAO,GAAG7F,YAAY,CAAC8F,QAAQ;kBACrC,MAAMpG,YAAY,GAAG,CAACM,YAAY,CAAC+F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAGxG,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACE1M,OAAA;oBAEE2C,KAAK,EAAE;sBACLsJ,OAAO,EAAE,MAAM;sBACfD,UAAU,EAAE6G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAC3CzN,YAAY,EAAE,MAAM;sBACpBtB,MAAM,EAAE+O,OAAO,GAAG,mBAAmB,GAAG,mBAAmB;sBAC3DrP,YAAY,EAAE,MAAM;sBACpB6B,MAAM,EAAE,SAAS;sBACjBK,UAAU,EAAE;oBACd,CAAE;oBACF5C,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG6G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClE3O,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAGkB,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACF9P,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG6G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClE3O,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAGkB,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACF1M,OAAO,EAAEA,CAAA,KAAM8C,qBAAqB,CAAC+D,YAAY,CAAE;oBAAA1J,QAAA,eAEnDtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtD,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbyG,UAAU,EAAE6G,OAAO,GAAG,SAAS,GAAII,aAAa,CAACjH,UAAU,CAAC+C,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAU;0BACxG3J,YAAY,EAAE,KAAK;0BACnBpB,SAAS,EAAE,QAAQ;0BACnBuO,UAAU,EAAE;wBACd;sBAAE;wBAAA7O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL7D,OAAA;wBAAK2C,KAAK,EAAE;0BAAEwO,IAAI,EAAE;wBAAE,CAAE;wBAAA7N,QAAA,gBACtBtD,OAAA;0BAAI2C,KAAK,EAAE;4BACTsO,MAAM,EAAE,cAAc;4BACtBxN,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE,SAAS;4BAChB6N,UAAU,EAAE;0BACd,CAAE;0BAAA5N,QAAA,EACC0J,YAAY,CAAC6B;wBAAK;0BAAAnL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL7D,OAAA;0BAAG2C,KAAK,EAAE;4BACRsO,MAAM,EAAE,cAAc;4BACtBxN,QAAQ,EAAE,QAAQ;4BAClBJ,KAAK,EAAE,SAAS;4BAChB6N,UAAU,EAAE;0BACd,CAAE;0BAAA5N,QAAA,EACC0J,YAAY,CAACgC,OAAO,CAACxK,MAAM,GAAG,EAAE,GAC7B,GAAGwI,YAAY,CAACgC,OAAO,CAACkE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7ClG,YAAY,CAACgC;wBAAO;0BAAAtL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ7D,OAAA;0BAAK2C,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE,QAAQ;4BACblC,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,gBACAtD,OAAA,CAACpB,QAAQ;4BAACwS,IAAI,EAAE;0BAAG;4BAAA1N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB7D,OAAA;4BAAAsD,QAAA,EAAO,IAAImM,IAAI,CAACzC,YAAY,CAACmG,UAAU,CAAC,CAACC,kBAAkB,CAAC;0BAAC;4BAAA1P,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhEDmJ,YAAY,CAACC,eAAe;oBAAAvJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAED+F,mBAAmB,CAACpF,MAAM,GAAG,CAAC,iBAC7BxE,OAAA;kBAAQ2C,KAAK,EAAE;oBACb2C,KAAK,EAAE,MAAM;oBACb2G,OAAO,EAAE,SAAS;oBAClBnI,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,KAAK;oBACnB4G,UAAU,EAAE,OAAO;oBACnB3I,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE;kBACd,CAAE;kBACF5C,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,SAAS;oBAC5C9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACF5O,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,OAAO;oBAC1C9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgP,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAArO,QAAA,GAAC,WACQ,EAACsG,mBAAmB,CAACpF,MAAM,EAAC,eACvC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH7D,OAAA;gBAAK2C,KAAK,EAAE;kBACVsJ,OAAO,EAAE,WAAW;kBACpB1I,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,gBACAtD,OAAA,CAACrB,GAAG;kBAACyS,IAAI,EAAE,EAAG;kBAACzO,KAAK,EAAE;oBAAEa,YAAY,EAAE,QAAQ;oBAAE6P,OAAO,EAAE;kBAAI;gBAAE;kBAAA3P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE7D,OAAA;kBAAG2C,KAAK,EAAE;oBAAEsO,MAAM,EAAE,CAAC;oBAAExN,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,EAAC;gBAE/C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YAAEwO,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAA1N,QAAA,GAEpC,CAAC7C,OAAO,IAAIuJ,eAAe,kBAC1BhK,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpB6I,SAAS,EAAE;YACb,CAAE;YAAAzI,QAAA,eACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBlD,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA;gBAAK2C,KAAK,EAAE;kBACV2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,MAAM,EAAE,kCAAkC;kBAC1CwP,SAAS,EAAE,mBAAmB;kBAC9BlO,YAAY,EAAE,KAAK;kBACnBmO,SAAS,EAAE;gBACb;cAAE;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,MAAM;kBAChBM,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAClD,KAAK,IAAIuJ,aAAa,KAAK,CAACzJ,OAAO,IAAI,CAACuJ,eAAe,iBACvDhK,OAAA;YAAK2C,KAAK,EAAE;cACVsJ,OAAO,EAAE,MAAM;cACfD,UAAU,EAAE,wBAAwB;cACpClI,MAAM,EAAE,kCAAkC;cAC1CsB,YAAY,EAAE,MAAM;cACpB7B,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdyG,UAAU,EAAE,wBAAwB;gBACpC5G,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8N,MAAM,EAAE;cACV,CAAE;cAAA3N,QAAA,eACAtD,OAAA,CAACnB,aAAa;gBAACuS,IAAI,EAAE,EAAG;gBAAC/N,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN7D,OAAA;cAAI2C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB4N,MAAM,EAAE,cAAc;gBACtBxN,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7D,OAAA;cAAG2C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB4N,MAAM,EAAE,cAAc;gBACtBxN,QAAQ,EAAE;cACZ,CAAE;cAAAH,QAAA,EACC3C,KAAK,IAAIuJ;YAAa;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ7D,OAAA;cACEmG,OAAO,EAAEA,CAAA,KAAM;gBACb4B,oBAAoB,CAAC,CAAC;gBACtBqC,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFzH,KAAK,EAAE;gBACLqJ,UAAU,EAAE,mDAAmD;gBAC/D3I,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB6G,OAAO,EAAE,gBAAgB;gBACzBxI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhO,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzN,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAACpD,OAAO,IAAI,CAACuJ,eAAe,IAAI,CAACrJ,KAAK,IAAI,CAACuJ,aAAa,IACxDuG,oBAAoB,CAACjM,MAAM,KAAK,CAAC,IAAIkM,aAAa,CAAClM,MAAM,KAAK,CAAC,iBAC9DxE,OAAA;YAAK2C,KAAK,EAAE;cACVsJ,OAAO,EAAE,WAAW;cACpB1I,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdyG,UAAU,EAAE,mDAAmD;gBAC/D5G,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8N,MAAM,EAAE;cACV,CAAE;cAAA3N,QAAA,eACAtD,OAAA,CAACvB,SAAS;gBAAC2S,IAAI,EAAE,EAAG;gBAAC/N,KAAK,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN7D,OAAA;cAAI2C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB4N,MAAM,EAAE,YAAY;gBACpBxN,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7D,OAAA;cAAG2C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB4N,MAAM,EAAE,YAAY;gBACpBxN,QAAQ,EAAE,MAAM;gBAChByN,UAAU,EAAE,KAAK;gBACjBhF,QAAQ,EAAE,OAAO;gBACjBsH,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAnQ,QAAA,EACCoF,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAAC6E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDxI,OAAA;cACEmG,OAAO,EAAEA,CAAA,KAAM;gBACbwC,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACF9F,KAAK,EAAE;gBACLqJ,UAAU,EAAE,mDAAmD;gBAC/D3I,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB6G,OAAO,EAAE,gBAAgB;gBACzBxI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhO,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzN,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAMA,CAACpD,OAAO,IAAI,CAACuJ,eAAe,KAAKyG,oBAAoB,CAACjM,MAAM,GAAG,CAAC,IAAIkM,aAAa,CAAClM,MAAM,GAAG,CAAC,CAAC,iBAC5FxE,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAECoN,aAAa,CAAClM,MAAM,GAAG,CAAC,iBACvBxE,OAAA,CAAAE,SAAA;cAAAoD,QAAA,EACGoN,aAAa,CAACrK,GAAG,CAACyE,KAAK,iBACtB9K,OAAA;gBAEE2C,KAAK,EAAE;kBACLqJ,UAAU,EAAE,2BAA2B;kBACvC5G,YAAY,EAAE,MAAM;kBACpB6G,OAAO,EAAE,QAAQ;kBACjBnI,MAAM,EAAE,8BAA8B;kBACtC4P,cAAc,EAAE,YAAY;kBAC5B3C,SAAS,EAAE,gCAAgC;kBAC3CrL,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACFhO,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAAzN,QAAA,gBAGFtD,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACV2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdyG,UAAU,EAAE,mDAAmD;sBAC/D5G,YAAY,EAAE,MAAM;sBACpBnC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBoP,UAAU,EAAE;oBACd,CAAE;oBAAAjP,QAAA,eACAtD,OAAA,CAACpB,QAAQ;sBAACwS,IAAI,EAAE,EAAG;sBAAC/N,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEwO,IAAI,EAAE;oBAAE,CAAE;oBAAA7N,QAAA,gBACtBtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMwJ,eAAe,GAAGhC,KAAK,CAACiI,aAAa,IAAI,cAAc;wBAC7D,MAAMY,YAAY,GAAG9G,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAM8G,aAAa,GAAGD,YAAY,CAAC/G,IAAI;wBAEvC,oBACE5M,OAAA;0BAAM2C,KAAK,EAAE;4BACXqJ,UAAU,EAAE2H,YAAY,CAAC3H,UAAU;4BACnC3I,KAAK,EAAE,OAAO;4BACdI,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjBkI,OAAO,EAAE,iBAAiB;4BAC1B7G,YAAY,EAAE,MAAM;4BACpByO,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtB7Q,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACAtD,OAAA,CAAC4T,aAAa;4BAACxC,IAAI,EAAE,EAAG;4BAAC/N,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxCiJ,eAAe;wBAAA;0BAAApJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,EAGHiH,KAAK,CAACiJ,eAAe,iBACpB/T,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,QAAQ;0BACbqG,UAAU,EAAE,yBAAyB;0BACrCC,OAAO,EAAE,iBAAiB;0BAC1B7G,YAAY,EAAE,MAAM;0BACpB3B,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACCwH,KAAK,CAACkJ,kBAAkB,gBACvBhU,OAAA;0BACEiE,GAAG,EAAE5F,WAAW,CAACyM,KAAK,CAACkJ,kBAAkB,CAAC,IAAI,EAAG;0BACjDtR,GAAG,EAAEoI,KAAK,CAACiJ,eAAgB;0BAC3BpR,KAAK,EAAE;4BACL2C,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdH,YAAY,EAAE,KAAK;4BACnBK,SAAS,EAAE;0BACb,CAAE;0BACFyM,OAAO,EAAGhO,CAAC,IAAK;4BACd,MAAMwH,MAAM,GAAGxH,CAAC,CAACwH,MAA0B;4BAC3CA,MAAM,CAAC/I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC/B;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7D,OAAA,CAACJ,IAAI;0BAACwR,IAAI,EAAE;wBAAG;0BAAA1N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAClB,eACD7D,OAAA;0BAAM2C,KAAK,EAAE;4BAAEoB,UAAU,EAAE;0BAAM,CAAE;0BAAAT,QAAA,EAChCwH,KAAK,CAACiJ;wBAAe;0BAAArQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,SAAS;0BACdtC,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,gBACAtD,OAAA,CAACpB,QAAQ;0BAACwS,IAAI,EAAE;wBAAG;0BAAA1N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAI4L,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACmD,kBAAkB,CAAC,OAAO,EAAE;0BACtDa,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAA1Q,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN7D,OAAA;sBAAI2C,KAAK,EAAE;wBACTsO,MAAM,EAAE,cAAc;wBACtBxN,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB6N,UAAU,EAAE;sBACd,CAAE;sBAAA5N,QAAA,EACCwH,KAAK,CAAC+D;oBAAK;sBAAAnL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAAC,MAAM;kBACN;kBACA,MAAMwQ,cAAwB,GAAG,EAAE;kBAEnC,IAAKvJ,KAAK,CAASzG,MAAM,IAAKyG,KAAK,CAASzG,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DsG,KAAK,CAASzG,MAAM,CAACiQ,OAAO,CAAEhI,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAAC1G,SAAS,EAAE;wBACjB;wBACA,MAAMrF,QAAQ,GAAGlC,WAAW,CAACiO,GAAG,CAAC1G,SAAS,CAAC;wBAC3C,IAAIrF,QAAQ,EAAE;0BACZ8T,cAAc,CAACE,IAAI,CAAChU,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAO8T,cAAc,CAAC7P,MAAM,GAAG,CAAC,gBAC9BxE,OAAA;oBAAK2C,KAAK,EAAE;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAAF,QAAA,eACnCtD,OAAA,CAAC7B,oBAAoB;sBACnBkG,MAAM,EAAEgQ,cAAc,CAAC/I,MAAM,CAACiB,OAAO,CAAc;sBACnDjI,SAAS,EAAEwG,KAAK,CAAC+D,KAAM;sBACvB2F,UAAU,EAAE,CAAE;sBACdjQ,YAAY,EAAGQ,KAAK,IAAK;wBACvB,MAAM0P,cAAc,GAAGJ,cAAc,CAAC/I,MAAM,CAACiB,OAAO,CAAa;wBACjEC,oBAAoB,CAACiI,cAAc,EAAE1P,KAAK,CAAC;sBAC7C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,EAGHiH,KAAK,CAACyE,WAAW,iBAChBvP,OAAA;kBAAK2C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnByN,UAAU,EAAE,KAAK;oBACjB1N,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCwH,KAAK,CAACyE;gBAAW;kBAAA7L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbsG,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,0BAA0B;oBACtC5G,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE;kBACZ,CAAE;kBAAAH,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAtD,OAAA,CAACpB,QAAQ;sBAACwS,IAAI,EAAE;oBAAG;sBAAA1N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB7D,OAAA;sBAAAsD,QAAA,EACGwH,KAAK,CAACsF,QAAQ,IAAItF,KAAK,CAACsF,QAAQ,KAAKtF,KAAK,CAACmF,UAAU,GAClD,GAAG,IAAIR,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACmD,kBAAkB,CAAC,CAAC,MAAM,IAAI3D,IAAI,CAAC3E,KAAK,CAACsF,QAAQ,CAAC,CAACgD,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAI3D,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACmD,kBAAkB,CAAC;oBAAC;sBAAA1P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAELiH,KAAK,CAACiI,aAAa,iBAClB/S,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,eACAtD,OAAA;sBAAM2C,KAAK,EAAE;wBACXsJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAE,yBAAyB;wBACrC5G,YAAY,EAAE,KAAK;wBACnB3B,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCwH,KAAK,CAACiI;oBAAa;sBAAArP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGN7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB0Q,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE,8BAA8B;oBACzCrQ,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBAEAtD,OAAA;oBACEmG,OAAO,EAAEA,CAAA,KAAMyH,wBAAwB,CAAC9C,KAAK,CAAE;oBAC/CnI,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbqG,UAAU,EAAE,MAAM;sBAClBlI,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAGyH,KAAK,CAAS+C,gBAAgB,GAAG,SAAS,GAAG,SAAS;sBAC9DxI,MAAM,EAAE,SAAS;sBACjB4G,OAAO,EAAE,QAAQ;sBACjB7G,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,qBAAqB;oBAC1D,CAAE;oBACFjJ,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAA1I,QAAA,gBAEFtD,OAAA,CAAClB,KAAK;sBACJsS,IAAI,EAAE,EAAG;sBACTuD,IAAI,EAAG7J,KAAK,CAAS+C,gBAAgB,GAAG,SAAS,GAAG;oBAAO;sBAAAnK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACF7D,OAAA;sBAAAsD,QAAA,EAAQwH,KAAK,CAASkD,cAAc,IAAI;oBAAC;sBAAAtK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,EAGPiH,KAAK,CAAS8J,cAAc,iBAC5B5U,OAAA;oBACEmG,OAAO,EAAEA,CAAA,KAAM4C,uBAAuB,CACpCD,oBAAoB,KAAKgC,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;oBACFrI,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbqG,UAAU,EAAE,MAAM;sBAClBlI,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAEyF,oBAAoB,KAAKgC,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;sBACzE3F,MAAM,EAAE,SAAS;sBACjB4G,OAAO,EAAE,QAAQ;sBACjB7G,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,qBAAqB;sBACxD9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGyF,oBAAoB,KAAKgC,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBACFjI,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,MAAM;sBACzC9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGyF,oBAAoB,KAAKgC,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBAAA1H,QAAA,gBAEFtD,OAAA,CAACnB,aAAa;sBAACuS,IAAI,EAAE;oBAAG;sBAAA1N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B7D,OAAA;sBAAAsD,QAAA,EAAQwH,KAAK,CAAS+J,aAAa,IAAI;oBAAC;sBAAAnR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGLiF,oBAAoB,KAAKgC,KAAK,CAACE,WAAW,IAAKF,KAAK,CAAS8J,cAAc,iBAC1E5U,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB0Q,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE;kBACb,CAAE;kBAAAhQ,QAAA,EACC8D,WAAW,KAAK,OAAO,gBACtBpH,OAAA,CAACjC,mBAAmB;oBAClB+W,UAAU,EAAEhK,KAAK,CAACE,WAAY;oBAC9B+J,aAAa,EAAGjK,KAAK,CAAS8J,cAAe;oBAC7CI,aAAa,EAAE1N,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAG;oBAC/B8H,eAAe,EAAC;kBAAO;oBAAAvR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF7D,OAAA,CAAChC,cAAc;oBACb8W,UAAU,EAAEhK,KAAK,CAACE,WAAY;oBAC9B+J,aAAa,EAAGjK,KAAK,CAAS8J,cAAe;oBAC7CI,aAAa,EAAE1N,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAG;oBAC/B8H,eAAe,EAAC;kBAAS;oBAAAvR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GAnUI,SAASiH,KAAK,CAACE,WAAW,EAAE;gBAAAtH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoU9B,CACN;YAAC,gBACF,CACH,EAGA4M,oBAAoB,CAACjM,MAAM,GAAG,CAAC,iBAC9BxE,OAAA,CAAAE,SAAA;cAAAoD,QAAA,EACGmN,oBAAoB,CAACpK,GAAG,CAAC2G,YAAY,iBACpChN,OAAA;gBAEEmN,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;gBACnDrK,SAAS,EAAEwF,kBAAkB,IAAIC,YAAY,KAAK,gBAAgB2E,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;gBAC9ItK,KAAK,EAAE;kBACLqJ,UAAU,EAAE,2BAA2B;kBACvC5G,YAAY,EAAE,MAAM;kBACpB6G,OAAO,EAAE,QAAQ;kBACjBnI,MAAM,EAAEkJ,YAAY,CAACxB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClCkI,cAAc,EAAE,YAAY;kBAC5B3C,SAAS,EAAE/D,YAAY,CAACxB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpC9F,UAAU,EAAE,2CAA2C;kBACvDR,QAAQ,EAAE;gBACZ,CAAE;gBACFpC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG/D,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFzI,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoO,SAAS,GAAG/D,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAAlI,QAAA,GAGD0J,YAAY,CAACxB,SAAS,iBACrBxL,OAAA;kBAAK2C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACb+F,UAAU,EAAE,mDAAmD;oBAC/D3I,KAAK,EAAE,OAAO;oBACd4I,OAAO,EAAE,iBAAiB;oBAC1B7G,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBd,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,SAAS;oBACdoL,SAAS,EAAE;kBACb,CAAE;kBAAAzN,QAAA,gBACAtD,OAAA,CAACrB,GAAG;oBAACyS,IAAI,EAAE;kBAAG;oBAAA1N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,GACC,CAAC,MAAM;oBACN,IAAI0J,YAAY,CAAC8F,QAAQ,EAAE;sBACzB,oBACE9S,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdyG,UAAU,EAAE,mDAAmD;0BAC/D5G,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBoP,UAAU,EAAE;wBACd,CAAE;wBAAAjP,QAAA,eACAtD,OAAA,CAACb,aAAa;0BAACiS,IAAI,EAAE,EAAG;0BAAC/N,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAM6I,YAAY,GAAG,CAACM,YAAY,CAAC+F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAGxG,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAMkH,aAAa,GAAGX,aAAa,CAACrG,IAAI;sBAExC,oBACE5M,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdyG,UAAU,EAAEiH,aAAa,CAACjH,UAAU;0BACpC5G,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBoP,UAAU,EAAE;wBACd,CAAE;wBAAAjP,QAAA,eACAtD,OAAA,CAAC4T,aAAa;0BAACxC,IAAI,EAAE,EAAG;0BAAC/N,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEwO,IAAI,EAAE;oBAAE,CAAE;oBAAA7N,QAAA,gBACtBtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE,QAAQ;wBACtB0R,QAAQ,EAAE;sBACZ,CAAE;sBAAA5R,QAAA,GACC,CAAC,MAAM;wBACN,IAAI0J,YAAY,CAAC8F,QAAQ,EAAE;0BACzB,oBACE9S,OAAA;4BAAM2C,KAAK,EAAE;8BACXqJ,UAAU,EAAE,mDAAmD;8BAC/D3I,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBkI,OAAO,EAAE,iBAAiB;8BAC1B7G,YAAY,EAAE,MAAM;8BACpByO,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB7Q,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtD,OAAA,CAACb,aAAa;8BAACiS,IAAI,EAAE,EAAG;8BAAC/N,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAM6I,YAAY,GAAG,CAACM,YAAY,CAAC+F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAGxG,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMkH,aAAa,GAAGX,aAAa,CAACrG,IAAI;0BAExC,oBACE5M,OAAA;4BAAM2C,KAAK,EAAE;8BACXqJ,UAAU,EAAEiH,aAAa,CAACjH,UAAU;8BACpC3I,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBkI,OAAO,EAAE,iBAAiB;8BAC1B7G,YAAY,EAAE,MAAM;8BACpByO,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB7Q,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtD,OAAA,CAAC4T,aAAa;8BAACxC,IAAI,EAAE,EAAG;8BAAC/N,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxC6I,YAAY;0BAAA;4BAAAhJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEHmJ,YAAY,CAACqC,WAAW,iBACvBrP,OAAA;wBAAM2C,KAAK,EAAE;0BACXqJ,UAAU,EAAE,yBAAyB;0BACrC3I,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,SAAS;0BACnBM,UAAU,EAAE,KAAK;0BACjBkI,OAAO,EAAE,iBAAiB;0BAC1B7G,YAAY,EAAE;wBAChB,CAAE;wBAAA9B,QAAA,GAAC,QACK,EAAC0J,YAAY,CAACqC,WAAW;sBAAA;wBAAA3L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,EAGAmJ,YAAY,CAACmI,WAAW,iBACvBnV,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,QAAQ;0BACbqG,UAAU,EAAE,wBAAwB;0BACpCC,OAAO,EAAE,iBAAiB;0BAC1B7G,YAAY,EAAE,MAAM;0BACpB3B,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACC0J,YAAY,CAACoI,cAAc,gBAC1BpV,OAAA;0BACEiE,GAAG,EAAE5F,WAAW,CAAC2O,YAAY,CAACoI,cAAc,CAAC,IAAI,EAAG;0BACpD1S,GAAG,EAAEsK,YAAY,CAACmI,WAAY;0BAC9BxS,KAAK,EAAE;4BACL2C,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdH,YAAY,EAAE,KAAK;4BACnBK,SAAS,EAAE;0BACb,CAAE;0BACFyM,OAAO,EAAGhO,CAAC,IAAK;4BACd,MAAMwH,MAAM,GAAGxH,CAAC,CAACwH,MAA0B;4BAC3CA,MAAM,CAAC/I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC/B;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7D,OAAA,CAACJ,IAAI;0BAACwR,IAAI,EAAE;wBAAG;0BAAA1N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAClB,eACD7D,OAAA;0BAAM2C,KAAK,EAAE;4BAAEoB,UAAU,EAAE;0BAAM,CAAE;0BAAAT,QAAA,EAChC0J,YAAY,CAACmI;wBAAW;0BAAAzR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAImM,IAAI,CAACzC,YAAY,CAACmG,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BAC7Da,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA1Q,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN7D,OAAA;sBAAI2C,KAAK,EAAE;wBACTsO,MAAM,EAAE,cAAc;wBACtBxN,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB6N,UAAU,EAAE;sBACd,CAAE;sBAAA5N,QAAA,EACC0J,YAAY,CAAC6B;oBAAK;sBAAAnL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLmJ,YAAY,CAAC9B,WAAW,IAAI8B,YAAY,CAAC9B,WAAW,CAAC1G,MAAM,GAAG,CAAC,iBAC9DxE,OAAA,CAACoE,YAAY;kBACXC,MAAM,EAAE2I,YAAY,CAAC9B,WAAY;kBACjC5G,SAAS,EAAE0I,YAAY,CAAC6B,KAAM;kBAC9BxO,QAAQ,EAAE+G,WAAY;kBACtB7C,YAAY,EAAGQ,KAAK,IAAK;oBACvBoH,YAAY,CAACa,YAAY,CAAC9B,WAAW,IAAI,EAAE,EAAEnG,KAAK,CAAC;kBACrD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnByN,UAAU,EAAE,KAAK;oBACjB1N,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACC0J,YAAY,CAACgC;gBAAO;kBAAAtL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGN7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B8I,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,qBAAqB;oBACjC5G,YAAY,EAAE,MAAM;oBACpB5B,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEAtD,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM4G,gBAAgB,CAACC,YAAY,CAAE;sBAC9CrK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbqG,UAAU,EAAE,MAAM;wBAClBlI,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE2J,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzD7H,MAAM,EAAE,SAAS;wBACjB4G,OAAO,EAAE,QAAQ;wBACjB7G,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACFjJ,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAA1I,QAAA,gBAEFtD,OAAA,CAAClB,KAAK;wBACJsS,IAAI,EAAE,EAAG;wBACTuD,IAAI,EAAE3H,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAAxJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF7D,OAAA;wBAAAsD,QAAA,EAAO0J,YAAY,CAACgB,cAAc,IAAI;sBAAC;wBAAAtK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGRmJ,YAAY,CAAC4H,cAAc,iBAC1B5U,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM0C,eAAe,CAC5BD,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;sBACFtK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbqG,UAAU,EAAE,MAAM;wBAClBlI,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEuF,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5E5H,MAAM,EAAE,SAAS;wBACjB4G,OAAO,EAAE,QAAQ;wBACjB7G,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,qBAAqB;wBACxD9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFN,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACqJ,UAAU,GAAG,MAAM;wBACzC9H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGuF,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAA3J,QAAA,gBAEFtD,OAAA,CAACnB,aAAa;wBAACuS,IAAI,EAAE;sBAAG;wBAAA1N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B7D,OAAA;wBAAAsD,QAAA,EAAO0J,YAAY,CAAC6H,aAAa,IAAI;sBAAC;wBAAAnR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGE,CAAC,eAGN7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,MAAM;sBACXlC,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtD,OAAA,CAACjB,KAAK;wBAACqS,IAAI,EAAE;sBAAG;wBAAA1N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB7D,OAAA;wBAAAsD,QAAA,GAAM,YAAU,EAAE0J,YAAY,CAASqI,cAAc,IAAIrI,YAAY,CAACmI,WAAW,IAAI,OAAO;sBAAA;wBAAAzR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjG,CAAC,eAEN7D,OAAA;sBAAK2C,KAAK,EAAE;wBACVsJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAEgB,YAAY,CAAC9K,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BmB,KAAK,EAAE2J,YAAY,CAAC9K,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClEkD,YAAY,EAAE,KAAK;wBACnBrB,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACC0J,YAAY,CAAC9K;oBAAM;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL+E,YAAY,KAAKoE,YAAY,CAACC,eAAe,IAAID,YAAY,CAAC4H,cAAc,iBAC3E5U,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB0Q,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE;kBACb,CAAE;kBAAAhQ,QAAA,EACC8D,WAAW,KAAK,OAAO,gBACtBpH,OAAA,CAACjC,mBAAmB;oBAClBuX,cAAc,EAAEtI,YAAY,CAACC,eAAgB;oBAC7C8H,aAAa,EAAE/H,YAAY,CAAC4H,cAAe;oBAC3CI,aAAa,EAAE1N,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAG;oBAC/B8H,eAAe,EAAC;kBAAO;oBAAAvR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF7D,OAAA,CAAChC,cAAc;oBACbsX,cAAc,EAAEtI,YAAY,CAACC,eAAgB;oBAC7C8H,aAAa,EAAE/H,YAAY,CAAC4H,cAAe;oBAC3CI,aAAa,EAAE1N,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6F,EAAG;oBAC/B8H,eAAe,EAAC;kBAAS;oBAAAvR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GAhYI,gBAAgBmJ,YAAY,CAACC,eAAe,EAAE;gBAAAvJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiYhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLmF,kBAAkB,iBACjBhJ,OAAA;MAAK2C,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxB0N,MAAM,EAAE,IAAI;QACZ5E,OAAO,EAAE;MACX,CAAE;MACF9F,OAAO,EAAEA,CAAA,KAAM8C,qBAAqB,CAAC,IAAI,CAAE;MAAA3F,QAAA,eAEzCtD,OAAA;QAAK2C,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpB8G,QAAQ,EAAE,OAAO;UACjB5G,KAAK,EAAE,MAAM;UACbiQ,SAAS,EAAE,MAAM;UACjBpQ,QAAQ,EAAE,MAAM;UAChB4L,SAAS,EAAE;QACb,CAAE;QACF5K,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACsR,eAAe,CAAC,CAAE;QAAAlS,QAAA,gBAGlCtD,OAAA;UAAK2C,KAAK,EAAE;YACVsJ,OAAO,EAAE,QAAQ;YACjB6E,YAAY,EAAE,mBAAmB;YACjC7N,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACAtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACAtD,OAAA,CAACrB,GAAG;cAACyS,IAAI,EAAE,EAAG;cAACzO,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C7D,OAAA;cAAI2C,KAAK,EAAE;gBACTsO,MAAM,EAAE,CAAC;gBACTxN,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN7D,OAAA;YACEmG,OAAO,EAAEA,CAAA,KAAM8C,qBAAqB,CAAC,IAAI,CAAE;YAC3CtG,KAAK,EAAE;cACLqJ,UAAU,EAAE,MAAM;cAClBlI,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClBJ,KAAK,EAAE,SAAS;cAChBgC,MAAM,EAAE,SAAS;cACjB4G,OAAO,EAAE,SAAS;cAClB7G,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YAAEsJ,OAAO,EAAE;UAAS,CAAE;UAAA3I,QAAA,gBAChCtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,SAAS;cACdnC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,GACC,CAAC,MAAM;cACN,IAAI0F,kBAAkB,CAAC8J,QAAQ,EAAE;gBAC/B,oBACE9S,OAAA;kBAAM2C,KAAK,EAAE;oBACXqJ,UAAU,EAAE,mDAAmD;oBAC/D3I,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBkI,OAAO,EAAE,iBAAiB;oBAC1B7G,YAAY,EAAE,MAAM;oBACpByO,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtB7Q,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtD,OAAA,CAACb,aAAa;oBAACiS,IAAI,EAAE,EAAG;oBAAC/N,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAEX,CAAC,MAAM;gBACL,MAAM6I,YAAY,GAAG,CAAC1D,kBAAkB,CAAC+J,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;gBAClF,MAAMC,aAAa,GAAGxG,gBAAgB,CAACC,YAAY,CAAC;gBACpD,MAAMkH,aAAa,GAAGX,aAAa,CAACrG,IAAI;gBAExC,oBACE5M,OAAA;kBAAM2C,KAAK,EAAE;oBACXqJ,UAAU,EAAEiH,aAAa,CAACjH,UAAU;oBACpC3I,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBkI,OAAO,EAAE,iBAAiB;oBAC1B7G,YAAY,EAAE,MAAM;oBACpByO,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtB7Q,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtD,OAAA,CAAC4T,aAAa;oBAACxC,IAAI,EAAE,EAAG;oBAAC/N,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxC6I,YAAY;gBAAA;kBAAAhJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAEX;YACF,CAAC,EAAE,CAAC,eAEJ7D,OAAA;cAAM2C,KAAK,EAAE;gBACXqJ,UAAU,EAAE,mDAAmD;gBAC/D3I,KAAK,EAAE,OAAO;gBACd4I,OAAO,EAAE,iBAAiB;gBAC1B7G,YAAY,EAAE,MAAM;gBACpB3B,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBd,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA,CAACrB,GAAG;gBAACyS,IAAI,EAAE;cAAG;gBAAA1N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN7D,OAAA;YAAI2C,KAAK,EAAE;cACTsO,MAAM,EAAE,YAAY;cACpBxN,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE,SAAS;cAChB6N,UAAU,EAAE;YACd,CAAE;YAAA5N,QAAA,EACC0F,kBAAkB,CAAC6F;UAAK;YAAAnL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EAGJmF,kBAAkB,CAACkC,WAAW,IAAIlC,kBAAkB,CAACkC,WAAW,CAAC1G,MAAM,GAAG,CAAC,iBAC1ExE,OAAA;YAAK2C,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,eACrCtD,OAAA,CAACoE,YAAY;cACXC,MAAM,EAAE2E,kBAAkB,CAACkC,WAAY;cACvC5G,SAAS,EAAE0E,kBAAkB,CAAC6F,KAAM;cACpCxO,QAAQ,EAAE+G,WAAY;cACtB7C,YAAY,EAAGQ,KAAK,IAAK;gBACvBoH,YAAY,CAACnD,kBAAkB,CAACkC,WAAW,EAAEnG,KAAK,CAAC;cACrD;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED7D,OAAA;YAAK2C,KAAK,EAAE;cACVU,KAAK,EAAE,SAAS;cAChBI,QAAQ,EAAE,MAAM;cAChByN,UAAU,EAAE,KAAK;cACjB1N,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACC0F,kBAAkB,CAACgG;UAAO;YAAAtL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEN7D,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXlC,QAAQ,EAAE,UAAU;cACpBJ,KAAK,EAAE,SAAS;cAChBqR,UAAU,EAAE,MAAM;cAClBpB,SAAS,EAAE;YACb,CAAE;YAAAhQ,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA,CAACpB,QAAQ;gBAACwS,IAAI,EAAE;cAAG;gBAAA1N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB7D,OAAA;gBAAAsD,QAAA,GAAM,aAAW,EAAC,IAAImM,IAAI,CAACzG,kBAAkB,CAACmK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA1P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACLmF,kBAAkB,CAACmM,WAAW,iBAC7BnV,OAAA;cAAAsD,QAAA,GAAK,MACC,EAAC0F,kBAAkB,CAACmM,WAAW;YAAA;cAAAzR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAuF,mBAAmB,IAAIhC,WAAW,KAAK,SAAS,iBAC/CpH,OAAA;MAAK2C,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxB0N,MAAM,EAAE,IAAI;QACZ5E,OAAO,EAAE;MACX,CAAE;MAAA3I,QAAA,eACAtD,OAAA;QAAK2C,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpB2L,SAAS,EAAE,2EAA2E;UACtF7E,QAAQ,EAAE,OAAO;UACjB5G,KAAK,EAAE,MAAM;UACbiQ,SAAS,EAAE,MAAM;UACjBpQ,QAAQ,EAAE;QACZ,CAAE;QAAA7B,QAAA,gBAEAtD,OAAA;UAAK2C,KAAK,EAAE;YACVsJ,OAAO,EAAE,QAAQ;YACjB6E,YAAY,EAAE,mBAAmB;YACjC7N,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACAtD,OAAA;YAAI2C,KAAK,EAAE;cACTsO,MAAM,EAAE,CAAC;cACTxN,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7D,OAAA;YACEmG,OAAO,EAAEA,CAAA,KAAMkD,sBAAsB,CAAC,KAAK,CAAE;YAC7C1G,KAAK,EAAE;cACLqJ,UAAU,EAAE,MAAM;cAClBlI,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClB4B,MAAM,EAAE,SAAS;cACjBhC,KAAK,EAAE,SAAS;cAChB4I,OAAO,EAAE,QAAQ;cACjB7G,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACS,eAAe,GAAG,SAAS;cACjDc,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACS,eAAe,GAAG,aAAa;cACrDc,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YACVsJ,OAAO,EAAE,QAAQ;YACjBhJ,OAAO,EAAE,MAAM;YACf0C,GAAG,EAAE,MAAM;YACXzC,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEAtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBlD,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXqL,QAAQ,EAAE;YACZ,CAAE;YAAA1N,QAAA,GAECgE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyK,cAAc,gBAC1B/R,OAAA;cACEiE,GAAG,EAAE5F,WAAW,CAACiJ,WAAW,CAACyK,cAAc,CAAC,IAAI,EAAG;cACnDrP,GAAG,EAAE,GAAG4E,WAAW,CAAC0K,SAAS,IAAI1K,WAAW,CAAC2K,QAAQ,EAAG;cACxDtP,KAAK,EAAE;gBACL2C,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfH,YAAY,EAAE,KAAK;gBACnBK,SAAS,EAAE,OAAO;gBAClB3B,MAAM,EAAE;cACV,CAAE;cACFoO,OAAO,EAAGhO,CAAC,IAAK;gBACd,MAAMwH,MAAM,GAAGxH,CAAC,CAACwH,MAA0B;gBAC3CA,MAAM,CAAC/I,KAAK,CAACM,OAAO,GAAG,MAAM;gBAC7B,MAAMkP,MAAM,GAAGzG,MAAM,CAAC0G,aAAa;gBACnC,IAAID,MAAM,EAAE;kBAAA,IAAAsD,sBAAA,EAAAC,sBAAA;kBACVvD,MAAM,CAACO,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,CAAApL,WAAW,aAAXA,WAAW,wBAAAmO,sBAAA,GAAXnO,WAAW,CAAE0K,SAAS,cAAAyD,sBAAA,uBAAtBA,sBAAA,CAAwB9C,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAArL,WAAW,aAAXA,WAAW,wBAAAoO,sBAAA,GAAXpO,WAAW,CAAE2K,QAAQ,cAAAyD,sBAAA,uBAArBA,sBAAA,CAAuB/C,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;AAC9G;AACA,yBAAyB;gBACH;cACF;YAAE;cAAAjP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEF7D,OAAA;cAAK2C,KAAK,EAAE;gBACV2C,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfH,YAAY,EAAE,KAAK;gBACnB4G,UAAU,EAAE,mDAAmD;gBAC/D/I,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBE,KAAK,EAAE,OAAO;gBACdU,UAAU,EAAE,KAAK;gBACjBN,QAAQ,EAAE,MAAM;gBAChBK,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,GACC,CAAAgE,WAAW,aAAXA,WAAW,wBAAAP,sBAAA,GAAXO,WAAW,CAAE0K,SAAS,cAAAjL,sBAAA,uBAAtBA,sBAAA,CAAwB4L,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAE,CAAArL,WAAW,aAAXA,WAAW,wBAAAN,sBAAA,GAAXM,WAAW,CAAE2K,QAAQ,cAAAjL,sBAAA,uBAArBA,sBAAA,CAAuB2L,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;YAAA;cAAAjP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CACN,eAED7D,OAAA;cAAK2C,KAAK,EAAE;gBACVY,SAAS,EAAE;cACb,CAAE;cAAAD,QAAA,gBACAtD,OAAA;gBAAI2C,KAAK,EAAE;kBACTsO,MAAM,EAAE,cAAc;kBACtBxN,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,GACCgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0K,SAAS,EAAC,GAAC,EAAC1K,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2K,QAAQ;cAAA;gBAAAvO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACL7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRsO,MAAM,EAAE,CAAC;kBACTxN,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAK2C,KAAK,EAAE;cACVwO,IAAI,EAAE,CAAC;cACPlO,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBAEAtD,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAI2C,KAAK,EAAE;kBACTsO,MAAM,EAAE,YAAY;kBACpBxN,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChByN,YAAY,EAAE,mBAAmB;kBACjC6E,aAAa,EAAE;gBACjB,CAAE;gBAAArS,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL7D,OAAA;gBAAK2C,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACf2S,mBAAmB,EAAE,SAAS;kBAC9BjQ,GAAG,EAAE;gBACP,CAAE;gBAAArC,QAAA,gBACAtD,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0K,SAAS,KAAI;kBAAK;oBAAAtO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2K,QAAQ,KAAI;kBAAK;oBAAAvO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsL,KAAK,KAAI;kBAAK;oBAAAlP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuO,aAAa,KAAI;kBAAK;oBAAAnS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,GAAC,QACK,EAAC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+H,WAAW,KAAI,KAAK;kBAAA;oBAAA3L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAO2C,KAAK,EAAE;sBACZM,OAAO,EAAE,OAAO;sBAChBQ,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE,SAAS;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVsJ,OAAO,EAAE,SAAS;sBAClB7I,eAAe,EAAE,SAAS;sBAC1BU,MAAM,EAAE,mBAAmB;sBAC3BsB,YAAY,EAAE,KAAK;sBACnB3B,QAAQ,EAAE,UAAU;sBACpBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,CAAAgE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwO,WAAW,KAAI;kBAAK;oBAAApS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7D,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAI2C,KAAK,EAAE;kBACTsO,MAAM,EAAE,YAAY;kBACpBxN,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChByN,YAAY,EAAE,mBAAmB;kBACjC6E,aAAa,EAAE;gBACjB,CAAE;gBAAArS,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL7D,OAAA;gBAAK2C,KAAK,EAAE;kBACVsJ,OAAO,EAAE,MAAM;kBACf7I,eAAe,EAAE,SAAS;kBAC1BU,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,KAAK;kBACnB7B,SAAS,EAAE;gBACb,CAAE;gBAAAD,QAAA,gBACAtD,OAAA;kBAAG2C,KAAK,EAAE;oBACRsO,MAAM,EAAE,YAAY;oBACpBxN,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ7D,OAAA;kBAAG2C,KAAK,EAAE;oBACRsO,MAAM,EAAE,CAAC;oBACTxN,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE,SAAS;oBAChBU,UAAU,EAAE;kBACd,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YACVsJ,OAAO,EAAE,QAAQ;YACjBqH,SAAS,EAAE,mBAAmB;YAC9BrQ,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,eACAtD,OAAA;YACEmG,OAAO,EAAEA,CAAA,KAAMkD,sBAAsB,CAAC,KAAK,CAAE;YAC7C1G,KAAK,EAAE;cACLsJ,OAAO,EAAE,gBAAgB;cACzB7I,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdS,MAAM,EAAE,MAAM;cACdsB,YAAY,EAAE,KAAK;cACnB3B,QAAQ,EAAE,UAAU;cACpBM,UAAU,EAAE,KAAK;cACjBsB,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACS,eAAe,GAAG,SAAS;YACnD,CAAE;YACFL,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACS,eAAe,GAAG,SAAS;YACnD,CAAE;YAAAE,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7D,OAAA,CAAC5B,aAAa;MACZiG,MAAM,EAAEmF,cAAe;MACvB4C,YAAY,EAAE1C,oBAAqB;MACnCqM,MAAM,EAAEzM,YAAa;MACrB0M,OAAO,EAAEA,CAAA,KAAMzM,eAAe,CAAC,KAAK,CAAE;MACtCjF,SAAS,EAAC;IAAoB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC+C,GAAA,CArkFID,QAAiC;EAAA,QACpBpJ,WAAW,EAeLG,aAAa,EAUhCC,gBAAgB,EASyBC,qBAAqB;AAAA;AAAAqY,GAAA,GAnC9DtP,QAAiC;AAukFvC,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAAuC,GAAA,EAAAuP,GAAA;AAAAC,YAAA,CAAA/R,EAAA;AAAA+R,YAAA,CAAAxP,GAAA;AAAAwP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}