{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 5H19V11\",\n  key: \"1n1gyv\"\n}], [\"path\", {\n  d: \"M19 5L5 19\",\n  key: \"72u4yj\"\n}]];\nconst MoveUpRight = createLucideIcon(\"move-up-right\", __iconNode);\nexport { __iconNode, MoveUpRight as default };\n//# sourceMappingURL=move-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}