{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 17-5-5 5-5\",\n  key: \"13zhaf\"\n}], [\"path\", {\n  d: \"m18 17-5-5 5-5\",\n  key: \"h8a8et\"\n}]];\nconst ChevronsLeft = createLucideIcon(\"chevrons-left\", __iconNode);\nexport { __iconNode, ChevronsLeft as default };\n//# sourceMappingURL=chevrons-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}