{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"4ckbqe\"\n}], [\"path\", {\n  d: \"m16 10-4.5 4.5\",\n  key: \"7p3ebg\"\n}], [\"path\", {\n  d: \"m15 11 1 1\",\n  key: \"1bsyx3\"\n}]];\nconst FileKey = createLucideIcon(\"file-key\", __iconNode);\nexport { __iconNode, FileKey as default };\n//# sourceMappingURL=file-key.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}