{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 4v16\",\n  key: \"vw9hq8\"\n}], [\"path\", {\n  d: \"M2 8h18a2 2 0 0 1 2 2v10\",\n  key: \"1dgv2r\"\n}], [\"path\", {\n  d: \"M2 17h20\",\n  key: \"18nfp3\"\n}], [\"path\", {\n  d: \"M6 8v9\",\n  key: \"1yriud\"\n}]];\nconst Bed = createLucideIcon(\"bed\", __iconNode);\nexport { __iconNode, Bed as default };\n//# sourceMappingURL=bed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}