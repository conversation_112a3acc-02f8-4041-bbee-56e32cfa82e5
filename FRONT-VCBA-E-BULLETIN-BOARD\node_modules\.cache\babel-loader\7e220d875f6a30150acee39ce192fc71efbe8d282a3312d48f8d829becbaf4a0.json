{"ast": null, "code": "import React,{useState}from'react';import{Outlet}from'react-router-dom';import StudentSidebar from'./StudentSidebar';import StudentHeader from'./StudentHeader';import'../student.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentLayout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(true);const toggleSidebar=()=>{setSidebarOpen(!sidebarOpen);};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',height:'100vh',width:'100vw',position:'fixed',top:0,left:0,background:'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',overflow:'hidden'},children:[/*#__PURE__*/_jsx(StudentSidebar,{isOpen:sidebarOpen,onToggle:toggleSidebar}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',marginLeft:sidebarOpen?'280px':'80px',transition:'margin-left 0.3s ease',height:'100vh',overflow:'hidden'},children:[/*#__PURE__*/_jsx(StudentHeader,{onToggleSidebar:toggleSidebar}),/*#__PURE__*/_jsx(\"main\",{style:{flex:1,background:'transparent',overflow:'hidden',height:'calc(100vh - 80px)',// Subtract header height\nposition:'relative'},children:/*#__PURE__*/_jsx(\"div\",{style:{height:'100%',overflow:'auto',padding:'2rem'},children:children||/*#__PURE__*/_jsx(Outlet,{})})})]})]});};export default StudentLayout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}