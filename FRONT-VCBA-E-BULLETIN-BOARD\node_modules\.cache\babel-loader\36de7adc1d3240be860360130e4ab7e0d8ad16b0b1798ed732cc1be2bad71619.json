{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M8 8h8v8\",\n  key: \"b65dnt\"\n}], [\"path\", {\n  d: \"m8 16 8-8\",\n  key: \"13b9ih\"\n}]];\nconst SquareArrowUpRight = createLucideIcon(\"square-arrow-up-right\", __iconNode);\nexport { __iconNode, SquareArrowUpRight as default };\n//# sourceMappingURL=square-arrow-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}