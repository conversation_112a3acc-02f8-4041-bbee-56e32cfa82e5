{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10h4\",\n  key: \"tcdvrf\"\n}], [\"path\", {\n  d: \"M19 7V4a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3\",\n  key: \"3apit1\"\n}], [\"path\", {\n  d: \"M20 21a2 2 0 0 0 2-2v-3.851c0-1.39-2-2.962-2-4.829V8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v11a2 2 0 0 0 2 2z\",\n  key: \"rhpgnw\"\n}], [\"path\", {\n  d: \"M 22 16 L 2 16\",\n  key: \"14lkq7\"\n}], [\"path\", {\n  d: \"M4 21a2 2 0 0 1-2-2v-3.851c0-1.39 2-2.962 2-4.829V8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v11a2 2 0 0 1-2 2z\",\n  key: \"104b3k\"\n}], [\"path\", {\n  d: \"M9 7V4a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v3\",\n  key: \"14fczp\"\n}]];\nconst Binoculars = createLucideIcon(\"binoculars\", __iconNode);\nexport { __iconNode, Binoculars as default };\n//# sourceMappingURL=binoculars.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}