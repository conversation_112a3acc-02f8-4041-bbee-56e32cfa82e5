{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"15mx69\"\n}], [\"path\", {\n  d: \"M20 10c-2 2-3 3.5-3 6\",\n  key: \"f35dl0\"\n}]];\nconst Heading6 = createLucideIcon(\"heading-6\", __iconNode);\nexport { __iconNode, Heading6 as default };\n//# sourceMappingURL=heading-6.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}