{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M 20 4 A2 2 0 0 1 22 6\",\n  key: \"1g1fkt\"\n}], [\"path\", {\n  d: \"M 22 6 L 22 16.41\",\n  key: \"1qjg3w\"\n}], [\"path\", {\n  d: \"M 7 16 L 16 16\",\n  key: \"n0yqwb\"\n}], [\"path\", {\n  d: \"M 9.69 4 L 20 4\",\n  key: \"kbpcgx\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}], [\"path\", {\n  d: \"M18 8h.01\",\n  key: \"emo2bl\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2\",\n  key: \"s23sx2\"\n}], [\"path\", {\n  d: \"M6 8h.01\",\n  key: \"x9i8wu\"\n}], [\"path\", {\n  d: \"M8 12h.01\",\n  key: \"czm47f\"\n}]];\nconst KeyboardOff = createLucideIcon(\"keyboard-off\", __iconNode);\nexport { __iconNode, KeyboardOff as default };\n//# sourceMappingURL=keyboard-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}