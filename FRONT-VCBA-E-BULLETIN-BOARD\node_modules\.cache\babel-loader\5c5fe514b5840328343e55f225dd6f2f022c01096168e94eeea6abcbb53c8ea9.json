{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 19V5\",\n  key: \"1r845m\"\n}], [\"path\", {\n  d: \"M10 19V6.8\",\n  key: \"9j2tfs\"\n}], [\"path\", {\n  d: \"M14 19v-7.8\",\n  key: \"10s8qv\"\n}], [\"path\", {\n  d: \"M18 5v4\",\n  key: \"1tajlv\"\n}], [\"path\", {\n  d: \"M18 19v-6\",\n  key: \"ielfq3\"\n}], [\"path\", {\n  d: \"M22 19V9\",\n  key: \"158nzp\"\n}], [\"path\", {\n  d: \"M2 19V9a4 4 0 0 1 4-4c2 0 4 1.33 6 4s4 4 6 4a4 4 0 1 0-3-6.65\",\n  key: \"1930oh\"\n}]];\nconst RollerCoaster = createLucideIcon(\"roller-coaster\", __iconNode);\nexport { __iconNode, RollerCoaster as default };\n//# sourceMappingURL=roller-coaster.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}