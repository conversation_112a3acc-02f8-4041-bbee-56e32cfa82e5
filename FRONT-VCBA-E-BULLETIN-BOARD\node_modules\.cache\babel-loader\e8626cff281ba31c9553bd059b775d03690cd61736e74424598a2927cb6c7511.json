{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M12 9v4\",\n  key: \"juzpu7\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}]];\nconst FileWarning = createLucideIcon(\"file-warning\", __iconNode);\nexport { __iconNode, FileWarning as default };\n//# sourceMappingURL=file-warning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}