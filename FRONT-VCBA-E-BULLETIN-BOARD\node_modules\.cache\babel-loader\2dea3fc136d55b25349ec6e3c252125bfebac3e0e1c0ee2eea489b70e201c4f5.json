{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 11-1 9\",\n  key: \"5wnq3a\"\n}], [\"path\", {\n  d: \"m19 11-4-7\",\n  key: \"cnml18\"\n}], [\"path\", {\n  d: \"M2 11h20\",\n  key: \"3eubbj\"\n}], [\"path\", {\n  d: \"m3.5 11 1.6 7.4a2 2 0 0 0 2 1.6h9.8a2 2 0 0 0 2-1.6l1.7-7.4\",\n  key: \"yiazzp\"\n}], [\"path\", {\n  d: \"M4.5 15.5h15\",\n  key: \"13mye1\"\n}], [\"path\", {\n  d: \"m5 11 4-7\",\n  key: \"116ra9\"\n}], [\"path\", {\n  d: \"m9 11 1 9\",\n  key: \"1ojof7\"\n}]];\nconst ShoppingBasket = createLucideIcon(\"shopping-basket\", __iconNode);\nexport { __iconNode, ShoppingBasket as default };\n//# sourceMappingURL=shopping-basket.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}