{"ast": null, "code": "// Form utilities for announcement management\n\n// Validation rules for announcements\nexport const announcementValidationRules = {\n  title: {\n    required: true,\n    minLength: 1,\n    maxLength: 255\n  },\n  content: {\n    required: true,\n    minLength: 1,\n    maxLength: 10000\n  },\n  category_id: {\n    required: true,\n    type: 'integer',\n    min: 1\n  },\n  subcategory_id: {\n    required: false,\n    type: 'integer',\n    min: 1\n  },\n  status: {\n    required: false,\n    enum: ['draft', 'scheduled', 'published', 'archived']\n  }\n};\n\n// Validate form fields\nexport const validateFormFields = formData => {\n  const errors = {};\n\n  // Title validation\n  if (!formData.title || formData.title.trim().length === 0) {\n    errors.title = 'Title is required';\n  } else if (formData.title.trim().length > 255) {\n    errors.title = 'Title must be 255 characters or less';\n  }\n\n  // Content validation\n  if (!formData.content || formData.content.trim().length === 0) {\n    errors.content = 'Content is required';\n  } else if (formData.content.trim().length > 10000) {\n    errors.content = 'Content must be 10000 characters or less';\n  }\n\n  // Category validation\n  if (!formData.category_id || formData.category_id.trim() === '') {\n    errors.category_id = 'Category is required';\n  } else {\n    const categoryId = parseInt(formData.category_id);\n    if (isNaN(categoryId) || categoryId < 1) {\n      errors.category_id = 'Please select a valid category';\n    }\n  }\n\n  // Subcategory validation (optional)\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (isNaN(subcategoryId) || subcategoryId < 1) {\n      errors.subcategory_id = 'Please select a valid subcategory';\n    }\n  }\n\n  // Status validation\n  const validStatuses = ['draft', 'scheduled', 'published', 'archived'];\n  if (formData.status && !validStatuses.includes(formData.status)) {\n    errors.status = 'Invalid status selected';\n  }\n  return errors;\n};\n\n// Create FormData object for API submission\nexport const createFormData = (formData, selectedImages = []) => {\n  const form = new FormData();\n\n  // Add text fields with proper data type conversion\n  form.append('title', formData.title.trim());\n  form.append('content', formData.content.trim());\n\n  // Convert string IDs to integers for backend validation\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    form.append('category_id', categoryId.toString());\n  }\n\n  // Only add subcategory_id if it's a valid positive integer\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      form.append('subcategory_id', subcategoryId.toString());\n    }\n  }\n\n  // Add status (default to 'draft' if not provided)\n  form.append('status', formData.status || 'draft');\n\n  // Convert boolean values to strings for FormData\n  form.append('is_pinned', formData.is_pinned ? '1' : '0');\n  form.append('is_alert', formData.is_alert ? '1' : '0');\n  form.append('allow_comments', formData.allow_comments ? '1' : '0');\n  form.append('allow_sharing', formData.allow_sharing ? '1' : '0');\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    form.append('scheduled_publish_at', formData.scheduled_publish_at);\n  }\n\n  // Add image files\n  selectedImages.forEach(image => {\n    form.append('images', image);\n  });\n  return form;\n};\n\n// Create JSON data object for API submission (alternative to FormData)\nexport const createJsonData = formData => {\n  const data = {\n    title: formData.title.trim(),\n    content: formData.content.trim(),\n    status: formData.status || 'draft',\n    is_pinned: formData.is_pinned,\n    is_alert: formData.is_alert,\n    allow_comments: formData.allow_comments,\n    allow_sharing: formData.allow_sharing\n  };\n\n  // Convert string IDs to integers\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    data.category_id = categoryId;\n  }\n\n  // Only add subcategory_id if it's a valid positive integer\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      data.subcategory_id = subcategoryId;\n    }\n  }\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    data.scheduled_publish_at = formData.scheduled_publish_at;\n  }\n  return data;\n};\n\n// Helper function to convert form data for updates (only include changed fields)\nexport const createUpdateData = formData => {\n  const updateData = {};\n\n  // Only include fields that have values and might have changed\n  if (formData.title && formData.title.trim() !== '') {\n    updateData.title = formData.title.trim();\n  }\n  if (formData.content && formData.content.trim() !== '') {\n    updateData.content = formData.content.trim();\n  }\n\n  // Convert and validate category_id\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    updateData.category_id = categoryId;\n  }\n\n  // Convert and validate subcategory_id (can be null/undefined)\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      updateData.subcategory_id = subcategoryId;\n    }\n  } else {\n    // Explicitly set to null if empty (to clear existing subcategory)\n    updateData.subcategory_id = null;\n  }\n\n  // Always include these fields as they have default values\n  updateData.status = formData.status || 'draft';\n  updateData.is_pinned = formData.is_pinned;\n  updateData.is_alert = formData.is_alert;\n  updateData.allow_comments = formData.allow_comments;\n  updateData.allow_sharing = formData.allow_sharing;\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    updateData.scheduled_publish_at = formData.scheduled_publish_at;\n  }\n  return updateData;\n};", "map": {"version": 3, "names": ["announcementValidationRules", "title", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "content", "category_id", "type", "min", "subcategory_id", "status", "enum", "validate<PERSON><PERSON><PERSON><PERSON>s", "formData", "errors", "trim", "length", "categoryId", "parseInt", "isNaN", "subcategoryId", "validStatuses", "includes", "createFormData", "selectedImages", "form", "FormData", "append", "toString", "is_pinned", "is_alert", "allow_comments", "allow_sharing", "scheduled_publish_at", "for<PERSON>ach", "image", "createJsonData", "data", "createUpdateData", "updateData"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["// Form utilities for announcement management\r\nexport interface AnnouncementFormData {\r\n  title: string;\r\n  content: string;\r\n  category_id: string;\r\n  subcategory_id: string;\r\n  status: 'draft' | 'published' | 'scheduled' | 'archived';\r\n  is_pinned: boolean;\r\n  is_alert: boolean;\r\n  allow_comments: boolean;\r\n  allow_sharing: boolean;\r\n  scheduled_publish_at: string;\r\n}\r\n\r\n// Validation rules for announcements\r\nexport const announcementValidationRules = {\r\n  title: {\r\n    required: true,\r\n    minLength: 1,\r\n    maxLength: 255\r\n  },\r\n  content: {\r\n    required: true,\r\n    minLength: 1,\r\n    maxLength: 10000\r\n  },\r\n  category_id: {\r\n    required: true,\r\n    type: 'integer',\r\n    min: 1\r\n  },\r\n  subcategory_id: {\r\n    required: false,\r\n    type: 'integer',\r\n    min: 1\r\n  },\r\n  status: {\r\n    required: false,\r\n    enum: ['draft', 'scheduled', 'published', 'archived']\r\n  }\r\n};\r\n\r\n// Validate form fields\r\nexport const validateFormFields = (formData: AnnouncementFormData): { [key: string]: string } => {\r\n  const errors: { [key: string]: string } = {};\r\n\r\n  // Title validation\r\n  if (!formData.title || formData.title.trim().length === 0) {\r\n    errors.title = 'Title is required';\r\n  } else if (formData.title.trim().length > 255) {\r\n    errors.title = 'Title must be 255 characters or less';\r\n  }\r\n\r\n  // Content validation\r\n  if (!formData.content || formData.content.trim().length === 0) {\r\n    errors.content = 'Content is required';\r\n  } else if (formData.content.trim().length > 10000) {\r\n    errors.content = 'Content must be 10000 characters or less';\r\n  }\r\n\r\n  // Category validation\r\n  if (!formData.category_id || formData.category_id.trim() === '') {\r\n    errors.category_id = 'Category is required';\r\n  } else {\r\n    const categoryId = parseInt(formData.category_id);\r\n    if (isNaN(categoryId) || categoryId < 1) {\r\n      errors.category_id = 'Please select a valid category';\r\n    }\r\n  }\r\n\r\n  // Subcategory validation (optional)\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (isNaN(subcategoryId) || subcategoryId < 1) {\r\n      errors.subcategory_id = 'Please select a valid subcategory';\r\n    }\r\n  }\r\n\r\n  // Status validation\r\n  const validStatuses = ['draft', 'scheduled', 'published', 'archived'];\r\n  if (formData.status && !validStatuses.includes(formData.status)) {\r\n    errors.status = 'Invalid status selected';\r\n  }\r\n\r\n  return errors;\r\n};\r\n\r\n// Create FormData object for API submission\r\nexport const createFormData = (formData: AnnouncementFormData, selectedImages: File[] = []): FormData => {\r\n  const form = new FormData();\r\n\r\n  // Add text fields with proper data type conversion\r\n  form.append('title', formData.title.trim());\r\n  form.append('content', formData.content.trim());\r\n\r\n  // Convert string IDs to integers for backend validation\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    form.append('category_id', categoryId.toString());\r\n  }\r\n\r\n  // Only add subcategory_id if it's a valid positive integer\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      form.append('subcategory_id', subcategoryId.toString());\r\n    }\r\n  }\r\n\r\n  // Add status (default to 'draft' if not provided)\r\n  form.append('status', formData.status || 'draft');\r\n\r\n  // Convert boolean values to strings for FormData\r\n  form.append('is_pinned', formData.is_pinned ? '1' : '0');\r\n  form.append('is_alert', formData.is_alert ? '1' : '0');\r\n  form.append('allow_comments', formData.allow_comments ? '1' : '0');\r\n  form.append('allow_sharing', formData.allow_sharing ? '1' : '0');\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    form.append('scheduled_publish_at', formData.scheduled_publish_at);\r\n  }\r\n\r\n  // Add image files\r\n  selectedImages.forEach((image) => {\r\n    form.append('images', image);\r\n  });\r\n\r\n  return form;\r\n};\r\n\r\n// Create JSON data object for API submission (alternative to FormData)\r\nexport const createJsonData = (formData: AnnouncementFormData): any => {\r\n  const data: any = {\r\n    title: formData.title.trim(),\r\n    content: formData.content.trim(),\r\n    status: formData.status || 'draft',\r\n    is_pinned: formData.is_pinned,\r\n    is_alert: formData.is_alert,\r\n    allow_comments: formData.allow_comments,\r\n    allow_sharing: formData.allow_sharing\r\n  };\r\n\r\n  // Convert string IDs to integers\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    data.category_id = categoryId;\r\n  }\r\n\r\n  // Only add subcategory_id if it's a valid positive integer\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      data.subcategory_id = subcategoryId;\r\n    }\r\n  }\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    data.scheduled_publish_at = formData.scheduled_publish_at;\r\n  }\r\n\r\n  return data;\r\n};\r\n\r\n// Helper function to convert form data for updates (only include changed fields)\r\nexport const createUpdateData = (formData: AnnouncementFormData): any => {\r\n  const updateData: any = {};\r\n\r\n  // Only include fields that have values and might have changed\r\n  if (formData.title && formData.title.trim() !== '') {\r\n    updateData.title = formData.title.trim();\r\n  }\r\n\r\n  if (formData.content && formData.content.trim() !== '') {\r\n    updateData.content = formData.content.trim();\r\n  }\r\n\r\n  // Convert and validate category_id\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    updateData.category_id = categoryId;\r\n  }\r\n\r\n  // Convert and validate subcategory_id (can be null/undefined)\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      updateData.subcategory_id = subcategoryId;\r\n    }\r\n  } else {\r\n    // Explicitly set to null if empty (to clear existing subcategory)\r\n    updateData.subcategory_id = null;\r\n  }\r\n\r\n  // Always include these fields as they have default values\r\n  updateData.status = formData.status || 'draft';\r\n  updateData.is_pinned = formData.is_pinned;\r\n  updateData.is_alert = formData.is_alert;\r\n  updateData.allow_comments = formData.allow_comments;\r\n  updateData.allow_sharing = formData.allow_sharing;\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    updateData.scheduled_publish_at = formData.scheduled_publish_at;\r\n  }\r\n\r\n  return updateData;\r\n};"], "mappings": "AAAA;;AAcA;AACA,OAAO,MAAMA,2BAA2B,GAAG;EACzCC,KAAK,EAAE;IACLC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC;EACDC,OAAO,EAAE;IACPH,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC;EACDE,WAAW,EAAE;IACXJ,QAAQ,EAAE,IAAI;IACdK,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE;EACP,CAAC;EACDC,cAAc,EAAE;IACdP,QAAQ,EAAE,KAAK;IACfK,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE;EACP,CAAC;EACDE,MAAM,EAAE;IACNR,QAAQ,EAAE,KAAK;IACfS,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU;EACtD;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAIC,QAA8B,IAAgC;EAC/F,MAAMC,MAAiC,GAAG,CAAC,CAAC;;EAE5C;EACA,IAAI,CAACD,QAAQ,CAACZ,KAAK,IAAIY,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IACzDF,MAAM,CAACb,KAAK,GAAG,mBAAmB;EACpC,CAAC,MAAM,IAAIY,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,GAAG,EAAE;IAC7CF,MAAM,CAACb,KAAK,GAAG,sCAAsC;EACvD;;EAEA;EACA,IAAI,CAACY,QAAQ,CAACR,OAAO,IAAIQ,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IAC7DF,MAAM,CAACT,OAAO,GAAG,qBAAqB;EACxC,CAAC,MAAM,IAAIQ,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,KAAK,EAAE;IACjDF,MAAM,CAACT,OAAO,GAAG,0CAA0C;EAC7D;;EAEA;EACA,IAAI,CAACQ,QAAQ,CAACP,WAAW,IAAIO,QAAQ,CAACP,WAAW,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC/DD,MAAM,CAACR,WAAW,GAAG,sBAAsB;EAC7C,CAAC,MAAM;IACL,MAAMW,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACP,WAAW,CAAC;IACjD,IAAIa,KAAK,CAACF,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACvCH,MAAM,CAACR,WAAW,GAAG,gCAAgC;IACvD;EACF;;EAEA;EACA,IAAIO,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACJ,cAAc,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMK,aAAa,GAAGF,QAAQ,CAACL,QAAQ,CAACJ,cAAc,CAAC;IACvD,IAAIU,KAAK,CAACC,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC7CN,MAAM,CAACL,cAAc,GAAG,mCAAmC;IAC7D;EACF;;EAEA;EACA,MAAMY,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;EACrE,IAAIR,QAAQ,CAACH,MAAM,IAAI,CAACW,aAAa,CAACC,QAAQ,CAACT,QAAQ,CAACH,MAAM,CAAC,EAAE;IAC/DI,MAAM,CAACJ,MAAM,GAAG,yBAAyB;EAC3C;EAEA,OAAOI,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAGA,CAACV,QAA8B,EAAEW,cAAsB,GAAG,EAAE,KAAe;EACvG,MAAMC,IAAI,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE3B;EACAD,IAAI,CAACE,MAAM,CAAC,OAAO,EAAEd,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC;EAC3CU,IAAI,CAACE,MAAM,CAAC,SAAS,EAAEd,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAME,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACP,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACF,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxCQ,IAAI,CAACE,MAAM,CAAC,aAAa,EAAEV,UAAU,CAACW,QAAQ,CAAC,CAAC,CAAC;EACnD;;EAEA;EACA,IAAIf,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACJ,cAAc,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMK,aAAa,GAAGF,QAAQ,CAACL,QAAQ,CAACJ,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACC,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CK,IAAI,CAACE,MAAM,CAAC,gBAAgB,EAAEP,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACzD;EACF;;EAEA;EACAH,IAAI,CAACE,MAAM,CAAC,QAAQ,EAAEd,QAAQ,CAACH,MAAM,IAAI,OAAO,CAAC;;EAEjD;EACAe,IAAI,CAACE,MAAM,CAAC,WAAW,EAAEd,QAAQ,CAACgB,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC;EACxDJ,IAAI,CAACE,MAAM,CAAC,UAAU,EAAEd,QAAQ,CAACiB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;EACtDL,IAAI,CAACE,MAAM,CAAC,gBAAgB,EAAEd,QAAQ,CAACkB,cAAc,GAAG,GAAG,GAAG,GAAG,CAAC;EAClEN,IAAI,CAACE,MAAM,CAAC,eAAe,EAAEd,QAAQ,CAACmB,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;;EAEhE;EACA,IAAInB,QAAQ,CAACoB,oBAAoB,IAAIpB,QAAQ,CAACoB,oBAAoB,CAAClB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFU,IAAI,CAACE,MAAM,CAAC,sBAAsB,EAAEd,QAAQ,CAACoB,oBAAoB,CAAC;EACpE;;EAEA;EACAT,cAAc,CAACU,OAAO,CAAEC,KAAK,IAAK;IAChCV,IAAI,CAACE,MAAM,CAAC,QAAQ,EAAEQ,KAAK,CAAC;EAC9B,CAAC,CAAC;EAEF,OAAOV,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMW,cAAc,GAAIvB,QAA8B,IAAU;EACrE,MAAMwB,IAAS,GAAG;IAChBpC,KAAK,EAAEY,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC;IAC5BV,OAAO,EAAEQ,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC;IAChCL,MAAM,EAAEG,QAAQ,CAACH,MAAM,IAAI,OAAO;IAClCmB,SAAS,EAAEhB,QAAQ,CAACgB,SAAS;IAC7BC,QAAQ,EAAEjB,QAAQ,CAACiB,QAAQ;IAC3BC,cAAc,EAAElB,QAAQ,CAACkB,cAAc;IACvCC,aAAa,EAAEnB,QAAQ,CAACmB;EAC1B,CAAC;;EAED;EACA,MAAMf,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACP,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACF,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxCoB,IAAI,CAAC/B,WAAW,GAAGW,UAAU;EAC/B;;EAEA;EACA,IAAIJ,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACJ,cAAc,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMK,aAAa,GAAGF,QAAQ,CAACL,QAAQ,CAACJ,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACC,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CiB,IAAI,CAAC5B,cAAc,GAAGW,aAAa;IACrC;EACF;;EAEA;EACA,IAAIP,QAAQ,CAACoB,oBAAoB,IAAIpB,QAAQ,CAACoB,oBAAoB,CAAClB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFsB,IAAI,CAACJ,oBAAoB,GAAGpB,QAAQ,CAACoB,oBAAoB;EAC3D;EAEA,OAAOI,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAIzB,QAA8B,IAAU;EACvE,MAAM0B,UAAe,GAAG,CAAC,CAAC;;EAE1B;EACA,IAAI1B,QAAQ,CAACZ,KAAK,IAAIY,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAClDwB,UAAU,CAACtC,KAAK,GAAGY,QAAQ,CAACZ,KAAK,CAACc,IAAI,CAAC,CAAC;EAC1C;EAEA,IAAIF,QAAQ,CAACR,OAAO,IAAIQ,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACtDwB,UAAU,CAAClC,OAAO,GAAGQ,QAAQ,CAACR,OAAO,CAACU,IAAI,CAAC,CAAC;EAC9C;;EAEA;EACA,MAAME,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACP,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACF,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxCsB,UAAU,CAACjC,WAAW,GAAGW,UAAU;EACrC;;EAEA;EACA,IAAIJ,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACJ,cAAc,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMK,aAAa,GAAGF,QAAQ,CAACL,QAAQ,CAACJ,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACC,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CmB,UAAU,CAAC9B,cAAc,GAAGW,aAAa;IAC3C;EACF,CAAC,MAAM;IACL;IACAmB,UAAU,CAAC9B,cAAc,GAAG,IAAI;EAClC;;EAEA;EACA8B,UAAU,CAAC7B,MAAM,GAAGG,QAAQ,CAACH,MAAM,IAAI,OAAO;EAC9C6B,UAAU,CAACV,SAAS,GAAGhB,QAAQ,CAACgB,SAAS;EACzCU,UAAU,CAACT,QAAQ,GAAGjB,QAAQ,CAACiB,QAAQ;EACvCS,UAAU,CAACR,cAAc,GAAGlB,QAAQ,CAACkB,cAAc;EACnDQ,UAAU,CAACP,aAAa,GAAGnB,QAAQ,CAACmB,aAAa;;EAEjD;EACA,IAAInB,QAAQ,CAACoB,oBAAoB,IAAIpB,QAAQ,CAACoB,oBAAoB,CAAClB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFwB,UAAU,CAACN,oBAAoB,GAAGpB,QAAQ,CAACoB,oBAAoB;EACjE;EAEA,OAAOM,UAAU;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}