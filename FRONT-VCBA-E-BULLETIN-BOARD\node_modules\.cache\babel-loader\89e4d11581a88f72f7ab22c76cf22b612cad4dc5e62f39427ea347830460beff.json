{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M16 22h2a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3.5\",\n  key: \"13ddob\"\n}], [\"path\", {\n  d: \"M4.017 11.512a6 6 0 1 0 8.466 8.475\",\n  key: \"s6vs5t\"\n}], [\"path\", {\n  d: \"M9 16a1 1 0 0 1-1-1v-4c0-.552.45-1.008.995-.917a6 6 0 0 1 4.922 4.922c.091.544-.365.995-.917.995z\",\n  key: \"1dl6s6\"\n}]];\nconst FileChartPie = createLucideIcon(\"file-chart-pie\", __iconNode);\nexport { __iconNode, FileChartPie as default };\n//# sourceMappingURL=file-chart-pie.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}