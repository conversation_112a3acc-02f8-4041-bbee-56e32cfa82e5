{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21.12 6.4-6.05-4.06a2 2 0 0 0-2.17-.05L2.95 8.41a2 2 0 0 0-.95 1.7v5.82a2 2 0 0 0 .88 1.66l6.05 4.07a2 2 0 0 0 2.17.05l9.95-6.12a2 2 0 0 0 .95-1.7V8.06a2 2 0 0 0-.88-1.66Z\",\n  key: \"1u2ovd\"\n}], [\"path\", {\n  d: \"M10 22v-8L2.25 9.15\",\n  key: \"11pn4q\"\n}], [\"path\", {\n  d: \"m10 14 11.77-6.87\",\n  key: \"1kt1wh\"\n}]];\nconst Cuboid = createLucideIcon(\"cuboid\", __iconNode);\nexport { __iconNode, Cuboid as default };\n//# sourceMappingURL=cuboid.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}