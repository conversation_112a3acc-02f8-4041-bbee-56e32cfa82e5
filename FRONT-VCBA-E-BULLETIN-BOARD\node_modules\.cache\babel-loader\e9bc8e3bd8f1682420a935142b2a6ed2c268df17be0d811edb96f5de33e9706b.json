{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"11\",\n  x: \"3\",\n  y: \"11\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1w4ew1\"\n}], [\"path\", {\n  d: \"M7 11V7a5 5 0 0 1 10 0v4\",\n  key: \"fwvmzm\"\n}]];\nconst Lock = createLucideIcon(\"lock\", __iconNode);\nexport { __iconNode, Lock as default };\n//# sourceMappingURL=lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}