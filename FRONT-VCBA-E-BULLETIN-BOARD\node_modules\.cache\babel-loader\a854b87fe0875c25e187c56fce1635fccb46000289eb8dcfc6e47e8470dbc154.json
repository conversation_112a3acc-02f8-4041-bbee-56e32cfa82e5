{"ast": null, "code": "/**\n * Form utility functions for consistent form handling across the application\n */\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields with proper validation and transformation\n  Object.entries(formData).forEach(([key, value]) => {\n    if (key === 'category_id' && typeof value === 'string') {\n      // category_id is required and must be a valid number\n      const categoryId = parseInt(value);\n      if (!isNaN(categoryId) && categoryId > 0) {\n        formDataToSubmit.append(key, categoryId.toString());\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // subcategory_id is optional - only append if it's a valid number\n      if (value.trim() !== '') {\n        const subcategoryId = parseInt(value);\n        if (!isNaN(subcategoryId) && subcategoryId > 0) {\n          formDataToSubmit.append(key, subcategoryId.toString());\n        }\n      }\n      // If empty, don't append anything (backend will handle as null)\n    } else if (key === 'scheduled_publish_at') {\n      // Only include scheduled_publish_at if status is 'scheduled' and value is not empty\n      if (formData.status === 'scheduled' && value && typeof value === 'string' && value.trim() !== '') {\n        formDataToSubmit.append(key, value.trim());\n      }\n      // For non-scheduled announcements, don't include this field\n    } else if (typeof value === 'boolean') {\n      // Boolean fields - convert to '1' or '0'\n      const convertedValue = value ? '1' : '0';\n      console.log(`📋 FormData boolean conversion: ${key} = ${value} -> ${convertedValue}`);\n      formDataToSubmit.append(key, convertedValue);\n    } else if (value !== null && value !== undefined && value !== '') {\n      // Regular fields - only append if they have actual content\n      formDataToSubmit.append(key, value.toString().trim());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "categoryId", "parseInt", "isNaN", "append", "toString", "trim", "subcategoryId", "status", "convertedValue", "console", "log", "undefined", "length", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "join", "size"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\n * Form utility functions for consistent form handling across the application\n */\n\nexport interface FormField {\n  [key: string]: string | number | boolean | null | undefined;\n}\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (\n  formData: FormField,\n  files: File[] = [],\n  options: {\n    skipScheduledDate?: boolean;\n    fileFieldName?: string;\n  } = {}\n): FormData => {\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields with proper validation and transformation\n  Object.entries(formData).forEach(([key, value]) => {\n    if (key === 'category_id' && typeof value === 'string') {\n      // category_id is required and must be a valid number\n      const categoryId = parseInt(value);\n      if (!isNaN(categoryId) && categoryId > 0) {\n        formDataToSubmit.append(key, categoryId.toString());\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // subcategory_id is optional - only append if it's a valid number\n      if (value.trim() !== '') {\n        const subcategoryId = parseInt(value);\n        if (!isNaN(subcategoryId) && subcategoryId > 0) {\n          formDataToSubmit.append(key, subcategoryId.toString());\n        }\n      }\n      // If empty, don't append anything (backend will handle as null)\n    } else if (key === 'scheduled_publish_at') {\n      // Only include scheduled_publish_at if status is 'scheduled' and value is not empty\n      if (formData.status === 'scheduled' && value && typeof value === 'string' && value.trim() !== '') {\n        formDataToSubmit.append(key, value.trim());\n      }\n      // For non-scheduled announcements, don't include this field\n    } else if (typeof value === 'boolean') {\n      // Boolean fields - convert to '1' or '0'\n      const convertedValue = value ? '1' : '0';\n      console.log(`📋 FormData boolean conversion: ${key} = ${value} -> ${convertedValue}`);\n      formDataToSubmit.append(key, convertedValue);\n    } else if (value !== null && value !== undefined && value !== '') {\n      // Regular fields - only append if they have actual content\n      formDataToSubmit.append(key, value.toString().trim());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach((file) => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (\n  formData: FormField,\n  rules: {\n    required?: string[];\n    maxLength?: { [key: string]: number };\n    custom?: { [key: string]: (value: any) => string | null };\n  } = {}\n): Record<string, string> => {\n  const errors: Record<string, string> = {};\n  const { required = [], maxLength = {}, custom = {} } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || (typeof value === 'string' && !value.trim())) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: { title: 255 },\n  custom: {\n    scheduled_publish_at: (value: any, formData?: FormField) => {\n      if (formData?.status === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (\n  file: File,\n  options: {\n    maxSize?: number;\n    allowedTypes?: string[];\n  } = {}\n): string | null => {\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\n\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n\n  return null;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACAC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,CAACS,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD,IAAID,GAAG,KAAK,aAAa,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MACtD;MACA,MAAMC,UAAU,GAAGC,QAAQ,CAACF,KAAK,CAAC;MAClC,IAAI,CAACG,KAAK,CAACF,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;QACxCP,gBAAgB,CAACU,MAAM,CAACL,GAAG,EAAEE,UAAU,CAACI,QAAQ,CAAC,CAAC,CAAC;MACrD;IACF,CAAC,MAAM,IAAIN,GAAG,KAAK,gBAAgB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMC,aAAa,GAAGL,QAAQ,CAACF,KAAK,CAAC;QACrC,IAAI,CAACG,KAAK,CAACI,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;UAC9Cb,gBAAgB,CAACU,MAAM,CAACL,GAAG,EAAEQ,aAAa,CAACF,QAAQ,CAAC,CAAC,CAAC;QACxD;MACF;MACA;IACF,CAAC,MAAM,IAAIN,GAAG,KAAK,sBAAsB,EAAE;MACzC;MACA,IAAIV,QAAQ,CAACmB,MAAM,KAAK,WAAW,IAAIR,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAChGZ,gBAAgB,CAACU,MAAM,CAACL,GAAG,EAAEC,KAAK,CAACM,IAAI,CAAC,CAAC,CAAC;MAC5C;MACA;IACF,CAAC,MAAM,IAAI,OAAON,KAAK,KAAK,SAAS,EAAE;MACrC;MACA,MAAMS,cAAc,GAAGT,KAAK,GAAG,GAAG,GAAG,GAAG;MACxCU,OAAO,CAACC,GAAG,CAAC,mCAAmCZ,GAAG,MAAMC,KAAK,OAAOS,cAAc,EAAE,CAAC;MACrFf,gBAAgB,CAACU,MAAM,CAACL,GAAG,EAAEU,cAAc,CAAC;IAC9C,CAAC,MAAM,IAAIT,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,IAAIZ,KAAK,KAAK,EAAE,EAAE;MAChE;MACAN,gBAAgB,CAACU,MAAM,CAACL,GAAG,EAAEC,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;;EAEF;EACA,IAAIhB,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;IACpBvB,KAAK,CAACQ,OAAO,CAAEgB,IAAI,IAAK;MACtBpB,gBAAgB,CAACU,MAAM,CAACX,aAAa,EAAEqB,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAOpB,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,kBAAkB,GAAGA,CAChC1B,QAAmB,EACnB2B,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACpB,OAAO,CAACuB,KAAK,IAAI;IACxB,MAAMrB,KAAK,GAAGX,QAAQ,CAACgC,KAAK,CAAC;IAC7B,IAAI,CAACrB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACM,IAAI,CAAC,CAAE,EAAE;MAC1DW,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACA1B,MAAM,CAACC,OAAO,CAACsB,SAAS,CAAC,CAACrB,OAAO,CAAC,CAAC,CAACuB,KAAK,EAAEE,GAAG,CAAC,KAAK;IAClD,MAAMvB,KAAK,GAAGX,QAAQ,CAACgC,KAAK,CAAC;IAC7B,IAAI,OAAOrB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACa,MAAM,GAAGU,GAAG,EAAE;MACnDN,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACA3B,MAAM,CAACC,OAAO,CAACuB,MAAM,CAAC,CAACtB,OAAO,CAAC,CAAC,CAACuB,KAAK,EAAEG,SAAS,CAAC,KAAK;IACrD,MAAMxB,KAAK,GAAGX,QAAQ,CAACgC,KAAK,CAAC;IAC7B,MAAMI,KAAK,GAAGD,SAAS,CAACxB,KAAK,CAAC;IAC9B,IAAIyB,KAAK,EAAE;MACTR,MAAM,CAACI,KAAK,CAAC,GAAGI,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,2BAA2B,GAAG;EACzCR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEQ,KAAK,EAAE;EAAI,CAAC;EACzBP,MAAM,EAAE;IACNQ,oBAAoB,EAAEA,CAAC5B,KAAU,EAAEX,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmB,MAAM,MAAK,WAAW,IAAI,CAACR,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACzB,GAAG,CAACmB,KAAK,CAAC,GAAGM,IAAI,CAACzB,GAAG,CAACqB,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGM,IAAI,CAACG,GAAG,CAACP,CAAC,EAAEG,CAAC,CAAC,EAAEK,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAGA,CAC1B3B,IAAU,EACVvB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEmD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGpD,OAAO;EAEpH,IAAI,CAACoD,YAAY,CAACC,QAAQ,CAAC9B,IAAI,CAAC+B,IAAI,CAAC,EAAE;IACrC,OAAO,aAAa/B,IAAI,CAAC+B,IAAI,qCAAqCF,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIhC,IAAI,CAACiC,IAAI,GAAGL,OAAO,EAAE;IACvB,OAAO,aAAab,cAAc,CAACf,IAAI,CAACiC,IAAI,CAAC,oCAAoClB,cAAc,CAACa,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}