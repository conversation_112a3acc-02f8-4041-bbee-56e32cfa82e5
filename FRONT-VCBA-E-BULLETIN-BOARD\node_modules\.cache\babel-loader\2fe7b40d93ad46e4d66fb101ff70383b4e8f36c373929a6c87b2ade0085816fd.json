{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 7v14\",\n  key: \"1akyts\"\n}], [\"path\", {\n  d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n  key: \"ruj8y\"\n}]];\nconst BookOpen = createLucideIcon(\"book-open\", __iconNode);\nexport { __iconNode, BookOpen as default };\n//# sourceMappingURL=book-open.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}