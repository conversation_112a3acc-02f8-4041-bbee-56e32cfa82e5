{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.5 12.5-5-5\",\n  key: \"1jahn5\"\n}], [\"path\", {\n  d: \"m9.5 12.5 5-5\",\n  key: \"1k2t7b\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}]];\nconst MonitorX = createLucideIcon(\"monitor-x\", __iconNode);\nexport { __iconNode, MonitorX as default };\n//# sourceMappingURL=monitor-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}