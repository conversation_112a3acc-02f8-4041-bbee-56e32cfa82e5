[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx": "68", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "69", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx": "70", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx": "71", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx": "72", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts": "73", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx": "74", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx": "75", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx": "76"}, {"size": 554, "mtime": 1752306944000, "results": "77", "hashOfConfig": "78"}, {"size": 419, "mtime": 1751128028000, "results": "79", "hashOfConfig": "78"}, {"size": 6148, "mtime": 1753733668213, "results": "80", "hashOfConfig": "78"}, {"size": 7830, "mtime": 1752857570939, "results": "81", "hashOfConfig": "78"}, {"size": 3769, "mtime": 1752721564000, "results": "82", "hashOfConfig": "78"}, {"size": 6136, "mtime": 1752857570952, "results": "83", "hashOfConfig": "78"}, {"size": 7447, "mtime": 1752271830000, "results": "84", "hashOfConfig": "78"}, {"size": 30726, "mtime": 1753161873129, "results": "85", "hashOfConfig": "78"}, {"size": 50360, "mtime": 1753139353321, "results": "86", "hashOfConfig": "78"}, {"size": 45894, "mtime": 1752867480062, "results": "87", "hashOfConfig": "78"}, {"size": 5757, "mtime": 1752391390000, "results": "88", "hashOfConfig": "78"}, {"size": 77680, "mtime": 1753728451520, "results": "89", "hashOfConfig": "78"}, {"size": 56, "mtime": 1753607885462, "results": "90", "hashOfConfig": "78"}, {"size": 1342, "mtime": 1751155290000, "results": "91", "hashOfConfig": "78"}, {"size": 1688, "mtime": 1751208620000, "results": "92", "hashOfConfig": "78"}, {"size": 103, "mtime": 1751140878000, "results": "93", "hashOfConfig": "78"}, {"size": 232, "mtime": 1751541102000, "results": "94", "hashOfConfig": "78"}, {"size": 3996, "mtime": 1751807708000, "results": "95", "hashOfConfig": "78"}, {"size": 8153, "mtime": 1753729692309, "results": "96", "hashOfConfig": "78"}, {"size": 16125, "mtime": 1753159156475, "results": "97", "hashOfConfig": "78"}, {"size": 17132, "mtime": 1752721564000, "results": "98", "hashOfConfig": "78"}, {"size": 7759, "mtime": 1753726439666, "results": "99", "hashOfConfig": "78"}, {"size": 14456, "mtime": 1753728961926, "results": "100", "hashOfConfig": "78"}, {"size": 8587, "mtime": 1752337674000, "results": "101", "hashOfConfig": "78"}, {"size": 25200, "mtime": 1753729065843, "results": "102", "hashOfConfig": "78"}, {"size": 12906, "mtime": 1752333634000, "results": "103", "hashOfConfig": "78"}, {"size": 6264, "mtime": 1753735348071, "results": "104", "hashOfConfig": "78"}, {"size": 9834, "mtime": 1753077700688, "results": "105", "hashOfConfig": "78"}, {"size": 8214, "mtime": 1752334762000, "results": "106", "hashOfConfig": "78"}, {"size": 12982, "mtime": 1752333550000, "results": "107", "hashOfConfig": "78"}, {"size": 19475, "mtime": 1753729131110, "results": "108", "hashOfConfig": "78"}, {"size": 15593, "mtime": 1752760674000, "results": "109", "hashOfConfig": "78"}, {"size": 12325, "mtime": 1752330204000, "results": "110", "hashOfConfig": "78"}, {"size": 28178, "mtime": 1753079518460, "results": "111", "hashOfConfig": "78"}, {"size": 24168, "mtime": 1752867418410, "results": "112", "hashOfConfig": "78"}, {"size": 39901, "mtime": 1753141345895, "results": "113", "hashOfConfig": "78"}, {"size": 6252, "mtime": 1753733196451, "results": "114", "hashOfConfig": "78"}, {"size": 9059, "mtime": 1753730582696, "results": "115", "hashOfConfig": "78"}, {"size": 15555, "mtime": 1753733696413, "results": "116", "hashOfConfig": "78"}, {"size": 5633, "mtime": 1753730655191, "results": "117", "hashOfConfig": "78"}, {"size": 2063, "mtime": 1751140856000, "results": "118", "hashOfConfig": "78"}, {"size": 2236, "mtime": 1753600593548, "results": "119", "hashOfConfig": "78"}, {"size": 4237, "mtime": 1753600593548, "results": "120", "hashOfConfig": "78"}, {"size": 230, "mtime": 1751371668000, "results": "121", "hashOfConfig": "78"}, {"size": 10813, "mtime": 1752761372000, "results": "122", "hashOfConfig": "78"}, {"size": 14506, "mtime": 1752879590759, "results": "123", "hashOfConfig": "78"}, {"size": 26448, "mtime": 1752380598000, "results": "124", "hashOfConfig": "78"}, {"size": 10147, "mtime": 1752334796000, "results": "125", "hashOfConfig": "78"}, {"size": 10510, "mtime": 1752310980000, "results": "126", "hashOfConfig": "78"}, {"size": 10877, "mtime": 1752092600000, "results": "127", "hashOfConfig": "78"}, {"size": 7318, "mtime": 1752381124000, "results": "128", "hashOfConfig": "78"}, {"size": 5263, "mtime": 1752867135753, "results": "129", "hashOfConfig": "78"}, {"size": 19520, "mtime": 1752338090000, "results": "130", "hashOfConfig": "78"}, {"size": 13444, "mtime": 1752870507174, "results": "131", "hashOfConfig": "78"}, {"size": 16870, "mtime": 1752338106000, "results": "132", "hashOfConfig": "78"}, {"size": 616, "mtime": 1752865556056, "results": "133", "hashOfConfig": "78"}, {"size": 15760, "mtime": 1752828048867, "results": "134", "hashOfConfig": "78"}, {"size": 9958, "mtime": 1751375132000, "results": "135", "hashOfConfig": "78"}, {"size": 42, "mtime": 1751129052000, "results": "136", "hashOfConfig": "78"}, {"size": 20869, "mtime": 1752885135797, "results": "137", "hashOfConfig": "78"}, {"size": 10085, "mtime": 1753141914993, "results": "138", "hashOfConfig": "78"}, {"size": 9297, "mtime": 1751476824000, "results": "139", "hashOfConfig": "78"}, {"size": 4237, "mtime": 1753722668541, "results": "140", "hashOfConfig": "78"}, {"size": 3686, "mtime": 1752890875441, "results": "141", "hashOfConfig": "78"}, {"size": 18927, "mtime": 1753141219821, "results": "142", "hashOfConfig": "78"}, {"size": 7423, "mtime": 1753077217399, "results": "143", "hashOfConfig": "78"}, {"size": 18379, "mtime": 1753155927801, "results": "144", "hashOfConfig": "78"}, {"size": 18988, "mtime": 1753609851189, "results": "145", "hashOfConfig": "78"}, {"size": 90955, "mtime": 1753616469959, "results": "146", "hashOfConfig": "78"}, {"size": 104542, "mtime": 1753730873531, "results": "147", "hashOfConfig": "78"}, {"size": 30091, "mtime": 1753732118317, "results": "148", "hashOfConfig": "78"}, {"size": 9960, "mtime": 1753735972583, "results": "149", "hashOfConfig": "78"}, {"size": 6463, "mtime": 1753736254532, "results": "150", "hashOfConfig": "78"}, {"size": 16384, "mtime": 1753735939665, "results": "151", "hashOfConfig": "78"}, {"size": 17240, "mtime": 1753735956350, "results": "152", "hashOfConfig": "78"}, {"size": 16677, "mtime": 1753735924469, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["382", "383"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["384"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["385", "386", "387", "388"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["389", "390", "391"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["392", "393"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["394"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["395"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["396"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["397"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["398"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["399", "400", "401"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["402"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["403"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["404", "405", "406", "407", "408"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["409", "410", "411", "412"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["413"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["414", "415", "416", "417"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", ["418", "419"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["420", "421", "422", "423", "424"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["425", "426", "427", "428", "429"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["430", "431", "432", "433", "434", "435"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["436", "437", "438"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["439"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["440", "441"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["442"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["443", "444", "445"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["446", "447", "448"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["449"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["450", "451", "452"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx", ["453", "454", "455"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts", ["456"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["457", "458", "459", "460", "461", "462"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx", ["463"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx", ["464", "465", "466", "467"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx", ["468"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx", ["469"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx", ["470"], [], {"ruleId": "471", "severity": 1, "message": "472", "line": 3, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 3, "endColumn": 22}, {"ruleId": "471", "severity": 1, "message": "475", "line": 21, "column": 8, "nodeType": "473", "messageId": "474", "endLine": 21, "endColumn": 21}, {"ruleId": "471", "severity": 1, "message": "476", "line": 2, "column": 28, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 38}, {"ruleId": "471", "severity": 1, "message": "477", "line": 6, "column": 132, "nodeType": "473", "messageId": "474", "endLine": 6, "endColumn": 137}, {"ruleId": "478", "severity": 1, "message": "479", "line": 217, "column": 6, "nodeType": "480", "endLine": 217, "endColumn": 57, "suggestions": "481"}, {"ruleId": "478", "severity": 1, "message": "482", "line": 217, "column": 7, "nodeType": "483", "endLine": 217, "endColumn": 32}, {"ruleId": "478", "severity": 1, "message": "482", "line": 217, "column": 34, "nodeType": "483", "endLine": 217, "endColumn": 56}, {"ruleId": "471", "severity": 1, "message": "484", "line": 53, "column": 19, "nodeType": "473", "messageId": "474", "endLine": 53, "endColumn": 29}, {"ruleId": "471", "severity": 1, "message": "485", "line": 83, "column": 5, "nodeType": "473", "messageId": "474", "endLine": 83, "endColumn": 10}, {"ruleId": "478", "severity": 1, "message": "486", "line": 162, "column": 6, "nodeType": "480", "endLine": 162, "endColumn": 82, "suggestions": "487"}, {"ruleId": "471", "severity": 1, "message": "488", "line": 2, "column": 57, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 73}, {"ruleId": "471", "severity": 1, "message": "489", "line": 18, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 18, "endColumn": 23}, {"ruleId": "478", "severity": 1, "message": "490", "line": 39, "column": 6, "nodeType": "480", "endLine": 39, "endColumn": 16, "suggestions": "491"}, {"ruleId": "471", "severity": 1, "message": "492", "line": 35, "column": 32, "nodeType": "473", "messageId": "474", "endLine": 35, "endColumn": 33}, {"ruleId": "471", "severity": 1, "message": "493", "line": 3, "column": 25, "nodeType": "473", "messageId": "474", "endLine": 3, "endColumn": 37}, {"ruleId": "478", "severity": 1, "message": "494", "line": 64, "column": 6, "nodeType": "480", "endLine": 64, "endColumn": 17, "suggestions": "495"}, {"ruleId": "471", "severity": 1, "message": "496", "line": 93, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 93, "endColumn": 19}, {"ruleId": "471", "severity": 1, "message": "497", "line": 7, "column": 3, "nodeType": "473", "messageId": "474", "endLine": 7, "endColumn": 15}, {"ruleId": "478", "severity": 1, "message": "498", "line": 82, "column": 6, "nodeType": "480", "endLine": 82, "endColumn": 33, "suggestions": "499"}, {"ruleId": "478", "severity": 1, "message": "500", "line": 82, "column": 7, "nodeType": "483", "endLine": 82, "endColumn": 32}, {"ruleId": "478", "severity": 1, "message": "501", "line": 57, "column": 6, "nodeType": "480", "endLine": 57, "endColumn": 16, "suggestions": "502"}, {"ruleId": "471", "severity": 1, "message": "496", "line": 93, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 93, "endColumn": 19}, {"ruleId": "471", "severity": 1, "message": "503", "line": 2, "column": 31, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 55}, {"ruleId": "471", "severity": 1, "message": "504", "line": 8, "column": 15, "nodeType": "473", "messageId": "474", "endLine": 8, "endColumn": 26}, {"ruleId": "478", "severity": 1, "message": "505", "line": 121, "column": 6, "nodeType": "480", "endLine": 121, "endColumn": 105, "suggestions": "506"}, {"ruleId": "478", "severity": 1, "message": "500", "line": 121, "column": 7, "nodeType": "483", "endLine": 121, "endColumn": 30}, {"ruleId": "478", "severity": 1, "message": "507", "line": 324, "column": 6, "nodeType": "480", "endLine": 324, "endColumn": 48, "suggestions": "508"}, {"ruleId": "471", "severity": 1, "message": "509", "line": 3, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 3, "endColumn": 21}, {"ruleId": "478", "severity": 1, "message": "494", "line": 56, "column": 6, "nodeType": "480", "endLine": 56, "endColumn": 17, "suggestions": "510"}, {"ruleId": "471", "severity": 1, "message": "511", "line": 147, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 147, "endColumn": 21}, {"ruleId": "478", "severity": 1, "message": "512", "line": 180, "column": 6, "nodeType": "480", "endLine": 180, "endColumn": 23, "suggestions": "513"}, {"ruleId": "478", "severity": 1, "message": "514", "line": 182, "column": 6, "nodeType": "480", "endLine": 182, "endColumn": 113, "suggestions": "515"}, {"ruleId": "471", "severity": 1, "message": "516", "line": 2, "column": 34, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 37}, {"ruleId": "478", "severity": 1, "message": "494", "line": 65, "column": 6, "nodeType": "480", "endLine": 65, "endColumn": 17, "suggestions": "517"}, {"ruleId": "518", "severity": 1, "message": "519", "line": 248, "column": 11, "nodeType": "520", "endLine": 262, "endColumn": 13}, {"ruleId": "518", "severity": 1, "message": "519", "line": 332, "column": 19, "nodeType": "520", "endLine": 346, "endColumn": 21}, {"ruleId": "471", "severity": 1, "message": "521", "line": 5, "column": 73, "nodeType": "473", "messageId": "474", "endLine": 5, "endColumn": 77}, {"ruleId": "471", "severity": 1, "message": "522", "line": 14, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 14, "endColumn": 17}, {"ruleId": "471", "severity": 1, "message": "523", "line": 2, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 24}, {"ruleId": "471", "severity": 1, "message": "524", "line": 12, "column": 3, "nodeType": "473", "messageId": "474", "endLine": 12, "endColumn": 17}, {"ruleId": "471", "severity": 1, "message": "525", "line": 13, "column": 3, "nodeType": "473", "messageId": "474", "endLine": 13, "endColumn": 28}, {"ruleId": "478", "severity": 1, "message": "526", "line": 140, "column": 6, "nodeType": "480", "endLine": 140, "endColumn": 57, "suggestions": "527"}, {"ruleId": "528", "severity": 1, "message": "529", "line": 426, "column": 44, "nodeType": "530", "messageId": "531", "endLine": 426, "endColumn": 98}, {"ruleId": "471", "severity": 1, "message": "532", "line": 1, "column": 22, "nodeType": "473", "messageId": "474", "endLine": 1, "endColumn": 37}, {"ruleId": "471", "severity": 1, "message": "533", "line": 1, "column": 39, "nodeType": "473", "messageId": "474", "endLine": 1, "endColumn": 56}, {"ruleId": "471", "severity": 1, "message": "534", "line": 2, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 26}, {"ruleId": "535", "severity": 1, "message": "536", "line": 581, "column": 3, "nodeType": "537", "messageId": "538", "endLine": 583, "endColumn": 4}, {"ruleId": "535", "severity": 1, "message": "536", "line": 647, "column": 3, "nodeType": "537", "messageId": "538", "endLine": 649, "endColumn": 4}, {"ruleId": "478", "severity": 1, "message": "539", "line": 128, "column": 6, "nodeType": "480", "endLine": 128, "endColumn": 18, "suggestions": "540"}, {"ruleId": "478", "severity": 1, "message": "541", "line": 173, "column": 6, "nodeType": "480", "endLine": 173, "endColumn": 33, "suggestions": "542"}, {"ruleId": "478", "severity": 1, "message": "541", "line": 204, "column": 6, "nodeType": "480", "endLine": 204, "endColumn": 21, "suggestions": "543"}, {"ruleId": "478", "severity": 1, "message": "541", "line": 239, "column": 6, "nodeType": "480", "endLine": 239, "endColumn": 33, "suggestions": "544"}, {"ruleId": "478", "severity": 1, "message": "545", "line": 299, "column": 6, "nodeType": "480", "endLine": 299, "endColumn": 22, "suggestions": "546"}, {"ruleId": "478", "severity": 1, "message": "547", "line": 306, "column": 6, "nodeType": "480", "endLine": 306, "endColumn": 18, "suggestions": "548"}, {"ruleId": "528", "severity": 1, "message": "529", "line": 35, "column": 44, "nodeType": "530", "messageId": "531", "endLine": 35, "endColumn": 98}, {"ruleId": "528", "severity": 1, "message": "549", "line": 96, "column": 46, "nodeType": "530", "messageId": "531", "endLine": 96, "endColumn": 97}, {"ruleId": "550", "severity": 1, "message": "551", "line": 234, "column": 1, "nodeType": "552", "endLine": 248, "endColumn": 3}, {"ruleId": "471", "severity": 1, "message": "553", "line": 23, "column": 11, "nodeType": "473", "messageId": "474", "endLine": 23, "endColumn": 28}, {"ruleId": "478", "severity": 1, "message": "494", "line": 100, "column": 6, "nodeType": "480", "endLine": 100, "endColumn": 17, "suggestions": "554"}, {"ruleId": "478", "severity": 1, "message": "555", "line": 375, "column": 6, "nodeType": "480", "endLine": 375, "endColumn": 8, "suggestions": "556"}, {"ruleId": "557", "severity": 1, "message": "558", "line": 259, "column": 7, "nodeType": "520", "endLine": 273, "endColumn": 8}, {"ruleId": "478", "severity": 1, "message": "494", "line": 66, "column": 6, "nodeType": "480", "endLine": 66, "endColumn": 17, "suggestions": "559"}, {"ruleId": "471", "severity": 1, "message": "560", "line": 385, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 385, "endColumn": 24}, {"ruleId": "478", "severity": 1, "message": "561", "line": 404, "column": 6, "nodeType": "480", "endLine": 404, "endColumn": 8, "suggestions": "562"}, {"ruleId": "471", "severity": 1, "message": "563", "line": 2, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 14}, {"ruleId": "471", "severity": 1, "message": "564", "line": 90, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 90, "endColumn": 33}, {"ruleId": "471", "severity": 1, "message": "565", "line": 94, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 94, "endColumn": 26}, {"ruleId": "471", "severity": 1, "message": "566", "line": 4, "column": 27, "nodeType": "473", "messageId": "474", "endLine": 4, "endColumn": 39}, {"ruleId": "471", "severity": 1, "message": "563", "line": 2, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 14}, {"ruleId": "471", "severity": 1, "message": "564", "line": 105, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 105, "endColumn": 33}, {"ruleId": "471", "severity": 1, "message": "567", "line": 110, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 110, "endColumn": 16}, {"ruleId": "471", "severity": 1, "message": "568", "line": 2, "column": 49, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 60}, {"ruleId": "471", "severity": 1, "message": "516", "line": 3, "column": 57, "nodeType": "473", "messageId": "474", "endLine": 3, "endColumn": 60}, {"ruleId": "478", "severity": 1, "message": "569", "line": 40, "column": 6, "nodeType": "480", "endLine": 40, "endColumn": 19, "suggestions": "570"}, {"ruleId": "471", "severity": 1, "message": "571", "line": 2, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 21}, {"ruleId": "471", "severity": 1, "message": "572", "line": 3, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 3, "endColumn": 29}, {"ruleId": "471", "severity": 1, "message": "573", "line": 23, "column": 3, "nodeType": "473", "messageId": "474", "endLine": 23, "endColumn": 7}, {"ruleId": "478", "severity": 1, "message": "494", "line": 101, "column": 6, "nodeType": "480", "endLine": 101, "endColumn": 17, "suggestions": "574"}, {"ruleId": "471", "severity": 1, "message": "575", "line": 473, "column": 31, "nodeType": "473", "messageId": "474", "endLine": 473, "endColumn": 45}, {"ruleId": "471", "severity": 1, "message": "576", "line": 536, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 536, "endColumn": 28}, {"ruleId": "471", "severity": 1, "message": "577", "line": 702, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 702, "endColumn": 24}, {"ruleId": "471", "severity": 1, "message": "578", "line": 4, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 4, "endColumn": 33}, {"ruleId": "471", "severity": 1, "message": "579", "line": 2, "column": 61, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 70}, {"ruleId": "471", "severity": 1, "message": "580", "line": 2, "column": 72, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 78}, {"ruleId": "471", "severity": 1, "message": "581", "line": 2, "column": 80, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 86}, {"ruleId": "471", "severity": 1, "message": "582", "line": 2, "column": 88, "nodeType": "473", "messageId": "474", "endLine": 2, "endColumn": 94}, {"ruleId": "478", "severity": 1, "message": "583", "line": 23, "column": 6, "nodeType": "480", "endLine": 23, "endColumn": 31, "suggestions": "584"}, {"ruleId": "478", "severity": 1, "message": "585", "line": 23, "column": 6, "nodeType": "480", "endLine": 23, "endColumn": 31, "suggestions": "586"}, {"ruleId": "478", "severity": 1, "message": "587", "line": 24, "column": 6, "nodeType": "480", "endLine": 24, "endColumn": 31, "suggestions": "588"}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'StudentLayout' is defined but never used.", "'ToastProps' is defined but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["589"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["590"], "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["591"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["592"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useCallback has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", ["593"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["594"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["595"], "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["596"], "'getImageUrl' is defined but never used.", ["597"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["598"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["599"], "'Eye' is defined but never used.", ["600"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'User' is defined but never used.", "'navigate' is assigned a value but never used.", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["601"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["602"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["603"], ["604"], ["605"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["606"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["607"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["608"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["609"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["610"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["611"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", "'SyncResults' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["612"], "'ApiResponse' is defined but never used.", "'announcementService' is defined but never used.", "'Edit' is defined but never used.", ["613"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'calendarReactionService' is defined but never used.", "'RotateCcw' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Filter' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEvents'. Either include it or remove the dependency array.", ["614"], "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["615"], "React Hook useEffect has a missing dependency: 'loadAnnouncements'. Either include it or remove the dependency array.", ["616"], {"desc": "617", "fix": "618"}, {"desc": "619", "fix": "620"}, {"desc": "621", "fix": "622"}, {"desc": "623", "fix": "624"}, {"desc": "617", "fix": "625"}, {"desc": "626", "fix": "627"}, {"desc": "628", "fix": "629"}, {"desc": "630", "fix": "631"}, {"desc": "623", "fix": "632"}, {"desc": "633", "fix": "634"}, {"desc": "635", "fix": "636"}, {"desc": "623", "fix": "637"}, {"desc": "638", "fix": "639"}, {"desc": "640", "fix": "641"}, {"desc": "642", "fix": "643"}, {"desc": "644", "fix": "645"}, {"desc": "642", "fix": "646"}, {"desc": "647", "fix": "648"}, {"desc": "649", "fix": "650"}, {"desc": "623", "fix": "651"}, {"desc": "652", "fix": "653"}, {"desc": "623", "fix": "654"}, {"desc": "652", "fix": "655"}, {"desc": "656", "fix": "657"}, {"desc": "623", "fix": "658"}, {"desc": "659", "fix": "660"}, {"desc": "661", "fix": "662"}, {"desc": "663", "fix": "664"}, "Update the dependencies array to be: [currentDate]", {"range": "665", "text": "666"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "667", "text": "668"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "669", "text": "670"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "671", "text": "672"}, {"range": "673", "text": "666"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "674", "text": "675"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "676", "text": "677"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "678", "text": "679"}, {"range": "680", "text": "672"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "681", "text": "682"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "683", "text": "684"}, {"range": "685", "text": "672"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "686", "text": "687"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "688", "text": "689"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "690", "text": "691"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "692", "text": "693"}, {"range": "694", "text": "691"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "695", "text": "696"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "697", "text": "698"}, {"range": "699", "text": "672"}, "Update the dependencies array to be: [images]", {"range": "700", "text": "701"}, {"range": "702", "text": "672"}, {"range": "703", "text": "701"}, "Update the dependencies array to be: [currentYear, loadData]", {"range": "704", "text": "705"}, {"range": "706", "text": "672"}, "Update the dependencies array to be: [currentPage, loadEvents, searchTerm]", {"range": "707", "text": "708"}, "Update the dependencies array to be: [currentPage, loadStudents, searchTerm]", {"range": "709", "text": "710"}, "Update the dependencies array to be: [currentPage, loadAnnouncements, searchTerm]", {"range": "711", "text": "712"}, [8001, 8052], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [900, 910], "[duration, handleClose]", [1735, 1746], "[imagePath, imageUrl]", [3099, 3126], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636], [1557, 1570], "[currentYear, loadData]", [3031, 3042], [958, 983], "[currentPage, loadEvents, searchTerm]", [938, 963], "[currentPage, loadStudents, searchTerm]", [1032, 1057], "[currentPage, loadAnnouncements, searchTerm]"]