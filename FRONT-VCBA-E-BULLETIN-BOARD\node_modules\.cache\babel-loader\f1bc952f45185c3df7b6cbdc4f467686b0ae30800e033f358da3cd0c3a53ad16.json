{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 18.5a3.5 3.5 0 1 0 7 0c0-1.57.92-2.52 2.04-3.46\",\n  key: \"1qngmn\"\n}], [\"path\", {\n  d: \"M6 8.5c0-.75.13-1.47.36-2.14\",\n  key: \"b06bma\"\n}], [\"path\", {\n  d: \"M8.8 3.15A6.5 6.5 0 0 1 19 8.5c0 1.63-.44 2.81-1.09 3.76\",\n  key: \"g10hsz\"\n}], [\"path\", {\n  d: \"M12.5 6A2.5 2.5 0 0 1 15 8.5M10 13a2 2 0 0 0 1.82-1.18\",\n  key: \"ygzou7\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst EarOff = createLucideIcon(\"ear-off\", __iconNode);\nexport { __iconNode, EarOff as default };\n//# sourceMappingURL=ear-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}