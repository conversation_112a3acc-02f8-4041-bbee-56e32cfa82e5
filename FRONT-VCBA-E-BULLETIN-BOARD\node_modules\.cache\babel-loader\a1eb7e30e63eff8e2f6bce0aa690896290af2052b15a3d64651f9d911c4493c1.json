{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"m15 9-6 6\",\n  key: \"1uzhvr\"\n}], [\"path\", {\n  d: \"M9 9h.01\",\n  key: \"1q5me6\"\n}], [\"path\", {\n  d: \"M15 15h.01\",\n  key: \"lqbp3k\"\n}]];\nconst SquarePercent = createLucideIcon(\"square-percent\", __iconNode);\nexport { __iconNode, SquarePercent as default };\n//# sourceMappingURL=square-percent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}