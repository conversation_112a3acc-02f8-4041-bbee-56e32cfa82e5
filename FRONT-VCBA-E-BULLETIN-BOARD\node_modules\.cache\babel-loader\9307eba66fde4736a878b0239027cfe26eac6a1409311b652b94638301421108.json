{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\CommentSection.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport { Heart, MessageCircle, AlertCircle, ArrowRight } from 'lucide-react';\nimport { shouldShowReplyButton, calculateIndentation, getDepthLimitMessage, getCommentDepthClasses, COMMENT_DEPTH_CONFIG } from '../../utils/commentDepth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CommentItem = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  _s();\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: `comment-${comment.comment_id}`,\n    className: depthClasses.join(' '),\n    style: {\n      marginLeft: `${indentation}px`,\n      marginBottom: '1rem',\n      padding: '1rem',\n      backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb',\n      position: 'relative'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'start',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '2.5rem',\n          height: '2.5rem',\n          borderRadius: '50%',\n          backgroundColor: '#22c55e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: '600',\n          fontSize: '0.875rem',\n          flexShrink: 0\n        },\n        children: comment.author_name ? comment.author_name.charAt(0).toUpperCase() : '?'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            },\n            children: comment.is_anonymous ? 'Anonymous' : comment.author_name || 'Unknown User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            },\n            children: formatCommentDate(comment.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#22c55e',\n              color: 'white',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '12px',\n              fontSize: '0.625rem',\n              fontWeight: '500'\n            },\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            margin: '0 0 0.75rem 0',\n            whiteSpace: 'pre-wrap'\n          },\n          children: comment.comment_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReactionToggle,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: hasUserReacted ? '#ef4444' : '#6b7280',\n              fontSize: '0.75rem',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => {\n              if (!hasUserReacted) {\n                e.currentTarget.style.color = '#374151';\n              }\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.color = hasUserReacted ? '#ef4444' : '#6b7280';\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Heart, {\n                size: 14,\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                fill: hasUserReacted ? '#ef4444' : 'none'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), comment.reaction_count || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), canReply ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReplyClick,\n            style: {\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), \"Reply\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Show depth limit message for max depth comments\n          _jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.75rem',\n              color: '#f59e0b',\n              fontStyle: 'italic'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reply depth limit reached\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                // Scroll to top-level comment for continuing thread\n                const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                if (rootElement) {\n                  rootElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center'\n                  });\n                }\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: '#3b82f6',\n                fontSize: '0.75rem',\n                textDecoration: 'underline',\n                padding: '0'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [\"Continue thread\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), showDepthWarning && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.5rem',\n            backgroundColor: '#fef3c7',\n            border: '1px solid #f59e0b',\n            borderRadius: '4px',\n            fontSize: '0.75rem',\n            color: '#92400e',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: depthLimitMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), showReplyForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.75rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(CommentForm, {\n            announcementId: comment.announcement_id,\n            parentCommentId: comment.comment_id,\n            onSubmit: () => setShowReplyForm(false),\n            onCancel: () => setShowReplyForm(false),\n            placeholder: \"Write a reply...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), comment.replies && comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem'\n          },\n          children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(CommentItem, {\n            comment: reply,\n            onReply: onReply,\n            onLike: onLike,\n            onUnlike: onUnlike,\n            currentUserId: currentUserId,\n            currentUserType: currentUserType,\n            depth: depth + 1\n          }, reply.comment_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(CommentItem, \"WiAqH9Fgopt6/hc1TKGdzirvGys=\");\n_c = CommentItem;\nconst CommentForm = ({\n  announcementId,\n  calendarId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  _s2();\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    createComment\n  } = useComments(announcementId, calendarId, 'student'); // Student service\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!commentText.trim()) return;\n    try {\n      setIsSubmitting(true);\n      const commentData = {\n        announcement_id: announcementId,\n        calendar_id: calendarId,\n        comment_text: commentText.trim(),\n        is_anonymous: isAnonymous\n      };\n      if (parentCommentId) {\n        commentData.parent_comment_id = parentCommentId;\n      }\n      await createComment(commentData);\n      setCommentText('');\n      setIsAnonymous(false);\n      onSubmit();\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      padding: '1rem',\n      backgroundColor: '#f9fafb',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n      value: commentText,\n      onChange: e => setCommentText(e.target.value),\n      placeholder: placeholder,\n      rows: 3,\n      style: {\n        width: '100%',\n        padding: '0.75rem',\n        border: '1px solid #d1d5db',\n        borderRadius: '6px',\n        fontSize: '0.875rem',\n        outline: 'none',\n        resize: 'vertical',\n        marginBottom: '0.75rem'\n      },\n      onFocus: e => {\n        e.currentTarget.style.borderColor = '#22c55e';\n        e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n      },\n      onBlur: e => {\n        e.currentTarget.style.borderColor = '#d1d5db';\n        e.currentTarget.style.boxShadow = 'none';\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: '0.75rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: isAnonymous,\n          onChange: e => setIsAnonymous(e.target.checked),\n          style: {\n            marginRight: '0.5rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), \"Post anonymously\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '0.5rem'\n        },\n        children: [onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#f3f4f6',\n            color: '#374151',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            fontSize: '0.75rem',\n            fontWeight: '500'\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !commentText.trim() || isSubmitting,\n          style: {\n            padding: '0.5rem 1rem',\n            background: !commentText.trim() || isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n            fontSize: '0.75rem',\n            fontWeight: '600'\n          },\n          children: isSubmitting ? 'Posting...' : 'Post'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 355,\n    columnNumber: 5\n  }, this);\n};\n_s2(CommentForm, \"BbE2Nc5wJlPq9At3ZSo/OwA4M08=\", false, function () {\n  return [useComments];\n});\n_c2 = CommentForm;\nconst CommentSection = ({\n  announcementId,\n  calendarId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'student'\n}) => {\n  _s3();\n  // Ensure either announcementId or calendarId is provided, but not both\n  if (!announcementId && !calendarId) {\n    throw new Error('Either announcementId or calendarId must be provided');\n  }\n  if (announcementId && calendarId) {\n    throw new Error('Cannot provide both announcementId and calendarId');\n  }\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment\n  } = useComments(announcementId, calendarId, 'student'); // Pass both IDs to hook\n\n  const handleReply = parentId => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n  if (!allowComments) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic'\n      },\n      children: \"Comments are disabled for this announcement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '600',\n        color: '#374151',\n        marginBottom: '1rem'\n      },\n      children: [\"Comments (\", comments.length, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '0.75rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '6px',\n        marginBottom: '1rem',\n        fontSize: '0.875rem'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(CommentForm, {\n        announcementId: announcementId,\n        onSubmit: refresh,\n        placeholder: \"Share your thoughts...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '1.5rem',\n          height: '1.5rem',\n          border: '2px solid #e5e7eb',\n          borderTop: '2px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 9\n    }, this) : comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      },\n      children: \"No comments yet. Be the first to share your thoughts!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: comments.map(comment => /*#__PURE__*/_jsxDEV(CommentItem, {\n        comment: comment,\n        onReply: handleReply,\n        onLike: likeComment,\n        onUnlike: unlikeComment,\n        currentUserId: currentUserId,\n        currentUserType: currentUserType\n      }, comment.comment_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 492,\n    columnNumber: 5\n  }, this);\n};\n_s3(CommentSection, \"u+7JKYie/psx+0/aFIFQzzd4MZM=\", false, function () {\n  return [useComments];\n});\n_c3 = CommentSection;\nexport default CommentSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CommentItem\");\n$RefreshReg$(_c2, \"CommentForm\");\n$RefreshReg$(_c3, \"CommentSection\");", "map": {"version": 3, "names": ["React", "useState", "useComments", "formatCommentDate", "Heart", "MessageCircle", "AlertCircle", "ArrowRight", "shouldShowReplyButton", "calculateIndentation", "getDepthLimitMessage", "getCommentDepthClasses", "COMMENT_DEPTH_CONFIG", "jsxDEV", "_jsxDEV", "CommentItem", "comment", "onReply", "onLike", "onUnlike", "currentUserId", "currentUserType", "depth", "_s", "showReplyForm", "setShowReplyForm", "showDepthWarning", "setShowDepthWarning", "hasUserReacted", "user_reaction", "undefined", "canReply", "indentation", "depthClasses", "depthLimitMessage", "isAtMaxDepth", "MAX_DEPTH", "handleReactionToggle", "comment_id", "handleReplyClick", "setTimeout", "id", "className", "join", "style", "marginLeft", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "position", "children", "display", "alignItems", "gap", "width", "height", "justifyContent", "color", "fontWeight", "fontSize", "flexShrink", "author_name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "min<PERSON><PERSON><PERSON>", "is_anonymous", "created_at", "user_type", "lineHeight", "margin", "whiteSpace", "comment_text", "onClick", "background", "cursor", "transition", "onMouseOver", "e", "currentTarget", "onMouseOut", "size", "fill", "reaction_count", "fontStyle", "rootElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "textDecoration", "marginTop", "CommentForm", "announcementId", "announcement_id", "parentCommentId", "onSubmit", "onCancel", "placeholder", "replies", "length", "map", "reply", "_c", "calendarId", "_s2", "commentText", "setCommentText", "isAnonymous", "setIsAnonymous", "isSubmitting", "setIsSubmitting", "createComment", "handleSubmit", "preventDefault", "trim", "commentData", "calendar_id", "parent_comment_id", "error", "console", "value", "onChange", "target", "rows", "outline", "resize", "onFocus", "borderColor", "boxShadow", "onBlur", "type", "checked", "marginRight", "disabled", "_c2", "CommentSection", "allowComments", "_s3", "Error", "comments", "loading", "refresh", "likeComment", "unlikeComment", "handleReply", "parentId", "log", "textAlign", "borderTop", "animation", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/CommentSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport type { Comment, CreateCommentData } from '../../services/commentService';\nimport { Heart, MessageCircle, AlertCircle, ArrowRight } from 'lucide-react';\nimport {\n  shouldShowReplyButton,\n  calculateIndentation,\n  getDepthLimitMessage,\n  getCommentDepthClasses,\n  COMMENT_DEPTH_CONFIG\n} from '../../utils/commentDepth';\n\ninterface CommentSectionProps {\n  announcementId?: number;\n  calendarId?: number;\n  allowComments?: boolean;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n}\n\ninterface CommentItemProps {\n  comment: Comment;\n  onReply: (parentId: number) => void;\n  onLike: (id: number) => void;\n  onUnlike: (id: number) => void;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n  depth?: number;\n}\n\nconst CommentItem: React.FC<CommentItemProps> = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n\n  return (\n    <div\n      id={`comment-${comment.comment_id}`}\n      className={depthClasses.join(' ')}\n      style={{\n        marginLeft: `${indentation}px`,\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb',\n        position: 'relative'\n      }}>\n      <div style={{ display: 'flex', alignItems: 'start', gap: '0.75rem' }}>\n        {/* Avatar */}\n        <div style={{\n          width: '2.5rem',\n          height: '2.5rem',\n          borderRadius: '50%',\n          backgroundColor: '#22c55e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: '600',\n          fontSize: '0.875rem',\n          flexShrink: 0\n        }}>\n          {comment.author_name ? comment.author_name.charAt(0).toUpperCase() : '?'}\n        </div>\n\n        <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Header */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem'\n          }}>\n            <span style={{\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            }}>\n              {comment.is_anonymous ? 'Anonymous' : (comment.author_name || 'Unknown User')}\n            </span>\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            }}>\n              {formatCommentDate(comment.created_at)}\n            </span>\n            {comment.user_type === 'admin' && (\n              <span style={{\n                backgroundColor: '#22c55e',\n                color: 'white',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '12px',\n                fontSize: '0.625rem',\n                fontWeight: '500'\n              }}>\n                Admin\n              </span>\n            )}\n          </div>\n\n          {/* Content */}\n          <p style={{\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            margin: '0 0 0.75rem 0',\n            whiteSpace: 'pre-wrap'\n          }}>\n            {comment.comment_text}\n          </p>\n\n          {/* Actions */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          }}>\n            <button\n              onClick={handleReactionToggle}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                fontSize: '0.75rem',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                transition: 'color 0.2s ease'\n              }}\n              onMouseOver={(e) => {\n                if (!hasUserReacted) {\n                  e.currentTarget.style.color = '#374151';\n                }\n              }}\n              onMouseOut={(e) => {\n                e.currentTarget.style.color = hasUserReacted ? '#ef4444' : '#6b7280';\n              }}\n            >\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <Heart\n                  size={14}\n                  color={hasUserReacted ? '#ef4444' : '#6b7280'}\n                  fill={hasUserReacted ? '#ef4444' : 'none'}\n                />\n                {comment.reaction_count || 0}\n              </span>\n            </button>\n\n            {/* Reply Button with Depth Limiting */}\n            {canReply ? (\n              <button\n                onClick={handleReplyClick}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: '#6b7280',\n                  fontSize: '0.75rem',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\n                onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\n              >\n                <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                  <MessageCircle size={12} />\n                  Reply\n                </span>\n              </button>\n            ) : (\n              // Show depth limit message for max depth comments\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.75rem',\n                color: '#f59e0b',\n                fontStyle: 'italic'\n              }}>\n                <AlertCircle size={12} />\n                <span>Reply depth limit reached</span>\n                <button\n                  onClick={() => {\n                    // Scroll to top-level comment for continuing thread\n                    const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                    if (rootElement) {\n                      rootElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                    }\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#3b82f6',\n                    fontSize: '0.75rem',\n                    textDecoration: 'underline',\n                    padding: '0'\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                    Continue thread\n                    <ArrowRight size={10} />\n                  </span>\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Depth Warning Message */}\n          {showDepthWarning && (\n            <div style={{\n              marginTop: '0.5rem',\n              padding: '0.5rem',\n              backgroundColor: '#fef3c7',\n              border: '1px solid #f59e0b',\n              borderRadius: '4px',\n              fontSize: '0.75rem',\n              color: '#92400e',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <AlertCircle size={14} />\n              <span>{depthLimitMessage}</span>\n            </div>\n          )}\n\n          {/* Reply Form */}\n          {showReplyForm && (\n            <div style={{ marginTop: '0.75rem' }}>\n              <CommentForm\n                announcementId={comment.announcement_id}\n                parentCommentId={comment.comment_id}\n                onSubmit={() => setShowReplyForm(false)}\n                onCancel={() => setShowReplyForm(false)}\n                placeholder=\"Write a reply...\"\n              />\n            </div>\n          )}\n\n          {/* Replies */}\n          {comment.replies && comment.replies.length > 0 && (\n            <div style={{ marginTop: '1rem' }}>\n              {comment.replies.map((reply) => (\n                <CommentItem\n                  key={reply.comment_id}\n                  comment={reply}\n                  onReply={onReply}\n                  onLike={onLike}\n                  onUnlike={onUnlike}\n                  currentUserId={currentUserId}\n                  currentUserType={currentUserType}\n                  depth={depth + 1}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface CommentFormProps {\n  announcementId?: number;\n  calendarId?: number;\n  parentCommentId?: number;\n  onSubmit: () => void;\n  onCancel?: () => void;\n  placeholder?: string;\n}\n\nconst CommentForm: React.FC<CommentFormProps> = ({\n  announcementId,\n  calendarId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { createComment } = useComments(announcementId, calendarId, 'student'); // Student service\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!commentText.trim()) return;\n\n    try {\n      setIsSubmitting(true);\n      \n      const commentData: CreateCommentData = {\n        announcement_id: announcementId,\n        calendar_id: calendarId,\n        comment_text: commentText.trim(),\n        is_anonymous: isAnonymous\n      };\n\n      if (parentCommentId) {\n        commentData.parent_comment_id = parentCommentId;\n      }\n\n      await createComment(commentData);\n      setCommentText('');\n      setIsAnonymous(false);\n      onSubmit();\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} style={{\n      padding: '1rem',\n      backgroundColor: '#f9fafb',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb'\n    }}>\n      <textarea\n        value={commentText}\n        onChange={(e) => setCommentText(e.target.value)}\n        placeholder={placeholder}\n        rows={3}\n        style={{\n          width: '100%',\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          outline: 'none',\n          resize: 'vertical',\n          marginBottom: '0.75rem'\n        }}\n        onFocus={(e) => {\n          e.currentTarget.style.borderColor = '#22c55e';\n          e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n        }}\n        onBlur={(e) => {\n          e.currentTarget.style.borderColor = '#d1d5db';\n          e.currentTarget.style.boxShadow = 'none';\n        }}\n      />\n      \n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      }}>\n        <label style={{\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: '0.75rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        }}>\n          <input\n            type=\"checkbox\"\n            checked={isAnonymous}\n            onChange={(e) => setIsAnonymous(e.target.checked)}\n            style={{ marginRight: '0.5rem' }}\n          />\n          Post anonymously\n        </label>\n\n        <div style={{ display: 'flex', gap: '0.5rem' }}>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              style={{\n                padding: '0.5rem 1rem',\n                backgroundColor: '#f3f4f6',\n                color: '#374151',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '0.75rem',\n                fontWeight: '500'\n              }}\n            >\n              Cancel\n            </button>\n          )}\n          <button\n            type=\"submit\"\n            disabled={!commentText.trim() || isSubmitting}\n            style={{\n              padding: '0.5rem 1rem',\n              background: (!commentText.trim() || isSubmitting) ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: (!commentText.trim() || isSubmitting) ? 'not-allowed' : 'pointer',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            }}\n          >\n            {isSubmitting ? 'Posting...' : 'Post'}\n          </button>\n        </div>\n      </div>\n    </form>\n  );\n};\n\nconst CommentSection: React.FC<CommentSectionProps> = ({\n  announcementId,\n  calendarId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'student'\n}) => {\n  // Ensure either announcementId or calendarId is provided, but not both\n  if (!announcementId && !calendarId) {\n    throw new Error('Either announcementId or calendarId must be provided');\n  }\n  if (announcementId && calendarId) {\n    throw new Error('Cannot provide both announcementId and calendarId');\n  }\n\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment\n  } = useComments(announcementId, calendarId, 'student'); // Pass both IDs to hook\n\n  const handleReply = (parentId: number) => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n\n  if (!allowComments) {\n    return (\n      <div style={{\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic'\n      }}>\n        Comments are disabled for this announcement.\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ marginTop: '1.5rem' }}>\n      <h3 style={{\n        fontSize: '1.125rem',\n        fontWeight: '600',\n        color: '#374151',\n        marginBottom: '1rem'\n      }}>\n        Comments ({comments.length})\n      </h3>\n\n      {error && (\n        <div style={{\n          padding: '0.75rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          color: '#dc2626',\n          borderRadius: '6px',\n          marginBottom: '1rem',\n          fontSize: '0.875rem'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* Comment Form */}\n      <div style={{ marginBottom: '1.5rem' }}>\n        <CommentForm\n          announcementId={announcementId}\n          onSubmit={refresh}\n          placeholder=\"Share your thoughts...\"\n        />\n      </div>\n\n      {/* Comments List */}\n      {loading ? (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '2rem'\n        }}>\n          <div style={{\n            width: '1.5rem',\n            height: '1.5rem',\n            border: '2px solid #e5e7eb',\n            borderTop: '2px solid #22c55e',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></div>\n        </div>\n      ) : comments.length === 0 ? (\n        <div style={{\n          textAlign: 'center',\n          padding: '2rem',\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        }}>\n          No comments yet. Be the first to share your thoughts!\n        </div>\n      ) : (\n        <div>\n          {comments.map((comment) => (\n            <CommentItem\n              key={comment.comment_id}\n              comment={comment}\n              onReply={handleReply}\n              onLike={likeComment}\n              onUnlike={unlikeComment}\n              currentUserId={currentUserId}\n              currentUserType={currentUserType}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CommentSection;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,yBAAyB;AAExE,SAASC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAC5E,SACEC,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,QACf,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBlC,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACbC,eAAe;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM2B,cAAc,GAAGZ,OAAO,CAACa,aAAa,KAAKC,SAAS,IAAId,OAAO,CAACa,aAAa,KAAK,IAAI;;EAE5F;EACA,MAAME,QAAQ,GAAGvB,qBAAqB,CAACc,KAAK,CAAC;EAC7C,MAAMU,WAAW,GAAGvB,oBAAoB,CAACa,KAAK,CAAC;EAC/C,MAAMW,YAAY,GAAGtB,sBAAsB,CAACW,KAAK,CAAC;EAClD,MAAMY,iBAAiB,GAAGxB,oBAAoB,CAACY,KAAK,CAAC;EACrD,MAAMa,YAAY,GAAGb,KAAK,IAAIV,oBAAoB,CAACwB,SAAS;EAE5D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIT,cAAc,EAAE;MAClBT,QAAQ,CAACH,OAAO,CAACsB,UAAU,CAAC;IAC9B,CAAC,MAAM;MACLpB,MAAM,CAACF,OAAO,CAACsB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIJ,YAAY,EAAE;MAChBR,mBAAmB,CAAC,IAAI,CAAC;MACzBa,UAAU,CAAC,MAAMb,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM;MACLF,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,oBACEX,OAAA;IACE2B,EAAE,EAAE,WAAWzB,OAAO,CAACsB,UAAU,EAAG;IACpCI,SAAS,EAAET,YAAY,CAACU,IAAI,CAAC,GAAG,CAAE;IAClCC,KAAK,EAAE;MACLC,UAAU,EAAE,GAAGb,WAAW,IAAI;MAC9Bc,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE1B,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;MAChD2B,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACFtC,OAAA;MAAK8B,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAE;MAAAH,QAAA,gBAEnEtC,OAAA;QAAK8B,KAAK,EAAE;UACVY,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBR,YAAY,EAAE,KAAK;UACnBD,eAAe,EAAE,SAAS;UAC1BK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBI,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EACCpC,OAAO,CAAC+C,WAAW,GAAG/C,OAAO,CAAC+C,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAENvD,OAAA;QAAK8B,KAAK,EAAE;UAAE0B,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAAnB,QAAA,gBAEnCtC,OAAA;UAAK8B,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbT,YAAY,EAAE;UAChB,CAAE;UAAAM,QAAA,gBACAtC,OAAA;YAAM8B,KAAK,EAAE;cACXgB,UAAU,EAAE,KAAK;cACjBD,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,EACCpC,OAAO,CAACwD,YAAY,GAAG,WAAW,GAAIxD,OAAO,CAAC+C,WAAW,IAAI;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACPvD,OAAA;YAAM8B,KAAK,EAAE;cACXe,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,EACCjD,iBAAiB,CAACa,OAAO,CAACyD,UAAU;UAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACNrD,OAAO,CAAC0D,SAAS,KAAK,OAAO,iBAC5B5D,OAAA;YAAM8B,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1BW,KAAK,EAAE,OAAO;cACdZ,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,MAAM;cACpBY,QAAQ,EAAE,UAAU;cACpBD,UAAU,EAAE;YACd,CAAE;YAAAR,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNvD,OAAA;UAAG8B,KAAK,EAAE;YACRe,KAAK,EAAE,SAAS;YAChBE,QAAQ,EAAE,UAAU;YACpBc,UAAU,EAAE,KAAK;YACjBC,MAAM,EAAE,eAAe;YACvBC,UAAU,EAAE;UACd,CAAE;UAAAzB,QAAA,EACCpC,OAAO,CAAC8D;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAGJvD,OAAA;UAAK8B,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,MAAM;YACXM,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,gBACAtC,OAAA;YACEiE,OAAO,EAAE1C,oBAAqB;YAC9BO,KAAK,EAAE;cACLS,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACdyB,UAAU,EAAE,MAAM;cAClB9B,MAAM,EAAE,MAAM;cACd+B,MAAM,EAAE,SAAS;cACjBtB,KAAK,EAAE/B,cAAc,GAAG,SAAS,GAAG,SAAS;cAC7CiC,QAAQ,EAAE,SAAS;cACnBd,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,KAAK;cACnBiC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAK;cAClB,IAAI,CAACxD,cAAc,EAAE;gBACnBwD,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACe,KAAK,GAAG,SAAS;cACzC;YACF,CAAE;YACF2B,UAAU,EAAGF,CAAC,IAAK;cACjBA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACe,KAAK,GAAG/B,cAAc,GAAG,SAAS,GAAG,SAAS;YACtE,CAAE;YAAAwB,QAAA,eAEFtC,OAAA;cAAM8B,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAU,CAAE;cAAAH,QAAA,gBACrEtC,OAAA,CAACV,KAAK;gBACJmF,IAAI,EAAE,EAAG;gBACT5B,KAAK,EAAE/B,cAAc,GAAG,SAAS,GAAG,SAAU;gBAC9C4D,IAAI,EAAE5D,cAAc,GAAG,SAAS,GAAG;cAAO;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACDrD,OAAO,CAACyE,cAAc,IAAI,CAAC;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGRtC,QAAQ,gBACPjB,OAAA;YACEiE,OAAO,EAAExC,gBAAiB;YAC1BK,KAAK,EAAE;cACLoC,UAAU,EAAE,MAAM;cAClB9B,MAAM,EAAE,MAAM;cACd+B,MAAM,EAAE,SAAS;cACjBtB,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE,SAAS;cACnBd,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,KAAK;cACnBiC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACe,KAAK,GAAG,SAAU;YAC5D2B,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACe,KAAK,GAAG,SAAU;YAAAP,QAAA,eAE3DtC,OAAA;cAAM8B,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAU,CAAE;cAAAH,QAAA,gBACrEtC,OAAA,CAACT,aAAa;gBAACkF,IAAI,EAAE;cAAG;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;UAAA;UAET;UACAvD,OAAA;YAAK8B,KAAK,EAAE;cACVS,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,QAAQ;cACbM,QAAQ,EAAE,SAAS;cACnBF,KAAK,EAAE,SAAS;cAChB+B,SAAS,EAAE;YACb,CAAE;YAAAtC,QAAA,gBACAtC,OAAA,CAACR,WAAW;cAACiF,IAAI,EAAE;YAAG;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBvD,OAAA;cAAAsC,QAAA,EAAM;YAAyB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCvD,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMY,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAW7E,OAAO,CAACsB,UAAU,EAAE,CAAC;gBAC5E,IAAIqD,WAAW,EAAE;kBACfA,WAAW,CAACG,cAAc,CAAC;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC,CAAC;gBACrE;cACF,CAAE;cACFpD,KAAK,EAAE;gBACLoC,UAAU,EAAE,MAAM;gBAClB9B,MAAM,EAAE,MAAM;gBACd+B,MAAM,EAAE,SAAS;gBACjBtB,KAAK,EAAE,SAAS;gBAChBE,QAAQ,EAAE,SAAS;gBACnBoC,cAAc,EAAE,WAAW;gBAC3BlD,OAAO,EAAE;cACX,CAAE;cAAAK,QAAA,eAEFtC,OAAA;gBAAM8B,KAAK,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAU,CAAE;gBAAAH,QAAA,GAAC,iBAEtE,eAAAtC,OAAA,CAACP,UAAU;kBAACgF,IAAI,EAAE;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL3C,gBAAgB,iBACfZ,OAAA;UAAK8B,KAAK,EAAE;YACVsD,SAAS,EAAE,QAAQ;YACnBnD,OAAO,EAAE,QAAQ;YACjBC,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBY,QAAQ,EAAE,SAAS;YACnBF,KAAK,EAAE,SAAS;YAChBN,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAH,QAAA,gBACAtC,OAAA,CAACR,WAAW;YAACiF,IAAI,EAAE;UAAG;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzBvD,OAAA;YAAAsC,QAAA,EAAOlB;UAAiB;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN,EAGA7C,aAAa,iBACZV,OAAA;UAAK8B,KAAK,EAAE;YAAEsD,SAAS,EAAE;UAAU,CAAE;UAAA9C,QAAA,eACnCtC,OAAA,CAACqF,WAAW;YACVC,cAAc,EAAEpF,OAAO,CAACqF,eAAgB;YACxCC,eAAe,EAAEtF,OAAO,CAACsB,UAAW;YACpCiE,QAAQ,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC,KAAK,CAAE;YACxC+E,QAAQ,EAAEA,CAAA,KAAM/E,gBAAgB,CAAC,KAAK,CAAE;YACxCgF,WAAW,EAAC;UAAkB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGArD,OAAO,CAAC0F,OAAO,IAAI1F,OAAO,CAAC0F,OAAO,CAACC,MAAM,GAAG,CAAC,iBAC5C7F,OAAA;UAAK8B,KAAK,EAAE;YAAEsD,SAAS,EAAE;UAAO,CAAE;UAAA9C,QAAA,EAC/BpC,OAAO,CAAC0F,OAAO,CAACE,GAAG,CAAEC,KAAK,iBACzB/F,OAAA,CAACC,WAAW;YAEVC,OAAO,EAAE6F,KAAM;YACf5F,OAAO,EAAEA,OAAQ;YACjBC,MAAM,EAAEA,MAAO;YACfC,QAAQ,EAAEA,QAAS;YACnBC,aAAa,EAAEA,aAAc;YAC7BC,eAAe,EAAEA,eAAgB;YACjCC,KAAK,EAAEA,KAAK,GAAG;UAAE,GAPZuF,KAAK,CAACvE,UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQtB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA7QIR,WAAuC;AAAA+F,EAAA,GAAvC/F,WAAuC;AAwR7C,MAAMoF,WAAuC,GAAGA,CAAC;EAC/CC,cAAc;EACdW,UAAU;EACVT,eAAe;EACfC,QAAQ;EACRC,QAAQ;EACRC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAO,GAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkH,WAAW,EAAEC,cAAc,CAAC,GAAGnH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoH,YAAY,EAAEC,eAAe,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEsH;EAAc,CAAC,GAAGrH,WAAW,CAACkG,cAAc,EAAEW,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;;EAE9E,MAAMS,YAAY,GAAG,MAAOpC,CAAkB,IAAK;IACjDA,CAAC,CAACqC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFJ,eAAe,CAAC,IAAI,CAAC;MAErB,MAAMK,WAA8B,GAAG;QACrCtB,eAAe,EAAED,cAAc;QAC/BwB,WAAW,EAAEb,UAAU;QACvBjC,YAAY,EAAEmC,WAAW,CAACS,IAAI,CAAC,CAAC;QAChClD,YAAY,EAAE2C;MAChB,CAAC;MAED,IAAIb,eAAe,EAAE;QACnBqB,WAAW,CAACE,iBAAiB,GAAGvB,eAAe;MACjD;MAEA,MAAMiB,aAAa,CAACI,WAAW,CAAC;MAChCT,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,KAAK,CAAC;MACrBb,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRR,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACExG,OAAA;IAAMyF,QAAQ,EAAEiB,YAAa;IAAC5E,KAAK,EAAE;MACnCG,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE;IACV,CAAE;IAAAE,QAAA,gBACAtC,OAAA;MACEkH,KAAK,EAAEf,WAAY;MACnBgB,QAAQ,EAAG7C,CAAC,IAAK8B,cAAc,CAAC9B,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;MAChDvB,WAAW,EAAEA,WAAY;MACzB0B,IAAI,EAAE,CAAE;MACRvF,KAAK,EAAE;QACLY,KAAK,EAAE,MAAM;QACbT,OAAO,EAAE,SAAS;QAClBG,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,KAAK;QACnBY,QAAQ,EAAE,UAAU;QACpBuE,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,UAAU;QAClBvF,YAAY,EAAE;MAChB,CAAE;MACFwF,OAAO,EAAGlD,CAAC,IAAK;QACdA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC2F,WAAW,GAAG,SAAS;QAC7CnD,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC4F,SAAS,GAAG,kCAAkC;MACtE,CAAE;MACFC,MAAM,EAAGrD,CAAC,IAAK;QACbA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC2F,WAAW,GAAG,SAAS;QAC7CnD,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC4F,SAAS,GAAG,MAAM;MAC1C;IAAE;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEFvD,OAAA;MAAK8B,KAAK,EAAE;QACVS,OAAO,EAAE,MAAM;QACfK,cAAc,EAAE,eAAe;QAC/BJ,UAAU,EAAE;MACd,CAAE;MAAAF,QAAA,gBACAtC,OAAA;QAAO8B,KAAK,EAAE;UACZS,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBO,QAAQ,EAAE,SAAS;UACnBF,KAAK,EAAE,SAAS;UAChBsB,MAAM,EAAE;QACV,CAAE;QAAA7B,QAAA,gBACAtC,OAAA;UACE4H,IAAI,EAAC,UAAU;UACfC,OAAO,EAAExB,WAAY;UACrBc,QAAQ,EAAG7C,CAAC,IAAKgC,cAAc,CAAChC,CAAC,CAAC8C,MAAM,CAACS,OAAO,CAAE;UAClD/F,KAAK,EAAE;YAAEgG,WAAW,EAAE;UAAS;QAAE;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,oBAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERvD,OAAA;QAAK8B,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,GAC5CoD,QAAQ,iBACP1F,OAAA;UACE4H,IAAI,EAAC,QAAQ;UACb3D,OAAO,EAAEyB,QAAS;UAClB5D,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBC,eAAe,EAAE,SAAS;YAC1BW,KAAK,EAAE,SAAS;YAChBT,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBgC,MAAM,EAAE,SAAS;YACjBpB,QAAQ,EAAE,SAAS;YACnBD,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDvD,OAAA;UACE4H,IAAI,EAAC,QAAQ;UACbG,QAAQ,EAAE,CAAC5B,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAa;UAC9CzE,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBiC,UAAU,EAAG,CAACiC,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAY,GAAI,SAAS,GAAG,mDAAmD;YACnH1D,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBgC,MAAM,EAAG,CAACgC,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAY,GAAI,aAAa,GAAG,SAAS;YACzExD,QAAQ,EAAE,SAAS;YACnBD,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,EAEDiE,YAAY,GAAG,YAAY,GAAG;QAAM;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC2C,GAAA,CAvIIb,WAAuC;EAAA,QAWjBjG,WAAW;AAAA;AAAA4I,GAAA,GAXjC3C,WAAuC;AAyI7C,MAAM4C,cAA6C,GAAGA,CAAC;EACrD3C,cAAc;EACdW,UAAU;EACViC,aAAa,GAAG,IAAI;EACpB5H,aAAa;EACbC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAA4H,GAAA;EACJ;EACA,IAAI,CAAC7C,cAAc,IAAI,CAACW,UAAU,EAAE;IAClC,MAAM,IAAImC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,IAAI9C,cAAc,IAAIW,UAAU,EAAE;IAChC,MAAM,IAAImC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPtB,KAAK;IACLuB,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGrJ,WAAW,CAACkG,cAAc,EAAEW,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;;EAExD,MAAMyC,WAAW,GAAIC,QAAgB,IAAK;IACxC;IACA1B,OAAO,CAAC2B,GAAG,CAAC,mBAAmB,EAAED,QAAQ,CAAC;EAC5C,CAAC;EAED,IAAI,CAACT,aAAa,EAAE;IAClB,oBACElI,OAAA;MAAK8B,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACf4G,SAAS,EAAE,QAAQ;QACnBhG,KAAK,EAAE,SAAS;QAChBE,QAAQ,EAAE,UAAU;QACpB6B,SAAS,EAAE;MACb,CAAE;MAAAtC,QAAA,EAAC;IAEH;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAK8B,KAAK,EAAE;MAAEsD,SAAS,EAAE;IAAS,CAAE;IAAA9C,QAAA,gBAClCtC,OAAA;MAAI8B,KAAK,EAAE;QACTiB,QAAQ,EAAE,UAAU;QACpBD,UAAU,EAAE,KAAK;QACjBD,KAAK,EAAE,SAAS;QAChBb,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,GAAC,YACS,EAAC+F,QAAQ,CAACxC,MAAM,EAAC,GAC7B;IAAA;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEJyD,KAAK,iBACJhH,OAAA;MAAK8B,KAAK,EAAE;QACVG,OAAO,EAAE,SAAS;QAClBC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BS,KAAK,EAAE,SAAS;QAChBV,YAAY,EAAE,KAAK;QACnBH,YAAY,EAAE,MAAM;QACpBe,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,EACC0E;IAAK;MAAA5D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDvD,OAAA;MAAK8B,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAS,CAAE;MAAAM,QAAA,eACrCtC,OAAA,CAACqF,WAAW;QACVC,cAAc,EAAEA,cAAe;QAC/BG,QAAQ,EAAE8C,OAAQ;QAClB5C,WAAW,EAAC;MAAwB;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL+E,OAAO,gBACNtI,OAAA;MAAK8B,KAAK,EAAE;QACVS,OAAO,EAAE,MAAM;QACfK,cAAc,EAAE,QAAQ;QACxBX,OAAO,EAAE;MACX,CAAE;MAAAK,QAAA,eACAtC,OAAA;QAAK8B,KAAK,EAAE;UACVY,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBP,MAAM,EAAE,mBAAmB;UAC3B0G,SAAS,EAAE,mBAAmB;UAC9B3G,YAAY,EAAE,KAAK;UACnB4G,SAAS,EAAE;QACb;MAAE;QAAA3F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ8E,QAAQ,CAACxC,MAAM,KAAK,CAAC,gBACvB7F,OAAA;MAAK8B,KAAK,EAAE;QACV+G,SAAS,EAAE,QAAQ;QACnB5G,OAAO,EAAE,MAAM;QACfY,KAAK,EAAE,SAAS;QAChBE,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAENvD,OAAA;MAAAsC,QAAA,EACG+F,QAAQ,CAACvC,GAAG,CAAE5F,OAAO,iBACpBF,OAAA,CAACC,WAAW;QAEVC,OAAO,EAAEA,OAAQ;QACjBC,OAAO,EAAEuI,WAAY;QACrBtI,MAAM,EAAEoI,WAAY;QACpBnI,QAAQ,EAAEoI,aAAc;QACxBnI,aAAa,EAAEA,aAAc;QAC7BC,eAAe,EAAEA;MAAgB,GAN5BL,OAAO,CAACsB,UAAU;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOxB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4E,GAAA,CAvHIF,cAA6C;EAAA,QAsB7C7I,WAAW;AAAA;AAAA4J,GAAA,GAtBXf,cAA6C;AAyHnD,eAAeA,cAAc;AAAC,IAAAjC,EAAA,EAAAgC,GAAA,EAAAgB,GAAA;AAAAC,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}