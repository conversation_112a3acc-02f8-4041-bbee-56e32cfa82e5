{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.7 21a2 2 0 0 1-3.5-2l8.6-14a6 6 0 0 1 10.4 6 2 2 0 1 1-3.464-2 2 2 0 1 0-3.464-2Z\",\n  key: \"isaq8g\"\n}], [\"path\", {\n  d: \"M17.75 7 15 2.1\",\n  key: \"12x7e8\"\n}], [\"path\", {\n  d: \"M10.9 4.8 13 9\",\n  key: \"100a87\"\n}], [\"path\", {\n  d: \"m7.9 9.7 2 4.4\",\n  key: \"ntfhaj\"\n}], [\"path\", {\n  d: \"M4.9 14.7 7 18.9\",\n  key: \"1x43jy\"\n}]];\nconst CandyCane = createLucideIcon(\"candy-cane\", __iconNode);\nexport { __iconNode, CandyCane as default };\n//# sourceMappingURL=candy-cane.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}