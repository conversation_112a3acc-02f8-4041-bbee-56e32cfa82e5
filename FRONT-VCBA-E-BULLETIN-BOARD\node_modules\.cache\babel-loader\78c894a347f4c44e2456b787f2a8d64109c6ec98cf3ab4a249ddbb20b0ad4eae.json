{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M14 8H8\",\n  key: \"1l3xfs\"\n}], [\"path\", {\n  d: \"M16 12H8\",\n  key: \"1fr5h0\"\n}], [\"path\", {\n  d: \"M13 16H8\",\n  key: \"wsln4y\"\n}]];\nconst ReceiptText = createLucideIcon(\"receipt-text\", __iconNode);\nexport { __iconNode, ReceiptText as default };\n//# sourceMappingURL=receipt-text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}