{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"3\",\n  y: \"8\",\n  width: \"18\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"bkv52\"\n}], [\"path\", {\n  d: \"M12 8v13\",\n  key: \"1c76mn\"\n}], [\"path\", {\n  d: \"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7\",\n  key: \"6wjy6b\"\n}], [\"path\", {\n  d: \"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5\",\n  key: \"1ihvrl\"\n}]];\nconst Gift = createLucideIcon(\"gift\", __iconNode);\nexport { __iconNode, Gift as default };\n//# sourceMappingURL=gift.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}