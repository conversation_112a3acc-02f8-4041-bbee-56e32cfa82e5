{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 7v11a1 1 0 0 0 1 1h11\",\n  key: \"13dt1j\"\n}], [\"path\", {\n  d: \"M5.293 18.707 11 13\",\n  key: \"ezgbsx\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"17f5cg\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"1gwv83\"\n}]];\nconst Scale3d = createLucideIcon(\"scale-3d\", __iconNode);\nexport { __iconNode, Scale3d as default };\n//# sourceMappingURL=scale-3d.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}