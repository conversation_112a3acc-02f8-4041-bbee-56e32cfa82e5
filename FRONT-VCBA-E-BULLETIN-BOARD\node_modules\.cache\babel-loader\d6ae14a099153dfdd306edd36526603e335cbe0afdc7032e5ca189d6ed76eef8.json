{"ast": null, "code": "/**\r\n * Form utility functions for consistent form handling across the application\r\n */\n\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\n/**\r\n * Creates FormData for multipart/form-data requests with proper type handling\r\n * Based on best practices for React + Express.js + express-validator\r\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n\n  // Process each field with proper type handling for express-validator\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip null/undefined values\n    if (value === null || value === undefined) {\n      return;\n    }\n\n    // Handle scheduled_publish_at logic\n    if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      return;\n    }\n\n    // Handle different data types properly\n    switch (key) {\n      case 'category_id':\n        // Always send as string representation of integer\n        if (typeof value === 'string' && value.trim() !== '') {\n          const parsed = parseInt(value, 10);\n          if (!isNaN(parsed) && parsed > 0) {\n            formDataToSubmit.append(key, parsed.toString());\n          }\n        }\n        break;\n      case 'subcategory_id':\n        // Optional field - only send if not empty\n        if (typeof value === 'string' && value.trim() !== '') {\n          const parsed = parseInt(value, 10);\n          if (!isNaN(parsed) && parsed > 0) {\n            formDataToSubmit.append(key, parsed.toString());\n          }\n        }\n        break;\n      case 'is_pinned':\n      case 'is_alert':\n      case 'allow_comments':\n      case 'allow_sharing':\n        // Boolean fields - express-validator expects \"true\"/\"false\" strings\n        if (typeof value === 'boolean') {\n          formDataToSubmit.append(key, value.toString());\n        }\n        break;\n      default:\n        // All other fields as strings\n        if (typeof value === 'string' || typeof value === 'number') {\n          formDataToSubmit.append(key, value.toString());\n        }\n        break;\n    }\n  });\n\n  // Add files if present\n  if (files && files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n  return formDataToSubmit;\n};\n\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\r\n * Common validation rules for announcements\r\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "status", "trim", "parsed", "parseInt", "isNaN", "append", "toString", "length", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "join", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\r\n * Form utility functions for consistent form handling across the application\r\n */\r\n\r\nexport interface FormField {\r\n  [key: string]: string | number | boolean | null | undefined;\r\n}\r\n\r\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\r\n/**\r\n * Creates FormData for multipart/form-data requests with proper type handling\r\n * Based on best practices for React + Express.js + express-validator\r\n */\r\nexport const createFormData = (\r\n  formData: FormField,\r\n  files: File[] = [],\r\n  options: {\r\n    skipScheduledDate?: boolean;\r\n    fileFieldName?: string;\r\n  } = {}\r\n): FormData => {\r\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\r\n  const formDataToSubmit = new FormData();\r\n\r\n  // Process each field with proper type handling for express-validator\r\n  Object.entries(formData).forEach(([key, value]) => {\r\n    // Skip null/undefined values\r\n    if (value === null || value === undefined) {\r\n      return;\r\n    }\r\n\r\n    // Handle scheduled_publish_at logic\r\n    if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\r\n      return;\r\n    }\r\n\r\n    // Handle different data types properly\r\n    switch (key) {\r\n      case 'category_id':\r\n        // Always send as string representation of integer\r\n        if (typeof value === 'string' && value.trim() !== '') {\r\n          const parsed = parseInt(value, 10);\r\n          if (!isNaN(parsed) && parsed > 0) {\r\n            formDataToSubmit.append(key, parsed.toString());\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'subcategory_id':\r\n        // Optional field - only send if not empty\r\n        if (typeof value === 'string' && value.trim() !== '') {\r\n          const parsed = parseInt(value, 10);\r\n          if (!isNaN(parsed) && parsed > 0) {\r\n            formDataToSubmit.append(key, parsed.toString());\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'is_pinned':\r\n      case 'is_alert':\r\n      case 'allow_comments':\r\n      case 'allow_sharing':\r\n        // Boolean fields - express-validator expects \"true\"/\"false\" strings\r\n        if (typeof value === 'boolean') {\r\n          formDataToSubmit.append(key, value.toString());\r\n        }\r\n        break;\r\n\r\n      default:\r\n        // All other fields as strings\r\n        if (typeof value === 'string' || typeof value === 'number') {\r\n          formDataToSubmit.append(key, value.toString());\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  // Add files if present\r\n  if (files && files.length > 0) {\r\n    files.forEach((file) => {\r\n      formDataToSubmit.append(fileFieldName, file);\r\n    });\r\n  }\r\n\r\n  return formDataToSubmit;\r\n};\r\n\r\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\r\nexport const validateFormFields = (\r\n  formData: FormField,\r\n  rules: {\r\n    required?: string[];\r\n    maxLength?: { [key: string]: number };\r\n    custom?: { [key: string]: (value: any) => string | null };\r\n  } = {}\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n  const { required = [], maxLength = {}, custom = {} } = rules;\r\n\r\n  // Check required fields\r\n  required.forEach(field => {\r\n    const value = formData[field];\r\n    if (!value || (typeof value === 'string' && !value.trim())) {\r\n      errors[field] = `${field.replace('_', ' ')} is required`;\r\n    }\r\n  });\r\n\r\n  // Check max length\r\n  Object.entries(maxLength).forEach(([field, max]) => {\r\n    const value = formData[field];\r\n    if (typeof value === 'string' && value.length > max) {\r\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\r\n    }\r\n  });\r\n\r\n  // Apply custom validation\r\n  Object.entries(custom).forEach(([field, validator]) => {\r\n    const value = formData[field];\r\n    const error = validator(value);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n/**\r\n * Common validation rules for announcements\r\n */\r\nexport const announcementValidationRules = {\r\n  required: ['title', 'content', 'category_id'],\r\n  maxLength: { title: 255 },\r\n  custom: {\r\n    scheduled_publish_at: (value: any, formData?: FormField) => {\r\n      if (formData?.status === 'scheduled' && !value) {\r\n        return 'Scheduled publish date is required for scheduled announcements';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\r\nexport const validateFile = (\r\n  file: File,\r\n  options: {\r\n    maxSize?: number;\r\n    allowedTypes?: string[];\r\n  } = {}\r\n): string | null => {\r\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\r\n\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  if (file.size > maxSize) {\r\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACAC,MAAM,CAACC,OAAO,CAACR,QAAQ,CAAC,CAACS,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD;IACA,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACzC;IACF;;IAEA;IACA,IAAIF,GAAG,KAAK,sBAAsB,IAAIP,iBAAiB,IAAIH,QAAQ,CAACa,MAAM,KAAK,WAAW,EAAE;MAC1F;IACF;;IAEA;IACA,QAAQH,GAAG;MACT,KAAK,aAAa;QAChB;QACA,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACpD,MAAMC,MAAM,GAAGC,QAAQ,CAACL,KAAK,EAAE,EAAE,CAAC;UAClC,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;YAChCV,gBAAgB,CAACa,MAAM,CAACR,GAAG,EAAEK,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC;UACjD;QACF;QACA;MAEF,KAAK,gBAAgB;QACnB;QACA,IAAI,OAAOR,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACpD,MAAMC,MAAM,GAAGC,QAAQ,CAACL,KAAK,EAAE,EAAE,CAAC;UAClC,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;YAChCV,gBAAgB,CAACa,MAAM,CAACR,GAAG,EAAEK,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC;UACjD;QACF;QACA;MAEF,KAAK,WAAW;MAChB,KAAK,UAAU;MACf,KAAK,gBAAgB;MACrB,KAAK,eAAe;QAClB;QACA,IAAI,OAAOR,KAAK,KAAK,SAAS,EAAE;UAC9BN,gBAAgB,CAACa,MAAM,CAACR,GAAG,EAAEC,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC;QAChD;QACA;MAEF;QACE;QACA,IAAI,OAAOR,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC1DN,gBAAgB,CAACa,MAAM,CAACR,GAAG,EAAEC,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC;QAChD;QACA;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,IAAIlB,KAAK,IAAIA,KAAK,CAACmB,MAAM,GAAG,CAAC,EAAE;IAC7BnB,KAAK,CAACQ,OAAO,CAAEY,IAAI,IAAK;MACtBhB,gBAAgB,CAACa,MAAM,CAACd,aAAa,EAAEiB,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAOhB,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiB,kBAAkB,GAAGA,CAChCtB,QAAmB,EACnBuB,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAAChB,OAAO,CAACmB,KAAK,IAAI;IACxB,MAAMjB,KAAK,GAAGX,QAAQ,CAAC4B,KAAK,CAAC;IAC7B,IAAI,CAACjB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACG,IAAI,CAAC,CAAE,EAAE;MAC1DU,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACAtB,MAAM,CAACC,OAAO,CAACkB,SAAS,CAAC,CAACjB,OAAO,CAAC,CAAC,CAACmB,KAAK,EAAEE,GAAG,CAAC,KAAK;IAClD,MAAMnB,KAAK,GAAGX,QAAQ,CAAC4B,KAAK,CAAC;IAC7B,IAAI,OAAOjB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACS,MAAM,GAAGU,GAAG,EAAE;MACnDN,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACAvB,MAAM,CAACC,OAAO,CAACmB,MAAM,CAAC,CAAClB,OAAO,CAAC,CAAC,CAACmB,KAAK,EAAEG,SAAS,CAAC,KAAK;IACrD,MAAMpB,KAAK,GAAGX,QAAQ,CAAC4B,KAAK,CAAC;IAC7B,MAAMI,KAAK,GAAGD,SAAS,CAACpB,KAAK,CAAC;IAC9B,IAAIqB,KAAK,EAAE;MACTR,MAAM,CAACI,KAAK,CAAC,GAAGI,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,2BAA2B,GAAG;EACzCR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEQ,KAAK,EAAE;EAAI,CAAC;EACzBP,MAAM,EAAE;IACNQ,oBAAoB,EAAEA,CAACxB,KAAU,EAAEX,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,MAAM,MAAK,WAAW,IAAI,CAACF,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyB,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACR,KAAK,CAAC,GAAGM,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,CAAC;EAEnD,OAAOO,UAAU,CAAC,CAACT,KAAK,GAAGM,IAAI,CAACI,GAAG,CAACR,CAAC,EAAEG,CAAC,CAAC,EAAEM,OAAO,CAACR,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,YAAY,GAAGA,CAC1B5B,IAAU,EACVnB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEgD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGjD,OAAO;EAEpH,IAAI,CAACiD,YAAY,CAACC,QAAQ,CAAC/B,IAAI,CAACgC,IAAI,CAAC,EAAE;IACrC,OAAO,aAAahC,IAAI,CAACgC,IAAI,qCAAqCF,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIjC,IAAI,CAACkC,IAAI,GAAGL,OAAO,EAAE;IACvB,OAAO,aAAad,cAAc,CAACf,IAAI,CAACkC,IAAI,CAAC,oCAAoCnB,cAAc,CAACc,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}