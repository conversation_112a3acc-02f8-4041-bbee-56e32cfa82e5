{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{useStudentAuth}from'../../contexts/StudentAuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Admin Protected Route Component\nconst AdminProtectedRoute=_ref=>{let{children,requiredRole,redirectTo}=_ref;const{isAuthenticated,user,isLoading}=useAdminAuth();const location=useLocation();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',minHeight:'100vh',flexDirection:'column',gap:'1rem',background:'#f9fafb'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'40px',border:'4px solid #e5e7eb',borderTop:'4px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Checking authentication...\"})]});}// Check if user is authenticated\nif(!isAuthenticated||!user){console.log('❌ AdminProtectedRoute - User not authenticated, redirecting to login');return/*#__PURE__*/_jsx(Navigate,{to:redirectTo||'/admin/login',state:{from:location},replace:true});}// Check role-based access if required role is specified\nif(requiredRole&&user.role!==requiredRole){console.log('❌ AdminProtectedRoute - Role mismatch:',{userRole:user.role,requiredRole});return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});}console.log('✅ AdminProtectedRoute - Access granted');return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Student Protected Route Component\nconst StudentProtectedRoute=_ref2=>{let{children,requiredRole,redirectTo}=_ref2;const{isAuthenticated,user,isLoading}=useStudentAuth();const location=useLocation();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',minHeight:'100vh',flexDirection:'column',gap:'1rem',background:'#f9fafb'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'40px',border:'4px solid #e5e7eb',borderTop:'4px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Checking authentication...\"})]});}// Check if user is authenticated\nif(!isAuthenticated||!user){console.log('❌ StudentProtectedRoute - User not authenticated, redirecting to login');return/*#__PURE__*/_jsx(Navigate,{to:redirectTo||'/student/login',state:{from:location},replace:true});}// Check role-based access if required role is specified\nif(requiredRole&&user.role!==requiredRole){console.log('❌ StudentProtectedRoute - Role mismatch:',{userRole:user.role,requiredRole});return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});}console.log('✅ StudentProtectedRoute - Access granted');return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Main Protected Route Component that routes to appropriate sub-component\nconst ProtectedRoute=props=>{const location=useLocation();// Determine which protected route component to use based on path\nif(location.pathname.startsWith('/admin')){return/*#__PURE__*/_jsx(AdminProtectedRoute,_objectSpread({},props));}else if(location.pathname.startsWith('/student')){return/*#__PURE__*/_jsx(StudentProtectedRoute,_objectSpread({},props));}// Fallback - redirect to appropriate login\nconst loginPath=location.pathname.startsWith('/student')?'/student/login':'/admin/login';return/*#__PURE__*/_jsx(Navigate,{to:loginPath,state:{from:location},replace:true});};export default ProtectedRoute;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}