{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12h3a2 2 0 0 0 1.902-1.38l1.056-3.333A1 1 0 0 0 21 6H3a1 1 0 0 0-.958 1.287l1.056 3.334A2 2 0 0 0 5 12h3\",\n  key: \"13jjxg\"\n}], [\"path\", {\n  d: \"M18 6V3a1 1 0 0 0-1-1h-3\",\n  key: \"1550fe\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"12\",\n  x: \"8\",\n  y: \"10\",\n  rx: \"1\",\n  key: \"qmu8b6\"\n}]];\nconst Lectern = createLucideIcon(\"lectern\", __iconNode);\nexport { __iconNode, Lectern as default };\n//# sourceMappingURL=lectern.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}