{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z\",\n  key: \"1vdc57\"\n}], [\"path\", {\n  d: \"M5 21h14\",\n  key: \"11awu3\"\n}]];\nconst Crown = createLucideIcon(\"crown\", __iconNode);\nexport { __iconNode, Crown as default };\n//# sourceMappingURL=crown.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}