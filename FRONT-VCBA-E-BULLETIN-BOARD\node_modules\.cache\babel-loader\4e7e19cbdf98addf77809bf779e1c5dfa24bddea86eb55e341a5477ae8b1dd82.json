{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport CommentSection from '../../components/student/CommentSection';\nimport StudentNotificationBell from '../../components/student/NotificationBell';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport { getImageUrl, API_BASE_URL, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Home, Search, Pin, Calendar, MessageSquare, Heart, Filter, MapPin, BookOpen, Users, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('[SUCCESS] Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('[ERROR] Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Reusable CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('[SUCCESS] Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n\n// Facebook-style image gallery component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst FacebookImageGallery = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n  const getImageStyle = (index, total) => {\n    const baseStyle = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n    return baseStyle;\n  };\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n  const renderOverlay = (index, count) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        },\n        children: [\"+\", count]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0],\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '40%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getContainerStyle(actualIndex, visibleImages.length),\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this), renderOverlay(actualIndex, remainingCount), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FacebookImageGallery;\nconst StudentNewsfeed = () => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    notificationId,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const {\n    categories\n  } = useCategories();\n\n  // Get student user context\n  const {\n    user: studentUser,\n    logout\n  } = useStudentAuth();\n\n  // Use the announcements hook for proper state management\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({\n    length: 2\n  }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem(STUDENT_AUTH_TOKEN_KEY)}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for announcement:', announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log('[DEBUG] Student user context:', {\n        id: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n        role: 'student'\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log('[DEBUG] Student removing reaction...');\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log('[DEBUG] Student adding reaction...');\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log('[SUCCESS] Student reaction toggled successfully');\n    } catch (error) {\n      console.error('[ERROR] Error toggling student like:', error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for calendar event:', event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log('[DEBUG] Student user context:', {\n        id: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n        role: 'student'\n      });\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: !event.user_has_reacted,\n          reaction_count: event.user_has_reacted ? (event.reaction_count || 1) - 1 : (event.reaction_count || 0) + 1\n        } : e));\n        console.log('[SUCCESS] Calendar event reaction toggled successfully');\n      }\n    } catch (error) {\n      console.error('[ERROR] Student error toggling calendar like:', error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Filter announcements based on search term, category, and grade level\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Grade level filtering - show announcements for selected grade or all grades (null)\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel || announcement.grade_level === null; // Show all-grade announcements regardless of filter\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events based on their actual event date range\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' + String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' + String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For students, only show published and active events\n    const isPublished = event.is_published === 1;\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventActive && isPublished && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 729,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-full\",\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          style: {\n            background: 'white',\n            borderBottom: '1px solid #e5e7eb',\n            position: 'sticky',\n            top: 0,\n            zIndex: 100,\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              padding: '0 2rem',\n              height: '72px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1.5rem',\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/vcba1.png\",\n                alt: \"VCBA Logo\",\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.5rem',\n                    fontWeight: '700',\n                    color: '#111827',\n                    lineHeight: '1.2'\n                  },\n                  children: \"VCBA E-Bulletin Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: \"Student Newsfeed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                maxWidth: '500px',\n                margin: '0 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20,\n                  style: {\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search post\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  style: {\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  },\n                  onFocus: e => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  },\n                  onBlur: e => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                minWidth: '400px',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterCategory,\n                  onChange: e => setFilterCategory(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Categories\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 19\n                  }, this), categories.filter(category =>\n                  // Hide holiday categories from dropdown\n                  !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category.category_id.toString(),\n                    children: category.name\n                  }, category.category_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterGradeLevel,\n                  onChange: e => setFilterGradeLevel(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Grades\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 19\n                  }, this), gradeLevels.map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade.toString(),\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 17\n                }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  },\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: '#ef4444',\n                    color: 'white',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#dc2626';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#ef4444';\n                  },\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative'\n                  },\n                  \"data-dropdown\": \"user-dropdown\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowUserDropdown(!showUserDropdown),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.1)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(User, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: (studentUser === null || studentUser === void 0 ? void 0 : studentUser.firstName) || 'Student'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                      size: 14,\n                      style: {\n                        transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                        transition: 'transform 0.2s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 19\n                  }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        },\n                        children: [studentUser === null || studentUser === void 0 ? void 0 : studentUser.firstName, \" \", studentUser === null || studentUser === void 0 ? void 0 : studentUser.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1051,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        },\n                        children: studentUser === null || studentUser === void 0 ? void 0 : studentUser.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1058,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.5rem 0'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          navigate('/student/dashboard');\n                          setShowUserDropdown(false);\n                        },\n                        style: {\n                          width: '100%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '0.75rem 1rem',\n                          background: 'transparent',\n                          border: 'none',\n                          color: '#374151',\n                          fontSize: '0.875rem',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = '#f3f4f6';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'transparent';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Home, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1092,\n                          columnNumber: 27\n                        }, this), \"Dashboard\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1067,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          handleLogout();\n                          setShowUserDropdown(false);\n                        },\n                        style: {\n                          width: '100%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '0.75rem 1rem',\n                          background: 'transparent',\n                          border: 'none',\n                          color: '#ef4444',\n                          fontSize: '0.875rem',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = '#fef2f2';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'transparent';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1121,\n                          columnNumber: 27\n                        }, this), \"Logout\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1096,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '1.5rem',\n            padding: '1.5rem 2rem',\n            background: 'transparent',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '280px',\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                borderRadius: '14px',\n                border: '1px solid #e5e7eb',\n                overflow: 'hidden',\n                position: 'sticky',\n                top: '80px',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem 1.25rem 0.75rem',\n                  borderBottom: '1px solid #f3f4f6',\n                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 20,\n                    style: {\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: 'white'\n                    },\n                    children: \"Important Updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: 'rgba(255, 255, 255, 0.8)'\n                  },\n                  children: \"Don't miss these announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '0.75rem'\n                },\n                children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                    // Handle alert announcements with special styling\n                    let colors;\n                    if (announcement.is_alert) {\n                      colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                      // Create gradient background based on category\n                      const gradientColors = {\n                        'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                        'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                        'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                        'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                        'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                        'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                      };\n                      colors = gradientColors[categoryName] || gradientColors['GENERAL'];\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.75rem',\n                        background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                        borderRadius: '10px',\n                        border: `1px solid ${colors[2]}`,\n                        marginBottom: '0.75rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = 'none';\n                      },\n                      onClick: () => setSelectedPinnedPost(announcement),\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'flex-start',\n                          gap: '0.75rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '10px',\n                            height: '10px',\n                            background: colors[2],\n                            borderRadius: '50%',\n                            marginTop: '0.5rem',\n                            flexShrink: 0,\n                            boxShadow: `0 0 0 3px ${colors[2]}30`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1239,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1249,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.8rem',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.content.length > 60 ? `${announcement.content.substring(0, 60)}...` : announcement.content\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1258,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              fontSize: '0.75rem',\n                              color: colors[2]\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                              size: 12\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1275,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: new Date(announcement.created_at).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1276,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1268,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1248,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1234,\n                        columnNumber: 27\n                      }, this)\n                    }, announcement.announcement_id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1213,\n                      columnNumber: 25\n                    }, this);\n                  }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                      color: '#3b82f6',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                      e.currentTarget.style.borderColor = '#e5e7eb';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    },\n                    children: [\"\\uD83D\\uDCCC View All \", pinnedAnnouncements.length, \" Important Updates\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1286,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 24,\n                    style: {\n                      marginBottom: '0.5rem',\n                      opacity: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '0.875rem'\n                    },\n                    children: \"No pinned posts available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1319,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              minWidth: 0\n            },\n            children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                padding: '3rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '2.5rem',\n                  height: '2.5rem',\n                  border: '3px solid #e5e7eb',\n                  borderTop: '3px solid #3b82f6',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1332,\n              columnNumber: 13\n            }, this), (error || calendarError) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(239, 68, 68, 0.1)',\n                border: '1px solid rgba(239, 68, 68, 0.2)',\n                borderRadius: '12px',\n                padding: '1rem',\n                marginBottom: '1.5rem',\n                color: '#dc2626'\n              },\n              children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Announcements: \", error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 25\n              }, this), calendarError && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Calendar: \", calendarError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1360,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1351,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.8)',\n                borderRadius: '16px',\n                padding: '3rem',\n                textAlign: 'center',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(Filter, {\n                  size: 48,\n                  color: \"#9ca3af\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1374,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#374151',\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  margin: '0 0 0.5rem 0'\n                },\n                children: \"No updates found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1379,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: searchTerm || filterCategory || filterGradeLevel ? 'Try adjusting your filters to see more content.' : 'Check back later for new updates.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1387,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1366,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              },\n              children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                      const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                      const IconComponent = holidayStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '50px',\n                          height: '50px',\n                          borderRadius: '12px',\n                          background: holidayStyle.background,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 20,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1453,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1444,\n                        columnNumber: 29\n                      }, this);\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: (() => {\n                          const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: holidayStyle.background,\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1481,\n                              columnNumber: 35\n                            }, this), holidayTypeName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1470,\n                            columnNumber: 33\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1458,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1487,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1457,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: event.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 23\n                  }, this), (() => {\n                    // Get event images if they exist\n                    const eventImageUrls = [];\n                    if (event.images && event.images.length > 0) {\n                      event.images.forEach(img => {\n                        if (img.file_path) {\n                          // Convert file_path to full URL\n                          const imageUrl = getImageUrl(img.file_path);\n                          if (imageUrl) {\n                            eventImageUrls.push(imageUrl);\n                          }\n                        }\n                      });\n                    }\n                    return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '1rem'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: eventImageUrls.filter(Boolean),\n                        altPrefix: event.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          const filteredImages = eventImageUrls.filter(Boolean);\n                          openLightbox(filteredImages, index);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1531,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1530,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0 0 1rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    children: event.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1545,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                          size: 14,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1571,\n                          columnNumber: 29\n                        }, this), \"School Event\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1570,\n                        columnNumber: 27\n                      }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Until \", new Date(event.end_date).toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1575,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1563,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleCalendarLikeToggle(event),\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'none',\n                          border: 'none',\n                          color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                          cursor: 'pointer',\n                          padding: '0.5rem',\n                          borderRadius: '8px',\n                          transition: 'all 0.2s ease',\n                          fontSize: '0.875rem',\n                          fontWeight: '500'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'none';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Heart, {\n                          size: 18,\n                          fill: event.user_has_reacted ? '#ef4444' : 'none'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1614,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: event.reaction_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1618,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1591,\n                        columnNumber: 27\n                      }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'none',\n                          border: 'none',\n                          color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                          cursor: 'pointer',\n                          padding: '0.5rem',\n                          borderRadius: '8px',\n                          transition: 'all 0.2s ease',\n                          fontSize: '0.875rem',\n                          fontWeight: '500'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'none';\n                          e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                          size: 18\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1650,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: event.comment_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1651,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1623,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1582,\n                      columnNumber: 25\n                    }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CommentSection, {\n                        calendarId: event.calendar_id,\n                        allowComments: event.allow_comments,\n                        currentUserId: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n                        currentUserType: \"student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1663,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1658,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1556,\n                    columnNumber: 23\n                  }, this)]\n                }, `event-${event.calendar_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: `announcement-${announcement.announcement_id}`,\n                  className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: announcement.is_alert ? '2px solid rgba(239, 68, 68, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: announcement.is_alert ? '0 4px 20px rgba(239, 68, 68, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = announcement.is_alert ? '0 8px 30px rgba(239, 68, 68, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = announcement.is_alert ? '0 4px 20px rgba(239, 68, 68, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      if (announcement.is_alert) {\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '50px',\n                            height: '50px',\n                            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                            borderRadius: '12px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            flexShrink: 0\n                          },\n                          children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                            size: 20,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1731,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1721,\n                          columnNumber: 31\n                        }, this);\n                      } else {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                        const categoryStyle = getCategoryStyle(categoryName);\n                        const IconComponent = categoryStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '50px',\n                            height: '50px',\n                            borderRadius: '12px',\n                            background: categoryStyle.background,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 20,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1749,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1740,\n                          columnNumber: 31\n                        }, this);\n                      }\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: [(() => {\n                          if (announcement.is_alert) {\n                            return /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                color: 'white',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                textTransform: 'uppercase',\n                                letterSpacing: '0.5px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                size: 12,\n                                color: \"white\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1777,\n                                columnNumber: 37\n                              }, this), \"Alert\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1764,\n                              columnNumber: 35\n                            }, this);\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n                            return /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                background: categoryStyle.background,\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                                size: 12,\n                                color: \"white\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1798,\n                                columnNumber: 37\n                              }, this), categoryName]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1787,\n                              columnNumber: 35\n                            }, this);\n                          }\n                        })(), announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                            color: 'white',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '12px',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Pin, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1816,\n                            columnNumber: 33\n                          }, this), \"PINNED\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1805,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1755,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: [\"By \", announcement.author_name, \" \\u2022 \", announcement.published_at ? new Date(announcement.published_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric',\n                          hour: '2-digit',\n                          minute: '2-digit'\n                        }) : 'Unknown date']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1821,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1754,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1712,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: announcement.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1838,\n                    columnNumber: 23\n                  }, this), (() => {\n                    // Get images from multiple sources\n                    const imageUrls = [];\n\n                    // Add images from attachments (new multiple image system)\n                    if (announcement.attachments && announcement.attachments.length > 0) {\n                      announcement.attachments.forEach(img => {\n                        if (img.file_path) {\n                          // Use getImageUrl to construct the full URL\n                          const fullUrl = getImageUrl(img.file_path);\n                          if (fullUrl) {\n                            imageUrls.push(fullUrl);\n                          }\n                        }\n                      });\n                    }\n\n                    // Fallback to legacy single image\n                    if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                      const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                      if (legacyUrl) {\n                        imageUrls.push(legacyUrl);\n                      }\n                    }\n                    return imageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: imageUrls.filter(Boolean),\n                      altPrefix: announcement.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = imageUrls.filter(Boolean);\n                        openLightbox(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1875,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '0 0 1.5rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: announcement.content\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1887,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleLikeToggle(announcement),\n                        style: {\n                          background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                          color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                          border: 'none',\n                          borderRadius: '8px',\n                          padding: '0.5rem 1rem',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          if (!announcement.user_reaction) {\n                            e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                            e.currentTarget.style.color = '#dc2626';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (!announcement.user_reaction) {\n                            e.currentTarget.style.background = 'transparent';\n                            e.currentTarget.style.color = '#6b7280';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Heart, {\n                          size: 16,\n                          color: announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\",\n                          fill: announcement.user_reaction ? \"#dc2626\" : \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1939,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: announcement.reaction_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1944,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1910,\n                        columnNumber: 27\n                      }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                        style: {\n                          background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                          color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                          border: 'none',\n                          borderRadius: '8px',\n                          padding: '0.5rem 1rem',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          if (showComments !== announcement.announcement_id) {\n                            e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            e.currentTarget.style.color = '#3b82f6';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (showComments !== announcement.announcement_id) {\n                            e.currentTarget.style.background = 'transparent';\n                            e.currentTarget.style.color = '#6b7280';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                          size: 16,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1980,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: announcement.comment_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1981,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1949,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1904,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1897,\n                    columnNumber: 23\n                  }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '1rem',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(CommentSection, {\n                      announcementId: announcement.announcement_id,\n                      allowComments: announcement.allow_comments,\n                      currentUserId: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n                      currentUserType: \"student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1999,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1991,\n                    columnNumber: 25\n                  }, this)]\n                }, `announcement-${announcement.announcement_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1681,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1400,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1329,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1134,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 7\n      }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        },\n        onClick: () => setSelectedPinnedPost(null),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 20,\n                style: {\n                  color: '#22c55e'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2058,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                },\n                children: \"Pinned Post\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2059,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2053,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedPinnedPost(null),\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#6b7280',\n                cursor: 'pointer',\n                padding: '0.25rem',\n                borderRadius: '4px',\n                transition: 'color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.color = '#374151';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.color = '#6b7280';\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2068,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2046,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              },\n              children: [(() => {\n                if (selectedPinnedPost.is_alert) {\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2115,\n                      columnNumber: 25\n                    }, this), \"Alert\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2102,\n                    columnNumber: 23\n                  }, this);\n                } else {\n                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  const IconComponent = categoryStyle.icon;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: categoryStyle.background,\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2138,\n                      columnNumber: 25\n                    }, this), categoryName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2125,\n                    columnNumber: 23\n                  }, this);\n                }\n              })(), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2156,\n                  columnNumber: 19\n                }, this), \"PINNED\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2093,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              },\n              children: selectedPinnedPost.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              },\n              children: selectedPinnedPost.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2171,\n              columnNumber: 15\n            }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                images: selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean),\n                altPrefix: selectedPinnedPost.title,\n                onImageClick: index => {\n                  const imageUrls = selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean);\n                  openLightbox(imageUrls, index);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2183,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2203,\n                columnNumber: 17\n              }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"By: \", selectedPinnedPost.author_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2092,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2034,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2019,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n        images: lightboxImages,\n        initialIndex: lightboxInitialIndex,\n        isOpen: lightboxOpen,\n        onClose: () => setLightboxOpen(false),\n        altPrefix: \"Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2223,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 766,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s3(StudentNewsfeed, \"i1ctYoT3Z1LBh34YA0aEpS6Wjak=\", false, function () {\n  return [useNavigate, useNotificationTarget, useCategories, useStudentAuth, useAnnouncements];\n});\n_c3 = StudentNewsfeed;\nexport default StudentNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"FacebookImageGallery\");\n$RefreshReg$(_c3, \"StudentNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAnnouncements", "useCategories", "useNotificationTarget", "useStudentAuth", "calendarReactionService", "CommentSection", "StudentNotificationBell", "ImageLightbox", "getImageUrl", "API_BASE_URL", "STUDENT_AUTH_TOKEN_KEY", "Home", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Filter", "MapPin", "BookOpen", "Users", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "FacebookImageGallery", "images", "altPrefix", "maxVisible", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "getImageStyle", "index", "total", "baseStyle", "width", "height", "objectFit", "cursor", "transition", "borderRadius", "getContainerStyle", "position", "overflow", "renderOverlay", "count", "top", "left", "right", "bottom", "gap", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "_c2", "StudentNewsfeed", "_s3", "navigate", "isFromNotification", "notificationId", "scrollTarget", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "pinnedAnnouncements", "setPinnedAnnouncements", "categories", "user", "studentUser", "logout", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "gradeLevels", "Array", "from", "_", "i", "openLightbox", "imageUrls", "initialIndex", "pinned", "filter", "ann", "is_pinned", "fetchCalendarEvents", "undefined", "token", "localStorage", "getItem", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "handleCalendarLikeToggle", "user_has_reacted", "toggleLike", "prevEvents", "reaction_count", "handleLogout", "href", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "filteredAnnouncements", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "event_date", "eventStartDateString", "eventEndDateString", "end_date", "endDate", "isEventActive", "isPublished", "is_published", "isActive", "is_active", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "scroll<PERSON>eh<PERSON>or", "flex", "borderBottom", "zIndex", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "grade", "firstName", "lastName", "email", "flexShrink", "colors", "is_alert", "category_name", "toUpperCase", "gradientColors", "substring", "toLocaleDateString", "opacity", "borderTop", "animation", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "file_path", "push", "Boolean", "filteredImages", "paddingTop", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "categoryStyle", "textTransform", "letterSpacing", "author_name", "published_at", "hour", "minute", "image_url", "image_path", "legacyUrl", "dangerouslySetInnerHTML", "__html", "announcementId", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { announcementService, calendarService } from '../../services';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport CommentSection from '../../components/student/CommentSection';\nimport StudentNotificationBell from '../../components/student/NotificationBell';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport type { Announcement } from '../../types/announcement.types';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Home,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Filter,\n  MapPin,\n  BookOpen,\n  Users,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('[SUCCESS] Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('[ERROR] Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Reusable CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('[SUCCESS] Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Facebook-style image gallery component\ninterface FacebookImageGalleryProps {\n  images: string[];\n  altPrefix: string;\n  maxVisible?: number;\n  onImageClick?: (index: number) => void;\n}\n\nconst FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n\n    return baseStyle;\n  };\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n\n  const renderOverlay = (index: number, count: number) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        }}>\n          +{count}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0]}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images (for 2+ images) */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '40%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            return (\n              <div\n                key={actualIndex}\n                style={getContainerStyle(actualIndex, visibleImages.length)}\n              >\n                <ImageDisplay\n                  imagePath={image}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                {renderOverlay(actualIndex, remainingCount)}\n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst StudentNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, notificationId, scrollTarget } = useNotificationTarget();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n  \n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n\n  const { categories } = useCategories();\n\n  // Get student user context\n  const { user: studentUser, logout } = useStudentAuth();\n\n  // Use the announcements hook for proper state management\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({ length: 2 }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem(STUDENT_AUTH_TOKEN_KEY)}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n  }, []);\n\n\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for announcement:', announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log('[DEBUG] Student user context:', { id: studentUser?.id, role: 'student' });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log('[DEBUG] Student removing reaction...');\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log('[DEBUG] Student adding reaction...');\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log('[SUCCESS] Student reaction toggled successfully');\n    } catch (error) {\n      console.error('[ERROR] Error toggling student like:', error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for calendar event:', event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log('[DEBUG] Student user context:', { id: studentUser?.id, role: 'student' });\n\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: !event.user_has_reacted,\n                  reaction_count: event.user_has_reacted\n                    ? (event.reaction_count || 1) - 1\n                    : (event.reaction_count || 0) + 1\n                }\n              : e\n          )\n        );\n        console.log('[SUCCESS] Calendar event reaction toggled successfully');\n      }\n    } catch (error) {\n      console.error('[ERROR] Student error toggling calendar like:', error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n\n\n  // Filter announcements based on search term, category, and grade level\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Grade level filtering - show announcements for selected grade or all grades (null)\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel ||\n      announcement.grade_level === null; // Show all-grade announcements regardless of filter\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events based on their actual event date range\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' +\n      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n      String(today.getDate()).padStart(2, '0');\n\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' +\n      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +\n      String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' +\n        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +\n        String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For students, only show published and active events\n    const isPublished = (event as any).is_published === 1;\n    const isActive = (event as any).is_active !== 0;\n\n    return matchesSearch && isEventActive && isPublished && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <>\n      {/* CSS Animations */}\n      <style>\n        {`\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `}\n      </style>\n\n      <div style={{\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      }}>\n\n      {/* Main Content Area */}\n      <div\n        className=\"mobile-full\"\n        style={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        }}>\n        {/* Modern Student Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            width: '100%',\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Student Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Filters + Actions */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories\n                    .filter(category =>\n                      // Hide holiday categories from dropdown\n                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                    )\n                    .map(category => (\n                      <option key={category.category_id} value={category.category_id.toString()}>\n                        {category.name}\n                      </option>\n                    ))\n                  }\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  {gradeLevels.map(grade => (\n                    <option key={grade} value={grade.toString()}>\n                      Grade {grade}\n                    </option>\n                  ))}\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell */}\n                <StudentNotificationBell />\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    <User size={16} />\n                    <span>{studentUser?.firstName || 'Student'}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        }}>\n                          {studentUser?.firstName} {studentUser?.lastName}\n                        </div>\n                        <div style={{\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          {studentUser?.email}\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        <button\n                          onClick={() => {\n                            navigate('/student/dashboard');\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#374151',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f3f4f6';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <Home size={16} />\n                          Dashboard\n                        </button>\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content Layout */}\n        <div style={{\n          flex: 1,\n          display: 'flex',\n          gap: '1.5rem',\n          padding: '1.5rem 2rem',\n          background: 'transparent',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '280px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '14px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '80px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1rem 1.25rem 0.75rem',\n                borderBottom: '1px solid #f3f4f6',\n                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: 'white' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: 'white'\n                  }}>\n                    Important Updates\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: 'rgba(255, 255, 255, 0.8)'\n                }}>\n                  Don't miss these announcements\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '0.75rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                      // Handle alert announcements with special styling\n                      let colors: [string, string, string];\n                      if (announcement.is_alert) {\n                        colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                      } else {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                        // Create gradient background based on category\n                        const gradientColors: Record<string, [string, string, string]> = {\n                          'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                          'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                          'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                          'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                          'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                          'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                        };\n\n                        colors = gradientColors[categoryName] || gradientColors['GENERAL'];\n                      }\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '0.75rem',\n                            background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                            borderRadius: '10px',\n                            border: `1px solid ${colors[2]}`,\n                            marginBottom: '0.75rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.transform = 'translateY(-2px)';\n                            e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.transform = 'translateY(0)';\n                            e.currentTarget.style.boxShadow = 'none';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '10px',\n                              height: '10px',\n                              background: colors[2],\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0,\n                              boxShadow: `0 0 0 3px ${colors[2]}30`\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 60\n                                  ? `${announcement.content.substring(0, 60)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: colors[2]\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                        color: '#3b82f6',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                        e.currentTarget.style.borderColor = '#3b82f6';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}>\n                        📌 View All {pinnedAnnouncements.length} Important Updates\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: '3rem'\n            }}>\n              <div style={{\n                width: '2.5rem',\n                height: '2.5rem',\n                border: '3px solid #e5e7eb',\n                borderTop: '3px solid #3b82f6',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></div>\n            </div>\n          )}\n\n          {/* Error Messages */}\n          {(error || calendarError) && (\n            <div style={{\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '12px',\n              padding: '1rem',\n              marginBottom: '1.5rem',\n              color: '#dc2626'\n            }}>\n              {error && <div>Announcements: {error}</div>}\n              {calendarError && <div>Calendar: {calendarError}</div>}\n            </div>\n          )}\n\n          {/* No Content */}\n          {!loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.8)',\n              borderRadius: '16px',\n              padding: '3rem',\n              textAlign: 'center',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)'\n            }}>\n              <div style={{\n                marginBottom: '1rem'\n              }}>\n                <Filter size={48} color=\"#9ca3af\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                margin: '0 0 0.5rem 0'\n              }}>\n                No updates found\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: 0\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'Try adjusting your filters to see more content.'\n                  : 'Check back later for new updates.'}\n              </p>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              maxWidth: '1200px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n\n                          return (\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: holidayStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}>\n                              <IconComponent size={20} color=\"white\" />\n                            </div>\n                          );\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            {new Date(event.event_date).toLocaleDateString('en-US', {\n                              weekday: 'long',\n                              year: 'numeric',\n                              month: 'long',\n                              day: 'numeric'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {event.title}\n                      </h3>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightbox(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {event.description && (\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6'\n                        }}>\n                          {event.description}\n                        </p>\n                      )}\n\n                      {/* Event Footer */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                            <MapPin size={14} color=\"#6b7280\" />\n                            School Event\n                          </span>\n                          {event.end_date && event.end_date !== event.event_date && (\n                            <span>\n                              Until {new Date(event.end_date).toLocaleDateString()}\n                            </span>\n                          )}\n                        </div>\n\n                        {/* Calendar Event Interaction Section */}\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleCalendarLikeToggle(event)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                            />\n                            <span>{(event as any).reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {(event as any).allow_comments && (\n                            <button\n                              onClick={() => setShowCalendarComments(\n                                showCalendarComments === event.calendar_id ? null : event.calendar_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{(event as any).comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n                        {/* Calendar Event Comments Section */}\n                        {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                          <div style={{\n                            marginTop: '1rem',\n                            paddingTop: '1rem',\n                            borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                          }}>\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={studentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_alert\n                          ? '2px solid rgba(239, 68, 68, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_alert\n                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_alert\n                          ? '0 8px 30px rgba(239, 68, 68, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_alert\n                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={20} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                borderRadius: '12px',\n                                background: categoryStyle.background,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                              }}>\n                                <IconComponent size={20} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n                            {announcement.is_pinned && (\n                              <span style={{\n                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              }}>\n                                <Pin size={12} color=\"white\" />\n                                PINNED\n                              </span>\n                            )}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            By {announcement.author_name} • {announcement.published_at ? new Date(announcement.published_at).toLocaleDateString('en-US', {\n                              weekday: 'short',\n                              year: 'numeric',\n                              month: 'short',\n                              day: 'numeric',\n                              hour: '2-digit',\n                              minute: '2-digit'\n                            }) : 'Unknown date'}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {announcement.title}\n                      </h3>\n\n                      {/* Images - Facebook-style Gallery */}\n                      {(() => {\n                        // Get images from multiple sources\n                        const imageUrls: string[] = [];\n\n                        // Add images from attachments (new multiple image system)\n                        if (announcement.attachments && announcement.attachments.length > 0) {\n                          announcement.attachments.forEach((img: AnnouncementAttachment) => {\n                            if (img.file_path) {\n                              // Use getImageUrl to construct the full URL\n                              const fullUrl = getImageUrl(img.file_path);\n                              if (fullUrl) {\n                                imageUrls.push(fullUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        // Fallback to legacy single image\n                        if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                          const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                          if (legacyUrl) {\n                            imageUrls.push(legacyUrl);\n                          }\n                        }\n\n                        return imageUrls.length > 0 ? (\n                          <FacebookImageGallery\n                            images={imageUrls.filter(Boolean) as string[]}\n                            altPrefix={announcement.title}\n                            maxVisible={4}\n                            onImageClick={(index) => {\n                              const filteredImages = imageUrls.filter(Boolean) as string[];\n                              openLightbox(filteredImages, index);\n                            }}\n                          />\n                        ) : null;\n                      })()}\n\n                      <div style={{\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      }}\n                      dangerouslySetInnerHTML={{ __html: announcement.content }}\n                      />\n\n                      {/* Announcement Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                              color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                              border: 'none',\n                              borderRadius: '8px',\n                              padding: '0.5rem 1rem',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                                e.currentTarget.style.color = '#dc2626';\n                              }\n                            }}\n                            onMouseLeave={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'transparent';\n                                e.currentTarget.style.color = '#6b7280';\n                              }\n                            }}\n                          >\n                            <Heart\n                              size={16}\n                              color={announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\"}\n                              fill={announcement.user_reaction ? \"#dc2626\" : \"none\"}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comment Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                                color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                transition: 'all 0.2s ease'\n                              }}\n                              onMouseEnter={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                                  e.currentTarget.style.color = '#3b82f6';\n                                }\n                              }}\n                              onMouseLeave={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'transparent';\n                                  e.currentTarget.style.color = '#6b7280';\n                                }\n                              }}\n                            >\n                              <MessageSquare size={16} color=\"#6b7280\" />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n\n\n                          {/* Existing Comments */}\n                          <CommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserId={studentUser?.id}\n                            currentUserType=\"student\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <FacebookImageGallery\n                    images={selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean)}\n                    altPrefix={selectedPinnedPost.title}\n                    onImageClick={(index) => {\n                      const imageUrls = selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean);\n                      openLightbox(imageUrls, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Image\"\n      />\n    </div>\n    </>\n  );\n};\n\nexport default StudentNewsfeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,cAAc,QAAQ,mCAAmC;AAElE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,uBAAuB,MAAM,2CAA2C;AAC/E,OAAOC,aAAa,MAAM,uCAAuC;AAIjE,SAASC,WAAW,EAAEC,YAAY,EAAEC,sBAAsB,QAAQ,wBAAwB;AAC1F,OAAO,wCAAwC;AAC/C,SACEC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACwC,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGvC,WAAW,CAAC8B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CrB,WAAW,CAACsB,SAAS,CAAC;QAEtBd,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAE9D,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEsB,GAAG,CAAC;QACjDrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA/DMF,cAAc;AA0EpB,MAAMiC,YAAyC,GAAGA,CAAC;EACjDhC,SAAS;EACTiC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAErC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEN,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/E9C,KAAK,iBACJV,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzEvC;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IACE4D,GAAG,EAAEtD,QAAS;IACd+B,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CArEMP,YAAyC;EAAA,QASRjC,cAAc;AAAA;AAAA2D,EAAA,GAT/C1B,YAAyC;AA6E/C,MAAM2B,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,SAAS;EACTC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;EACjD,MAAMK,cAAc,GAAGP,MAAM,CAACI,MAAM,GAAGF,UAAU;EAEjD,MAAMM,aAAa,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC3E,MAAMC,SAA8B,GAAG;MACrCC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,uCAAuC;MACnDC,YAAY,EAAER,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG;IACtD,CAAC;IAED,OAAOC,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCQ,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBrC,eAAe,EAAE;IACnB,CAAC;IAED,IAAI2B,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UACL,GAAGE,SAAS;UACZC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGN,SAAS;UACZC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGE,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL,GAAGN,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE;MAChB,CAAC;IACH;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAACZ,KAAa,EAAEa,KAAa,KAAK;IACtD,IAAIb,KAAK,KAAKP,UAAU,GAAG,CAAC,IAAIoB,KAAK,GAAG,CAAC,EAAE;MACzC,oBACEtF,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdI,QAAQ,EAAE,QAAQ;UAClBM,UAAU,EAAE,KAAK;UACjBuB,YAAY,EAAE;QAChB,CAAE;QAAAhC,QAAA,GAAC,GACA,EAACqC,KAAK;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACExD,OAAA;IAAKsC,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf+C,GAAG,EAAE,KAAK;MACVf,KAAK,EAAE,MAAM;MACbzB,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAjD,OAAA;MAAKsC,KAAK,EAAE4C,iBAAiB,CAAC,CAAC,EAAEb,aAAa,CAACD,MAAM,CAAE;MAAAnB,QAAA,gBACrDjD,OAAA,CAACoC,YAAY;QACXhC,SAAS,EAAEiE,aAAa,CAAC,CAAC,CAAE;QAC5BhC,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAEkC,aAAa,CAAC,CAAC,EAAEH,aAAa,CAACD,MAAM,CAAE;QAC9C3B,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;QACjD,CAAE;QACFnD,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDW,YAAY,iBACXnE,OAAA;QACEsC,KAAK,EAAE;UACL6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTX,MAAM,EAAE;QACV,CAAE;QACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,CAAC;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLa,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBpE,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE,QAAQ;QACvBJ,GAAG,EAAE,KAAK;QACVf,KAAK,EAAEP,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAnB,QAAA,EACCoB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,oBACElG,OAAA;UAEEsC,KAAK,EAAE4C,iBAAiB,CAACiB,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;UAAAnB,QAAA,gBAE5DjD,OAAA,CAACoC,YAAY;YACXhC,SAAS,EAAE6F,KAAM;YACjB5D,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAEkC,aAAa,CAAC2B,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;YACxD3B,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;YACjD,CAAE;YACFnD,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD6B,aAAa,CAACc,WAAW,EAAE5B,cAAc,CAAC,EAC1CJ,YAAY,iBACXnE,OAAA;YACEsC,KAAK,EAAE;cACL6C,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTX,MAAM,EAAE;YACV,CAAE;YACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACgC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GA3BI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Bb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4C,GAAA,GAhMIrC,oBAAyD;AAkM/D,MAAMsC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtC,MAAMC,QAAQ,GAAG1I,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAE2I,kBAAkB;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAG1I,qBAAqB,CAAC,CAAC;;EAEpF;EACA,MAAM2I,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9H;MACR,CAAC;MACD,SAAS,EAAE;QACT6H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE7H;MACR,CAAC;MACD,QAAQ,EAAE;QACR4H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE5H;MACR,CAAC;MACD,WAAW,EAAE;QACX2H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE3H;MACR,CAAC;MACD,QAAQ,EAAE;QACR0H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzH;MACR,CAAC;MACD,WAAW,EAAE;QACXwH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE1H;MACR;IACF,CAAC;IAED,OAAOwH,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtH;MACR,CAAC;MACD,cAAc,EAAE;QACdqH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvH;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBsH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErH;MACR,CAAC;MACD,cAAc,EAAE;QACdoH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzH;MACR,CAAC;MACD,YAAY,EAAE;QACZwH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpH;MACR,CAAC;MACD,SAAS,EAAE;QACTmH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExH;MACR;IACF,CAAC;IAED,OAAOsH,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACyJ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1J,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC2J,UAAU,EAAEC,aAAa,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAAC6J,YAAY,EAAEC,eAAe,CAAC,GAAG9J,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhK,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACiK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlK,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAACmK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACqK,YAAY,EAAEC,eAAe,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuK,cAAc,EAAEC,iBAAiB,CAAC,GAAGxK,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACyK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1K,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC2K,cAAc,EAAEC,iBAAiB,CAAC,GAAG5K,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAAC6K,eAAe,EAAEC,kBAAkB,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+K,aAAa,EAAEC,gBAAgB,CAAC,GAAGhL,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAACiL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlL,QAAQ,CAAQ,EAAE,CAAC;EAEzE,MAAM;IAAEmL;EAAW,CAAC,GAAG/K,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEgL,IAAI,EAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGhL,cAAc,CAAC,CAAC;;EAEtD;EACA,MAAM;IACJiL,aAAa;IACb1I,OAAO;IACPE,KAAK;IACLyI,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAGxL,gBAAgB,CAAC;IACnB4D,MAAM,EAAE,WAAW;IACnB6H,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEzF,MAAM,EAAE;EAAE,CAAC,EAAE,CAAC0F,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMC,YAAY,GAAGA,CAACC,SAAmB,EAAEC,YAAoB,KAAK;IAClE/B,iBAAiB,CAAC8B,SAAS,CAAC;IAC5B5B,uBAAuB,CAAC6B,YAAY,CAAC;IACrCjC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACArK,SAAS,CAAC,MAAM;IACd,MAAMuM,MAAM,GAAGjB,aAAa,CAACkB,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtEzB,sBAAsB,CAACsB,MAAM,CAAC;EAChC,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF9B,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAAC6B,SAAS,CAAC;MAE3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACnM,sBAAsB,CAAC;MAE1D,MAAMyC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3C,YAAY,0DAA0D,EAAE;QACtG6C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUqJ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMG,IAAI,GAAG,MAAM3J,QAAQ,CAAC4J,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAAC/E,GAAG,CAAC,MAAOoF,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMnK,KAAK,CAAC,GAAG3C,YAAY,iBAAiB6M,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FlK,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUsJ,YAAY,CAACC,OAAO,CAACnM,sBAAsB,CAAC,EAAE;gBACzE,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAM+M,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACpH,MAAM,GAAGuH,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACpH,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAOyH,MAAM,EAAE;YACf1K,OAAO,CAAC2K,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACpH,MAAM,GAAG,EAAE;UACnB;UACA,OAAOoH,KAAK;QACd,CAAC,CACH,CAAC;QAED7C,iBAAiB,CAAC0C,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLtC,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAO3G,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEsB,GAAG,CAAC;MACrD2G,gBAAgB,CAAC3G,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRwG,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA7K,SAAS,CAAC,MAAM;IACd2M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAMoB,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF7K,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE4K,YAAY,CAACC,eAAe,CAAC;MAChG9K,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4K,YAAY,CAACE,aAAa,CAAC;MACzE/K,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAE+K,EAAE,EAAE/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;MAEtF,IAAIJ,YAAY,CAACE,aAAa,EAAE;QAC9B;QACA/K,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,MAAMoI,kBAAkB,CAACwC,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACA9K,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAMmI,gBAAgB,CAACyC,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEA9K,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAChE,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAMuL,wBAAwB,GAAG,MAAOb,KAAU,IAAK;IACrD,IAAI;MACFrK,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEoK,KAAK,CAACE,WAAW,CAAC;MACvFvK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoK,KAAK,CAACc,gBAAgB,CAAC;MACxEnL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAE+K,EAAE,EAAE/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;MAEtF,MAAM/K,QAAQ,GAAG,MAAM/C,uBAAuB,CAACiO,UAAU,CAACf,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACc,gBAAgB,IAAI,KAAK,CAAC;MAE7G,IAAIjL,QAAQ,CAAC6J,OAAO,EAAE;QACpB;QACAvC,iBAAiB,CAAC6D,UAAU,IAC1BA,UAAU,CAACpG,GAAG,CAACnC,CAAC,IACdA,CAAC,CAACyH,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAGzH,CAAC;UACJqI,gBAAgB,EAAE,CAACd,KAAK,CAACc,gBAAgB;UACzCG,cAAc,EAAEjB,KAAK,CAACc,gBAAgB,GAClC,CAACd,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI,CAAC,GAC/B,CAACjB,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI;QACpC,CAAC,GACDxI,CACN,CACF,CAAC;QACD9C,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAM4L,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrD,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACAW,MAAM,CAACC,QAAQ,CAACiL,IAAI,GAAG,gBAAgB;IACzC;EACF,CAAC;;EAED;EACA3O,SAAS,CAAC,MAAM;IACd,MAAM4O,kBAAkB,GAAIpB,KAAiB,IAAK;MAChD,IAAItD,gBAAgB,EAAE;QACpB,MAAM2E,MAAM,GAAGrB,KAAK,CAACqB,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtD3E,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAED4E,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAAC1E,gBAAgB,CAAC,CAAC;;EAItB;EACA,MAAMgF,qBAAqB,GAAG5D,aAAa,CAACkB,MAAM,CAACwB,YAAY,IAAI;IAAA,IAAAmB,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAAC1F,UAAU,IAC/BsE,YAAY,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC,IACnEtB,YAAY,CAACwB,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAACnG,cAAc,IAAI0E,YAAY,CAAC0B,WAAW,CAACC,QAAQ,CAAC,CAAC,KAAKrG,cAAc;;IAEjG;IACA,MAAMsG,iBAAiB,GAAG,CAACpG,gBAAgB,IACzC,EAAA2F,qBAAA,GAAAnB,YAAY,CAAC6B,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAKnG,gBAAgB,IACzDwE,YAAY,CAAC6B,WAAW,KAAK,IAAI,CAAC,CAAC;;IAErC,OAAOT,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGpF,cAAc,CAAC8B,MAAM,CAACgB,KAAK,IAAI;IAC5D,MAAM4B,aAAa,GAAG,CAAC1F,UAAU,IAC/B8D,KAAK,CAAC6B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC,IAC3D9B,KAAK,CAACuC,WAAW,IAAIvC,KAAK,CAACuC,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACJ,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1C,MAAME,cAAc,GAAG,IAAIP,IAAI,CAACzC,KAAK,CAACiD,UAAU,CAAC;IACjD,MAAMC,oBAAoB,GAAGF,cAAc,CAACL,WAAW,CAAC,CAAC,GAAG,GAAG,GAC7DC,MAAM,CAACI,cAAc,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAC5DF,MAAM,CAACI,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMK,kBAAkB,GAAGnD,KAAK,CAACoD,QAAQ,GAAG,CAAC,MAAM;MACjD,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAACzC,KAAK,CAACoD,QAAQ,CAAC;MACxC,OAAOC,OAAO,CAACV,WAAW,CAAC,CAAC,GAAG,GAAG,GAChCC,MAAM,CAACS,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACrDF,MAAM,CAACS,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9C,CAAC,EAAE,CAAC,GAAGI,oBAAoB;;IAE3B;IACA,MAAMI,aAAa,GAAGZ,eAAe,IAAIQ,oBAAoB,IAAIR,eAAe,IAAIS,kBAAkB;;IAEtG;IACA,MAAMI,WAAW,GAAIvD,KAAK,CAASwD,YAAY,KAAK,CAAC;IACrD,MAAMC,QAAQ,GAAIzD,KAAK,CAAS0D,SAAS,KAAK,CAAC;IAE/C,OAAO9B,aAAa,IAAI0B,aAAa,IAAIC,WAAW,IAAIE,QAAQ;EAClE,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGjC,qBAAqB;EAClD,MAAMkC,aAAa,GAAGtB,sBAAsB;;EAI5C;EACA,MAAMuB,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAAC/I,GAAG,CAACkJ,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIvB,IAAI,CAACqB,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAAChJ,GAAG,CAACkJ,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIvB,IAAI,CAACqB,IAAI,CAACb,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACEzP,OAAA,CAAAE,SAAA;IAAA+C,QAAA,gBAEEjD,OAAA;MAAAiD,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERxD,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACf8M,SAAS,EAAE,OAAO;QAClB5I,UAAU,EAAE,mDAAmD;QAC/D6I,cAAc,EAAE;MAClB,CAAE;MAAA1M,QAAA,gBAGFjD,OAAA;QACEuC,SAAS,EAAC,aAAa;QACvBD,KAAK,EAAE;UACLsN,IAAI,EAAE,CAAC;UACPhN,OAAO,EAAE,MAAM;UACfmD,aAAa,EAAE,QAAQ;UACvB2J,SAAS,EAAE,OAAO;UAClB9K,KAAK,EAAE;QACT,CAAE;QAAA3B,QAAA,gBAEFjD,OAAA;UAAQsC,KAAK,EAAE;YACbwE,UAAU,EAAE,OAAO;YACnB+I,YAAY,EAAE,mBAAmB;YACjC1K,QAAQ,EAAE,QAAQ;YAClBI,GAAG,EAAE,CAAC;YACNuK,MAAM,EAAE,GAAG;YACXC,SAAS,EAAE;UACb,CAAE;UAAA9M,QAAA,eACAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,MAAM;cACboL,OAAO,EAAE,QAAQ;cACjBnL,MAAM,EAAE,MAAM;cACdjC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAEAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,QAAQ;gBACbsK,QAAQ,EAAE;cACZ,CAAE;cAAAhN,QAAA,gBACAjD,OAAA;gBACE4D,GAAG,EAAC,iBAAiB;gBACrBvB,GAAG,EAAC,WAAW;gBACfC,KAAK,EAAE;kBACLsC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,SAAS,EAAE;gBACb;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIsC,KAAK,EAAE;oBACT4N,MAAM,EAAE,CAAC;oBACT9M,QAAQ,EAAE,QAAQ;oBAClBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE,SAAS;oBAChBmN,UAAU,EAAE;kBACd,CAAE;kBAAAlN,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAGsC,KAAK,EAAE;oBACR4N,MAAM,EAAE,CAAC;oBACT9M,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVsN,IAAI,EAAE,CAAC;gBACPQ,QAAQ,EAAE,OAAO;gBACjBF,MAAM,EAAE;cACV,CAAE;cAAAjN,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE6C,QAAQ,EAAE;gBAAW,CAAE;gBAAAlC,QAAA,gBACnCjD,OAAA,CAACtB,MAAM;kBACL2R,IAAI,EAAE,EAAG;kBACT/N,KAAK,EAAE;oBACL6C,QAAQ,EAAE,UAAU;oBACpBK,IAAI,EAAE,MAAM;oBACZD,GAAG,EAAE,KAAK;oBACVM,SAAS,EAAE,kBAAkB;oBAC7B7C,KAAK,EAAE;kBACT;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxD,OAAA;kBACEmP,IAAI,EAAC,MAAM;kBACXmB,WAAW,EAAC,aAAa;kBACzBC,KAAK,EAAEjJ,UAAW;kBAClBkJ,QAAQ,EAAG3M,CAAC,IAAK0D,aAAa,CAAC1D,CAAC,CAAC4I,MAAM,CAAC8D,KAAK,CAAE;kBAC/CjO,KAAK,EAAE;oBACLsC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdmL,OAAO,EAAE,eAAe;oBACxBvM,MAAM,EAAE,mBAAmB;oBAC3BwB,YAAY,EAAE,MAAM;oBACpB6B,UAAU,EAAE,SAAS;oBACrB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBqN,OAAO,EAAE,MAAM;oBACfzL,UAAU,EAAE;kBACd,CAAE;kBACF0L,OAAO,EAAG7M,CAAC,IAAK;oBACdA,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACqO,WAAW,GAAG,SAAS;oBACtC9M,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACwE,UAAU,GAAG,OAAO;oBACnCjD,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACyN,SAAS,GAAG,mCAAmC;kBAChE,CAAE;kBACFa,MAAM,EAAG/M,CAAC,IAAK;oBACbA,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACqO,WAAW,GAAG,SAAS;oBACtC9M,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACwE,UAAU,GAAG,SAAS;oBACrCjD,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACyN,SAAS,GAAG,MAAM;kBACnC;gBAAE;kBAAA1M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXsK,QAAQ,EAAE,OAAO;gBACjBnN,cAAc,EAAE;cAClB,CAAE;cAAAG,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACbqK,OAAO,EAAE,QAAQ;kBACjBlJ,UAAU,EAAE,SAAS;kBACrB7B,YAAY,EAAE,MAAM;kBACpBxB,MAAM,EAAE;gBACV,CAAE;gBAAAR,QAAA,gBACAjD,OAAA;kBACEuQ,KAAK,EAAErJ,cAAe;kBACtBsJ,QAAQ,EAAG3M,CAAC,IAAKsD,iBAAiB,CAACtD,CAAC,CAAC4I,MAAM,CAAC8D,KAAK,CAAE;kBACnDjO,KAAK,EAAE;oBACL0N,OAAO,EAAE,gBAAgB;oBACzBvM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,OAAO;oBACnB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBqN,OAAO,EAAE,MAAM;oBACf1L,MAAM,EAAE,SAAS;oBACjBkL,QAAQ,EAAE;kBACZ,CAAE;kBAAAhN,QAAA,gBAEFjD,OAAA;oBAAQuQ,KAAK,EAAC,EAAE;oBAAAtN,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvCsF,UAAU,CACRsB,MAAM,CAACyG,QAAQ;kBACd;kBACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC1D,QAAQ,CAAC0D,QAAQ,CAACC,IAAI,CACjG,CAAC,CACA9K,GAAG,CAAC6K,QAAQ,iBACX7Q,OAAA;oBAAmCuQ,KAAK,EAAEM,QAAQ,CAACvD,WAAW,CAACC,QAAQ,CAAC,CAAE;oBAAAtK,QAAA,EACvE4N,QAAQ,CAACC;kBAAI,GADHD,QAAQ,CAACvD,WAAW;oBAAAjK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEE,CAAC,eAETxD,OAAA;kBACEuQ,KAAK,EAAEnJ,gBAAiB;kBACxBoJ,QAAQ,EAAG3M,CAAC,IAAKwD,mBAAmB,CAACxD,CAAC,CAAC4I,MAAM,CAAC8D,KAAK,CAAE;kBACrDjO,KAAK,EAAE;oBACL0N,OAAO,EAAE,gBAAgB;oBACzBvM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,OAAO;oBACnB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBqN,OAAO,EAAE,MAAM;oBACf1L,MAAM,EAAE,SAAS;oBACjBkL,QAAQ,EAAE;kBACZ,CAAE;kBAAAhN,QAAA,gBAEFjD,OAAA;oBAAQuQ,KAAK,EAAC,EAAE;oBAAAtN,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnCmG,WAAW,CAAC3D,GAAG,CAAC+K,KAAK,iBACpB/Q,OAAA;oBAAoBuQ,KAAK,EAAEQ,KAAK,CAACxD,QAAQ,CAAC,CAAE;oBAAAtK,QAAA,GAAC,QACrC,EAAC8N,KAAK;kBAAA,GADDA,KAAK;oBAAA1N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EAER,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDpH,OAAA;kBACE8F,OAAO,EAAEA,CAAA,KAAM;oBACbyB,aAAa,CAAC,EAAE,CAAC;oBACjBJ,iBAAiB,CAAC,EAAE,CAAC;oBACrBE,mBAAmB,CAAC,EAAE,CAAC;kBACzB,CAAE;kBACF/E,KAAK,EAAE;oBACL0N,OAAO,EAAE,gBAAgB;oBACzBvM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,SAAS;oBACrB9D,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBqB,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBACFpE,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBAAA7D,QAAA,EACH;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAKNxD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBAEAjD,OAAA,CAAC5B,uBAAuB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG3BxD,OAAA;kBAAKsC,KAAK,EAAE;oBAAE6C,QAAQ,EAAE;kBAAW,CAAE;kBAAC,iBAAc,eAAe;kBAAAlC,QAAA,gBACjEjD,OAAA;oBACE8F,OAAO,EAAEA,CAAA,KAAMiC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;oBACtDxF,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,QAAQ;sBACbqK,OAAO,EAAE,cAAc;sBACvBlJ,UAAU,EAAE,OAAO;sBACnBrD,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpBjC,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBqB,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE,eAAe;sBAC3B+K,SAAS,EAAE;oBACb,CAAE;oBACFtN,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqO,WAAW,GAAG,SAAS;sBAC7C9M,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,mCAAmC;oBACvE,CAAE;oBACFrN,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqO,WAAW,GAAG,SAAS;sBAC7C9M,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,8BAA8B;oBAClE,CAAE;oBAAA9M,QAAA,gBAEFjD,OAAA,CAACH,IAAI;sBAACwQ,IAAI,EAAE;oBAAG;sBAAAhN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClBxD,OAAA;sBAAAiD,QAAA,EAAO,CAAA+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgI,SAAS,KAAI;oBAAS;sBAAA3N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClDxD,OAAA,CAACJ,WAAW;sBAACyQ,IAAI,EAAE,EAAG;sBAAC/N,KAAK,EAAE;wBAC5BuD,SAAS,EAAEiC,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;wBAC/D9C,UAAU,EAAE;sBACd;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EAGRsE,gBAAgB,iBACf9H,OAAA;oBAAKsC,KAAK,EAAE;sBACV6C,QAAQ,EAAE,UAAU;sBACpBI,GAAG,EAAE,MAAM;sBACXE,KAAK,EAAE,CAAC;sBACR9B,SAAS,EAAE,QAAQ;sBACnBmD,UAAU,EAAE,OAAO;sBACnBrD,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpB8K,SAAS,EAAE,iCAAiC;sBAC5CE,QAAQ,EAAE,OAAO;sBACjBH,MAAM,EAAE,IAAI;sBACZ1K,QAAQ,EAAE;oBACZ,CAAE;oBAAAnC,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACV0N,OAAO,EAAE,cAAc;wBACvBH,YAAY,EAAE,mBAAmB;wBACjC/I,UAAU,EAAE;sBACd,CAAE;sBAAA7D,QAAA,gBACAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVc,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBV,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACC+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgI,SAAS,EAAC,GAAC,EAAChI,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiI,QAAQ;sBAAA;wBAAA5N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVc,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,EACC+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkI;sBAAK;wBAAA7N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAE0N,OAAO,EAAE;sBAAW,CAAE;sBAAA/M,QAAA,gBAClCjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM;0BACbS,QAAQ,CAAC,oBAAoB,CAAC;0BAC9BwB,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFzF,KAAK,EAAE;0BACLsC,KAAK,EAAE,MAAM;0BACbhC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,SAAS;0BACdqK,OAAO,EAAE,cAAc;0BACvBlJ,UAAU,EAAE,aAAa;0BACzBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,UAAU;0BACpB2B,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;wBAC9C,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;wBAClD,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAACvB,IAAI;0BAAC4R,IAAI,EAAE;wBAAG;0BAAAhN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,aAEpB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAETxD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM;0BACbwG,YAAY,CAAC,CAAC;0BACdvE,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFzF,KAAK,EAAE;0BACLsC,KAAK,EAAE,MAAM;0BACbhC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,SAAS;0BACdqK,OAAO,EAAE,cAAc;0BACvBlJ,UAAU,EAAE,aAAa;0BACzBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,UAAU;0BACpB2B,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;wBAC9C,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;wBAClD,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAACF,MAAM;0BAACuQ,IAAI,EAAE;wBAAG;0BAAAhN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAEtB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTxD,OAAA;UAAKsC,KAAK,EAAE;YACVsN,IAAI,EAAE,CAAC;YACPhN,OAAO,EAAE,MAAM;YACf+C,GAAG,EAAE,QAAQ;YACbqK,OAAO,EAAE,aAAa;YACtBlJ,UAAU,EAAE,aAAa;YACzBjE,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACduM,UAAU,EAAE;YACd,CAAE;YAAAlO,QAAA,eACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,OAAO;gBACnB7B,YAAY,EAAE,MAAM;gBACpBxB,MAAM,EAAE,mBAAmB;gBAC3B2B,QAAQ,EAAE,QAAQ;gBAClBD,QAAQ,EAAE,QAAQ;gBAClBI,GAAG,EAAE,MAAM;gBACXwK,SAAS,EAAE;cACb,CAAE;cAAA9M,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACV0N,OAAO,EAAE,sBAAsB;kBAC/BH,YAAY,EAAE,mBAAmB;kBACjC/I,UAAU,EAAE;gBACd,CAAE;gBAAA7D,QAAA,gBACAjD,OAAA;kBAAKsC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB8C,GAAG,EAAE,SAAS;oBACdxC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAjD,OAAA,CAACrB,GAAG;oBAAC0R,IAAI,EAAE,EAAG;oBAAC/N,KAAK,EAAE;sBAAEU,KAAK,EAAE;oBAAQ;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CxD,OAAA;oBAAIsC,KAAK,EAAE;sBACT4N,MAAM,EAAE,CAAC;sBACT9M,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNxD,OAAA;kBAAGsC,KAAK,EAAE;oBACR4N,MAAM,EAAE,CAAC;oBACT9M,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNxD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE0N,OAAO,EAAE;gBAAU,CAAE;gBAAA/M,QAAA,EAChC2F,mBAAmB,CAACxE,MAAM,GAAG,CAAC,gBAC7BpE,OAAA,CAAAE,SAAA;kBAAA+C,QAAA,GACG2F,mBAAmB,CAACtE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAAC4F,YAAY,EAAEnH,KAAK,KAAK;oBAC5D;oBACA,IAAI2M,MAAgC;oBACpC,IAAIxF,YAAY,CAACyF,QAAQ,EAAE;sBACzBD,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC9C,CAAC,MAAM;sBACL,MAAMxK,YAAY,GAAG,CAACgF,YAAY,CAAC0F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;;sBAE5E;sBACA,MAAMC,cAAwD,GAAG;wBAC/D,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC7C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBAC7C,CAAC;sBAEDJ,MAAM,GAAGI,cAAc,CAAC5K,YAAY,CAAC,IAAI4K,cAAc,CAAC,SAAS,CAAC;oBACpE;oBAEA,oBACExR,OAAA;sBAEEsC,KAAK,EAAE;wBACL0N,OAAO,EAAE,SAAS;wBAClBlJ,UAAU,EAAE,2BAA2BsK,MAAM,CAAC,CAAC,CAAC,QAAQA,MAAM,CAAC,CAAC,CAAC,QAAQ;wBACzEnM,YAAY,EAAE,MAAM;wBACpBxB,MAAM,EAAE,aAAa2N,MAAM,CAAC,CAAC,CAAC,EAAE;wBAChCjO,YAAY,EAAE,SAAS;wBACvB4B,MAAM,EAAE,SAAS;wBACjBC,UAAU,EAAE;sBACd,CAAE;sBACFvC,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;wBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,cAAcqB,MAAM,CAAC,CAAC,CAAC,IAAI;sBAC/D,CAAE;sBACF1O,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;wBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,MAAM;sBAC1C,CAAE;sBACFjK,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC+D,YAAY,CAAE;sBAAA3I,QAAA,eAEnDjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,YAAY;0BACxB8C,GAAG,EAAE;wBACP,CAAE;wBAAA1C,QAAA,gBACAjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdiC,UAAU,EAAEsK,MAAM,CAAC,CAAC,CAAC;4BACrBnM,YAAY,EAAE,KAAK;4BACnBtB,SAAS,EAAE,QAAQ;4BACnBwN,UAAU,EAAE,CAAC;4BACbpB,SAAS,EAAE,aAAaqB,MAAM,CAAC,CAAC,CAAC;0BACnC;wBAAE;0BAAA/N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLxD,OAAA;0BAAKsC,KAAK,EAAE;4BAAEsN,IAAI,EAAE;0BAAE,CAAE;0BAAA3M,QAAA,gBACtBjD,OAAA;4BAAIsC,KAAK,EAAE;8BACT4N,MAAM,EAAE,cAAc;8BACtB9M,QAAQ,EAAE,UAAU;8BACpBM,UAAU,EAAE,KAAK;8BACjBV,KAAK,EAAEoO,MAAM,CAAC,CAAC,CAAC;8BAChBjB,UAAU,EAAE;4BACd,CAAE;4BAAAlN,QAAA,EACC2I,YAAY,CAACqB;0BAAK;4BAAA5J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACLxD,OAAA;4BAAGsC,KAAK,EAAE;8BACR4N,MAAM,EAAE,cAAc;8BACtB9M,QAAQ,EAAE,QAAQ;8BAClBJ,KAAK,EAAEoO,MAAM,CAAC,CAAC,CAAC;8BAChBjB,UAAU,EAAE;4BACd,CAAE;4BAAAlN,QAAA,EACC2I,YAAY,CAACwB,OAAO,CAAChJ,MAAM,GAAG,EAAE,GAC7B,GAAGwH,YAAY,CAACwB,OAAO,CAACqE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7C7F,YAAY,CAACwB;0BAAO;4BAAA/J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACJxD,OAAA;4BAAKsC,KAAK,EAAE;8BACVM,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE,QAAQ;8BACbvC,QAAQ,EAAE,SAAS;8BACnBJ,KAAK,EAAEoO,MAAM,CAAC,CAAC;4BACjB,CAAE;4BAAAnO,QAAA,gBACAjD,OAAA,CAACpB,QAAQ;8BAACyR,IAAI,EAAE;4BAAG;8BAAAhN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACtBxD,OAAA;8BAAAiD,QAAA,EAAO,IAAI4K,IAAI,CAACjC,YAAY,CAACyD,UAAU,CAAC,CAACqC,kBAAkB,CAAC;4BAAC;8BAAArO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAjEDoI,YAAY,CAACC,eAAe;sBAAAxI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkE9B,CAAC;kBAEV,CAAC,CAAC,EAGDoF,mBAAmB,CAACxE,MAAM,GAAG,CAAC,iBAC7BpE,OAAA;oBAAQsC,KAAK,EAAE;sBACbsC,KAAK,EAAE,MAAM;sBACboL,OAAO,EAAE,SAAS;sBAClBvM,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpB6B,UAAU,EAAE,mDAAmD;sBAC/D9D,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBqB,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE;oBACd,CAAE;oBACFvC,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,mDAAmD;sBACtFjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqO,WAAW,GAAG,SAAS;sBAC7C9M,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACtD,CAAE;oBACFnD,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,mDAAmD;sBACtFjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqO,WAAW,GAAG,SAAS;sBAC7C9M,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACnD,CAAE;oBAAA5C,QAAA,GAAC,wBACW,EAAC2F,mBAAmB,CAACxE,MAAM,EAAC,oBAC1C;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,eACD,CAAC,gBAEHxD,OAAA;kBAAKsC,KAAK,EAAE;oBACV0N,OAAO,EAAE,WAAW;oBACpB9M,SAAS,EAAE,QAAQ;oBACnBF,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,gBACAjD,OAAA,CAACrB,GAAG;oBAAC0R,IAAI,EAAE,EAAG;oBAAC/N,KAAK,EAAE;sBAAEa,YAAY,EAAE,QAAQ;sBAAEwO,OAAO,EAAE;oBAAI;kBAAE;oBAAAtO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClExD,OAAA;oBAAGsC,KAAK,EAAE;sBAAE4N,MAAM,EAAE,CAAC;sBAAE9M,QAAQ,EAAE;oBAAW,CAAE;oBAAAH,QAAA,EAAC;kBAE/C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAEsN,IAAI,EAAE,CAAC;cAAEK,QAAQ,EAAE;YAAE,CAAE;YAAAhN,QAAA,GAEpC,CAACzC,OAAO,IAAIgI,eAAe,kBAC1BxI,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,QAAQ;gBACxBD,UAAU,EAAE,QAAQ;gBACpBmN,OAAO,EAAE;cACX,CAAE;cAAA/M,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVsC,KAAK,EAAE,QAAQ;kBACfC,MAAM,EAAE,QAAQ;kBAChBpB,MAAM,EAAE,mBAAmB;kBAC3BmO,SAAS,EAAE,mBAAmB;kBAC9B3M,YAAY,EAAE,KAAK;kBACnB4M,SAAS,EAAE;gBACb;cAAE;gBAAAxO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA,CAAC9C,KAAK,IAAIgI,aAAa,kBACtB1I,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,wBAAwB;gBACpCrD,MAAM,EAAE,kCAAkC;gBAC1CwB,YAAY,EAAE,MAAM;gBACpB+K,OAAO,EAAE,MAAM;gBACf7M,YAAY,EAAE,QAAQ;gBACtBH,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,GACCvC,KAAK,iBAAIV,OAAA;gBAAAiD,QAAA,GAAK,iBAAe,EAACvC,KAAK;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CkF,aAAa,iBAAI1I,OAAA;gBAAAiD,QAAA,GAAK,YAAU,EAACyF,aAAa;cAAA;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACgI,eAAe,IAAIuG,oBAAoB,CAAC3K,MAAM,KAAK,CAAC,IAAI4K,aAAa,CAAC5K,MAAM,KAAK,CAAC,iBAC9FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,0BAA0B;gBACtC7B,YAAY,EAAE,MAAM;gBACpB+K,OAAO,EAAE,MAAM;gBACf9M,SAAS,EAAE,QAAQ;gBACnBO,MAAM,EAAE,8BAA8B;gBACtCqO,cAAc,EAAE;cAClB,CAAE;cAAA7O,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVa,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,eACAjD,OAAA,CAACjB,MAAM;kBAACsR,IAAI,EAAE,EAAG;kBAACrN,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNxD,OAAA;gBAAIsC,KAAK,EAAE;kBACTU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBwM,MAAM,EAAE;gBACV,CAAE;gBAAAjN,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxD,OAAA;gBAAGsC,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBkN,MAAM,EAAE;gBACV,CAAE;gBAAAjN,QAAA,EACCqE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,iDAAiD,GACjD;cAAmC;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACgI,eAAe,KAAKuG,oBAAoB,CAAC3K,MAAM,GAAG,CAAC,IAAI4K,aAAa,CAAC5K,MAAM,GAAG,CAAC,CAAC,iBAC5FpE,OAAA;cAAKsC,KAAK,EAAE;gBACV8N,QAAQ,EAAE,QAAQ;gBAClBF,MAAM,EAAE,QAAQ;gBAChBtN,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBJ,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,GAEC+L,aAAa,CAAC5K,MAAM,GAAG,CAAC,iBACvBpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACG+L,aAAa,CAAChJ,GAAG,CAACoF,KAAK,iBACtBpL,OAAA;kBAEEsC,KAAK,EAAE;oBACLwE,UAAU,EAAE,2BAA2B;oBACvC7B,YAAY,EAAE,MAAM;oBACpB+K,OAAO,EAAE,QAAQ;oBACjBvM,MAAM,EAAE,8BAA8B;oBACtCqO,cAAc,EAAE,YAAY;oBAC5B/B,SAAS,EAAE,gCAAgC;oBAC3C/K,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBACFrN,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBAAA9M,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,MAAMgE,eAAe,GAAG,CAACmE,KAAK,CAACkG,aAAa,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;sBAC7E,MAAMQ,YAAY,GAAG/K,mBAAmB,CAACC,eAAe,CAAC;sBACzD,MAAM+K,aAAa,GAAGD,YAAY,CAAChL,IAAI;sBAEvC,oBACE/G,OAAA;wBAAKsC,KAAK,EAAE;0BACVsC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdI,YAAY,EAAE,MAAM;0BACpB6B,UAAU,EAAEiL,YAAY,CAACjL,UAAU;0BACnClE,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE;wBAClB,CAAE;wBAAAG,QAAA,eACAjD,OAAA,CAACgS,aAAa;0BAAC3B,IAAI,EAAE,EAAG;0BAACrN,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEsN,IAAI,EAAE;sBAAE,CAAE;sBAAA3M,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,EACC,CAAC,MAAM;0BACN,MAAMgE,eAAe,GAAG,CAACmE,KAAK,CAACkG,aAAa,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;0BAC7E,MAAMQ,YAAY,GAAG/K,mBAAmB,CAACC,eAAe,CAAC;0BACzD,MAAM+K,aAAa,GAAGD,YAAY,CAAChL,IAAI;0BAEvC,oBACE/G,OAAA;4BAAMsC,KAAK,EAAE;8BACXwE,UAAU,EAAEiL,YAAY,CAACjL,UAAU;8BACnC9D,KAAK,EAAE,OAAO;8BACdgN,OAAO,EAAE,iBAAiB;8BAC1B/K,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAACgS,aAAa;8BAAC3B,IAAI,EAAE,EAAG;8BAACrN,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCyD,eAAe;0BAAA;4BAAA5D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC;wBAEX,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAI4K,IAAI,CAACzC,KAAK,CAACiD,UAAU,CAAC,CAACqD,kBAAkB,CAAC,OAAO,EAAE;0BACtDO,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA/O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACT4N,MAAM,EAAE,eAAe;sBACvBlN,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjByM,UAAU,EAAE;oBACd,CAAE;oBAAAlN,QAAA,EACCmI,KAAK,CAAC6B;kBAAK;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAGJ,CAAC,MAAM;oBACN;oBACA,MAAM6O,cAAwB,GAAG,EAAE;oBAEnC,IAAKjH,KAAK,CAASpH,MAAM,IAAKoH,KAAK,CAASpH,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;sBAC5DgH,KAAK,CAASpH,MAAM,CAACsO,OAAO,CAAEC,GAAQ,IAAK;wBAC1C,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAMlS,QAAQ,GAAGhC,WAAW,CAACiU,GAAG,CAACC,SAAS,CAAC;0BAC3C,IAAIlS,QAAQ,EAAE;4BACZ+R,cAAc,CAACI,IAAI,CAACnS,QAAQ,CAAC;0BAC/B;wBACF;sBACF,CAAC,CAAC;oBACJ;oBAEA,OAAO+R,cAAc,CAACjO,MAAM,GAAG,CAAC,gBAC9BpE,OAAA;sBAAKsC,KAAK,EAAE;wBAAEa,YAAY,EAAE;sBAAO,CAAE;sBAAAF,QAAA,eACnCjD,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEqO,cAAc,CAACjI,MAAM,CAACsI,OAAO,CAAc;wBACnDzO,SAAS,EAAEmH,KAAK,CAAC6B,KAAM;wBACvB/I,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB,MAAMkO,cAAc,GAAGN,cAAc,CAACjI,MAAM,CAACsI,OAAO,CAAa;0BACjE1I,YAAY,CAAC2I,cAAc,EAAElO,KAAK,CAAC;wBACrC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACJ,IAAI;kBACV,CAAC,EAAE,CAAC,EAEH4H,KAAK,CAACuC,WAAW,iBAChB3N,OAAA;oBAAGsC,KAAK,EAAE;sBACR4N,MAAM,EAAE,YAAY;sBACpBlN,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnB+M,UAAU,EAAE;oBACd,CAAE;oBAAAlN,QAAA,EACCmI,KAAK,CAACuC;kBAAW;oBAAAtK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACJ,eAGDxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/B8P,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAA3O,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACX3C,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,gBACAjD,OAAA;wBAAMsC,KAAK,EAAE;0BAAEM,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAE8C,GAAG,EAAE;wBAAU,CAAE;wBAAA1C,QAAA,gBACrEjD,OAAA,CAAChB,MAAM;0BAACqR,IAAI,EAAE,EAAG;0BAACrN,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACN4H,KAAK,CAACoD,QAAQ,IAAIpD,KAAK,CAACoD,QAAQ,KAAKpD,KAAK,CAACiD,UAAU,iBACpDrO,OAAA;wBAAAiD,QAAA,GAAM,QACE,EAAC,IAAI4K,IAAI,CAACzC,KAAK,CAACoD,QAAQ,CAAC,CAACkD,kBAAkB,CAAC,CAAC;sBAAA;wBAAArO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNxD,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjBiP,UAAU,EAAE,MAAM;wBAClBhB,SAAS,EAAE,8BAA8B;wBACzChP,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE;sBACP,CAAE;sBAAA1C,QAAA,gBAEAjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAMmG,wBAAwB,CAACb,KAAK,CAAE;wBAC/C9I,KAAK,EAAE;0BACLM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbmB,UAAU,EAAE,MAAM;0BAClBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAGoI,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG,SAAS;0BAC9DnH,MAAM,EAAE,SAAS;0BACjBiL,OAAO,EAAE,QAAQ;0BACjB/K,YAAY,EAAE,KAAK;0BACnBD,UAAU,EAAE,eAAe;0BAC3B5B,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE;wBACd,CAAE;wBACFjB,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,qBAAqB;wBAC1D,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,MAAM;wBAC3C,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAAClB,KAAK;0BACJuR,IAAI,EAAE,EAAG;0BACTwC,IAAI,EAAGzH,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG;wBAAO;0BAAA7I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,eACFxD,OAAA;0BAAAiD,QAAA,EAAQmI,KAAK,CAASiB,cAAc,IAAI;wBAAC;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,EAGP4H,KAAK,CAAS0H,cAAc,iBAC5B9S,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM6B,uBAAuB,CACpCD,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;wBACFhJ,KAAK,EAAE;0BACLM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbmB,UAAU,EAAE,MAAM;0BAClBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;0BACzEvG,MAAM,EAAE,SAAS;0BACjBiL,OAAO,EAAE,QAAQ;0BACjB/K,YAAY,EAAE,KAAK;0BACnBD,UAAU,EAAE,eAAe;0BAC3B5B,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE;wBACd,CAAE;wBACFjB,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,qBAAqB;0BACxDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClG,CAAE;wBACF5I,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,MAAM;0BACzCjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClG,CAAE;wBAAArI,QAAA,gBAEFjD,OAAA,CAACnB,aAAa;0BAACwR,IAAI,EAAE;wBAAG;0BAAAhN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3BxD,OAAA;0BAAAiD,QAAA,EAAQmI,KAAK,CAAS2H,aAAa,IAAI;wBAAC;0BAAA1P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,EAGLkE,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,IAAKF,KAAK,CAAS0H,cAAc,iBAC1E9S,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjBiP,UAAU,EAAE,MAAM;wBAClBhB,SAAS,EAAE;sBACb,CAAE;sBAAA3O,QAAA,eACAjD,OAAA,CAAC7B,cAAc;wBACb6U,UAAU,EAAE5H,KAAK,CAACE,WAAY;wBAC9B2H,aAAa,EAAG7H,KAAK,CAAS0H,cAAe;wBAC7CI,aAAa,EAAElK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAG;wBAC/BoH,eAAe,EAAC;sBAAS;wBAAA9P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAnQD,SAAS4H,KAAK,CAACE,WAAW,EAAE;kBAAAjI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoQ9B,CACN;cAAC,gBACF,CACH,EAGAuL,oBAAoB,CAAC3K,MAAM,GAAG,CAAC,iBAC9BpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACG8L,oBAAoB,CAAC/I,GAAG,CAAC4F,YAAY,iBACpC5L,OAAA;kBAEE+L,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;kBACnDtJ,SAAS,EAAEiE,kBAAkB,IAAIE,YAAY,KAAK,gBAAgBkF,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;kBAC9IvJ,KAAK,EAAE;oBACLwE,UAAU,EAAE,2BAA2B;oBACvC7B,YAAY,EAAE,MAAM;oBACpB+K,OAAO,EAAE,QAAQ;oBACjBvM,MAAM,EAAEmI,YAAY,CAACyF,QAAQ,GACzB,kCAAkC,GAClC,8BAA8B;oBAClCS,cAAc,EAAE,YAAY;oBAC5B/B,SAAS,EAAEnE,YAAY,CAACyF,QAAQ,GAC5B,oCAAoC,GACpC,gCAAgC;oBACpCrM,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAGnE,YAAY,CAACyF,QAAQ,GACnD,oCAAoC,GACpC,gCAAgC;kBACtC,CAAE;kBACF3O,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACyN,SAAS,GAAGnE,YAAY,CAACyF,QAAQ,GACnD,oCAAoC,GACpC,gCAAgC;kBACtC,CAAE;kBAAApO,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,IAAI2I,YAAY,CAACyF,QAAQ,EAAE;wBACzB,oBACErR,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdiC,UAAU,EAAE,mDAAmD;4BAC/D7B,YAAY,EAAE,MAAM;4BACpBrC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE,QAAQ;4BACxBqO,UAAU,EAAE;0BACd,CAAE;0BAAAlO,QAAA,eACAjD,OAAA,CAACZ,aAAa;4BAACiR,IAAI,EAAE,EAAG;4BAACrN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAEV,CAAC,MAAM;wBACL,MAAMoD,YAAY,GAAG,CAACgF,YAAY,CAAC0F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;wBAC5E,MAAM6B,aAAa,GAAGzM,gBAAgB,CAACC,YAAY,CAAC;wBACpD,MAAMoL,aAAa,GAAGoB,aAAa,CAACrM,IAAI;wBAExC,oBACE/G,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdI,YAAY,EAAE,MAAM;4BACpB6B,UAAU,EAAEsM,aAAa,CAACtM,UAAU;4BACpClE,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE;0BAClB,CAAE;0BAAAG,QAAA,eACAjD,OAAA,CAACgS,aAAa;4BAAC3B,IAAI,EAAE,EAAG;4BAACrN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAEV;oBACF,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEsN,IAAI,EAAE;sBAAE,CAAE;sBAAA3M,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,GACC,CAAC,MAAM;0BACN,IAAI2I,YAAY,CAACyF,QAAQ,EAAE;4BACzB,oBACErR,OAAA;8BAAMsC,KAAK,EAAE;gCACXwE,UAAU,EAAE,mDAAmD;gCAC/D9D,KAAK,EAAE,OAAO;gCACdI,QAAQ,EAAE,SAAS;gCACnBM,UAAU,EAAE,KAAK;gCACjBsM,OAAO,EAAE,iBAAiB;gCAC1B/K,YAAY,EAAE,MAAM;gCACpBoO,aAAa,EAAE,WAAW;gCAC1BC,aAAa,EAAE,OAAO;gCACtB1Q,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB8C,GAAG,EAAE;8BACP,CAAE;8BAAA1C,QAAA,gBACAjD,OAAA,CAACZ,aAAa;gCAACiR,IAAI,EAAE,EAAG;gCAACrN,KAAK,EAAC;8BAAO;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,SAE3C;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAEX,CAAC,MAAM;4BACL,MAAMoD,YAAY,GAAG,CAACgF,YAAY,CAAC0F,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;4BAC5E,MAAM6B,aAAa,GAAGzM,gBAAgB,CAACC,YAAY,CAAC;4BACpD,MAAMoL,aAAa,GAAGoB,aAAa,CAACrM,IAAI;4BAExC,oBACE/G,OAAA;8BAAMsC,KAAK,EAAE;gCACXwE,UAAU,EAAEsM,aAAa,CAACtM,UAAU;gCACpC9D,KAAK,EAAE,OAAO;gCACdgN,OAAO,EAAE,iBAAiB;gCAC1B/K,YAAY,EAAE,MAAM;gCACpB7B,QAAQ,EAAE,SAAS;gCACnBM,UAAU,EAAE,KAAK;gCACjBd,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB8C,GAAG,EAAE;8BACP,CAAE;8BAAA1C,QAAA,gBACAjD,OAAA,CAACgS,aAAa;gCAAC3B,IAAI,EAAE,EAAG;gCAACrN,KAAK,EAAC;8BAAO;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACxCoD,YAAY;4BAAA;8BAAAvD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC;0BAEX;wBACF,CAAC,EAAE,CAAC,EACHoI,YAAY,CAACtB,SAAS,iBACrBtK,OAAA;0BAAMsC,KAAK,EAAE;4BACXwE,UAAU,EAAE,mDAAmD;4BAC/D9D,KAAK,EAAE,OAAO;4BACdgN,OAAO,EAAE,iBAAiB;4BAC1B/K,YAAY,EAAE,MAAM;4BACpB7B,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjBd,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE;0BACP,CAAE;0BAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;4BAAC0R,IAAI,EAAE,EAAG;4BAACrN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,UAEjC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,GAAC,KACE,EAAC2I,YAAY,CAAC2H,WAAW,EAAC,UAAG,EAAC3H,YAAY,CAAC4H,YAAY,GAAG,IAAI3F,IAAI,CAACjC,YAAY,CAAC4H,YAAY,CAAC,CAAC9B,kBAAkB,CAAC,OAAO,EAAE;0BAC3HO,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE,SAAS;0BACdqB,IAAI,EAAE,SAAS;0BACfC,MAAM,EAAE;wBACV,CAAC,CAAC,GAAG,cAAc;sBAAA;wBAAArQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACT4N,MAAM,EAAE,eAAe;sBACvBlN,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjByM,UAAU,EAAE;oBACd,CAAE;oBAAAlN,QAAA,EACC2I,YAAY,CAACqB;kBAAK;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,EAGJ,CAAC,MAAM;oBACN;oBACA,MAAMyG,SAAmB,GAAG,EAAE;;oBAE9B;oBACA,IAAI2B,YAAY,CAACJ,WAAW,IAAII,YAAY,CAACJ,WAAW,CAACpH,MAAM,GAAG,CAAC,EAAE;sBACnEwH,YAAY,CAACJ,WAAW,CAAC8G,OAAO,CAAEC,GAA2B,IAAK;wBAChE,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAM3R,OAAO,GAAGvC,WAAW,CAACiU,GAAG,CAACC,SAAS,CAAC;0BAC1C,IAAI3R,OAAO,EAAE;4BACXoJ,SAAS,CAACwI,IAAI,CAAC5R,OAAO,CAAC;0BACzB;wBACF;sBACF,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIoJ,SAAS,CAAC7F,MAAM,KAAK,CAAC,KAAKwH,YAAY,CAAC+H,SAAS,IAAI/H,YAAY,CAACgI,UAAU,CAAC,EAAE;sBACjF,MAAMC,SAAS,GAAGvV,WAAW,CAACsN,YAAY,CAAC+H,SAAS,IAAI/H,YAAY,CAACgI,UAAU,CAAC;sBAChF,IAAIC,SAAS,EAAE;wBACb5J,SAAS,CAACwI,IAAI,CAACoB,SAAS,CAAC;sBAC3B;oBACF;oBAEA,OAAO5J,SAAS,CAAC7F,MAAM,GAAG,CAAC,gBACzBpE,OAAA,CAAC+D,oBAAoB;sBACnBC,MAAM,EAAEiG,SAAS,CAACG,MAAM,CAACsI,OAAO,CAAc;sBAC9CzO,SAAS,EAAE2H,YAAY,CAACqB,KAAM;sBAC9B/I,UAAU,EAAE,CAAE;sBACdC,YAAY,EAAGM,KAAK,IAAK;wBACvB,MAAMkO,cAAc,GAAG1I,SAAS,CAACG,MAAM,CAACsI,OAAO,CAAa;wBAC5D1I,YAAY,CAAC2I,cAAc,EAAElO,KAAK,CAAC;sBACrC;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GACA,IAAI;kBACV,CAAC,EAAE,CAAC,eAEJxD,OAAA;oBAAKsC,KAAK,EAAE;sBACV4N,MAAM,EAAE,cAAc;sBACtBlN,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnB+M,UAAU,EAAE;oBACd,CAAE;oBACF2D,uBAAuB,EAAE;sBAAEC,MAAM,EAAEnI,YAAY,CAACwB;oBAAQ;kBAAE;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eAGFxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/B8P,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAA3O,QAAA,eACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE;sBACP,CAAE;sBAAA1C,QAAA,gBAEAjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM6F,gBAAgB,CAACC,YAAY,CAAE;wBAC9CtJ,KAAK,EAAE;0BACLwE,UAAU,EAAE8E,YAAY,CAACE,aAAa,GAAG,wBAAwB,GAAG,aAAa;0BACjF9I,KAAK,EAAE4I,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;0BACzDrI,MAAM,EAAE,MAAM;0BACdwB,YAAY,EAAE,KAAK;0BACnB+K,OAAO,EAAE,aAAa;0BACtB5M,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBqB,MAAM,EAAE,SAAS;0BACjBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbX,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnB,IAAI,CAAC+H,YAAY,CAACE,aAAa,EAAE;4BAC/BjI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,wBAAwB;4BAC3DjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBACFN,YAAY,EAAGmB,CAAC,IAAK;0BACnB,IAAI,CAAC+H,YAAY,CAACE,aAAa,EAAE;4BAC/BjI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;4BAChDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBAAAC,QAAA,gBAEFjD,OAAA,CAAClB,KAAK;0BACJuR,IAAI,EAAE,EAAG;0BACTrN,KAAK,EAAE4I,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAU;0BAC1D+G,IAAI,EAAEjH,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;wBAAO;0BAAAzI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC,eACFxD,OAAA;0BAAAiD,QAAA,EAAO2I,YAAY,CAACS,cAAc,IAAI;wBAAC;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC,EAGRoI,YAAY,CAACkH,cAAc,iBAC1B9S,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM2B,eAAe,CAC5BD,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;wBACFvJ,KAAK,EAAE;0BACLwE,UAAU,EAAEU,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,yBAAyB,GAAG,aAAa;0BACrG7I,KAAK,EAAEwE,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;0BAC5EpI,MAAM,EAAE,MAAM;0BACdwB,YAAY,EAAE,KAAK;0BACnB+K,OAAO,EAAE,aAAa;0BACtB5M,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBqB,MAAM,EAAE,SAAS;0BACjBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbX,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnB,IAAI2D,YAAY,KAAKoE,YAAY,CAACC,eAAe,EAAE;4BACjDhI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,yBAAyB;4BAC5DjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBACFN,YAAY,EAAGmB,CAAC,IAAK;0BACnB,IAAI2D,YAAY,KAAKoE,YAAY,CAACC,eAAe,EAAE;4BACjDhI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;4BAChDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBAAAC,QAAA,gBAEFjD,OAAA,CAACnB,aAAa;0BAACwR,IAAI,EAAE,EAAG;0BAACrN,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3CxD,OAAA;0BAAAiD,QAAA,EAAO2I,YAAY,CAACmH,aAAa,IAAI;wBAAC;0BAAA1P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGH,CAAC,EAGLgE,YAAY,KAAKoE,YAAY,CAACC,eAAe,IAAID,YAAY,CAACkH,cAAc,iBAC3E9S,OAAA;oBAAKsC,KAAK,EAAE;sBACVqB,SAAS,EAAE,MAAM;sBACjBiP,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAA3O,QAAA,eAIAjD,OAAA,CAAC7B,cAAc;sBACb6V,cAAc,EAAEpI,YAAY,CAACC,eAAgB;sBAC7CoH,aAAa,EAAErH,YAAY,CAACkH,cAAe;sBAC3CI,aAAa,EAAElK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAG;sBAC/BoH,eAAe,EAAC;oBAAS;sBAAA9P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA,GApUI,gBAAgBoI,YAAY,CAACC,eAAe,EAAE;kBAAAxI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqUhD,CACN;cAAC,gBACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLoE,kBAAkB,iBACjB5H,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,OAAO;UACjBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBgN,MAAM,EAAE,IAAI;UACZE,OAAO,EAAE;QACX,CAAE;QACFlK,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC,IAAI,CAAE;QAAA5E,QAAA,eAEzCjD,OAAA;UAAKsC,KAAK,EAAE;YACVS,eAAe,EAAE,OAAO;YACxBkC,YAAY,EAAE,MAAM;YACpBmL,QAAQ,EAAE,OAAO;YACjBxL,KAAK,EAAE,MAAM;YACbqP,SAAS,EAAE,MAAM;YACjB7O,QAAQ,EAAE,MAAM;YAChB2K,SAAS,EAAE;UACb,CAAE;UACFjK,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACqQ,eAAe,CAAC,CAAE;UAAAjR,QAAA,gBAGlCjD,OAAA;YAAKsC,KAAK,EAAE;cACV0N,OAAO,EAAE,QAAQ;cACjBH,YAAY,EAAE,mBAAmB;cACjCjN,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;gBAAC0R,IAAI,EAAE,EAAG;gBAAC/N,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAU;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CxD,OAAA;gBAAIsC,KAAK,EAAE;kBACT4N,MAAM,EAAE,CAAC;kBACT9M,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNxD,OAAA;cACE8F,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC,IAAI,CAAE;cAC3CvF,KAAK,EAAE;gBACLwE,UAAU,EAAE,MAAM;gBAClBrD,MAAM,EAAE,MAAM;gBACdL,QAAQ,EAAE,QAAQ;gBAClBJ,KAAK,EAAE,SAAS;gBAChB+B,MAAM,EAAE,SAAS;gBACjBiL,OAAO,EAAE,SAAS;gBAClB/K,YAAY,EAAE,KAAK;gBACnBD,UAAU,EAAE;cACd,CAAE;cACFvC,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cACFN,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cAAAC,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAE0N,OAAO,EAAE;YAAS,CAAE;YAAA/M,QAAA,gBAChCjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,SAAS;gBACdxC,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,GACC,CAAC,MAAM;gBACN,IAAI2E,kBAAkB,CAACyJ,QAAQ,EAAE;kBAC/B,oBACErR,OAAA;oBAAMsC,KAAK,EAAE;sBACXwE,UAAU,EAAE,mDAAmD;sBAC/D9D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBsM,OAAO,EAAE,iBAAiB;sBAC1B/K,YAAY,EAAE,MAAM;sBACpBoO,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtB1Q,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAACZ,aAAa;sBAACiR,IAAI,EAAE,EAAG;sBAACrN,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAEX,CAAC,MAAM;kBACL,MAAMoD,YAAY,GAAG,CAACgB,kBAAkB,CAAC0J,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAClF,MAAM6B,aAAa,GAAGzM,gBAAgB,CAACC,YAAY,CAAC;kBACpD,MAAMoL,aAAa,GAAGoB,aAAa,CAACrM,IAAI;kBAExC,oBACE/G,OAAA;oBAAMsC,KAAK,EAAE;sBACXwE,UAAU,EAAEsM,aAAa,CAACtM,UAAU;sBACpC9D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBsM,OAAO,EAAE,iBAAiB;sBAC1B/K,YAAY,EAAE,MAAM;sBACpBoO,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtB1Q,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAACgS,aAAa;sBAAC3B,IAAI,EAAE,EAAG;sBAACrN,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACxCoD,YAAY;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAEX;cACF,CAAC,EAAE,CAAC,eAEJxD,OAAA;gBAAMsC,KAAK,EAAE;kBACXwE,UAAU,EAAE,mDAAmD;kBAC/D9D,KAAK,EAAE,OAAO;kBACdgN,OAAO,EAAE,iBAAiB;kBAC1B/K,YAAY,EAAE,MAAM;kBACpB7B,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBd,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;kBAAC0R,IAAI,EAAE;gBAAG;kBAAAhN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxD,OAAA;cAAIsC,KAAK,EAAE;gBACT4N,MAAM,EAAE,YAAY;gBACpB9M,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE,SAAS;gBAChBmN,UAAU,EAAE;cACd,CAAE;cAAAlN,QAAA,EACC2E,kBAAkB,CAACqF;YAAK;cAAA5J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAELxD,OAAA;cAAKsC,KAAK,EAAE;gBACVU,KAAK,EAAE,SAAS;gBAChBI,QAAQ,EAAE,MAAM;gBAChB+M,UAAU,EAAE,KAAK;gBACjBhN,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,EACC2E,kBAAkB,CAACwF;YAAO;cAAA/J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EAGLoE,kBAAkB,CAAC4D,WAAW,IAAI5D,kBAAkB,CAAC4D,WAAW,CAACpH,MAAM,GAAG,CAAC,iBAC1EpE,OAAA;cAAKsC,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,eACrCjD,OAAA,CAAC+D,oBAAoB;gBACnBC,MAAM,EAAE4D,kBAAkB,CAAC4D,WAAW,CAACxF,GAAG,CAAEuM,GAAQ,IAAKjU,WAAW,CAACiU,GAAG,CAACC,SAAS,CAAC,CAAC,CAACpI,MAAM,CAACsI,OAAO,CAAE;gBACrGzO,SAAS,EAAE2D,kBAAkB,CAACqF,KAAM;gBACpC9I,YAAY,EAAGM,KAAK,IAAK;kBACvB,MAAMwF,SAAS,GAAGrC,kBAAkB,CAAC4D,WAAW,CAACxF,GAAG,CAAEuM,GAAQ,IAAKjU,WAAW,CAACiU,GAAG,CAACC,SAAS,CAAC,CAAC,CAACpI,MAAM,CAACsI,OAAO,CAAC;kBAC9G1I,YAAY,CAACC,SAAS,EAAExF,KAAK,CAAC;gBAChC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXvC,QAAQ,EAAE,UAAU;gBACpBJ,KAAK,EAAE,SAAS;gBAChB4P,UAAU,EAAE,MAAM;gBAClBhB,SAAS,EAAE;cACb,CAAE;cAAA3O,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAACpB,QAAQ;kBAACyR,IAAI,EAAE;gBAAG;kBAAAhN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtBxD,OAAA;kBAAAiD,QAAA,GAAM,aAAW,EAAC,IAAI4K,IAAI,CAACjG,kBAAkB,CAACyH,UAAU,CAAC,CAACqC,kBAAkB,CAAC,CAAC;gBAAA;kBAAArO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,EACLoE,kBAAkB,CAAC2L,WAAW,iBAC7BvT,OAAA;gBAAAiD,QAAA,GAAK,MACC,EAAC2E,kBAAkB,CAAC2L,WAAW;cAAA;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxD,OAAA,CAAC3B,aAAa;QACZ2F,MAAM,EAAEkE,cAAe;QACvBgC,YAAY,EAAE9B,oBAAqB;QACnC+L,MAAM,EAAEnM,YAAa;QACrBoM,OAAO,EAAEA,CAAA,KAAMnM,eAAe,CAAC,KAAK,CAAE;QACtChE,SAAS,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC8C,GAAA,CAtzDID,eAAyB;EAAA,QACZxI,WAAW,EAGiCG,qBAAqB,EAwF3DD,aAAa,EAGEE,cAAc,EAUhDH,gBAAgB;AAAA;AAAAuW,GAAA,GAzGhBhO,eAAyB;AAwzD/B,eAAeA,eAAe;AAAC,IAAAvC,EAAA,EAAAsC,GAAA,EAAAiO,GAAA;AAAAC,YAAA,CAAAxQ,EAAA;AAAAwQ,YAAA,CAAAlO,GAAA;AAAAkO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}