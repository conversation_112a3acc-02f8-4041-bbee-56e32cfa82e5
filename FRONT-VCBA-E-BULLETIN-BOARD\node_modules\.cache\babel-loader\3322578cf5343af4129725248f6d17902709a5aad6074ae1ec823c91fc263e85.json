{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.67 19a2 2 0 0 0 1.416-.588l6.154-6.172a6 6 0 0 0-8.49-8.49L5.586 9.914A2 2 0 0 0 5 11.328V18a1 1 0 0 0 1 1z\",\n  key: \"18jl4k\"\n}], [\"path\", {\n  d: \"M16 8 2 22\",\n  key: \"vp34q\"\n}], [\"path\", {\n  d: \"M17.5 15H9\",\n  key: \"1oz8nu\"\n}]];\nconst Feather = createLucideIcon(\"feather\", __iconNode);\nexport { __iconNode, Feather as default };\n//# sourceMappingURL=feather.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}