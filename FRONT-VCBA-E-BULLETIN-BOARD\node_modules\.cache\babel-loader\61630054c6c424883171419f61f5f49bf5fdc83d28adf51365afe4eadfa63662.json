{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 6V4a2 2 0 1 0-4 0v2\",\n  key: \"1aquzs\"\n}], [\"path\", {\n  d: \"M20 15v6a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"1rkj32\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H10\",\n  key: \"18wgow\"\n}], [\"rect\", {\n  x: \"12\",\n  y: \"6\",\n  width: \"8\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"73l30o\"\n}]];\nconst BookLock = createLucideIcon(\"book-lock\", __iconNode);\nexport { __iconNode, BookLock as default };\n//# sourceMappingURL=book-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}