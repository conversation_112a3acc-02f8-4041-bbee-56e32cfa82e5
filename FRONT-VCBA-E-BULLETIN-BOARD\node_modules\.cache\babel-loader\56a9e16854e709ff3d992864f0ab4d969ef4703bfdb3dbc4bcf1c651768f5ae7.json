{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M8 16V8h8\",\n  key: \"19xb1h\"\n}], [\"path\", {\n  d: \"M16 16 8 8\",\n  key: \"1qdy8n\"\n}]];\nconst SquareArrowUpLeft = createLucideIcon(\"square-arrow-up-left\", __iconNode);\nexport { __iconNode, SquareArrowUpLeft as default };\n//# sourceMappingURL=square-arrow-up-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}