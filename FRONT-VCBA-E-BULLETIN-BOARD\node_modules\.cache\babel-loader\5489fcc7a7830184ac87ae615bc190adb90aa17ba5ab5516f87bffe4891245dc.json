{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"11.5\",\n  cy: \"12.5\",\n  r: \"2.5\",\n  key: \"1ea5ju\"\n}], [\"path\", {\n  d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\",\n  key: \"1kt360\"\n}], [\"path\", {\n  d: \"M13.3 14.3 15 16\",\n  key: \"1y4v1n\"\n}]];\nconst FolderSearch2 = createLucideIcon(\"folder-search-2\", __iconNode);\nexport { __iconNode, FolderSearch2 as default };\n//# sourceMappingURL=folder-search-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}