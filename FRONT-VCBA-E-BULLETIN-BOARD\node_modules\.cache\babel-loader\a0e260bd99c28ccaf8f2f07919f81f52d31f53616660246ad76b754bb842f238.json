{"ast": null, "code": "import React from'react';import{useStudentAuth}from'../../contexts/StudentAuthContext';import{Hand,Newspaper,Settings}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentDashboard=()=>{const{user}=useStudentAuth();return/*#__PURE__*/_jsxs(\"div\",{style:{maxWidth:'1200px',margin:'0 auto',padding:'2rem',background:'transparent'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',marginBottom:'2rem',border:'1px solid rgba(34, 197, 94, 0.2)',backdropFilter:'blur(10px)'},children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontSize:'2rem',fontWeight:'bold',color:'#22c55e',marginBottom:'0.5rem'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[\"Welcome back, \",(user===null||user===void 0?void 0:user.firstName)||'Student',\"!\",/*#__PURE__*/_jsx(Hand,{size:24,color:\"#2d5016\"})]})}),/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.8)',fontSize:'1.1rem'},children:\"Stay updated with the latest announcements and events from Villamor College of Business and Arts, Inc.\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(300px, 1fr))',gap:'1.5rem',marginBottom:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',border:'1px solid rgba(34, 197, 94, 0.2)',backdropFilter:'blur(10px)',cursor:'pointer',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-4px)';e.currentTarget.style.boxShadow='0 8px 32px rgba(34, 197, 94, 0.2)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},onClick:()=>window.location.href='/student/newsfeed',children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Newspaper,{size:40,color:\"#22c55e\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#22c55e',marginBottom:'0.5rem'},children:\"Latest News\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.7)'},children:\"Check out the latest announcements and updates from the school\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',border:'1px solid rgba(34, 197, 94, 0.2)',backdropFilter:'blur(10px)',cursor:'pointer',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-4px)';e.currentTarget.style.boxShadow='0 8px 32px rgba(34, 197, 94, 0.2)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},onClick:()=>window.location.href='/student/settings',children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Settings,{size:40,color:\"#22c55e\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#22c55e',marginBottom:'0.5rem'},children:\"Settings\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.7)'},children:\"Manage your profile and notification preferences\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',border:'1px solid rgba(34, 197, 94, 0.2)',backdropFilter:'blur(10px)'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{color:'#22c55e',marginBottom:'1rem',fontSize:'1.5rem'},children:\"Student Information\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(250px, 1fr))',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.6)',fontSize:'0.9rem'},children:\"Full Name\"}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'white',fontWeight:'500'},children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.6)',fontSize:'0.9rem'},children:\"Email\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'white',fontWeight:'500'},children:user===null||user===void 0?void 0:user.email})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.6)',fontSize:'0.9rem'},children:\"Student Number\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'white',fontWeight:'500'},children:(user===null||user===void 0?void 0:user.studentNumber)||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.6)',fontSize:'0.9rem'},children:\"Status\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#22c55e',fontWeight:'500'},children:user!==null&&user!==void 0&&user.isActive?'Active':'Inactive'})]})]})]})]});};export default StudentDashboard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}