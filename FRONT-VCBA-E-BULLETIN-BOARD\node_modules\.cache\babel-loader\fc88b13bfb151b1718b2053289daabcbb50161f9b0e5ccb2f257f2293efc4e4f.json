{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"3\",\n  x2: \"15\",\n  y1: \"22\",\n  y2: \"22\",\n  key: \"xegly4\"\n}], [\"line\", {\n  x1: \"4\",\n  x2: \"14\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"xcnuvu\"\n}], [\"path\", {\n  d: \"M14 22V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v18\",\n  key: \"16j0yd\"\n}], [\"path\", {\n  d: \"M14 13h2a2 2 0 0 1 2 2v2a2 2 0 0 0 2 2a2 2 0 0 0 2-2V9.83a2 2 0 0 0-.59-1.42L18 5\",\n  key: \"7cu91f\"\n}]];\nconst Fuel = createLucideIcon(\"fuel\", __iconNode);\nexport { __iconNode, Fuel as default };\n//# sourceMappingURL=fuel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}