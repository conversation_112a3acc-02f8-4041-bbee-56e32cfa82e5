{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"7.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"kqv944\"\n}], [\"circle\", {\n  cx: \"18.5\",\n  cy: \"5.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"lysivs\"\n}], [\"circle\", {\n  cx: \"11.5\",\n  cy: \"11.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"byv1b8\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"16.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"nkw3mc\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"14.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"1gjh6j\"\n}], [\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}]];\nconst ChartScatter = createLucideIcon(\"chart-scatter\", __iconNode);\nexport { __iconNode, ChartScatter as default };\n//# sourceMappingURL=chart-scatter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}