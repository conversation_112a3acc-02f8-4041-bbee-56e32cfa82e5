{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M21 7.5H3\",\n  key: \"1hm9pq\"\n}], [\"path\", {\n  d: \"M21 12H3\",\n  key: \"2avoz0\"\n}], [\"path\", {\n  d: \"M21 16.5H3\",\n  key: \"n7jzkj\"\n}]];\nconst Rows4 = createLucideIcon(\"rows-4\", __iconNode);\nexport { __iconNode, Rows4 as default };\n//# sourceMappingURL=rows-4.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}