{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"polyline\", {\n  points: \"11 3 11 11 14 8 17 11 17 3\",\n  key: \"1wcwz3\"\n}]];\nconst Album = createLucideIcon(\"album\", __iconNode);\nexport { __iconNode, Album as default };\n//# sourceMappingURL=album.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}