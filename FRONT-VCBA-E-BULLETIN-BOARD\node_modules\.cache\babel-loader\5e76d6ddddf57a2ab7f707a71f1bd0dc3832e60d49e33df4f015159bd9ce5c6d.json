{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5a3 3 0 1 1 3 3m-3-3a3 3 0 1 0-3 3m3-3v1M9 8a3 3 0 1 0 3 3M9 8h1m5 0a3 3 0 1 1-3 3m3-3h-1m-2 3v-1\",\n  key: \"3pnvol\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"1822b1\"\n}], [\"path\", {\n  d: \"M12 10v12\",\n  key: \"6ubwww\"\n}], [\"path\", {\n  d: \"M12 22c4.2 0 7-1.667 7-5-4.2 0-7 1.667-7 5Z\",\n  key: \"9hd38g\"\n}], [\"path\", {\n  d: \"M12 22c-4.2 0-7-1.667-7-5 4.2 0 7 1.667 7 5Z\",\n  key: \"ufn41s\"\n}]];\nconst Flower2 = createLucideIcon(\"flower-2\", __iconNode);\nexport { __iconNode, Flower2 as default };\n//# sourceMappingURL=flower-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}