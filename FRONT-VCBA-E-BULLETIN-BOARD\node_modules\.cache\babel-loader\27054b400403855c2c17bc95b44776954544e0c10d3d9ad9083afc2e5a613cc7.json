{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"18\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"l9bcsi\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"2\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"13hhkx\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"6\",\n  y2: \"2\",\n  key: \"10w3f3\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"18\",\n  key: \"15g9kq\"\n}]];\nconst Crosshair = createLucideIcon(\"crosshair\", __iconNode);\nexport { __iconNode, Crosshair as default };\n//# sourceMappingURL=crosshair.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}