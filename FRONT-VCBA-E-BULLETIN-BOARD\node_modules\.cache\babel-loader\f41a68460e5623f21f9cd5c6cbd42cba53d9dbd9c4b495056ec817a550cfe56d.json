{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 22V2L7 7l10 5\",\n  key: \"1rmf0r\"\n}]];\nconst FlagTriangleLeft = createLucideIcon(\"flag-triangle-left\", __iconNode);\nexport { __iconNode, FlagTriangleLeft as default };\n//# sourceMappingURL=flag-triangle-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}