{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 21a1 1 0 0 0 1-1v-5.35c0-.457.316-.844.727-1.041a4 4 0 0 0-2.134-7.589 5 5 0 0 0-9.186 0 4 4 0 0 0-2.134 7.588c.411.198.727.585.727 1.041V20a1 1 0 0 0 1 1Z\",\n  key: \"1qvrer\"\n}], [\"path\", {\n  d: \"M6 17h12\",\n  key: \"1jwigz\"\n}]];\nconst ChefHat = createLucideIcon(\"chef-hat\", __iconNode);\nexport { __iconNode, ChefHat as default };\n//# sourceMappingURL=chef-hat.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}