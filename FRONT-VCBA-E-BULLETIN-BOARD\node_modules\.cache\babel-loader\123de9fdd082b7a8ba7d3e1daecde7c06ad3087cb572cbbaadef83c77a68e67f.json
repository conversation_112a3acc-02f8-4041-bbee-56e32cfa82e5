{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 10h12\",\n  key: \"1y6xl8\"\n}], [\"path\", {\n  d: \"M4 14h9\",\n  key: \"1loblj\"\n}], [\"path\", {\n  d: \"M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2\",\n  key: \"1j6lzo\"\n}]];\nconst Euro = createLucideIcon(\"euro\", __iconNode);\nexport { __iconNode, Euro as default };\n//# sourceMappingURL=euro.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}