{"ast": null, "code": "import React,{useState,useEffect}from'react';import{adminAnnouncementServiceWithToken,studentAnnouncementServiceWithToken}from'../../services/announcementService';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{useStudentAuth}from'../../contexts/StudentAuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ApiTest=()=>{const[adminResults,setAdminResults]=useState(null);const[studentResults,setStudentResults]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const{user:adminUser,isAuthenticated:isAdminAuth}=useAdminAuth();const{user:studentUser,isAuthenticated:isStudentAuth}=useStudentAuth();const testAdminAPI=async()=>{try{setLoading(true);setError(null);console.log('🧪 Testing Admin API...');const response=await adminAnnouncementServiceWithToken.getAnnouncements({status:'published',limit:5});console.log('🧪 Admin API Response:',response);setAdminResults(response);}catch(err){console.error('🧪 Admin API Error:',err);setError(\"Admin API Error: \".concat(err.message));}finally{setLoading(false);}};const testStudentAPI=async()=>{try{setLoading(true);setError(null);console.log('🧪 Testing Student API...');const response=await studentAnnouncementServiceWithToken.getAnnouncements({status:'published',limit:5});console.log('🧪 Student API Response:',response);setStudentResults(response);}catch(err){console.error('🧪 Student API Error:',err);setError(\"Student API Error: \".concat(err.message));}finally{setLoading(false);}};const testDirectAPI=async()=>{try{setLoading(true);setError(null);console.log('🧪 Testing Direct API...');const response=await fetch('http://localhost:5000/api/announcements?status=published&limit=5');const data=await response.json();console.log('🧪 Direct API Response:',data);setAdminResults(data);}catch(err){console.error('🧪 Direct API Error:',err);setError(\"Direct API Error: \".concat(err.message));}finally{setLoading(false);}};const checkTokens=()=>{const adminToken=localStorage.getItem('vcba_admin_auth_token');const studentToken=localStorage.getItem('vcba_student_auth_token');const adminUser=localStorage.getItem('vcba_admin_user_data');const studentUser=localStorage.getItem('vcba_student_user_data');console.log('🔑 Token Check:',{hasAdminToken:!!adminToken,hasStudentToken:!!studentToken,hasAdminUser:!!adminUser,hasStudentUser:!!studentUser,adminTokenPreview:adminToken?adminToken.substring(0,20)+'...':null,studentTokenPreview:studentToken?studentToken.substring(0,20)+'...':null});};useEffect(()=>{checkTokens();},[]);return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem',maxWidth:'1200px',margin:'0 auto'},children:[/*#__PURE__*/_jsx(\"h1\",{children:\"API Debug Test\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2rem',padding:'1rem',background:'#f5f5f5',borderRadius:'8px'},children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Authentication Status\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Admin Auth:\"}),\" \",isAdminAuth?'✅ Authenticated':'❌ Not Authenticated']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Admin User:\"}),\" \",adminUser?\"\".concat(adminUser.email,\" (\").concat(adminUser.role,\")\"):'None']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Student Auth:\"}),\" \",isStudentAuth?'✅ Authenticated':'❌ Not Authenticated']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Student User:\"}),\" \",studentUser?\"\".concat(studentUser.email,\" (\").concat(studentUser.role,\")\"):'None']}),/*#__PURE__*/_jsx(\"button\",{onClick:checkTokens,style:{marginTop:'1rem',padding:'0.5rem 1rem'},children:\"Check Tokens in Console\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'1rem',marginBottom:'2rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:testAdminAPI,disabled:loading,style:{padding:'0.75rem 1.5rem',background:'#007bff',color:'white',border:'none',borderRadius:'4px'},children:\"Test Admin API\"}),/*#__PURE__*/_jsx(\"button\",{onClick:testStudentAPI,disabled:loading,style:{padding:'0.75rem 1.5rem',background:'#28a745',color:'white',border:'none',borderRadius:'4px'},children:\"Test Student API\"}),/*#__PURE__*/_jsx(\"button\",{onClick:testDirectAPI,disabled:loading,style:{padding:'0.75rem 1.5rem',background:'#6c757d',color:'white',border:'none',borderRadius:'4px'},children:\"Test Direct API\"})]}),loading&&/*#__PURE__*/_jsx(\"p\",{children:\"Loading...\"}),error&&/*#__PURE__*/_jsx(\"p\",{style:{color:'red'},children:error}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Admin API Results\"}),/*#__PURE__*/_jsx(\"pre\",{style:{background:'#f8f9fa',padding:'1rem',borderRadius:'4px',overflow:'auto',maxHeight:'400px'},children:adminResults?JSON.stringify(adminResults,null,2):'No results yet'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Student API Results\"}),/*#__PURE__*/_jsx(\"pre\",{style:{background:'#f8f9fa',padding:'1rem',borderRadius:'4px',overflow:'auto',maxHeight:'400px'},children:studentResults?JSON.stringify(studentResults,null,2):'No results yet'})]})]})]});};export default ApiTest;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}