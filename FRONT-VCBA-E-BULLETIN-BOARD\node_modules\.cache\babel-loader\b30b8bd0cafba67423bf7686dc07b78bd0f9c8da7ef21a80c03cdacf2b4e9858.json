{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 2v2\",\n  key: \"scm5qe\"\n}], [\"path\", {\n  d: \"M17.915 22a6 6 0 0 0-12 0\",\n  key: \"suqz9p\"\n}], [\"path\", {\n  d: \"M8 2v2\",\n  key: \"pbkmx\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"4\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"12vinp\"\n}]];\nconst ContactRound = createLucideIcon(\"contact-round\", __iconNode);\nexport { __iconNode, ContactRound as default };\n//# sourceMappingURL=contact-round.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}