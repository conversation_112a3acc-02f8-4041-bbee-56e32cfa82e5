{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProfilePicture.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { User } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfilePicture = ({\n  src,\n  alt,\n  firstName = '',\n  lastName = '',\n  size = 'md',\n  className = '',\n  showFallback = true,\n  onClick,\n  loading = false\n}) => {\n  _s();\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(!!src);\n\n  // Reset error state when src changes\n  useEffect(() => {\n    if (src) {\n      setImageError(false);\n      setImageLoading(true);\n    }\n  }, [src]);\n\n  // Size configurations\n  const sizeClasses = {\n    xs: 'w-6 h-6 text-xs',\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-12 h-12 text-base',\n    lg: 'w-16 h-16 text-lg',\n    xl: 'w-20 h-20 text-xl',\n    '2xl': 'w-24 h-24 text-2xl'\n  };\n  const iconSizes = {\n    xs: 12,\n    sm: 16,\n    md: 20,\n    lg: 24,\n    xl: 28,\n    '2xl': 32\n  };\n\n  // Generate initials for fallback\n  const getInitials = () => {\n    var _firstName$charAt, _lastName$charAt;\n    const firstInitial = (firstName === null || firstName === void 0 ? void 0 : (_firstName$charAt = firstName.charAt(0)) === null || _firstName$charAt === void 0 ? void 0 : _firstName$charAt.toUpperCase()) || '';\n    const lastInitial = (lastName === null || lastName === void 0 ? void 0 : (_lastName$charAt = lastName.charAt(0)) === null || _lastName$charAt === void 0 ? void 0 : _lastName$charAt.toUpperCase()) || '';\n    return firstInitial + lastInitial || '?';\n  };\n\n  // Generate default avatar URL\n  const getDefaultAvatarUrl = () => {\n    const initials = getInitials();\n    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=3b82f6&color=ffffff&size=300`;\n  };\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n  const shouldShowImage = src && !imageError && !loading;\n  const shouldShowFallback = showFallback && (!src || imageError) && !loading;\n  const shouldShowLoading = loading || imageLoading;\n  const baseClasses = `\n    ${sizeClasses[size]}\n    rounded-full\n    flex\n    items-center\n    justify-center\n    overflow-hidden\n    bg-gray-100\n    border-2\n    border-gray-200\n    ${onClick ? 'cursor-pointer hover:border-blue-300 transition-colors' : ''}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: baseClasses,\n    onClick: onClick,\n    children: [shouldShowLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center w-full h-full\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full border-2 border-blue-500 border-t-transparent w-1/2 h-1/2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this), shouldShowImage && /*#__PURE__*/_jsxDEV(\"img\", {\n      src: src,\n      alt: alt || `${firstName} ${lastName}`.trim() || 'Profile picture',\n      className: \"w-full h-full object-cover\",\n      onLoad: handleImageLoad,\n      onError: handleImageError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 9\n    }, this), shouldShowFallback && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: getInitials() !== '?' ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: getDefaultAvatarUrl(),\n        alt: alt || `${firstName} ${lastName}`.trim() || 'Default avatar',\n        className: \"w-full h-full object-cover\",\n        onError: () => {\n          // If even the default avatar fails, show icon\n          setImageError(true);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(User, {\n        size: iconSizes[size],\n        className: \"text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 13\n      }, this)\n    }, void 0, false), !shouldShowImage && !shouldShowFallback && !shouldShowLoading && /*#__PURE__*/_jsxDEV(User, {\n      size: iconSizes[size],\n      className: \"text-gray-400\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePicture, \"fSoPavcwGVsxdxfWZTDCmwAbF1U=\");\n_c = ProfilePicture;\nexport default ProfilePicture;\nvar _c;\n$RefreshReg$(_c, \"ProfilePicture\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "User", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfilePicture", "src", "alt", "firstName", "lastName", "size", "className", "showFallback", "onClick", "loading", "_s", "imageError", "setImageError", "imageLoading", "setImageLoading", "sizeClasses", "xs", "sm", "md", "lg", "xl", "iconSizes", "getInitials", "_firstName$charAt", "_lastName$charAt", "firstInitial", "char<PERSON>t", "toUpperCase", "lastInitial", "getDefaultAvatarUrl", "initials", "encodeURIComponent", "handleImageLoad", "handleImageError", "shouldShowImage", "shouldShowFallback", "shouldShowLoading", "baseClasses", "trim", "replace", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLoad", "onError", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProfilePicture.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { User } from 'lucide-react';\n\ninterface ProfilePictureProps {\n  src?: string | null;\n  alt?: string;\n  firstName?: string;\n  lastName?: string;\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';\n  className?: string;\n  showFallback?: boolean;\n  onClick?: () => void;\n  loading?: boolean;\n}\n\nconst ProfilePicture: React.FC<ProfilePictureProps> = ({\n  src,\n  alt,\n  firstName = '',\n  lastName = '',\n  size = 'md',\n  className = '',\n  showFallback = true,\n  onClick,\n  loading = false\n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(!!src);\n\n  // Reset error state when src changes\n  useEffect(() => {\n    if (src) {\n      setImageError(false);\n      setImageLoading(true);\n    }\n  }, [src]);\n\n  // Size configurations\n  const sizeClasses = {\n    xs: 'w-6 h-6 text-xs',\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-12 h-12 text-base',\n    lg: 'w-16 h-16 text-lg',\n    xl: 'w-20 h-20 text-xl',\n    '2xl': 'w-24 h-24 text-2xl'\n  };\n\n  const iconSizes = {\n    xs: 12,\n    sm: 16,\n    md: 20,\n    lg: 24,\n    xl: 28,\n    '2xl': 32\n  };\n\n  // Generate initials for fallback\n  const getInitials = () => {\n    const firstInitial = firstName?.charAt(0)?.toUpperCase() || '';\n    const lastInitial = lastName?.charAt(0)?.toUpperCase() || '';\n    return firstInitial + lastInitial || '?';\n  };\n\n  // Generate default avatar URL\n  const getDefaultAvatarUrl = () => {\n    const initials = getInitials();\n    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=3b82f6&color=ffffff&size=300`;\n  };\n\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n\n  const shouldShowImage = src && !imageError && !loading;\n  const shouldShowFallback = showFallback && (!src || imageError) && !loading;\n  const shouldShowLoading = loading || imageLoading;\n\n  const baseClasses = `\n    ${sizeClasses[size]}\n    rounded-full\n    flex\n    items-center\n    justify-center\n    overflow-hidden\n    bg-gray-100\n    border-2\n    border-gray-200\n    ${onClick ? 'cursor-pointer hover:border-blue-300 transition-colors' : ''}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <div className={baseClasses} onClick={onClick}>\n      {shouldShowLoading && (\n        <div className=\"flex items-center justify-center w-full h-full\">\n          <div className=\"animate-spin rounded-full border-2 border-blue-500 border-t-transparent w-1/2 h-1/2\" />\n        </div>\n      )}\n\n      {shouldShowImage && (\n        <img\n          src={src}\n          alt={alt || `${firstName} ${lastName}`.trim() || 'Profile picture'}\n          className=\"w-full h-full object-cover\"\n          onLoad={handleImageLoad}\n          onError={handleImageError}\n        />\n      )}\n\n      {shouldShowFallback && (\n        <>\n          {getInitials() !== '?' ? (\n            <img\n              src={getDefaultAvatarUrl()}\n              alt={alt || `${firstName} ${lastName}`.trim() || 'Default avatar'}\n              className=\"w-full h-full object-cover\"\n              onError={() => {\n                // If even the default avatar fails, show icon\n                setImageError(true);\n              }}\n            />\n          ) : (\n            <User \n              size={iconSizes[size]} \n              className=\"text-gray-400\"\n            />\n          )}\n        </>\n      )}\n\n      {!shouldShowImage && !shouldShowFallback && !shouldShowLoading && (\n        <User \n          size={iconSizes[size]} \n          className=\"text-gray-400\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProfilePicture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAcpC,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,GAAG;EACHC,GAAG;EACHC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG,EAAE;EACbC,IAAI,GAAG,IAAI;EACXC,SAAS,GAAG,EAAE;EACdC,YAAY,GAAG,IAAI;EACnBC,OAAO;EACPC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAACQ,GAAG,CAAC;;EAEvD;EACAP,SAAS,CAAC,MAAM;IACd,IAAIO,GAAG,EAAE;MACPW,aAAa,CAAC,KAAK,CAAC;MACpBE,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACb,GAAG,CAAC,CAAC;;EAET;EACA,MAAMc,WAAW,GAAG;IAClBC,EAAE,EAAE,iBAAiB;IACrBC,EAAE,EAAE,iBAAiB;IACrBC,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvB,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,SAAS,GAAG;IAChBL,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACNC,EAAE,EAAE,EAAE;IACN,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA,EAAAC,gBAAA;IACxB,MAAMC,YAAY,GAAG,CAAAtB,SAAS,aAATA,SAAS,wBAAAoB,iBAAA,GAATpB,SAAS,CAAEuB,MAAM,CAAC,CAAC,CAAC,cAAAH,iBAAA,uBAApBA,iBAAA,CAAsBI,WAAW,CAAC,CAAC,KAAI,EAAE;IAC9D,MAAMC,WAAW,GAAG,CAAAxB,QAAQ,aAARA,QAAQ,wBAAAoB,gBAAA,GAARpB,QAAQ,CAAEsB,MAAM,CAAC,CAAC,CAAC,cAAAF,gBAAA,uBAAnBA,gBAAA,CAAqBG,WAAW,CAAC,CAAC,KAAI,EAAE;IAC5D,OAAOF,YAAY,GAAGG,WAAW,IAAI,GAAG;EAC1C,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;IAC9B,OAAO,oCAAoCS,kBAAkB,CAACD,QAAQ,CAAC,0CAA0C;EACnH,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BlB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrB,aAAa,CAAC,IAAI,CAAC;IACnBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMoB,eAAe,GAAGjC,GAAG,IAAI,CAACU,UAAU,IAAI,CAACF,OAAO;EACtD,MAAM0B,kBAAkB,GAAG5B,YAAY,KAAK,CAACN,GAAG,IAAIU,UAAU,CAAC,IAAI,CAACF,OAAO;EAC3E,MAAM2B,iBAAiB,GAAG3B,OAAO,IAAII,YAAY;EAEjD,MAAMwB,WAAW,GAAG;AACtB,MAAMtB,WAAW,CAACV,IAAI,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,OAAO,GAAG,wDAAwD,GAAG,EAAE;AAC7E,MAAMF,SAAS;AACf,GAAG,CAACgC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAE7B,oBACE1C,OAAA;IAAKS,SAAS,EAAE+B,WAAY;IAAC7B,OAAO,EAAEA,OAAQ;IAAAgC,QAAA,GAC3CJ,iBAAiB,iBAChBvC,OAAA;MAAKS,SAAS,EAAC,gDAAgD;MAAAkC,QAAA,eAC7D3C,OAAA;QAAKS,SAAS,EAAC;MAAqF;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN,EAEAV,eAAe,iBACdrC,OAAA;MACEI,GAAG,EAAEA,GAAI;MACTC,GAAG,EAAEA,GAAG,IAAI,GAAGC,SAAS,IAAIC,QAAQ,EAAE,CAACkC,IAAI,CAAC,CAAC,IAAI,iBAAkB;MACnEhC,SAAS,EAAC,4BAA4B;MACtCuC,MAAM,EAAEb,eAAgB;MACxBc,OAAO,EAAEb;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACF,EAEAT,kBAAkB,iBACjBtC,OAAA,CAAAE,SAAA;MAAAyC,QAAA,EACGlB,WAAW,CAAC,CAAC,KAAK,GAAG,gBACpBzB,OAAA;QACEI,GAAG,EAAE4B,mBAAmB,CAAC,CAAE;QAC3B3B,GAAG,EAAEA,GAAG,IAAI,GAAGC,SAAS,IAAIC,QAAQ,EAAE,CAACkC,IAAI,CAAC,CAAC,IAAI,gBAAiB;QAClEhC,SAAS,EAAC,4BAA4B;QACtCwC,OAAO,EAAEA,CAAA,KAAM;UACb;UACAlC,aAAa,CAAC,IAAI,CAAC;QACrB;MAAE;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEF/C,OAAA,CAACF,IAAI;QACHU,IAAI,EAAEgB,SAAS,CAAChB,IAAI,CAAE;QACtBC,SAAS,EAAC;MAAe;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IACF,gBACD,CACH,EAEA,CAACV,eAAe,IAAI,CAACC,kBAAkB,IAAI,CAACC,iBAAiB,iBAC5DvC,OAAA,CAACF,IAAI;MACHU,IAAI,EAAEgB,SAAS,CAAChB,IAAI,CAAE;MACtBC,SAAS,EAAC;IAAe;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClC,EAAA,CAhIIV,cAA6C;AAAA+C,EAAA,GAA7C/C,cAA6C;AAkInD,eAAeA,cAAc;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}