{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 13V8.5C14 7 15 7 15 5a3 3 0 0 0-6 0c0 2 1 2 1 3.5V13\",\n  key: \"i9gjdv\"\n}], [\"path\", {\n  d: \"M20 15.5a2.5 2.5 0 0 0-2.5-2.5h-11A2.5 2.5 0 0 0 4 15.5V17a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1z\",\n  key: \"1vzg3v\"\n}], [\"path\", {\n  d: \"M5 22h14\",\n  key: \"ehvnwv\"\n}]];\nconst Stamp = createLucideIcon(\"stamp\", __iconNode);\nexport { __iconNode, Stamp as default };\n//# sourceMappingURL=stamp.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}