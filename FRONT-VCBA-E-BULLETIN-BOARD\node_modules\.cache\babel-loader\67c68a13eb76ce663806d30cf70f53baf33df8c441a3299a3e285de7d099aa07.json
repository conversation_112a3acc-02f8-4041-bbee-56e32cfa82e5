{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 3v18\",\n  key: \"pzttux\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"18\",\n  x: \"6\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"btr8bg\"\n}], [\"path\", {\n  d: \"M22 3v18\",\n  key: \"6jf3v\"\n}]];\nconst GalleryHorizontal = createLucideIcon(\"gallery-horizontal\", __iconNode);\nexport { __iconNode, GalleryHorizontal as default };\n//# sourceMappingURL=gallery-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}