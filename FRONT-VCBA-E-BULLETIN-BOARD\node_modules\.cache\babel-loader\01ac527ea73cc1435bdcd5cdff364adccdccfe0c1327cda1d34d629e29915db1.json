{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"1hypcn\"\n}], [\"path\", {\n  d: \"M20 21a8 8 0 0 0-16 0\",\n  key: \"rfgkzh\"\n}]];\nconst UserRound = createLucideIcon(\"user-round\", __iconNode);\nexport { __iconNode, UserRound as default };\n//# sourceMappingURL=user-round.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}