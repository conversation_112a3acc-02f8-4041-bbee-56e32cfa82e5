{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 20H2\",\n  key: \"nlcfvz\"\n}], [\"path\", {\n  d: \"M11 4.562v16.157a1 1 0 0 0 1.242.97L19 20V5.562a2 2 0 0 0-1.515-1.94l-4-1A2 2 0 0 0 11 4.561z\",\n  key: \"au4z13\"\n}], [\"path\", {\n  d: \"M11 4H8a2 2 0 0 0-2 2v14\",\n  key: \"74r1mk\"\n}], [\"path\", {\n  d: \"M14 12h.01\",\n  key: \"1jfl7z\"\n}], [\"path\", {\n  d: \"M22 20h-3\",\n  key: \"vhrsz\"\n}]];\nconst DoorOpen = createLucideIcon(\"door-open\", __iconNode);\nexport { __iconNode, DoorOpen as default };\n//# sourceMappingURL=door-open.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}