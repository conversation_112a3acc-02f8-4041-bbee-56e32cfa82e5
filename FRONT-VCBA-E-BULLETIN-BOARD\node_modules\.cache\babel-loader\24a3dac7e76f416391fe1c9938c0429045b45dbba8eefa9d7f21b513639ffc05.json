{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"8\",\n  key: \"46899m\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"6\",\n  y1: \"3\",\n  y2: \"6\",\n  key: \"1jkytn\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"18\",\n  y1: \"3\",\n  y2: \"6\",\n  key: \"14zfjt\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"6\",\n  y1: \"21\",\n  y2: \"18\",\n  key: \"iusuec\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"18\",\n  y1: \"21\",\n  y2: \"18\",\n  key: \"yj2dd7\"\n}]];\nconst Currency = createLucideIcon(\"currency\", __iconNode);\nexport { __iconNode, Currency as default };\n//# sourceMappingURL=currency.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}