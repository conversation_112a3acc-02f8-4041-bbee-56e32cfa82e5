{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 18h8\",\n  key: \"1borvv\"\n}], [\"path\", {\n  d: \"M3 22h18\",\n  key: \"8prr45\"\n}], [\"path\", {\n  d: \"M14 22a7 7 0 1 0 0-14h-1\",\n  key: \"1jwaiy\"\n}], [\"path\", {\n  d: \"M9 14h2\",\n  key: \"197e7h\"\n}], [\"path\", {\n  d: \"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z\",\n  key: \"1bmzmy\"\n}], [\"path\", {\n  d: \"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3\",\n  key: \"1drr47\"\n}]];\nconst Microscope = createLucideIcon(\"microscope\", __iconNode);\nexport { __iconNode, Microscope as default };\n//# sourceMappingURL=microscope.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}