{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"1pf5j1\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M3 15h6\",\n  key: \"4e2qda\"\n}]];\nconst FileMinus2 = createLucideIcon(\"file-minus-2\", __iconNode);\nexport { __iconNode, FileMinus2 as default };\n//# sourceMappingURL=file-minus-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}