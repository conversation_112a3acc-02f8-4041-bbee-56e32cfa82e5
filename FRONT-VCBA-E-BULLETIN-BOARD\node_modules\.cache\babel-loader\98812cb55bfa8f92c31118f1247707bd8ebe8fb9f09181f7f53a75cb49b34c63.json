{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}], [\"path\", {\n  d: \"M12 2v3\",\n  key: \"qbqxhf\"\n}], [\"path\", {\n  d: \"M18.89 13.24a7 7 0 0 0-8.13-8.13\",\n  key: \"1v9jrh\"\n}], [\"path\", {\n  d: \"M19 12h3\",\n  key: \"osuazr\"\n}], [\"path\", {\n  d: \"M2 12h3\",\n  key: \"1wrr53\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M7.05 7.05a7 7 0 0 0 9.9 9.9\",\n  key: \"rc5l2e\"\n}]];\nconst LocateOff = createLucideIcon(\"locate-off\", __iconNode);\nexport { __iconNode, LocateOff as default };\n//# sourceMappingURL=locate-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}