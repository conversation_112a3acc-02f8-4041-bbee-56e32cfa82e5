{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\layout\\\\AdminHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport NotificationBell from '../NotificationBell';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, School, Menu, User, LogOut, Rss } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = ({\n  onToggleSidebar\n}) => {\n  _s();\n  var _user$firstName, _user$lastName;\n  const {\n    user,\n    logout\n  } = useAdminAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getPageInfo = () => {\n    const path = location.pathname;\n    switch (path) {\n      case '/admin':\n      case '/admin/dashboard':\n        return {\n          title: 'Dashboard',\n          subtitle: 'Overview & Analytics',\n          icon: BarChart3,\n          description: 'Welcome to your admin dashboard'\n        };\n      case '/admin/newsfeed':\n        return {\n          title: 'Admin Newsfeed',\n          subtitle: 'Monitor & Engage',\n          icon: Rss,\n          description: 'Monitor announcements, events, and community engagement'\n        };\n      case '/admin/calendar':\n        return {\n          title: 'Calendar & Events',\n          subtitle: 'Schedule Management',\n          icon: Calendar,\n          description: 'Manage academic calendar, events, and announcements'\n        };\n      case '/admin/posts':\n        return {\n          title: 'Post Management',\n          subtitle: 'Content Publishing',\n          icon: Newspaper,\n          description: 'Create and manage announcements, news, and bulletin posts'\n        };\n      case '/admin/student-management':\n        return {\n          title: 'Student Management',\n          subtitle: 'User Administration',\n          icon: Users,\n          description: 'Manage student accounts, profiles, and academic information'\n        };\n      case '/admin/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'System Configuration',\n          icon: Settings,\n          description: 'Manage your profile, system preferences, and security settings'\n        };\n      default:\n        return {\n          title: 'Admin Panel',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: School,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n  const pageInfo = getPageInfo();\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    style: {\n      background: 'white',\n      borderBottom: '1px solid #e8f5e8',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 100\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onToggleSidebar,\n        style: {\n          background: 'none',\n          border: 'none',\n          padding: '0.5rem',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transition: 'background-color 0.2s ease',\n          fontSize: '1.25rem'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#f3f4f6';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'none';\n        },\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          size: 20,\n          color: \"#2d5016\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(pageInfo.icon, {\n            size: 24,\n            color: \"#2d5016\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: pageInfo.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            },\n            children: pageInfo.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          },\n          children: pageInfo.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          },\n          children: getCurrentTime()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowUserMenu(!showUserMenu),\n          style: {\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '12px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            transition: 'background-color 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.background = '#f3f4f6';\n          },\n          onMouseLeave: e => {\n            if (!showUserMenu) {\n              e.currentTarget.style.background = 'none';\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem'\n            },\n            children: [user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0), user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#2d5016',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              },\n              children: (user === null || user === void 0 ? void 0 : user.position) || 'Administrator'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            },\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '100%',\n            right: 0,\n            marginTop: '0.5rem',\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n            border: '1px solid #e8f5e8',\n            minWidth: '200px',\n            zIndex: 1000\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#2d5016',\n                fontWeight: '600',\n                marginBottom: '0.25rem'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                marginBottom: '1rem'\n              },\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              style: {\n                border: 'none',\n                borderTop: '1px solid #e8f5e8',\n                margin: '1rem 0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowUserMenu(false);\n                navigate('/admin/settings');\n              },\n              style: {\n                width: '100%',\n                background: 'none',\n                border: 'none',\n                padding: '0.75rem',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                textAlign: 'left',\n                color: '#374151',\n                fontSize: '0.875rem',\n                marginBottom: '0.5rem',\n                transition: 'background-color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.background = '#f3f4f6';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.background = 'none';\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  size: 16,\n                  color: \"#6b7280\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), \"Profile Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowUserMenu(false);\n                handleLogout();\n              },\n              style: {\n                width: '100%',\n                background: 'none',\n                border: 'none',\n                padding: '0.75rem',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                textAlign: 'left',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                transition: 'background-color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.background = '#fef2f2';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.background = 'none';\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 999\n      },\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHeader, \"w6TRZ+ktZdrL5dIzb8rjMQoX1PI=\", false, function () {\n  return [useAdminAuth, useLocation, useNavigate];\n});\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "useState", "useLocation", "useNavigate", "useAdminAuth", "NotificationBell", "BarChart3", "Calendar", "Newspaper", "Users", "Settings", "School", "<PERSON><PERSON>", "User", "LogOut", "Rss", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "onToggleSidebar", "_s", "_user$firstName", "_user$lastName", "user", "logout", "location", "navigate", "showUserMenu", "setShowUserMenu", "handleLogout", "error", "console", "getCurrentTime", "Date", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "getPageInfo", "path", "pathname", "title", "subtitle", "icon", "description", "pageInfo", "style", "background", "borderBottom", "padding", "display", "alignItems", "justifyContent", "boxShadow", "position", "top", "zIndex", "children", "gap", "onClick", "border", "borderRadius", "cursor", "transition", "fontSize", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "margin", "fontWeight", "width", "height", "firstName", "char<PERSON>t", "lastName", "textAlign", "lineHeight", "transform", "right", "marginTop", "min<PERSON><PERSON><PERSON>", "email", "borderTop", "left", "bottom", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/layout/AdminHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport NotificationBell from '../NotificationBell';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, School, Menu, User, LogOut, Rss, CalendarDays } from 'lucide-react';\n\ninterface AdminHeaderProps {\n  onToggleSidebar: () => void;\n}\n\nconst AdminHeader: React.FC<AdminHeaderProps> = ({ onToggleSidebar }) => {\n  const { user, logout } = useAdminAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getPageInfo = () => {\n    const path = location.pathname;\n\n    switch (path) {\n      case '/admin':\n      case '/admin/dashboard':\n        return {\n          title: 'Dashboard',\n          subtitle: 'Overview & Analytics',\n          icon: BarChart3,\n          description: 'Welcome to your admin dashboard'\n        };\n      case '/admin/newsfeed':\n        return {\n          title: 'Admin Newsfeed',\n          subtitle: 'Monitor & Engage',\n          icon: Rss,\n          description: 'Monitor announcements, events, and community engagement'\n        };\n      case '/admin/calendar':\n        return {\n          title: 'Calendar & Events',\n          subtitle: 'Schedule Management',\n          icon: Calendar,\n          description: 'Manage academic calendar, events, and announcements'\n        };\n      case '/admin/posts':\n        return {\n          title: 'Post Management',\n          subtitle: 'Content Publishing',\n          icon: Newspaper,\n          description: 'Create and manage announcements, news, and bulletin posts'\n        };\n      case '/admin/student-management':\n        return {\n          title: 'Student Management',\n          subtitle: 'User Administration',\n          icon: Users,\n          description: 'Manage student accounts, profiles, and academic information'\n        };\n      case '/admin/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'System Configuration',\n          icon: Settings,\n          description: 'Manage your profile, system preferences, and security settings'\n        };\n      default:\n        return {\n          title: 'Admin Panel',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: School,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n\n  const pageInfo = getPageInfo();\n\n  return (\n    <header style={{\n      background: 'white',\n      borderBottom: '1px solid #e8f5e8',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 100\n    }}>\n      {/* Left Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Sidebar Toggle */}\n        <button\n          onClick={onToggleSidebar}\n          style={{\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transition: 'background-color 0.2s ease',\n            fontSize: '1.25rem'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#f3f4f6';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'none';\n          }}\n        >\n          <Menu size={20} color=\"#2d5016\" />\n        </button>\n\n        {/* Page Title */}\n        <div>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.25rem' }}>\n            <pageInfo.icon size={24} color=\"#2d5016\" />\n            <h1 style={{\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            }}>\n              {pageInfo.title}\n            </h1>\n            <span style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            }}>\n              {pageInfo.subtitle}\n            </span>\n          </div>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          }}>\n            {pageInfo.description}\n          </p>\n          <p style={{\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          }}>\n            {getCurrentTime()}\n          </p>\n        </div>\n      </div>\n\n      {/* Right Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Notifications */}\n        <NotificationBell />\n\n        {/* User Profile */}\n        <div style={{ position: 'relative' }}>\n          <button\n            onClick={() => setShowUserMenu(!showUserMenu)}\n            style={{\n              background: 'none',\n              border: 'none',\n              padding: '0.5rem',\n              borderRadius: '12px',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              transition: 'background-color 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = '#f3f4f6';\n            }}\n            onMouseLeave={(e) => {\n              if (!showUserMenu) {\n                e.currentTarget.style.background = 'none';\n              }\n            }}\n          >\n            {/* Avatar */}\n            <div style={{\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem'\n            }}>\n              {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}\n            </div>\n\n            {/* User Info */}\n            <div style={{ textAlign: 'left' }}>\n              <div style={{\n                color: '#2d5016',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              }}>\n                {user?.firstName} {user?.lastName}\n              </div>\n              <div style={{\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              }}>\n                {user?.position || 'Administrator'}\n              </div>\n            </div>\n\n            {/* Dropdown Arrow */}\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            }}>\n              ▼\n            </span>\n          </button>\n\n          {/* User Dropdown Menu */}\n          {showUserMenu && (\n            <div style={{\n              position: 'absolute',\n              top: '100%',\n              right: 0,\n              marginTop: '0.5rem',\n              background: 'white',\n              borderRadius: '12px',\n              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n              border: '1px solid #e8f5e8',\n              minWidth: '200px',\n              zIndex: 1000\n            }}>\n              <div style={{ padding: '1rem' }}>\n                <div style={{\n                  color: '#2d5016',\n                  fontWeight: '600',\n                  marginBottom: '0.25rem'\n                }}>\n                  {user?.firstName} {user?.lastName}\n                </div>\n                <div style={{\n                  color: '#6b7280',\n                  fontSize: '0.875rem',\n                  marginBottom: '1rem'\n                }}>\n                  {user?.email}\n                </div>\n                \n                <hr style={{\n                  border: 'none',\n                  borderTop: '1px solid #e8f5e8',\n                  margin: '1rem 0'\n                }} />\n\n                <button\n                  onClick={() => {\n                    setShowUserMenu(false);\n                    navigate('/admin/settings');\n                  }}\n                  style={{\n                    width: '100%',\n                    background: 'none',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    textAlign: 'left',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    marginBottom: '0.5rem',\n                    transition: 'background-color 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.background = '#f3f4f6';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.background = 'none';\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <User size={16} color=\"#6b7280\" />\n                    Profile Settings\n                  </span>\n                </button>\n\n                <button\n                  onClick={() => {\n                    setShowUserMenu(false);\n                    handleLogout();\n                  }}\n                  style={{\n                    width: '100%',\n                    background: 'none',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    textAlign: 'left',\n                    color: '#dc2626',\n                    fontSize: '0.875rem',\n                    transition: 'background-color 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.background = '#fef2f2';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.background = 'none';\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <LogOut size={16} color=\"#ef4444\" />\n                    Logout\n                  </span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 999\n          }}\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default AdminHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,oCAAoC;AACjE,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,QAAsB,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9H,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,cAAA;EACvE,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGpB,YAAY,CAAC,CAAC;EACvC,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAML,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,IAAI,GAAGjB,QAAQ,CAACkB,QAAQ;IAE9B,QAAQD,IAAI;MACV,KAAK,QAAQ;MACb,KAAK,kBAAkB;QACrB,OAAO;UACLE,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,sBAAsB;UAChCC,IAAI,EAAExC,SAAS;UACfyC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,gBAAgB;UACvBC,QAAQ,EAAE,kBAAkB;UAC5BC,IAAI,EAAE/B,GAAG;UACTgC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,qBAAqB;UAC/BC,IAAI,EAAEvC,QAAQ;UACdwC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,cAAc;QACjB,OAAO;UACLH,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,oBAAoB;UAC9BC,IAAI,EAAEtC,SAAS;UACfuC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,2BAA2B;QAC9B,OAAO;UACLH,KAAK,EAAE,oBAAoB;UAC3BC,QAAQ,EAAE,qBAAqB;UAC/BC,IAAI,EAAErC,KAAK;UACXsC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,sBAAsB;UAChCC,IAAI,EAAEpC,QAAQ;UACdqC,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACLH,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,uBAAuB;UACjCC,IAAI,EAAEnC,MAAM;UACZoC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACExB,OAAA;IAAQgC,KAAK,EAAE;MACbC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,mBAAmB;MACjCC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,SAAS,EAAE,gCAAgC;MAC3CC,QAAQ,EAAE,QAAQ;MAClBC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBAEA3C,OAAA;MAAKgC,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE3C,OAAA;QACE6C,OAAO,EAAE3C,eAAgB;QACzB8B,KAAK,EAAE;UACLC,UAAU,EAAE,MAAM;UAClBa,MAAM,EAAE,MAAM;UACdX,OAAO,EAAE,QAAQ;UACjBY,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBZ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBW,UAAU,EAAE,4BAA4B;UACxCC,QAAQ,EAAE;QACZ,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;QAC9C,CAAE;QACFqB,YAAY,EAAGF,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;QAC3C,CAAE;QAAAU,QAAA,eAEF3C,OAAA,CAACL,IAAI;UAAC4D,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGT5D,OAAA;QAAA2C,QAAA,gBACE3C,OAAA;UAAKgC,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE,SAAS;YAAEiB,YAAY,EAAE;UAAU,CAAE;UAAAlB,QAAA,gBAC7F3C,OAAA,CAAC+B,QAAQ,CAACF,IAAI;YAAC0B,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C5D,OAAA;YAAIgC,KAAK,EAAE;cACT8B,MAAM,EAAE,CAAC;cACTN,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,QAAQ;cAClBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACJ;UAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACL5D,OAAA;YAAMgC,KAAK,EAAE;cACXC,UAAU,EAAE,mDAAmD;cAC/DuB,KAAK,EAAE,OAAO;cACdrB,OAAO,EAAE,iBAAiB;cAC1BY,YAAY,EAAE,MAAM;cACpBG,QAAQ,EAAE,SAAS;cACnBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACH;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5D,OAAA;UAAGgC,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE,UAAU;YACpBW,YAAY,EAAE;UAChB,CAAE;UAAAlB,QAAA,EACCZ,QAAQ,CAACD;QAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJ5D,OAAA;UAAGgC,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE;UACZ,CAAE;UAAAP,QAAA,EACC5B,cAAc,CAAC;QAAC;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKgC,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE3C,OAAA,CAACZ,gBAAgB;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpB5D,OAAA;QAAKgC,KAAK,EAAE;UAAEQ,QAAQ,EAAE;QAAW,CAAE;QAAAG,QAAA,gBACnC3C,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CsB,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBa,MAAM,EAAE,MAAM;YACdX,OAAO,EAAE,QAAQ;YACjBY,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,SAAS;YACjBZ,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBO,GAAG,EAAE,SAAS;YACdK,UAAU,EAAE;UACd,CAAE;UACFE,YAAY,EAAGC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;UAC9C,CAAE;UACFqB,YAAY,EAAGF,CAAC,IAAK;YACnB,IAAI,CAAC1C,YAAY,EAAE;cACjB0C,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;YAC3C;UACF,CAAE;UAAAU,QAAA,gBAGF3C,OAAA;YAAKgC,KAAK,EAAE;cACVgC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,KAAK;cACnBd,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBkB,KAAK,EAAE,OAAO;cACdO,UAAU,EAAE,KAAK;cACjBb,QAAQ,EAAE;YACZ,CAAE;YAAAP,QAAA,GACCrC,IAAI,aAAJA,IAAI,wBAAAF,eAAA,GAAJE,IAAI,CAAE4D,SAAS,cAAA9D,eAAA,uBAAfA,eAAA,CAAiB+D,MAAM,CAAC,CAAC,CAAC,EAAE7D,IAAI,aAAJA,IAAI,wBAAAD,cAAA,GAAJC,IAAI,CAAE8D,QAAQ,cAAA/D,cAAA,uBAAdA,cAAA,CAAgB8D,MAAM,CAAC,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGN5D,OAAA;YAAKgC,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBAChC3C,OAAA;cAAKgC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBO,UAAU,EAAE,KAAK;gBACjBb,QAAQ,EAAE,QAAQ;gBAClBoB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,GACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,SAAS,EAAC,GAAC,EAAC5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN5D,OAAA;cAAKgC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,SAAS;gBACnBoB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EACC,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,QAAQ,KAAI;YAAe;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAMgC,KAAK,EAAE;cACXwB,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,SAAS;cACnBqB,SAAS,EAAE7D,YAAY,GAAG,gBAAgB,GAAG,cAAc;cAC3DuC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGRlD,YAAY,iBACXV,OAAA;UAAKgC,KAAK,EAAE;YACVQ,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,MAAM;YACX+B,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,QAAQ;YACnBxC,UAAU,EAAE,OAAO;YACnBc,YAAY,EAAE,MAAM;YACpBR,SAAS,EAAE,iCAAiC;YAC5CO,MAAM,EAAE,mBAAmB;YAC3B4B,QAAQ,EAAE,OAAO;YACjBhC,MAAM,EAAE;UACV,CAAE;UAAAC,QAAA,eACA3C,OAAA;YAAKgC,KAAK,EAAE;cAAEG,OAAO,EAAE;YAAO,CAAE;YAAAQ,QAAA,gBAC9B3C,OAAA;cAAKgC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBO,UAAU,EAAE,KAAK;gBACjBF,YAAY,EAAE;cAChB,CAAE;cAAAlB,QAAA,GACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,SAAS,EAAC,GAAC,EAAC5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN5D,OAAA;cAAKgC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBW,YAAY,EAAE;cAChB,CAAE;cAAAlB,QAAA,EACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN5D,OAAA;cAAIgC,KAAK,EAAE;gBACTc,MAAM,EAAE,MAAM;gBACd8B,SAAS,EAAE,mBAAmB;gBAC9Bd,MAAM,EAAE;cACV;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEL5D,OAAA;cACE6C,OAAO,EAAEA,CAAA,KAAM;gBACblC,eAAe,CAAC,KAAK,CAAC;gBACtBF,QAAQ,CAAC,iBAAiB,CAAC;cAC7B,CAAE;cACFuB,KAAK,EAAE;gBACLgC,KAAK,EAAE,MAAM;gBACb/B,UAAU,EAAE,MAAM;gBAClBa,MAAM,EAAE,MAAM;gBACdX,OAAO,EAAE,SAAS;gBAClBY,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBqB,SAAS,EAAE,MAAM;gBACjBb,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBW,YAAY,EAAE,QAAQ;gBACtBZ,UAAU,EAAE;cACd,CAAE;cACFE,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;cAC9C,CAAE;cACFqB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;cAC3C,CAAE;cAAAU,QAAA,eAEF3C,OAAA;gBAAMgC,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,GAAG,EAAE;gBAAS,CAAE;gBAAAD,QAAA,gBACpE3C,OAAA,CAACJ,IAAI;kBAAC2D,IAAI,EAAE,EAAG;kBAACC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAET5D,OAAA;cACE6C,OAAO,EAAEA,CAAA,KAAM;gBACblC,eAAe,CAAC,KAAK,CAAC;gBACtBC,YAAY,CAAC,CAAC;cAChB,CAAE;cACFoB,KAAK,EAAE;gBACLgC,KAAK,EAAE,MAAM;gBACb/B,UAAU,EAAE,MAAM;gBAClBa,MAAM,EAAE,MAAM;gBACdX,OAAO,EAAE,SAAS;gBAClBY,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBqB,SAAS,EAAE,MAAM;gBACjBb,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBD,UAAU,EAAE;cACd,CAAE;cACFE,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;cAC9C,CAAE;cACFqB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;cAC3C,CAAE;cAAAU,QAAA,eAEF3C,OAAA;gBAAMgC,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,GAAG,EAAE;gBAAS,CAAE;gBAAAD,QAAA,gBACpE3C,OAAA,CAACH,MAAM;kBAAC0D,IAAI,EAAE,EAAG;kBAACC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlD,YAAY,iBACXV,OAAA;MACEgC,KAAK,EAAE;QACLQ,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNoC,IAAI,EAAE,CAAC;QACPL,KAAK,EAAE,CAAC;QACRM,MAAM,EAAE,CAAC;QACTpC,MAAM,EAAE;MACV,CAAE;MACFG,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,KAAK;IAAE;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACzD,EAAA,CAtWIF,WAAuC;EAAA,QAClBd,YAAY,EACpBF,WAAW,EACXC,WAAW;AAAA;AAAA6F,EAAA,GAHxB9E,WAAuC;AAwW7C,eAAeA,WAAW;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}