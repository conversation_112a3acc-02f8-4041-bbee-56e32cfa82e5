{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\NewsFeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart,\n// Edit, // Not used in unified component\nUsers, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = (imagePath, userRole) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath, userRole, imageUrl]); // Added missing dependencies\n\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath, userRole);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  userRole,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = () => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(),\n        userRole: userRole,\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(),\n            userRole: userRole,\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n\n// Props interface for the unified NewsFeed component\n_c2 = ImageGallery;\n// Main unified NewsFeed Component\nconst NewsFeed = ({\n  userRole\n}) => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine current user role and context\n  const currentRole = userRole || (adminAuth.isAuthenticated ? 'admin' : studentAuth.isAuthenticated ? 'student' : null);\n  const currentUser = currentRole === 'admin' ? adminAuth.user : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth.logout : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const {\n    categories\n  } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const authToken = currentRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${authToken}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#ef4444',\n            marginBottom: '1rem'\n          },\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            marginBottom: '1.5rem'\n          },\n          children: \"Please log in to access the newsfeed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          style: {\n            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            padding: '0.75rem 1.5rem',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: !event.user_has_reacted,\n          reaction_count: event.user_has_reacted ? (event.reaction_count || 1) - 1 : (event.reaction_count || 0) + 1\n        } : e));\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await currentLogout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' + String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' + String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                },\n                children: currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search post\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 19\n                }, this), categories.filter(category =>\n                // Hide holiday categories from dropdown\n                !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 67\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                \"data-dropdown\": \"user-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowUserDropdown(!showUserDropdown),\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    padding: '0.75rem 1rem',\n                    background: 'white',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(User, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentRole === 'admin' ? 'Admin' : 'Student')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                    size: 14,\n                    style: {\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 19\n                }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '100%',\n                    right: 0,\n                    marginTop: '0.5rem',\n                    background: 'white',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '12px',\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                    minWidth: '200px',\n                    zIndex: 1000,\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem 1rem',\n                      borderBottom: '1px solid #f3f4f6',\n                      background: '#f9fafb'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827'\n                      },\n                      children: [currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      },\n                      children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.5rem 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                        navigate(dashboardPath);\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#374151',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#f3f4f6';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1090,\n                        columnNumber: 27\n                      }, this), \"Dashboard\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1064,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        handleLogout();\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#ef4444',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#fef2f2';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1119,\n                        columnNumber: 27\n                      }, this), \"Logout\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map(announcement => {\n                  // Handle alert announcements with special styling\n                  const isAlert = announcement.is_alert;\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: isAlert ? '#fef2f2' : '#f8fafc',\n                      borderRadius: '12px',\n                      border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                      e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                      e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: isAlert ? '#ef4444' : categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1220,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1229,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1238,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1255,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1256,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1248,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1215,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                refreshAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1342,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1433,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1448,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1409,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1531,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.category_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1560,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1547,\n                          columnNumber: 33\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1573,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1566,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1535,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1583,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1534,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1515,\n                  columnNumber: 23\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = eventImageUrls.filter(Boolean);\n                        openLightboxWithUrls(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1614,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1613,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1655,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1656,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1649,\n                    columnNumber: 25\n                  }, this), event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.category_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1671,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1665,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCalendarLikeToggle(event),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: event.user_has_reacted ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1717,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.reaction_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1721,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1694,\n                    columnNumber: 25\n                  }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1753,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.comment_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1754,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1726,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1685,\n                  columnNumber: 23\n                }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1767,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1774,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1761,\n                  columnNumber: 25\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                id: `announcement-${announcement.announcement_id}`,\n                className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1840,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1825,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1865,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1855,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1884,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1874,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1914,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1901,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1937,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1924,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1945,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1957,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1891,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1970,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1890,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1846,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  userRole: currentRole,\n                  onImageClick: index => {\n                    openLightbox(announcement.attachments || [], index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1984,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1995,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2043,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2047,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2020,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2079,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2080,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2052,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2014,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2100,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || announcement.author_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2101,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2095,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2104,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2088,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2005,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2126,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2133,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2120,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1792,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              if (selectedPinnedPost.is_alert) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2250,\n                    columnNumber: 25\n                  }, this), \"Alert\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2237,\n                  columnNumber: 23\n                }, this);\n              } else {\n                const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                const categoryStyle = getCategoryStyle(categoryName);\n                const IconComponent = categoryStyle.icon;\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: categoryStyle.background,\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2273,\n                    columnNumber: 25\n                  }, this), categoryName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2260,\n                  columnNumber: 23\n                }, this);\n              }\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2291,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2280,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2296,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              userRole: currentRole,\n              onImageClick: index => {\n                openLightbox(selectedPinnedPost.attachments, index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2309,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2308,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2344,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2338,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2347,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2227,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2169,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2154,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      isOpen: lightboxOpen,\n      onClose: () => setLightboxOpen(false),\n      altPrefix: \"Announcement Image\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2358,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 764,\n    columnNumber: 5\n  }, this);\n};\n_s3(NewsFeed, \"ZhLzYUh6hZd93n0XmO1QTWCXlXg=\", true, function () {\n  return [useNavigate, useCategories, useAnnouncements, useNotificationTarget];\n});\n_c3 = NewsFeed;\nexport default NewsFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"NewsFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "calendarReactionService", "useCategories", "useAnnouncements", "useNotificationTarget", "AdminCommentSection", "CommentSection", "NotificationBell", "StudentNotificationBell", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "userRole", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "authToken", "localStorage", "getItem", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "position", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "transition", "gap", "file_path", "currentTarget", "transform", "top", "left", "right", "bottom", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "_c2", "NewsFeed", "_s3", "navigate", "adminAuth", "useAdminAuth", "studentAuth", "useStudentAuth", "currentRole", "isAuthenticated", "currentUser", "user", "currentLogout", "logout", "categories", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "isFromNotification", "scrollTarget", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "fetchCalendarEvents", "undefined", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "pinned", "filter", "ann", "is_pinned", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "minHeight", "background", "padding", "max<PERSON><PERSON><PERSON>", "openLightbox", "initialIndex", "imageUrls", "img", "Boolean", "openLightboxWithUrls", "getCategoryStyle", "categoryName", "styles", "icon", "getHolidayTypeStyle", "holidayTypeName", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "handleCalendarLikeToggle", "user_has_reacted", "toggleLike", "prevEvents", "reaction_count", "handleLogout", "redirectPath", "href", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "event_date", "eventStartDateString", "eventEndDateString", "end_date", "endDate", "isEventActive", "isActive", "is_active", "displayAnnouncements", "displayEvents", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "size", "type", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "firstName", "lastName", "email", "dashboardPath", "flexShrink", "<PERSON><PERSON><PERSON><PERSON>", "is_alert", "category_name", "toUpperCase", "categoryStyle", "substring", "created_at", "toLocaleDateString", "opacity", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "push", "maxVisible", "filteredImages", "paddingTop", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "flexWrap", "posted_by_name", "author_name", "announcementId", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/NewsFeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminAuthContext from '../../contexts/AdminAuthContext';\nimport StudentAuthContext from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  // Edit, // Not used in unified component\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nconst useImageLoader = (imagePath: string | null, userRole?: 'admin' | 'student') => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin'\n          ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n          : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath, userRole, imageUrl]); // Added missing dependencies\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  userRole?: 'admin' | 'student';\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath, userRole);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  userRole?: 'admin' | 'student';\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, userRole, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle()}\n          userRole={userRole}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle()}\n                  userRole={userRole}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Props interface for the unified NewsFeed component\ninterface NewsFeedProps {\n  userRole?: 'admin' | 'student'; // Optional prop to override role detection\n}\n\n// Main unified NewsFeed Component\nconst NewsFeed: React.FC<NewsFeedProps> = ({ userRole }) => {\n  const navigate = useNavigate();\n\n  // Get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine current user role and context\n  const currentRole = userRole ||\n    (adminAuth.isAuthenticated ? 'admin' :\n     studentAuth.isAuthenticated ? 'student' : null);\n\n  const currentUser = currentRole === 'admin' ? adminAuth.user : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth.logout : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const { categories } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, scrollTarget } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const authToken = currentRole === 'admin'\n        ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n        : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${authToken}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      }}>\n        <div style={{\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        }}>\n          <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>Authentication Required</h2>\n          <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>\n            Please log in to access the newsfeed.\n          </p>\n          <button\n            onClick={() => navigate('/')}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '12px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: !event.user_has_reacted,\n                  reaction_count: event.user_has_reacted\n                    ? (event.reaction_count || 1) - 1\n                    : (event.reaction_count || 0) + 1\n                }\n              : e\n          )\n        );\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await currentLogout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' +\n      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n      String(today.getDate()).padStart(2, '0');\n\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' +\n      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +\n      String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' +\n        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +\n        String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = (event as any).is_active !== 0;\n\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                  {currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'}\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories\n                    .filter(category =>\n                      // Hide holiday categories from dropdown\n                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                    )\n                    .map(category => (\n                      <option key={category.category_id} value={category.category_id.toString()}>\n                        {category.name}\n                      </option>\n                    ))\n                  }\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell - Role-aware */}\n                {currentRole === 'admin' ? <NotificationBell /> : <StudentNotificationBell />}\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#22c55e';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    <User size={16} />\n                    <span>{currentUser?.firstName || (currentRole === 'admin' ? 'Admin' : 'Student')}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        }}>\n                          {currentUser?.firstName} {currentUser?.lastName}\n                        </div>\n                        <div style={{\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          {currentUser?.email}\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        <button\n                          onClick={() => {\n                            const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                            navigate(dashboardPath);\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#374151',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f3f4f6';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LayoutDashboard size={16} />\n                          Dashboard\n                        </button>\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement) => {\n                      // Handle alert announcements with special styling\n                      const isAlert = announcement.is_alert;\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: isAlert ? '#fef2f2' : '#f8fafc',\n                            borderRadius: '12px',\n                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  refreshAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) - Commented out for now */}\n          {/* Future feature: Recent student registrations for admin dashboard */}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.category_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightboxWithUrls(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.category_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.category_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Interaction Section */}\n                      <div style={{\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      }}>\n                        {/* Like Button */}\n                        <button\n                          onClick={() => handleCalendarLikeToggle(event)}\n                          style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            background: 'none',\n                            border: 'none',\n                            color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                            cursor: 'pointer',\n                            padding: '0.5rem',\n                            borderRadius: '8px',\n                            transition: 'all 0.2s ease',\n                            fontSize: '0.875rem',\n                            fontWeight: '500'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'none';\n                          }}\n                        >\n                          <Heart\n                            size={18}\n                            fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                          />\n                          <span>{(event as any).reaction_count || 0}</span>\n                        </button>\n\n                        {/* Comments Button */}\n                        {(event as any).allow_comments && (\n                          <button\n                            onClick={() => setShowCalendarComments(\n                              showCalendarComments === event.calendar_id ? null : event.calendar_id\n                            )}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                            }}\n                          >\n                            <MessageSquare size={18} />\n                            <span>{(event as any).comment_count || 0}</span>\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Comments Section */}\n                      {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          userRole={currentRole}\n                          onImageClick={(index) => {\n                            openLightbox(announcement.attachments || [], index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {(announcement as any).posted_by_name || announcement.author_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    userRole={currentRole}\n                    onImageClick={(index) => {\n                      openLightbox(selectedPinnedPost.attachments, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Announcement Image\"\n      />\n    </div>\n  );\n};\n\nexport default NewsFeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C;AACA,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAG7E,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,uBAAuB,MAAM,6BAA6B;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAG3C,SAASC,WAAW,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAChH,OAAO,wCAAwC;AAC/C,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK;AACL;AACAC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAwB,EAAEC,QAA8B,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,SAAS,EAAE;MACdI,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGxC,WAAW,CAAC8B,SAAS,CAAC;QACtC,IAAI,CAACU,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,SAAS,GAAGb,QAAQ,KAAK,OAAO,GAClCc,YAAY,CAACC,OAAO,CAAC5C,oBAAoB,CAAC,GAC1C2C,YAAY,CAACC,OAAO,CAAC3C,sBAAsB,CAAC;;QAEhD;QACA,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAACR,OAAO,EAAE;UACpCS,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,SAAS,EAAE;YACtC,QAAQ,EAAEO,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAId,KAAK,CAAC,QAAQM,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CxB,WAAW,CAACyB,SAAS,CAAC;QAEtBjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOmB,GAAG,EAAE;QACZpB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEyB,GAAG,CAAC;QAC3CxB,QAAQ,CAACwB,GAAG,YAAYrB,KAAK,GAAGqB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACR3B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAChC,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACH,SAAS,EAAEC,QAAQ,EAAEE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAErC,OAAO;IAAEA,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CArEMH,cAAc;AAiFpB,MAAMqC,YAAyC,GAAGA,CAAC;EACjDpC,SAAS;EACTqC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTtC,QAAQ;EACRuC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAExC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGR,cAAc,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAExE,IAAII,OAAO,EAAE;IACX,oBACET,OAAA;MAAK0C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBrD,OAAA;QAAK0C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCrD,OAAA;UAAK0C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE5D,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjD,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEP,OAAA;MAAK0C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBrD,OAAA;QAAK0C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCrD,OAAA;UAAK0C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrE5D,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/EjD,KAAK,iBACJX,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzE1C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IACEgE,GAAG,EAAEzD,QAAS;IACdkC,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACbjD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE2B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CAtEMP,YAAyC;EAAA,QAURrC,cAAc;AAAA;AAAA+D,EAAA,GAV/C1B,YAAyC;AA8E/C,MAAM2B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEhE,QAAQ;EAAEiE;AAAa,CAAC,KAAK;EACnG,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEd,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIR,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAA2B;IAC/C,OAAO;MACLF,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACEzF,OAAA;IAAK0C,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf0C,GAAG,EAAE,KAAK;MACVL,KAAK,EAAE,MAAM;MACb9B,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEArD,OAAA;MAAK0C,KAAK,EAAEmC,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAAlB,QAAA,gBACrDrD,OAAA,CAACwC,YAAY;QACXpC,SAAS,EAAEoE,aAAa,CAAC,CAAC,CAAC,CAACmB,SAAU;QACtClD,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAE6C,aAAa,CAAC,CAAE;QACvBlF,QAAQ,EAAEA,QAAS;QACnBwC,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;QACjD,CAAE;QACF/C,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDU,YAAY,iBACXtE,OAAA;QACE0C,KAAK,EAAE;UACLuC,QAAQ,EAAE,UAAU;UACpBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAAC;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLY,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBvE,OAAA;MAAK0C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE3B,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DmB,GAAG,EAAE,KAAK;QACVL,KAAK,EAAEb,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAlB,QAAA,EACCmB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK/B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE1E,OAAA;UAEE0C,KAAK,EAAE;YACL,GAAGmC,iBAAiB,CAAC0B,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAC;YACvDU,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBAEFrD,OAAA,CAACwC,YAAY;YACXpC,SAAS,EAAEiG,KAAK,CAACV,SAAU;YAC3BlD,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAE6C,aAAa,CAAC,CAAE;YACvBlF,QAAQ,EAAEA,QAAS;YACnBwC,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;YACjD,CAAE;YACF/C,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD4C,MAAM,IAAI9B,cAAc,GAAG,CAAC,iBAC3B1E,OAAA;YAAK0C,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT9C,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdI,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,GACA,EAACqB,cAAc;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAU,YAAY,iBACXtE,OAAA;YACE0C,KAAK,EAAE;cACLuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACiC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAnDI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA6C,GAAA,GAzJMtC,YAAyC;AA8J/C;AACA,MAAMuC,QAAiC,GAAGA,CAAC;EAAErG;AAAS,CAAC,KAAK;EAAAsG,GAAA;EAC1D,MAAMC,QAAQ,GAAGjJ,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMkJ,SAAS,GAAGC,YAAY,CAAC,CAAC;EAChC,MAAMC,WAAW,GAAGC,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMC,WAAW,GAAG5G,QAAQ,KACzBwG,SAAS,CAACK,eAAe,GAAG,OAAO,GACnCH,WAAW,CAACG,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC;EAElD,MAAMC,WAAW,GAAGF,WAAW,KAAK,OAAO,GAAGJ,SAAS,CAACO,IAAI,GAAGL,WAAW,CAACK,IAAI;EAC/E,MAAMC,aAAa,GAAGJ,WAAW,KAAK,OAAO,GAAGJ,SAAS,CAACS,MAAM,GAAGP,WAAW,CAACO,MAAM;;EAErF;EACA,MAAM;IAAEC;EAAW,CAAC,GAAG1J,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IACJ2J,aAAa;IACb/G,OAAO;IACPE,KAAK;IACL8G,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAG9J,gBAAgB,CAAC;IACnBgE,MAAM,EAAE,WAAW;IACnB+F,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAEf,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAM;IAAEgB,kBAAkB;IAAEC;EAAa,CAAC,GAAGnK,qBAAqB,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACoK,cAAc,EAAEC,iBAAiB,CAAC,GAAG3K,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC4K,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7K,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC8K,UAAU,EAAEC,aAAa,CAAC,GAAG/K,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACgL,YAAY,EAAEC,eAAe,CAAC,GAAGjL,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACkL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnL,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACoL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrL,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAACsL,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvL,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACwL,YAAY,EAAEC,eAAe,CAAC,GAAGzL,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0L,cAAc,EAAEC,iBAAiB,CAAC,GAAG3L,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC4L,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7L,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC8L,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/L,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACgM,cAAc,EAAEC,iBAAiB,CAAC,GAAGjM,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACkM,eAAe,EAAEC,kBAAkB,CAAC,GAAGnM,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoM,aAAa,EAAEC,gBAAgB,CAAC,GAAGrM,QAAQ,CAAqB,CAAC;EACxE;;EAEA;EACA,MAAMsM,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFH,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACE,SAAS,CAAC;MAE3B,MAAM9I,SAAS,GAAG+F,WAAW,KAAK,OAAO,GACrC9F,YAAY,CAACC,OAAO,CAAC5C,oBAAoB,CAAC,GAC1C2C,YAAY,CAACC,OAAO,CAAC3C,sBAAsB,CAAC;MAEhD,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,0DAA0D,EAAE;QACtGiD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUN,SAAS,EAAE;UACtC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAM+I,IAAI,GAAG,MAAM5I,QAAQ,CAAC6I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAAChE,GAAG,CAAC,MAAOqE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMpJ,KAAK,CAAC,GAAG/C,YAAY,iBAAiBkM,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FnJ,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUN,SAAS,EAAE;gBACtC,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAM0J,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACrG,MAAM,GAAGwG,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACrG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO0G,MAAM,EAAE;YACf9J,OAAO,CAAC+J,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACrG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOqG,KAAK;QACd,CAAC,CACH,CAAC;QAEDf,iBAAiB,CAACY,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLR,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAO1H,GAAQ,EAAE;MACjBpB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEyB,GAAG,CAAC;MACrD0H,gBAAgB,CAAC1H,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRuH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACAlM,SAAS,CAAC,MAAM;IACd,MAAMsN,MAAM,GAAGxD,aAAa,CAACyD,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtE3B,sBAAsB,CAACwB,MAAM,CAAC;EAChC,CAAC,EAAE,CAACxD,aAAa,CAAC,CAAC;;EAEnB;EACA9J,SAAS,CAAC,MAAM;IACdqM,mBAAmB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC9C,WAAW,EAAE8C,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExC;EACArM,SAAS,CAAC,MAAM;IACd,MAAM0N,kBAAkB,GAAIX,KAAiB,IAAK;MAChD,IAAI1B,gBAAgB,EAAE;QACpB,MAAMsC,MAAM,GAAGZ,KAAK,CAACY,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtDtC,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAEDuC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACrC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,IAAI,CAAC9B,WAAW,EAAE;IAChB,oBACEjH,OAAA;MAAK0C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpByI,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE;MACd,CAAE;MAAAtI,QAAA,eACArD,OAAA;QAAK0C,KAAK,EAAE;UACViJ,UAAU,EAAE,OAAO;UACnBC,OAAO,EAAE,MAAM;UACfzG,YAAY,EAAE,MAAM;UACpBtB,MAAM,EAAE,mBAAmB;UAC3BP,SAAS,EAAE,QAAQ;UACnBuI,QAAQ,EAAE;QACZ,CAAE;QAAAxI,QAAA,gBACArD,OAAA;UAAI0C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF5D,OAAA;UAAG0C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ5D,OAAA;UACEkG,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAAC,GAAG,CAAE;UAC7BlE,KAAK,EAAE;YACLiJ,UAAU,EAAE,mDAAmD;YAC/DvI,KAAK,EAAE,OAAO;YACdS,MAAM,EAAE,MAAM;YACdsB,YAAY,EAAE,MAAM;YACpByG,OAAO,EAAE,gBAAgB;YACzBpI,QAAQ,EAAE,UAAU;YACpBM,UAAU,EAAE,KAAK;YACjBsB,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMkI,YAAY,GAAGA,CAAC1H,MAAgC,EAAE2H,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAG5H,MAAM,CAACgC,GAAG,CAAC6F,GAAG,IAAI3N,WAAW,CAAC2N,GAAG,CAACtG,SAAS,CAAC,CAAC,CAACsF,MAAM,CAACiB,OAAO,CAAa;IAC3F9C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiD,oBAAoB,GAAGA,CAACH,SAAmB,EAAED,YAAoB,KAAK;IAC1E3C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAErN;MACR,CAAC;MACD,SAAS,EAAE;QACTyM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEvN;MACR,CAAC;MACD,QAAQ,EAAE;QACR2M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEpN;MACR,CAAC;MACD,WAAW,EAAE;QACXwM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEnN;MACR,CAAC;MACD,QAAQ,EAAE;QACRuM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEjN;MACR,CAAC;MACD,WAAW,EAAE;QACXqM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAElN;MACR;IACF,CAAC;IAED,OAAOiN,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMH,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE9M;MACR,CAAC;MACD,cAAc,EAAE;QACdkM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE/M;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBmM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE7M;MACR,CAAC;MACD,cAAc,EAAE;QACdiM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEjN;MACR,CAAC;MACD,YAAY,EAAE;QACZqM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE5M;MACR,CAAC;MACD,SAAS,EAAE;QACTgM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEhN;MACR;IACF,CAAC;IAED,OAAO+M,MAAM,CAACG,eAAe,CAAwB,IAAIH,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;;EAEA;EACA,MAAMI,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF3L,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,sCAAsC,EAAE0F,YAAY,CAACC,eAAe,CAAC;MACvG5L,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0L,YAAY,CAACE,aAAa,CAAC;MACzE7L,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,gBAAgB,EAAE;QAAE6F,EAAE,EAAE3F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAE;QAAEC,IAAI,EAAE9F;MAAY,CAAC,CAAC;MAE/F,IAAI0F,YAAY,CAACE,aAAa,EAAE;QAC9B;QACA7L,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,uBAAuB,CAAC;QAC1D,MAAMS,kBAAkB,CAACiF,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACA5L,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,qBAAqB,CAAC;QACxD,MAAMQ,gBAAgB,CAACkF,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEA5L,OAAO,CAACC,GAAG,CAAC,aAAagG,WAAW,gCAAgC,CAAC;IACvE,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,WAAWsG,WAAW,uBAAuB,EAAEtG,KAAK,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMqM,wBAAwB,GAAG,MAAOvC,KAAU,IAAK;IACrD,IAAI;MACFzJ,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,wCAAwC,EAAEwD,KAAK,CAACE,WAAW,CAAC;MAC9F3J,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwJ,KAAK,CAACwC,gBAAgB,CAAC;MACxEjM,OAAO,CAACC,GAAG,CAAC,WAAWgG,WAAW,gBAAgB,EAAE;QAAE6F,EAAE,EAAE3F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAE;QAAEC,IAAI,EAAE9F;MAAY,CAAC,CAAC;MAE/F,MAAM5F,QAAQ,GAAG,MAAMzD,uBAAuB,CAACsP,UAAU,CAACzC,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACwC,gBAAgB,IAAI,KAAK,CAAC;MAE7G,IAAI5L,QAAQ,CAAC8I,OAAO,EAAE;QACpB;QACAT,iBAAiB,CAACyD,UAAU,IAC1BA,UAAU,CAAC/G,GAAG,CAACnC,CAAC,IACdA,CAAC,CAAC0G,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAG1G,CAAC;UACJgJ,gBAAgB,EAAE,CAACxC,KAAK,CAACwC,gBAAgB;UACzCG,cAAc,EAAE3C,KAAK,CAACwC,gBAAgB,GAClC,CAACxC,KAAK,CAAC2C,cAAc,IAAI,CAAC,IAAI,CAAC,GAC/B,CAAC3C,KAAK,CAAC2C,cAAc,IAAI,CAAC,IAAI;QACpC,CAAC,GACDnJ,CACN,CACF,CAAC;QACDjD,OAAO,CAACC,GAAG,CAAC,aAAagG,WAAW,+CAA+C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,WAAWsG,WAAW,gCAAgC,EAAEtG,KAAK,CAAC;IAC9E;EACF,CAAC;;EAED;EACA,MAAM0M,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMhG,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAO1G,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACA,MAAM2M,YAAY,GAAGrG,WAAW,KAAK,OAAO,GAAG,cAAc,GAAG,gBAAgB;MAChFxF,MAAM,CAACC,QAAQ,CAAC6L,IAAI,GAAGD,YAAY;IACrC;EACF,CAAC;;EAED;;EAIA;EACA,MAAME,qBAAqB,GAAGhG,aAAa,CAACyD,MAAM,CAAC0B,YAAY,IAAI;IAAA,IAAAc,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAACpF,UAAU,IAC/BoE,YAAY,CAACiB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC,IACnElB,YAAY,CAACoB,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAAC7F,cAAc,IACrC,EAAAsF,qBAAA,GAAAd,YAAY,CAACsB,WAAW,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BS,QAAQ,CAAC,CAAC,MAAK/F,cAAc;IAEzD,MAAMgG,iBAAiB,GAAG,CAAC9F,gBAAgB,IACzC,EAAAqF,qBAAA,GAAAf,YAAY,CAACyB,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAK7F,gBAAgB;IAE3D,OAAOsF,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAG5E,cAAc,CAACwB,MAAM,CAACR,KAAK,IAAI;IAC5D,MAAMkD,aAAa,GAAG,CAACpF,UAAU,IAC/BkC,KAAK,CAACmD,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC,IAC3DpD,KAAK,CAAC6D,WAAW,IAAI7D,KAAK,CAAC6D,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACJ,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1C,MAAME,cAAc,GAAG,IAAIP,IAAI,CAAC/D,KAAK,CAACuE,UAAU,CAAC;IACjD,MAAMC,oBAAoB,GAAGF,cAAc,CAACL,WAAW,CAAC,CAAC,GAAG,GAAG,GAC7DC,MAAM,CAACI,cAAc,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAC5DF,MAAM,CAACI,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMK,kBAAkB,GAAGzE,KAAK,CAAC0E,QAAQ,GAAG,CAAC,MAAM;MACjD,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAAC/D,KAAK,CAAC0E,QAAQ,CAAC;MACxC,OAAOC,OAAO,CAACV,WAAW,CAAC,CAAC,GAAG,GAAG,GAChCC,MAAM,CAACS,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACrDF,MAAM,CAACS,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9C,CAAC,EAAE,CAAC,GAAGI,oBAAoB;;IAE3B;IACA,MAAMI,aAAa,GAAGZ,eAAe,IAAIQ,oBAAoB,IAAIR,eAAe,IAAIS,kBAAkB;;IAEtG;IACA,MAAMI,QAAQ,GAAI7E,KAAK,CAAS8E,SAAS,KAAK,CAAC;IAE/C,OAAO5B,aAAa,IAAI0B,aAAa,IAAIC,QAAQ;EACnD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGhC,qBAAqB;EAClD,MAAMiC,aAAa,GAAGpB,sBAAsB;;EAI5C;EACA;EACA;EACA;EACA;;EAEA,oBACErO,OAAA;IAAK0C,KAAK,EAAE;MACVgJ,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/D1G,QAAQ,EAAE;IACZ,CAAE;IAAA5B,QAAA,gBAEArD,OAAA;MAAK0C,KAAK,EAAE;QACVuC,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTyJ,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAlM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL5D,OAAA;MAAK0C,KAAK,EAAE;QAAEuC,QAAQ,EAAE,UAAU;QAAE2K,MAAM,EAAE;MAAE,CAAE;MAAAvM,QAAA,gBAE9CrD,OAAA;QAAQ0C,KAAK,EAAE;UACbiJ,UAAU,EAAE,OAAO;UACnBkE,YAAY,EAAE,mBAAmB;UACjC5K,QAAQ,EAAE,QAAQ;UAClBa,GAAG,EAAE,CAAC;UACN8J,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAAzM,QAAA,eACArD,OAAA;UAAK0C,KAAK,EAAE;YACVkJ,OAAO,EAAE,QAAQ;YACjBtG,MAAM,EAAE,MAAM;YACdtC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBAEArD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,QAAQ;cACbqK,QAAQ,EAAE;YACZ,CAAE;YAAA1M,QAAA,gBACArD,OAAA;cACEgE,GAAG,EAAC,iBAAiB;cACrBvB,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACL2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5D,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAI0C,KAAK,EAAE;kBACTsN,MAAM,EAAE,CAAC;kBACTxM,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChB6M,UAAU,EAAE;gBACd,CAAE;gBAAA5M,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRsN,MAAM,EAAE,CAAC;kBACTxM,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE,SAAS;kBAChB6M,UAAU,EAAE;gBACd,CAAE;gBAAA5M,QAAA,EACC4D,WAAW,KAAK,OAAO,GAAG,gBAAgB,GAAG;cAAkB;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAK0C,KAAK,EAAE;cACVwN,IAAI,EAAE,CAAC;cACPrE,QAAQ,EAAE,OAAO;cACjBmE,MAAM,EAAE;YACV,CAAE;YAAA3M,QAAA,eACArD,OAAA;cAAK0C,KAAK,EAAE;gBAAEuC,QAAQ,EAAE;cAAW,CAAE;cAAA5B,QAAA,gBACnCrD,OAAA,CAACrB,MAAM;gBACLwR,IAAI,EAAE,EAAG;gBACTzN,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBc,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVD,SAAS,EAAE,kBAAkB;kBAC7BzC,KAAK,EAAE;gBACT;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5D,OAAA;gBACEoQ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,aAAa;gBACzBC,KAAK,EAAE/H,UAAW;gBAClBgI,QAAQ,EAAGtM,CAAC,IAAKuE,aAAa,CAACvE,CAAC,CAACoH,MAAM,CAACiF,KAAK,CAAE;gBAC/C5N,KAAK,EAAE;kBACL2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdsG,OAAO,EAAE,eAAe;kBACxB/H,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,MAAM;kBACpBwG,UAAU,EAAE,SAAS;kBACrBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgN,OAAO,EAAE,MAAM;kBACf/K,UAAU,EAAE;gBACd,CAAE;gBACFgL,OAAO,EAAGxM,CAAC,IAAK;kBACdA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;kBAC7CzM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,OAAO;kBAC1C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFa,MAAM,EAAG1M,CAAC,IAAK;kBACbA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;kBAC7CzM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;kBAC5C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAArM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXqK,QAAQ,EAAE,OAAO;cACjB7M,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAGArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbkG,OAAO,EAAE,QAAQ;gBACjBD,UAAU,EAAE,SAAS;gBACrBxG,YAAY,EAAE,MAAM;gBACpBtB,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACArD,OAAA;gBACEsQ,KAAK,EAAEnI,cAAe;gBACtBoI,QAAQ,EAAGtM,CAAC,IAAKmE,iBAAiB,CAACnE,CAAC,CAACoH,MAAM,CAACiF,KAAK,CAAE;gBACnD5N,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,OAAO;kBACnBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgN,OAAO,EAAE,MAAM;kBACfpL,MAAM,EAAE,SAAS;kBACjB2K,QAAQ,EAAE;gBACZ,CAAE;gBAAA1M,QAAA,gBAEFrD,OAAA;kBAAQsQ,KAAK,EAAC,EAAE;kBAAAjN,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvC2D,UAAU,CACR0D,MAAM,CAAC2F,QAAQ;gBACd;gBACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC9C,QAAQ,CAAC8C,QAAQ,CAACC,IAAI,CACjG,CAAC,CACAzK,GAAG,CAACwK,QAAQ,iBACX5Q,OAAA;kBAAmCsQ,KAAK,EAAEM,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAA7K,QAAA,EACvEuN,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC3C,WAAW;kBAAAxK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eAET5D,OAAA;gBACEsQ,KAAK,EAAEjI,gBAAiB;gBACxBkI,QAAQ,EAAGtM,CAAC,IAAKqE,mBAAmB,CAACrE,CAAC,CAACoH,MAAM,CAACiF,KAAK,CAAE;gBACrD5N,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,OAAO;kBACnBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBgN,OAAO,EAAE,MAAM;kBACfpL,MAAM,EAAE,SAAS;kBACjB2K,QAAQ,EAAE;gBACZ,CAAE;gBAAA1M,QAAA,gBAEFrD,OAAA;kBAAQsQ,KAAK,EAAC,EAAE;kBAAAjN,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC5D,OAAA;kBAAQsQ,KAAK,EAAC,IAAI;kBAAAjN,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC5D,OAAA;kBAAQsQ,KAAK,EAAC,IAAI;kBAAAjN,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAAC2E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDrI,OAAA;gBACEkG,OAAO,EAAEA,CAAA,KAAM;kBACbsC,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACF5F,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,SAAS;kBACrBvI,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBsB,MAAM,EAAE,SAAS;kBACjBK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACF7I,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAAtI,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5D,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,GAEC4D,WAAW,KAAK,OAAO,gBAAGjH,OAAA,CAAC9B,gBAAgB;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5D,OAAA,CAAC7B,uBAAuB;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG7E5D,OAAA;gBAAK0C,KAAK,EAAE;kBAAEuC,QAAQ,EAAE;gBAAW,CAAE;gBAAC,iBAAc,eAAe;gBAAA5B,QAAA,gBACjErD,OAAA;kBACEkG,OAAO,EAAEA,CAAA,KAAM8C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDrG,KAAK,EAAE;oBACLM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbkG,OAAO,EAAE,cAAc;oBACvBD,UAAU,EAAE,OAAO;oBACnB9H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB/B,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE,eAAe;oBAC3BqK,SAAS,EAAE;kBACb,CAAE;kBACFjN,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;oBAC7CzM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,kCAAkC;kBACtE,CAAE;kBACFhN,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;oBAC7CzM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,8BAA8B;kBAClE,CAAE;kBAAAzM,QAAA,gBAEFrD,OAAA,CAACH,IAAI;oBAACsQ,IAAI,EAAE;kBAAG;oBAAA1M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClB5D,OAAA;oBAAAqD,QAAA,EAAO,CAAA8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2J,SAAS,MAAK7J,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;kBAAC;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5D,OAAA,CAACJ,WAAW;oBAACuQ,IAAI,EAAE,EAAG;oBAACzN,KAAK,EAAE;sBAC5BmD,SAAS,EAAEkD,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;sBAC/DtD,UAAU,EAAE;oBACd;kBAAE;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGRmF,gBAAgB,iBACf/I,OAAA;kBAAK0C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,CAAC;oBACRjC,SAAS,EAAE,QAAQ;oBACnB4H,UAAU,EAAE,OAAO;oBACnB9H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB2K,SAAS,EAAE,iCAAiC;oBAC5CC,QAAQ,EAAE,OAAO;oBACjBH,MAAM,EAAE,IAAI;oBACZ1K,QAAQ,EAAE;kBACZ,CAAE;kBAAA7B,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVkJ,OAAO,EAAE,cAAc;sBACvBiE,YAAY,EAAE,mBAAmB;sBACjClE,UAAU,EAAE;oBACd,CAAE;oBAAAtI,QAAA,gBACArD,OAAA;sBAAK0C,KAAK,EAAE;wBACVc,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE;sBACT,CAAE;sBAAAC,QAAA,GACC8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2J,SAAS,EAAC,GAAC,EAAC3J,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4J,QAAQ;oBAAA;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACN5D,OAAA;sBAAK0C,KAAK,EAAE;wBACVc,QAAQ,EAAE,SAAS;wBACnBJ,KAAK,EAAE;sBACT,CAAE;sBAAAC,QAAA,EACC8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6J;oBAAK;sBAAAvN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEkJ,OAAO,EAAE;oBAAW,CAAE;oBAAAvI,QAAA,gBAClCrD,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM+K,aAAa,GAAGhK,WAAW,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;wBACzFL,QAAQ,CAACqK,aAAa,CAAC;wBACvBjI,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFtG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdkG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAtI,QAAA,gBAEFrD,OAAA,CAACf,eAAe;wBAACkR,IAAI,EAAE;sBAAG;wBAAA1M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAE/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAET5D,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAM;wBACbmH,YAAY,CAAC,CAAC;wBACdrE,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFtG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdkG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAtI,QAAA,gBAEFrD,OAAA,CAACF,MAAM;wBAACqQ,IAAI,EAAE;sBAAG;wBAAA1M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT5D,OAAA;QAAK0C,KAAK,EAAE;UACVkJ,OAAO,EAAE,MAAM;UACf5I,OAAO,EAAE,MAAM;UACf0C,GAAG,EAAE,MAAM;UACXzC,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEArD,OAAA;UAAK0C,KAAK,EAAE;YACV2C,KAAK,EAAE,OAAO;YACd6L,UAAU,EAAE;UACd,CAAE;UAAA7N,QAAA,eACArD,OAAA;YAAK0C,KAAK,EAAE;cACViJ,UAAU,EAAE,OAAO;cACnBxG,YAAY,EAAE,MAAM;cACpBtB,MAAM,EAAE,mBAAmB;cAC3BqB,QAAQ,EAAE,QAAQ;cAClBD,QAAQ,EAAE,QAAQ;cAClBa,GAAG,EAAE;YACP,CAAE;YAAAzC,QAAA,gBAEArD,OAAA;cAAK0C,KAAK,EAAE;gBACVkJ,OAAO,EAAE,oBAAoB;gBAC7BiE,YAAY,EAAE;cAChB,CAAE;cAAAxM,QAAA,gBACArD,OAAA;gBAAK0C,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE,SAAS;kBACdnC,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,gBACArD,OAAA,CAACpB,GAAG;kBAACuR,IAAI,EAAE,EAAG;kBAACzN,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C5D,OAAA;kBAAI0C,KAAK,EAAE;oBACTsN,MAAM,EAAE,CAAC;oBACTxM,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRsN,MAAM,EAAE,CAAC;kBACTxM,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN5D,OAAA;cAAK0C,KAAK,EAAE;gBAAEkJ,OAAO,EAAE;cAAO,CAAE;cAAAvI,QAAA,EAC7BkG,mBAAmB,CAAChF,MAAM,GAAG,CAAC,gBAC7BvE,OAAA,CAAAE,SAAA;gBAAAmD,QAAA,GACGkG,mBAAmB,CAAC9E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAEuG,YAAY,IAAK;kBACrD;kBACA,MAAMwE,OAAO,GAAGxE,YAAY,CAACyE,QAAQ;kBACrC,MAAM/E,YAAY,GAAG,CAACM,YAAY,CAAC0E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAGnF,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACErM,OAAA;oBAEE0C,KAAK,EAAE;sBACLkJ,OAAO,EAAE,MAAM;sBACfD,UAAU,EAAEwF,OAAO,GAAG,SAAS,GAAG,SAAS;sBAC3ChM,YAAY,EAAE,MAAM;sBACpBtB,MAAM,EAAEsN,OAAO,GAAG,mBAAmB,GAAG,mBAAmB;sBAC3D5N,YAAY,EAAE,MAAM;sBACpB6B,MAAM,EAAE,SAAS;sBACjBK,UAAU,EAAE;oBACd,CAAE;oBACF5C,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAGwF,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClElN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAGS,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACFrO,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAGwF,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClElN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAGS,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACFjL,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC6D,YAAY,CAAE;oBAAAtJ,QAAA,eAEnDrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACArD,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbqG,UAAU,EAAEwF,OAAO,GAAG,SAAS,GAAII,aAAa,CAAC5F,UAAU,CAACmC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAU;0BACxG3I,YAAY,EAAE,KAAK;0BACnBpB,SAAS,EAAE,QAAQ;0BACnBmN,UAAU,EAAE;wBACd;sBAAE;wBAAAzN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL5D,OAAA;wBAAK0C,KAAK,EAAE;0BAAEwN,IAAI,EAAE;wBAAE,CAAE;wBAAA7M,QAAA,gBACtBrD,OAAA;0BAAI0C,KAAK,EAAE;4BACTsN,MAAM,EAAE,cAAc;4BACtBxM,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE,SAAS;4BAChB6M,UAAU,EAAE;0BACd,CAAE;0BAAA5M,QAAA,EACCsJ,YAAY,CAACiB;wBAAK;0BAAAnK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL5D,OAAA;0BAAG0C,KAAK,EAAE;4BACRsN,MAAM,EAAE,cAAc;4BACtBxM,QAAQ,EAAE,QAAQ;4BAClBJ,KAAK,EAAE,SAAS;4BAChB6M,UAAU,EAAE;0BACd,CAAE;0BAAA5M,QAAA,EACCsJ,YAAY,CAACoB,OAAO,CAACxJ,MAAM,GAAG,EAAE,GAC7B,GAAGoI,YAAY,CAACoB,OAAO,CAACyD,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7C7E,YAAY,CAACoB;wBAAO;0BAAAtK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ5D,OAAA;0BAAK0C,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE,QAAQ;4BACblC,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;4BAACsR,IAAI,EAAE;0BAAG;4BAAA1M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB5D,OAAA;4BAAAqD,QAAA,EAAO,IAAImL,IAAI,CAAC7B,YAAY,CAAC8E,UAAU,CAAC,CAACC,kBAAkB,CAAC;0BAAC;4BAAAjO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhED+I,YAAY,CAACC,eAAe;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAED2F,mBAAmB,CAAChF,MAAM,GAAG,CAAC,iBAC7BvE,OAAA;kBAAQ0C,KAAK,EAAE;oBACb2C,KAAK,EAAE,MAAM;oBACbuG,OAAO,EAAE,SAAS;oBAClB/H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,KAAK;oBACnBwG,UAAU,EAAE,OAAO;oBACnBvI,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE;kBACd,CAAE;kBACF5C,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;oBAC5C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACF5N,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,OAAO;oBAC1C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAArN,QAAA,GAAC,WACQ,EAACkG,mBAAmB,CAAChF,MAAM,EAAC,eACvC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH5D,OAAA;gBAAK0C,KAAK,EAAE;kBACVkJ,OAAO,EAAE,WAAW;kBACpBtI,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,gBACArD,OAAA,CAACpB,GAAG;kBAACuR,IAAI,EAAE,EAAG;kBAACzN,KAAK,EAAE;oBAAEa,YAAY,EAAE,QAAQ;oBAAEoO,OAAO,EAAE;kBAAI;gBAAE;kBAAAlO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE5D,OAAA;kBAAG0C,KAAK,EAAE;oBAAEsN,MAAM,EAAE,CAAC;oBAAExM,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,EAAC;gBAE/C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAK0C,KAAK,EAAE;YAAEwN,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAA1M,QAAA,GAEpC,CAAC5C,OAAO,IAAIkJ,eAAe,kBAC1B3J,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpByI,SAAS,EAAE;YACb,CAAE;YAAArI,QAAA,eACArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBlD,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA;gBAAK0C,KAAK,EAAE;kBACV2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,MAAM,EAAE,kCAAkC;kBAC1C+N,SAAS,EAAE,mBAAmB;kBAC9BzM,YAAY,EAAE,KAAK;kBACnB0M,SAAS,EAAE;gBACb;cAAE;gBAAApO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,MAAM;kBAChBM,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAACjD,KAAK,IAAIkJ,aAAa,KAAK,CAACpJ,OAAO,IAAI,CAACkJ,eAAe,iBACvD3J,OAAA;YAAK0C,KAAK,EAAE;cACVkJ,OAAO,EAAE,MAAM;cACfD,UAAU,EAAE,wBAAwB;cACpC9H,MAAM,EAAE,kCAAkC;cAC1CsB,YAAY,EAAE,MAAM;cACpB7B,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdqG,UAAU,EAAE,wBAAwB;gBACpCxG,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8M,MAAM,EAAE;cACV,CAAE;cAAA3M,QAAA,eACArD,OAAA,CAAClB,aAAa;gBAACqR,IAAI,EAAE,EAAG;gBAAC/M,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN5D,OAAA;cAAI0C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB4M,MAAM,EAAE,cAAc;gBACtBxM,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAG0C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB4M,MAAM,EAAE,cAAc;gBACtBxM,QAAQ,EAAE;cACZ,CAAE;cAAAH,QAAA,EACC1C,KAAK,IAAIkJ;YAAa;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ5D,OAAA;cACEkG,OAAO,EAAEA,CAAA,KAAM;gBACb0B,oBAAoB,CAAC,CAAC;gBACtBmC,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFrH,KAAK,EAAE;gBACLiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpByG,OAAO,EAAE,gBAAgB;gBACzBpI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhN,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzM,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAACnD,OAAO,IAAI,CAACkJ,eAAe,IAAI,CAAChJ,KAAK,IAAI,CAACkJ,aAAa,IACxD2F,oBAAoB,CAACjL,MAAM,KAAK,CAAC,IAAIkL,aAAa,CAAClL,MAAM,KAAK,CAAC,iBAC9DvE,OAAA;YAAK0C,KAAK,EAAE;cACVkJ,OAAO,EAAE,WAAW;cACpBtI,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdqG,UAAU,EAAE,mDAAmD;gBAC/DxG,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8M,MAAM,EAAE;cACV,CAAE;cAAA3M,QAAA,eACArD,OAAA,CAACtB,SAAS;gBAACyR,IAAI,EAAE,EAAG;gBAAC/M,KAAK,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN5D,OAAA;cAAI0C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB4M,MAAM,EAAE,YAAY;gBACpBxM,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAG0C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB4M,MAAM,EAAE,YAAY;gBACpBxM,QAAQ,EAAE,MAAM;gBAChByM,UAAU,EAAE,KAAK;gBACjBpE,QAAQ,EAAE,OAAO;gBACjBiG,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAA1O,QAAA,EACCkF,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAAC2E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDrI,OAAA;cACEkG,OAAO,EAAEA,CAAA,KAAM;gBACbsC,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACF5F,KAAK,EAAE;gBACLiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpByG,OAAO,EAAE,gBAAgB;gBACzBpI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhN,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzM,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAMA,CAACnD,OAAO,IAAI,CAACkJ,eAAe,KAAK6F,oBAAoB,CAACjL,MAAM,GAAG,CAAC,IAAIkL,aAAa,CAAClL,MAAM,GAAG,CAAC,CAAC,iBAC5FvE,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAECoM,aAAa,CAAClL,MAAM,GAAG,CAAC,iBACvBvE,OAAA,CAAAE,SAAA;cAAAmD,QAAA,EACGoM,aAAa,CAACrJ,GAAG,CAACqE,KAAK,iBACtBzK,OAAA;gBAEE0C,KAAK,EAAE;kBACLiJ,UAAU,EAAE,2BAA2B;kBACvCxG,YAAY,EAAE,MAAM;kBACpByG,OAAO,EAAE,QAAQ;kBACjB/H,MAAM,EAAE,8BAA8B;kBACtCmO,cAAc,EAAE,YAAY;kBAC5BlC,SAAS,EAAE,gCAAgC;kBAC3CrK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACFhN,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAAzM,QAAA,gBAGFrD,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACV2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdqG,UAAU,EAAE,mDAAmD;sBAC/DxG,YAAY,EAAE,MAAM;sBACpBnC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBgO,UAAU,EAAE;oBACd,CAAE;oBAAA7N,QAAA,eACArD,OAAA,CAACnB,QAAQ;sBAACsR,IAAI,EAAE,EAAG;sBAAC/M,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEwN,IAAI,EAAE;oBAAE,CAAE;oBAAA7M,QAAA,gBACtBrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMoJ,eAAe,GAAGhC,KAAK,CAAC4G,aAAa,IAAI,cAAc;wBAC7D,MAAMY,YAAY,GAAGzF,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAMyF,aAAa,GAAGD,YAAY,CAAC1F,IAAI;wBAEvC,oBACEvM,OAAA;0BAAM0C,KAAK,EAAE;4BACXiJ,UAAU,EAAEsG,YAAY,CAACtG,UAAU;4BACnCvI,KAAK,EAAE,OAAO;4BACdI,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjB8H,OAAO,EAAE,iBAAiB;4BAC1BzG,YAAY,EAAE,MAAM;4BACpBgN,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtBpP,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACArD,OAAA,CAACkS,aAAa;4BAAC/B,IAAI,EAAE,EAAG;4BAAC/M,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxC6I,eAAe;wBAAA;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,eAEJ5D,OAAA;wBAAK0C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,SAAS;0BACdtC,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,gBACArD,OAAA,CAACnB,QAAQ;0BAACsR,IAAI,EAAE;wBAAG;0BAAA1M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAI4K,IAAI,CAAC/D,KAAK,CAACuE,UAAU,CAAC,CAAC0C,kBAAkB,CAAC,OAAO,EAAE;0BACtDW,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAA/O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN5D,OAAA;sBAAI0C,KAAK,EAAE;wBACTsN,MAAM,EAAE,cAAc;wBACtBxM,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB6M,UAAU,EAAE;sBACd,CAAE;sBAAA5M,QAAA,EACCoH,KAAK,CAACmD;oBAAK;sBAAAnK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAAC,MAAM;kBACN;kBACA,MAAM6O,cAAwB,GAAG,EAAE;kBAEnC,IAAKhI,KAAK,CAASrG,MAAM,IAAKqG,KAAK,CAASrG,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DkG,KAAK,CAASrG,MAAM,CAACsO,OAAO,CAAEzG,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACtG,SAAS,EAAE;wBACjB;wBACA,MAAMpF,QAAQ,GAAGjC,WAAW,CAAC2N,GAAG,CAACtG,SAAS,CAAC;wBAC3C,IAAIpF,QAAQ,EAAE;0BACZkS,cAAc,CAACE,IAAI,CAACpS,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAOkS,cAAc,CAAClO,MAAM,GAAG,CAAC,gBAC9BvE,OAAA;oBAAK0C,KAAK,EAAE;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAAF,QAAA,eACnCrD,OAAA,CAAC5B,oBAAoB;sBACnBgG,MAAM,EAAEqO,cAAc,CAACxH,MAAM,CAACiB,OAAO,CAAc;sBACnD7H,SAAS,EAAEoG,KAAK,CAACmD,KAAM;sBACvBgF,UAAU,EAAE,CAAE;sBACdtO,YAAY,EAAGQ,KAAK,IAAK;wBACvB,MAAM+N,cAAc,GAAGJ,cAAc,CAACxH,MAAM,CAACiB,OAAO,CAAa;wBACjEC,oBAAoB,CAAC0G,cAAc,EAAE/N,KAAK,CAAC;sBAC7C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,EAGH6G,KAAK,CAAC6D,WAAW,iBAChBtO,OAAA;kBAAK0C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnByM,UAAU,EAAE,KAAK;oBACjB1M,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCoH,KAAK,CAAC6D;gBAAW;kBAAA7K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbkG,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,0BAA0B;oBACtCxG,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE;kBACZ,CAAE;kBAAAH,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;sBAACsR,IAAI,EAAE;oBAAG;sBAAA1M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB5D,OAAA;sBAAAqD,QAAA,EACGoH,KAAK,CAAC0E,QAAQ,IAAI1E,KAAK,CAAC0E,QAAQ,KAAK1E,KAAK,CAACuE,UAAU,GAClD,GAAG,IAAIR,IAAI,CAAC/D,KAAK,CAACuE,UAAU,CAAC,CAAC0C,kBAAkB,CAAC,CAAC,MAAM,IAAIlD,IAAI,CAAC/D,KAAK,CAAC0E,QAAQ,CAAC,CAACuC,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAIlD,IAAI,CAAC/D,KAAK,CAACuE,UAAU,CAAC,CAAC0C,kBAAkB,CAAC;oBAAC;sBAAAjO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAEL6G,KAAK,CAAC4G,aAAa,iBAClBrR,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,eACArD,OAAA;sBAAM0C,KAAK,EAAE;wBACXkJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAE,yBAAyB;wBACrCxG,YAAY,EAAE,KAAK;wBACnB3B,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCoH,KAAK,CAAC4G;oBAAa;sBAAA5N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGN5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB+O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE,8BAA8B;oBACzC5O,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBAEArD,OAAA;oBACEkG,OAAO,EAAEA,CAAA,KAAM8G,wBAAwB,CAACvC,KAAK,CAAE;oBAC/C/H,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbiG,UAAU,EAAE,MAAM;sBAClB9H,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAGqH,KAAK,CAASwC,gBAAgB,GAAG,SAAS,GAAG,SAAS;sBAC9D7H,MAAM,EAAE,SAAS;sBACjBwG,OAAO,EAAE,QAAQ;sBACjBzG,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;oBAC1D,CAAE;oBACF7I,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAAtI,QAAA,gBAEFrD,OAAA,CAACjB,KAAK;sBACJoR,IAAI,EAAE,EAAG;sBACT4C,IAAI,EAAGtI,KAAK,CAASwC,gBAAgB,GAAG,SAAS,GAAG;oBAAO;sBAAAxJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACF5D,OAAA;sBAAAqD,QAAA,EAAQoH,KAAK,CAAS2C,cAAc,IAAI;oBAAC;sBAAA3J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,EAGP6G,KAAK,CAASuI,cAAc,iBAC5BhT,OAAA;oBACEkG,OAAO,EAAEA,CAAA,KAAM0C,uBAAuB,CACpCD,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;oBACFjI,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbiG,UAAU,EAAE,MAAM;sBAClB9H,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAEuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;sBACzEvF,MAAM,EAAE,SAAS;sBACjBwG,OAAO,EAAE,QAAQ;sBACjBzG,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;sBACxD1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBACF7H,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;sBACzC1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBAAAtH,QAAA,gBAEFrD,OAAA,CAAClB,aAAa;sBAACqR,IAAI,EAAE;oBAAG;sBAAA1M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B5D,OAAA;sBAAAqD,QAAA,EAAQoH,KAAK,CAASwI,aAAa,IAAI;oBAAC;sBAAAxP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGL+E,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,IAAKF,KAAK,CAASuI,cAAc,iBAC1EhT,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB+O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE;kBACb,CAAE;kBAAAvO,QAAA,EACC4D,WAAW,KAAK,OAAO,gBACtBjH,OAAA,CAAChC,mBAAmB;oBAClBkV,UAAU,EAAEzI,KAAK,CAACE,WAAY;oBAC9BwI,aAAa,EAAG1I,KAAK,CAASuI,cAAe;oBAC7CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/BuG,eAAe,EAAC;kBAAO;oBAAA5P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF5D,OAAA,CAAC/B,cAAc;oBACbiV,UAAU,EAAEzI,KAAK,CAACE,WAAY;oBAC9BwI,aAAa,EAAG1I,KAAK,CAASuI,cAAe;oBAC7CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/BuG,eAAe,EAAC;kBAAS;oBAAA5P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GA/RI,SAAS6G,KAAK,CAACE,WAAW,EAAE;gBAAAlH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgS9B,CACN;YAAC,gBACF,CACH,EAGA4L,oBAAoB,CAACjL,MAAM,GAAG,CAAC,iBAC9BvE,OAAA,CAAAE,SAAA;cAAAmD,QAAA,EACGmM,oBAAoB,CAACpJ,GAAG,CAACuG,YAAY,iBACpC3M,OAAA;gBAEE8M,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;gBACnDjK,SAAS,EAAEsF,kBAAkB,IAAIC,YAAY,KAAK,gBAAgByE,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;gBAC9IlK,KAAK,EAAE;kBACLiJ,UAAU,EAAE,2BAA2B;kBACvCxG,YAAY,EAAE,MAAM;kBACpByG,OAAO,EAAE,QAAQ;kBACjB/H,MAAM,EAAE8I,YAAY,CAACxB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClC6G,cAAc,EAAE,YAAY;kBAC5BlC,SAAS,EAAEnD,YAAY,CAACxB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpC1F,UAAU,EAAE,2CAA2C;kBACvDR,QAAQ,EAAE;gBACZ,CAAE;gBACFpC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAGnD,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFrI,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoN,SAAS,GAAGnD,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAA9H,QAAA,GAGDsJ,YAAY,CAACxB,SAAS,iBACrBnL,OAAA;kBAAK0C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACb2F,UAAU,EAAE,mDAAmD;oBAC/DvI,KAAK,EAAE,OAAO;oBACdwI,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBd,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,SAAS;oBACdoK,SAAS,EAAE;kBACb,CAAE;kBAAAzM,QAAA,gBACArD,OAAA,CAACpB,GAAG;oBAACuR,IAAI,EAAE;kBAAG;oBAAA1M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,GACC,CAAC,MAAM;oBACN,IAAIsJ,YAAY,CAACyE,QAAQ,EAAE;sBACzB,oBACEpR,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdqG,UAAU,EAAE,mDAAmD;0BAC/DxG,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBgO,UAAU,EAAE;wBACd,CAAE;wBAAA7N,QAAA,eACArD,OAAA,CAACZ,aAAa;0BAAC+Q,IAAI,EAAE,EAAG;0BAAC/M,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAMyI,YAAY,GAAG,CAACM,YAAY,CAAC0E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAGnF,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAM6F,aAAa,GAAGX,aAAa,CAAChF,IAAI;sBAExC,oBACEvM,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdqG,UAAU,EAAE4F,aAAa,CAAC5F,UAAU;0BACpCxG,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBgO,UAAU,EAAE;wBACd,CAAE;wBAAA7N,QAAA,eACArD,OAAA,CAACkS,aAAa;0BAAC/B,IAAI,EAAE,EAAG;0BAAC/M,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEwN,IAAI,EAAE;oBAAE,CAAE;oBAAA7M,QAAA,gBACtBrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE,QAAQ;wBACtB+P,QAAQ,EAAE;sBACZ,CAAE;sBAAAjQ,QAAA,GACC,CAAC,MAAM;wBACN,IAAIsJ,YAAY,CAACyE,QAAQ,EAAE;0BACzB,oBACEpR,OAAA;4BAAM0C,KAAK,EAAE;8BACXiJ,UAAU,EAAE,mDAAmD;8BAC/DvI,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjB8H,OAAO,EAAE,iBAAiB;8BAC1BzG,YAAY,EAAE,MAAM;8BACpBgN,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBpP,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACArD,OAAA,CAACZ,aAAa;8BAAC+Q,IAAI,EAAE,EAAG;8BAAC/M,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAMyI,YAAY,GAAG,CAACM,YAAY,CAAC0E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAGnF,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAM6F,aAAa,GAAGX,aAAa,CAAChF,IAAI;0BAExC,oBACEvM,OAAA;4BAAM0C,KAAK,EAAE;8BACXiJ,UAAU,EAAE4F,aAAa,CAAC5F,UAAU;8BACpCvI,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjB8H,OAAO,EAAE,iBAAiB;8BAC1BzG,YAAY,EAAE,MAAM;8BACpBgN,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBpP,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACArD,OAAA,CAACkS,aAAa;8BAAC/B,IAAI,EAAE,EAAG;8BAAC/M,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCyI,YAAY;0BAAA;4BAAA5I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEH+I,YAAY,CAACyB,WAAW,iBACvBpO,OAAA;wBAAM0C,KAAK,EAAE;0BACXiJ,UAAU,EAAE,yBAAyB;0BACrCvI,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,SAAS;0BACnBM,UAAU,EAAE,KAAK;0BACjB8H,OAAO,EAAE,iBAAiB;0BAC1BzG,YAAY,EAAE;wBAChB,CAAE;wBAAA9B,QAAA,GAAC,QACK,EAACsJ,YAAY,CAACyB,WAAW;sBAAA;wBAAA3K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,eAED5D,OAAA;wBAAK0C,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAImL,IAAI,CAAC7B,YAAY,CAAC8E,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BAC7DW,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA/O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN5D,OAAA;sBAAI0C,KAAK,EAAE;wBACTsN,MAAM,EAAE,cAAc;wBACtBxM,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB6M,UAAU,EAAE;sBACd,CAAE;sBAAA5M,QAAA,EACCsJ,YAAY,CAACiB;oBAAK;sBAAAnK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL+I,YAAY,CAAC9B,WAAW,IAAI8B,YAAY,CAAC9B,WAAW,CAACtG,MAAM,GAAG,CAAC,iBAC9DvE,OAAA,CAACmE,YAAY;kBACXC,MAAM,EAAEuI,YAAY,CAAC9B,WAAY;kBACjCxG,SAAS,EAAEsI,YAAY,CAACiB,KAAM;kBAC9BvN,QAAQ,EAAE4G,WAAY;kBACtB3C,YAAY,EAAGQ,KAAK,IAAK;oBACvBgH,YAAY,CAACa,YAAY,CAAC9B,WAAW,IAAI,EAAE,EAAE/F,KAAK,CAAC;kBACrD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnByM,UAAU,EAAE,KAAK;oBACjB1M,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCsJ,YAAY,CAACoB;gBAAO;kBAAAtK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGN5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B0I,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,qBAAqB;oBACjCxG,YAAY,EAAE,MAAM;oBACpB5B,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEArD,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAMwG,gBAAgB,CAACC,YAAY,CAAE;sBAC9CjK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbiG,UAAU,EAAE,MAAM;wBAClB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEuJ,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDzH,MAAM,EAAE,SAAS;wBACjBwG,OAAO,EAAE,QAAQ;wBACjBzG,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAAtI,QAAA,gBAEFrD,OAAA,CAACjB,KAAK;wBACJoR,IAAI,EAAE,EAAG;wBACT4C,IAAI,EAAEpG,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAApJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF5D,OAAA;wBAAAqD,QAAA,EAAOsJ,YAAY,CAACS,cAAc,IAAI;sBAAC;wBAAA3J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGR+I,YAAY,CAACqG,cAAc,iBAC1BhT,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAMwC,eAAe,CAC5BD,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;sBACFlK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbiG,UAAU,EAAE,MAAM;wBAClB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEqF,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5ExH,MAAM,EAAE,SAAS;wBACjBwG,OAAO,EAAE,QAAQ;wBACjBzG,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;wBACxD1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFN,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;wBACzC1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGqF,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAAvJ,QAAA,gBAEFrD,OAAA,CAAClB,aAAa;wBAACqR,IAAI,EAAE;sBAAG;wBAAA1M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B5D,OAAA;wBAAAqD,QAAA,EAAOsJ,YAAY,CAACsG,aAAa,IAAI;sBAAC;wBAAAxP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGE,CAAC,eAGN5D,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,MAAM;sBACXlC,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACArD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACArD,OAAA,CAAChB,KAAK;wBAACmR,IAAI,EAAE;sBAAG;wBAAA1M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB5D,OAAA;wBAAAqD,QAAA,GAAM,YAAU,EAAEsJ,YAAY,CAAS4G,cAAc,IAAI5G,YAAY,CAAC6G,WAAW,IAAI,OAAO;sBAAA;wBAAA/P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjG,CAAC,eAEN5D,OAAA;sBAAK0C,KAAK,EAAE;wBACVkJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAEgB,YAAY,CAAC7K,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BsB,KAAK,EAAEuJ,YAAY,CAAC7K,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClEqD,YAAY,EAAE,KAAK;wBACnBrB,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCsJ,YAAY,CAAC7K;oBAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL6E,YAAY,KAAKkE,YAAY,CAACC,eAAe,IAAID,YAAY,CAACqG,cAAc,iBAC3EhT,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB+O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE;kBACb,CAAE;kBAAAvO,QAAA,EACC4D,WAAW,KAAK,OAAO,gBACtBjH,OAAA,CAAChC,mBAAmB;oBAClByV,cAAc,EAAE9G,YAAY,CAACC,eAAgB;oBAC7CuG,aAAa,EAAExG,YAAY,CAACqG,cAAe;oBAC3CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/BuG,eAAe,EAAC;kBAAO;oBAAA5P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF5D,OAAA,CAAC/B,cAAc;oBACbwV,cAAc,EAAE9G,YAAY,CAACC,eAAgB;oBAC7CuG,aAAa,EAAExG,YAAY,CAACqG,cAAe;oBAC3CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/BuG,eAAe,EAAC;kBAAS;oBAAA5P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GA5VI,gBAAgB+I,YAAY,CAACC,eAAe,EAAE;gBAAAnJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6VhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLiF,kBAAkB,iBACjB7I,OAAA;MAAK0C,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxB0M,MAAM,EAAE,IAAI;QACZhE,OAAO,EAAE;MACX,CAAE;MACF1F,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC,IAAI,CAAE;MAAAzF,QAAA,eAEzCrD,OAAA;QAAK0C,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpB0G,QAAQ,EAAE,OAAO;UACjBxG,KAAK,EAAE,MAAM;UACbqO,SAAS,EAAE,MAAM;UACjBxO,QAAQ,EAAE,MAAM;UAChB4K,SAAS,EAAE;QACb,CAAE;QACF5J,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAAC0P,eAAe,CAAC,CAAE;QAAAtQ,QAAA,gBAGlCrD,OAAA;UAAK0C,KAAK,EAAE;YACVkJ,OAAO,EAAE,QAAQ;YACjBiE,YAAY,EAAE,mBAAmB;YACjC7M,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACArD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACArD,OAAA,CAACpB,GAAG;cAACuR,IAAI,EAAE,EAAG;cAACzN,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5D,OAAA;cAAI0C,KAAK,EAAE;gBACTsN,MAAM,EAAE,CAAC;gBACTxM,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN5D,OAAA;YACEkG,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC,IAAI,CAAE;YAC3CpG,KAAK,EAAE;cACLiJ,UAAU,EAAE,MAAM;cAClB9H,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClBJ,KAAK,EAAE,SAAS;cAChBgC,MAAM,EAAE,SAAS;cACjBwG,OAAO,EAAE,SAAS;cAClBzG,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5D,OAAA;UAAK0C,KAAK,EAAE;YAAEkJ,OAAO,EAAE;UAAS,CAAE;UAAAvI,QAAA,gBAChCrD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,SAAS;cACdnC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,GACC,CAAC,MAAM;cACN,IAAIwF,kBAAkB,CAACuI,QAAQ,EAAE;gBAC/B,oBACEpR,OAAA;kBAAM0C,KAAK,EAAE;oBACXiJ,UAAU,EAAE,mDAAmD;oBAC/DvI,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjB8H,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpBgN,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtBpP,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACArD,OAAA,CAACZ,aAAa;oBAAC+Q,IAAI,EAAE,EAAG;oBAAC/M,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAEX,CAAC,MAAM;gBACL,MAAMyI,YAAY,GAAG,CAACxD,kBAAkB,CAACwI,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;gBAClF,MAAMC,aAAa,GAAGnF,gBAAgB,CAACC,YAAY,CAAC;gBACpD,MAAM6F,aAAa,GAAGX,aAAa,CAAChF,IAAI;gBAExC,oBACEvM,OAAA;kBAAM0C,KAAK,EAAE;oBACXiJ,UAAU,EAAE4F,aAAa,CAAC5F,UAAU;oBACpCvI,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjB8H,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpBgN,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtBpP,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACArD,OAAA,CAACkS,aAAa;oBAAC/B,IAAI,EAAE,EAAG;oBAAC/M,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxCyI,YAAY;gBAAA;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAEX;YACF,CAAC,EAAE,CAAC,eAEJ5D,OAAA;cAAM0C,KAAK,EAAE;gBACXiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdwI,OAAO,EAAE,iBAAiB;gBAC1BzG,YAAY,EAAE,MAAM;gBACpB3B,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBd,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA,CAACpB,GAAG;gBAACuR,IAAI,EAAE;cAAG;gBAAA1M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN5D,OAAA;YAAI0C,KAAK,EAAE;cACTsN,MAAM,EAAE,YAAY;cACpBxM,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE,SAAS;cAChB6M,UAAU,EAAE;YACd,CAAE;YAAA5M,QAAA,EACCwF,kBAAkB,CAAC+E;UAAK;YAAAnK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EAGJiF,kBAAkB,CAACgC,WAAW,IAAIhC,kBAAkB,CAACgC,WAAW,CAACtG,MAAM,GAAG,CAAC,iBAC1EvE,OAAA;YAAK0C,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,eACrCrD,OAAA,CAACmE,YAAY;cACXC,MAAM,EAAEyE,kBAAkB,CAACgC,WAAY;cACvCxG,SAAS,EAAEwE,kBAAkB,CAAC+E,KAAM;cACpCvN,QAAQ,EAAE4G,WAAY;cACtB3C,YAAY,EAAGQ,KAAK,IAAK;gBACvBgH,YAAY,CAACjD,kBAAkB,CAACgC,WAAW,EAAE/F,KAAK,CAAC;cACrD;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED5D,OAAA;YAAK0C,KAAK,EAAE;cACVU,KAAK,EAAE,SAAS;cAChBI,QAAQ,EAAE,MAAM;cAChByM,UAAU,EAAE,KAAK;cACjB1M,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACCwF,kBAAkB,CAACkF;UAAO;YAAAtK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEN5D,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXlC,QAAQ,EAAE,UAAU;cACpBJ,KAAK,EAAE,SAAS;cAChB0P,UAAU,EAAE,MAAM;cAClBlB,SAAS,EAAE;YACb,CAAE;YAAAvO,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;gBAACsR,IAAI,EAAE;cAAG;gBAAA1M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB5D,OAAA;gBAAAqD,QAAA,GAAM,aAAW,EAAC,IAAImL,IAAI,CAAC3F,kBAAkB,CAAC4I,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAjO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACLiF,kBAAkB,CAAC2K,WAAW,iBAC7BxT,OAAA;cAAAqD,QAAA,GAAK,MACC,EAACwF,kBAAkB,CAAC2K,WAAW;YAAA;cAAA/P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5D,OAAA,CAAC3B,aAAa;MACZ+F,MAAM,EAAE+E,cAAe;MACvB4C,YAAY,EAAE1C,oBAAqB;MACnCuK,MAAM,EAAE3K,YAAa;MACrB4K,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,KAAK,CAAE;MACtC7E,SAAS,EAAC;IAAoB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC+C,GAAA,CAp9DID,QAAiC;EAAA,QACpB/I,WAAW,EAeLE,aAAa,EAUhCC,gBAAgB,EASyBC,qBAAqB;AAAA;AAAA+V,GAAA,GAnC9DpN,QAAiC;AAs9DvC,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAAuC,GAAA,EAAAqN,GAAA;AAAAC,YAAA,CAAA7P,EAAA;AAAA6P,YAAA,CAAAtN,GAAA;AAAAsN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}