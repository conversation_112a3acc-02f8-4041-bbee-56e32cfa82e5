{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v1.5\",\n  key: \"6hud8k\"\n}], [\"path\", {\n  d: \"M13.9 17.45c-1.2-1.2-1.14-2.8-.2-3.73a2.43 2.43 0 0 1 3.44 0l.36.34.34-.34a2.43 2.43 0 0 1 3.45-.01c.95.95 1 2.53-.2 3.74L17.5 21Z\",\n  key: \"wpff58\"\n}]];\nconst FolderHeart = createLucideIcon(\"folder-heart\", __iconNode);\nexport { __iconNode, FolderHeart as default };\n//# sourceMappingURL=folder-heart.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}