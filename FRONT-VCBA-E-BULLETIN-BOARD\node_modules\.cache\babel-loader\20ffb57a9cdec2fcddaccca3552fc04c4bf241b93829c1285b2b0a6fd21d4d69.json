{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 12h.01\",\n  key: \"nlz23k\"\n}], [\"path\", {\n  d: \"M3 18h.01\",\n  key: \"1tta3j\"\n}], [\"path\", {\n  d: \"M3 6h.01\",\n  key: \"1rqtza\"\n}], [\"path\", {\n  d: \"M8 12h13\",\n  key: \"1za7za\"\n}], [\"path\", {\n  d: \"M8 18h13\",\n  key: \"1lx6n3\"\n}], [\"path\", {\n  d: \"M8 6h13\",\n  key: \"ik3vkj\"\n}]];\nconst List = createLucideIcon(\"list\", __iconNode);\nexport { __iconNode, List as default };\n//# sourceMappingURL=list.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}