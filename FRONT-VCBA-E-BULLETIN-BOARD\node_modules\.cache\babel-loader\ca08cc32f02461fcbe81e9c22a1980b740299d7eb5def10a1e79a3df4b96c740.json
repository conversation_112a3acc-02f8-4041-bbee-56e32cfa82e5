{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 12h14\",\n  key: \"1ays0h\"\n}], [\"path\", {\n  d: \"m12 5 7 7-7 7\",\n  key: \"xquz4c\"\n}]];\nconst ArrowRight = createLucideIcon(\"arrow-right\", __iconNode);\nexport { __iconNode, ArrowRight as default };\n//# sourceMappingURL=arrow-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}