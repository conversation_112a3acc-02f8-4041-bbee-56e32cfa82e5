{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"path\", {\n  d: \"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\",\n  key: \"1u773s\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12.01\",\n  y1: \"17\",\n  y2: \"17\",\n  key: \"io3f8k\"\n}]];\nconst BadgeQuestionMark = createLucideIcon(\"badge-question-mark\", __iconNode);\nexport { __iconNode, BadgeQuestionMark as default };\n//# sourceMappingURL=badge-question-mark.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}