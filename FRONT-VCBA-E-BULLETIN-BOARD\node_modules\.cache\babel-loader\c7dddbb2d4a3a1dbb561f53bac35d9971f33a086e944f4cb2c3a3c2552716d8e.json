{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 18 6-6-6-6\",\n  key: \"eg8j8\"\n}], [\"path\", {\n  d: \"m8 6-6 6 6 6\",\n  key: \"ppft3o\"\n}]];\nconst Code = createLucideIcon(\"code\", __iconNode);\nexport { __iconNode, Code as default };\n//# sourceMappingURL=code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}