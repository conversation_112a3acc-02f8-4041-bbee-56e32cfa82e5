{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 18H3\",\n  key: \"olowqp\"\n}], [\"path\", {\n  d: \"M17 6H3\",\n  key: \"16j9eg\"\n}], [\"path\", {\n  d: \"M21 12H3\",\n  key: \"2avoz0\"\n}]];\nconst Text = createLucideIcon(\"text\", __iconNode);\nexport { __iconNode, Text as default };\n//# sourceMappingURL=text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}