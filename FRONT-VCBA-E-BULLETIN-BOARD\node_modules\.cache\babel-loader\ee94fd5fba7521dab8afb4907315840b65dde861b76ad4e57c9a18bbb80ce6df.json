{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2\",\n  key: \"1fvzgz\"\n}], [\"path\", {\n  d: \"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2\",\n  key: \"1kc0my\"\n}], [\"path\", {\n  d: \"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8\",\n  key: \"10h0bg\"\n}], [\"path\", {\n  d: \"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15\",\n  key: \"1s1gnw\"\n}]];\nconst Hand = createLucideIcon(\"hand\", __iconNode);\nexport { __iconNode, Hand as default };\n//# sourceMappingURL=hand.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}