{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22a10 10 0 1 1 10-10\",\n  key: \"130bv5\"\n}], [\"path\", {\n  d: \"M22 22 12 12\",\n  key: \"131aw7\"\n}], [\"path\", {\n  d: \"M22 16v6h-6\",\n  key: \"1gvm70\"\n}]];\nconst CircleArrowOutDownRight = createLucideIcon(\"circle-arrow-out-down-right\", __iconNode);\nexport { __iconNode, CircleArrowOutDownRight as default };\n//# sourceMappingURL=circle-arrow-out-down-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}