{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 6h18\",\n  key: \"d0wm0j\"\n}], [\"path\", {\n  d: \"M7 12h10\",\n  key: \"b7w52i\"\n}], [\"path\", {\n  d: \"M10 18h4\",\n  key: \"1ulq68\"\n}]];\nconst ListFilter = createLucideIcon(\"list-filter\", __iconNode);\nexport { __iconNode, ListFilter as default };\n//# sourceMappingURL=list-filter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}