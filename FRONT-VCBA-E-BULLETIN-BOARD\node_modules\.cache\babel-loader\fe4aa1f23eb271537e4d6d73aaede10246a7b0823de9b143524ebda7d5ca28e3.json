{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M12 2a7 7 0 1 0 10 10\",\n  key: \"1yuj32\"\n}]];\nconst Eclipse = createLucideIcon(\"eclipse\", __iconNode);\nexport { __iconNode, Eclipse as default };\n//# sourceMappingURL=eclipse.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}