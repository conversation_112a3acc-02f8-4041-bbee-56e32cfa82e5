{"ast": null, "code": "class ArchiveService {\n  // Get archived announcements\n  async getArchivedAnnouncements(filters = {}, pagination = {}) {\n    const params = new URLSearchParams();\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    const queryString = params.toString();\n    const url = queryString ? `/archive/announcements?${queryString}` : '/archive/announcements';\n    return this.makeRequest('GET', url);\n  }\n\n  // Get archived calendar events\n  async getArchivedCalendarEvents(filters = {}, pagination = {}) {\n    const params = new URLSearchParams();\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    const queryString = params.toString();\n    const url = queryString ? `/archive/calendar-events?${queryString}` : '/archive/calendar-events';\n    return this.makeRequest('GET', url);\n  }\n\n  // Get archived students\n  async getArchivedStudents(filters = {}, pagination = {}) {\n    const params = new URLSearchParams();\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    const queryString = params.toString();\n    const url = queryString ? `/archive/students?${queryString}` : '/archive/students';\n    return this.makeRequest('GET', url);\n  }\n\n  // Get archive statistics\n  async getArchiveStatistics() {\n    return this.makeRequest('GET', '/archive/statistics');\n  }\n\n  // Restore archived announcement\n  async restoreAnnouncement(announcementId) {\n    return this.makeRequest('PUT', `/archive/announcements/${announcementId}/restore`);\n  }\n\n  // Restore archived calendar event\n  async restoreCalendarEvent(eventId) {\n    return this.makeRequest('PUT', `/archive/calendar-events/${eventId}/restore`);\n  }\n\n  // Restore archived student\n  async restoreStudent(studentId) {\n    return this.makeRequest('PUT', `/archive/students/${studentId}/restore`);\n  }\n\n  // Permanently delete announcement\n  async permanentlyDeleteAnnouncement(announcementId) {\n    return this.makeRequest('DELETE', `/archive/announcements/${announcementId}/permanent`);\n  }\n}\nexport const archiveService = new ArchiveService();\nexport default archiveService;", "map": {"version": 3, "names": ["ArchiveService", "getArchivedAnnouncements", "filters", "pagination", "params", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "append", "toString", "queryString", "url", "makeRequest", "getArchivedCalendarEvents", "getArchivedStudents", "getArchiveStatistics", "restoreAnnouncement", "announcementId", "restoreCalendarEvent", "eventId", "restoreStudent", "studentId", "permanentlyDeleteAnnouncement", "archiveService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/archiveService.ts"], "sourcesContent": ["import { adminHttpClient } from './api.service';\nimport { API_ENDPOINTS } from '../config/constants';\nimport { ApiResponse } from '../types';\n\nexport interface ArchiveFilters {\n  search?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  posted_by?: number;\n  created_by?: number;\n  grade_level?: number;\n  section?: string;\n  start_date?: string;\n  end_date?: string;\n}\n\nexport interface ArchivePagination {\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface ArchiveResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface ArchivedAnnouncement {\n  announcement_id: number;\n  title: string;\n  content: string;\n  image_path?: string;\n  category_id: number;\n  subcategory_id?: number;\n  posted_by: number;\n  grade_level?: number;\n  status: string;\n  is_pinned: boolean;\n  is_alert: boolean;\n  allow_comments: boolean;\n  allow_sharing: boolean;\n  scheduled_publish_at?: string;\n  published_at?: string;\n  archived_at?: string;\n  deleted_at: string;\n  view_count: number;\n  created_at: string;\n  updated_at: string;\n  category_name: string;\n  category_color: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  author_name: string;\n}\n\nexport interface ArchivedCalendarEvent {\n  calendar_id: number;\n  title: string;\n  description?: string;\n  event_date: string;\n  end_date?: string;\n  event_time?: string;\n  end_time?: string;\n  location?: string;\n  category_id: number;\n  subcategory_id?: number;\n  created_by: number;\n  is_recurring: boolean;\n  recurrence_pattern?: string;\n  is_active: boolean;\n  is_published: boolean;\n  allow_comments: boolean;\n  is_alert: boolean;\n  deleted_at: string;\n  created_at: string;\n  updated_at: string;\n  category_name: string;\n  category_color: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  created_by_name: string;\n}\n\nexport interface ArchivedStudent {\n  student_id: number;\n  email: string;\n  student_number: string;\n  is_active: boolean;\n  last_login?: string;\n  created_by: number;\n  created_by_name: string;\n  created_at: string;\n  updated_at: string;\n  profile?: {\n    profile_id: number;\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number: string;\n    grade_level: number;\n    section: string;\n    parent_guardian_name?: string;\n    parent_guardian_phone?: string;\n    address?: string;\n    profile_picture?: string;\n    created_at: string;\n    updated_at: string;\n  };\n}\n\nexport interface ArchiveStatistics {\n  announcements: number;\n  calendar_events: number;\n  students: number;\n  total: number;\n}\n\nclass ArchiveService {\n  // Get archived announcements\n  async getArchivedAnnouncements(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedAnnouncement>>> {\n    const params = new URLSearchParams();\n    \n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n\n    const queryString = params.toString();\n    const url = queryString ? `/archive/announcements?${queryString}` : '/archive/announcements';\n    \n    return this.makeRequest<ArchiveResponse<ArchivedAnnouncement>>('GET', url);\n  }\n\n  // Get archived calendar events\n  async getArchivedCalendarEvents(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedCalendarEvent>>> {\n    const params = new URLSearchParams();\n    \n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n\n    const queryString = params.toString();\n    const url = queryString ? `/archive/calendar-events?${queryString}` : '/archive/calendar-events';\n    \n    return this.makeRequest<ArchiveResponse<ArchivedCalendarEvent>>('GET', url);\n  }\n\n  // Get archived students\n  async getArchivedStudents(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedStudent>>> {\n    const params = new URLSearchParams();\n    \n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n\n    const queryString = params.toString();\n    const url = queryString ? `/archive/students?${queryString}` : '/archive/students';\n    \n    return this.makeRequest<ArchiveResponse<ArchivedStudent>>('GET', url);\n  }\n\n  // Get archive statistics\n  async getArchiveStatistics(): Promise<ApiResponse<ArchiveStatistics>> {\n    return this.makeRequest<ArchiveStatistics>('GET', '/archive/statistics');\n  }\n\n  // Restore archived announcement\n  async restoreAnnouncement(announcementId: number): Promise<ApiResponse<{ announcement: ArchivedAnnouncement }>> {\n    return this.makeRequest<{ announcement: ArchivedAnnouncement }>('PUT', `/archive/announcements/${announcementId}/restore`);\n  }\n\n  // Restore archived calendar event\n  async restoreCalendarEvent(eventId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('PUT', `/archive/calendar-events/${eventId}/restore`);\n  }\n\n  // Restore archived student\n  async restoreStudent(studentId: number): Promise<ApiResponse<{ student: ArchivedStudent }>> {\n    return this.makeRequest<{ student: ArchivedStudent }>('PUT', `/archive/students/${studentId}/restore`);\n  }\n\n  // Permanently delete announcement\n  async permanentlyDeleteAnnouncement(announcementId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('DELETE', `/archive/announcements/${announcementId}/permanent`);\n  }\n}\n\nexport const archiveService = new ArchiveService();\nexport default archiveService;\n"], "mappings": "AA+HA,MAAMA,cAAc,CAAC;EACnB;EACA,MAAMC,wBAAwBA,CAC5BC,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EAC2B;IAC7D,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEpC;IACAC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,CAACM,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;;IAEF;IACAP,MAAM,CAACC,OAAO,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,MAAMC,WAAW,GAAGV,MAAM,CAACS,QAAQ,CAAC,CAAC;IACrC,MAAME,GAAG,GAAGD,WAAW,GAAG,0BAA0BA,WAAW,EAAE,GAAG,wBAAwB;IAE5F,OAAO,IAAI,CAACE,WAAW,CAAwC,KAAK,EAAED,GAAG,CAAC;EAC5E;;EAEA;EACA,MAAME,yBAAyBA,CAC7Bf,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EAC4B;IAC9D,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEpC;IACAC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,CAACM,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;;IAEF;IACAP,MAAM,CAACC,OAAO,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,MAAMC,WAAW,GAAGV,MAAM,CAACS,QAAQ,CAAC,CAAC;IACrC,MAAME,GAAG,GAAGD,WAAW,GAAG,4BAA4BA,WAAW,EAAE,GAAG,0BAA0B;IAEhG,OAAO,IAAI,CAACE,WAAW,CAAyC,KAAK,EAAED,GAAG,CAAC;EAC7E;;EAEA;EACA,MAAMG,mBAAmBA,CACvBhB,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EACsB;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEpC;IACAC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,CAACM,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;;IAEF;IACAP,MAAM,CAACC,OAAO,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,MAAMC,WAAW,GAAGV,MAAM,CAACS,QAAQ,CAAC,CAAC;IACrC,MAAME,GAAG,GAAGD,WAAW,GAAG,qBAAqBA,WAAW,EAAE,GAAG,mBAAmB;IAElF,OAAO,IAAI,CAACE,WAAW,CAAmC,KAAK,EAAED,GAAG,CAAC;EACvE;;EAEA;EACA,MAAMI,oBAAoBA,CAAA,EAA4C;IACpE,OAAO,IAAI,CAACH,WAAW,CAAoB,KAAK,EAAE,qBAAqB,CAAC;EAC1E;;EAEA;EACA,MAAMI,mBAAmBA,CAACC,cAAsB,EAAgE;IAC9G,OAAO,IAAI,CAACL,WAAW,CAAyC,KAAK,EAAE,0BAA0BK,cAAc,UAAU,CAAC;EAC5H;;EAEA;EACA,MAAMC,oBAAoBA,CAACC,OAAe,EAA8B;IACtE,OAAO,IAAI,CAACP,WAAW,CAAO,KAAK,EAAE,4BAA4BO,OAAO,UAAU,CAAC;EACrF;;EAEA;EACA,MAAMC,cAAcA,CAACC,SAAiB,EAAsD;IAC1F,OAAO,IAAI,CAACT,WAAW,CAA+B,KAAK,EAAE,qBAAqBS,SAAS,UAAU,CAAC;EACxG;;EAEA;EACA,MAAMC,6BAA6BA,CAACL,cAAsB,EAA8B;IACtF,OAAO,IAAI,CAACL,WAAW,CAAO,QAAQ,EAAE,0BAA0BK,cAAc,YAAY,CAAC;EAC/F;AACF;AAEA,OAAO,MAAMM,cAAc,GAAG,IAAI3B,cAAc,CAAC,CAAC;AAClD,eAAe2B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}