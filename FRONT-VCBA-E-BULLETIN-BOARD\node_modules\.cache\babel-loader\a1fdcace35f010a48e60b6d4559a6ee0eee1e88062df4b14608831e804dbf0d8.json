{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 19V5\",\n  key: \"rwsyhb\"\n}], [\"path\", {\n  d: \"m13 6-6 6 6 6\",\n  key: \"1yhaz7\"\n}], [\"path\", {\n  d: \"M7 12h14\",\n  key: \"uoisry\"\n}]];\nconst ArrowLeftToLine = createLucideIcon(\"arrow-left-to-line\", __iconNode);\nexport { __iconNode, ArrowLeftToLine as default };\n//# sourceMappingURL=arrow-left-to-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}