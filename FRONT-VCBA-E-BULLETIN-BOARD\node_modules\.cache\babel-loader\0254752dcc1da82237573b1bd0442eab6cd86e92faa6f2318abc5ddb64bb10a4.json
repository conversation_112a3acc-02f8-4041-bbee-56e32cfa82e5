{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"5\",\n  key: \"nd82uf\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}]];\nconst DiscAlbum = createLucideIcon(\"disc-album\", __iconNode);\nexport { __iconNode, DiscAlbum as default };\n//# sourceMappingURL=disc-album.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}