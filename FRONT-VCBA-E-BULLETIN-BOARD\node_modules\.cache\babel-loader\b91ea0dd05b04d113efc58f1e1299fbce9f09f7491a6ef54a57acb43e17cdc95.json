{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate,useLocation}from'react-router-dom';import{AdminAuthProvider}from'./contexts/AdminAuthContext';import{StudentAuthProvider}from'./contexts/StudentAuthContext';import{ToastProvider}from'./contexts/ToastContext';import{ProtectedRoute,PublicRoute}from'./components/common';import{ErrorBoundary}from'./components/ErrorBoundary';import'./styles/commentDepth.css';import{AdminLogin,StudentLogin,AdminRegister}from'./pages';import AdminLayout from'./components/admin/layout/AdminLayout';import AdminDashboard from'./pages/admin/AdminDashboard';import AdminNewsfeed from'./pages/admin/AdminNewsfeed';import Calendar from'./pages/admin/Calendar';import PostManagement from'./pages/admin/PostManagement';import StudentManagement from'./pages/admin/StudentManagement';import Settings from'./pages/admin/Settings';import ApiTest from'./pages/debug/ApiTest';import StudentLayout from'./components/student/layout/StudentLayout';import StudentDashboard from'./pages/student/StudentDashboard';import StudentNewsfeed from'./pages/student/StudentNewsfeed';import StudentSettings from'./pages/student/StudentSettings';import'./App.css';// Smart redirect component that determines the appropriate login page based on the current path\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SmartRedirect=()=>{const location=useLocation();// If the path starts with /student, redirect to student login\nif(location.pathname.startsWith('/student')){return/*#__PURE__*/_jsx(Navigate,{to:\"/student/login\",replace:true});}// Default to admin login for all other paths\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/admin/login\",replace:true});};// Admin Routes Component with isolated auth context\nconst AdminRoutes=()=>/*#__PURE__*/_jsx(AdminAuthProvider,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(PublicRoute,{restricted:true,children:/*#__PURE__*/_jsx(AdminLogin,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(PublicRoute,{restricted:true,children:/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsx(AdminRegister,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminLayout,{children:/*#__PURE__*/_jsx(AdminDashboard,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/newsfeed\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminNewsfeed,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/calendar\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminLayout,{children:/*#__PURE__*/_jsx(Calendar,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/posts\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminLayout,{children:/*#__PURE__*/_jsx(PostManagement,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/student-management\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminLayout,{children:/*#__PURE__*/_jsx(StudentManagement,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(AdminLayout,{children:/*#__PURE__*/_jsx(Settings,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/debug\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(ApiTest,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"admin\",children:/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true})})})]})});// Student Routes Component with isolated auth context\nconst StudentRoutes=()=>/*#__PURE__*/_jsx(StudentAuthProvider,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(PublicRoute,{restricted:true,children:/*#__PURE__*/_jsx(StudentLogin,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"student\",children:/*#__PURE__*/_jsx(StudentLayout,{children:/*#__PURE__*/_jsx(StudentDashboard,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/newsfeed\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"student\",children:/*#__PURE__*/_jsx(StudentNewsfeed,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"student\",children:/*#__PURE__*/_jsx(StudentLayout,{children:/*#__PURE__*/_jsx(StudentSettings,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredRole:\"student\",children:/*#__PURE__*/_jsx(Navigate,{to:\"/student/newsfeed\",replace:true})})})]})});function App(){return/*#__PURE__*/_jsx(ToastProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/admin/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/*\",element:/*#__PURE__*/_jsx(AdminRoutes,{})}),/*#__PURE__*/_jsx(Route,{path:\"/student/*\",element:/*#__PURE__*/_jsx(StudentRoutes,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(SmartRedirect,{})})]})})})});}export default App;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}