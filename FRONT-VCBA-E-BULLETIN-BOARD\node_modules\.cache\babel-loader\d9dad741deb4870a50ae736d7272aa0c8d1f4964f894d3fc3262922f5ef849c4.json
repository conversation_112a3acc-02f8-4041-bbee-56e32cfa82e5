{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m19 11-8-8-8.6 8.6a2 2 0 0 0 0 2.8l5.2 5.2c.8.8 2 .8 2.8 0L19 11Z\",\n  key: \"irua1i\"\n}], [\"path\", {\n  d: \"m5 2 5 5\",\n  key: \"1lls2c\"\n}], [\"path\", {\n  d: \"M2 13h15\",\n  key: \"1hkzvu\"\n}], [\"path\", {\n  d: \"M22 20a2 2 0 1 1-4 0c0-1.6 1.7-2.4 2-4 .3 1.6 2 2.4 2 4Z\",\n  key: \"xk76lq\"\n}]];\nconst PaintBucket = createLucideIcon(\"paint-bucket\", __iconNode);\nexport { __iconNode, PaintBucket as default };\n//# sourceMappingURL=paint-bucket.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}