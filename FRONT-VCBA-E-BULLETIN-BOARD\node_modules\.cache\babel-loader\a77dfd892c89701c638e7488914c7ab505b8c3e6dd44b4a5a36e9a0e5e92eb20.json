{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 14.772-.383.923\",\n  key: \"11vil6\"\n}], [\"path\", {\n  d: \"m10.852 9.228-.383-.923\",\n  key: \"1fjppe\"\n}], [\"path\", {\n  d: \"m13.148 14.772.382.924\",\n  key: \"je3va1\"\n}], [\"path\", {\n  d: \"m13.531 8.305-.383.923\",\n  key: \"18epck\"\n}], [\"path\", {\n  d: \"m14.772 10.852.923-.383\",\n  key: \"k9m8cz\"\n}], [\"path\", {\n  d: \"m14.772 13.148.923.383\",\n  key: \"1xvhww\"\n}], [\"path\", {\n  d: \"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 0 0-5.63-1.446 3 3 0 0 0-.368 1.571 4 4 0 0 0-2.525 5.771\",\n  key: \"jcbbz1\"\n}], [\"path\", {\n  d: \"M17.998 5.125a4 4 0 0 1 2.525 5.771\",\n  key: \"1kkn7e\"\n}], [\"path\", {\n  d: \"M19.505 10.294a4 4 0 0 1-1.5 7.706\",\n  key: \"18bmuc\"\n}], [\"path\", {\n  d: \"M4.032 17.483A4 4 0 0 0 11.464 20c.18-.311.892-.311 1.072 0a4 4 0 0 0 7.432-2.516\",\n  key: \"uozx0d\"\n}], [\"path\", {\n  d: \"M4.5 10.291A4 4 0 0 0 6 18\",\n  key: \"whdemb\"\n}], [\"path\", {\n  d: \"M6.002 5.125a3 3 0 0 0 .4 1.375\",\n  key: \"1kqy2g\"\n}], [\"path\", {\n  d: \"m9.228 10.852-.923-.383\",\n  key: \"1wtb30\"\n}], [\"path\", {\n  d: \"m9.228 13.148-.923.383\",\n  key: \"1a830x\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}]];\nconst BrainCog = createLucideIcon(\"brain-cog\", __iconNode);\nexport { __iconNode, BrainCog as default };\n//# sourceMappingURL=brain-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}