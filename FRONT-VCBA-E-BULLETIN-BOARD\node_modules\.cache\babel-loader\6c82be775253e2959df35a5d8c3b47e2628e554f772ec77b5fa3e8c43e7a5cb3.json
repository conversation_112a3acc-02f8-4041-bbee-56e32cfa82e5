{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\HolidayManagement.tsx\";\nimport React from 'react';\nimport HolidayManagement from '../../components/admin/HolidayManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HolidayManagementPage = () => {\n  return /*#__PURE__*/_jsxDEV(HolidayManagement, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 10\n  }, this);\n};\n_c = HolidayManagementPage;\nexport default HolidayManagementPage;\nvar _c;\n$RefreshReg$(_c, \"HolidayManagementPage\");", "map": {"version": 3, "names": ["React", "HolidayManagement", "jsxDEV", "_jsxDEV", "HolidayManagementPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/HolidayManagement.tsx"], "sourcesContent": ["import React from 'react';\nimport HolidayManagement from '../../components/admin/HolidayManagement';\n\nconst HolidayManagementPage: React.FC = () => {\n  return <HolidayManagement />;\n};\n\nexport default HolidayManagementPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,qBAA+B,GAAGA,CAAA,KAAM;EAC5C,oBAAOD,OAAA,CAACF,iBAAiB;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9B,CAAC;AAACC,EAAA,GAFIL,qBAA+B;AAIrC,eAAeA,qBAAqB;AAAC,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}