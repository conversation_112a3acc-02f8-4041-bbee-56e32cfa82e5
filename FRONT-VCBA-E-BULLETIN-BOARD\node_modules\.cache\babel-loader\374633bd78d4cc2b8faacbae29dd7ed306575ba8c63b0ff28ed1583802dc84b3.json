{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49\",\n  key: \"ct8e1f\"\n}], [\"path\", {\n  d: \"M14.084 14.158a3 3 0 0 1-4.242-4.242\",\n  key: \"151rxh\"\n}], [\"path\", {\n  d: \"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143\",\n  key: \"13bj9a\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst EyeOff = createLucideIcon(\"eye-off\", __iconNode);\nexport { __iconNode, EyeOff as default };\n//# sourceMappingURL=eye-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}