{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m4 19 8-8\",\n  key: \"hr47gm\"\n}], [\"path\", {\n  d: \"m12 19-8-8\",\n  key: \"1dhhmo\"\n}], [\"path\", {\n  d: \"M20 12h-4c0-1.5.442-2 1.5-2.5S20 8.334 20 7.002c0-.472-.17-.93-.484-1.29a2.105 2.105 0 0 0-2.617-.436c-.42.239-.738.614-.899 1.06\",\n  key: \"1dfcux\"\n}]];\nconst Superscript = createLucideIcon(\"superscript\", __iconNode);\nexport { __iconNode, Superscript as default };\n//# sourceMappingURL=superscript.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}