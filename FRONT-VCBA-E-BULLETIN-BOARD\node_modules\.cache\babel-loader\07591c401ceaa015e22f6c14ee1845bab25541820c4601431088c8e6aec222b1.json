{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 21h6a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v6\",\n  key: \"14qz4y\"\n}], [\"path\", {\n  d: \"m3 21 9-9\",\n  key: \"1jfql5\"\n}], [\"path\", {\n  d: \"M9 21H3v-6\",\n  key: \"wtvkvv\"\n}]];\nconst SquareArrowOutDownLeft = createLucideIcon(\"square-arrow-out-down-left\", __iconNode);\nexport { __iconNode, SquareArrowOutDownLeft as default };\n//# sourceMappingURL=square-arrow-out-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}