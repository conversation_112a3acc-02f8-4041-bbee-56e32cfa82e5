{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\WelcomePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { GraduationCap, Users, BookOpen, ArrowRight } from 'lucide-react';\n\n/**\n * WelcomePage Component\n * \n * A mobile-first, responsive welcome page for William College General Santos City.\n * Features a full-screen background image, school logo, and prominent student login button.\n * \n * Design Principles:\n * - Mobile-first responsive design using Tailwind CSS\n * - Accessible with proper ARIA labels and semantic HTML\n * - Professional academic styling with smooth animations\n * - Touch-friendly button sizes for mobile devices\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n  const handleAdminLogin = () => {\n    navigate('/admin/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-cover bg-center bg-no-repeat\",\n      style: {\n        backgroundImage: `url('/villamor-image/villamor-collge-BG-landscape.jpg')`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 min-h-screen flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"flex-shrink-0 p-4 sm:p-6 lg:p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center mb-4 animate-fade-in\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"William College General Santos City Logo\",\n              className: \"h-16 w-auto sm:h-20 md:h-24 lg:h-28 object-contain drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8 sm:mb-12 animate-slide-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight\",\n              children: [\"Welcome to\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block text-primary-400 mt-2\",\n                children: \"William College\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block text-secondary-400 text-2xl sm:text-3xl md:text-4xl lg:text-5xl mt-2\",\n                children: \"General Santos City\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg sm:text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed\",\n              children: \"Your gateway to academic excellence and digital learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12 animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary-600 bg-opacity-80 p-4 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(GraduationCap, {\n                  className: \"h-8 w-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Academic Excellence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-300 text-center\",\n                children: \"Quality education for your future success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-secondary-600 bg-opacity-80 p-4 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-8 w-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Community\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-300 text-center\",\n                children: \"Join our vibrant learning community\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-accent-600 bg-opacity-80 p-4 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(BookOpen, {\n                  className: \"h-8 w-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Digital Learning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-300 text-center\",\n                children: \"Modern tools for modern education\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center animate-slide-up\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStudentLogin,\n              className: \"group w-full sm:w-auto bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 sm:px-12 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50\",\n              \"aria-label\": \"Access student portal and login\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center justify-center text-lg\",\n                children: [\"Student Login\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAdminLogin,\n              className: \"group w-full sm:w-auto bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 sm:px-12 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50\",\n              \"aria-label\": \"Access administrator portal and login\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center justify-center text-lg\",\n                children: [\"Admin Login\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"flex-shrink-0 p-4 sm:p-6 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-300 text-sm sm:text-base\",\n          children: \"\\xA9 2024 William College General Santos City. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomePage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = WelcomePage;\nexport default WelcomePage;\nvar _c;\n$RefreshReg$(_c, \"WelcomePage\");", "map": {"version": 3, "names": ["React", "useNavigate", "GraduationCap", "Users", "BookOpen", "ArrowRight", "jsxDEV", "_jsxDEV", "WelcomePage", "_s", "navigate", "handleStudentLogin", "handleAdminLogin", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/WelcomePage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { GraduationCap, Users, BookOpen, ArrowRight } from 'lucide-react';\n\n/**\n * WelcomePage Component\n * \n * A mobile-first, responsive welcome page for William College General Santos City.\n * Features a full-screen background image, school logo, and prominent student login button.\n * \n * Design Principles:\n * - Mobile-first responsive design using Tailwind CSS\n * - Accessible with proper ARIA labels and semantic HTML\n * - Professional academic styling with smooth animations\n * - Touch-friendly button sizes for mobile devices\n */\nconst WelcomePage: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n\n  const handleAdminLogin = () => {\n    navigate('/admin/login');\n  };\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Background Image with Overlay */}\n      <div \n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `url('/villamor-image/villamor-collge-BG-landscape.jpg')`,\n        }}\n      >\n        {/* Dark overlay for better text readability */}\n        <div className=\"absolute inset-0 bg-black bg-opacity-50\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 min-h-screen flex flex-col\">\n        {/* Header Section */}\n        <header className=\"flex-shrink-0 p-4 sm:p-6 lg:p-8\">\n          <div className=\"max-w-7xl mx-auto\">\n            {/* School Logo */}\n            <div className=\"flex justify-center mb-4 animate-fade-in\">\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"William College General Santos City Logo\"\n                className=\"h-16 w-auto sm:h-20 md:h-24 lg:h-28 object-contain drop-shadow-lg\"\n              />\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content Area */}\n        <main className=\"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            {/* Welcome Text */}\n            <div className=\"mb-8 sm:mb-12 animate-slide-up\">\n              <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight\">\n                Welcome to\n                <span className=\"block text-primary-400 mt-2\">\n                  William College\n                </span>\n                <span className=\"block text-secondary-400 text-2xl sm:text-3xl md:text-4xl lg:text-5xl mt-2\">\n                  General Santos City\n                </span>\n              </h1>\n              \n              <p className=\"text-lg sm:text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed\">\n                Your gateway to academic excellence and digital learning\n              </p>\n            </div>\n\n            {/* Feature Icons */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12 animate-fade-in\">\n              <div className=\"flex flex-col items-center text-white\">\n                <div className=\"bg-primary-600 bg-opacity-80 p-4 rounded-full mb-3\">\n                  <GraduationCap className=\"h-8 w-8\" />\n                </div>\n                <h3 className=\"text-lg font-semibold mb-2\">Academic Excellence</h3>\n                <p className=\"text-sm text-gray-300 text-center\">\n                  Quality education for your future success\n                </p>\n              </div>\n              \n              <div className=\"flex flex-col items-center text-white\">\n                <div className=\"bg-secondary-600 bg-opacity-80 p-4 rounded-full mb-3\">\n                  <Users className=\"h-8 w-8\" />\n                </div>\n                <h3 className=\"text-lg font-semibold mb-2\">Community</h3>\n                <p className=\"text-sm text-gray-300 text-center\">\n                  Join our vibrant learning community\n                </p>\n              </div>\n              \n              <div className=\"flex flex-col items-center text-white\">\n                <div className=\"bg-accent-600 bg-opacity-80 p-4 rounded-full mb-3\">\n                  <BookOpen className=\"h-8 w-8\" />\n                </div>\n                <h3 className=\"text-lg font-semibold mb-2\">Digital Learning</h3>\n                <p className=\"text-sm text-gray-300 text-center\">\n                  Modern tools for modern education\n                </p>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center animate-slide-up\">\n              {/* Primary Student Login Button */}\n              <button\n                onClick={handleStudentLogin}\n                className=\"group w-full sm:w-auto bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 sm:px-12 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50\"\n                aria-label=\"Access student portal and login\"\n              >\n                <span className=\"flex items-center justify-center text-lg\">\n                  Student Login\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                </span>\n              </button>\n\n              {/* Secondary Admin Login Button */}\n              <button\n                onClick={handleAdminLogin}\n                className=\"group w-full sm:w-auto bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 sm:px-12 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50\"\n                aria-label=\"Access administrator portal and login\"\n              >\n                <span className=\"flex items-center justify-center text-lg\">\n                  Admin Login\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                </span>\n              </button>\n            </div>\n          </div>\n        </main>\n\n        {/* Footer */}\n        <footer className=\"flex-shrink-0 p-4 sm:p-6 text-center\">\n          <p className=\"text-gray-300 text-sm sm:text-base\">\n            © 2024 William College General Santos City. All rights reserved.\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;;AAEzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,oBACEH,OAAA;IAAKM,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAEpDP,OAAA;MACEM,SAAS,EAAC,kDAAkD;MAC5DE,KAAK,EAAE;QACLC,eAAe,EAAE;MACnB,CAAE;MAAAF,QAAA,eAGFP,OAAA;QAAKM,SAAS,EAAC;MAAyC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAGNb,OAAA;MAAKM,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEvDP,OAAA;QAAQM,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eACjDP,OAAA;UAAKM,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAEhCP,OAAA;YAAKM,SAAS,EAAC,0CAA0C;YAAAC,QAAA,eACvDP,OAAA;cACEc,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,0CAA0C;cAC9CT,SAAS,EAAC;YAAmE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTb,OAAA;QAAMM,SAAS,EAAC,8DAA8D;QAAAC,QAAA,eAC5EP,OAAA;UAAKM,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAE5CP,OAAA;YAAKM,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CP,OAAA;cAAIM,SAAS,EAAC,8FAA8F;cAAAC,QAAA,GAAC,YAE3G,eAAAP,OAAA;gBAAMM,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAE9C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPb,OAAA;gBAAMM,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EAAC;cAE7F;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELb,OAAA;cAAGM,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE9F;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNb,OAAA;YAAKM,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3FP,OAAA;cAAKM,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDP,OAAA;gBAAKM,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,eACjEP,OAAA,CAACL,aAAa;kBAACW,SAAS,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACNb,OAAA;gBAAIM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEb,OAAA;gBAAGM,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENb,OAAA;cAAKM,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDP,OAAA;gBAAKM,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEP,OAAA,CAACJ,KAAK;kBAACU,SAAS,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNb,OAAA;gBAAIM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDb,OAAA;gBAAGM,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENb,OAAA;cAAKM,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDP,OAAA;gBAAKM,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChEP,OAAA,CAACH,QAAQ;kBAACS,SAAS,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNb,OAAA;gBAAIM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEb,OAAA;gBAAGM,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNb,OAAA;YAAKM,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBAEpGP,OAAA;cACEgB,OAAO,EAAEZ,kBAAmB;cAC5BE,SAAS,EAAC,4RAA4R;cACtS,cAAW,iCAAiC;cAAAC,QAAA,eAE5CP,OAAA;gBAAMM,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GAAC,eAEzD,eAAAP,OAAA,CAACF,UAAU;kBAACQ,SAAS,EAAC;gBAA0E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGTb,OAAA;cACEgB,OAAO,EAAEX,gBAAiB;cAC1BC,SAAS,EAAC,0TAA0T;cACpU,cAAW,uCAAuC;cAAAC,QAAA,eAElDP,OAAA;gBAAMM,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GAAC,aAEzD,eAAAP,OAAA,CAACF,UAAU;kBAACQ,SAAS,EAAC;gBAA0E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPb,OAAA;QAAQM,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACtDP,OAAA;UAAGM,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CAnIID,WAAqB;EAAA,QACRP,WAAW;AAAA;AAAAuB,EAAA,GADxBhB,WAAqB;AAqI3B,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}