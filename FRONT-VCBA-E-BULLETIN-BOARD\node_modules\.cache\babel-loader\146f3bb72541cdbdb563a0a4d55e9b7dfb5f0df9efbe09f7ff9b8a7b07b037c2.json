{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 3h12\",\n  key: \"ggurg9\"\n}], [\"path\", {\n  d: \"M6 8h12\",\n  key: \"6g4wlu\"\n}], [\"path\", {\n  d: \"m6 13 8.5 8\",\n  key: \"u1kupk\"\n}], [\"path\", {\n  d: \"M6 13h3\",\n  key: \"wdp6ag\"\n}], [\"path\", {\n  d: \"M9 13c6.667 0 6.667-10 0-10\",\n  key: \"1nkvk2\"\n}]];\nconst IndianRupee = createLucideIcon(\"indian-rupee\", __iconNode);\nexport { __iconNode, IndianRupee as default };\n//# sourceMappingURL=indian-rupee.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}