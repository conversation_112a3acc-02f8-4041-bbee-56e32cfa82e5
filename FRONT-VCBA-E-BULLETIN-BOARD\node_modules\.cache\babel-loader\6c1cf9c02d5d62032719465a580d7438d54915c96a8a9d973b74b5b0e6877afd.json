{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h9\",\n  key: \"1j9vog\"\n}], [\"path\", {\n  d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\",\n  key: \"1ocrg3\"\n}], [\"path\", {\n  d: \"m17 17 4 4\",\n  key: \"1b3523\"\n}], [\"path\", {\n  d: \"m21 17-4 4\",\n  key: \"uinynz\"\n}]];\nconst MailX = createLucideIcon(\"mail-x\", __iconNode);\nexport { __iconNode, MailX as default };\n//# sourceMappingURL=mail-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}