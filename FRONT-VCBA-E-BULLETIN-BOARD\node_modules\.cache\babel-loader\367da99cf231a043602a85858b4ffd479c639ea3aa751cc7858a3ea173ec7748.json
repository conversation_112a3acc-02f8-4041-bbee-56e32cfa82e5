{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 12H3\",\n  key: \"6jk70r\"\n}], [\"path\", {\n  d: \"M17 18H3\",\n  key: \"1amg6g\"\n}], [\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}]];\nconst AlignLeft = createLucideIcon(\"align-left\", __iconNode);\nexport { __iconNode, AlignLeft as default };\n//# sourceMappingURL=align-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}