{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5\",\n  key: \"1u7htd\"\n}], [\"path\", {\n  d: \"M15 12h.01\",\n  key: \"1k8ypt\"\n}], [\"path\", {\n  d: \"M19.38 6.813A9 9 0 0 1 20.8 10.2a2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1\",\n  key: \"11xh7x\"\n}], [\"path\", {\n  d: \"M9 12h.01\",\n  key: \"157uk2\"\n}]];\nconst Baby = createLucideIcon(\"baby\", __iconNode);\nexport { __iconNode, Baby as default };\n//# sourceMappingURL=baby.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}