{"ast": null, "code": "import _objectSpread from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"className\"];\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef((_ref, ref) => {\n    let {\n        className\n      } = _ref,\n      props = _objectWithoutProperties(_ref, _excluded);\n    return createElement(Icon, _objectSpread({\n      ref,\n      iconNode,\n      className: mergeClasses(\"lucide-\".concat(toKebabCase(toPascalCase(iconName))), \"lucide-\".concat(iconName), className)\n    }, props));\n  });\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}