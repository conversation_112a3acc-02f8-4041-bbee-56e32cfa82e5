{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 20-1.25-2.5L6 18\",\n  key: \"18frcb\"\n}], [\"path\", {\n  d: \"M10 4 8.75 6.5 6 6\",\n  key: \"7mghy3\"\n}], [\"path\", {\n  d: \"M10.585 15H10\",\n  key: \"4nqulp\"\n}], [\"path\", {\n  d: \"M2 12h6.5L10 9\",\n  key: \"kv9z4n\"\n}], [\"path\", {\n  d: \"M20 14.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0z\",\n  key: \"yu0u2z\"\n}], [\"path\", {\n  d: \"m4 10 1.5 2L4 14\",\n  key: \"k9enpj\"\n}], [\"path\", {\n  d: \"m7 21 3-6-1.5-3\",\n  key: \"j8hb9u\"\n}], [\"path\", {\n  d: \"m7 3 3 6h2\",\n  key: \"1bbqgq\"\n}]];\nconst ThermometerSnowflake = createLucideIcon(\"thermometer-snowflake\", __iconNode);\nexport { __iconNode, ThermometerSnowflake as default };\n//# sourceMappingURL=thermometer-snowflake.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}