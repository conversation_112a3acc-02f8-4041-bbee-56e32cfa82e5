{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20.2 6 3 11l-.9-2.4c-.3-1.1.3-2.2 1.3-2.5l13.5-4c1.1-.3 2.2.3 2.5 1.3Z\",\n  key: \"1tn4o7\"\n}], [\"path\", {\n  d: \"m6.2 5.3 3.1 3.9\",\n  key: \"iuk76l\"\n}], [\"path\", {\n  d: \"m12.4 3.4 3.1 4\",\n  key: \"6hsd6n\"\n}], [\"path\", {\n  d: \"M3 11h18v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Z\",\n  key: \"ltgou9\"\n}]];\nconst Clapperboard = createLucideIcon(\"clapperboard\", __iconNode);\nexport { __iconNode, Clapperboard as default };\n//# sourceMappingURL=clapperboard.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}