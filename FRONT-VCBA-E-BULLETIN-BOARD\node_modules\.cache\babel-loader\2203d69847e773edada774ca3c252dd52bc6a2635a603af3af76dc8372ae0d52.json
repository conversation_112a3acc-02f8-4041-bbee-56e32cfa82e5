{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"10wcwx\"\n}], [\"rect\", {\n  width: \"9\",\n  height: \"6\",\n  x: \"9\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"4p5bwg\"\n}], [\"path\", {\n  d: \"M22 22V2\",\n  key: \"12ipfv\"\n}]];\nconst AlignEndVertical = createLucideIcon(\"align-end-vertical\", __iconNode);\nexport { __iconNode, AlignEndVertical as default };\n//# sourceMappingURL=align-end-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}