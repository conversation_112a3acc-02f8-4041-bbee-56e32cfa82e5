{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 18 3-3-3-3\",\n  key: \"18f6ys\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M4 11V4a2 2 0 0 1 2-2h9l5 5v13a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h7\",\n  key: \"50q2rw\"\n}]];\nconst FileSymlink = createLucideIcon(\"file-symlink\", __iconNode);\nexport { __iconNode, FileSymlink as default };\n//# sourceMappingURL=file-symlink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}