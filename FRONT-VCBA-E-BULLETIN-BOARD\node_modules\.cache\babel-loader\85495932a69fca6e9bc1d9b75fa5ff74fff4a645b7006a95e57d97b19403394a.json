{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 3-5 5-5-5h10\",\n  key: \"1ftt6x\"\n}], [\"path\", {\n  d: \"m17 21-5-5-5 5h10\",\n  key: \"1m0wmu\"\n}], [\"path\", {\n  d: \"M4 12H2\",\n  key: \"rhcxmi\"\n}], [\"path\", {\n  d: \"M10 12H8\",\n  key: \"s88cx1\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}]];\nconst FlipVertical2 = createLucideIcon(\"flip-vertical-2\", __iconNode);\nexport { __iconNode, FlipVertical2 as default };\n//# sourceMappingURL=flip-vertical-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}