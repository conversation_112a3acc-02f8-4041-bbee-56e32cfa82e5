{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12h11\",\n  key: \"6m4ad9\"\n}], [\"path\", {\n  d: \"M10 18h11\",\n  key: \"11hvi2\"\n}], [\"path\", {\n  d: \"M10 6h11\",\n  key: \"c7qv1k\"\n}], [\"path\", {\n  d: \"m3 10 3-3-3-3\",\n  key: \"i7pm08\"\n}], [\"path\", {\n  d: \"m3 20 3-3-3-3\",\n  key: \"20gx1n\"\n}]];\nconst ListCollapse = createLucideIcon(\"list-collapse\", __iconNode);\nexport { __iconNode, ListCollapse as default };\n//# sourceMappingURL=list-collapse.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}