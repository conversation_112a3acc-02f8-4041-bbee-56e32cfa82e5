{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/common/ProfilePictureUpload';\nimport { StudentProfilePictureService, ProfilePictureService } from '../../services/profilePictureService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentSettings = () => {\n  _s();\n  const {\n    user,\n    refreshUser\n  } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile',\n    icon: User\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }, {\n    key: 'privacy',\n    label: 'Privacy',\n    icon: Lock\n  }];\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async file => {\n    try {\n      setProfilePictureLoading(true);\n      setProfilePictureError(null);\n      setSuccessMessage(null);\n      const response = await StudentProfilePictureService.uploadProfilePicture(file);\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        // Refresh user data to get updated profile picture\n        await refreshUser();\n      } else {\n        throw new Error(response.message || 'Upload failed');\n      }\n    } catch (error) {\n      console.error('Profile picture upload error:', error);\n      setProfilePictureError(error instanceof Error ? error.message : 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    try {\n      setProfilePictureLoading(true);\n      setProfilePictureError(null);\n      setSuccessMessage(null);\n      const response = await StudentProfilePictureService.deleteProfilePicture();\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // Refresh user data to get updated profile picture\n        await refreshUser();\n      } else {\n        throw new Error(response.message || 'Remove failed');\n      }\n    } catch (error) {\n      console.error('Profile picture remove error:', error);\n      setProfilePictureError(error instanceof Error ? error.message : 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n  const renderProfileSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"Profile Picture\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n        currentImageUrl: ProfilePictureService.getProfilePictureUrl(user === null || user === void 0 ? void 0 : user.profilePicture, 'student'),\n        onImageSelect: handleProfilePictureUpload,\n        onImageRemove: handleProfilePictureRemove,\n        loading: profilePictureLoading,\n        error: profilePictureError,\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          color: '#059669',\n          fontSize: '0.875rem',\n          backgroundColor: 'rgba(236, 253, 245, 0.8)',\n          padding: '0.75rem',\n          borderRadius: '8px',\n          border: '1px solid rgba(167, 243, 208, 0.5)',\n          marginTop: '1rem',\n          backdropFilter: 'blur(10px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), successMessage]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"Personal Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            },\n            children: \"First Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            defaultValue: user === null || user === void 0 ? void 0 : user.firstName,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            },\n            children: \"Last Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            defaultValue: user === null || user === void 0 ? void 0 : user.lastName,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            gridColumn: '1 / -1'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            },\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            defaultValue: user === null || user === void 0 ? void 0 : user.email,\n            disabled: true,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none',\n              background: '#f8fafc',\n              color: '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              margin: '0.5rem 0 0 0'\n            },\n            children: \"Email address cannot be changed. Contact admin for assistance.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            },\n            children: \"Student ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            defaultValue: \"VCBA-2025-001\",\n            disabled: true,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none',\n              background: '#f8fafc',\n              color: '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            },\n            children: \"Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            defaultValue: \"BS Business Administration\",\n            disabled: true,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none',\n              background: '#f8fafc',\n              color: '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '2rem',\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            background: 'none',\n            border: '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            cursor: 'pointer',\n            color: '#6b7280'\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n  const renderNotificationSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Notification Preferences\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Email Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive announcements via email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Push Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Get instant notifications on your device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Alert Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive urgent alerts and important notices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n  const renderPrivacySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Privacy Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Profile Visibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Allow other students to see your profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Activity Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Show when you're online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 402,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 18,\n            color: activeTab === tab.key ? '#22c55e' : '#6b7280'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this), renderContent()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 486,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentSettings, \"wjQ+S0AUSDClATTGd9K/L0nOYxc=\", false, function () {\n  return [useStudentAuth];\n});\n_c = StudentSettings;\nexport default StudentSettings;\nvar _c;\n$RefreshReg$(_c, \"StudentSettings\");", "map": {"version": 3, "names": ["React", "useState", "useStudentAuth", "User", "Bell", "Lock", "CheckCircle", "ProfilePictureUpload", "StudentProfilePictureService", "ProfilePictureService", "jsxDEV", "_jsxDEV", "StudentSettings", "_s", "user", "refreshUser", "activeTab", "setActiveTab", "profilePictureLoading", "setProfilePictureLoading", "profilePictureError", "setProfilePictureError", "successMessage", "setSuccessMessage", "tabs", "key", "label", "icon", "handleProfilePictureUpload", "file", "response", "uploadProfilePicture", "success", "Error", "message", "error", "console", "handleProfilePictureRemove", "deleteProfilePicture", "useEffect", "timer", "setTimeout", "clearTimeout", "renderProfileSettings", "style", "display", "flexDirection", "gap", "children", "background", "borderRadius", "padding", "border", "<PERSON><PERSON>ilter", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentImageUrl", "getProfilePictureUrl", "profilePicture", "onImageSelect", "onImageRemove", "loading", "size", "alignItems", "backgroundColor", "marginTop", "gridTemplateColumns", "marginBottom", "type", "defaultValue", "firstName", "width", "outline", "lastName", "gridColumn", "email", "disabled", "cursor", "renderNotificationSettings", "boxShadow", "justifyContent", "position", "height", "defaultChecked", "opacity", "top", "left", "right", "bottom", "transition", "renderPrivacySettings", "renderContent", "flexWrap", "map", "tab", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentSettings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/common/ProfilePictureUpload';\nimport { StudentProfilePictureService, ProfilePictureService } from '../../services/profilePictureService';\n\nconst StudentSettings: React.FC = () => {\n  const { user, refreshUser } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'privacy'>('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile', icon: User },\n    { key: 'notifications', label: 'Notifications', icon: Bell },\n    { key: 'privacy', label: 'Privacy', icon: Lock }\n  ];\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async (file: File) => {\n    try {\n      setProfilePictureLoading(true);\n      setProfilePictureError(null);\n      setSuccessMessage(null);\n\n      const response = await StudentProfilePictureService.uploadProfilePicture(file);\n\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        // Refresh user data to get updated profile picture\n        await refreshUser();\n      } else {\n        throw new Error(response.message || 'Upload failed');\n      }\n    } catch (error) {\n      console.error('Profile picture upload error:', error);\n      setProfilePictureError(error instanceof Error ? error.message : 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    try {\n      setProfilePictureLoading(true);\n      setProfilePictureError(null);\n      setSuccessMessage(null);\n\n      const response = await StudentProfilePictureService.deleteProfilePicture();\n\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // Refresh user data to get updated profile picture\n        await refreshUser();\n      } else {\n        throw new Error(response.message || 'Remove failed');\n      }\n    } catch (error) {\n      console.error('Profile picture remove error:', error);\n      setProfilePictureError(error instanceof Error ? error.message : 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Picture */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Profile Picture\n        </h3>\n\n        <ProfilePictureUpload\n          currentImageUrl={ProfilePictureService.getProfilePictureUrl(user?.profilePicture, 'student')}\n          onImageSelect={handleProfilePictureUpload}\n          onImageRemove={handleProfilePictureRemove}\n          loading={profilePictureLoading}\n          error={profilePictureError}\n          size=\"large\"\n        />\n\n        {/* Success Message */}\n        {successMessage && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            color: '#059669',\n            fontSize: '0.875rem',\n            backgroundColor: 'rgba(236, 253, 245, 0.8)',\n            padding: '0.75rem',\n            borderRadius: '8px',\n            border: '1px solid rgba(167, 243, 208, 0.5)',\n            marginTop: '1rem',\n            backdropFilter: 'blur(10px)'\n          }}>\n            <CheckCircle size={16} />\n            {successMessage}\n          </div>\n        )}\n      </div>\n\n      {/* Personal Information */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Personal Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              First Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.firstName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Last Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.lastName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div style={{ gridColumn: '1 / -1' }}>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              defaultValue={user?.email}\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n            <p style={{\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              margin: '0.5rem 0 0 0'\n            }}>\n              Email address cannot be changed. Contact admin for assistance.\n            </p>\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Student ID\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"VCBA-2025-001\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Course\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"BS Business Administration\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n        </div>\n        \n        <div style={{ marginTop: '2rem', display: 'flex', gap: '1rem' }}>\n          <button style={{\n            background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          }}>\n            Save Changes\n          </button>\n          <button style={{\n            background: 'none',\n            border: '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            cursor: 'pointer',\n            color: '#6b7280'\n          }}>\n            Cancel\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Notification Preferences\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Email Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive announcements via email\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Push Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Get instant notifications on your device\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Alert Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive urgent alerts and important notices\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPrivacySettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Privacy Settings\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Profile Visibility\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Allow other students to see your profile\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Activity Status\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Show when you're online\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div style={{ width: '100%', padding: '2rem' }}>\n      {/* Tabs */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      }}>\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key \n                  ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              <tab.icon size={18} color={activeTab === tab.key ? '#22c55e' : '#6b7280'} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      {renderContent()}\n    </div>\n  );\n};\n\nexport default StudentSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,QAAQ,cAAc;AAC5D,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,4BAA4B,EAAEC,qBAAqB,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3G,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,IAAI;IAAEC;EAAY,CAAC,GAAGb,cAAc,CAAC,CAAC;EAC9C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAA0C,SAAS,CAAC;EAC9F,MAAM,CAACiB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACmB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EAEzE,MAAMuB,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAExB;EAAK,CAAC,EAChD;IAAEsB,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAEvB;EAAK,CAAC,EAC5D;IAAEqB,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEtB;EAAK,CAAC,CACjD;;EAED;EACA,MAAMuB,0BAA0B,GAAG,MAAOC,IAAU,IAAK;IACvD,IAAI;MACFV,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,sBAAsB,CAAC,IAAI,CAAC;MAC5BE,iBAAiB,CAAC,IAAI,CAAC;MAEvB,MAAMO,QAAQ,GAAG,MAAMtB,4BAA4B,CAACuB,oBAAoB,CAACF,IAAI,CAAC;MAE9E,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpBT,iBAAiB,CAAC,wCAAwC,CAAC;QAC3D;QACA,MAAMR,WAAW,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAM,IAAIkB,KAAK,CAACH,QAAQ,CAACI,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDd,sBAAsB,CAACc,KAAK,YAAYF,KAAK,GAAGE,KAAK,CAACD,OAAO,GAAG,kCAAkC,CAAC;IACrG,CAAC,SAAS;MACRf,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMkB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACFlB,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,sBAAsB,CAAC,IAAI,CAAC;MAC5BE,iBAAiB,CAAC,IAAI,CAAC;MAEvB,MAAMO,QAAQ,GAAG,MAAMtB,4BAA4B,CAAC8B,oBAAoB,CAAC,CAAC;MAE1E,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBT,iBAAiB,CAAC,uCAAuC,CAAC;QAC1D;QACA,MAAMR,WAAW,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAM,IAAIkB,KAAK,CAACH,QAAQ,CAACI,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDd,sBAAsB,CAACc,KAAK,YAAYF,KAAK,GAAGE,KAAK,CAACD,OAAO,GAAG,kCAAkC,CAAC;IACrG,CAAC,SAAS;MACRf,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACAnB,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAIjB,cAAc,IAAIF,mBAAmB,EAAE;MACzC,MAAMoB,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlB,iBAAiB,CAAC,IAAI,CAAC;QACvBF,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMqB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAClB,cAAc,EAAEF,mBAAmB,CAAC,CAAC;EAEzC,MAAMuB,qBAAqB,GAAGA,CAAA,kBAC5BhC,OAAA;IAAKiC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEpErC,OAAA;MAAKiC,KAAK,EAAE;QACVK,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,oCAAoC;QAC5CC,cAAc,EAAE;MAClB,CAAE;MAAAL,QAAA,gBACArC,OAAA;QAAIiC,KAAK,EAAE;UACTU,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlD,OAAA,CAACJ,oBAAoB;QACnBuD,eAAe,EAAErD,qBAAqB,CAACsD,oBAAoB,CAACjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,cAAc,EAAE,SAAS,CAAE;QAC7FC,aAAa,EAAErC,0BAA2B;QAC1CsC,aAAa,EAAE7B,0BAA2B;QAC1C8B,OAAO,EAAEjD,qBAAsB;QAC/BiB,KAAK,EAAEf,mBAAoB;QAC3BgD,IAAI,EAAC;MAAO;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EAGDvC,cAAc,iBACbX,OAAA;QAAKiC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfwB,UAAU,EAAE,QAAQ;UACpBtB,GAAG,EAAE,QAAQ;UACbQ,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,UAAU;UACpBc,eAAe,EAAE,0BAA0B;UAC3CnB,OAAO,EAAE,SAAS;UAClBD,YAAY,EAAE,KAAK;UACnBE,MAAM,EAAE,oCAAoC;UAC5CmB,SAAS,EAAE,MAAM;UACjBlB,cAAc,EAAE;QAClB,CAAE;QAAAL,QAAA,gBACArC,OAAA,CAACL,WAAW;UAAC8D,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxBvC,cAAc;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNlD,OAAA;MAAKiC,KAAK,EAAE;QACVK,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,oCAAoC;QAC5CC,cAAc,EAAE;MAClB,CAAE;MAAAL,QAAA,gBACArC,OAAA;QAAIiC,KAAK,EAAE;UACTU,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlD,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE2B,mBAAmB,EAAE,SAAS;UAAEzB,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAC7ErC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAOiC,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChB4B,YAAY,EAAE,QAAQ;cACtBlB,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,YAAY,EAAE7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,SAAU;YAC9BhC,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACb1B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE,MAAM;cAChBsB,OAAO,EAAE;YACX;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlD,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAOiC,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChB4B,YAAY,EAAE,QAAQ;cACtBlB,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,YAAY,EAAE7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,QAAS;YAC7BnC,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACb1B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE,MAAM;cAChBsB,OAAO,EAAE;YACX;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlD,OAAA;UAAKiC,KAAK,EAAE;YAAEoC,UAAU,EAAE;UAAS,CAAE;UAAAhC,QAAA,gBACnCrC,OAAA;YAAOiC,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChB4B,YAAY,EAAE,QAAQ;cACtBlB,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE+D,IAAI,EAAC,OAAO;YACZC,YAAY,EAAE7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,KAAM;YAC1BC,QAAQ;YACRtC,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACb1B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE,MAAM;cAChBsB,OAAO,EAAE,MAAM;cACf7B,UAAU,EAAE,SAAS;cACrBM,KAAK,EAAE;YACT;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFlD,OAAA;YAAGiC,KAAK,EAAE;cACRY,QAAQ,EAAE,SAAS;cACnBD,KAAK,EAAE,SAAS;cAChBD,MAAM,EAAE;YACV,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENlD,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAOiC,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChB4B,YAAY,EAAE,QAAQ;cACtBlB,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,YAAY,EAAC,eAAe;YAC5BO,QAAQ;YACRtC,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACb1B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE,MAAM;cAChBsB,OAAO,EAAE,MAAM;cACf7B,UAAU,EAAE,SAAS;cACrBM,KAAK,EAAE;YACT;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlD,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAOiC,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChB4B,YAAY,EAAE,QAAQ;cACtBlB,KAAK,EAAE,SAAS;cAChBE,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,YAAY,EAAC,4BAA4B;YACzCO,QAAQ;YACRtC,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACb1B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE,MAAM;cAChBsB,OAAO,EAAE,MAAM;cACf7B,UAAU,EAAE,SAAS;cACrBM,KAAK,EAAE;YACT;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAKiC,KAAK,EAAE;UAAE2B,SAAS,EAAE,MAAM;UAAE1B,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAC,QAAA,gBAC9DrC,OAAA;UAAQiC,KAAK,EAAE;YACbK,UAAU,EAAE,mDAAmD;YAC/DM,KAAK,EAAE,OAAO;YACdH,MAAM,EAAE,MAAM;YACdF,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,cAAc;YACvBM,UAAU,EAAE,KAAK;YACjB0B,MAAM,EAAE;UACV,CAAE;UAAAnC,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA;UAAQiC,KAAK,EAAE;YACbK,UAAU,EAAE,MAAM;YAClBG,MAAM,EAAE,mBAAmB;YAC3BF,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,cAAc;YACvBgC,MAAM,EAAE,SAAS;YACjB5B,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMuB,0BAA0B,GAAGA,CAAA,kBACjCzE,OAAA;IAAKiC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfkC,SAAS,EAAE,gCAAgC;MAC3CjC,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACArC,OAAA;MAAIiC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELlD,OAAA;MAAKiC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtErC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAEjB,UAAU,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACrFrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKiC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEkB,YAAY,EAAE;YAAU,CAAE;YAAAzB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAKiC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAOiC,KAAK,EAAE;YAAE2C,QAAQ,EAAE,UAAU;YAAE1C,OAAO,EAAE,cAAc;YAAEgC,KAAK,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC7FrC,OAAA;YAAO+D,IAAI,EAAC,UAAU;YAACe,cAAc;YAAC7C,KAAK,EAAE;cAAE8C,OAAO,EAAE,CAAC;cAAEb,KAAK,EAAE,CAAC;cAAEW,MAAM,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFlD,OAAA;YAAMiC,KAAK,EAAE;cACX2C,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT7C,UAAU,EAAE,SAAS;cACrB8C,UAAU,EAAE,MAAM;cAClB7C,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAEjB,UAAU,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACrFrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKiC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEkB,YAAY,EAAE;YAAU,CAAE;YAAAzB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAKiC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAOiC,KAAK,EAAE;YAAE2C,QAAQ,EAAE,UAAU;YAAE1C,OAAO,EAAE,cAAc;YAAEgC,KAAK,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC7FrC,OAAA;YAAO+D,IAAI,EAAC,UAAU;YAACe,cAAc;YAAC7C,KAAK,EAAE;cAAE8C,OAAO,EAAE,CAAC;cAAEb,KAAK,EAAE,CAAC;cAAEW,MAAM,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFlD,OAAA;YAAMiC,KAAK,EAAE;cACX2C,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT7C,UAAU,EAAE,SAAS;cACrB8C,UAAU,EAAE,MAAM;cAClB7C,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAEjB,UAAU,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACrFrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKiC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEkB,YAAY,EAAE;YAAU,CAAE;YAAAzB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAKiC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAOiC,KAAK,EAAE;YAAE2C,QAAQ,EAAE,UAAU;YAAE1C,OAAO,EAAE,cAAc;YAAEgC,KAAK,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC7FrC,OAAA;YAAO+D,IAAI,EAAC,UAAU;YAACe,cAAc;YAAC7C,KAAK,EAAE;cAAE8C,OAAO,EAAE,CAAC;cAAEb,KAAK,EAAE,CAAC;cAAEW,MAAM,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFlD,OAAA;YAAMiC,KAAK,EAAE;cACX2C,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT7C,UAAU,EAAE,SAAS;cACrB8C,UAAU,EAAE,MAAM;cAClB7C,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMmC,qBAAqB,GAAGA,CAAA,kBAC5BrF,OAAA;IAAKiC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfkC,SAAS,EAAE,gCAAgC;MAC3CjC,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACArC,OAAA;MAAIiC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELlD,OAAA;MAAKiC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtErC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAEjB,UAAU,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACrFrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKiC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEkB,YAAY,EAAE;YAAU,CAAE;YAAAzB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAKiC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAOiC,KAAK,EAAE;YAAE2C,QAAQ,EAAE,UAAU;YAAE1C,OAAO,EAAE,cAAc;YAAEgC,KAAK,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC7FrC,OAAA;YAAO+D,IAAI,EAAC,UAAU;YAACe,cAAc;YAAC7C,KAAK,EAAE;cAAE8C,OAAO,EAAE,CAAC;cAAEb,KAAK,EAAE,CAAC;cAAEW,MAAM,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFlD,OAAA;YAAMiC,KAAK,EAAE;cACX2C,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT7C,UAAU,EAAE,SAAS;cACrB8C,UAAU,EAAE,MAAM;cAClB7C,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAEjB,UAAU,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACrFrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKiC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEkB,YAAY,EAAE;YAAU,CAAE;YAAAzB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAKiC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAOiC,KAAK,EAAE;YAAE2C,QAAQ,EAAE,UAAU;YAAE1C,OAAO,EAAE,cAAc;YAAEgC,KAAK,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC7FrC,OAAA;YAAO+D,IAAI,EAAC,UAAU;YAAC9B,KAAK,EAAE;cAAE8C,OAAO,EAAE,CAAC;cAAEb,KAAK,EAAE,CAAC;cAAEW,MAAM,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrElD,OAAA;YAAMiC,KAAK,EAAE;cACX2C,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT7C,UAAU,EAAE,MAAM;cAClB8C,UAAU,EAAE,MAAM;cAClB7C,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMoC,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQjF,SAAS;MACf,KAAK,SAAS;QACZ,OAAO2B,qBAAqB,CAAC,CAAC;MAChC,KAAK,eAAe;QAClB,OAAOyC,0BAA0B,CAAC,CAAC;MACrC,KAAK,SAAS;QACZ,OAAOY,qBAAqB,CAAC,CAAC;MAChC;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACErF,OAAA;IAAKiC,KAAK,EAAE;MAAEiC,KAAK,EAAE,MAAM;MAAE1B,OAAO,EAAE;IAAO,CAAE;IAAAH,QAAA,gBAE7CrC,OAAA;MAAKiC,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBsB,YAAY,EAAE,MAAM;QACpBY,SAAS,EAAE,gCAAgC;QAC3CjC,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,eACArC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEmD,QAAQ,EAAE;QAAO,CAAE;QAAAlD,QAAA,EAC5DxB,IAAI,CAAC2E,GAAG,CAACC,GAAG,iBACXzF,OAAA;UAEE0F,OAAO,EAAEA,CAAA,KAAMpF,YAAY,CAACmF,GAAG,CAAC3E,GAAU,CAAE;UAC5CmB,KAAK,EAAE;YACLK,UAAU,EAAEjC,SAAS,KAAKoF,GAAG,CAAC3E,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjB8B,KAAK,EAAEvC,SAAS,KAAKoF,GAAG,CAAC3E,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD2B,MAAM,EAAEpC,SAAS,KAAKoF,GAAG,CAAC3E,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5DyB,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzBgC,MAAM,EAAE,SAAS;YACjB1B,UAAU,EAAE,KAAK;YACjBZ,OAAO,EAAE,MAAM;YACfwB,UAAU,EAAE,QAAQ;YACpBtB,GAAG,EAAE,QAAQ;YACbgD,UAAU,EAAE;UACd,CAAE;UAAA/C,QAAA,gBAEFrC,OAAA,CAACyF,GAAG,CAACzE,IAAI;YAACyC,IAAI,EAAE,EAAG;YAACb,KAAK,EAAEvC,SAAS,KAAKoF,GAAG,CAAC3E,GAAG,GAAG,SAAS,GAAG;UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC3EuC,GAAG,CAAC1E,KAAK;QAAA,GAnBL0E,GAAG,CAAC3E,GAAG;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLoC,aAAa,CAAC,CAAC;EAAA;IAAAvC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAAChD,EAAA,CAzgBID,eAAyB;EAAA,QACCV,cAAc;AAAA;AAAAoG,EAAA,GADxC1F,eAAyB;AA2gB/B,eAAeA,eAAe;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}