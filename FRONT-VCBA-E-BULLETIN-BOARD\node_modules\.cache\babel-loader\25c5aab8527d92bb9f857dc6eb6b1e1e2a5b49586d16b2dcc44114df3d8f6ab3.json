{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useReducer,useCallback,useEffect}from'react';import{StudentAuthService}from'../services/student-auth.service';// Student Auth context interface\nimport{jsx as _jsx}from\"react/jsx-runtime\";// Initial state\nconst initialState={user:null,isAuthenticated:false,isLoading:true,error:null};// Action types\n// Reducer\nconst authReducer=(state,action)=>{switch(action.type){case'AUTH_START':return _objectSpread(_objectSpread({},state),{},{isLoading:true,error:null});case'AUTH_SUCCESS':return _objectSpread(_objectSpread({},state),{},{user:action.payload,isAuthenticated:true,isLoading:false,error:null});case'AUTH_ERROR':return _objectSpread(_objectSpread({},state),{},{user:null,isAuthenticated:false,isLoading:false,error:action.payload});case'AUTH_LOGOUT':return _objectSpread(_objectSpread({},state),{},{user:null,isAuthenticated:false,isLoading:false,error:null});case'SET_LOADING':return _objectSpread(_objectSpread({},state),{},{isLoading:action.payload});case'CLEAR_ERROR':return _objectSpread(_objectSpread({},state),{},{error:null});default:return state;}};// Create context\nconst StudentAuthContext=/*#__PURE__*/createContext(undefined);// Provider component\nexport const StudentAuthProvider=_ref=>{let{children}=_ref;const[state,dispatch]=useReducer(authReducer,initialState);// Clear error function\nconst clearError=useCallback(()=>{dispatch({type:'CLEAR_ERROR'});},[]);// Check authentication status\nconst checkAuthStatus=useCallback(async()=>{try{console.log('🔍 StudentAuth - Checking authentication status');dispatch({type:'SET_LOADING',payload:true});// First check local storage for authentication data\nconst storedUser=StudentAuthService.getStoredUser();const hasToken=StudentAuthService.isAuthenticated();console.log('🔍 StudentAuth - Local auth data:',{hasUser:!!storedUser,hasToken:!!hasToken,userRole:storedUser===null||storedUser===void 0?void 0:storedUser.role,userEmail:storedUser===null||storedUser===void 0?void 0:storedUser.email});// If we have local data and it's a student user, authenticate immediately\nif(storedUser&&hasToken&&storedUser.role==='student'){console.log('✅ StudentAuth - Student user authenticated from local storage:',storedUser.email);dispatch({type:'AUTH_SUCCESS',payload:storedUser});return;}// If no local data or wrong role, logout\nconsole.log('❌ StudentAuth - No student user found in local storage or wrong role');dispatch({type:'AUTH_LOGOUT'});}catch(error){console.error('❌ StudentAuth - Auth check failed:',error.message);dispatch({type:'AUTH_LOGOUT'});}},[]);// Login function\nconst login=async credentials=>{try{dispatch({type:'AUTH_START'});// Use StudentAuthService for login\nconst response=await StudentAuthService.login(credentials);// Verify the user is actually a student\nif(response.data.user.role!=='student'){throw new Error('Access denied: Student account required');}console.log('✅ StudentAuth - Student login successful:',response.data.user.email);dispatch({type:'AUTH_SUCCESS',payload:response.data.user});}catch(error){const errorMessage=error.message||'Student login failed. Please try again.';console.error('❌ StudentAuth - Login failed:',errorMessage);dispatch({type:'AUTH_ERROR',payload:errorMessage});throw error;}};// Logout function\nconst logout=async()=>{try{console.log('🚪 StudentAuth - Starting student logout process');await StudentAuthService.logout();console.log('✅ StudentAuth - Server logout successful');}catch(error){console.error('❌ StudentAuth - Logout error:',error);}finally{console.log('🧹 StudentAuth - Clearing student state');dispatch({type:'AUTH_LOGOUT'});// Redirect to student login\nwindow.location.href='/student/login';}};// Setup response interceptor for handling unauthorized requests - DISABLED\nuseEffect(()=>{console.log('🔧 StudentAuth - Response interceptor DISABLED to prevent automatic logouts');// setupResponseInterceptor(() => {\n//   console.log('🚨 StudentAuth - Unauthorized request detected, logging out student');\n//   dispatch({ type: 'AUTH_LOGOUT' });\n// });\n},[]);// Check authentication status on mount\nuseEffect(()=>{console.log('🚀 StudentAuth - Component mounted, checking student auth status');checkAuthStatus();},[checkAuthStatus]);// Context value\nconst value=_objectSpread(_objectSpread({},state),{},{login,logout,clearError,checkAuthStatus,userType:'student'});return/*#__PURE__*/_jsx(StudentAuthContext.Provider,{value:value,children:children});};// Hook to use student auth context\nexport const useStudentAuth=()=>{const context=useContext(StudentAuthContext);if(context===undefined){throw new Error('useStudentAuth must be used within a StudentAuthProvider');}return context;};export default StudentAuthContext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}