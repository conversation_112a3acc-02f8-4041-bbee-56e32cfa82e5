{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 11h.01\",\n  key: \"d2at3l\"\n}], [\"path\", {\n  d: \"M14 6h.01\",\n  key: \"k028ub\"\n}], [\"path\", {\n  d: \"M18 6h.01\",\n  key: \"1v4wsw\"\n}], [\"path\", {\n  d: \"M6.5 13.1h.01\",\n  key: \"1748ia\"\n}], [\"path\", {\n  d: \"M22 5c0 9-4 12-6 12s-6-3-6-12c0-2 2-3 6-3s6 1 6 3\",\n  key: \"172yzv\"\n}], [\"path\", {\n  d: \"M17.4 9.9c-.8.8-2 .8-2.8 0\",\n  key: \"1obv0w\"\n}], [\"path\", {\n  d: \"M10.1 7.1C9 7.2 7.7 7.7 6 8.6c-3.5 2-4.7 3.9-3.7 5.6 4.5 7.8 9.5 8.4 11.2 7.4.9-.5 1.9-2.1 1.9-4.7\",\n  key: \"rqjl8i\"\n}], [\"path\", {\n  d: \"M9.1 16.5c.3-1.1 1.4-1.7 2.4-1.4\",\n  key: \"1mr6wy\"\n}]];\nconst Drama = createLucideIcon(\"drama\", __iconNode);\nexport { __iconNode, Drama as default };\n//# sourceMappingURL=drama.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}