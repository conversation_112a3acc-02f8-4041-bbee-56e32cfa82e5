{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = string => string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = string => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function () {\n  for (var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++) {\n    classes[_key] = arguments[_key];\n  }\n  return classes.filter((className, index, array) => {\n    return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n  }).join(\" \").trim();\n};\nconst hasA11yProp = props => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}