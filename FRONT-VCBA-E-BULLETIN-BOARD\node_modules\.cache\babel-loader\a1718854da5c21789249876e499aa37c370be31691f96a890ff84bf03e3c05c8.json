{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 10.5 15 9\",\n  key: \"1nsxvm\"\n}], [\"path\", {\n  d: \"M4 4v15a1 1 0 0 0 1 1h15\",\n  key: \"1w6lkd\"\n}], [\"path\", {\n  d: \"M4.293 19.707 6 18\",\n  key: \"3g1p8c\"\n}], [\"path\", {\n  d: \"m9 15 1.5-1.5\",\n  key: \"1xfbes\"\n}]];\nconst Axis3d = createLucideIcon(\"axis-3d\", __iconNode);\nexport { __iconNode, Axis3d as default };\n//# sourceMappingURL=axis-3d.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}