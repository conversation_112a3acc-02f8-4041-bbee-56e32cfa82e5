{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\UnifiedCommentSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo } from 'react';\nimport { detectUserContext } from '../../utils/authUtils';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n/**\n * Unified Comment Section that automatically detects user role\n * and renders the appropriate comment component with role-specific features\n */\nconst UnifiedCommentSection = ({\n  announcementId,\n  calendarId,\n  allowComments = true,\n  currentUserId,\n  currentUserType,\n  forceRole\n}) => {\n  _s();\n  // Detect user context\n  const userContext = useMemo(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return {\n        ...context,\n        role: forceRole\n      };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Determine the effective user type\n  const effectiveUserType = useMemo(() => {\n    // Priority 1: Explicit prop\n    if (currentUserType) {\n      return currentUserType;\n    }\n\n    // Priority 2: Detected context\n    if (userContext.role) {\n      return userContext.role;\n    }\n\n    // Priority 3: Path-based detection\n    const currentPath = window.location.pathname;\n    if (currentPath.includes('/admin')) {\n      return 'admin';\n    } else if (currentPath.includes('/student')) {\n      return 'student';\n    }\n\n    // Default fallback\n    return 'student';\n  }, [currentUserType, userContext.role]);\n\n  // Determine the effective user ID\n  const effectiveUserId = useMemo(() => {\n    if (currentUserId) {\n      return currentUserId;\n    }\n    if (userContext.user) {\n      // Try to get ID from user context\n      return userContext.user.id || userContext.user.admin_id || userContext.user.student_id;\n    }\n    return undefined;\n  }, [currentUserId, userContext.user]);\n\n  // Validation\n  if (!announcementId && !calendarId) {\n    console.error('UnifiedCommentSection: Either announcementId or calendarId must be provided');\n    return null;\n  }\n  if (announcementId && calendarId) {\n    console.error('UnifiedCommentSection: Cannot provide both announcementId and calendarId');\n    return null;\n  }\n\n  // Render appropriate comment component based on user role\n  if (effectiveUserType === 'admin') {\n    return /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n      announcementId: announcementId,\n      calendarId: calendarId,\n      allowComments: allowComments,\n      currentUserId: effectiveUserId,\n      currentUserType: \"admin\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  } else {\n    return /*#__PURE__*/_jsxDEV(CommentSection, {\n      announcementId: announcementId,\n      calendarId: calendarId,\n      allowComments: allowComments,\n      currentUserId: effectiveUserId,\n      currentUserType: \"student\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this);\n  }\n};\n_s(UnifiedCommentSection, \"SO10kPQHj1VVZYKg0Ae2HmGE12k=\");\n_c = UnifiedCommentSection;\nexport default UnifiedCommentSection;\nvar _c;\n$RefreshReg$(_c, \"UnifiedCommentSection\");", "map": {"version": 3, "names": ["React", "useMemo", "detectUserContext", "AdminCommentSection", "CommentSection", "jsxDEV", "_jsxDEV", "UnifiedCommentSection", "announcementId", "calendarId", "allowComments", "currentUserId", "currentUserType", "forceRole", "_s", "userContext", "context", "role", "effectiveUserType", "currentPath", "window", "location", "pathname", "includes", "effectiveUserId", "user", "id", "admin_id", "student_id", "undefined", "console", "error", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/UnifiedCommentSection.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { detectUserContext } from '../../utils/authUtils';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\n\ninterface UnifiedCommentSectionProps {\n  announcementId?: number;\n  calendarId?: number;\n  allowComments?: boolean;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n  // Optional prop to force a specific role (useful for testing)\n  forceRole?: 'admin' | 'student';\n}\n\n/**\n * Unified Comment Section that automatically detects user role\n * and renders the appropriate comment component with role-specific features\n */\nconst UnifiedCommentSection: React.FC<UnifiedCommentSectionProps> = ({\n  announcementId,\n  calendarId,\n  allowComments = true,\n  currentUserId,\n  currentUserType,\n  forceRole\n}) => {\n  // Detect user context\n  const userContext = useMemo(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return { ...context, role: forceRole };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Determine the effective user type\n  const effectiveUserType = useMemo(() => {\n    // Priority 1: Explicit prop\n    if (currentUserType) {\n      return currentUserType;\n    }\n    \n    // Priority 2: Detected context\n    if (userContext.role) {\n      return userContext.role;\n    }\n    \n    // Priority 3: Path-based detection\n    const currentPath = window.location.pathname;\n    if (currentPath.includes('/admin')) {\n      return 'admin';\n    } else if (currentPath.includes('/student')) {\n      return 'student';\n    }\n    \n    // Default fallback\n    return 'student';\n  }, [currentUserType, userContext.role]);\n\n  // Determine the effective user ID\n  const effectiveUserId = useMemo(() => {\n    if (currentUserId) {\n      return currentUserId;\n    }\n    \n    if (userContext.user) {\n      // Try to get ID from user context\n      return userContext.user.id || userContext.user.admin_id || userContext.user.student_id;\n    }\n    \n    return undefined;\n  }, [currentUserId, userContext.user]);\n\n  // Validation\n  if (!announcementId && !calendarId) {\n    console.error('UnifiedCommentSection: Either announcementId or calendarId must be provided');\n    return null;\n  }\n  \n  if (announcementId && calendarId) {\n    console.error('UnifiedCommentSection: Cannot provide both announcementId and calendarId');\n    return null;\n  }\n\n  // Render appropriate comment component based on user role\n  if (effectiveUserType === 'admin') {\n    return (\n      <AdminCommentSection\n        announcementId={announcementId}\n        calendarId={calendarId}\n        allowComments={allowComments}\n        currentUserId={effectiveUserId}\n        currentUserType=\"admin\"\n      />\n    );\n  } else {\n    return (\n      <CommentSection\n        announcementId={announcementId}\n        calendarId={calendarId}\n        allowComments={allowComments}\n        currentUserId={effectiveUserId}\n        currentUserType=\"student\"\n      />\n    );\n  }\n};\n\nexport default UnifiedCommentSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYvD;AACA;AACA;AACA;AACA,MAAMC,qBAA2D,GAAGA,CAAC;EACnEC,cAAc;EACdC,UAAU;EACVC,aAAa,GAAG,IAAI;EACpBC,aAAa;EACbC,eAAe;EACfC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAMC,WAAW,GAAGd,OAAO,CAAC,MAAM;IAChC,IAAIY,SAAS,EAAE;MACb,MAAMG,OAAO,GAAGd,iBAAiB,CAAC,CAAC;MACnC,OAAO;QAAE,GAAGc,OAAO;QAAEC,IAAI,EAAEJ;MAAU,CAAC;IACxC;IACA,OAAOX,iBAAiB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACW,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMK,iBAAiB,GAAGjB,OAAO,CAAC,MAAM;IACtC;IACA,IAAIW,eAAe,EAAE;MACnB,OAAOA,eAAe;IACxB;;IAEA;IACA,IAAIG,WAAW,CAACE,IAAI,EAAE;MACpB,OAAOF,WAAW,CAACE,IAAI;IACzB;;IAEA;IACA,MAAME,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,IAAIH,WAAW,CAACI,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAClC,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIJ,WAAW,CAACI,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC3C,OAAO,SAAS;IAClB;;IAEA;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACX,eAAe,EAAEG,WAAW,CAACE,IAAI,CAAC,CAAC;;EAEvC;EACA,MAAMO,eAAe,GAAGvB,OAAO,CAAC,MAAM;IACpC,IAAIU,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;IAEA,IAAII,WAAW,CAACU,IAAI,EAAE;MACpB;MACA,OAAOV,WAAW,CAACU,IAAI,CAACC,EAAE,IAAIX,WAAW,CAACU,IAAI,CAACE,QAAQ,IAAIZ,WAAW,CAACU,IAAI,CAACG,UAAU;IACxF;IAEA,OAAOC,SAAS;EAClB,CAAC,EAAE,CAAClB,aAAa,EAAEI,WAAW,CAACU,IAAI,CAAC,CAAC;;EAErC;EACA,IAAI,CAACjB,cAAc,IAAI,CAACC,UAAU,EAAE;IAClCqB,OAAO,CAACC,KAAK,CAAC,6EAA6E,CAAC;IAC5F,OAAO,IAAI;EACb;EAEA,IAAIvB,cAAc,IAAIC,UAAU,EAAE;IAChCqB,OAAO,CAACC,KAAK,CAAC,0EAA0E,CAAC;IACzF,OAAO,IAAI;EACb;;EAEA;EACA,IAAIb,iBAAiB,KAAK,OAAO,EAAE;IACjC,oBACEZ,OAAA,CAACH,mBAAmB;MAClBK,cAAc,EAAEA,cAAe;MAC/BC,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BC,aAAa,EAAEa,eAAgB;MAC/BZ,eAAe,EAAC;IAAO;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEN,CAAC,MAAM;IACL,oBACE7B,OAAA,CAACF,cAAc;MACbI,cAAc,EAAEA,cAAe;MAC/BC,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BC,aAAa,EAAEa,eAAgB;MAC/BZ,eAAe,EAAC;IAAS;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN;AACF,CAAC;AAACrB,EAAA,CAxFIP,qBAA2D;AAAA6B,EAAA,GAA3D7B,qBAA2D;AA0FjE,eAAeA,qBAAqB;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}