{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2\",\n  key: \"125lnx\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}], [\"path\", {\n  d: \"M14.5 16h-5\",\n  key: \"1ox875\"\n}]];\nconst TestTube = createLucideIcon(\"test-tube\", __iconNode);\nexport { __iconNode, TestTube as default };\n//# sourceMappingURL=test-tube.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}