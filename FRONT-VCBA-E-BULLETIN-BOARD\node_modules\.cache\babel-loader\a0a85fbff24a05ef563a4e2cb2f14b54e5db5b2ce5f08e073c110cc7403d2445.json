{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\",\n  key: \"hh9hay\"\n}], [\"path\", {\n  d: \"m3.3 7 8.7 5 8.7-5\",\n  key: \"g66t2b\"\n}], [\"path\", {\n  d: \"M12 22V12\",\n  key: \"d0xqtd\"\n}]];\nconst Box = createLucideIcon(\"box\", __iconNode);\nexport { __iconNode, Box as default };\n//# sourceMappingURL=box.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}