{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 12.5V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h7.5\",\n  key: \"w80f2v\"\n}], [\"path\", {\n  d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\",\n  key: \"1ocrg3\"\n}], [\"path\", {\n  d: \"M18 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z\",\n  key: \"8lzu5m\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"path\", {\n  d: \"m22 22-1.5-1.5\",\n  key: \"1x83k4\"\n}]];\nconst MailSearch = createLucideIcon(\"mail-search\", __iconNode);\nexport { __iconNode, MailSearch as default };\n//# sourceMappingURL=mail-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}