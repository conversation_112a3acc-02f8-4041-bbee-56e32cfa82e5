{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1mpf1b\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}], [\"path\", {\n  d: \"M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9\",\n  key: \"1uq4wg\"\n}], [\"path\", {\n  d: \"M12 12v3\",\n  key: \"158kv8\"\n}]];\nconst GitFork = createLucideIcon(\"git-fork\", __iconNode);\nexport { __iconNode, GitFork as default };\n//# sourceMappingURL=git-fork.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}