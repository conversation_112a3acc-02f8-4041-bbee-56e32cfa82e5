{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344\",\n  key: \"2acyp4\"\n}], [\"path\", {\n  d: \"m9 11 3 3L22 4\",\n  key: \"1pflzl\"\n}]];\nconst SquareCheckBig = createLucideIcon(\"square-check-big\", __iconNode);\nexport { __iconNode, SquareCheckBig as default };\n//# sourceMappingURL=square-check-big.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}