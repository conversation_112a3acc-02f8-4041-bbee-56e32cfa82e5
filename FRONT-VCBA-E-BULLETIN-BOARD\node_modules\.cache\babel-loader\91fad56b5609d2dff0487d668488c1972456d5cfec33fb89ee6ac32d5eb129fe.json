{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useMemo}from'react';import{detectUserContext}from'../../utils/authUtils';import AdminCommentSection from'../admin/AdminCommentSection';import CommentSection from'../student/CommentSection';import{jsx as _jsx}from\"react/jsx-runtime\";/**\n * Unified Comment Section that automatically detects user role\n * and renders the appropriate comment component with role-specific features\n */const UnifiedCommentSection=_ref=>{let{announcementId,calendarId,allowComments=true,currentUserId,currentUserType,forceRole}=_ref;// Detect user context\nconst userContext=useMemo(()=>{if(forceRole){const context=detectUserContext();return _objectSpread(_objectSpread({},context),{},{role:forceRole});}return detectUserContext();},[forceRole]);// Determine the effective user type\nconst effectiveUserType=useMemo(()=>{// Priority 1: Explicit prop\nif(currentUserType){return currentUserType;}// Priority 2: Detected context\nif(userContext.role){return userContext.role;}// Priority 3: Path-based detection\nconst currentPath=window.location.pathname;if(currentPath.includes('/admin')){return'admin';}else if(currentPath.includes('/student')){return'student';}// Default fallback\nreturn'student';},[currentUserType,userContext.role]);// Determine the effective user ID\nconst effectiveUserId=useMemo(()=>{if(currentUserId){return currentUserId;}if(userContext.user){// Try to get ID from user context\nreturn userContext.user.id||userContext.user.admin_id||userContext.user.student_id;}return undefined;},[currentUserId,userContext.user]);// Validation\nif(!announcementId&&!calendarId){console.error('UnifiedCommentSection: Either announcementId or calendarId must be provided');return null;}if(announcementId&&calendarId){console.error('UnifiedCommentSection: Cannot provide both announcementId and calendarId');return null;}// Render appropriate comment component based on user role\nif(effectiveUserType==='admin'){return/*#__PURE__*/_jsx(AdminCommentSection,{announcementId:announcementId,calendarId:calendarId,allowComments:allowComments,currentUserId:effectiveUserId,currentUserType:\"admin\"});}else{return/*#__PURE__*/_jsx(CommentSection,{announcementId:announcementId,calendarId:calendarId,allowComments:allowComments,currentUserId:effectiveUserId,currentUserType:\"student\"});}};export default UnifiedCommentSection;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}