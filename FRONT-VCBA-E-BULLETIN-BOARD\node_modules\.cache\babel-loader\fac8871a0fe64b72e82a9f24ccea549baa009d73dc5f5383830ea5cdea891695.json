{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 22H7a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v.5\",\n  key: \"qeb09x\"\n}], [\"path\", {\n  d: \"m16 19 2 2 4-4\",\n  key: \"1b14m6\"\n}], [\"path\", {\n  d: \"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v2\",\n  key: \"1md90i\"\n}], [\"path\", {\n  d: \"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6\",\n  key: \"1itne7\"\n}]];\nconst PrinterCheck = createLucideIcon(\"printer-check\", __iconNode);\nexport { __iconNode, PrinterCheck as default };\n//# sourceMappingURL=printer-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}