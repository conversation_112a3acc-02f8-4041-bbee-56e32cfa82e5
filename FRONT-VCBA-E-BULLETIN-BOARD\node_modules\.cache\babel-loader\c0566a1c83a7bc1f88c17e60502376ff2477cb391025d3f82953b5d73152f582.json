{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"12\",\n  x2: \"18\",\n  y1: \"12\",\n  y2: \"18\",\n  key: \"1rg63v\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"18\",\n  y1: \"18\",\n  y2: \"12\",\n  key: \"ebkxgr\"\n}], [\"rect\", {\n  width: \"14\",\n  height: \"14\",\n  x: \"8\",\n  y: \"8\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"17jyea\"\n}], [\"path\", {\n  d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n  key: \"zix9uf\"\n}]];\nconst CopyX = createLucideIcon(\"copy-x\", __iconNode);\nexport { __iconNode, CopyX as default };\n//# sourceMappingURL=copy-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}