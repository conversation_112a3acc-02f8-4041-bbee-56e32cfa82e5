{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M12 6.5v11\",\n  key: \"ecfhkf\"\n}], [\"path\", {\n  d: \"M15 9.4a4 4 0 1 0 0 5.2\",\n  key: \"1makmb\"\n}]];\nconst ReceiptCent = createLucideIcon(\"receipt-cent\", __iconNode);\nexport { __iconNode, ReceiptCent as default };\n//# sourceMappingURL=receipt-cent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}