{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"path\", {\n  d: \"M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8\",\n  key: \"7n84p3\"\n}]];\nconst AtSign = createLucideIcon(\"at-sign\", __iconNode);\nexport { __iconNode, AtSign as default };\n//# sourceMappingURL=at-sign.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}