{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3\",\n  key: \"ms7g94\"\n}], [\"path\", {\n  d: \"m9 18-1.5-1.5\",\n  key: \"1j6qii\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"14\",\n  r: \"3\",\n  key: \"ufru5t\"\n}]];\nconst FileSearch = createLucideIcon(\"file-search\", __iconNode);\nexport { __iconNode, FileSearch as default };\n//# sourceMappingURL=file-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}