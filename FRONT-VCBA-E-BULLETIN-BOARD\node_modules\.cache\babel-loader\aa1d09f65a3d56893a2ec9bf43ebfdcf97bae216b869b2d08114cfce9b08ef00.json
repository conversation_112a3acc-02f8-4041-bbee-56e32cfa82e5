{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 5h6\",\n  key: \"1vod17\"\n}], [\"path\", {\n  d: \"M19 2v6\",\n  key: \"4bpg5p\"\n}], [\"path\", {\n  d: \"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5\",\n  key: \"1ue2ih\"\n}], [\"path\", {\n  d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n  key: \"1xmnt7\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}]];\nconst ImagePlus = createLucideIcon(\"image-plus\", __iconNode);\nexport { __iconNode, ImagePlus as default };\n//# sourceMappingURL=image-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}