{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 9h4\",\n  key: \"u4k05v\"\n}], [\"path\", {\n  d: \"M12 7v5\",\n  key: \"ma6bk\"\n}], [\"path\", {\n  d: \"M14 22v-4a2 2 0 0 0-4 0v4\",\n  key: \"1pdhuj\"\n}], [\"path\", {\n  d: \"M18 22V5.618a1 1 0 0 0-.553-.894l-4.553-2.277a2 2 0 0 0-1.788 0L6.553 4.724A1 1 0 0 0 6 5.618V22\",\n  key: \"1rkokr\"\n}], [\"path\", {\n  d: \"m18 7 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9.618a1 1 0 0 1 .553-.894L6 7\",\n  key: \"1w6esw\"\n}]];\nconst Church = createLucideIcon(\"church\", __iconNode);\nexport { __iconNode, Church as default };\n//# sourceMappingURL=church.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}