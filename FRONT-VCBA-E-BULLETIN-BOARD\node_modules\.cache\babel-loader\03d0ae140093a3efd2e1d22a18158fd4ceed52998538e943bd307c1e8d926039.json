{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z\",\n  key: \"a7tn18\"\n}]];\nconst Moon = createLucideIcon(\"moon\", __iconNode);\nexport { __iconNode, Moon as default };\n//# sourceMappingURL=moon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}