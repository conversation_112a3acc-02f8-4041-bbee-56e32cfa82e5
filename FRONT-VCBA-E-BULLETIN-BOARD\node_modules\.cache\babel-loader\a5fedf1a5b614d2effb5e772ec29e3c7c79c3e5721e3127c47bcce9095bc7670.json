{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v14\",\n  key: \"jyx4ut\"\n}], [\"path\", {\n  d: \"m19 9-7 7-7-7\",\n  key: \"1oe3oy\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"21\",\n  r: \"1\",\n  key: \"o0uj5v\"\n}]];\nconst ArrowDownToDot = createLucideIcon(\"arrow-down-to-dot\", __iconNode);\nexport { __iconNode, ArrowDownToDot as default };\n//# sourceMappingURL=arrow-down-to-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}