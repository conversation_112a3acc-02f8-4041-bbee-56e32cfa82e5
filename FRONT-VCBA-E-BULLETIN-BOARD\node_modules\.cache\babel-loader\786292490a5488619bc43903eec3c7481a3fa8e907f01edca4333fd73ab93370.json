{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m5 5 14 14\",\n  key: \"11anup\"\n}], [\"path\", {\n  d: \"M13 13a3 3 0 1 0 0-6H9v2\",\n  key: \"uoagbd\"\n}], [\"path\", {\n  d: \"M9 17v-2.34\",\n  key: \"a9qo08\"\n}]];\nconst CircleParkingOff = createLucideIcon(\"circle-parking-off\", __iconNode);\nexport { __iconNode, CircleParkingOff as default };\n//# sourceMappingURL=circle-parking-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}