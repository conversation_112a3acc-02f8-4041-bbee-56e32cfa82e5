{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"2\",\n  y: \"4\",\n  width: \"20\",\n  height: \"16\",\n  rx: \"2\",\n  key: \"izxlao\"\n}], [\"path\", {\n  d: \"M10 4v4\",\n  key: \"pp8u80\"\n}], [\"path\", {\n  d: \"M2 8h20\",\n  key: \"d11cs7\"\n}], [\"path\", {\n  d: \"M6 4v4\",\n  key: \"1svtjw\"\n}]];\nconst AppWindow = createLucideIcon(\"app-window\", __iconNode);\nexport { __iconNode, AppWindow as default };\n//# sourceMappingURL=app-window.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}