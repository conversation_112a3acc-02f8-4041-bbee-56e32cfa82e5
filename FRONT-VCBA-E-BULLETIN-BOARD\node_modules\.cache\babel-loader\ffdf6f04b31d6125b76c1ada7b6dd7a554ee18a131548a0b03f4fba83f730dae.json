{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 21V7\",\n  key: \"gj6g52\"\n}], [\"path\", {\n  d: \"m16 12 2 2 4-4\",\n  key: \"mdajum\"\n}], [\"path\", {\n  d: \"M22 6V4a1 1 0 0 0-1-1h-5a4 4 0 0 0-4 4 4 4 0 0 0-4-4H3a1 1 0 0 0-1 1v13a1 1 0 0 0 1 1h6a3 3 0 0 1 3 3 3 3 0 0 1 3-3h6a1 1 0 0 0 1-1v-1.3\",\n  key: \"8arnkb\"\n}]];\nconst BookOpenCheck = createLucideIcon(\"book-open-check\", __iconNode);\nexport { __iconNode, BookOpenCheck as default };\n//# sourceMappingURL=book-open-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}