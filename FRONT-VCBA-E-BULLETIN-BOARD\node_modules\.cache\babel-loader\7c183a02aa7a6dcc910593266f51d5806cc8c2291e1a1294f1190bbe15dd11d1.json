{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M7 11h8\",\n  key: \"1feolt\"\n}], [\"path\", {\n  d: \"M7 16h12\",\n  key: \"wsnu98\"\n}], [\"path\", {\n  d: \"M7 6h3\",\n  key: \"w9rmul\"\n}]];\nconst ChartBarIncreasing = createLucideIcon(\"chart-bar-increasing\", __iconNode);\nexport { __iconNode, ChartBarIncreasing as default };\n//# sourceMappingURL=chart-bar-increasing.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}