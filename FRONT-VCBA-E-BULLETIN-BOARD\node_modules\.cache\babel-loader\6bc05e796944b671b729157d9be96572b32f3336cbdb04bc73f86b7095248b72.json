{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19\",\n  key: \"1cbfv1\"\n}], [\"path\", {\n  d: \"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z\",\n  key: \"135mg7\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"9.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"5pm5xn\"\n}]];\nconst Tags = createLucideIcon(\"tags\", __iconNode);\nexport { __iconNode, Tags as default };\n//# sourceMappingURL=tags.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}