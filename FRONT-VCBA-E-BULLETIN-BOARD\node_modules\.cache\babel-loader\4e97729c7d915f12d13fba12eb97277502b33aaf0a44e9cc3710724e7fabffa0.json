{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 19h6\",\n  key: \"xwg31i\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M21 15V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8.5\",\n  key: \"1scpom\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}]];\nconst CalendarMinus = createLucideIcon(\"calendar-minus\", __iconNode);\nexport { __iconNode, CalendarMinus as default };\n//# sourceMappingURL=calendar-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}