{"ast": null, "code": "import { AdminAuthService } from './admin-auth.service';\nexport class AdminProfileService {\n  /**\n   * Get admin profile\n   */\n  static async getProfile() {\n    return AdminAuthService.get('/api/admin/profile');\n  }\n\n  /**\n   * Update admin profile\n   */\n  static async updateProfile(data) {\n    return AdminAuthService.put('/api/admin/profile', data);\n  }\n\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return AdminAuthService.post('/api/admin/profile/picture', formData);\n  }\n\n  /**\n   * Update admin profile picture\n   */\n  static async updateProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return AdminAuthService.put('/api/admin/profile/picture', formData);\n  }\n\n  /**\n   * Delete admin profile picture\n   */\n  static async deleteProfilePicture() {\n    return AdminAuthService.delete('/api/admin/profile/picture');\n  }\n}\nexport default AdminProfileService;", "map": {"version": 3, "names": ["AdminAuthService", "AdminProfileService", "getProfile", "get", "updateProfile", "data", "put", "uploadProfilePicture", "file", "formData", "FormData", "append", "post", "updateProfilePicture", "deleteProfilePicture", "delete"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/adminProfileService.ts"], "sourcesContent": ["import { AdminAuthService } from './admin-auth.service';\nimport { ApiResponse } from '../types';\n\nexport interface AdminProfile {\n  admin_id: number;\n  email: string;\n  first_name: string;\n  middle_name?: string;\n  last_name: string;\n  suffix?: string;\n  full_name: string;\n  phone_number?: string;\n  department?: string;\n  position?: string;\n  grade_level?: number;\n  bio?: string;\n  profile_picture?: string;\n  profile_picture_url?: string;\n  is_active: boolean;\n  last_login?: string;\n  account_created_at: string;\n  account_updated_at: string;\n  profile_created_at: string;\n  profile_updated_at: string;\n}\n\nexport interface UpdateAdminProfileData {\n  first_name?: string;\n  middle_name?: string;\n  last_name?: string;\n  suffix?: string;\n  phone_number?: string;\n  department?: string;\n  position?: string;\n  grade_level?: number;\n  bio?: string;\n}\n\nexport class AdminProfileService {\n  /**\n   * Get admin profile\n   */\n  static async getProfile(): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    return AdminAuthService.get<{ admin: AdminProfile }>('/api/admin/profile');\n  }\n\n  /**\n   * Update admin profile\n   */\n  static async updateProfile(data: UpdateAdminProfileData): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    return AdminAuthService.put<{ admin: AdminProfile }>('/api/admin/profile', data);\n  }\n\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    \n    return AdminAuthService.post<{ admin: AdminProfile }>('/api/admin/profile/picture', formData);\n  }\n\n  /**\n   * Update admin profile picture\n   */\n  static async updateProfilePicture(file: File): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    \n    return AdminAuthService.put<{ admin: AdminProfile }>('/api/admin/profile/picture', formData);\n  }\n\n  /**\n   * Delete admin profile picture\n   */\n  static async deleteProfilePicture(): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    return AdminAuthService.delete<{ admin: AdminProfile }>('/api/admin/profile/picture');\n  }\n}\n\nexport default AdminProfileService;\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AAsCvD,OAAO,MAAMC,mBAAmB,CAAC;EAC/B;AACF;AACA;EACE,aAAaC,UAAUA,CAAA,EAAkD;IACvE,OAAOF,gBAAgB,CAACG,GAAG,CAA0B,oBAAoB,CAAC;EAC5E;;EAEA;AACF;AACA;EACE,aAAaC,aAAaA,CAACC,IAA4B,EAAiD;IACtG,OAAOL,gBAAgB,CAACM,GAAG,CAA0B,oBAAoB,EAAED,IAAI,CAAC;EAClF;;EAEA;AACF;AACA;EACE,aAAaE,oBAAoBA,CAACC,IAAU,EAAiD;IAC3F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOR,gBAAgB,CAACY,IAAI,CAA0B,4BAA4B,EAAEH,QAAQ,CAAC;EAC/F;;EAEA;AACF;AACA;EACE,aAAaI,oBAAoBA,CAACL,IAAU,EAAiD;IAC3F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOR,gBAAgB,CAACM,GAAG,CAA0B,4BAA4B,EAAEG,QAAQ,CAAC;EAC9F;;EAEA;AACF;AACA;EACE,aAAaK,oBAAoBA,CAAA,EAAkD;IACjF,OAAOd,gBAAgB,CAACe,MAAM,CAA0B,4BAA4B,CAAC;EACvF;AACF;AAEA,eAAed,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}