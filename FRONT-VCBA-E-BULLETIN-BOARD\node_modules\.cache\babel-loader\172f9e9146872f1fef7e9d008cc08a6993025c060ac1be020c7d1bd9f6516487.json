{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 7h.01\",\n  key: \"1kdx03\"\n}], [\"path\", {\n  d: \"M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20\",\n  key: \"oj1oa8\"\n}], [\"path\", {\n  d: \"m20 7 2 .5-2 .5\",\n  key: \"12nv4d\"\n}], [\"path\", {\n  d: \"M10 18v3\",\n  key: \"1yea0a\"\n}], [\"path\", {\n  d: \"M14 17.75V21\",\n  key: \"1pymcb\"\n}], [\"path\", {\n  d: \"M7 18a6 6 0 0 0 3.84-10.61\",\n  key: \"1npnn0\"\n}]];\nconst Bird = createLucideIcon(\"bird\", __iconNode);\nexport { __iconNode, Bird as default };\n//# sourceMappingURL=bird.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}