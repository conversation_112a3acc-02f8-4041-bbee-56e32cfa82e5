{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 3h.01\",\n  key: \"lbucoy\"\n}], [\"path\", {\n  d: \"M14 2h.01\",\n  key: \"1k8aa1\"\n}], [\"path\", {\n  d: \"m2 9 20-5\",\n  key: \"1kz0j5\"\n}], [\"path\", {\n  d: \"M12 12V6.5\",\n  key: \"1vbrij\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"10\",\n  x: \"4\",\n  y: \"12\",\n  rx: \"3\",\n  key: \"if91er\"\n}], [\"path\", {\n  d: \"M9 12v5\",\n  key: \"3anwtq\"\n}], [\"path\", {\n  d: \"M15 12v5\",\n  key: \"5xh3zn\"\n}], [\"path\", {\n  d: \"M4 17h16\",\n  key: \"g4d7ey\"\n}]];\nconst CableCar = createLucideIcon(\"cable-car\", __iconNode);\nexport { __iconNode, CableCar as default };\n//# sourceMappingURL=cable-car.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}