{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 7 5 5-5 5V7\",\n  key: \"couhi7\"\n}], [\"path\", {\n  d: \"m21 7-5 5 5 5V7\",\n  key: \"6ouia7\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"M12 14v2\",\n  key: \"8jcxud\"\n}], [\"path\", {\n  d: \"M12 8v2\",\n  key: \"1woqiv\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}]];\nconst FlipHorizontal2 = createLucideIcon(\"flip-horizontal-2\", __iconNode);\nexport { __iconNode, FlipHorizontal2 as default };\n//# sourceMappingURL=flip-horizontal-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}