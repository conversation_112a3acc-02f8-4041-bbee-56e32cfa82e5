{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"gxeob9\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"w8mnmm\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"lttvr7\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"41hilf\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1wjl8i\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1pcz8c\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"lyex9k\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"shf9b7\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"bfqh0e\"\n}]];\nconst Grip = createLucideIcon(\"grip\", __iconNode);\nexport { __iconNode, Grip as default };\n//# sourceMappingURL=grip.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}