{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M6 8h4\",\n  key: \"utf9t1\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}], [\"path\", {\n  d: \"M18 8h.01\",\n  key: \"emo2bl\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"M6 12v4\",\n  key: \"dy92yo\"\n}], [\"path\", {\n  d: \"M10 12v4\",\n  key: \"1fxnav\"\n}], [\"path\", {\n  d: \"M14 12v4\",\n  key: \"1hft58\"\n}], [\"path\", {\n  d: \"M18 12v4\",\n  key: \"tjjnbz\"\n}]];\nconst KeyboardMusic = createLucideIcon(\"keyboard-music\", __iconNode);\nexport { __iconNode, KeyboardMusic as default };\n//# sourceMappingURL=keyboard-music.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}