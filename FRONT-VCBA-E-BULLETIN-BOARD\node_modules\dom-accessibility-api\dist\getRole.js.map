{"version": 3, "file": "getRole.js", "names": ["getLocalName", "element", "localName", "tagName", "toLowerCase", "localNameToRoleMappings", "article", "aside", "button", "datalist", "dd", "details", "dialog", "dt", "fieldset", "figure", "form", "footer", "h1", "h2", "h3", "h4", "h5", "h6", "header", "hr", "html", "legend", "li", "math", "main", "menu", "nav", "ol", "optgroup", "option", "output", "progress", "section", "summary", "table", "tbody", "textarea", "tfoot", "td", "th", "thead", "tr", "ul", "prohibitedAttributes", "caption", "Set", "code", "deletion", "emphasis", "generic", "insertion", "paragraph", "presentation", "strong", "subscript", "superscript", "hasGlobalAriaAttributes", "role", "some", "attributeName", "hasAttribute", "has", "ignorePresentationalRole", "implicitRole", "getRole", "explicitRole", "getExplicitRole", "getImplicitRole", "mappedByTag", "undefined", "getAttribute", "type", "size", "trim", "split", "length"], "sources": ["../sources/getRole.ts"], "sourcesContent": ["// https://w3c.github.io/html-aria/#document-conformance-requirements-for-use-of-aria-attributes-in-html\n\n/**\n * Safe Element.localName for all supported environments\n * @param element\n */\nexport function getLocalName(element: Element): string {\n\treturn (\n\t\t// eslint-disable-next-line no-restricted-properties -- actual guard for environments without localName\n\t\telement.localName ??\n\t\t// eslint-disable-next-line no-restricted-properties -- required for the fallback\n\t\telement.tagName.toLowerCase()\n\t);\n}\n\nconst localNameToRoleMappings: Record<string, string | undefined> = {\n\tarticle: \"article\",\n\taside: \"complementary\",\n\tbutton: \"button\",\n\tdatalist: \"listbox\",\n\tdd: \"definition\",\n\tdetails: \"group\",\n\tdialog: \"dialog\",\n\tdt: \"term\",\n\tfieldset: \"group\",\n\tfigure: \"figure\",\n\t// WARNING: Only with an accessible name\n\tform: \"form\",\n\tfooter: \"contentinfo\",\n\th1: \"heading\",\n\th2: \"heading\",\n\th3: \"heading\",\n\th4: \"heading\",\n\th5: \"heading\",\n\th6: \"heading\",\n\theader: \"banner\",\n\thr: \"separator\",\n\thtml: \"document\",\n\tlegend: \"legend\",\n\tli: \"listitem\",\n\tmath: \"math\",\n\tmain: \"main\",\n\tmenu: \"list\",\n\tnav: \"navigation\",\n\tol: \"list\",\n\toptgroup: \"group\",\n\t// WARNING: Only in certain context\n\toption: \"option\",\n\toutput: \"status\",\n\tprogress: \"progressbar\",\n\t// WARNING: Only with an accessible name\n\tsection: \"region\",\n\tsummary: \"button\",\n\ttable: \"table\",\n\ttbody: \"rowgroup\",\n\ttextarea: \"textbox\",\n\ttfoot: \"rowgroup\",\n\t// WARNING: Only in certain context\n\ttd: \"cell\",\n\tth: \"columnheader\",\n\tthead: \"rowgroup\",\n\ttr: \"row\",\n\tul: \"list\",\n};\n\nconst prohibitedAttributes: Record<string, Set<string>> = {\n\tcaption: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tcode: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tdeletion: new Set([\"aria-label\", \"aria-labelledby\"]),\n\temphasis: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tgeneric: new Set([\"aria-label\", \"aria-labelledby\", \"aria-roledescription\"]),\n\tinsertion: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tparagraph: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tpresentation: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tstrong: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tsubscript: new Set([\"aria-label\", \"aria-labelledby\"]),\n\tsuperscript: new Set([\"aria-label\", \"aria-labelledby\"]),\n};\n\n/**\n *\n * @param element\n * @param role The role used for this element. This is specified to control whether you want to use the implicit or explicit role.\n */\nfunction hasGlobalAriaAttributes(element: Element, role: string): boolean {\n\t// https://rawgit.com/w3c/aria/stable/#global_states\n\t// commented attributes are deprecated\n\treturn [\n\t\t\"aria-atomic\",\n\t\t\"aria-busy\",\n\t\t\"aria-controls\",\n\t\t\"aria-current\",\n\t\t\"aria-describedby\",\n\t\t\"aria-details\",\n\t\t// \"disabled\",\n\t\t\"aria-dropeffect\",\n\t\t// \"errormessage\",\n\t\t\"aria-flowto\",\n\t\t\"aria-grabbed\",\n\t\t// \"haspopup\",\n\t\t\"aria-hidden\",\n\t\t// \"invalid\",\n\t\t\"aria-keyshortcuts\",\n\t\t\"aria-label\",\n\t\t\"aria-labelledby\",\n\t\t\"aria-live\",\n\t\t\"aria-owns\",\n\t\t\"aria-relevant\",\n\t\t\"aria-roledescription\",\n\t].some((attributeName) => {\n\t\treturn (\n\t\t\telement.hasAttribute(attributeName) &&\n\t\t\t!prohibitedAttributes[role]?.has(attributeName)\n\t\t);\n\t});\n}\n\nfunction ignorePresentationalRole(\n\telement: Element,\n\timplicitRole: string\n): boolean {\n\t// https://rawgit.com/w3c/aria/stable/#conflict_resolution_presentation_none\n\treturn hasGlobalAriaAttributes(element, implicitRole);\n}\n\nexport default function getRole(element: Element): string | null {\n\tconst explicitRole = getExplicitRole(element);\n\tif (explicitRole === null || explicitRole === \"presentation\") {\n\t\tconst implicitRole = getImplicitRole(element);\n\t\tif (\n\t\t\texplicitRole !== \"presentation\" ||\n\t\t\tignorePresentationalRole(element, implicitRole || \"\")\n\t\t) {\n\t\t\treturn implicitRole;\n\t\t}\n\t}\n\n\treturn explicitRole;\n}\n\nfunction getImplicitRole(element: Element): string | null {\n\tconst mappedByTag = localNameToRoleMappings[getLocalName(element)];\n\tif (mappedByTag !== undefined) {\n\t\treturn mappedByTag;\n\t}\n\n\tswitch (getLocalName(element)) {\n\t\tcase \"a\":\n\t\tcase \"area\":\n\t\tcase \"link\":\n\t\t\tif (element.hasAttribute(\"href\")) {\n\t\t\t\treturn \"link\";\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"img\":\n\t\t\tif (\n\t\t\t\telement.getAttribute(\"alt\") === \"\" &&\n\t\t\t\t!ignorePresentationalRole(element, \"img\")\n\t\t\t) {\n\t\t\t\treturn \"presentation\";\n\t\t\t}\n\t\t\treturn \"img\";\n\t\tcase \"input\": {\n\t\t\tconst { type } = element as HTMLInputElement;\n\t\t\tswitch (type) {\n\t\t\t\tcase \"button\":\n\t\t\t\tcase \"image\":\n\t\t\t\tcase \"reset\":\n\t\t\t\tcase \"submit\":\n\t\t\t\t\treturn \"button\";\n\t\t\t\tcase \"checkbox\":\n\t\t\t\tcase \"radio\":\n\t\t\t\t\treturn type;\n\t\t\t\tcase \"range\":\n\t\t\t\t\treturn \"slider\";\n\t\t\t\tcase \"email\":\n\t\t\t\tcase \"tel\":\n\t\t\t\tcase \"text\":\n\t\t\t\tcase \"url\":\n\t\t\t\t\tif (element.hasAttribute(\"list\")) {\n\t\t\t\t\t\treturn \"combobox\";\n\t\t\t\t\t}\n\t\t\t\t\treturn \"textbox\";\n\n\t\t\t\tcase \"search\":\n\t\t\t\t\tif (element.hasAttribute(\"list\")) {\n\t\t\t\t\t\treturn \"combobox\";\n\t\t\t\t\t}\n\t\t\t\t\treturn \"searchbox\";\n\t\t\t\tcase \"number\":\n\t\t\t\t\treturn \"spinbutton\";\n\t\t\t\tdefault:\n\t\t\t\t\treturn null;\n\t\t\t}\n\t\t}\n\t\tcase \"select\":\n\t\t\tif (\n\t\t\t\telement.hasAttribute(\"multiple\") ||\n\t\t\t\t(element as HTMLSelectElement).size > 1\n\t\t\t) {\n\t\t\t\treturn \"listbox\";\n\t\t\t}\n\t\t\treturn \"combobox\";\n\t}\n\treturn null;\n}\n\nfunction getExplicitRole(element: Element): string | null {\n\tconst role = element.getAttribute(\"role\");\n\tif (role !== null) {\n\t\tconst explicitRole = role.trim().split(\" \")[0];\n\t\t// String.prototype.split(sep, limit) will always return an array with at least one member\n\t\t// as long as limit is either undefined or > 0\n\t\tif (explicitRole.length > 0) {\n\t\t\treturn explicitRole;\n\t\t}\n\t}\n\n\treturn null;\n}\n"], "mappings": ";;;;;AAAA;;AAEA;AACA;AACA;AACA;AACO,SAASA,YAAY,CAACC,OAAgB,EAAU;EAAA;EACtD,OACC;IAAA,sBACAA,OAAO,CAACC,SAAS;IACjB;IACAD,OAAO,CAACE,OAAO,CAACC,WAAW;EAAE;AAE/B;AAEA,IAAMC,uBAA2D,GAAG;EACnEC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,eAAe;EACtBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,SAAS;EACnBC,EAAE,EAAE,YAAY;EAChBC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,QAAQ;EAChB;EACAC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,aAAa;EACrBC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,WAAW;EACfC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,UAAU;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,YAAY;EACjBC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,OAAO;EACjB;EACAC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,aAAa;EACvB;EACAC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,UAAU;EACjB;EACAC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,cAAc;EAClBC,KAAK,EAAE,UAAU;EACjBC,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE;AACL,CAAC;AAED,IAAMC,oBAAiD,GAAG;EACzDC,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACnDC,IAAI,EAAE,IAAID,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EAChDE,QAAQ,EAAE,IAAIF,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACpDG,QAAQ,EAAE,IAAIH,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACpDI,OAAO,EAAE,IAAIJ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;EAC3EK,SAAS,EAAE,IAAIL,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDM,SAAS,EAAE,IAAIN,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDO,YAAY,EAAE,IAAIP,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACxDQ,MAAM,EAAE,IAAIR,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EAClDS,SAAS,EAAE,IAAIT,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;EACrDU,WAAW,EAAE,IAAIV,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,CAAC;AACvD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASW,uBAAuB,CAAC7D,OAAgB,EAAE8D,IAAY,EAAW;EACzE;EACA;EACA,OAAO,CACN,aAAa,EACb,WAAW,EACX,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,cAAc;EACd;EACA,iBAAiB;EACjB;EACA,aAAa,EACb,cAAc;EACd;EACA,aAAa;EACb;EACA,mBAAmB,EACnB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,eAAe,EACf,sBAAsB,CACtB,CAACC,IAAI,CAAC,UAACC,aAAa,EAAK;IAAA;IACzB,OACChE,OAAO,CAACiE,YAAY,CAACD,aAAa,CAAC,IACnC,2BAAChB,oBAAoB,CAACc,IAAI,CAAC,kDAA1B,sBAA4BI,GAAG,CAACF,aAAa,CAAC;EAEjD,CAAC,CAAC;AACH;AAEA,SAASG,wBAAwB,CAChCnE,OAAgB,EAChBoE,YAAoB,EACV;EACV;EACA,OAAOP,uBAAuB,CAAC7D,OAAO,EAAEoE,YAAY,CAAC;AACtD;AAEe,SAASC,OAAO,CAACrE,OAAgB,EAAiB;EAChE,IAAMsE,YAAY,GAAGC,eAAe,CAACvE,OAAO,CAAC;EAC7C,IAAIsE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,cAAc,EAAE;IAC7D,IAAMF,YAAY,GAAGI,eAAe,CAACxE,OAAO,CAAC;IAC7C,IACCsE,YAAY,KAAK,cAAc,IAC/BH,wBAAwB,CAACnE,OAAO,EAAEoE,YAAY,IAAI,EAAE,CAAC,EACpD;MACD,OAAOA,YAAY;IACpB;EACD;EAEA,OAAOE,YAAY;AACpB;AAEA,SAASE,eAAe,CAACxE,OAAgB,EAAiB;EACzD,IAAMyE,WAAW,GAAGrE,uBAAuB,CAACL,YAAY,CAACC,OAAO,CAAC,CAAC;EAClE,IAAIyE,WAAW,KAAKC,SAAS,EAAE;IAC9B,OAAOD,WAAW;EACnB;EAEA,QAAQ1E,YAAY,CAACC,OAAO,CAAC;IAC5B,KAAK,GAAG;IACR,KAAK,MAAM;IACX,KAAK,MAAM;MACV,IAAIA,OAAO,CAACiE,YAAY,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,MAAM;MACd;MACA;IACD,KAAK,KAAK;MACT,IACCjE,OAAO,CAAC2E,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,IAClC,CAACR,wBAAwB,CAACnE,OAAO,EAAE,KAAK,CAAC,EACxC;QACD,OAAO,cAAc;MACtB;MACA,OAAO,KAAK;IACb,KAAK,OAAO;MAAE;QACb,WAAiBA,OAAO;UAAhB4E,IAAI,QAAJA,IAAI;QACZ,QAAQA,IAAI;UACX,KAAK,QAAQ;UACb,KAAK,OAAO;UACZ,KAAK,OAAO;UACZ,KAAK,QAAQ;YACZ,OAAO,QAAQ;UAChB,KAAK,UAAU;UACf,KAAK,OAAO;YACX,OAAOA,IAAI;UACZ,KAAK,OAAO;YACX,OAAO,QAAQ;UAChB,KAAK,OAAO;UACZ,KAAK,KAAK;UACV,KAAK,MAAM;UACX,KAAK,KAAK;YACT,IAAI5E,OAAO,CAACiE,YAAY,CAAC,MAAM,CAAC,EAAE;cACjC,OAAO,UAAU;YAClB;YACA,OAAO,SAAS;UAEjB,KAAK,QAAQ;YACZ,IAAIjE,OAAO,CAACiE,YAAY,CAAC,MAAM,CAAC,EAAE;cACjC,OAAO,UAAU;YAClB;YACA,OAAO,WAAW;UACnB,KAAK,QAAQ;YACZ,OAAO,YAAY;UACpB;YACC,OAAO,IAAI;QAAC;MAEf;IACA,KAAK,QAAQ;MACZ,IACCjE,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC,IAC/BjE,OAAO,CAAuB6E,IAAI,GAAG,CAAC,EACtC;QACD,OAAO,SAAS;MACjB;MACA,OAAO,UAAU;EAAC;EAEpB,OAAO,IAAI;AACZ;AAEA,SAASN,eAAe,CAACvE,OAAgB,EAAiB;EACzD,IAAM8D,IAAI,GAAG9D,OAAO,CAAC2E,YAAY,CAAC,MAAM,CAAC;EACzC,IAAIb,IAAI,KAAK,IAAI,EAAE;IAClB,IAAMQ,YAAY,GAAGR,IAAI,CAACgB,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9C;IACA;IACA,IAAIT,YAAY,CAACU,MAAM,GAAG,CAAC,EAAE;MAC5B,OAAOV,YAAY;IACpB;EACD;EAEA,OAAO,IAAI;AACZ"}