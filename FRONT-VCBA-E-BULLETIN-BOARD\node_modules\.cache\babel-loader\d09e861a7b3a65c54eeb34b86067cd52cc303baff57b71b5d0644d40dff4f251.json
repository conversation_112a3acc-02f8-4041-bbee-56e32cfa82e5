{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M9 17V7h4a3 3 0 0 1 0 6H9\",\n  key: \"1dfk2c\"\n}]];\nconst CircleParking = createLucideIcon(\"circle-parking\", __iconNode);\nexport { __iconNode, CircleParking as default };\n//# sourceMappingURL=circle-parking.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}