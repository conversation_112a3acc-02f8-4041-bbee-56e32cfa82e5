{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 6h10\",\n  key: \"9lnwnk\"\n}], [\"path\", {\n  d: \"M6 12h9\",\n  key: \"1g9pqf\"\n}], [\"path\", {\n  d: \"M11 18h7\",\n  key: \"c8dzvl\"\n}]];\nconst ChartNoAxesGantt = createLucideIcon(\"chart-no-axes-gantt\", __iconNode);\nexport { __iconNode, ChartNoAxesGantt as default };\n//# sourceMappingURL=chart-no-axes-gantt.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}