{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"3\",\n  width: \"20\",\n  height: \"14\",\n  rx: \"2\",\n  key: \"x3v2xh\"\n}], [\"rect\", {\n  x: \"9\",\n  y: \"7\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"5m2oou\"\n}]];\nconst MonitorStop = createLucideIcon(\"monitor-stop\", __iconNode);\nexport { __iconNode, MonitorStop as default };\n//# sourceMappingURL=monitor-stop.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}