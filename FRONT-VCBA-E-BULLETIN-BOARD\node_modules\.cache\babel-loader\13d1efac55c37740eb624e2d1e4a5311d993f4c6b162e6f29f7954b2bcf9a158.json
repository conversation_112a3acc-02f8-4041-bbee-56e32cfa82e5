{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17h1.5\",\n  key: \"1gkc67\"\n}], [\"path\", {\n  d: \"M12 22h1.5\",\n  key: \"1my7sn\"\n}], [\"path\", {\n  d: \"M12 2h1.5\",\n  key: \"19tvb7\"\n}], [\"path\", {\n  d: \"M17.5 22H19a1 1 0 0 0 1-1\",\n  key: \"10akbh\"\n}], [\"path\", {\n  d: \"M17.5 2H19a1 1 0 0 1 1 1v1.5\",\n  key: \"1vrfjs\"\n}], [\"path\", {\n  d: \"M20 14v3h-2.5\",\n  key: \"1naeju\"\n}], [\"path\", {\n  d: \"M20 8.5V10\",\n  key: \"1ctpfu\"\n}], [\"path\", {\n  d: \"M4 10V8.5\",\n  key: \"1o3zg5\"\n}], [\"path\", {\n  d: \"M4 19.5V14\",\n  key: \"ob81pf\"\n}], [\"path\", {\n  d: \"M4 4.5A2.5 2.5 0 0 1 6.5 2H8\",\n  key: \"s8vcyb\"\n}], [\"path\", {\n  d: \"M8 22H6.5a1 1 0 0 1 0-5H8\",\n  key: \"1cu73q\"\n}]];\nconst BookDashed = createLucideIcon(\"book-dashed\", __iconNode);\nexport { __iconNode, BookDashed as default };\n//# sourceMappingURL=book-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}