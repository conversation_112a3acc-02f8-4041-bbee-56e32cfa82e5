{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 8L18.74 5.74A9.75 9.75 0 0 0 12 3C11 3 10.03 3.16 9.13 3.47\",\n  key: \"1krf6h\"\n}], [\"path\", {\n  d: \"M8 16H3v5\",\n  key: \"1cv678\"\n}], [\"path\", {\n  d: \"M3 12C3 9.51 4 7.26 5.64 5.64\",\n  key: \"ruvoct\"\n}], [\"path\", {\n  d: \"m3 16 2.26 2.26A9.75 9.75 0 0 0 12 21c2.49 0 4.74-1 6.36-2.64\",\n  key: \"19q130\"\n}], [\"path\", {\n  d: \"M21 12c0 1-.16 1.97-.47 2.87\",\n  key: \"4w8emr\"\n}], [\"path\", {\n  d: \"M21 3v5h-5\",\n  key: \"1q7to0\"\n}], [\"path\", {\n  d: \"M22 22 2 2\",\n  key: \"1r8tn9\"\n}]];\nconst RefreshCwOff = createLucideIcon(\"refresh-cw-off\", __iconNode);\nexport { __iconNode, RefreshCwOff as default };\n//# sourceMappingURL=refresh-cw-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}