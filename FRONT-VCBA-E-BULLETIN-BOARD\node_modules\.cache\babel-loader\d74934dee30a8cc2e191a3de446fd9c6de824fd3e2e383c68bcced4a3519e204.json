{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m20.9 18.55-8-15.98a1 1 0 0 0-1.8 0l-8 15.98\",\n  key: \"53pte7\"\n}], [\"ellipse\", {\n  cx: \"12\",\n  cy: \"19\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"1ji25f\"\n}]];\nconst Cone = createLucideIcon(\"cone\", __iconNode);\nexport { __iconNode, Cone as default };\n//# sourceMappingURL=cone.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}