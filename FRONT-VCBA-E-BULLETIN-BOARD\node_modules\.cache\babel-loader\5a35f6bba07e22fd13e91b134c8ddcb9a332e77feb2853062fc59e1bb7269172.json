{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 8.5a6.5 6.5 0 1 1 13 0c0 6-6 6-6 10a3.5 3.5 0 1 1-7 0\",\n  key: \"1dfaln\"\n}], [\"path\", {\n  d: \"M15 8.5a2.5 2.5 0 0 0-5 0v1a2 2 0 1 1 0 4\",\n  key: \"1qnva7\"\n}]];\nconst Ear = createLucideIcon(\"ear\", __iconNode);\nexport { __iconNode, Ear as default };\n//# sourceMappingURL=ear.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}