{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\AdminCommentSection.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport { Heart, MessageCircle, Shield, Trash2, Flag, AlertCircle, ArrowRight } from 'lucide-react';\nimport ProfileAvatar from '../common/ProfileAvatar';\nimport { shouldShowReplyButton, calculateIndentation, getDepthLimitMessage, getCommentDepthClasses, COMMENT_DEPTH_CONFIG } from '../../utils/commentDepth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminCommentItem = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  onDelete,\n  onFlag,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  _s();\n  var _comment$author_name, _comment$author_name2;\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showActions, setShowActions] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      onDelete(comment.comment_id);\n    }\n  };\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n  const handleFlag = () => {\n    const reason = window.prompt('Please provide a reason for flagging this comment:');\n    if (reason && reason.trim()) {\n      onFlag(comment.comment_id, reason.trim());\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: `comment-${comment.comment_id}`,\n    className: depthClasses.join(' '),\n    style: {\n      marginLeft: `${indentation}px`,\n      marginBottom: '1rem',\n      padding: '1rem',\n      backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb',\n      position: 'relative'\n    },\n    onMouseEnter: () => setShowActions(true),\n    onMouseLeave: () => setShowActions(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'start',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ProfileAvatar, {\n          profilePicture: comment.profile_picture,\n          firstName: (_comment$author_name = comment.author_name) === null || _comment$author_name === void 0 ? void 0 : _comment$author_name.split(' ')[0],\n          lastName: (_comment$author_name2 = comment.author_name) === null || _comment$author_name2 === void 0 ? void 0 : _comment$author_name2.split(' ')[1],\n          size: \"large\",\n          gradientColors: {\n            from: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n            to: comment.user_type === 'admin' ? '#1e40af' : '#16a34a'\n          },\n          style: {\n            flexShrink: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '-2px',\n            right: '-2px',\n            width: '1rem',\n            height: '1rem',\n            backgroundColor: '#facc15',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Shield, {\n            size: 8,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            },\n            children: comment.author_name || 'Anonymous'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              fontSize: '0.75rem',\n              fontWeight: '500',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '0.375rem',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), \"Admin\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), comment.is_flagged && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#ef4444',\n              color: 'white',\n              fontSize: '0.75rem',\n              fontWeight: '500',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '0.375rem',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Flag, {\n              size: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), \"Flagged\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            },\n            children: formatCommentDate(comment.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            marginBottom: '0.75rem',\n            wordBreak: 'break-word'\n          },\n          children: comment.comment_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReactionToggle,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              color: hasUserReacted ? '#ef4444' : '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '0.375rem',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#f3f4f6';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n            },\n            children: [/*#__PURE__*/_jsxDEV(Heart, {\n              size: 14,\n              fill: hasUserReacted ? '#ef4444' : 'none'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: comment.reaction_count || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), canReply ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReplyClick,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '0.375rem',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#f3f4f6';\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              size: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), \"Reply\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Show depth limit message for max depth comments\n          _jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.75rem',\n              color: '#f59e0b',\n              fontStyle: 'italic'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reply depth limit reached\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                // Scroll to top-level comment for continuing thread\n                const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                if (rootElement) {\n                  rootElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center'\n                  });\n                }\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: '#3b82f6',\n                fontSize: '0.75rem',\n                textDecoration: 'underline',\n                padding: '0'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [\"Continue thread\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), currentUserType === 'admin' && showActions && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFlag,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: '#f59e0b',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.backgroundColor = '#fef3c7';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Flag, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), \"Flag\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDelete,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: '#ef4444',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.backgroundColor = '#fef2f2';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), showReplyForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.75rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(AdminCommentForm, {\n            announcementId: comment.announcement_id,\n            parentCommentId: comment.comment_id,\n            onSubmit: () => setShowReplyForm(false),\n            onCancel: () => setShowReplyForm(false),\n            placeholder: \"Write a reply...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), showDepthWarning && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.5rem',\n            backgroundColor: '#fef3c7',\n            border: '1px solid #f59e0b',\n            borderRadius: '4px',\n            fontSize: '0.75rem',\n            color: '#92400e',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: depthLimitMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), comment.replies && comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem'\n          },\n          children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(AdminCommentItem, {\n            comment: reply,\n            onReply: onReply,\n            onLike: onLike,\n            onUnlike: onUnlike,\n            onDelete: onDelete,\n            onFlag: onFlag,\n            currentUserId: currentUserId,\n            currentUserType: currentUserType,\n            depth: depth + 1\n          }, reply.comment_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n\n// Admin Comment Form Component will be added in the next chunk\n_s(AdminCommentItem, \"OYfzjSRSx7720jmObxR/xvaWehw=\");\n_c = AdminCommentItem;\nconst AdminCommentForm = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  _s2();\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    createComment\n  } = useComments(announcementId, 'admin'); // Admin service\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!commentText.trim() || isSubmitting) return;\n    try {\n      setIsSubmitting(true);\n      const commentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        parent_comment_id: parentCommentId,\n        is_anonymous: isAnonymous\n      };\n      await createComment(commentData);\n      setCommentText('');\n      onSubmit();\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      padding: '1rem',\n      backgroundColor: '#f8fafc',\n      borderRadius: '8px',\n      border: '1px solid #e2e8f0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: commentText,\n        onChange: e => setCommentText(e.target.value),\n        placeholder: placeholder,\n        rows: 3,\n        style: {\n          width: '100%',\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          outline: 'none',\n          resize: 'vertical',\n          fontFamily: 'inherit'\n        },\n        onFocus: e => {\n          e.currentTarget.style.borderColor = '#3b82f6';\n          e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n        },\n        onBlur: e => {\n          e.currentTarget.style.borderColor = '#d1d5db';\n          e.currentTarget.style.boxShadow = 'none';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '0.875rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: isAnonymous,\n          onChange: e => setIsAnonymous(e.target.checked),\n          style: {\n            width: '1rem',\n            height: '1rem',\n            accentColor: '#3b82f6'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), \"Post as Anonymous Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '0.5rem'\n        },\n        children: [onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          style: {\n            padding: '0.5rem 1rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '6px',\n            backgroundColor: 'white',\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.backgroundColor = '#f9fafb';\n            e.currentTarget.style.borderColor = '#9ca3af';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.backgroundColor = 'white';\n            e.currentTarget.style.borderColor = '#d1d5db';\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !commentText.trim() || isSubmitting,\n          style: {\n            padding: '0.5rem 1rem',\n            border: 'none',\n            borderRadius: '6px',\n            backgroundColor: !commentText.trim() || isSubmitting ? '#9ca3af' : '#3b82f6',\n            color: 'white',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => {\n            if (!isSubmitting && commentText.trim()) {\n              e.currentTarget.style.backgroundColor = '#2563eb';\n            }\n          },\n          onMouseLeave: e => {\n            if (!isSubmitting && commentText.trim()) {\n              e.currentTarget.style.backgroundColor = '#3b82f6';\n            }\n          },\n          children: isSubmitting ? 'Posting...' : 'Post Comment'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 467,\n    columnNumber: 5\n  }, this);\n};\n\n// Main AdminCommentSection Component\n_s2(AdminCommentForm, \"BbE2Nc5wJlPq9At3ZSo/OwA4M08=\", false, function () {\n  return [useComments];\n});\n_c2 = AdminCommentForm;\nconst AdminCommentSection = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'admin'\n}) => {\n  _s3();\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment,\n    deleteComment,\n    flagComment\n  } = useComments(announcementId, 'admin'); // Explicitly use admin service\n\n  const handleReply = parentId => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n  const handleDelete = async commentId => {\n    try {\n      await deleteComment(commentId);\n      await refresh();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n    }\n  };\n  const handleFlag = async (commentId, reason) => {\n    try {\n      await flagComment(commentId, reason);\n      await refresh();\n    } catch (error) {\n      console.error('Error flagging comment:', error);\n    }\n  };\n  if (!allowComments) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic',\n        backgroundColor: '#f9fafb',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n        size: 20,\n        style: {\n          marginBottom: '0.5rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Comments are disabled for this announcement.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Shield, {\n        size: 20,\n        color: \"#3b82f6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151',\n          margin: 0\n        },\n        children: [\"Admin Comments (\", comments.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '0.75rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '6px',\n        marginBottom: '1rem',\n        fontSize: '0.875rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Flag, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminCommentForm, {\n        announcementId: announcementId,\n        onSubmit: refresh,\n        placeholder: \"Share your admin insights...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '1.5rem',\n          height: '1.5rem',\n          border: '2px solid #e5e7eb',\n          borderTop: '2px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 9\n    }, this) : comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        backgroundColor: '#f8fafc',\n        borderRadius: '8px',\n        border: '1px solid #e2e8f0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n        size: 24,\n        style: {\n          marginBottom: '0.5rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No comments yet. Be the first admin to share insights!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: comments.map(comment => /*#__PURE__*/_jsxDEV(AdminCommentItem, {\n        comment: comment,\n        onReply: handleReply,\n        onLike: likeComment,\n        onUnlike: unlikeComment,\n        onDelete: handleDelete,\n        onFlag: handleFlag,\n        currentUserId: currentUserId,\n        currentUserType: currentUserType\n      }, comment.comment_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 724,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 649,\n    columnNumber: 5\n  }, this);\n};\n_s3(AdminCommentSection, \"QXmdCaMyDgdlX99Xi+oikqU+5xQ=\", false, function () {\n  return [useComments];\n});\n_c3 = AdminCommentSection;\nexport default AdminCommentSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AdminCommentItem\");\n$RefreshReg$(_c2, \"AdminCommentForm\");\n$RefreshReg$(_c3, \"AdminCommentSection\");", "map": {"version": 3, "names": ["React", "useState", "useComments", "formatCommentDate", "Heart", "MessageCircle", "Shield", "Trash2", "Flag", "AlertCircle", "ArrowRight", "ProfileAvatar", "shouldShowReplyButton", "calculateIndentation", "getDepthLimitMessage", "getCommentDepthClasses", "COMMENT_DEPTH_CONFIG", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminCommentItem", "comment", "onReply", "onLike", "onUnlike", "onDelete", "onFlag", "currentUserId", "currentUserType", "depth", "_s", "_comment$author_name", "_comment$author_name2", "showReplyForm", "setShowReplyForm", "showActions", "setShowActions", "showDepthWarning", "setShowDepthWarning", "hasUserReacted", "user_reaction", "undefined", "canReply", "indentation", "depthClasses", "depthLimitMessage", "isAtMaxDepth", "MAX_DEPTH", "handleReactionToggle", "comment_id", "handleDelete", "window", "confirm", "handleReplyClick", "setTimeout", "handleFlag", "reason", "prompt", "trim", "id", "className", "join", "style", "marginLeft", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "position", "onMouseEnter", "onMouseLeave", "children", "display", "alignItems", "gap", "profilePicture", "profile_picture", "firstName", "author_name", "split", "lastName", "size", "gradientColors", "from", "user_type", "to", "flexShrink", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bottom", "right", "width", "height", "justifyContent", "color", "flex", "min<PERSON><PERSON><PERSON>", "flexWrap", "fontWeight", "fontSize", "is_flagged", "created_at", "lineHeight", "wordBreak", "comment_text", "onClick", "background", "cursor", "transition", "e", "currentTarget", "fill", "reaction_count", "fontStyle", "rootElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "textDecoration", "marginTop", "AdminCommentForm", "announcementId", "announcement_id", "parentCommentId", "onSubmit", "onCancel", "placeholder", "replies", "length", "map", "reply", "_c", "_s2", "commentText", "setCommentText", "isAnonymous", "setIsAnonymous", "isSubmitting", "setIsSubmitting", "createComment", "handleSubmit", "preventDefault", "commentData", "parent_comment_id", "is_anonymous", "error", "console", "value", "onChange", "target", "rows", "outline", "resize", "fontFamily", "onFocus", "borderColor", "boxShadow", "onBlur", "type", "checked", "accentColor", "disabled", "_c2", "AdminCommentSection", "allowComments", "_s3", "comments", "loading", "refresh", "likeComment", "unlikeComment", "deleteComment", "flagComment", "handleReply", "parentId", "log", "commentId", "textAlign", "opacity", "margin", "borderTop", "animation", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/AdminCommentSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport type { Comment, CreateCommentData } from '../../services/commentService';\nimport { Heart, MessageCircle, Shield, Trash2, Flag, AlertCircle, ArrowRight } from 'lucide-react';\nimport ProfileAvatar from '../common/ProfileAvatar';\nimport {\n  shouldShowReplyButton,\n  calculateIndentation,\n  getDepthLimitMessage,\n  getCommentDepthClasses,\n  COMMENT_DEPTH_CONFIG\n} from '../../utils/commentDepth';\n\ninterface AdminCommentSectionProps {\n  announcementId: number;\n  allowComments?: boolean;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n}\n\ninterface AdminCommentItemProps {\n  comment: Comment;\n  onReply: (parentId: number) => void;\n  onLike: (id: number) => void;\n  onUnlike: (id: number) => void;\n  onDelete: (id: number) => void;\n  onFlag: (id: number, reason: string) => void;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n  depth?: number;\n}\n\nconst AdminCommentItem: React.FC<AdminCommentItemProps> = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  onDelete,\n  onFlag,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showActions, setShowActions] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      onDelete(comment.comment_id);\n    }\n  };\n\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n\n  const handleFlag = () => {\n    const reason = window.prompt('Please provide a reason for flagging this comment:');\n    if (reason && reason.trim()) {\n      onFlag(comment.comment_id, reason.trim());\n    }\n  };\n\n  return (\n    <div\n      id={`comment-${comment.comment_id}`}\n      className={depthClasses.join(' ')}\n      style={{\n        marginLeft: `${indentation}px`,\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb',\n        position: 'relative'\n      }}\n    onMouseEnter={() => setShowActions(true)}\n    onMouseLeave={() => setShowActions(false)}\n    >\n      <div style={{ display: 'flex', alignItems: 'start', gap: '0.75rem' }}>\n        {/* Avatar */}\n        <div style={{ position: 'relative' }}>\n          <ProfileAvatar\n            profilePicture={comment.profile_picture}\n            firstName={comment.author_name?.split(' ')[0]}\n            lastName={comment.author_name?.split(' ')[1]}\n            size=\"large\"\n            gradientColors={{\n              from: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n              to: comment.user_type === 'admin' ? '#1e40af' : '#16a34a'\n            }}\n            style={{ flexShrink: 0 }}\n          />\n          {comment.user_type === 'admin' && (\n            <div style={{\n              position: 'absolute',\n              bottom: '-2px',\n              right: '-2px',\n              width: '1rem',\n              height: '1rem',\n              backgroundColor: '#facc15',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '2px solid white'\n            }}>\n              <Shield size={8} color=\"white\" />\n            </div>\n          )}\n        </div>\n\n        <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Comment Header */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem',\n            flexWrap: 'wrap'\n          }}>\n            <span style={{\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            }}>\n              {comment.author_name || 'Anonymous'}\n            </span>\n            \n            {comment.user_type === 'admin' && (\n              <span style={{\n                backgroundColor: '#3b82f6',\n                color: 'white',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '0.375rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              }}>\n                <Shield size={10} />\n                Admin\n              </span>\n            )}\n\n            {comment.is_flagged && (\n              <span style={{\n                backgroundColor: '#ef4444',\n                color: 'white',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '0.375rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              }}>\n                <Flag size={10} />\n                Flagged\n              </span>\n            )}\n\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            }}>\n              {formatCommentDate(comment.created_at)}\n            </span>\n          </div>\n\n          {/* Comment Content */}\n          <div style={{\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            marginBottom: '0.75rem',\n            wordBreak: 'break-word'\n          }}>\n            {comment.comment_text}\n          </div>\n\n          {/* Comment Actions */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          }}>\n            {/* Like Button */}\n            <button\n              onClick={handleReactionToggle}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              }}\n            >\n              <Heart\n                size={14}\n                fill={hasUserReacted ? '#ef4444' : 'none'}\n              />\n              <span>{comment.reaction_count || 0}</span>\n            </button>\n\n            {/* Reply Button with Depth Limiting */}\n            {canReply ? (\n              <button\n                onClick={handleReplyClick}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem',\n                  background: 'none',\n                  border: 'none',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n                e.currentTarget.style.color = '#374151';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n                e.currentTarget.style.color = '#6b7280';\n              }}\n            >\n              <MessageCircle size={14} />\n              Reply\n            </button>\n            ) : (\n              // Show depth limit message for max depth comments\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.75rem',\n                color: '#f59e0b',\n                fontStyle: 'italic'\n              }}>\n                <AlertCircle size={12} />\n                <span>Reply depth limit reached</span>\n                <button\n                  onClick={() => {\n                    // Scroll to top-level comment for continuing thread\n                    const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                    if (rootElement) {\n                      rootElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                    }\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#3b82f6',\n                    fontSize: '0.75rem',\n                    textDecoration: 'underline',\n                    padding: '0'\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                    Continue thread\n                    <ArrowRight size={10} />\n                  </span>\n                </button>\n              </div>\n            )}\n\n            {/* Admin Actions */}\n            {currentUserType === 'admin' && showActions && (\n              <>\n                <button\n                  onClick={handleFlag}\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    background: 'none',\n                    border: 'none',\n                    color: '#f59e0b',\n                    cursor: 'pointer',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '0.375rem',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#fef3c7';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  }}\n                >\n                  <Flag size={14} />\n                  Flag\n                </button>\n\n                <button\n                  onClick={handleDelete}\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    background: 'none',\n                    border: 'none',\n                    color: '#ef4444',\n                    cursor: 'pointer',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '0.375rem',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#fef2f2';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  }}\n                >\n                  <Trash2 size={14} />\n                  Delete\n                </button>\n              </>\n            )}\n          </div>\n\n          {/* Reply Form */}\n          {showReplyForm && (\n            <div style={{ marginTop: '0.75rem' }}>\n              <AdminCommentForm\n                announcementId={comment.announcement_id}\n                parentCommentId={comment.comment_id}\n                onSubmit={() => setShowReplyForm(false)}\n                onCancel={() => setShowReplyForm(false)}\n                placeholder=\"Write a reply...\"\n              />\n            </div>\n          )}\n\n          {/* Depth Warning Message */}\n          {showDepthWarning && (\n            <div style={{\n              marginTop: '0.5rem',\n              padding: '0.5rem',\n              backgroundColor: '#fef3c7',\n              border: '1px solid #f59e0b',\n              borderRadius: '4px',\n              fontSize: '0.75rem',\n              color: '#92400e',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <AlertCircle size={14} />\n              <span>{depthLimitMessage}</span>\n            </div>\n          )}\n\n          {/* Replies */}\n          {comment.replies && comment.replies.length > 0 && (\n            <div style={{ marginTop: '1rem' }}>\n              {comment.replies.map((reply) => (\n                <AdminCommentItem\n                  key={reply.comment_id}\n                  comment={reply}\n                  onReply={onReply}\n                  onLike={onLike}\n                  onUnlike={onUnlike}\n                  onDelete={onDelete}\n                  onFlag={onFlag}\n                  currentUserId={currentUserId}\n                  currentUserType={currentUserType}\n                  depth={depth + 1}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Admin Comment Form Component will be added in the next chunk\ninterface AdminCommentFormProps {\n  announcementId: number;\n  parentCommentId?: number;\n  onSubmit: () => void;\n  onCancel?: () => void;\n  placeholder?: string;\n}\n\nconst AdminCommentForm: React.FC<AdminCommentFormProps> = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { createComment } = useComments(announcementId, 'admin'); // Admin service\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!commentText.trim() || isSubmitting) return;\n\n    try {\n      setIsSubmitting(true);\n\n      const commentData: CreateCommentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        parent_comment_id: parentCommentId,\n        is_anonymous: isAnonymous\n      };\n\n      await createComment(commentData);\n      setCommentText('');\n      onSubmit();\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} style={{\n      padding: '1rem',\n      backgroundColor: '#f8fafc',\n      borderRadius: '8px',\n      border: '1px solid #e2e8f0'\n    }}>\n      <div style={{ marginBottom: '0.75rem' }}>\n        <textarea\n          value={commentText}\n          onChange={(e) => setCommentText(e.target.value)}\n          placeholder={placeholder}\n          rows={3}\n          style={{\n            width: '100%',\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            outline: 'none',\n            resize: 'vertical',\n            fontFamily: 'inherit'\n          }}\n          onFocus={(e) => {\n            e.currentTarget.style.borderColor = '#3b82f6';\n            e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n          }}\n          onBlur={(e) => {\n            e.currentTarget.style.borderColor = '#d1d5db';\n            e.currentTarget.style.boxShadow = 'none';\n          }}\n        />\n      </div>\n\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '0.75rem'\n      }}>\n        <label style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '0.875rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        }}>\n          <input\n            type=\"checkbox\"\n            checked={isAnonymous}\n            onChange={(e) => setIsAnonymous(e.target.checked)}\n            style={{\n              width: '1rem',\n              height: '1rem',\n              accentColor: '#3b82f6'\n            }}\n          />\n          Post as Anonymous Admin\n        </label>\n\n        <div style={{ display: 'flex', gap: '0.5rem' }}>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              style={{\n                padding: '0.5rem 1rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                backgroundColor: 'white',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f9fafb';\n                e.currentTarget.style.borderColor = '#9ca3af';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'white';\n                e.currentTarget.style.borderColor = '#d1d5db';\n              }}\n            >\n              Cancel\n            </button>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={!commentText.trim() || isSubmitting}\n            style={{\n              padding: '0.5rem 1rem',\n              border: 'none',\n              borderRadius: '6px',\n              backgroundColor: !commentText.trim() || isSubmitting ? '#9ca3af' : '#3b82f6',\n              color: 'white',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              if (!isSubmitting && commentText.trim()) {\n                e.currentTarget.style.backgroundColor = '#2563eb';\n              }\n            }}\n            onMouseLeave={(e) => {\n              if (!isSubmitting && commentText.trim()) {\n                e.currentTarget.style.backgroundColor = '#3b82f6';\n              }\n            }}\n          >\n            {isSubmitting ? 'Posting...' : 'Post Comment'}\n          </button>\n        </div>\n      </div>\n    </form>\n  );\n};\n\n// Main AdminCommentSection Component\nconst AdminCommentSection: React.FC<AdminCommentSectionProps> = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'admin'\n}) => {\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment,\n    deleteComment,\n    flagComment\n  } = useComments(announcementId, 'admin'); // Explicitly use admin service\n\n  const handleReply = (parentId: number) => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n\n  const handleDelete = async (commentId: number) => {\n    try {\n      await deleteComment(commentId);\n      await refresh();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n    }\n  };\n\n  const handleFlag = async (commentId: number, reason: string) => {\n    try {\n      await flagComment(commentId, reason);\n      await refresh();\n    } catch (error) {\n      console.error('Error flagging comment:', error);\n    }\n  };\n\n  if (!allowComments) {\n    return (\n      <div style={{\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic',\n        backgroundColor: '#f9fafb',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb'\n      }}>\n        <MessageCircle size={20} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n        <div>Comments are disabled for this announcement.</div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ marginTop: '1.5rem' }}>\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        marginBottom: '1rem'\n      }}>\n        <Shield size={20} color=\"#3b82f6\" />\n        <h3 style={{\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151',\n          margin: 0\n        }}>\n          Admin Comments ({comments.length})\n        </h3>\n      </div>\n\n      {error && (\n        <div style={{\n          padding: '0.75rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          color: '#dc2626',\n          borderRadius: '6px',\n          marginBottom: '1rem',\n          fontSize: '0.875rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <Flag size={16} />\n          {error}\n        </div>\n      )}\n\n      {/* Comment Form */}\n      <div style={{ marginBottom: '1.5rem' }}>\n        <AdminCommentForm\n          announcementId={announcementId}\n          onSubmit={refresh}\n          placeholder=\"Share your admin insights...\"\n        />\n      </div>\n\n      {/* Comments List */}\n      {loading ? (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '2rem'\n        }}>\n          <div style={{\n            width: '1.5rem',\n            height: '1.5rem',\n            border: '2px solid #e5e7eb',\n            borderTop: '2px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></div>\n        </div>\n      ) : comments.length === 0 ? (\n        <div style={{\n          textAlign: 'center',\n          padding: '2rem',\n          color: '#6b7280',\n          fontSize: '0.875rem',\n          backgroundColor: '#f8fafc',\n          borderRadius: '8px',\n          border: '1px solid #e2e8f0'\n        }}>\n          <MessageCircle size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n          <div>No comments yet. Be the first admin to share insights!</div>\n        </div>\n      ) : (\n        <div>\n          {comments.map((comment) => (\n            <AdminCommentItem\n              key={comment.comment_id}\n              comment={comment}\n              onReply={handleReply}\n              onLike={likeComment}\n              onUnlike={unlikeComment}\n              onDelete={handleDelete}\n              onFlag={handleFlag}\n              currentUserId={currentUserId}\n              currentUserType={currentUserType}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminCommentSection;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,yBAAyB;AAExE,SAASC,KAAK,EAAEC,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAClG,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SACEC,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,QACf,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBlC,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,aAAa;EACbC,eAAe;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMuC,cAAc,GAAGlB,OAAO,CAACmB,aAAa,KAAKC,SAAS,IAAIpB,OAAO,CAACmB,aAAa,KAAK,IAAI;;EAE5F;EACA,MAAME,QAAQ,GAAG/B,qBAAqB,CAACkB,KAAK,CAAC;EAC7C,MAAMc,WAAW,GAAG/B,oBAAoB,CAACiB,KAAK,CAAC;EAC/C,MAAMe,YAAY,GAAG9B,sBAAsB,CAACe,KAAK,CAAC;EAClD,MAAMgB,iBAAiB,GAAGhC,oBAAoB,CAACgB,KAAK,CAAC;EACrD,MAAMiB,YAAY,GAAGjB,KAAK,IAAId,oBAAoB,CAACgC,SAAS;EAE5D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIT,cAAc,EAAE;MAClBf,QAAQ,CAACH,OAAO,CAAC4B,UAAU,CAAC;IAC9B,CAAC,MAAM;MACL1B,MAAM,CAACF,OAAO,CAAC4B,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjG3B,QAAQ,CAACJ,OAAO,CAAC4B,UAAU,CAAC;IAC9B;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,YAAY,EAAE;MAChBR,mBAAmB,CAAC,IAAI,CAAC;MACzBgB,UAAU,CAAC,MAAMhB,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM;MACLJ,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAGL,MAAM,CAACM,MAAM,CAAC,oDAAoD,CAAC;IAClF,IAAID,MAAM,IAAIA,MAAM,CAACE,IAAI,CAAC,CAAC,EAAE;MAC3BhC,MAAM,CAACL,OAAO,CAAC4B,UAAU,EAAEO,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,oBACEzC,OAAA;IACE0C,EAAE,EAAE,WAAWtC,OAAO,CAAC4B,UAAU,EAAG;IACpCW,SAAS,EAAEhB,YAAY,CAACiB,IAAI,CAAC,GAAG,CAAE;IAClCC,KAAK,EAAE;MACLC,UAAU,EAAE,GAAGpB,WAAW,IAAI;MAC9BqB,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAErC,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;MAChDsC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE;IACZ,CAAE;IACJC,YAAY,EAAEA,CAAA,KAAMlC,cAAc,CAAC,IAAI,CAAE;IACzCmC,YAAY,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK,CAAE;IAAAoC,QAAA,eAExCvD,OAAA;MAAK6C,KAAK,EAAE;QAAEW,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAE;MAAAH,QAAA,gBAEnEvD,OAAA;QAAK6C,KAAK,EAAE;UAAEO,QAAQ,EAAE;QAAW,CAAE;QAAAG,QAAA,gBACnCvD,OAAA,CAACP,aAAa;UACZkE,cAAc,EAAEvD,OAAO,CAACwD,eAAgB;UACxCC,SAAS,GAAA/C,oBAAA,GAAEV,OAAO,CAAC0D,WAAW,cAAAhD,oBAAA,uBAAnBA,oBAAA,CAAqBiD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;UAC9CC,QAAQ,GAAAjD,qBAAA,GAAEX,OAAO,CAAC0D,WAAW,cAAA/C,qBAAA,uBAAnBA,qBAAA,CAAqBgD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;UAC7CE,IAAI,EAAC,OAAO;UACZC,cAAc,EAAE;YACdC,IAAI,EAAE/D,OAAO,CAACgE,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;YAC3DC,EAAE,EAAEjE,OAAO,CAACgE,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG;UAClD,CAAE;UACFvB,KAAK,EAAE;YAAEyB,UAAU,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,EACDtE,OAAO,CAACgE,SAAS,KAAK,OAAO,iBAC5BpE,OAAA;UAAK6C,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpBuB,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACbC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACd7B,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnBM,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBsB,cAAc,EAAE,QAAQ;YACxB5B,MAAM,EAAE;UACV,CAAE;UAAAI,QAAA,eACAvD,OAAA,CAACZ,MAAM;YAAC6E,IAAI,EAAE,CAAE;YAACe,KAAK,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1E,OAAA;QAAK6C,KAAK,EAAE;UAAEoC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAA3B,QAAA,gBAEnCvD,OAAA;UAAK6C,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbX,YAAY,EAAE,QAAQ;YACtBoC,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBACAvD,OAAA;YAAM6C,KAAK,EAAE;cACXuC,UAAU,EAAE,KAAK;cACjBJ,KAAK,EAAE,SAAS;cAChBK,QAAQ,EAAE;YACZ,CAAE;YAAA9B,QAAA,EACCnD,OAAO,CAAC0D,WAAW,IAAI;UAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EAENtE,OAAO,CAACgE,SAAS,KAAK,OAAO,iBAC5BpE,OAAA;YAAM6C,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1B+B,KAAK,EAAE,OAAO;cACdK,QAAQ,EAAE,SAAS;cACnBD,UAAU,EAAE,KAAK;cACjBpC,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,UAAU;cACxBM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAH,QAAA,gBACAvD,OAAA,CAACZ,MAAM;cAAC6E,IAAI,EAAE;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EAEAtE,OAAO,CAACkF,UAAU,iBACjBtF,OAAA;YAAM6C,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1B+B,KAAK,EAAE,OAAO;cACdK,QAAQ,EAAE,SAAS;cACnBD,UAAU,EAAE,KAAK;cACjBpC,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,UAAU;cACxBM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAH,QAAA,gBACAvD,OAAA,CAACV,IAAI;cAAC2E,IAAI,EAAE;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,eAED1E,OAAA;YAAM6C,KAAK,EAAE;cACXmC,KAAK,EAAE,SAAS;cAChBK,QAAQ,EAAE;YACZ,CAAE;YAAA9B,QAAA,EACCtE,iBAAiB,CAACmB,OAAO,CAACmF,UAAU;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN1E,OAAA;UAAK6C,KAAK,EAAE;YACVmC,KAAK,EAAE,SAAS;YAChBK,QAAQ,EAAE,UAAU;YACpBG,UAAU,EAAE,KAAK;YACjBzC,YAAY,EAAE,SAAS;YACvB0C,SAAS,EAAE;UACb,CAAE;UAAAlC,QAAA,EACCnD,OAAO,CAACsF;QAAY;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGN1E,OAAA;UAAK6C,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,MAAM;YACX2B,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,gBAEAvD,OAAA;YACE2F,OAAO,EAAE5D,oBAAqB;YAC9Bc,KAAK,EAAE;cACLW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACdkC,UAAU,EAAE,MAAM;cAClBzC,MAAM,EAAE,MAAM;cACd6B,KAAK,EAAE1D,cAAc,GAAG,SAAS,GAAG,SAAS;cAC7CuE,MAAM,EAAE,SAAS;cACjB7C,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,UAAU;cACxB4C,UAAU,EAAE;YACd,CAAE;YACFzC,YAAY,EAAG0C,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD,CAAE;YACFK,YAAY,EAAGyC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,aAAa;YACvD,CAAE;YAAAM,QAAA,gBAEFvD,OAAA,CAACd,KAAK;cACJ+E,IAAI,EAAE,EAAG;cACTgC,IAAI,EAAE3E,cAAc,GAAG,SAAS,GAAG;YAAO;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACF1E,OAAA;cAAAuD,QAAA,EAAOnD,OAAO,CAAC8F,cAAc,IAAI;YAAC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EAGRjD,QAAQ,gBACPzB,OAAA;YACE2F,OAAO,EAAEvD,gBAAiB;YAC1BS,KAAK,EAAE;cACLW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACdkC,UAAU,EAAE,MAAM;cAClBzC,MAAM,EAAE,MAAM;cACd6B,KAAK,EAAE,SAAS;cAChBa,MAAM,EAAE,SAAS;cACnB7C,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,UAAU;cACxB4C,UAAU,EAAE;YACd,CAAE;YACFzC,YAAY,EAAG0C,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;cACjD8C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACmC,KAAK,GAAG,SAAS;YACzC,CAAE;YACF1B,YAAY,EAAGyC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,aAAa;cACrD8C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACmC,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAzB,QAAA,gBAEFvD,OAAA,CAACb,aAAa;cAAC8E,IAAI,EAAE;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;UAAA;UAEP;UACA1E,OAAA;YAAK6C,KAAK,EAAE;cACVW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,QAAQ;cACb2B,QAAQ,EAAE,SAAS;cACnBL,KAAK,EAAE,SAAS;cAChBmB,SAAS,EAAE;YACb,CAAE;YAAA5C,QAAA,gBACAvD,OAAA,CAACT,WAAW;cAAC0E,IAAI,EAAE;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzB1E,OAAA;cAAAuD,QAAA,EAAM;YAAyB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC1E,OAAA;cACE2F,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMS,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWlG,OAAO,CAAC4B,UAAU,EAAE,CAAC;gBAC5E,IAAIoE,WAAW,EAAE;kBACfA,WAAW,CAACG,cAAc,CAAC;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC,CAAC;gBACrE;cACF,CAAE;cACF5D,KAAK,EAAE;gBACL+C,UAAU,EAAE,MAAM;gBAClBzC,MAAM,EAAE,MAAM;gBACd0C,MAAM,EAAE,SAAS;gBACjBb,KAAK,EAAE,SAAS;gBAChBK,QAAQ,EAAE,SAAS;gBACnBqB,cAAc,EAAE,WAAW;gBAC3B1D,OAAO,EAAE;cACX,CAAE;cAAAO,QAAA,eAEFvD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAU,CAAE;gBAAAH,QAAA,GAAC,iBAEtE,eAAAvD,OAAA,CAACR,UAAU;kBAACyE,IAAI,EAAE;gBAAG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA/D,eAAe,KAAK,OAAO,IAAIO,WAAW,iBACzClB,OAAA,CAAAE,SAAA;YAAAqD,QAAA,gBACEvD,OAAA;cACE2F,OAAO,EAAErD,UAAW;cACpBO,KAAK,EAAE;gBACLW,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,SAAS;gBACdkC,UAAU,EAAE,MAAM;gBAClBzC,MAAM,EAAE,MAAM;gBACd6B,KAAK,EAAE,SAAS;gBAChBa,MAAM,EAAE,SAAS;gBACjB7C,OAAO,EAAE,gBAAgB;gBACzBE,YAAY,EAAE,UAAU;gBACxB4C,UAAU,EAAE;cACd,CAAE;cACFzC,YAAY,EAAG0C,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;cACnD,CAAE;cACFK,YAAY,EAAGyC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,aAAa;cACvD,CAAE;cAAAM,QAAA,gBAEFvD,OAAA,CAACV,IAAI;gBAAC2E,IAAI,EAAE;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1E,OAAA;cACE2F,OAAO,EAAE1D,YAAa;cACtBY,KAAK,EAAE;gBACLW,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,SAAS;gBACdkC,UAAU,EAAE,MAAM;gBAClBzC,MAAM,EAAE,MAAM;gBACd6B,KAAK,EAAE,SAAS;gBAChBa,MAAM,EAAE,SAAS;gBACjB7C,OAAO,EAAE,gBAAgB;gBACzBE,YAAY,EAAE,UAAU;gBACxB4C,UAAU,EAAE;cACd,CAAE;cACFzC,YAAY,EAAG0C,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;cACnD,CAAE;cACFK,YAAY,EAAGyC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,aAAa;cACvD,CAAE;cAAAM,QAAA,gBAEFvD,OAAA,CAACX,MAAM;gBAAC4E,IAAI,EAAE;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL1D,aAAa,iBACZhB,OAAA;UAAK6C,KAAK,EAAE;YAAE8D,SAAS,EAAE;UAAU,CAAE;UAAApD,QAAA,eACnCvD,OAAA,CAAC4G,gBAAgB;YACfC,cAAc,EAAEzG,OAAO,CAAC0G,eAAgB;YACxCC,eAAe,EAAE3G,OAAO,CAAC4B,UAAW;YACpCgF,QAAQ,EAAEA,CAAA,KAAM/F,gBAAgB,CAAC,KAAK,CAAE;YACxCgG,QAAQ,EAAEA,CAAA,KAAMhG,gBAAgB,CAAC,KAAK,CAAE;YACxCiG,WAAW,EAAC;UAAkB;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAtD,gBAAgB,iBACfpB,OAAA;UAAK6C,KAAK,EAAE;YACV8D,SAAS,EAAE,QAAQ;YACnB3D,OAAO,EAAE,QAAQ;YACjBC,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBmC,QAAQ,EAAE,SAAS;YACnBL,KAAK,EAAE,SAAS;YAChBxB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAH,QAAA,gBACAvD,OAAA,CAACT,WAAW;YAAC0E,IAAI,EAAE;UAAG;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB1E,OAAA;YAAAuD,QAAA,EAAO3B;UAAiB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN,EAGAtE,OAAO,CAAC+G,OAAO,IAAI/G,OAAO,CAAC+G,OAAO,CAACC,MAAM,GAAG,CAAC,iBAC5CpH,OAAA;UAAK6C,KAAK,EAAE;YAAE8D,SAAS,EAAE;UAAO,CAAE;UAAApD,QAAA,EAC/BnD,OAAO,CAAC+G,OAAO,CAACE,GAAG,CAAEC,KAAK,iBACzBtH,OAAA,CAACG,gBAAgB;YAEfC,OAAO,EAAEkH,KAAM;YACfjH,OAAO,EAAEA,OAAQ;YACjBC,MAAM,EAAEA,MAAO;YACfC,QAAQ,EAAEA,QAAS;YACnBC,QAAQ,EAAEA,QAAS;YACnBC,MAAM,EAAEA,MAAO;YACfC,aAAa,EAAEA,aAAc;YAC7BC,eAAe,EAAEA,eAAgB;YACjCC,KAAK,EAAEA,KAAK,GAAG;UAAE,GATZ0G,KAAK,CAACtF,UAAU;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUtB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA7D,EAAA,CAnYMV,gBAAiD;AAAAoH,EAAA,GAAjDpH,gBAAiD;AA4YvD,MAAMyG,gBAAiD,GAAGA,CAAC;EACzDC,cAAc;EACdE,eAAe;EACfC,QAAQ;EACRC,QAAQ;EACRC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAM,GAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4I,WAAW,EAAEC,cAAc,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8I,YAAY,EAAEC,eAAe,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEgJ;EAAc,CAAC,GAAG/I,WAAW,CAAC6H,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEhE,MAAMmB,YAAY,GAAG,MAAOjC,CAAkB,IAAK;IACjDA,CAAC,CAACkC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,WAAW,CAAChF,IAAI,CAAC,CAAC,IAAIoF,YAAY,EAAE;IAEzC,IAAI;MACFC,eAAe,CAAC,IAAI,CAAC;MAErB,MAAMI,WAA8B,GAAG;QACrCpB,eAAe,EAAED,cAAc;QAC/BnB,YAAY,EAAE+B,WAAW,CAAChF,IAAI,CAAC,CAAC;QAChC0F,iBAAiB,EAAEpB,eAAe;QAClCqB,YAAY,EAAET;MAChB,CAAC;MAED,MAAMI,aAAa,CAACG,WAAW,CAAC;MAChCR,cAAc,CAAC,EAAE,CAAC;MAClBV,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRP,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE9H,OAAA;IAAMgH,QAAQ,EAAEgB,YAAa;IAACnF,KAAK,EAAE;MACnCG,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE;IACV,CAAE;IAAAI,QAAA,gBACAvD,OAAA;MAAK6C,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAU,CAAE;MAAAQ,QAAA,eACtCvD,OAAA;QACEuI,KAAK,EAAEd,WAAY;QACnBe,QAAQ,EAAGzC,CAAC,IAAK2B,cAAc,CAAC3B,CAAC,CAAC0C,MAAM,CAACF,KAAK,CAAE;QAChDrB,WAAW,EAAEA,WAAY;QACzBwB,IAAI,EAAE,CAAE;QACR7F,KAAK,EAAE;UACLgC,KAAK,EAAE,MAAM;UACb7B,OAAO,EAAE,SAAS;UAClBG,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,KAAK;UACnBmC,QAAQ,EAAE,UAAU;UACpBsD,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,UAAU;UAClBC,UAAU,EAAE;QACd,CAAE;QACFC,OAAO,EAAG/C,CAAC,IAAK;UACdA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACkG,WAAW,GAAG,SAAS;UAC7ChD,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACmG,SAAS,GAAG,mCAAmC;QACvE,CAAE;QACFC,MAAM,EAAGlD,CAAC,IAAK;UACbA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACkG,WAAW,GAAG,SAAS;UAC7ChD,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACmG,SAAS,GAAG,MAAM;QAC1C;MAAE;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1E,OAAA;MAAK6C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBsB,cAAc,EAAE,eAAe;QAC/BrB,GAAG,EAAE;MACP,CAAE;MAAAH,QAAA,gBACAvD,OAAA;QAAO6C,KAAK,EAAE;UACZW,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,QAAQ;UACb2B,QAAQ,EAAE,UAAU;UACpBL,KAAK,EAAE,SAAS;UAChBa,MAAM,EAAE;QACV,CAAE;QAAAtC,QAAA,gBACAvD,OAAA;UACEkJ,IAAI,EAAC,UAAU;UACfC,OAAO,EAAExB,WAAY;UACrBa,QAAQ,EAAGzC,CAAC,IAAK6B,cAAc,CAAC7B,CAAC,CAAC0C,MAAM,CAACU,OAAO,CAAE;UAClDtG,KAAK,EAAE;YACLgC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdsE,WAAW,EAAE;UACf;QAAE;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,2BAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAER1E,OAAA;QAAK6C,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,GAC5C0D,QAAQ,iBACPjH,OAAA;UACEkJ,IAAI,EAAC,QAAQ;UACbvD,OAAO,EAAEsB,QAAS;UAClBpE,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,OAAO;YACxB+B,KAAK,EAAE,SAAS;YAChBK,QAAQ,EAAE,UAAU;YACpBD,UAAU,EAAE,KAAK;YACjBS,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE;UACd,CAAE;UACFzC,YAAY,EAAG0C,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;YACjD8C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACkG,WAAW,GAAG,SAAS;UAC/C,CAAE;UACFzF,YAAY,EAAGyC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,OAAO;YAC/C8C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACkG,WAAW,GAAG,SAAS;UAC/C,CAAE;UAAAxF,QAAA,EACH;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED1E,OAAA;UACEkJ,IAAI,EAAC,QAAQ;UACbG,QAAQ,EAAE,CAAC5B,WAAW,CAAChF,IAAI,CAAC,CAAC,IAAIoF,YAAa;UAC9ChF,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBG,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,CAACwE,WAAW,CAAChF,IAAI,CAAC,CAAC,IAAIoF,YAAY,GAAG,SAAS,GAAG,SAAS;YAC5E7C,KAAK,EAAE,OAAO;YACdK,QAAQ,EAAE,UAAU;YACpBD,UAAU,EAAE,KAAK;YACjBS,MAAM,EAAE,CAAC4B,WAAW,CAAChF,IAAI,CAAC,CAAC,IAAIoF,YAAY,GAAG,aAAa,GAAG,SAAS;YACvE/B,UAAU,EAAE;UACd,CAAE;UACFzC,YAAY,EAAG0C,CAAC,IAAK;YACnB,IAAI,CAAC8B,YAAY,IAAIJ,WAAW,CAAChF,IAAI,CAAC,CAAC,EAAE;cACvCsD,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD;UACF,CAAE;UACFK,YAAY,EAAGyC,CAAC,IAAK;YACnB,IAAI,CAAC8B,YAAY,IAAIJ,WAAW,CAAChF,IAAI,CAAC,CAAC,EAAE;cACvCsD,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD;UACF,CAAE;UAAAM,QAAA,EAEDsE,YAAY,GAAG,YAAY,GAAG;QAAc;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;;AAED;AAAA8C,GAAA,CAhKMZ,gBAAiD;EAAA,QAU3B5H,WAAW;AAAA;AAAAsK,GAAA,GAVjC1C,gBAAiD;AAiKvD,MAAM2C,mBAAuD,GAAGA,CAAC;EAC/D1C,cAAc;EACd2C,aAAa,GAAG,IAAI;EACpB9I,aAAa;EACbC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAA8I,GAAA;EACJ,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPtB,KAAK;IACLuB,OAAO;IACPC,WAAW;IACXC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGhL,WAAW,CAAC6H,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;;EAE1C,MAAMoD,WAAW,GAAIC,QAAgB,IAAK;IACxC;IACA5B,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,EAAED,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMjI,YAAY,GAAG,MAAOmI,SAAiB,IAAK;IAChD,IAAI;MACF,MAAML,aAAa,CAACK,SAAS,CAAC;MAC9B,MAAMR,OAAO,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM/F,UAAU,GAAG,MAAAA,CAAO8H,SAAiB,EAAE7H,MAAc,KAAK;IAC9D,IAAI;MACF,MAAMyH,WAAW,CAACI,SAAS,EAAE7H,MAAM,CAAC;MACpC,MAAMqH,OAAO,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,IAAI,CAACmB,aAAa,EAAE;IAClB,oBACExJ,OAAA;MAAK6C,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACfqH,SAAS,EAAE,QAAQ;QACnBrF,KAAK,EAAE,SAAS;QAChBK,QAAQ,EAAE,UAAU;QACpBc,SAAS,EAAE,QAAQ;QACnBlD,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAI,QAAA,gBACAvD,OAAA,CAACb,aAAa;QAAC8E,IAAI,EAAE,EAAG;QAACpB,KAAK,EAAE;UAAEE,YAAY,EAAE,QAAQ;UAAEuH,OAAO,EAAE;QAAI;MAAE;QAAA/F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5E1E,OAAA;QAAAuD,QAAA,EAAK;MAA4C;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,oBACE1E,OAAA;IAAK6C,KAAK,EAAE;MAAE8D,SAAS,EAAE;IAAS,CAAE;IAAApD,QAAA,gBAClCvD,OAAA;MAAK6C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,QAAQ;QACbX,YAAY,EAAE;MAChB,CAAE;MAAAQ,QAAA,gBACAvD,OAAA,CAACZ,MAAM;QAAC6E,IAAI,EAAE,EAAG;QAACe,KAAK,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpC1E,OAAA;QAAI6C,KAAK,EAAE;UACTwC,QAAQ,EAAE,UAAU;UACpBD,UAAU,EAAE,KAAK;UACjBJ,KAAK,EAAE,SAAS;UAChBuF,MAAM,EAAE;QACV,CAAE;QAAAhH,QAAA,GAAC,kBACe,EAACmG,QAAQ,CAACtC,MAAM,EAAC,GACnC;MAAA;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEL2D,KAAK,iBACJrI,OAAA;MAAK6C,KAAK,EAAE;QACVG,OAAO,EAAE,SAAS;QAClBC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3B6B,KAAK,EAAE,SAAS;QAChB9B,YAAY,EAAE,KAAK;QACnBH,YAAY,EAAE,MAAM;QACpBsC,QAAQ,EAAE,UAAU;QACpB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAH,QAAA,gBACAvD,OAAA,CAACV,IAAI;QAAC2E,IAAI,EAAE;MAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjB2D,KAAK;IAAA;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1E,OAAA;MAAK6C,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAS,CAAE;MAAAQ,QAAA,eACrCvD,OAAA,CAAC4G,gBAAgB;QACfC,cAAc,EAAEA,cAAe;QAC/BG,QAAQ,EAAE4C,OAAQ;QAClB1C,WAAW,EAAC;MAA8B;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLiF,OAAO,gBACN3J,OAAA;MAAK6C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfuB,cAAc,EAAE,QAAQ;QACxB/B,OAAO,EAAE;MACX,CAAE;MAAAO,QAAA,eACAvD,OAAA;QAAK6C,KAAK,EAAE;UACVgC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChB3B,MAAM,EAAE,mBAAmB;UAC3BqH,SAAS,EAAE,mBAAmB;UAC9BtH,YAAY,EAAE,KAAK;UACnBuH,SAAS,EAAE;QACb;MAAE;QAAAlG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJgF,QAAQ,CAACtC,MAAM,KAAK,CAAC,gBACvBpH,OAAA;MAAK6C,KAAK,EAAE;QACVwH,SAAS,EAAE,QAAQ;QACnBrH,OAAO,EAAE,MAAM;QACfgC,KAAK,EAAE,SAAS;QAChBK,QAAQ,EAAE,UAAU;QACpBpC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAI,QAAA,gBACAvD,OAAA,CAACb,aAAa;QAAC8E,IAAI,EAAE,EAAG;QAACpB,KAAK,EAAE;UAAEE,YAAY,EAAE,QAAQ;UAAEuH,OAAO,EAAE;QAAI;MAAE;QAAA/F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5E1E,OAAA;QAAAuD,QAAA,EAAK;MAAsD;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,gBAEN1E,OAAA;MAAAuD,QAAA,EACGmG,QAAQ,CAACrC,GAAG,CAAEjH,OAAO,iBACpBJ,OAAA,CAACG,gBAAgB;QAEfC,OAAO,EAAEA,OAAQ;QACjBC,OAAO,EAAE4J,WAAY;QACrB3J,MAAM,EAAEuJ,WAAY;QACpBtJ,QAAQ,EAAEuJ,aAAc;QACxBtJ,QAAQ,EAAEyB,YAAa;QACvBxB,MAAM,EAAE6B,UAAW;QACnB5B,aAAa,EAAEA,aAAc;QAC7BC,eAAe,EAAEA;MAAgB,GAR5BP,OAAO,CAAC4B,UAAU;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASxB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC+E,GAAA,CAxJIF,mBAAuD;EAAA,QAevDvK,WAAW;AAAA;AAAA0L,GAAA,GAfXnB,mBAAuD;AA0J7D,eAAeA,mBAAmB;AAAC,IAAAhC,EAAA,EAAA+B,GAAA,EAAAoB,GAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}