{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a3 3 0 0 1-3-3 1 1 0 0 1 1-1z\",\n  key: \"ioqxb1\"\n}], [\"path\", {\n  d: \"M13 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1l-.81 3.242a1 1 0 0 1-.97.758H8\",\n  key: \"1rs59n\"\n}], [\"path\", {\n  d: \"M14 4h3a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3\",\n  key: \"105ega\"\n}], [\"path\", {\n  d: \"M18 6h4\",\n  key: \"66u95g\"\n}], [\"path\", {\n  d: \"m5 10-2 8\",\n  key: \"xt2lic\"\n}], [\"path\", {\n  d: \"m7 18 2-8\",\n  key: \"1bzku2\"\n}]];\nconst Drill = createLucideIcon(\"drill\", __iconNode);\nexport { __iconNode, Drill as default };\n//# sourceMappingURL=drill.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}