{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"9\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"yn7j0q\"\n}], [\"path\", {\n  d: \"M4 22V2\",\n  key: \"tsjzd3\"\n}], [\"path\", {\n  d: \"M20 22V2\",\n  key: \"1bnhr8\"\n}]];\nconst AlignHorizontalSpaceAround = createLucideIcon(\"align-horizontal-space-around\", __iconNode);\nexport { __iconNode, AlignHorizontalSpaceAround as default };\n//# sourceMappingURL=align-horizontal-space-around.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}