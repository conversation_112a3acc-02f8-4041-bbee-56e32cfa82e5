{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.236 22a3 3 0 0 0-2.2-5\",\n  key: \"21bitc\"\n}], [\"path\", {\n  d: \"M16 20a3 3 0 0 1 3-3h1a2 2 0 0 0 2-2v-2a4 4 0 0 0-4-4V4\",\n  key: \"oh0fg0\"\n}], [\"path\", {\n  d: \"M18 13h.01\",\n  key: \"9veqaj\"\n}], [\"path\", {\n  d: \"M18 6a4 4 0 0 0-4 4 7 7 0 0 0-7 7c0-5 4-5 4-10.5a4.5 4.5 0 1 0-9 0 2.5 2.5 0 0 0 5 0C7 10 3 11 3 17c0 2.8 2.2 5 5 5h10\",\n  key: \"980v8a\"\n}]];\nconst Squirrel = createLucideIcon(\"squirrel\", __iconNode);\nexport { __iconNode, Squirrel as default };\n//# sourceMappingURL=squirrel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}