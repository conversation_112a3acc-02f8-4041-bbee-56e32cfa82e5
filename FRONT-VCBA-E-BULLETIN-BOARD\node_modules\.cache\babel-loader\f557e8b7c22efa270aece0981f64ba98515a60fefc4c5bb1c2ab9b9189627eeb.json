{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 6c0 2-2 2-2 4v10a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4V2h12z\",\n  key: \"1orkel\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"18\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"1z11jq\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1f4yc1\"\n}]];\nconst Flashlight = createLucideIcon(\"flashlight\", __iconNode);\nexport { __iconNode, Flashlight as default };\n//# sourceMappingURL=flashlight.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}