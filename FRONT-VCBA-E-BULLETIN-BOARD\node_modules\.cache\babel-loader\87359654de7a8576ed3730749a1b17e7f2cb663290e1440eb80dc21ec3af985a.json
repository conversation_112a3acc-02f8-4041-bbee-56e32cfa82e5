{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"13\",\n  height: \"7\",\n  x: \"8\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"pkso9a\"\n}], [\"path\", {\n  d: \"m2 9 3 3-3 3\",\n  key: \"1agib5\"\n}], [\"rect\", {\n  width: \"13\",\n  height: \"7\",\n  x: \"8\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"1q5fc1\"\n}]];\nconst BetweenHorizontalStart = createLucideIcon(\"between-horizontal-start\", __iconNode);\nexport { __iconNode, BetweenHorizontalStart as default };\n//# sourceMappingURL=between-horizontal-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}