{"ast": null, "code": "import { httpClient, tokenManager } from './api.service';\nimport { API_ENDPOINTS, USER_DATA_KEY, REFRESH_TOKEN_KEY } from '../config/constants';\nexport class AuthService {\n  /**\n   * Get current authenticated user\n   */\n  static async getCurrentUser() {\n    try {\n      const token = tokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n      const response = await httpClient.get(API_ENDPOINTS.AUTH.PROFILE);\n      if (response.success && response.data) {\n        return response.data.user;\n      }\n      return null;\n    } catch (error) {\n      console.error('Failed to get current user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login user (admin or student)\n   */\n  static async login(credentials) {\n    try {\n      var _response$error;\n      const response = await httpClient.post(API_ENDPOINTS.AUTH.LOGIN, credentials);\n      if (response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user;\n        const transformedUser = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at\n        };\n\n        // Store tokens and transformed user data\n        tokenManager.setToken(response.data.accessToken);\n        // Note: refreshToken is typically stored as httpOnly cookie by the backend\n        // Only store in localStorage if explicitly provided in response\n        if (response.data.refreshToken) {\n          localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(USER_DATA_KEY, JSON.stringify(transformedUser));\n        return {\n          success: true,\n          message: response.message || 'Login successful',\n          data: {\n            ...response.data,\n            user: transformedUser\n          }\n        };\n      }\n      throw new Error(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Login failed');\n    } catch (error) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        var _error$data, _error$data$error;\n        throw new Error(((_error$data = error.data) === null || _error$data === void 0 ? void 0 : (_error$data$error = _error$data.error) === null || _error$data$error === void 0 ? void 0 : _error$data$error.message) || error.message || 'Login failed');\n      }\n      throw new Error(error.message || 'Login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data) {\n    try {\n      var _response$error2;\n      const response = await httpClient.post(API_ENDPOINTS.AUTH.ADMIN_REGISTER, data);\n\n      // The response already contains the full structure from the backend\n      if (response.success && response.data) {\n        return {\n          success: true,\n          message: response.message || 'Registration initiated successfully',\n          data: response.data\n        };\n      }\n      throw new Error(((_response$error2 = response.error) === null || _response$error2 === void 0 ? void 0 : _response$error2.message) || 'Registration failed');\n    } catch (error) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        var _error$data2, _error$data2$error;\n        throw new Error(((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : (_error$data2$error = _error$data2.error) === null || _error$data2$error === void 0 ? void 0 : _error$data2$error.message) || error.message || 'Registration failed');\n      }\n      throw new Error(error.message || 'Registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data) {\n    try {\n      var _response$error3;\n      const response = await httpClient.post(API_ENDPOINTS.AUTH.VERIFY_OTP, data);\n\n      // The response already contains the full structure from the backend\n      if (response.success && response.data) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data\n        };\n      }\n      throw new Error(((_response$error3 = response.error) === null || _response$error3 === void 0 ? void 0 : _response$error3.message) || 'OTP verification failed');\n    } catch (error) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        var _error$data3, _error$data3$error;\n        throw new Error(((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : (_error$data3$error = _error$data3.error) === null || _error$data3$error === void 0 ? void 0 : _error$data3$error.message) || error.message || 'OTP verification failed');\n      }\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email) {\n    try {\n      var _response$error4;\n      const response = await httpClient.post(API_ENDPOINTS.AUTH.RESEND_OTP, {\n        email\n      });\n      if (response.success) {\n        return response;\n      }\n      throw new Error(((_response$error4 = response.error) === null || _response$error4 === void 0 ? void 0 : _response$error4.message) || 'Failed to resend OTP');\n    } catch (error) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        var _error$data4, _error$data4$error;\n        throw new Error(((_error$data4 = error.data) === null || _error$data4 === void 0 ? void 0 : (_error$data4$error = _error$data4.error) === null || _error$data4$error === void 0 ? void 0 : _error$data4$error.message) || error.message || 'Failed to resend OTP');\n      }\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  static async refreshToken() {\n    try {\n      var _response$error5;\n      const response = await httpClient.post(API_ENDPOINTS.AUTH.REFRESH);\n      if (response.success && response.data) {\n        // Update stored tokens\n        tokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        return {\n          success: true,\n          message: response.message,\n          data: response.data\n        };\n      }\n      throw new Error(((_response$error5 = response.error) === null || _response$error5 === void 0 ? void 0 : _response$error5.message) || 'Token refresh failed');\n    } catch (error) {\n      throw new Error(error.message || 'Token refresh failed');\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  static async getProfile() {\n    try {\n      var _response$error6;\n      const response = await httpClient.get(API_ENDPOINTS.AUTH.PROFILE);\n      if (response.success && response.data) {\n        // Update stored user data\n        localStorage.setItem(USER_DATA_KEY, JSON.stringify(response.data.user));\n        return response.data.user;\n      }\n      throw new Error(((_response$error6 = response.error) === null || _response$error6 === void 0 ? void 0 : _response$error6.message) || 'Failed to get profile');\n    } catch (error) {\n      throw new Error(error.message || 'Failed to get profile');\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  static async logout() {\n    try {\n      console.log('🚪 AuthService - Calling server logout endpoint');\n      // Call logout endpoint to invalidate token on server\n      await httpClient.post(API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AuthService - Server logout successful');\n    } catch (error) {\n      // Continue with local logout even if server call fails\n      console.warn('⚠️ AuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      // Always clear local storage\n      console.log('🧹 AuthService - Clearing all local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear local storage and tokens\n   */\n  static clearLocalStorage() {\n    console.log('🧹 AuthService - Clearing all authentication data');\n\n    // Remove tokens using token manager\n    tokenManager.removeToken();\n\n    // Remove user data and refresh token\n    localStorage.removeItem(USER_DATA_KEY);\n    localStorage.removeItem(REFRESH_TOKEN_KEY);\n\n    // Clear any other auth-related items (comprehensive cleanup)\n    const authKeys = ['vcba_auth_token', 'vcba_user_data', 'vcba_refresh_token', 'auth_token', 'user_data', 'refresh_token'];\n    authKeys.forEach(key => {\n      localStorage.removeItem(key);\n      console.log(`🗑️ Removed: ${key}`);\n    });\n\n    // Clear any session storage as well\n    sessionStorage.clear();\n    console.log('✅ AuthService - All authentication data cleared');\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  static isAuthenticated() {\n    const token = tokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData);\n  }\n\n  /**\n   * Get stored user data\n   */\n  static getStoredUser() {\n    try {\n      const userData = localStorage.getItem(USER_DATA_KEY);\n      return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n      console.error('Error parsing stored user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Validate current session - DISABLED TO PREVENT LOGOUTS\n   */\n  static async validateSession() {\n    // ALWAYS RETURN TRUE - No validation to prevent unwanted logouts\n    console.log('AuthService.validateSession - DISABLED, always returning true');\n    return true;\n  }\n\n  /**\n   * Get user role\n   */\n  static getUserRole() {\n    const user = this.getStoredUser();\n    return (user === null || user === void 0 ? void 0 : user.role) || null;\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  static hasRole(role) {\n    return this.getUserRole() === role;\n  }\n}\nexport default AuthService;", "map": {"version": 3, "names": ["httpClient", "tokenManager", "API_ENDPOINTS", "USER_DATA_KEY", "REFRESH_TOKEN_KEY", "AuthService", "getCurrentUser", "token", "getToken", "response", "get", "AUTH", "PROFILE", "success", "data", "user", "error", "console", "login", "credentials", "_response$error", "post", "LOGIN", "rawUser", "transformedUser", "id", "admin_id", "student_id", "email", "role", "firstName", "first_name", "lastName", "last_name", "middleName", "middle_name", "suffix", "phoneNumber", "phone_number", "phone", "department", "position", "grade_level", "studentNumber", "student_number", "isActive", "Boolean", "is_active", "lastLogin", "last_login", "createdAt", "account_created_at", "created_at", "updatedAt", "account_updated_at", "updated_at", "setToken", "accessToken", "refreshToken", "localStorage", "setItem", "JSON", "stringify", "message", "Error", "status", "_error$data", "_error$data$error", "registerAdmin", "_response$error2", "ADMIN_REGISTER", "_error$data2", "_error$data2$error", "verifyOtp", "_response$error3", "VERIFY_OTP", "_error$data3", "_error$data3$error", "resendOtp", "_response$error4", "RESEND_OTP", "_error$data4", "_error$data4$error", "_response$error5", "REFRESH", "getProfile", "_response$error6", "logout", "log", "LOGOUT", "warn", "clearLocalStorage", "removeToken", "removeItem", "auth<PERSON><PERSON><PERSON>", "for<PERSON>ach", "key", "sessionStorage", "clear", "isAuthenticated", "userData", "getStoredUser", "getItem", "parse", "validateSession", "getUserRole", "hasRole"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/auth.service.ts"], "sourcesContent": ["import { httpClient, tokenManager } from './api.service';\nimport { API_ENDPOINTS, USER_DATA_KEY, REFRESH_TOKEN_KEY } from '../config/constants';\nimport {\n  LoginCredentials,\n  AdminRegistrationData,\n  OtpVerificationData,\n  AuthResponse,\n  RegistrationResponse,\n  OtpVerificationResponse,\n  User,\n  ApiResponse,\n} from '../types';\n\nexport class AuthService {\n  /**\n   * Get current authenticated user\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = tokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n\n      const response = await httpClient.get<{ user: User }>(\n        API_ENDPOINTS.AUTH.PROFILE\n      );\n\n      if (response.success && response.data) {\n        return response.data.user;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Failed to get current user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login user (admin or student)\n   */\n  static async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    try {\n      const response = await httpClient.post<{\n        user: User;\n        accessToken: string;\n        expiresIn: number;\n        refreshToken?: string;\n      }>(\n        API_ENDPOINTS.AUTH.LOGIN,\n        credentials\n      );\n\n      if (response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user as any;\n        const transformedUser: User = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at,\n        };\n\n        // Store tokens and transformed user data\n        tokenManager.setToken(response.data.accessToken);\n        // Note: refreshToken is typically stored as httpOnly cookie by the backend\n        // Only store in localStorage if explicitly provided in response\n        if (response.data.refreshToken) {\n          localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(USER_DATA_KEY, JSON.stringify(transformedUser));\n\n        return {\n          success: true,\n          message: response.message || 'Login successful',\n          data: {\n            ...response.data,\n            user: transformedUser,\n          },\n        };\n      }\n\n      throw new Error(response.error?.message || 'Login failed');\n    } catch (error: any) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        throw new Error(error.data?.error?.message || error.message || 'Login failed');\n      }\n      throw new Error(error.message || 'Login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data: AdminRegistrationData): Promise<RegistrationResponse> {\n    try {\n      const response = await httpClient.post<{\n        email: string;\n        otpSent: boolean;\n      }>(\n        API_ENDPOINTS.AUTH.ADMIN_REGISTER,\n        data\n      );\n\n      // The response already contains the full structure from the backend\n      if (response.success && response.data) {\n        return {\n          success: true,\n          message: response.message || 'Registration initiated successfully',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response.error?.message || 'Registration failed');\n    } catch (error: any) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        throw new Error(error.data?.error?.message || error.message || 'Registration failed');\n      }\n      throw new Error(error.message || 'Registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data: OtpVerificationData): Promise<OtpVerificationResponse> {\n    try {\n      const response = await httpClient.post<{\n        admin: User;\n      }>(\n        API_ENDPOINTS.AUTH.VERIFY_OTP,\n        data\n      );\n\n      // The response already contains the full structure from the backend\n      if (response.success && response.data) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response.error?.message || 'OTP verification failed');\n    } catch (error: any) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        throw new Error(error.data?.error?.message || error.message || 'OTP verification failed');\n      }\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email: string): Promise<ApiResponse> {\n    try {\n      const response = await httpClient.post<ApiResponse>(\n        API_ENDPOINTS.AUTH.RESEND_OTP,\n        { email }\n      );\n\n      if (response.success) {\n        return response;\n      }\n\n      throw new Error(response.error?.message || 'Failed to resend OTP');\n    } catch (error: any) {\n      // Handle ApiError from httpClient\n      if (error.status && error.data) {\n        throw new Error(error.data?.error?.message || error.message || 'Failed to resend OTP');\n      }\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  static async refreshToken(): Promise<AuthResponse> {\n    try {\n      const response = await httpClient.post<{\n        user: User;\n        accessToken: string;\n        expiresIn: number;\n        refreshToken?: string;\n      }>(\n        API_ENDPOINTS.AUTH.REFRESH\n      );\n\n      if (response.success && response.data) {\n        // Update stored tokens\n        tokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n\n        return {\n          success: true,\n          message: response.message,\n          data: response.data,\n        };\n      }\n\n      throw new Error(response.error?.message || 'Token refresh failed');\n    } catch (error: any) {\n      throw new Error(error.message || 'Token refresh failed');\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  static async getProfile(): Promise<User> {\n    try {\n      const response = await httpClient.get<{ user: User }>(\n        API_ENDPOINTS.AUTH.PROFILE\n      );\n\n      if (response.success && response.data) {\n        // Update stored user data\n        localStorage.setItem(USER_DATA_KEY, JSON.stringify(response.data.user));\n        return response.data.user;\n      }\n\n      throw new Error(response.error?.message || 'Failed to get profile');\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to get profile');\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  static async logout(): Promise<void> {\n    try {\n      console.log('🚪 AuthService - Calling server logout endpoint');\n      // Call logout endpoint to invalidate token on server\n      await httpClient.post(API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AuthService - Server logout successful');\n    } catch (error) {\n      // Continue with local logout even if server call fails\n      console.warn('⚠️ AuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      // Always clear local storage\n      console.log('🧹 AuthService - Clearing all local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear local storage and tokens\n   */\n  static clearLocalStorage(): void {\n    console.log('🧹 AuthService - Clearing all authentication data');\n\n    // Remove tokens using token manager\n    tokenManager.removeToken();\n\n    // Remove user data and refresh token\n    localStorage.removeItem(USER_DATA_KEY);\n    localStorage.removeItem(REFRESH_TOKEN_KEY);\n\n    // Clear any other auth-related items (comprehensive cleanup)\n    const authKeys = [\n      'vcba_auth_token',\n      'vcba_user_data',\n      'vcba_refresh_token',\n      'auth_token',\n      'user_data',\n      'refresh_token'\n    ];\n    authKeys.forEach(key => {\n      localStorage.removeItem(key);\n      console.log(`🗑️ Removed: ${key}`);\n    });\n\n    // Clear any session storage as well\n    sessionStorage.clear();\n\n    console.log('✅ AuthService - All authentication data cleared');\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  static isAuthenticated(): boolean {\n    const token = tokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData);\n  }\n\n  /**\n   * Get stored user data\n   */\n  static getStoredUser(): User | null {\n    try {\n      const userData = localStorage.getItem(USER_DATA_KEY);\n      return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n      console.error('Error parsing stored user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Validate current session - DISABLED TO PREVENT LOGOUTS\n   */\n  static async validateSession(): Promise<boolean> {\n    // ALWAYS RETURN TRUE - No validation to prevent unwanted logouts\n    console.log('AuthService.validateSession - DISABLED, always returning true');\n    return true;\n  }\n\n  /**\n   * Get user role\n   */\n  static getUserRole(): 'admin' | 'student' | null {\n    const user = this.getStoredUser();\n    return user?.role || null;\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  static hasRole(role: 'admin' | 'student'): boolean {\n    return this.getUserRole() === role;\n  }\n}\n\nexport default AuthService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAY,QAAQ,eAAe;AACxD,SAASC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,qBAAqB;AAYrF,OAAO,MAAMC,WAAW,CAAC;EACvB;AACF;AACA;EACE,aAAaC,cAAcA,CAAA,EAAyB;IAClD,IAAI;MACF,MAAMC,KAAK,GAAGN,YAAY,CAACO,QAAQ,CAAC,CAAC;MACrC,IAAI,CAACD,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MAEA,MAAME,QAAQ,GAAG,MAAMT,UAAU,CAACU,GAAG,CACnCR,aAAa,CAACS,IAAI,CAACC,OACrB,CAAC;MAED,IAAIH,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC,OAAOL,QAAQ,CAACK,IAAI,CAACC,IAAI;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAaE,KAAKA,CAACC,WAA6B,EAAyB;IACvE,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMX,QAAQ,GAAG,MAAMT,UAAU,CAACqB,IAAI,CAMpCnB,aAAa,CAACS,IAAI,CAACW,KAAK,EACxBH,WACF,CAAC;MAED,IAAIV,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC;QACA,MAAMS,OAAO,GAAGd,QAAQ,CAACK,IAAI,CAACC,IAAW;QACzC,MAAMS,eAAqB,GAAG;UAC5BC,EAAE,EAAEF,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACI,UAAU;UAC1CC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBC,IAAI,EAAEN,OAAO,CAACG,QAAQ,GAAG,OAAO,GAAG,SAAS;UAC5CI,SAAS,EAAEP,OAAO,CAACQ,UAAU;UAC7BC,QAAQ,EAAET,OAAO,CAACU,SAAS;UAC3BC,UAAU,EAAEX,OAAO,CAACY,WAAW;UAC/BC,MAAM,EAAEb,OAAO,CAACa,MAAM;UACtBC,WAAW,EAAEd,OAAO,CAACe,YAAY,IAAIf,OAAO,CAACgB,KAAK;UAClDC,UAAU,EAAEjB,OAAO,CAACiB,UAAU;UAC9BC,QAAQ,EAAElB,OAAO,CAACkB,QAAQ;UAC1BC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;UAChCC,aAAa,EAAEpB,OAAO,CAACqB,cAAc;UACrCC,QAAQ,EAAEC,OAAO,CAACvB,OAAO,CAACwB,SAAS,CAAC;UACpCC,SAAS,EAAEzB,OAAO,CAAC0B,UAAU;UAC7BC,SAAS,EAAE3B,OAAO,CAAC4B,kBAAkB,IAAI5B,OAAO,CAAC6B,UAAU;UAC3DC,SAAS,EAAE9B,OAAO,CAAC+B,kBAAkB,IAAI/B,OAAO,CAACgC;QACnD,CAAC;;QAED;QACAtD,YAAY,CAACuD,QAAQ,CAAC/C,QAAQ,CAACK,IAAI,CAAC2C,WAAW,CAAC;QAChD;QACA;QACA,IAAIhD,QAAQ,CAACK,IAAI,CAAC4C,YAAY,EAAE;UAC9BC,YAAY,CAACC,OAAO,CAACxD,iBAAiB,EAAEK,QAAQ,CAACK,IAAI,CAAC4C,YAAY,CAAC;QACrE;QACAC,YAAY,CAACC,OAAO,CAACzD,aAAa,EAAE0D,IAAI,CAACC,SAAS,CAACtC,eAAe,CAAC,CAAC;QAEpE,OAAO;UACLX,OAAO,EAAE,IAAI;UACbkD,OAAO,EAAEtD,QAAQ,CAACsD,OAAO,IAAI,kBAAkB;UAC/CjD,IAAI,EAAE;YACJ,GAAGL,QAAQ,CAACK,IAAI;YAChBC,IAAI,EAAES;UACR;QACF,CAAC;MACH;MAEA,MAAM,IAAIwC,KAAK,CAAC,EAAA5C,eAAA,GAAAX,QAAQ,CAACO,KAAK,cAAAI,eAAA,uBAAdA,eAAA,CAAgB2C,OAAO,KAAI,cAAc,CAAC;IAC5D,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB;MACA,IAAIA,KAAK,CAACiD,MAAM,IAAIjD,KAAK,CAACF,IAAI,EAAE;QAAA,IAAAoD,WAAA,EAAAC,iBAAA;QAC9B,MAAM,IAAIH,KAAK,CAAC,EAAAE,WAAA,GAAAlD,KAAK,CAACF,IAAI,cAAAoD,WAAA,wBAAAC,iBAAA,GAAVD,WAAA,CAAYlD,KAAK,cAAAmD,iBAAA,uBAAjBA,iBAAA,CAAmBJ,OAAO,KAAI/C,KAAK,CAAC+C,OAAO,IAAI,cAAc,CAAC;MAChF;MACA,MAAM,IAAIC,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,cAAc,CAAC;IAClD;EACF;;EAEA;AACF;AACA;EACE,aAAaK,aAAaA,CAACtD,IAA2B,EAAiC;IACrF,IAAI;MAAA,IAAAuD,gBAAA;MACF,MAAM5D,QAAQ,GAAG,MAAMT,UAAU,CAACqB,IAAI,CAIpCnB,aAAa,CAACS,IAAI,CAAC2D,cAAc,EACjCxD,IACF,CAAC;;MAED;MACA,IAAIL,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC,OAAO;UACLD,OAAO,EAAE,IAAI;UACbkD,OAAO,EAAEtD,QAAQ,CAACsD,OAAO,IAAI,qCAAqC;UAClEjD,IAAI,EAAEL,QAAQ,CAACK;QACjB,CAAC;MACH;MAEA,MAAM,IAAIkD,KAAK,CAAC,EAAAK,gBAAA,GAAA5D,QAAQ,CAACO,KAAK,cAAAqD,gBAAA,uBAAdA,gBAAA,CAAgBN,OAAO,KAAI,qBAAqB,CAAC;IACnE,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB;MACA,IAAIA,KAAK,CAACiD,MAAM,IAAIjD,KAAK,CAACF,IAAI,EAAE;QAAA,IAAAyD,YAAA,EAAAC,kBAAA;QAC9B,MAAM,IAAIR,KAAK,CAAC,EAAAO,YAAA,GAAAvD,KAAK,CAACF,IAAI,cAAAyD,YAAA,wBAAAC,kBAAA,GAAVD,YAAA,CAAYvD,KAAK,cAAAwD,kBAAA,uBAAjBA,kBAAA,CAAmBT,OAAO,KAAI/C,KAAK,CAAC+C,OAAO,IAAI,qBAAqB,CAAC;MACvF;MACA,MAAM,IAAIC,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,qBAAqB,CAAC;IACzD;EACF;;EAEA;AACF;AACA;EACE,aAAaU,SAASA,CAAC3D,IAAyB,EAAoC;IAClF,IAAI;MAAA,IAAA4D,gBAAA;MACF,MAAMjE,QAAQ,GAAG,MAAMT,UAAU,CAACqB,IAAI,CAGpCnB,aAAa,CAACS,IAAI,CAACgE,UAAU,EAC7B7D,IACF,CAAC;;MAED;MACA,IAAIL,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC,OAAO;UACLD,OAAO,EAAE,IAAI;UACbkD,OAAO,EAAEtD,QAAQ,CAACsD,OAAO,IAAI,6BAA6B;UAC1DjD,IAAI,EAAEL,QAAQ,CAACK;QACjB,CAAC;MACH;MAEA,MAAM,IAAIkD,KAAK,CAAC,EAAAU,gBAAA,GAAAjE,QAAQ,CAACO,KAAK,cAAA0D,gBAAA,uBAAdA,gBAAA,CAAgBX,OAAO,KAAI,yBAAyB,CAAC;IACvE,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB;MACA,IAAIA,KAAK,CAACiD,MAAM,IAAIjD,KAAK,CAACF,IAAI,EAAE;QAAA,IAAA8D,YAAA,EAAAC,kBAAA;QAC9B,MAAM,IAAIb,KAAK,CAAC,EAAAY,YAAA,GAAA5D,KAAK,CAACF,IAAI,cAAA8D,YAAA,wBAAAC,kBAAA,GAAVD,YAAA,CAAY5D,KAAK,cAAA6D,kBAAA,uBAAjBA,kBAAA,CAAmBd,OAAO,KAAI/C,KAAK,CAAC+C,OAAO,IAAI,yBAAyB,CAAC;MAC3F;MACA,MAAM,IAAIC,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF;;EAEA;AACF;AACA;EACE,aAAae,SAASA,CAAClD,KAAa,EAAwB;IAC1D,IAAI;MAAA,IAAAmD,gBAAA;MACF,MAAMtE,QAAQ,GAAG,MAAMT,UAAU,CAACqB,IAAI,CACpCnB,aAAa,CAACS,IAAI,CAACqE,UAAU,EAC7B;QAAEpD;MAAM,CACV,CAAC;MAED,IAAInB,QAAQ,CAACI,OAAO,EAAE;QACpB,OAAOJ,QAAQ;MACjB;MAEA,MAAM,IAAIuD,KAAK,CAAC,EAAAe,gBAAA,GAAAtE,QAAQ,CAACO,KAAK,cAAA+D,gBAAA,uBAAdA,gBAAA,CAAgBhB,OAAO,KAAI,sBAAsB,CAAC;IACpE,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB;MACA,IAAIA,KAAK,CAACiD,MAAM,IAAIjD,KAAK,CAACF,IAAI,EAAE;QAAA,IAAAmE,YAAA,EAAAC,kBAAA;QAC9B,MAAM,IAAIlB,KAAK,CAAC,EAAAiB,YAAA,GAAAjE,KAAK,CAACF,IAAI,cAAAmE,YAAA,wBAAAC,kBAAA,GAAVD,YAAA,CAAYjE,KAAK,cAAAkE,kBAAA,uBAAjBA,kBAAA,CAAmBnB,OAAO,KAAI/C,KAAK,CAAC+C,OAAO,IAAI,sBAAsB,CAAC;MACxF;MACA,MAAM,IAAIC,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAaL,YAAYA,CAAA,EAA0B;IACjD,IAAI;MAAA,IAAAyB,gBAAA;MACF,MAAM1E,QAAQ,GAAG,MAAMT,UAAU,CAACqB,IAAI,CAMpCnB,aAAa,CAACS,IAAI,CAACyE,OACrB,CAAC;MAED,IAAI3E,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC;QACAb,YAAY,CAACuD,QAAQ,CAAC/C,QAAQ,CAACK,IAAI,CAAC2C,WAAW,CAAC;QAChD,IAAIhD,QAAQ,CAACK,IAAI,CAAC4C,YAAY,EAAE;UAC9BC,YAAY,CAACC,OAAO,CAACxD,iBAAiB,EAAEK,QAAQ,CAACK,IAAI,CAAC4C,YAAY,CAAC;QACrE;QAEA,OAAO;UACL7C,OAAO,EAAE,IAAI;UACbkD,OAAO,EAAEtD,QAAQ,CAACsD,OAAO;UACzBjD,IAAI,EAAEL,QAAQ,CAACK;QACjB,CAAC;MACH;MAEA,MAAM,IAAIkD,KAAK,CAAC,EAAAmB,gBAAA,GAAA1E,QAAQ,CAACO,KAAK,cAAAmE,gBAAA,uBAAdA,gBAAA,CAAgBpB,OAAO,KAAI,sBAAsB,CAAC;IACpE,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB,MAAM,IAAIgD,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAasB,UAAUA,CAAA,EAAkB;IACvC,IAAI;MAAA,IAAAC,gBAAA;MACF,MAAM7E,QAAQ,GAAG,MAAMT,UAAU,CAACU,GAAG,CACnCR,aAAa,CAACS,IAAI,CAACC,OACrB,CAAC;MAED,IAAIH,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC;QACA6C,YAAY,CAACC,OAAO,CAACzD,aAAa,EAAE0D,IAAI,CAACC,SAAS,CAACrD,QAAQ,CAACK,IAAI,CAACC,IAAI,CAAC,CAAC;QACvE,OAAON,QAAQ,CAACK,IAAI,CAACC,IAAI;MAC3B;MAEA,MAAM,IAAIiD,KAAK,CAAC,EAAAsB,gBAAA,GAAA7E,QAAQ,CAACO,KAAK,cAAAsE,gBAAA,uBAAdA,gBAAA,CAAgBvB,OAAO,KAAI,uBAAuB,CAAC;IACrE,CAAC,CAAC,OAAO/C,KAAU,EAAE;MACnB,MAAM,IAAIgD,KAAK,CAAChD,KAAK,CAAC+C,OAAO,IAAI,uBAAuB,CAAC;IAC3D;EACF;;EAEA;AACF;AACA;EACE,aAAawB,MAAMA,CAAA,EAAkB;IACnC,IAAI;MACFtE,OAAO,CAACuE,GAAG,CAAC,iDAAiD,CAAC;MAC9D;MACA,MAAMxF,UAAU,CAACqB,IAAI,CAACnB,aAAa,CAACS,IAAI,CAAC8E,MAAM,CAAC;MAChDxE,OAAO,CAACuE,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,OAAOxE,KAAK,EAAE;MACd;MACAC,OAAO,CAACyE,IAAI,CAAC,sEAAsE,EAAE1E,KAAK,CAAC;IAC7F,CAAC,SAAS;MACR;MACAC,OAAO,CAACuE,GAAG,CAAC,6CAA6C,CAAC;MAC1D,IAAI,CAACG,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACE,OAAOA,iBAAiBA,CAAA,EAAS;IAC/B1E,OAAO,CAACuE,GAAG,CAAC,mDAAmD,CAAC;;IAEhE;IACAvF,YAAY,CAAC2F,WAAW,CAAC,CAAC;;IAE1B;IACAjC,YAAY,CAACkC,UAAU,CAAC1F,aAAa,CAAC;IACtCwD,YAAY,CAACkC,UAAU,CAACzF,iBAAiB,CAAC;;IAE1C;IACA,MAAM0F,QAAQ,GAAG,CACf,iBAAiB,EACjB,gBAAgB,EAChB,oBAAoB,EACpB,YAAY,EACZ,WAAW,EACX,eAAe,CAChB;IACDA,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAI;MACtBrC,YAAY,CAACkC,UAAU,CAACG,GAAG,CAAC;MAC5B/E,OAAO,CAACuE,GAAG,CAAC,gBAAgBQ,GAAG,EAAE,CAAC;IACpC,CAAC,CAAC;;IAEF;IACAC,cAAc,CAACC,KAAK,CAAC,CAAC;IAEtBjF,OAAO,CAACuE,GAAG,CAAC,iDAAiD,CAAC;EAChE;;EAEA;AACF;AACA;EACE,OAAOW,eAAeA,CAAA,EAAY;IAChC,MAAM5F,KAAK,GAAGN,YAAY,CAACO,QAAQ,CAAC,CAAC;IACrC,MAAM4F,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,OAAO,CAAC,EAAE9F,KAAK,IAAI6F,QAAQ,CAAC;EAC9B;;EAEA;AACF;AACA;EACE,OAAOC,aAAaA,CAAA,EAAgB;IAClC,IAAI;MACF,MAAMD,QAAQ,GAAGzC,YAAY,CAAC2C,OAAO,CAACnG,aAAa,CAAC;MACpD,OAAOiG,QAAQ,GAAGvC,IAAI,CAAC0C,KAAK,CAACH,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAawF,eAAeA,CAAA,EAAqB;IAC/C;IACAvF,OAAO,CAACuE,GAAG,CAAC,+DAA+D,CAAC;IAC5E,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,OAAOiB,WAAWA,CAAA,EAA+B;IAC/C,MAAM1F,IAAI,GAAG,IAAI,CAACsF,aAAa,CAAC,CAAC;IACjC,OAAO,CAAAtF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,IAAI,KAAI,IAAI;EAC3B;;EAEA;AACF;AACA;EACE,OAAO6E,OAAOA,CAAC7E,IAAyB,EAAW;IACjD,OAAO,IAAI,CAAC4E,WAAW,CAAC,CAAC,KAAK5E,IAAI;EACpC;AACF;AAEA,eAAexB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}