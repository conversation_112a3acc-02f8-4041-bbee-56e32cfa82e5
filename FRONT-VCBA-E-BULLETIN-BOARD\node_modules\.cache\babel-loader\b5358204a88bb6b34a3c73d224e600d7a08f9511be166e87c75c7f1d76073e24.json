{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"ellipse\", {\n  cx: \"12\",\n  cy: \"5\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"msslwz\"\n}], [\"path\", {\n  d: \"M3 5v14a9 3 0 0 0 18 0V5\",\n  key: \"aqi0yr\"\n}]];\nconst Cylinder = createLucideIcon(\"cylinder\", __iconNode);\nexport { __iconNode, Cylinder as default };\n//# sourceMappingURL=cylinder.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}