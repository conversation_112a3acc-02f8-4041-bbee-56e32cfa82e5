{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{getImageUrl}from'../../config/constants';// Custom hook for CORS-safe image loading\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);setLoading(false);setError(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}console.log('🔄 Loading image via CORS-safe method:',fullUrl);const response=await fetch(fullUrl,{method:'GET',headers:{'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);console.log('✅ Image loaded successfully via CORS-safe method');setImageUrl(objectUrl);}catch(err){console.error('❌ Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// Reusable CORS-safe image component\nconst ImageDisplay=_ref=>{let{imagePath,alt,style,className,onLoad,onMouseEnter,onMouseLeave}=_ref;const{imageUrl,loading,error}=useImageLoader(imagePath);if(loading){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Loading...\"})]})});}if(error||!imageUrl){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Image loading...\"})]})});}return/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:alt,style:style,className:className,onLoad:e=>{console.log('✅ Image rendered successfully via CORS-safe method');onLoad===null||onLoad===void 0?void 0:onLoad(e);},onMouseEnter:onMouseEnter,onMouseLeave:onMouseLeave});};// Facebook-style image gallery component\nconst FacebookImageGallery=_ref2=>{let{images,altPrefix,maxVisible=4,onImageClick}=_ref2;if(!images||images.length===0)return null;const visibleImages=images.slice(0,maxVisible);const remainingCount=images.length-maxVisible;const getImageStyle=(index,total)=>{const baseStyle={width:'100%',height:'100%',objectFit:'cover',cursor:'pointer',transition:'transform 0.2s ease, filter 0.2s ease',borderRadius:index===0&&total===1?'12px':'8px'};return baseStyle;};const getContainerStyle=(index,total)=>{const baseStyle={position:'relative',overflow:'hidden',backgroundColor:'#f8fafc',borderRadius:index===0&&total===1?'12px':'8px'};if(total===1){return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'300px'});}if(total===2){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'200px'});}if(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'60%',height:'250px'});}return _objectSpread(_objectSpread({},baseStyle),{},{height:total===3&&index===1?'250px':'120px'});};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'4px',width:'100%',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(0,visibleImages.length),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:visibleImages[0],alt:\"\".concat(altPrefix,\" - Image 1\"),style:getImageStyle(0,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(0)})]}),visibleImages.length>1&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'4px',width:visibleImages.length===2?'50%':'40%'},children:visibleImages.slice(1).map((image,idx)=>{const actualIndex=idx+1;return/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(actualIndex,visibleImages.length),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:image,alt:\"\".concat(altPrefix,\" - Image \").concat(actualIndex+1),style:getImageStyle(actualIndex,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),actualIndex===maxVisible-1&&remainingCount>0&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'1.5rem',fontWeight:'600',borderRadius:'8px'},children:[\"+\",remainingCount]}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(actualIndex)})]},actualIndex);})})]});};export default FacebookImageGallery;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}