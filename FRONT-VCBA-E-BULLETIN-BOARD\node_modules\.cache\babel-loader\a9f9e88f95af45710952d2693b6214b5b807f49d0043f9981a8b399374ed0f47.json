{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13 21-3-3 3-3\",\n  key: \"s3o1nf\"\n}], [\"path\", {\n  d: \"M20 18H10\",\n  key: \"14r3mt\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"v9paqo\"\n}]];\nconst DecimalsArrowLeft = createLucideIcon(\"decimals-arrow-left\", __iconNode);\nexport { __iconNode, DecimalsArrowLeft as default };\n//# sourceMappingURL=decimals-arrow-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}