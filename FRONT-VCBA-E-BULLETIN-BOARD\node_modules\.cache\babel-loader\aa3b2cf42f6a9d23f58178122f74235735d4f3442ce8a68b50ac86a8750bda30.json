{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 18v-6H5l7-7 7 7h-4v6H9z\",\n  key: \"1x06kx\"\n}]];\nconst ArrowBigUp = createLucideIcon(\"arrow-big-up\", __iconNode);\nexport { __iconNode, ArrowBigUp as default };\n//# sourceMappingURL=arrow-big-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}