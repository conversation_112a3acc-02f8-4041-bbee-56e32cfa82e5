{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12H9\",\n  key: \"dn1m92\"\n}], [\"path\", {\n  d: \"M21 18H7\",\n  key: \"1ygte8\"\n}], [\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}]];\nconst AlignRight = createLucideIcon(\"align-right\", __iconNode);\nexport { __iconNode, AlignRight as default };\n//# sourceMappingURL=align-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}