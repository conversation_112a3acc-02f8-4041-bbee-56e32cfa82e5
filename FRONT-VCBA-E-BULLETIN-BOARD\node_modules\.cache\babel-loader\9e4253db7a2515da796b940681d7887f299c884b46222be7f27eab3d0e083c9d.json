{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2a6 6 0 0 0 1.2 3.6l.6.8A6 6 0 0 1 17 13v8a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1v-8a6 6 0 0 1 1.2-3.6l.6-.8A6 6 0 0 0 10 5z\",\n  key: \"blqgoc\"\n}], [\"path\", {\n  d: \"M17 13h-4a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h4\",\n  key: \"43jbee\"\n}]];\nconst BottleWine = createLucideIcon(\"bottle-wine\", __iconNode);\nexport { __iconNode, BottleWine as default };\n//# sourceMappingURL=bottle-wine.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}