{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"path\", {\n  d: \"M8 12a2 2 0 0 0 2-2V8H8\",\n  key: \"1jfesj\"\n}], [\"path\", {\n  d: \"M14 12a2 2 0 0 0 2-2V8h-2\",\n  key: \"1dq9mh\"\n}]];\nconst MessageSquareQuote = createLucideIcon(\"message-square-quote\", __iconNode);\nexport { __iconNode, MessageSquareQuote as default };\n//# sourceMappingURL=message-square-quote.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}