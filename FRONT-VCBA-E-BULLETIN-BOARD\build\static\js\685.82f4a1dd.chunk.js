"use strict";(self.webpackChunkfront_vcba_e_bulletin_board=self.webpackChunkfront_vcba_e_bulletin_board||[]).push([[685],{685:(e,t,n)=>{n.r(t),n.d(t,{CLSThresholds:()=>S,FCPThresholds:()=>P,INPThresholds:()=>q,LCPThresholds:()=>O,TTFBThresholds:()=>j,onCLS:()=>w,onFCP:()=>E,onINP:()=>H,onLCP:()=>W,onTTFB:()=>G});var i=n(379),r=n(705);let o=-1;const s=e=>{addEventListener("pageshow",t=>{t.persisted&&(o=t.timeStamp,e(t))},!0)},a=(e,t,n,i)=>{let r,o;return s=>{t.value>=0&&(s||i)&&(o=t.value-(null!==r&&void 0!==r?r:0),(o||void 0===r)&&(r=t.value,t.delta=o,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}},l=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},d=()=>{const e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},c=()=>{var e;const t=d();return null!==(e=null===t||void 0===t?void 0:t.activationStart)&&void 0!==e?e:0},h=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;const n=d();let i="navigate";return o>=0?i="back-forward-cache":n&&(document.prerendering||c()>0?i="prerender":document.wasDiscarded?i="restore":n.type&&(i=n.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:"v5-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},u=new WeakMap;function v(e,t){return u.get(e)||u.set(e,new t),u.get(e)}class m{constructor(){(0,r.A)(this,"t",void 0),(0,r.A)(this,"i",0),(0,r.A)(this,"o",[])}h(e){var t;if(e.hadRecentInput)return;const n=this.o[0],i=this.o.at(-1);this.i&&n&&i&&e.startTime-i.startTime<1e3&&e.startTime-n.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),null===(t=this.t)||void 0===t||t.call(this,e)}}const p=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return r.observe((0,i.A)({type:e,buffered:!0},n)),r}}catch(r){}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let g=-1;const T=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,b=e=>{"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===e.type?e.timeStamp:0,C())},y=()=>{addEventListener("visibilitychange",b,!0),addEventListener("prerenderingchange",b,!0)},C=()=>{removeEventListener("visibilitychange",b,!0),removeEventListener("prerenderingchange",b,!0)},A=()=>{if(g<0){var e;const t=c(),n=document.prerendering||null===(e=globalThis.performance.getEntriesByType("visibility-state").filter(e=>"hidden"===e.name&&e.startTime>t)[0])||void 0===e?void 0:e.startTime;g=null!==n&&void 0!==n?n:T(),y(),s(()=>{setTimeout(()=>{g=T(),y()})})}return{get firstHiddenTime(){return g}}},L=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},P=[1800,3e3],E=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};L(()=>{const n=A();let i,r=h("FCP");const o=p("paint",e=>{for(const t of e)"first-contentful-paint"===t.name&&(o.disconnect(),t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-c(),0),r.entries.push(t),i(!0)))});o&&(i=a(e,r,P,t.reportAllChanges),s(n=>{r=h("FCP"),i=a(e,r,P,t.reportAllChanges),l(()=>{r.value=performance.now()-n.timeStamp,i(!0)})}))})},S=[.1,.25],w=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};E(f(()=>{let n,i=h("CLS",0);const r=v(t,m),o=e=>{for(const t of e)r.h(t);r.i>i.value&&(i.value=r.i,i.entries=r.o,n())},d=p("layout-shift",o);d&&(n=a(e,i,S,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(o(d.takeRecords()),n(!0))}),s(()=>{r.i=0,i=h("CLS",0),n=a(e,i,S,t.reportAllChanges),l(()=>n())}),setTimeout(n))}))};let I=0,k=1/0,M=0;const F=e=>{for(const t of e)t.interactionId&&(k=Math.min(k,t.interactionId),M=Math.max(M,t.interactionId),I=M?(M-k)/7+1:0)};let _;const B=()=>{var e;return _?I:null!==(e=performance.interactionCount)&&void 0!==e?e:0};let x=0;class N{constructor(){(0,r.A)(this,"u",[]),(0,r.A)(this,"l",new Map),(0,r.A)(this,"m",void 0),(0,r.A)(this,"v",void 0)}p(){x=B(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((B()-x)/50));return this.u[e]}h(e){var t;if(null!==(t=this.m)&&void 0!==t&&t.call(this,e),!e.interactionId&&"first-input"!==e.entryType)return;const n=this.u.at(-1);let i=this.l.get(e.interactionId);if(i||this.u.length<10||e.duration>n.T){var r;if(i?e.duration>i.T?(i.entries=[e],i.T=e.duration):e.duration===i.T&&e.startTime===i.entries[0].startTime&&i.entries.push(e):(i={id:e.interactionId,entries:[e],T:e.duration},this.l.set(i.id,i),this.u.push(i)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}null===(r=this.v)||void 0===r||r.call(this,i)}}}const R=e=>{const t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(e=f(e),document.addEventListener("visibilitychange",e,{once:!0}),t(()=>{e(),document.removeEventListener("visibilitychange",e)}))},q=[200,500],H=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&L(()=>{var n;"interactionCount"in performance||_||(_=p("event",F,{type:"event",buffered:!0,durationThreshold:0}));let i,r=h("INP");const o=v(t,N),l=e=>{R(()=>{for(const n of e)o.h(n);const t=o.P();t&&t.T!==r.value&&(r.value=t.T,r.entries=t.entries,i())})},d=p("event",l,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});i=a(e,r,q,t.reportAllChanges),d&&(d.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(l(d.takeRecords()),i(!0))}),s(()=>{o.p(),r=h("INP"),i=a(e,r,q,t.reportAllChanges)}))})};class D{constructor(){(0,r.A)(this,"m",void 0)}h(e){var t;null===(t=this.m)||void 0===t||t.call(this,e)}}const O=[2500,4e3],W=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};L(()=>{const n=A();let i,r=h("LCP");const o=v(t,D),d=e=>{t.reportAllChanges||(e=e.slice(-1));for(const t of e)o.h(t),t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-c(),0),r.entries=[t],i())},u=p("largest-contentful-paint",d);if(u){i=a(e,r,O,t.reportAllChanges);const n=f(()=>{d(u.takeRecords()),u.disconnect(),i(!0)});for(const e of["keydown","click","visibilitychange"])addEventListener(e,()=>R(n),{capture:!0,once:!0});s(n=>{r=h("LCP"),i=a(e,r,O,t.reportAllChanges),l(()=>{r.value=performance.now()-n.timeStamp,i(!0)})})}})},j=[800,1800],z=e=>{document.prerendering?L(()=>z(e)):"complete"!==document.readyState?addEventListener("load",()=>z(e),!0):setTimeout(e)},G=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=h("TTFB"),i=a(e,n,j,t.reportAllChanges);z(()=>{const r=d();r&&(n.value=Math.max(r.responseStart-c(),0),n.entries=[r],i(!0),s(()=>{n=h("TTFB",0),i=a(e,n,j,t.reportAllChanges),i(!0)}))})}}}]);