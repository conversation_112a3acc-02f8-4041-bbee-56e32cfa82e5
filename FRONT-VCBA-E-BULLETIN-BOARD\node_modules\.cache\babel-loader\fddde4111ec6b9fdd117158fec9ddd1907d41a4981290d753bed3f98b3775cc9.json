{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.752 11.901A7.78 7.78 0 0 0 20 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 1.202 0 19 19 0 0 0 .09-.077\",\n  key: \"y0ewhp\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}], [\"path\", {\n  d: \"m21.5 15.5-5 5\",\n  key: \"11iqnx\"\n}], [\"path\", {\n  d: \"m21.5 20.5-5-5\",\n  key: \"1bylgx\"\n}]];\nconst MapPinX = createLucideIcon(\"map-pin-x\", __iconNode);\nexport { __iconNode, MapPinX as default };\n//# sourceMappingURL=map-pin-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}