{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 14.772-.383.923\",\n  key: \"11vil6\"\n}], [\"path\", {\n  d: \"M13.148 14.772a3 3 0 1 0-2.296-5.544l-.383-.923\",\n  key: \"1v3clb\"\n}], [\"path\", {\n  d: \"m13.148 9.228.383-.923\",\n  key: \"t2zzyc\"\n}], [\"path\", {\n  d: \"m13.53 15.696-.382-.924a3 3 0 1 1-2.296-5.544\",\n  key: \"1bxfiv\"\n}], [\"path\", {\n  d: \"m14.772 10.852.923-.383\",\n  key: \"k9m8cz\"\n}], [\"path\", {\n  d: \"m14.772 13.148.923.383\",\n  key: \"1xvhww\"\n}], [\"path\", {\n  d: \"M4.5 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-.5\",\n  key: \"tn8das\"\n}], [\"path\", {\n  d: \"M4.5 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-.5\",\n  key: \"1g2pve\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"M6 6h.01\",\n  key: \"1utrut\"\n}], [\"path\", {\n  d: \"m9.228 10.852-.923-.383\",\n  key: \"1wtb30\"\n}], [\"path\", {\n  d: \"m9.228 13.148-.923.383\",\n  key: \"1a830x\"\n}]];\nconst ServerCog = createLucideIcon(\"server-cog\", __iconNode);\nexport { __iconNode, ServerCog as default };\n//# sourceMappingURL=server-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}