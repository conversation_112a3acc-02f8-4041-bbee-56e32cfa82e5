{"ast": null, "code": "import React,{useState,useRef,useCallback}from'react';import{Upload,X,Camera,AlertCircle,CheckCircle}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProfilePictureUpload=_ref=>{let{currentPicture,userInitials='U',onUpload,onRemove,isLoading=false,className=''}=_ref;const[preview,setPreview]=useState(null);const[isDragOver,setIsDragOver]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);const fileInputRef=useRef(null);// File validation\nconst validateFile=useCallback(file=>{const maxSize=2*1024*1024;// 2MB\nconst allowedTypes=['image/jpeg','image/jpg','image/png','image/webp'];if(!allowedTypes.includes(file.type)){return'Please select a valid image file (JPEG, PNG, or WebP)';}if(file.size>maxSize){return'File size must be less than 2MB';}return null;},[]);// Handle file selection\nconst handleFileSelect=useCallback(async file=>{setError(null);setSuccess(null);const validationError=validateFile(file);if(validationError){setError(validationError);return;}// Create preview\nconst reader=new FileReader();reader.onload=e=>{var _e$target;setPreview((_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result);};reader.onerror=()=>{setError('Failed to read file');};reader.readAsDataURL(file);try{await onUpload(file);setSuccess('Profile picture updated successfully!');setTimeout(()=>setSuccess(null),3000);}catch(err){setError(err.message||'Failed to upload profile picture');setPreview(null);}},[validateFile,onUpload]);// Handle file input change\nconst handleInputChange=e=>{var _e$target$files;const file=(_e$target$files=e.target.files)===null||_e$target$files===void 0?void 0:_e$target$files[0];if(file){handleFileSelect(file);}};// Handle drag and drop\nconst handleDragOver=e=>{e.preventDefault();setIsDragOver(true);};const handleDragLeave=e=>{e.preventDefault();setIsDragOver(false);};const handleDrop=e=>{e.preventDefault();setIsDragOver(false);const file=e.dataTransfer.files[0];if(file){handleFileSelect(file);}};// Handle remove\nconst handleRemove=async()=>{setError(null);setSuccess(null);setPreview(null);try{await onRemove();setSuccess('Profile picture removed successfully!');setTimeout(()=>setSuccess(null),3000);}catch(err){setError(err.message||'Failed to remove profile picture');}};// Get display image\nconst displayImage=preview||currentPicture;const hasImage=Boolean(displayImage);return/*#__PURE__*/_jsxs(\"div\",{className:\"profile-picture-upload \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',width:'100px',height:'100px',borderRadius:'50%',overflow:'hidden',border:isDragOver?'3px dashed #22c55e':'3px solid #e8f5e8',transition:'border-color 0.2s ease',cursor:'pointer'},onDragOver:handleDragOver,onDragLeave:handleDragLeave,onDrop:handleDrop,onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},children:[hasImage?/*#__PURE__*/_jsx(\"img\",{src:displayImage,alt:\"Profile\",style:{width:'100%',height:'100%',objectFit:'cover'}}):/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'100%',background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'700',fontSize:'2rem'},children:userInitials}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',opacity:isDragOver?1:0,transition:'opacity 0.2s ease'},children:/*#__PURE__*/_jsx(Camera,{size:24,color:\"white\"})}),isLoading&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'rgba(255, 255, 255, 0.8)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'24px',height:'24px',border:'2px solid #e8f5e8',borderTop:'2px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _fileInputRef$current2;return(_fileInputRef$current2=fileInputRef.current)===null||_fileInputRef$current2===void 0?void 0:_fileInputRef$current2.click();},disabled:isLoading,style:{background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontWeight:'600',cursor:isLoading?'not-allowed':'pointer',opacity:isLoading?0.6:1,display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Upload,{size:16}),hasImage?'Change Photo':'Upload Photo']}),hasImage&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleRemove,disabled:isLoading,style:{background:'none',border:'1px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:isLoading?'not-allowed':'pointer',color:'#6b7280',opacity:isLoading?0.6:1,display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(X,{size:16}),\"Remove\"]})]}),/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\"image/jpeg,image/jpg,image/png,image/webp\",onChange:handleInputChange,style:{display:'none'}}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',color:'#6b7280',margin:0},children:\"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"})]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'0.75rem',background:'#fef2f2',border:'1px solid #fecaca',borderRadius:'8px',color:'#dc2626',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:16}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'0.75rem',background:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'8px',color:'#16a34a',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16}),success]}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n      \"})]});};export default ProfilePictureUpload;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}