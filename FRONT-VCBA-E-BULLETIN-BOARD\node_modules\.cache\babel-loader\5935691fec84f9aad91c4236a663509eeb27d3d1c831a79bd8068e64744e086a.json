{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13.69 12.479 1.29 4.88a.5.5 0 0 1-.697.591l-1.844-.849a1 1 0 0 0-.88.001l-1.846.85a.5.5 0 0 1-.693-.593l1.29-4.88\",\n  key: \"7a4gmr\"\n}], [\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z\",\n  key: \"1mlx9k\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}]];\nconst FileBadge2 = createLucideIcon(\"file-badge-2\", __iconNode);\nexport { __iconNode, FileBadge2 as default };\n//# sourceMappingURL=file-badge-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}