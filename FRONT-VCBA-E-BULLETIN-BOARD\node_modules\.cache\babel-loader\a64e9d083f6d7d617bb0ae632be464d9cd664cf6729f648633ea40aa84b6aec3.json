{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Calendar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\nimport { calendarService } from '../../services/calendarService';\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon, AlertTriangle, MessageCircle, Star } from 'lucide-react';\nimport CalendarEventLikeButton from '../../components/common/CalendarEventLikeButton';\nimport HolidayManagement from '../../components/admin/HolidayManagement';\n// Removed calendar attachment imports since this feature is not yet implemented\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Calendar = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  // Add CSS animation for spinning refresh icon\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes spin {\n        from { transform: rotate(0deg); }\n        to { transform: rotate(360deg); }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n  const [currentDate, setCurrentDate] = useState(() => new Date());\n  const [selectedDate, setSelectedDate] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [editingEvent, setEditingEvent] = useState(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [saving, setSaving] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [showHolidayManagement, setShowHolidayManagement] = useState(false);\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n\n  // Event attachments state - disabled until backend implementation is complete\n  // const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\n\n  // Use the calendar hook\n  const {\n    events,\n    loading,\n    error,\n    createEvent,\n    updateEvent,\n    getEventsForDate,\n    refresh\n  } = useCalendar(currentDate);\n  const {\n    categories\n  } = useCalendarCategories();\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage || error) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage, error]);\n  const handleCreateEvent = useCallback(date => {\n    setEditingEvent(null);\n    setSelectedDate(date || null);\n    setShowModal(true);\n  }, []);\n  const handleEditEvent = useCallback(event => {\n    setEditingEvent(event);\n    setShowModal(true);\n  }, []);\n  const handleSaveEvent = useCallback(async (data, applyPendingDeletes, onComplete) => {\n    setSaving(true);\n    try {\n      if (editingEvent) {\n        await updateEvent(editingEvent.calendar_id, data);\n\n        // Apply pending image deletions AFTER successful update\n        if (applyPendingDeletes) {\n          console.log('🗑️ Applying pending image deletions after successful update');\n          await applyPendingDeletes();\n        }\n        setSuccessMessage('Event updated successfully! Calendar refreshed.');\n      } else {\n        await createEvent(data);\n        setSuccessMessage('Event created successfully! Calendar refreshed.');\n      }\n\n      // Execute completion callback for additional operations\n      if (onComplete) {\n        await onComplete();\n      }\n\n      // Force refresh the calendar to ensure immediate update\n      console.log('🔄 Refreshing calendar to show updated events...');\n      setRefreshing(true);\n      await refresh();\n      setRefreshing(false);\n\n      // Small delay to ensure smooth UI transition\n      setTimeout(() => {\n        setShowModal(false);\n        setEditingEvent(null);\n        setSelectedDate(null);\n        setSaving(false);\n      }, 100);\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to save event');\n      setSaving(false);\n    }\n  }, [editingEvent, updateEvent, createEvent, refresh]);\n  const handleCloseModal = useCallback(() => {\n    setShowModal(false);\n    setEditingEvent(null);\n    setSelectedDate(null);\n  }, []);\n\n  // Event management functions\n  const handlePublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.publishEvent(eventId);\n      setSuccessMessage('Event published successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to publish event');\n    }\n  }, [refresh]);\n  const handleUnpublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.unpublishEvent(eventId);\n      setSuccessMessage('Event unpublished successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to unpublish event');\n    }\n  }, [refresh]);\n  const handleDeleteEvent = useCallback(async eventId => {\n    // Use window.confirm to avoid ESLint error\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await calendarService.softDeleteEvent(eventId);\n      setSuccessMessage('Event deleted successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to delete event');\n    }\n  }, [refresh]);\n  const navigateMonth = useCallback(direction => {\n    const newDate = new Date(currentDate);\n    if (direction === 'prev') {\n      newDate.setMonth(currentDate.getMonth() - 1);\n    } else {\n      newDate.setMonth(currentDate.getMonth() + 1);\n    }\n    setCurrentDate(newDate);\n  }, [currentDate]);\n  const goToToday = useCallback(() => {\n    setCurrentDate(new Date());\n  }, []);\n  const handleDateClick = useCallback(date => {\n    setSelectedDate(date);\n    handleCreateEvent(date);\n  }, [handleCreateEvent]);\n  const getEventTypeColor = useCallback(event => {\n    // Use category color if available, otherwise subcategory color, otherwise default\n    return event.category_color || event.subcategory_color || '#22c55e';\n  }, []);\n\n  // Helper function to format event duration\n  const getEventDuration = useCallback(event => {\n    if (!event.end_date || event.end_date === event.event_date) {\n      return 'Single day event';\n    }\n    const startDate = new Date(event.event_date);\n    const endDate = new Date(event.end_date);\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\n\n    return `${diffDays} day event`;\n  }, []);\n\n  // Helper function to get first two words from event title for calendar chip display\n  const getEventChipTitle = useCallback(title => {\n    const words = title.trim().split(/\\s+/);\n    return words.slice(0, 2).join(' ');\n  }, []);\n\n  // Memoize calendar days to prevent infinite re-renders\n  const days = useMemo(() => {\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\n\n  // Get unique events for the event list (deduplicate multi-day events)\n  const uniqueEvents = useMemo(() => {\n    const eventMap = new Map();\n    events.forEach(event => {\n      // Use calendar_id as the unique identifier\n      if (!eventMap.has(event.calendar_id)) {\n        eventMap.set(event.calendar_id, event);\n      }\n    });\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\n      // Sort by event_date, then by title\n      const dateA = new Date(a.event_date);\n      const dateB = new Date(b.event_date);\n      if (dateA.getTime() !== dateB.getTime()) {\n        return dateA.getTime() - dateB.getTime();\n      }\n      return a.title.localeCompare(b.title);\n    });\n\n    // Debug: Log deduplication results\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\n    return uniqueEventsList;\n  }, [events]);\n\n  // Filter events based on search term and holiday type (exclude holidays from the list)\n  const filteredEvents = useMemo(() => {\n    return uniqueEvents.filter(event => {\n      var _event$description, _event$category_id;\n      // Exclude holidays from the events list\n      const isNotHoliday = !event.is_holiday;\n      const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_event$description = event.description) === null || _event$description === void 0 ? void 0 : _event$description.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesCategory = !selectedCategory || ((_event$category_id = event.category_id) === null || _event$category_id === void 0 ? void 0 : _event$category_id.toString()) === selectedCategory;\n      return isNotHoliday && matchesSearch && matchesCategory;\n    });\n  }, [uniqueEvents, searchTerm, selectedCategory]);\n\n  // Pagination calculations\n  const totalItems = filteredEvents.length;\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, selectedCategory, itemsPerPage]);\n\n  // Fetch attachments for visible events - disabled until backend implementation is complete\n  // useEffect(() => {\n  //   const fetchAttachments = async () => {\n  //     if (!paginatedEvents || paginatedEvents.length === 0) return;\n  //     // Attachment functionality will be implemented later\n  //   };\n  //   fetchAttachments();\n  // }, [paginatedEvents]);\n\n  // Component to display event images - disabled until backend implementation is complete\n  const EventImages = ({\n    eventId\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '60px',\n        height: '60px',\n        backgroundColor: '#f3f4f6',\n        borderRadius: '6px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#9ca3af',\n        fontSize: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Component to display individual image thumbnail - disabled until backend implementation is complete\n  // const EventImageThumbnail: React.FC<{ attachment: any }> = ({ attachment }) => {\n  //   // This component will be implemented when the backend supports file attachments\n  //   return null;\n  // };\n\n  // Pagination component - Always visible\n  const PaginationControls = () => {\n    // Always show pagination controls, even for single page\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\n\n    const getPageNumbers = () => {\n      const pages = [];\n      const maxVisiblePages = 5;\n      if (effectiveTotalPages <= maxVisiblePages) {\n        for (let i = 1; i <= effectiveTotalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        if (effectiveCurrentPage <= 3) {\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\n        } else {\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\n        }\n      }\n      return pages;\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '2rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        border: '1px solid #e5e7eb',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"Show:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: itemsPerPage,\n          onChange: e => setItemsPerPage(Number(e.target.value)),\n          style: {\n            padding: '0.25rem 0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            fontSize: '0.875rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5,\n            children: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10,\n            children: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 20,\n            children: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 50,\n            children: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"per page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [\"Showing \", Math.max(totalItems > 0 ? startIndex + 1 : 0, 0), \"-\", Math.min(endIndex, totalItems), \" of \", totalItems, \" events\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.25rem',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(1),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"First\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.max(1, prev - 1)),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), getPageNumbers().map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => typeof page === 'number' && setCurrentPage(page),\n          disabled: page === '...',\n          style: {\n            padding: '0.5rem 0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\n            color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\n            cursor: page === '...' ? 'default' : 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: page === effectiveCurrentPage ? '600' : '400'\n          },\n          children: page\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1)),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(effectiveTotalPages),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Last\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    \"data-calendar-component\": \"main\",\n    children: [successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        color: '#166534',\n        borderRadius: '8px'\n      },\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '8px'\n      },\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1rem',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n        border: '1px solid #e8f5e8',\n        position: 'relative',\n        zIndex: 10\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '1.25rem',\n              fontWeight: '600',\n              color: '#2d5016',\n              margin: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                size: 16,\n                color: \"#1e40af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this), \"School Calendar\", refreshing && /*#__PURE__*/_jsxDEV(RefreshCw, {\n                size: 14,\n                color: \"#22c55e\",\n                style: {\n                  animation: 'spin 1s linear infinite',\n                  marginLeft: '0.25rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280',\n              margin: '0 0 0 0.5rem',\n              fontSize: '0.875rem'\n            },\n            children: [getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('prev'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToToday,\n            style: {\n              padding: '0.25rem 0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('next'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowHolidayManagement(true),\n            style: {\n              display: 'inline-flex',\n              alignItems: 'center',\n              padding: '0.5rem 1rem',\n              background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n              gap: '0.5rem'\n            },\n            onMouseOver: e => {\n              e.currentTarget.style.transform = 'translateY(-1px)';\n              e.currentTarget.style.boxShadow = '0 2px 6px rgba(245, 158, 11, 0.2)';\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(Star, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this), \"Holidays\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCreateEvent(),\n            style: {\n              display: 'inline-flex',\n              alignItems: 'center',\n              padding: '0.5rem 1rem',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n            },\n            onMouseOver: e => {\n              e.currentTarget.style.transform = 'translateY(-1px)';\n              e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: \"+ Add Event\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)',\n          backgroundColor: '#f9fafb',\n          borderBottom: '1px solid #e5e7eb'\n        },\n        children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            textAlign: 'center',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#374151'\n          },\n          children: day\n        }, day, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)'\n        },\n        children: days.map((date, index) => {\n          const dayEvents = getEventsForDate(date);\n          const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\n          const isTodayDate = isToday(date);\n\n          // Create unique key based on date to prevent React key conflicts\n          // Use date timestamp as key since it's unique and stable across re-renders\n          const dateKey = `calendar-day-${date.getTime()}`;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              minHeight: '120px',\n              padding: '0.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb',\n              // Remove right border on last column\n              cursor: 'pointer',\n              backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\n              color: !isCurrentMonth ? '#9ca3af' : '#374151',\n              transition: 'all 0.2s ease',\n              position: 'relative'\n            },\n            onClick: () => handleDateClick(date),\n            onMouseOver: e => {\n              if (isCurrentMonth) {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n                e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\n              }\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                marginBottom: '0.25rem',\n                color: isTodayDate ? '#2563eb' : 'inherit'\n              },\n              children: date.getDate()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.25rem'\n              },\n              children: [dayEvents.slice(0, 3).map(event => {\n                // Determine styling for multi-day events\n                const isMultiDay = event.isMultiDay;\n                const isStart = event.isEventStart;\n                const isEnd = event.isEventEnd;\n                const isContinuation = isMultiDay && !isStart && !isEnd;\n                const eventColor = getEventTypeColor(event);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.75rem',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: isMultiDay ? isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px' : '6px',\n                    backgroundColor: eventColor + (isContinuation ? '25' : '15'),\n                    border: `1px solid ${eventColor}`,\n                    borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    cursor: 'pointer',\n                    position: 'relative',\n                    color: '#374151',\n                    fontWeight: '500',\n                    transition: 'all 0.2s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleEditEvent(event);\n                  },\n                  onMouseOver: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + '30';\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\n                  },\n                  onMouseOut: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  title: isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title,\n                  children: [isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25B6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 27\n                  }, this), isContinuation && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25AC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 27\n                  }, this), isEnd && isMultiDay && !isStart && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25C0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      flex: 1,\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis'\n                    },\n                    children: getEventChipTitle(event.title)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this), isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem',\n                      opacity: 0.7\n                    },\n                    children: \"\\u2192\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 27\n                  }, this)]\n                }, `event-${event.calendar_id}-${date.getTime()}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 23\n                }, this);\n              }), dayEvents.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280'\n                },\n                children: [\"+\", dayEvents.length - 3, \" more\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this)]\n          }, dateKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        margin: '2rem 0',\n        padding: '1.5rem',\n        backgroundColor: '#f8fafc'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1.5rem',\n          alignItems: 'end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Search Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              size: 16,\n              color: \"#9ca3af\",\n              style: {\n                position: 'absolute',\n                left: '0.75rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search events...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              },\n              onFocus: e => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              },\n              onBlur: e => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white',\n              transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\n              minHeight: '2.5rem',\n              lineHeight: '1.2'\n            },\n            onFocus: e => {\n              e.currentTarget.style.borderColor = '#22c55e';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n            },\n            onBlur: e => {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 15\n            }, this), categories.filter(category =>\n            // Hide holiday categories from dropdown\n            !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.category_id,\n              children: category.name\n            }, category.category_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refresh,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease',\n              height: '2.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 842,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#2d5016',\n            margin: 0\n          },\n          children: [\"Events for \", getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: [filteredEvents.length, \" school event\", filteredEvents.length !== 1 ? 's' : '', \" found\", /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              marginTop: '0.25rem',\n              fontStyle: 'italic'\n            },\n            children: \"Holidays are shown in calendar but not in this list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 24,\n          style: {\n            marginBottom: '1rem',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1024,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 11\n      }, this) : filteredEvents.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No events found for this month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleCreateEvent(),\n          style: {\n            marginTop: '1rem',\n            padding: '0.75rem 1.5rem',\n            backgroundColor: '#22c55e',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontWeight: '500'\n          },\n          children: \"Create First Event\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1028,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '1rem'\n        },\n        children: paginatedEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: '1px solid #e5e7eb',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            backgroundColor: 'white',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: '0 0 0.5rem 0',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [event.title, event.is_alert && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 25\n                  }, this), event.event_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 23\n                }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2192 \", event.end_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1100,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: '#f3f4f6',\n                    color: '#374151',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  },\n                  children: getEventDuration(event)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: getEventTypeColor(event),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem'\n                  },\n                  children: event.category_name || 'Uncategorized'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  margin: '0.5rem 0 0 0',\n                  lineHeight: '1.5'\n                },\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  marginTop: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CalendarEventLikeButton, {\n                  eventId: event.calendar_id,\n                  initialLiked: event.user_has_reacted || false,\n                  initialCount: event.reaction_count || 0,\n                  size: \"small\",\n                  onLikeChange: (liked, newCount) => {\n                    // Update the event in the local state if needed\n                    console.log(`Event ${event.calendar_id} like changed:`, {\n                      liked,\n                      newCount\n                    });\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1141,\n                  columnNumber: 23\n                }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1159,\n                    columnNumber: 27\n                  }, this), event.comment_count || 0, \" comments\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1152,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem',\n                marginLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditEvent(event),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Edit event\",\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => event.is_published ? handleUnpublishEvent(event.calendar_id) : handlePublishEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: event.is_published ? '#fef3c7' : '#dcfce7',\n                  color: event.is_published ? '#d97706' : '#16a34a',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: event.is_published ? 'Unpublish event' : 'Publish event',\n                children: event.is_published ? /*#__PURE__*/_jsxDEV(Clock, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 54\n                }, this) : /*#__PURE__*/_jsxDEV(Send, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 76\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#fef2f2',\n                  color: '#dc2626',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Delete event\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f3f4f6',\n              paddingTop: '1rem',\n              marginTop: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Event Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1235,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(EventImages, {\n              eventId: event.calendar_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 17\n          }, this)]\n        }, `unique-event-${event.calendar_id}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1052,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 985,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaginationControls, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CalendarEventModal, {\n      isOpen: showModal,\n      onClose: handleCloseModal,\n      onSave: handleSaveEvent,\n      event: editingEvent,\n      selectedDate: selectedDate,\n      loading: saving || loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1255,\n      columnNumber: 7\n    }, this), showHolidayManagement && /*#__PURE__*/_jsxDEV(HolidayManagement, {\n      onClose: () => {\n        setShowHolidayManagement(false);\n        // Refresh calendar events after holiday management\n        refresh();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1266,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 466,\n    columnNumber: 5\n  }, this);\n}, \"x1z4sbEuh+vBKXr1j36ja/vRxDo=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n})), \"x1z4sbEuh+vBKXr1j36ja/vRxDo=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n});\n_c2 = Calendar;\nCalendar.displayName = 'Calendar';\nexport default Calendar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Calendar$React.memo\");\n$RefreshReg$(_c2, \"Calendar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useCalendar", "useCalendarCategories", "getCalendarDays", "isToday", "isSameMonth", "getMonthName", "CalendarEventModal", "calendarService", "Calendar", "CalendarIcon", "Search", "RefreshCw", "Trash2", "Edit", "Send", "Clock", "Image", "ImageIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageCircle", "Star", "CalendarEventLikeButton", "HolidayManagement", "jsxDEV", "_jsxDEV", "_s", "memo", "_c", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "currentDate", "setCurrentDate", "Date", "selectedDate", "setSelectedDate", "showModal", "setShowModal", "editingEvent", "setEditingEvent", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "saving", "setSaving", "refreshing", "setRefreshing", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showHolidayManagement", "setShowHolidayManagement", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "events", "loading", "error", "createEvent", "updateEvent", "getEventsForDate", "refresh", "categories", "timer", "setTimeout", "clearTimeout", "handleCreateEvent", "date", "handleEditEvent", "event", "handleSaveEvent", "data", "applyPendingDeletes", "onComplete", "calendar_id", "console", "log", "message", "handleCloseModal", "handlePublishEvent", "eventId", "publishEvent", "handleUnpublishEvent", "unpublishEvent", "handleDeleteEvent", "window", "confirm", "softDeleteEvent", "navigateMonth", "direction", "newDate", "setMonth", "getMonth", "goToToday", "handleDateClick", "getEventTypeColor", "category_color", "subcategory_color", "getEventDuration", "end_date", "event_date", "startDate", "endDate", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "getEventChipTitle", "title", "words", "trim", "split", "slice", "join", "days", "getFullYear", "uniqueEvents", "eventMap", "Map", "for<PERSON>ach", "has", "set", "uniqueEventsList", "Array", "from", "values", "sort", "a", "b", "dateA", "dateB", "localeCompare", "length", "filteredEvents", "filter", "_event$description", "_event$category_id", "isNotHoliday", "is_holiday", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "category_id", "toString", "totalItems", "totalPages", "startIndex", "endIndex", "paginatedEvents", "EventImages", "width", "height", "backgroundColor", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontSize", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "PaginationControls", "effectiveTotalPages", "max", "effectiveCurrentPage", "getPageNumbers", "pages", "maxVisiblePages", "i", "push", "marginTop", "marginBottom", "padding", "border", "boxShadow", "gap", "value", "onChange", "e", "Number", "target", "min", "flexWrap", "onClick", "disabled", "cursor", "prev", "map", "page", "index", "fontWeight", "max<PERSON><PERSON><PERSON>", "margin", "background", "position", "zIndex", "animation", "marginLeft", "transition", "onMouseOver", "currentTarget", "onMouseOut", "transform", "overflow", "gridTemplateColumns", "borderBottom", "day", "textAlign", "dayEvents", "isCurrentMonth", "isTodayDate", "<PERSON><PERSON><PERSON>", "minHeight", "borderRight", "getDate", "flexDirection", "isMultiDay", "isStart", "isEventStart", "isEnd", "isEventEnd", "isContinuation", "eventColor", "borderLeft", "textOverflow", "whiteSpace", "stopPropagation", "originalStartDate", "originalEndDate", "flex", "opacity", "left", "top", "pointerEvents", "type", "placeholder", "outline", "onFocus", "borderColor", "onBlur", "lineHeight", "category", "name", "fontStyle", "is_alert", "className", "category_name", "initialLiked", "user_has_reacted", "initialCount", "reaction_count", "onLikeChange", "liked", "newCount", "allow_comments", "comment_count", "is_published", "borderTop", "paddingTop", "isOpen", "onClose", "onSave", "_c2", "displayName", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Calendar.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\r\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\r\nimport type { CalendarEvent, CreateEventData, UpdateEventData } from '../../types/calendar.types';\r\nimport { calendarService } from '../../services/calendarService';\r\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon, AlertTriangle, MessageCircle, Heart, Star, Repeat } from 'lucide-react';\r\nimport CalendarEventLikeButton from '../../components/common/CalendarEventLikeButton';\r\nimport HolidayManagement from '../../components/admin/HolidayManagement';\r\n// Removed calendar attachment imports since this feature is not yet implemented\r\n\r\nconst Calendar: React.FC = React.memo(() => {\r\n  // Add CSS animation for spinning refresh icon\r\n  React.useEffect(() => {\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      @keyframes spin {\r\n        from { transform: rotate(0deg); }\r\n        to { transform: rotate(360deg); }\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n    return () => {\r\n      document.head.removeChild(style);\r\n    };\r\n  }, []);\r\n  const [currentDate, setCurrentDate] = useState(() => new Date());\r\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [saving, setSaving] = useState(false);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n  const [showHolidayManagement, setShowHolidayManagement] = useState(false);\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(10);\r\n\r\n  // Event attachments state - disabled until backend implementation is complete\r\n  // const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\r\n\r\n  // Use the calendar hook\r\n  const {\r\n    events,\r\n    loading,\r\n    error,\r\n    createEvent,\r\n    updateEvent,\r\n    getEventsForDate,\r\n    refresh\r\n  } = useCalendar(currentDate);\r\n\r\n  const { categories } = useCalendarCategories();\r\n\r\n  // Clear messages after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage || errorMessage || error) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage, errorMessage, error]);\r\n\r\n  const handleCreateEvent = useCallback((date?: Date) => {\r\n    setEditingEvent(null);\r\n    setSelectedDate(date || null);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n  const handleEditEvent = useCallback((event: CalendarEvent) => {\r\n    setEditingEvent(event);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n\r\n\r\n  const handleSaveEvent = useCallback(async (\r\n    data: CreateEventData | UpdateEventData,\r\n    applyPendingDeletes?: () => Promise<void>,\r\n    onComplete?: () => Promise<void>\r\n  ) => {\r\n    setSaving(true);\r\n    try {\r\n      if (editingEvent) {\r\n        await updateEvent(editingEvent.calendar_id, data as UpdateEventData);\r\n\r\n        // Apply pending image deletions AFTER successful update\r\n        if (applyPendingDeletes) {\r\n          console.log('🗑️ Applying pending image deletions after successful update');\r\n          await applyPendingDeletes();\r\n        }\r\n\r\n        setSuccessMessage('Event updated successfully! Calendar refreshed.');\r\n      } else {\r\n        await createEvent(data as CreateEventData);\r\n        setSuccessMessage('Event created successfully! Calendar refreshed.');\r\n      }\r\n\r\n      // Execute completion callback for additional operations\r\n      if (onComplete) {\r\n        await onComplete();\r\n      }\r\n\r\n      // Force refresh the calendar to ensure immediate update\r\n      console.log('🔄 Refreshing calendar to show updated events...');\r\n      setRefreshing(true);\r\n      await refresh();\r\n      setRefreshing(false);\r\n\r\n      // Small delay to ensure smooth UI transition\r\n      setTimeout(() => {\r\n        setShowModal(false);\r\n        setEditingEvent(null);\r\n        setSelectedDate(null);\r\n        setSaving(false);\r\n      }, 100);\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to save event');\r\n      setSaving(false);\r\n    }\r\n  }, [editingEvent, updateEvent, createEvent, refresh]);\r\n\r\n  const handleCloseModal = useCallback(() => {\r\n    setShowModal(false);\r\n    setEditingEvent(null);\r\n    setSelectedDate(null);\r\n  }, []);\r\n\r\n  // Event management functions\r\n  const handlePublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.publishEvent(eventId);\r\n      setSuccessMessage('Event published successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to publish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleUnpublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.unpublishEvent(eventId);\r\n      setSuccessMessage('Event unpublished successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to unpublish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleDeleteEvent = useCallback(async (eventId: number) => {\r\n    // Use window.confirm to avoid ESLint error\r\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await calendarService.softDeleteEvent(eventId);\r\n      setSuccessMessage('Event deleted successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to delete event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const navigateMonth = useCallback((direction: 'prev' | 'next') => {\r\n    const newDate = new Date(currentDate);\r\n    if (direction === 'prev') {\r\n      newDate.setMonth(currentDate.getMonth() - 1);\r\n    } else {\r\n      newDate.setMonth(currentDate.getMonth() + 1);\r\n    }\r\n    setCurrentDate(newDate);\r\n  }, [currentDate]);\r\n\r\n  const goToToday = useCallback(() => {\r\n    setCurrentDate(new Date());\r\n  }, []);\r\n\r\n  const handleDateClick = useCallback((date: Date) => {\r\n    setSelectedDate(date);\r\n    handleCreateEvent(date);\r\n  }, [handleCreateEvent]);\r\n\r\n  const getEventTypeColor = useCallback((event: CalendarEvent) => {\r\n    // Use category color if available, otherwise subcategory color, otherwise default\r\n    return event.category_color || event.subcategory_color || '#22c55e';\r\n  }, []);\r\n\r\n  // Helper function to format event duration\r\n  const getEventDuration = useCallback((event: CalendarEvent) => {\r\n    if (!event.end_date || event.end_date === event.event_date) {\r\n      return 'Single day event';\r\n    }\r\n\r\n    const startDate = new Date(event.event_date);\r\n    const endDate = new Date(event.end_date);\r\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\r\n\r\n    return `${diffDays} day event`;\r\n  }, []);\r\n\r\n  // Helper function to get first two words from event title for calendar chip display\r\n  const getEventChipTitle = useCallback((title: string) => {\r\n    const words = title.trim().split(/\\s+/);\r\n    return words.slice(0, 2).join(' ');\r\n  }, []);\r\n\r\n  // Memoize calendar days to prevent infinite re-renders\r\n  const days = useMemo(() => {\r\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\r\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\r\n\r\n  // Get unique events for the event list (deduplicate multi-day events)\r\n  const uniqueEvents = useMemo(() => {\r\n    const eventMap = new Map();\r\n\r\n    events.forEach(event => {\r\n      // Use calendar_id as the unique identifier\r\n      if (!eventMap.has(event.calendar_id)) {\r\n        eventMap.set(event.calendar_id, event);\r\n      }\r\n    });\r\n\r\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\r\n      // Sort by event_date, then by title\r\n      const dateA = new Date(a.event_date);\r\n      const dateB = new Date(b.event_date);\r\n      if (dateA.getTime() !== dateB.getTime()) {\r\n        return dateA.getTime() - dateB.getTime();\r\n      }\r\n      return a.title.localeCompare(b.title);\r\n    });\r\n\r\n    // Debug: Log deduplication results\r\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\r\n\r\n    return uniqueEventsList;\r\n  }, [events]);\r\n\r\n  // Filter events based on search term and holiday type (exclude holidays from the list)\r\n  const filteredEvents = useMemo(() => {\r\n    return uniqueEvents.filter(event => {\r\n      // Exclude holidays from the events list\r\n      const isNotHoliday = !event.is_holiday;\r\n\r\n      const matchesSearch = !searchTerm ||\r\n        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        event.description?.toLowerCase().includes(searchTerm.toLowerCase());\r\n\r\n      const matchesCategory = !selectedCategory ||\r\n        event.category_id?.toString() === selectedCategory;\r\n\r\n      return isNotHoliday && matchesSearch && matchesCategory;\r\n    });\r\n  }, [uniqueEvents, searchTerm, selectedCategory]);\r\n\r\n  // Pagination calculations\r\n  const totalItems = filteredEvents.length;\r\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\r\n\r\n  // Reset to first page when filters change\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [searchTerm, selectedCategory, itemsPerPage]);\r\n\r\n  // Fetch attachments for visible events - disabled until backend implementation is complete\r\n  // useEffect(() => {\r\n  //   const fetchAttachments = async () => {\r\n  //     if (!paginatedEvents || paginatedEvents.length === 0) return;\r\n  //     // Attachment functionality will be implemented later\r\n  //   };\r\n  //   fetchAttachments();\r\n  // }, [paginatedEvents]);\r\n\r\n  // Component to display event images - disabled until backend implementation is complete\r\n  const EventImages: React.FC<{ eventId: number }> = ({ eventId }) => {\r\n    return (\r\n      <div style={{\r\n        width: '60px',\r\n        height: '60px',\r\n        backgroundColor: '#f3f4f6',\r\n        borderRadius: '6px',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        color: '#9ca3af',\r\n        fontSize: '0.75rem'\r\n      }}>\r\n        <ImageIcon size={20} />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Component to display individual image thumbnail - disabled until backend implementation is complete\r\n  // const EventImageThumbnail: React.FC<{ attachment: any }> = ({ attachment }) => {\r\n  //   // This component will be implemented when the backend supports file attachments\r\n  //   return null;\r\n  // };\r\n\r\n  // Pagination component - Always visible\r\n  const PaginationControls = () => {\r\n    // Always show pagination controls, even for single page\r\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\r\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\r\n\r\n    const getPageNumbers = () => {\r\n      const pages = [];\r\n      const maxVisiblePages = 5;\r\n\r\n      if (effectiveTotalPages <= maxVisiblePages) {\r\n        for (let i = 1; i <= effectiveTotalPages; i++) {\r\n          pages.push(i);\r\n        }\r\n      } else {\r\n        if (effectiveCurrentPage <= 3) {\r\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\r\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\r\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\r\n        } else {\r\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\r\n        }\r\n      }\r\n\r\n      return pages;\r\n    };\r\n\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginTop: '2rem',\r\n        marginBottom: '2rem',\r\n        padding: '1.5rem',\r\n        backgroundColor: 'white',\r\n        borderRadius: '12px',\r\n        border: '1px solid #e5e7eb',\r\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\r\n      }}>\r\n        {/* Items per page selector */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Show:</span>\r\n          <select\r\n            value={itemsPerPage}\r\n            onChange={(e) => setItemsPerPage(Number(e.target.value))}\r\n            style={{\r\n              padding: '0.25rem 0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            <option value={5}>5</option>\r\n            <option value={10}>10</option>\r\n            <option value={20}>20</option>\r\n            <option value={50}>50</option>\r\n          </select>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>per page</span>\r\n        </div>\r\n\r\n        {/* Page info */}\r\n        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\r\n          Showing {Math.max(totalItems > 0 ? startIndex + 1 : 0, 0)}-{Math.min(endIndex, totalItems)} of {totalItems} events\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>\r\n          <button\r\n            onClick={() => setCurrentPage(1)}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            First\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Previous\r\n          </button>\r\n\r\n          {getPageNumbers().map((page, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => typeof page === 'number' && setCurrentPage(page)}\r\n              disabled={page === '...'}\r\n              style={{\r\n                padding: '0.5rem 0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '4px',\r\n                backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\r\n                color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\r\n                cursor: page === '...' ? 'default' : 'pointer',\r\n                fontSize: '0.875rem',\r\n                fontWeight: page === effectiveCurrentPage ? '600' : '400'\r\n              }}\r\n            >\r\n              {page}\r\n            </button>\r\n          ))}\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1))}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Next\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(effectiveTotalPages)}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Last\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }} data-calendar-component=\"main\">\r\n      {/* Success/Error Messages */}\r\n      {successMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#f0fdf4',\r\n          border: '1px solid #bbf7d0',\r\n          color: '#166534',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n      {errorMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#fef2f2',\r\n          border: '1px solid #fecaca',\r\n          color: '#dc2626',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {errorMessage}\r\n        </div>\r\n      )}\r\n\r\n      {/* Calendar Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '8px',\r\n        padding: '1rem',\r\n        marginBottom: '1rem',\r\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\r\n        border: '1px solid #e8f5e8',\r\n        position: 'relative',\r\n        zIndex: 10\r\n      }}>\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '0.5rem' }}>\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n            <h1 style={{\r\n              fontSize: '1.25rem',\r\n              fontWeight: '600',\r\n              color: '#2d5016',\r\n              margin: 0\r\n            }}>\r\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n                <CalendarIcon size={16} color=\"#1e40af\" />\r\n                School Calendar\r\n                {refreshing && (\r\n                  <RefreshCw\r\n                    size={14}\r\n                    color=\"#22c55e\"\r\n                    style={{\r\n                      animation: 'spin 1s linear infinite',\r\n                      marginLeft: '0.25rem'\r\n                    }}\r\n                  />\r\n                )}\r\n              </span>\r\n            </h1>\r\n            <p style={{\r\n              color: '#6b7280',\r\n              margin: '0 0 0 0.5rem',\r\n              fontSize: '0.875rem'\r\n            }}>\r\n              {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n            <button\r\n              onClick={() => navigateMonth('prev')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              ←\r\n            </button>\r\n\r\n            <button\r\n              onClick={goToToday}\r\n              style={{\r\n                padding: '0.25rem 0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              Today\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => navigateMonth('next')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              →\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => setShowHolidayManagement(true)}\r\n              style={{\r\n                display: 'inline-flex',\r\n                alignItems: 'center',\r\n                padding: '0.5rem 1rem',\r\n                background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease',\r\n                gap: '0.5rem'\r\n              }}\r\n              onMouseOver={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(-1px)';\r\n                e.currentTarget.style.boxShadow = '0 2px 6px rgba(245, 158, 11, 0.2)';\r\n              }}\r\n              onMouseOut={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(0)';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              <Star size={16} />\r\n              Holidays\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                display: 'inline-flex',\r\n                alignItems: 'center',\r\n                padding: '0.5rem 1rem',\r\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(-1px)';\r\n                e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\r\n              }}\r\n              onMouseOut={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(0)';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              + Add Event\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Calendar Grid */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        overflow: 'hidden'\r\n      }}>\r\n        {/* Calendar Header */}\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(7, 1fr)',\r\n          backgroundColor: '#f9fafb',\r\n          borderBottom: '1px solid #e5e7eb'\r\n        }}>\r\n          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (\r\n            <div key={day} style={{\r\n              padding: '1rem',\r\n              textAlign: 'center',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151'\r\n            }}>\r\n              {day}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Calendar Body */}\r\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)' }}>\r\n          {days.map((date, index) => {\r\n            const dayEvents = getEventsForDate(date);\r\n            const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\r\n            const isTodayDate = isToday(date);\r\n\r\n            // Create unique key based on date to prevent React key conflicts\r\n            // Use date timestamp as key since it's unique and stable across re-renders\r\n            const dateKey = `calendar-day-${date.getTime()}`;\r\n\r\n            return (\r\n              <div\r\n                key={dateKey}\r\n                style={{\r\n                  minHeight: '120px',\r\n                  padding: '0.5rem',\r\n                  borderBottom: '1px solid #e5e7eb',\r\n                  borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb', // Remove right border on last column\r\n                  cursor: 'pointer',\r\n                  backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\r\n                  color: !isCurrentMonth ? '#9ca3af' : '#374151',\r\n                  transition: 'all 0.2s ease',\r\n                  position: 'relative'\r\n                }}\r\n                onClick={() => handleDateClick(date)}\r\n                onMouseOver={(e) => {\r\n                  if (isCurrentMonth) {\r\n                    e.currentTarget.style.backgroundColor = '#f3f4f6';\r\n                    e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\r\n                  }\r\n                }}\r\n                onMouseOut={(e) => {\r\n                  e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              >\r\n                <div style={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: '500',\r\n                  marginBottom: '0.25rem',\r\n                  color: isTodayDate ? '#2563eb' : 'inherit'\r\n                }}>\r\n                  {date.getDate()}\r\n                </div>\r\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>\r\n                  {dayEvents.slice(0, 3).map((event) => {\r\n                    // Determine styling for multi-day events\r\n                    const isMultiDay = event.isMultiDay;\r\n                    const isStart = event.isEventStart;\r\n                    const isEnd = event.isEventEnd;\r\n                    const isContinuation = isMultiDay && !isStart && !isEnd;\r\n                    const eventColor = getEventTypeColor(event);\r\n\r\n                    return (\r\n                      <div\r\n                        key={`event-${event.calendar_id}-${date.getTime()}`}\r\n                        style={{\r\n                          fontSize: '0.75rem',\r\n                          padding: '0.25rem 0.5rem',\r\n                          borderRadius: isMultiDay ? (isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px') : '6px',\r\n                          backgroundColor: eventColor + (isContinuation ? '25' : '15'),\r\n                          border: `1px solid ${eventColor}`,\r\n                          borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis',\r\n                          whiteSpace: 'nowrap',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          color: '#374151',\r\n                          fontWeight: '500',\r\n                          transition: 'all 0.2s ease',\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: '0.25rem'\r\n                        }}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleEditEvent(event);\r\n                        }}\r\n                        onMouseOver={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + '30';\r\n                          e.currentTarget.style.transform = 'translateY(-1px)';\r\n                          e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\r\n                        }}\r\n                        onMouseOut={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}\r\n                        title={isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title}\r\n                      >\r\n                        {/* Start indicator */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▶\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Continuation indicator */}\r\n                        {isContinuation && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▬\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* End indicator */}\r\n                        {isEnd && isMultiDay && !isStart && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ◀\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Event title */}\r\n                        <span style={{\r\n                          flex: 1,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis'\r\n                        }}>\r\n                          {getEventChipTitle(event.title)}\r\n                        </span>\r\n\r\n                        {/* End arrow for start day */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem',\r\n                            opacity: 0.7\r\n                          }}>\r\n                            →\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {dayEvents.length > 3 && (\r\n                    <div style={{\r\n                      fontSize: '0.75rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      +{dayEvents.length - 3} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Filters */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        margin: '2rem 0',\r\n        padding: '1.5rem',\r\n        backgroundColor: '#f8fafc'\r\n      }}>\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\r\n          gap: '1.5rem',\r\n          alignItems: 'end'\r\n        }}>\r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Search Events\r\n            </label>\r\n            <div style={{ position: 'relative' }}>\r\n              <Search\r\n                size={16}\r\n                color=\"#9ca3af\"\r\n                style={{\r\n                  position: 'absolute',\r\n                  left: '0.75rem',\r\n                  top: '50%',\r\n                  transform: 'translateY(-50%)',\r\n                  pointerEvents: 'none'\r\n                }}\r\n              />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                style={{\r\n                  width: '100%',\r\n                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',\r\n                  border: '1px solid #d1d5db',\r\n                  borderRadius: '8px',\r\n                  fontSize: '0.875rem',\r\n                  outline: 'none',\r\n                  transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\r\n                }}\r\n                onFocus={(e) => {\r\n                  e.currentTarget.style.borderColor = '#22c55e';\r\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n                }}\r\n                onBlur={(e) => {\r\n                  e.currentTarget.style.borderColor = '#d1d5db';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Category\r\n            </label>\r\n            <select\r\n              value={selectedCategory}\r\n              onChange={(e) => setSelectedCategory(e.target.value)}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '8px',\r\n                fontSize: '0.875rem',\r\n                outline: 'none',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\r\n                minHeight: '2.5rem',\r\n                lineHeight: '1.2'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.currentTarget.style.borderColor = '#22c55e';\r\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.currentTarget.style.borderColor = '#d1d5db';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              <option value=\"\">All Categories</option>\r\n              {categories\r\n                .filter((category: any) =>\r\n                  // Hide holiday categories from dropdown\r\n                  !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\r\n                )\r\n                .map((category: any) => (\r\n                  <option key={category.category_id} value={category.category_id}>\r\n                    {category.name}\r\n                  </option>\r\n                ))\r\n              }\r\n            </select>\r\n          </div>\r\n          \r\n          <div>\r\n            <button\r\n              onClick={refresh}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease',\r\n                height: '2.5rem',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                gap: '0.5rem'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              <RefreshCw size={16} />\r\n              Refresh\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Event List Section */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        padding: '2rem',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        marginBottom: '1rem'\r\n      }}>\r\n        <div style={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          marginBottom: '1.5rem'\r\n        }}>\r\n          <h2 style={{\r\n            fontSize: '1.5rem',\r\n            fontWeight: '600',\r\n            color: '#2d5016',\r\n            margin: 0\r\n          }}>\r\n            Events for {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n          </h2>\r\n          <div style={{\r\n            fontSize: '0.875rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            {filteredEvents.length} school event{filteredEvents.length !== 1 ? 's' : ''} found\r\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', fontStyle: 'italic' }}>\r\n              Holidays are shown in calendar but not in this list\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <RefreshCw size={24} style={{ marginBottom: '1rem', animation: 'spin 1s linear infinite' }} />\r\n            <p>Loading events...</p>\r\n          </div>\r\n        ) : filteredEvents.length === 0 ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <CalendarIcon size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\r\n            <p>No events found for this month</p>\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                marginTop: '1rem',\r\n                padding: '0.75rem 1.5rem',\r\n                backgroundColor: '#22c55e',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500'\r\n              }}\r\n            >\r\n              Create First Event\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div style={{\r\n            display: 'grid',\r\n            gap: '1rem'\r\n          }}>\r\n            {paginatedEvents.map((event) => (\r\n              <div\r\n                key={`unique-event-${event.calendar_id}`}\r\n                style={{\r\n                  border: '1px solid #e5e7eb',\r\n                  borderRadius: '12px',\r\n                  padding: '1.5rem',\r\n                  backgroundColor: 'white',\r\n                  transition: 'all 0.2s ease'\r\n                }}\r\n              >\r\n                <div style={{\r\n                  display: 'flex',\r\n                  justifyContent: 'space-between',\r\n                  alignItems: 'flex-start',\r\n                  marginBottom: '1rem'\r\n                }}>\r\n                  <div style={{ flex: 1 }}>\r\n                    <h4 style={{\r\n                      fontSize: '1.125rem',\r\n                      fontWeight: '600',\r\n                      color: '#374151',\r\n                      margin: '0 0 0.5rem 0',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '0.5rem'\r\n                    }}>\r\n                      {event.title}\r\n                      {(event as any).is_alert && (\r\n                        <AlertTriangle size={16} color=\"#ef4444\" />\r\n                      )}\r\n                    </h4>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '1rem',\r\n                      fontSize: '0.875rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      <span className=\"flex items-center gap-2\">\r\n                        <CalendarIcon className=\"h-4 w-4\" />\r\n                        {event.event_date}\r\n                      </span>\r\n                      {event.end_date && event.end_date !== event.event_date && (\r\n                        <span>→ {event.end_date}</span>\r\n                      )}\r\n                      <span style={{\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem',\r\n                        fontWeight: '500'\r\n                      }}>\r\n                        {getEventDuration(event)}\r\n                      </span>\r\n                      <span style={{\r\n                        backgroundColor: getEventTypeColor(event),\r\n                        color: 'white',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem'\r\n                      }}>\r\n                        {event.category_name || 'Uncategorized'}\r\n                      </span>\r\n\r\n                    </div>\r\n                    {event.description && (\r\n                      <p style={{\r\n                        fontSize: '0.875rem',\r\n                        color: '#6b7280',\r\n                        margin: '0.5rem 0 0 0',\r\n                        lineHeight: '1.5'\r\n                      }}>\r\n                        {event.description}\r\n                      </p>\r\n                    )}\r\n\r\n                    {/* Reaction and Comment Counts */}\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '1rem',\r\n                      marginTop: '0.75rem'\r\n                    }}>\r\n                      <CalendarEventLikeButton\r\n                        eventId={event.calendar_id}\r\n                        initialLiked={(event as any).user_has_reacted || false}\r\n                        initialCount={(event as any).reaction_count || 0}\r\n                        size=\"small\"\r\n                        onLikeChange={(liked: boolean, newCount: number) => {\r\n                          // Update the event in the local state if needed\r\n                          console.log(`Event ${event.calendar_id} like changed:`, { liked, newCount });\r\n                        }}\r\n                      />\r\n                      {(event as any).allow_comments && (\r\n                        <span style={{\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: '0.25rem',\r\n                          fontSize: '0.875rem',\r\n                          color: '#6b7280'\r\n                        }}>\r\n                          <MessageCircle size={14} />\r\n                          {(event as any).comment_count || 0} comments\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div style={{\r\n                    display: 'flex',\r\n                    gap: '0.5rem',\r\n                    marginLeft: '1rem'\r\n                  }}>\r\n                    <button\r\n                      onClick={() => handleEditEvent(event)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Edit event\"\r\n                    >\r\n                      <Edit size={16} />\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => (event as any).is_published\r\n                        ? handleUnpublishEvent(event.calendar_id)\r\n                        : handlePublishEvent(event.calendar_id)\r\n                      }\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: (event as any).is_published ? '#fef3c7' : '#dcfce7',\r\n                        color: (event as any).is_published ? '#d97706' : '#16a34a',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title={(event as any).is_published ? 'Unpublish event' : 'Publish event'}\r\n                    >\r\n                      {(event as any).is_published ? <Clock size={16} /> : <Send size={16} />}\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => handleDeleteEvent(event.calendar_id)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#fef2f2',\r\n                        color: '#dc2626',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Delete event\"\r\n                    >\r\n                      <Trash2 size={16} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div style={{\r\n                  borderTop: '1px solid #f3f4f6',\r\n                  paddingTop: '1rem',\r\n                  marginTop: '1rem'\r\n                }}>\r\n                  <div style={{\r\n                    fontSize: '0.875rem',\r\n                    fontWeight: '500',\r\n                    color: '#374151',\r\n                    marginBottom: '0.5rem'\r\n                  }}>\r\n                    Event Images\r\n                  </div>\r\n                  <EventImages eventId={event.calendar_id} />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination Controls - Outside the event list container */}\r\n      <PaginationControls />\r\n\r\n      {/* Calendar Event Modal */}\r\n      <CalendarEventModal\r\n        isOpen={showModal}\r\n        onClose={handleCloseModal}\r\n        onSave={handleSaveEvent}\r\n        event={editingEvent}\r\n        selectedDate={selectedDate}\r\n        loading={saving || loading}\r\n      />\r\n\r\n      {/* Holiday Management Modal */}\r\n      {showHolidayManagement && (\r\n        <HolidayManagement\r\n          onClose={() => {\r\n            setShowHolidayManagement(false);\r\n            // Refresh calendar events after holiday management\r\n            refresh();\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport default Calendar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,QAAQ,yBAAyB;AACjI,OAAOC,kBAAkB,MAAM,kDAAkD;AAEjF,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,IAAIC,SAAS,EAAEC,aAAa,EAAEC,aAAa,EAASC,IAAI,QAAgB,cAAc;AAC5K,OAAOC,uBAAuB,MAAM,iDAAiD;AACrF,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMhB,QAAkB,gBAAAiB,EAAA,cAAG9B,KAAK,CAAC+B,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC1C;EACA9B,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAM+B,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAChC,OAAO,MAAM;MACXC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,MAAM,IAAIyC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA;;EAEA;EACA,MAAM;IACJkE,MAAM;IACNC,OAAO;IACPC,KAAK;IACLC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC;EACF,CAAC,GAAGpE,WAAW,CAACmC,WAAW,CAAC;EAE5B,MAAM;IAAEkC;EAAW,CAAC,GAAGpE,qBAAqB,CAAC,CAAC;;EAE9C;EACAJ,SAAS,CAAC,MAAM;IACd,IAAI+C,cAAc,IAAIE,YAAY,IAAIkB,KAAK,EAAE;MAC3C,MAAMM,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B1B,iBAAiB,CAAC,EAAE,CAAC;QACrBE,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMyB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1B,cAAc,EAAEE,YAAY,EAAEkB,KAAK,CAAC,CAAC;EAEzC,MAAMS,iBAAiB,GAAG1E,WAAW,CAAE2E,IAAW,IAAK;IACrD/B,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAACmC,IAAI,IAAI,IAAI,CAAC;IAC7BjC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkC,eAAe,GAAG5E,WAAW,CAAE6E,KAAoB,IAAK;IAC5DjC,eAAe,CAACiC,KAAK,CAAC;IACtBnC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMoC,eAAe,GAAG9E,WAAW,CAAC,OAClC+E,IAAuC,EACvCC,mBAAyC,EACzCC,UAAgC,KAC7B;IACH/B,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,IAAIP,YAAY,EAAE;QAChB,MAAMwB,WAAW,CAACxB,YAAY,CAACuC,WAAW,EAAEH,IAAuB,CAAC;;QAEpE;QACA,IAAIC,mBAAmB,EAAE;UACvBG,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3E,MAAMJ,mBAAmB,CAAC,CAAC;QAC7B;QAEAlC,iBAAiB,CAAC,iDAAiD,CAAC;MACtE,CAAC,MAAM;QACL,MAAMoB,WAAW,CAACa,IAAuB,CAAC;QAC1CjC,iBAAiB,CAAC,iDAAiD,CAAC;MACtE;;MAEA;MACA,IAAImC,UAAU,EAAE;QACd,MAAMA,UAAU,CAAC,CAAC;MACpB;;MAEA;MACAE,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DhC,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMiB,OAAO,CAAC,CAAC;MACfjB,aAAa,CAAC,KAAK,CAAC;;MAEpB;MACAoB,UAAU,CAAC,MAAM;QACf9B,YAAY,CAAC,KAAK,CAAC;QACnBE,eAAe,CAAC,IAAI,CAAC;QACrBJ,eAAe,CAAC,IAAI,CAAC;QACrBU,SAAS,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOe,KAAU,EAAE;MACnBjB,eAAe,CAACiB,KAAK,CAACoB,OAAO,IAAI,sBAAsB,CAAC;MACxDnC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,EAAE,CAACP,YAAY,EAAEwB,WAAW,EAAED,WAAW,EAAEG,OAAO,CAAC,CAAC;EAErD,MAAMiB,gBAAgB,GAAGtF,WAAW,CAAC,MAAM;IACzC0C,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,kBAAkB,GAAGvF,WAAW,CAAC,MAAOwF,OAAe,IAAK;IAChE,IAAI;MACF,MAAMhF,eAAe,CAACiF,YAAY,CAACD,OAAO,CAAC;MAC3C1C,iBAAiB,CAAC,8BAA8B,CAAC;MACjDuB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBjB,eAAe,CAACiB,KAAK,CAACoB,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMqB,oBAAoB,GAAG1F,WAAW,CAAC,MAAOwF,OAAe,IAAK;IAClE,IAAI;MACF,MAAMhF,eAAe,CAACmF,cAAc,CAACH,OAAO,CAAC;MAC7C1C,iBAAiB,CAAC,gCAAgC,CAAC;MACnDuB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBjB,eAAe,CAACiB,KAAK,CAACoB,OAAO,IAAI,2BAA2B,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMuB,iBAAiB,GAAG5F,WAAW,CAAC,MAAOwF,OAAe,IAAK;IAC/D;IACA,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,2EAA2E,CAAC,EAAE;MAChG;IACF;IAEA,IAAI;MACF,MAAMtF,eAAe,CAACuF,eAAe,CAACP,OAAO,CAAC;MAC9C1C,iBAAiB,CAAC,4BAA4B,CAAC;MAC/CuB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBjB,eAAe,CAACiB,KAAK,CAACoB,OAAO,IAAI,wBAAwB,CAAC;IAC5D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAM2B,aAAa,GAAGhG,WAAW,CAAEiG,SAA0B,IAAK;IAChE,MAAMC,OAAO,GAAG,IAAI5D,IAAI,CAACF,WAAW,CAAC;IACrC,IAAI6D,SAAS,KAAK,MAAM,EAAE;MACxBC,OAAO,CAACC,QAAQ,CAAC/D,WAAW,CAACgE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLF,OAAO,CAACC,QAAQ,CAAC/D,WAAW,CAACgE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA/D,cAAc,CAAC6D,OAAO,CAAC;EACzB,CAAC,EAAE,CAAC9D,WAAW,CAAC,CAAC;EAEjB,MAAMiE,SAAS,GAAGrG,WAAW,CAAC,MAAM;IAClCqC,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgE,eAAe,GAAGtG,WAAW,CAAE2E,IAAU,IAAK;IAClDnC,eAAe,CAACmC,IAAI,CAAC;IACrBD,iBAAiB,CAACC,IAAI,CAAC;EACzB,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAM6B,iBAAiB,GAAGvG,WAAW,CAAE6E,KAAoB,IAAK;IAC9D;IACA,OAAOA,KAAK,CAAC2B,cAAc,IAAI3B,KAAK,CAAC4B,iBAAiB,IAAI,SAAS;EACrE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAG1G,WAAW,CAAE6E,KAAoB,IAAK;IAC7D,IAAI,CAACA,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,EAAE;MAC1D,OAAO,kBAAkB;IAC3B;IAEA,MAAMC,SAAS,GAAG,IAAIvE,IAAI,CAACuC,KAAK,CAAC+B,UAAU,CAAC;IAC5C,MAAME,OAAO,GAAG,IAAIxE,IAAI,CAACuC,KAAK,CAAC8B,QAAQ,CAAC;IACxC,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAGL,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;IAClE,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAElE,OAAO,GAAGI,QAAQ,YAAY;EAChC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,iBAAiB,GAAGrH,WAAW,CAAEsH,KAAa,IAAK;IACvD,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;IACvC,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,IAAI,GAAG7H,OAAO,CAAC,MAAM;IACzB,OAAOI,eAAe,CAACiC,WAAW,CAACyF,WAAW,CAAC,CAAC,EAAEzF,WAAW,CAACgE,QAAQ,CAAC,CAAC,CAAC;EAC3E,CAAC,EAAE,CAAChE,WAAW,CAACyF,WAAW,CAAC,CAAC,EAAEzF,WAAW,CAACgE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM0B,YAAY,GAAG/H,OAAO,CAAC,MAAM;IACjC,MAAMgI,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE1BjE,MAAM,CAACkE,OAAO,CAACpD,KAAK,IAAI;MACtB;MACA,IAAI,CAACkD,QAAQ,CAACG,GAAG,CAACrD,KAAK,CAACK,WAAW,CAAC,EAAE;QACpC6C,QAAQ,CAACI,GAAG,CAACtD,KAAK,CAACK,WAAW,EAAEL,KAAK,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,MAAMuD,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACP,QAAQ,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpE;MACA,MAAMC,KAAK,GAAG,IAAIrG,IAAI,CAACmG,CAAC,CAAC7B,UAAU,CAAC;MACpC,MAAMgC,KAAK,GAAG,IAAItG,IAAI,CAACoG,CAAC,CAAC9B,UAAU,CAAC;MACpC,IAAI+B,KAAK,CAACzB,OAAO,CAAC,CAAC,KAAK0B,KAAK,CAAC1B,OAAO,CAAC,CAAC,EAAE;QACvC,OAAOyB,KAAK,CAACzB,OAAO,CAAC,CAAC,GAAG0B,KAAK,CAAC1B,OAAO,CAAC,CAAC;MAC1C;MACA,OAAOuB,CAAC,CAACnB,KAAK,CAACuB,aAAa,CAACH,CAAC,CAACpB,KAAK,CAAC;IACvC,CAAC,CAAC;;IAEF;IACAnC,OAAO,CAACC,GAAG,CAAC,2BAA2BrB,MAAM,CAAC+E,MAAM,mBAAmBV,gBAAgB,CAACU,MAAM,gBAAgB,CAAC;IAE/G,OAAOV,gBAAgB;EACzB,CAAC,EAAE,CAACrE,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMgF,cAAc,GAAGhJ,OAAO,CAAC,MAAM;IACnC,OAAO+H,YAAY,CAACkB,MAAM,CAACnE,KAAK,IAAI;MAAA,IAAAoE,kBAAA,EAAAC,kBAAA;MAClC;MACA,MAAMC,YAAY,GAAG,CAACtE,KAAK,CAACuE,UAAU;MAEtC,MAAMC,aAAa,GAAG,CAAChG,UAAU,IAC/BwB,KAAK,CAACyC,KAAK,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC,MAAAL,kBAAA,GAC5DpE,KAAK,CAAC2E,WAAW,cAAAP,kBAAA,uBAAjBA,kBAAA,CAAmBK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC;MAErE,MAAMG,eAAe,GAAG,CAAClG,gBAAgB,IACvC,EAAA2F,kBAAA,GAAArE,KAAK,CAAC6E,WAAW,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmBS,QAAQ,CAAC,CAAC,MAAKpG,gBAAgB;MAEpD,OAAO4F,YAAY,IAAIE,aAAa,IAAII,eAAe;IACzD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3B,YAAY,EAAEzE,UAAU,EAAEE,gBAAgB,CAAC,CAAC;;EAEhD;EACA,MAAMqG,UAAU,GAAGb,cAAc,CAACD,MAAM;EACxC,MAAMe,UAAU,GAAG7C,IAAI,CAACI,IAAI,CAACwC,UAAU,GAAG/F,YAAY,CAAC;EACvD,MAAMiG,UAAU,GAAG,CAACnG,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMkG,QAAQ,GAAGD,UAAU,GAAGjG,YAAY;EAC1C,MAAMmG,eAAe,GAAGjB,cAAc,CAACrB,KAAK,CAACoC,UAAU,EAAEC,QAAQ,CAAC;;EAElE;EACAjK,SAAS,CAAC,MAAM;IACd8D,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,UAAU,EAAEE,gBAAgB,EAAEM,YAAY,CAAC,CAAC;;EAEhD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMoG,WAA0C,GAAGA,CAAC;IAAEzE;EAAQ,CAAC,KAAK;IAClE,oBACE/D,OAAA;MAAKI,KAAK,EAAE;QACVqI,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,eACAlJ,OAAA,CAACP,SAAS;QAAC0J,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAEV,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,mBAAmB,GAAGlE,IAAI,CAACmE,GAAG,CAACtB,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMuB,oBAAoB,GAAGpE,IAAI,CAACmE,GAAG,CAACxH,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvD,MAAM0H,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,KAAK,GAAG,EAAE;MAChB,MAAMC,eAAe,GAAG,CAAC;MAEzB,IAAIL,mBAAmB,IAAIK,eAAe,EAAE;QAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,mBAAmB,EAAEM,CAAC,EAAE,EAAE;UAC7CF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACL,IAAIJ,oBAAoB,IAAI,CAAC,EAAE;UAC7BE,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAEP,mBAAmB,CAAC;QACpD,CAAC,MAAM,IAAIE,oBAAoB,IAAIF,mBAAmB,GAAG,CAAC,EAAE;UAC1DI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEP,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,CAAC;QACtH,CAAC,MAAM;UACLI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEL,oBAAoB,GAAG,CAAC,EAAEA,oBAAoB,EAAEA,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAEF,mBAAmB,CAAC;QAC5H;MACF;MAEA,OAAOI,KAAK;IACd,CAAC;IAED,oBACE7J,OAAA;MAAKI,KAAK,EAAE;QACVyI,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBmB,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBxB,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBwB,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAnB,QAAA,gBAEAlJ,OAAA;QAAKI,KAAK,EAAE;UAAEyI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnElJ,OAAA;UAAMI,KAAK,EAAE;YAAE6I,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrEvJ,OAAA;UACEuK,KAAK,EAAEnI,YAAa;UACpBoI,QAAQ,EAAGC,CAAC,IAAKpI,eAAe,CAACqI,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;UACzDnK,KAAK,EAAE;YACL+J,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBK,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,gBAEFlJ,OAAA;YAAQuK,KAAK,EAAE,CAAE;YAAArB,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5BvJ,OAAA;YAAQuK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BvJ,OAAA;YAAQuK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BvJ,OAAA;YAAQuK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACTvJ,OAAA;UAAMI,KAAK,EAAE;YAAE6I,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAGNvJ,OAAA;QAAKI,KAAK,EAAE;UAAE6I,QAAQ,EAAE,UAAU;UAAED,KAAK,EAAE;QAAU,CAAE;QAAAE,QAAA,GAAC,UAC9C,EAAC3D,IAAI,CAACmE,GAAG,CAACvB,UAAU,GAAG,CAAC,GAAGE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAC,GAAC,EAAC9C,IAAI,CAACqF,GAAG,CAACtC,QAAQ,EAAEH,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,SAC7G;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNvJ,OAAA;QAAKI,KAAK,EAAE;UAAEyI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE,SAAS;UAAEO,QAAQ,EAAE;QAAO,CAAE;QAAA3B,QAAA,gBACtFlJ,OAAA;UACE8K,OAAO,EAAEA,CAAA,KAAM3I,cAAc,CAAC,CAAC,CAAE;UACjC4I,QAAQ,EAAEpB,oBAAoB,KAAK,CAAE;UACrCvJ,KAAK,EAAE;YACL+J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjEX,KAAK,EAAEW,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDqB,MAAM,EAAErB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9DV,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvJ,OAAA;UACE8K,OAAO,EAAEA,CAAA,KAAM3I,cAAc,CAAC8I,IAAI,IAAI1F,IAAI,CAACmE,GAAG,CAAC,CAAC,EAAEuB,IAAI,GAAG,CAAC,CAAC,CAAE;UAC7DF,QAAQ,EAAEpB,oBAAoB,KAAK,CAAE;UACrCvJ,KAAK,EAAE;YACL+J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjEX,KAAK,EAAEW,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDqB,MAAM,EAAErB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9DV,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERK,cAAc,CAAC,CAAC,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChCpL,OAAA;UAEE8K,OAAO,EAAEA,CAAA,KAAM,OAAOK,IAAI,KAAK,QAAQ,IAAIhJ,cAAc,CAACgJ,IAAI,CAAE;UAChEJ,QAAQ,EAAEI,IAAI,KAAK,KAAM;UACzB/K,KAAK,EAAE;YACL+J,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEwC,IAAI,KAAKxB,oBAAoB,GAAG,SAAS,GAAGwB,IAAI,KAAK,KAAK,GAAG,aAAa,GAAG,OAAO;YACrGnC,KAAK,EAAEmC,IAAI,KAAKxB,oBAAoB,GAAG,OAAO,GAAGwB,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YACvFH,MAAM,EAAEG,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YAC9ClC,QAAQ,EAAE,UAAU;YACpBoC,UAAU,EAAEF,IAAI,KAAKxB,oBAAoB,GAAG,KAAK,GAAG;UACtD,CAAE;UAAAT,QAAA,EAEDiC;QAAI,GAdAC,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeJ,CACT,CAAC,eAEFvJ,OAAA;UACE8K,OAAO,EAAEA,CAAA,KAAM3I,cAAc,CAAC8I,IAAI,IAAI1F,IAAI,CAACqF,GAAG,CAACnB,mBAAmB,EAAEwB,IAAI,GAAG,CAAC,CAAC,CAAE;UAC/EF,QAAQ,EAAEpB,oBAAoB,KAAKF,mBAAoB;UACvDrJ,KAAK,EAAE;YACL+J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnFT,KAAK,EAAEW,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EuB,MAAM,EAAErB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChFR,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvJ,OAAA;UACE8K,OAAO,EAAEA,CAAA,KAAM3I,cAAc,CAACsH,mBAAmB,CAAE;UACnDsB,QAAQ,EAAEpB,oBAAoB,KAAKF,mBAAoB;UACvDrJ,KAAK,EAAE;YACL+J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnFT,KAAK,EAAEW,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EuB,MAAM,EAAErB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChFR,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEvJ,OAAA;IAAKI,KAAK,EAAE;MAAEkL,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAC,2BAAwB,MAAM;IAAArC,QAAA,GAEjF9H,cAAc,iBACbpB,OAAA;MAAKI,KAAK,EAAE;QACV8J,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfxB,eAAe,EAAE,SAAS;QAC1ByB,MAAM,EAAE,mBAAmB;QAC3BpB,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACC9H;IAAc;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,EACAjI,YAAY,iBACXtB,OAAA;MAAKI,KAAK,EAAE;QACV8J,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfxB,eAAe,EAAE,SAAS;QAC1ByB,MAAM,EAAE,mBAAmB;QAC3BpB,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACC5H;IAAY;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDvJ,OAAA;MAAKI,KAAK,EAAE;QACVoL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,KAAK;QACnBuB,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,MAAM;QACpBG,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BqB,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE;MACV,CAAE;MAAAxC,QAAA,eACAlJ,OAAA;QAAKI,KAAK,EAAE;UAAEyI,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACpGlJ,OAAA;UAAKI,KAAK,EAAE;YAAEyI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwB,GAAG,EAAE;UAAU,CAAE;UAAApB,QAAA,gBACpElJ,OAAA;YAAII,KAAK,EAAE;cACT6I,QAAQ,EAAE,SAAS;cACnBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBuC,MAAM,EAAE;YACV,CAAE;YAAArC,QAAA,eACAlJ,OAAA;cAAMI,KAAK,EAAE;gBAAEyI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEwB,GAAG,EAAE;cAAU,CAAE;cAAApB,QAAA,gBACrElJ,OAAA,CAACf,YAAY;gBAACkK,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE1C,EAAC7H,UAAU,iBACT1B,OAAA,CAACb,SAAS;gBACRgK,IAAI,EAAE,EAAG;gBACTH,KAAK,EAAC,SAAS;gBACf5I,KAAK,EAAE;kBACLuL,SAAS,EAAE,yBAAyB;kBACpCC,UAAU,EAAE;gBACd;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLvJ,OAAA;YAAGI,KAAK,EAAE;cACR4I,KAAK,EAAE,SAAS;cAChBuC,MAAM,EAAE,cAAc;cACtBtC,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,GACCrK,YAAY,CAAC8B,WAAW,CAACgE,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAChE,WAAW,CAACyF,WAAW,CAAC,CAAC;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvJ,OAAA;UAAKI,KAAK,EAAE;YAAEyI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwB,GAAG,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACnElJ,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMvG,aAAa,CAAC,MAAM,CAAE;YACrCnE,KAAK,EAAE;cACL+J,OAAO,EAAE,SAAS;cAClBnB,KAAK,EAAE,SAAS;cAChBwC,UAAU,EAAE,MAAM;cAClBpB,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB/B,QAAQ,EAAE,MAAM;cAChB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC4I,KAAK,GAAG,SAAU;YAC5DgD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC4I,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvJ,OAAA;YACE8K,OAAO,EAAElG,SAAU;YACnBxE,KAAK,EAAE;cACL+J,OAAO,EAAE,iBAAiB;cAC1BxB,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,SAAU;YACtEqD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,SAAU;YAAAO,QAAA,EACtE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvJ,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMvG,aAAa,CAAC,MAAM,CAAE;YACrCnE,KAAK,EAAE;cACL+J,OAAO,EAAE,SAAS;cAClBnB,KAAK,EAAE,SAAS;cAChBwC,UAAU,EAAE,MAAM;cAClBpB,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB/B,QAAQ,EAAE,MAAM;cAChB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC4I,KAAK,GAAG,SAAU;YAC5DgD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC4I,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvJ,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM7I,wBAAwB,CAAC,IAAI,CAAE;YAC9C7B,KAAK,EAAE;cACLyI,OAAO,EAAE,aAAa;cACtBC,UAAU,EAAE,QAAQ;cACpBqB,OAAO,EAAE,aAAa;cACtBqB,UAAU,EAAE,mDAAmD;cAC/DxC,KAAK,EAAE,OAAO;cACdoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE,2CAA2C;cACvDvB,GAAG,EAAE;YACP,CAAE;YACFwB,WAAW,EAAGrB,CAAC,IAAK;cAClBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,kBAAkB;cACpDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,mCAAmC;YACvE,CAAE;YACF2B,UAAU,EAAGvB,CAAC,IAAK;cACjBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,eAAe;cACjDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,gBAEFlJ,OAAA,CAACJ,IAAI;cAACuJ,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvJ,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC,CAAE;YACnC7C,KAAK,EAAE;cACLyI,OAAO,EAAE,aAAa;cACtBC,UAAU,EAAE,QAAQ;cACpBqB,OAAO,EAAE,aAAa;cACtBqB,UAAU,EAAE,mDAAmD;cAC/DxC,KAAK,EAAE,OAAO;cACdoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAK;cAClBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,kBAAkB;cACpDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACF2B,UAAU,EAAGvB,CAAC,IAAK;cACjBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,eAAe;cACjDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvJ,OAAA;MAAKI,KAAK,EAAE;QACVoL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpByB,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3B8B,QAAQ,EAAE;MACZ,CAAE;MAAAhD,QAAA,gBAEAlJ,OAAA;QAAKI,KAAK,EAAE;UACVyI,OAAO,EAAE,MAAM;UACfsD,mBAAmB,EAAE,gBAAgB;UACrCxD,eAAe,EAAE,SAAS;UAC1ByD,YAAY,EAAE;QAChB,CAAE;QAAAlD,QAAA,EACC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACgC,GAAG,CAAEmB,GAAG,iBACzDrM,OAAA;UAAeI,KAAK,EAAE;YACpB+J,OAAO,EAAE,MAAM;YACfmC,SAAS,EAAE,QAAQ;YACnBrD,QAAQ,EAAE,UAAU;YACpBoC,UAAU,EAAE,KAAK;YACjBrC,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EACCmD;QAAG,GAPIA,GAAG;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNvJ,OAAA;QAAKI,KAAK,EAAE;UAAEyI,OAAO,EAAE,MAAM;UAAEsD,mBAAmB,EAAE;QAAiB,CAAE;QAAAjD,QAAA,EACpE/C,IAAI,CAAC+E,GAAG,CAAC,CAAChI,IAAI,EAAEkI,KAAK,KAAK;UACzB,MAAMmB,SAAS,GAAG5J,gBAAgB,CAACO,IAAI,CAAC;UACxC,MAAMsJ,cAAc,GAAG5N,WAAW,CAACsE,IAAI,EAAEvC,WAAW,CAACgE,QAAQ,CAAC,CAAC,EAAEhE,WAAW,CAACyF,WAAW,CAAC,CAAC,CAAC;UAC3F,MAAMqG,WAAW,GAAG9N,OAAO,CAACuE,IAAI,CAAC;;UAEjC;UACA;UACA,MAAMwJ,OAAO,GAAG,gBAAgBxJ,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;UAEhD,oBACEzF,OAAA;YAEEI,KAAK,EAAE;cACLuM,SAAS,EAAE,OAAO;cAClBxC,OAAO,EAAE,QAAQ;cACjBiC,YAAY,EAAE,mBAAmB;cACjCQ,WAAW,EAAExB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,mBAAmB;cAAE;cAC7DJ,MAAM,EAAE,SAAS;cACjBrC,eAAe,EAAE,CAAC6D,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cAChFzD,KAAK,EAAE,CAACwD,cAAc,GAAG,SAAS,GAAG,SAAS;cAC9CX,UAAU,EAAE,eAAe;cAC3BJ,QAAQ,EAAE;YACZ,CAAE;YACFX,OAAO,EAAEA,CAAA,KAAMjG,eAAe,CAAC3B,IAAI,CAAE;YACrC4I,WAAW,EAAGrB,CAAC,IAAK;cAClB,IAAI+B,cAAc,EAAE;gBAClB/B,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,SAAS;gBACjD8B,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,2BAA2B;cAC/D;YACF,CAAE;YACF2B,UAAU,EAAGvB,CAAC,IAAK;cACjBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,CAAC6D,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cACvGhC,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,gBAEFlJ,OAAA;cAAKI,KAAK,EAAE;gBACV6I,QAAQ,EAAE,UAAU;gBACpBoC,UAAU,EAAE,KAAK;gBACjBnB,YAAY,EAAE,SAAS;gBACvBlB,KAAK,EAAEyD,WAAW,GAAG,SAAS,GAAG;cACnC,CAAE;cAAAvD,QAAA,EACChG,IAAI,CAAC2J,OAAO,CAAC;YAAC;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACNvJ,OAAA;cAAKI,KAAK,EAAE;gBAAEyI,OAAO,EAAE,MAAM;gBAAEiE,aAAa,EAAE,QAAQ;gBAAExC,GAAG,EAAE;cAAU,CAAE;cAAApB,QAAA,GACtEqD,SAAS,CAACtG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACiF,GAAG,CAAE9H,KAAK,IAAK;gBACpC;gBACA,MAAM2J,UAAU,GAAG3J,KAAK,CAAC2J,UAAU;gBACnC,MAAMC,OAAO,GAAG5J,KAAK,CAAC6J,YAAY;gBAClC,MAAMC,KAAK,GAAG9J,KAAK,CAAC+J,UAAU;gBAC9B,MAAMC,cAAc,GAAGL,UAAU,IAAI,CAACC,OAAO,IAAI,CAACE,KAAK;gBACvD,MAAMG,UAAU,GAAGvI,iBAAiB,CAAC1B,KAAK,CAAC;gBAE3C,oBACEpD,OAAA;kBAEEI,KAAK,EAAE;oBACL6I,QAAQ,EAAE,SAAS;oBACnBkB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAEmE,UAAU,GAAIC,OAAO,GAAG,iBAAiB,GAAGE,KAAK,GAAG,iBAAiB,GAAG,KAAK,GAAI,KAAK;oBACpGvE,eAAe,EAAE0E,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBAC5DhD,MAAM,EAAE,aAAaiD,UAAU,EAAE;oBACjCC,UAAU,EAAEN,OAAO,IAAI,CAACD,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBAC1FT,WAAW,EAAEM,KAAK,IAAI,CAACH,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBACzFnB,QAAQ,EAAE,QAAQ;oBAClBqB,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE,QAAQ;oBACpBxC,MAAM,EAAE,SAAS;oBACjBS,QAAQ,EAAE,UAAU;oBACpBzC,KAAK,EAAE,SAAS;oBAChBqC,UAAU,EAAE,KAAK;oBACjBQ,UAAU,EAAE,eAAe;oBAC3BhD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBwB,GAAG,EAAE;kBACP,CAAE;kBACFQ,OAAO,EAAGL,CAAC,IAAK;oBACdA,CAAC,CAACgD,eAAe,CAAC,CAAC;oBACnBtK,eAAe,CAACC,KAAK,CAAC;kBACxB,CAAE;kBACF0I,WAAW,EAAGrB,CAAC,IAAK;oBAClBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG0E,UAAU,GAAG,IAAI;oBACzD5C,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,kBAAkB;oBACpDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,aAAagD,UAAU,IAAI;kBAC/D,CAAE;kBACFrB,UAAU,EAAGvB,CAAC,IAAK;oBACjBA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG0E,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBACnF3C,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAAC6L,SAAS,GAAG,eAAe;oBACjDxB,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBACFxE,KAAK,EAAEkH,UAAU,GAAG,GAAG3J,KAAK,CAACyC,KAAK,KAAKzC,KAAK,CAACsK,iBAAiB,OAAOtK,KAAK,CAACuK,eAAe,GAAG,GAAGvK,KAAK,CAACyC,KAAM;kBAAAqD,QAAA,GAG3G8D,OAAO,IAAID,UAAU,iBACpB/M,OAAA;oBAAMI,KAAK,EAAE;sBACX4I,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGA6D,cAAc,iBACbpN,OAAA;oBAAMI,KAAK,EAAE;sBACX4I,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGA2D,KAAK,IAAIH,UAAU,IAAI,CAACC,OAAO,iBAC9BhN,OAAA;oBAAMI,KAAK,EAAE;sBACX4I,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eAGDvJ,OAAA;oBAAMI,KAAK,EAAE;sBACXwN,IAAI,EAAE,CAAC;sBACP1B,QAAQ,EAAE,QAAQ;sBAClBqB,YAAY,EAAE;oBAChB,CAAE;oBAAArE,QAAA,EACCtD,iBAAiB,CAACxC,KAAK,CAACyC,KAAK;kBAAC;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,EAGNyD,OAAO,IAAID,UAAU,iBACpB/M,OAAA;oBAAMI,KAAK,EAAE;sBACX4I,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE,QAAQ;sBAClB4E,OAAO,EAAE;oBACX,CAAE;oBAAA3E,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA,GAzFI,SAASnG,KAAK,CAACK,WAAW,IAAIP,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0FhD,CAAC;cAEV,CAAC,CAAC,EACDgD,SAAS,CAAClF,MAAM,GAAG,CAAC,iBACnBrH,OAAA;gBAAKI,KAAK,EAAE;kBACV6I,QAAQ,EAAE,SAAS;kBACnBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,GAAC,GACA,EAACqD,SAAS,CAAClF,MAAM,GAAG,CAAC,EAAC,OACzB;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAhJDmD,OAAO;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiJT,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvJ,OAAA;MAAKI,KAAK,EAAE;QACVoL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpByB,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BmB,MAAM,EAAE,QAAQ;QAChBpB,OAAO,EAAE,QAAQ;QACjBxB,eAAe,EAAE;MACnB,CAAE;MAAAO,QAAA,eACAlJ,OAAA;QAAKI,KAAK,EAAE;UACVyI,OAAO,EAAE,MAAM;UACfsD,mBAAmB,EAAE,sCAAsC;UAC3D7B,GAAG,EAAE,QAAQ;UACbxB,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBACAlJ,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA;YAAOI,KAAK,EAAE;cACZyI,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBkB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRvJ,OAAA;YAAKI,KAAK,EAAE;cAAEqL,QAAQ,EAAE;YAAW,CAAE;YAAAvC,QAAA,gBACnClJ,OAAA,CAACd,MAAM;cACLiK,IAAI,EAAE,EAAG;cACTH,KAAK,EAAC,SAAS;cACf5I,KAAK,EAAE;gBACLqL,QAAQ,EAAE,UAAU;gBACpBqC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACV9B,SAAS,EAAE,kBAAkB;gBAC7B+B,aAAa,EAAE;cACjB;YAAE;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFvJ,OAAA;cACEiO,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kBAAkB;cAC9B3D,KAAK,EAAE3I,UAAW;cAClB4I,QAAQ,EAAGC,CAAC,IAAK5I,aAAa,CAAC4I,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cAC/CnK,KAAK,EAAE;gBACLqI,KAAK,EAAE,MAAM;gBACb0B,OAAO,EAAE,gCAAgC;gBACzCC,MAAM,EAAE,mBAAmB;gBAC3BxB,YAAY,EAAE,KAAK;gBACnBK,QAAQ,EAAE,UAAU;gBACpBkF,OAAO,EAAE,MAAM;gBACftC,UAAU,EAAE;cACd,CAAE;cACFuC,OAAO,EAAG3D,CAAC,IAAK;gBACdA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiO,WAAW,GAAG,SAAS;gBAC7C5D,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,kCAAkC;cACtE,CAAE;cACFiE,MAAM,EAAG7D,CAAC,IAAK;gBACbA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiO,WAAW,GAAG,SAAS;gBAC7C5D,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;cAC1C;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvJ,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA;YAAOI,KAAK,EAAE;cACZyI,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBkB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRvJ,OAAA;YACEuK,KAAK,EAAEzI,gBAAiB;YACxB0I,QAAQ,EAAGC,CAAC,IAAK1I,mBAAmB,CAAC0I,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YACrDnK,KAAK,EAAE;cACLqI,KAAK,EAAE,MAAM;cACb0B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BxB,YAAY,EAAE,KAAK;cACnBK,QAAQ,EAAE,UAAU;cACpBkF,OAAO,EAAE,MAAM;cACfxF,eAAe,EAAE,OAAO;cACxBkD,UAAU,EAAE,8CAA8C;cAC1Dc,SAAS,EAAE,QAAQ;cACnB4B,UAAU,EAAE;YACd,CAAE;YACFH,OAAO,EAAG3D,CAAC,IAAK;cACdA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiO,WAAW,GAAG,SAAS;cAC7C5D,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFiE,MAAM,EAAG7D,CAAC,IAAK;cACbA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiO,WAAW,GAAG,SAAS;cAC7C5D,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACiK,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,gBAEFlJ,OAAA;cAAQuK,KAAK,EAAC,EAAE;cAAArB,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvC1G,UAAU,CACR0E,MAAM,CAAEiH,QAAa;YACpB;YACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC1G,QAAQ,CAAC0G,QAAQ,CAACC,IAAI,CACjG,CAAC,CACAvD,GAAG,CAAEsD,QAAa,iBACjBxO,OAAA;cAAmCuK,KAAK,EAAEiE,QAAQ,CAACvG,WAAY;cAAAiB,QAAA,EAC5DsF,QAAQ,CAACC;YAAI,GADHD,QAAQ,CAACvG,WAAW;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvJ,OAAA;UAAAkJ,QAAA,eACElJ,OAAA;YACE8K,OAAO,EAAElI,OAAQ;YACjBxC,KAAK,EAAE;cACLqI,KAAK,EAAE,MAAM;cACb0B,OAAO,EAAE,SAAS;cAClBxB,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE,4BAA4B;cACxCnD,MAAM,EAAE,QAAQ;cAChBG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBuB,GAAG,EAAE;YACP,CAAE;YACFwB,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,SAAU;YACtEqD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAAC3L,KAAK,CAACuI,eAAe,GAAG,SAAU;YAAAO,QAAA,gBAErElJ,OAAA,CAACb,SAAS;cAACgK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvJ,OAAA;MAAKI,KAAK,EAAE;QACVoL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpBuB,OAAO,EAAE,MAAM;QACfE,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE;MAChB,CAAE;MAAAhB,QAAA,gBACAlJ,OAAA;QAAKI,KAAK,EAAE;UACVyI,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBoB,YAAY,EAAE;QAChB,CAAE;QAAAhB,QAAA,gBACAlJ,OAAA;UAAII,KAAK,EAAE;YACT6I,QAAQ,EAAE,QAAQ;YAClBoC,UAAU,EAAE,KAAK;YACjBrC,KAAK,EAAE,SAAS;YAChBuC,MAAM,EAAE;UACV,CAAE;UAAArC,QAAA,GAAC,aACU,EAACrK,YAAY,CAAC8B,WAAW,CAACgE,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAChE,WAAW,CAACyF,WAAW,CAAC,CAAC;QAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACLvJ,OAAA;UAAKI,KAAK,EAAE;YACV6I,QAAQ,EAAE,UAAU;YACpBD,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,GACC5B,cAAc,CAACD,MAAM,EAAC,eAAa,EAACC,cAAc,CAACD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QAC5E,eAAArH,OAAA;YAAKI,KAAK,EAAE;cAAE6I,QAAQ,EAAE,SAAS;cAAEgB,SAAS,EAAE,SAAS;cAAEyE,SAAS,EAAE;YAAS,CAAE;YAAAxF,QAAA,EAAC;UAEhF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELhH,OAAO,gBACNvC,OAAA;QAAKI,KAAK,EAAE;UACVkM,SAAS,EAAE,QAAQ;UACnBnC,OAAO,EAAE,MAAM;UACfnB,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACAlJ,OAAA,CAACb,SAAS;UAACgK,IAAI,EAAE,EAAG;UAAC/I,KAAK,EAAE;YAAE8J,YAAY,EAAE,MAAM;YAAEyB,SAAS,EAAE;UAA0B;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9FvJ,OAAA;UAAAkJ,QAAA,EAAG;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJjC,cAAc,CAACD,MAAM,KAAK,CAAC,gBAC7BrH,OAAA;QAAKI,KAAK,EAAE;UACVkM,SAAS,EAAE,QAAQ;UACnBnC,OAAO,EAAE,MAAM;UACfnB,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACAlJ,OAAA,CAACf,YAAY;UAACkK,IAAI,EAAE,EAAG;UAAC/I,KAAK,EAAE;YAAE8J,YAAY,EAAE,MAAM;YAAE2D,OAAO,EAAE;UAAI;QAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzEvJ,OAAA;UAAAkJ,QAAA,EAAG;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrCvJ,OAAA;UACE8K,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC,CAAE;UACnC7C,KAAK,EAAE;YACL6J,SAAS,EAAE,MAAM;YACjBE,OAAO,EAAE,gBAAgB;YACzBxB,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACdoB,MAAM,EAAE,MAAM;YACdxB,YAAY,EAAE,KAAK;YACnBoC,MAAM,EAAE,SAAS;YACjBK,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENvJ,OAAA;QAAKI,KAAK,EAAE;UACVyI,OAAO,EAAE,MAAM;UACfyB,GAAG,EAAE;QACP,CAAE;QAAApB,QAAA,EACCX,eAAe,CAAC2C,GAAG,CAAE9H,KAAK,iBACzBpD,OAAA;UAEEI,KAAK,EAAE;YACLgK,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,MAAM;YACpBuB,OAAO,EAAE,QAAQ;YACjBxB,eAAe,EAAE,OAAO;YACxBkD,UAAU,EAAE;UACd,CAAE;UAAA3C,QAAA,gBAEFlJ,OAAA;YAAKI,KAAK,EAAE;cACVyI,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBoB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,gBACAlJ,OAAA;cAAKI,KAAK,EAAE;gBAAEwN,IAAI,EAAE;cAAE,CAAE;cAAA1E,QAAA,gBACtBlJ,OAAA;gBAAII,KAAK,EAAE;kBACT6I,QAAQ,EAAE,UAAU;kBACpBoC,UAAU,EAAE,KAAK;kBACjBrC,KAAK,EAAE,SAAS;kBAChBuC,MAAM,EAAE,cAAc;kBACtB1C,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE;gBACP,CAAE;gBAAApB,QAAA,GACC9F,KAAK,CAACyC,KAAK,EACVzC,KAAK,CAASuL,QAAQ,iBACtB3O,OAAA,CAACN,aAAa;kBAACyJ,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLvJ,OAAA;gBAAKI,KAAK,EAAE;kBACVyI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE,MAAM;kBACXrB,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,gBACAlJ,OAAA;kBAAM4O,SAAS,EAAC,yBAAyB;kBAAA1F,QAAA,gBACvClJ,OAAA,CAACf,YAAY;oBAAC2P,SAAS,EAAC;kBAAS;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCnG,KAAK,CAAC+B,UAAU;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNnG,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,iBACpDnF,OAAA;kBAAAkJ,QAAA,GAAM,SAAE,EAAC9F,KAAK,CAAC8B,QAAQ;gBAAA;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/B,eACDvJ,OAAA;kBAAMI,KAAK,EAAE;oBACXuI,eAAe,EAAE,SAAS;oBAC1BK,KAAK,EAAE,SAAS;oBAChBmB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE,SAAS;oBACnBoC,UAAU,EAAE;kBACd,CAAE;kBAAAnC,QAAA,EACCjE,gBAAgB,CAAC7B,KAAK;gBAAC;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACPvJ,OAAA;kBAAMI,KAAK,EAAE;oBACXuI,eAAe,EAAE7D,iBAAiB,CAAC1B,KAAK,CAAC;oBACzC4F,KAAK,EAAE,OAAO;oBACdmB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE;kBACZ,CAAE;kBAAAC,QAAA,EACC9F,KAAK,CAACyL,aAAa,IAAI;gBAAe;kBAAAzF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEJ,CAAC,EACLnG,KAAK,CAAC2E,WAAW,iBAChB/H,OAAA;gBAAGI,KAAK,EAAE;kBACR6I,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBuC,MAAM,EAAE,cAAc;kBACtBgD,UAAU,EAAE;gBACd,CAAE;gBAAArF,QAAA,EACC9F,KAAK,CAAC2E;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACJ,eAGDvJ,OAAA;gBAAKI,KAAK,EAAE;kBACVyI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE,MAAM;kBACXL,SAAS,EAAE;gBACb,CAAE;gBAAAf,QAAA,gBACAlJ,OAAA,CAACH,uBAAuB;kBACtBkE,OAAO,EAAEX,KAAK,CAACK,WAAY;kBAC3BqL,YAAY,EAAG1L,KAAK,CAAS2L,gBAAgB,IAAI,KAAM;kBACvDC,YAAY,EAAG5L,KAAK,CAAS6L,cAAc,IAAI,CAAE;kBACjD9F,IAAI,EAAC,OAAO;kBACZ+F,YAAY,EAAEA,CAACC,KAAc,EAAEC,QAAgB,KAAK;oBAClD;oBACA1L,OAAO,CAACC,GAAG,CAAC,SAASP,KAAK,CAACK,WAAW,gBAAgB,EAAE;sBAAE0L,KAAK;sBAAEC;oBAAS,CAAC,CAAC;kBAC9E;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACAnG,KAAK,CAASiM,cAAc,iBAC5BrP,OAAA;kBAAMI,KAAK,EAAE;oBACXyI,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBwB,GAAG,EAAE,SAAS;oBACdrB,QAAQ,EAAE,UAAU;oBACpBD,KAAK,EAAE;kBACT,CAAE;kBAAAE,QAAA,gBACAlJ,OAAA,CAACL,aAAa;oBAACwJ,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBnG,KAAK,CAASkM,aAAa,IAAI,CAAC,EAAC,WACrC;gBAAA;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvJ,OAAA;cAAKI,KAAK,EAAE;gBACVyI,OAAO,EAAE,MAAM;gBACfyB,GAAG,EAAE,QAAQ;gBACbsB,UAAU,EAAE;cACd,CAAE;cAAA1C,QAAA,gBACAlJ,OAAA;gBACE8K,OAAO,EAAEA,CAAA,KAAM3H,eAAe,CAACC,KAAK,CAAE;gBACtChD,KAAK,EAAE;kBACL+J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBoB,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFlD,KAAK,EAAC,YAAY;gBAAAqD,QAAA,eAElBlJ,OAAA,CAACX,IAAI;kBAAC8J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAETvJ,OAAA;gBACE8K,OAAO,EAAEA,CAAA,KAAO1H,KAAK,CAASmM,YAAY,GACtCtL,oBAAoB,CAACb,KAAK,CAACK,WAAW,CAAC,GACvCK,kBAAkB,CAACV,KAAK,CAACK,WAAW,CACvC;gBACDrD,KAAK,EAAE;kBACL+J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAGvF,KAAK,CAASmM,YAAY,GAAG,SAAS,GAAG,SAAS;kBACpEvG,KAAK,EAAG5F,KAAK,CAASmM,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC1DnF,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFlD,KAAK,EAAGzC,KAAK,CAASmM,YAAY,GAAG,iBAAiB,GAAG,eAAgB;gBAAArG,QAAA,EAEvE9F,KAAK,CAASmM,YAAY,gBAAGvP,OAAA,CAACT,KAAK;kBAAC4J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGvJ,OAAA,CAACV,IAAI;kBAAC6J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAETvJ,OAAA;gBACE8K,OAAO,EAAEA,CAAA,KAAM3G,iBAAiB,CAACf,KAAK,CAACK,WAAW,CAAE;gBACpDrD,KAAK,EAAE;kBACL+J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBoB,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFlD,KAAK,EAAC,cAAc;gBAAAqD,QAAA,eAEpBlJ,OAAA,CAACZ,MAAM;kBAAC+J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvJ,OAAA;YAAKI,KAAK,EAAE;cACVoP,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClBxF,SAAS,EAAE;YACb,CAAE;YAAAf,QAAA,gBACAlJ,OAAA;cAAKI,KAAK,EAAE;gBACV6I,QAAQ,EAAE,UAAU;gBACpBoC,UAAU,EAAE,KAAK;gBACjBrC,KAAK,EAAE,SAAS;gBAChBkB,YAAY,EAAE;cAChB,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvJ,OAAA,CAACwI,WAAW;cAACzE,OAAO,EAAEX,KAAK,CAACK;YAAY;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA,GA1LD,gBAAgBnG,KAAK,CAACK,WAAW,EAAE;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2LrC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvJ,OAAA,CAACwJ,kBAAkB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtBvJ,OAAA,CAAClB,kBAAkB;MACjB4Q,MAAM,EAAE1O,SAAU;MAClB2O,OAAO,EAAE9L,gBAAiB;MAC1B+L,MAAM,EAAEvM,eAAgB;MACxBD,KAAK,EAAElC,YAAa;MACpBJ,YAAY,EAAEA,YAAa;MAC3ByB,OAAO,EAAEf,MAAM,IAAIe;IAAQ;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,EAGDvH,qBAAqB,iBACpBhC,OAAA,CAACF,iBAAiB;MAChB6P,OAAO,EAAEA,CAAA,KAAM;QACb1N,wBAAwB,CAAC,KAAK,CAAC;QAC/B;QACAW,OAAO,CAAC,CAAC;MACX;IAAE;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAtsCK/K,WAAW,EAEQC,qBAAqB;AAAA,EAosC7C,CAAC;EAAA,QAtsCID,WAAW,EAEQC,qBAAqB;AAAA,EAosC5C;AAACoR,GAAA,GAjvCG7Q,QAAkB;AAmvCxBA,QAAQ,CAAC8Q,WAAW,GAAG,UAAU;AAEjC,eAAe9Q,QAAQ;AAAC,IAAAmB,EAAA,EAAA0P,GAAA;AAAAE,YAAA,CAAA5P,EAAA;AAAA4P,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}