{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14 6 4 4\",\n  key: \"1q72g9\"\n}], [\"path\", {\n  d: \"M17 3h4v4\",\n  key: \"19p9u1\"\n}], [\"path\", {\n  d: \"m21 3-7.75 7.75\",\n  key: \"1cjbfd\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"15\",\n  r: \"6\",\n  key: \"bx5svt\"\n}]];\nconst MarsStroke = createLucideIcon(\"mars-stroke\", __iconNode);\nexport { __iconNode, MarsStroke as default };\n//# sourceMappingURL=mars-stroke.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}