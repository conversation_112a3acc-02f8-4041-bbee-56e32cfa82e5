{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 16V9.5a1 1 0 0 1 5 0\",\n  key: \"1i1are\"\n}], [\"path\", {\n  d: \"M8 12h4\",\n  key: \"qz6y1c\"\n}], [\"path\", {\n  d: \"M8 16h7\",\n  key: \"sbedsn\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst CirclePoundSterling = createLucideIcon(\"circle-pound-sterling\", __iconNode);\nexport { __iconNode, CirclePoundSterling as default };\n//# sourceMappingURL=circle-pound-sterling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}