{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"b1zbii\"\n}], [\"path\", {\n  d: \"M22 20H2\",\n  key: \"1p1f7z\"\n}], [\"path\", {\n  d: \"M22 4H2\",\n  key: \"1b7qnq\"\n}]];\nconst AlignVerticalSpaceAround = createLucideIcon(\"align-vertical-space-around\", __iconNode);\nexport { __iconNode, AlignVerticalSpaceAround as default };\n//# sourceMappingURL=align-vertical-space-around.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}