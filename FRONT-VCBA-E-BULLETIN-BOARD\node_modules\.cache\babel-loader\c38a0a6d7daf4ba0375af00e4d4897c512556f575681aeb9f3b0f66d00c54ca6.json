{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\StudentAuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { StudentAuthService } from '../services/student-auth.service';\n\n// Student Auth context interface\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst StudentAuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\nexport const StudentAuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 StudentAuth - Checking authentication status');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n\n      // First check local storage for authentication data\n      const storedUser = StudentAuthService.getStoredUser();\n      const hasToken = StudentAuthService.isAuthenticated();\n      console.log('🔍 StudentAuth - Local auth data:', {\n        hasUser: !!storedUser,\n        hasToken: !!hasToken,\n        userRole: storedUser === null || storedUser === void 0 ? void 0 : storedUser.role,\n        userEmail: storedUser === null || storedUser === void 0 ? void 0 : storedUser.email\n      });\n\n      // If we have local data and it's a student user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'student') {\n        console.log('✅ StudentAuth - Student user authenticated from local storage:', storedUser.email);\n        dispatch({\n          type: 'AUTH_SUCCESS',\n          payload: storedUser\n        });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ StudentAuth - No student user found in local storage or wrong role');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    } catch (error) {\n      console.error('❌ StudentAuth - Auth check failed:', error.message);\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n\n      // Use StudentAuthService for login\n      const response = await StudentAuthService.login(credentials);\n\n      // Verify the user is actually a student\n      if (response.data.user.role !== 'student') {\n        throw new Error('Access denied: Student account required');\n      }\n      console.log('✅ StudentAuth - Student login successful:', response.data.user.email);\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: response.data.user\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Student login failed. Please try again.';\n      console.error('❌ StudentAuth - Login failed:', errorMessage);\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      console.log('🚪 StudentAuth - Starting student logout process');\n      await StudentAuthService.logout();\n      console.log('✅ StudentAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ StudentAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 StudentAuth - Clearing student state');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n\n      // Redirect to student login\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 StudentAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 StudentAuth - Unauthorized request detected, logging out student');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 StudentAuth - Component mounted, checking student auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    clearError,\n    checkAuthStatus,\n    userType: 'student'\n  };\n  return /*#__PURE__*/_jsxDEV(StudentAuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use student auth context\n_s(StudentAuthProvider, \"az7Z8nchPpIKydmJ9mbFqt7e/WQ=\");\n_c = StudentAuthProvider;\nexport const useStudentAuth = () => {\n  _s2();\n  const context = useContext(StudentAuthContext);\n  if (context === undefined) {\n    throw new Error('useStudentAuth must be used within a StudentAuthProvider');\n  }\n  return context;\n};\n_s2(useStudentAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default StudentAuthContext;\nvar _c;\n$RefreshReg$(_c, \"StudentAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "useEffect", "StudentAuthService", "jsxDEV", "_jsxDEV", "initialState", "user", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "StudentAuthContext", "undefined", "StudentAuthProvider", "children", "_s", "dispatch", "clearError", "checkAuthStatus", "console", "log", "storedUser", "getStoredUser", "hasToken", "<PERSON><PERSON>ser", "userRole", "role", "userEmail", "email", "message", "login", "credentials", "response", "data", "Error", "errorMessage", "logout", "window", "location", "href", "value", "userType", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useStudentAuth", "_s2", "context", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/StudentAuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { StudentAuthService } from '../services/student-auth.service';\nimport {\n  AuthState,\n  LoginCredentials\n} from '../types/auth.types';\n\n// Student Auth context interface\ninterface StudentAuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n  checkAuthStatus: () => Promise<void>;\n  userType: 'student';\n}\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: any }\n  | { type: 'AUTH_ERROR'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'C<PERSON>AR_ERROR' };\n\n// Reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return { ...state, isLoading: true, error: null };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'CLEAR_ERROR':\n      return { ...state, error: null };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst StudentAuthContext = createContext<StudentAuthContextType | undefined>(undefined);\n\n// Provider component\nexport const StudentAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 StudentAuth - Checking authentication status');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      // First check local storage for authentication data\n      const storedUser = StudentAuthService.getStoredUser();\n      const hasToken = StudentAuthService.isAuthenticated();\n\n      console.log('🔍 StudentAuth - Local auth data:', {\n        hasUser: !!storedUser,\n        hasToken: !!hasToken,\n        userRole: storedUser?.role,\n        userEmail: storedUser?.email\n      });\n\n      // If we have local data and it's a student user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'student') {\n        console.log('✅ StudentAuth - Student user authenticated from local storage:', storedUser.email);\n        dispatch({ type: 'AUTH_SUCCESS', payload: storedUser });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ StudentAuth - No student user found in local storage or wrong role');\n      dispatch({ type: 'AUTH_LOGOUT' });\n\n    } catch (error: any) {\n      console.error('❌ StudentAuth - Auth check failed:', error.message);\n      dispatch({ type: 'AUTH_LOGOUT' });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      // Use StudentAuthService for login\n      const response = await StudentAuthService.login(credentials);\n      \n      // Verify the user is actually a student\n      if (response.data.user.role !== 'student') {\n        throw new Error('Access denied: Student account required');\n      }\n\n      console.log('✅ StudentAuth - Student login successful:', response.data.user.email);\n      dispatch({ type: 'AUTH_SUCCESS', payload: response.data.user });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Student login failed. Please try again.';\n      console.error('❌ StudentAuth - Login failed:', errorMessage);\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      console.log('🚪 StudentAuth - Starting student logout process');\n      await StudentAuthService.logout();\n      console.log('✅ StudentAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ StudentAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 StudentAuth - Clearing student state');\n      dispatch({ type: 'AUTH_LOGOUT' });\n      \n      // Redirect to student login\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 StudentAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 StudentAuth - Unauthorized request detected, logging out student');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 StudentAuth - Component mounted, checking student auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value: StudentAuthContextType = {\n    ...state,\n    login,\n    logout,\n    clearError,\n    checkAuthStatus,\n    userType: 'student',\n  };\n\n  return (\n    <StudentAuthContext.Provider value={value}>\n      {children}\n    </StudentAuthContext.Provider>\n  );\n};\n\n// Hook to use student auth context\nexport const useStudentAuth = (): StudentAuthContextType => {\n  const context = useContext(StudentAuthContext);\n  if (context === undefined) {\n    throw new Error('useStudentAuth must be used within a StudentAuthProvider');\n  }\n  return context;\n};\n\nexport default StudentAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC5F,SAASC,kBAAkB,QAAQ,kCAAkC;;AAMrE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;;AASA;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QAAE,GAAGF,KAAK;QAAEH,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC;IACnD,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAEM,MAAM,CAACE,OAAO;QACpBP,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,YAAY;MACf,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGE,KAAK;QAAEH,SAAS,EAAEI,MAAM,CAACE;MAAQ,CAAC;IAChD,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGH,KAAK;QAAEF,KAAK,EAAE;MAAK,CAAC;IAClC;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,kBAAkB,gBAAGlB,aAAa,CAAqCmB,SAAS,CAAC;;AAEvF;AACA,OAAO,MAAMC,mBAA4D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGrB,UAAU,CAACW,WAAW,EAAEL,YAAY,CAAC;;EAE/D;EACA,MAAMgB,UAAU,GAAGrB,WAAW,CAAC,MAAM;IACnCoB,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,eAAe,GAAGtB,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFuB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DJ,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;;MAEhD;MACA,MAAMW,UAAU,GAAGvB,kBAAkB,CAACwB,aAAa,CAAC,CAAC;MACrD,MAAMC,QAAQ,GAAGzB,kBAAkB,CAACK,eAAe,CAAC,CAAC;MAErDgB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAC/CI,OAAO,EAAE,CAAC,CAACH,UAAU;QACrBE,QAAQ,EAAE,CAAC,CAACA,QAAQ;QACpBE,QAAQ,EAAEJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,IAAI;QAC1BC,SAAS,EAAEN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO;MACzB,CAAC,CAAC;;MAEF;MACA,IAAIP,UAAU,IAAIE,QAAQ,IAAIF,UAAU,CAACK,IAAI,KAAK,SAAS,EAAE;QAC3DP,OAAO,CAACC,GAAG,CAAC,gEAAgE,EAAEC,UAAU,CAACO,KAAK,CAAC;QAC/FZ,QAAQ,CAAC;UAAEP,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAEW;QAAW,CAAC,CAAC;QACvD;MACF;;MAEA;MACAF,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;MACnFJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IAEnC,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBc,OAAO,CAACd,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAACwB,OAAO,CAAC;MAClEb,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFf,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;;MAEhC;MACA,MAAMuB,QAAQ,GAAG,MAAMlC,kBAAkB,CAACgC,KAAK,CAACC,WAAW,CAAC;;MAE5D;MACA,IAAIC,QAAQ,CAACC,IAAI,CAAC/B,IAAI,CAACwB,IAAI,KAAK,SAAS,EAAE;QACzC,MAAM,IAAIQ,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MAEAf,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEY,QAAQ,CAACC,IAAI,CAAC/B,IAAI,CAAC0B,KAAK,CAAC;MAClFZ,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEsB,QAAQ,CAACC,IAAI,CAAC/B;MAAK,CAAC,CAAC;IACjE,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACwB,OAAO,IAAI,yCAAyC;MAC/EV,OAAO,CAACd,KAAK,CAAC,+BAA+B,EAAE8B,YAAY,CAAC;MAC5DnB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEyB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACFjB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,MAAMtB,kBAAkB,CAACsC,MAAM,CAAC,CAAC;MACjCjB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRc,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;;MAEjC;MACA4B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgB;IACzC;EACF,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACdsB,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;IAC1F;IACA;IACA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACdsB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;IAC/EF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMsB,KAA6B,GAAG;IACpC,GAAGjC,KAAK;IACRuB,KAAK;IACLM,MAAM;IACNnB,UAAU;IACVC,eAAe;IACfuB,QAAQ,EAAE;EACZ,CAAC;EAED,oBACEzC,OAAA,CAACW,kBAAkB,CAAC+B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA1B,QAAA,EACvCA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAElC,CAAC;;AAED;AAAA/B,EAAA,CAlHaF,mBAA4D;AAAAkC,EAAA,GAA5DlC,mBAA4D;AAmHzE,OAAO,MAAMmC,cAAc,GAAGA,CAAA,KAA8B;EAAAC,GAAA;EAC1D,MAAMC,OAAO,GAAGxD,UAAU,CAACiB,kBAAkB,CAAC;EAC9C,IAAIuC,OAAO,KAAKtC,SAAS,EAAE;IACzB,MAAM,IAAIsB,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,OAAOgB,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,cAAc;AAQ3B,eAAerC,kBAAkB;AAAC,IAAAoC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}