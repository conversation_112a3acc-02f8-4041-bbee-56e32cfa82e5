{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 4 8 6\",\n  key: \"1rru8s\"\n}], [\"path\", {\n  d: \"M17 19v2\",\n  key: \"ts1sot\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"M7 19v2\",\n  key: \"12npes\"\n}], [\"path\", {\n  d: \"M9 5 7.621 3.621A2.121 2.121 0 0 0 4 5v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5\",\n  key: \"14ym8i\"\n}]];\nconst Bath = createLucideIcon(\"bath\", __iconNode);\nexport { __iconNode, Bath as default };\n//# sourceMappingURL=bath.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}