{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14 12-8.381 8.38a1 1 0 0 1-3.001-3L11 9\",\n  key: \"5z9253\"\n}], [\"path\", {\n  d: \"M15 15.5a.5.5 0 0 0 .5.5A6.5 6.5 0 0 0 22 9.5a.5.5 0 0 0-.5-.5h-1.672a2 2 0 0 1-1.414-.586l-5.062-5.062a1.205 1.205 0 0 0-1.704 0L9.352 5.648a1.205 1.205 0 0 0 0 1.704l5.062 5.062A2 2 0 0 1 15 13.828z\",\n  key: \"19zklq\"\n}]];\nconst Axe = createLucideIcon(\"axe\", __iconNode);\nexport { __iconNode, Axe as default };\n//# sourceMappingURL=axe.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}