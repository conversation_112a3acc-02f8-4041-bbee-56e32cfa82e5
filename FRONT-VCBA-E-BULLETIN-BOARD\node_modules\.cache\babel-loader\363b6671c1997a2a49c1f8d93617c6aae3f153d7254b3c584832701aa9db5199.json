{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M7 7h.01\",\n  key: \"7u93v4\"\n}], [\"path\", {\n  d: \"M17 7h.01\",\n  key: \"14a9sn\"\n}], [\"path\", {\n  d: \"M7 17h.01\",\n  key: \"19xn7k\"\n}], [\"path\", {\n  d: \"M17 17h.01\",\n  key: \"1sd3ek\"\n}]];\nconst InspectionPanel = createLucideIcon(\"inspection-panel\", __iconNode);\nexport { __iconNode, InspectionPanel as default };\n//# sourceMappingURL=inspection-panel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}