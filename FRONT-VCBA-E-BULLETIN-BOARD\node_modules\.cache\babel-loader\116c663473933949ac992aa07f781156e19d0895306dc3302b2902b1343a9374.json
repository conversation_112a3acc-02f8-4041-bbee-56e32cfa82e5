{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 17 5-5-5-5\",\n  key: \"1bsop3\"\n}], [\"path\", {\n  d: \"M15 12H3\",\n  key: \"6jk70r\"\n}], [\"path\", {\n  d: \"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\",\n  key: \"u53s6r\"\n}]];\nconst LogIn = createLucideIcon(\"log-in\", __iconNode);\nexport { __iconNode, LogIn as default };\n//# sourceMappingURL=log-in.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}