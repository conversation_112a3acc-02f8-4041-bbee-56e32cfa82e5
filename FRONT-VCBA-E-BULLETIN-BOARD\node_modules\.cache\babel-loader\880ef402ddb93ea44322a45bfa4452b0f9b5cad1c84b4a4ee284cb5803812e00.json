{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 3.5c5-2 7 2.5 3 4C1.5 10 2 15 5 16c5 2 9-10 14-7s.5 13.5-4 12c-5-2.5.5-11 6-2\",\n  key: \"1lrphd\"\n}]];\nconst LineSquiggle = createLucideIcon(\"line-squiggle\", __iconNode);\nexport { __iconNode, LineSquiggle as default };\n//# sourceMappingURL=line-squiggle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}