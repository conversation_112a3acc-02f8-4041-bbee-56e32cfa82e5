{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 17h-3\",\n  key: \"1lwga1\"\n}], [\"path\", {\n  d: \"M22 7h-5\",\n  key: \"o2endc\"\n}], [\"path\", {\n  d: \"M5 17H2\",\n  key: \"1gx9xc\"\n}], [\"path\", {\n  d: \"M7 7H2\",\n  key: \"6bq26l\"\n}], [\"rect\", {\n  x: \"5\",\n  y: \"14\",\n  width: \"14\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"1qrzuf\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"4\",\n  width: \"10\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"we8e9z\"\n}]];\nconst AlignVerticalDistributeCenter = createLucideIcon(\"align-vertical-distribute-center\", __iconNode);\nexport { __iconNode, AlignVerticalDistributeCenter as default };\n//# sourceMappingURL=align-vertical-distribute-center.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}