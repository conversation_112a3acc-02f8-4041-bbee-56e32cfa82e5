{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\UnifiedAuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { unifiedAuthService } from '../services/unified-auth.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n  currentRole: null,\n  availableRoles: []\n};\n\n// Action types\n\n// Reducer\nconst unifiedAuthReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        currentRole: action.payload.role,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n        availableRoles: unifiedAuthService.getAvailableRoles()\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        currentRole: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n        availableRoles: []\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        currentRole: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        availableRoles: []\n      };\n    case 'ROLE_SWITCH':\n      return {\n        ...state,\n        user: action.payload.user,\n        currentRole: action.payload.role,\n        isAuthenticated: true,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    case 'UPDATE_AVAILABLE_ROLES':\n      return {\n        ...state,\n        availableRoles: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst UnifiedAuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\nexport const UnifiedAuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(unifiedAuthReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 UnifiedAuth - Checking authentication status');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      const currentRole = unifiedAuthService.getCurrentRole();\n      console.log('🔍 UnifiedAuth - Available roles:', availableRoles);\n      console.log('🔍 UnifiedAuth - Current role:', currentRole);\n      if (availableRoles.length === 0) {\n        console.log('❌ UnifiedAuth - No authenticated roles found');\n        dispatch({\n          type: 'AUTH_LOGOUT'\n        });\n        return;\n      }\n\n      // Use the most recent role or the first available role\n      const roleToUse = currentRole || availableRoles[0];\n      const user = unifiedAuthService.getUser(roleToUse);\n      if (!user) {\n        console.log('❌ UnifiedAuth - No user data found for role:', roleToUse);\n        dispatch({\n          type: 'AUTH_LOGOUT'\n        });\n        return;\n      }\n      console.log('✅ UnifiedAuth - User authenticated:', {\n        role: roleToUse,\n        email: user.email\n      });\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user,\n          role: roleToUse\n        }\n      });\n    } catch (error) {\n      console.error('❌ UnifiedAuth - Auth check failed:', error.message);\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      console.log('🔐 UnifiedAuth - Attempting unified login');\n      const response = await unifiedAuthService.login(credentials);\n      const user = response.data.user;\n      const role = user.role;\n      console.log('✅ UnifiedAuth - Login successful:', {\n        role,\n        email: user.email\n      });\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user,\n          role\n        }\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Login failed. Please try again.';\n      console.error('❌ UnifiedAuth - Login failed:', errorMessage);\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      console.log('🚪 UnifiedAuth - Starting logout process');\n      if (state.currentRole) {\n        await unifiedAuthService.logout(state.currentRole);\n      } else {\n        // Logout from all roles if current role is unknown\n        await unifiedAuthService.logoutAll();\n      }\n      console.log('✅ UnifiedAuth - Logout successful');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n\n      // Redirect based on the role that was logged out\n      const redirectPath = state.currentRole === 'student' ? '/student/login' : '/admin/login';\n      window.location.href = redirectPath;\n    } catch (error) {\n      console.error('❌ UnifiedAuth - Logout error:', error);\n      // Force logout even if server call fails\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Switch role function\n  const switchRole = async targetRole => {\n    try {\n      console.log(`🔄 UnifiedAuth - Switching to ${targetRole} role`);\n      const user = await unifiedAuthService.switchRole(targetRole);\n      console.log(`✅ UnifiedAuth - Role switch successful:`, {\n        role: targetRole,\n        email: user.email\n      });\n      dispatch({\n        type: 'ROLE_SWITCH',\n        payload: {\n          user,\n          role: targetRole\n        }\n      });\n    } catch (error) {\n      const errorMessage = error.message || `Failed to switch to ${targetRole} role`;\n      console.error('❌ UnifiedAuth - Role switch failed:', errorMessage);\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Admin registration function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await unifiedAuthService.registerAdmin(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      return response;\n    } catch (error) {\n      const errorMessage = error.message || 'Registration failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await unifiedAuthService.verifyOtp(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async email => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await unifiedAuthService.resendOtp(email);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Update available roles when storage changes\n  useEffect(() => {\n    const updateRoles = () => {\n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      dispatch({\n        type: 'UPDATE_AVAILABLE_ROLES',\n        payload: availableRoles\n      });\n    };\n\n    // Listen for storage changes (for cross-tab synchronization)\n    window.addEventListener('storage', updateRoles);\n    return () => {\n      window.removeEventListener('storage', updateRoles);\n    };\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 UnifiedAuth - Component mounted, checking auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    switchRole,\n    clearError,\n    checkAuthStatus,\n    register,\n    verifyOtp,\n    resendOtp\n  };\n  return /*#__PURE__*/_jsxDEV(UnifiedAuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use unified auth context\n_s(UnifiedAuthProvider, \"az7Z8nchPpIKydmJ9mbFqt7e/WQ=\");\n_c = UnifiedAuthProvider;\nexport const useUnifiedAuth = () => {\n  _s2();\n  const context = useContext(UnifiedAuthContext);\n  if (context === undefined) {\n    throw new Error('useUnifiedAuth must be used within a UnifiedAuthProvider');\n  }\n  return context;\n};\n_s2(useUnifiedAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default UnifiedAuthContext;\nvar _c;\n$RefreshReg$(_c, \"UnifiedAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "useEffect", "unifiedAuthService", "jsxDEV", "_jsxDEV", "initialState", "user", "isAuthenticated", "isLoading", "error", "currentRole", "availableRoles", "unifiedAuthReducer", "state", "action", "type", "payload", "role", "getAvailableRoles", "UnifiedAuthContext", "undefined", "UnifiedAuthProvider", "children", "_s", "dispatch", "clearError", "checkAuthStatus", "console", "log", "getCurrentRole", "length", "roleToUse", "getUser", "email", "message", "login", "credentials", "response", "data", "errorMessage", "logout", "logoutAll", "redirectPath", "window", "location", "href", "switchRole", "targetRole", "register", "registerAdmin", "verifyOtp", "resendOtp", "updateRoles", "addEventListener", "removeEventListener", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useUnifiedAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/UnifiedAuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { unifiedAuthService } from '../services/unified-auth.service';\nimport { unifiedStorage, UserRole } from '../utils/unifiedStorage';\nimport {\n  UnifiedAuthState,\n  UnifiedLoginCredentials,\n  UnifiedAuthContextType,\n  User,\n  AdminRegistrationData,\n  RegistrationResponse,\n  OtpVerificationData\n} from '../types/auth.types';\n\n// Initial state\nconst initialState: UnifiedAuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n  currentRole: null,\n  availableRoles: [],\n};\n\n// Action types\ntype UnifiedAuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: { user: User; role: UserRole } }\n  | { type: 'AUTH_ERROR'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'ROLE_SWITCH'; payload: { user: User; role: UserRole } }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'CLEAR_ERROR' }\n  | { type: 'UPDATE_AVAILABLE_ROLES'; payload: UserRole[] };\n\n// Reducer\nconst unifiedAuthReducer = (state: UnifiedAuthState, action: UnifiedAuthAction): UnifiedAuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return { ...state, isLoading: true, error: null };\n    \n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        currentRole: action.payload.role,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n        availableRoles: unifiedAuthService.getAvailableRoles(),\n      };\n    \n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        currentRole: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n        availableRoles: [],\n      };\n    \n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        currentRole: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        availableRoles: [],\n      };\n    \n    case 'ROLE_SWITCH':\n      return {\n        ...state,\n        user: action.payload.user,\n        currentRole: action.payload.role,\n        isAuthenticated: true,\n        error: null,\n      };\n    \n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    \n    case 'CLEAR_ERROR':\n      return { ...state, error: null };\n    \n    case 'UPDATE_AVAILABLE_ROLES':\n      return { ...state, availableRoles: action.payload };\n    \n    default:\n      return state;\n  }\n};\n\n// Create context\nconst UnifiedAuthContext = createContext<UnifiedAuthContextType | undefined>(undefined);\n\n// Provider component\nexport const UnifiedAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(unifiedAuthReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 UnifiedAuth - Checking authentication status');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      const currentRole = unifiedAuthService.getCurrentRole();\n\n      console.log('🔍 UnifiedAuth - Available roles:', availableRoles);\n      console.log('🔍 UnifiedAuth - Current role:', currentRole);\n\n      if (availableRoles.length === 0) {\n        console.log('❌ UnifiedAuth - No authenticated roles found');\n        dispatch({ type: 'AUTH_LOGOUT' });\n        return;\n      }\n\n      // Use the most recent role or the first available role\n      const roleToUse = currentRole || availableRoles[0];\n      const user = unifiedAuthService.getUser(roleToUse);\n\n      if (!user) {\n        console.log('❌ UnifiedAuth - No user data found for role:', roleToUse);\n        dispatch({ type: 'AUTH_LOGOUT' });\n        return;\n      }\n\n      console.log('✅ UnifiedAuth - User authenticated:', { role: roleToUse, email: user.email });\n      dispatch({ \n        type: 'AUTH_SUCCESS', \n        payload: { user, role: roleToUse } \n      });\n\n    } catch (error: any) {\n      console.error('❌ UnifiedAuth - Auth check failed:', error.message);\n      dispatch({ type: 'AUTH_LOGOUT' });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials: UnifiedLoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      console.log('🔐 UnifiedAuth - Attempting unified login');\n      const response = await unifiedAuthService.login(credentials);\n      \n      const user = response.data.user;\n      const role = user.role as UserRole;\n\n      console.log('✅ UnifiedAuth - Login successful:', { role, email: user.email });\n      dispatch({ \n        type: 'AUTH_SUCCESS', \n        payload: { user, role } \n      });\n\n    } catch (error: any) {\n      const errorMessage = error.message || 'Login failed. Please try again.';\n      console.error('❌ UnifiedAuth - Login failed:', errorMessage);\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      console.log('🚪 UnifiedAuth - Starting logout process');\n      \n      if (state.currentRole) {\n        await unifiedAuthService.logout(state.currentRole);\n      } else {\n        // Logout from all roles if current role is unknown\n        await unifiedAuthService.logoutAll();\n      }\n      \n      console.log('✅ UnifiedAuth - Logout successful');\n      dispatch({ type: 'AUTH_LOGOUT' });\n      \n      // Redirect based on the role that was logged out\n      const redirectPath = state.currentRole === 'student' ? '/student/login' : '/admin/login';\n      window.location.href = redirectPath;\n      \n    } catch (error) {\n      console.error('❌ UnifiedAuth - Logout error:', error);\n      // Force logout even if server call fails\n      dispatch({ type: 'AUTH_LOGOUT' });\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Switch role function\n  const switchRole = async (targetRole: UserRole): Promise<void> => {\n    try {\n      console.log(`🔄 UnifiedAuth - Switching to ${targetRole} role`);\n      \n      const user = await unifiedAuthService.switchRole(targetRole);\n      \n      console.log(`✅ UnifiedAuth - Role switch successful:`, { role: targetRole, email: user.email });\n      dispatch({ \n        type: 'ROLE_SWITCH', \n        payload: { user, role: targetRole } \n      });\n\n    } catch (error: any) {\n      const errorMessage = error.message || `Failed to switch to ${targetRole} role`;\n      console.error('❌ UnifiedAuth - Role switch failed:', errorMessage);\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Admin registration function\n  const register = async (data: AdminRegistrationData): Promise<RegistrationResponse> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await unifiedAuthService.registerAdmin(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n      return response;\n    } catch (error: any) {\n      const errorMessage = error.message || 'Registration failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async (data: OtpVerificationData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await unifiedAuthService.verifyOtp(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async (email: string): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await unifiedAuthService.resendOtp(email);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Update available roles when storage changes\n  useEffect(() => {\n    const updateRoles = () => {\n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      dispatch({ type: 'UPDATE_AVAILABLE_ROLES', payload: availableRoles });\n    };\n\n    // Listen for storage changes (for cross-tab synchronization)\n    window.addEventListener('storage', updateRoles);\n\n    return () => {\n      window.removeEventListener('storage', updateRoles);\n    };\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 UnifiedAuth - Component mounted, checking auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value: UnifiedAuthContextType = {\n    ...state,\n    login,\n    logout,\n    switchRole,\n    clearError,\n    checkAuthStatus,\n    register,\n    verifyOtp,\n    resendOtp,\n  };\n\n  return (\n    <UnifiedAuthContext.Provider value={value}>\n      {children}\n    </UnifiedAuthContext.Provider>\n  );\n};\n\n// Hook to use unified auth context\nexport const useUnifiedAuth = (): UnifiedAuthContextType => {\n  const context = useContext(UnifiedAuthContext);\n  if (context === undefined) {\n    throw new Error('useUnifiedAuth must be used within a UnifiedAuthProvider');\n  }\n  return context;\n};\n\nexport default UnifiedAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC5F,SAASC,kBAAkB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYtE;AACA,MAAMC,YAA8B,GAAG;EACrCC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE;AAClB,CAAC;;AAED;;AAWA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,KAAuB,EAAEC,MAAyB,KAAuB;EACnG,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QAAE,GAAGF,KAAK;QAAEL,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC;IAEnD,KAAK,cAAc;MACjB,OAAO;QACL,GAAGI,KAAK;QACRP,IAAI,EAAEQ,MAAM,CAACE,OAAO,CAACV,IAAI;QACzBI,WAAW,EAAEI,MAAM,CAACE,OAAO,CAACC,IAAI;QAChCV,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,IAAI;QACXE,cAAc,EAAET,kBAAkB,CAACgB,iBAAiB,CAAC;MACvD,CAAC;IAEH,KAAK,YAAY;MACf,OAAO;QACL,GAAGL,KAAK;QACRP,IAAI,EAAE,IAAI;QACVI,WAAW,EAAE,IAAI;QACjBH,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEK,MAAM,CAACE,OAAO;QACrBL,cAAc,EAAE;MAClB,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRP,IAAI,EAAE,IAAI;QACVI,WAAW,EAAE,IAAI;QACjBH,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,IAAI;QACXE,cAAc,EAAE;MAClB,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRP,IAAI,EAAEQ,MAAM,CAACE,OAAO,CAACV,IAAI;QACzBI,WAAW,EAAEI,MAAM,CAACE,OAAO,CAACC,IAAI;QAChCV,eAAe,EAAE,IAAI;QACrBE,KAAK,EAAE;MACT,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGI,KAAK;QAAEL,SAAS,EAAEM,MAAM,CAACE;MAAQ,CAAC;IAEhD,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGH,KAAK;QAAEJ,KAAK,EAAE;MAAK,CAAC;IAElC,KAAK,wBAAwB;MAC3B,OAAO;QAAE,GAAGI,KAAK;QAAEF,cAAc,EAAEG,MAAM,CAACE;MAAQ,CAAC;IAErD;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMM,kBAAkB,gBAAGtB,aAAa,CAAqCuB,SAAS,CAAC;;AAEvF;AACA,OAAO,MAAMC,mBAA4D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAM,CAACV,KAAK,EAAEW,QAAQ,CAAC,GAAGzB,UAAU,CAACa,kBAAkB,EAAEP,YAAY,CAAC;;EAEtE;EACA,MAAMoB,UAAU,GAAGzB,WAAW,CAAC,MAAM;IACnCwB,QAAQ,CAAC;MAAET,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,eAAe,GAAG1B,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF2B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DJ,QAAQ,CAAC;QAAET,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhD,MAAML,cAAc,GAAGT,kBAAkB,CAACgB,iBAAiB,CAAC,CAAC;MAC7D,MAAMR,WAAW,GAAGR,kBAAkB,CAAC2B,cAAc,CAAC,CAAC;MAEvDF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEjB,cAAc,CAAC;MAChEgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAElB,WAAW,CAAC;MAE1D,IAAIC,cAAc,CAACmB,MAAM,KAAK,CAAC,EAAE;QAC/BH,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3DJ,QAAQ,CAAC;UAAET,IAAI,EAAE;QAAc,CAAC,CAAC;QACjC;MACF;;MAEA;MACA,MAAMgB,SAAS,GAAGrB,WAAW,IAAIC,cAAc,CAAC,CAAC,CAAC;MAClD,MAAML,IAAI,GAAGJ,kBAAkB,CAAC8B,OAAO,CAACD,SAAS,CAAC;MAElD,IAAI,CAACzB,IAAI,EAAE;QACTqB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEG,SAAS,CAAC;QACtEP,QAAQ,CAAC;UAAET,IAAI,EAAE;QAAc,CAAC,CAAC;QACjC;MACF;MAEAY,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;QAAEX,IAAI,EAAEc,SAAS;QAAEE,KAAK,EAAE3B,IAAI,CAAC2B;MAAM,CAAC,CAAC;MAC1FT,QAAQ,CAAC;QACPT,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAEV,IAAI;UAAEW,IAAI,EAAEc;QAAU;MACnC,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtB,KAAU,EAAE;MACnBkB,OAAO,CAAClB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAACyB,OAAO,CAAC;MAClEV,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoB,KAAK,GAAG,MAAOC,WAAoC,IAAoB;IAC3E,IAAI;MACFZ,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhCY,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,MAAMS,QAAQ,GAAG,MAAMnC,kBAAkB,CAACiC,KAAK,CAACC,WAAW,CAAC;MAE5D,MAAM9B,IAAI,GAAG+B,QAAQ,CAACC,IAAI,CAAChC,IAAI;MAC/B,MAAMW,IAAI,GAAGX,IAAI,CAACW,IAAgB;MAElCU,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAAEX,IAAI;QAAEgB,KAAK,EAAE3B,IAAI,CAAC2B;MAAM,CAAC,CAAC;MAC7ET,QAAQ,CAAC;QACPT,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAEV,IAAI;UAAEW;QAAK;MACxB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACyB,OAAO,IAAI,iCAAiC;MACvEP,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAE8B,YAAY,CAAC;MAC5Df,QAAQ,CAAC;QAAET,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAEvD,IAAIf,KAAK,CAACH,WAAW,EAAE;QACrB,MAAMR,kBAAkB,CAACsC,MAAM,CAAC3B,KAAK,CAACH,WAAW,CAAC;MACpD,CAAC,MAAM;QACL;QACA,MAAMR,kBAAkB,CAACuC,SAAS,CAAC,CAAC;MACtC;MAEAd,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDJ,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAc,CAAC,CAAC;;MAEjC;MACA,MAAM2B,YAAY,GAAG7B,KAAK,CAACH,WAAW,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc;MACxFiC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,YAAY;IAErC,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAe,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAc,CAAC,CAAC;MACjC4B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;IACvC;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAOC,UAAoB,IAAoB;IAChE,IAAI;MACFpB,OAAO,CAACC,GAAG,CAAC,iCAAiCmB,UAAU,OAAO,CAAC;MAE/D,MAAMzC,IAAI,GAAG,MAAMJ,kBAAkB,CAAC4C,UAAU,CAACC,UAAU,CAAC;MAE5DpB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QAAEX,IAAI,EAAE8B,UAAU;QAAEd,KAAK,EAAE3B,IAAI,CAAC2B;MAAM,CAAC,CAAC;MAC/FT,QAAQ,CAAC;QACPT,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE;UAAEV,IAAI;UAAEW,IAAI,EAAE8B;QAAW;MACpC,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtC,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACyB,OAAO,IAAI,uBAAuBa,UAAU,OAAO;MAC9EpB,OAAO,CAAClB,KAAK,CAAC,qCAAqC,EAAE8B,YAAY,CAAC;MAClEf,QAAQ,CAAC;QAAET,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMuC,QAAQ,GAAG,MAAOV,IAA2B,IAAoC;IACrF,IAAI;MACFd,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMsB,QAAQ,GAAG,MAAMnC,kBAAkB,CAAC+C,aAAa,CAACX,IAAI,CAAC;MAE7Dd,QAAQ,CAAC;QAAET,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjD,OAAOqB,QAAQ;IACjB,CAAC,CAAC,OAAO5B,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACyB,OAAO,IAAI,wCAAwC;MAC9EV,QAAQ,CAAC;QAAET,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMyC,SAAS,GAAG,MAAOZ,IAAyB,IAAoB;IACpE,IAAI;MACFd,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMb,kBAAkB,CAACgD,SAAS,CAACZ,IAAI,CAAC;MAExCd,QAAQ,CAAC;QAAET,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACyB,OAAO,IAAI,4CAA4C;MAClFV,QAAQ,CAAC;QAAET,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0C,SAAS,GAAG,MAAOlB,KAAa,IAAoB;IACxD,IAAI;MACFT,QAAQ,CAAC;QAAET,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMb,kBAAkB,CAACiD,SAAS,CAAClB,KAAK,CAAC;MAEzCT,QAAQ,CAAC;QAAET,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnB,MAAM8B,YAAY,GAAG9B,KAAK,CAACyB,OAAO,IAAI,yCAAyC;MAC/EV,QAAQ,CAAC;QAAET,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM9B,KAAK;IACb;EACF,CAAC;;EAED;EACAR,SAAS,CAAC,MAAM;IACd,MAAMmD,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMzC,cAAc,GAAGT,kBAAkB,CAACgB,iBAAiB,CAAC,CAAC;MAC7DM,QAAQ,CAAC;QAAET,IAAI,EAAE,wBAAwB;QAAEC,OAAO,EAAEL;MAAe,CAAC,CAAC;IACvE,CAAC;;IAED;IACAgC,MAAM,CAACU,gBAAgB,CAAC,SAAS,EAAED,WAAW,CAAC;IAE/C,OAAO,MAAM;MACXT,MAAM,CAACW,mBAAmB,CAAC,SAAS,EAAEF,WAAW,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnD,SAAS,CAAC,MAAM;IACd0B,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvEF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAM6B,KAA6B,GAAG;IACpC,GAAG1C,KAAK;IACRsB,KAAK;IACLK,MAAM;IACNM,UAAU;IACVrB,UAAU;IACVC,eAAe;IACfsB,QAAQ;IACRE,SAAS;IACTC;EACF,CAAC;EAED,oBACE/C,OAAA,CAACe,kBAAkB,CAACqC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EACvCA;EAAQ;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAElC,CAAC;;AAED;AAAArC,EAAA,CAhNaF,mBAA4D;AAAAwC,EAAA,GAA5DxC,mBAA4D;AAiNzE,OAAO,MAAMyC,cAAc,GAAGA,CAAA,KAA8B;EAAAC,GAAA;EAC1D,MAAMC,OAAO,GAAGlE,UAAU,CAACqB,kBAAkB,CAAC;EAC9C,IAAI6C,OAAO,KAAK5C,SAAS,EAAE;IACzB,MAAM,IAAI6C,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,cAAc;AAQ3B,eAAe3C,kBAAkB;AAAC,IAAA0C,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}