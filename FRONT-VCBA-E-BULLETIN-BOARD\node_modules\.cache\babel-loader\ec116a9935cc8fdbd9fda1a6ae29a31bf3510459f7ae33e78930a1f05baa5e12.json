{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v20\",\n  key: \"t6zp3m\"\n}], [\"path\", {\n  d: \"m15 19-3 3-3-3\",\n  key: \"11eu04\"\n}], [\"path\", {\n  d: \"m19 9 3 3-3 3\",\n  key: \"1mg7y2\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"m5 9-3 3 3 3\",\n  key: \"j64kie\"\n}], [\"path\", {\n  d: \"m9 5 3-3 3 3\",\n  key: \"l8vdw6\"\n}]];\nconst Move = createLucideIcon(\"move\", __iconNode);\nexport { __iconNode, Move as default };\n//# sourceMappingURL=move.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}