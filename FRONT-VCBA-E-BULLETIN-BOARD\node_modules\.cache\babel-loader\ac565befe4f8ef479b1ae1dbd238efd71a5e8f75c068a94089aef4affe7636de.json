{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n  key: \"uqj9uw\"\n}], [\"path\", {\n  d: \"M16 9a5 5 0 0 1 0 6\",\n  key: \"1q6k2b\"\n}], [\"path\", {\n  d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n  key: \"ijwkga\"\n}]];\nconst Volume2 = createLucideIcon(\"volume-2\", __iconNode);\nexport { __iconNode, Volume2 as default };\n//# sourceMappingURL=volume-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}