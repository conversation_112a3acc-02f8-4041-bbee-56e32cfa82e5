{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2\",\n  key: \"1ksdt3\"\n}], [\"path\", {\n  d: \"M22 13a18.15 18.15 0 0 1-20 0\",\n  key: \"12hx5q\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"i6l2r4\"\n}]];\nconst BriefcaseBusiness = createLucideIcon(\"briefcase-business\", __iconNode);\nexport { __iconNode, BriefcaseBusiness as default };\n//# sourceMappingURL=briefcase-business.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}