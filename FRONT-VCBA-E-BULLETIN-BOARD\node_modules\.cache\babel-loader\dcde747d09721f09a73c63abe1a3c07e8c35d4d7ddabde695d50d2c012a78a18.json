{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6\",\n  key: \"y09zxi\"\n}], [\"path\", {\n  d: \"m21 3-9 9\",\n  key: \"mpx6sq\"\n}], [\"path\", {\n  d: \"M15 3h6v6\",\n  key: \"1q9fwt\"\n}]];\nconst SquareArrowOutUpRight = createLucideIcon(\"square-arrow-out-up-right\", __iconNode);\nexport { __iconNode, SquareArrowOutUpRight as default };\n//# sourceMappingURL=square-arrow-out-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}