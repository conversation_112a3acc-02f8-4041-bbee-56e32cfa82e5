{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 8h-3\",\n  key: \"xvov4w\"\n}], [\"path\", {\n  d: \"m15 2-1 2h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h3\",\n  key: \"16uttc\"\n}], [\"path\", {\n  d: \"M16.899 22A5 5 0 0 0 7.1 22\",\n  key: \"1d0ppr\"\n}], [\"path\", {\n  d: \"m9 2 3 6\",\n  key: \"1o7bd9\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"15\",\n  r: \"3\",\n  key: \"g36mzq\"\n}]];\nconst IdCardLanyard = createLucideIcon(\"id-card-lanyard\", __iconNode);\nexport { __iconNode, IdCardLanyard as default };\n//# sourceMappingURL=id-card-lanyard.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}