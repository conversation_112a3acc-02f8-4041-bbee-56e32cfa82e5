{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2c1 3 2.5 3.5 3.5 4.5A5 5 0 0 1 17 10a5 5 0 1 1-10 0c0-.3 0-.6.1-.9a2 2 0 1 0 3.3-2C8 4.5 11 2 12 2Z\",\n  key: \"1ir223\"\n}], [\"path\", {\n  d: \"m5 22 14-4\",\n  key: \"1brv4h\"\n}], [\"path\", {\n  d: \"m5 18 14 4\",\n  key: \"lgyyje\"\n}]];\nconst FlameKindling = createLucideIcon(\"flame-kindling\", __iconNode);\nexport { __iconNode, FlameKindling as default };\n//# sourceMappingURL=flame-kindling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}