{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n  key: \"vv11sd\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}], [\"path\", {\n  d: \"M12 8v8\",\n  key: \"napkw2\"\n}]];\nconst MessageCirclePlus = createLucideIcon(\"message-circle-plus\", __iconNode);\nexport { __iconNode, MessageCirclePlus as default };\n//# sourceMappingURL=message-circle-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}