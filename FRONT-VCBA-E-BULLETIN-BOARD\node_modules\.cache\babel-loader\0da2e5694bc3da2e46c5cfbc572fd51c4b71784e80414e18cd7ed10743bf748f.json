{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 19V9a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v8a2 2 0 0 0 2 2h2\",\n  key: \"19jm3t\"\n}], [\"path\", {\n  d: \"M2 9h3a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H2\",\n  key: \"13hakp\"\n}], [\"path\", {\n  d: \"M22 17v1a1 1 0 0 1-1 1H10v-9a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v9\",\n  key: \"1crci8\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"t8fc5s\"\n}]];\nconst Caravan = createLucideIcon(\"caravan\", __iconNode);\nexport { __iconNode, <PERSON><PERSON> as default };\n//# sourceMappingURL=caravan.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}