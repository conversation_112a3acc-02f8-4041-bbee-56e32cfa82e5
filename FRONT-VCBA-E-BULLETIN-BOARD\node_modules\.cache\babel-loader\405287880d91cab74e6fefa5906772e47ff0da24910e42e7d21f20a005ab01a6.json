{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\NotificationBell.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { studentNotificationService } from '../../services/notificationService';\nimport { useNotificationNavigation } from '../../hooks/useNotificationNavigation';\nimport { Bell, Megaphone, AlertTriangle, MessageCircle, Heart, Settings, Clock } from 'lucide-react';\nimport '../../styles/notificationHighlight.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentNotificationBell = ({\n  className = ''\n}) => {\n  _s();\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isOpen, setIsOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  // Initialize notification navigation\n  const {\n    handleNotificationClick\n  } = useNotificationNavigation({\n    userRole: 'student',\n    onNavigationStart: notification => {\n      console.log('🚀 Starting navigation for notification:', notification.title);\n      setIsOpen(false); // Close dropdown when navigating\n    },\n    onNavigationComplete: (notification, success) => {\n      if (success) {\n        console.log('✅ Navigation completed successfully');\n        // Update notification as read in local state\n        setNotifications(prev => prev.map(notif => notif.notification_id === notification.notification_id ? {\n          ...notif,\n          is_read: true\n        } : notif));\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      } else {\n        console.error('❌ Navigation failed');\n      }\n    },\n    onNavigationError: (error, notification) => {\n      console.error('🚨 Navigation error:', error, 'for notification:', notification.title);\n      // Could show user-friendly error message here\n    }\n  });\n\n  // Fetch notifications and unread count\n  const fetchNotifications = async () => {\n    try {\n      setLoading(true);\n      console.log('🔔 Fetching student notifications...');\n      const [notificationsResponse, unreadResponse] = await Promise.all([studentNotificationService.getNotifications({\n        limit: 10,\n        sort_by: 'created_at',\n        sort_order: 'DESC'\n      }), studentNotificationService.getUnreadCount()]);\n      if (notificationsResponse.success && notificationsResponse.data) {\n        setNotifications(notificationsResponse.data.notifications);\n        console.log('✅ Student notifications loaded:', notificationsResponse.data.notifications.length);\n      } else {\n        console.warn('⚠️ Student notifications response not successful:', notificationsResponse);\n      }\n      if (unreadResponse.success && unreadResponse.data) {\n        setUnreadCount(unreadResponse.data.unreadCount);\n        console.log('✅ Student unread count loaded:', unreadResponse.data.unreadCount);\n      } else {\n        console.warn('⚠️ Student unread count response not successful:', unreadResponse);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching student notifications:', error);\n\n      // Show user-friendly error message\n      if (error instanceof Error && error.message.includes('Network connection failed')) {\n        console.error('🚨 Backend connection issue detected. Please check if the server is running.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle notification click with navigation\n  const handleNotificationItemClick = async notification => {\n    try {\n      console.log('🔔 Student notification clicked:', notification);\n      await handleNotificationClick(notification);\n    } catch (error) {\n      console.error('Error handling notification click:', error);\n    }\n  };\n\n  // Mark notification as read (for mark all read functionality)\n  const markAsRead = async notificationId => {\n    try {\n      await studentNotificationService.markAsRead(notificationId);\n      setNotifications(prev => prev.map(notif => notif.notification_id === notificationId ? {\n        ...notif,\n        is_read: true\n      } : notif));\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  // Mark all as read\n  const markAllAsRead = async () => {\n    try {\n      await studentNotificationService.markAllAsRead();\n      setNotifications(prev => prev.map(notif => ({\n        ...notif,\n        is_read: true\n      })));\n      setUnreadCount(0);\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n    }\n  };\n\n  // Format time ago\n  const getTimeAgo = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n  };\n\n  // Get notification icon\n  const getNotificationIcon = type => {\n    const icons = {\n      1: Megaphone,\n      // announcement\n      2: AlertTriangle,\n      // alert\n      3: MessageCircle,\n      // comment\n      4: Heart,\n      // reaction\n      5: Settings,\n      // system\n      6: Clock // reminder\n    };\n    return icons[type] || Bell;\n  };\n  useEffect(() => {\n    fetchNotifications();\n\n    // Poll for new notifications every 30 seconds\n    const interval = setInterval(fetchNotifications, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      style: {\n        position: 'relative',\n        background: 'none',\n        border: 'none',\n        cursor: 'pointer',\n        padding: '0.5rem',\n        borderRadius: '50%',\n        transition: 'background-color 0.2s ease'\n      },\n      onMouseOver: e => {\n        e.currentTarget.style.backgroundColor = '#f3f4f6';\n      },\n      onMouseOut: e => {\n        e.currentTarget.style.backgroundColor = 'transparent';\n      },\n      children: [/*#__PURE__*/_jsxDEV(Bell, {\n        size: 24,\n        color: \"#1f2937\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          position: 'absolute',\n          top: '0.25rem',\n          right: '0.25rem',\n          background: '#ef4444',\n          color: 'white',\n          borderRadius: '50%',\n          width: '1.25rem',\n          height: '1.25rem',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          fontSize: '0.75rem',\n          fontWeight: '600'\n        },\n        children: unreadCount > 99 ? '99+' : unreadCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '100%',\n        right: 0,\n        width: '400px',\n        maxHeight: '500px',\n        background: 'white',\n        border: '1px solid #e5e7eb',\n        borderRadius: '12px',\n        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n        zIndex: 1000,\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          borderBottom: '1px solid #e5e7eb',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            fontSize: '1.125rem',\n            fontWeight: '600',\n            color: '#1f2937'\n          },\n          children: \"Notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: markAllAsRead,\n          style: {\n            background: 'none',\n            border: 'none',\n            color: '#3b82f6',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: 'pointer'\n          },\n          children: \"Mark all read\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '400px',\n          overflowY: 'auto'\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '2rem',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: \"Loading notifications...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 15\n        }, this) : notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '2rem',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'block',\n              marginBottom: '0.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bell, {\n              size: 48,\n              color: \"#6b7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this), \"No notifications yet\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 15\n        }, this) : notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => handleNotificationItemClick(notification),\n          style: {\n            padding: '1rem',\n            borderBottom: '1px solid #f3f4f6',\n            cursor: 'pointer',\n            background: notification.is_read ? 'white' : '#eff6ff',\n            transition: 'all 0.2s ease'\n          },\n          onMouseOver: e => {\n            e.currentTarget.style.backgroundColor = notification.is_read ? '#f9fafb' : '#dbeafe';\n            e.currentTarget.style.transform = 'translateX(2px)';\n          },\n          onMouseOut: e => {\n            e.currentTarget.style.backgroundColor = notification.is_read ? 'white' : '#eff6ff';\n            e.currentTarget.style.transform = 'translateX(0)';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.75rem',\n              alignItems: 'flex-start'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/React.createElement(getNotificationIcon(notification.notification_type_id), {\n                size: 20,\n                color: '#6b7280'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: notification.is_read ? '400' : '600',\n                  color: '#1f2937',\n                  marginBottom: '0.25rem'\n                },\n                children: notification.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.8rem',\n                  color: '#6b7280',\n                  marginBottom: '0.5rem'\n                },\n                children: notification.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#9ca3af'\n                },\n                children: getTimeAgo(notification.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 21\n            }, this), !notification.is_read && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '8px',\n                height: '8px',\n                borderRadius: '50%',\n                background: '#3b82f6'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 19\n          }, this)\n        }, notification.notification_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '0.75rem',\n          borderTop: '1px solid #e5e7eb',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            background: 'none',\n            border: 'none',\n            color: '#3b82f6',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: 'pointer'\n          },\n          children: \"View all notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 999\n      },\n      onClick: () => setIsOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentNotificationBell, \"YeugN10kJrt844xXnQiACMriVdY=\", false, function () {\n  return [useNotificationNavigation];\n});\n_c = StudentNotificationBell;\nexport default StudentNotificationBell;\nvar _c;\n$RefreshReg$(_c, \"StudentNotificationBell\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "studentNotificationService", "useNotificationNavigation", "Bell", "Megaphone", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageCircle", "Heart", "Settings", "Clock", "jsxDEV", "_jsxDEV", "StudentNotificationBell", "className", "_s", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "isOpen", "setIsOpen", "loading", "setLoading", "handleNotificationClick", "userRole", "onNavigationStart", "notification", "console", "log", "title", "onNavigationComplete", "success", "prev", "map", "notif", "notification_id", "is_read", "Math", "max", "error", "onNavigationError", "fetchNotifications", "notificationsResponse", "unreadResponse", "Promise", "all", "getNotifications", "limit", "sort_by", "sort_order", "getUnreadCount", "data", "length", "warn", "Error", "message", "includes", "handleNotificationItemClick", "mark<PERSON><PERSON><PERSON>", "notificationId", "markAllAsRead", "getTimeAgo", "dateString", "date", "Date", "now", "diffInSeconds", "floor", "getTime", "getNotificationIcon", "type", "icons", "interval", "setInterval", "clearInterval", "children", "onClick", "style", "position", "background", "border", "cursor", "padding", "borderRadius", "transition", "onMouseOver", "e", "currentTarget", "backgroundColor", "onMouseOut", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "right", "width", "height", "display", "alignItems", "justifyContent", "fontSize", "fontWeight", "maxHeight", "boxShadow", "zIndex", "overflow", "borderBottom", "margin", "overflowY", "textAlign", "marginBottom", "transform", "gap", "createElement", "notification_type_id", "flex", "created_at", "borderTop", "left", "bottom", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/NotificationBell.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { studentNotificationService, type Notification } from '../../services/notificationService';\nimport { useNotificationNavigation } from '../../hooks/useNotificationNavigation';\nimport { Bell, Megaphone, AlertTriangle, MessageCircle, Heart, Settings, Clock } from 'lucide-react';\nimport '../../styles/notificationHighlight.css';\n\ninterface NotificationBellProps {\n  className?: string;\n}\n\nconst StudentNotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isOpen, setIsOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  // Initialize notification navigation\n  const { handleNotificationClick } = useNotificationNavigation({\n    userRole: 'student',\n    onNavigationStart: (notification) => {\n      console.log('🚀 Starting navigation for notification:', notification.title);\n      setIsOpen(false); // Close dropdown when navigating\n    },\n    onNavigationComplete: (notification, success) => {\n      if (success) {\n        console.log('✅ Navigation completed successfully');\n        // Update notification as read in local state\n        setNotifications(prev =>\n          prev.map(notif =>\n            notif.notification_id === notification.notification_id\n              ? { ...notif, is_read: true }\n              : notif\n          )\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      } else {\n        console.error('❌ Navigation failed');\n      }\n    },\n    onNavigationError: (error, notification) => {\n      console.error('🚨 Navigation error:', error, 'for notification:', notification.title);\n      // Could show user-friendly error message here\n    }\n  });\n\n  // Fetch notifications and unread count\n  const fetchNotifications = async () => {\n    try {\n      setLoading(true);\n      console.log('🔔 Fetching student notifications...');\n\n      const [notificationsResponse, unreadResponse] = await Promise.all([\n        studentNotificationService.getNotifications({ limit: 10, sort_by: 'created_at', sort_order: 'DESC' }),\n        studentNotificationService.getUnreadCount()\n      ]);\n\n      if (notificationsResponse.success && notificationsResponse.data) {\n        setNotifications(notificationsResponse.data.notifications);\n        console.log('✅ Student notifications loaded:', notificationsResponse.data.notifications.length);\n      } else {\n        console.warn('⚠️ Student notifications response not successful:', notificationsResponse);\n      }\n\n      if (unreadResponse.success && unreadResponse.data) {\n        setUnreadCount(unreadResponse.data.unreadCount);\n        console.log('✅ Student unread count loaded:', unreadResponse.data.unreadCount);\n      } else {\n        console.warn('⚠️ Student unread count response not successful:', unreadResponse);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching student notifications:', error);\n\n      // Show user-friendly error message\n      if (error instanceof Error && error.message.includes('Network connection failed')) {\n        console.error('🚨 Backend connection issue detected. Please check if the server is running.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle notification click with navigation\n  const handleNotificationItemClick = async (notification: Notification) => {\n    try {\n      console.log('🔔 Student notification clicked:', notification);\n      await handleNotificationClick(notification);\n    } catch (error) {\n      console.error('Error handling notification click:', error);\n    }\n  };\n\n  // Mark notification as read (for mark all read functionality)\n  const markAsRead = async (notificationId: number) => {\n    try {\n      await studentNotificationService.markAsRead(notificationId);\n      setNotifications(prev =>\n        prev.map(notif =>\n          notif.notification_id === notificationId\n            ? { ...notif, is_read: true }\n            : notif\n        )\n      );\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  // Mark all as read\n  const markAllAsRead = async () => {\n    try {\n      await studentNotificationService.markAllAsRead();\n      setNotifications(prev => \n        prev.map(notif => ({ ...notif, is_read: true }))\n      );\n      setUnreadCount(0);\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n    }\n  };\n\n  // Format time ago\n  const getTimeAgo = (dateString: string): string => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n  };\n\n  // Get notification icon\n  const getNotificationIcon = (type: number): React.ComponentType<{ size?: number; color?: string }> => {\n    const icons: Record<number, React.ComponentType<{ size?: number; color?: string }>> = {\n      1: Megaphone,     // announcement\n      2: AlertTriangle, // alert\n      3: MessageCircle, // comment\n      4: Heart,         // reaction\n      5: Settings,      // system\n      6: Clock          // reminder\n    };\n    return icons[type] || Bell;\n  };\n\n  useEffect(() => {\n    fetchNotifications();\n    \n    // Poll for new notifications every 30 seconds\n    const interval = setInterval(fetchNotifications, 30000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className={`relative ${className}`}>\n      {/* Notification Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        style={{\n          position: 'relative',\n          background: 'none',\n          border: 'none',\n          cursor: 'pointer',\n          padding: '0.5rem',\n          borderRadius: '50%',\n          transition: 'background-color 0.2s ease'\n        }}\n        onMouseOver={(e) => {\n          e.currentTarget.style.backgroundColor = '#f3f4f6';\n        }}\n        onMouseOut={(e) => {\n          e.currentTarget.style.backgroundColor = 'transparent';\n        }}\n      >\n        <Bell size={24} color=\"#1f2937\" />\n        \n        {/* Unread Count Badge */}\n        {unreadCount > 0 && (\n          <span style={{\n            position: 'absolute',\n            top: '0.25rem',\n            right: '0.25rem',\n            background: '#ef4444',\n            color: 'white',\n            borderRadius: '50%',\n            width: '1.25rem',\n            height: '1.25rem',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '0.75rem',\n            fontWeight: '600'\n          }}>\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Notifications Dropdown */}\n      {isOpen && (\n        <div style={{\n          position: 'absolute',\n          top: '100%',\n          right: 0,\n          width: '400px',\n          maxHeight: '500px',\n          background: 'white',\n          border: '1px solid #e5e7eb',\n          borderRadius: '12px',\n          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n          zIndex: 1000,\n          overflow: 'hidden'\n        }}>\n          {/* Header */}\n          <div style={{\n            padding: '1rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          }}>\n            <h3 style={{\n              margin: 0,\n              fontSize: '1.125rem',\n              fontWeight: '600',\n              color: '#1f2937'\n            }}>\n              Notifications\n            </h3>\n            {unreadCount > 0 && (\n              <button\n                onClick={markAllAsRead}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#3b82f6',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}\n              >\n                Mark all read\n              </button>\n            )}\n          </div>\n\n          {/* Notifications List */}\n          <div style={{\n            maxHeight: '400px',\n            overflowY: 'auto'\n          }}>\n            {loading ? (\n              <div style={{\n                padding: '2rem',\n                textAlign: 'center',\n                color: '#6b7280'\n              }}>\n                Loading notifications...\n              </div>\n            ) : notifications.length === 0 ? (\n              <div style={{\n                padding: '2rem',\n                textAlign: 'center',\n                color: '#6b7280'\n              }}>\n                <div style={{ display: 'block', marginBottom: '0.5rem' }}>\n                  <Bell size={48} color=\"#6b7280\" />\n                </div>\n                No notifications yet\n              </div>\n            ) : (\n              notifications.map((notification) => (\n                <div\n                  key={notification.notification_id}\n                  onClick={() => handleNotificationItemClick(notification)}\n                  style={{\n                    padding: '1rem',\n                    borderBottom: '1px solid #f3f4f6',\n                    cursor: 'pointer',\n                    background: notification.is_read ? 'white' : '#eff6ff',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseOver={(e) => {\n                    e.currentTarget.style.backgroundColor = notification.is_read ? '#f9fafb' : '#dbeafe';\n                    e.currentTarget.style.transform = 'translateX(2px)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.currentTarget.style.backgroundColor = notification.is_read ? 'white' : '#eff6ff';\n                    e.currentTarget.style.transform = 'translateX(0)';\n                  }}\n                >\n                  <div style={{\n                    display: 'flex',\n                    gap: '0.75rem',\n                    alignItems: 'flex-start'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                      {React.createElement(getNotificationIcon(notification.notification_type_id), { size: 20, color: '#6b7280' })}\n                    </div>\n                    <div style={{ flex: 1 }}>\n                      <div style={{\n                        fontSize: '0.875rem',\n                        fontWeight: notification.is_read ? '400' : '600',\n                        color: '#1f2937',\n                        marginBottom: '0.25rem'\n                      }}>\n                        {notification.title}\n                      </div>\n                      <div style={{\n                        fontSize: '0.8rem',\n                        color: '#6b7280',\n                        marginBottom: '0.5rem'\n                      }}>\n                        {notification.message}\n                      </div>\n                      <div style={{\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      }}>\n                        {getTimeAgo(notification.created_at)}\n                      </div>\n                    </div>\n                    {!notification.is_read && (\n                      <div style={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        background: '#3b82f6'\n                      }} />\n                    )}\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div style={{\n              padding: '0.75rem',\n              borderTop: '1px solid #e5e7eb',\n              textAlign: 'center'\n            }}>\n              <button style={{\n                background: 'none',\n                border: 'none',\n                color: '#3b82f6',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: 'pointer'\n              }}>\n                View all notifications\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Overlay to close dropdown */}\n      {isOpen && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 999\n          }}\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default StudentNotificationBell;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,0BAA0B,QAA2B,oCAAoC;AAClG,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,IAAI,EAAEC,SAAS,EAAEC,aAAa,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,cAAc;AACpG,OAAO,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhD,MAAMC,uBAAwD,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM;IAAEwB;EAAwB,CAAC,GAAGrB,yBAAyB,CAAC;IAC5DsB,QAAQ,EAAE,SAAS;IACnBC,iBAAiB,EAAGC,YAAY,IAAK;MACnCC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,YAAY,CAACG,KAAK,CAAC;MAC3ET,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IACpB,CAAC;IACDU,oBAAoB,EAAEA,CAACJ,YAAY,EAAEK,OAAO,KAAK;MAC/C,IAAIA,OAAO,EAAE;QACXJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD;QACAZ,gBAAgB,CAACgB,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,KAAK,IACZA,KAAK,CAACC,eAAe,KAAKT,YAAY,CAACS,eAAe,GAClD;UAAE,GAAGD,KAAK;UAAEE,OAAO,EAAE;QAAK,CAAC,GAC3BF,KACN,CACF,CAAC;QACDhB,cAAc,CAACc,IAAI,IAAIK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,IAAI,GAAG,CAAC,CAAC,CAAC;MAC/C,CAAC,MAAM;QACLL,OAAO,CAACY,KAAK,CAAC,qBAAqB,CAAC;MACtC;IACF,CAAC;IACDC,iBAAiB,EAAEA,CAACD,KAAK,EAAEb,YAAY,KAAK;MAC1CC,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAE,mBAAmB,EAAEb,YAAY,CAACG,KAAK,CAAC;MACrF;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMY,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,MAAM,CAACc,qBAAqB,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChE5C,0BAA0B,CAAC6C,gBAAgB,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAO,CAAC,CAAC,EACrGhD,0BAA0B,CAACiD,cAAc,CAAC,CAAC,CAC5C,CAAC;MAEF,IAAIR,qBAAqB,CAACX,OAAO,IAAIW,qBAAqB,CAACS,IAAI,EAAE;QAC/DnC,gBAAgB,CAAC0B,qBAAqB,CAACS,IAAI,CAACpC,aAAa,CAAC;QAC1DY,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEc,qBAAqB,CAACS,IAAI,CAACpC,aAAa,CAACqC,MAAM,CAAC;MACjG,CAAC,MAAM;QACLzB,OAAO,CAAC0B,IAAI,CAAC,mDAAmD,EAAEX,qBAAqB,CAAC;MAC1F;MAEA,IAAIC,cAAc,CAACZ,OAAO,IAAIY,cAAc,CAACQ,IAAI,EAAE;QACjDjC,cAAc,CAACyB,cAAc,CAACQ,IAAI,CAAClC,WAAW,CAAC;QAC/CU,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEe,cAAc,CAACQ,IAAI,CAAClC,WAAW,CAAC;MAChF,CAAC,MAAM;QACLU,OAAO,CAAC0B,IAAI,CAAC,kDAAkD,EAAEV,cAAc,CAAC;MAClF;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;MAE/D;MACA,IAAIA,KAAK,YAAYe,KAAK,IAAIf,KAAK,CAACgB,OAAO,CAACC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;QACjF7B,OAAO,CAACY,KAAK,CAAC,8EAA8E,CAAC;MAC/F;IACF,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,2BAA2B,GAAG,MAAO/B,YAA0B,IAAK;IACxE,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEF,YAAY,CAAC;MAC7D,MAAMH,uBAAuB,CAACG,YAAY,CAAC;IAC7C,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;EACF,CAAC;;EAED;EACA,MAAMmB,UAAU,GAAG,MAAOC,cAAsB,IAAK;IACnD,IAAI;MACF,MAAM1D,0BAA0B,CAACyD,UAAU,CAACC,cAAc,CAAC;MAC3D3C,gBAAgB,CAACgB,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,KAAK,IACZA,KAAK,CAACC,eAAe,KAAKwB,cAAc,GACpC;QAAE,GAAGzB,KAAK;QAAEE,OAAO,EAAE;MAAK,CAAC,GAC3BF,KACN,CACF,CAAC;MACDhB,cAAc,CAACc,IAAI,IAAIK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,IAAI,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMqB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM3D,0BAA0B,CAAC2D,aAAa,CAAC,CAAC;MAChD5C,gBAAgB,CAACgB,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,KAAK,KAAK;QAAE,GAAGA,KAAK;QAAEE,OAAO,EAAE;MAAK,CAAC,CAAC,CACjD,CAAC;MACDlB,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAClE;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,aAAa,GAAG7B,IAAI,CAAC8B,KAAK,CAAC,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,GAAGL,IAAI,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;IAEzE,IAAIF,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAG7B,IAAI,CAAC8B,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC,OAAO;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAG7B,IAAI,CAAC8B,KAAK,CAACD,aAAa,GAAG,IAAI,CAAC,OAAO;IAC5E,OAAO,GAAG7B,IAAI,CAAC8B,KAAK,CAACD,aAAa,GAAG,KAAK,CAAC,OAAO;EACpD,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,IAAY,IAA6D;IACpG,MAAMC,KAA6E,GAAG;MACpF,CAAC,EAAEnE,SAAS;MAAM;MAClB,CAAC,EAAEC,aAAa;MAAE;MAClB,CAAC,EAAEC,aAAa;MAAE;MAClB,CAAC,EAAEC,KAAK;MAAU;MAClB,CAAC,EAAEC,QAAQ;MAAO;MAClB,CAAC,EAAEC,KAAK,CAAU;IACpB,CAAC;IACD,OAAO8D,KAAK,CAACD,IAAI,CAAC,IAAInE,IAAI;EAC5B,CAAC;EAEDH,SAAS,CAAC,MAAM;IACdyC,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAM+B,QAAQ,GAAGC,WAAW,CAAChC,kBAAkB,EAAE,KAAK,CAAC;IAEvD,OAAO,MAAMiC,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7D,OAAA;IAAKE,SAAS,EAAE,YAAYA,SAAS,EAAG;IAAA8D,QAAA,gBAEtChE,OAAA;MACEiE,OAAO,EAAEA,CAAA,KAAMxD,SAAS,CAAC,CAACD,MAAM,CAAE;MAClC0D,KAAK,EAAE;QACLC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,QAAQ;QACjBC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;MACd,CAAE;MACFC,WAAW,EAAGC,CAAC,IAAK;QAClBA,CAAC,CAACC,aAAa,CAACV,KAAK,CAACW,eAAe,GAAG,SAAS;MACnD,CAAE;MACFC,UAAU,EAAGH,CAAC,IAAK;QACjBA,CAAC,CAACC,aAAa,CAACV,KAAK,CAACW,eAAe,GAAG,aAAa;MACvD,CAAE;MAAAb,QAAA,gBAEFhE,OAAA,CAACR,IAAI;QAACuF,IAAI,EAAE,EAAG;QAACC,KAAK,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGjC9E,WAAW,GAAG,CAAC,iBACdN,OAAA;QAAMkE,KAAK,EAAE;UACXC,QAAQ,EAAE,UAAU;UACpBkB,GAAG,EAAE,SAAS;UACdC,KAAK,EAAE,SAAS;UAChBlB,UAAU,EAAE,SAAS;UACrBY,KAAK,EAAE,OAAO;UACdR,YAAY,EAAE,KAAK;UACnBe,KAAK,EAAE,SAAS;UAChBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAA7B,QAAA,EACC1D,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;MAAW;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGR5E,MAAM,iBACLR,OAAA;MAAKkE,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBkB,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,OAAO;QACdO,SAAS,EAAE,OAAO;QAClB1B,UAAU,EAAE,OAAO;QACnBC,MAAM,EAAE,mBAAmB;QAC3BG,YAAY,EAAE,MAAM;QACpBuB,SAAS,EAAE,iCAAiC;QAC5CC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE;MACZ,CAAE;MAAAjC,QAAA,gBAEAhE,OAAA;QAAKkE,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACf2B,YAAY,EAAE,mBAAmB;UACjCT,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE;QACd,CAAE;QAAA1B,QAAA,gBACAhE,OAAA;UAAIkE,KAAK,EAAE;YACTiC,MAAM,EAAE,CAAC;YACTP,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBb,KAAK,EAAE;UACT,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJ9E,WAAW,GAAG,CAAC,iBACdN,OAAA;UACEiE,OAAO,EAAEhB,aAAc;UACvBiB,KAAK,EAAE;YACLE,UAAU,EAAE,MAAM;YAClBC,MAAM,EAAE,MAAM;YACdW,KAAK,EAAE,SAAS;YAChBY,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBvB,MAAM,EAAE;UACV,CAAE;UAAAN,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpF,OAAA;QAAKkE,KAAK,EAAE;UACV4B,SAAS,EAAE,OAAO;UAClBM,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,EACCtD,OAAO,gBACNV,OAAA;UAAKkE,KAAK,EAAE;YACVK,OAAO,EAAE,MAAM;YACf8B,SAAS,EAAE,QAAQ;YACnBrB,KAAK,EAAE;UACT,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACJhF,aAAa,CAACqC,MAAM,KAAK,CAAC,gBAC5BzC,OAAA;UAAKkE,KAAK,EAAE;YACVK,OAAO,EAAE,MAAM;YACf8B,SAAS,EAAE,QAAQ;YACnBrB,KAAK,EAAE;UACT,CAAE;UAAAhB,QAAA,gBACAhE,OAAA;YAAKkE,KAAK,EAAE;cAAEuB,OAAO,EAAE,OAAO;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAtC,QAAA,eACvDhE,OAAA,CAACR,IAAI;cAACuF,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,wBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAENhF,aAAa,CAACkB,GAAG,CAAEP,YAAY,iBAC7Bf,OAAA;UAEEiE,OAAO,EAAEA,CAAA,KAAMnB,2BAA2B,CAAC/B,YAAY,CAAE;UACzDmD,KAAK,EAAE;YACLK,OAAO,EAAE,MAAM;YACf2B,YAAY,EAAE,mBAAmB;YACjC5B,MAAM,EAAE,SAAS;YACjBF,UAAU,EAAErD,YAAY,CAACU,OAAO,GAAG,OAAO,GAAG,SAAS;YACtDgD,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGC,CAAC,IAAK;YAClBA,CAAC,CAACC,aAAa,CAACV,KAAK,CAACW,eAAe,GAAG9D,YAAY,CAACU,OAAO,GAAG,SAAS,GAAG,SAAS;YACpFkD,CAAC,CAACC,aAAa,CAACV,KAAK,CAACqC,SAAS,GAAG,iBAAiB;UACrD,CAAE;UACFzB,UAAU,EAAGH,CAAC,IAAK;YACjBA,CAAC,CAACC,aAAa,CAACV,KAAK,CAACW,eAAe,GAAG9D,YAAY,CAACU,OAAO,GAAG,OAAO,GAAG,SAAS;YAClFkD,CAAC,CAACC,aAAa,CAACV,KAAK,CAACqC,SAAS,GAAG,eAAe;UACnD,CAAE;UAAAvC,QAAA,eAEFhE,OAAA;YAAKkE,KAAK,EAAE;cACVuB,OAAO,EAAE,MAAM;cACfe,GAAG,EAAE,SAAS;cACdd,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,gBACAhE,OAAA;cAAKkE,KAAK,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAA3B,QAAA,eAC7E7E,KAAK,CAACsH,aAAa,CAAC/C,mBAAmB,CAAC3C,YAAY,CAAC2F,oBAAoB,CAAC,EAAE;gBAAE3B,IAAI,EAAE,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACNpF,OAAA;cAAKkE,KAAK,EAAE;gBAAEyC,IAAI,EAAE;cAAE,CAAE;cAAA3C,QAAA,gBACtBhE,OAAA;gBAAKkE,KAAK,EAAE;kBACV0B,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE9E,YAAY,CAACU,OAAO,GAAG,KAAK,GAAG,KAAK;kBAChDuD,KAAK,EAAE,SAAS;kBAChBsB,YAAY,EAAE;gBAChB,CAAE;gBAAAtC,QAAA,EACCjD,YAAY,CAACG;cAAK;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNpF,OAAA;gBAAKkE,KAAK,EAAE;kBACV0B,QAAQ,EAAE,QAAQ;kBAClBZ,KAAK,EAAE,SAAS;kBAChBsB,YAAY,EAAE;gBAChB,CAAE;gBAAAtC,QAAA,EACCjD,YAAY,CAAC6B;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNpF,OAAA;gBAAKkE,KAAK,EAAE;kBACV0B,QAAQ,EAAE,SAAS;kBACnBZ,KAAK,EAAE;gBACT,CAAE;gBAAAhB,QAAA,EACCd,UAAU,CAACnC,YAAY,CAAC6F,UAAU;cAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL,CAACrE,YAAY,CAACU,OAAO,iBACpBzB,OAAA;cAAKkE,KAAK,EAAE;gBACVqB,KAAK,EAAE,KAAK;gBACZC,MAAM,EAAE,KAAK;gBACbhB,YAAY,EAAE,KAAK;gBACnBJ,UAAU,EAAE;cACd;YAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAzDDrE,YAAY,CAACS,eAAe;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0D9B,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLhF,aAAa,CAACqC,MAAM,GAAG,CAAC,iBACvBzC,OAAA;QAAKkE,KAAK,EAAE;UACVK,OAAO,EAAE,SAAS;UAClBsC,SAAS,EAAE,mBAAmB;UAC9BR,SAAS,EAAE;QACb,CAAE;QAAArC,QAAA,eACAhE,OAAA;UAAQkE,KAAK,EAAE;YACbE,UAAU,EAAE,MAAM;YAClBC,MAAM,EAAE,MAAM;YACdW,KAAK,EAAE,SAAS;YAChBY,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBvB,MAAM,EAAE;UACV,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA5E,MAAM,iBACLR,OAAA;MACEkE,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBkB,GAAG,EAAE,CAAC;QACNyB,IAAI,EAAE,CAAC;QACPxB,KAAK,EAAE,CAAC;QACRyB,MAAM,EAAE,CAAC;QACTf,MAAM,EAAE;MACV,CAAE;MACF/B,OAAO,EAAEA,CAAA,KAAMxD,SAAS,CAAC,KAAK;IAAE;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CA9WIF,uBAAwD;EAAA,QAOxBV,yBAAyB;AAAA;AAAAyH,EAAA,GAPzD/G,uBAAwD;AAgX9D,eAAeA,uBAAuB;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}