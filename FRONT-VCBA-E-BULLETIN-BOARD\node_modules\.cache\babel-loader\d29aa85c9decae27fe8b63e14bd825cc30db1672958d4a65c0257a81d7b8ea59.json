{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.2 8.4c.5.38.8.97.8 1.6v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V10a2 2 0 0 1 .8-1.6l8-6a2 2 0 0 1 2.4 0l8 6Z\",\n  key: \"1jhwl8\"\n}], [\"path\", {\n  d: \"m22 10-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 10\",\n  key: \"1qfld7\"\n}]];\nconst MailOpen = createLucideIcon(\"mail-open\", __iconNode);\nexport { __iconNode, MailOpen as default };\n//# sourceMappingURL=mail-open.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}