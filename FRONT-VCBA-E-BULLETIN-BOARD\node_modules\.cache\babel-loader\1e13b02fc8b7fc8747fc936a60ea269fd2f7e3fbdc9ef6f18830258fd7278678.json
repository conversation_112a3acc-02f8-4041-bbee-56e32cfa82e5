{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 21H8a2 2 0 0 1-1.42-.587l-3.994-3.999a2 2 0 0 1 0-2.828l10-10a2 2 0 0 1 2.829 0l5.999 6a2 2 0 0 1 0 2.828L12.834 21\",\n  key: \"g5wo59\"\n}], [\"path\", {\n  d: \"m5.082 11.09 8.828 8.828\",\n  key: \"1wx5vj\"\n}]];\nconst Eraser = createLucideIcon(\"eraser\", __iconNode);\nexport { __iconNode, Eraser as default };\n//# sourceMappingURL=eraser.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}