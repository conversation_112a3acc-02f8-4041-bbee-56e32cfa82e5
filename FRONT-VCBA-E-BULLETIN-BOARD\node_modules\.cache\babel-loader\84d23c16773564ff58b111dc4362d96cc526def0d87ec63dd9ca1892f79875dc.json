{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\",\n  key: \"1u773s\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}]];\nconst CircleQuestionMark = createLucideIcon(\"circle-question-mark\", __iconNode);\nexport { __iconNode, CircleQuestionMark as default };\n//# sourceMappingURL=circle-question-mark.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}