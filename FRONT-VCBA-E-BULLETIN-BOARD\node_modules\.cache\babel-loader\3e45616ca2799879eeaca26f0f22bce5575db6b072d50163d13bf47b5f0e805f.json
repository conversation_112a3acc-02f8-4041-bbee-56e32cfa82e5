{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Admin Protected Route Component\nconst AdminProtectedRoute = ({\n  children,\n  requiredRole,\n  redirectTo\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAdminAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: \"Checking authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ AdminProtectedRoute - User not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo || '/admin/login',\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ AdminProtectedRoute - Role mismatch:', {\n      userRole: user.role,\n      requiredRole\n    });\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ AdminProtectedRoute - Access granted');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Student Protected Route Component\n_s(AdminProtectedRoute, \"Q/Wy693qavFlaSd3QuyBDPnKMXk=\", false, function () {\n  return [useAdminAuth, useLocation];\n});\n_c = AdminProtectedRoute;\nconst StudentProtectedRoute = ({\n  children,\n  requiredRole,\n  redirectTo\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useStudentAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: \"Checking authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ StudentProtectedRoute - User not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo || '/student/login',\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ StudentProtectedRoute - Role mismatch:', {\n      userRole: user.role,\n      requiredRole\n    });\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ StudentProtectedRoute - Access granted');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Unified Protected Route Component\n_s2(StudentProtectedRoute, \"n6jH2ir7ODVkWJS6EpLdBRTW75U=\", false, function () {\n  return [useStudentAuth, useLocation];\n});\n_c2 = StudentProtectedRoute;\nconst UnifiedProtectedRoute = ({\n  children,\n  requiredRole,\n  redirectTo\n}) => {\n  _s3();\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    currentRole\n  } = useUnifiedAuth();\n  const location = useLocation();\n  console.log('🔍 UnifiedProtectedRoute - Auth state:', {\n    isAuthenticated,\n    isLoading,\n    currentRole,\n    requiredRole,\n    userEmail: user === null || user === void 0 ? void 0 : user.email\n  });\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user || !currentRole) {\n    console.log('❌ UnifiedProtectedRoute - User not authenticated, redirecting to unified login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo || '/login',\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && currentRole !== requiredRole) {\n    console.log('❌ UnifiedProtectedRoute - Role mismatch:', {\n      currentRole,\n      requiredRole\n    });\n\n    // Redirect to appropriate dashboard based on current role\n    const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/newsfeed';\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: dashboardPath,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ UnifiedProtectedRoute - Access granted');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Main Protected Route Component that routes to appropriate sub-component\n_s3(UnifiedProtectedRoute, \"TYYLUoNiBsCFzO9lxwKdR6rVwX4=\", false, function () {\n  return [useUnifiedAuth, useLocation];\n});\n_c3 = UnifiedProtectedRoute;\nconst ProtectedRoute = props => {\n  _s4();\n  const location = useLocation();\n\n  // Use unified auth if explicitly requested\n  if (props.useUnified) {\n    return /*#__PURE__*/_jsxDEV(UnifiedProtectedRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Determine which protected route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 12\n    }, this);\n  } else if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(StudentProtectedRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Fallback - redirect to unified login for new routes\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    state: {\n      from: location\n    },\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 10\n  }, this);\n};\n_s4(ProtectedRoute, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c4 = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"AdminProtectedRoute\");\n$RefreshReg$(_c2, \"StudentProtectedRoute\");\n$RefreshReg$(_c3, \"UnifiedProtectedRoute\");\n$RefreshReg$(_c4, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdminAuth", "useStudentAuth", "useUnifiedAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminProtectedRoute", "children", "requiredRole", "redirectTo", "_s", "isAuthenticated", "user", "isLoading", "location", "style", "display", "alignItems", "justifyContent", "minHeight", "flexDirection", "gap", "background", "width", "height", "border", "borderTop", "borderRadius", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "console", "log", "to", "state", "from", "replace", "role", "userRole", "_c", "StudentProtectedRoute", "_s2", "_c2", "UnifiedProtectedRoute", "_s3", "currentRole", "userEmail", "email", "className", "dashboardPath", "_c3", "ProtectedRoute", "props", "_s4", "useUnified", "pathname", "startsWith", "_c4", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { UserRole } from '../../types';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: UserRole;\n  redirectTo?: string;\n  useUnified?: boolean; // Flag to use unified auth context\n}\n\n// Admin Protected Route Component\nconst AdminProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  redirectTo,\n}) => {\n  const { isAuthenticated, user, isLoading } = useAdminAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }}></div>\n        <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n          Checking authentication...\n        </p>\n      </div>\n    );\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ AdminProtectedRoute - User not authenticated, redirecting to login');\n    return <Navigate to={redirectTo || '/admin/login'} state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ AdminProtectedRoute - Role mismatch:', { userRole: user.role, requiredRole });\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  console.log('✅ AdminProtectedRoute - Access granted');\n  return <>{children}</>;\n};\n\n// Student Protected Route Component\nconst StudentProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  redirectTo,\n}) => {\n  const { isAuthenticated, user, isLoading } = useStudentAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }}></div>\n        <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n          Checking authentication...\n        </p>\n      </div>\n    );\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ StudentProtectedRoute - User not authenticated, redirecting to login');\n    return <Navigate to={redirectTo || '/student/login'} state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ StudentProtectedRoute - Role mismatch:', { userRole: user.role, requiredRole });\n    return <Navigate to=\"/student/dashboard\" replace />;\n  }\n\n  console.log('✅ StudentProtectedRoute - Access granted');\n  return <>{children}</>;\n};\n\n// Unified Protected Route Component\nconst UnifiedProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  redirectTo,\n}) => {\n  const { user, isAuthenticated, isLoading, currentRole } = useUnifiedAuth();\n  const location = useLocation();\n\n  console.log('🔍 UnifiedProtectedRoute - Auth state:', {\n    isAuthenticated,\n    isLoading,\n    currentRole,\n    requiredRole,\n    userEmail: user?.email,\n  });\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user || !currentRole) {\n    console.log('❌ UnifiedProtectedRoute - User not authenticated, redirecting to unified login');\n    return <Navigate to={redirectTo || '/login'} state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && currentRole !== requiredRole) {\n    console.log('❌ UnifiedProtectedRoute - Role mismatch:', { currentRole, requiredRole });\n\n    // Redirect to appropriate dashboard based on current role\n    const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/newsfeed';\n    return <Navigate to={dashboardPath} replace />;\n  }\n\n  console.log('✅ UnifiedProtectedRoute - Access granted');\n  return <>{children}</>;\n};\n\n// Main Protected Route Component that routes to appropriate sub-component\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = (props) => {\n  const location = useLocation();\n\n  // Use unified auth if explicitly requested\n  if (props.useUnified) {\n    return <UnifiedProtectedRoute {...props} />;\n  }\n\n  // Determine which protected route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return <AdminProtectedRoute {...props} />;\n  } else if (location.pathname.startsWith('/student')) {\n    return <StudentProtectedRoute {...props} />;\n  }\n\n  // Fallback - redirect to unified login for new routes\n  return <Navigate to=\"/login\" state={{ from: location }} replace />;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAExD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASnE;AACA,MAAMC,mBAAkD,GAAGA,CAAC;EAC1DC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGd,YAAY,CAAC,CAAC;EAC3D,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIe,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKY,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBACAJ,OAAA;QAAKY,KAAK,EAAE;UACVQ,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACT7B,OAAA;QAAGY,KAAK,EAAE;UAAEkB,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAA3B,QAAA,EAAC;MAEtD;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7BuB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;IACnF,oBAAOjC,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAE5B,UAAU,IAAI,cAAe;MAAC6B,KAAK,EAAE;QAAEC,IAAI,EAAEzB;MAAS,CAAE;MAAC0B,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1F;;EAEA;EACA,IAAIxB,YAAY,IAAII,IAAI,CAAC6B,IAAI,KAAKjC,YAAY,EAAE;IAC9C2B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MAAEM,QAAQ,EAAE9B,IAAI,CAAC6B,IAAI;MAAEjC;IAAa,CAAC,CAAC;IAC5F,oBAAOL,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAC,kBAAkB;MAACG,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;EAEAG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACrD,oBAAOjC,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAG,EAAA,CAnDMJ,mBAAkD;EAAA,QAKTP,YAAY,EACxCD,WAAW;AAAA;AAAA6C,EAAA,GANxBrC,mBAAkD;AAoDxD,MAAMsC,qBAAoD,GAAGA,CAAC;EAC5DrC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAoC,GAAA;EACJ,MAAM;IAAElC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGb,cAAc,CAAC,CAAC;EAC7D,MAAMc,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIe,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKY,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBACAJ,OAAA;QAAKY,KAAK,EAAE;UACVQ,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACT7B,OAAA;QAAGY,KAAK,EAAE;UAAEkB,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAA3B,QAAA,EAAC;MAEtD;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7BuB,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;IACrF,oBAAOjC,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAE5B,UAAU,IAAI,gBAAiB;MAAC6B,KAAK,EAAE;QAAEC,IAAI,EAAEzB;MAAS,CAAE;MAAC0B,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5F;;EAEA;EACA,IAAIxB,YAAY,IAAII,IAAI,CAAC6B,IAAI,KAAKjC,YAAY,EAAE;IAC9C2B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MAAEM,QAAQ,EAAE9B,IAAI,CAAC6B,IAAI;MAAEjC;IAAa,CAAC,CAAC;IAC9F,oBAAOL,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAC,oBAAoB;MAACG,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEAG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACvD,oBAAOjC,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAsC,GAAA,CAnDMD,qBAAoD;EAAA,QAKX5C,cAAc,EAC1CF,WAAW;AAAA;AAAAgD,GAAA,GANxBF,qBAAoD;AAoD1D,MAAMG,qBAAoD,GAAGA,CAAC;EAC5DxC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAuC,GAAA;EACJ,MAAM;IAAEpC,IAAI;IAAED,eAAe;IAAEE,SAAS;IAAEoC;EAAY,CAAC,GAAGhD,cAAc,CAAC,CAAC;EAC1E,MAAMa,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9BqC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;IACpDzB,eAAe;IACfE,SAAS;IACToC,WAAW;IACXzC,YAAY;IACZ0C,SAAS,EAAEtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC;EACnB,CAAC,CAAC;;EAEF;EACA,IAAItC,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKiD,SAAS,EAAC,+CAA+C;MAAA7C,QAAA,eAC5DJ,OAAA;QAAKiD,SAAS,EAAC;MAAiE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,IAAI,CAACC,IAAI,IAAI,CAACqC,WAAW,EAAE;IAC7Cd,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;IAC7F,oBAAOjC,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAE5B,UAAU,IAAI,QAAS;MAAC6B,KAAK,EAAE;QAAEC,IAAI,EAAEzB;MAAS,CAAE;MAAC0B,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpF;;EAEA;EACA,IAAIxB,YAAY,IAAIyC,WAAW,KAAKzC,YAAY,EAAE;IAChD2B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MAAEa,WAAW;MAAEzC;IAAa,CAAC,CAAC;;IAEtF;IACA,MAAM6C,aAAa,GAAGJ,WAAW,KAAK,OAAO,GAAG,kBAAkB,GAAG,mBAAmB;IACxF,oBAAO9C,OAAA,CAACN,QAAQ;MAACwC,EAAE,EAAEgB,aAAc;MAACb,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChD;EAEAG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACvD,oBAAOjC,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAyC,GAAA,CA5CMD,qBAAoD;EAAA,QAKE9C,cAAc,EACvDH,WAAW;AAAA;AAAAwD,GAAA,GANxBP,qBAAoD;AA6C1D,MAAMQ,cAA6C,GAAIC,KAAK,IAAK;EAAAC,GAAA;EAC/D,MAAM3C,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAI0D,KAAK,CAACE,UAAU,EAAE;IACpB,oBAAOvD,OAAA,CAAC4C,qBAAqB;MAAA,GAAKS;IAAK;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;;EAEA;EACA,IAAIlB,QAAQ,CAAC6C,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;IAC1C,oBAAOzD,OAAA,CAACG,mBAAmB;MAAA,GAAKkD;IAAK;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC3C,CAAC,MAAM,IAAIlB,QAAQ,CAAC6C,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACnD,oBAAOzD,OAAA,CAACyC,qBAAqB;MAAA,GAAKY;IAAK;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;;EAEA;EACA,oBAAO7B,OAAA,CAACN,QAAQ;IAACwC,EAAE,EAAC,QAAQ;IAACC,KAAK,EAAE;MAAEC,IAAI,EAAEzB;IAAS,CAAE;IAAC0B,OAAO;EAAA;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpE,CAAC;AAACyB,GAAA,CAjBIF,cAA6C;EAAA,QAChCzD,WAAW;AAAA;AAAA+D,GAAA,GADxBN,cAA6C;AAmBnD,eAAeA,cAAc;AAAC,IAAAZ,EAAA,EAAAG,GAAA,EAAAQ,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAnB,EAAA;AAAAmB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}