{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 20h16\",\n  key: \"14thso\"\n}], [\"path\", {\n  d: \"m6 16 6-12 6 12\",\n  key: \"1b4byz\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}]];\nconst Baseline = createLucideIcon(\"baseline\", __iconNode);\nexport { __iconNode, Baseline as default };\n//# sourceMappingURL=baseline.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}