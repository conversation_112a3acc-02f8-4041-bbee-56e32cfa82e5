{"ast": null, "code": "import { API_ENDPOINTS } from '../config/constants';\nimport { httpClient } from './api.service';\nclass StudentService {\n  async createStudent(studentData) {\n    try {\n      var _responseData$data;\n      const response = await httpClient.post(API_ENDPOINTS.ADMIN.STUDENTS, studentData);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n      if (responseData !== null && responseData !== void 0 && responseData.student) {\n        return responseData.student;\n      } else if (responseData !== null && responseData !== void 0 && responseData.success && responseData !== null && responseData !== void 0 && (_responseData$data = responseData.data) !== null && _responseData$data !== void 0 && _responseData$data.student) {\n        return responseData.data.student;\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error) {\n      console.error('Error creating student:', error);\n\n      // If it's a validation error, provide more specific details\n      if (error.details && Array.isArray(error.details)) {\n        const validationErrors = error.details.map(detail => `${detail.field}: ${detail.message}`).join(', ');\n        throw new Error(`Validation failed: ${validationErrors}`);\n      }\n      throw new Error(error.message || 'Failed to create student account');\n    }\n  }\n  async getStudents(params) {\n    try {\n      // Filter out undefined values to avoid sending \"undefined\" as string\n      const cleanParams = {};\n      if (params) {\n        if (params.page !== undefined) cleanParams.page = params.page;\n        if (params.limit !== undefined) cleanParams.limit = params.limit;\n        if (params.search !== undefined && params.search !== '') cleanParams.search = params.search;\n        if (params.grade_level !== undefined) cleanParams.grade_level = params.grade_level;\n        if (params.section !== undefined && params.section !== '') cleanParams.section = params.section;\n        if (params.is_active !== undefined) cleanParams.is_active = params.is_active.toString();\n      }\n      const response = await httpClient.get(API_ENDPOINTS.ADMIN.STUDENTS, cleanParams);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n      if (responseData !== null && responseData !== void 0 && responseData.success && responseData !== null && responseData !== void 0 && responseData.students && responseData !== null && responseData !== void 0 && responseData.pagination) {\n        return {\n          students: responseData.students,\n          pagination: responseData.pagination\n        };\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error) {\n      console.error('Error fetching students:', error);\n      throw new Error(error.message || 'Failed to fetch students');\n    }\n  }\n  async getStudent(studentId) {\n    try {\n      var _response$data, _response$data$data;\n      const response = await httpClient.get(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));\n      if (!((_response$data = response.data) !== null && _response$data !== void 0 && (_response$data$data = _response$data.data) !== null && _response$data$data !== void 0 && _response$data$data.student)) {\n        throw new Error('Invalid response from server');\n      }\n      return response.data.data.student;\n    } catch (error) {\n      console.error('Error fetching student:', error);\n      throw new Error(error.message || 'Failed to fetch student');\n    }\n  }\n  async updateStudent(studentId, studentData) {\n    try {\n      var _responseData$data2;\n      const response = await httpClient.put(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId), studentData);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n      if (responseData !== null && responseData !== void 0 && responseData.student) {\n        return responseData.student;\n      } else if (responseData !== null && responseData !== void 0 && responseData.success && responseData !== null && responseData !== void 0 && (_responseData$data2 = responseData.data) !== null && _responseData$data2 !== void 0 && _responseData$data2.student) {\n        return responseData.data.student;\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error) {\n      console.error('Error updating student:', error);\n      throw new Error(error.message || 'Failed to update student');\n    }\n  }\n  async deleteStudent(studentId) {\n    try {\n      await httpClient.delete(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));\n    } catch (error) {\n      console.error('Error deleting student:', error);\n      throw new Error(error.message || 'Failed to delete student');\n    }\n  }\n  async resetStudentPassword(studentId, newPassword = 'Student123') {\n    try {\n      await httpClient.post(API_ENDPOINTS.ADMIN.RESET_STUDENT_PASSWORD(studentId), {\n        newPassword: newPassword\n      });\n    } catch (error) {\n      console.error('Error resetting student password:', error);\n      throw new Error(error.message || 'Failed to reset student password');\n    }\n  }\n}\nexport const studentService = new StudentService();", "map": {"version": 3, "names": ["API_ENDPOINTS", "httpClient", "StudentService", "createStudent", "studentData", "_responseData$data", "response", "post", "ADMIN", "STUDENTS", "responseData", "data", "student", "success", "Error", "error", "console", "details", "Array", "isArray", "validationErrors", "map", "detail", "field", "message", "join", "getStudents", "params", "cleanParams", "page", "undefined", "limit", "search", "grade_level", "section", "is_active", "toString", "get", "students", "pagination", "getStudent", "studentId", "_response$data", "_response$data$data", "STUDENT_BY_ID", "updateStudent", "_responseData$data2", "put", "deleteStudent", "delete", "resetStudentPassword", "newPassword", "RESET_STUDENT_PASSWORD", "studentService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/studentService.ts"], "sourcesContent": ["import { API_ENDPOINTS } from '../config/constants';\nimport { httpClient } from './api.service';\nimport { ApiResponse } from '../types';\n\nexport interface CreateStudentRequest {\n  // Account data\n  student_number: string;\n  email: string;\n  password: string;\n  is_active?: boolean;\n  created_by: number;\n  \n  // Profile data\n  first_name: string;\n  middle_name?: string;\n  last_name: string;\n  suffix?: string;\n  phone_number: string;\n  grade_level: number;\n  section: string;\n  parent_guardian_name?: string;\n  parent_guardian_phone?: string;\n  address?: string;\n}\n\nexport interface StudentAccount {\n  student_id: number;\n  student_number: string;\n  email: string;\n  is_active: boolean;\n  last_login: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface StudentProfile {\n  profile_id: number;\n  student_id: number;\n  first_name: string;\n  middle_name: string | null;\n  last_name: string;\n  suffix: string | null;\n  full_name: string; // Computed field for backward compatibility\n  phone_number: string;\n  grade_level: number;\n  section: string;\n  parent_guardian_name: string | null;\n  parent_guardian_phone: string | null;\n  address: string | null;\n  profile_picture: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Student extends StudentAccount {\n  profile: StudentProfile;\n}\n\nexport interface StudentsResponse {\n  students: Student[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\nclass StudentService {\n  async createStudent(studentData: CreateStudentRequest): Promise<Student> {\n    try {\n      const response = await httpClient.post<any>(API_ENDPOINTS.ADMIN.STUDENTS, studentData);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n      if (responseData?.student) {\n        return responseData.student;\n      } else if (responseData?.success && responseData?.data?.student) {\n        return responseData.data.student;\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error: any) {\n      console.error('Error creating student:', error);\n\n      // If it's a validation error, provide more specific details\n      if (error.details && Array.isArray(error.details)) {\n        const validationErrors = error.details.map((detail: any) =>\n          `${detail.field}: ${detail.message}`\n        ).join(', ');\n        throw new Error(`Validation failed: ${validationErrors}`);\n      }\n\n      throw new Error(error.message || 'Failed to create student account');\n    }\n  }\n\n  async getStudents(params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n    grade_level?: number;\n    section?: string;\n    is_active?: boolean;\n  }): Promise<StudentsResponse> {\n    try {\n      // Filter out undefined values to avoid sending \"undefined\" as string\n      const cleanParams: any = {};\n      if (params) {\n        if (params.page !== undefined) cleanParams.page = params.page;\n        if (params.limit !== undefined) cleanParams.limit = params.limit;\n        if (params.search !== undefined && params.search !== '') cleanParams.search = params.search;\n        if (params.grade_level !== undefined) cleanParams.grade_level = params.grade_level;\n        if (params.section !== undefined && params.section !== '') cleanParams.section = params.section;\n        if (params.is_active !== undefined) cleanParams.is_active = params.is_active.toString();\n      }\n\n      const response = await httpClient.get<any>(API_ENDPOINTS.ADMIN.STUDENTS, cleanParams);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n\n      if (responseData?.success && responseData?.students && responseData?.pagination) {\n        return {\n          students: responseData.students,\n          pagination: responseData.pagination\n        };\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error: any) {\n      console.error('Error fetching students:', error);\n      throw new Error(error.message || 'Failed to fetch students');\n    }\n  }\n\n  async getStudent(studentId: string): Promise<Student> {\n    try {\n      const response = await httpClient.get<ApiResponse<{ student: Student }>>(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));\n      if (!response.data?.data?.student) {\n        throw new Error('Invalid response from server');\n      }\n      return response.data.data.student;\n    } catch (error: any) {\n      console.error('Error fetching student:', error);\n      throw new Error(error.message || 'Failed to fetch student');\n    }\n  }\n\n  async updateStudent(studentId: string, studentData: Partial<CreateStudentRequest>): Promise<Student> {\n    try {\n      const response = await httpClient.put<any>(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId), studentData);\n\n      // Handle the actual response structure from backend\n      const responseData = response.data || response;\n      if (responseData?.student) {\n        return responseData.student;\n      } else if (responseData?.success && responseData?.data?.student) {\n        return responseData.data.student;\n      } else {\n        throw new Error('Invalid response from server');\n      }\n    } catch (error: any) {\n      console.error('Error updating student:', error);\n      throw new Error(error.message || 'Failed to update student');\n    }\n  }\n\n  async deleteStudent(studentId: string): Promise<void> {\n    try {\n      await httpClient.delete(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));\n    } catch (error: any) {\n      console.error('Error deleting student:', error);\n      throw new Error(error.message || 'Failed to delete student');\n    }\n  }\n\n  async resetStudentPassword(studentId: string, newPassword: string = 'Student123'): Promise<void> {\n    try {\n      await httpClient.post(API_ENDPOINTS.ADMIN.RESET_STUDENT_PASSWORD(studentId), {\n        newPassword: newPassword\n      });\n    } catch (error: any) {\n      console.error('Error resetting student password:', error);\n      throw new Error(error.message || 'Failed to reset student password');\n    }\n  }\n}\n\nexport const studentService = new StudentService();\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,SAASC,UAAU,QAAQ,eAAe;AAmE1C,MAAMC,cAAc,CAAC;EACnB,MAAMC,aAAaA,CAACC,WAAiC,EAAoB;IACvE,IAAI;MAAA,IAAAC,kBAAA;MACF,MAAMC,QAAQ,GAAG,MAAML,UAAU,CAACM,IAAI,CAAMP,aAAa,CAACQ,KAAK,CAACC,QAAQ,EAAEL,WAAW,CAAC;;MAEtF;MACA,MAAMM,YAAY,GAAGJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ;MAC9C,IAAII,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,OAAO,EAAE;QACzB,OAAOF,YAAY,CAACE,OAAO;MAC7B,CAAC,MAAM,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEG,OAAO,IAAIH,YAAY,aAAZA,YAAY,gBAAAL,kBAAA,GAAZK,YAAY,CAAEC,IAAI,cAAAN,kBAAA,eAAlBA,kBAAA,CAAoBO,OAAO,EAAE;QAC/D,OAAOF,YAAY,CAACC,IAAI,CAACC,OAAO;MAClC,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;MAE/C;MACA,IAAIA,KAAK,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAACE,OAAO,CAAC,EAAE;QACjD,MAAMG,gBAAgB,GAAGL,KAAK,CAACE,OAAO,CAACI,GAAG,CAAEC,MAAW,IACrD,GAAGA,MAAM,CAACC,KAAK,KAAKD,MAAM,CAACE,OAAO,EACpC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACZ,MAAM,IAAIX,KAAK,CAAC,sBAAsBM,gBAAgB,EAAE,CAAC;MAC3D;MAEA,MAAM,IAAIN,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,kCAAkC,CAAC;IACtE;EACF;EAEA,MAAME,WAAWA,CAACC,MAOjB,EAA6B;IAC5B,IAAI;MACF;MACA,MAAMC,WAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,MAAM,EAAE;QACV,IAAIA,MAAM,CAACE,IAAI,KAAKC,SAAS,EAAEF,WAAW,CAACC,IAAI,GAAGF,MAAM,CAACE,IAAI;QAC7D,IAAIF,MAAM,CAACI,KAAK,KAAKD,SAAS,EAAEF,WAAW,CAACG,KAAK,GAAGJ,MAAM,CAACI,KAAK;QAChE,IAAIJ,MAAM,CAACK,MAAM,KAAKF,SAAS,IAAIH,MAAM,CAACK,MAAM,KAAK,EAAE,EAAEJ,WAAW,CAACI,MAAM,GAAGL,MAAM,CAACK,MAAM;QAC3F,IAAIL,MAAM,CAACM,WAAW,KAAKH,SAAS,EAAEF,WAAW,CAACK,WAAW,GAAGN,MAAM,CAACM,WAAW;QAClF,IAAIN,MAAM,CAACO,OAAO,KAAKJ,SAAS,IAAIH,MAAM,CAACO,OAAO,KAAK,EAAE,EAAEN,WAAW,CAACM,OAAO,GAAGP,MAAM,CAACO,OAAO;QAC/F,IAAIP,MAAM,CAACQ,SAAS,KAAKL,SAAS,EAAEF,WAAW,CAACO,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAACC,QAAQ,CAAC,CAAC;MACzF;MAEA,MAAM9B,QAAQ,GAAG,MAAML,UAAU,CAACoC,GAAG,CAAMrC,aAAa,CAACQ,KAAK,CAACC,QAAQ,EAAEmB,WAAW,CAAC;;MAErF;MACA,MAAMlB,YAAY,GAAGJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ;MAE9C,IAAII,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEG,OAAO,IAAIH,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE4B,QAAQ,IAAI5B,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE6B,UAAU,EAAE;QAC/E,OAAO;UACLD,QAAQ,EAAE5B,YAAY,CAAC4B,QAAQ;UAC/BC,UAAU,EAAE7B,YAAY,CAAC6B;QAC3B,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzB,KAAK,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAM,IAAID,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,0BAA0B,CAAC;IAC9D;EACF;EAEA,MAAMgB,UAAUA,CAACC,SAAiB,EAAoB;IACpD,IAAI;MAAA,IAAAC,cAAA,EAAAC,mBAAA;MACF,MAAMrC,QAAQ,GAAG,MAAML,UAAU,CAACoC,GAAG,CAAoCrC,aAAa,CAACQ,KAAK,CAACoC,aAAa,CAACH,SAAS,CAAC,CAAC;MACtH,IAAI,GAAAC,cAAA,GAACpC,QAAQ,CAACK,IAAI,cAAA+B,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAe/B,IAAI,cAAAgC,mBAAA,eAAnBA,mBAAA,CAAqB/B,OAAO,GAAE;QACjC,MAAM,IAAIE,KAAK,CAAC,8BAA8B,CAAC;MACjD;MACA,OAAOR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAACC,OAAO;IACnC,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAID,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF;EAEA,MAAMqB,aAAaA,CAACJ,SAAiB,EAAErC,WAA0C,EAAoB;IACnG,IAAI;MAAA,IAAA0C,mBAAA;MACF,MAAMxC,QAAQ,GAAG,MAAML,UAAU,CAAC8C,GAAG,CAAM/C,aAAa,CAACQ,KAAK,CAACoC,aAAa,CAACH,SAAS,CAAC,EAAErC,WAAW,CAAC;;MAErG;MACA,MAAMM,YAAY,GAAGJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ;MAC9C,IAAII,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,OAAO,EAAE;QACzB,OAAOF,YAAY,CAACE,OAAO;MAC7B,CAAC,MAAM,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEG,OAAO,IAAIH,YAAY,aAAZA,YAAY,gBAAAoC,mBAAA,GAAZpC,YAAY,CAAEC,IAAI,cAAAmC,mBAAA,eAAlBA,mBAAA,CAAoBlC,OAAO,EAAE;QAC/D,OAAOF,YAAY,CAACC,IAAI,CAACC,OAAO;MAClC,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAID,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,0BAA0B,CAAC;IAC9D;EACF;EAEA,MAAMwB,aAAaA,CAACP,SAAiB,EAAiB;IACpD,IAAI;MACF,MAAMxC,UAAU,CAACgD,MAAM,CAACjD,aAAa,CAACQ,KAAK,CAACoC,aAAa,CAACH,SAAS,CAAC,CAAC;IACvE,CAAC,CAAC,OAAO1B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAM,IAAID,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,0BAA0B,CAAC;IAC9D;EACF;EAEA,MAAM0B,oBAAoBA,CAACT,SAAiB,EAAEU,WAAmB,GAAG,YAAY,EAAiB;IAC/F,IAAI;MACF,MAAMlD,UAAU,CAACM,IAAI,CAACP,aAAa,CAACQ,KAAK,CAAC4C,sBAAsB,CAACX,SAAS,CAAC,EAAE;QAC3EU,WAAW,EAAEA;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAID,KAAK,CAACC,KAAK,CAACS,OAAO,IAAI,kCAAkC,CAAC;IACtE;EACF;AACF;AAEA,OAAO,MAAM6B,cAAc,GAAG,IAAInD,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}