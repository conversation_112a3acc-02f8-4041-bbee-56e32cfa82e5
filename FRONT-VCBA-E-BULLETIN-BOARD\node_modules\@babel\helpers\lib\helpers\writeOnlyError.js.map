{"version": 3, "names": ["_writeOnlyError", "name", "TypeError"], "sources": ["../../src/helpers/writeOnlyError.ts"], "sourcesContent": ["/* @minVersion 7.12.13 */\n\nexport default function _writeOnlyError(name: string) {\n  throw new TypeError('\"' + name + '\" is write-only');\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CAACC,IAAY,EAAE;EACpD,MAAM,IAAIC,SAAS,CAAC,GAAG,GAAGD,IAAI,GAAG,iBAAiB,CAAC;AACrD", "ignoreList": []}