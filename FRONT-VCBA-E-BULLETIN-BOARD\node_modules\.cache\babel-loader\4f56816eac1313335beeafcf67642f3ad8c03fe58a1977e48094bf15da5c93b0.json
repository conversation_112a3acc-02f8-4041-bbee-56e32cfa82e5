{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 6h4\",\n  key: \"aawbzj\"\n}], [\"path\", {\n  d: \"M2 10h4\",\n  key: \"l0bgd4\"\n}], [\"path\", {\n  d: \"M2 14h4\",\n  key: \"1gsvsf\"\n}], [\"path\", {\n  d: \"M2 18h4\",\n  key: \"1bu2t1\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1nb95v\"\n}], [\"path\", {\n  d: \"M15 2v20\",\n  key: \"dcj49h\"\n}], [\"path\", {\n  d: \"M15 7h5\",\n  key: \"1xj5lc\"\n}], [\"path\", {\n  d: \"M15 12h5\",\n  key: \"w5shd9\"\n}], [\"path\", {\n  d: \"M15 17h5\",\n  key: \"1qaofu\"\n}]];\nconst NotebookTabs = createLucideIcon(\"notebook-tabs\", __iconNode);\nexport { __iconNode, NotebookTabs as default };\n//# sourceMappingURL=notebook-tabs.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}