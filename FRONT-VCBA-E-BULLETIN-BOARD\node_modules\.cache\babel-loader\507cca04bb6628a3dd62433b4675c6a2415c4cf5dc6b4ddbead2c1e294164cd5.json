{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 4v7a4 4 0 0 1-4 4H4\",\n  key: \"6o5b7l\"\n}], [\"path\", {\n  d: \"m9 10-5 5 5 5\",\n  key: \"1kshq7\"\n}]];\nconst CornerDownLeft = createLucideIcon(\"corner-down-left\", __iconNode);\nexport { __iconNode, CornerDownLeft as default };\n//# sourceMappingURL=corner-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}