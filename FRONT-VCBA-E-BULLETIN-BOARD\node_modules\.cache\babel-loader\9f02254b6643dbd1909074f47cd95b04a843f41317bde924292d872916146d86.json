{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\",\n  key: \"1tc9qg\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"3\",\n  key: \"1vg3eu\"\n}]];\nconst Camera = createLucideIcon(\"camera\", __iconNode);\nexport { __iconNode, Camera as default };\n//# sourceMappingURL=camera.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}