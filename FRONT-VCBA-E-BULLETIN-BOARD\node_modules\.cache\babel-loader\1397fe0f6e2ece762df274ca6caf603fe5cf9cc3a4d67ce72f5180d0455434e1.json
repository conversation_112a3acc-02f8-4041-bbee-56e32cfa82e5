{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973\",\n  key: \"1cez44\"\n}], [\"path\", {\n  d: \"m13 12-3 5h4l-3 5\",\n  key: \"1t22er\"\n}]];\nconst CloudLightning = createLucideIcon(\"cloud-lightning\", __iconNode);\nexport { __iconNode, CloudLightning as default };\n//# sourceMappingURL=cloud-lightning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}