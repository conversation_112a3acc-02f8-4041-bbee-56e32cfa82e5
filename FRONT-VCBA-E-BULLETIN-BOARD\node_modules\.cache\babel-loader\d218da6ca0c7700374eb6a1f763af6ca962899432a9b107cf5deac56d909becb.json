{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z\",\n  key: \"p1xzt8\"\n}], [\"path\", {\n  d: \"M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1\",\n  key: \"1cx29u\"\n}]];\nconst MessagesSquare = createLucideIcon(\"messages-square\", __iconNode);\nexport { __iconNode, MessagesSquare as default };\n//# sourceMappingURL=messages-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}