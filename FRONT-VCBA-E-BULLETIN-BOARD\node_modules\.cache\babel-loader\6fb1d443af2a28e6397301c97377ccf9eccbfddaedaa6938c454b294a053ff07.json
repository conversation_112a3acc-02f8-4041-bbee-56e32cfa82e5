{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { adminCommentServiceWithToken, studentCommentServiceWithToken } from '../services/commentService';\nimport { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\n\n// Hook return type\n\n// Hook for managing comments\nexport const useComments = (announcementId, calendarId, currentUserType) => {\n  _s();\n  // Ensure either announcementId or calendarId is provided, but not both\n  if (!announcementId && !calendarId) {\n    throw new Error('Either announcementId or calendarId must be provided');\n  }\n  if (announcementId && calendarId) {\n    throw new Error('Cannot provide both announcementId and calendarId');\n  }\n  // Determine the appropriate service based on current user context\n  const getService = useCallback(() => {\n    // If user type is explicitly provided, use that\n    if (currentUserType === 'admin') {\n      console.log('🎯 useComments - Using admin service (explicit)');\n      return adminCommentServiceWithToken;\n    }\n    if (currentUserType === 'student') {\n      console.log('🎯 useComments - Using student service (explicit)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Auto-detect based on current page context and available tokens\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    if (isAdminPage && adminToken) {\n      console.log('🎯 useComments - Using admin service (admin page context)');\n      return adminCommentServiceWithToken;\n    }\n    if (isStudentPage && studentToken) {\n      console.log('🎯 useComments - Using student service (student page context)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Fallback: prioritize student service if student token exists\n    if (studentToken) {\n      console.log('🎯 useComments - Using student service (fallback)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Last resort: use admin service\n    console.log('🎯 useComments - Using admin service (fallback)');\n    return adminCommentServiceWithToken;\n  }, [currentUserType]);\n  const service = getService();\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [pagination, setPagination] = useState({\n    page: 1,\n    totalPages: 0,\n    total: 0,\n    hasNext: false,\n    hasPrev: false\n  });\n\n  // Track current user context to detect changes\n  const currentUserContextRef = useRef('');\n\n  // Function to get current user context identifier\n  const getCurrentUserContext = useCallback(() => {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n    // Create a unique identifier for the current user context\n    if (adminToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (studentToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    }\n    return 'anonymous';\n  }, []);\n\n  // Function to clear cache when user context changes\n  const clearCacheIfUserChanged = useCallback(() => {\n    const currentContext = getCurrentUserContext();\n    if (currentUserContextRef.current && currentUserContextRef.current !== currentContext) {\n      console.log('🔄 User context changed, clearing comment cache', {\n        previous: currentUserContextRef.current,\n        current: currentContext,\n        announcementId\n      });\n      setComments([]);\n      setPagination({\n        page: 1,\n        totalPages: 0,\n        total: 0,\n        hasNext: false,\n        hasPrev: false\n      });\n    }\n    currentUserContextRef.current = currentContext;\n  }, [getCurrentUserContext, announcementId, calendarId]);\n  const fetchComments = useCallback(async () => {\n    if (!announcementId && !calendarId) return;\n    try {\n      // Clear cache if user context changed\n      clearCacheIfUserChanged();\n      setLoading(true);\n      setError(undefined);\n      let response;\n      if (announcementId) {\n        response = await service.getCommentsByAnnouncement(announcementId, {\n          page: 1,\n          limit: 50,\n          sort_by: 'created_at',\n          sort_order: 'ASC'\n        });\n      } else if (calendarId) {\n        response = await service.getCommentsByCalendar(calendarId, {\n          page: 1,\n          limit: 50,\n          sort_by: 'created_at',\n          sort_order: 'ASC'\n        });\n      } else {\n        // This should never happen due to validation above, but handle it gracefully\n        setError('No valid ID provided for fetching comments');\n        return;\n      }\n      if (response && response.success && response.data) {\n        setComments(response.data.comments || []);\n        setPagination(response.data.pagination);\n      } else {\n        var _response;\n        setError(((_response = response) === null || _response === void 0 ? void 0 : _response.message) || 'Failed to fetch comments');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching comments');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, calendarId, clearCacheIfUserChanged, service]);\n  const refresh = useCallback(async () => {\n    await fetchComments();\n  }, [fetchComments]);\n  const createComment = useCallback(async data => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.createComment(data);\n      if (response.success) {\n        // Refresh the list to get the new comment\n        await fetchComments();\n      } else {\n        throw new Error(response.message || 'Failed to create comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while creating comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchComments, service]);\n  const createReply = useCallback(async (parentCommentId, data) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.createReply(parentCommentId, data);\n      if (response.success) {\n        // Refresh the list to get the new reply\n        await fetchComments();\n      } else {\n        throw new Error(response.message || 'Failed to create reply');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while creating reply');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchComments, service]);\n  const updateComment = useCallback(async (id, data) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.updateComment(id, data);\n      if (response.success && response.data) {\n        // Update the comment in the local state\n        setComments(prev => prev.map(comment => {\n          var _response$data;\n          return comment.comment_id === id ? {\n            ...comment,\n            ...((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.comment)\n          } : comment;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to update comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while updating comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n  const deleteComment = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.deleteComment(id);\n      if (response.success) {\n        // Remove the comment from local state\n        setComments(prev => prev.filter(comment => comment.comment_id !== id));\n      } else {\n        throw new Error(response.message || 'Failed to delete comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while deleting comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n  const likeComment = useCallback(async (id, reactionId = 1) => {\n    try {\n      setError(undefined);\n      const response = await service.addReaction(id, reactionId);\n      if (response.success) {\n        // Update the comment reaction count and user reaction in local state\n        setComments(prev => prev.map(comment => {\n          if (comment.comment_id === id) {\n            // Only increment count if user hasn't reacted before\n            const newCount = comment.user_reaction ? comment.reaction_count || 0 // Already reacted, don't change count\n            : (comment.reaction_count || 0) + 1; // New reaction, increment count\n\n            return {\n              ...comment,\n              reaction_count: newCount,\n              user_reaction: {\n                reaction_id: reactionId,\n                reaction_name: 'like',\n                reaction_emoji: '❤️'\n              }\n            };\n          }\n          return comment;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to like comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while liking comment');\n      throw err;\n    }\n  }, [service]);\n  const unlikeComment = useCallback(async id => {\n    try {\n      setError(undefined);\n      const response = await service.removeReaction(id);\n      if (response.success) {\n        // Update the comment reaction count and remove user reaction in local state\n        setComments(prev => prev.map(comment => {\n          if (comment.comment_id === id) {\n            // Only decrement count if user had reacted before\n            const newCount = comment.user_reaction ? Math.max((comment.reaction_count || 0) - 1, 0) // Had reaction, decrement count\n            : comment.reaction_count || 0; // No reaction, don't change count\n\n            return {\n              ...comment,\n              reaction_count: newCount,\n              user_reaction: undefined\n            };\n          }\n          return comment;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to unlike comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while unliking comment');\n      throw err;\n    }\n  }, [service]);\n  const flagComment = useCallback(async (id, reason) => {\n    try {\n      setError(undefined);\n      const response = await service.flagComment(id, reason);\n      if (response.success) {\n        // Update the comment flag status in local state\n        setComments(prev => prev.map(comment => comment.comment_id === id ? {\n          ...comment,\n          is_flagged: true\n        } : comment));\n      } else {\n        throw new Error(response.message || 'Failed to flag comment');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while flagging comment');\n      throw err;\n    }\n  }, [service]);\n  useEffect(() => {\n    fetchComments();\n  }, [fetchComments]);\n  return {\n    comments,\n    loading,\n    error,\n    pagination,\n    refresh,\n    createComment,\n    createReply,\n    updateComment,\n    deleteComment,\n    likeComment,\n    unlikeComment,\n    flagComment\n  };\n};\n\n// Utility functions for comment operations\n_s(useComments, \"sodT0tI/G1acHrBbvyv4qOQJgjk=\");\nexport const formatCommentDate = dateString => {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  } else {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n};\nexport const getCommentDepth = (comment, allComments) => {\n  let depth = 0;\n  let currentComment = comment;\n  while (currentComment.parent_comment_id) {\n    depth++;\n    const parentComment = allComments.find(c => c.comment_id === currentComment.parent_comment_id);\n    if (!parentComment) break;\n    currentComment = parentComment;\n  }\n  return depth;\n};\nexport const buildCommentTree = comments => {\n  const commentMap = new Map();\n  const rootComments = [];\n\n  // Initialize all comments with empty replies array\n  comments.forEach(comment => {\n    commentMap.set(comment.comment_id, {\n      ...comment,\n      replies: []\n    });\n  });\n\n  // Build the tree structure\n  comments.forEach(comment => {\n    const commentWithReplies = commentMap.get(comment.comment_id);\n    if (comment.parent_comment_id) {\n      const parent = commentMap.get(comment.parent_comment_id);\n      if (parent) {\n        parent.replies.push(commentWithReplies);\n      }\n    } else {\n      rootComments.push(commentWithReplies);\n    }\n  });\n  return rootComments;\n};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "adminCommentServiceWithToken", "studentCommentServiceWithToken", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "useComments", "announcementId", "calendarId", "currentUserType", "_s", "Error", "getService", "console", "log", "currentPath", "window", "location", "pathname", "isAdminPage", "includes", "isStudentPage", "adminToken", "localStorage", "getItem", "studentToken", "service", "comments", "setComments", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "page", "totalPages", "total", "hasNext", "has<PERSON>rev", "currentUserContextRef", "getCurrentUserContext", "substring", "clearCacheIfUserChanged", "currentContext", "current", "previous", "fetchComments", "undefined", "response", "getCommentsByAnnouncement", "limit", "sort_by", "sort_order", "getCommentsByCalendar", "success", "data", "_response", "message", "err", "refresh", "createComment", "createReply", "parentCommentId", "updateComment", "id", "prev", "map", "comment", "_response$data", "comment_id", "deleteComment", "filter", "likeComment", "reactionId", "addReaction", "newCount", "user_reaction", "reaction_count", "reaction_id", "reaction_name", "reaction_emoji", "unlikeComment", "removeReaction", "Math", "max", "flagComment", "reason", "is_flagged", "formatCommentDate", "dateString", "date", "Date", "now", "diffInSeconds", "floor", "getTime", "minutes", "hours", "days", "toLocaleDateString", "year", "month", "day", "getCommentDepth", "allComments", "depth", "currentComment", "parent_comment_id", "parentComment", "find", "c", "buildCommentTree", "commentMap", "Map", "rootComments", "for<PERSON>ach", "set", "replies", "commentWithReplies", "get", "parent", "push"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useComments.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport { commentService } from '../services';\nimport {\n  adminCommentServiceWithToken,\n  studentCommentServiceWithToken\n} from '../services/commentService';\nimport { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\nimport type {\n  Comment,\n  CreateCommentData,\n  UpdateCommentData,\n  CommentFilters,\n  PaginatedCommentsResponse\n} from '../services/commentService';\n\n// Hook return type\nexport interface UseCommentsReturn {\n  comments: Comment[];\n  loading: boolean;\n  error?: string;\n  pagination: {\n    page: number;\n    totalPages: number;\n    total: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n  refresh: () => Promise<void>;\n  createComment: (data: CreateCommentData) => Promise<void>;\n  createReply: (parentCommentId: number, data: Omit<CreateCommentData, 'parent_comment_id'>) => Promise<void>;\n  updateComment: (id: number, data: UpdateCommentData) => Promise<void>;\n  deleteComment: (id: number) => Promise<void>;\n  likeComment: (id: number, reactionId?: number) => Promise<void>;\n  unlikeComment: (id: number) => Promise<void>;\n  flagComment: (id: number, reason: string) => Promise<void>;\n}\n\n// Hook for managing comments\nexport const useComments = (\n  announcementId?: number,\n  calendarId?: number,\n  currentUserType?: 'admin' | 'student'\n): UseCommentsReturn => {\n  // Ensure either announcementId or calendarId is provided, but not both\n  if (!announcementId && !calendarId) {\n    throw new Error('Either announcementId or calendarId must be provided');\n  }\n  if (announcementId && calendarId) {\n    throw new Error('Cannot provide both announcementId and calendarId');\n  }\n  // Determine the appropriate service based on current user context\n  const getService = useCallback(() => {\n    // If user type is explicitly provided, use that\n    if (currentUserType === 'admin') {\n      console.log('🎯 useComments - Using admin service (explicit)');\n      return adminCommentServiceWithToken;\n    }\n    if (currentUserType === 'student') {\n      console.log('🎯 useComments - Using student service (explicit)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Auto-detect based on current page context and available tokens\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n    if (isAdminPage && adminToken) {\n      console.log('🎯 useComments - Using admin service (admin page context)');\n      return adminCommentServiceWithToken;\n    }\n\n    if (isStudentPage && studentToken) {\n      console.log('🎯 useComments - Using student service (student page context)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Fallback: prioritize student service if student token exists\n    if (studentToken) {\n      console.log('🎯 useComments - Using student service (fallback)');\n      return studentCommentServiceWithToken;\n    }\n\n    // Last resort: use admin service\n    console.log('🎯 useComments - Using admin service (fallback)');\n    return adminCommentServiceWithToken;\n  }, [currentUserType]);\n\n  const service = getService();\n  const [comments, setComments] = useState<Comment[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [pagination, setPagination] = useState({\n    page: 1,\n    totalPages: 0,\n    total: 0,\n    hasNext: false,\n    hasPrev: false\n  });\n\n  // Track current user context to detect changes\n  const currentUserContextRef = useRef<string>('');\n\n  // Function to get current user context identifier\n  const getCurrentUserContext = useCallback(() => {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n    // Create a unique identifier for the current user context\n    if (adminToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (studentToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    }\n    return 'anonymous';\n  }, []);\n\n  // Function to clear cache when user context changes\n  const clearCacheIfUserChanged = useCallback(() => {\n    const currentContext = getCurrentUserContext();\n    if (currentUserContextRef.current && currentUserContextRef.current !== currentContext) {\n      console.log('🔄 User context changed, clearing comment cache', {\n        previous: currentUserContextRef.current,\n        current: currentContext,\n        announcementId\n      });\n      setComments([]);\n      setPagination({\n        page: 1,\n        totalPages: 0,\n        total: 0,\n        hasNext: false,\n        hasPrev: false\n      });\n    }\n    currentUserContextRef.current = currentContext;\n  }, [getCurrentUserContext, announcementId, calendarId]);\n\n  const fetchComments = useCallback(async () => {\n    if (!announcementId && !calendarId) return;\n\n    try {\n      // Clear cache if user context changed\n      clearCacheIfUserChanged();\n\n      setLoading(true);\n      setError(undefined);\n\n      let response;\n      if (announcementId) {\n        response = await service.getCommentsByAnnouncement(announcementId, {\n          page: 1,\n          limit: 50,\n          sort_by: 'created_at',\n          sort_order: 'ASC'\n        });\n      } else if (calendarId) {\n        response = await service.getCommentsByCalendar(calendarId, {\n          page: 1,\n          limit: 50,\n          sort_by: 'created_at',\n          sort_order: 'ASC'\n        });\n      } else {\n        // This should never happen due to validation above, but handle it gracefully\n        setError('No valid ID provided for fetching comments');\n        return;\n      }\n\n      if (response && response.success && response.data) {\n        setComments(response.data.comments || []);\n        setPagination(response.data.pagination);\n      } else {\n        setError(response?.message || 'Failed to fetch comments');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching comments');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, calendarId, clearCacheIfUserChanged, service]);\n\n  const refresh = useCallback(async () => {\n    await fetchComments();\n  }, [fetchComments]);\n\n  const createComment = useCallback(async (data: CreateCommentData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await service.createComment(data);\n      \n      if (response.success) {\n        // Refresh the list to get the new comment\n        await fetchComments();\n      } else {\n        throw new Error(response.message || 'Failed to create comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchComments, service]);\n\n  const createReply = useCallback(async (parentCommentId: number, data: Omit<CreateCommentData, 'parent_comment_id'>) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await service.createReply(parentCommentId, data);\n\n      if (response.success) {\n        // Refresh the list to get the new reply\n        await fetchComments();\n      } else {\n        throw new Error(response.message || 'Failed to create reply');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating reply');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchComments, service]);\n\n  const updateComment = useCallback(async (id: number, data: UpdateCommentData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await service.updateComment(id, data);\n      \n      if (response.success && response.data) {\n        // Update the comment in the local state\n        setComments(prev =>\n          prev.map(comment =>\n            comment.comment_id === id\n              ? { ...comment, ...response.data?.comment }\n              : comment\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to update comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while updating comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n\n  const deleteComment = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await service.deleteComment(id);\n      \n      if (response.success) {\n        // Remove the comment from local state\n        setComments(prev => \n          prev.filter(comment => comment.comment_id !== id)\n        );\n      } else {\n        throw new Error(response.message || 'Failed to delete comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while deleting comment');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n\n  const likeComment = useCallback(async (id: number, reactionId: number = 1) => {\n    try {\n      setError(undefined);\n\n      const response = await service.addReaction(id, reactionId);\n\n      if (response.success) {\n        // Update the comment reaction count and user reaction in local state\n        setComments(prev =>\n          prev.map(comment => {\n            if (comment.comment_id === id) {\n              // Only increment count if user hasn't reacted before\n              const newCount = comment.user_reaction\n                ? comment.reaction_count || 0  // Already reacted, don't change count\n                : (comment.reaction_count || 0) + 1;  // New reaction, increment count\n\n              return {\n                ...comment,\n                reaction_count: newCount,\n                user_reaction: { reaction_id: reactionId, reaction_name: 'like', reaction_emoji: '❤️' }\n              };\n            }\n            return comment;\n          })\n        );\n      } else {\n        throw new Error(response.message || 'Failed to like comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while liking comment');\n      throw err;\n    }\n  }, [service]);\n\n  const unlikeComment = useCallback(async (id: number) => {\n    try {\n      setError(undefined);\n\n      const response = await service.removeReaction(id);\n\n      if (response.success) {\n        // Update the comment reaction count and remove user reaction in local state\n        setComments(prev =>\n          prev.map(comment => {\n            if (comment.comment_id === id) {\n              // Only decrement count if user had reacted before\n              const newCount = comment.user_reaction\n                ? Math.max((comment.reaction_count || 0) - 1, 0)  // Had reaction, decrement count\n                : comment.reaction_count || 0;  // No reaction, don't change count\n\n              return {\n                ...comment,\n                reaction_count: newCount,\n                user_reaction: undefined\n              };\n            }\n            return comment;\n          })\n        );\n      } else {\n        throw new Error(response.message || 'Failed to unlike comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while unliking comment');\n      throw err;\n    }\n  }, [service]);\n\n  const flagComment = useCallback(async (id: number, reason: string) => {\n    try {\n      setError(undefined);\n      \n      const response = await service.flagComment(id, reason);\n      \n      if (response.success) {\n        // Update the comment flag status in local state\n        setComments(prev =>\n          prev.map(comment =>\n            comment.comment_id === id\n              ? { ...comment, is_flagged: true }\n              : comment\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to flag comment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while flagging comment');\n      throw err;\n    }\n  }, [service]);\n\n  useEffect(() => {\n    fetchComments();\n  }, [fetchComments]);\n\n  return {\n    comments,\n    loading,\n    error,\n    pagination,\n    refresh,\n    createComment,\n    createReply,\n    updateComment,\n    deleteComment,\n    likeComment,\n    unlikeComment,\n    flagComment\n  };\n};\n\n// Utility functions for comment operations\nexport const formatCommentDate = (dateString: string): string => {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  } else {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n};\n\nexport const getCommentDepth = (comment: Comment, allComments: Comment[]): number => {\n  let depth = 0;\n  let currentComment = comment;\n  \n  while (currentComment.parent_comment_id) {\n    depth++;\n    const parentComment = allComments.find(c => c.comment_id === currentComment.parent_comment_id);\n    if (!parentComment) break;\n    currentComment = parentComment;\n  }\n  \n  return depth;\n};\n\nexport const buildCommentTree = (comments: Comment[]): Comment[] => {\n  const commentMap = new Map<number, Comment & { replies: Comment[] }>();\n  const rootComments: (Comment & { replies: Comment[] })[] = [];\n\n  // Initialize all comments with empty replies array\n  comments.forEach(comment => {\n    commentMap.set(comment.comment_id, { ...comment, replies: [] });\n  });\n\n  // Build the tree structure\n  comments.forEach(comment => {\n    const commentWithReplies = commentMap.get(comment.comment_id)!;\n    \n    if (comment.parent_comment_id) {\n      const parent = commentMap.get(comment.parent_comment_id);\n      if (parent) {\n        parent.replies.push(commentWithReplies);\n      }\n    } else {\n      rootComments.push(commentWithReplies);\n    }\n  });\n\n  return rootComments;\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAEhE,SACEC,4BAA4B,EAC5BC,8BAA8B,QACzB,4BAA4B;AACnC,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,qBAAqB;;AASlF;;AAsBA;AACA,OAAO,MAAMC,WAAW,GAAGA,CACzBC,cAAuB,EACvBC,UAAmB,EACnBC,eAAqC,KACf;EAAAC,EAAA;EACtB;EACA,IAAI,CAACH,cAAc,IAAI,CAACC,UAAU,EAAE;IAClC,MAAM,IAAIG,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,IAAIJ,cAAc,IAAIC,UAAU,EAAE;IAChC,MAAM,IAAIG,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA;EACA,MAAMC,UAAU,GAAGZ,WAAW,CAAC,MAAM;IACnC;IACA,IAAIS,eAAe,KAAK,OAAO,EAAE;MAC/BI,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,OAAOZ,4BAA4B;IACrC;IACA,IAAIO,eAAe,KAAK,SAAS,EAAE;MACjCI,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,OAAOX,8BAA8B;IACvC;;IAEA;IACA,MAAMY,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,WAAW,GAAGJ,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC;IAClD,MAAMC,aAAa,GAAGN,WAAW,CAACK,QAAQ,CAAC,UAAU,CAAC;IAEtD,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACpB,oBAAoB,CAAC;IAC7D,MAAMqB,YAAY,GAAGF,YAAY,CAACC,OAAO,CAACnB,sBAAsB,CAAC;IAEjE,IAAIc,WAAW,IAAIG,UAAU,EAAE;MAC7BT,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOZ,4BAA4B;IACrC;IAEA,IAAImB,aAAa,IAAII,YAAY,EAAE;MACjCZ,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5E,OAAOX,8BAA8B;IACvC;;IAEA;IACA,IAAIsB,YAAY,EAAE;MAChBZ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,OAAOX,8BAA8B;IACvC;;IAEA;IACAU,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAOZ,4BAA4B;EACrC,CAAC,EAAE,CAACO,eAAe,CAAC,CAAC;EAErB,MAAMiB,OAAO,GAAGd,UAAU,CAAC,CAAC;EAC5B,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC;IAC3CqC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,CAAC;IACbC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,qBAAqB,GAAGvC,MAAM,CAAS,EAAE,CAAC;;EAEhD;EACA,MAAMwC,qBAAqB,GAAGzC,WAAW,CAAC,MAAM;IAC9C,MAAMsB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACpB,oBAAoB,CAAC;IAC7D,MAAMqB,YAAY,GAAGF,YAAY,CAACC,OAAO,CAACnB,sBAAsB,CAAC;;IAEjE;IACA,IAAIiB,UAAU,EAAE;MACd,OAAO,SAASA,UAAU,CAACoB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC/C,CAAC,MAAM,IAAIjB,YAAY,EAAE;MACvB,OAAO,WAAWA,YAAY,CAACiB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IACnD;IACA,OAAO,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,uBAAuB,GAAG3C,WAAW,CAAC,MAAM;IAChD,MAAM4C,cAAc,GAAGH,qBAAqB,CAAC,CAAC;IAC9C,IAAID,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACK,OAAO,KAAKD,cAAc,EAAE;MACrF/B,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;QAC7DgC,QAAQ,EAAEN,qBAAqB,CAACK,OAAO;QACvCA,OAAO,EAAED,cAAc;QACvBrC;MACF,CAAC,CAAC;MACFqB,WAAW,CAAC,EAAE,CAAC;MACfM,aAAa,CAAC;QACZC,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACAC,qBAAqB,CAACK,OAAO,GAAGD,cAAc;EAChD,CAAC,EAAE,CAACH,qBAAqB,EAAElC,cAAc,EAAEC,UAAU,CAAC,CAAC;EAEvD,MAAMuC,aAAa,GAAG/C,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACO,cAAc,IAAI,CAACC,UAAU,EAAE;IAEpC,IAAI;MACF;MACAmC,uBAAuB,CAAC,CAAC;MAEzBb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACgB,SAAS,CAAC;MAEnB,IAAIC,QAAQ;MACZ,IAAI1C,cAAc,EAAE;QAClB0C,QAAQ,GAAG,MAAMvB,OAAO,CAACwB,yBAAyB,CAAC3C,cAAc,EAAE;UACjE4B,IAAI,EAAE,CAAC;UACPgB,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,YAAY;UACrBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI7C,UAAU,EAAE;QACrByC,QAAQ,GAAG,MAAMvB,OAAO,CAAC4B,qBAAqB,CAAC9C,UAAU,EAAE;UACzD2B,IAAI,EAAE,CAAC;UACPgB,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,YAAY;UACrBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACArB,QAAQ,CAAC,4CAA4C,CAAC;QACtD;MACF;MAEA,IAAIiB,QAAQ,IAAIA,QAAQ,CAACM,OAAO,IAAIN,QAAQ,CAACO,IAAI,EAAE;QACjD5B,WAAW,CAACqB,QAAQ,CAACO,IAAI,CAAC7B,QAAQ,IAAI,EAAE,CAAC;QACzCO,aAAa,CAACe,QAAQ,CAACO,IAAI,CAACvB,UAAU,CAAC;MACzC,CAAC,MAAM;QAAA,IAAAwB,SAAA;QACLzB,QAAQ,CAAC,EAAAyB,SAAA,GAAAR,QAAQ,cAAAQ,SAAA,uBAARA,SAAA,CAAUC,OAAO,KAAI,0BAA0B,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,2CAA2C,CAAC;IACtE,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,cAAc,EAAEC,UAAU,EAAEmC,uBAAuB,EAAEjB,OAAO,CAAC,CAAC;EAElE,MAAMkC,OAAO,GAAG5D,WAAW,CAAC,YAAY;IACtC,MAAM+C,aAAa,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMc,aAAa,GAAG7D,WAAW,CAAC,MAAOwD,IAAuB,IAAK;IACnE,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAACmC,aAAa,CAACL,IAAI,CAAC;MAElD,IAAIP,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA,MAAMR,aAAa,CAAC,CAAC;MACvB,CAAC,MAAM;QACL,MAAM,IAAIpC,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,0CAA0C,CAAC;MACnE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACiB,aAAa,EAAErB,OAAO,CAAC,CAAC;EAE5B,MAAMoC,WAAW,GAAG9D,WAAW,CAAC,OAAO+D,eAAuB,EAAEP,IAAkD,KAAK;IACrH,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAACoC,WAAW,CAACC,eAAe,EAAEP,IAAI,CAAC;MAEjE,IAAIP,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA,MAAMR,aAAa,CAAC,CAAC;MACvB,CAAC,MAAM;QACL,MAAM,IAAIpC,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACiB,aAAa,EAAErB,OAAO,CAAC,CAAC;EAE5B,MAAMsC,aAAa,GAAGhE,WAAW,CAAC,OAAOiE,EAAU,EAAET,IAAuB,KAAK;IAC/E,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAACsC,aAAa,CAACC,EAAE,EAAET,IAAI,CAAC;MAEtD,IAAIP,QAAQ,CAACM,OAAO,IAAIN,QAAQ,CAACO,IAAI,EAAE;QACrC;QACA5B,WAAW,CAACsC,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO;UAAA,IAAAC,cAAA;UAAA,OACdD,OAAO,CAACE,UAAU,KAAKL,EAAE,GACrB;YAAE,GAAGG,OAAO;YAAE,KAAAC,cAAA,GAAGpB,QAAQ,CAACO,IAAI,cAAAa,cAAA,uBAAbA,cAAA,CAAeD,OAAO;UAAC,CAAC,GACzCA,OAAO;QAAA,CACb,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzD,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,0CAA0C,CAAC;MACnE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EAEb,MAAM6C,aAAa,GAAGvE,WAAW,CAAC,MAAOiE,EAAU,IAAK;IACtD,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAAC6C,aAAa,CAACN,EAAE,CAAC;MAEhD,IAAIhB,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA3B,WAAW,CAACsC,IAAI,IACdA,IAAI,CAACM,MAAM,CAACJ,OAAO,IAAIA,OAAO,CAACE,UAAU,KAAKL,EAAE,CAClD,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAItD,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,0CAA0C,CAAC;MACnE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EAEb,MAAM+C,WAAW,GAAGzE,WAAW,CAAC,OAAOiE,EAAU,EAAES,UAAkB,GAAG,CAAC,KAAK;IAC5E,IAAI;MACF1C,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAACiD,WAAW,CAACV,EAAE,EAAES,UAAU,CAAC;MAE1D,IAAIzB,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA3B,WAAW,CAACsC,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO,IAAI;UAClB,IAAIA,OAAO,CAACE,UAAU,KAAKL,EAAE,EAAE;YAC7B;YACA,MAAMW,QAAQ,GAAGR,OAAO,CAACS,aAAa,GAClCT,OAAO,CAACU,cAAc,IAAI,CAAC,CAAE;YAAA,EAC7B,CAACV,OAAO,CAACU,cAAc,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE;;YAExC,OAAO;cACL,GAAGV,OAAO;cACVU,cAAc,EAAEF,QAAQ;cACxBC,aAAa,EAAE;gBAAEE,WAAW,EAAEL,UAAU;gBAAEM,aAAa,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAK;YACxF,CAAC;UACH;UACA,OAAOb,OAAO;QAChB,CAAC,CACH,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzD,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMC,GAAG;IACX;EACF,CAAC,EAAE,CAACjC,OAAO,CAAC,CAAC;EAEb,MAAMwD,aAAa,GAAGlF,WAAW,CAAC,MAAOiE,EAAU,IAAK;IACtD,IAAI;MACFjC,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAACyD,cAAc,CAAClB,EAAE,CAAC;MAEjD,IAAIhB,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA3B,WAAW,CAACsC,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO,IAAI;UAClB,IAAIA,OAAO,CAACE,UAAU,KAAKL,EAAE,EAAE;YAC7B;YACA,MAAMW,QAAQ,GAAGR,OAAO,CAACS,aAAa,GAClCO,IAAI,CAACC,GAAG,CAAC,CAACjB,OAAO,CAACU,cAAc,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAE;YAAA,EAChDV,OAAO,CAACU,cAAc,IAAI,CAAC,CAAC,CAAE;;YAElC,OAAO;cACL,GAAGV,OAAO;cACVU,cAAc,EAAEF,QAAQ;cACxBC,aAAa,EAAE7B;YACjB,CAAC;UACH;UACA,OAAOoB,OAAO;QAChB,CAAC,CACH,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzD,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,0CAA0C,CAAC;MACnE,MAAMC,GAAG;IACX;EACF,CAAC,EAAE,CAACjC,OAAO,CAAC,CAAC;EAEb,MAAM4D,WAAW,GAAGtF,WAAW,CAAC,OAAOiE,EAAU,EAAEsB,MAAc,KAAK;IACpE,IAAI;MACFvD,QAAQ,CAACgB,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAAC4D,WAAW,CAACrB,EAAE,EAAEsB,MAAM,CAAC;MAEtD,IAAItC,QAAQ,CAACM,OAAO,EAAE;QACpB;QACA3B,WAAW,CAACsC,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO,IACdA,OAAO,CAACE,UAAU,KAAKL,EAAE,GACrB;UAAE,GAAGG,OAAO;UAAEoB,UAAU,EAAE;QAAK,CAAC,GAChCpB,OACN,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzD,KAAK,CAACsC,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACD,OAAO,IAAI,0CAA0C,CAAC;MACnE,MAAMC,GAAG;IACX;EACF,CAAC,EAAE,CAACjC,OAAO,CAAC,CAAC;EAEb3B,SAAS,CAAC,MAAM;IACdgD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,OAAO;IACLpB,QAAQ;IACRE,OAAO;IACPE,KAAK;IACLE,UAAU;IACV2B,OAAO;IACPC,aAAa;IACbC,WAAW;IACXE,aAAa;IACbO,aAAa;IACbE,WAAW;IACXS,aAAa;IACbI;EACF,CAAC;AACH,CAAC;;AAED;AAAA5E,EAAA,CAnWaJ,WAAW;AAoWxB,OAAO,MAAMmF,iBAAiB,GAAIC,UAAkB,IAAa;EAC/D,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;EACtB,MAAME,aAAa,GAAGV,IAAI,CAACW,KAAK,CAAC,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,GAAGL,IAAI,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;EAEzE,IAAIF,aAAa,GAAG,EAAE,EAAE;IACtB,OAAO,UAAU;EACnB,CAAC,MAAM,IAAIA,aAAa,GAAG,IAAI,EAAE;IAC/B,MAAMG,OAAO,GAAGb,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAC9C,OAAO,GAAGG,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;EACzD,CAAC,MAAM,IAAIH,aAAa,GAAG,KAAK,EAAE;IAChC,MAAMI,KAAK,GAAGd,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,IAAI,CAAC;IAC9C,OAAO,GAAGI,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;EACnD,CAAC,MAAM,IAAIJ,aAAa,GAAG,MAAM,EAAE;IACjC,MAAMK,IAAI,GAAGf,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,KAAK,CAAC;IAC9C,OAAO,GAAGK,IAAI,OAAOA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;EAChD,CAAC,MAAM;IACL,OAAOR,IAAI,CAACS,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMC,eAAe,GAAGA,CAACpC,OAAgB,EAAEqC,WAAsB,KAAa;EACnF,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,cAAc,GAAGvC,OAAO;EAE5B,OAAOuC,cAAc,CAACC,iBAAiB,EAAE;IACvCF,KAAK,EAAE;IACP,MAAMG,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,UAAU,KAAKqC,cAAc,CAACC,iBAAiB,CAAC;IAC9F,IAAI,CAACC,aAAa,EAAE;IACpBF,cAAc,GAAGE,aAAa;EAChC;EAEA,OAAOH,KAAK;AACd,CAAC;AAED,OAAO,MAAMM,gBAAgB,GAAIrF,QAAmB,IAAgB;EAClE,MAAMsF,UAAU,GAAG,IAAIC,GAAG,CAA2C,CAAC;EACtE,MAAMC,YAAkD,GAAG,EAAE;;EAE7D;EACAxF,QAAQ,CAACyF,OAAO,CAAChD,OAAO,IAAI;IAC1B6C,UAAU,CAACI,GAAG,CAACjD,OAAO,CAACE,UAAU,EAAE;MAAE,GAAGF,OAAO;MAAEkD,OAAO,EAAE;IAAG,CAAC,CAAC;EACjE,CAAC,CAAC;;EAEF;EACA3F,QAAQ,CAACyF,OAAO,CAAChD,OAAO,IAAI;IAC1B,MAAMmD,kBAAkB,GAAGN,UAAU,CAACO,GAAG,CAACpD,OAAO,CAACE,UAAU,CAAE;IAE9D,IAAIF,OAAO,CAACwC,iBAAiB,EAAE;MAC7B,MAAMa,MAAM,GAAGR,UAAU,CAACO,GAAG,CAACpD,OAAO,CAACwC,iBAAiB,CAAC;MACxD,IAAIa,MAAM,EAAE;QACVA,MAAM,CAACH,OAAO,CAACI,IAAI,CAACH,kBAAkB,CAAC;MACzC;IACF,CAAC,MAAM;MACLJ,YAAY,CAACO,IAAI,CAACH,kBAAkB,CAAC;IACvC;EACF,CAAC,CAAC;EAEF,OAAOJ,YAAY;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}