{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 3h14\",\n  key: \"7usisc\"\n}], [\"path\", {\n  d: \"m18 13-6-6-6 6\",\n  key: \"1kf1n9\"\n}], [\"path\", {\n  d: \"M12 7v14\",\n  key: \"1akyts\"\n}]];\nconst ArrowUpToLine = createLucideIcon(\"arrow-up-to-line\", __iconNode);\nexport { __iconNode, ArrowUpToLine as default };\n//# sourceMappingURL=arrow-up-to-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}