{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.636 6A13 13 0 0 0 19.4 3.2 1 1 0 0 1 21 4v11.344\",\n  key: \"bycexp\"\n}], [\"path\", {\n  d: \"M14.378 14.357A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h1\",\n  key: \"1t17s6\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14\",\n  key: \"1853fq\"\n}], [\"path\", {\n  d: \"M8 8v6\",\n  key: \"aieo6v\"\n}]];\nconst MegaphoneOff = createLucideIcon(\"megaphone-off\", __iconNode);\nexport { __iconNode, MegaphoneOff as default };\n//# sourceMappingURL=megaphone-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}