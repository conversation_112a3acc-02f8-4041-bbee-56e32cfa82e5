{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"4\",\n  cy: \"4\",\n  r: \"2\",\n  key: \"bt5ra8\"\n}], [\"path\", {\n  d: \"m14 5 3-3 3 3\",\n  key: \"1sorif\"\n}], [\"path\", {\n  d: \"m14 10 3-3 3 3\",\n  key: \"1jyi9h\"\n}], [\"path\", {\n  d: \"M17 14V2\",\n  key: \"8ymqnk\"\n}], [\"path\", {\n  d: \"M17 14H7l-5 8h20Z\",\n  key: \"13ar7p\"\n}], [\"path\", {\n  d: \"M8 14v8\",\n  key: \"1ghmqk\"\n}], [\"path\", {\n  d: \"m9 14 5 8\",\n  key: \"13pgi6\"\n}]];\nconst TentTree = createLucideIcon(\"tent-tree\", __iconNode);\nexport { __iconNode, TentTree as default };\n//# sourceMappingURL=tent-tree.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}