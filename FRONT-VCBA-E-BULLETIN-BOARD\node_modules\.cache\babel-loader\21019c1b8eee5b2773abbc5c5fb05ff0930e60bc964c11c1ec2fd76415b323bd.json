{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 9v6\",\n  key: \"17i7lo\"\n}], [\"path\", {\n  d: \"M12.543 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.605\",\n  key: \"o09yah\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M7 12h6\",\n  key: \"iekk3h\"\n}], [\"path\", {\n  d: \"M7.606 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.606\",\n  key: \"xyqvf1\"\n}]];\nconst BatteryPlus = createLucideIcon(\"battery-plus\", __iconNode);\nexport { __iconNode, BatteryPlus as default };\n//# sourceMappingURL=battery-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}