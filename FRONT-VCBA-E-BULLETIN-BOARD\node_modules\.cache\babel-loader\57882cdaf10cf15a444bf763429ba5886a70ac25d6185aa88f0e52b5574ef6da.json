{"ast": null, "code": "import React,{useState,useEffect,useMemo,useCallback}from'react';import{useCalendar,useCalendarCategories,getCalendarDays,isToday,isSameMonth,getMonthName}from'../../hooks/useCalendar';import CalendarEventModal from'../../components/admin/modals/CalendarEventModal';import{calendarService}from'../../services/calendarService';import{Calendar as CalendarIcon,Search,RefreshCw,Trash2,Edit,Send,Clock,Image as ImageIcon,AlertTriangle,MessageCircle,Star,Repeat}from'lucide-react';import CalendarEventLikeButton from'../../components/common/CalendarEventLikeButton';import HolidayManagement from'../../components/admin/HolidayManagement';// Removed calendar attachment imports since this feature is not yet implemented\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Calendar=/*#__PURE__*/React.memo(()=>{// Add CSS animation for spinning refresh icon\nReact.useEffect(()=>{const style=document.createElement('style');style.textContent=\"\\n      @keyframes spin {\\n        from { transform: rotate(0deg); }\\n        to { transform: rotate(360deg); }\\n      }\\n    \";document.head.appendChild(style);return()=>{document.head.removeChild(style);};},[]);const[currentDate,setCurrentDate]=useState(()=>new Date());const[selectedDate,setSelectedDate]=useState(null);const[showModal,setShowModal]=useState(false);const[editingEvent,setEditingEvent]=useState(null);const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');const[saving,setSaving]=useState(false);const[refreshing,setRefreshing]=useState(false);const[searchTerm,setSearchTerm]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[showHolidayManagement,setShowHolidayManagement]=useState(false);// Pagination state\nconst[currentPage,setCurrentPage]=useState(1);const[itemsPerPage,setItemsPerPage]=useState(10);// Event attachments state - disabled until backend implementation is complete\n// const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\n// Use the calendar hook\nconst{events,loading,error,createEvent,updateEvent,getEventsForDate,refresh}=useCalendar(currentDate);const{categories}=useCalendarCategories();// Clear messages after 5 seconds\nuseEffect(()=>{if(successMessage||errorMessage||error){const timer=setTimeout(()=>{setSuccessMessage('');setErrorMessage('');},5000);return()=>clearTimeout(timer);}},[successMessage,errorMessage,error]);const handleCreateEvent=useCallback(date=>{setEditingEvent(null);setSelectedDate(date||null);setShowModal(true);},[]);const handleEditEvent=useCallback(event=>{setEditingEvent(event);setShowModal(true);},[]);const handleSaveEvent=useCallback(async(data,applyPendingDeletes,onComplete)=>{setSaving(true);try{if(editingEvent){await updateEvent(editingEvent.calendar_id,data);// Apply pending image deletions AFTER successful update\nif(applyPendingDeletes){console.log('🗑️ Applying pending image deletions after successful update');await applyPendingDeletes();}setSuccessMessage('Event updated successfully! Calendar refreshed.');}else{await createEvent(data);setSuccessMessage('Event created successfully! Calendar refreshed.');}// Execute completion callback for additional operations\nif(onComplete){await onComplete();}// Force refresh the calendar to ensure immediate update\nconsole.log('🔄 Refreshing calendar to show updated events...');setRefreshing(true);await refresh();setRefreshing(false);// Small delay to ensure smooth UI transition\nsetTimeout(()=>{setShowModal(false);setEditingEvent(null);setSelectedDate(null);setSaving(false);},100);}catch(error){setErrorMessage(error.message||'Failed to save event');setSaving(false);}},[editingEvent,updateEvent,createEvent,refresh]);const handleCloseModal=useCallback(()=>{setShowModal(false);setEditingEvent(null);setSelectedDate(null);},[]);// Event management functions\nconst handlePublishEvent=useCallback(async eventId=>{try{await calendarService.publishEvent(eventId);setSuccessMessage('Event published successfully');refresh();// Refresh calendar data\n}catch(error){setErrorMessage(error.message||'Failed to publish event');}},[refresh]);const handleUnpublishEvent=useCallback(async eventId=>{try{await calendarService.unpublishEvent(eventId);setSuccessMessage('Event unpublished successfully');refresh();// Refresh calendar data\n}catch(error){setErrorMessage(error.message||'Failed to unpublish event');}},[refresh]);const handleDeleteEvent=useCallback(async eventId=>{// Use window.confirm to avoid ESLint error\nif(!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')){return;}try{await calendarService.softDeleteEvent(eventId);setSuccessMessage('Event deleted successfully');refresh();// Refresh calendar data\n}catch(error){setErrorMessage(error.message||'Failed to delete event');}},[refresh]);const navigateMonth=useCallback(direction=>{const newDate=new Date(currentDate);if(direction==='prev'){newDate.setMonth(currentDate.getMonth()-1);}else{newDate.setMonth(currentDate.getMonth()+1);}setCurrentDate(newDate);},[currentDate]);const goToToday=useCallback(()=>{setCurrentDate(new Date());},[]);const handleDateClick=useCallback(date=>{setSelectedDate(date);handleCreateEvent(date);},[handleCreateEvent]);const getEventTypeColor=useCallback(event=>{// Use category color if available, otherwise subcategory color, otherwise default\nreturn event.category_color||event.subcategory_color||'#22c55e';},[]);// Helper function to format event duration\nconst getEventDuration=useCallback(event=>{if(!event.end_date||event.end_date===event.event_date){return'Single day event';}const startDate=new Date(event.event_date);const endDate=new Date(event.end_date);const diffTime=Math.abs(endDate.getTime()-startDate.getTime());const diffDays=Math.ceil(diffTime/(1000*60*60*24))+1;// +1 to include both start and end days\nreturn\"\".concat(diffDays,\" day event\");},[]);// Helper function to get first two words from event title for calendar chip display\nconst getEventChipTitle=useCallback(title=>{const words=title.trim().split(/\\s+/);return words.slice(0,2).join(' ');},[]);// Memoize calendar days to prevent infinite re-renders\nconst days=useMemo(()=>{return getCalendarDays(currentDate.getFullYear(),currentDate.getMonth());},[currentDate.getFullYear(),currentDate.getMonth()]);// Get unique events for the event list (deduplicate multi-day events)\nconst uniqueEvents=useMemo(()=>{const eventMap=new Map();events.forEach(event=>{// Use calendar_id as the unique identifier\nif(!eventMap.has(event.calendar_id)){eventMap.set(event.calendar_id,event);}});const uniqueEventsList=Array.from(eventMap.values()).sort((a,b)=>{// Sort by event_date, then by title\nconst dateA=new Date(a.event_date);const dateB=new Date(b.event_date);if(dateA.getTime()!==dateB.getTime()){return dateA.getTime()-dateB.getTime();}return a.title.localeCompare(b.title);});// Debug: Log deduplication results\nconsole.log(\"\\uD83D\\uDCCA Event deduplication: \".concat(events.length,\" total events \\u2192 \").concat(uniqueEventsList.length,\" unique events\"));return uniqueEventsList;},[events]);// Filter events based on search term and holiday type (exclude holidays from the list)\nconst filteredEvents=useMemo(()=>{return uniqueEvents.filter(event=>{var _event$description,_event$category_id;// Exclude holidays from the events list\nconst isNotHoliday=!event.is_holiday;const matchesSearch=!searchTerm||event.title.toLowerCase().includes(searchTerm.toLowerCase())||((_event$description=event.description)===null||_event$description===void 0?void 0:_event$description.toLowerCase().includes(searchTerm.toLowerCase()));const matchesCategory=!selectedCategory||((_event$category_id=event.category_id)===null||_event$category_id===void 0?void 0:_event$category_id.toString())===selectedCategory;return isNotHoliday&&matchesSearch&&matchesCategory;});},[uniqueEvents,searchTerm,selectedCategory]);// Pagination calculations\nconst totalItems=filteredEvents.length;const totalPages=Math.ceil(totalItems/itemsPerPage);const startIndex=(currentPage-1)*itemsPerPage;const endIndex=startIndex+itemsPerPage;const paginatedEvents=filteredEvents.slice(startIndex,endIndex);// Reset to first page when filters change\nuseEffect(()=>{setCurrentPage(1);},[searchTerm,selectedCategory,itemsPerPage]);// Fetch attachments for visible events - disabled until backend implementation is complete\n// useEffect(() => {\n//   const fetchAttachments = async () => {\n//     if (!paginatedEvents || paginatedEvents.length === 0) return;\n//     // Attachment functionality will be implemented later\n//   };\n//   fetchAttachments();\n// }, [paginatedEvents]);\n// Component to display event images - disabled until backend implementation is complete\nconst EventImages=_ref=>{let{eventId}=_ref;return/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',backgroundColor:'#f3f4f6',borderRadius:'6px',display:'flex',alignItems:'center',justifyContent:'center',color:'#9ca3af',fontSize:'0.75rem'},children:/*#__PURE__*/_jsx(ImageIcon,{size:20})});};// Component to display individual image thumbnail - disabled until backend implementation is complete\n// const EventImageThumbnail: React.FC<{ attachment: any }> = ({ attachment }) => {\n//   // This component will be implemented when the backend supports file attachments\n//   return null;\n// };\n// Pagination component - Always visible\nconst PaginationControls=()=>{// Always show pagination controls, even for single page\nconst effectiveTotalPages=Math.max(totalPages,1);// Ensure at least 1 page\nconst effectiveCurrentPage=Math.max(currentPage,1);// Ensure at least page 1\nconst getPageNumbers=()=>{const pages=[];const maxVisiblePages=5;if(effectiveTotalPages<=maxVisiblePages){for(let i=1;i<=effectiveTotalPages;i++){pages.push(i);}}else{if(effectiveCurrentPage<=3){pages.push(1,2,3,4,'...',effectiveTotalPages);}else if(effectiveCurrentPage>=effectiveTotalPages-2){pages.push(1,'...',effectiveTotalPages-3,effectiveTotalPages-2,effectiveTotalPages-1,effectiveTotalPages);}else{pages.push(1,'...',effectiveCurrentPage-1,effectiveCurrentPage,effectiveCurrentPage+1,'...',effectiveTotalPages);}}return pages;};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginTop:'2rem',marginBottom:'2rem',padding:'1.5rem',backgroundColor:'white',borderRadius:'12px',border:'1px solid #e5e7eb',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.05)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:\"Show:\"}),/*#__PURE__*/_jsxs(\"select\",{value:itemsPerPage,onChange:e=>setItemsPerPage(Number(e.target.value)),style:{padding:'0.25rem 0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"option\",{value:5,children:\"5\"}),/*#__PURE__*/_jsx(\"option\",{value:10,children:\"10\"}),/*#__PURE__*/_jsx(\"option\",{value:20,children:\"20\"}),/*#__PURE__*/_jsx(\"option\",{value:50,children:\"50\"})]}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:\"per page\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:[\"Showing \",Math.max(totalItems>0?startIndex+1:0,0),\"-\",Math.min(endIndex,totalItems),\" of \",totalItems,\" events\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(1),disabled:effectiveCurrentPage===1,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:effectiveCurrentPage===1?'#f3f4f6':'white',color:effectiveCurrentPage===1?'#9ca3af':'#374151',cursor:effectiveCurrentPage===1?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"First\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(prev=>Math.max(1,prev-1)),disabled:effectiveCurrentPage===1,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:effectiveCurrentPage===1?'#f3f4f6':'white',color:effectiveCurrentPage===1?'#9ca3af':'#374151',cursor:effectiveCurrentPage===1?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Previous\"}),getPageNumbers().map((page,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>typeof page==='number'&&setCurrentPage(page),disabled:page==='...',style:{padding:'0.5rem 0.75rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:page===effectiveCurrentPage?'#3b82f6':page==='...'?'transparent':'white',color:page===effectiveCurrentPage?'white':page==='...'?'#9ca3af':'#374151',cursor:page==='...'?'default':'pointer',fontSize:'0.875rem',fontWeight:page===effectiveCurrentPage?'600':'400'},children:page},index)),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(prev=>Math.min(effectiveTotalPages,prev+1)),disabled:effectiveCurrentPage===effectiveTotalPages,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:effectiveCurrentPage===effectiveTotalPages?'#f3f4f6':'white',color:effectiveCurrentPage===effectiveTotalPages?'#9ca3af':'#374151',cursor:effectiveCurrentPage===effectiveTotalPages?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Next\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentPage(effectiveTotalPages),disabled:effectiveCurrentPage===effectiveTotalPages,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:effectiveCurrentPage===effectiveTotalPages?'#f3f4f6':'white',color:effectiveCurrentPage===effectiveTotalPages?'#9ca3af':'#374151',cursor:effectiveCurrentPage===effectiveTotalPages?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Last\"})]})]});};return/*#__PURE__*/_jsxs(\"div\",{style:{maxWidth:'1200px',margin:'0 auto'},\"data-calendar-component\":\"main\",children:[successMessage&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',backgroundColor:'#f0fdf4',border:'1px solid #bbf7d0',color:'#166534',borderRadius:'8px'},children:successMessage}),errorMessage&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',color:'#dc2626',borderRadius:'8px'},children:errorMessage}),/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'8px',padding:'1rem',marginBottom:'1rem',boxShadow:'0 2px 10px rgba(0, 0, 0, 0.05)',border:'1px solid #e8f5e8',position:'relative',zIndex:10},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontSize:'1.25rem',fontWeight:'600',color:'#2d5016',margin:0},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(CalendarIcon,{size:16,color:\"#1e40af\"}),\"School Calendar\",refreshing&&/*#__PURE__*/_jsx(RefreshCw,{size:14,color:\"#22c55e\",style:{animation:'spin 1s linear infinite',marginLeft:'0.25rem'}})]})}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'#6b7280',margin:'0 0 0 0.5rem',fontSize:'0.875rem'},children:[getMonthName(currentDate.getMonth()),\" \",currentDate.getFullYear()]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigateMonth('prev'),style:{padding:'0.25rem',color:'#6b7280',background:'none',border:'none',cursor:'pointer',fontSize:'1rem',transition:'color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.color='#374151',onMouseOut:e=>e.currentTarget.style.color='#6b7280',children:\"\\u2190\"}),/*#__PURE__*/_jsx(\"button\",{onClick:goToToday,style:{padding:'0.25rem 0.75rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'6px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'background-color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.backgroundColor='#e5e7eb',onMouseOut:e=>e.currentTarget.style.backgroundColor='#f3f4f6',children:\"Today\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigateMonth('next'),style:{padding:'0.25rem',color:'#6b7280',background:'none',border:'none',cursor:'pointer',fontSize:'1rem',transition:'color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.color='#374151',onMouseOut:e=>e.currentTarget.style.color='#6b7280',children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowHolidayManagement(true),style:{display:'inline-flex',alignItems:'center',padding:'0.5rem 1rem',background:'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',color:'white',border:'none',borderRadius:'6px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'transform 0.2s ease, box-shadow 0.2s ease',gap:'0.5rem'},onMouseOver:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 2px 6px rgba(245, 158, 11, 0.2)';},onMouseOut:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:[/*#__PURE__*/_jsx(Star,{size:16}),\"Holidays\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleCreateEvent(),style:{display:'inline-flex',alignItems:'center',padding:'0.5rem 1rem',background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'6px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 2px 6px rgba(34, 197, 94, 0.2)';},onMouseOut:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:\"+ Add Event\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',overflow:'hidden'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(7, 1fr)',backgroundColor:'#f9fafb',borderBottom:'1px solid #e5e7eb'},children:['Sun','Mon','Tue','Wed','Thu','Fri','Sat'].map(day=>/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',textAlign:'center',fontSize:'0.875rem',fontWeight:'500',color:'#374151'},children:day},day))}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(7, 1fr)'},children:days.map((date,index)=>{const dayEvents=getEventsForDate(date);const isCurrentMonth=isSameMonth(date,currentDate.getMonth(),currentDate.getFullYear());const isTodayDate=isToday(date);// Create unique key based on date to prevent React key conflicts\n// Use date timestamp as key since it's unique and stable across re-renders\nconst dateKey=\"calendar-day-\".concat(date.getTime());return/*#__PURE__*/_jsxs(\"div\",{style:{minHeight:'120px',padding:'0.5rem',borderBottom:'1px solid #e5e7eb',borderRight:index%7===6?'none':'1px solid #e5e7eb',// Remove right border on last column\ncursor:'pointer',backgroundColor:!isCurrentMonth?'#f9fafb':isTodayDate?'#eff6ff':'white',color:!isCurrentMonth?'#9ca3af':'#374151',transition:'all 0.2s ease',position:'relative'},onClick:()=>handleDateClick(date),onMouseOver:e=>{if(isCurrentMonth){e.currentTarget.style.backgroundColor='#f3f4f6';e.currentTarget.style.boxShadow='inset 0 0 0 1px #22c55e20';}},onMouseOut:e=>{e.currentTarget.style.backgroundColor=!isCurrentMonth?'#f9fafb':isTodayDate?'#eff6ff':'white';e.currentTarget.style.boxShadow='none';},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:'500',marginBottom:'0.25rem',color:isTodayDate?'#2563eb':'inherit'},children:date.getDate()}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'0.25rem'},children:[dayEvents.slice(0,3).map(event=>{// Determine styling for multi-day events\nconst isMultiDay=event.isMultiDay;const isStart=event.isEventStart;const isEnd=event.isEventEnd;const isContinuation=isMultiDay&&!isStart&&!isEnd;const eventColor=getEventTypeColor(event);return/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',padding:'0.25rem 0.5rem',borderRadius:isMultiDay?isStart?'6px 2px 2px 6px':isEnd?'2px 6px 6px 2px':'2px':'6px',backgroundColor:eventColor+(isContinuation?'25':'15'),border:\"1px solid \".concat(eventColor),borderLeft:isStart||!isMultiDay?\"4px solid \".concat(eventColor):\"1px solid \".concat(eventColor),borderRight:isEnd||!isMultiDay?\"4px solid \".concat(eventColor):\"1px solid \".concat(eventColor),overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap',cursor:'pointer',position:'relative',color:'#374151',fontWeight:'500',transition:'all 0.2s ease',display:'flex',alignItems:'center',gap:'0.25rem'},onClick:e=>{e.stopPropagation();handleEditEvent(event);},onMouseOver:e=>{e.currentTarget.style.backgroundColor=eventColor+'30';e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow=\"0 2px 8px \".concat(eventColor,\"40\");},onMouseOut:e=>{e.currentTarget.style.backgroundColor=eventColor+(isContinuation?'25':'15');e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},title:isMultiDay?\"\".concat(event.title,\" (\").concat(event.originalStartDate,\" to \").concat(event.originalEndDate,\")\"):event.title,children:[isStart&&isMultiDay&&/*#__PURE__*/_jsx(\"span\",{style:{color:eventColor,fontWeight:'bold',fontSize:'0.7rem'},children:\"\\u25B6\"}),isContinuation&&/*#__PURE__*/_jsx(\"span\",{style:{color:eventColor,fontWeight:'bold',fontSize:'0.7rem'},children:\"\\u25AC\"}),isEnd&&isMultiDay&&!isStart&&/*#__PURE__*/_jsx(\"span\",{style:{color:eventColor,fontWeight:'bold',fontSize:'0.7rem'},children:\"\\u25C0\"}),/*#__PURE__*/_jsx(\"span\",{style:{flex:1,overflow:'hidden',textOverflow:'ellipsis'},children:getEventChipTitle(event.title)}),event.is_recurring&&/*#__PURE__*/_jsx(\"span\",{title:\"Recurring \".concat(event.recurrence_pattern||'event'),children:/*#__PURE__*/_jsx(Repeat,{size:10,style:{color:eventColor,opacity:0.8,marginLeft:'2px'}})}),isStart&&isMultiDay&&/*#__PURE__*/_jsx(\"span\",{style:{color:eventColor,fontWeight:'bold',fontSize:'0.7rem',opacity:0.7},children:\"\\u2192\"})]},\"event-\".concat(event.calendar_id,\"-\").concat(date.getTime()));}),dayEvents.length>3&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280'},children:[\"+\",dayEvents.length-3,\" more\"]})]})]},dateKey);})})]}),/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',margin:'2rem 0',padding:'1.5rem',backgroundColor:'#f8fafc'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'1.5rem',alignItems:'end'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Search Events\"}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(Search,{size:16,color:\"#9ca3af\",style:{position:'absolute',left:'0.75rem',top:'50%',transform:'translateY(-50%)',pointerEvents:'none'}}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search events...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{width:'100%',padding:'0.75rem 0.75rem 0.75rem 2.5rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease, box-shadow 0.2s ease'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';}})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Category\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white',transition:'border-color 0.2s ease, box-shadow 0.2s ease',minHeight:'2.5rem',lineHeight:'1.2'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),categories.filter(category=>// Hide holiday categories from dropdown\n!['Philippine Holidays','International Holidays','Religious Holidays'].includes(category.name)).map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.category_id,children:category.name},category.category_id))]})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:refresh,style:{width:'100%',padding:'0.75rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'background-color 0.2s ease',height:'2.5rem',display:'flex',alignItems:'center',justifyContent:'center',gap:'0.5rem'},onMouseOver:e=>e.currentTarget.style.backgroundColor='#e5e7eb',onMouseOut:e=>e.currentTarget.style.backgroundColor='#f3f4f6',children:[/*#__PURE__*/_jsx(RefreshCw,{size:16}),\"Refresh\"]})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"h2\",{style:{fontSize:'1.5rem',fontWeight:'600',color:'#2d5016',margin:0},children:[\"Events for \",getMonthName(currentDate.getMonth()),\" \",currentDate.getFullYear()]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:[filteredEvents.length,\" school event\",filteredEvents.length!==1?'s':'',\" found\",/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',marginTop:'0.25rem',fontStyle:'italic'},children:\"Holidays are shown in calendar but not in this list\"})]})]}),loading?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'3rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(RefreshCw,{size:24,style:{marginBottom:'1rem',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"p\",{children:\"Loading events...\"})]}):filteredEvents.length===0?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'3rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(CalendarIcon,{size:48,style:{marginBottom:'1rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"p\",{children:\"No events found for this month\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleCreateEvent(),style:{marginTop:'1rem',padding:'0.75rem 1.5rem',backgroundColor:'#22c55e',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500'},children:\"Create First Event\"})]}):/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gap:'1rem'},children:paginatedEvents.map(event=>/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e5e7eb',borderRadius:'12px',padding:'1.5rem',backgroundColor:'white',transition:'all 0.2s ease'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsxs(\"h4\",{style:{fontSize:'1.125rem',fontWeight:'600',color:'#374151',margin:'0 0 0.5rem 0',display:'flex',alignItems:'center',gap:'0.5rem'},children:[event.title,event.is_recurring&&/*#__PURE__*/_jsx(\"span\",{title:\"Recurring \".concat(event.recurrence_pattern||'event'),children:/*#__PURE__*/_jsx(Repeat,{size:16,color:\"#22c55e\"})}),event.is_alert&&/*#__PURE__*/_jsx(AlertTriangle,{size:16,color:\"#ef4444\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280'},children:[/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(CalendarIcon,{className:\"h-4 w-4\"}),event.event_date]}),event.end_date&&event.end_date!==event.event_date&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u2192 \",event.end_date]}),/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:'#f3f4f6',color:'#374151',padding:'0.25rem 0.5rem',borderRadius:'4px',fontSize:'0.75rem',fontWeight:'500'},children:getEventDuration(event)}),/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:getEventTypeColor(event),color:'white',padding:'0.25rem 0.5rem',borderRadius:'4px',fontSize:'0.75rem'},children:event.category_name||'Uncategorized'}),event.is_recurring&&/*#__PURE__*/_jsxs(\"span\",{style:{backgroundColor:'#22c55e',color:'white',padding:'0.25rem 0.5rem',borderRadius:'4px',fontSize:'0.75rem',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Repeat,{size:12}),event.recurrence_pattern||'Recurring']})]}),event.description&&/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',color:'#6b7280',margin:'0.5rem 0 0 0',lineHeight:'1.5'},children:event.description}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',marginTop:'0.75rem'},children:[/*#__PURE__*/_jsx(CalendarEventLikeButton,{eventId:event.calendar_id,initialLiked:event.user_has_reacted||false,initialCount:event.reaction_count||0,size:\"small\",onLikeChange:(liked,newCount)=>{// Update the event in the local state if needed\nconsole.log(\"Event \".concat(event.calendar_id,\" like changed:\"),{liked,newCount});}}),event.allow_comments&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',fontSize:'0.875rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:14}),event.comment_count||0,\" comments\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem',marginLeft:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditEvent(event),style:{padding:'0.5rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'6px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:\"Edit event\",children:/*#__PURE__*/_jsx(Edit,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>event.is_published?handleUnpublishEvent(event.calendar_id):handlePublishEvent(event.calendar_id),style:{padding:'0.5rem',backgroundColor:event.is_published?'#fef3c7':'#dcfce7',color:event.is_published?'#d97706':'#16a34a',border:'none',borderRadius:'6px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:event.is_published?'Unpublish event':'Publish event',children:event.is_published?/*#__PURE__*/_jsx(Clock,{size:16}):/*#__PURE__*/_jsx(Send,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteEvent(event.calendar_id),style:{padding:'0.5rem',backgroundColor:'#fef2f2',color:'#dc2626',border:'none',borderRadius:'6px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:\"Delete event\",children:/*#__PURE__*/_jsx(Trash2,{size:16})})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{borderTop:'1px solid #f3f4f6',paddingTop:'1rem',marginTop:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Event Images\"}),/*#__PURE__*/_jsx(EventImages,{eventId:event.calendar_id})]})]},\"unique-event-\".concat(event.calendar_id)))})]}),/*#__PURE__*/_jsx(PaginationControls,{}),/*#__PURE__*/_jsx(CalendarEventModal,{isOpen:showModal,onClose:handleCloseModal,onSave:handleSaveEvent,event:editingEvent,selectedDate:selectedDate,loading:saving||loading}),showHolidayManagement&&/*#__PURE__*/_jsx(HolidayManagement,{onClose:()=>{setShowHolidayManagement(false);// Refresh calendar events after holiday management\nrefresh();}})]});});Calendar.displayName='Calendar';export default Calendar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}