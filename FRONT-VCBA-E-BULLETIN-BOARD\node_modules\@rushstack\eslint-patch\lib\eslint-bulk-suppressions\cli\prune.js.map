{"version": 3, "file": "prune.js", "sourceRoot": "", "sources": ["../../../src/eslint-bulk-suppressions/cli/prune.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAY3D,gCAsBC;AAhCD,4CAAoB;AAEpB,mDAAoD;AACpD,2CAA6C;AAC7C,4CAA8D;AAC9D,sEAGmC;AAE5B,KAAK,UAAU,UAAU;IAC9B,MAAM,IAAI,GAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7C,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,IAAA,2BAAc,GAAE,CAAC;QACjB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAClF,CAAC;IAED,MAAM,aAAa,GAAW,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAChE,MAAM,QAAQ,GAAa,MAAM,8CAA8C,CAAC,aAAa,CAAC,CAAC;IAC/F,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,0CAA8B,CAAC,GAAG,GAAG,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;QACpE,MAAM,IAAA,0BAAc,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,IAAA,uEAA8C,EAAC,aAAa,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED,KAAK,UAAU,8CAA8C,CAAC,aAAqB;IACjF,MAAM,EAAE,UAAU,EAAE,0BAA0B,EAAE,GAC9C,IAAA,uEAA8C,EAAC,aAAa,CAAC,CAAC;IAChE,MAAM,QAAQ,GAAgB,IAAI,GAAG,EAAE,CAAC;IACxC,KAAK,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,0BAA0B,CAAC,YAAY,EAAE,CAAC;QACzE,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,aAAa,GAAa,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAErD,MAAM,gBAAgB,GAAa,EAAE,CAAC;IACtC,wGAAwG;IACxG,MAAM,OAAO,CAAC,GAAG,CACf,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,QAAgB,EAAE,EAAE;QAC3C,IAAI,CAAC;YACH,MAAM,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtD,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAAC,WAAM,CAAC;YACP,yBAAyB;QAC3B,CAAC;IACH,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,SAAS,gBAAgB,CAAC,MAAM,oCAAoC,CAAC,CAAC;IAClF,MAAM,YAAY,GAAW,aAAa,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAC5E,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,wCAAwC,CAAC,CAAC;IACvE,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport fs from 'fs';\n\nimport { printPruneHelp } from './utils/print-help';\nimport { runEslintAsync } from './runEslint';\nimport { ESLINT_BULK_PRUNE_ENV_VAR_NAME } from '../constants';\nimport {\n  deleteBulkSuppressionsFileInEslintConfigFolder,\n  getSuppressionsConfigForEslintConfigFolderPath\n} from '../bulk-suppressions-file';\n\nexport async function pruneAsync(): Promise<void> {\n  const args: string[] = process.argv.slice(3);\n\n  if (args.includes('--help') || args.includes('-h')) {\n    printPruneHelp();\n    process.exit(0);\n  }\n\n  if (args.length > 0) {\n    throw new Error(`@rushstack/eslint-bulk: Unknown arguments: ${args.join(' ')}`);\n  }\n\n  const normalizedCwd: string = process.cwd().replace(/\\\\/g, '/');\n  const allFiles: string[] = await getAllFilesWithExistingSuppressionsForCwdAsync(normalizedCwd);\n  if (allFiles.length > 0) {\n    process.env[ESLINT_BULK_PRUNE_ENV_VAR_NAME] = '1';\n    console.log(`Pruning suppressions for ${allFiles.length} files...`);\n    await runEslintAsync(allFiles, 'prune');\n  } else {\n    console.log('No files with existing suppressions found.');\n    deleteBulkSuppressionsFileInEslintConfigFolder(normalizedCwd);\n  }\n}\n\nasync function getAllFilesWithExistingSuppressionsForCwdAsync(normalizedCwd: string): Promise<string[]> {\n  const { jsonObject: bulkSuppressionsConfigJson } =\n    getSuppressionsConfigForEslintConfigFolderPath(normalizedCwd);\n  const allFiles: Set<string> = new Set();\n  for (const { file: filePath } of bulkSuppressionsConfigJson.suppressions) {\n    allFiles.add(filePath);\n  }\n\n  const allFilesArray: string[] = Array.from(allFiles);\n\n  const allExistingFiles: string[] = [];\n  // TODO: limit parallelism here with something similar to `Async.forEachAsync` from `node-core-library`.\n  await Promise.all(\n    allFilesArray.map(async (filePath: string) => {\n      try {\n        await fs.promises.access(filePath, fs.constants.F_OK);\n        allExistingFiles.push(filePath);\n      } catch {\n        // Doesn't exist - ignore\n      }\n    })\n  );\n\n  console.log(`Found ${allExistingFiles.length} files with existing suppressions.`);\n  const deletedCount: number = allFilesArray.length - allExistingFiles.length;\n  if (deletedCount > 0) {\n    console.log(`${deletedCount} files with suppressions were deleted.`);\n  }\n\n  return allExistingFiles;\n}\n"]}