{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useMemo}from'react';import{useAdminAuth}from'../contexts/AdminAuthContext';import{useStudentAuth}from'../contexts/StudentAuthContext';import{detectUserContext}from'../utils/authUtils';/**\n * Hook that provides unified authentication context\n * Automatically detects user role and provides appropriate auth context\n */export const useUnifiedAuth=forceRole=>{const[isLoading,setIsLoading]=useState(true);const[error,setError]=useState(null);// Get both auth contexts\nconst adminAuth=useAdminAuth();const studentAuth=useStudentAuth();// Detect user context\nconst userContext=useMemo(()=>{if(forceRole){const context=detectUserContext();return _objectSpread(_objectSpread({},context),{},{role:forceRole});}return detectUserContext();},[forceRole]);// Determine which auth context to use\nconst currentAuth=useMemo(()=>{if(userContext.role==='admin'){return{user:adminAuth.user,logout:adminAuth.logout,userType:'admin'};}else if(userContext.role==='student'){return{user:studentAuth.user,logout:studentAuth.logout,userType:'student'};}return null;},[userContext.role,adminAuth,studentAuth]);// Handle loading states\nuseEffect(()=>{const adminLoading=adminAuth.isLoading;const studentLoading=studentAuth.isLoading;// If we know the role, only wait for that specific auth context\nif(userContext.role==='admin'){setIsLoading(adminLoading);}else if(userContext.role==='student'){setIsLoading(studentLoading);}else{// If role is unknown, wait for both to finish loading\nsetIsLoading(adminLoading||studentLoading);}},[adminAuth.isLoading,studentAuth.isLoading,userContext.role]);// Handle error states\nuseEffect(()=>{if(userContext.role==='admin'&&adminAuth.error){setError(adminAuth.error);}else if(userContext.role==='student'&&studentAuth.error){setError(studentAuth.error);}else{setError(null);}},[adminAuth.error,studentAuth.error,userContext.role]);return{userContext,currentAuth,isLoading,error};};export default useUnifiedAuth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}