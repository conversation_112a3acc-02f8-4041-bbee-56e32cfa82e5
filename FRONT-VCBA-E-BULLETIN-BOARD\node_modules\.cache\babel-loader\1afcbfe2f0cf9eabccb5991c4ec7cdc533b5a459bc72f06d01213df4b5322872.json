{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{httpClient}from'./api.service';import{API_ENDPOINTS,STUDENT_AUTH_TOKEN_KEY,ADMIN_AUTH_TOKEN_KEY,ADMIN_USER_DATA_KEY,STUDENT_USER_DATA_KEY,API_BASE_URL}from'../config/constants';// Types for notifications\nclass NotificationService{// Role-based authentication context detection (similar to comment service)\ngetCurrentUserAuth(preferredUserType){const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);const adminUser=localStorage.getItem(ADMIN_USER_DATA_KEY);const studentUser=localStorage.getItem(STUDENT_USER_DATA_KEY);console.log('🔍 NotificationService - Detecting user authentication context:',{preferredUserType,hasAdminToken:!!adminToken,hasStudentToken:!!studentToken,hasAdminUser:!!adminUser,hasStudentUser:!!studentUser,currentPath:window.location.pathname});// If a preferred user type is specified, use that context first\nif(preferredUserType==='admin'&&adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 NotificationService - Using admin authentication (preferred)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}if(preferredUserType==='student'&&studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 NotificationService - Using student authentication (preferred)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// If no preference specified, determine based on current page context\nconst currentPath=window.location.pathname;const isAdminPage=currentPath.includes('/admin');const isStudentPage=currentPath.includes('/student');if(isAdminPage&&adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 NotificationService - Using admin authentication (admin page context)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}if(isStudentPage&&studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 NotificationService - Using student authentication (student page context)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// Fallback: Use student authentication if available (prioritize student over admin)\nif(studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 NotificationService - Using student authentication (fallback)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// Then try admin authentication\nif(adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 NotificationService - Using admin authentication (fallback)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}// Last resort: check tokens without user data validation (prioritize student)\nif(studentToken){console.log('🔑 NotificationService - Using student token (no user data)');return{useStudentAuth:true,token:studentToken,userType:'student'};}if(adminToken){console.log('🔑 NotificationService - Using admin token (no user data)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}// No authentication available\nconsole.warn('⚠️ NotificationService - No authentication context available');throw new Error('No authentication context available');}// Get user notifications with role-based authentication\nasync getNotifications(filters,preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);// Log authentication context for debugging\nconsole.log('NotificationService.getNotifications - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType,tokenPrefix:token?token.substring(0,10)+'...':null});const params=filters?this.buildQueryParams(filters):undefined;// Use specific authentication if we have a token\nif(useStudentAuth&&token){try{const url=params?\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.BASE,\"?\").concat(new URLSearchParams(params)):\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.BASE);const response=await fetch(url,{method:'GET',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)}});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Notifications retrieved successfully',data:result.data};}catch(error){throw new Error(error.message||'Failed to get notifications');}}// Use httpClient for admin authentication or general fallback\nreturn httpClient.get(API_ENDPOINTS.NOTIFICATIONS.BASE,params);}// Get unread notification count with role-based authentication\nasync getUnreadCount(preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);console.log('NotificationService.getUnreadCount - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType});// Use specific authentication if we have a token\nif(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT),{method:'GET',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)}});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Unread count retrieved successfully',data:result.data};}catch(error){throw new Error(error.message||'Failed to get unread count');}}return httpClient.get(API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT);}// Mark notification as read with role-based authentication\nasync markAsRead(id,preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);console.log('NotificationService.markAsRead - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType,notificationId:id});// Use specific authentication if we have a token\nif(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString())),{method:'PUT',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)}});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Notification marked as read',data:result.data};}catch(error){throw new Error(error.message||'Failed to mark notification as read');}}return httpClient.put(API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString()));}// Mark all notifications as read with role-based authentication\nasync markAllAsRead(preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);console.log('NotificationService.markAllAsRead - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType});// Use specific authentication if we have a token\nif(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ),{method:'PUT',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)}});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'All notifications marked as read',data:result.data};}catch(error){throw new Error(error.message||'Failed to mark all notifications as read');}}return httpClient.put(API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ);}// Delete notification with role-based authentication\nasync deleteNotification(id,preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);console.log('NotificationService.deleteNotification - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType,notificationId:id});// Use specific authentication if we have a token\nif(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.NOTIFICATIONS.BASE,\"/\").concat(id),{method:'DELETE',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)}});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Notification deleted successfully',data:result.data};}catch(error){throw new Error(error.message||'Failed to delete notification');}}return httpClient.delete(API_ENDPOINTS.NOTIFICATIONS.BASE+\"/\".concat(id));}// Helper method to build query parameters\nbuildQueryParams(filters){const params={};Object.entries(filters).forEach(_ref=>{let[key,value]=_ref;if(value!==undefined&&value!==null&&value!==''){params[key]=value.toString();}});return params;}// Get unread notifications with role-based authentication\nasync getUnreadNotifications(limit,preferredUserType){return this.getNotifications({is_read:false,limit:limit||20,sort_by:'created_at',sort_order:'DESC'},preferredUserType);}// Get recent notifications with role-based authentication\nasync getRecentNotifications(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;let preferredUserType=arguments.length>1?arguments[1]:undefined;return this.getNotifications({limit,sort_by:'created_at',sort_order:'DESC'},preferredUserType);}// Get notifications by type with role-based authentication\nasync getNotificationsByType(typeId,filters,preferredUserType){return this.getNotifications(_objectSpread({notification_type_id:typeId},filters),preferredUserType);}// Get announcement-related notifications\nasync getAnnouncementNotifications(announcementId){// This would require backend support for filtering by related_announcement_id\n// For now, return all notifications\nreturn this.getNotifications({sort_by:'created_at',sort_order:'DESC'});}// Get comment-related notifications\nasync getCommentNotifications(commentId){// This would require backend support for filtering by related_comment_id\n// For now, return all notifications\nreturn this.getNotifications({sort_by:'created_at',sort_order:'DESC'});}// Bulk mark notifications as read with role-based authentication\nasync bulkMarkAsRead(notificationIds,preferredUserType){// This would require a bulk endpoint in the backend\n// For now, mark each notification individually\nconst promises=notificationIds.map(id=>this.markAsRead(id,preferredUserType));await Promise.all(promises);return{success:true,message:'Notifications marked as read',data:undefined};}// Bulk delete notifications with role-based authentication\nasync bulkDeleteNotifications(notificationIds,preferredUserType){// This would require a bulk endpoint in the backend\n// For now, delete each notification individually\nconst promises=notificationIds.map(id=>this.deleteNotification(id,preferredUserType));await Promise.all(promises);return{success:true,message:'Notifications deleted',data:undefined};}// Get notification statistics with role-based authentication\nasync getNotificationStatistics(preferredUserType){try{var _allResponse$data,_unreadResponse$data;const[unreadResponse,allResponse]=await Promise.all([this.getUnreadCount(preferredUserType),this.getNotifications({limit:1},preferredUserType)]);return{total:((_allResponse$data=allResponse.data)===null||_allResponse$data===void 0?void 0:_allResponse$data.pagination.total)||0,unread:((_unreadResponse$data=unreadResponse.data)===null||_unreadResponse$data===void 0?void 0:_unreadResponse$data.unreadCount)||0,today:0,// Would need backend support\nthisWeek:0// Would need backend support\n};}catch(error){return{total:0,unread:0,today:0,thisWeek:0};}}// Check for new notifications (polling) with role-based authentication\nasync checkForNewNotifications(lastCheckTime,preferredUserType){try{var _response$data;const response=await this.getUnreadNotifications(10,preferredUserType);const notifications=((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.notifications)||[];let newNotifications=notifications;if(lastCheckTime){newNotifications=notifications.filter(notification=>new Date(notification.created_at)>new Date(lastCheckTime));}return{hasNew:newNotifications.length>0,count:newNotifications.length,notifications:newNotifications};}catch(error){return{hasNew:false,count:0,notifications:[]};}}// Format notification for display\nformatNotification(notification){return{title:notification.title,message:notification.message,timeAgo:this.getTimeAgo(notification.created_at),type:this.getNotificationType(notification.notification_type_id)};}// Get time ago string\ngetTimeAgo(dateString){const date=new Date(dateString);const now=new Date();const diffInSeconds=Math.floor((now.getTime()-date.getTime())/1000);if(diffInSeconds<60){return'Just now';}else if(diffInSeconds<3600){const minutes=Math.floor(diffInSeconds/60);return\"\".concat(minutes,\" minute\").concat(minutes>1?'s':'',\" ago\");}else if(diffInSeconds<86400){const hours=Math.floor(diffInSeconds/3600);return\"\".concat(hours,\" hour\").concat(hours>1?'s':'',\" ago\");}else if(diffInSeconds<604800){const days=Math.floor(diffInSeconds/86400);return\"\".concat(days,\" day\").concat(days>1?'s':'',\" ago\");}else{return date.toLocaleDateString();}}// Get notification type name\ngetNotificationType(typeId){const types={1:'announcement',2:'alert',3:'comment',4:'reaction',5:'system',6:'reminder'};return types[typeId]||'notification';}// Subscribe to real-time notifications (WebSocket)\nsubscribeToNotifications(callback){// This would implement WebSocket connection for real-time notifications\n// For now, return a dummy unsubscribe function\nconsole.log('Notification subscription would be implemented here');return()=>{console.log('Unsubscribing from notifications');};}}// Role-specific notification service classes\nclass AdminNotificationService extends NotificationService{constructor(){super();}async getNotifications(filters){console.log('🔧 AdminNotificationService - Getting notifications as admin');return super.getNotifications(filters,'admin');}async getUnreadCount(){console.log('🔧 AdminNotificationService - Getting unread count as admin');return super.getUnreadCount('admin');}async markAsRead(id){console.log('🔧 AdminNotificationService - Marking notification as read as admin');return super.markAsRead(id,'admin');}async markAllAsRead(){console.log('🔧 AdminNotificationService - Marking all notifications as read as admin');return super.markAllAsRead('admin');}async deleteNotification(id){console.log('🔧 AdminNotificationService - Deleting notification as admin');return super.deleteNotification(id,'admin');}async getUnreadNotifications(limit){console.log('🔧 AdminNotificationService - Getting unread notifications as admin');return super.getUnreadNotifications(limit,'admin');}async getRecentNotifications(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;console.log('🔧 AdminNotificationService - Getting recent notifications as admin');return super.getRecentNotifications(limit,'admin');}async getNotificationsByType(typeId,filters){console.log('🔧 AdminNotificationService - Getting notifications by type as admin');return super.getNotificationsByType(typeId,filters,'admin');}async getNotificationStatistics(){console.log('🔧 AdminNotificationService - Getting notification statistics as admin');return super.getNotificationStatistics('admin');}async checkForNewNotifications(lastCheckTime){console.log('🔧 AdminNotificationService - Checking for new notifications as admin');return super.checkForNewNotifications(lastCheckTime,'admin');}async bulkMarkAsRead(notificationIds){console.log('🔧 AdminNotificationService - Bulk marking notifications as read as admin');return super.bulkMarkAsRead(notificationIds,'admin');}async bulkDeleteNotifications(notificationIds){console.log('🔧 AdminNotificationService - Bulk deleting notifications as admin');return super.bulkDeleteNotifications(notificationIds,'admin');}}class StudentNotificationService extends NotificationService{constructor(){super();}async getNotifications(filters){console.log('🔧 StudentNotificationService - Getting notifications as student');return super.getNotifications(filters,'student');}async getUnreadCount(){console.log('🔧 StudentNotificationService - Getting unread count as student');return super.getUnreadCount('student');}async markAsRead(id){console.log('🔧 StudentNotificationService - Marking notification as read as student');return super.markAsRead(id,'student');}async markAllAsRead(){console.log('🔧 StudentNotificationService - Marking all notifications as read as student');return super.markAllAsRead('student');}async deleteNotification(id){console.log('🔧 StudentNotificationService - Deleting notification as student');return super.deleteNotification(id,'student');}async getUnreadNotifications(limit){console.log('🔧 StudentNotificationService - Getting unread notifications as student');return super.getUnreadNotifications(limit,'student');}async getRecentNotifications(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;console.log('🔧 StudentNotificationService - Getting recent notifications as student');return super.getRecentNotifications(limit,'student');}async getNotificationsByType(typeId,filters){console.log('🔧 StudentNotificationService - Getting notifications by type as student');return super.getNotificationsByType(typeId,filters,'student');}async getNotificationStatistics(){console.log('🔧 StudentNotificationService - Getting notification statistics as student');return super.getNotificationStatistics('student');}async checkForNewNotifications(lastCheckTime){console.log('🔧 StudentNotificationService - Checking for new notifications as student');return super.checkForNewNotifications(lastCheckTime,'student');}async bulkMarkAsRead(notificationIds){console.log('🔧 StudentNotificationService - Bulk marking notifications as read as student');return super.bulkMarkAsRead(notificationIds,'student');}async bulkDeleteNotifications(notificationIds){console.log('🔧 StudentNotificationService - Bulk deleting notifications as student');return super.bulkDeleteNotifications(notificationIds,'student');}}export const notificationService=new NotificationService();export const adminNotificationService=new AdminNotificationService();export const studentNotificationService=new StudentNotificationService();export default notificationService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}