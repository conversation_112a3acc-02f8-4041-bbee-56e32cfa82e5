{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2\",\n  key: \"srzb37\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"15.5\",\n  key: \"1t73f2\"\n}], [\"polyline\", {\n  points: \"22 8.5 12 15.5 2 8.5\",\n  key: \"ajlxae\"\n}], [\"polyline\", {\n  points: \"2 15.5 12 8.5 22 15.5\",\n  key: \"susrui\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"8.5\",\n  key: \"2cldga\"\n}]];\nconst Codepen = createLucideIcon(\"codepen\", __iconNode);\nexport { __iconNode, Codepen as default };\n//# sourceMappingURL=codepen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}