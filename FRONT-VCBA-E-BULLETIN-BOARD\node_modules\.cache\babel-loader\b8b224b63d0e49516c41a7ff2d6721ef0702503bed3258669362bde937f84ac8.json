{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5\",\n  key: \"1gvzjb\"\n}], [\"path\", {\n  d: \"M9 18h6\",\n  key: \"x1upvd\"\n}], [\"path\", {\n  d: \"M10 22h4\",\n  key: \"ceow96\"\n}]];\nconst Lightbulb = createLucideIcon(\"lightbulb\", __iconNode);\nexport { __iconNode, Lightbulb as default };\n//# sourceMappingURL=lightbulb.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}