{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 9-3 3 3 3\",\n  key: \"1oro0q\"\n}], [\"path\", {\n  d: \"m14 15 3-3-3-3\",\n  key: \"bz13h7\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"h1oib\"\n}]];\nconst SquareCode = createLucideIcon(\"square-code\", __iconNode);\nexport { __iconNode, SquareCode as default };\n//# sourceMappingURL=square-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}