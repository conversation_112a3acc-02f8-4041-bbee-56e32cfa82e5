{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m20 19.5-5.5 1.2\",\n  key: \"1aenhr\"\n}], [\"path\", {\n  d: \"M14.5 4v11.22a1 1 0 0 0 1.242.97L20 15.2\",\n  key: \"2rtezt\"\n}], [\"path\", {\n  d: \"m2.978 19.351 5.549-1.363A2 2 0 0 0 10 16V2\",\n  key: \"1kbm92\"\n}], [\"path\", {\n  d: \"M20 10 4 13.5\",\n  key: \"8nums9\"\n}]];\nconst SaudiRiyal = createLucideIcon(\"saudi-riyal\", __iconNode);\nexport { __iconNode, SaudiRiyal as default };\n//# sourceMappingURL=saudi-riyal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}