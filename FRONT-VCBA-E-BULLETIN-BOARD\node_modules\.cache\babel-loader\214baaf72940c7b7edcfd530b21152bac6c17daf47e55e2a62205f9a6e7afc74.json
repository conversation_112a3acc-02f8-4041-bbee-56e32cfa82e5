{"ast": null, "code": "import { apiClient } from './apiClient';\nclass CalendarReactionService {\n  // Like a calendar event\n  async likeEvent(eventId) {\n    try {\n      const response = await apiClient.post(`/api/calendar/${eventId}/like`);\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error liking calendar event:', error);\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId) {\n    try {\n      const response = await apiClient.delete(`/api/calendar/${eventId}/like`);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error unliking calendar event:', error);\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["apiClient", "CalendarReactionService", "likeEvent", "eventId", "response", "post", "data", "error", "_error$response", "_error$response$data", "console", "Error", "message", "unlikeEvent", "delete", "_error$response2", "_error$response2$data", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: {\n    added?: boolean;\n    removed?: boolean;\n  };\n}\n\nclass CalendarReactionService {\n  // Like a calendar event\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const response = await apiClient.post(`/api/calendar/${eventId}/like`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.response?.data?.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const response = await apiClient.delete(`/api/calendar/${eventId}/like`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.response?.data?.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAWvC,MAAMC,uBAAuB,CAAC;EAC5B;EACA,MAAMC,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,iBAAiBF,OAAO,OAAO,CAAC;MACtE,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBC,OAAO,CAACH,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAII,KAAK,CAAC,EAAAH,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,+BAA+B,CAAC;IACnF;EACF;;EAEA;EACA,MAAMC,WAAWA,CAACV,OAAe,EAAqC;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACc,MAAM,CAAC,iBAAiBX,OAAO,OAAO,CAAC;MACxE,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAQ,gBAAA,EAAAC,qBAAA;MACnBN,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAII,KAAK,CAAC,EAAAI,gBAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,iCAAiC,CAAC;IACrF;EACF;;EAEA;EACA,MAAMK,UAAUA,CAACd,OAAe,EAAEe,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACL,WAAW,CAACV,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAMgB,uBAAuB,GAAG,IAAIlB,uBAAuB,CAAC,CAAC;AACpE,eAAekB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}