{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister } from './pages';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport AdminNewsfeed from './pages/admin/AdminNewsfeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\nimport StudentLayout from './components/student/layout/StudentLayout';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport StudentNewsfeed from './pages/student/StudentNewsfeed';\nimport StudentSettings from './pages/student/StudentSettings';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartRedirect = () => {\n  _s();\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Default to admin login for all other paths\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/admin/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 10\n  }, this);\n};\n\n// Admin Routes Component with isolated auth context\n_s(SmartRedirect, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = SmartRedirect;\nconst AdminRoutes = () => /*#__PURE__*/_jsxDEV(AdminAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/register\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(AdminRegister, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminNewsfeed, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/calendar\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Calendar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/posts\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(PostManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/student-management\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(StudentManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/settings\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/debug\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/admin/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 40,\n  columnNumber: 3\n}, this);\n\n// Student Routes Component with isolated auth context\n_c2 = AdminRoutes;\nconst StudentRoutes = () => /*#__PURE__*/_jsxDEV(StudentAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(StudentLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(StudentLayout, {\n          children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(StudentNewsfeed, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/settings\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(StudentLayout, {\n          children: /*#__PURE__*/_jsxDEV(StudentSettings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/student/newsfeed\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 144,\n  columnNumber: 3\n}, this);\n_c3 = StudentRoutes;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ToastProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/admin/login\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/*\",\n            element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/student/*\",\n            element: /*#__PURE__*/_jsxDEV(StudentRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(SmartRedirect, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SmartRedirect\");\n$RefreshReg$(_c2, \"AdminRoutes\");\n$RefreshReg$(_c3, \"StudentRoutes\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "AdminAuth<PERSON><PERSON><PERSON>", "StudentAuthProvider", "ToastProvider", "ProtectedRoute", "PublicRoute", "Error<PERSON>ou<PERSON><PERSON>", "AdminLogin", "StudentLogin", "AdminRegister", "AdminLayout", "AdminDashboard", "AdminNewsfeed", "Calendar", "PostManagement", "StudentManagement", "Settings", "ApiTest", "StudentLayout", "StudentDashboard", "StudentNewsfeed", "StudentSettings", "jsxDEV", "_jsxDEV", "SmartRedirect", "_s", "location", "pathname", "startsWith", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminRoutes", "children", "path", "element", "restricted", "requiredRole", "_c2", "StudentRoutes", "_c3", "App", "className", "_c4", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AuthProvider } from './contexts';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister, WelcomePage } from './pages';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport AdminNewsfeed from './pages/admin/AdminNewsfeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\nimport StudentLayout from './components/student/layout/StudentLayout';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport StudentNewsfeed from './pages/student/StudentNewsfeed';\nimport StudentSettings from './pages/student/StudentSettings';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nconst SmartRedirect: React.FC = () => {\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return <Navigate to=\"/student/login\" replace />;\n  }\n\n  // Default to admin login for all other paths\n  return <Navigate to=\"/admin/login\" replace />;\n};\n\n// Admin Routes Component with isolated auth context\nconst AdminRoutes: React.FC = () => (\n  <AdminAuthProvider>\n    <Routes>\n      {/* Admin public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <AdminLogin />\n          </PublicRoute>\n        }\n      />\n\n      <Route\n        path=\"/register\"\n        element={\n          <PublicRoute restricted>\n            <ErrorBoundary>\n              <AdminRegister />\n            </ErrorBoundary>\n          </PublicRoute>\n        }\n      />\n\n      {/* Admin protected routes with layout */}\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <AdminDashboard />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminNewsfeed />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/calendar\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Calendar />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/posts\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <PostManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/student-management\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <StudentManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/settings\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Settings />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/debug\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <ApiTest />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <Navigate to=\"/admin/dashboard\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </AdminAuthProvider>\n);\n\n// Student Routes Component with isolated auth context\nconst StudentRoutes: React.FC = () => (\n  <StudentAuthProvider>\n    <Routes>\n      {/* Student public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <StudentLogin />\n          </PublicRoute>\n        }\n      />\n\n      {/* Student protected routes with layout */}\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <StudentLayout>\n              <StudentDashboard />\n            </StudentLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <StudentNewsfeed />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/settings\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <StudentLayout>\n              <StudentSettings />\n            </StudentLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <Navigate to=\"/student/newsfeed\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </StudentAuthProvider>\n);\n\nfunction App() {\n  return (\n    <ToastProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            {/* Default redirect to admin login */}\n            <Route path=\"/\" element={<Navigate to=\"/admin/login\" replace />} />\n\n            {/* Admin routes with isolated auth context */}\n            <Route path=\"/admin/*\" element={<AdminRoutes />} />\n\n            {/* Student routes with isolated auth context */}\n            <Route path=\"/student/*\" element={<StudentRoutes />} />\n\n            {/* Catch all route - smart redirect based on path */}\n            <Route path=\"*\" element={<SmartRedirect />} />\n          </Routes>\n        </div>\n      </Router>\n    </ToastProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAEhG,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,EAAEC,WAAW,QAAQ,qBAAqB;AACjE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,2BAA2B;AAClC,SAASC,UAAU,EAAEC,YAAY,EAAEC,aAAa,QAAqB,SAAS;AAC9E,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,aAAa,MAAM,2CAA2C;AACrE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAI0B,QAAQ,CAACC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC5C,oBAAOL,OAAA,CAACxB,QAAQ;MAAC8B,EAAE,EAAC,gBAAgB;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD;;EAEA;EACA,oBAAOX,OAAA,CAACxB,QAAQ;IAAC8B,EAAE,EAAC,cAAc;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;;AAED;AAAAT,EAAA,CAZMD,aAAuB;EAAA,QACVxB,WAAW;AAAA;AAAAmC,EAAA,GADxBX,aAAuB;AAa7B,MAAMY,WAAqB,GAAGA,CAAA,kBAC5Bb,OAAA,CAACtB,iBAAiB;EAAAoC,QAAA,eAChBd,OAAA,CAAC1B,MAAM;IAAAwC,QAAA,gBAELd,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAAClB,WAAW;QAACmC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAAChB,UAAU;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAAClB,WAAW;QAACmC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACjB,aAAa;UAAA+B,QAAA,eACZd,OAAA,CAACd,aAAa;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,YAAY;MACjBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACb,WAAW;UAAA2B,QAAA,eACVd,OAAA,CAACZ,cAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,aAAa;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACb,WAAW;UAAA2B,QAAA,eACVd,OAAA,CAACV,QAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACb,WAAW;UAAA2B,QAAA,eACVd,OAAA,CAACT,cAAc;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,qBAAqB;MAC1BC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACb,WAAW;UAAA2B,QAAA,eACVd,OAAA,CAACR,iBAAiB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACb,WAAW;UAAA2B,QAAA,eACVd,OAAA,CAACP,QAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACN,OAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACxB,QAAQ;UAAC8B,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACQ,CACpB;;AAED;AAAAQ,GAAA,GAvGMN,WAAqB;AAwG3B,MAAMO,aAAuB,GAAGA,CAAA,kBAC9BpB,OAAA,CAACrB,mBAAmB;EAAAmC,QAAA,eAClBd,OAAA,CAAC1B,MAAM;IAAAwC,QAAA,gBAELd,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAAClB,WAAW;QAACmC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACf,YAAY;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,YAAY;MACjBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACL,aAAa;UAAAmB,QAAA,eACZd,OAAA,CAACJ,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACH,eAAe;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACL,aAAa;UAAAmB,QAAA,eACZd,OAAA,CAACF,eAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACzB,KAAK;MACJwC,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAACnB,cAAc;QAACqC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACxB,QAAQ;UAAC8B,EAAE,EAAC,mBAAmB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACU,CACtB;AAACU,GAAA,GApDID,aAAuB;AAsD7B,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEtB,OAAA,CAACpB,aAAa;IAAAkC,QAAA,eACZd,OAAA,CAAC3B,MAAM;MAAAyC,QAAA,eACLd,OAAA;QAAKuB,SAAS,EAAC,KAAK;QAAAT,QAAA,eAClBd,OAAA,CAAC1B,MAAM;UAAAwC,QAAA,gBAELd,OAAA,CAACzB,KAAK;YAACwC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACxB,QAAQ;cAAC8B,EAAE,EAAC,cAAc;cAACC,OAAO;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnEX,OAAA,CAACzB,KAAK;YAACwC,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhB,OAAA,CAACa,WAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnDX,OAAA,CAACzB,KAAK;YAACwC,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACoB,aAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGvDX,OAAA,CAACzB,KAAK;YAACwC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACC,aAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACa,GAAA,GAtBQF,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAV,EAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}