{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 14-1 1\",\n  key: \"11onhr\"\n}], [\"path\", {\n  d: \"m13.75 18.25-1.25 1.42\",\n  key: \"1yisr3\"\n}], [\"path\", {\n  d: \"M17.775 5.654a15.68 15.68 0 0 0-12.121 12.12\",\n  key: \"1qtqk6\"\n}], [\"path\", {\n  d: \"M18.8 9.3a1 1 0 0 0 2.1 7.7\",\n  key: \"fbbbr2\"\n}], [\"path\", {\n  d: \"M21.964 20.732a1 1 0 0 1-1.232 1.232l-18-5a1 1 0 0 1-.695-1.232A19.68 19.68 0 0 1 15.732 2.037a1 1 0 0 1 1.232.695z\",\n  key: \"1hyfdd\"\n}]];\nconst Pizza = createLucideIcon(\"pizza\", __iconNode);\nexport { __iconNode, Pizza as default };\n//# sourceMappingURL=pizza.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}