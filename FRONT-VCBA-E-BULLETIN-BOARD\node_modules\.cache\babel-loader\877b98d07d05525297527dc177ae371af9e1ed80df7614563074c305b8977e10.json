{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v6.292a7 7 0 1 0 4 0V2\",\n  key: \"1s42pc\"\n}], [\"path\", {\n  d: \"M5 15h14\",\n  key: \"m0yey3\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}]];\nconst FlaskRound = createLucideIcon(\"flask-round\", __iconNode);\nexport { __iconNode, FlaskRound as default };\n//# sourceMappingURL=flask-round.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}