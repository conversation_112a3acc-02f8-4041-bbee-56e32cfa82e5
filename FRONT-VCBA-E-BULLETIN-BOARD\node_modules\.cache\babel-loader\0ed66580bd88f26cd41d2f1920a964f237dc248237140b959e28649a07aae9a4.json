{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 9V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h4\",\n  key: \"daa4of\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"7\",\n  x: \"12\",\n  y: \"13\",\n  rx: \"2\",\n  key: \"1nb8gs\"\n}]];\nconst PictureInPicture2 = createLucideIcon(\"picture-in-picture-2\", __iconNode);\nexport { __iconNode, PictureInPicture2 as default };\n//# sourceMappingURL=picture-in-picture-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}