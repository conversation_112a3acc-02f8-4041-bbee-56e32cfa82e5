{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 12a10.06 10.06 1 0 0-20 0Z\",\n  key: \"1teyop\"\n}], [\"path\", {\n  d: \"M12 12v8a2 2 0 0 0 4 0\",\n  key: \"ulpmoc\"\n}], [\"path\", {\n  d: \"M12 2v1\",\n  key: \"11qlp1\"\n}]];\nconst Umbrella = createLucideIcon(\"umbrella\", __iconNode);\nexport { __iconNode, Umbrella as default };\n//# sourceMappingURL=umbrella.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}