{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\CalendarEventLikeButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Heart } from 'lucide-react';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CalendarEventLikeButton = ({\n  eventId,\n  initialLiked,\n  initialCount,\n  onLikeChange,\n  size = 'medium',\n  showCount = true\n}) => {\n  _s();\n  const [liked, setLiked] = useState(initialLiked);\n  const [count, setCount] = useState(initialCount);\n  const [loading, setLoading] = useState(false);\n  const sizeConfig = {\n    small: {\n      iconSize: 14,\n      fontSize: '0.75rem',\n      padding: '0.25rem 0.5rem'\n    },\n    medium: {\n      iconSize: 16,\n      fontSize: '0.875rem',\n      padding: '0.5rem 0.75rem'\n    },\n    large: {\n      iconSize: 18,\n      fontSize: '1rem',\n      padding: '0.75rem 1rem'\n    }\n  };\n  const config = sizeConfig[size];\n  const handleToggleLike = async () => {\n    if (loading) return;\n    setLoading(true);\n    try {\n      const response = await calendarReactionService.toggleLike(eventId, liked);\n      if (response.success) {\n        const newLiked = !liked;\n        const newCount = newLiked ? count + 1 : count - 1;\n        setLiked(newLiked);\n        setCount(Math.max(0, newCount)); // Ensure count doesn't go below 0\n\n        // Notify parent component\n        if (onLikeChange) {\n          onLikeChange(newLiked, newCount);\n        }\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      // Could add toast notification here\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: handleToggleLike,\n    disabled: loading,\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.25rem',\n      padding: config.padding,\n      backgroundColor: liked ? '#fef2f2' : 'transparent',\n      border: liked ? '1px solid #fecaca' : '1px solid #e5e7eb',\n      borderRadius: '0.375rem',\n      color: liked ? '#dc2626' : '#6b7280',\n      fontSize: config.fontSize,\n      fontWeight: '500',\n      cursor: loading ? 'not-allowed' : 'pointer',\n      transition: 'all 0.2s ease-in-out',\n      opacity: loading ? 0.6 : 1\n    },\n    onMouseEnter: e => {\n      if (!loading) {\n        e.currentTarget.style.backgroundColor = liked ? '#fee2e2' : '#f9fafb';\n        e.currentTarget.style.borderColor = liked ? '#fca5a5' : '#d1d5db';\n      }\n    },\n    onMouseLeave: e => {\n      if (!loading) {\n        e.currentTarget.style.backgroundColor = liked ? '#fef2f2' : 'transparent';\n        e.currentTarget.style.borderColor = liked ? '#fecaca' : '#e5e7eb';\n      }\n    },\n    title: liked ? 'Unlike this event' : 'Like this event',\n    children: [/*#__PURE__*/_jsxDEV(Heart, {\n      size: config.iconSize,\n      fill: liked ? 'currentColor' : 'none',\n      style: {\n        transition: 'all 0.2s ease-in-out',\n        transform: loading ? 'scale(0.9)' : 'scale(1)'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), showCount && /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        minWidth: '1rem',\n        textAlign: 'left'\n      },\n      children: count\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendarEventLikeButton, \"pL7x6X2rLHJ0yfDY9jBYlTRC2w0=\");\n_c = CalendarEventLikeButton;\nexport default CalendarEventLikeButton;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventLikeButton\");", "map": {"version": 3, "names": ["React", "useState", "Heart", "calendarReactionService", "jsxDEV", "_jsxDEV", "CalendarEventLikeButton", "eventId", "initialLiked", "initialCount", "onLikeChange", "size", "showCount", "_s", "liked", "setLiked", "count", "setCount", "loading", "setLoading", "sizeConfig", "small", "iconSize", "fontSize", "padding", "medium", "large", "config", "handleToggleLike", "response", "toggleLike", "success", "newLiked", "newCount", "Math", "max", "error", "console", "onClick", "disabled", "style", "display", "alignItems", "gap", "backgroundColor", "border", "borderRadius", "color", "fontWeight", "cursor", "transition", "opacity", "onMouseEnter", "e", "currentTarget", "borderColor", "onMouseLeave", "title", "children", "fill", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/CalendarEventLikeButton.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Heart } from 'lucide-react';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\n\ninterface CalendarEventLikeButtonProps {\n  eventId: number;\n  initialLiked: boolean;\n  initialCount: number;\n  userRole?: 'admin' | 'student'; // Add role prop for explicit role handling\n  onLikeChange?: (liked: boolean, newCount: number) => void;\n  size?: 'small' | 'medium' | 'large';\n  showCount?: boolean;\n}\n\nconst CalendarEventLikeButton: React.FC<CalendarEventLikeButtonProps> = ({\n  eventId,\n  initialLiked,\n  initialCount,\n  onLikeChange,\n  size = 'medium',\n  showCount = true\n}) => {\n  const [liked, setLiked] = useState(initialLiked);\n  const [count, setCount] = useState(initialCount);\n  const [loading, setLoading] = useState(false);\n\n  const sizeConfig = {\n    small: { iconSize: 14, fontSize: '0.75rem', padding: '0.25rem 0.5rem' },\n    medium: { iconSize: 16, fontSize: '0.875rem', padding: '0.5rem 0.75rem' },\n    large: { iconSize: 18, fontSize: '1rem', padding: '0.75rem 1rem' }\n  };\n\n  const config = sizeConfig[size];\n\n  const handleToggleLike = async () => {\n    if (loading) return;\n\n    setLoading(true);\n    try {\n      const response = await calendarReactionService.toggleLike(eventId, liked);\n      \n      if (response.success) {\n        const newLiked = !liked;\n        const newCount = newLiked ? count + 1 : count - 1;\n        \n        setLiked(newLiked);\n        setCount(Math.max(0, newCount)); // Ensure count doesn't go below 0\n        \n        // Notify parent component\n        if (onLikeChange) {\n          onLikeChange(newLiked, newCount);\n        }\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      // Could add toast notification here\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleToggleLike}\n      disabled={loading}\n      style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.25rem',\n        padding: config.padding,\n        backgroundColor: liked ? '#fef2f2' : 'transparent',\n        border: liked ? '1px solid #fecaca' : '1px solid #e5e7eb',\n        borderRadius: '0.375rem',\n        color: liked ? '#dc2626' : '#6b7280',\n        fontSize: config.fontSize,\n        fontWeight: '500',\n        cursor: loading ? 'not-allowed' : 'pointer',\n        transition: 'all 0.2s ease-in-out',\n        opacity: loading ? 0.6 : 1\n      }}\n      onMouseEnter={(e) => {\n        if (!loading) {\n          e.currentTarget.style.backgroundColor = liked ? '#fee2e2' : '#f9fafb';\n          e.currentTarget.style.borderColor = liked ? '#fca5a5' : '#d1d5db';\n        }\n      }}\n      onMouseLeave={(e) => {\n        if (!loading) {\n          e.currentTarget.style.backgroundColor = liked ? '#fef2f2' : 'transparent';\n          e.currentTarget.style.borderColor = liked ? '#fecaca' : '#e5e7eb';\n        }\n      }}\n      title={liked ? 'Unlike this event' : 'Like this event'}\n    >\n      <Heart\n        size={config.iconSize}\n        fill={liked ? 'currentColor' : 'none'}\n        style={{\n          transition: 'all 0.2s ease-in-out',\n          transform: loading ? 'scale(0.9)' : 'scale(1)'\n        }}\n      />\n      {showCount && (\n        <span style={{ minWidth: '1rem', textAlign: 'left' }}>\n          {count}\n        </span>\n      )}\n    </button>\n  );\n};\n\nexport default CalendarEventLikeButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,uBAAuB,QAAQ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAajF,MAAMC,uBAA+D,GAAGA,CAAC;EACvEC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAACO,YAAY,CAAC;EAChD,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAACQ,YAAY,CAAC;EAChD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMmB,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAiB,CAAC;IACvEC,MAAM,EAAE;MAAEH,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,OAAO,EAAE;IAAiB,CAAC;IACzEE,KAAK,EAAE;MAAEJ,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAe;EACnE,CAAC;EAED,MAAMG,MAAM,GAAGP,UAAU,CAACT,IAAI,CAAC;EAE/B,MAAMiB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIV,OAAO,EAAE;IAEbC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAM1B,uBAAuB,CAAC2B,UAAU,CAACvB,OAAO,EAAEO,KAAK,CAAC;MAEzE,IAAIe,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,QAAQ,GAAG,CAAClB,KAAK;QACvB,MAAMmB,QAAQ,GAAGD,QAAQ,GAAGhB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;QAEjDD,QAAQ,CAACiB,QAAQ,CAAC;QAClBf,QAAQ,CAACiB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAEjC;QACA,IAAIvB,YAAY,EAAE;UAChBA,YAAY,CAACsB,QAAQ,EAAEC,QAAQ,CAAC;QAClC;MACF;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEd,OAAA;IACEiC,OAAO,EAAEV,gBAAiB;IAC1BW,QAAQ,EAAErB,OAAQ;IAClBsB,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,SAAS;MACdnB,OAAO,EAAEG,MAAM,CAACH,OAAO;MACvBoB,eAAe,EAAE9B,KAAK,GAAG,SAAS,GAAG,aAAa;MAClD+B,MAAM,EAAE/B,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;MACzDgC,YAAY,EAAE,UAAU;MACxBC,KAAK,EAAEjC,KAAK,GAAG,SAAS,GAAG,SAAS;MACpCS,QAAQ,EAAEI,MAAM,CAACJ,QAAQ;MACzByB,UAAU,EAAE,KAAK;MACjBC,MAAM,EAAE/B,OAAO,GAAG,aAAa,GAAG,SAAS;MAC3CgC,UAAU,EAAE,sBAAsB;MAClCC,OAAO,EAAEjC,OAAO,GAAG,GAAG,GAAG;IAC3B,CAAE;IACFkC,YAAY,EAAGC,CAAC,IAAK;MACnB,IAAI,CAACnC,OAAO,EAAE;QACZmC,CAAC,CAACC,aAAa,CAACd,KAAK,CAACI,eAAe,GAAG9B,KAAK,GAAG,SAAS,GAAG,SAAS;QACrEuC,CAAC,CAACC,aAAa,CAACd,KAAK,CAACe,WAAW,GAAGzC,KAAK,GAAG,SAAS,GAAG,SAAS;MACnE;IACF,CAAE;IACF0C,YAAY,EAAGH,CAAC,IAAK;MACnB,IAAI,CAACnC,OAAO,EAAE;QACZmC,CAAC,CAACC,aAAa,CAACd,KAAK,CAACI,eAAe,GAAG9B,KAAK,GAAG,SAAS,GAAG,aAAa;QACzEuC,CAAC,CAACC,aAAa,CAACd,KAAK,CAACe,WAAW,GAAGzC,KAAK,GAAG,SAAS,GAAG,SAAS;MACnE;IACF,CAAE;IACF2C,KAAK,EAAE3C,KAAK,GAAG,mBAAmB,GAAG,iBAAkB;IAAA4C,QAAA,gBAEvDrD,OAAA,CAACH,KAAK;MACJS,IAAI,EAAEgB,MAAM,CAACL,QAAS;MACtBqC,IAAI,EAAE7C,KAAK,GAAG,cAAc,GAAG,MAAO;MACtC0B,KAAK,EAAE;QACLU,UAAU,EAAE,sBAAsB;QAClCU,SAAS,EAAE1C,OAAO,GAAG,YAAY,GAAG;MACtC;IAAE;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACDpD,SAAS,iBACRP,OAAA;MAAMmC,KAAK,EAAE;QAAEyB,QAAQ,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAR,QAAA,EAClD1C;IAAK;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACnD,EAAA,CA/FIP,uBAA+D;AAAA6D,EAAA,GAA/D7D,uBAA+D;AAiGrE,eAAeA,uBAAuB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}