{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22a2 2 0 0 1-2-2\",\n  key: \"i7yj1i\"\n}], [\"path\", {\n  d: \"M16 22h-2\",\n  key: \"18d249\"\n}], [\"path\", {\n  d: \"M16 4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-5a2 2 0 0 1 2-2h5a1 1 0 0 0 1-1z\",\n  key: \"1njgbb\"\n}], [\"path\", {\n  d: \"M20 8a2 2 0 0 1 2 2\",\n  key: \"1770vt\"\n}], [\"path\", {\n  d: \"M22 14v2\",\n  key: \"iot8ja\"\n}], [\"path\", {\n  d: \"M22 20a2 2 0 0 1-2 2\",\n  key: \"qj8q6g\"\n}]];\nconst SquaresSubtract = createLucideIcon(\"squares-subtract\", __iconNode);\nexport { __iconNode, SquaresSubtract as default };\n//# sourceMappingURL=squares-subtract.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}