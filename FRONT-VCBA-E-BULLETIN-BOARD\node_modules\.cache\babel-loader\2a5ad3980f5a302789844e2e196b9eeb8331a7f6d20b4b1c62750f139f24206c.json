{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 12h3.28a1 1 0 0 1 .948.684l2.298 7.934a.5.5 0 0 0 .96-.044L13.82 4.771A1 1 0 0 1 14.792 4H21\",\n  key: \"1mqj8i\"\n}]];\nconst Radical = createLucideIcon(\"radical\", __iconNode);\nexport { __iconNode, Radical as default };\n//# sourceMappingURL=radical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}