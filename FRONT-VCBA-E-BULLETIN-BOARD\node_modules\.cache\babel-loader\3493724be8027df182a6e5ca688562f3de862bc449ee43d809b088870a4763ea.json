{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 9V7a2 2 0 0 0-2-2h-6\",\n  key: \"19z8uc\"\n}], [\"path\", {\n  d: \"m15 2-3 3 3 3\",\n  key: \"177bxs\"\n}], [\"path\", {\n  d: \"M20 13v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2\",\n  key: \"d36hnl\"\n}]];\nconst RotateCcwSquare = createLucideIcon(\"rotate-ccw-square\", __iconNode);\nexport { __iconNode, RotateCcwSquare as default };\n//# sourceMappingURL=rotate-ccw-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}