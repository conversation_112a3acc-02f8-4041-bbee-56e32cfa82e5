{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z\",\n  key: \"c7niix\"\n}]];\nconst Droplet = createLucideIcon(\"droplet\", __iconNode);\nexport { __iconNode, Droplet as default };\n//# sourceMappingURL=droplet.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}