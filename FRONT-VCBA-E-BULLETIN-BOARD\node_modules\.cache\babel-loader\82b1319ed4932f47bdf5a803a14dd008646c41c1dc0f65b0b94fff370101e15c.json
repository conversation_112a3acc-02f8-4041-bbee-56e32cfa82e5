{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.305 19.53.923-.382\",\n  key: \"3m78fa\"\n}], [\"path\", {\n  d: \"m15.228 16.852-.923-.383\",\n  key: \"npixar\"\n}], [\"path\", {\n  d: \"m16.852 15.228-.383-.923\",\n  key: \"5xggr7\"\n}], [\"path\", {\n  d: \"m16.852 20.772-.383.924\",\n  key: \"dpfhf9\"\n}], [\"path\", {\n  d: \"m19.148 15.228.383-.923\",\n  key: \"1reyyz\"\n}], [\"path\", {\n  d: \"m19.53 21.696-.382-.924\",\n  key: \"1goivc\"\n}], [\"path\", {\n  d: \"M2 21a8 8 0 0 1 10.434-7.62\",\n  key: \"1yezr2\"\n}], [\"path\", {\n  d: \"m20.772 16.852.924-.383\",\n  key: \"htqkph\"\n}], [\"path\", {\n  d: \"m20.772 19.148.924.383\",\n  key: \"9w9pjp\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}]];\nconst UserRoundCog = createLucideIcon(\"user-round-cog\", __iconNode);\nexport { __iconNode, UserRoundCog as default };\n//# sourceMappingURL=user-round-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}