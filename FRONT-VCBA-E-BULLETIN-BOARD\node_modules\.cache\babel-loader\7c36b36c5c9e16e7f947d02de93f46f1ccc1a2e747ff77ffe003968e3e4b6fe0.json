{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useCallback,useRef,useEffect}from'react';import{Upload,X,AlertCircle,CheckCircle,RotateCcw}from'lucide-react';import{validateFile,formatFileSize}from'../../utils/formUtils';import{getImageUrl}from'../../config/constants';// Reusable delete button component\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DeleteButton=_ref=>{let{onClick,isMarkedForDeletion=false,title}=_ref;return/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onClick,style:{padding:'0.25rem',backgroundColor:isMarkedForDeletion?'rgba(34, 197, 94, 0.9)'// Green for undo\n:'rgba(239, 68, 68, 0.9)',// Red for delete\ncolor:'white',border:'none',borderRadius:'4px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:title||(isMarkedForDeletion?\"Undo deletion\":\"Delete\"),children:isMarkedForDeletion?/*#__PURE__*/_jsx(RotateCcw,{size:14}):/*#__PURE__*/_jsx(X,{size:14})});};// Custom hook for CORS-safe image loading\nconst useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);setLoading(false);setError(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}console.log('🔄 Loading image via CORS-safe method:',fullUrl);const response=await fetch(fullUrl,{method:'GET',headers:{'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);console.log('✅ Image loaded successfully via CORS-safe method');setImageUrl(objectUrl);}catch(err){console.error('❌ Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// CORS-safe image component\nconst SafeImage=_ref2=>{let{imagePath,alt,style,className}=_ref2;const{imageUrl,loading,error}=useImageLoader(imagePath);if(loading){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Loading...\"})]})});}if(error||!imageUrl){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Image loading...\"})]})});}return/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:alt,style:style,className:className,onLoad:()=>{console.log('✅ Image rendered successfully via CORS-safe method');}});};const MultipleImageUpload=_ref3=>{let{onImagesChange,existingImages=[],onExistingImageDelete,onSetPrimary,maxImages=10,maxFileSize=5*1024*1024,// 5MB\nacceptedTypes=['image/jpeg','image/png','image/gif','image/webp'],className='',disabled=false,pendingDeletes=[],onMarkForDeletion,onUnmarkForDeletion}=_ref3;const[images,setImages]=useState([]);const[dragActive,setDragActive]=useState(false);const[error,setError]=useState(null);const fileInputRef=useRef(null);const dragCounter=useRef(0);// Debug existing images\nuseEffect(()=>{console.log('🖼️ MultipleImageUpload - Existing images received:',{count:existingImages.length,images:existingImages.map(img=>({id:img.attachment_id,name:img.file_name}))});},[existingImages]);// Calculate total images (existing + new)\nconst totalImages=existingImages.length+images.length;const canAddMore=totalImages<maxImages&&!disabled;// Validate file using utility function\nconst validateFileLocal=useCallback(file=>{return validateFile(file,{maxSize:maxFileSize,allowedTypes:acceptedTypes});},[acceptedTypes,maxFileSize]);// Process files\nconst processFiles=useCallback(fileList=>{const files=Array.from(fileList);const newImages=[];let hasErrors=false;// Check if adding these files would exceed the limit\nif(totalImages+files.length>maxImages){setError(\"Cannot add \".concat(files.length,\" images. Maximum \").concat(maxImages,\" images allowed (\").concat(totalImages,\" already selected).\"));return;}files.forEach((file,index)=>{const validationError=validateFileLocal(file);if(validationError){hasErrors=true;setError(validationError);return;}const imageFile={id:\"\".concat(Date.now(),\"-\").concat(index),file,preview:URL.createObjectURL(file),status:'pending',progress:0};newImages.push(imageFile);});if(!hasErrors&&newImages.length>0){setError(null);setImages(prev=>[...prev,...newImages]);// Notify parent component\nconst allFiles=[...images.map(img=>img.file),...newImages.map(img=>img.file)];onImagesChange(allFiles);}},[images,totalImages,maxImages,validateFileLocal,onImagesChange]);// Handle file input change\nconst handleFileSelect=useCallback(e=>{const files=e.target.files;if(files&&files.length>0){processFiles(files);}// Reset input value to allow selecting the same file again\nif(fileInputRef.current){fileInputRef.current.value='';}},[processFiles]);// Handle drag events\nconst handleDrag=useCallback(e=>{e.preventDefault();e.stopPropagation();},[]);const handleDragIn=useCallback(e=>{e.preventDefault();e.stopPropagation();dragCounter.current++;if(e.dataTransfer.items&&e.dataTransfer.items.length>0){setDragActive(true);}},[]);const handleDragOut=useCallback(e=>{e.preventDefault();e.stopPropagation();dragCounter.current--;if(dragCounter.current===0){setDragActive(false);}},[]);const handleDrop=useCallback(e=>{e.preventDefault();e.stopPropagation();setDragActive(false);dragCounter.current=0;if(e.dataTransfer.files&&e.dataTransfer.files.length>0){processFiles(e.dataTransfer.files);}},[processFiles]);// Remove image\nconst removeImage=useCallback(imageId=>{setImages(prev=>{const updated=prev.filter(img=>img.id!==imageId);// Clean up object URL\nconst removedImage=prev.find(img=>img.id===imageId);if(removedImage){URL.revokeObjectURL(removedImage.preview);}// Notify parent component\nonImagesChange(updated.map(img=>img.file));return updated;});},[onImagesChange]);// Toggle existing image for deletion\nconst toggleExistingImageDeletion=useCallback(attachmentId=>{const isMarkedForDeletion=pendingDeletes.includes(attachmentId);if(isMarkedForDeletion){onUnmarkForDeletion===null||onUnmarkForDeletion===void 0?void 0:onUnmarkForDeletion(attachmentId);}else{onMarkForDeletion===null||onMarkForDeletion===void 0?void 0:onMarkForDeletion(attachmentId);}},[pendingDeletes,onMarkForDeletion,onUnmarkForDeletion]);// Remove existing image (fallback to pending system)\nconst removeExistingImage=useCallback(attachmentId=>{toggleExistingImageDeletion(attachmentId);},[toggleExistingImageDeletion]);// Set primary image\nconst setPrimaryImage=useCallback(attachmentId=>{if(onSetPrimary){onSetPrimary(attachmentId);}},[onSetPrimary]);// Cleanup object URLs on unmount\nuseEffect(()=>{return()=>{images.forEach(image=>{URL.revokeObjectURL(image.preview);});};},[]);// Clear error after 5 seconds\nuseEffect(()=>{if(error){const timer=setTimeout(()=>setError(null),5000);return()=>clearTimeout(timer);}},[error]);return/*#__PURE__*/_jsxs(\"div\",{className:\"multiple-image-upload \".concat(className),children:[error&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'8px',color:'#dc2626',fontSize:'0.875rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:16}),error]}),canAddMore&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',border:\"2px dashed \".concat(dragActive?'#22c55e':'#d1d5db'),borderRadius:'12px',padding:'2rem',textAlign:'center',backgroundColor:dragActive?'#f0fdf4':'#fafafa',cursor:disabled?'not-allowed':'pointer',transition:'all 0.2s ease',marginBottom:'1.5rem'},onClick:()=>{var _fileInputRef$current;return!disabled&&((_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click());},onDragEnter:handleDragIn,onDragLeave:handleDragOut,onDragOver:handleDrag,onDrop:handleDrop,children:[/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",multiple:true,accept:acceptedTypes.join(','),onChange:handleFileSelect,style:{display:'none'},disabled:disabled}),/*#__PURE__*/_jsx(Upload,{size:48,style:{color:dragActive?'#22c55e':'#9ca3af',marginBottom:'1rem'}}),/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'1.125rem',fontWeight:'600',color:'#374151',marginBottom:'0.5rem'},children:dragActive?'Drop images here':'Upload Images'}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',color:'#6b7280',marginBottom:'0.5rem'},children:\"Drag and drop images here, or click to select files\"}),/*#__PURE__*/_jsxs(\"p\",{style:{fontSize:'0.75rem',color:'#9ca3af'},children:[\"Maximum \",maxImages,\" images \\u2022 \",formatFileSize(maxFileSize),\" per file\",/*#__PURE__*/_jsx(\"br\",{}),totalImages,\"/\",maxImages,\" images selected\"]})]}),(existingImages.length>0||images.length>0)&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fill, minmax(150px, 1fr))',gap:'1rem',marginTop:'1rem'},children:[existingImages.map(image=>{const isMarkedForDeletion=pendingDeletes.includes(image.attachment_id);return/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',aspectRatio:'1',borderRadius:'8px',overflow:'hidden',border:image.is_primary?'3px solid #22c55e':'1px solid #e5e7eb',backgroundColor:'#f9fafb',opacity:isMarkedForDeletion?0.5:1,filter:isMarkedForDeletion?'grayscale(100%)':'none',transition:'opacity 0.2s ease, filter 0.2s ease'},children:[isMarkedForDeletion&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',inset:0,backgroundColor:'rgba(239, 68, 68, 0.3)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1},children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:'rgba(239, 68, 68, 0.9)',color:'white',padding:'0.5rem 1rem',borderRadius:'4px',fontSize:'0.875rem',fontWeight:'600'},children:\"Will be deleted\"})}),/*#__PURE__*/_jsx(SafeImage,{imagePath:image.file_path,alt:image.file_name,style:{width:'100%',height:'100%',objectFit:'cover'}}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'0.5rem',right:'0.5rem',display:'flex',gap:'0.25rem'},children:[!image.is_primary&&onSetPrimary&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>setPrimaryImage(image.attachment_id),style:{padding:'0.25rem',backgroundColor:'rgba(0, 0, 0, 0.7)',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:\"Set as primary\",children:/*#__PURE__*/_jsx(CheckCircle,{size:14})}),(()=>{const hasPendingProps=onMarkForDeletion&&onUnmarkForDeletion;const hasImmediateProps=onExistingImageDelete;console.log('🔍 Button render for image',image.attachment_id,{hasPendingProps,hasImmediateProps,isMarkedForDeletion});if(hasPendingProps){return/*#__PURE__*/_jsx(DeleteButton,{onClick:()=>toggleExistingImageDeletion(image.attachment_id),isMarkedForDeletion:isMarkedForDeletion,title:isMarkedForDeletion?\"Undo deletion\":\"Mark for deletion\"});}else if(hasImmediateProps){return/*#__PURE__*/_jsx(DeleteButton,{onClick:()=>removeExistingImage(image.attachment_id),title:\"Remove image\"});}return null;})()]})]},\"existing-\".concat(image.attachment_id));}),images.map(image=>/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',aspectRatio:'1',borderRadius:'8px',overflow:'hidden',border:'1px solid #e5e7eb',backgroundColor:'#f9fafb'},children:[/*#__PURE__*/_jsx(\"img\",{src:image.preview,alt:image.file.name,style:{width:'100%',height:'100%',objectFit:'cover',opacity:image.status==='uploading'?0.7:1}}),image.status==='uploading'&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',inset:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'0.875rem'},children:\"Uploading...\"}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'0.5rem',right:'0.5rem'},children:/*#__PURE__*/_jsx(DeleteButton,{onClick:()=>removeImage(image.id),title:\"Remove image\"})})]},image.id))]})]});};export default MultipleImageUpload;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}