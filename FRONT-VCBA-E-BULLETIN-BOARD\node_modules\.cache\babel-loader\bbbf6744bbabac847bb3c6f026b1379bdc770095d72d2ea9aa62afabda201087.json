{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n  key: \"vv11sd\"\n}], [\"path\", {\n  d: \"m10 15-3-3 3-3\",\n  key: \"1pgupc\"\n}], [\"path\", {\n  d: \"M7 12h7a2 2 0 0 1 2 2v1\",\n  key: \"1gheu4\"\n}]];\nconst MessageCircleReply = createLucideIcon(\"message-circle-reply\", __iconNode);\nexport { __iconNode, MessageCircleReply as default };\n//# sourceMappingURL=message-circle-reply.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}