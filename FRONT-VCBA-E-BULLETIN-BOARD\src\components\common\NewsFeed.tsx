import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
// import { announcementService } from '../../services'; // Not used in unified component
import { calendarReactionService } from '../../services/calendarReactionService';
import { adminHttpClient, studentHttpClient } from '../../services/api.service';
import { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';
import { useNotificationTarget } from '../../hooks/useNotificationNavigation';
import AdminAuthContext from '../../contexts/AdminAuthContext';
import StudentAuthContext from '../../contexts/StudentAuthContext';
import AdminCommentSection from '../admin/AdminCommentSection';
import CommentSection from '../student/CommentSection';
import NotificationBell from '../admin/NotificationBell';
import StudentNotificationBell from '../student/NotificationBell';
import FacebookImageGallery from './FacebookImageGallery';
import ImageLightbox from './ImageLightbox';
import StudentProfileSettingsModal from '../student/StudentProfileSettingsModal';
import type { AnnouncementAttachment } from '../../services/announcementService';
import type { CalendarEvent } from '../../types/calendar.types';
import { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';
import '../../styles/notificationHighlight.css';
import {
  Newspaper,
  Search,
  Pin,
  Calendar,
  MessageSquare,
  Heart,
  // Edit, // Not used in unified component
  Users,
  LayoutDashboard,
  BookOpen,
  PartyPopper,
  AlertTriangle,
  Clock,
  Trophy,
  Briefcase,
  GraduationCap,
  Flag,
  Coffee,
  Plane,
  ChevronDown,
  User,
  LogOut,
  Settings
} from 'lucide-react';

// Custom hook for CORS-safe image loading (role-aware)
const useImageLoader = (imagePath: string | null, userRole?: 'admin' | 'student') => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const currentBlobUrl = useRef<string | null>(null);

  useEffect(() => {
    // Cleanup previous blob URL if it exists
    if (currentBlobUrl.current) {
      URL.revokeObjectURL(currentBlobUrl.current);
      currentBlobUrl.current = null;
    }

    if (!imagePath) {
      setImageUrl(null);
      return;
    }

    const loadImage = async () => {
      setLoading(true);
      setError(null);

      try {
        const fullUrl = getImageUrl(imagePath);
        if (!fullUrl) {
          throw new Error('Invalid image path');
        }

        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);

        // Get the appropriate token based on user role
        const authToken = userRole === 'admin'
          ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)
          : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);

        // Fetch image as blob to bypass CORS restrictions
        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Origin': window.location.origin,
          },
          mode: 'cors',
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const objectUrl = URL.createObjectURL(blob);

        // Store the blob URL for cleanup
        currentBlobUrl.current = objectUrl;
        setImageUrl(objectUrl);

        console.log('✅ Image loaded successfully via fetch');

      } catch (err) {
        console.error('❌ Image fetch failed:', err);
        setError(err instanceof Error ? err.message : 'Failed to load image');
      } finally {
        setLoading(false);
      }
    };

    loadImage();

    // Cleanup function
    return () => {
      if (currentBlobUrl.current) {
        URL.revokeObjectURL(currentBlobUrl.current);
        currentBlobUrl.current = null;
      }
    };
  }, [imagePath, userRole]); // Fixed: removed imageUrl to prevent infinite loop

  return { imageUrl, loading, error };
};

// CORS-safe image component
interface ImageDisplayProps {
  imagePath: string | null;
  alt: string;
  style?: React.CSSProperties;
  className?: string;
  userRole?: 'admin' | 'student';
  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;
  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({
  imagePath,
  alt,
  style,
  className,
  userRole,
  onLoad,
  onMouseEnter,
  onMouseLeave
}) => {
  const { imageUrl, loading, error } = useImageLoader(imagePath, userRole);

  if (loading) {
    return (
      <div style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8fafc',
        color: '#64748b'
      }} className={className}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>
          <div style={{ fontSize: '0.875rem' }}>Loading...</div>
        </div>
      </div>
    );
  }

  if (error || !imageUrl) {
    return (
      <div style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8fafc',
        color: '#64748b',
        border: '2px dashed #cbd5e1'
      }} className={className}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>
          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>
          {error && (
            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>
              {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      style={style}
      className={className}
      onLoad={(e) => {
        console.log('✅ Image rendered successfully via CORS-safe method');
        onLoad?.(e);
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    />
  );
};

// Image Gallery Component
interface ImageGalleryProps {
  images: AnnouncementAttachment[];
  altPrefix: string;
  userRole?: 'admin' | 'student';
  onImageClick?: (index: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, userRole, onImageClick }) => {
  if (!images || images.length === 0) return null;

  const visibleImages = images.slice(0, 4);
  const remainingCount = Math.max(0, images.length - 4);

  const getContainerStyle = (index: number, total: number): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      borderRadius: '12px',
      cursor: onImageClick ? 'pointer' : 'default'
    };

    if (total === 1) {
      return { ...baseStyle, width: '100%', height: '300px' };
    } else if (total === 2) {
      return { ...baseStyle, width: '50%', height: '250px' };
    } else if (total === 3) {
      if (index === 0) {
        return { ...baseStyle, width: '50%', height: '250px' };
      } else {
        return { ...baseStyle, width: '50%', height: '120px' };
      }
    } else {
      if (index === 0) {
        return { ...baseStyle, width: '50%', height: '250px' };
      } else {
        return { ...baseStyle, width: '33.33%', height: '120px' };
      }
    }
  };

  const getImageStyle = (): React.CSSProperties => {
    return {
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const,
      transition: 'transform 0.3s ease'
    };
  };

  return (
    <div style={{
      display: 'flex',
      gap: '4px',
      width: '100%',
      marginBottom: '1rem'
    }}>
      {/* Main image or left side */}
      <div style={getContainerStyle(0, visibleImages.length)}>
        <ImageDisplay
          imagePath={visibleImages[0].file_path}
          alt={`${altPrefix} - Image 1`}
          style={getImageStyle()}
          userRole={userRole}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.02)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
        />
        {onImageClick && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              cursor: 'pointer'
            }}
            onClick={() => onImageClick(0)}
          />
        )}
      </div>

      {/* Right side images */}
      {visibleImages.length > 1 && (
        <div style={{
          display: 'flex',
          flexDirection: visibleImages.length === 2 ? 'row' : 'column',
          gap: '4px',
          width: visibleImages.length === 2 ? '50%' : '50%'
        }}>
          {visibleImages.slice(1).map((image, idx) => {
            const actualIndex = idx + 1;
            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;
            
            return (
              <div
                key={actualIndex}
                style={{
                  ...getContainerStyle(actualIndex, visibleImages.length),
                  position: 'relative'
                }}
              >
                <ImageDisplay
                  imagePath={image.file_path}
                  alt={`${altPrefix} - Image ${actualIndex + 1}`}
                  style={getImageStyle()}
                  userRole={userRole}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
                
                {/* Overlay for remaining images count */}
                {isLast && remainingCount > 0 && (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem',
                    fontWeight: '600'
                  }}>
                    +{remainingCount}
                  </div>
                )}
                
                {onImageClick && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      cursor: 'pointer'
                    }}
                    onClick={() => onImageClick(actualIndex)}
                  />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Props interface for the unified NewsFeed component
interface NewsFeedProps {
  userRole?: 'admin' | 'student'; // Optional prop to override role detection
}

// Main unified NewsFeed Component
const NewsFeed: React.FC<NewsFeedProps> = ({ userRole }) => {
  const navigate = useNavigate();

  // Safely get authentication contexts for both roles (must be called before any conditional logic)
  const adminAuth = useContext(AdminAuthContext);
  const studentAuth = useContext(StudentAuthContext);

  // Determine current user role and context
  const currentRole = userRole ||
    (adminAuth?.isAuthenticated ? 'admin' :
     studentAuth?.isAuthenticated ? 'student' : null);

  const currentUser = currentRole === 'admin' ? adminAuth?.user : studentAuth?.user;
  const currentLogout = currentRole === 'admin' ? adminAuth?.logout : studentAuth?.logout;

  // All hooks must be called before any early returns
  const { categories } = useCategories();

  // Use the announcements hook for proper state management with role-based service
  const {
    announcements,
    loading,
    error,
    likeAnnouncement,
    unlikeAnnouncement,
    refresh: refreshAnnouncements
  } = useAnnouncements({
    status: 'published',
    page: 1,
    limit: 50,
    sort_by: 'created_at',
    sort_order: 'DESC'
  }, currentRole === 'admin'); // true for admin service, false for student service

  // Handle notification-triggered navigation
  const { isFromNotification, scrollTarget } = useNotificationTarget();

  // Filter states
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  // UI states
  const [showComments, setShowComments] = useState<number | null>(null);
  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);
  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showProfileSettings, setShowProfileSettings] = useState(false);

  // Lightbox states
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxImages, setLightboxImages] = useState<string[]>([]);
  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);

  // Data states
  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [calendarError, setCalendarError] = useState<string | undefined>();
  // Note: recentStudents and studentLoading state can be added later if needed

  // Fetch calendar events function
  const fetchCalendarEvents = useCallback(async () => {
    try {
      setCalendarLoading(true);
      setCalendarError(undefined);

      const authToken = currentRole === 'admin'
        ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)
        : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);

      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.success && data.data) {
        const eventsData = data.data.events || data.data || [];

        // Fetch images for each event
        const eventsWithImages = await Promise.all(
          eventsData.map(async (event: any) => {
            try {
              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {
                headers: {
                  'Authorization': `Bearer ${authToken}`,
                  'Content-Type': 'application/json'
                }
              });
              const imageData = await imageResponse.json();

              if (imageData.success && imageData.data) {
                event.images = imageData.data.attachments || [];
              } else {
                event.images = [];
              }
            } catch (imgErr) {
              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);
              event.images = [];
            }
            return event;
          })
        );

        setCalendarEvents(eventsWithImages);
      } else {
        setCalendarError('Failed to load calendar events');
      }
    } catch (err: any) {
      console.error('Error fetching calendar events:', err);
      setCalendarError(err.message || 'Failed to load calendar events');
    } finally {
      setCalendarLoading(false);
    }
  }, [currentRole]);

  // Update pinned announcements when announcements change
  useEffect(() => {
    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);
    setPinnedAnnouncements(pinned);
  }, [announcements]);

  // Initial data fetch
  useEffect(() => {
    fetchCalendarEvents();
    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard
  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserDropdown) {
        const target = event.target as Element;
        if (!target.closest('[data-dropdown="user-dropdown"]')) {
          setShowUserDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserDropdown]);

  // Early return if no valid role is detected (after all hooks)
  if (!currentRole) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'
      }}>
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '16px',
          border: '1px solid #e5e7eb',
          textAlign: 'center',
          maxWidth: '400px'
        }}>
          <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>Authentication Required</h2>
          <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>
            Please log in to access the newsfeed.
          </p>
          <button
            onClick={() => navigate('/')}
            style={{
              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Open lightbox function for announcements
  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {
    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];
    setLightboxImages(imageUrls);
    setLightboxInitialIndex(initialIndex);
    setLightboxOpen(true);
  };

  // Open lightbox function for image URLs (calendar events)
  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {
    setLightboxImages(imageUrls);
    setLightboxInitialIndex(initialIndex);
    setLightboxOpen(true);
  };

  // Category styling function
  const getCategoryStyle = (categoryName: string) => {
    const styles = {
      'ACADEMIC': {
        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
        icon: BookOpen
      },
      'GENERAL': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Users
      },
      'EVENTS': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: PartyPopper
      },
      'EMERGENCY': {
        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
        icon: AlertTriangle
      },
      'SPORTS': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Trophy
      },
      'DEADLINES': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Clock
      }
    };

    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];
  };

  // Holiday type styling function
  const getHolidayTypeStyle = (holidayTypeName: string) => {
    const styles = {
      'National Holiday': {
        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
        icon: Flag
      },
      'School Event': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: GraduationCap
      },
      'Academic Break': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Coffee
      },
      'Sports Event': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Trophy
      },
      'Field Trip': {
        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
        icon: Plane
      },
      'Meeting': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Briefcase
      }
    };

    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];
  };

  // Note: Duplicate useEffect and function removed - already defined above

  // Handle like/unlike functionality (role-aware)
  const handleLikeToggle = async (announcement: any) => {
    try {
      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);
      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);
      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });

      if (announcement.user_reaction) {
        // Unlike the announcement
        console.log(`[DEBUG] ${currentRole} removing reaction...`);
        await unlikeAnnouncement(announcement.announcement_id);
      } else {
        // Like the announcement
        console.log(`[DEBUG] ${currentRole} adding reaction...`);
        await likeAnnouncement(announcement.announcement_id, 1);
      }

      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);
    } catch (error) {
      console.error(`[ERROR] ${currentRole} error toggling like:`, error);
    }
  };

  // Role-aware calendar reaction function
  const toggleCalendarReaction = async (eventId: number, currentlyLiked: boolean) => {
    const client = currentRole === 'admin' ? adminHttpClient : studentHttpClient;
    const endpoint = `/api/calendar/${eventId}/like`;

    console.log(`[DEBUG] ${currentRole} making direct API call to:`, endpoint);
    console.log(`[DEBUG] Using ${currentRole} HTTP client`);

    if (currentlyLiked) {
      // Unlike the event
      return await client.delete(endpoint);
    } else {
      // Like the event
      return await client.post(endpoint, {});
    }
  };

  // Handle calendar event like/unlike functionality
  const handleCalendarLikeToggle = async (event: any) => {
    try {
      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);
      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);
      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });

      const response = await toggleCalendarReaction(event.calendar_id, event.user_has_reacted || false);

      if (response.success) {
        const newReactionState = !event.user_has_reacted;
        const newReactionCount = event.user_has_reacted
          ? Math.max(0, (event.reaction_count || 0) - 1)
          : (event.reaction_count || 0) + 1;

        console.log(`[DEBUG] ${currentRole} updating local state:`, {
          eventId: event.calendar_id,
          oldReactionState: event.user_has_reacted,
          newReactionState,
          oldCount: event.reaction_count,
          newCount: newReactionCount,
          userId: currentUser?.id,
          userRole: currentRole
        });

        // Update the local state
        setCalendarEvents(prevEvents =>
          prevEvents.map(e =>
            e.calendar_id === event.calendar_id
              ? {
                  ...e,
                  user_has_reacted: newReactionState,
                  reaction_count: newReactionCount
                }
              : e
          )
        );
        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);
      }
    } catch (error) {
      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      if (currentLogout) {
        await currentLogout();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Force redirect even if logout fails
      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';
      window.location.href = redirectPath;
    }
  };

  // Note: Duplicate useEffect for dropdown removed - already defined above



  // Filter announcements
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = !searchTerm ||
      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = !filterCategory ||
      announcement.category_id?.toString() === filterCategory;

    const matchesGradeLevel = !filterGradeLevel ||
      announcement.grade_level?.toString() === filterGradeLevel;

    return matchesSearch && matchesCategory && matchesGradeLevel;
  });

  // Filter calendar events with date-based filtering
  const filteredCalendarEvents = calendarEvents.filter(event => {
    const matchesSearch = !searchTerm ||
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Show events that are currently active (between start and end date)
    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)
    const today = new Date();
    const todayDateString = today.getFullYear() + '-' +
      String(today.getMonth() + 1).padStart(2, '0') + '-' +
      String(today.getDate()).padStart(2, '0');

    const eventStartDate = new Date(event.event_date);
    const eventStartDateString = eventStartDate.getFullYear() + '-' +
      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +
      String(eventStartDate.getDate()).padStart(2, '0');

    // If event has an end date, use it; otherwise, show for the event date only
    const eventEndDateString = event.end_date ? (() => {
      const endDate = new Date(event.end_date);
      return endDate.getFullYear() + '-' +
        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +
        String(endDate.getDate()).padStart(2, '0');
    })() : eventStartDateString;

    // Event is active if today is between start and end date (inclusive)
    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;

    // For admins, show both published and unpublished events (but only currently active events)
    const isActive = (event as any).is_active !== 0;

    return matchesSearch && isEventActive && isActive;
  });

  // Combine and sort all content by date (most recent first)
  const displayAnnouncements = filteredAnnouncements;
  const displayEvents = filteredCalendarEvents;



  // Create combined content array for better chronological display (for future use)
  // const combinedContent = [
  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),
  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))
  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)
        `,
        pointerEvents: 'none'
      }} />

      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Modern Admin Header */}
        <header style={{
          background: 'white',
          borderBottom: '1px solid #e5e7eb',
          position: 'sticky',
          top: 0,
          zIndex: 100,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            padding: '0 2rem',
            height: '72px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            {/* Left Section: Logo + Page Title */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1.5rem',
              minWidth: '300px'
            }}>
              <img
                src="/logo/vcba1.png"
                alt="VCBA Logo"
                style={{
                  width: '48px',
                  height: '48px',
                  objectFit: 'contain'
                }}
              />
              <div>
                <h1 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#111827',
                  lineHeight: '1.2'
                }}>
                  VCBA E-Bulletin Board
                </h1>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  lineHeight: '1.2'
                }}>
                 {/* ill comment this for now and uncomment it soon */}
                  {/* {currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'} */}
                </p>
              </div>
            </div>

            {/* Center Section: Search */}
            <div style={{
              flex: 1,
              maxWidth: '500px',
              margin: '0 2rem'
            }}>
              <div style={{ position: 'relative' }}>
                <Search
                  size={20}
                  style={{
                    position: 'absolute',
                    left: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#9ca3af'
                  }}
                />
                <input
                  type="text"
                  placeholder="Search post"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    height: '44px',
                    padding: '0 1rem 0 3rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '12px',
                    background: '#f9fafb',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'all 0.2s ease'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#22c55e';
                    e.currentTarget.style.background = 'white';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.background = '#f9fafb';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            {/* Right Section: Navigation + Filters */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              minWidth: '400px',
              justifyContent: 'flex-end'
            }}>
              
              {/* Filters Group */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem',
                background: '#f9fafb',
                borderRadius: '12px',
                border: '1px solid #e5e7eb'
              }}>
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '110px'
                  }}
                >
                  <option value="">All Categories</option>
                  {categories
                    .filter(category =>
                      // Hide holiday categories from dropdown
                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)
                    )
                    .map(category => (
                      <option key={category.category_id} value={category.category_id.toString()}>
                        {category.name}
                      </option>
                    ))
                  }
                </select>

                <select
                  value={filterGradeLevel}
                  onChange={(e) => setFilterGradeLevel(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '100px'
                  }}
                >
                  <option value="">All Grades</option>
                  <option value="11">Grade 11</option>
                  <option value="12">Grade 12</option>
                </select>

                {(searchTerm || filterCategory || filterGradeLevel) && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setFilterCategory('');
                      setFilterGradeLevel('');
                    }}
                    style={{
                      padding: '0.5rem 0.75rem',
                      border: 'none',
                      borderRadius: '8px',
                      background: '#ef4444',
                      color: 'white',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#dc2626';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = '#ef4444';
                    }}
                  >
                    Clear
                  </button>
                )}
              </div>

              {/* Right Side Actions */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem'
              }}>
                {/* Notification Bell - Role-aware */}
                {currentRole === 'admin' ? <NotificationBell /> : <StudentNotificationBell />}

                {/* User Dropdown */}
                <div style={{ position: 'relative' }} data-dropdown="user-dropdown">
                  <button
                    onClick={() => setShowUserDropdown(!showUserDropdown)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.75rem 1rem',
                      background: 'white',
                      border: '1px solid #d1d5db',
                      borderRadius: '12px',
                      color: '#374151',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#22c55e';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    }}
                  >
                    {/* Profile Picture */}
                    {currentUser?.profilePicture ? (
                      <img
                        src={getImageUrl(currentUser.profilePicture) || ''}
                        alt={`${currentUser.firstName} ${currentUser.lastName}`}
                        style={{
                          width: '24px',
                          height: '24px',
                          borderRadius: '50%',
                          objectFit: 'cover',
                          border: '1px solid #e5e7eb'
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent) {
                            const userIcon = parent.querySelector('.user-icon');
                            if (userIcon) {
                              (userIcon as HTMLElement).style.display = 'block';
                            }
                          }
                        }}
                      />
                    ) : null}
                    <User
                      size={16}
                      className="user-icon"
                      style={{
                        display: currentUser?.profilePicture ? 'none' : 'block'
                      }}
                    />
                    <span>{currentUser?.firstName || (currentRole === 'admin' ? 'Admin' : 'Student')}</span>
                    <ChevronDown size={14} style={{
                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s ease'
                    }} />
                  </button>

                  {/* Dropdown Menu */}
                  {showUserDropdown && (
                    <div style={{
                      position: 'absolute',
                      top: '100%',
                      right: 0,
                      marginTop: '0.5rem',
                      background: 'white',
                      border: '1px solid #e5e7eb',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
                      minWidth: '200px',
                      zIndex: 1000,
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        padding: '0.75rem 1rem',
                        borderBottom: '1px solid #f3f4f6',
                        background: '#f9fafb'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.75rem',
                          marginBottom: '0.5rem'
                        }}>
                          {/* Profile Picture */}
                          {currentUser?.profilePicture ? (
                            <img
                              src={getImageUrl(currentUser.profilePicture) || ''}
                              alt={`${currentUser.firstName} ${currentUser.lastName}`}
                              style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '50%',
                                objectFit: 'cover',
                                border: '2px solid #e5e7eb',
                                flexShrink: 0
                              }}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  parent.innerHTML = `
                                    <div style="
                                      width: 40px;
                                      height: 40px;
                                      border-radius: 50%;
                                      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                                      display: flex;
                                      align-items: center;
                                      justify-content: center;
                                      color: white;
                                      font-weight: 600;
                                      font-size: 1rem;
                                      flex-shrink: 0;
                                    ">
                                      ${currentUser?.firstName?.charAt(0) || ''}${currentUser?.lastName?.charAt(0) || ''}
                                    </div>
                                  `;
                                }
                              }}
                            />
                          ) : (
                            <div style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '50%',
                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              fontWeight: '600',
                              fontSize: '1rem',
                              flexShrink: 0
                            }}>
                              {currentUser?.firstName?.charAt(0) || ''}{currentUser?.lastName?.charAt(0) || ''}
                            </div>
                          )}

                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontSize: '0.875rem',
                              fontWeight: '600',
                              color: '#111827'
                            }}>
                              {currentUser?.firstName} {currentUser?.lastName}
                            </div>
                            <div style={{
                              fontSize: '0.75rem',
                              color: '#6b7280'
                            }}>
                              {currentUser?.email}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div style={{ padding: '0.5rem 0' }}>
                        {/* Role-based menu items */}
                        {currentRole === 'admin' ? (
                          // Admin: Show Dashboard button
                          <button
                            onClick={() => {
                              navigate('/admin/dashboard');
                              setShowUserDropdown(false);
                            }}
                            style={{
                              width: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.75rem',
                              padding: '0.75rem 1rem',
                              background: 'transparent',
                              border: 'none',
                              color: '#374151',
                              fontSize: '0.875rem',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = '#f3f4f6';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'transparent';
                            }}
                          >
                            <LayoutDashboard size={16} />
                            Dashboard
                          </button>
                        ) : (
                          // Student: Show Profile Settings button
                          <button
                            onClick={() => {
                              setShowProfileSettings(true);
                              setShowUserDropdown(false);
                            }}
                            style={{
                              width: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.75rem',
                              padding: '0.75rem 1rem',
                              background: 'transparent',
                              border: 'none',
                              color: '#374151',
                              fontSize: '0.875rem',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = '#f3f4f6';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'transparent';
                            }}
                          >
                            <Settings size={16} />
                            Profile Settings
                          </button>
                        )}

                        <button
                          onClick={() => {
                            handleLogout();
                            setShowUserDropdown(false);
                          }}
                          style={{
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            padding: '0.75rem 1rem',
                            background: 'transparent',
                            border: 'none',
                            color: '#ef4444',
                            fontSize: '0.875rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = '#fef2f2';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = 'transparent';
                          }}
                        >
                          <LogOut size={16} />
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>



        {/* Main Content Layout */}
        <div style={{
          padding: '2rem',
          display: 'flex',
          gap: '2rem',
          alignItems: 'flex-start'
        }}>
          {/* Left Sidebar: Pinned Posts */}
          <div style={{
            width: '320px',
            flexShrink: 0
          }}>
            <div style={{
              background: 'white',
              borderRadius: '16px',
              border: '1px solid #e5e7eb',
              overflow: 'hidden',
              position: 'sticky',
              top: '100px'
            }}>
              {/* Pinned Posts Header */}
              <div style={{
                padding: '1.5rem 1.5rem 1rem',
                borderBottom: '1px solid #f3f4f6'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '0.5rem'
                }}>
                  <Pin size={20} style={{ color: '#22c55e' }} />
                  <h3 style={{
                    margin: 0,
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827'
                  }}>
                    Pinned Posts
                  </h3>
                </div>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280'
                }}>
                  Important announcements and updates
                </p>
              </div>

              {/* Pinned Posts List */}
              <div style={{ padding: '1rem' }}>
                {pinnedAnnouncements.length > 0 ? (
                  <>
                    {pinnedAnnouncements.slice(0, 3).map((announcement) => {
                      // Handle alert announcements with special styling
                      const isAlert = announcement.is_alert;
                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                      const categoryStyle = getCategoryStyle(categoryName);

                      return (
                        <div
                          key={announcement.announcement_id}
                          style={{
                            padding: '1rem',
                            background: isAlert ? '#fef2f2' : '#f8fafc',
                            borderRadius: '12px',
                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',
                            marginBottom: '1rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';
                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';
                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';
                          }}
                          onClick={() => setSelectedPinnedPost(announcement)}
                        >
                          <div style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: '0.75rem'
                          }}>
                            <div style={{
                              width: '8px',
                              height: '8px',
                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),
                              borderRadius: '50%',
                              marginTop: '0.5rem',
                              flexShrink: 0
                            }} />
                            <div style={{ flex: 1 }}>
                              <h4 style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.875rem',
                                fontWeight: '600',
                                color: '#111827',
                                lineHeight: '1.4'
                              }}>
                                {announcement.title}
                              </h4>
                              <p style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.8rem',
                                color: '#6b7280',
                                lineHeight: '1.4'
                              }}>
                                {announcement.content.length > 80
                                  ? `${announcement.content.substring(0, 80)}...`
                                  : announcement.content}
                              </p>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                fontSize: '0.75rem',
                                color: '#9ca3af'
                              }}>
                                <Calendar size={12} />
                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {pinnedAnnouncements.length > 3 && (
                      <button style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        background: 'white',
                        color: '#22c55e',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = '#f0fdf4';
                        e.currentTarget.style.borderColor = '#22c55e';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'white';
                        e.currentTarget.style.borderColor = '#e5e7eb';
                      }}>
                        View All {pinnedAnnouncements.length} Pinned Posts
                      </button>
                    )}
                  </>
                ) : (
                  <div style={{
                    padding: '2rem 1rem',
                    textAlign: 'center',
                    color: '#6b7280'
                  }}>
                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />
                    <p style={{ margin: 0, fontSize: '0.875rem' }}>
                      No pinned posts available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Content: Main Feed */}
          <div style={{ flex: 1, minWidth: 0 }}>
          {/* Loading State */}
          {(loading || calendarLoading) && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '400px'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  border: '4px solid rgba(34, 197, 94, 0.2)',
                  borderTop: '4px solid #22c55e',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{
                  color: '#6b7280',
                  fontSize: '1rem',
                  fontWeight: '500'
                }}>
                  Loading content...
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {(error || calendarError) && !loading && !calendarLoading && (
            <div style={{
              padding: '2rem',
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              borderRadius: '16px',
              textAlign: 'center'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'rgba(239, 68, 68, 0.1)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1rem'
              }}>
                <MessageSquare size={24} color="#ef4444" />
              </div>
              <h3 style={{
                color: '#ef4444',
                margin: '0 0 0.5rem 0',
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                Error Loading Content
              </h3>
              <p style={{
                color: '#6b7280',
                margin: '0 0 1.5rem 0',
                fontSize: '1rem'
              }}>
                {error || calendarError}
              </p>
              <button
                onClick={() => {
                  refreshAnnouncements();
                  fetchCalendarEvents();
                }}
                style={{
                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Try Again
              </button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !calendarLoading && !error && !calendarError &&
           displayAnnouncements.length === 0 && displayEvents.length === 0 && (
            <div style={{
              padding: '4rem 2rem',
              textAlign: 'center'
            }}>
              <div style={{
                width: '5rem',
                height: '5rem',
                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 2rem'
              }}>
                <Newspaper size={32} color="white" />
              </div>
              <h3 style={{
                color: '#374151',
                margin: '0 0 1rem 0',
                fontSize: '1.5rem',
                fontWeight: '600'
              }}>
                No Content Available
              </h3>
              <p style={{
                color: '#6b7280',
                margin: '0 0 2rem 0',
                fontSize: '1rem',
                lineHeight: '1.6',
                maxWidth: '500px',
                marginLeft: 'auto',
                marginRight: 'auto'
              }}>
                {searchTerm || filterCategory || filterGradeLevel
                  ? 'No content matches your current filters. Try adjusting your search criteria.'
                  : 'There are no published announcements or events at the moment. Check back later for updates.'
                }
              </p>
              {(searchTerm || filterCategory || filterGradeLevel) && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterCategory('');
                    setFilterGradeLevel('');
                  }}
                  style={{
                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    padding: '0.75rem 1.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  Clear Filters
                </button>
              )}
            </div>
          )}

          {/* Recent Students Section (Admin Only) - Commented out for now */}
          {/* Future feature: Recent student registrations for admin dashboard */}

          {/* Content Feed */}
          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem'
            }}>
              {/* Calendar Events */}
              {displayEvents.length > 0 && (
                <>
                  {displayEvents.map(event => (
                    <div
                      key={`event-${event.calendar_id}`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Event Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          width: '48px',
                          height: '48px',
                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                          borderRadius: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          <Calendar size={24} color="white" />
                        </div>

                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            marginBottom: '0.5rem'
                          }}>
                            {(() => {
                              const holidayTypeName = event.category_name || 'School Event';
                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);
                              const IconComponent = holidayStyle.icon;

                              return (
                                <span style={{
                                  background: holidayStyle.background,
                                  color: 'white',
                                  fontSize: '0.75rem',
                                  fontWeight: '600',
                                  padding: '0.25rem 0.75rem',
                                  borderRadius: '20px',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.25rem'
                                }}>
                                  <IconComponent size={12} color="white" />
                                  {holidayTypeName}
                                </span>
                              );
                            })()}

                            {/* Author Information - Show for all users */}
                            {event.created_by_name && (
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                background: 'rgba(59, 130, 246, 0.1)',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '20px',
                                fontSize: '0.75rem',
                                color: '#3b82f6'
                              }}>
                                {event.created_by_picture ? (
                                  <img
                                    src={getImageUrl(event.created_by_picture) || ''}
                                    alt={event.created_by_name}
                                    style={{
                                      width: '16px',
                                      height: '16px',
                                      borderRadius: '50%',
                                      objectFit: 'cover'
                                    }}
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                    }}
                                  />
                                ) : (
                                  <User size={12} />
                                )}
                                <span style={{ fontWeight: '500' }}>
                                  {event.created_by_name}
                                </span>
                              </div>
                            )}

                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              color: '#6b7280',
                              fontSize: '0.875rem'
                            }}>
                              <Calendar size={14} />
                              {new Date(event.event_date).toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </div>
                          </div>

                          <h3 style={{
                            margin: '0 0 0.5rem 0',
                            fontSize: '1.25rem',
                            fontWeight: '700',
                            color: '#1f2937',
                            lineHeight: '1.3'
                          }}>
                            {event.title}
                          </h3>
                        </div>
                      </div>

                      {/* Event Images */}
                      {(() => {
                        // Get event images if they exist
                        const eventImageUrls: string[] = [];

                        if ((event as any).images && (event as any).images.length > 0) {
                          (event as any).images.forEach((img: any) => {
                            if (img.file_path) {
                              // Convert file_path to full URL
                              const imageUrl = getImageUrl(img.file_path);
                              if (imageUrl) {
                                eventImageUrls.push(imageUrl);
                              }
                            }
                          });
                        }

                        return eventImageUrls.length > 0 ? (
                          <div style={{ marginBottom: '1rem' }}>
                            <FacebookImageGallery
                              images={eventImageUrls.filter(Boolean) as string[]}
                              altPrefix={event.title}
                              maxVisible={4}
                              onImageClick={(index) => {
                                const filteredImages = eventImageUrls.filter(Boolean) as string[];
                                openLightboxWithUrls(filteredImages, index);
                              }}
                            />
                          </div>
                        ) : null;
                      })()}

                      {/* Event Content */}
                      {event.description && (
                        <div style={{
                          color: '#4b5563',
                          fontSize: '0.95rem',
                          lineHeight: '1.6',
                          marginBottom: '1rem'
                        }}>
                          {event.description}
                        </div>
                      )}

                      {/* Event Details */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1.5rem',
                        padding: '1rem',
                        background: 'rgba(59, 130, 246, 0.05)',
                        borderRadius: '12px',
                        fontSize: '0.875rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          color: '#6b7280'
                        }}>
                          <Calendar size={16} />
                          <span>
                            {event.end_date && event.end_date !== event.event_date
                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`
                              : new Date(event.event_date).toLocaleDateString()
                            }
                          </span>
                        </div>

                        {event.category_name && (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            color: '#6b7280'
                          }}>
                            <span style={{
                              padding: '0.25rem 0.5rem',
                              background: 'rgba(59, 130, 246, 0.1)',
                              borderRadius: '6px',
                              fontSize: '0.75rem',
                              fontWeight: '500'
                            }}>
                              {event.category_name}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Calendar Event Interaction Section */}
                      <div style={{
                        marginTop: '1rem',
                        paddingTop: '1rem',
                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem'
                      }}>
                        {/* Like Button */}
                        <button
                          onClick={() => handleCalendarLikeToggle(event)}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            background: 'none',
                            border: 'none',
                            color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',
                            cursor: 'pointer',
                            padding: '0.5rem',
                            borderRadius: '8px',
                            transition: 'all 0.2s ease',
                            fontSize: '0.875rem',
                            fontWeight: '500'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = 'none';
                          }}
                        >
                          <Heart
                            size={18}
                            fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}
                          />
                          <span>{(event as any).reaction_count || 0}</span>
                        </button>

                        {/* Comments Button */}
                        {(event as any).allow_comments && (
                          <button
                            onClick={() => setShowCalendarComments(
                              showCalendarComments === event.calendar_id ? null : event.calendar_id
                            )}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              background: 'none',
                              border: 'none',
                              color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',
                              cursor: 'pointer',
                              padding: '0.5rem',
                              borderRadius: '8px',
                              transition: 'all 0.2s ease',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'none';
                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';
                            }}
                          >
                            <MessageSquare size={18} />
                            <span>{(event as any).comment_count || 0}</span>
                          </button>
                        )}
                      </div>

                      {/* Calendar Event Comments Section */}
                      {showCalendarComments === event.calendar_id && (event as any).allow_comments && (
                        <div style={{
                          marginTop: '1rem',
                          paddingTop: '1rem',
                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                        }}>
                          {currentRole === 'admin' ? (
                            <AdminCommentSection
                              calendarId={event.calendar_id}
                              allowComments={(event as any).allow_comments}
                              currentUserId={currentUser?.id}
                              currentUserType="admin"
                            />
                          ) : (
                            <CommentSection
                              calendarId={event.calendar_id}
                              allowComments={(event as any).allow_comments}
                              currentUserId={currentUser?.id}
                              currentUserType="student"
                            />
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}

              {/* Announcements */}
              {displayAnnouncements.length > 0 && (
                <>
                  {displayAnnouncements.map(announcement => (
                    <div
                      key={`announcement-${announcement.announcement_id}`}
                      id={`announcement-${announcement.announcement_id}`}
                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: announcement.is_pinned
                          ? '2px solid rgba(250, 204, 21, 0.3)'
                          : '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: announcement.is_pinned
                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = announcement.is_pinned
                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'
                          : '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = announcement.is_pinned
                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Pinned Badge */}
                      {announcement.is_pinned && (
                        <div style={{
                          position: 'absolute',
                          top: '-8px',
                          right: '1rem',
                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',
                          color: 'white',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '12px',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.25rem',
                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'
                        }}>
                          <Pin size={12} />
                          Pinned
                        </div>
                      )}

                      {/* Announcement Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        {(() => {
                          if (announcement.is_alert) {
                            return (
                              <div style={{
                                width: '48px',
                                height: '48px',
                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <AlertTriangle size={24} color="white" />
                              </div>
                            );
                          } else {
                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                            const categoryStyle = getCategoryStyle(categoryName);
                            const IconComponent = categoryStyle.icon;

                            return (
                              <div style={{
                                width: '48px',
                                height: '48px',
                                background: categoryStyle.background,
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <IconComponent size={24} color="white" />
                              </div>
                            );
                          }
                        })()}

                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            marginBottom: '0.5rem',
                            flexWrap: 'wrap'
                          }}>
                            {(() => {
                              if (announcement.is_alert) {
                                return (
                                  <span style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                    color: 'white',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '20px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <AlertTriangle size={12} color="white" />
                                    Alert
                                  </span>
                                );
                              } else {
                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                                const categoryStyle = getCategoryStyle(categoryName);
                                const IconComponent = categoryStyle.icon;

                                return (
                                  <span style={{
                                    background: categoryStyle.background,
                                    color: 'white',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '20px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <IconComponent size={12} color="white" />
                                    {categoryName}
                                  </span>
                                );
                              }
                            })()}

                            {announcement.grade_level && (
                              <span style={{
                                background: 'rgba(59, 130, 246, 0.1)',
                                color: '#3b82f6',
                                fontSize: '0.75rem',
                                fontWeight: '500',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '20px'
                              }}>
                                Grade {announcement.grade_level}
                              </span>
                            )}

                            {/* Author Information - Show for all users */}
                            {announcement.author_name && (
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                background: 'rgba(34, 197, 94, 0.1)',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '20px',
                                fontSize: '0.75rem',
                                color: '#16a34a'
                              }}>
                                {announcement.author_picture ? (
                                  <img
                                    src={getImageUrl(announcement.author_picture) || ''}
                                    alt={announcement.author_name}
                                    style={{
                                      width: '16px',
                                      height: '16px',
                                      borderRadius: '50%',
                                      objectFit: 'cover'
                                    }}
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                    }}
                                  />
                                ) : (
                                  <User size={12} />
                                )}
                                <span style={{ fontWeight: '500' }}>
                                  {announcement.author_name}
                                </span>
                              </div>
                            )}

                            <div style={{
                              color: '#6b7280',
                              fontSize: '0.875rem'
                            }}>
                              {new Date(announcement.created_at).toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </div>
                          </div>

                          <h3 style={{
                            margin: '0 0 0.5rem 0',
                            fontSize: '1.25rem',
                            fontWeight: '700',
                            color: '#1f2937',
                            lineHeight: '1.3'
                          }}>
                            {announcement.title}
                          </h3>
                        </div>
                      </div>

                      {/* Images */}
                      {announcement.attachments && announcement.attachments.length > 0 && (
                        <ImageGallery
                          images={announcement.attachments}
                          altPrefix={announcement.title}
                          userRole={currentRole}
                          onImageClick={(index) => {
                            openLightbox(announcement.attachments || [], index);
                          }}
                        />
                      )}

                      {/* Announcement Content */}
                      <div style={{
                        color: '#4b5563',
                        fontSize: '0.95rem',
                        lineHeight: '1.6',
                        marginBottom: '1rem'
                      }}>
                        {announcement.content}
                      </div>

                      {/* Announcement Stats & Actions */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '1rem',
                        background: 'rgba(0, 0, 0, 0.02)',
                        borderRadius: '12px',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1.5rem'
                        }}>
                          {/* Like Button */}
                          <button
                            onClick={() => handleLikeToggle(announcement)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              background: 'none',
                              border: 'none',
                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',
                              cursor: 'pointer',
                              padding: '0.5rem',
                              borderRadius: '8px',
                              transition: 'all 0.2s ease',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'none';
                            }}
                          >
                            <Heart
                              size={18}
                              fill={announcement.user_reaction ? '#ef4444' : 'none'}
                            />
                            <span>{announcement.reaction_count || 0}</span>
                          </button>

                          {/* Comments Button */}
                          {announcement.allow_comments && (
                            <button
                              onClick={() => setShowComments(
                                showComments === announcement.announcement_id ? null : announcement.announcement_id
                              )}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                background: 'none',
                                border: 'none',
                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',
                                cursor: 'pointer',
                                padding: '0.5rem',
                                borderRadius: '8px',
                                transition: 'all 0.2s ease',
                                fontSize: '0.875rem',
                                fontWeight: '500'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                                e.currentTarget.style.color = '#22c55e';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.background = 'none';
                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';
                              }}
                            >
                              <MessageSquare size={18} />
                              <span>{announcement.comment_count || 0}</span>
                            </button>
                          )}


                        </div>

                        {/* Admin Stats */}
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          fontSize: '0.75rem',
                          color: '#6b7280'
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem'
                          }}>
                            <Users size={14} />
                            <span>Posted by {(announcement as any).posted_by_name || announcement.author_name || 'Admin'}</span>
                          </div>

                          <div style={{
                            padding: '0.25rem 0.5rem',
                            background: announcement.status === 'published'
                              ? 'rgba(34, 197, 94, 0.1)'
                              : 'rgba(107, 114, 128, 0.1)',
                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',
                            borderRadius: '6px',
                            fontWeight: '500'
                          }}>
                            {announcement.status}
                          </div>
                        </div>
                      </div>

                      {/* Comments Section */}
                      {showComments === announcement.announcement_id && announcement.allow_comments && (
                        <div style={{
                          marginTop: '1rem',
                          paddingTop: '1rem',
                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                        }}>
                          {currentRole === 'admin' ? (
                            <AdminCommentSection
                              announcementId={announcement.announcement_id}
                              allowComments={announcement.allow_comments}
                              currentUserId={currentUser?.id}
                              currentUserType="admin"
                            />
                          ) : (
                            <CommentSection
                              announcementId={announcement.announcement_id}
                              allowComments={announcement.allow_comments}
                              currentUserId={currentUser?.id}
                              currentUserType="student"
                            />
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Pinned Post Dialog */}
      {selectedPinnedPost && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}
        onClick={() => setSelectedPinnedPost(null)}
        >
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}
          onClick={(e) => e.stopPropagation()}
          >
            {/* Dialog Header */}
            <div style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e5e7eb',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <Pin size={20} style={{ color: '#22c55e' }} />
                <h3 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#111827'
                }}>
                  Pinned Post
                </h3>
              </div>
              <button
                onClick={() => setSelectedPinnedPost(null)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  color: '#6b7280',
                  cursor: 'pointer',
                  padding: '0.25rem',
                  borderRadius: '4px',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6b7280';
                }}
              >
                ×
              </button>
            </div>

            {/* Dialog Content */}
            <div style={{ padding: '1.5rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                marginBottom: '1rem'
              }}>
                {(() => {
                  if (selectedPinnedPost.is_alert) {
                    return (
                      <span style={{
                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '20px',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                      }}>
                        <AlertTriangle size={12} color="white" />
                        Alert
                      </span>
                    );
                  } else {
                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();
                    const categoryStyle = getCategoryStyle(categoryName);
                    const IconComponent = categoryStyle.icon;

                    return (
                      <span style={{
                        background: categoryStyle.background,
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '20px',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                      }}>
                        <IconComponent size={12} color="white" />
                        {categoryName}
                      </span>
                    );
                  }
                })()}

                <span style={{
                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <Pin size={12} />
                  PINNED
                </span>
              </div>

              <h2 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.5rem',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.3'
              }}>
                {selectedPinnedPost.title}
              </h2>

              {/* Images */}
              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (
                <div style={{ marginBottom: '1.5rem' }}>
                  <ImageGallery
                    images={selectedPinnedPost.attachments}
                    altPrefix={selectedPinnedPost.title}
                    userRole={currentRole}
                    onImageClick={(index) => {
                      openLightbox(selectedPinnedPost.attachments, index);
                    }}
                  />
                </div>
              )}

              <div style={{
                color: '#4b5563',
                fontSize: '1rem',
                lineHeight: '1.6',
                marginBottom: '1.5rem'
              }}>
                {selectedPinnedPost.content}
              </div>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                fontSize: '0.875rem',
                color: '#6b7280',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <Calendar size={16} />
                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>
                </div>
                {selectedPinnedPost.author_name && (
                  <div>
                    By: {selectedPinnedPost.author_name}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Student Profile Settings Modal */}
      <StudentProfileSettingsModal
        isOpen={showProfileSettings && currentRole === 'student'}
        onClose={() => setShowProfileSettings(false)}
        currentUser={currentUser || null}
      />

      {/* Image Lightbox */}
      <ImageLightbox
        images={lightboxImages}
        initialIndex={lightboxInitialIndex}
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        altPrefix="Announcement Image"
      />
    </div>
  );
};

export default NewsFeed;
