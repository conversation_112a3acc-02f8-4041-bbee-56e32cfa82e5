{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m18 5-2.414-2.414A2 2 0 0 0 14.172 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2\",\n  key: \"142zxg\"\n}], [\"path\", {\n  d: \"M21.378 12.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"2t3380\"\n}], [\"path\", {\n  d: \"M8 18h1\",\n  key: \"13wk12\"\n}]];\nconst FilePenLine = createLucideIcon(\"file-pen-line\", __iconNode);\nexport { __iconNode, FilePenLine as default };\n//# sourceMappingURL=file-pen-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}