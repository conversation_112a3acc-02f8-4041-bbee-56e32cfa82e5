{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 12h6\",\n  key: \"1wqiqv\"\n}], [\"path\", {\n  d: \"M22 12h-6\",\n  key: \"1eg9hc\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M12 8v2\",\n  key: \"1woqiv\"\n}], [\"path\", {\n  d: \"M12 14v2\",\n  key: \"8jcxud\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"m19 9-3 3 3 3\",\n  key: \"12ol22\"\n}], [\"path\", {\n  d: \"m5 15 3-3-3-3\",\n  key: \"1kdhjc\"\n}]];\nconst FoldHorizontal = createLucideIcon(\"fold-horizontal\", __iconNode);\nexport { __iconNode, FoldHorizontal as default };\n//# sourceMappingURL=fold-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}