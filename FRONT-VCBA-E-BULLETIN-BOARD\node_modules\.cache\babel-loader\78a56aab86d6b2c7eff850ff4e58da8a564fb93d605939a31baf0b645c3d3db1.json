{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 16v2\",\n  key: \"g5qcv5\"\n}], [\"path\", {\n  d: \"M19 16v2\",\n  key: \"1gbaio\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"8\",\n  rx: \"2\",\n  key: \"vjsjur\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}]];\nconst RadioReceiver = createLucideIcon(\"radio-receiver\", __iconNode);\nexport { __iconNode, RadioReceiver as default };\n//# sourceMappingURL=radio-receiver.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}