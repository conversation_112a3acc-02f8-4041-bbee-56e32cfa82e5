{"ast": null, "code": "import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nclass CalendarReactionService {\n  constructor() {\n    this.client = void 0;\n    // Determine which client to use based on authentication context\n    this.client = this.getAuthenticatedClient();\n  }\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId) {\n    try {\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n      const response = await this.client.post(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          added: false\n        }\n      };\n    } catch (error) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event (following announcement pattern)\n  async unlikeEvent(eventId) {\n    try {\n      console.log('💔 CalendarReactionService - Unliking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n      const response = await this.client.delete(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          removed: false\n        }\n      };\n    } catch (error) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["httpClient", "adminHttpClient", "studentHttpClient", "CalendarReactionService", "constructor", "client", "getAuthenticatedClient", "studentToken", "localStorage", "getItem", "studentUser", "adminToken", "adminUser", "console", "log", "likeEvent", "eventId", "clientType", "response", "post", "success", "message", "data", "added", "error", "Error", "unlikeEvent", "delete", "removed", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { ApiResponse } from '../types/common.types';\nimport { API_BASE_URL } from '../config/constants';\n\nexport interface CalendarReactionData {\n  added?: boolean;\n  removed?: boolean;\n}\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: CalendarReactionData;\n}\n\nclass CalendarReactionService {\n  private client: any;\n\n  constructor() {\n    // Determine which client to use based on authentication context\n    this.client = this.getAuthenticatedClient();\n  }\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  private getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n\n      const response = await this.client.post<CalendarReactionData>(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { added: false }\n      };\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event (following announcement pattern)\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      console.log('💔 CalendarReactionService - Unliking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n\n      const response = await this.client.delete<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { removed: false }\n      };\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAe9E,MAAMC,uBAAuB,CAAC;EAG5BC,WAAWA,CAAA,EAAG;IAAA,KAFNC,MAAM;IAGZ;IACA,IAAI,CAACA,MAAM,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;EACUA,sBAAsBA,CAAA,EAAG;IAC/B,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;;IAEnD;IACA,IAAIF,YAAY,IAAIG,WAAW,EAAE;MAC/BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOZ,iBAAiB;IAC1B,CAAC,MAAM,IAAIS,UAAU,IAAIC,SAAS,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,OAAOb,eAAe;IACxB,CAAC,MAAM;MACLY,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOd,UAAU;IACnB;EACF;;EAEA;EACA,MAAMe,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACFH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QACjEE,OAAO;QACPC,UAAU,EAAE,IAAI,CAACZ,MAAM,KAAKJ,eAAe,GAAG,OAAO,GAAG,IAAI,CAACI,MAAM,KAAKH,iBAAiB,GAAG,SAAS,GAAG;MAC1G,CAAC,CAAC;MAEF,MAAMgB,QAAQ,GAAG,MAAM,IAAI,CAACb,MAAM,CAACc,IAAI,CAAuB,iBAAiBH,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;MAClG,OAAO;QACLI,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEC,KAAK,EAAE;QAAM;MACxC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,+BAA+B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMK,WAAWA,CAACV,OAAe,EAAqC;IACpE,IAAI;MACFH,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;QACnEE,OAAO;QACPC,UAAU,EAAE,IAAI,CAACZ,MAAM,KAAKJ,eAAe,GAAG,OAAO,GAAG,IAAI,CAACI,MAAM,KAAKH,iBAAiB,GAAG,SAAS,GAAG;MAC1G,CAAC,CAAC;MAEF,MAAMgB,QAAQ,GAAG,MAAM,IAAI,CAACb,MAAM,CAACsB,MAAM,CAAuB,iBAAiBX,OAAO,OAAO,CAAC;MAChG,OAAO;QACLI,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEM,OAAO,EAAE;QAAM;MAC1C,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,iCAAiC,CAAC;IACrE;EACF;;EAEA;EACA,MAAMQ,UAAUA,CAACb,OAAe,EAAEc,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACJ,WAAW,CAACV,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAMe,uBAAuB,GAAG,IAAI5B,uBAAuB,CAAC,CAAC;AACpE,eAAe4B,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}