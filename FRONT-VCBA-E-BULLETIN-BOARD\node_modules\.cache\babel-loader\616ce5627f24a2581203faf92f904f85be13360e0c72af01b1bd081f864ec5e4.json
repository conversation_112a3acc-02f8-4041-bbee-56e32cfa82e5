{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef(null);\n  const dropdownRef = useRef(null);\n  const avatarRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '100px',\n          height: '100px',\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n          transition: 'border-color 0.2s ease',\n          cursor: 'pointer'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n        },\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(Camera, {\n            size: 24,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              border: '2px solid #e8f5e8',\n              borderTop: '2px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"f5z6sbCF8HcXPUgF/Z8A/mPWxSw=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "size", "showActions", "_s", "preview", "setPreview", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showDropdown", "setShowDropdown", "showConfirmDialog", "setShowConfirmDialog", "pendingFile", "setPendingFile", "isHovered", "setIsHovered", "fileInputRef", "dropdownRef", "avatar<PERSON><PERSON>", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "setTimeout", "err", "message", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemove", "displayImage", "hasImage", "Boolean", "console", "log", "children", "style", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "current", "click", "src", "alt", "onLoad", "onError", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "flexDirection", "_fileInputRef$current2", "disabled", "padding", "ref", "accept", "onChange", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, MoreVertical, Edit3, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n  size?: number; // Size in pixels for the circular avatar\n  showActions?: boolean; // Whether to show action buttons\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [pendingFile, setPendingFile] = useState<File | null>(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const avatarRef = useRef<HTMLDivElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async (file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div\n          style={{\n            position: 'relative',\n            width: '100px',\n            height: '100px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n            transition: 'border-color 0.2s ease',\n            cursor: 'pointer'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: '2rem'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Upload Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(0, 0, 0, 0.5)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.2s ease'\n            }}\n          >\n            <Camera size={24} color=\"white\" />\n          </div>\n\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  border: '2px solid #e8f5e8',\n                  borderTop: '2px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.6 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAmB,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAqC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAaxG,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,GAAG;EACVC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMoC,YAAY,GAAGnC,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMoC,WAAW,GAAGpC,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMqC,SAAS,GAAGrC,MAAM,CAAiB,IAAI,CAAC;;EAE9C;EACA,MAAMsC,YAAY,GAAGrC,WAAW,CAAEsC,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACvB,IAAI,GAAGwB,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,gBAAgB,GAAG3C,WAAW,CAAC,MAAOsC,IAAU,IAAK;IACzDf,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMmB,eAAe,GAAGP,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIM,eAAe,EAAE;MACnBrB,QAAQ,CAACqB,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrB9B,UAAU,EAAA8B,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;IACxC,CAAC;IACDN,MAAM,CAACO,OAAO,GAAG,MAAM;MACrB7B,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDsB,MAAM,CAACQ,aAAa,CAACf,IAAI,CAAC;IAE1B,IAAI;MACF,MAAM3B,QAAQ,CAAC2B,IAAI,CAAC;MACpBb,UAAU,CAAC,uCAAuC,CAAC;MACnD6B,UAAU,CAAC,MAAM7B,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAO8B,GAAQ,EAAE;MACjBhC,QAAQ,CAACgC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3DrC,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACkB,YAAY,EAAE1B,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAM8C,iBAAiB,GAAIT,CAAsC,IAAK;IAAA,IAAAU,eAAA;IACpE,MAAMpB,IAAI,IAAAoB,eAAA,GAAGV,CAAC,CAACE,MAAM,CAACS,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIpB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsB,cAAc,GAAIZ,CAAkB,IAAK;IAC7CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBxC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMyC,eAAe,GAAId,CAAkB,IAAK;IAC9CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBxC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM0C,UAAU,GAAIf,CAAkB,IAAK;IACzCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBxC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMiB,IAAI,GAAGU,CAAC,CAACgB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAIrB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B1C,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBN,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMP,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnD6B,UAAU,CAAC,MAAM7B,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAO8B,GAAQ,EAAE;MACjBhC,QAAQ,CAACgC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMU,YAAY,GAAGhD,OAAO,IAAIT,cAAc;EAC9C,MAAM0D,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;;EAEtC;EACAG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C7D,cAAc;IACdS,OAAO;IACPgD,YAAY;IACZC;EACF,CAAC,CAAC;EAEF,oBACE5D,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAyD,QAAA,gBACpDhE,OAAA;MAAKiE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAEjEhE,OAAA;QACEiE,KAAK,EAAE;UACLI,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE7D,UAAU,GAAG,oBAAoB,GAAG,mBAAmB;UAC/D8D,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAExB,cAAe;QAC3ByB,WAAW,EAAEvB,eAAgB;QAC7BwB,MAAM,EAAEvB,UAAW;QACnBwB,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAMtD,YAAY,CAACuD,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;QAAA,CAAC;QAAAnB,QAAA,GAE5CJ,QAAQ,gBACP5D,OAAA;UACEoF,GAAG,EAAEzB,YAAa;UAClB0B,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,YAAY,CAAE;UACxE4B,OAAO,EAAG9C,CAAC,IAAKqB,OAAO,CAAC/C,KAAK,CAAC,yBAAyB,EAAE4C,YAAY,EAAElB,CAAC,CAAE;UAC1EwB,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF5F,OAAA;UACEiE,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdsB,UAAU,EAAE,mDAAmD;YAC/D3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAjC,QAAA,EAED7D;QAAY;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD5F,OAAA;UACEiE,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,oBAAoB;YAChC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAEzF,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3B8D,UAAU,EAAE;UACd,CAAE;UAAAX,QAAA,eAEFhE,OAAA,CAACJ,MAAM;YAACY,IAAI,EAAE,EAAG;YAACuF,KAAK,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAELtF,SAAS,iBACRN,OAAA;UACEiE,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,0BAA0B;YACtC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE;UAClB,CAAE;UAAA9B,QAAA,eAEFhE,OAAA;YACEiE,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,MAAM,EAAE,mBAAmB;cAC3B6B,SAAS,EAAE,mBAAmB;cAC9B/B,YAAY,EAAE,KAAK;cACnBgC,SAAS,EAAE;YACb;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5F,OAAA;QAAKiE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEuC,aAAa,EAAE,QAAQ;UAAErC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACpEhE,OAAA;UAAKiE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAC3ChE,OAAA;YACEgF,OAAO,EAAEA,CAAA;cAAA,IAAA0B,sBAAA;cAAA,QAAAA,sBAAA,GAAM/E,YAAY,CAACuD,OAAO,cAAAwB,sBAAA,uBAApBA,sBAAA,CAAsBvB,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CwB,QAAQ,EAAErG,SAAU;YACpB2D,KAAK,EAAE;cACL4B,UAAU,EAAE,mDAAmD;cAC/DE,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBZ,UAAU,EAAE,KAAK;cACjBpB,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CgG,OAAO,EAAEhG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B4D,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEFhE,OAAA,CAACN,MAAM;cAACc,IAAI,EAAE;YAAG;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBhC,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,EAERhC,QAAQ,iBACP5D,OAAA;YACEgF,OAAO,EAAEtB,YAAa;YACtBiD,QAAQ,EAAErG,SAAU;YACpB2D,KAAK,EAAE;cACL4B,UAAU,EAAE,MAAM;cAClBnB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBhC,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CyF,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAEhG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B4D,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEFhE,OAAA,CAACL,CAAC;cAACa,IAAI,EAAE;YAAG;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN5F,OAAA;UACE6G,GAAG,EAAElF,YAAa;UAClBQ,IAAI,EAAC,MAAM;UACX2E,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAE7D,iBAAkB;UAC5Be,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF5F,OAAA;UAAGiE,KAAK,EAAE;YACRgC,QAAQ,EAAE,UAAU;YACpBF,KAAK,EAAE,SAAS;YAChBiB,MAAM,EAAE;UACV,CAAE;UAAAhD,QAAA,EAAC;QAEH;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7E,KAAK,iBACJf,OAAA;MAAKiE,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAhE,OAAA,CAACH,WAAW;QAACW,IAAI,EAAE;MAAG;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB7E,KAAK;IAAA;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA3E,OAAO,iBACNjB,OAAA;MAAKiE,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAhE,OAAA,CAACF,WAAW;QAACU,IAAI,EAAE;MAAG;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB3E,OAAO;IAAA;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD5F,OAAA;MAAAgE,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClF,EAAA,CA7UIT,oBAAyD;AAAAiH,EAAA,GAAzDjH,oBAAyD;AA+U/D,eAAeA,oBAAoB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}