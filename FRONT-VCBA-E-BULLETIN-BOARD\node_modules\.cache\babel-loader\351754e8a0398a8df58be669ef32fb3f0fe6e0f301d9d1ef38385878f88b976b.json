{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6\",\n  key: \"1ipuwl\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst Clock12 = createLucideIcon(\"clock-12\", __iconNode);\nexport { __iconNode, Clock12 as default };\n//# sourceMappingURL=clock-12.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}