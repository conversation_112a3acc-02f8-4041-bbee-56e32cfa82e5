{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 7-3 5h4l-3 5\",\n  key: \"b4a64w\"\n}], [\"path\", {\n  d: \"M14.856 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.935\",\n  key: \"lre1cr\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M5.14 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2.936\",\n  key: \"13q5k0\"\n}]];\nconst BatteryCharging = createLucideIcon(\"battery-charging\", __iconNode);\nexport { __iconNode, BatteryCharging as default };\n//# sourceMappingURL=battery-charging.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}