{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n  key: \"yps3ct\"\n}], [\"path\", {\n  d: \"m9 11 3 3L22 4\",\n  key: \"1pflzl\"\n}]];\nconst CircleCheckBig = createLucideIcon(\"circle-check-big\", __iconNode);\nexport { __iconNode, CircleCheckBig as default };\n//# sourceMappingURL=circle-check-big.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}