{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 17H7A5 5 0 0 1 7 7h2\",\n  key: \"8i5ue5\"\n}], [\"path\", {\n  d: \"M15 7h2a5 5 0 1 1 0 10h-2\",\n  key: \"1b9ql8\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"16\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1jonct\"\n}]];\nconst Link2 = createLucideIcon(\"link-2\", __iconNode);\nexport { __iconNode, Link2 as default };\n//# sourceMappingURL=link-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}