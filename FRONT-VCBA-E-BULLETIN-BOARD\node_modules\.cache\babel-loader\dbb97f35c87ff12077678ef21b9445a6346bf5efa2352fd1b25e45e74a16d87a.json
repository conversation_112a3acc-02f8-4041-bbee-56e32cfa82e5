{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M6 9v12\",\n  key: \"1sc30k\"\n}], [\"path\", {\n  d: \"M13 6h3a2 2 0 0 1 2 2v3\",\n  key: \"1jb6z3\"\n}], [\"path\", {\n  d: \"M18 15v6\",\n  key: \"9wciyi\"\n}], [\"path\", {\n  d: \"M21 18h-6\",\n  key: \"139f0c\"\n}]];\nconst GitPullRequestCreate = createLucideIcon(\"git-pull-request-create\", __iconNode);\nexport { __iconNode, GitPullRequestCreate as default };\n//# sourceMappingURL=git-pull-request-create.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}