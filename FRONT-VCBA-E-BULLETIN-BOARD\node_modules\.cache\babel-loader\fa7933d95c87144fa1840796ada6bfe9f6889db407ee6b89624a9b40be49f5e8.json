{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M8 7h8\",\n  key: \"i86dvs\"\n}], [\"path\", {\n  d: \"M12 17.5 8 15h1a4 4 0 0 0 0-8\",\n  key: \"grpkl4\"\n}], [\"path\", {\n  d: \"M8 11h8\",\n  key: \"vwpz6n\"\n}]];\nconst ReceiptIndianRupee = createLucideIcon(\"receipt-indian-rupee\", __iconNode);\nexport { __iconNode, ReceiptIndianRupee as default };\n//# sourceMappingURL=receipt-indian-rupee.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}