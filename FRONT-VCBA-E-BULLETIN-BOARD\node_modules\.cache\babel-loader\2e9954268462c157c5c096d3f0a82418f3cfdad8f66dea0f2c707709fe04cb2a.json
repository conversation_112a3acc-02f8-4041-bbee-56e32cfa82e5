{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 12h12\",\n  key: \"8npq4p\"\n}], [\"path\", {\n  d: \"M6 20V4\",\n  key: \"1w1bmo\"\n}], [\"path\", {\n  d: \"M18 20V4\",\n  key: \"o2hl4u\"\n}]];\nconst Heading = createLucideIcon(\"heading\", __iconNode);\nexport { __iconNode, Heading as default };\n//# sourceMappingURL=heading.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}