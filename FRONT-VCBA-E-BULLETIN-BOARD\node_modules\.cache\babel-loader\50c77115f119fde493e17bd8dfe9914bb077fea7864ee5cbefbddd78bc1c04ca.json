{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 9.5V21m0-11.5L6 3m6 6.5L18 3\",\n  key: \"2ej80x\"\n}], [\"path\", {\n  d: \"M6 15h12\",\n  key: \"1hwgt5\"\n}], [\"path\", {\n  d: \"M6 11h12\",\n  key: \"wf4gp6\"\n}]];\nconst JapaneseYen = createLucideIcon(\"japanese-yen\", __iconNode);\nexport { __iconNode, JapaneseYen as default };\n//# sourceMappingURL=japanese-yen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}