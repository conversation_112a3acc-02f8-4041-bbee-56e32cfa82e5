{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\",\n  key: \"5wwlr5\"\n}], [\"path\", {\n  d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n  key: \"1d0kgt\"\n}]];\nconst House = createLucideIcon(\"house\", __iconNode);\nexport { __iconNode, House as default };\n//# sourceMappingURL=house.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}