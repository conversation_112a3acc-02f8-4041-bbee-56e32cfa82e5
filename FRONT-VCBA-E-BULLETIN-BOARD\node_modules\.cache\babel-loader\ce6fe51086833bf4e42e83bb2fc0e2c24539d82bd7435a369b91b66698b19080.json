{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2a10 10 0 0 1 7.38 16.75\",\n  key: \"175t95\"\n}], [\"path\", {\n  d: \"m16 12-4-4-4 4\",\n  key: \"177agl\"\n}], [\"path\", {\n  d: \"M12 16V8\",\n  key: \"1sbj14\"\n}], [\"path\", {\n  d: \"M2.5 8.875a10 10 0 0 0-.5 3\",\n  key: \"1vce0s\"\n}], [\"path\", {\n  d: \"M2.83 16a10 10 0 0 0 2.43 3.4\",\n  key: \"o3fkw4\"\n}], [\"path\", {\n  d: \"M4.636 5.235a10 10 0 0 1 .891-.857\",\n  key: \"1szpfk\"\n}], [\"path\", {\n  d: \"M8.644 21.42a10 10 0 0 0 7.631-.38\",\n  key: \"9yhvd4\"\n}]];\nconst CircleFadingArrowUp = createLucideIcon(\"circle-fading-arrow-up\", __iconNode);\nexport { __iconNode, CircleFadingArrowUp as default };\n//# sourceMappingURL=circle-fading-arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}