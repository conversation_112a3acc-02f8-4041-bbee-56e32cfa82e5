{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"7\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"f1a2em\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"3\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"1bb6yr\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"14\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"nxv5o0\"\n}]];\nconst LayoutPanelTop = createLucideIcon(\"layout-panel-top\", __iconNode);\nexport { __iconNode, LayoutPanelTop as default };\n//# sourceMappingURL=layout-panel-top.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}