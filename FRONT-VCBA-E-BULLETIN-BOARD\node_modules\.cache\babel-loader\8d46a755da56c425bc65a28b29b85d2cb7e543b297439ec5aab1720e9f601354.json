{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 9a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h1l2 2h12l2-2h1a1 1 0 0 0 1-1Z\",\n  key: \"2128wb\"\n}], [\"path\", {\n  d: \"M7.5 12h9\",\n  key: \"1t0ckc\"\n}]];\nconst HdmiPort = createLucideIcon(\"hdmi-port\", __iconNode);\nexport { __iconNode, HdmiPort as default };\n//# sourceMappingURL=hdmi-port.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}