{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.5 13h6\",\n  key: \"p1my2r\"\n}], [\"path\", {\n  d: \"m2 16 4.5-9 4.5 9\",\n  key: \"ndf0b3\"\n}], [\"path\", {\n  d: \"M18 16V7\",\n  key: \"ty0viw\"\n}], [\"path\", {\n  d: \"m14 11 4-4 4 4\",\n  key: \"1pu57t\"\n}]];\nconst AArrowUp = createLucideIcon(\"a-arrow-up\", __iconNode);\nexport { __iconNode, AArrowUp as default };\n//# sourceMappingURL=a-arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}