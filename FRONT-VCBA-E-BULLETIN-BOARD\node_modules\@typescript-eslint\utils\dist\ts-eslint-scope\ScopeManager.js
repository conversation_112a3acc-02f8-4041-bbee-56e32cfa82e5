"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeManager = void 0;
const scope_manager_1 = __importDefault(require("eslint-scope/lib/scope-manager"));
const ScopeManager = scope_manager_1.default;
exports.ScopeManager = ScopeManager;
//# sourceMappingURL=ScopeManager.js.map