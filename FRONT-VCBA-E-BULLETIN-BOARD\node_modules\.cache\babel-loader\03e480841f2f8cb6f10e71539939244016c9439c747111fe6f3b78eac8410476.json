{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.034 12.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z\",\n  key: \"xwnzip\"\n}], [\"path\", {\n  d: \"M5 3a2 2 0 0 0-2 2\",\n  key: \"y57alp\"\n}], [\"path\", {\n  d: \"M19 3a2 2 0 0 1 2 2\",\n  key: \"18rm91\"\n}], [\"path\", {\n  d: \"M5 21a2 2 0 0 1-2-2\",\n  key: \"sbafld\"\n}], [\"path\", {\n  d: \"M9 3h1\",\n  key: \"1<PERSON>ri\"\n}], [\"path\", {\n  d: \"M9 21h2\",\n  key: \"1qve2z\"\n}], [\"path\", {\n  d: \"M14 3h1\",\n  key: \"1ec4yj\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}], [\"path\", {\n  d: \"M21 9v2\",\n  key: \"p14lih\"\n}], [\"path\", {\n  d: \"M3 14v1\",\n  key: \"vnatye\"\n}]];\nconst SquareDashedMousePointer = createLucideIcon(\"square-dashed-mouse-pointer\", __iconNode);\nexport { __iconNode, SquareDashedMousePointer as default };\n//# sourceMappingURL=square-dashed-mouse-pointer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}