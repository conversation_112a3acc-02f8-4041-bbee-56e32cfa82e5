{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.4 15.63a7.875 6 135 1 1 6.23-6.23 4.5 3.43 135 0 0-6.23 6.23\",\n  key: \"1dtqwm\"\n}], [\"path\", {\n  d: \"m8.29 12.71-2.6 2.6a2.5 2.5 0 1 0-1.65 4.65A2.5 2.5 0 1 0 8.7 18.3l2.59-2.59\",\n  key: \"1oq1fw\"\n}]];\nconst Drumstick = createLucideIcon(\"drumstick\", __iconNode);\nexport { __iconNode, Drumstick as default };\n//# sourceMappingURL=drumstick.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}