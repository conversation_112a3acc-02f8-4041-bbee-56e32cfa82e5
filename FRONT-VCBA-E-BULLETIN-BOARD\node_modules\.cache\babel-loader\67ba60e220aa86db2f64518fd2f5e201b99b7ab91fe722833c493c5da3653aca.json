{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"6\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"hsirpf\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"16\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"13zkjt\"\n}], [\"path\", {\n  d: \"M2 2v20\",\n  key: \"1ivd8o\"\n}]];\nconst AlignHorizontalJustifyStart = createLucideIcon(\"align-horizontal-justify-start\", __iconNode);\nexport { __iconNode, AlignHorizontalJustifyStart as default };\n//# sourceMappingURL=align-horizontal-justify-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}