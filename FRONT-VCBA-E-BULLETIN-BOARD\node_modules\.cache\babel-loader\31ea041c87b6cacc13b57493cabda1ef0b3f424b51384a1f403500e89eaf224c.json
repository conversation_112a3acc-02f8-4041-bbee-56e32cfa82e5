{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 12H7\",\n  key: \"16if0g\"\n}], [\"path\", {\n  d: \"M19 18H5\",\n  key: \"18s9l3\"\n}], [\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}]];\nconst AlignCenter = createLucideIcon(\"align-center\", __iconNode);\nexport { __iconNode, AlignCenter as default };\n//# sourceMappingURL=align-center.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}