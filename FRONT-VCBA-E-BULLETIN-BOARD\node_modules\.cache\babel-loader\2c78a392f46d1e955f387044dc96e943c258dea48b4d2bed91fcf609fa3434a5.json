{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"path\", {\n  d: \"M2 21a8 8 0 0 1 10.434-7.62\",\n  key: \"1yezr2\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"path\", {\n  d: \"m22 22-1.9-1.9\",\n  key: \"1e5ubv\"\n}]];\nconst UserRoundSearch = createLucideIcon(\"user-round-search\", __iconNode);\nexport { __iconNode, UserRoundSearch as default };\n//# sourceMappingURL=user-round-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}