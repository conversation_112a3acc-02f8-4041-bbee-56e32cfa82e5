{"ast": null, "code": "import React,{useState,useEffect}from'react';import{holidayService}from'../../services/holidayService';import{Calendar,RefreshCw,Download,Upload,Trash2,BarChart3,Globe,MapPin,Star}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HolidayManagement=_ref=>{let{onClose}=_ref;const[currentYear,setCurrentYear]=useState(new Date().getFullYear());const[holidays,setHolidays]=useState([]);const[stats,setStats]=useState(null);const[loading,setLoading]=useState(false);const[syncing,setSyncing]=useState(false);const[activeTab,setActiveTab]=useState('overview');const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');// Load holidays and stats\nconst loadData=async()=>{setLoading(true);try{const[holidaysData,statsData]=await Promise.all([holidayService.getHolidays(currentYear),holidayService.getHolidayStats(currentYear)]);setHolidays(holidaysData);setStats(statsData);}catch(error){console.error('Error loading holiday data:',error);setErrorMessage('Failed to load holiday data');}finally{setLoading(false);}};// Load data when component mounts or year changes\nuseEffect(()=>{loadData();},[currentYear]);// Clear messages after 5 seconds\nuseEffect(()=>{if(successMessage||errorMessage){const timer=setTimeout(()=>{setSuccessMessage('');setErrorMessage('');},5000);return()=>clearTimeout(timer);}},[successMessage,errorMessage]);// Sync holidays\nconst handleSync=async function(){let force=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;setSyncing(true);try{const results=await holidayService.syncHolidays(currentYear,force);setSuccessMessage(\"Sync completed! Created: \".concat(results.created,\", Updated: \").concat(results.updated,\", Skipped: \").concat(results.skipped));await loadData();// Reload data after sync\n}catch(error){console.error('Error syncing holidays:',error);setErrorMessage('Failed to sync holidays');}finally{setSyncing(false);}};// Delete auto-generated holidays\nconst handleDeleteAutoGenerated=async()=>{if(!window.confirm(\"Are you sure you want to delete all auto-generated holidays for \".concat(currentYear,\"?\"))){return;}setLoading(true);try{const deletedCount=await holidayService.deleteAutoGeneratedHolidays(currentYear);setSuccessMessage(\"Deleted \".concat(deletedCount,\" auto-generated holidays\"));await loadData();// Reload data after deletion\n}catch(error){console.error('Error deleting holidays:',error);setErrorMessage('Failed to delete holidays');}finally{setLoading(false);}};// Filter holidays by category\nconst getHolidaysByCategory=category=>{return holidays.filter(holiday=>{var _holiday$category_nam;switch(category){case'philippine':return holiday.holiday_type==='local'&&holiday.country_code==='PH';case'international':return holiday.holiday_type==='international'||holiday.is_global;case'religious':return(_holiday$category_nam=holiday.category_name)===null||_holiday$category_nam===void 0?void 0:_holiday$category_nam.toLowerCase().includes('religious');default:return true;}});};// Render holiday list\nconst renderHolidayList=holidayList=>/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'400px',overflowY:'auto'},children:holidayList.length===0?/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#6b7280'},children:\"No holidays found for this category\"}):/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'0.5rem'},children:holidayList.map(holiday=>/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',backgroundColor:'white',border:'1px solid #e5e7eb',borderRadius:'8px',borderLeft:\"4px solid \".concat(holiday.category_color),display:'flex',justifyContent:'space-between',alignItems:'center'},children:/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#111827',marginBottom:'0.25rem'},children:holiday.title}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280',marginBottom:'0.25rem'},children:new Date(holiday.event_date).toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'})}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#9ca3af',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:holiday.category_color,color:'white',padding:'0.125rem 0.375rem',borderRadius:'4px'},children:holiday.category_name}),holiday.country_code&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(MapPin,{size:12}),holiday.country_code]}),holiday.is_auto_generated&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(RefreshCw,{size:12}),\"Auto-generated\"]})]})]})},holiday.calendar_id))})});return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',boxShadow:'0 20px 25px -5px rgba(0, 0, 0, 0.1)',width:'100%',maxWidth:'1200px',maxHeight:'90vh',overflow:'hidden',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',borderBottom:'1px solid #e5e7eb',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(Calendar,{style:{color:'#22c55e'},size:24}),/*#__PURE__*/_jsx(\"h2\",{style:{margin:0,fontSize:'1.5rem',fontWeight:'600',color:'#111827'},children:\"Holiday View\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"select\",{value:currentYear,onChange:e=>setCurrentYear(parseInt(e.target.value)),style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'6px',fontSize:'0.875rem'},children:Array.from({length:6},(_,i)=>new Date().getFullYear()-1+i).map(year=>/*#__PURE__*/_jsx(\"option\",{value:year,children:year},year))}),onClose&&/*#__PURE__*/_jsx(\"button\",{onClick:onClose,style:{padding:'0.5rem',backgroundColor:'#f3f4f6',border:'none',borderRadius:'6px',cursor:'pointer',fontSize:'1.25rem',color:'#6b7280'},children:\"\\xD7\"})]})]}),(successMessage||errorMessage)&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem 1.5rem',backgroundColor:successMessage?'#f0fdf4':'#fef2f2',borderBottom:'1px solid #e5e7eb',color:successMessage?'#166534':'#dc2626'},children:successMessage||errorMessage}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'0 1.5rem',borderBottom:'1px solid #e5e7eb',display:'flex',gap:'0.5rem'},children:[{key:'overview',label:'Overview',icon:BarChart3},{key:'philippine',label:'Philippine',icon:MapPin},{key:'international',label:'International',icon:Globe},{key:'religious',label:'Religious',icon:Star}// { key: 'sync', label: 'Sync', icon: RefreshCw }\n].map(_ref2=>{let{key,label,icon:Icon}=_ref2;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(key),style:{padding:'0.75rem 1rem',border:'none',backgroundColor:'transparent',borderBottom:activeTab===key?'2px solid #22c55e':'2px solid transparent',color:activeTab===key?'#22c55e':'#6b7280',cursor:'pointer',display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.875rem',fontWeight:'500',transition:'all 0.2s ease'},children:[/*#__PURE__*/_jsx(Icon,{size:16}),label]},key);})}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,padding:'1.5rem',overflow:'auto'},children:loading?/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',height:'200px'},children:/*#__PURE__*/_jsx(RefreshCw,{className:\"animate-spin\",size:24,style:{color:'#22c55e'}})}):/*#__PURE__*/_jsxs(_Fragment,{children:[activeTab==='overview'&&stats&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',backgroundColor:'#f0fdf4',borderRadius:'8px',border:'1px solid #bbf7d0'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'2rem',fontWeight:'700',color:'#166534'},children:stats.total}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',color:'#166534'},children:\"Total Holidays\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',backgroundColor:'#eff6ff',borderRadius:'8px',border:'1px solid #bfdbfe'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'2rem',fontWeight:'700',color:'#1d4ed8'},children:stats.autoGenerated}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',color:'#1d4ed8'},children:\"Auto-Generated\"})]})]})}),activeTab==='philippine'&&renderHolidayList(getHolidaysByCategory('philippine')),activeTab==='international'&&renderHolidayList(getHolidaysByCategory('international')),activeTab==='religious'&&renderHolidayList(getHolidaysByCategory('religious')),activeTab==='sync'&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1rem 0',fontSize:'1.125rem',fontWeight:'600'},children:\"Sync Holidays\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',marginBottom:'1rem'},children:\"Sync holidays from external sources to the database. This will automatically create holiday events for Philippine holidays, international holidays, and religious observances.\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'1rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleSync(false),disabled:syncing,style:{padding:'0.75rem 1.5rem',backgroundColor:'#22c55e',color:'white',border:'none',borderRadius:'6px',cursor:syncing?'not-allowed':'pointer',display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.875rem',fontWeight:'500',opacity:syncing?0.6:1},children:[syncing?/*#__PURE__*/_jsx(RefreshCw,{className:\"animate-spin\",size:16}):/*#__PURE__*/_jsx(Download,{size:16}),syncing?'Syncing...':'Sync Holidays']}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleSync(true),disabled:syncing,style:{padding:'0.75rem 1.5rem',backgroundColor:'#f59e0b',color:'white',border:'none',borderRadius:'6px',cursor:syncing?'not-allowed':'pointer',display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.875rem',fontWeight:'500',opacity:syncing?0.6:1},children:[/*#__PURE__*/_jsx(Upload,{size:16}),\"Force Sync\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleDeleteAutoGenerated,disabled:loading,style:{padding:'0.75rem 1.5rem',backgroundColor:'#ef4444',color:'white',border:'none',borderRadius:'6px',cursor:loading?'not-allowed':'pointer',display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.875rem',fontWeight:'500',opacity:loading?0.6:1},children:[/*#__PURE__*/_jsx(Trash2,{size:16}),\"Delete Auto-Generated\"]})]})]})})]})})]})});};export default HolidayManagement;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}