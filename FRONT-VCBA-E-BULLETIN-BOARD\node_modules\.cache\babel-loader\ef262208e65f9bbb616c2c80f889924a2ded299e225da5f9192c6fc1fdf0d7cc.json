{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"11\",\n  cy: \"11\",\n  r: \"8\",\n  key: \"4ej97u\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"16.65\",\n  y1: \"21\",\n  y2: \"16.65\",\n  key: \"13gj7c\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"14\",\n  y1: \"11\",\n  y2: \"11\",\n  key: \"durymu\"\n}]];\nconst ZoomOut = createLucideIcon(\"zoom-out\", __iconNode);\nexport { __iconNode, ZoomOut as default };\n//# sourceMappingURL=zoom-out.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}