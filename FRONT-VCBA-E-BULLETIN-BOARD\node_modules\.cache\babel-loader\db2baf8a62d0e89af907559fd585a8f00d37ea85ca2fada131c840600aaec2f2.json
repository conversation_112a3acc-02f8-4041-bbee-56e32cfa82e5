{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.765 21.522a.5.5 0 0 1-.765-.424v-8.196a.5.5 0 0 1 .765-.424l5.878 3.674a1 1 0 0 1 0 1.696z\",\n  key: \"17shqo\"\n}], [\"path\", {\n  d: \"M14.12 3.88 16 2\",\n  key: \"qol33r\"\n}], [\"path\", {\n  d: \"M18 11a4 4 0 0 0-4-4h-4a4 4 0 0 0-4 4v3a6.1 6.1 0 0 0 2 4.5\",\n  key: \"1tjixy\"\n}], [\"path\", {\n  d: \"M20.97 5c0 2.1-1.6 3.8-3.5 4\",\n  key: \"18gb23\"\n}], [\"path\", {\n  d: \"M3 21c0-2.1 1.7-3.9 3.8-4\",\n  key: \"4p0ekp\"\n}], [\"path\", {\n  d: \"M6 13H2\",\n  key: \"82j7cp\"\n}], [\"path\", {\n  d: \"M6.53 9C4.6 8.8 3 7.1 3 5\",\n  key: \"32zzws\"\n}], [\"path\", {\n  d: \"m8 2 1.88 1.88\",\n  key: \"fmnt4t\"\n}], [\"path\", {\n  d: \"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1\",\n  key: \"d7y7pr\"\n}]];\nconst BugPlay = createLucideIcon(\"bug-play\", __iconNode);\nexport { __iconNode, BugPlay as default };\n//# sourceMappingURL=bug-play.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}