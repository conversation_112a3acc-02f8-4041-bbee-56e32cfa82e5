{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 20v-9H2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2Z\",\n  key: \"109fe4\"\n}], [\"path\", {\n  d: \"M18 11V4H6v7\",\n  key: \"mon5oj\"\n}], [\"path\", {\n  d: \"M15 22v-4a3 3 0 0 0-3-3a3 3 0 0 0-3 3v4\",\n  key: \"1k4jtn\"\n}], [\"path\", {\n  d: \"M22 11V9\",\n  key: \"3zbp94\"\n}], [\"path\", {\n  d: \"M2 11V9\",\n  key: \"1x5rnq\"\n}], [\"path\", {\n  d: \"M6 4V2\",\n  key: \"1rsq15\"\n}], [\"path\", {\n  d: \"M18 4V2\",\n  key: \"1jsdo1\"\n}], [\"path\", {\n  d: \"M10 4V2\",\n  key: \"75d9ly\"\n}], [\"path\", {\n  d: \"M14 4V2\",\n  key: \"8nj3z6\"\n}]];\nconst Castle = createLucideIcon(\"castle\", __iconNode);\nexport { __iconNode, Castle as default };\n//# sourceMappingURL=castle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}