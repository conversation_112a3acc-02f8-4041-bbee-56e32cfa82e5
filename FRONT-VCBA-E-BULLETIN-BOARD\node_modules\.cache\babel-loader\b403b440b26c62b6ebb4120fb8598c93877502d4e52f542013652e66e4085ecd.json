{"ast": null, "code": "/**\n * Comment Depth Management Utilities\n * \n * This module provides utilities for managing comment threading depth\n * to prevent infinite reply loops, following industry best practices.\n * \n * Depth Levels:\n * - Level 0: Original comment (top-level)\n * - Level 1: Reply to original comment\n * - Level 2: Reply to reply (maximum allowed depth)\n * - Level 3+: Should be flattened or redirected\n */\n\n// Configuration constants\nexport const COMMENT_DEPTH_CONFIG = {\n  MAX_DEPTH: 2,\n  // Maximum allowed depth (0-based)\n  MAX_VISUAL_DEPTH: 3,\n  // Maximum visual indentation depth\n  FLATTEN_THRESHOLD: 3,\n  // Depth at which to start flattening\n  INDENT_SIZE: 20,\n  // Pixels per depth level\n  MAX_INDENT: 60 // Maximum indentation in pixels\n};\n\n/**\n * Calculate the depth of a comment in the thread hierarchy\n */\nexport function calculateCommentDepth(comment, allComments) {\n  let depth = 0;\n  let currentComment = comment;\n\n  // Traverse up the parent chain to calculate depth\n  while (currentComment.parent_comment_id) {\n    depth++;\n    const parentComment = allComments.find(c => c.comment_id === currentComment.parent_comment_id);\n    if (!parentComment) break;\n    currentComment = parentComment;\n\n    // Safety check to prevent infinite loops\n    if (depth > 10) {\n      console.warn('Comment depth calculation exceeded safety limit');\n      break;\n    }\n  }\n  return depth;\n}\n\n/**\n * Check if a comment can have replies based on depth limits\n */\nexport function canCommentHaveReplies(depth) {\n  return depth < COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n}\n\n/**\n * Check if a comment should show the reply button\n */\nexport function shouldShowReplyButton(depth) {\n  return canCommentHaveReplies(depth);\n}\n\n/**\n * Check if a comment thread should be flattened\n */\nexport function shouldFlattenThread(depth) {\n  return depth >= COMMENT_DEPTH_CONFIG.FLATTEN_THRESHOLD;\n}\n\n/**\n * Calculate visual indentation for a comment based on depth\n */\nexport function calculateIndentation(depth) {\n  const visualDepth = Math.min(depth, COMMENT_DEPTH_CONFIG.MAX_VISUAL_DEPTH);\n  const indentation = visualDepth * COMMENT_DEPTH_CONFIG.INDENT_SIZE;\n  return Math.min(indentation, COMMENT_DEPTH_CONFIG.MAX_INDENT);\n}\n\n/**\n * Get the appropriate parent comment ID for a new reply\n * If depth limit is reached, redirect to the root comment\n */\nexport function getReplyParentId(targetComment, allComments) {\n  const currentDepth = calculateCommentDepth(targetComment, allComments);\n  if (currentDepth < COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    // Normal reply - use the target comment as parent\n    return targetComment.comment_id;\n  } else {\n    // Depth limit reached - find the root comment of this thread\n    let rootComment = targetComment;\n    while (rootComment.parent_comment_id) {\n      const parentComment = allComments.find(c => c.comment_id === rootComment.parent_comment_id);\n      if (!parentComment) break;\n      rootComment = parentComment;\n    }\n    return rootComment.comment_id;\n  }\n}\n\n/**\n * Build a hierarchical comment tree with depth limits\n */\nexport function buildCommentTree(comments) {\n  const commentMap = new Map();\n  const rootComments = [];\n\n  // Create a map of all comments\n  comments.forEach(comment => {\n    commentMap.set(comment.comment_id, {\n      ...comment,\n      replies: []\n    });\n  });\n\n  // Build the tree structure\n  comments.forEach(comment => {\n    const commentWithReplies = commentMap.get(comment.comment_id);\n    if (comment.parent_comment_id) {\n      const parent = commentMap.get(comment.parent_comment_id);\n      if (parent) {\n        const parentDepth = calculateCommentDepth(parent, comments);\n        const currentDepth = parentDepth + 1;\n\n        // Only add as reply if within depth limits\n        if (currentDepth <= COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n          parent.replies = parent.replies || [];\n          parent.replies.push(commentWithReplies);\n        } else {\n          // If depth limit exceeded, add as root comment\n          rootComments.push(commentWithReplies);\n        }\n      } else {\n        // Parent not found, add as root comment\n        rootComments.push(commentWithReplies);\n      }\n    } else {\n      // Top-level comment\n      rootComments.push(commentWithReplies);\n    }\n  });\n  return rootComments;\n}\n\n/**\n * Get a user-friendly message for depth limit reached\n */\nexport function getDepthLimitMessage(depth) {\n  if (depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    return \"Reply depth limit reached. Your reply will be added as a new comment in this thread.\";\n  }\n  return \"\";\n}\n\n/**\n * Get thread continuation message\n */\nexport function getThreadContinuationMessage(commentCount) {\n  if (commentCount > 0) {\n    return `Continue this thread (${commentCount} more ${commentCount === 1 ? 'reply' : 'replies'})`;\n  }\n  return \"Continue this thread\";\n}\n\n/**\n * Validate comment depth before creation (for backend validation)\n */\nexport function validateCommentDepth(parentCommentId, allComments) {\n  if (!parentCommentId) {\n    // Top-level comment is always valid\n    return {\n      isValid: true\n    };\n  }\n  const parentComment = allComments.find(c => c.comment_id === parentCommentId);\n  if (!parentComment) {\n    return {\n      isValid: false,\n      message: \"Parent comment not found\"\n    };\n  }\n  const parentDepth = calculateCommentDepth(parentComment, allComments);\n  const newCommentDepth = parentDepth + 1;\n  if (newCommentDepth > COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    const suggestedParentId = getReplyParentId(parentComment, allComments);\n    return {\n      isValid: false,\n      message: `Comment depth limit (${COMMENT_DEPTH_CONFIG.MAX_DEPTH + 1} levels) exceeded. Reply will be added to thread root.`,\n      suggestedParentId\n    };\n  }\n  return {\n    isValid: true\n  };\n}\n\n/**\n * Get CSS classes for comment depth styling\n */\nexport function getCommentDepthClasses(depth) {\n  const classes = [`comment-depth-${depth}`];\n  if (depth === 0) {\n    classes.push('comment-root');\n  } else if (depth === 1) {\n    classes.push('comment-reply');\n  } else if (depth >= 2) {\n    classes.push('comment-deep-reply');\n  }\n  if (shouldFlattenThread(depth)) {\n    classes.push('comment-flattened');\n  }\n  return classes;\n}\n\n/**\n * Format depth information for debugging\n */\nexport function formatDepthInfo(comment, allComments) {\n  const depth = calculateCommentDepth(comment, allComments);\n  const canReply = canCommentHaveReplies(depth);\n  const shouldFlatten = shouldFlattenThread(depth);\n  return `Depth: ${depth}, CanReply: ${canReply}, ShouldFlatten: ${shouldFlatten}`;\n}\nexport default {\n  COMMENT_DEPTH_CONFIG,\n  calculateCommentDepth,\n  canCommentHaveReplies,\n  shouldShowReplyButton,\n  shouldFlattenThread,\n  calculateIndentation,\n  getReplyParentId,\n  buildCommentTree,\n  getDepthLimitMessage,\n  getThreadContinuationMessage,\n  validateCommentDepth,\n  getCommentDepthClasses,\n  formatDepthInfo\n};", "map": {"version": 3, "names": ["COMMENT_DEPTH_CONFIG", "MAX_DEPTH", "MAX_VISUAL_DEPTH", "FLATTEN_THRESHOLD", "INDENT_SIZE", "MAX_INDENT", "calculateCommentDepth", "comment", "allComments", "depth", "currentComment", "parent_comment_id", "parentComment", "find", "c", "comment_id", "console", "warn", "canCommentHaveReplies", "shouldShowReplyButton", "shouldFlattenThread", "calculateIndentation", "visual<PERSON>epth", "Math", "min", "indentation", "getReplyParentId", "targetComment", "<PERSON><PERSON><PERSON><PERSON>", "rootComment", "buildCommentTree", "comments", "commentMap", "Map", "rootComments", "for<PERSON>ach", "set", "replies", "commentWithReplies", "get", "parent", "parent<PERSON><PERSON><PERSON>", "push", "getDepthLimitMessage", "getThreadContinuationMessage", "commentCount", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentCommentId", "<PERSON><PERSON><PERSON><PERSON>", "message", "newCommentDepth", "suggestedParentId", "getCommentDepthClasses", "classes", "formatDepthInfo", "canReply", "shouldFlatten"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/commentDepth.ts"], "sourcesContent": ["/**\n * Comment Depth Management Utilities\n * \n * This module provides utilities for managing comment threading depth\n * to prevent infinite reply loops, following industry best practices.\n * \n * Depth Levels:\n * - Level 0: Original comment (top-level)\n * - Level 1: Reply to original comment\n * - Level 2: Reply to reply (maximum allowed depth)\n * - Level 3+: Should be flattened or redirected\n */\n\nimport type { Comment } from '../services/commentService';\n\n// Configuration constants\nexport const COMMENT_DEPTH_CONFIG = {\n  MAX_DEPTH: 2,           // Maximum allowed depth (0-based)\n  MAX_VISUAL_DEPTH: 3,    // Maximum visual indentation depth\n  FLATTEN_THRESHOLD: 3,   // Depth at which to start flattening\n  INDENT_SIZE: 20,        // Pixels per depth level\n  MAX_INDENT: 60          // Maximum indentation in pixels\n} as const;\n\n/**\n * Calculate the depth of a comment in the thread hierarchy\n */\nexport function calculateCommentDepth(comment: Comment, allComments: Comment[]): number {\n  let depth = 0;\n  let currentComment = comment;\n\n  // Traverse up the parent chain to calculate depth\n  while (currentComment.parent_comment_id) {\n    depth++;\n    const parentComment = allComments.find(c => c.comment_id === currentComment.parent_comment_id);\n    if (!parentComment) break;\n    currentComment = parentComment;\n    \n    // Safety check to prevent infinite loops\n    if (depth > 10) {\n      console.warn('Comment depth calculation exceeded safety limit');\n      break;\n    }\n  }\n\n  return depth;\n}\n\n/**\n * Check if a comment can have replies based on depth limits\n */\nexport function canCommentHaveReplies(depth: number): boolean {\n  return depth < COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n}\n\n/**\n * Check if a comment should show the reply button\n */\nexport function shouldShowReplyButton(depth: number): boolean {\n  return canCommentHaveReplies(depth);\n}\n\n/**\n * Check if a comment thread should be flattened\n */\nexport function shouldFlattenThread(depth: number): boolean {\n  return depth >= COMMENT_DEPTH_CONFIG.FLATTEN_THRESHOLD;\n}\n\n/**\n * Calculate visual indentation for a comment based on depth\n */\nexport function calculateIndentation(depth: number): number {\n  const visualDepth = Math.min(depth, COMMENT_DEPTH_CONFIG.MAX_VISUAL_DEPTH);\n  const indentation = visualDepth * COMMENT_DEPTH_CONFIG.INDENT_SIZE;\n  return Math.min(indentation, COMMENT_DEPTH_CONFIG.MAX_INDENT);\n}\n\n/**\n * Get the appropriate parent comment ID for a new reply\n * If depth limit is reached, redirect to the root comment\n */\nexport function getReplyParentId(\n  targetComment: Comment, \n  allComments: Comment[]\n): number {\n  const currentDepth = calculateCommentDepth(targetComment, allComments);\n  \n  if (currentDepth < COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    // Normal reply - use the target comment as parent\n    return targetComment.comment_id;\n  } else {\n    // Depth limit reached - find the root comment of this thread\n    let rootComment = targetComment;\n    while (rootComment.parent_comment_id) {\n      const parentComment = allComments.find(c => c.comment_id === rootComment.parent_comment_id);\n      if (!parentComment) break;\n      rootComment = parentComment;\n    }\n    return rootComment.comment_id;\n  }\n}\n\n/**\n * Build a hierarchical comment tree with depth limits\n */\nexport function buildCommentTree(comments: Comment[]): Comment[] {\n  const commentMap = new Map<number, Comment>();\n  const rootComments: Comment[] = [];\n\n  // Create a map of all comments\n  comments.forEach(comment => {\n    commentMap.set(comment.comment_id, { ...comment, replies: [] });\n  });\n\n  // Build the tree structure\n  comments.forEach(comment => {\n    const commentWithReplies = commentMap.get(comment.comment_id)!;\n    \n    if (comment.parent_comment_id) {\n      const parent = commentMap.get(comment.parent_comment_id);\n      if (parent) {\n        const parentDepth = calculateCommentDepth(parent, comments);\n        const currentDepth = parentDepth + 1;\n        \n        // Only add as reply if within depth limits\n        if (currentDepth <= COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n          parent.replies = parent.replies || [];\n          parent.replies.push(commentWithReplies);\n        } else {\n          // If depth limit exceeded, add as root comment\n          rootComments.push(commentWithReplies);\n        }\n      } else {\n        // Parent not found, add as root comment\n        rootComments.push(commentWithReplies);\n      }\n    } else {\n      // Top-level comment\n      rootComments.push(commentWithReplies);\n    }\n  });\n\n  return rootComments;\n}\n\n/**\n * Get a user-friendly message for depth limit reached\n */\nexport function getDepthLimitMessage(depth: number): string {\n  if (depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    return \"Reply depth limit reached. Your reply will be added as a new comment in this thread.\";\n  }\n  return \"\";\n}\n\n/**\n * Get thread continuation message\n */\nexport function getThreadContinuationMessage(commentCount: number): string {\n  if (commentCount > 0) {\n    return `Continue this thread (${commentCount} more ${commentCount === 1 ? 'reply' : 'replies'})`;\n  }\n  return \"Continue this thread\";\n}\n\n/**\n * Validate comment depth before creation (for backend validation)\n */\nexport function validateCommentDepth(\n  parentCommentId: number | null,\n  allComments: Comment[]\n): { isValid: boolean; message?: string; suggestedParentId?: number } {\n  if (!parentCommentId) {\n    // Top-level comment is always valid\n    return { isValid: true };\n  }\n\n  const parentComment = allComments.find(c => c.comment_id === parentCommentId);\n  if (!parentComment) {\n    return { \n      isValid: false, \n      message: \"Parent comment not found\" \n    };\n  }\n\n  const parentDepth = calculateCommentDepth(parentComment, allComments);\n  const newCommentDepth = parentDepth + 1;\n\n  if (newCommentDepth > COMMENT_DEPTH_CONFIG.MAX_DEPTH) {\n    const suggestedParentId = getReplyParentId(parentComment, allComments);\n    return {\n      isValid: false,\n      message: `Comment depth limit (${COMMENT_DEPTH_CONFIG.MAX_DEPTH + 1} levels) exceeded. Reply will be added to thread root.`,\n      suggestedParentId\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Get CSS classes for comment depth styling\n */\nexport function getCommentDepthClasses(depth: number): string[] {\n  const classes = [`comment-depth-${depth}`];\n  \n  if (depth === 0) {\n    classes.push('comment-root');\n  } else if (depth === 1) {\n    classes.push('comment-reply');\n  } else if (depth >= 2) {\n    classes.push('comment-deep-reply');\n  }\n\n  if (shouldFlattenThread(depth)) {\n    classes.push('comment-flattened');\n  }\n\n  return classes;\n}\n\n/**\n * Format depth information for debugging\n */\nexport function formatDepthInfo(comment: Comment, allComments: Comment[]): string {\n  const depth = calculateCommentDepth(comment, allComments);\n  const canReply = canCommentHaveReplies(depth);\n  const shouldFlatten = shouldFlattenThread(depth);\n  \n  return `Depth: ${depth}, CanReply: ${canReply}, ShouldFlatten: ${shouldFlatten}`;\n}\n\nexport default {\n  COMMENT_DEPTH_CONFIG,\n  calculateCommentDepth,\n  canCommentHaveReplies,\n  shouldShowReplyButton,\n  shouldFlattenThread,\n  calculateIndentation,\n  getReplyParentId,\n  buildCommentTree,\n  getDepthLimitMessage,\n  getThreadContinuationMessage,\n  validateCommentDepth,\n  getCommentDepthClasses,\n  formatDepthInfo\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA,OAAO,MAAMA,oBAAoB,GAAG;EAClCC,SAAS,EAAE,CAAC;EAAY;EACxBC,gBAAgB,EAAE,CAAC;EAAK;EACxBC,iBAAiB,EAAE,CAAC;EAAI;EACxBC,WAAW,EAAE,EAAE;EAAS;EACxBC,UAAU,EAAE,EAAE,CAAU;AAC1B,CAAU;;AAEV;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,OAAgB,EAAEC,WAAsB,EAAU;EACtF,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,cAAc,GAAGH,OAAO;;EAE5B;EACA,OAAOG,cAAc,CAACC,iBAAiB,EAAE;IACvCF,KAAK,EAAE;IACP,MAAMG,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKL,cAAc,CAACC,iBAAiB,CAAC;IAC9F,IAAI,CAACC,aAAa,EAAE;IACpBF,cAAc,GAAGE,aAAa;;IAE9B;IACA,IAAIH,KAAK,GAAG,EAAE,EAAE;MACdO,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;MAC/D;IACF;EACF;EAEA,OAAOR,KAAK;AACd;;AAEA;AACA;AACA;AACA,OAAO,SAASS,qBAAqBA,CAACT,KAAa,EAAW;EAC5D,OAAOA,KAAK,GAAGT,oBAAoB,CAACC,SAAS;AAC/C;;AAEA;AACA;AACA;AACA,OAAO,SAASkB,qBAAqBA,CAACV,KAAa,EAAW;EAC5D,OAAOS,qBAAqB,CAACT,KAAK,CAAC;AACrC;;AAEA;AACA;AACA;AACA,OAAO,SAASW,mBAAmBA,CAACX,KAAa,EAAW;EAC1D,OAAOA,KAAK,IAAIT,oBAAoB,CAACG,iBAAiB;AACxD;;AAEA;AACA;AACA;AACA,OAAO,SAASkB,oBAAoBA,CAACZ,KAAa,EAAU;EAC1D,MAAMa,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACf,KAAK,EAAET,oBAAoB,CAACE,gBAAgB,CAAC;EAC1E,MAAMuB,WAAW,GAAGH,WAAW,GAAGtB,oBAAoB,CAACI,WAAW;EAClE,OAAOmB,IAAI,CAACC,GAAG,CAACC,WAAW,EAAEzB,oBAAoB,CAACK,UAAU,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASqB,gBAAgBA,CAC9BC,aAAsB,EACtBnB,WAAsB,EACd;EACR,MAAMoB,YAAY,GAAGtB,qBAAqB,CAACqB,aAAa,EAAEnB,WAAW,CAAC;EAEtE,IAAIoB,YAAY,GAAG5B,oBAAoB,CAACC,SAAS,EAAE;IACjD;IACA,OAAO0B,aAAa,CAACZ,UAAU;EACjC,CAAC,MAAM;IACL;IACA,IAAIc,WAAW,GAAGF,aAAa;IAC/B,OAAOE,WAAW,CAAClB,iBAAiB,EAAE;MACpC,MAAMC,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKc,WAAW,CAAClB,iBAAiB,CAAC;MAC3F,IAAI,CAACC,aAAa,EAAE;MACpBiB,WAAW,GAAGjB,aAAa;IAC7B;IACA,OAAOiB,WAAW,CAACd,UAAU;EAC/B;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASe,gBAAgBA,CAACC,QAAmB,EAAa;EAC/D,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAkB,CAAC;EAC7C,MAAMC,YAAuB,GAAG,EAAE;;EAElC;EACAH,QAAQ,CAACI,OAAO,CAAC5B,OAAO,IAAI;IAC1ByB,UAAU,CAACI,GAAG,CAAC7B,OAAO,CAACQ,UAAU,EAAE;MAAE,GAAGR,OAAO;MAAE8B,OAAO,EAAE;IAAG,CAAC,CAAC;EACjE,CAAC,CAAC;;EAEF;EACAN,QAAQ,CAACI,OAAO,CAAC5B,OAAO,IAAI;IAC1B,MAAM+B,kBAAkB,GAAGN,UAAU,CAACO,GAAG,CAAChC,OAAO,CAACQ,UAAU,CAAE;IAE9D,IAAIR,OAAO,CAACI,iBAAiB,EAAE;MAC7B,MAAM6B,MAAM,GAAGR,UAAU,CAACO,GAAG,CAAChC,OAAO,CAACI,iBAAiB,CAAC;MACxD,IAAI6B,MAAM,EAAE;QACV,MAAMC,WAAW,GAAGnC,qBAAqB,CAACkC,MAAM,EAAET,QAAQ,CAAC;QAC3D,MAAMH,YAAY,GAAGa,WAAW,GAAG,CAAC;;QAEpC;QACA,IAAIb,YAAY,IAAI5B,oBAAoB,CAACC,SAAS,EAAE;UAClDuC,MAAM,CAACH,OAAO,GAAGG,MAAM,CAACH,OAAO,IAAI,EAAE;UACrCG,MAAM,CAACH,OAAO,CAACK,IAAI,CAACJ,kBAAkB,CAAC;QACzC,CAAC,MAAM;UACL;UACAJ,YAAY,CAACQ,IAAI,CAACJ,kBAAkB,CAAC;QACvC;MACF,CAAC,MAAM;QACL;QACAJ,YAAY,CAACQ,IAAI,CAACJ,kBAAkB,CAAC;MACvC;IACF,CAAC,MAAM;MACL;MACAJ,YAAY,CAACQ,IAAI,CAACJ,kBAAkB,CAAC;IACvC;EACF,CAAC,CAAC;EAEF,OAAOJ,YAAY;AACrB;;AAEA;AACA;AACA;AACA,OAAO,SAASS,oBAAoBA,CAAClC,KAAa,EAAU;EAC1D,IAAIA,KAAK,IAAIT,oBAAoB,CAACC,SAAS,EAAE;IAC3C,OAAO,sFAAsF;EAC/F;EACA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACA,OAAO,SAAS2C,4BAA4BA,CAACC,YAAoB,EAAU;EACzE,IAAIA,YAAY,GAAG,CAAC,EAAE;IACpB,OAAO,yBAAyBA,YAAY,SAASA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,GAAG;EAClG;EACA,OAAO,sBAAsB;AAC/B;;AAEA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAClCC,eAA8B,EAC9BvC,WAAsB,EAC8C;EACpE,IAAI,CAACuC,eAAe,EAAE;IACpB;IACA,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;EAC1B;EAEA,MAAMpC,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKgC,eAAe,CAAC;EAC7E,IAAI,CAACnC,aAAa,EAAE;IAClB,OAAO;MACLoC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH;EAEA,MAAMR,WAAW,GAAGnC,qBAAqB,CAACM,aAAa,EAAEJ,WAAW,CAAC;EACrE,MAAM0C,eAAe,GAAGT,WAAW,GAAG,CAAC;EAEvC,IAAIS,eAAe,GAAGlD,oBAAoB,CAACC,SAAS,EAAE;IACpD,MAAMkD,iBAAiB,GAAGzB,gBAAgB,CAACd,aAAa,EAAEJ,WAAW,CAAC;IACtE,OAAO;MACLwC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,wBAAwBjD,oBAAoB,CAACC,SAAS,GAAG,CAAC,wDAAwD;MAC3HkD;IACF,CAAC;EACH;EAEA,OAAO;IAAEH,OAAO,EAAE;EAAK,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,OAAO,SAASI,sBAAsBA,CAAC3C,KAAa,EAAY;EAC9D,MAAM4C,OAAO,GAAG,CAAC,iBAAiB5C,KAAK,EAAE,CAAC;EAE1C,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf4C,OAAO,CAACX,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC,MAAM,IAAIjC,KAAK,KAAK,CAAC,EAAE;IACtB4C,OAAO,CAACX,IAAI,CAAC,eAAe,CAAC;EAC/B,CAAC,MAAM,IAAIjC,KAAK,IAAI,CAAC,EAAE;IACrB4C,OAAO,CAACX,IAAI,CAAC,oBAAoB,CAAC;EACpC;EAEA,IAAItB,mBAAmB,CAACX,KAAK,CAAC,EAAE;IAC9B4C,OAAO,CAACX,IAAI,CAAC,mBAAmB,CAAC;EACnC;EAEA,OAAOW,OAAO;AAChB;;AAEA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAC/C,OAAgB,EAAEC,WAAsB,EAAU;EAChF,MAAMC,KAAK,GAAGH,qBAAqB,CAACC,OAAO,EAAEC,WAAW,CAAC;EACzD,MAAM+C,QAAQ,GAAGrC,qBAAqB,CAACT,KAAK,CAAC;EAC7C,MAAM+C,aAAa,GAAGpC,mBAAmB,CAACX,KAAK,CAAC;EAEhD,OAAO,UAAUA,KAAK,eAAe8C,QAAQ,oBAAoBC,aAAa,EAAE;AAClF;AAEA,eAAe;EACbxD,oBAAoB;EACpBM,qBAAqB;EACrBY,qBAAqB;EACrBC,qBAAqB;EACrBC,mBAAmB;EACnBC,oBAAoB;EACpBK,gBAAgB;EAChBI,gBAAgB;EAChBa,oBAAoB;EACpBC,4BAA4B;EAC5BE,oBAAoB;EACpBM,sBAAsB;EACtBE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}