{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m8 2 1.88 1.88\",\n  key: \"fmnt4t\"\n}], [\"path\", {\n  d: \"M14.12 3.88 16 2\",\n  key: \"qol33r\"\n}], [\"path\", {\n  d: \"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1\",\n  key: \"d7y7pr\"\n}], [\"path\", {\n  d: \"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6\",\n  key: \"xs1cw7\"\n}], [\"path\", {\n  d: \"M12 20v-9\",\n  key: \"1qisl0\"\n}], [\"path\", {\n  d: \"M6.53 9C4.6 8.8 3 7.1 3 5\",\n  key: \"32zzws\"\n}], [\"path\", {\n  d: \"M6 13H2\",\n  key: \"82j7cp\"\n}], [\"path\", {\n  d: \"M3 21c0-2.1 1.7-3.9 3.8-4\",\n  key: \"4p0ekp\"\n}], [\"path\", {\n  d: \"M20.97 5c0 2.1-1.6 3.8-3.5 4\",\n  key: \"18gb23\"\n}], [\"path\", {\n  d: \"M22 13h-4\",\n  key: \"1jl80f\"\n}], [\"path\", {\n  d: \"M17.2 17c2.1.1 3.8 1.9 3.8 4\",\n  key: \"k3fwyw\"\n}]];\nconst Bug = createLucideIcon(\"bug\", __iconNode);\nexport { __iconNode, Bug as default };\n//# sourceMappingURL=bug.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}