{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { calendarService } from '../services';\n// Hook for managing calendar events\nexport const useCalendar = initialDate => {\n  _s();\n  const [events, setEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [currentDate, setCurrentDate] = useState(initialDate || new Date());\n  const [view, setView] = useState('month');\n  const [calendarData, setCalendarData] = useState({\n    events: {}\n  });\n  const fetchCalendarData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const year = currentDate.getFullYear();\n      const month = view === 'month' ? currentDate.getMonth() + 1 : undefined;\n\n      // console.log(`📅 Fetching calendar data for ${year}${month ? `-${month}` : ''}...`);\n      const response = await calendarService.getCalendarView(year, month);\n      if (response.success && response.data) {\n        setCalendarData(response.data);\n        // Convert grouped events to flat array for easier manipulation\n        const flatEvents = Object.values(response.data.events).flat();\n        setEvents(flatEvents);\n        // console.log(`✅ Calendar data loaded: ${flatEvents.length} events`);\n      } else {\n        const errorMsg = response.message || 'Failed to fetch calendar data';\n        // console.warn('⚠️ Calendar response not successful:', response);\n        setError(errorMsg);\n      }\n    } catch (err) {\n      // console.error('❌ Error fetching calendar data:', err);\n\n      let errorMessage = 'An error occurred while fetching calendar data';\n      if (err.message.includes('Network connection failed')) {\n        errorMessage = 'Unable to connect to server. Please check your connection and try again.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentDate, view]);\n  const refresh = useCallback(async () => {\n    await fetchCalendarData();\n  }, [fetchCalendarData]);\n  const createEvent = useCallback(async data => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.createEvent(data);\n      if (response.success) {\n        // Refresh calendar data to get the new event\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to create event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while creating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const updateEvent = useCallback(async (id, data) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.updateEvent(id, data);\n      if (response.success && response.data) {\n        // Refresh the calendar data to ensure multi-day events are properly handled\n        // This is more reliable than trying to manually update the complex calendar state\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to update event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while updating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const deleteEvent = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.deleteEvent(id);\n      if (response.success) {\n        // Refresh the calendar data to ensure consistency\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to delete event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while deleting event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const getEventsForDate = useCallback(date => {\n    // Format date manually to avoid timezone issues\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const dateKey = `${year}-${month}-${day}`;\n    return calendarData.events[dateKey] || [];\n  }, [calendarData]);\n  const getEventsForDateRange = useCallback(async (startDate, endDate) => {\n    try {\n      // Format dates manually to avoid timezone issues\n      const formatDate = date => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n      const start = formatDate(startDate);\n      const end = formatDate(endDate);\n      const response = await calendarService.getEventsByDateRange(start, end);\n      if (response.success && response.data) {\n        return response.data.events;\n      } else {\n        throw new Error(response.message || 'Failed to fetch events for date range');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching events for date range');\n      return [];\n    }\n  }, []);\n  useEffect(() => {\n    fetchCalendarData();\n  }, [fetchCalendarData]);\n  return {\n    events,\n    loading,\n    error,\n    currentDate,\n    view,\n    calendarData,\n    setCurrentDate,\n    setView,\n    refresh,\n    createEvent,\n    updateEvent,\n    deleteEvent,\n    getEventsForDate,\n    getEventsForDateRange\n  };\n};\n\n// Hook for managing holiday types\n_s(useCalendar, \"2vA0Qb3Hd7A09pRA30+K+g24wJk=\");\nexport const useHolidayTypes = () => {\n  _s2();\n  const [holidayTypes, setHolidayTypes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const fetchHolidayTypes = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.getHolidayTypes();\n      if (response.success && response.data) {\n        setHolidayTypes(response.data.holidayTypes);\n      } else {\n        setError(response.message || 'Failed to fetch holiday types');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching holiday types');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  const refresh = useCallback(async () => {\n    await fetchHolidayTypes();\n  }, [fetchHolidayTypes]);\n  useEffect(() => {\n    fetchHolidayTypes();\n  }, [fetchHolidayTypes]);\n  return {\n    holidayTypes,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Utility functions for calendar operations\n_s2(useHolidayTypes, \"i1uHOCFg0w3JMO5BLv9+yI+x/VM=\");\nexport const getCalendarDays = (year, month) => {\n  const firstDay = new Date(year, month, 1);\n  const lastDay = new Date(year, month + 1, 0);\n  const startDate = new Date(firstDay);\n  const endDate = new Date(lastDay);\n\n  // Adjust to start from Sunday (or Monday based on preference)\n  startDate.setDate(startDate.getDate() - startDate.getDay());\n\n  // Adjust to end on Saturday (or Sunday based on preference)\n  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n  const days = [];\n  const currentDate = new Date(startDate);\n  while (currentDate <= endDate) {\n    days.push(new Date(currentDate));\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n  return days;\n};\nexport const isToday = date => {\n  const today = new Date();\n  return date.toDateString() === today.toDateString();\n};\nexport const isSameMonth = (date, month, year) => {\n  return date.getMonth() === month && date.getFullYear() === year;\n};\nexport const formatDateForDisplay = date => {\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n};\nexport const formatTimeForDisplay = date => {\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nexport const getMonthName = month => {\n  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n  return monthNames[month];\n};\nexport const getDayName = day => {\n  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n  return dayNames[day];\n};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "calendarService", "useCalendar", "initialDate", "_s", "events", "setEvents", "loading", "setLoading", "error", "setError", "currentDate", "setCurrentDate", "Date", "view", "<PERSON><PERSON><PERSON><PERSON>", "calendarData", "setCalendarData", "fetchCalendarData", "undefined", "year", "getFullYear", "month", "getMonth", "response", "getCalendarView", "success", "data", "flatEvents", "Object", "values", "flat", "errorMsg", "message", "err", "errorMessage", "includes", "refresh", "createEvent", "Error", "updateEvent", "id", "deleteEvent", "getEventsForDate", "date", "String", "padStart", "day", "getDate", "<PERSON><PERSON><PERSON>", "getEventsForDateRange", "startDate", "endDate", "formatDate", "start", "end", "getEventsByDateRange", "useHolidayTypes", "_s2", "holidayTypes", "setHolidayTypes", "fetchHolidayTypes", "getHolidayTypes", "getCalendarDays", "firstDay", "lastDay", "setDate", "getDay", "days", "push", "isToday", "today", "toDateString", "isSameMonth", "formatDateForDisplay", "toLocaleDateString", "weekday", "formatTimeForDisplay", "toLocaleTimeString", "hour", "minute", "getMonthName", "monthNames", "getDayName", "dayNames"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useCalendar.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { calendarService } from '../services';\nimport type {\n  CalendarEvent,\n  CreateEventData,\n  UpdateEventData,\n  EventFilters,\n  HolidayType,\n  UseCalendarReturn,\n  UseHolidayTypesReturn,\n  CalendarViewResponse\n} from '../types/calendar.types';\n\n// Hook for managing calendar events\nexport const useCalendar = (initialDate?: Date): UseCalendarReturn => {\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [currentDate, setCurrentDate] = useState(initialDate || new Date());\n  const [view, setView] = useState<'month' | 'week' | 'day'>('month');\n  const [calendarData, setCalendarData] = useState<CalendarViewResponse>({ events: {} });\n\n  const fetchCalendarData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const year = currentDate.getFullYear();\n      const month = view === 'month' ? currentDate.getMonth() + 1 : undefined;\n\n      // console.log(`📅 Fetching calendar data for ${year}${month ? `-${month}` : ''}...`);\n      const response = await calendarService.getCalendarView(year, month);\n\n      if (response.success && response.data) {\n        setCalendarData(response.data);\n        // Convert grouped events to flat array for easier manipulation\n        const flatEvents = Object.values(response.data.events).flat();\n        setEvents(flatEvents);\n        // console.log(`✅ Calendar data loaded: ${flatEvents.length} events`);\n      } else {\n        const errorMsg = response.message || 'Failed to fetch calendar data';\n        // console.warn('⚠️ Calendar response not successful:', response);\n        setError(errorMsg);\n      }\n    } catch (err: any) {\n      // console.error('❌ Error fetching calendar data:', err);\n\n      let errorMessage = 'An error occurred while fetching calendar data';\n      if (err.message.includes('Network connection failed')) {\n        errorMessage = 'Unable to connect to server. Please check your connection and try again.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentDate, view]);\n\n  const refresh = useCallback(async () => {\n    await fetchCalendarData();\n  }, [fetchCalendarData]);\n\n  const createEvent = useCallback(async (data: CreateEventData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.createEvent(data);\n      \n      if (response.success) {\n        // Refresh calendar data to get the new event\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to create event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const updateEvent = useCallback(async (id: number, data: UpdateEventData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.updateEvent(id, data);\n      \n      if (response.success && response.data) {\n        // Refresh the calendar data to ensure multi-day events are properly handled\n        // This is more reliable than trying to manually update the complex calendar state\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to update event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while updating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const deleteEvent = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.deleteEvent(id);\n      \n      if (response.success) {\n        // Refresh the calendar data to ensure consistency\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to delete event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while deleting event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const getEventsForDate = useCallback((date: Date): CalendarEvent[] => {\n    // Format date manually to avoid timezone issues\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const dateKey = `${year}-${month}-${day}`;\n    return calendarData.events[dateKey] || [];\n  }, [calendarData]);\n\n  const getEventsForDateRange = useCallback(async (startDate: Date, endDate: Date): Promise<CalendarEvent[]> => {\n    try {\n      // Format dates manually to avoid timezone issues\n      const formatDate = (date: Date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const start = formatDate(startDate);\n      const end = formatDate(endDate);\n\n      const response = await calendarService.getEventsByDateRange(start, end);\n\n      if (response.success && response.data) {\n        return response.data.events;\n      } else {\n        throw new Error(response.message || 'Failed to fetch events for date range');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching events for date range');\n      return [];\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchCalendarData();\n  }, [fetchCalendarData]);\n\n  return {\n    events,\n    loading,\n    error,\n    currentDate,\n    view,\n    calendarData,\n    setCurrentDate,\n    setView,\n    refresh,\n    createEvent,\n    updateEvent,\n    deleteEvent,\n    getEventsForDate,\n    getEventsForDateRange\n  };\n};\n\n// Hook for managing holiday types\nexport const useHolidayTypes = (): UseHolidayTypesReturn => {\n  const [holidayTypes, setHolidayTypes] = useState<HolidayType[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const fetchHolidayTypes = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.getHolidayTypes();\n      \n      if (response.success && response.data) {\n        setHolidayTypes(response.data.holidayTypes);\n      } else {\n        setError(response.message || 'Failed to fetch holiday types');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching holiday types');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const refresh = useCallback(async () => {\n    await fetchHolidayTypes();\n  }, [fetchHolidayTypes]);\n\n  useEffect(() => {\n    fetchHolidayTypes();\n  }, [fetchHolidayTypes]);\n\n  return {\n    holidayTypes,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Utility functions for calendar operations\nexport const getCalendarDays = (year: number, month: number): Date[] => {\n  const firstDay = new Date(year, month, 1);\n  const lastDay = new Date(year, month + 1, 0);\n  const startDate = new Date(firstDay);\n  const endDate = new Date(lastDay);\n  \n  // Adjust to start from Sunday (or Monday based on preference)\n  startDate.setDate(startDate.getDate() - startDate.getDay());\n  \n  // Adjust to end on Saturday (or Sunday based on preference)\n  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n  \n  const days: Date[] = [];\n  const currentDate = new Date(startDate);\n  \n  while (currentDate <= endDate) {\n    days.push(new Date(currentDate));\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n  \n  return days;\n};\n\nexport const isToday = (date: Date): boolean => {\n  const today = new Date();\n  return date.toDateString() === today.toDateString();\n};\n\nexport const isSameMonth = (date: Date, month: number, year: number): boolean => {\n  return date.getMonth() === month && date.getFullYear() === year;\n};\n\nexport const formatDateForDisplay = (date: Date): string => {\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n};\n\nexport const formatTimeForDisplay = (date: Date): string => {\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\nexport const getMonthName = (month: number): string => {\n  const monthNames = [\n    'January', 'February', 'March', 'April', 'May', 'June',\n    'July', 'August', 'September', 'October', 'November', 'December'\n  ];\n  return monthNames[month];\n};\n\nexport const getDayName = (day: number): string => {\n  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n  return dayNames[day];\n};\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,eAAe,QAAQ,aAAa;AAY7C;AACA,OAAO,MAAMC,WAAW,GAAIC,WAAkB,IAAwB;EAAAC,EAAA;EACpE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAkB,EAAE,CAAC;EACzD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAACK,WAAW,IAAI,IAAIU,IAAI,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAA2B,OAAO,CAAC;EACnE,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAuB;IAAEO,MAAM,EAAE,CAAC;EAAE,CAAC,CAAC;EAEtF,MAAMa,iBAAiB,GAAGlB,WAAW,CAAC,YAAY;IAChD,IAAI;MACFQ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMC,IAAI,GAAGT,WAAW,CAACU,WAAW,CAAC,CAAC;MACtC,MAAMC,KAAK,GAAGR,IAAI,KAAK,OAAO,GAAGH,WAAW,CAACY,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGJ,SAAS;;MAEvE;MACA,MAAMK,QAAQ,GAAG,MAAMvB,eAAe,CAACwB,eAAe,CAACL,IAAI,EAAEE,KAAK,CAAC;MAEnE,IAAIE,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCV,eAAe,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC9B;QACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACN,QAAQ,CAACG,IAAI,CAACtB,MAAM,CAAC,CAAC0B,IAAI,CAAC,CAAC;QAC7DzB,SAAS,CAACsB,UAAU,CAAC;QACrB;MACF,CAAC,MAAM;QACL,MAAMI,QAAQ,GAAGR,QAAQ,CAACS,OAAO,IAAI,+BAA+B;QACpE;QACAvB,QAAQ,CAACsB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjB;;MAEA,IAAIC,YAAY,GAAG,gDAAgD;MACnE,IAAID,GAAG,CAACD,OAAO,CAACG,QAAQ,CAAC,2BAA2B,CAAC,EAAE;QACrDD,YAAY,GAAG,0EAA0E;MAC3F,CAAC,MAAM,IAAID,GAAG,CAACD,OAAO,EAAE;QACtBE,YAAY,GAAGD,GAAG,CAACD,OAAO;MAC5B;MAEAvB,QAAQ,CAACyB,YAAY,CAAC;IACxB,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACG,WAAW,EAAEG,IAAI,CAAC,CAAC;EAEvB,MAAMuB,OAAO,GAAGrC,WAAW,CAAC,YAAY;IACtC,MAAMkB,iBAAiB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMoB,WAAW,GAAGtC,WAAW,CAAC,MAAO2B,IAAqB,IAAK;IAC/D,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMK,QAAQ,GAAG,MAAMvB,eAAe,CAACqC,WAAW,CAACX,IAAI,CAAC;MAExD,IAAIH,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMR,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIqB,KAAK,CAACf,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMsB,WAAW,GAAGxC,WAAW,CAAC,OAAOyC,EAAU,EAAEd,IAAqB,KAAK;IAC3E,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMK,QAAQ,GAAG,MAAMvB,eAAe,CAACuC,WAAW,CAACC,EAAE,EAAEd,IAAI,CAAC;MAE5D,IAAIH,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC;QACA;QACA,MAAMT,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIqB,KAAK,CAACf,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMwB,WAAW,GAAG1C,WAAW,CAAC,MAAOyC,EAAU,IAAK;IACpD,IAAI;MACFjC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMK,QAAQ,GAAG,MAAMvB,eAAe,CAACyC,WAAW,CAACD,EAAE,CAAC;MAEtD,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMR,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIqB,KAAK,CAACf,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMC,GAAG;IACX,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMyB,gBAAgB,GAAG3C,WAAW,CAAE4C,IAAU,IAAsB;IACpE;IACA,MAAMxB,IAAI,GAAGwB,IAAI,CAACvB,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGuB,MAAM,CAACD,IAAI,CAACrB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMG,OAAO,GAAG,GAAG7B,IAAI,IAAIE,KAAK,IAAIyB,GAAG,EAAE;IACzC,OAAO/B,YAAY,CAACX,MAAM,CAAC4C,OAAO,CAAC,IAAI,EAAE;EAC3C,CAAC,EAAE,CAACjC,YAAY,CAAC,CAAC;EAElB,MAAMkC,qBAAqB,GAAGlD,WAAW,CAAC,OAAOmD,SAAe,EAAEC,OAAa,KAA+B;IAC5G,IAAI;MACF;MACA,MAAMC,UAAU,GAAIT,IAAU,IAAK;QACjC,MAAMxB,IAAI,GAAGwB,IAAI,CAACvB,WAAW,CAAC,CAAC;QAC/B,MAAMC,KAAK,GAAGuB,MAAM,CAACD,IAAI,CAACrB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAG,GAAGF,MAAM,CAACD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,OAAO,GAAG1B,IAAI,IAAIE,KAAK,IAAIyB,GAAG,EAAE;MAClC,CAAC;MAED,MAAMO,KAAK,GAAGD,UAAU,CAACF,SAAS,CAAC;MACnC,MAAMI,GAAG,GAAGF,UAAU,CAACD,OAAO,CAAC;MAE/B,MAAM5B,QAAQ,GAAG,MAAMvB,eAAe,CAACuD,oBAAoB,CAACF,KAAK,EAAEC,GAAG,CAAC;MAEvE,IAAI/B,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC,OAAOH,QAAQ,CAACG,IAAI,CAACtB,MAAM;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIkC,KAAK,CAACf,QAAQ,CAACS,OAAO,IAAI,uCAAuC,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,IAAI,wDAAwD,CAAC;MACjF,OAAO,EAAE;IACX;EACF,CAAC,EAAE,EAAE,CAAC;EAENlC,SAAS,CAAC,MAAM;IACdmB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,OAAO;IACLb,MAAM;IACNE,OAAO;IACPE,KAAK;IACLE,WAAW;IACXG,IAAI;IACJE,YAAY;IACZJ,cAAc;IACdG,OAAO;IACPsB,OAAO;IACPC,WAAW;IACXE,WAAW;IACXE,WAAW;IACXC,gBAAgB;IAChBO;EACF,CAAC;AACH,CAAC;;AAED;AAAA9C,EAAA,CA3KaF,WAAW;AA4KxB,OAAO,MAAMuD,eAAe,GAAGA,CAAA,KAA6B;EAAAC,GAAA;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAqB,CAAC;EAExD,MAAM+D,iBAAiB,GAAG7D,WAAW,CAAC,YAAY;IAChD,IAAI;MACFQ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMK,QAAQ,GAAG,MAAMvB,eAAe,CAAC6D,eAAe,CAAC,CAAC;MAExD,IAAItC,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCiC,eAAe,CAACpC,QAAQ,CAACG,IAAI,CAACgC,YAAY,CAAC;MAC7C,CAAC,MAAM;QACLjD,QAAQ,CAACc,QAAQ,CAACS,OAAO,IAAI,+BAA+B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,IAAI,gDAAgD,CAAC;IAC3E,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6B,OAAO,GAAGrC,WAAW,CAAC,YAAY;IACtC,MAAM6D,iBAAiB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB9D,SAAS,CAAC,MAAM;IACd8D,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,OAAO;IACLF,YAAY;IACZpD,OAAO;IACPE,KAAK;IACL4B;EACF,CAAC;AACH,CAAC;;AAED;AAAAqB,GAAA,CAxCaD,eAAe;AAyC5B,OAAO,MAAMM,eAAe,GAAGA,CAAC3C,IAAY,EAAEE,KAAa,KAAa;EACtE,MAAM0C,QAAQ,GAAG,IAAInD,IAAI,CAACO,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;EACzC,MAAM2C,OAAO,GAAG,IAAIpD,IAAI,CAACO,IAAI,EAAEE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5C,MAAM6B,SAAS,GAAG,IAAItC,IAAI,CAACmD,QAAQ,CAAC;EACpC,MAAMZ,OAAO,GAAG,IAAIvC,IAAI,CAACoD,OAAO,CAAC;;EAEjC;EACAd,SAAS,CAACe,OAAO,CAACf,SAAS,CAACH,OAAO,CAAC,CAAC,GAAGG,SAAS,CAACgB,MAAM,CAAC,CAAC,CAAC;;EAE3D;EACAf,OAAO,CAACc,OAAO,CAACd,OAAO,CAACJ,OAAO,CAAC,CAAC,IAAI,CAAC,GAAGI,OAAO,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;EAE3D,MAAMC,IAAY,GAAG,EAAE;EACvB,MAAMzD,WAAW,GAAG,IAAIE,IAAI,CAACsC,SAAS,CAAC;EAEvC,OAAOxC,WAAW,IAAIyC,OAAO,EAAE;IAC7BgB,IAAI,CAACC,IAAI,CAAC,IAAIxD,IAAI,CAACF,WAAW,CAAC,CAAC;IAChCA,WAAW,CAACuD,OAAO,CAACvD,WAAW,CAACqC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EAChD;EAEA,OAAOoB,IAAI;AACb,CAAC;AAED,OAAO,MAAME,OAAO,GAAI1B,IAAU,IAAc;EAC9C,MAAM2B,KAAK,GAAG,IAAI1D,IAAI,CAAC,CAAC;EACxB,OAAO+B,IAAI,CAAC4B,YAAY,CAAC,CAAC,KAAKD,KAAK,CAACC,YAAY,CAAC,CAAC;AACrD,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAC7B,IAAU,EAAEtB,KAAa,EAAEF,IAAY,KAAc;EAC/E,OAAOwB,IAAI,CAACrB,QAAQ,CAAC,CAAC,KAAKD,KAAK,IAAIsB,IAAI,CAACvB,WAAW,CAAC,CAAC,KAAKD,IAAI;AACjE,CAAC;AAED,OAAO,MAAMsD,oBAAoB,GAAI9B,IAAU,IAAa;EAC1D,OAAOA,IAAI,CAAC+B,kBAAkB,CAAC,OAAO,EAAE;IACtCC,OAAO,EAAE,MAAM;IACfxD,IAAI,EAAE,SAAS;IACfE,KAAK,EAAE,MAAM;IACbyB,GAAG,EAAE;EACP,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM8B,oBAAoB,GAAIjC,IAAU,IAAa;EAC1D,OAAOA,IAAI,CAACkC,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,YAAY,GAAI3D,KAAa,IAAa;EACrD,MAAM4D,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;EACD,OAAOA,UAAU,CAAC5D,KAAK,CAAC;AAC1B,CAAC;AAED,OAAO,MAAM6D,UAAU,GAAIpC,GAAW,IAAa;EACjD,MAAMqC,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC/F,OAAOA,QAAQ,CAACrC,GAAG,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}