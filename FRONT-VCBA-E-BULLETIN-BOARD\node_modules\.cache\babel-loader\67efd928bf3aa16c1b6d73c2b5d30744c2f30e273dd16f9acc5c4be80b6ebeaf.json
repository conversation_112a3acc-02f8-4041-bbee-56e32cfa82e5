{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"8\",\n  cy: \"8\",\n  r: \"6\",\n  key: \"3yglwk\"\n}], [\"path\", {\n  d: \"M18.09 10.37A6 6 0 1 1 10.34 18\",\n  key: \"t5s6rm\"\n}], [\"path\", {\n  d: \"M7 6h1v4\",\n  key: \"1obek4\"\n}], [\"path\", {\n  d: \"m16.71 13.88.7.71-2.82 2.82\",\n  key: \"1rbuyh\"\n}]];\nconst Coins = createLucideIcon(\"coins\", __iconNode);\nexport { __iconNode, Coins as default };\n//# sourceMappingURL=coins.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}