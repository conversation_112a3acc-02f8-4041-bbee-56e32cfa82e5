{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"path\", {\n  d: \"M8 8h8\",\n  key: \"1bis0t\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}], [\"path\", {\n  d: \"m13 17-5-1h1a4 4 0 0 0 0-8\",\n  key: \"nu2bwa\"\n}]];\nconst BadgeIndianRupee = createLucideIcon(\"badge-indian-rupee\", __iconNode);\nexport { __iconNode, BadgeIndianRupee as default };\n//# sourceMappingURL=badge-indian-rupee.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}