{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"15\",\n  y1: \"15\",\n  y2: \"9\",\n  key: \"1dfufj\"\n}]];\nconst SquareSlash = createLucideIcon(\"square-slash\", __iconNode);\nexport { __iconNode, SquareSlash as default };\n//# sourceMappingURL=square-slash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}