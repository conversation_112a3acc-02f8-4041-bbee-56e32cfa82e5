{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 7 6.82 21.18a2.83 2.83 0 0 1-3.99-.01a2.83 2.83 0 0 1 0-4L17 3\",\n  key: \"1ub6xw\"\n}], [\"path\", {\n  d: \"m16 2 6 6\",\n  key: \"1gw87d\"\n}], [\"path\", {\n  d: \"M12 16H4\",\n  key: \"1cjfip\"\n}]];\nconst TestTubeDiagonal = createLucideIcon(\"test-tube-diagonal\", __iconNode);\nexport { __iconNode, TestTubeDiagonal as default };\n//# sourceMappingURL=test-tube-diagonal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}