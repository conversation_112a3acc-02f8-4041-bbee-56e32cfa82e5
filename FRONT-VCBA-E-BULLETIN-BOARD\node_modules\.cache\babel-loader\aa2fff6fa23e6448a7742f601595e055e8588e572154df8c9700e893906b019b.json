{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\testing\\\\UnifiedAuthTest.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\nimport { unifiedStorage } from '../../utils/unifiedStorage';\nimport { unifiedAuthService } from '../../services/unified-auth.service';\n\n/**\n * Comprehensive test component for the unified authentication system\n * This component provides debugging tools and validation for the unified auth implementation\n *\n * NOTE: This only tests LOGIN functionality. Registration and OTP systems remain separate:\n * - Admin registration: Use AdminAuthContext and AdminAuthService\n * - Student registration: Use StudentAuthContext and StudentAuthService\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnifiedAuthTest = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    currentRole,\n    availableRoles,\n    login,\n    logout,\n    switchRole,\n    clearError\n  } = useUnifiedAuth();\n  const [testCredentials, setTestCredentials] = useState({\n    identifier: '',\n    password: '',\n    userType: 'auto'\n  });\n  const [testResults, setTestResults] = useState([]);\n  const [isRunningTests, setIsRunningTests] = useState(false);\n  const addTestResult = result => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);\n  };\n  const clearTestResults = () => {\n    setTestResults([]);\n  };\n\n  // Test storage functionality\n  const testStorage = () => {\n    addTestResult('🧪 Testing storage functionality...');\n    try {\n      const debugInfo = unifiedStorage.getDebugInfo();\n      addTestResult(`✅ Storage debug info: ${JSON.stringify(debugInfo, null, 2)}`);\n      const availableRoles = unifiedStorage.getAvailableRoles();\n      addTestResult(`✅ Available roles: ${availableRoles.join(', ') || 'None'}`);\n      const mostRecentRole = unifiedStorage.getMostRecentRole();\n      addTestResult(`✅ Most recent role: ${mostRecentRole || 'None'}`);\n    } catch (error) {\n      addTestResult(`❌ Storage test failed: ${error.message}`);\n    }\n  };\n\n  // Test service functionality\n  const testService = () => {\n    addTestResult('🧪 Testing service functionality...');\n    try {\n      const serviceDebugInfo = unifiedAuthService.getDebugInfo();\n      addTestResult(`✅ Service debug info: ${JSON.stringify(serviceDebugInfo, null, 2)}`);\n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      addTestResult(`✅ Service available roles: ${availableRoles.join(', ') || 'None'}`);\n      const currentRole = unifiedAuthService.getCurrentRole();\n      addTestResult(`✅ Service current role: ${currentRole || 'None'}`);\n    } catch (error) {\n      addTestResult(`❌ Service test failed: ${error.message}`);\n    }\n  };\n\n  // Test login functionality\n  const testLogin = async () => {\n    if (!testCredentials.identifier || !testCredentials.password) {\n      addTestResult('❌ Please enter test credentials');\n      return;\n    }\n    addTestResult('🧪 Testing login functionality...');\n    setIsRunningTests(true);\n    try {\n      await login({\n        identifier: testCredentials.identifier,\n        password: testCredentials.password,\n        userType: testCredentials.userType === 'auto' ? undefined : testCredentials.userType\n      });\n      addTestResult('✅ Login test successful');\n    } catch (error) {\n      addTestResult(`❌ Login test failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Test role switching\n  const testRoleSwitch = async targetRole => {\n    addTestResult(`🧪 Testing role switch to ${targetRole}...`);\n    setIsRunningTests(true);\n    try {\n      await switchRole(targetRole);\n      addTestResult(`✅ Role switch to ${targetRole} successful`);\n    } catch (error) {\n      addTestResult(`❌ Role switch to ${targetRole} failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Test logout functionality\n  const testLogout = async () => {\n    addTestResult('🧪 Testing logout functionality...');\n    setIsRunningTests(true);\n    try {\n      await logout();\n      addTestResult('✅ Logout test successful');\n    } catch (error) {\n      addTestResult(`❌ Logout test failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Run comprehensive test suite\n  const runComprehensiveTests = async () => {\n    addTestResult('🚀 Starting comprehensive test suite...');\n    clearTestResults();\n\n    // Test 1: Storage functionality\n    testStorage();\n\n    // Test 2: Service functionality\n    testService();\n\n    // Test 3: Context state validation\n    addTestResult('🧪 Testing context state...');\n    addTestResult(`✅ Context isAuthenticated: ${isAuthenticated}`);\n    addTestResult(`✅ Context isLoading: ${isLoading}`);\n    addTestResult(`✅ Context currentRole: ${currentRole || 'None'}`);\n    addTestResult(`✅ Context availableRoles: ${availableRoles.join(', ') || 'None'}`);\n    addTestResult(`✅ Context user: ${user ? user.email : 'None'}`);\n    addTestResult(`✅ Context error: ${error || 'None'}`);\n    addTestResult('🎉 Comprehensive test suite completed');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"unified-auth-test\",\n    style: {\n      padding: '2rem',\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        marginBottom: '2rem',\n        color: '#1f2937'\n      },\n      children: \"\\uD83E\\uDDEA Unified Authentication Test Suite\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        padding: '1rem',\n        backgroundColor: '#f3f4f6',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#374151'\n        },\n        children: \"Current Authentication State\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Authenticated:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), \" \", isAuthenticated ? '✅ Yes' : '❌ No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Loading:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), \" \", isLoading ? '⏳ Yes' : '✅ No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Current Role:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), \" \", currentRole || 'None']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Available Roles:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), \" \", availableRoles.join(', ') || 'None']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"User Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), \" \", (user === null || user === void 0 ? void 0 : user.email) || 'None']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Error:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), \" \", error || 'None']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        padding: '1rem',\n        backgroundColor: '#f9fafb',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#374151'\n        },\n        children: \"Test Credentials\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Email or Student Number\",\n          value: testCredentials.identifier,\n          onChange: e => setTestCredentials(prev => ({\n            ...prev,\n            identifier: e.target.value\n          })),\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          placeholder: \"Password\",\n          value: testCredentials.password,\n          onChange: e => setTestCredentials(prev => ({\n            ...prev,\n            password: e.target.value\n          })),\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: testCredentials.userType,\n          onChange: e => setTestCredentials(prev => ({\n            ...prev,\n            userType: e.target.value\n          })),\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"auto\",\n            children: \"Auto-detect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"admin\",\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"student\",\n            children: \"Student\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        padding: '1rem',\n        backgroundColor: '#f0f9ff',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#374151'\n        },\n        children: \"Test Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: runComprehensiveTests,\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDE80 Run All Tests\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testStorage,\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#10b981',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDDC4\\uFE0F Test Storage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testService,\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#8b5cf6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\u2699\\uFE0F Test Service\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testLogin,\n          disabled: isRunningTests || !testCredentials.identifier || !testCredentials.password,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#f59e0b',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDD10 Test Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), availableRoles.includes('admin') && currentRole !== 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => testRoleSwitch('admin'),\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#ef4444',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC Switch to Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), availableRoles.includes('student') && currentRole !== 'student' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => testRoleSwitch('student'),\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#06b6d4',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83C\\uDF93 Switch to Student\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testLogout,\n          disabled: isRunningTests,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#6b7280',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDEAA Test Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearError,\n          disabled: !error,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#84cc16',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83E\\uDDF9 Clear Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearTestResults,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#64748b',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: \"\\uD83D\\uDDD1\\uFE0F Clear Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        backgroundColor: '#1f2937',\n        borderRadius: '8px',\n        color: '#f9fafb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#f3f4f6'\n        },\n        children: \"Test Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '400px',\n          overflowY: 'auto',\n          fontFamily: 'monospace',\n          fontSize: '0.875rem'\n        },\n        children: testResults.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#9ca3af'\n          },\n          children: \"No test results yet. Run some tests to see results here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this) : testResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.25rem',\n            whiteSpace: 'pre-wrap'\n          },\n          children: result\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedAuthTest, \"817pYW8pCTQDmzNurD3tm47Mwiw=\", false, function () {\n  return [useUnifiedAuth];\n});\n_c = UnifiedAuthTest;\nexport default UnifiedAuthTest;\nvar _c;\n$RefreshReg$(_c, \"UnifiedAuthTest\");", "map": {"version": 3, "names": ["React", "useState", "useUnifiedAuth", "unifiedStorage", "unifiedAuthService", "jsxDEV", "_jsxDEV", "UnifiedAuthTest", "_s", "user", "isAuthenticated", "isLoading", "error", "currentRole", "availableRoles", "login", "logout", "switchRole", "clearError", "testCredentials", "setTestCredentials", "identifier", "password", "userType", "testResults", "setTestResults", "isRunningTests", "setIsRunningTests", "addTestResult", "result", "prev", "Date", "toLocaleTimeString", "clearTestResults", "testStorage", "debugInfo", "getDebugInfo", "JSON", "stringify", "getAvailableRoles", "join", "mostRecentRole", "getMostRecentRole", "message", "testService", "serviceDebugInfo", "getCurrentRole", "testLogin", "undefined", "testRoleSwitch", "targetRole", "testLogout", "runComprehensiveTests", "email", "className", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "borderRadius", "display", "gridTemplateColumns", "gap", "type", "placeholder", "value", "onChange", "e", "target", "border", "flexWrap", "onClick", "disabled", "cursor", "includes", "maxHeight", "overflowY", "fontFamily", "fontSize", "length", "map", "index", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/testing/UnifiedAuthTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\nimport { unifiedStorage } from '../../utils/unifiedStorage';\nimport { unifiedAuthService } from '../../services/unified-auth.service';\n\n/**\n * Comprehensive test component for the unified authentication system\n * This component provides debugging tools and validation for the unified auth implementation\n *\n * NOTE: This only tests LOGIN functionality. Registration and OTP systems remain separate:\n * - Admin registration: Use AdminAuthContext and AdminAuthService\n * - Student registration: Use StudentAuthContext and StudentAuthService\n */\nconst UnifiedAuthTest: React.FC = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    currentRole,\n    availableRoles,\n    login,\n    logout,\n    switchRole,\n    clearError,\n  } = useUnifiedAuth();\n\n  const [testCredentials, setTestCredentials] = useState({\n    identifier: '',\n    password: '',\n    userType: 'auto' as 'admin' | 'student' | 'auto',\n  });\n\n  const [testResults, setTestResults] = useState<string[]>([]);\n  const [isRunningTests, setIsRunningTests] = useState(false);\n\n  const addTestResult = (result: string) => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);\n  };\n\n  const clearTestResults = () => {\n    setTestResults([]);\n  };\n\n  // Test storage functionality\n  const testStorage = () => {\n    addTestResult('🧪 Testing storage functionality...');\n    \n    try {\n      const debugInfo = unifiedStorage.getDebugInfo();\n      addTestResult(`✅ Storage debug info: ${JSON.stringify(debugInfo, null, 2)}`);\n      \n      const availableRoles = unifiedStorage.getAvailableRoles();\n      addTestResult(`✅ Available roles: ${availableRoles.join(', ') || 'None'}`);\n      \n      const mostRecentRole = unifiedStorage.getMostRecentRole();\n      addTestResult(`✅ Most recent role: ${mostRecentRole || 'None'}`);\n      \n    } catch (error: any) {\n      addTestResult(`❌ Storage test failed: ${error.message}`);\n    }\n  };\n\n  // Test service functionality\n  const testService = () => {\n    addTestResult('🧪 Testing service functionality...');\n    \n    try {\n      const serviceDebugInfo = unifiedAuthService.getDebugInfo();\n      addTestResult(`✅ Service debug info: ${JSON.stringify(serviceDebugInfo, null, 2)}`);\n      \n      const availableRoles = unifiedAuthService.getAvailableRoles();\n      addTestResult(`✅ Service available roles: ${availableRoles.join(', ') || 'None'}`);\n      \n      const currentRole = unifiedAuthService.getCurrentRole();\n      addTestResult(`✅ Service current role: ${currentRole || 'None'}`);\n      \n    } catch (error: any) {\n      addTestResult(`❌ Service test failed: ${error.message}`);\n    }\n  };\n\n  // Test login functionality\n  const testLogin = async () => {\n    if (!testCredentials.identifier || !testCredentials.password) {\n      addTestResult('❌ Please enter test credentials');\n      return;\n    }\n\n    addTestResult('🧪 Testing login functionality...');\n    setIsRunningTests(true);\n    \n    try {\n      await login({\n        identifier: testCredentials.identifier,\n        password: testCredentials.password,\n        userType: testCredentials.userType === 'auto' ? undefined : testCredentials.userType,\n      });\n      \n      addTestResult('✅ Login test successful');\n    } catch (error: any) {\n      addTestResult(`❌ Login test failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Test role switching\n  const testRoleSwitch = async (targetRole: 'admin' | 'student') => {\n    addTestResult(`🧪 Testing role switch to ${targetRole}...`);\n    setIsRunningTests(true);\n    \n    try {\n      await switchRole(targetRole);\n      addTestResult(`✅ Role switch to ${targetRole} successful`);\n    } catch (error: any) {\n      addTestResult(`❌ Role switch to ${targetRole} failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Test logout functionality\n  const testLogout = async () => {\n    addTestResult('🧪 Testing logout functionality...');\n    setIsRunningTests(true);\n    \n    try {\n      await logout();\n      addTestResult('✅ Logout test successful');\n    } catch (error: any) {\n      addTestResult(`❌ Logout test failed: ${error.message}`);\n    } finally {\n      setIsRunningTests(false);\n    }\n  };\n\n  // Run comprehensive test suite\n  const runComprehensiveTests = async () => {\n    addTestResult('🚀 Starting comprehensive test suite...');\n    clearTestResults();\n    \n    // Test 1: Storage functionality\n    testStorage();\n    \n    // Test 2: Service functionality\n    testService();\n    \n    // Test 3: Context state validation\n    addTestResult('🧪 Testing context state...');\n    addTestResult(`✅ Context isAuthenticated: ${isAuthenticated}`);\n    addTestResult(`✅ Context isLoading: ${isLoading}`);\n    addTestResult(`✅ Context currentRole: ${currentRole || 'None'}`);\n    addTestResult(`✅ Context availableRoles: ${availableRoles.join(', ') || 'None'}`);\n    addTestResult(`✅ Context user: ${user ? user.email : 'None'}`);\n    addTestResult(`✅ Context error: ${error || 'None'}`);\n    \n    addTestResult('🎉 Comprehensive test suite completed');\n  };\n\n  return (\n    <div className=\"unified-auth-test\" style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>\n      <h1 style={{ marginBottom: '2rem', color: '#1f2937' }}>🧪 Unified Authentication Test Suite</h1>\n      \n      {/* Current State Display */}\n      <div style={{ marginBottom: '2rem', padding: '1rem', backgroundColor: '#f3f4f6', borderRadius: '8px' }}>\n        <h2 style={{ marginBottom: '1rem', color: '#374151' }}>Current Authentication State</h2>\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>\n          <div>\n            <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}\n          </div>\n          <div>\n            <strong>Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}\n          </div>\n          <div>\n            <strong>Current Role:</strong> {currentRole || 'None'}\n          </div>\n          <div>\n            <strong>Available Roles:</strong> {availableRoles.join(', ') || 'None'}\n          </div>\n          <div>\n            <strong>User Email:</strong> {user?.email || 'None'}\n          </div>\n          <div>\n            <strong>Error:</strong> {error || 'None'}\n          </div>\n        </div>\n      </div>\n\n      {/* Test Credentials */}\n      <div style={{ marginBottom: '2rem', padding: '1rem', backgroundColor: '#f9fafb', borderRadius: '8px' }}>\n        <h3 style={{ marginBottom: '1rem', color: '#374151' }}>Test Credentials</h3>\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>\n          <input\n            type=\"text\"\n            placeholder=\"Email or Student Number\"\n            value={testCredentials.identifier}\n            onChange={(e) => setTestCredentials(prev => ({ ...prev, identifier: e.target.value }))}\n            style={{ padding: '0.5rem', border: '1px solid #d1d5db', borderRadius: '4px' }}\n          />\n          <input\n            type=\"password\"\n            placeholder=\"Password\"\n            value={testCredentials.password}\n            onChange={(e) => setTestCredentials(prev => ({ ...prev, password: e.target.value }))}\n            style={{ padding: '0.5rem', border: '1px solid #d1d5db', borderRadius: '4px' }}\n          />\n          <select\n            value={testCredentials.userType}\n            onChange={(e) => setTestCredentials(prev => ({ ...prev, userType: e.target.value as any }))}\n            style={{ padding: '0.5rem', border: '1px solid #d1d5db', borderRadius: '4px' }}\n          >\n            <option value=\"auto\">Auto-detect</option>\n            <option value=\"admin\">Admin</option>\n            <option value=\"student\">Student</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Test Actions */}\n      <div style={{ marginBottom: '2rem', padding: '1rem', backgroundColor: '#f0f9ff', borderRadius: '8px' }}>\n        <h3 style={{ marginBottom: '1rem', color: '#374151' }}>Test Actions</h3>\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>\n          <button\n            onClick={runComprehensiveTests}\n            disabled={isRunningTests}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#3b82f6', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            🚀 Run All Tests\n          </button>\n          \n          <button\n            onClick={testStorage}\n            disabled={isRunningTests}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#10b981', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            🗄️ Test Storage\n          </button>\n          \n          <button\n            onClick={testService}\n            disabled={isRunningTests}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#8b5cf6', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            ⚙️ Test Service\n          </button>\n          \n          <button\n            onClick={testLogin}\n            disabled={isRunningTests || !testCredentials.identifier || !testCredentials.password}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#f59e0b', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            🔐 Test Login\n          </button>\n          \n          {availableRoles.includes('admin') && currentRole !== 'admin' && (\n            <button\n              onClick={() => testRoleSwitch('admin')}\n              disabled={isRunningTests}\n              style={{ padding: '0.5rem 1rem', backgroundColor: '#ef4444', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              👨‍💼 Switch to Admin\n            </button>\n          )}\n          \n          {availableRoles.includes('student') && currentRole !== 'student' && (\n            <button\n              onClick={() => testRoleSwitch('student')}\n              disabled={isRunningTests}\n              style={{ padding: '0.5rem 1rem', backgroundColor: '#06b6d4', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              🎓 Switch to Student\n            </button>\n          )}\n          \n          {isAuthenticated && (\n            <button\n              onClick={testLogout}\n              disabled={isRunningTests}\n              style={{ padding: '0.5rem 1rem', backgroundColor: '#6b7280', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n            >\n              🚪 Test Logout\n            </button>\n          )}\n          \n          <button\n            onClick={clearError}\n            disabled={!error}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#84cc16', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            🧹 Clear Error\n          </button>\n          \n          <button\n            onClick={clearTestResults}\n            style={{ padding: '0.5rem 1rem', backgroundColor: '#64748b', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}\n          >\n            🗑️ Clear Results\n          </button>\n        </div>\n      </div>\n\n      {/* Test Results */}\n      <div style={{ padding: '1rem', backgroundColor: '#1f2937', borderRadius: '8px', color: '#f9fafb' }}>\n        <h3 style={{ marginBottom: '1rem', color: '#f3f4f6' }}>Test Results</h3>\n        <div style={{ maxHeight: '400px', overflowY: 'auto', fontFamily: 'monospace', fontSize: '0.875rem' }}>\n          {testResults.length === 0 ? (\n            <p style={{ color: '#9ca3af' }}>No test results yet. Run some tests to see results here.</p>\n          ) : (\n            testResults.map((result, index) => (\n              <div key={index} style={{ marginBottom: '0.25rem', whiteSpace: 'pre-wrap' }}>\n                {result}\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedAuthTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,kBAAkB,QAAQ,qCAAqC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IACJC,IAAI;IACJC,eAAe;IACfC,SAAS;IACTC,KAAK;IACLC,WAAW;IACXC,cAAc;IACdC,KAAK;IACLC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC;IACrDoB,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM2B,aAAa,GAAIC,MAAc,IAAK;IACxCJ,cAAc,CAACK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,KAAKH,MAAM,EAAE,CAAC,CAAC;EACpF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED;EACA,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBN,aAAa,CAAC,qCAAqC,CAAC;IAEpD,IAAI;MACF,MAAMO,SAAS,GAAGhC,cAAc,CAACiC,YAAY,CAAC,CAAC;MAC/CR,aAAa,CAAC,yBAAyBS,IAAI,CAACC,SAAS,CAACH,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;MAE5E,MAAMrB,cAAc,GAAGX,cAAc,CAACoC,iBAAiB,CAAC,CAAC;MACzDX,aAAa,CAAC,sBAAsBd,cAAc,CAAC0B,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC;MAE1E,MAAMC,cAAc,GAAGtC,cAAc,CAACuC,iBAAiB,CAAC,CAAC;MACzDd,aAAa,CAAC,uBAAuBa,cAAc,IAAI,MAAM,EAAE,CAAC;IAElE,CAAC,CAAC,OAAO7B,KAAU,EAAE;MACnBgB,aAAa,CAAC,0BAA0BhB,KAAK,CAAC+B,OAAO,EAAE,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBhB,aAAa,CAAC,qCAAqC,CAAC;IAEpD,IAAI;MACF,MAAMiB,gBAAgB,GAAGzC,kBAAkB,CAACgC,YAAY,CAAC,CAAC;MAC1DR,aAAa,CAAC,yBAAyBS,IAAI,CAACC,SAAS,CAACO,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;MAEnF,MAAM/B,cAAc,GAAGV,kBAAkB,CAACmC,iBAAiB,CAAC,CAAC;MAC7DX,aAAa,CAAC,8BAA8Bd,cAAc,CAAC0B,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC;MAElF,MAAM3B,WAAW,GAAGT,kBAAkB,CAAC0C,cAAc,CAAC,CAAC;MACvDlB,aAAa,CAAC,2BAA2Bf,WAAW,IAAI,MAAM,EAAE,CAAC;IAEnE,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBgB,aAAa,CAAC,0BAA0BhB,KAAK,CAAC+B,OAAO,EAAE,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAMI,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAC5B,eAAe,CAACE,UAAU,IAAI,CAACF,eAAe,CAACG,QAAQ,EAAE;MAC5DM,aAAa,CAAC,iCAAiC,CAAC;MAChD;IACF;IAEAA,aAAa,CAAC,mCAAmC,CAAC;IAClDD,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMZ,KAAK,CAAC;QACVM,UAAU,EAAEF,eAAe,CAACE,UAAU;QACtCC,QAAQ,EAAEH,eAAe,CAACG,QAAQ;QAClCC,QAAQ,EAAEJ,eAAe,CAACI,QAAQ,KAAK,MAAM,GAAGyB,SAAS,GAAG7B,eAAe,CAACI;MAC9E,CAAC,CAAC;MAEFK,aAAa,CAAC,yBAAyB,CAAC;IAC1C,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBgB,aAAa,CAAC,wBAAwBhB,KAAK,CAAC+B,OAAO,EAAE,CAAC;IACxD,CAAC,SAAS;MACRhB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMsB,cAAc,GAAG,MAAOC,UAA+B,IAAK;IAChEtB,aAAa,CAAC,6BAA6BsB,UAAU,KAAK,CAAC;IAC3DvB,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMV,UAAU,CAACiC,UAAU,CAAC;MAC5BtB,aAAa,CAAC,oBAAoBsB,UAAU,aAAa,CAAC;IAC5D,CAAC,CAAC,OAAOtC,KAAU,EAAE;MACnBgB,aAAa,CAAC,oBAAoBsB,UAAU,YAAYtC,KAAK,CAAC+B,OAAO,EAAE,CAAC;IAC1E,CAAC,SAAS;MACRhB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BvB,aAAa,CAAC,oCAAoC,CAAC;IACnDD,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMX,MAAM,CAAC,CAAC;MACdY,aAAa,CAAC,0BAA0B,CAAC;IAC3C,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBgB,aAAa,CAAC,yBAAyBhB,KAAK,CAAC+B,OAAO,EAAE,CAAC;IACzD,CAAC,SAAS;MACRhB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMyB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCxB,aAAa,CAAC,yCAAyC,CAAC;IACxDK,gBAAgB,CAAC,CAAC;;IAElB;IACAC,WAAW,CAAC,CAAC;;IAEb;IACAU,WAAW,CAAC,CAAC;;IAEb;IACAhB,aAAa,CAAC,6BAA6B,CAAC;IAC5CA,aAAa,CAAC,8BAA8BlB,eAAe,EAAE,CAAC;IAC9DkB,aAAa,CAAC,wBAAwBjB,SAAS,EAAE,CAAC;IAClDiB,aAAa,CAAC,0BAA0Bf,WAAW,IAAI,MAAM,EAAE,CAAC;IAChEe,aAAa,CAAC,6BAA6Bd,cAAc,CAAC0B,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC;IACjFZ,aAAa,CAAC,mBAAmBnB,IAAI,GAAGA,IAAI,CAAC4C,KAAK,GAAG,MAAM,EAAE,CAAC;IAC9DzB,aAAa,CAAC,oBAAoBhB,KAAK,IAAI,MAAM,EAAE,CAAC;IAEpDgB,aAAa,CAAC,uCAAuC,CAAC;EACxD,CAAC;EAED,oBACEtB,OAAA;IAAKgD,SAAS,EAAC,mBAAmB;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAClGrD,OAAA;MAAIiD,KAAK,EAAE;QAAEK,YAAY,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAF,QAAA,EAAC;IAAoC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGhG3D,OAAA;MAAKiD,KAAK,EAAE;QAAEK,YAAY,EAAE,MAAM;QAAEJ,OAAO,EAAE,MAAM;QAAEU,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAR,QAAA,gBACrGrD,OAAA;QAAIiD,KAAK,EAAE;UAAEK,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAA4B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxF3D,OAAA;QAAKiD,KAAK,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,sCAAsC;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACxGrD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvD,eAAe,GAAG,OAAO,GAAG,MAAM;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACN3D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACtD,SAAS,GAAG,OAAO,GAAG,MAAM;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN3D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpD,WAAW,IAAI,MAAM;QAAA;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN3D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,cAAc,CAAC0B,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN3D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,KAAK,KAAI,MAAM;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN3D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAAqD,QAAA,EAAQ;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACrD,KAAK,IAAI,MAAM;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAKiD,KAAK,EAAE;QAAEK,YAAY,EAAE,MAAM;QAAEJ,OAAO,EAAE,MAAM;QAAEU,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAR,QAAA,gBACrGrD,OAAA;QAAIiD,KAAK,EAAE;UAAEK,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E3D,OAAA;QAAKiD,KAAK,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE,sCAAsC;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACxGrD,OAAA;UACEiE,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,yBAAyB;UACrCC,KAAK,EAAEtD,eAAe,CAACE,UAAW;UAClCqD,QAAQ,EAAGC,CAAC,IAAKvD,kBAAkB,CAACU,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAET,UAAU,EAAEsD,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UACvFlB,KAAK,EAAE;YAAEC,OAAO,EAAE,QAAQ;YAAEqB,MAAM,EAAE,mBAAmB;YAAEV,YAAY,EAAE;UAAM;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACF3D,OAAA;UACEiE,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,UAAU;UACtBC,KAAK,EAAEtD,eAAe,CAACG,QAAS;UAChCoD,QAAQ,EAAGC,CAAC,IAAKvD,kBAAkB,CAACU,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAER,QAAQ,EAAEqD,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UACrFlB,KAAK,EAAE;YAAEC,OAAO,EAAE,QAAQ;YAAEqB,MAAM,EAAE,mBAAmB;YAAEV,YAAY,EAAE;UAAM;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACF3D,OAAA;UACEmE,KAAK,EAAEtD,eAAe,CAACI,QAAS;UAChCmD,QAAQ,EAAGC,CAAC,IAAKvD,kBAAkB,CAACU,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEP,QAAQ,EAAEoD,CAAC,CAACC,MAAM,CAACH;UAAa,CAAC,CAAC,CAAE;UAC5FlB,KAAK,EAAE;YAAEC,OAAO,EAAE,QAAQ;YAAEqB,MAAM,EAAE,mBAAmB;YAAEV,YAAY,EAAE;UAAM,CAAE;UAAAR,QAAA,gBAE/ErD,OAAA;YAAQmE,KAAK,EAAC,MAAM;YAAAd,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzC3D,OAAA;YAAQmE,KAAK,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3D,OAAA;YAAQmE,KAAK,EAAC,SAAS;YAAAd,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAKiD,KAAK,EAAE;QAAEK,YAAY,EAAE,MAAM;QAAEJ,OAAO,EAAE,MAAM;QAAEU,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAR,QAAA,gBACrGrD,OAAA;QAAIiD,KAAK,EAAE;UAAEK,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE3D,OAAA;QAAKiD,KAAK,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEU,QAAQ,EAAE,MAAM;UAAER,GAAG,EAAE;QAAS,CAAE;QAAAX,QAAA,gBAC/DrD,OAAA;UACEyE,OAAO,EAAE3B,qBAAsB;UAC/B4B,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEyE,OAAO,EAAE7C,WAAY;UACrB8C,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEyE,OAAO,EAAEnC,WAAY;UACrBoC,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEyE,OAAO,EAAEhC,SAAU;UACnBiC,QAAQ,EAAEtD,cAAc,IAAI,CAACP,eAAe,CAACE,UAAU,IAAI,CAACF,eAAe,CAACG,QAAS;UACrFiC,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERnD,cAAc,CAACoE,QAAQ,CAAC,OAAO,CAAC,IAAIrE,WAAW,KAAK,OAAO,iBAC1DP,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC,OAAO,CAAE;UACvC+B,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAnD,cAAc,CAACoE,QAAQ,CAAC,SAAS,CAAC,IAAIrE,WAAW,KAAK,SAAS,iBAC9DP,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC,SAAS,CAAE;UACzC+B,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAvD,eAAe,iBACdJ,OAAA;UACEyE,OAAO,EAAE5B,UAAW;UACpB6B,QAAQ,EAAEtD,cAAe;UACzB6B,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED3D,OAAA;UACEyE,OAAO,EAAE7D,UAAW;UACpB8D,QAAQ,EAAE,CAACpE,KAAM;UACjB2C,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEyE,OAAO,EAAE9C,gBAAiB;UAC1BsB,KAAK,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAEU,eAAe,EAAE,SAAS;YAAEL,KAAK,EAAE,OAAO;YAAEgB,MAAM,EAAE,MAAM;YAAEV,YAAY,EAAE,KAAK;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAtB,QAAA,EACvI;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAKiD,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEU,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE,KAAK;QAAEN,KAAK,EAAE;MAAU,CAAE;MAAAF,QAAA,gBACjGrD,OAAA;QAAIiD,KAAK,EAAE;UAAEK,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE3D,OAAA;QAAKiD,KAAK,EAAE;UAAE4B,SAAS,EAAE,OAAO;UAAEC,SAAS,EAAE,MAAM;UAAEC,UAAU,EAAE,WAAW;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAA3B,QAAA,EAClGnC,WAAW,CAAC+D,MAAM,KAAK,CAAC,gBACvBjF,OAAA;UAAGiD,KAAK,EAAE;YAAEM,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAwD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GAE5FzC,WAAW,CAACgE,GAAG,CAAC,CAAC3D,MAAM,EAAE4D,KAAK,kBAC5BnF,OAAA;UAAiBiD,KAAK,EAAE;YAAEK,YAAY,EAAE,SAAS;YAAE8B,UAAU,EAAE;UAAW,CAAE;UAAA/B,QAAA,EACzE9B;QAAM,GADC4D,KAAK;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CAlTID,eAAyB;EAAA,QAYzBL,cAAc;AAAA;AAAAyF,EAAA,GAZdpF,eAAyB;AAoT/B,eAAeA,eAAe;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}