{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 12h16\",\n  key: \"1lakjw\"\n}], [\"path\", {\n  d: \"M4 18h16\",\n  key: \"19g7jn\"\n}], [\"path\", {\n  d: \"M4 6h16\",\n  key: \"1o0s65\"\n}]];\nconst Menu = createLucideIcon(\"menu\", __iconNode);\nexport { __iconNode, Menu as default };\n//# sourceMappingURL=menu.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}