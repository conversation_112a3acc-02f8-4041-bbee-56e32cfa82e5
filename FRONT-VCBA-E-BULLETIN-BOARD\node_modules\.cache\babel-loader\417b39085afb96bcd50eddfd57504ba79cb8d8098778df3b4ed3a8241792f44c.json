{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\modals\\\\AnnouncementViewDialog.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, Calendar, User, Tag, Eye, MessageCircle, Heart, Pin, AlertTriangle } from 'lucide-react';\nimport { getImageUrl } from '../../../config/constants';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Facebook-style image gallery component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst FacebookImageGallery = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  _s2();\n  // Call hooks at the top level for maximum possible images (Rules of Hooks compliance)\n  const imageLoader0 = useImageLoader((images === null || images === void 0 ? void 0 : images[0]) || null);\n  const imageLoader1 = useImageLoader((images === null || images === void 0 ? void 0 : images[1]) || null);\n  const imageLoader2 = useImageLoader((images === null || images === void 0 ? void 0 : images[2]) || null);\n  const imageLoader3 = useImageLoader((images === null || images === void 0 ? void 0 : images[3]) || null);\n\n  // Create array of results for easy access\n  const imageLoaderResults = [imageLoader0, imageLoader1, imageLoader2, imageLoader3];\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n  const getImageStyle = (index, total) => {\n    const baseStyle = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n    return baseStyle;\n  };\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n  const renderOverlay = (index, count) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        },\n        children: [\"+\", count]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n\n  // Get the first image loader result\n  const firstImageResult = imageLoaderResults[0];\n  const {\n    imageUrl: firstImageUrl,\n    loading: firstImageLoading,\n    error: firstImageError\n  } = firstImageResult;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [firstImageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          color: '#6b7280'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '0.5rem',\n              fontSize: '2rem'\n            },\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Loading image...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), firstImageError && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          background: '#f3f4f6',\n          color: '#6b7280',\n          borderRadius: '12px',\n          fontSize: '0.875rem',\n          border: '2px dashed #d1d5db'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '0.5rem',\n              fontSize: '2rem'\n            },\n            children: \"\\uD83D\\uDDBC\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500'\n            },\n            children: \"Image unavailable\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              marginTop: '0.25rem',\n              color: '#9ca3af'\n            },\n            children: firstImageError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), firstImageUrl && !firstImageLoading && !firstImageError && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: firstImageUrl,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onLoad: e => {\n          e.currentTarget.style.opacity = '1';\n          console.log('✅ Image rendered successfully via CORS-safe method');\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '40%'\n      },\n      children: visibleImages.slice(1).map((_, idx) => {\n        const actualIndex = idx + 1;\n        // Use the pre-loaded image loader result instead of calling the hook\n        const {\n          imageUrl,\n          loading,\n          error\n        } = imageLoaderResults[actualIndex];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getContainerStyle(actualIndex, visibleImages.length),\n          children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n              color: '#6b7280'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.5rem',\n                  fontSize: '1.5rem'\n                },\n                children: \"\\u23F3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem'\n                },\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 19\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n              background: '#f3f4f6',\n              color: '#6b7280',\n              borderRadius: '8px',\n              fontSize: '0.75rem',\n              border: '2px dashed #d1d5db'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.25rem',\n                  fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDDBC\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 19\n          }, this), imageUrl && !loading && !error && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageUrl,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onLoad: e => {\n              e.currentTarget.style.opacity = '1';\n              console.log('✅ Image rendered successfully via CORS-safe method');\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 19\n          }, this), renderOverlay(actualIndex, remainingCount), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s2(FacebookImageGallery, \"bTEL4wM7UJsdw1TgB4GUGrFI1F8=\", false, function () {\n  return [useImageLoader, useImageLoader, useImageLoader, useImageLoader];\n});\n_c = FacebookImageGallery;\nconst AnnouncementViewDialog = ({\n  isOpen,\n  onClose,\n  announcement\n}) => {\n  if (!isOpen || !announcement) return null;\n  const formatDate = dateString => {\n    if (!dateString) return 'Not set';\n    try {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch (error) {\n      return 'Invalid date';\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'published':\n        return '#10b981';\n      case 'draft':\n        return '#6b7280';\n      case 'scheduled':\n        return '#f59e0b';\n      case 'archived':\n        return '#ef4444';\n      default:\n        return '#6b7280';\n    }\n  };\n  const getStatusText = status => {\n    return status.charAt(0).toUpperCase() + status.slice(1);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    },\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        width: '100%',\n        maxWidth: '800px',\n        maxHeight: '90vh',\n        overflow: 'hidden',\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'\n      },\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1.5rem',\n          borderBottom: '1px solid #e5e7eb',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            marginRight: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.5rem',\n                fontWeight: '600',\n                color: '#111827',\n                margin: 0,\n                marginRight: '1rem'\n              },\n              children: announcement.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: getStatusColor(announcement.status),\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '9999px',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                textTransform: 'uppercase'\n              },\n              children: getStatusText(announcement.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              flexWrap: 'wrap'\n            },\n            children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                backgroundColor: '#fef3c7',\n                color: '#92400e',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '6px',\n                fontSize: '0.75rem',\n                fontWeight: '500'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), \"Pinned\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), announcement.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                backgroundColor: '#fee2e2',\n                color: '#dc2626',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '6px',\n                fontSize: '0.75rem',\n                fontWeight: '500'\n              },\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), \"Alert\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          style: {\n            padding: '0.5rem',\n            backgroundColor: 'transparent',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            color: '#6b7280',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1.5rem',\n          maxHeight: 'calc(90vh - 200px)',\n          overflowY: 'auto'\n        },\n        children: [(announcement.attachments && announcement.attachments.length > 0 || announcement.images && announcement.images.length > 0 || announcement.image_url || announcement.image_path) && /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n          images:\n          // Use attachments/images array if available, otherwise fallback to single image\n          announcement.attachments && announcement.attachments.length > 0 ? announcement.attachments.map(att => att.file_path) : announcement.images && announcement.images.length > 0 ? announcement.images.map(img => img.file_path) : [announcement.image_url || announcement.image_path].filter(Boolean),\n          altPrefix: announcement.title,\n          maxVisible: 4,\n          onImageClick: index => {\n            console.log(`Clicked image ${index + 1} for announcement: ${announcement.title}`);\n            // Future: Open image viewer/lightbox\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1rem',\n            lineHeight: '1.6',\n            color: '#374151',\n            marginBottom: '2rem',\n            whiteSpace: 'pre-wrap'\n          },\n          children: announcement.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n            gap: '1rem',\n            padding: '1.5rem',\n            backgroundColor: '#f9fafb',\n            borderRadius: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Author\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: announcement.author_name || 'Unknown'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: announcement.category_name || 'Uncategorized'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Created\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: formatDate(announcement.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), announcement.published_at && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Published\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: formatDate(announcement.published_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), announcement.scheduled_publish_at && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Scheduled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: formatDate(announcement.scheduled_publish_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Eye, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Views\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: (announcement.view_count || 0).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Heart, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Reactions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: (announcement.reaction_count || 0).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              size: 16,\n              style: {\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: \"Comments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#111827',\n                  margin: 0\n                },\n                children: (announcement.comment_count || 0).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem',\n            padding: '1rem',\n            backgroundColor: '#f3f4f6',\n            borderRadius: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#111827',\n              margin: '0 0 0.75rem 0'\n            },\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1.5rem',\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '12px',\n                  height: '12px',\n                  borderRadius: '50%',\n                  backgroundColor: announcement.allow_comments ? '#10b981' : '#ef4444'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: [\"Comments \", announcement.allow_comments ? 'Enabled' : 'Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '12px',\n                  height: '12px',\n                  borderRadius: '50%',\n                  backgroundColor: announcement.allow_sharing ? '#10b981' : '#ef4444'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: [\"Sharing \", announcement.allow_sharing ? 'Enabled' : 'Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 5\n  }, this);\n};\n_c2 = AnnouncementViewDialog;\nexport default AnnouncementViewDialog;\nvar _c, _c2;\n$RefreshReg$(_c, \"FacebookImageGallery\");\n$RefreshReg$(_c2, \"AnnouncementViewDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Calendar", "User", "Tag", "Eye", "MessageCircle", "Heart", "<PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getImageUrl", "jsxDEV", "_jsxDEV", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "FacebookImageGallery", "images", "altPrefix", "maxVisible", "onImageClick", "_s2", "imageLoader0", "imageLoader1", "imageLoader2", "imageLoader3", "imageLoaderResults", "length", "visibleImages", "slice", "remainingCount", "getImageStyle", "index", "total", "baseStyle", "width", "height", "objectFit", "cursor", "transition", "borderRadius", "getContainerStyle", "position", "overflow", "backgroundColor", "renderOverlay", "count", "style", "top", "left", "right", "bottom", "display", "alignItems", "justifyContent", "color", "fontSize", "fontWeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstImageResult", "firstImageUrl", "firstImageLoading", "firstImageError", "gap", "marginBottom", "textAlign", "background", "border", "marginTop", "src", "alt", "onLoad", "e", "currentTarget", "opacity", "onMouseEnter", "transform", "onMouseLeave", "onClick", "flexDirection", "map", "_", "idx", "actualIndex", "_c", "AnnouncementViewDialog", "isOpen", "onClose", "announcement", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusColor", "getStatusText", "char<PERSON>t", "toUpperCase", "zIndex", "padding", "max<PERSON><PERSON><PERSON>", "maxHeight", "boxShadow", "stopPropagation", "borderBottom", "flex", "marginRight", "margin", "title", "textTransform", "flexWrap", "is_pinned", "size", "is_alert", "overflowY", "attachments", "image_url", "image_path", "att", "file_path", "img", "filter", "Boolean", "lineHeight", "whiteSpace", "content", "gridTemplateColumns", "author_name", "category_name", "created_at", "published_at", "scheduled_publish_at", "view_count", "toLocaleString", "reaction_count", "comment_count", "allow_comments", "allow_sharing", "_c2", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/modals/AnnouncementViewDialog.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, Calendar, User, Tag, Eye, MessageCircle, Heart, Pin, AlertTriangle } from 'lucide-react';\nimport type { Announcement } from '../../../services/announcementService';\nimport { getImageUrl } from '../../../config/constants';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Facebook-style image gallery component\ninterface FacebookImageGalleryProps {\n  images: string[];\n  altPrefix: string;\n  maxVisible?: number;\n  onImageClick?: (index: number) => void;\n}\n\nconst FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  // Call hooks at the top level for maximum possible images (Rules of Hooks compliance)\n  const imageLoader0 = useImageLoader(images?.[0] || null);\n  const imageLoader1 = useImageLoader(images?.[1] || null);\n  const imageLoader2 = useImageLoader(images?.[2] || null);\n  const imageLoader3 = useImageLoader(images?.[3] || null);\n\n  // Create array of results for easy access\n  const imageLoaderResults = [imageLoader0, imageLoader1, imageLoader2, imageLoader3];\n\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n\n    return baseStyle;\n  };\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n\n  const renderOverlay = (index: number, count: number) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        }}>\n          +{count}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Get the first image loader result\n  const firstImageResult = imageLoaderResults[0];\n  const { imageUrl: firstImageUrl, loading: firstImageLoading, error: firstImageError } = firstImageResult;\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1.5rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        {firstImageLoading && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            height: '100%',\n            color: '#6b7280'\n          }}>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>⏳</div>\n              <div>Loading image...</div>\n            </div>\n          </div>\n        )}\n\n        {firstImageError && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            height: '100%',\n            background: '#f3f4f6',\n            color: '#6b7280',\n            borderRadius: '12px',\n            fontSize: '0.875rem',\n            border: '2px dashed #d1d5db'\n          }}>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>🖼️</div>\n              <div style={{ fontWeight: '500' }}>Image unavailable</div>\n              <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n                {firstImageError}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {firstImageUrl && !firstImageLoading && !firstImageError && (\n          <img\n            src={firstImageUrl}\n            alt={`${altPrefix} - Image 1`}\n            style={getImageStyle(0, visibleImages.length)}\n            onLoad={(e) => {\n              e.currentTarget.style.opacity = '1';\n              console.log('✅ Image rendered successfully via CORS-safe method');\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }}\n          />\n        )}\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images (for 2+ images) */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '40%'\n        }}>\n          {visibleImages.slice(1).map((_, idx) => {\n            const actualIndex = idx + 1;\n            // Use the pre-loaded image loader result instead of calling the hook\n            const { imageUrl, loading, error } = imageLoaderResults[actualIndex];\n\n            return (\n              <div\n                key={actualIndex}\n                style={getContainerStyle(actualIndex, visibleImages.length)}\n              >\n                {loading && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    height: '100%',\n                    color: '#6b7280'\n                  }}>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n                      <div style={{ fontSize: '0.75rem' }}>Loading...</div>\n                    </div>\n                  </div>\n                )}\n\n                {error && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    height: '100%',\n                    background: '#f3f4f6',\n                    color: '#6b7280',\n                    borderRadius: '8px',\n                    fontSize: '0.75rem',\n                    border: '2px dashed #d1d5db'\n                  }}>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ marginBottom: '0.25rem', fontSize: '1.5rem' }}>🖼️</div>\n                      <div>Error</div>\n                    </div>\n                  </div>\n                )}\n\n                {imageUrl && !loading && !error && (\n                  <img\n                    src={imageUrl}\n                    alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                    style={getImageStyle(actualIndex, visibleImages.length)}\n                    onLoad={(e) => {\n                      e.currentTarget.style.opacity = '1';\n                      console.log('✅ Image rendered successfully via CORS-safe method');\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'scale(1.02)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'scale(1)';\n                    }}\n                  />\n                )}\n                {renderOverlay(actualIndex, remainingCount)}\n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\ninterface AnnouncementViewDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  announcement: Announcement | null;\n}\n\nconst AnnouncementViewDialog: React.FC<AnnouncementViewDialogProps> = ({\n  isOpen,\n  onClose,\n  announcement\n}) => {\n  if (!isOpen || !announcement) return null;\n\n  const formatDate = (dateString: string | null | undefined) => {\n    if (!dateString) return 'Not set';\n    try {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch (error) {\n      return 'Invalid date';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'published': return '#10b981';\n      case 'draft': return '#6b7280';\n      case 'scheduled': return '#f59e0b';\n      case 'archived': return '#ef4444';\n      default: return '#6b7280';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    return status.charAt(0).toUpperCase() + status.slice(1);\n  };\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '1rem'\n      }}\n      onClick={onClose}\n    >\n      <div\n        style={{\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          width: '100%',\n          maxWidth: '800px',\n          maxHeight: '90vh',\n          overflow: 'hidden',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'\n        }}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div\n          style={{\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start'\n          }}\n        >\n          <div style={{ flex: 1, marginRight: '1rem' }}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>\n              <h2\n                style={{\n                  fontSize: '1.5rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  margin: 0,\n                  marginRight: '1rem'\n                }}\n              >\n                {announcement.title}\n              </h2>\n              \n              {/* Status Badge */}\n              <span\n                style={{\n                  backgroundColor: getStatusColor(announcement.status),\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '9999px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500',\n                  textTransform: 'uppercase'\n                }}\n              >\n                {getStatusText(announcement.status)}\n              </span>\n            </div>\n\n            {/* Badges */}\n            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>\n              {announcement.is_pinned && (\n                <span\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    backgroundColor: '#fef3c7',\n                    color: '#92400e',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '6px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  }}\n                >\n                  <Pin size={12} />\n                  Pinned\n                </span>\n              )}\n              \n              {announcement.is_alert && (\n                <span\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    backgroundColor: '#fee2e2',\n                    color: '#dc2626',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '6px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  }}\n                >\n                  <AlertTriangle size={12} />\n                  Alert\n                </span>\n              )}\n            </div>\n          </div>\n\n          <button\n            onClick={onClose}\n            style={{\n              padding: '0.5rem',\n              backgroundColor: 'transparent',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              color: '#6b7280',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div\n          style={{\n            padding: '1.5rem',\n            maxHeight: 'calc(90vh - 200px)',\n            overflowY: 'auto'\n          }}\n        >\n          {/* Image - Facebook-style Gallery */}\n          {((announcement.attachments && announcement.attachments.length > 0) ||\n            (announcement.images && announcement.images.length > 0) ||\n            announcement.image_url || announcement.image_path) && (\n            <FacebookImageGallery\n              images={\n                // Use attachments/images array if available, otherwise fallback to single image\n                (announcement.attachments && announcement.attachments.length > 0)\n                  ? announcement.attachments.map(att => att.file_path)\n                  : (announcement.images && announcement.images.length > 0)\n                    ? announcement.images.map(img => img.file_path)\n                    : [announcement.image_url || announcement.image_path].filter(Boolean) as string[]\n              }\n              altPrefix={announcement.title}\n              maxVisible={4}\n              onImageClick={(index) => {\n                console.log(`Clicked image ${index + 1} for announcement: ${announcement.title}`);\n                // Future: Open image viewer/lightbox\n              }}\n            />\n          )}\n\n          {/* Content */}\n          <div\n            style={{\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              color: '#374151',\n              marginBottom: '2rem',\n              whiteSpace: 'pre-wrap'\n            }}\n          >\n            {announcement.content}\n          </div>\n\n          {/* Metadata Grid */}\n          <div\n            style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n              gap: '1rem',\n              padding: '1.5rem',\n              backgroundColor: '#f9fafb',\n              borderRadius: '8px'\n            }}\n          >\n            {/* Author */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <User size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Author</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {announcement.author_name || 'Unknown'}\n                </p>\n              </div>\n            </div>\n\n            {/* Category */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <Tag size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Category</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {announcement.category_name || 'Uncategorized'}\n                </p>\n              </div>\n            </div>\n\n            {/* Created Date */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <Calendar size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Created</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {formatDate(announcement.created_at)}\n                </p>\n              </div>\n            </div>\n\n            {/* Published Date */}\n            {announcement.published_at && (\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <Calendar size={16} style={{ color: '#6b7280' }} />\n                <div>\n                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Published</p>\n                  <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                    {formatDate(announcement.published_at)}\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* Scheduled Date */}\n            {announcement.scheduled_publish_at && (\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <Calendar size={16} style={{ color: '#6b7280' }} />\n                <div>\n                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Scheduled</p>\n                  <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                    {formatDate(announcement.scheduled_publish_at)}\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* View Count */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <Eye size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Views</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {(announcement.view_count || 0).toLocaleString()}\n                </p>\n              </div>\n            </div>\n\n            {/* Reaction Count */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <Heart size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Reactions</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {(announcement.reaction_count || 0).toLocaleString()}\n                </p>\n              </div>\n            </div>\n\n            {/* Comment Count */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <MessageCircle size={16} style={{ color: '#6b7280' }} />\n              <div>\n                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>Comments</p>\n                <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827', margin: 0 }}>\n                  {(announcement.comment_count || 0).toLocaleString()}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Settings */}\n          <div\n            style={{\n              marginTop: '1.5rem',\n              padding: '1rem',\n              backgroundColor: '#f3f4f6',\n              borderRadius: '8px'\n            }}\n          >\n            <h4 style={{ fontSize: '0.875rem', fontWeight: '600', color: '#111827', margin: '0 0 0.75rem 0' }}>\n              Settings\n            </h4>\n            <div style={{ display: 'flex', gap: '1.5rem', flexWrap: 'wrap' }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <div\n                  style={{\n                    width: '12px',\n                    height: '12px',\n                    borderRadius: '50%',\n                    backgroundColor: announcement.allow_comments ? '#10b981' : '#ef4444'\n                  }}\n                />\n                <span style={{ fontSize: '0.875rem', color: '#374151' }}>\n                  Comments {announcement.allow_comments ? 'Enabled' : 'Disabled'}\n                </span>\n              </div>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <div\n                  style={{\n                    width: '12px',\n                    height: '12px',\n                    borderRadius: '50%',\n                    backgroundColor: announcement.allow_sharing ? '#10b981' : '#ef4444'\n                  }}\n                />\n                <span style={{ fontSize: '0.875rem', color: '#374151' }}>\n                  Sharing {announcement.allow_sharing ? 'Enabled' : 'Disabled'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnnouncementViewDialog;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,aAAa,EAAEC,KAAK,EAAEC,GAAG,EAAEC,aAAa,QAAQ,cAAc;AAEpG,SAASC,WAAW,QAAQ,2BAA2B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGb,WAAW,CAACI,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CrB,WAAW,CAACsB,SAAS,CAAC;QAEtBd,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEsB,GAAG,CAAC;QAC3CrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA/DMF,cAAc;AAuEpB,MAAMiC,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,SAAS;EACTC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ;EACA,MAAMC,YAAY,GAAGvC,cAAc,CAAC,CAAAkC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC,CAAC,KAAI,IAAI,CAAC;EACxD,MAAMM,YAAY,GAAGxC,cAAc,CAAC,CAAAkC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC,CAAC,KAAI,IAAI,CAAC;EACxD,MAAMO,YAAY,GAAGzC,cAAc,CAAC,CAAAkC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC,CAAC,KAAI,IAAI,CAAC;EACxD,MAAMQ,YAAY,GAAG1C,cAAc,CAAC,CAAAkC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC,CAAC,KAAI,IAAI,CAAC;;EAExD;EACA,MAAMS,kBAAkB,GAAG,CAACJ,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,CAAC;EAEnF,IAAI,CAACR,MAAM,IAAIA,MAAM,CAACU,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGX,MAAM,CAACY,KAAK,CAAC,CAAC,EAAEV,UAAU,CAAC;EACjD,MAAMW,cAAc,GAAGb,MAAM,CAACU,MAAM,GAAGR,UAAU;EAEjD,MAAMY,aAAa,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC3E,MAAMC,SAA8B,GAAG;MACrCC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,uCAAuC;MACnDC,YAAY,EAAER,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG;IACtD,CAAC;IAED,OAAOC,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCQ,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,eAAe,EAAE;IACnB,CAAC;IAED,IAAIX,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UACL,GAAGE,SAAS;UACZC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGN,SAAS;UACZC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGE,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL,GAAGN,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE;MAChB,CAAC;IACH;EACF,CAAC;EAED,MAAMK,aAAa,GAAGA,CAACb,KAAa,EAAEc,KAAa,KAAK;IACtD,IAAId,KAAK,KAAKb,UAAU,GAAG,CAAC,IAAI2B,KAAK,GAAG,CAAC,EAAE;MACzC,oBACEhE,OAAA;QAAKiE,KAAK,EAAE;UACVL,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTP,eAAe,EAAE,oBAAoB;UACrCQ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBjB,YAAY,EAAE;QAChB,CAAE;QAAAkB,QAAA,GAAC,GACA,EAACZ,KAAK;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGrC,kBAAkB,CAAC,CAAC,CAAC;EAC9C,MAAM;IAAExC,QAAQ,EAAE8E,aAAa;IAAE5E,OAAO,EAAE6E,iBAAiB;IAAE3E,KAAK,EAAE4E;EAAgB,CAAC,GAAGH,gBAAgB;EAExG,oBACEjF,OAAA;IAAKiE,KAAK,EAAE;MACVK,OAAO,EAAE,MAAM;MACfe,GAAG,EAAE,KAAK;MACVhC,KAAK,EAAE,MAAM;MACbiC,YAAY,EAAE;IAChB,CAAE;IAAAV,QAAA,gBAEA5E,OAAA;MAAKiE,KAAK,EAAEN,iBAAiB,CAAC,CAAC,EAAEb,aAAa,CAACD,MAAM,CAAE;MAAA+B,QAAA,GACpDO,iBAAiB,iBAChBnF,OAAA;QAAKiE,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBlB,MAAM,EAAE,MAAM;UACdmB,KAAK,EAAE;QACT,CAAE;QAAAG,QAAA,eACA5E,OAAA;UAAKiE,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAS,CAAE;UAAAX,QAAA,gBAClC5E,OAAA;YAAKiE,KAAK,EAAE;cAAEqB,YAAY,EAAE,QAAQ;cAAEZ,QAAQ,EAAE;YAAO,CAAE;YAAAE,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjEhF,OAAA;YAAA4E,QAAA,EAAK;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAI,eAAe,iBACdpF,OAAA;QAAKiE,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBlB,MAAM,EAAE,MAAM;UACdkC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,SAAS;UAChBf,YAAY,EAAE,MAAM;UACpBgB,QAAQ,EAAE,UAAU;UACpBe,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,eACA5E,OAAA;UAAKiE,KAAK,EAAE;YAAEsB,SAAS,EAAE;UAAS,CAAE;UAAAX,QAAA,gBAClC5E,OAAA;YAAKiE,KAAK,EAAE;cAAEqB,YAAY,EAAE,QAAQ;cAAEZ,QAAQ,EAAE;YAAO,CAAE;YAAAE,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnEhF,OAAA;YAAKiE,KAAK,EAAE;cAAEU,UAAU,EAAE;YAAM,CAAE;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1DhF,OAAA;YAAKiE,KAAK,EAAE;cAAES,QAAQ,EAAE,SAAS;cAAEgB,SAAS,EAAE,SAAS;cAAEjB,KAAK,EAAE;YAAU,CAAE;YAAAG,QAAA,EACzEQ;UAAe;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAE,aAAa,IAAI,CAACC,iBAAiB,IAAI,CAACC,eAAe,iBACtDpF,OAAA;QACE2F,GAAG,EAAET,aAAc;QACnBU,GAAG,EAAE,GAAGxD,SAAS,YAAa;QAC9B6B,KAAK,EAAEhB,aAAa,CAAC,CAAC,EAAEH,aAAa,CAACD,MAAM,CAAE;QAC9CgD,MAAM,EAAGC,CAAC,IAAK;UACbA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAAC+B,OAAO,GAAG,GAAG;UACnCnF,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACnE,CAAE;QACFmF,YAAY,EAAGH,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACiC,SAAS,GAAG,aAAa;QACjD,CAAE;QACFC,YAAY,EAAGL,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACiC,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EACA1C,YAAY,iBACXtC,OAAA;QACEiE,KAAK,EAAE;UACLL,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACF4C,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAAC,CAAC;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLlC,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvB7C,OAAA;MAAKiE,KAAK,EAAE;QACVK,OAAO,EAAE,MAAM;QACf+B,aAAa,EAAE,QAAQ;QACvBhB,GAAG,EAAE,KAAK;QACVhC,KAAK,EAAEP,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAA+B,QAAA,EACC9B,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAACuD,GAAG,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QACtC,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B;QACA,MAAM;UAAEpG,QAAQ;UAAEE,OAAO;UAAEE;QAAM,CAAC,GAAGoC,kBAAkB,CAAC6D,WAAW,CAAC;QAEpE,oBACEzG,OAAA;UAEEiE,KAAK,EAAEN,iBAAiB,CAAC8C,WAAW,EAAE3D,aAAa,CAACD,MAAM,CAAE;UAAA+B,QAAA,GAE3DtE,OAAO,iBACNN,OAAA;YAAKiE,KAAK,EAAE;cACVK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBlB,MAAM,EAAE,MAAM;cACdmB,KAAK,EAAE;YACT,CAAE;YAAAG,QAAA,eACA5E,OAAA;cAAKiE,KAAK,EAAE;gBAAEsB,SAAS,EAAE;cAAS,CAAE;cAAAX,QAAA,gBAClC5E,OAAA;gBAAKiE,KAAK,EAAE;kBAAEqB,YAAY,EAAE,QAAQ;kBAAEZ,QAAQ,EAAE;gBAAS,CAAE;gBAAAE,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnEhF,OAAA;gBAAKiE,KAAK,EAAE;kBAAES,QAAQ,EAAE;gBAAU,CAAE;gBAAAE,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAxE,KAAK,iBACJR,OAAA;YAAKiE,KAAK,EAAE;cACVK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBlB,MAAM,EAAE,MAAM;cACdkC,UAAU,EAAE,SAAS;cACrBf,KAAK,EAAE,SAAS;cAChBf,YAAY,EAAE,KAAK;cACnBgB,QAAQ,EAAE,SAAS;cACnBe,MAAM,EAAE;YACV,CAAE;YAAAb,QAAA,eACA5E,OAAA;cAAKiE,KAAK,EAAE;gBAAEsB,SAAS,EAAE;cAAS,CAAE;cAAAX,QAAA,gBAClC5E,OAAA;gBAAKiE,KAAK,EAAE;kBAAEqB,YAAY,EAAE,SAAS;kBAAEZ,QAAQ,EAAE;gBAAS,CAAE;gBAAAE,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtEhF,OAAA;gBAAA4E,QAAA,EAAK;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA5E,QAAQ,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,iBAC7BR,OAAA;YACE2F,GAAG,EAAEvF,QAAS;YACdwF,GAAG,EAAE,GAAGxD,SAAS,YAAYqE,WAAW,GAAG,CAAC,EAAG;YAC/CxC,KAAK,EAAEhB,aAAa,CAACwD,WAAW,EAAE3D,aAAa,CAACD,MAAM,CAAE;YACxDgD,MAAM,EAAGC,CAAC,IAAK;cACbA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAAC+B,OAAO,GAAG,GAAG;cACnCnF,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;YACnE,CAAE;YACFmF,YAAY,EAAGH,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACiC,SAAS,GAAG,aAAa;YACjD,CAAE;YACFC,YAAY,EAAGL,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACiC,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,EACAjB,aAAa,CAAC0C,WAAW,EAAEzD,cAAc,CAAC,EAC1CV,YAAY,iBACXtC,OAAA;YACEiE,KAAK,EAAE;cACLL,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACF4C,OAAO,EAAEA,CAAA,KAAM9D,YAAY,CAACmE,WAAW;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAnEIyB,WAAW;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoEb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzC,GAAA,CAnSIL,oBAAyD;EAAA,QAOxCjC,cAAc,EACdA,cAAc,EACdA,cAAc,EACdA,cAAc;AAAA;AAAAyG,EAAA,GAV/BxE,oBAAyD;AA2S/D,MAAMyE,sBAA6D,GAAGA,CAAC;EACrEC,MAAM;EACNC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,IAAI,CAACF,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;EAEzC,MAAMC,UAAU,GAAIC,UAAqC,IAAK;IAC5D,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,IAAI;MACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO/G,KAAK,EAAE;MACd,OAAO,cAAc;IACvB;EACF,CAAC;EAED,MAAMgH,cAAc,GAAIhG,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMiG,aAAa,GAAIjG,MAAc,IAAK;IACxC,OAAOA,MAAM,CAACkG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnG,MAAM,CAACuB,KAAK,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,oBACE/C,OAAA;IACEiE,KAAK,EAAE;MACLL,QAAQ,EAAE,OAAO;MACjBM,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTP,eAAe,EAAE,oBAAoB;MACrCQ,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBoD,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAE;IACFzB,OAAO,EAAES,OAAQ;IAAAjC,QAAA,eAEjB5E,OAAA;MACEiE,KAAK,EAAE;QACLH,eAAe,EAAE,OAAO;QACxBJ,YAAY,EAAE,MAAM;QACpBL,KAAK,EAAE,MAAM;QACbyE,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBlE,QAAQ,EAAE,QAAQ;QAClBmE,SAAS,EAAE;MACb,CAAE;MACF5B,OAAO,EAAGN,CAAC,IAAKA,CAAC,CAACmC,eAAe,CAAC,CAAE;MAAArD,QAAA,gBAGpC5E,OAAA;QACEiE,KAAK,EAAE;UACL4D,OAAO,EAAE,QAAQ;UACjBK,YAAY,EAAE,mBAAmB;UACjC5D,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBAEF5E,OAAA;UAAKiE,KAAK,EAAE;YAAEkE,IAAI,EAAE,CAAC;YAAEC,WAAW,EAAE;UAAO,CAAE;UAAAxD,QAAA,gBAC3C5E,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEe,YAAY,EAAE;YAAS,CAAE;YAAAV,QAAA,gBAC5E5E,OAAA;cACEiE,KAAK,EAAE;gBACLS,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE,KAAK;gBACjBF,KAAK,EAAE,SAAS;gBAChB4D,MAAM,EAAE,CAAC;gBACTD,WAAW,EAAE;cACf,CAAE;cAAAxD,QAAA,EAEDkC,YAAY,CAACwB;YAAK;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eAGLhF,OAAA;cACEiE,KAAK,EAAE;gBACLH,eAAe,EAAE0D,cAAc,CAACV,YAAY,CAACtF,MAAM,CAAC;gBACpDiD,KAAK,EAAE,OAAO;gBACdoD,OAAO,EAAE,iBAAiB;gBAC1BnE,YAAY,EAAE,QAAQ;gBACtBgB,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE,KAAK;gBACjB4D,aAAa,EAAE;cACjB,CAAE;cAAA3D,QAAA,EAED6C,aAAa,CAACX,YAAY,CAACtF,MAAM;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEe,GAAG,EAAE,QAAQ;cAAEmD,QAAQ,EAAE;YAAO,CAAE;YAAA5D,QAAA,GAC9DkC,YAAY,CAAC2B,SAAS,iBACrBzI,OAAA;cACEiE,KAAK,EAAE;gBACLK,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBc,GAAG,EAAE,SAAS;gBACdvB,eAAe,EAAE,SAAS;gBAC1BW,KAAK,EAAE,SAAS;gBAChBoD,OAAO,EAAE,gBAAgB;gBACzBnE,YAAY,EAAE,KAAK;gBACnBgB,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAC,QAAA,gBAEF5E,OAAA,CAACJ,GAAG;gBAAC8I,IAAI,EAAE;cAAG;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,EAEA8B,YAAY,CAAC6B,QAAQ,iBACpB3I,OAAA;cACEiE,KAAK,EAAE;gBACLK,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBc,GAAG,EAAE,SAAS;gBACdvB,eAAe,EAAE,SAAS;gBAC1BW,KAAK,EAAE,SAAS;gBAChBoD,OAAO,EAAE,gBAAgB;gBACzBnE,YAAY,EAAE,KAAK;gBACnBgB,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAC,QAAA,gBAEF5E,OAAA,CAACH,aAAa;gBAAC6I,IAAI,EAAE;cAAG;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA;UACEoG,OAAO,EAAES,OAAQ;UACjB5C,KAAK,EAAE;YACL4D,OAAO,EAAE,QAAQ;YACjB/D,eAAe,EAAE,aAAa;YAC9B2B,MAAM,EAAE,MAAM;YACd/B,YAAY,EAAE,KAAK;YACnBF,MAAM,EAAE,SAAS;YACjBiB,KAAK,EAAE,SAAS;YAChBH,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAI,QAAA,eAEF5E,OAAA,CAACX,CAAC;YAACqJ,IAAI,EAAE;UAAG;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhF,OAAA;QACEiE,KAAK,EAAE;UACL4D,OAAO,EAAE,QAAQ;UACjBE,SAAS,EAAE,oBAAoB;UAC/Ba,SAAS,EAAE;QACb,CAAE;QAAAhE,QAAA,GAGD,CAAEkC,YAAY,CAAC+B,WAAW,IAAI/B,YAAY,CAAC+B,WAAW,CAAChG,MAAM,GAAG,CAAC,IAC/DiE,YAAY,CAAC3E,MAAM,IAAI2E,YAAY,CAAC3E,MAAM,CAACU,MAAM,GAAG,CAAE,IACvDiE,YAAY,CAACgC,SAAS,IAAIhC,YAAY,CAACiC,UAAU,kBACjD/I,OAAA,CAACkC,oBAAoB;UACnBC,MAAM;UACJ;UACC2E,YAAY,CAAC+B,WAAW,IAAI/B,YAAY,CAAC+B,WAAW,CAAChG,MAAM,GAAG,CAAC,GAC5DiE,YAAY,CAAC+B,WAAW,CAACvC,GAAG,CAAC0C,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,GACjDnC,YAAY,CAAC3E,MAAM,IAAI2E,YAAY,CAAC3E,MAAM,CAACU,MAAM,GAAG,CAAC,GACpDiE,YAAY,CAAC3E,MAAM,CAACmE,GAAG,CAAC4C,GAAG,IAAIA,GAAG,CAACD,SAAS,CAAC,GAC7C,CAACnC,YAAY,CAACgC,SAAS,IAAIhC,YAAY,CAACiC,UAAU,CAAC,CAACI,MAAM,CAACC,OAAO,CACzE;UACDhH,SAAS,EAAE0E,YAAY,CAACwB,KAAM;UAC9BjG,UAAU,EAAE,CAAE;UACdC,YAAY,EAAGY,KAAK,IAAK;YACvBrC,OAAO,CAACC,GAAG,CAAC,iBAAiBoC,KAAK,GAAG,CAAC,sBAAsB4D,YAAY,CAACwB,KAAK,EAAE,CAAC;YACjF;UACF;QAAE;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eAGDhF,OAAA;UACEiE,KAAK,EAAE;YACLS,QAAQ,EAAE,MAAM;YAChB2E,UAAU,EAAE,KAAK;YACjB5E,KAAK,EAAE,SAAS;YAChBa,YAAY,EAAE,MAAM;YACpBgE,UAAU,EAAE;UACd,CAAE;UAAA1E,QAAA,EAEDkC,YAAY,CAACyC;QAAO;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGNhF,OAAA;UACEiE,KAAK,EAAE;YACLK,OAAO,EAAE,MAAM;YACfkF,mBAAmB,EAAE,sCAAsC;YAC3DnE,GAAG,EAAE,MAAM;YACXwC,OAAO,EAAE,QAAQ;YACjB/D,eAAe,EAAE,SAAS;YAC1BJ,YAAY,EAAE;UAChB,CAAE;UAAAkB,QAAA,gBAGF5E,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACT,IAAI;cAACmJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/ChF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChFkC,YAAY,CAAC2C,WAAW,IAAI;cAAS;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACR,GAAG;cAACkJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ChF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChFkC,YAAY,CAAC4C,aAAa,IAAI;cAAe;gBAAA7E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACV,QAAQ;cAACoJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChFmC,UAAU,CAACD,YAAY,CAAC6C,UAAU;cAAC;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL8B,YAAY,CAAC8C,YAAY,iBACxB5J,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACV,QAAQ;cAACoJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChFmC,UAAU,CAACD,YAAY,CAAC8C,YAAY;cAAC;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA8B,YAAY,CAAC+C,oBAAoB,iBAChC7J,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACV,QAAQ;cAACoJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChFmC,UAAU,CAACD,YAAY,CAAC+C,oBAAoB;cAAC;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACP,GAAG;cAACiJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ChF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzEhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChF,CAACkC,YAAY,CAACgD,UAAU,IAAI,CAAC,EAAEC,cAAc,CAAC;cAAC;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACL,KAAK;cAAC+I,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChF,CAACkC,YAAY,CAACkD,cAAc,IAAI,CAAC,EAAED,cAAc,CAAC;cAAC;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEc,GAAG,EAAE;YAAS,CAAE;YAAAT,QAAA,gBACnE5E,OAAA,CAACN,aAAa;cAACgJ,IAAI,EAAE,EAAG;cAACzE,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAU;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,SAAS;kBAAED,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5EhF,OAAA;gBAAGiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEF,KAAK,EAAE,SAAS;kBAAE4D,MAAM,EAAE;gBAAE,CAAE;gBAAAzD,QAAA,EAChF,CAACkC,YAAY,CAACmD,aAAa,IAAI,CAAC,EAAEF,cAAc,CAAC;cAAC;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhF,OAAA;UACEiE,KAAK,EAAE;YACLyB,SAAS,EAAE,QAAQ;YACnBmC,OAAO,EAAE,MAAM;YACf/D,eAAe,EAAE,SAAS;YAC1BJ,YAAY,EAAE;UAChB,CAAE;UAAAkB,QAAA,gBAEF5E,OAAA;YAAIiE,KAAK,EAAE;cAAES,QAAQ,EAAE,UAAU;cAAEC,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAE4D,MAAM,EAAE;YAAgB,CAAE;YAAAzD,QAAA,EAAC;UAEnG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAKiE,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEe,GAAG,EAAE,QAAQ;cAAEmD,QAAQ,EAAE;YAAO,CAAE;YAAA5D,QAAA,gBAC/D5E,OAAA;cAAKiE,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEc,GAAG,EAAE;cAAS,CAAE;cAAAT,QAAA,gBACnE5E,OAAA;gBACEiE,KAAK,EAAE;kBACLZ,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdI,YAAY,EAAE,KAAK;kBACnBI,eAAe,EAAEgD,YAAY,CAACoD,cAAc,GAAG,SAAS,GAAG;gBAC7D;cAAE;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFhF,OAAA;gBAAMiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAED,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,GAAC,WAC9C,EAACkC,YAAY,CAACoD,cAAc,GAAG,SAAS,GAAG,UAAU;cAAA;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhF,OAAA;cAAKiE,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEc,GAAG,EAAE;cAAS,CAAE;cAAAT,QAAA,gBACnE5E,OAAA;gBACEiE,KAAK,EAAE;kBACLZ,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdI,YAAY,EAAE,KAAK;kBACnBI,eAAe,EAAEgD,YAAY,CAACqD,aAAa,GAAG,SAAS,GAAG;gBAC5D;cAAE;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFhF,OAAA;gBAAMiE,KAAK,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAED,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,GAAC,UAC/C,EAACkC,YAAY,CAACqD,aAAa,GAAG,SAAS,GAAG,UAAU;cAAA;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoF,GAAA,GArWIzD,sBAA6D;AAuWnE,eAAeA,sBAAsB;AAAC,IAAAD,EAAA,EAAA0D,GAAA;AAAAC,YAAA,CAAA3D,EAAA;AAAA2D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}