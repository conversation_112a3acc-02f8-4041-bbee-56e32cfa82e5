{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v6\",\n  key: \"1holv5\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"M12 15v6\",\n  key: \"a9ows0\"\n}]];\nconst GitCommitVertical = createLucideIcon(\"git-commit-vertical\", __iconNode);\nexport { __iconNode, GitCommitVertical as default };\n//# sourceMappingURL=git-commit-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}