{"ast": null, "code": "/**\n * @license React\n * react-dom.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nvar React = require(\"react\");\nfunction formatProdErrorMessage(code) {\n  var url = \"https://react.dev/errors/\" + code;\n  if (1 < arguments.length) {\n    url += \"?args[]=\" + encodeURIComponent(arguments[1]);\n    for (var i = 2; i < arguments.length; i++) url += \"&args[]=\" + encodeURIComponent(arguments[i]);\n  }\n  return \"Minified React error #\" + code + \"; visit \" + url + \" for the full message or use the non-minified dev environment for full errors and additional helpful warnings.\";\n}\nfunction noop() {}\nvar Internals = {\n    d: {\n      f: noop,\n      r: function () {\n        throw Error(formatProdErrorMessage(522));\n      },\n      D: noop,\n      C: noop,\n      L: noop,\n      m: noop,\n      X: noop,\n      S: noop,\n      M: noop\n    },\n    p: 0,\n    findDOMNode: null\n  },\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\nfunction createPortal$1(children, containerInfo, implementation) {\n  var key = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : null;\n  return {\n    $$typeof: REACT_PORTAL_TYPE,\n    key: null == key ? null : \"\" + key,\n    children: children,\n    containerInfo: containerInfo,\n    implementation: implementation\n  };\n}\nvar ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\nfunction getCrossOriginStringAs(as, input) {\n  if (\"font\" === as) return \"\";\n  if (\"string\" === typeof input) return \"use-credentials\" === input ? input : \"\";\n}\nexports.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = Internals;\nexports.createPortal = function (children, container) {\n  var key = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : null;\n  if (!container || 1 !== container.nodeType && 9 !== container.nodeType && 11 !== container.nodeType) throw Error(formatProdErrorMessage(299));\n  return createPortal$1(children, container, null, key);\n};\nexports.flushSync = function (fn) {\n  var previousTransition = ReactSharedInternals.T,\n    previousUpdatePriority = Internals.p;\n  try {\n    if (ReactSharedInternals.T = null, Internals.p = 2, fn) return fn();\n  } finally {\n    ReactSharedInternals.T = previousTransition, Internals.p = previousUpdatePriority, Internals.d.f();\n  }\n};\nexports.preconnect = function (href, options) {\n  \"string\" === typeof href && (options ? (options = options.crossOrigin, options = \"string\" === typeof options ? \"use-credentials\" === options ? options : \"\" : void 0) : options = null, Internals.d.C(href, options));\n};\nexports.prefetchDNS = function (href) {\n  \"string\" === typeof href && Internals.d.D(href);\n};\nexports.preinit = function (href, options) {\n  if (\"string\" === typeof href && options && \"string\" === typeof options.as) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin),\n      integrity = \"string\" === typeof options.integrity ? options.integrity : void 0,\n      fetchPriority = \"string\" === typeof options.fetchPriority ? options.fetchPriority : void 0;\n    \"style\" === as ? Internals.d.S(href, \"string\" === typeof options.precedence ? options.precedence : void 0, {\n      crossOrigin: crossOrigin,\n      integrity: integrity,\n      fetchPriority: fetchPriority\n    }) : \"script\" === as && Internals.d.X(href, {\n      crossOrigin: crossOrigin,\n      integrity: integrity,\n      fetchPriority: fetchPriority,\n      nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n    });\n  }\n};\nexports.preinitModule = function (href, options) {\n  if (\"string\" === typeof href) if (\"object\" === typeof options && null !== options) {\n    if (null == options.as || \"script\" === options.as) {\n      var crossOrigin = getCrossOriginStringAs(options.as, options.crossOrigin);\n      Internals.d.M(href, {\n        crossOrigin: crossOrigin,\n        integrity: \"string\" === typeof options.integrity ? options.integrity : void 0,\n        nonce: \"string\" === typeof options.nonce ? options.nonce : void 0\n      });\n    }\n  } else null == options && Internals.d.M(href);\n};\nexports.preload = function (href, options) {\n  if (\"string\" === typeof href && \"object\" === typeof options && null !== options && \"string\" === typeof options.as) {\n    var as = options.as,\n      crossOrigin = getCrossOriginStringAs(as, options.crossOrigin);\n    Internals.d.L(href, as, {\n      crossOrigin: crossOrigin,\n      integrity: \"string\" === typeof options.integrity ? options.integrity : void 0,\n      nonce: \"string\" === typeof options.nonce ? options.nonce : void 0,\n      type: \"string\" === typeof options.type ? options.type : void 0,\n      fetchPriority: \"string\" === typeof options.fetchPriority ? options.fetchPriority : void 0,\n      referrerPolicy: \"string\" === typeof options.referrerPolicy ? options.referrerPolicy : void 0,\n      imageSrcSet: \"string\" === typeof options.imageSrcSet ? options.imageSrcSet : void 0,\n      imageSizes: \"string\" === typeof options.imageSizes ? options.imageSizes : void 0,\n      media: \"string\" === typeof options.media ? options.media : void 0\n    });\n  }\n};\nexports.preloadModule = function (href, options) {\n  if (\"string\" === typeof href) if (options) {\n    var crossOrigin = getCrossOriginStringAs(options.as, options.crossOrigin);\n    Internals.d.m(href, {\n      as: \"string\" === typeof options.as && \"script\" !== options.as ? options.as : void 0,\n      crossOrigin: crossOrigin,\n      integrity: \"string\" === typeof options.integrity ? options.integrity : void 0\n    });\n  } else Internals.d.m(href);\n};\nexports.requestFormReset = function (form) {\n  Internals.d.r(form);\n};\nexports.unstable_batchedUpdates = function (fn, a) {\n  return fn(a);\n};\nexports.useFormState = function (action, initialState, permalink) {\n  return ReactSharedInternals.H.useFormState(action, initialState, permalink);\n};\nexports.useFormStatus = function () {\n  return ReactSharedInternals.H.useHostTransitionStatus();\n};\nexports.version = \"19.1.0\";", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}