{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{X,ChevronLeft,ChevronRight}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Custom hook for CORS-safe image loading\nconst useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{console.log('🔄 Fetching image via CORS-safe method:',imagePath);// Fetch image as blob to bypass CORS restrictions\nconst response=await fetch(imagePath,{method:'GET',mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);setImageUrl(objectUrl);console.log('✅ Image loaded successfully via fetch');}catch(err){console.error('❌ Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// CORS-safe image component for lightbox\nconst SafeImage=_ref=>{let{src,alt,style,onClick,onLoad}=_ref;const{imageUrl,loading,error}=useImageLoader(src);if(loading){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'rgba(0, 0, 0, 0.3)',color:'white'}),children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Loading...\"})]})});}if(error||!imageUrl){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'rgba(0, 0, 0, 0.3)',color:'white',border:'2px dashed rgba(255, 255, 255, 0.3)'}),children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:\"Image unavailable\"}),error&&/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',marginTop:'0.25rem',color:'#ccc'},children:error})]})});}return/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:alt,style:style,onClick:onClick,onLoad:()=>{console.log('✅ Image rendered successfully in lightbox');onLoad===null||onLoad===void 0?void 0:onLoad();},draggable:false});};const ImageLightbox=_ref2=>{let{images,initialIndex,isOpen,onClose,altPrefix='Image'}=_ref2;const[currentIndex,setCurrentIndex]=useState(initialIndex);const[isZoomed,setIsZoomed]=useState(false);const[imageLoaded,setImageLoaded]=useState(false);// Reset state when lightbox opens\nuseEffect(()=>{if(isOpen){setCurrentIndex(initialIndex);setIsZoomed(false);setImageLoaded(false);document.body.style.overflow='hidden';// Prevent background scrolling\n}else{document.body.style.overflow='unset';}return()=>{document.body.style.overflow='unset';};},[isOpen,initialIndex]);// Keyboard navigation\nconst handleKeyDown=useCallback(e=>{if(!isOpen)return;switch(e.key){case'Escape':onClose();break;case'ArrowLeft':goToPrevious();break;case'ArrowRight':goToNext();break;}},[isOpen,onClose]);useEffect(()=>{document.addEventListener('keydown',handleKeyDown);return()=>document.removeEventListener('keydown',handleKeyDown);},[handleKeyDown]);const goToNext=()=>{setCurrentIndex(prev=>(prev+1)%images.length);setImageLoaded(false);setIsZoomed(false);};const goToPrevious=()=>{setCurrentIndex(prev=>(prev-1+images.length)%images.length);setImageLoaded(false);setIsZoomed(false);};const goToImage=index=>{setCurrentIndex(index);setImageLoaded(false);setIsZoomed(false);};const toggleZoom=()=>{setIsZoomed(!isZoomed);};if(!isOpen)return null;return/*#__PURE__*/_jsxs(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.9)',zIndex:9999,display:'flex',alignItems:'center',justifyContent:'center',padding:'2rem'},onClick:onClose,children:[/*#__PURE__*/_jsx(\"button\",{onClick:onClose,style:{position:'absolute',top:'1rem',right:'1rem',background:'rgba(255, 255, 255, 0.1)',border:'none',borderRadius:'50%',width:'48px',height:'48px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',cursor:'pointer',transition:'all 0.2s ease',zIndex:10001},onMouseEnter:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.2)';},onMouseLeave:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.1)';},children:/*#__PURE__*/_jsx(X,{size:24})}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'1rem',left:'1rem',background:'rgba(0, 0, 0, 0.7)',color:'white',padding:'0.5rem 1rem',borderRadius:'20px',fontSize:'0.875rem',fontWeight:'500',zIndex:10001},children:[currentIndex+1,\" / \",images.length]}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',maxWidth:'90vw',maxHeight:'80vh',display:'flex',alignItems:'center',justifyContent:'center'},onClick:e=>e.stopPropagation(),children:[images.length>1&&/*#__PURE__*/_jsx(\"button\",{onClick:goToPrevious,style:{position:'absolute',left:'-4rem',top:'50%',transform:'translateY(-50%)',background:'rgba(255, 255, 255, 0.1)',border:'none',borderRadius:'50%',width:'56px',height:'56px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',cursor:'pointer',transition:'all 0.2s ease',zIndex:10001},onMouseEnter:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.2)';e.currentTarget.style.transform='translateY(-50%) scale(1.1)';},onMouseLeave:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.1)';e.currentTarget.style.transform='translateY(-50%) scale(1)';},children:/*#__PURE__*/_jsx(ChevronLeft,{size:28})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'relative',overflow:isZoomed?'auto':'hidden',borderRadius:'12px',maxWidth:'100%',maxHeight:'100%'},children:/*#__PURE__*/_jsx(SafeImage,{src:images[currentIndex],alt:\"\".concat(altPrefix,\" \").concat(currentIndex+1),style:{maxWidth:isZoomed?'none':'90vw',maxHeight:isZoomed?'none':'80vh',width:isZoomed?'150%':'auto',height:'auto',borderRadius:'12px',transition:'all 0.3s ease',cursor:isZoomed?'grab':'zoom-in'},onLoad:()=>setImageLoaded(true),onClick:toggleZoom})}),images.length>1&&/*#__PURE__*/_jsx(\"button\",{onClick:goToNext,style:{position:'absolute',right:'-4rem',top:'50%',transform:'translateY(-50%)',background:'rgba(255, 255, 255, 0.1)',border:'none',borderRadius:'50%',width:'56px',height:'56px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',cursor:'pointer',transition:'all 0.2s ease',zIndex:10001},onMouseEnter:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.2)';e.currentTarget.style.transform='translateY(-50%) scale(1.1)';},onMouseLeave:e=>{e.currentTarget.style.background='rgba(255, 255, 255, 0.1)';e.currentTarget.style.transform='translateY(-50%) scale(1)';},children:/*#__PURE__*/_jsx(ChevronRight,{size:28})})]}),images.length>1&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',bottom:'2rem',left:'50%',transform:'translateX(-50%)',display:'flex',gap:'0.5rem',background:'rgba(0, 0, 0, 0.7)',padding:'1rem',borderRadius:'12px',maxWidth:'90vw',overflowX:'auto'},onClick:e=>e.stopPropagation(),children:images.map((image,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',borderRadius:'8px',cursor:'pointer',opacity:index===currentIndex?1:0.6,border:index===currentIndex?'2px solid white':'2px solid transparent',transition:'all 0.2s ease',overflow:'hidden'},onClick:()=>goToImage(index),onMouseEnter:e=>{if(index!==currentIndex){e.currentTarget.style.opacity='0.8';}},onMouseLeave:e=>{if(index!==currentIndex){e.currentTarget.style.opacity='0.6';}},children:/*#__PURE__*/_jsx(SafeImage,{src:image,alt:\"\".concat(altPrefix,\" \").concat(index+1,\" thumbnail\"),style:{width:'100%',height:'100%',objectFit:'cover'}})},index))})]});};export default ImageLightbox;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}