{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"14\",\n  y: \"4\",\n  width: \"4\",\n  height: \"16\",\n  rx: \"1\",\n  key: \"zuxfzm\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"4\",\n  width: \"4\",\n  height: \"16\",\n  rx: \"1\",\n  key: \"1okwgv\"\n}]];\nconst Pause = createLucideIcon(\"pause\", __iconNode);\nexport { __iconNode, Pause as default };\n//# sourceMappingURL=pause.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}