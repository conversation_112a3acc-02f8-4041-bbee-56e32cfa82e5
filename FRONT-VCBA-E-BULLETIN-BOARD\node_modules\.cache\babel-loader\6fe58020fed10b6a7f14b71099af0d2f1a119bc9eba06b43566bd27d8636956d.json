{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M16 16s-1.5-2-4-2-4 2-4 2\",\n  key: \"epbg0q\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"9.01\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"yxxnd0\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"15.01\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"1p4y9e\"\n}]];\nconst Frown = createLucideIcon(\"frown\", __iconNode);\nexport { __iconNode, Frown as default };\n//# sourceMappingURL=frown.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}