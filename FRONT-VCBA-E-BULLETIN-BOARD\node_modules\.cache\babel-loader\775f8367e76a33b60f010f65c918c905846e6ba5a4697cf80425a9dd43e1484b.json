{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22h6a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3.072\",\n  key: \"n6s66f\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"m6.69 16.479 1.29 4.88a.5.5 0 0 1-.698.591l-1.843-.849a1 1 0 0 0-.88.001l-1.846.85a.5.5 0 0 1-.693-.593l1.29-4.88\",\n  key: \"1e6yvx\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"14\",\n  r: \"3\",\n  key: \"ufru5t\"\n}]];\nconst FileBadge = createLucideIcon(\"file-badge\", __iconNode);\nexport { __iconNode, FileBadge as default };\n//# sourceMappingURL=file-badge.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}