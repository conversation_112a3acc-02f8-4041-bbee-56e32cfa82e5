{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 19V7a2 2 0 0 0-2-2H9\",\n  key: \"15peso\"\n}], [\"path\", {\n  d: \"M15 19H9\",\n  key: \"18q6dt\"\n}], [\"path\", {\n  d: \"M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14\",\n  key: \"1dkp3j\"\n}], [\"path\", {\n  d: \"M2 13v5a1 1 0 0 0 1 1h2\",\n  key: \"pkmmzz\"\n}], [\"path\", {\n  d: \"M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02\",\n  key: \"1n26pd\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"1nxcgd\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"gzo7y7\"\n}]];\nconst TruckElectric = createLucideIcon(\"truck-electric\", __iconNode);\nexport { __iconNode, TruckElectric as default };\n//# sourceMappingURL=truck-electric.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}