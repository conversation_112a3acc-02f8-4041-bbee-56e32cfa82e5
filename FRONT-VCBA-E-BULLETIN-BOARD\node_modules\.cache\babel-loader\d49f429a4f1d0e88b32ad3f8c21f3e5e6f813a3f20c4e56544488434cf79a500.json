{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12v3a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h7\",\n  key: \"tqtdkg\"\n}], [\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"m16 8 5-5\",\n  key: \"15mbrl\"\n}]];\nconst MessageSquareShare = createLucideIcon(\"message-square-share\", __iconNode);\nexport { __iconNode, MessageSquareShare as default };\n//# sourceMappingURL=message-square-share.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}