{"ast": null, "code": "// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, ADMIN_USER_DATA_KEY, ADMIN_REFRESH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\n// Admin-specific token manager\nclass AdminTokenManager {\n  getToken() {\n    return localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n  setToken(token) {\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  }\n  removeToken() {\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n  }\n}\nconst adminTokenManager = new AdminTokenManager();\nexport class AdminAuthService {\n  /**\n   * Custom request method with admin token\n   */\n  static async request(method, endpoint, data, retryCount = 0) {\n    const token = adminTokenManager.getToken();\n    const headers = {};\n\n    // Only set Content-Type for non-FormData requests\n    if (!(data instanceof FormData)) {\n      headers['Content-Type'] = 'application/json';\n    }\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    const config = {\n      method,\n      headers,\n      credentials: 'include'\n    };\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      // Handle FormData differently - don't stringify it\n      config.body = data instanceof FormData ? data : JSON.stringify(data);\n    }\n    const url = `${API_BASE_URL}${endpoint}`;\n    try {\n      console.log(`🔄 Admin API Request: ${method} ${url}`);\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        var _responseData$error;\n        if (response.status === 401) {\n          // Token expired or invalid - clear auth and redirect\n          adminTokenManager.removeToken();\n          throw new Error('Authentication required');\n        }\n        const responseData = await response.json().catch(() => ({\n          message: 'Request failed'\n        }));\n        const errorMessage = (responseData === null || responseData === void 0 ? void 0 : responseData.message) || (responseData === null || responseData === void 0 ? void 0 : (_responseData$error = responseData.error) === null || _responseData$error === void 0 ? void 0 : _responseData$error.message) || `HTTP ${response.status}`;\n        throw new Error(errorMessage);\n      }\n      const responseData = await response.json();\n      console.log(`✅ Admin API Success: ${method} ${endpoint}`);\n      return responseData;\n    } catch (error) {\n      console.error(`❌ Admin API request failed: ${method} ${endpoint}`, error);\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error instanceof TypeError || error instanceof Error && error.message.includes('Failed to fetch'))) {\n        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/3)...`);\n        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff\n        return this.request(method, endpoint, data, retryCount + 1);\n      }\n\n      // Enhanced error information for connection issues\n      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n        throw new Error(`❌ Network connection failed. Please check:\\n1. Backend server is running on ${API_BASE_URL}\\n2. CORS is properly configured\\n3. No firewall blocking the connection`);\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * Get current authenticated admin user\n   */\n  static async getCurrentUser() {\n    try {\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n      const response = await this.request('GET', API_ENDPOINTS.AUTH.PROFILE);\n      if (response && response.user) {\n        return response.user;\n      }\n      return null;\n    } catch (error) {\n      console.error('Failed to get current admin user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login admin user\n   */\n  static async login(credentials) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, {\n        ...credentials,\n        userType: 'admin'\n      });\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user;\n        const transformedUser = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          profilePicture: rawUser.profile_picture,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at\n        };\n\n        // Verify the user is actually an admin\n        if (transformedUser.role !== 'admin') {\n          throw new Error('Access denied: Admin privileges required');\n        }\n\n        // Store tokens and transformed user data in admin-specific keys\n        adminTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(ADMIN_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedUser));\n        return {\n          success: true,\n          message: response.message || 'Admin login successful',\n          data: {\n            ...response.data,\n            user: transformedUser\n          }\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Admin login failed');\n    } catch (error) {\n      console.error('AdminAuthService.login error:', error);\n      throw new Error(error.message || 'Admin login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.ADMIN_REGISTER, data);\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'Admin registration initiated successfully',\n          data: response.data\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Admin registration failed');\n    } catch (error) {\n      console.error('AdminAuthService.registerAdmin error:', error);\n      throw new Error(error.message || 'Admin registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.VERIFY_OTP, data);\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'OTP verification failed');\n    } catch (error) {\n      console.error('AdminAuthService.verifyOtp error:', error);\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.RESEND_OTP, {\n        email\n      });\n      if (response && response.success) {\n        return response;\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to resend OTP');\n    } catch (error) {\n      console.error('AdminAuthService.resendOtp error:', error);\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Logout admin user\n   */\n  static async logout() {\n    try {\n      console.log('🚪 AdminAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AdminAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ AdminAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 AdminAuthService - Clearing admin local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear admin local storage and tokens\n   */\n  static clearLocalStorage() {\n    console.log('🧹 AdminAuthService - Clearing admin authentication data');\n    adminTokenManager.removeToken();\n    localStorage.removeItem(ADMIN_USER_DATA_KEY);\n    localStorage.removeItem(ADMIN_REFRESH_TOKEN_KEY);\n    console.log('✅ AdminAuthService - Admin authentication data cleared');\n  }\n\n  /**\n   * Check if admin is authenticated\n   */\n  static isAuthenticated() {\n    const token = adminTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'admin');\n  }\n\n  /**\n   * Get stored admin user data\n   */\n  static getStoredUser() {\n    try {\n      const userData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'admin' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored admin user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get admin user role\n   */\n  static getUserRole() {\n    const user = this.getStoredUser();\n    return (user === null || user === void 0 ? void 0 : user.role) === 'admin' ? 'admin' : null;\n  }\n\n  /**\n   * HTTP GET method with admin authentication\n   */\n  static async get(endpoint) {\n    return this.request('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with admin authentication\n   */\n  static async post(endpoint, data) {\n    return this.request('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with admin authentication\n   */\n  static async put(endpoint, data) {\n    return this.request('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with admin authentication\n   */\n  static async delete(endpoint) {\n    return this.request('DELETE', endpoint);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  static async uploadProfilePicture(file) {\n    console.log('📤 AdminAuthService - Uploading profile picture:', file.name);\n    try {\n      const formData = new FormData();\n      formData.append('profilePicture', file);\n\n      // Get the token for authorization\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Make direct fetch request with proper headers for file upload\n      const response = await fetch(`${API_BASE_URL}/admin/profile/picture`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n          // Don't set Content-Type for FormData - browser will set it with boundary\n        },\n        body: formData\n      });\n      const result = await response.json();\n      if (!response.ok || !result.success) {\n        var _result$error;\n        throw new Error(((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || 'Failed to upload profile picture');\n      }\n      console.log('✅ AdminAuthService - Profile picture uploaded successfully');\n\n      // Transform the admin data\n      const rawAdmin = result.data.admin;\n      const transformedAdmin = {\n        id: rawAdmin.admin_id,\n        email: rawAdmin.email,\n        role: 'admin',\n        firstName: rawAdmin.first_name,\n        lastName: rawAdmin.last_name,\n        middleName: rawAdmin.middle_name,\n        suffix: rawAdmin.suffix,\n        phoneNumber: rawAdmin.phone_number,\n        department: rawAdmin.department,\n        position: rawAdmin.position,\n        grade_level: rawAdmin.grade_level,\n        profilePicture: rawAdmin.profile_picture,\n        isActive: Boolean(rawAdmin.is_active),\n        lastLogin: rawAdmin.last_login,\n        createdAt: rawAdmin.account_created_at,\n        updatedAt: rawAdmin.account_updated_at\n      };\n\n      // Update stored user data\n      localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n      return {\n        admin: transformedAdmin,\n        profilePicture: result.data.profilePicture\n      };\n    } catch (error) {\n      console.error('❌ AdminAuthService - Profile picture upload failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Remove profile picture\n   */\n  static async removeProfilePicture() {\n    console.log('🗑️ AdminAuthService - Removing profile picture');\n    const response = await this.request('DELETE', '/admin/profile/picture');\n    if (!response.success) {\n      var _response$error;\n      throw new Error(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Failed to remove profile picture');\n    }\n    console.log('✅ AdminAuthService - Profile picture removed successfully');\n\n    // Transform the admin data\n    const rawAdmin = response.data.admin;\n    const transformedAdmin = {\n      id: rawAdmin.admin_id,\n      email: rawAdmin.email,\n      role: 'admin',\n      firstName: rawAdmin.first_name,\n      lastName: rawAdmin.last_name,\n      middleName: rawAdmin.middle_name,\n      suffix: rawAdmin.suffix,\n      phoneNumber: rawAdmin.phone_number,\n      department: rawAdmin.department,\n      position: rawAdmin.position,\n      grade_level: rawAdmin.grade_level,\n      profilePicture: rawAdmin.profile_picture,\n      isActive: Boolean(rawAdmin.is_active),\n      lastLogin: rawAdmin.last_login,\n      createdAt: rawAdmin.account_created_at,\n      updatedAt: rawAdmin.account_updated_at\n    };\n\n    // Update stored user data\n    localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n    return transformedAdmin;\n  }\n}\nexport default AdminAuthService;", "map": {"version": 3, "names": ["API_ENDPOINTS", "ADMIN_USER_DATA_KEY", "ADMIN_REFRESH_TOKEN_KEY", "ADMIN_AUTH_TOKEN_KEY", "API_BASE_URL", "AdminTokenManager", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "removeToken", "removeItem", "getAuthHeaders", "Authorization", "adminTokenManager", "AdminAuthService", "request", "method", "endpoint", "data", "retryCount", "headers", "FormData", "config", "credentials", "body", "JSON", "stringify", "url", "console", "log", "response", "fetch", "ok", "_responseData$error", "status", "Error", "responseData", "json", "catch", "message", "errorMessage", "error", "TypeError", "includes", "Promise", "resolve", "setTimeout", "getCurrentUser", "AUTH", "PROFILE", "user", "login", "LOGIN", "userType", "success", "rawUser", "transformedUser", "id", "admin_id", "student_id", "email", "role", "firstName", "first_name", "lastName", "last_name", "middleName", "middle_name", "suffix", "phoneNumber", "phone_number", "phone", "department", "position", "grade_level", "studentNumber", "student_number", "profilePicture", "profile_picture", "isActive", "Boolean", "is_active", "lastLogin", "last_login", "createdAt", "account_created_at", "created_at", "updatedAt", "account_updated_at", "updated_at", "accessToken", "refreshToken", "registerAdmin", "ADMIN_REGISTER", "verifyOtp", "VERIFY_OTP", "resendOtp", "RESEND_OTP", "logout", "LOGOUT", "warn", "clearLocalStorage", "isAuthenticated", "userData", "getStoredUser", "parse", "getUserRole", "get", "post", "put", "delete", "uploadProfilePicture", "file", "name", "formData", "append", "result", "_result$error", "rawAdmin", "admin", "transformedAdmin", "removeProfilePicture", "_response$error"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/admin-auth.service.ts"], "sourcesContent": ["// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, ADMIN_USER_DATA_KEY, ADMIN_REFRESH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\nimport {\n  LoginCredentials,\n  AdminRegistrationData,\n  OtpVerificationData,\n  AuthResponse,\n  RegistrationResponse,\n  OtpVerificationResponse,\n  User,\n  ApiResponse,\n} from '../types';\n\n// Admin-specific token manager\nclass AdminTokenManager {\n  getToken(): string | null {\n    return localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n\n  setToken(token: string): void {\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  }\n\n  removeToken(): void {\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    return token ? { Authorization: `Bearer ${token}` } : {};\n  }\n}\n\nconst adminTokenManager = new AdminTokenManager();\n\nexport class AdminAuthService {\n  /**\n   * Custom request method with admin token\n   */\n  public static async request<T>(\n    method: string,\n    endpoint: string,\n    data?: any,\n    retryCount: number = 0\n  ): Promise<any> {\n    const token = adminTokenManager.getToken();\n    const headers: Record<string, string> = {};\n\n    // Only set Content-Type for non-FormData requests\n    if (!(data instanceof FormData)) {\n      headers['Content-Type'] = 'application/json';\n    }\n\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    const config: RequestInit = {\n      method,\n      headers,\n      credentials: 'include',\n    };\n\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      // Handle FormData differently - don't stringify it\n      config.body = data instanceof FormData ? data : JSON.stringify(data);\n    }\n\n    const url = `${API_BASE_URL}${endpoint}`;\n\n    try {\n      console.log(`🔄 Admin API Request: ${method} ${url}`);\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Token expired or invalid - clear auth and redirect\n          adminTokenManager.removeToken();\n          throw new Error('Authentication required');\n        }\n\n        const responseData = await response.json().catch(() => ({ message: 'Request failed' }));\n        const errorMessage = responseData?.message || responseData?.error?.message || `HTTP ${response.status}`;\n        throw new Error(errorMessage);\n      }\n\n      const responseData = await response.json();\n      console.log(`✅ Admin API Success: ${method} ${endpoint}`);\n      return responseData;\n    } catch (error) {\n      console.error(`❌ Admin API request failed: ${method} ${endpoint}`, error);\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error instanceof TypeError || (error instanceof Error && error.message.includes('Failed to fetch')))) {\n        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/3)...`);\n        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff\n        return this.request<T>(method, endpoint, data, retryCount + 1);\n      }\n\n      // Enhanced error information for connection issues\n      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n        throw new Error(`❌ Network connection failed. Please check:\\n1. Backend server is running on ${API_BASE_URL}\\n2. CORS is properly configured\\n3. No firewall blocking the connection`);\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Get current authenticated admin user\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n\n      const response = await this.request<{ user: User }>('GET', API_ENDPOINTS.AUTH.PROFILE);\n\n      if (response && response.user) {\n        return response.user;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Failed to get current admin user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login admin user\n   */\n  static async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, { ...credentials, userType: 'admin' });\n\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user as any;\n        const transformedUser: User = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          profilePicture: rawUser.profile_picture,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at,\n        };\n\n        // Verify the user is actually an admin\n        if (transformedUser.role !== 'admin') {\n          throw new Error('Access denied: Admin privileges required');\n        }\n\n        // Store tokens and transformed user data in admin-specific keys\n        adminTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(ADMIN_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedUser));\n\n        return {\n          success: true,\n          message: response.message || 'Admin login successful',\n          data: {\n            ...response.data,\n            user: transformedUser,\n          },\n        };\n      }\n\n      throw new Error(response?.message || 'Admin login failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.login error:', error);\n      throw new Error(error.message || 'Admin login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data: AdminRegistrationData): Promise<RegistrationResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.ADMIN_REGISTER, data);\n\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'Admin registration initiated successfully',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response?.message || 'Admin registration failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.registerAdmin error:', error);\n      throw new Error(error.message || 'Admin registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data: OtpVerificationData): Promise<OtpVerificationResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.VERIFY_OTP, data);\n\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response?.message || 'OTP verification failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.verifyOtp error:', error);\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email: string): Promise<ApiResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.RESEND_OTP, { email });\n\n      if (response && response.success) {\n        return response;\n      }\n\n      throw new Error(response?.message || 'Failed to resend OTP');\n    } catch (error: any) {\n      console.error('AdminAuthService.resendOtp error:', error);\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Logout admin user\n   */\n  static async logout(): Promise<void> {\n    try {\n      console.log('🚪 AdminAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AdminAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ AdminAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 AdminAuthService - Clearing admin local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear admin local storage and tokens\n   */\n  static clearLocalStorage(): void {\n    console.log('🧹 AdminAuthService - Clearing admin authentication data');\n    adminTokenManager.removeToken();\n    localStorage.removeItem(ADMIN_USER_DATA_KEY);\n    localStorage.removeItem(ADMIN_REFRESH_TOKEN_KEY);\n    console.log('✅ AdminAuthService - Admin authentication data cleared');\n  }\n\n  /**\n   * Check if admin is authenticated\n   */\n  static isAuthenticated(): boolean {\n    const token = adminTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'admin');\n  }\n\n  /**\n   * Get stored admin user data\n   */\n  static getStoredUser(): User | null {\n    try {\n      const userData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'admin' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored admin user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get admin user role\n   */\n  static getUserRole(): 'admin' | null {\n    const user = this.getStoredUser();\n    return user?.role === 'admin' ? 'admin' : null;\n  }\n\n  /**\n   * HTTP GET method with admin authentication\n   */\n  static async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with admin authentication\n   */\n  static async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with admin authentication\n   */\n  static async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with admin authentication\n   */\n  static async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('DELETE', endpoint);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<{ admin: User; profilePicture: string }> {\n    console.log('📤 AdminAuthService - Uploading profile picture:', file.name);\n\n    try {\n      const formData = new FormData();\n      formData.append('profilePicture', file);\n\n      // Get the token for authorization\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Make direct fetch request with proper headers for file upload\n      const response = await fetch(`${API_BASE_URL}/admin/profile/picture`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          // Don't set Content-Type for FormData - browser will set it with boundary\n        },\n        body: formData,\n      });\n\n      const result = await response.json();\n\n      if (!response.ok || !result.success) {\n        throw new Error(result.error?.message || 'Failed to upload profile picture');\n      }\n\n      console.log('✅ AdminAuthService - Profile picture uploaded successfully');\n\n      // Transform the admin data\n      const rawAdmin = result.data.admin;\n      const transformedAdmin: User = {\n        id: rawAdmin.admin_id,\n        email: rawAdmin.email,\n        role: 'admin',\n        firstName: rawAdmin.first_name,\n        lastName: rawAdmin.last_name,\n        middleName: rawAdmin.middle_name,\n        suffix: rawAdmin.suffix,\n        phoneNumber: rawAdmin.phone_number,\n        department: rawAdmin.department,\n        position: rawAdmin.position,\n        grade_level: rawAdmin.grade_level,\n        profilePicture: rawAdmin.profile_picture,\n        isActive: Boolean(rawAdmin.is_active),\n        lastLogin: rawAdmin.last_login,\n        createdAt: rawAdmin.account_created_at,\n        updatedAt: rawAdmin.account_updated_at,\n      };\n\n      // Update stored user data\n      localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n\n      return {\n        admin: transformedAdmin,\n        profilePicture: result.data.profilePicture\n      };\n    } catch (error: any) {\n      console.error('❌ AdminAuthService - Profile picture upload failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Remove profile picture\n   */\n  static async removeProfilePicture(): Promise<User> {\n    console.log('🗑️ AdminAuthService - Removing profile picture');\n\n    const response = await this.request('DELETE', '/admin/profile/picture');\n\n    if (!response.success) {\n      throw new Error(response.error?.message || 'Failed to remove profile picture');\n    }\n\n    console.log('✅ AdminAuthService - Profile picture removed successfully');\n\n    // Transform the admin data\n    const rawAdmin = response.data.admin;\n    const transformedAdmin: User = {\n      id: rawAdmin.admin_id,\n      email: rawAdmin.email,\n      role: 'admin',\n      firstName: rawAdmin.first_name,\n      lastName: rawAdmin.last_name,\n      middleName: rawAdmin.middle_name,\n      suffix: rawAdmin.suffix,\n      phoneNumber: rawAdmin.phone_number,\n      department: rawAdmin.department,\n      position: rawAdmin.position,\n      grade_level: rawAdmin.grade_level,\n      profilePicture: rawAdmin.profile_picture,\n      isActive: Boolean(rawAdmin.is_active),\n      lastLogin: rawAdmin.last_login,\n      createdAt: rawAdmin.account_created_at,\n      updatedAt: rawAdmin.account_updated_at,\n    };\n\n    // Update stored user data\n    localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n\n    return transformedAdmin;\n  }\n}\n\nexport default AdminAuthService;\n"], "mappings": "AAAA;AACA,SAASA,aAAa,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,YAAY,QAAQ,qBAAqB;AAYrI;AACA,MAAMC,iBAAiB,CAAC;EACtBC,QAAQA,CAAA,EAAkB;IACxB,OAAOC,YAAY,CAACC,OAAO,CAACL,oBAAoB,CAAC;EACnD;EAEAM,QAAQA,CAACC,KAAa,EAAQ;IAC5BH,YAAY,CAACI,OAAO,CAACR,oBAAoB,EAAEO,KAAK,CAAC;EACnD;EAEAE,WAAWA,CAAA,EAAS;IAClBL,YAAY,CAACM,UAAU,CAACV,oBAAoB,CAAC;EAC/C;EAEAW,cAAcA,CAAA,EAA2B;IACvC,MAAMJ,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;IAC7B,OAAOI,KAAK,GAAG;MAAEK,aAAa,EAAE,UAAUL,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;AACF;AAEA,MAAMM,iBAAiB,GAAG,IAAIX,iBAAiB,CAAC,CAAC;AAEjD,OAAO,MAAMY,gBAAgB,CAAC;EAC5B;AACF;AACA;EACE,aAAoBC,OAAOA,CACzBC,MAAc,EACdC,QAAgB,EAChBC,IAAU,EACVC,UAAkB,GAAG,CAAC,EACR;IACd,MAAMZ,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;IAC1C,MAAMiB,OAA+B,GAAG,CAAC,CAAC;;IAE1C;IACA,IAAI,EAAEF,IAAI,YAAYG,QAAQ,CAAC,EAAE;MAC/BD,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;IAC9C;IAEA,IAAIb,KAAK,EAAE;MACTa,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUb,KAAK,EAAE;IAC9C;IAEA,MAAMe,MAAmB,GAAG;MAC1BN,MAAM;MACNI,OAAO;MACPG,WAAW,EAAE;IACf,CAAC;IAED,IAAIL,IAAI,KAAKF,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,OAAO,CAAC,EAAE;MACzE;MACAM,MAAM,CAACE,IAAI,GAAGN,IAAI,YAAYG,QAAQ,GAAGH,IAAI,GAAGO,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;IACtE;IAEA,MAAMS,GAAG,GAAG,GAAG1B,YAAY,GAAGgB,QAAQ,EAAE;IAExC,IAAI;MACFW,OAAO,CAACC,GAAG,CAAC,yBAAyBb,MAAM,IAAIW,GAAG,EAAE,CAAC;MACrD,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEL,MAAM,CAAC;MAEzC,IAAI,CAACQ,QAAQ,CAACE,EAAE,EAAE;QAAA,IAAAC,mBAAA;QAChB,IAAIH,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UAC3B;UACArB,iBAAiB,CAACJ,WAAW,CAAC,CAAC;UAC/B,MAAM,IAAI0B,KAAK,CAAC,yBAAyB,CAAC;QAC5C;QAEA,MAAMC,YAAY,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;UAAEC,OAAO,EAAE;QAAiB,CAAC,CAAC,CAAC;QACvF,MAAMC,YAAY,GAAG,CAAAJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,OAAO,MAAIH,YAAY,aAAZA,YAAY,wBAAAH,mBAAA,GAAZG,YAAY,CAAEK,KAAK,cAAAR,mBAAA,uBAAnBA,mBAAA,CAAqBM,OAAO,KAAI,QAAQT,QAAQ,CAACI,MAAM,EAAE;QACvG,MAAM,IAAIC,KAAK,CAACK,YAAY,CAAC;MAC/B;MAEA,MAAMJ,YAAY,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAC1CT,OAAO,CAACC,GAAG,CAAC,wBAAwBb,MAAM,IAAIC,QAAQ,EAAE,CAAC;MACzD,OAAOmB,YAAY;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,+BAA+BzB,MAAM,IAAIC,QAAQ,EAAE,EAAEwB,KAAK,CAAC;;MAEzE;MACA,IAAItB,UAAU,GAAG,CAAC,KAAKsB,KAAK,YAAYC,SAAS,IAAKD,KAAK,YAAYN,KAAK,IAAIM,KAAK,CAACF,OAAO,CAACI,QAAQ,CAAC,iBAAiB,CAAE,CAAC,EAAE;QAC3Hf,OAAO,CAACC,GAAG,CAAC,gCAAgCV,UAAU,GAAG,CAAC,QAAQ,CAAC;QACnE,MAAM,IAAIyB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,IAAI1B,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,OAAO,IAAI,CAACJ,OAAO,CAAIC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC;MAChE;;MAEA;MACA,IAAIsB,KAAK,YAAYC,SAAS,IAAID,KAAK,CAACF,OAAO,CAACI,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC3E,MAAM,IAAIR,KAAK,CAAC,+EAA+ElC,YAAY,0EAA0E,CAAC;MACxL;MAEA,MAAMwC,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAaM,cAAcA,CAAA,EAAyB;IAClD,IAAI;MACF,MAAMxC,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;MAC1C,IAAI,CAACI,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MAEA,MAAMuB,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAiB,KAAK,EAAElB,aAAa,CAACmD,IAAI,CAACC,OAAO,CAAC;MAEtF,IAAInB,QAAQ,IAAIA,QAAQ,CAACoB,IAAI,EAAE;QAC7B,OAAOpB,QAAQ,CAACoB,IAAI;MACtB;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAaU,KAAKA,CAAC5B,WAA6B,EAAyB;IACvE,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACmD,IAAI,CAACI,KAAK,EAAE;QAAE,GAAG7B,WAAW;QAAE8B,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAE5G,IAAIvB,QAAQ,IAAIA,QAAQ,CAACwB,OAAO,IAAIxB,QAAQ,CAACZ,IAAI,EAAE;QACjD;QACA,MAAMqC,OAAO,GAAGzB,QAAQ,CAACZ,IAAI,CAACgC,IAAW;QACzC,MAAMM,eAAqB,GAAG;UAC5BC,EAAE,EAAEF,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACI,UAAU;UAC1CC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBC,IAAI,EAAEN,OAAO,CAACG,QAAQ,GAAG,OAAO,GAAG,SAAS;UAC5CI,SAAS,EAAEP,OAAO,CAACQ,UAAU;UAC7BC,QAAQ,EAAET,OAAO,CAACU,SAAS;UAC3BC,UAAU,EAAEX,OAAO,CAACY,WAAW;UAC/BC,MAAM,EAAEb,OAAO,CAACa,MAAM;UACtBC,WAAW,EAAEd,OAAO,CAACe,YAAY,IAAIf,OAAO,CAACgB,KAAK;UAClDC,UAAU,EAAEjB,OAAO,CAACiB,UAAU;UAC9BC,QAAQ,EAAElB,OAAO,CAACkB,QAAQ;UAC1BC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;UAChCC,aAAa,EAAEpB,OAAO,CAACqB,cAAc;UACrCC,cAAc,EAAEtB,OAAO,CAACuB,eAAe;UACvCC,QAAQ,EAAEC,OAAO,CAACzB,OAAO,CAAC0B,SAAS,CAAC;UACpCC,SAAS,EAAE3B,OAAO,CAAC4B,UAAU;UAC7BC,SAAS,EAAE7B,OAAO,CAAC8B,kBAAkB,IAAI9B,OAAO,CAAC+B,UAAU;UAC3DC,SAAS,EAAEhC,OAAO,CAACiC,kBAAkB,IAAIjC,OAAO,CAACkC;QACnD,CAAC;;QAED;QACA,IAAIjC,eAAe,CAACK,IAAI,KAAK,OAAO,EAAE;UACpC,MAAM,IAAI1B,KAAK,CAAC,0CAA0C,CAAC;QAC7D;;QAEA;QACAtB,iBAAiB,CAACP,QAAQ,CAACwB,QAAQ,CAACZ,IAAI,CAACwE,WAAW,CAAC;QACrD,IAAI5D,QAAQ,CAACZ,IAAI,CAACyE,YAAY,EAAE;UAC9BvF,YAAY,CAACI,OAAO,CAACT,uBAAuB,EAAE+B,QAAQ,CAACZ,IAAI,CAACyE,YAAY,CAAC;QAC3E;QACAvF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE2B,IAAI,CAACC,SAAS,CAAC8B,eAAe,CAAC,CAAC;QAE1E,OAAO;UACLF,OAAO,EAAE,IAAI;UACbf,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,wBAAwB;UACrDrB,IAAI,EAAE;YACJ,GAAGY,QAAQ,CAACZ,IAAI;YAChBgC,IAAI,EAAEM;UACR;QACF,CAAC;MACH;MAEA,MAAM,IAAIrB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,oBAAoB,CAAC;IAC5D,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAIN,KAAK,CAACM,KAAK,CAACF,OAAO,IAAI,oBAAoB,CAAC;IACxD;EACF;;EAEA;AACF;AACA;EACE,aAAaqD,aAAaA,CAAC1E,IAA2B,EAAiC;IACrF,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACmD,IAAI,CAAC6C,cAAc,EAAE3E,IAAI,CAAC;MAEpF,IAAIY,QAAQ,IAAIA,QAAQ,CAACwB,OAAO,EAAE;QAChC,OAAO;UACLA,OAAO,EAAE,IAAI;UACbf,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,2CAA2C;UACxErB,IAAI,EAAEY,QAAQ,CAACZ;QACjB,CAAC;MACH;MAEA,MAAM,IAAIiB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,2BAA2B,CAAC;IACnE,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAM,IAAIN,KAAK,CAACM,KAAK,CAACF,OAAO,IAAI,2BAA2B,CAAC;IAC/D;EACF;;EAEA;AACF;AACA;EACE,aAAauD,SAASA,CAAC5E,IAAyB,EAAoC;IAClF,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACmD,IAAI,CAAC+C,UAAU,EAAE7E,IAAI,CAAC;MAEhF,IAAIY,QAAQ,IAAIA,QAAQ,CAACwB,OAAO,EAAE;QAChC,OAAO;UACLA,OAAO,EAAE,IAAI;UACbf,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,6BAA6B;UAC1DrB,IAAI,EAAEY,QAAQ,CAACZ;QACjB,CAAC;MACH;MAEA,MAAM,IAAIiB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,yBAAyB,CAAC;IACjE,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAIN,KAAK,CAACM,KAAK,CAACF,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF;;EAEA;AACF;AACA;EACE,aAAayD,SAASA,CAACpC,KAAa,EAAwB;IAC1D,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACmD,IAAI,CAACiD,UAAU,EAAE;QAAErC;MAAM,CAAC,CAAC;MAErF,IAAI9B,QAAQ,IAAIA,QAAQ,CAACwB,OAAO,EAAE;QAChC,OAAOxB,QAAQ;MACjB;MAEA,MAAM,IAAIK,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,sBAAsB,CAAC;IAC9D,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAIN,KAAK,CAACM,KAAK,CAACF,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAa2D,MAAMA,CAAA,EAAkB;IACnC,IAAI;MACFtE,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,MAAM,IAAI,CAACd,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACmD,IAAI,CAACmD,MAAM,CAAC;MACrDvE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdb,OAAO,CAACwE,IAAI,CAAC,2EAA2E,EAAE3D,KAAK,CAAC;IAClG,CAAC,SAAS;MACRb,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACwE,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACE,OAAOA,iBAAiBA,CAAA,EAAS;IAC/BzE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvEhB,iBAAiB,CAACJ,WAAW,CAAC,CAAC;IAC/BL,YAAY,CAACM,UAAU,CAACZ,mBAAmB,CAAC;IAC5CM,YAAY,CAACM,UAAU,CAACX,uBAAuB,CAAC;IAChD6B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EACvE;;EAEA;AACF;AACA;EACE,OAAOyE,eAAeA,CAAA,EAAY;IAChC,MAAM/F,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;IAC1C,MAAMoG,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,OAAO,CAAC,EAAEjG,KAAK,IAAIgG,QAAQ,IAAIA,QAAQ,CAAC1C,IAAI,KAAK,OAAO,CAAC;EAC3D;;EAEA;AACF;AACA;EACE,OAAO2C,aAAaA,CAAA,EAAgB;IAClC,IAAI;MACF,MAAMD,QAAQ,GAAGnG,YAAY,CAACC,OAAO,CAACP,mBAAmB,CAAC;MAC1D,MAAMoD,IAAI,GAAGqD,QAAQ,GAAG9E,IAAI,CAACgF,KAAK,CAACF,QAAQ,CAAC,GAAG,IAAI;MACnD,OAAOrD,IAAI,IAAIA,IAAI,CAACW,IAAI,KAAK,OAAO,GAAGX,IAAI,GAAG,IAAI;IACpD,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,OAAOiE,WAAWA,CAAA,EAAmB;IACnC,MAAMxD,IAAI,GAAG,IAAI,CAACsD,aAAa,CAAC,CAAC;IACjC,OAAO,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,MAAK,OAAO,GAAG,OAAO,GAAG,IAAI;EAChD;;EAEA;AACF;AACA;EACE,aAAa8C,GAAGA,CAAI1F,QAAgB,EAA2B;IAC7D,OAAO,IAAI,CAACF,OAAO,CAAiB,KAAK,EAAEE,QAAQ,CAAC;EACtD;;EAEA;AACF;AACA;EACE,aAAa2F,IAAIA,CAAI3F,QAAgB,EAAEC,IAAU,EAA2B;IAC1E,OAAO,IAAI,CAACH,OAAO,CAAiB,MAAM,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,aAAa2F,GAAGA,CAAI5F,QAAgB,EAAEC,IAAU,EAA2B;IACzE,OAAO,IAAI,CAACH,OAAO,CAAiB,KAAK,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC5D;;EAEA;AACF;AACA;EACE,aAAa4F,MAAMA,CAAI7F,QAAgB,EAA2B;IAChE,OAAO,IAAI,CAACF,OAAO,CAAiB,QAAQ,EAAEE,QAAQ,CAAC;EACzD;;EAEA;AACF;AACA;EACE,aAAa8F,oBAAoBA,CAACC,IAAU,EAAoD;IAC9FpF,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEmF,IAAI,CAACC,IAAI,CAAC;IAE1E,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAI7F,QAAQ,CAAC,CAAC;MAC/B6F,QAAQ,CAACC,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;;MAEvC;MACA,MAAMzG,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;MAC1C,IAAI,CAACI,KAAK,EAAE;QACV,MAAM,IAAI4B,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAML,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,wBAAwB,EAAE;QACpEe,MAAM,EAAE,MAAM;QACdI,OAAO,EAAE;UACP,eAAe,EAAE,UAAUb,KAAK;UAChC;QACF,CAAC;QACDiB,IAAI,EAAE0F;MACR,CAAC,CAAC;MAEF,MAAME,MAAM,GAAG,MAAMtF,QAAQ,CAACO,IAAI,CAAC,CAAC;MAEpC,IAAI,CAACP,QAAQ,CAACE,EAAE,IAAI,CAACoF,MAAM,CAAC9D,OAAO,EAAE;QAAA,IAAA+D,aAAA;QACnC,MAAM,IAAIlF,KAAK,CAAC,EAAAkF,aAAA,GAAAD,MAAM,CAAC3E,KAAK,cAAA4E,aAAA,uBAAZA,aAAA,CAAc9E,OAAO,KAAI,kCAAkC,CAAC;MAC9E;MAEAX,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;MAEzE;MACA,MAAMyF,QAAQ,GAAGF,MAAM,CAAClG,IAAI,CAACqG,KAAK;MAClC,MAAMC,gBAAsB,GAAG;QAC7B/D,EAAE,EAAE6D,QAAQ,CAAC5D,QAAQ;QACrBE,KAAK,EAAE0D,QAAQ,CAAC1D,KAAK;QACrBC,IAAI,EAAE,OAAO;QACbC,SAAS,EAAEwD,QAAQ,CAACvD,UAAU;QAC9BC,QAAQ,EAAEsD,QAAQ,CAACrD,SAAS;QAC5BC,UAAU,EAAEoD,QAAQ,CAACnD,WAAW;QAChCC,MAAM,EAAEkD,QAAQ,CAAClD,MAAM;QACvBC,WAAW,EAAEiD,QAAQ,CAAChD,YAAY;QAClCE,UAAU,EAAE8C,QAAQ,CAAC9C,UAAU;QAC/BC,QAAQ,EAAE6C,QAAQ,CAAC7C,QAAQ;QAC3BC,WAAW,EAAE4C,QAAQ,CAAC5C,WAAW;QACjCG,cAAc,EAAEyC,QAAQ,CAACxC,eAAe;QACxCC,QAAQ,EAAEC,OAAO,CAACsC,QAAQ,CAACrC,SAAS,CAAC;QACrCC,SAAS,EAAEoC,QAAQ,CAACnC,UAAU;QAC9BC,SAAS,EAAEkC,QAAQ,CAACjC,kBAAkB;QACtCE,SAAS,EAAE+B,QAAQ,CAAC9B;MACtB,CAAC;;MAED;MACApF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE2B,IAAI,CAACC,SAAS,CAAC8F,gBAAgB,CAAC,CAAC;MAE3E,OAAO;QACLD,KAAK,EAAEC,gBAAgB;QACvB3C,cAAc,EAAEuC,MAAM,CAAClG,IAAI,CAAC2D;MAC9B,CAAC;IACH,CAAC,CAAC,OAAOpC,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAagF,oBAAoBA,CAAA,EAAkB;IACjD7F,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAE9D,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACf,OAAO,CAAC,QAAQ,EAAE,wBAAwB,CAAC;IAEvE,IAAI,CAACe,QAAQ,CAACwB,OAAO,EAAE;MAAA,IAAAoE,eAAA;MACrB,MAAM,IAAIvF,KAAK,CAAC,EAAAuF,eAAA,GAAA5F,QAAQ,CAACW,KAAK,cAAAiF,eAAA,uBAAdA,eAAA,CAAgBnF,OAAO,KAAI,kCAAkC,CAAC;IAChF;IAEAX,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;IAExE;IACA,MAAMyF,QAAQ,GAAGxF,QAAQ,CAACZ,IAAI,CAACqG,KAAK;IACpC,MAAMC,gBAAsB,GAAG;MAC7B/D,EAAE,EAAE6D,QAAQ,CAAC5D,QAAQ;MACrBE,KAAK,EAAE0D,QAAQ,CAAC1D,KAAK;MACrBC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAEwD,QAAQ,CAACvD,UAAU;MAC9BC,QAAQ,EAAEsD,QAAQ,CAACrD,SAAS;MAC5BC,UAAU,EAAEoD,QAAQ,CAACnD,WAAW;MAChCC,MAAM,EAAEkD,QAAQ,CAAClD,MAAM;MACvBC,WAAW,EAAEiD,QAAQ,CAAChD,YAAY;MAClCE,UAAU,EAAE8C,QAAQ,CAAC9C,UAAU;MAC/BC,QAAQ,EAAE6C,QAAQ,CAAC7C,QAAQ;MAC3BC,WAAW,EAAE4C,QAAQ,CAAC5C,WAAW;MACjCG,cAAc,EAAEyC,QAAQ,CAACxC,eAAe;MACxCC,QAAQ,EAAEC,OAAO,CAACsC,QAAQ,CAACrC,SAAS,CAAC;MACrCC,SAAS,EAAEoC,QAAQ,CAACnC,UAAU;MAC9BC,SAAS,EAAEkC,QAAQ,CAACjC,kBAAkB;MACtCE,SAAS,EAAE+B,QAAQ,CAAC9B;IACtB,CAAC;;IAED;IACApF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE2B,IAAI,CAACC,SAAS,CAAC8F,gBAAgB,CAAC,CAAC;IAE3E,OAAOA,gBAAgB;EACzB;AACF;AAEA,eAAe1G,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}