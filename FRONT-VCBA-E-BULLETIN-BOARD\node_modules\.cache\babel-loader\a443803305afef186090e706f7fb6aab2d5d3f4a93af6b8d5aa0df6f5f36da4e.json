{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4.9 16.1C1 12.2 1 5.8 4.9 1.9\",\n  key: \"s0qx1y\"\n}], [\"path\", {\n  d: \"M7.8 4.7a6.14 6.14 0 0 0-.8 7.5\",\n  key: \"1idnkw\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"1092wv\"\n}], [\"path\", {\n  d: \"M16.2 4.8c2 2 2.26 5.11.8 7.47\",\n  key: \"ojru2q\"\n}], [\"path\", {\n  d: \"M19.1 1.9a9.96 9.96 0 0 1 0 14.1\",\n  key: \"rhi7fg\"\n}], [\"path\", {\n  d: \"M9.5 18h5\",\n  key: \"mfy3pd\"\n}], [\"path\", {\n  d: \"m8 22 4-11 4 11\",\n  key: \"25yftu\"\n}]];\nconst RadioTower = createLucideIcon(\"radio-tower\", __iconNode);\nexport { __iconNode, RadioTower as default };\n//# sourceMappingURL=radio-tower.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}