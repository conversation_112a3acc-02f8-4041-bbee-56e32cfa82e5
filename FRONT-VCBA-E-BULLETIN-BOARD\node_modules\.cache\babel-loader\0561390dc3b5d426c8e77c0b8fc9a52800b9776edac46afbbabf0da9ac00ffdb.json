{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n  key: \"vwvbt9\"\n}], [\"path\", {\n  d: \"M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665\",\n  key: \"1tip0g\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"8\",\n  r: \"3\",\n  key: \"1g0gzu\"\n}]];\nconst BellDot = createLucideIcon(\"bell-dot\", __iconNode);\nexport { __iconNode, BellDot as default };\n//# sourceMappingURL=bell-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}