{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5\",\n  key: \"14wa3c\"\n}], [\"path\", {\n  d: \"M12 7.5V9\",\n  key: \"1oy5b0\"\n}], [\"path\", {\n  d: \"M7.5 12H9\",\n  key: \"eltsq1\"\n}], [\"path\", {\n  d: \"M16.5 12H15\",\n  key: \"vk5kw4\"\n}], [\"path\", {\n  d: \"M12 16.5V15\",\n  key: \"k7eayi\"\n}], [\"path\", {\n  d: \"m8 8 1.88 1.88\",\n  key: \"nxy4qf\"\n}], [\"path\", {\n  d: \"M14.12 9.88 16 8\",\n  key: \"1lst6k\"\n}], [\"path\", {\n  d: \"m8 16 1.88-1.88\",\n  key: \"h2eex1\"\n}], [\"path\", {\n  d: \"M14.12 14.12 16 16\",\n  key: \"uqkrx3\"\n}]];\nconst Flower = createLucideIcon(\"flower\", __iconNode);\nexport { __iconNode, Flower as default };\n//# sourceMappingURL=flower.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}