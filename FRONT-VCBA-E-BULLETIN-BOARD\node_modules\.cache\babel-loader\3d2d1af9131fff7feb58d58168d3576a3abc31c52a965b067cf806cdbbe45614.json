{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v14\",\n  key: \"7cf3v8\"\n}], [\"path\", {\n  d: \"M5 10h14\",\n  key: \"elsbfy\"\n}], [\"path\", {\n  d: \"M5 21h14\",\n  key: \"11awu3\"\n}]];\nconst Diff = createLucideIcon(\"diff\", __iconNode);\nexport { __iconNode, Diff as default };\n//# sourceMappingURL=diff.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}