{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41L13.7 2.71a2.41 2.41 0 0 0-3.41 0z\",\n  key: \"1ey20j\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}]];\nconst DiamondMinus = createLucideIcon(\"diamond-minus\", __iconNode);\nexport { __iconNode, DiamondMinus as default };\n//# sourceMappingURL=diamond-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}