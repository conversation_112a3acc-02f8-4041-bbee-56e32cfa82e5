{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v6a2 2 0 0 0 .245.96l5.51 10.08A2 2 0 0 1 18 22H6a2 2 0 0 1-1.755-2.96l5.51-10.08A2 2 0 0 0 10 8V2\",\n  key: \"18mbvz\"\n}], [\"path\", {\n  d: \"M6.453 15h11.094\",\n  key: \"3shlmq\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}]];\nconst FlaskConical = createLucideIcon(\"flask-conical\", __iconNode);\nexport { __iconNode, FlaskConical as default };\n//# sourceMappingURL=flask-conical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}