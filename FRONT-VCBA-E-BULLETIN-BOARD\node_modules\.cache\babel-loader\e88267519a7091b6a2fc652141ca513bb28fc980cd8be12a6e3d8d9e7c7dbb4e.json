{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"7eqyqh\"\n}], [\"path\", {\n  d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\",\n  key: \"1b0p4s\"\n}]];\nconst DollarSign = createLucideIcon(\"dollar-sign\", __iconNode);\nexport { __iconNode, DollarSign as default };\n//# sourceMappingURL=dollar-sign.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}