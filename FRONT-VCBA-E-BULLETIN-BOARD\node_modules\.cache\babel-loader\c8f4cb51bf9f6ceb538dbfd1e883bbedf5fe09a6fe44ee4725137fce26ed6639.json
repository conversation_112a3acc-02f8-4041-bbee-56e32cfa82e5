{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z\",\n  key: \"icamh8\"\n}], [\"path\", {\n  d: \"m14.5 12.5 2-2\",\n  key: \"inckbg\"\n}], [\"path\", {\n  d: \"m11.5 9.5 2-2\",\n  key: \"fmmyf7\"\n}], [\"path\", {\n  d: \"m8.5 6.5 2-2\",\n  key: \"vc6u1g\"\n}], [\"path\", {\n  d: \"m17.5 15.5 2-2\",\n  key: \"wo5hmg\"\n}]];\nconst Ruler = createLucideIcon(\"ruler\", __iconNode);\nexport { __iconNode, Ruler as default };\n//# sourceMappingURL=ruler.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}