{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"16\",\n  r: \"1\",\n  key: \"1au0dj\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"12\",\n  x: \"3\",\n  y: \"10\",\n  rx: \"2\",\n  key: \"l0tzu3\"\n}], [\"path\", {\n  d: \"M7 10V7a5 5 0 0 1 9.33-2.5\",\n  key: \"car5b7\"\n}]];\nconst LockKeyholeOpen = createLucideIcon(\"lock-keyhole-open\", __iconNode);\nexport { __iconNode, LockKeyholeOpen as default };\n//# sourceMappingURL=lock-keyhole-open.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}