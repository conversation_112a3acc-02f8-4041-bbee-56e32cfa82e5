{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13h.01\",\n  key: \"y0uutt\"\n}], [\"path\", {\n  d: \"M12 6v3\",\n  key: \"1m4b9j\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"k3hazp\"\n}]];\nconst BookAlert = createLucideIcon(\"book-alert\", __iconNode);\nexport { __iconNode, BookAlert as default };\n//# sourceMappingURL=book-alert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}