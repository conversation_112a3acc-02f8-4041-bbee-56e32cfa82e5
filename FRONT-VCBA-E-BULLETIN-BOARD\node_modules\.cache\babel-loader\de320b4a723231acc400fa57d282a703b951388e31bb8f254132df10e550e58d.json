{"ast": null, "code": "import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS } from '../config/constants';\n\n// Types for announcements\n\nclass AnnouncementService {\n  // HTTP client instance\n\n  constructor(customHttpClient) {\n    this.client = void 0;\n    this.client = customHttpClient || httpClient; // Use custom client or default\n  }\n  // Get all announcements with filters and pagination\n  async getAnnouncements(filters) {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BASE, params);\n  }\n\n  // Get featured announcements\n  async getFeaturedAnnouncements(limit) {\n    const params = limit ? {\n      limit\n    } : undefined;\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.FEATURED, params);\n  }\n\n  // Get single announcement by ID\n  async getAnnouncementById(id) {\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Create new announcement\n  async createAnnouncement(data) {\n    return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.BASE, data);\n  }\n\n  // Update announcement\n  async updateAnnouncement(id, data) {\n    return httpClient.put(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()), data);\n  }\n\n  // Delete announcement\n  async deleteAnnouncement(id) {\n    return httpClient.delete(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Publish announcement\n  async publishAnnouncement(id) {\n    return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));\n  }\n\n  // Unpublish announcement\n  async unpublishAnnouncement(id) {\n    return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));\n  }\n\n  // Mark announcement as viewed\n  async markAsViewed(id) {\n    return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.VIEW(id.toString()));\n  }\n\n  // Add reaction to announcement\n  async addReaction(id, reactionId) {\n    console.log('❤️ AnnouncementService - Adding reaction:', {\n      announcementId: id,\n      reactionId,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.post(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()), {\n      reaction_id: reactionId\n    });\n  }\n\n  // Remove reaction from announcement\n  async removeReaction(id) {\n    console.log('💔 AnnouncementService - Removing reaction:', {\n      announcementId: id,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.delete(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()));\n  }\n\n  // Get announcement reaction statistics\n  async getReactionStats(id) {\n    const endpoint = id ? API_ENDPOINTS.ANNOUNCEMENTS.REACTIONS(id.toString()) : API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES;\n    return httpClient.get(endpoint);\n  }\n\n  // Get categories (public endpoint)\n  async getCategories() {\n    return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);\n  }\n\n  // Get all subcategories (public endpoint)\n  async getSubcategories() {\n    return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);\n  }\n\n  // Get subcategories by category ID (public endpoint)\n  async getSubcategoriesByCategory(categoryId) {\n    return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));\n  }\n\n  // Get categories with their subcategories (hierarchical structure) (public endpoint)\n  async getCategoriesWithSubcategories() {\n    return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);\n  }\n\n  // Get reaction types\n  async getReactionTypes() {\n    return httpClient.get(API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES);\n  }\n\n  // Helper method to build query parameters\n  buildQueryParams(filters) {\n    const params = {};\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    return params;\n  }\n\n  // Get announcements for admin dashboard\n  async getAdminAnnouncements(filters) {\n    const defaultFilters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get published announcements for students\n  async getPublishedAnnouncements(filters) {\n    const defaultFilters = {\n      page: 1,\n      limit: 20,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get pinned announcements\n  async getPinnedAnnouncements() {\n    return this.getAnnouncements({\n      is_pinned: true,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Get alert announcements\n  async getAlertAnnouncements() {\n    return this.getAnnouncements({\n      is_alert: true,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Search announcements\n  async searchAnnouncements(query, filters) {\n    return this.getAnnouncements({\n      search: query,\n      status: 'published',\n      ...filters\n    });\n  }\n}\n\n// Admin-specific announcement service that uses admin authentication\nclass AdminAnnouncementService {\n  // Get all announcements with admin auth\n  async getAnnouncements(filters) {\n    const params = filters ? this.buildQueryParams(filters) : '';\n    const endpoint = `${API_ENDPOINTS.ANNOUNCEMENTS.BASE}${params}`;\n    return AdminAuthService.get(endpoint);\n  }\n\n  // Create new announcement with admin auth\n  async createAnnouncement(data) {\n    return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.BASE, data);\n  }\n\n  // Update announcement with admin auth\n  async updateAnnouncement(id, data) {\n    return AdminAuthService.put(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()), data);\n  }\n\n  // Delete announcement with admin auth\n  async deleteAnnouncement(id) {\n    return AdminAuthService.delete(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Publish announcement with admin auth\n  async publishAnnouncement(id) {\n    return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));\n  }\n\n  // Unpublish announcement with admin auth\n  async unpublishAnnouncement(id) {\n    return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));\n  }\n\n  // Get single announcement by ID with admin auth\n  async getAnnouncementById(id) {\n    return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Helper method to build query parameters\n  buildQueryParams(filters) {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n    return params.toString() ? `?${params.toString()}` : '';\n  }\n\n  // Get announcements for admin dashboard with admin auth\n  async getAdminAnnouncements(filters) {\n    const defaultFilters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get categories with admin auth\n  async getCategories() {\n    return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);\n  }\n\n  // Get all subcategories with admin auth\n  async getSubcategories() {\n    return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);\n  }\n\n  // Get subcategories by category ID with admin auth\n  async getSubcategoriesByCategory(categoryId) {\n    return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));\n  }\n\n  // Get categories with their subcategories (hierarchical structure) with admin auth\n  async getCategoriesWithSubcategories() {\n    return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);\n  }\n\n  // Multiple image management methods\n\n  // Add multiple images to announcement\n  async addAnnouncementImages(announcementId, formData) {\n    return AdminAuthService.post(`${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images`, formData);\n  }\n\n  // Get all images for announcement\n  async getAnnouncementImages(announcementId) {\n    return AdminAuthService.get(`${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images`);\n  }\n\n  // Delete specific image from announcement\n  async deleteAnnouncementImage(announcementId, attachmentId) {\n    return AdminAuthService.delete(`${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/${attachmentId}`);\n  }\n\n  // Update image display order\n  async updateImageOrder(announcementId, imageOrder) {\n    return AdminAuthService.put(`${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/order`, {\n      imageOrder\n    });\n  }\n\n  // Set primary image for announcement\n  async setPrimaryImage(announcementId, attachmentId) {\n    return AdminAuthService.put(`${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/${attachmentId}/primary`, {});\n  }\n\n  // Add reaction to announcement (admin)\n  async addReaction(announcementId, reactionId) {\n    return AdminAuthService.post(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${announcementId}/like`, {\n      reaction_id: reactionId\n    });\n  }\n\n  // Remove reaction from announcement (admin)\n  async removeReaction(announcementId) {\n    return AdminAuthService.delete(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${announcementId}/like`);\n  }\n}\n\n// Export service instances\nexport const announcementService = new AnnouncementService(); // Default/legacy service\nexport const adminAnnouncementService = new AdminAnnouncementService();\n\n// Role-specific announcement services with proper token management\nexport const adminAnnouncementServiceWithToken = new AnnouncementService(adminHttpClient);\nexport const studentAnnouncementServiceWithToken = new AnnouncementService(studentHttpClient);\nexport default announcementService;", "map": {"version": 3, "names": ["httpClient", "adminHttpClient", "studentHttpClient", "AdminAuthService", "API_ENDPOINTS", "AnnouncementService", "constructor", "customHttpClient", "client", "getAnnouncements", "filters", "params", "buildQueryParams", "undefined", "get", "ANNOUNCEMENTS", "BASE", "getFeaturedAnnouncements", "limit", "FEATURED", "getAnnouncementById", "id", "BY_ID", "toString", "createAnnouncement", "data", "post", "updateAnnouncement", "put", "deleteAnnouncement", "delete", "publishAnnouncement", "PUBLISH", "unpublishAnnouncement", "UNPUBLISH", "<PERSON><PERSON><PERSON>", "VIEW", "addReaction", "reactionId", "console", "log", "announcementId", "clientType", "LIKE", "reaction_id", "removeReaction", "getReactionStats", "endpoint", "REACTIONS", "REACTION_TYPES", "getCategories", "getPublic", "CATEGORIES", "getSubcategories", "SUBCATEGORIES", "getSubcategoriesByCategory", "categoryId", "SUBCATEGORIES_BY_CATEGORY", "getCategoriesWithSubcategories", "CATEGORIES_WITH_SUBCATEGORIES", "getReactionTypes", "Object", "entries", "for<PERSON>ach", "key", "value", "getAdminAnnouncements", "defaultFilters", "page", "sort_by", "sort_order", "getPublishedAnnouncements", "status", "getPinnedAnnouncements", "is_pinned", "getAlertAnnouncements", "is_alert", "searchAnnouncements", "query", "search", "AdminAnnouncementService", "URLSearchParams", "append", "addAnnouncementImages", "formData", "getAnnouncementImages", "deleteAnnouncementImage", "attachmentId", "updateImageOrder", "imageOrder", "setPrimaryImage", "announcementService", "adminAnnouncementService", "adminAnnouncementServiceWithToken", "studentAnnouncementServiceWithToken"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/announcementService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS } from '../config/constants';\nimport { ApiResponse } from '../types';\n\n// Types for announcements\nexport interface Announcement {\n  announcement_id: number;\n  title: string;\n  content: string;\n  image_path?: string;\n  image_url?: string;\n  category_id: number;\n  subcategory_id?: number;\n  category_name?: string;\n  category_color?: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  posted_by: number;\n  grade_level?: number;\n  author_name?: string;\n  author_picture?: string;\n  status: 'draft' | 'scheduled' | 'published' | 'archived';\n  is_pinned: boolean;\n  is_alert: boolean;\n  allow_comments: boolean;\n  allow_sharing: boolean;\n  scheduled_publish_at?: string;\n  published_at?: string;\n  archived_at?: string;\n  view_count: number;\n  reaction_count: number;\n  comment_count: number;\n  created_at: string;\n  updated_at: string;\n  user_reaction?: UserReaction;\n  reactions?: ReactionSummary[];\n  // Multiple image support\n  attachments?: AnnouncementAttachment[];\n  images?: AnnouncementAttachment[];\n}\n\nexport interface AnnouncementAttachment {\n  attachment_id: number;\n  announcement_id: number;\n  file_name: string;\n  file_path: string;\n  file_url: string;\n  file_type: 'image' | 'document';\n  file_size: number;\n  mime_type: string;\n  display_order: number;\n  is_primary: boolean;\n  uploaded_at: string;\n  deleted_at?: string;\n}\n\nexport interface CreateAnnouncementData {\n  title: string;\n  content: string;\n  category_id: number;\n  subcategory_id?: number;\n  grade_level?: number;\n  status?: 'draft' | 'scheduled' | 'published';\n  is_pinned?: boolean;\n  is_alert?: boolean;\n  allow_comments?: boolean;\n  allow_sharing?: boolean;\n  scheduled_publish_at?: string;\n}\n\nexport interface UpdateAnnouncementData {\n  title?: string;\n  content?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  grade_level?: number;\n  status?: 'draft' | 'scheduled' | 'published' | 'archived';\n  is_pinned?: boolean;\n  is_alert?: boolean;\n  allow_comments?: boolean;\n  allow_sharing?: boolean;\n  scheduled_publish_at?: string;\n}\n\nexport interface AnnouncementFilters {\n  page?: number;\n  limit?: number;\n  status?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  posted_by?: number;\n  grade_level?: number;\n  is_pinned?: boolean;\n  is_alert?: boolean;\n  search?: string;\n  start_date?: string;\n  end_date?: string;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface Category {\n  category_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  created_at: string;\n  updated_at: string;\n  subcategories?: Subcategory[];\n}\n\nexport interface Subcategory {\n  subcategory_id: number;\n  category_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  display_order: number;\n  created_at: string;\n  updated_at: string;\n  category_name?: string;\n  category_color?: string;\n}\n\nexport interface ReactionType {\n  reaction_id: number;\n  reaction_name: string;\n  reaction_emoji: string;\n  is_active: boolean;\n}\n\nexport interface UserReaction {\n  reaction_id: number;\n  reaction_name: string;\n  reaction_emoji: string;\n}\n\nexport interface ReactionSummary {\n  reaction_id: number;\n  reaction_name: string;\n  reaction_emoji: string;\n  count: number;\n}\n\nexport interface PaginatedResponse<T> {\n  announcements: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nclass AnnouncementService {\n  private client: typeof httpClient; // HTTP client instance\n\n  constructor(customHttpClient?: typeof httpClient) {\n    this.client = customHttpClient || httpClient; // Use custom client or default\n  }\n  // Get all announcements with filters and pagination\n  async getAnnouncements(filters?: AnnouncementFilters): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BASE, params);\n  }\n\n  // Get featured announcements\n  async getFeaturedAnnouncements(limit?: number): Promise<ApiResponse<{ announcements: Announcement[] }>> {\n    const params = limit ? { limit } : undefined;\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.FEATURED, params);\n  }\n\n  // Get single announcement by ID\n  async getAnnouncementById(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Create new announcement\n  async createAnnouncement(data: CreateAnnouncementData): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return httpClient.post<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.BASE, data);\n  }\n\n  // Update announcement\n  async updateAnnouncement(id: number, data: UpdateAnnouncementData): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return httpClient.put<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()), data);\n  }\n\n  // Delete announcement\n  async deleteAnnouncement(id: number): Promise<ApiResponse<void>> {\n    return httpClient.delete<void>(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Publish announcement\n  async publishAnnouncement(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return httpClient.post<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));\n  }\n\n  // Unpublish announcement\n  async unpublishAnnouncement(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return httpClient.post<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));\n  }\n\n  // Mark announcement as viewed\n  async markAsViewed(id: number): Promise<ApiResponse<void>> {\n    return httpClient.post<void>(API_ENDPOINTS.ANNOUNCEMENTS.VIEW(id.toString()));\n  }\n\n  // Add reaction to announcement\n  async addReaction(id: number, reactionId: number): Promise<ApiResponse<void>> {\n    console.log('❤️ AnnouncementService - Adding reaction:', {\n      announcementId: id,\n      reactionId,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.post(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()), { reaction_id: reactionId });\n  }\n\n  // Remove reaction from announcement\n  async removeReaction(id: number): Promise<ApiResponse<{ removed: boolean }>> {\n    console.log('💔 AnnouncementService - Removing reaction:', {\n      announcementId: id,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.delete(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()));\n  }\n\n  // Get announcement reaction statistics\n  async getReactionStats(id?: number): Promise<ApiResponse<{ stats: ReactionSummary[] }>> {\n    const endpoint = id \n      ? API_ENDPOINTS.ANNOUNCEMENTS.REACTIONS(id.toString())\n      : API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES;\n    return httpClient.get<{ stats: ReactionSummary[] }>(endpoint);\n  }\n\n  // Get categories (public endpoint)\n  async getCategories(): Promise<ApiResponse<{ categories: Category[] }>> {\n    return httpClient.getPublic<{ categories: Category[] }>(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);\n  }\n\n  // Get all subcategories (public endpoint)\n  async getSubcategories(): Promise<ApiResponse<{ subcategories: Subcategory[] }>> {\n    return httpClient.getPublic<{ subcategories: Subcategory[] }>(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);\n  }\n\n  // Get subcategories by category ID (public endpoint)\n  async getSubcategoriesByCategory(categoryId: number): Promise<ApiResponse<{ subcategories: Subcategory[] }>> {\n    return httpClient.getPublic<{ subcategories: Subcategory[] }>(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));\n  }\n\n  // Get categories with their subcategories (hierarchical structure) (public endpoint)\n  async getCategoriesWithSubcategories(): Promise<ApiResponse<{ categories: Category[] }>> {\n    return httpClient.getPublic<{ categories: Category[] }>(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);\n  }\n\n  // Get reaction types\n  async getReactionTypes(): Promise<ApiResponse<{ reactionTypes: ReactionType[] }>> {\n    return httpClient.get<{ reactionTypes: ReactionType[] }>(API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES);\n  }\n\n  // Helper method to build query parameters\n  private buildQueryParams(filters: AnnouncementFilters): Record<string, string> {\n    const params: Record<string, string> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    \n    return params;\n  }\n\n  // Get announcements for admin dashboard\n  async getAdminAnnouncements(filters?: Partial<AnnouncementFilters>): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    const defaultFilters: AnnouncementFilters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get published announcements for students\n  async getPublishedAnnouncements(filters?: Partial<AnnouncementFilters>): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    const defaultFilters: AnnouncementFilters = {\n      page: 1,\n      limit: 20,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get pinned announcements\n  async getPinnedAnnouncements(): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    return this.getAnnouncements({\n      is_pinned: true,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Get alert announcements\n  async getAlertAnnouncements(): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    return this.getAnnouncements({\n      is_alert: true,\n      status: 'published',\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Search announcements\n  async searchAnnouncements(query: string, filters?: Partial<AnnouncementFilters>): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    return this.getAnnouncements({\n      search: query,\n      status: 'published',\n      ...filters\n    });\n  }\n}\n\n// Admin-specific announcement service that uses admin authentication\nclass AdminAnnouncementService {\n  // Get all announcements with admin auth\n  async getAnnouncements(filters?: AnnouncementFilters): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    const params = filters ? this.buildQueryParams(filters) : '';\n    const endpoint = `${API_ENDPOINTS.ANNOUNCEMENTS.BASE}${params}`;\n    return AdminAuthService.get<PaginatedResponse<Announcement>>(endpoint);\n  }\n\n  // Create new announcement with admin auth\n  async createAnnouncement(data: CreateAnnouncementData | FormData): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return AdminAuthService.post<{ announcement: Announcement }>(\n      API_ENDPOINTS.ANNOUNCEMENTS.BASE,\n      data\n    );\n  }\n\n  // Update announcement with admin auth\n  async updateAnnouncement(id: number, data: UpdateAnnouncementData | FormData): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return AdminAuthService.put<{ announcement: Announcement }>(\n      API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()),\n      data\n    );\n  }\n\n  // Delete announcement with admin auth\n  async deleteAnnouncement(id: number): Promise<ApiResponse<void>> {\n    return AdminAuthService.delete<void>(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Publish announcement with admin auth\n  async publishAnnouncement(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return AdminAuthService.post<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));\n  }\n\n  // Unpublish announcement with admin auth\n  async unpublishAnnouncement(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return AdminAuthService.post<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));\n  }\n\n  // Get single announcement by ID with admin auth\n  async getAnnouncementById(id: number): Promise<ApiResponse<{ announcement: Announcement }>> {\n    return AdminAuthService.get<{ announcement: Announcement }>(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));\n  }\n\n  // Helper method to build query parameters\n  private buildQueryParams(filters: AnnouncementFilters): string {\n    const params = new URLSearchParams();\n\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return params.toString() ? `?${params.toString()}` : '';\n  }\n\n  // Get announcements for admin dashboard with admin auth\n  async getAdminAnnouncements(filters?: Partial<AnnouncementFilters>): Promise<ApiResponse<PaginatedResponse<Announcement>>> {\n    const defaultFilters: AnnouncementFilters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC',\n      ...filters\n    };\n    return this.getAnnouncements(defaultFilters);\n  }\n\n  // Get categories with admin auth\n  async getCategories(): Promise<ApiResponse<{ categories: Category[] }>> {\n    return AdminAuthService.get<{ categories: Category[] }>(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);\n  }\n\n  // Get all subcategories with admin auth\n  async getSubcategories(): Promise<ApiResponse<{ subcategories: Subcategory[] }>> {\n    return AdminAuthService.get<{ subcategories: Subcategory[] }>(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);\n  }\n\n  // Get subcategories by category ID with admin auth\n  async getSubcategoriesByCategory(categoryId: number): Promise<ApiResponse<{ subcategories: Subcategory[] }>> {\n    return AdminAuthService.get<{ subcategories: Subcategory[] }>(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));\n  }\n\n  // Get categories with their subcategories (hierarchical structure) with admin auth\n  async getCategoriesWithSubcategories(): Promise<ApiResponse<{ categories: Category[] }>> {\n    return AdminAuthService.get<{ categories: Category[] }>(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);\n  }\n\n  // Multiple image management methods\n\n  // Add multiple images to announcement\n  async addAnnouncementImages(announcementId: number, formData: FormData): Promise<ApiResponse<any>> {\n    return AdminAuthService.post<any>(\n      `${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images`,\n      formData\n    );\n  }\n\n  // Get all images for announcement\n  async getAnnouncementImages(announcementId: number): Promise<ApiResponse<{ images: any[] }>> {\n    return AdminAuthService.get<{ images: any[] }>(\n      `${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images`\n    );\n  }\n\n  // Delete specific image from announcement\n  async deleteAnnouncementImage(announcementId: number, attachmentId: number): Promise<ApiResponse<void>> {\n    return AdminAuthService.delete<void>(\n      `${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/${attachmentId}`\n    );\n  }\n\n  // Update image display order\n  async updateImageOrder(announcementId: number, imageOrder: { attachment_id: number; display_order: number }[]): Promise<ApiResponse<void>> {\n    return AdminAuthService.put<void>(\n      `${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/order`,\n      { imageOrder }\n    );\n  }\n\n  // Set primary image for announcement\n  async setPrimaryImage(announcementId: number, attachmentId: number): Promise<ApiResponse<void>> {\n    return AdminAuthService.put<void>(\n      `${API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString())}/images/${attachmentId}/primary`,\n      {}\n    );\n  }\n\n  // Add reaction to announcement (admin)\n  async addReaction(announcementId: number, reactionId: number): Promise<ApiResponse<void>> {\n    return AdminAuthService.post<void>(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${announcementId}/like`, {\n      reaction_id: reactionId\n    });\n  }\n\n  // Remove reaction from announcement (admin)\n  async removeReaction(announcementId: number): Promise<ApiResponse<void>> {\n    return AdminAuthService.delete<void>(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${announcementId}/like`);\n  }\n}\n\n// Export service instances\nexport const announcementService = new AnnouncementService(); // Default/legacy service\nexport const adminAnnouncementService = new AdminAnnouncementService();\n\n// Role-specific announcement services with proper token management\nexport const adminAnnouncementServiceWithToken = new AnnouncementService(adminHttpClient);\nexport const studentAnnouncementServiceWithToken = new AnnouncementService(studentHttpClient);\n\nexport default announcementService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;;AAGnD;;AA0JA,MAAMC,mBAAmB,CAAC;EACW;;EAEnCC,WAAWA,CAACC,gBAAoC,EAAE;IAAA,KAF1CC,MAAM;IAGZ,IAAI,CAACA,MAAM,GAAGD,gBAAgB,IAAIP,UAAU,CAAC,CAAC;EAChD;EACA;EACA,MAAMS,gBAAgBA,CAACC,OAA6B,EAAyD;IAC3G,MAAMC,MAAM,GAAGD,OAAO,GAAG,IAAI,CAACE,gBAAgB,CAACF,OAAO,CAAC,GAAGG,SAAS;IACnE,OAAO,IAAI,CAACL,MAAM,CAACM,GAAG,CAACV,aAAa,CAACW,aAAa,CAACC,IAAI,EAAEL,MAAM,CAAC;EAClE;;EAEA;EACA,MAAMM,wBAAwBA,CAACC,KAAc,EAA2D;IACtG,MAAMP,MAAM,GAAGO,KAAK,GAAG;MAAEA;IAAM,CAAC,GAAGL,SAAS;IAC5C,OAAO,IAAI,CAACL,MAAM,CAACM,GAAG,CAACV,aAAa,CAACW,aAAa,CAACI,QAAQ,EAAER,MAAM,CAAC;EACtE;;EAEA;EACA,MAAMS,mBAAmBA,CAACC,EAAU,EAAwD;IAC1F,OAAO,IAAI,CAACb,MAAM,CAACM,GAAG,CAACV,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1E;;EAEA;EACA,MAAMC,kBAAkBA,CAACC,IAA4B,EAAwD;IAC3G,OAAOzB,UAAU,CAAC0B,IAAI,CAAiCtB,aAAa,CAACW,aAAa,CAACC,IAAI,EAAES,IAAI,CAAC;EAChG;;EAEA;EACA,MAAME,kBAAkBA,CAACN,EAAU,EAAEI,IAA4B,EAAwD;IACvH,OAAOzB,UAAU,CAAC4B,GAAG,CAAiCxB,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEE,IAAI,CAAC;EAC/G;;EAEA;EACA,MAAMI,kBAAkBA,CAACR,EAAU,EAA8B;IAC/D,OAAOrB,UAAU,CAAC8B,MAAM,CAAO1B,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClF;;EAEA;EACA,MAAMQ,mBAAmBA,CAACV,EAAU,EAAwD;IAC1F,OAAOrB,UAAU,CAAC0B,IAAI,CAAiCtB,aAAa,CAACW,aAAa,CAACiB,OAAO,CAACX,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5G;;EAEA;EACA,MAAMU,qBAAqBA,CAACZ,EAAU,EAAwD;IAC5F,OAAOrB,UAAU,CAAC0B,IAAI,CAAiCtB,aAAa,CAACW,aAAa,CAACmB,SAAS,CAACb,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9G;;EAEA;EACA,MAAMY,YAAYA,CAACd,EAAU,EAA8B;IACzD,OAAOrB,UAAU,CAAC0B,IAAI,CAAOtB,aAAa,CAACW,aAAa,CAACqB,IAAI,CAACf,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC/E;;EAEA;EACA,MAAMc,WAAWA,CAAChB,EAAU,EAAEiB,UAAkB,EAA8B;IAC5EC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvDC,cAAc,EAAEpB,EAAE;MAClBiB,UAAU;MACVI,UAAU,EAAE,IAAI,CAAClC,MAAM,KAAKP,eAAe,GAAG,OAAO,GAAG,IAAI,CAACO,MAAM,KAAKN,iBAAiB,GAAG,SAAS,GAAG;IAC1G,CAAC,CAAC;IACF,OAAO,IAAI,CAACM,MAAM,CAACkB,IAAI,CAACtB,aAAa,CAACW,aAAa,CAAC4B,IAAI,CAACtB,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAE;MAAEqB,WAAW,EAAEN;IAAW,CAAC,CAAC;EACvG;;EAEA;EACA,MAAMO,cAAcA,CAACxB,EAAU,EAA8C;IAC3EkB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;MACzDC,cAAc,EAAEpB,EAAE;MAClBqB,UAAU,EAAE,IAAI,CAAClC,MAAM,KAAKP,eAAe,GAAG,OAAO,GAAG,IAAI,CAACO,MAAM,KAAKN,iBAAiB,GAAG,SAAS,GAAG;IAC1G,CAAC,CAAC;IACF,OAAO,IAAI,CAACM,MAAM,CAACsB,MAAM,CAAC1B,aAAa,CAACW,aAAa,CAAC4B,IAAI,CAACtB,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5E;;EAEA;EACA,MAAMuB,gBAAgBA,CAACzB,EAAW,EAAsD;IACtF,MAAM0B,QAAQ,GAAG1B,EAAE,GACfjB,aAAa,CAACW,aAAa,CAACiC,SAAS,CAAC3B,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,GACpDnB,aAAa,CAACW,aAAa,CAACkC,cAAc;IAC9C,OAAOjD,UAAU,CAACc,GAAG,CAA+BiC,QAAQ,CAAC;EAC/D;;EAEA;EACA,MAAMG,aAAaA,CAAA,EAAqD;IACtE,OAAOlD,UAAU,CAACmD,SAAS,CAA6B/C,aAAa,CAACW,aAAa,CAACqC,UAAU,CAAC;EACjG;;EAEA;EACA,MAAMC,gBAAgBA,CAAA,EAA2D;IAC/E,OAAOrD,UAAU,CAACmD,SAAS,CAAmC/C,aAAa,CAACW,aAAa,CAACuC,aAAa,CAAC;EAC1G;;EAEA;EACA,MAAMC,0BAA0BA,CAACC,UAAkB,EAA0D;IAC3G,OAAOxD,UAAU,CAACmD,SAAS,CAAmC/C,aAAa,CAACW,aAAa,CAAC0C,yBAAyB,CAACD,UAAU,CAACjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7I;;EAEA;EACA,MAAMmC,8BAA8BA,CAAA,EAAqD;IACvF,OAAO1D,UAAU,CAACmD,SAAS,CAA6B/C,aAAa,CAACW,aAAa,CAAC4C,6BAA6B,CAAC;EACpH;;EAEA;EACA,MAAMC,gBAAgBA,CAAA,EAA4D;IAChF,OAAO5D,UAAU,CAACc,GAAG,CAAoCV,aAAa,CAACW,aAAa,CAACkC,cAAc,CAAC;EACtG;;EAEA;EACQrC,gBAAgBA,CAACF,OAA4B,EAA0B;IAC7E,MAAMC,MAA8B,GAAG,CAAC,CAAC;IAEzCkD,MAAM,CAACC,OAAO,CAACpD,OAAO,CAAC,CAACqD,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKpD,SAAS,IAAIoD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDtD,MAAM,CAACqD,GAAG,CAAC,GAAGC,KAAK,CAAC1C,QAAQ,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IAEF,OAAOZ,MAAM;EACf;;EAEA;EACA,MAAMuD,qBAAqBA,CAACxD,OAAsC,EAAyD;IACzH,MAAMyD,cAAmC,GAAG;MAC1CC,IAAI,EAAE,CAAC;MACPlD,KAAK,EAAE,EAAE;MACTmD,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,MAAM;MAClB,GAAG5D;IACL,CAAC;IACD,OAAO,IAAI,CAACD,gBAAgB,CAAC0D,cAAc,CAAC;EAC9C;;EAEA;EACA,MAAMI,yBAAyBA,CAAC7D,OAAsC,EAAyD;IAC7H,MAAMyD,cAAmC,GAAG;MAC1CC,IAAI,EAAE,CAAC;MACPlD,KAAK,EAAE,EAAE;MACTsD,MAAM,EAAE,WAAW;MACnBH,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,MAAM;MAClB,GAAG5D;IACL,CAAC;IACD,OAAO,IAAI,CAACD,gBAAgB,CAAC0D,cAAc,CAAC;EAC9C;;EAEA;EACA,MAAMM,sBAAsBA,CAAA,EAA0D;IACpF,OAAO,IAAI,CAAChE,gBAAgB,CAAC;MAC3BiE,SAAS,EAAE,IAAI;MACfF,MAAM,EAAE,WAAW;MACnBH,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMK,qBAAqBA,CAAA,EAA0D;IACnF,OAAO,IAAI,CAAClE,gBAAgB,CAAC;MAC3BmE,QAAQ,EAAE,IAAI;MACdJ,MAAM,EAAE,WAAW;MACnBH,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,mBAAmBA,CAACC,KAAa,EAAEpE,OAAsC,EAAyD;IACtI,OAAO,IAAI,CAACD,gBAAgB,CAAC;MAC3BsE,MAAM,EAAED,KAAK;MACbN,MAAM,EAAE,WAAW;MACnB,GAAG9D;IACL,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,MAAMsE,wBAAwB,CAAC;EAC7B;EACA,MAAMvE,gBAAgBA,CAACC,OAA6B,EAAyD;IAC3G,MAAMC,MAAM,GAAGD,OAAO,GAAG,IAAI,CAACE,gBAAgB,CAACF,OAAO,CAAC,GAAG,EAAE;IAC5D,MAAMqC,QAAQ,GAAG,GAAG3C,aAAa,CAACW,aAAa,CAACC,IAAI,GAAGL,MAAM,EAAE;IAC/D,OAAOR,gBAAgB,CAACW,GAAG,CAAkCiC,QAAQ,CAAC;EACxE;;EAEA;EACA,MAAMvB,kBAAkBA,CAACC,IAAuC,EAAwD;IACtH,OAAOtB,gBAAgB,CAACuB,IAAI,CAC1BtB,aAAa,CAACW,aAAa,CAACC,IAAI,EAChCS,IACF,CAAC;EACH;;EAEA;EACA,MAAME,kBAAkBA,CAACN,EAAU,EAAEI,IAAuC,EAAwD;IAClI,OAAOtB,gBAAgB,CAACyB,GAAG,CACzBxB,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAChDE,IACF,CAAC;EACH;;EAEA;EACA,MAAMI,kBAAkBA,CAACR,EAAU,EAA8B;IAC/D,OAAOlB,gBAAgB,CAAC2B,MAAM,CAAO1B,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxF;;EAEA;EACA,MAAMQ,mBAAmBA,CAACV,EAAU,EAAwD;IAC1F,OAAOlB,gBAAgB,CAACuB,IAAI,CAAiCtB,aAAa,CAACW,aAAa,CAACiB,OAAO,CAACX,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClH;;EAEA;EACA,MAAMU,qBAAqBA,CAACZ,EAAU,EAAwD;IAC5F,OAAOlB,gBAAgB,CAACuB,IAAI,CAAiCtB,aAAa,CAACW,aAAa,CAACmB,SAAS,CAACb,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpH;;EAEA;EACA,MAAMH,mBAAmBA,CAACC,EAAU,EAAwD;IAC1F,OAAOlB,gBAAgB,CAACW,GAAG,CAAiCV,aAAa,CAACW,aAAa,CAACO,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC/G;;EAEA;EACQX,gBAAgBA,CAACF,OAA4B,EAAU;IAC7D,MAAMC,MAAM,GAAG,IAAIsE,eAAe,CAAC,CAAC;IAEpCpB,MAAM,CAACC,OAAO,CAACpD,OAAO,CAAC,CAACqD,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKpD,SAAS,IAAIoD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDtD,MAAM,CAACuE,MAAM,CAAClB,GAAG,EAAEC,KAAK,CAAC1C,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAOZ,MAAM,CAACY,QAAQ,CAAC,CAAC,GAAG,IAAIZ,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE;EACzD;;EAEA;EACA,MAAM2C,qBAAqBA,CAACxD,OAAsC,EAAyD;IACzH,MAAMyD,cAAmC,GAAG;MAC1CC,IAAI,EAAE,CAAC;MACPlD,KAAK,EAAE,EAAE;MACTmD,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,MAAM;MAClB,GAAG5D;IACL,CAAC;IACD,OAAO,IAAI,CAACD,gBAAgB,CAAC0D,cAAc,CAAC;EAC9C;;EAEA;EACA,MAAMjB,aAAaA,CAAA,EAAqD;IACtE,OAAO/C,gBAAgB,CAACW,GAAG,CAA6BV,aAAa,CAACW,aAAa,CAACqC,UAAU,CAAC;EACjG;;EAEA;EACA,MAAMC,gBAAgBA,CAAA,EAA2D;IAC/E,OAAOlD,gBAAgB,CAACW,GAAG,CAAmCV,aAAa,CAACW,aAAa,CAACuC,aAAa,CAAC;EAC1G;;EAEA;EACA,MAAMC,0BAA0BA,CAACC,UAAkB,EAA0D;IAC3G,OAAOrD,gBAAgB,CAACW,GAAG,CAAmCV,aAAa,CAACW,aAAa,CAAC0C,yBAAyB,CAACD,UAAU,CAACjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7I;;EAEA;EACA,MAAMmC,8BAA8BA,CAAA,EAAqD;IACvF,OAAOvD,gBAAgB,CAACW,GAAG,CAA6BV,aAAa,CAACW,aAAa,CAAC4C,6BAA6B,CAAC;EACpH;;EAEA;;EAEA;EACA,MAAMwB,qBAAqBA,CAAC1C,cAAsB,EAAE2C,QAAkB,EAA6B;IACjG,OAAOjF,gBAAgB,CAACuB,IAAI,CAC1B,GAAGtB,aAAa,CAACW,aAAa,CAACO,KAAK,CAACmB,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,SAAS,EACxE6D,QACF,CAAC;EACH;;EAEA;EACA,MAAMC,qBAAqBA,CAAC5C,cAAsB,EAA2C;IAC3F,OAAOtC,gBAAgB,CAACW,GAAG,CACzB,GAAGV,aAAa,CAACW,aAAa,CAACO,KAAK,CAACmB,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,SACjE,CAAC;EACH;;EAEA;EACA,MAAM+D,uBAAuBA,CAAC7C,cAAsB,EAAE8C,YAAoB,EAA8B;IACtG,OAAOpF,gBAAgB,CAAC2B,MAAM,CAC5B,GAAG1B,aAAa,CAACW,aAAa,CAACO,KAAK,CAACmB,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,WAAWgE,YAAY,EACxF,CAAC;EACH;;EAEA;EACA,MAAMC,gBAAgBA,CAAC/C,cAAsB,EAAEgD,UAA8D,EAA8B;IACzI,OAAOtF,gBAAgB,CAACyB,GAAG,CACzB,GAAGxB,aAAa,CAACW,aAAa,CAACO,KAAK,CAACmB,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,eAAe,EAC9E;MAAEkE;IAAW,CACf,CAAC;EACH;;EAEA;EACA,MAAMC,eAAeA,CAACjD,cAAsB,EAAE8C,YAAoB,EAA8B;IAC9F,OAAOpF,gBAAgB,CAACyB,GAAG,CACzB,GAAGxB,aAAa,CAACW,aAAa,CAACO,KAAK,CAACmB,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,WAAWgE,YAAY,UAAU,EAChG,CAAC,CACH,CAAC;EACH;;EAEA;EACA,MAAMlD,WAAWA,CAACI,cAAsB,EAAEH,UAAkB,EAA8B;IACxF,OAAOnC,gBAAgB,CAACuB,IAAI,CAAO,GAAGtB,aAAa,CAACW,aAAa,CAACC,IAAI,IAAIyB,cAAc,OAAO,EAAE;MAC/FG,WAAW,EAAEN;IACf,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,cAAcA,CAACJ,cAAsB,EAA8B;IACvE,OAAOtC,gBAAgB,CAAC2B,MAAM,CAAO,GAAG1B,aAAa,CAACW,aAAa,CAACC,IAAI,IAAIyB,cAAc,OAAO,CAAC;EACpG;AACF;;AAEA;AACA,OAAO,MAAMkD,mBAAmB,GAAG,IAAItF,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC9D,OAAO,MAAMuF,wBAAwB,GAAG,IAAIZ,wBAAwB,CAAC,CAAC;;AAEtE;AACA,OAAO,MAAMa,iCAAiC,GAAG,IAAIxF,mBAAmB,CAACJ,eAAe,CAAC;AACzF,OAAO,MAAM6F,mCAAmC,GAAG,IAAIzF,mBAAmB,CAACH,iBAAiB,CAAC;AAE7F,eAAeyF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}