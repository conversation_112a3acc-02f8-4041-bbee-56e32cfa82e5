{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1nb95v\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"16\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"x4nwl0\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"14\",\n  y2: \"18\",\n  key: \"wjye3r\"\n}], [\"path\", {\n  d: \"M16 10h.01\",\n  key: \"1m94wz\"\n}], [\"path\", {\n  d: \"M12 10h.01\",\n  key: \"1nrarc\"\n}], [\"path\", {\n  d: \"M8 10h.01\",\n  key: \"19clt8\"\n}], [\"path\", {\n  d: \"M12 14h.01\",\n  key: \"1etili\"\n}], [\"path\", {\n  d: \"M8 14h.01\",\n  key: \"6423bh\"\n}], [\"path\", {\n  d: \"M12 18h.01\",\n  key: \"mhygvu\"\n}], [\"path\", {\n  d: \"M8 18h.01\",\n  key: \"lrp35t\"\n}]];\nconst Calculator = createLucideIcon(\"calculator\", __iconNode);\nexport { __iconNode, Calculator as default };\n//# sourceMappingURL=calculator.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}