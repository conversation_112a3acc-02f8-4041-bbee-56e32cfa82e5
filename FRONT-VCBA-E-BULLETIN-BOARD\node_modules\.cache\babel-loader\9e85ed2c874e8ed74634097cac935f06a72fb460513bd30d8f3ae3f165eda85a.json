{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M9 8h7\",\n  key: \"kbo1nt\"\n}], [\"path\", {\n  d: \"M8 12h6\",\n  key: \"ikassy\"\n}], [\"path\", {\n  d: \"M11 16h5\",\n  key: \"oq65wt\"\n}]];\nconst SquareChartGantt = createLucideIcon(\"square-chart-gantt\", __iconNode);\nexport { __iconNode, SquareChartGantt as default };\n//# sourceMappingURL=square-chart-gantt.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}