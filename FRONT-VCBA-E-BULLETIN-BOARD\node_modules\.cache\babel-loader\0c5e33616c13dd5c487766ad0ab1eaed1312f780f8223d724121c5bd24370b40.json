{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 20h.01\",\n  key: \"4haj6o\"\n}], [\"path\", {\n  d: \"M7 20v-4\",\n  key: \"j294jx\"\n}], [\"path\", {\n  d: \"M12 20v-8\",\n  key: \"i3yub9\"\n}]];\nconst SignalMedium = createLucideIcon(\"signal-medium\", __iconNode);\nexport { __iconNode, SignalMedium as default };\n//# sourceMappingURL=signal-medium.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}