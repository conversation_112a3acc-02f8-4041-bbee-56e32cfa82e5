{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"16\",\n  y1: \"21\",\n  y2: \"21\",\n  key: \"1svkeh\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"17\",\n  y2: \"21\",\n  key: \"vw1qmm\"\n}]];\nconst Monitor = createLucideIcon(\"monitor\", __iconNode);\nexport { __iconNode, Monitor as default };\n//# sourceMappingURL=monitor.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}