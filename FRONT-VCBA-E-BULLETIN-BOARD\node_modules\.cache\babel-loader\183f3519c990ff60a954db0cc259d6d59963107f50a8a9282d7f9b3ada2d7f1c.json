{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{httpClient,adminHttpClient,studentHttpClient}from'./api.service';import{AdminAuthService}from'./admin-auth.service';import{API_ENDPOINTS}from'../config/constants';// Types for announcements\nclass AnnouncementService{// HTTP client instance\nconstructor(customHttpClient){this.client=void 0;this.client=customHttpClient||httpClient;// Use custom client or default\n}// Get all announcements with filters and pagination\nasync getAnnouncements(filters){const params=filters?this.buildQueryParams(filters):undefined;return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BASE,params);}// Get featured announcements\nasync getFeaturedAnnouncements(limit){const params=limit?{limit}:undefined;return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.FEATURED,params);}// Get single announcement by ID\nasync getAnnouncementById(id){return this.client.get(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));}// Create new announcement\nasync createAnnouncement(data){return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.BASE,data);}// Update announcement\nasync updateAnnouncement(id,data){return httpClient.put(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()),data);}// Delete announcement\nasync deleteAnnouncement(id){return httpClient.delete(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));}// Publish announcement\nasync publishAnnouncement(id){return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));}// Unpublish announcement\nasync unpublishAnnouncement(id){return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));}// Mark announcement as viewed\nasync markAsViewed(id){return httpClient.post(API_ENDPOINTS.ANNOUNCEMENTS.VIEW(id.toString()));}// Add reaction to announcement\nasync addReaction(id,reactionId){console.log('❤️ AnnouncementService - Adding reaction:',{announcementId:id,reactionId,clientType:this.client===adminHttpClient?'ADMIN':this.client===studentHttpClient?'STUDENT':'DEFAULT'});return this.client.post(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()),{reaction_id:reactionId});}// Remove reaction from announcement\nasync removeReaction(id){console.log('💔 AnnouncementService - Removing reaction:',{announcementId:id,clientType:this.client===adminHttpClient?'ADMIN':this.client===studentHttpClient?'STUDENT':'DEFAULT'});return this.client.delete(API_ENDPOINTS.ANNOUNCEMENTS.LIKE(id.toString()));}// Get announcement reaction statistics\nasync getReactionStats(id){const endpoint=id?API_ENDPOINTS.ANNOUNCEMENTS.REACTIONS(id.toString()):API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES;return httpClient.get(endpoint);}// Get categories (public endpoint)\nasync getCategories(){return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);}// Get all subcategories (public endpoint)\nasync getSubcategories(){return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);}// Get subcategories by category ID (public endpoint)\nasync getSubcategoriesByCategory(categoryId){return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));}// Get categories with their subcategories (hierarchical structure) (public endpoint)\nasync getCategoriesWithSubcategories(){return httpClient.getPublic(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);}// Get reaction types\nasync getReactionTypes(){return httpClient.get(API_ENDPOINTS.ANNOUNCEMENTS.REACTION_TYPES);}// Helper method to build query parameters\nbuildQueryParams(filters){const params={};Object.entries(filters).forEach(_ref=>{let[key,value]=_ref;if(value!==undefined&&value!==null&&value!==''){params[key]=value.toString();}});return params;}// Get announcements for admin dashboard\nasync getAdminAnnouncements(filters){const defaultFilters=_objectSpread({page:1,limit:20,sort_by:'created_at',sort_order:'DESC'},filters);return this.getAnnouncements(defaultFilters);}// Get published announcements for students\nasync getPublishedAnnouncements(filters){const defaultFilters=_objectSpread({page:1,limit:20,status:'published',sort_by:'created_at',sort_order:'DESC'},filters);return this.getAnnouncements(defaultFilters);}// Get pinned announcements\nasync getPinnedAnnouncements(){return this.getAnnouncements({is_pinned:true,status:'published',sort_by:'created_at',sort_order:'DESC'});}// Get alert announcements\nasync getAlertAnnouncements(){return this.getAnnouncements({is_alert:true,status:'published',sort_by:'created_at',sort_order:'DESC'});}// Search announcements\nasync searchAnnouncements(query,filters){return this.getAnnouncements(_objectSpread({search:query,status:'published'},filters));}}// Admin-specific announcement service that uses admin authentication\nclass AdminAnnouncementService{// Get all announcements with admin auth\nasync getAnnouncements(filters){const params=filters?this.buildQueryParams(filters):'';const endpoint=\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BASE).concat(params);return AdminAuthService.get(endpoint);}// Create new announcement with admin auth\nasync createAnnouncement(data){return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.BASE,data);}// Update announcement with admin auth\nasync updateAnnouncement(id,data){return AdminAuthService.put(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()),data);}// Delete announcement with admin auth\nasync deleteAnnouncement(id){return AdminAuthService.delete(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));}// Publish announcement with admin auth\nasync publishAnnouncement(id){return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.PUBLISH(id.toString()));}// Unpublish announcement with admin auth\nasync unpublishAnnouncement(id){return AdminAuthService.post(API_ENDPOINTS.ANNOUNCEMENTS.UNPUBLISH(id.toString()));}// Get single announcement by ID with admin auth\nasync getAnnouncementById(id){return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(id.toString()));}// Helper method to build query parameters\nbuildQueryParams(filters){const params=new URLSearchParams();Object.entries(filters).forEach(_ref2=>{let[key,value]=_ref2;if(value!==undefined&&value!==null&&value!==''){params.append(key,value.toString());}});return params.toString()?\"?\".concat(params.toString()):'';}// Get announcements for admin dashboard with admin auth\nasync getAdminAnnouncements(filters){const defaultFilters=_objectSpread({page:1,limit:20,sort_by:'created_at',sort_order:'DESC'},filters);return this.getAnnouncements(defaultFilters);}// Get categories with admin auth\nasync getCategories(){return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES);}// Get all subcategories with admin auth\nasync getSubcategories(){return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES);}// Get subcategories by category ID with admin auth\nasync getSubcategoriesByCategory(categoryId){return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.SUBCATEGORIES_BY_CATEGORY(categoryId.toString()));}// Get categories with their subcategories (hierarchical structure) with admin auth\nasync getCategoriesWithSubcategories(){return AdminAuthService.get(API_ENDPOINTS.ANNOUNCEMENTS.CATEGORIES_WITH_SUBCATEGORIES);}// Multiple image management methods\n// Add multiple images to announcement\nasync addAnnouncementImages(announcementId,formData){return AdminAuthService.post(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString()),\"/images\"),formData);}// Get all images for announcement\nasync getAnnouncementImages(announcementId){return AdminAuthService.get(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString()),\"/images\"));}// Delete specific image from announcement\nasync deleteAnnouncementImage(announcementId,attachmentId){return AdminAuthService.delete(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString()),\"/images/\").concat(attachmentId));}// Update image display order\nasync updateImageOrder(announcementId,imageOrder){return AdminAuthService.put(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString()),\"/images/order\"),{imageOrder});}// Set primary image for announcement\nasync setPrimaryImage(announcementId,attachmentId){return AdminAuthService.put(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BY_ID(announcementId.toString()),\"/images/\").concat(attachmentId,\"/primary\"),{});}// Add reaction to announcement (admin)\nasync addReaction(announcementId,reactionId){return AdminAuthService.post(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BASE,\"/\").concat(announcementId,\"/like\"),{reaction_id:reactionId});}// Remove reaction from announcement (admin)\nasync removeReaction(announcementId){return AdminAuthService.delete(\"\".concat(API_ENDPOINTS.ANNOUNCEMENTS.BASE,\"/\").concat(announcementId,\"/like\"));}}// Export service instances\nexport const announcementService=new AnnouncementService();// Default/legacy service\nexport const adminAnnouncementService=new AdminAnnouncementService();// Role-specific announcement services with proper token management\nexport const adminAnnouncementServiceWithToken=new AnnouncementService(adminHttpClient);export const studentAnnouncementServiceWithToken=new AnnouncementService(studentHttpClient);export default announcementService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}