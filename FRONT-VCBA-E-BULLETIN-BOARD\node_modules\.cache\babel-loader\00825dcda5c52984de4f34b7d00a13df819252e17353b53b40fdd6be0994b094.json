{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v2.343\",\n  key: \"15t272\"\n}], [\"path\", {\n  d: \"M14 2v6.343\",\n  key: \"sxr80q\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 20a2 2 0 0 1-2 2H6a2 2 0 0 1-1.755-2.96l5.227-9.563\",\n  key: \"k0duyd\"\n}], [\"path\", {\n  d: \"M6.453 15H15\",\n  key: \"1f0z33\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}]];\nconst FlaskConicalOff = createLucideIcon(\"flask-conical-off\", __iconNode);\nexport { __iconNode, FlaskConicalOff as default };\n//# sourceMappingURL=flask-conical-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}