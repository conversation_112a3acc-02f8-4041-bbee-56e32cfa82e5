{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.5 13h6\",\n  key: \"p1my2r\"\n}], [\"path\", {\n  d: \"m2 16 4.5-9 4.5 9\",\n  key: \"ndf0b3\"\n}], [\"path\", {\n  d: \"M18 7v9\",\n  key: \"pknjwm\"\n}], [\"path\", {\n  d: \"m14 12 4 4 4-4\",\n  key: \"buelq4\"\n}]];\nconst AArrowDown = createLucideIcon(\"a-arrow-down\", __iconNode);\nexport { __iconNode, AArrowDown as default };\n//# sourceMappingURL=a-arrow-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}