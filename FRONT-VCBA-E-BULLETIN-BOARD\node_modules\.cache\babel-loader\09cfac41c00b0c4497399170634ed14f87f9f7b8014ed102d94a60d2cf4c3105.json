{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 12H3\",\n  key: \"51ecnj\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M21 12h-6\",\n  key: \"bt1uis\"\n}]];\nconst ListMinus = createLucideIcon(\"list-minus\", __iconNode);\nexport { __iconNode, ListMinus as default };\n//# sourceMappingURL=list-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}