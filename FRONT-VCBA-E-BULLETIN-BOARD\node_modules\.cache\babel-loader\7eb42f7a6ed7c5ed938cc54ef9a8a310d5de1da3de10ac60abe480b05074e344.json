{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.929 9.629A1 1 0 0 1 19 11H9a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 11 4h6a1 1 0 0 1 .928.629z\",\n  key: \"1uvrbf\"\n}], [\"path\", {\n  d: \"M6 15a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1z\",\n  key: \"154r2a\"\n}], [\"path\", {\n  d: \"M8 18h4a2 2 0 0 0 2-2v-5\",\n  key: \"z9mbu0\"\n}]];\nconst LampWallUp = createLucideIcon(\"lamp-wall-up\", __iconNode);\nexport { __iconNode, LampWallUp as default };\n//# sourceMappingURL=lamp-wall-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}