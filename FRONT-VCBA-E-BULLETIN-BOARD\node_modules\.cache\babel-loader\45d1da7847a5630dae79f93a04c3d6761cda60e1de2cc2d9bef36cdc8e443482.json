{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 15 5 5 5-5\",\n  key: \"1hf1tw\"\n}], [\"path\", {\n  d: \"m7 9 5-5 5 5\",\n  key: \"sgt6xg\"\n}]];\nconst ChevronsUpDown = createLucideIcon(\"chevrons-up-down\", __iconNode);\nexport { __iconNode, ChevronsUpDown as default };\n//# sourceMappingURL=chevrons-up-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}