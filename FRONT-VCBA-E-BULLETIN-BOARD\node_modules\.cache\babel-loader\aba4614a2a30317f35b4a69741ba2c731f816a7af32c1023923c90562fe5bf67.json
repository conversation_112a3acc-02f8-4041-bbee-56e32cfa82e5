{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 11 11 .9a1 1 0 0 1 .8 1.1l-.665 4.158a1 1 0 0 1-.988.842H20\",\n  key: \"she1j9\"\n}], [\"path\", {\n  d: \"M16 18h-5\",\n  key: \"bq60fd\"\n}], [\"path\", {\n  d: \"M18 5a1 1 0 0 0-1 1v5.573\",\n  key: \"1kv8ia\"\n}], [\"path\", {\n  d: \"M3 4h8.129a1 1 0 0 1 .99.863L13 11.246\",\n  key: \"1q1ert\"\n}], [\"path\", {\n  d: \"M4 11V4\",\n  key: \"9ft8pt\"\n}], [\"path\", {\n  d: \"M7 15h.01\",\n  key: \"k5ht0j\"\n}], [\"path\", {\n  d: \"M8 10.1V4\",\n  key: \"1j<PERSON><PERSON>\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1emm8v\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"15\",\n  r: \"5\",\n  key: \"ddtuc\"\n}]];\nconst Tractor = createLucideIcon(\"tractor\", __iconNode);\nexport { __iconNode, Tractor as default };\n//# sourceMappingURL=tractor.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}