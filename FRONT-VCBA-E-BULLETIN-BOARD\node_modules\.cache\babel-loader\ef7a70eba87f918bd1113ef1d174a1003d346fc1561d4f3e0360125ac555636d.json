{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"dy24zr\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"12\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1ht384\"\n}], [\"path\", {\n  d: \"M22 2v20\",\n  key: \"40qfg1\"\n}]];\nconst AlignHorizontalJustifyEnd = createLucideIcon(\"align-horizontal-justify-end\", __iconNode);\nexport { __iconNode, AlignHorizontalJustifyEnd as default };\n//# sourceMappingURL=align-horizontal-justify-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}