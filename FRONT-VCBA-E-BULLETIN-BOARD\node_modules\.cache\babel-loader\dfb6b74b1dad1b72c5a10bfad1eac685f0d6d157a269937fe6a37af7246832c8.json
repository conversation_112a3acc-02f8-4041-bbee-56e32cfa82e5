{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 6 2 7\",\n  key: \"1mqr15\"\n}], [\"path\", {\n  d: \"M10 6h4\",\n  key: \"1itunk\"\n}], [\"path\", {\n  d: \"m22 7-2-1\",\n  key: \"1umjhc\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"16\",\n  x: \"4\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"1wxw4b\"\n}], [\"path\", {\n  d: \"M4 11h16\",\n  key: \"mpoxn0\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}], [\"path\", {\n  d: \"M16 15h.01\",\n  key: \"rnfrdf\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"path\", {\n  d: \"M18 21v-2\",\n  key: \"sqyl04\"\n}]];\nconst BusFront = createLucideIcon(\"bus-front\", __iconNode);\nexport { __iconNode, BusFront as default };\n//# sourceMappingURL=bus-front.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}