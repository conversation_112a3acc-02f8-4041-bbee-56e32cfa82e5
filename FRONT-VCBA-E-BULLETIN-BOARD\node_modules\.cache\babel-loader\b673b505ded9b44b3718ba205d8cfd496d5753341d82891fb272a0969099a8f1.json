{"ast": null, "code": "import { httpClient } from './api.service';\nclass HolidayService {\n  constructor() {\n    this.baseUrl = '/api/holidays';\n  }\n  /**\n   * Get holiday sync status for a specific year\n   */\n  async getHolidaySyncStatus(year) {\n    const response = await httpClient.get(`${this.baseUrl}/status/${year}`);\n    return response.data;\n  }\n\n  /**\n   * Sync holidays for a specific year\n   */\n  async syncHolidays(year, options = {}) {\n    var _options$includePhili, _options$includeInter, _options$clearExistin;\n    const response = await httpClient.post(`${this.baseUrl}/sync/${year}`, {\n      includePhilippine: (_options$includePhili = options.includePhilippine) !== null && _options$includePhili !== void 0 ? _options$includePhili : true,\n      includeInternational: (_options$includeInter = options.includeInternational) !== null && _options$includeInter !== void 0 ? _options$includeInter : true,\n      clearExisting: (_options$clearExistin = options.clearExisting) !== null && _options$clearExistin !== void 0 ? _options$clearExistin : true\n    });\n    return response.data;\n  }\n\n  /**\n   * Get holidays for a specific year with pagination and filters\n   */\n  async getHolidays(year, queryParams) {\n    const url = queryParams ? `${this.baseUrl}/${year}?${queryParams}` : `${this.baseUrl}/${year}`;\n    const response = await httpClient.get(url);\n    return response.data;\n  }\n\n  /**\n   * Toggle holiday active status\n   */\n  async toggleHolidayStatus(holidayId, isActive) {\n    const response = await httpClient.put(`${this.baseUrl}/${holidayId}/status`, {\n      is_active: isActive\n    });\n    return response.data;\n  }\n\n  /**\n   * Clear holidays for a specific year\n   */\n  async clearHolidays(year, holidayType) {\n    const body = holidayType ? {\n      holiday_type: holidayType\n    } : {};\n    const response = await httpClient.delete(`${this.baseUrl}/${year}`, body);\n    return response.data;\n  }\n\n  /**\n   * Get holiday statistics for multiple years\n   */\n  async getHolidayStatistics() {\n    const response = await httpClient.get(`${this.baseUrl}/statistics`);\n    return response.data;\n  }\n\n  /**\n   * Sync holidays for current year (convenience method)\n   */\n  async syncCurrentYear(options) {\n    const currentYear = new Date().getFullYear();\n    return this.syncHolidays(currentYear, options);\n  }\n\n  /**\n   * Get holidays for current year (convenience method)\n   */\n  async getCurrentYearHolidays(queryParams) {\n    const currentYear = new Date().getFullYear();\n    return this.getHolidays(currentYear, queryParams);\n  }\n\n  /**\n   * Check if holidays are synced for a year\n   */\n  async isYearSynced(year) {\n    try {\n      const response = await this.getHolidaySyncStatus(year);\n      return response.data.isSynced;\n    } catch (error) {\n      console.error(`Failed to check sync status for ${year}:`, error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holiday type display name\n   */\n  getHolidayTypeDisplayName(type) {\n    switch (type) {\n      case 'local':\n        return 'Philippine Holiday';\n      case 'international':\n        return 'International Holiday';\n      case 'school':\n        return 'School Holiday';\n      default:\n        return 'Holiday';\n    }\n  }\n\n  /**\n   * Get holiday type icon\n   */\n  getHolidayTypeIcon(type) {\n    switch (type) {\n      case 'local':\n        return '🇵🇭';\n      case 'international':\n        return '🌍';\n      case 'school':\n        return '🏫';\n      default:\n        return '🎉';\n    }\n  }\n\n  /**\n   * Get holiday type color\n   */\n  getHolidayTypeColor(type) {\n    switch (type) {\n      case 'local':\n        return '#dc2626';\n      // Red\n      case 'international':\n        return '#7c3aed';\n      // Purple\n      case 'school':\n        return '#059669';\n      // Green\n      default:\n        return '#6b7280';\n      // Gray\n    }\n  }\n\n  /**\n   * Format holiday date for display\n   */\n  formatHolidayDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  /**\n   * Check if a holiday is read-only (auto-generated)\n   */\n  isHolidayReadOnly(holiday) {\n    return holiday.is_holiday && holiday.is_auto_generated;\n  }\n\n  /**\n   * Get years available for holiday management\n   */\n  getAvailableYears() {\n    const currentYear = new Date().getFullYear();\n    const years = [];\n\n    // 5 years back to 10 years forward\n    for (let i = -5; i <= 10; i++) {\n      years.push(currentYear + i);\n    }\n    return years;\n  }\n\n  /**\n   * Validate year for holiday operations\n   */\n  validateYear(year) {\n    const currentYear = new Date().getFullYear();\n    return year >= currentYear - 5 && year <= currentYear + 10;\n  }\n}\n\n// Export singleton instance\nexport const holidayService = new HolidayService();\nexport default holidayService;", "map": {"version": 3, "names": ["httpClient", "HolidayService", "constructor", "baseUrl", "getHolidaySyncStatus", "year", "response", "get", "data", "syncHolidays", "options", "_options$includePhili", "_options$includeInter", "_options$clearExistin", "post", "includePhilippine", "includeInternational", "clearExisting", "getHolidays", "queryParams", "url", "toggleHolidayStatus", "holidayId", "isActive", "put", "is_active", "clearHolidays", "holidayType", "body", "holiday_type", "delete", "getHolidayStatistics", "syncCurrentYear", "currentYear", "Date", "getFullYear", "getCurrentYearHolidays", "isYearSynced", "isSynced", "error", "console", "getHolidayTypeDisplayName", "type", "getHolidayTypeIcon", "getHolidayTypeColor", "formatHolidayDate", "dateString", "date", "toLocaleDateString", "weekday", "month", "day", "isHolidayReadOnly", "holiday", "is_holiday", "is_auto_generated", "getAvailableYears", "years", "i", "push", "validateYear", "holidayService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/holidayService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { ApiResponse } from '../types/common.types';\n\nexport interface Holiday {\n  calendar_id: number;\n  title: string;\n  description: string;\n  event_date: string;\n  end_date: string;\n  is_active: boolean;\n  is_published: boolean;\n  is_holiday: boolean;\n  holiday_type: 'local' | 'international' | 'school';\n  country_code?: string;\n  is_auto_generated: boolean;\n  api_source?: string;\n  local_name?: string;\n  holiday_types?: string;\n  is_global?: boolean;\n  is_fixed?: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface HolidaySyncStatus {\n  year: number;\n  lastSync: string | null;\n  totalHolidays: number;\n  philippineHolidays: number;\n  internationalHolidays: number;\n  schoolHolidays: number;\n  isSynced: boolean;\n}\n\nexport interface HolidaySyncResult {\n  year: number;\n  philippineHolidays: number;\n  internationalHolidays: number;\n  totalInserted: number;\n  clearedCount: number;\n  errors: string[];\n}\n\nexport interface HolidayPagination {\n  page: number;\n  limit: number;\n  total: number;\n  totalPages: number;\n  hasNext: boolean;\n  hasPrev: boolean;\n}\n\nexport interface HolidaysResponse {\n  holidays: Holiday[];\n  pagination: HolidayPagination;\n}\n\nexport interface HolidayStatistics {\n  [year: number]: HolidaySyncStatus;\n}\n\nclass HolidayService {\n  private baseUrl = '/api/holidays';\n\n  /**\n   * Get holiday sync status for a specific year\n   */\n  async getHolidaySyncStatus(year: number): Promise<{ success: boolean; data: HolidaySyncStatus }> {\n    const response = await httpClient.get<{ success: boolean; data: HolidaySyncStatus }>(`${this.baseUrl}/status/${year}`);\n    return response.data;\n  }\n\n  /**\n   * Sync holidays for a specific year\n   */\n  async syncHolidays(\n    year: number,\n    options: {\n      includePhilippine?: boolean;\n      includeInternational?: boolean;\n      clearExisting?: boolean;\n    } = {}\n  ): Promise<{ success: boolean; data: HolidaySyncResult }> {\n    const response = await httpClient.post<{ success: boolean; data: HolidaySyncResult }>(`${this.baseUrl}/sync/${year}`, {\n      includePhilippine: options.includePhilippine ?? true,\n      includeInternational: options.includeInternational ?? true,\n      clearExisting: options.clearExisting ?? true\n    });\n    return response.data;\n  }\n\n  /**\n   * Get holidays for a specific year with pagination and filters\n   */\n  async getHolidays(\n    year: number,\n    queryParams?: string\n  ): Promise<{ success: boolean; data: HolidaysResponse }> {\n    const url = queryParams\n      ? `${this.baseUrl}/${year}?${queryParams}`\n      : `${this.baseUrl}/${year}`;\n\n    const response = await httpClient.get<{ success: boolean; data: HolidaysResponse }>(url);\n    return response.data;\n  }\n\n  /**\n   * Toggle holiday active status\n   */\n  async toggleHolidayStatus(\n    holidayId: number,\n    isActive: boolean\n  ): Promise<{ success: boolean; data: { holidayId: number; is_active: boolean } }> {\n    const response = await httpClient.put<{ success: boolean; data: { holidayId: number; is_active: boolean } }>(`${this.baseUrl}/${holidayId}/status`, {\n      is_active: isActive\n    });\n    return response.data;\n  }\n\n  /**\n   * Clear holidays for a specific year\n   */\n  async clearHolidays(\n    year: number,\n    holidayType?: string\n  ): Promise<{ success: boolean; data: { year: number; clearedCount: number; holiday_type: string } }> {\n    const body = holidayType ? { holiday_type: holidayType } : {};\n    const response = await httpClient.delete<{ success: boolean; data: { year: number; clearedCount: number; holiday_type: string } }>(`${this.baseUrl}/${year}`, body);\n    return response.data;\n  }\n\n  /**\n   * Get holiday statistics for multiple years\n   */\n  async getHolidayStatistics(): Promise<{ success: boolean; data: HolidayStatistics }> {\n    const response = await httpClient.get<{ success: boolean; data: HolidayStatistics }>(`${this.baseUrl}/statistics`);\n    return response.data;\n  }\n\n  /**\n   * Sync holidays for current year (convenience method)\n   */\n  async syncCurrentYear(options?: {\n    includePhilippine?: boolean;\n    includeInternational?: boolean;\n    clearExisting?: boolean;\n  }): Promise<{ success: boolean; data: HolidaySyncResult }> {\n    const currentYear = new Date().getFullYear();\n    return this.syncHolidays(currentYear, options);\n  }\n\n  /**\n   * Get holidays for current year (convenience method)\n   */\n  async getCurrentYearHolidays(queryParams?: string): Promise<{ success: boolean; data: HolidaysResponse }> {\n    const currentYear = new Date().getFullYear();\n    return this.getHolidays(currentYear, queryParams);\n  }\n\n  /**\n   * Check if holidays are synced for a year\n   */\n  async isYearSynced(year: number): Promise<boolean> {\n    try {\n      const response = await this.getHolidaySyncStatus(year);\n      return response.data.isSynced;\n    } catch (error) {\n      console.error(`Failed to check sync status for ${year}:`, error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holiday type display name\n   */\n  getHolidayTypeDisplayName(type: string): string {\n    switch (type) {\n      case 'local':\n        return 'Philippine Holiday';\n      case 'international':\n        return 'International Holiday';\n      case 'school':\n        return 'School Holiday';\n      default:\n        return 'Holiday';\n    }\n  }\n\n  /**\n   * Get holiday type icon\n   */\n  getHolidayTypeIcon(type: string): string {\n    switch (type) {\n      case 'local':\n        return '🇵🇭';\n      case 'international':\n        return '🌍';\n      case 'school':\n        return '🏫';\n      default:\n        return '🎉';\n    }\n  }\n\n  /**\n   * Get holiday type color\n   */\n  getHolidayTypeColor(type: string): string {\n    switch (type) {\n      case 'local':\n        return '#dc2626'; // Red\n      case 'international':\n        return '#7c3aed'; // Purple\n      case 'school':\n        return '#059669'; // Green\n      default:\n        return '#6b7280'; // Gray\n    }\n  }\n\n  /**\n   * Format holiday date for display\n   */\n  formatHolidayDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  /**\n   * Check if a holiday is read-only (auto-generated)\n   */\n  isHolidayReadOnly(holiday: Holiday): boolean {\n    return holiday.is_holiday && holiday.is_auto_generated;\n  }\n\n  /**\n   * Get years available for holiday management\n   */\n  getAvailableYears(): number[] {\n    const currentYear = new Date().getFullYear();\n    const years: number[] = [];\n    \n    // 5 years back to 10 years forward\n    for (let i = -5; i <= 10; i++) {\n      years.push(currentYear + i);\n    }\n    \n    return years;\n  }\n\n  /**\n   * Validate year for holiday operations\n   */\n  validateYear(year: number): boolean {\n    const currentYear = new Date().getFullYear();\n    return year >= currentYear - 5 && year <= currentYear + 10;\n  }\n}\n\n// Export singleton instance\nexport const holidayService = new HolidayService();\nexport default holidayService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AA6D1C,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,eAAe;EAAA;EAEjC;AACF;AACA;EACE,MAAMC,oBAAoBA,CAACC,IAAY,EAA0D;IAC/F,MAAMC,QAAQ,GAAG,MAAMN,UAAU,CAACO,GAAG,CAAgD,GAAG,IAAI,CAACJ,OAAO,WAAWE,IAAI,EAAE,CAAC;IACtH,OAAOC,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMC,YAAYA,CAChBJ,IAAY,EACZK,OAIC,GAAG,CAAC,CAAC,EACkD;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACxD,MAAMP,QAAQ,GAAG,MAAMN,UAAU,CAACc,IAAI,CAAgD,GAAG,IAAI,CAACX,OAAO,SAASE,IAAI,EAAE,EAAE;MACpHU,iBAAiB,GAAAJ,qBAAA,GAAED,OAAO,CAACK,iBAAiB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MACpDK,oBAAoB,GAAAJ,qBAAA,GAAEF,OAAO,CAACM,oBAAoB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MAC1DK,aAAa,GAAAJ,qBAAA,GAAEH,OAAO,CAACO,aAAa,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI;IAC1C,CAAC,CAAC;IACF,OAAOP,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMU,WAAWA,CACfb,IAAY,EACZc,WAAoB,EACmC;IACvD,MAAMC,GAAG,GAAGD,WAAW,GACnB,GAAG,IAAI,CAAChB,OAAO,IAAIE,IAAI,IAAIc,WAAW,EAAE,GACxC,GAAG,IAAI,CAAChB,OAAO,IAAIE,IAAI,EAAE;IAE7B,MAAMC,QAAQ,GAAG,MAAMN,UAAU,CAACO,GAAG,CAA+Ca,GAAG,CAAC;IACxF,OAAOd,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMa,mBAAmBA,CACvBC,SAAiB,EACjBC,QAAiB,EAC+D;IAChF,MAAMjB,QAAQ,GAAG,MAAMN,UAAU,CAACwB,GAAG,CAAwE,GAAG,IAAI,CAACrB,OAAO,IAAImB,SAAS,SAAS,EAAE;MAClJG,SAAS,EAAEF;IACb,CAAC,CAAC;IACF,OAAOjB,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMkB,aAAaA,CACjBrB,IAAY,EACZsB,WAAoB,EAC+E;IACnG,MAAMC,IAAI,GAAGD,WAAW,GAAG;MAAEE,YAAY,EAAEF;IAAY,CAAC,GAAG,CAAC,CAAC;IAC7D,MAAMrB,QAAQ,GAAG,MAAMN,UAAU,CAAC8B,MAAM,CAA2F,GAAG,IAAI,CAAC3B,OAAO,IAAIE,IAAI,EAAE,EAAEuB,IAAI,CAAC;IACnK,OAAOtB,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMuB,oBAAoBA,CAAA,EAA2D;IACnF,MAAMzB,QAAQ,GAAG,MAAMN,UAAU,CAACO,GAAG,CAAgD,GAAG,IAAI,CAACJ,OAAO,aAAa,CAAC;IAClH,OAAOG,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMwB,eAAeA,CAACtB,OAIrB,EAA0D;IACzD,MAAMuB,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,OAAO,IAAI,CAAC1B,YAAY,CAACwB,WAAW,EAAEvB,OAAO,CAAC;EAChD;;EAEA;AACF;AACA;EACE,MAAM0B,sBAAsBA,CAACjB,WAAoB,EAAyD;IACxG,MAAMc,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,OAAO,IAAI,CAACjB,WAAW,CAACe,WAAW,EAAEd,WAAW,CAAC;EACnD;;EAEA;AACF;AACA;EACE,MAAMkB,YAAYA,CAAChC,IAAY,EAAoB;IACjD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAACC,IAAI,CAAC;MACtD,OAAOC,QAAQ,CAACE,IAAI,CAAC8B,QAAQ;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmClC,IAAI,GAAG,EAAEkC,KAAK,CAAC;MAChE,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACEE,yBAAyBA,CAACC,IAAY,EAAU;IAC9C,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,oBAAoB;MAC7B,KAAK,eAAe;QAClB,OAAO,uBAAuB;MAChC,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,SAAS;IACpB;EACF;;EAEA;AACF;AACA;EACEC,kBAAkBA,CAACD,IAAY,EAAU;IACvC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,MAAM;MACf,KAAK,eAAe;QAClB,OAAO,IAAI;MACb,KAAK,QAAQ;QACX,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;;EAEA;AACF;AACA;EACEE,mBAAmBA,CAACF,IAAY,EAAU;IACxC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,SAAS;MAAE;MACpB,KAAK,eAAe;QAClB,OAAO,SAAS;MAAE;MACpB,KAAK,QAAQ;QACX,OAAO,SAAS;MAAE;MACpB;QACE,OAAO,SAAS;MAAE;IACtB;EACF;;EAEA;AACF;AACA;EACEG,iBAAiBA,CAACC,UAAkB,EAAU;IAC5C,MAAMC,IAAI,GAAG,IAAIb,IAAI,CAACY,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACf5C,IAAI,EAAE,SAAS;MACf6C,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEC,iBAAiBA,CAACC,OAAgB,EAAW;IAC3C,OAAOA,OAAO,CAACC,UAAU,IAAID,OAAO,CAACE,iBAAiB;EACxD;;EAEA;AACF;AACA;EACEC,iBAAiBA,CAAA,EAAa;IAC5B,MAAMvB,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,MAAMsB,KAAe,GAAG,EAAE;;IAE1B;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC7BD,KAAK,CAACE,IAAI,CAAC1B,WAAW,GAAGyB,CAAC,CAAC;IAC7B;IAEA,OAAOD,KAAK;EACd;;EAEA;AACF;AACA;EACEG,YAAYA,CAACvD,IAAY,EAAW;IAClC,MAAM4B,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,OAAO9B,IAAI,IAAI4B,WAAW,GAAG,CAAC,IAAI5B,IAAI,IAAI4B,WAAW,GAAG,EAAE;EAC5D;AACF;;AAEA;AACA,OAAO,MAAM4B,cAAc,GAAG,IAAI5D,cAAc,CAAC,CAAC;AAClD,eAAe4D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}