{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 15H6a4 4 0 0 0-4 4v2\",\n  key: \"1nfge6\"\n}], [\"path\", {\n  d: \"m14.305 16.53.923-.382\",\n  key: \"1itpsq\"\n}], [\"path\", {\n  d: \"m15.228 13.852-.923-.383\",\n  key: \"eplpkm\"\n}], [\"path\", {\n  d: \"m16.852 12.228-.383-.923\",\n  key: \"13v3q0\"\n}], [\"path\", {\n  d: \"m16.852 17.772-.383.924\",\n  key: \"1i8mnm\"\n}], [\"path\", {\n  d: \"m19.148 12.228.383-.923\",\n  key: \"1q8j1v\"\n}], [\"path\", {\n  d: \"m19.53 18.696-.382-.924\",\n  key: \"vk1qj3\"\n}], [\"path\", {\n  d: \"m20.772 13.852.924-.383\",\n  key: \"n880s0\"\n}], [\"path\", {\n  d: \"m20.772 16.148.924.383\",\n  key: \"1g6xey\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"15\",\n  r: \"3\",\n  key: \"gjjjvw\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}]];\nconst UserCog = createLucideIcon(\"user-cog\", __iconNode);\nexport { __iconNode, UserCog as default };\n//# sourceMappingURL=user-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}