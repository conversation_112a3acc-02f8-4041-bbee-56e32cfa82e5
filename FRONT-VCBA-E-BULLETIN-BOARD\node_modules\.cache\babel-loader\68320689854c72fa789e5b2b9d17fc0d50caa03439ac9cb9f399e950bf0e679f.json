{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M8 13h5\",\n  key: \"1k9z8w\"\n}], [\"path\", {\n  d: \"M10 17V9.5a2.5 2.5 0 0 1 5 0\",\n  key: \"1dzgp0\"\n}], [\"path\", {\n  d: \"M8 17h7\",\n  key: \"8mjdqu\"\n}]];\nconst ReceiptPoundSterling = createLucideIcon(\"receipt-pound-sterling\", __iconNode);\nexport { __iconNode, ReceiptPoundSterling as default };\n//# sourceMappingURL=receipt-pound-sterling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}