{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4\",\n  key: \"1nerag\"\n}], [\"path\", {\n  d: \"M14 13.12c0 2.38 0 6.38-1 8.88\",\n  key: \"o46ks0\"\n}], [\"path\", {\n  d: \"M17.29 21.02c.12-.6.43-2.3.5-3.02\",\n  key: \"ptglia\"\n}], [\"path\", {\n  d: \"M2 12a10 10 0 0 1 18-6\",\n  key: \"ydlgp0\"\n}], [\"path\", {\n  d: \"M2 16h.01\",\n  key: \"1gqxmh\"\n}], [\"path\", {\n  d: \"M21.8 16c.2-2 .131-5.354 0-6\",\n  key: \"drycrb\"\n}], [\"path\", {\n  d: \"M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2\",\n  key: \"1tidbn\"\n}], [\"path\", {\n  d: \"M8.65 22c.21-.66.45-1.32.57-2\",\n  key: \"13wd9y\"\n}], [\"path\", {\n  d: \"M9 6.8a6 6 0 0 1 9 5.2v2\",\n  key: \"1fr1j5\"\n}]];\nconst Fingerprint = createLucideIcon(\"fingerprint\", __iconNode);\nexport { __iconNode, Fingerprint as default };\n//# sourceMappingURL=fingerprint.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}