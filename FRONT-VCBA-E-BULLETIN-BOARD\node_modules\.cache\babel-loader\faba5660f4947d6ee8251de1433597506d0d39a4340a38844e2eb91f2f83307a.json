{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{httpClient,adminHttpClient,studentHttpClient}from'./api.service';import{API_ENDPOINTS,STUDENT_AUTH_TOKEN_KEY,ADMIN_AUTH_TOKEN_KEY,ADMIN_USER_DATA_KEY,STUDENT_USER_DATA_KEY,API_BASE_URL}from'../config/constants';// Types for comments\nclass CommentService{// HTTP client instance\nconstructor(customHttpClient){this.client=void 0;this.client=customHttpClient||httpClient;// Use custom client or default\n}// Determine which authentication method to use based on current user context\ngetCurrentUserAuth(preferredUserType){const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const adminUser=localStorage.getItem(ADMIN_USER_DATA_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);const studentUser=localStorage.getItem(STUDENT_USER_DATA_KEY);// If a preferred user type is specified, use that context first\nif(preferredUserType==='admin'&&adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 CommentService - Using admin authentication (preferred)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}if(preferredUserType==='student'&&studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 CommentService - Using student authentication (preferred)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// If no preference specified, determine based on current page context\nconst currentPath=window.location.pathname;const isAdminPage=currentPath.includes('/admin');const isStudentPage=currentPath.includes('/student');if(isAdminPage&&adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 CommentService - Using admin authentication (admin page context)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}if(isStudentPage&&studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 CommentService - Using student authentication (student page context)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// Fallback: Use student authentication if available (prioritize student over admin)\nif(studentToken&&studentUser){try{const userData=JSON.parse(studentUser);if(userData.role==='student'){console.log('🔑 CommentService - Using student authentication (fallback)');return{useStudentAuth:true,token:studentToken,userType:'student'};}}catch(e){console.warn('Failed to parse student user data');}}// Then try admin authentication\nif(adminToken&&adminUser){try{const userData=JSON.parse(adminUser);if(userData.role==='admin'){console.log('🔑 CommentService - Using admin authentication (fallback)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}}catch(e){console.warn('Failed to parse admin user data');}}// Last resort: check tokens without user data validation (prioritize student)\nif(studentToken){console.log('🔑 CommentService - Using student token (no user data)');return{useStudentAuth:true,token:studentToken,userType:'student'};}if(adminToken){console.log('🔑 CommentService - Using admin token (no user data)');return{useStudentAuth:false,token:adminToken,userType:'admin'};}// No authentication available\nconsole.log('🔑 CommentService - No authentication available');return{useStudentAuth:false,token:null,userType:null};}// Get comments for an announcement\nasync getComments(filters){const params=this.buildQueryParams(filters);return httpClient.get(API_ENDPOINTS.COMMENTS.BASE,params);}// Get comments by announcement ID\nasync getCommentsByAnnouncement(announcementId,options){const filters={announcement_id:announcementId,page:(options===null||options===void 0?void 0:options.page)||1,limit:(options===null||options===void 0?void 0:options.limit)||20,sort_by:(options===null||options===void 0?void 0:options.sort_by)||'created_at',sort_order:(options===null||options===void 0?void 0:options.sort_order)||'ASC'};return this.getComments(filters);}// Get comments by calendar event ID\nasync getCommentsByCalendar(calendarId,options){const params=new URLSearchParams({page:((options===null||options===void 0?void 0:options.page)||1).toString(),limit:((options===null||options===void 0?void 0:options.limit)||20).toString(),sort_by:(options===null||options===void 0?void 0:options.sort_by)||'created_at',sort_order:(options===null||options===void 0?void 0:options.sort_order)||'ASC'});return httpClient.get(\"/api/comments/calendar/\".concat(calendarId,\"?\").concat(params));}// Get single comment by ID\nasync getCommentById(id){return httpClient.get(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));}// Create new comment with optional user type preference\nasync createComment(data,preferredUserType){const{useStudentAuth,token,userType}=this.getCurrentUserAuth(preferredUserType);// Log authentication context for debugging\nconsole.log('CommentService.createComment - Auth context:',{preferredUserType,useStudentAuth,hasToken:!!token,userType,tokenPrefix:token?token.substring(0,10)+'...':null,isCalendarComment:!!data.calendar_id});// Use dedicated calendar comment endpoint if this is a calendar comment\nif(data.calendar_id){const endpoint=\"/api/comments/calendar/\".concat(data.calendar_id);const calendarCommentData={parent_comment_id:data.parent_comment_id,comment_text:data.comment_text,is_anonymous:data.is_anonymous||false};if(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(endpoint),{method:'POST',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)},body:JSON.stringify(calendarCommentData)});if(!response.ok){const errorData=await response.json();console.error('Calendar comment creation failed:',errorData);throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Calendar comment created successfully',data:result.data};}catch(error){throw new Error(error.message||'Failed to create calendar comment');}}// Fallback to default httpClient (admin auth) for calendar comments\nreturn httpClient.post(endpoint,calendarCommentData);}// Regular announcement comment logic\nif(useStudentAuth&&token){try{const response=await fetch(\"\".concat(API_BASE_URL).concat(API_ENDPOINTS.COMMENTS.BASE),{method:'POST',headers:{'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)},body:JSON.stringify(data)});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const result=await response.json();return{success:true,message:result.message||'Comment created successfully',data:result.data};}catch(error){throw new Error(error.message||'Failed to create comment');}}// Use httpClient for admin authentication or general fallback\nreturn httpClient.post(API_ENDPOINTS.COMMENTS.BASE,data);}// Update comment\nasync updateComment(id,data){return httpClient.put(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()),data);}// Delete comment\nasync deleteComment(id){return httpClient.delete(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));}// Add reaction to comment\nasync addReaction(id,reactionId){console.log('❤️ CommentService - Adding reaction:',{commentId:id,reactionId,clientType:this.client===adminHttpClient?'ADMIN':this.client===studentHttpClient?'STUDENT':'DEFAULT'});return this.client.post(API_ENDPOINTS.COMMENTS.LIKE(id.toString()),{reaction_id:reactionId});}// Remove reaction from comment\nasync removeReaction(id){console.log('💔 CommentService - Removing reaction:',{commentId:id,clientType:this.client===adminHttpClient?'ADMIN':this.client===studentHttpClient?'STUDENT':'DEFAULT'});return this.client.delete(API_ENDPOINTS.COMMENTS.LIKE(id.toString()));}// Flag comment\nasync flagComment(id,reason){return httpClient.post(API_ENDPOINTS.COMMENTS.FLAG(id.toString()),{reason});}// Get flagged comments (admin only)\nasync getFlaggedComments(filters){const params=filters?this.buildQueryParams(filters):undefined;return httpClient.get(API_ENDPOINTS.COMMENTS.FLAGGED,params);}// Approve flagged comment (admin only)\nasync approveComment(id){return httpClient.post(API_ENDPOINTS.COMMENTS.APPROVE(id.toString()));}// Reject flagged comment (admin only)\nasync rejectComment(id){return httpClient.post(API_ENDPOINTS.COMMENTS.REJECT(id.toString()));}// Get comment reaction statistics\nasync getReactionStats(id){const endpoint=id?API_ENDPOINTS.COMMENTS.REACTIONS(id.toString()):API_ENDPOINTS.COMMENTS.BASE+'/reactions';return httpClient.get(endpoint);}// Helper method to build query parameters\nbuildQueryParams(filters){const params={};Object.entries(filters).forEach(_ref=>{let[key,value]=_ref;if(value!==undefined&&value!==null&&value!==''){params[key]=value.toString();}});return params;}// Create reply to a comment\nasync createReply(parentCommentId,data,preferredUserType){return this.createComment(_objectSpread(_objectSpread({},data),{},{parent_comment_id:parentCommentId}),preferredUserType);}// Get replies for a comment\nasync getReplies(parentCommentId){var _response$data;// This would typically be included in the parent comment response\n// but can be implemented as a separate endpoint if needed\nconst response=await this.getCommentById(parentCommentId);return((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.comment.replies)||[];}// Get comment count for an announcement\nasync getCommentCount(announcementId){var _response$data2;const response=await this.getCommentsByAnnouncement(announcementId,{limit:1});return((_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.pagination.total)||0;}// Get recent comments for admin dashboard\nasync getRecentComments(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;return this.getComments({page:1,limit,sort_by:'created_at',sort_order:'DESC'});}// Search comments\nasync searchComments(query,announcementId){const filters={page:1,limit:20,sort_by:'created_at',sort_order:'DESC'};if(announcementId){filters.announcement_id=announcementId;}// Note: Search functionality would need to be implemented in the backend\n// For now, this returns all comments with the filters\nreturn this.getComments(filters);}// Get user's comments\nasync getUserComments(userId,userType){// This would require additional backend endpoint or filtering\n// For now, return all comments (would need backend implementation)\nreturn this.getComments({page:1,limit:20,sort_by:'created_at',sort_order:'DESC'});}// Bulk operations for admin\nasync bulkApproveComments(commentIds){// This would require a bulk endpoint in the backend\n// For now, approve each comment individually\nconst promises=commentIds.map(id=>this.approveComment(id));await Promise.all(promises);return{success:true,message:'Comments approved successfully',data:undefined};}async bulkRejectComments(commentIds){// This would require a bulk endpoint in the backend\n// For now, reject each comment individually\nconst promises=commentIds.map(id=>this.rejectComment(id));await Promise.all(promises);return{success:true,message:'Comments rejected successfully',data:undefined};}// Get comment statistics\nasync getCommentStatistics(){// This would require a statistics endpoint in the backend\n// For now, return mock data\nreturn{total:0,flagged:0,today:0,thisWeek:0};}}// Role-specific comment service classes\nclass AdminCommentService extends CommentService{constructor(){super(adminHttpClient);}async createComment(data){console.log('🔧 AdminCommentService - Creating comment as admin');return super.createComment(data,'admin');}async getCommentsByAnnouncement(announcementId,options){console.log('🔧 AdminCommentService - Getting comments as admin');return super.getCommentsByAnnouncement(announcementId,options);}async getCommentsByCalendar(calendarId,options){console.log('🔧 AdminCommentService - Getting calendar comments as admin');return super.getCommentsByCalendar(calendarId,options);}async addReaction(id,reactionId){console.log('🔧 AdminCommentService - Adding reaction as admin');return super.addReaction(id,reactionId);}async removeReaction(id){console.log('🔧 AdminCommentService - Removing reaction as admin');return super.removeReaction(id);}async createReply(parentCommentId,data){console.log('🔧 AdminCommentService - Creating reply as admin');return super.createReply(parentCommentId,data,'admin');}}class StudentCommentService extends CommentService{constructor(){super(studentHttpClient);}async createComment(data){console.log('🔧 StudentCommentService - Creating comment as student');return super.createComment(data,'student');}async getCommentsByAnnouncement(announcementId,options){console.log('🔧 StudentCommentService - Getting comments as student');return super.getCommentsByAnnouncement(announcementId,options);}async getCommentsByCalendar(calendarId,options){console.log('🔧 StudentCommentService - Getting calendar comments as student');return super.getCommentsByCalendar(calendarId,options);}async addReaction(id,reactionId){console.log('🔧 StudentCommentService - Adding reaction as student');return super.addReaction(id,reactionId);}async removeReaction(id){console.log('🔧 StudentCommentService - Removing reaction as student');return super.removeReaction(id);}async createReply(parentCommentId,data){console.log('🔧 StudentCommentService - Creating reply as student');return super.createReply(parentCommentId,data,'student');}}// Export service instances\nexport const commentService=new CommentService();// Default/legacy service\n// Role-specific comment services with proper token management and user type enforcement\nexport const adminCommentServiceWithToken=new AdminCommentService();export const studentCommentServiceWithToken=new StudentCommentService();export default commentService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}