{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 17v1c0 .5-.5 1-1 1H3c-.5 0-1-.5-1-1v-1\",\n  key: \"lt2kga\"\n}]];\nconst Space = createLucideIcon(\"space\", __iconNode);\nexport { __iconNode, Space as default };\n//# sourceMappingURL=space.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}