{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 21c0-2.5 2-2.5 2-5\",\n  key: \"1sicvv\"\n}], [\"path\", {\n  d: \"M16 21c0-2.5 2-2.5 2-5\",\n  key: \"1o3eny\"\n}], [\"path\", {\n  d: \"m19 8-.8 3a1.25 1.25 0 0 1-1.2 1H7a1.25 1.25 0 0 1-1.2-1L5 8\",\n  key: \"1bvca4\"\n}], [\"path\", {\n  d: \"M21 3a1 1 0 0 1 1 1v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a1 1 0 0 1 1-1z\",\n  key: \"x3qr1j\"\n}], [\"path\", {\n  d: \"M6 21c0-2.5 2-2.5 2-5\",\n  key: \"i3w1gp\"\n}]];\nconst AlarmSmoke = createLucideIcon(\"alarm-smoke\", __iconNode);\nexport { __iconNode, AlarmSmoke as default };\n//# sourceMappingURL=alarm-smoke.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}