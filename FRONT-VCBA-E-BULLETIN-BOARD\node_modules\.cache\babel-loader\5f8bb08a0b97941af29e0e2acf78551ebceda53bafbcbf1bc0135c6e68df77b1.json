{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3\",\n  key: \"irtipd\"\n}], [\"path\", {\n  d: \"M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V11a2 2 0 0 0-4 0z\",\n  key: \"1qyhux\"\n}], [\"path\", {\n  d: \"M5 18v2\",\n  key: \"ppbyun\"\n}], [\"path\", {\n  d: \"M19 18v2\",\n  key: \"gy7782\"\n}]];\nconst Armchair = createLucideIcon(\"armchair\", __iconNode);\nexport { __iconNode, Armchair as default };\n//# sourceMappingURL=armchair.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}