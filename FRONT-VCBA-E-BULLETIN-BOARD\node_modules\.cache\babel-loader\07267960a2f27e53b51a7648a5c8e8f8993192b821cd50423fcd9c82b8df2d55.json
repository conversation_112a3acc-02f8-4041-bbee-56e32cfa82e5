{"ast": null, "code": "import{API_ENDPOINTS}from'../config/constants';import{httpClient}from'./api.service';class StudentService{async createStudent(studentData){try{var _responseData$data;const response=await httpClient.post(API_ENDPOINTS.ADMIN.STUDENTS,studentData);// Handle the actual response structure from backend\nconst responseData=response.data||response;if(responseData!==null&&responseData!==void 0&&responseData.student){return responseData.student;}else if(responseData!==null&&responseData!==void 0&&responseData.success&&responseData!==null&&responseData!==void 0&&(_responseData$data=responseData.data)!==null&&_responseData$data!==void 0&&_responseData$data.student){return responseData.data.student;}else{throw new Error('Invalid response from server');}}catch(error){console.error('Error creating student:',error);// If it's a validation error, provide more specific details\nif(error.details&&Array.isArray(error.details)){const validationErrors=error.details.map(detail=>\"\".concat(detail.field,\": \").concat(detail.message)).join(', ');throw new Error(\"Validation failed: \".concat(validationErrors));}throw new Error(error.message||'Failed to create student account');}}async getStudents(params){try{// Filter out undefined values to avoid sending \"undefined\" as string\nconst cleanParams={};if(params){if(params.page!==undefined)cleanParams.page=params.page;if(params.limit!==undefined)cleanParams.limit=params.limit;if(params.search!==undefined&&params.search!=='')cleanParams.search=params.search;if(params.grade_level!==undefined)cleanParams.grade_level=params.grade_level;if(params.section!==undefined&&params.section!=='')cleanParams.section=params.section;if(params.is_active!==undefined)cleanParams.is_active=params.is_active.toString();}const response=await httpClient.get(API_ENDPOINTS.ADMIN.STUDENTS,cleanParams);// Handle the actual response structure from backend\nconst responseData=response.data||response;if(responseData!==null&&responseData!==void 0&&responseData.success&&responseData!==null&&responseData!==void 0&&responseData.students&&responseData!==null&&responseData!==void 0&&responseData.pagination){return{students:responseData.students,pagination:responseData.pagination};}else{throw new Error('Invalid response from server');}}catch(error){console.error('Error fetching students:',error);throw new Error(error.message||'Failed to fetch students');}}async getStudent(studentId){try{var _response$data,_response$data$data;const response=await httpClient.get(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));if(!((_response$data=response.data)!==null&&_response$data!==void 0&&(_response$data$data=_response$data.data)!==null&&_response$data$data!==void 0&&_response$data$data.student)){throw new Error('Invalid response from server');}return response.data.data.student;}catch(error){console.error('Error fetching student:',error);throw new Error(error.message||'Failed to fetch student');}}async updateStudent(studentId,studentData){try{var _responseData$data2;const response=await httpClient.put(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId),studentData);// Handle the actual response structure from backend\nconst responseData=response.data||response;if(responseData!==null&&responseData!==void 0&&responseData.student){return responseData.student;}else if(responseData!==null&&responseData!==void 0&&responseData.success&&responseData!==null&&responseData!==void 0&&(_responseData$data2=responseData.data)!==null&&_responseData$data2!==void 0&&_responseData$data2.student){return responseData.data.student;}else{throw new Error('Invalid response from server');}}catch(error){console.error('Error updating student:',error);throw new Error(error.message||'Failed to update student');}}async deleteStudent(studentId){try{await httpClient.delete(API_ENDPOINTS.ADMIN.STUDENT_BY_ID(studentId));}catch(error){console.error('Error deleting student:',error);throw new Error(error.message||'Failed to delete student');}}async resetStudentPassword(studentId){let newPassword=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'Student123';try{await httpClient.post(API_ENDPOINTS.ADMIN.RESET_STUDENT_PASSWORD(studentId),{newPassword:newPassword});}catch(error){console.error('Error resetting student password:',error);throw new Error(error.message||'Failed to reset student password');}}}export const studentService=new StudentService();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}