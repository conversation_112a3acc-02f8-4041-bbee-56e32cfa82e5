{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 10a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v10a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2z\",\n  key: \"1ol0lm\"\n}], [\"path\", {\n  d: \"M8 10h8\",\n  key: \"c7uz4u\"\n}], [\"path\", {\n  d: \"M8 18h8\",\n  key: \"1no2b1\"\n}], [\"path\", {\n  d: \"M8 22v-6a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v6\",\n  key: \"1fr6do\"\n}], [\"path\", {\n  d: \"M9 6V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2\",\n  key: \"donm21\"\n}]];\nconst Backpack = createLucideIcon(\"backpack\", __iconNode);\nexport { __iconNode, Backpack as default };\n//# sourceMappingURL=backpack.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}