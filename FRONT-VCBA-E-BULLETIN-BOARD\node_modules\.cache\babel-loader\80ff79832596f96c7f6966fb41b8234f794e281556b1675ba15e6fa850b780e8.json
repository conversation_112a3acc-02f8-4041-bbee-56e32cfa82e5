{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"19\",\n  x2: \"5\",\n  y1: \"5\",\n  y2: \"19\",\n  key: \"1x9vlm\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"6.5\",\n  r: \"2.5\",\n  key: \"4mh3h7\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"1mdrzq\"\n}]];\nconst Percent = createLucideIcon(\"percent\", __iconNode);\nexport { __iconNode, Percent as default };\n//# sourceMappingURL=percent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}