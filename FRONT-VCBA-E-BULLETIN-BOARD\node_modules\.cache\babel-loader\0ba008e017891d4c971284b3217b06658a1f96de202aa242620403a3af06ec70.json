{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12h11\",\n  key: \"6m4ad9\"\n}], [\"path\", {\n  d: \"M10 18h11\",\n  key: \"11hvi2\"\n}], [\"path\", {\n  d: \"M10 6h11\",\n  key: \"c7qv1k\"\n}], [\"path\", {\n  d: \"M4 10h2\",\n  key: \"16xx2s\"\n}], [\"path\", {\n  d: \"M4 6h1v4\",\n  key: \"cnovpq\"\n}], [\"path\", {\n  d: \"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1\",\n  key: \"m9a95d\"\n}]];\nconst ListOrdered = createLucideIcon(\"list-ordered\", __iconNode);\nexport { __iconNode, ListOrdered as default };\n//# sourceMappingURL=list-ordered.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}