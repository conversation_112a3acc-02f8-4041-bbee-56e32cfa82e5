{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 2v14a2 2 0 0 0 2 2h14\",\n  key: \"ron5a4\"\n}], [\"path\", {\n  d: \"M18 22V8a2 2 0 0 0-2-2H2\",\n  key: \"7s9ehn\"\n}]];\nconst Crop = createLucideIcon(\"crop\", __iconNode);\nexport { __iconNode, Crop as default };\n//# sourceMappingURL=crop.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}