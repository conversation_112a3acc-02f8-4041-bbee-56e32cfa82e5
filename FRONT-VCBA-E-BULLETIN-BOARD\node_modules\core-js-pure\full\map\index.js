'use strict';
var parent = require('../../actual/map');
require('../../modules/esnext.map.from');
require('../../modules/esnext.map.of');
require('../../modules/esnext.map.key-by');
require('../../modules/esnext.map.delete-all');
require('../../modules/esnext.map.emplace');
require('../../modules/esnext.map.every');
require('../../modules/esnext.map.filter');
require('../../modules/esnext.map.find');
require('../../modules/esnext.map.find-key');
require('../../modules/esnext.map.includes');
require('../../modules/esnext.map.get-or-insert');
require('../../modules/esnext.map.get-or-insert-computed');
require('../../modules/esnext.map.key-of');
require('../../modules/esnext.map.map-keys');
require('../../modules/esnext.map.map-values');
require('../../modules/esnext.map.merge');
require('../../modules/esnext.map.reduce');
require('../../modules/esnext.map.some');
require('../../modules/esnext.map.update');
// TODO: remove from `core-js@4`
require('../../modules/esnext.map.upsert');
// TODO: remove from `core-js@4`
require('../../modules/esnext.map.update-or-insert');

module.exports = parent;
