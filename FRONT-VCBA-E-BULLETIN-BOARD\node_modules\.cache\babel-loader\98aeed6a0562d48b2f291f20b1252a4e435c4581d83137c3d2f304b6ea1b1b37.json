{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M7 16c.5-2 1.5-7 4-7 2 0 2 3 4 3 2.5 0 4.5-5 5-7\",\n  key: \"lw07rv\"\n}]];\nconst ChartSpline = createLucideIcon(\"chart-spline\", __iconNode);\nexport { __iconNode, ChartSpline as default };\n//# sourceMappingURL=chart-spline.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}