{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 9v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"m87ecr\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"22\",\n  y1: \"5\",\n  y2: \"5\",\n  key: \"ez7e4s\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n  key: \"1xmnt7\"\n}]];\nconst ImageMinus = createLucideIcon(\"image-minus\", __iconNode);\nexport { __iconNode, ImageMinus as default };\n//# sourceMappingURL=image-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}