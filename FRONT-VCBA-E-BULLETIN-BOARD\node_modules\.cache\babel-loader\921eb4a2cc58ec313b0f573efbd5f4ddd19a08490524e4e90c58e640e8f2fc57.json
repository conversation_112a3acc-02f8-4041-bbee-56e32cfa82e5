{"ast": null, "code": "/**\n * Form utility functions for consistent form handling across the application\n */\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images',\n    isUpdate = false,\n    originalData\n  } = options;\n  // Debug logging (can be removed in production)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🔧 createFormData called with isUpdate:', isUpdate);\n  }\n  const formDataToSubmit = new FormData();\n  let hasValidFields = false;\n\n  // Helper function to check if a value is empty\n  const isEmpty = value => {\n    return value === '' || value === null || value === undefined;\n  };\n\n  // Helper function to check if value has changed (for updates)\n  const hasChanged = (key, value) => {\n    if (!isUpdate || !originalData) return true;\n\n    // Convert both values to strings for comparison (since FormData converts everything to strings)\n    const originalValue = originalData[key];\n    const currentValue = value;\n\n    // Handle boolean values\n    if (typeof originalValue === 'boolean' || typeof currentValue === 'boolean') {\n      return Boolean(originalValue) !== Boolean(currentValue);\n    }\n\n    // Handle numeric values\n    if (key === 'category_id' || key === 'subcategory_id') {\n      const origNum = originalValue ? parseInt(originalValue.toString()) : null;\n      const currNum = currentValue ? parseInt(currentValue.toString()) : null;\n      return origNum !== currNum;\n    }\n\n    // Handle string values\n    return String(originalValue || '') !== String(currentValue || '');\n  };\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip empty values for updates to avoid \"No valid fields to update\" error\n    if (isUpdate && isEmpty(value)) {\n      return;\n    }\n\n    // For updates, only include changed fields\n    if (isUpdate && !hasChanged(key, value)) {\n      return;\n    }\n    if (key === 'category_id' && typeof value === 'string') {\n      const parsedValue = parseInt(value);\n      if (!isNaN(parsedValue) && parsedValue > 0) {\n        formDataToSubmit.append(key, parsedValue.toString());\n        hasValidFields = true;\n      } else if (!isUpdate) {\n        // For creation, category_id is required\n        throw new Error('Category is required');\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and is a valid number\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue) && parsedValue > 0) {\n          formDataToSubmit.append(key, parsedValue.toString());\n          hasValidFields = true;\n        }\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n      hasValidFields = true;\n    } else if (!isEmpty(value)) {\n      formDataToSubmit.append(key, value.toString());\n      hasValidFields = true;\n    }\n  });\n\n  // Check if we have any valid fields for updates\n  if (isUpdate && !hasValidFields) {\n    console.log('🔧 No valid fields to update - throwing error');\n    throw new Error('No changes detected. Please modify at least one field before saving.');\n  }\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  // Debug: Log final FormData entries\n  console.log('🔧 Final FormData entries:');\n  const entries = [];\n  formDataToSubmit.forEach((value, key) => {\n    entries.push(`  ${key}: ${value}`);\n  });\n  console.log(entries.join('\\n'));\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "isUpdate", "originalData", "process", "env", "NODE_ENV", "console", "log", "formDataToSubmit", "FormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEmpty", "value", "undefined", "has<PERSON><PERSON>ed", "key", "originalValue", "currentValue", "Boolean", "origNum", "parseInt", "toString", "currNum", "String", "Object", "entries", "for<PERSON>ach", "parsedValue", "isNaN", "append", "Error", "trim", "status", "length", "file", "push", "join", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\n * Form utility functions for consistent form handling across the application\n */\n\nexport interface FormField {\n  [key: string]: string | number | boolean | null | undefined;\n}\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (\n  formData: FormField,\n  files: File[] = [],\n  options: {\n    skipScheduledDate?: boolean;\n    fileFieldName?: string;\n    isUpdate?: boolean;\n    originalData?: FormField;\n  } = {}\n): FormData => {\n  const { skipScheduledDate = true, fileFieldName = 'images', isUpdate = false, originalData } = options;\n  // Debug logging (can be removed in production)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🔧 createFormData called with isUpdate:', isUpdate);\n  }\n\n  const formDataToSubmit = new FormData();\n  let hasValidFields = false;\n\n  // Helper function to check if a value is empty\n  const isEmpty = (value: any): boolean => {\n    return value === '' || value === null || value === undefined;\n  };\n\n  // Helper function to check if value has changed (for updates)\n  const hasChanged = (key: string, value: any): boolean => {\n    if (!isUpdate || !originalData) return true;\n\n    // Convert both values to strings for comparison (since FormData converts everything to strings)\n    const originalValue = originalData[key];\n    const currentValue = value;\n\n    // Handle boolean values\n    if (typeof originalValue === 'boolean' || typeof currentValue === 'boolean') {\n      return Boolean(originalValue) !== Boolean(currentValue);\n    }\n\n    // Handle numeric values\n    if (key === 'category_id' || key === 'subcategory_id') {\n      const origNum = originalValue ? parseInt(originalValue.toString()) : null;\n      const currNum = currentValue ? parseInt(currentValue.toString()) : null;\n      return origNum !== currNum;\n    }\n\n    // Handle string values\n    return String(originalValue || '') !== String(currentValue || '');\n  };\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip empty values for updates to avoid \"No valid fields to update\" error\n    if (isUpdate && isEmpty(value)) {\n      return;\n    }\n\n    // For updates, only include changed fields\n    if (isUpdate && !hasChanged(key, value)) {\n      return;\n    }\n\n    if (key === 'category_id' && typeof value === 'string') {\n      const parsedValue = parseInt(value);\n      if (!isNaN(parsedValue) && parsedValue > 0) {\n        formDataToSubmit.append(key, parsedValue.toString());\n        hasValidFields = true;\n      } else if (!isUpdate) {\n        // For creation, category_id is required\n        throw new Error('Category is required');\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and is a valid number\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue) && parsedValue > 0) {\n          formDataToSubmit.append(key, parsedValue.toString());\n          hasValidFields = true;\n        }\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n      hasValidFields = true;\n    } else if (!isEmpty(value)) {\n      formDataToSubmit.append(key, value.toString());\n      hasValidFields = true;\n    }\n  });\n\n  // Check if we have any valid fields for updates\n  if (isUpdate && !hasValidFields) {\n    console.log('🔧 No valid fields to update - throwing error');\n    throw new Error('No changes detected. Please modify at least one field before saving.');\n  }\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach((file) => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  // Debug: Log final FormData entries\n  console.log('🔧 Final FormData entries:');\n  const entries: string[] = [];\n  formDataToSubmit.forEach((value, key) => {\n    entries.push(`  ${key}: ${value}`);\n  });\n  console.log(entries.join('\\n'));\n\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (\n  formData: FormField,\n  rules: {\n    required?: string[];\n    maxLength?: { [key: string]: number };\n    custom?: { [key: string]: (value: any) => string | null };\n  } = {}\n): Record<string, string> => {\n  const errors: Record<string, string> = {};\n  const { required = [], maxLength = {}, custom = {} } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || (typeof value === 'string' && !value.trim())) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: { title: 255 },\n  custom: {\n    scheduled_publish_at: (value: any, formData?: FormField) => {\n      if (formData?.status === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (\n  file: File,\n  options: {\n    maxSize?: number;\n    allowedTypes?: string[];\n  } = {}\n): string | null => {\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\n\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n\n  return null;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAKC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG,QAAQ;IAAEC,QAAQ,GAAG,KAAK;IAAEC;EAAa,CAAC,GAAGJ,OAAO;EACtG;EACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEN,QAAQ,CAAC;EAClE;EAEA,MAAMO,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;EACvC,IAAIC,cAAc,GAAG,KAAK;;EAE1B;EACA,MAAMC,OAAO,GAAIC,KAAU,IAAc;IACvC,OAAOA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;EAC9D,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAACC,GAAW,EAAEH,KAAU,KAAc;IACvD,IAAI,CAACX,QAAQ,IAAI,CAACC,YAAY,EAAE,OAAO,IAAI;;IAE3C;IACA,MAAMc,aAAa,GAAGd,YAAY,CAACa,GAAG,CAAC;IACvC,MAAME,YAAY,GAAGL,KAAK;;IAE1B;IACA,IAAI,OAAOI,aAAa,KAAK,SAAS,IAAI,OAAOC,YAAY,KAAK,SAAS,EAAE;MAC3E,OAAOC,OAAO,CAACF,aAAa,CAAC,KAAKE,OAAO,CAACD,YAAY,CAAC;IACzD;;IAEA;IACA,IAAIF,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,gBAAgB,EAAE;MACrD,MAAMI,OAAO,GAAGH,aAAa,GAAGI,QAAQ,CAACJ,aAAa,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;MACzE,MAAMC,OAAO,GAAGL,YAAY,GAAGG,QAAQ,CAACH,YAAY,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;MACvE,OAAOF,OAAO,KAAKG,OAAO;IAC5B;;IAEA;IACA,OAAOC,MAAM,CAACP,aAAa,IAAI,EAAE,CAAC,KAAKO,MAAM,CAACN,YAAY,IAAI,EAAE,CAAC;EACnE,CAAC;;EAED;EACAO,MAAM,CAACC,OAAO,CAAC7B,QAAQ,CAAC,CAAC8B,OAAO,CAAC,CAAC,CAACX,GAAG,EAAEH,KAAK,CAAC,KAAK;IACjD;IACA,IAAIX,QAAQ,IAAIU,OAAO,CAACC,KAAK,CAAC,EAAE;MAC9B;IACF;;IAEA;IACA,IAAIX,QAAQ,IAAI,CAACa,UAAU,CAACC,GAAG,EAAEH,KAAK,CAAC,EAAE;MACvC;IACF;IAEA,IAAIG,GAAG,KAAK,aAAa,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MACtD,MAAMe,WAAW,GAAGP,QAAQ,CAACR,KAAK,CAAC;MACnC,IAAI,CAACgB,KAAK,CAACD,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;QAC1CnB,gBAAgB,CAACqB,MAAM,CAACd,GAAG,EAAEY,WAAW,CAACN,QAAQ,CAAC,CAAC,CAAC;QACpDX,cAAc,GAAG,IAAI;MACvB,CAAC,MAAM,IAAI,CAACT,QAAQ,EAAE;QACpB;QACA,MAAM,IAAI6B,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,MAAM,IAAIf,GAAG,KAAK,gBAAgB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACmB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMJ,WAAW,GAAGP,QAAQ,CAACR,KAAK,CAAC;QACnC,IAAI,CAACgB,KAAK,CAACD,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;UAC1CnB,gBAAgB,CAACqB,MAAM,CAACd,GAAG,EAAEY,WAAW,CAACN,QAAQ,CAAC,CAAC,CAAC;UACpDX,cAAc,GAAG,IAAI;QACvB;MACF;IACF,CAAC,MAAM,IAAIK,GAAG,KAAK,sBAAsB,IAAIhB,iBAAiB,IAAIH,QAAQ,CAACoC,MAAM,KAAK,WAAW,EAAE;MACjG;MACA;IACF,CAAC,MAAM,IAAI,OAAOpB,KAAK,KAAK,SAAS,EAAE;MACrCJ,gBAAgB,CAACqB,MAAM,CAACd,GAAG,EAAEH,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;MAC/CF,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM,IAAI,CAACC,OAAO,CAACC,KAAK,CAAC,EAAE;MAC1BJ,gBAAgB,CAACqB,MAAM,CAACd,GAAG,EAAEH,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;MAC9CX,cAAc,GAAG,IAAI;IACvB;EACF,CAAC,CAAC;;EAEF;EACA,IAAIT,QAAQ,IAAI,CAACS,cAAc,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,MAAM,IAAIuB,KAAK,CAAC,sEAAsE,CAAC;EACzF;;EAEA;EACA,IAAIjC,KAAK,CAACoC,MAAM,GAAG,CAAC,EAAE;IACpBpC,KAAK,CAAC6B,OAAO,CAAEQ,IAAI,IAAK;MACtB1B,gBAAgB,CAACqB,MAAM,CAAC7B,aAAa,EAAEkC,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACA5B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;EACzC,MAAMkB,OAAiB,GAAG,EAAE;EAC5BjB,gBAAgB,CAACkB,OAAO,CAAC,CAACd,KAAK,EAAEG,GAAG,KAAK;IACvCU,OAAO,CAACU,IAAI,CAAC,KAAKpB,GAAG,KAAKH,KAAK,EAAE,CAAC;EACpC,CAAC,CAAC;EACFN,OAAO,CAACC,GAAG,CAACkB,OAAO,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;EAE/B,OAAO5B,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,kBAAkB,GAAGA,CAChCzC,QAAmB,EACnB0C,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACd,OAAO,CAACiB,KAAK,IAAI;IACxB,MAAM/B,KAAK,GAAGhB,QAAQ,CAAC+C,KAAK,CAAC;IAC7B,IAAI,CAAC/B,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACmB,IAAI,CAAC,CAAE,EAAE;MAC1DQ,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACApB,MAAM,CAACC,OAAO,CAACgB,SAAS,CAAC,CAACf,OAAO,CAAC,CAAC,CAACiB,KAAK,EAAEE,GAAG,CAAC,KAAK;IAClD,MAAMjC,KAAK,GAAGhB,QAAQ,CAAC+C,KAAK,CAAC;IAC7B,IAAI,OAAO/B,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACqB,MAAM,GAAGY,GAAG,EAAE;MACnDN,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACArB,MAAM,CAACC,OAAO,CAACiB,MAAM,CAAC,CAAChB,OAAO,CAAC,CAAC,CAACiB,KAAK,EAAEG,SAAS,CAAC,KAAK;IACrD,MAAMlC,KAAK,GAAGhB,QAAQ,CAAC+C,KAAK,CAAC;IAC7B,MAAMI,KAAK,GAAGD,SAAS,CAAClC,KAAK,CAAC;IAC9B,IAAImC,KAAK,EAAE;MACTR,MAAM,CAACI,KAAK,CAAC,GAAGI,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,2BAA2B,GAAG;EACzCR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEQ,KAAK,EAAE;EAAI,CAAC;EACzBP,MAAM,EAAE;IACNQ,oBAAoB,EAAEA,CAACtC,KAAU,EAAEhB,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoC,MAAM,MAAK,WAAW,IAAI,CAACpB,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuC,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACnD,GAAG,CAAC6C,KAAK,CAAC,GAAGM,IAAI,CAACnD,GAAG,CAAC+C,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGM,IAAI,CAACG,GAAG,CAACP,CAAC,EAAEG,CAAC,CAAC,EAAEK,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAGA,CAC1B7B,IAAU,EACVpC,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEkE,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGnE,OAAO;EAEpH,IAAI,CAACmE,YAAY,CAACC,QAAQ,CAAChC,IAAI,CAACiC,IAAI,CAAC,EAAE;IACrC,OAAO,aAAajC,IAAI,CAACiC,IAAI,qCAAqCF,YAAY,CAAC7B,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIF,IAAI,CAACkC,IAAI,GAAGJ,OAAO,EAAE;IACvB,OAAO,aAAab,cAAc,CAACjB,IAAI,CAACkC,IAAI,CAAC,oCAAoCjB,cAAc,CAACa,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}