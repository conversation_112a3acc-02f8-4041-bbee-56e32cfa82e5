{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1\",\n  key: \"ns4c3b\"\n}], [\"path\", {\n  d: \"m12 15 5 6H7Z\",\n  key: \"14qnn2\"\n}]];\nconst Airplay = createLucideIcon(\"airplay\", __iconNode);\nexport { __iconNode, Airplay as default };\n//# sourceMappingURL=airplay.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}