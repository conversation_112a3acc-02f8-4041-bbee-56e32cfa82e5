{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\CalendarEventLikeButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Heart } from 'lucide-react';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CalendarEventLikeButton = ({\n  eventId,\n  initialLiked,\n  initialCount,\n  userRole,\n  onLikeChange,\n  size = 'medium',\n  showCount = true\n}) => {\n  _s();\n  const [liked, setLiked] = useState(initialLiked);\n  const [count, setCount] = useState(initialCount);\n  const [loading, setLoading] = useState(false);\n  const sizeConfig = {\n    small: {\n      iconSize: 14,\n      fontSize: '0.75rem',\n      padding: '0.25rem 0.5rem'\n    },\n    medium: {\n      iconSize: 16,\n      fontSize: '0.875rem',\n      padding: '0.5rem 0.75rem'\n    },\n    large: {\n      iconSize: 18,\n      fontSize: '1rem',\n      padding: '0.75rem 1rem'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId, currentlyLiked) => {\n    if (userRole) {\n      // Use role-specific client if role is provided\n      const client = userRole === 'admin' ? adminHttpClient : studentHttpClient;\n      const endpoint = `/api/calendar/${eventId}/like`;\n      console.log(`[DEBUG] CalendarEventLikeButton ${userRole} making direct API call to:`, endpoint);\n      if (currentlyLiked) {\n        return await client.delete(endpoint);\n      } else {\n        return await client.post(endpoint, {});\n      }\n    } else {\n      // Fallback to original service if no role provided (backward compatibility)\n      return await calendarReactionService.toggleLike(eventId, currentlyLiked);\n    }\n  };\n  const handleToggleLike = async () => {\n    if (loading) return;\n    setLoading(true);\n    try {\n      const response = await toggleCalendarReaction(eventId, liked);\n      if (response.success) {\n        const newLiked = !liked;\n        const newCount = newLiked ? count + 1 : count - 1;\n        setLiked(newLiked);\n        setCount(Math.max(0, newCount)); // Ensure count doesn't go below 0\n\n        // Notify parent component\n        if (onLikeChange) {\n          onLikeChange(newLiked, newCount);\n        }\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      // Could add toast notification here\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: handleToggleLike,\n    disabled: loading,\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.25rem',\n      padding: config.padding,\n      backgroundColor: liked ? '#fef2f2' : 'transparent',\n      border: liked ? '1px solid #fecaca' : '1px solid #e5e7eb',\n      borderRadius: '0.375rem',\n      color: liked ? '#dc2626' : '#6b7280',\n      fontSize: config.fontSize,\n      fontWeight: '500',\n      cursor: loading ? 'not-allowed' : 'pointer',\n      transition: 'all 0.2s ease-in-out',\n      opacity: loading ? 0.6 : 1\n    },\n    onMouseEnter: e => {\n      if (!loading) {\n        e.currentTarget.style.backgroundColor = liked ? '#fee2e2' : '#f9fafb';\n        e.currentTarget.style.borderColor = liked ? '#fca5a5' : '#d1d5db';\n      }\n    },\n    onMouseLeave: e => {\n      if (!loading) {\n        e.currentTarget.style.backgroundColor = liked ? '#fef2f2' : 'transparent';\n        e.currentTarget.style.borderColor = liked ? '#fecaca' : '#e5e7eb';\n      }\n    },\n    title: liked ? 'Unlike this event' : 'Like this event',\n    children: [/*#__PURE__*/_jsxDEV(Heart, {\n      size: config.iconSize,\n      fill: liked ? 'currentColor' : 'none',\n      style: {\n        transition: 'all 0.2s ease-in-out',\n        transform: loading ? 'scale(0.9)' : 'scale(1)'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), showCount && /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        minWidth: '1rem',\n        textAlign: 'left'\n      },\n      children: count\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendarEventLikeButton, \"pL7x6X2rLHJ0yfDY9jBYlTRC2w0=\");\n_c = CalendarEventLikeButton;\nexport default CalendarEventLikeButton;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventLikeButton\");", "map": {"version": 3, "names": ["React", "useState", "Heart", "calendarReactionService", "adminHttpClient", "studentHttpClient", "jsxDEV", "_jsxDEV", "CalendarEventLikeButton", "eventId", "initialLiked", "initialCount", "userRole", "onLikeChange", "size", "showCount", "_s", "liked", "setLiked", "count", "setCount", "loading", "setLoading", "sizeConfig", "small", "iconSize", "fontSize", "padding", "medium", "large", "config", "toggleCalendarReaction", "currentlyLiked", "client", "endpoint", "console", "log", "delete", "post", "toggleLike", "handleToggleLike", "response", "success", "newLiked", "newCount", "Math", "max", "error", "onClick", "disabled", "style", "display", "alignItems", "gap", "backgroundColor", "border", "borderRadius", "color", "fontWeight", "cursor", "transition", "opacity", "onMouseEnter", "e", "currentTarget", "borderColor", "onMouseLeave", "title", "children", "fill", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/CalendarEventLikeButton.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Heart } from 'lucide-react';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\n\ninterface CalendarEventLikeButtonProps {\n  eventId: number;\n  initialLiked: boolean;\n  initialCount: number;\n  userRole?: 'admin' | 'student'; // Add role prop for explicit role handling\n  onLikeChange?: (liked: boolean, newCount: number) => void;\n  size?: 'small' | 'medium' | 'large';\n  showCount?: boolean;\n}\n\nconst CalendarEventLikeButton: React.FC<CalendarEventLikeButtonProps> = ({\n  eventId,\n  initialLiked,\n  initialCount,\n  userRole,\n  onLikeChange,\n  size = 'medium',\n  showCount = true\n}) => {\n  const [liked, setLiked] = useState(initialLiked);\n  const [count, setCount] = useState(initialCount);\n  const [loading, setLoading] = useState(false);\n\n  const sizeConfig = {\n    small: { iconSize: 14, fontSize: '0.75rem', padding: '0.25rem 0.5rem' },\n    medium: { iconSize: 16, fontSize: '0.875rem', padding: '0.5rem 0.75rem' },\n    large: { iconSize: 18, fontSize: '1rem', padding: '0.75rem 1rem' }\n  };\n\n  const config = sizeConfig[size];\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId: number, currentlyLiked: boolean) => {\n    if (userRole) {\n      // Use role-specific client if role is provided\n      const client = userRole === 'admin' ? adminHttpClient : studentHttpClient;\n      const endpoint = `/api/calendar/${eventId}/like`;\n\n      console.log(`[DEBUG] CalendarEventLikeButton ${userRole} making direct API call to:`, endpoint);\n\n      if (currentlyLiked) {\n        return await client.delete(endpoint);\n      } else {\n        return await client.post(endpoint, {});\n      }\n    } else {\n      // Fallback to original service if no role provided (backward compatibility)\n      return await calendarReactionService.toggleLike(eventId, currentlyLiked);\n    }\n  };\n\n  const handleToggleLike = async () => {\n    if (loading) return;\n\n    setLoading(true);\n    try {\n      const response = await toggleCalendarReaction(eventId, liked);\n\n      if (response.success) {\n        const newLiked = !liked;\n        const newCount = newLiked ? count + 1 : count - 1;\n\n        setLiked(newLiked);\n        setCount(Math.max(0, newCount)); // Ensure count doesn't go below 0\n\n        // Notify parent component\n        if (onLikeChange) {\n          onLikeChange(newLiked, newCount);\n        }\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      // Could add toast notification here\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleToggleLike}\n      disabled={loading}\n      style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.25rem',\n        padding: config.padding,\n        backgroundColor: liked ? '#fef2f2' : 'transparent',\n        border: liked ? '1px solid #fecaca' : '1px solid #e5e7eb',\n        borderRadius: '0.375rem',\n        color: liked ? '#dc2626' : '#6b7280',\n        fontSize: config.fontSize,\n        fontWeight: '500',\n        cursor: loading ? 'not-allowed' : 'pointer',\n        transition: 'all 0.2s ease-in-out',\n        opacity: loading ? 0.6 : 1\n      }}\n      onMouseEnter={(e) => {\n        if (!loading) {\n          e.currentTarget.style.backgroundColor = liked ? '#fee2e2' : '#f9fafb';\n          e.currentTarget.style.borderColor = liked ? '#fca5a5' : '#d1d5db';\n        }\n      }}\n      onMouseLeave={(e) => {\n        if (!loading) {\n          e.currentTarget.style.backgroundColor = liked ? '#fef2f2' : 'transparent';\n          e.currentTarget.style.borderColor = liked ? '#fecaca' : '#e5e7eb';\n        }\n      }}\n      title={liked ? 'Unlike this event' : 'Like this event'}\n    >\n      <Heart\n        size={config.iconSize}\n        fill={liked ? 'currentColor' : 'none'}\n        style={{\n          transition: 'all 0.2s ease-in-out',\n          transform: loading ? 'scale(0.9)' : 'scale(1)'\n        }}\n      />\n      {showCount && (\n        <span style={{ minWidth: '1rem', textAlign: 'left' }}>\n          {count}\n        </span>\n      )}\n    </button>\n  );\n};\n\nexport default CalendarEventLikeButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYhF,MAAMC,uBAA+D,GAAGA,CAAC;EACvEC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,QAAQ;EACRC,YAAY;EACZC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAACS,YAAY,CAAC;EAChD,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAACU,YAAY,CAAC;EAChD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMsB,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAiB,CAAC;IACvEC,MAAM,EAAE;MAAEH,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,OAAO,EAAE;IAAiB,CAAC;IACzEE,KAAK,EAAE;MAAEJ,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAe;EACnE,CAAC;EAED,MAAMG,MAAM,GAAGP,UAAU,CAACT,IAAI,CAAC;;EAE/B;EACA,MAAMiB,sBAAsB,GAAG,MAAAA,CAAOtB,OAAe,EAAEuB,cAAuB,KAAK;IACjF,IAAIpB,QAAQ,EAAE;MACZ;MACA,MAAMqB,MAAM,GAAGrB,QAAQ,KAAK,OAAO,GAAGR,eAAe,GAAGC,iBAAiB;MACzE,MAAM6B,QAAQ,GAAG,iBAAiBzB,OAAO,OAAO;MAEhD0B,OAAO,CAACC,GAAG,CAAC,mCAAmCxB,QAAQ,6BAA6B,EAAEsB,QAAQ,CAAC;MAE/F,IAAIF,cAAc,EAAE;QAClB,OAAO,MAAMC,MAAM,CAACI,MAAM,CAACH,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,OAAO,MAAMD,MAAM,CAACK,IAAI,CAACJ,QAAQ,EAAE,CAAC,CAAC,CAAC;MACxC;IACF,CAAC,MAAM;MACL;MACA,OAAO,MAAM/B,uBAAuB,CAACoC,UAAU,CAAC9B,OAAO,EAAEuB,cAAc,CAAC;IAC1E;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAInB,OAAO,EAAE;IAEbC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMV,sBAAsB,CAACtB,OAAO,EAAEQ,KAAK,CAAC;MAE7D,IAAIwB,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMC,QAAQ,GAAG,CAAC1B,KAAK;QACvB,MAAM2B,QAAQ,GAAGD,QAAQ,GAAGxB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;QAEjDD,QAAQ,CAACyB,QAAQ,CAAC;QAClBvB,QAAQ,CAACyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAEjC;QACA,IAAI/B,YAAY,EAAE;UAChBA,YAAY,CAAC8B,QAAQ,EAAEC,QAAQ,CAAC;QAClC;MACF;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEf,OAAA;IACEyC,OAAO,EAAER,gBAAiB;IAC1BS,QAAQ,EAAE5B,OAAQ;IAClB6B,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,SAAS;MACd1B,OAAO,EAAEG,MAAM,CAACH,OAAO;MACvB2B,eAAe,EAAErC,KAAK,GAAG,SAAS,GAAG,aAAa;MAClDsC,MAAM,EAAEtC,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;MACzDuC,YAAY,EAAE,UAAU;MACxBC,KAAK,EAAExC,KAAK,GAAG,SAAS,GAAG,SAAS;MACpCS,QAAQ,EAAEI,MAAM,CAACJ,QAAQ;MACzBgC,UAAU,EAAE,KAAK;MACjBC,MAAM,EAAEtC,OAAO,GAAG,aAAa,GAAG,SAAS;MAC3CuC,UAAU,EAAE,sBAAsB;MAClCC,OAAO,EAAExC,OAAO,GAAG,GAAG,GAAG;IAC3B,CAAE;IACFyC,YAAY,EAAGC,CAAC,IAAK;MACnB,IAAI,CAAC1C,OAAO,EAAE;QACZ0C,CAAC,CAACC,aAAa,CAACd,KAAK,CAACI,eAAe,GAAGrC,KAAK,GAAG,SAAS,GAAG,SAAS;QACrE8C,CAAC,CAACC,aAAa,CAACd,KAAK,CAACe,WAAW,GAAGhD,KAAK,GAAG,SAAS,GAAG,SAAS;MACnE;IACF,CAAE;IACFiD,YAAY,EAAGH,CAAC,IAAK;MACnB,IAAI,CAAC1C,OAAO,EAAE;QACZ0C,CAAC,CAACC,aAAa,CAACd,KAAK,CAACI,eAAe,GAAGrC,KAAK,GAAG,SAAS,GAAG,aAAa;QACzE8C,CAAC,CAACC,aAAa,CAACd,KAAK,CAACe,WAAW,GAAGhD,KAAK,GAAG,SAAS,GAAG,SAAS;MACnE;IACF,CAAE;IACFkD,KAAK,EAAElD,KAAK,GAAG,mBAAmB,GAAG,iBAAkB;IAAAmD,QAAA,gBAEvD7D,OAAA,CAACL,KAAK;MACJY,IAAI,EAAEgB,MAAM,CAACL,QAAS;MACtB4C,IAAI,EAAEpD,KAAK,GAAG,cAAc,GAAG,MAAO;MACtCiC,KAAK,EAAE;QACLU,UAAU,EAAE,sBAAsB;QAClCU,SAAS,EAAEjD,OAAO,GAAG,YAAY,GAAG;MACtC;IAAE;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACD3D,SAAS,iBACRR,OAAA;MAAM2C,KAAK,EAAE;QAAEyB,QAAQ,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAR,QAAA,EAClDjD;IAAK;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC1D,EAAA,CApHIR,uBAA+D;AAAAqE,EAAA,GAA/DrE,uBAA+D;AAsHrE,eAAeA,uBAAuB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}