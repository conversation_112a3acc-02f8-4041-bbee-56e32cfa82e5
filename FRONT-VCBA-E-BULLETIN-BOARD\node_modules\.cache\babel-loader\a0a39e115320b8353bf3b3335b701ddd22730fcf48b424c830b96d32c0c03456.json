{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 6.5V3a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3.5\",\n  key: \"sqyvz\"\n}], [\"path\", {\n  d: \"M9 18h8\",\n  key: \"i7pszb\"\n}], [\"path\", {\n  d: \"M18 3h-3\",\n  key: \"7idoqj\"\n}], [\"path\", {\n  d: \"M11 3a6 6 0 0 0-6 6v11\",\n  key: \"1v5je3\"\n}], [\"path\", {\n  d: \"M5 13h4\",\n  key: \"svpcxo\"\n}], [\"path\", {\n  d: \"M17 10a4 4 0 0 0-8 0v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2Z\",\n  key: \"vsjego\"\n}]];\nconst FireExtinguisher = createLucideIcon(\"fire-extinguisher\", __iconNode);\nexport { __iconNode, FireExtinguisher as default };\n//# sourceMappingURL=fire-extinguisher.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}