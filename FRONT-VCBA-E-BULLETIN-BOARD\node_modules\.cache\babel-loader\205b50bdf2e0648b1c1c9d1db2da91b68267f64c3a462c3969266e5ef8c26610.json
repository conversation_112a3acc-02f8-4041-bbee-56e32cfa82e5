{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"path\", {\n  d: \"M16 8h.01\",\n  key: \"cr5u4v\"\n}], [\"path\", {\n  d: \"M8 8h.01\",\n  key: \"1e4136\"\n}], [\"path\", {\n  d: \"M8 16h.01\",\n  key: \"18s6g9\"\n}], [\"path\", {\n  d: \"M16 16h.01\",\n  key: \"1f9h7w\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}]];\nconst Dice5 = createLucideIcon(\"dice-5\", __iconNode);\nexport { __iconNode, Dice5 as default };\n//# sourceMappingURL=dice-5.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}