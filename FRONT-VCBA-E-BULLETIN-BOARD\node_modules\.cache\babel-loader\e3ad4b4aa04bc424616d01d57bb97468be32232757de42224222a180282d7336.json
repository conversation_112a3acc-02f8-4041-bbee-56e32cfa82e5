{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v4\",\n  key: \"16clxf\"\n}], [\"path\", {\n  d: \"M14 14h-4\",\n  key: \"esezmu\"\n}], [\"path\", {\n  d: \"M14 18h-4\",\n  key: \"16mqa2\"\n}], [\"path\", {\n  d: \"M14 8h-4\",\n  key: \"z8ypaz\"\n}], [\"path\", {\n  d: \"M18 12h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-9a2 2 0 0 1 2-2h2\",\n  key: \"b1k337\"\n}], [\"path\", {\n  d: \"M18 22V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v18\",\n  key: \"16g51d\"\n}]];\nconst Hospital = createLucideIcon(\"hospital\", __iconNode);\nexport { __iconNode, Hospital as default };\n//# sourceMappingURL=hospital.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}