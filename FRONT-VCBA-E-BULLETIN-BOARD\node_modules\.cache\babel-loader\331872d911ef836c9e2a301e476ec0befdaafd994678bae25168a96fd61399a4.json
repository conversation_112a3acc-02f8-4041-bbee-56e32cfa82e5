{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{httpClient,tokenManager}from'./api.service';import{API_ENDPOINTS,USER_DATA_KEY,REFRESH_TOKEN_KEY}from'../config/constants';export class AuthService{/**\n   * Get current authenticated user\n   */static async getCurrentUser(){try{const token=tokenManager.getToken();if(!token){return null;}const response=await httpClient.get(API_ENDPOINTS.AUTH.PROFILE);if(response.success&&response.data){return response.data.user;}return null;}catch(error){console.error('Failed to get current user:',error);return null;}}/**\n   * Login user (admin or student)\n   */static async login(credentials){try{var _response$error;const response=await httpClient.post(API_ENDPOINTS.AUTH.LOGIN,credentials);if(response.success&&response.data){// Transform raw database user data to frontend format\nconst rawUser=response.data.user;const transformedUser={id:rawUser.admin_id||rawUser.student_id,email:rawUser.email,role:rawUser.admin_id?'admin':'student',firstName:rawUser.first_name,lastName:rawUser.last_name,middleName:rawUser.middle_name,suffix:rawUser.suffix,phoneNumber:rawUser.phone_number||rawUser.phone,department:rawUser.department,position:rawUser.position,grade_level:rawUser.grade_level,studentNumber:rawUser.student_number,isActive:Boolean(rawUser.is_active),lastLogin:rawUser.last_login,createdAt:rawUser.account_created_at||rawUser.created_at,updatedAt:rawUser.account_updated_at||rawUser.updated_at};// Store tokens and transformed user data\ntokenManager.setToken(response.data.accessToken);// Note: refreshToken is typically stored as httpOnly cookie by the backend\n// Only store in localStorage if explicitly provided in response\nif(response.data.refreshToken){localStorage.setItem(REFRESH_TOKEN_KEY,response.data.refreshToken);}localStorage.setItem(USER_DATA_KEY,JSON.stringify(transformedUser));return{success:true,message:response.message||'Login successful',data:_objectSpread(_objectSpread({},response.data),{},{user:transformedUser})};}throw new Error(((_response$error=response.error)===null||_response$error===void 0?void 0:_response$error.message)||'Login failed');}catch(error){// Handle ApiError from httpClient\nif(error.status&&error.data){var _error$data,_error$data$error;throw new Error(((_error$data=error.data)===null||_error$data===void 0?void 0:(_error$data$error=_error$data.error)===null||_error$data$error===void 0?void 0:_error$data$error.message)||error.message||'Login failed');}throw new Error(error.message||'Login failed');}}/**\n   * Register admin user\n   */static async registerAdmin(data){try{var _response$error2;const response=await httpClient.post(API_ENDPOINTS.AUTH.ADMIN_REGISTER,data);// The response already contains the full structure from the backend\nif(response.success&&response.data){return{success:true,message:response.message||'Registration initiated successfully',data:response.data};}throw new Error(((_response$error2=response.error)===null||_response$error2===void 0?void 0:_response$error2.message)||'Registration failed');}catch(error){// Handle ApiError from httpClient\nif(error.status&&error.data){var _error$data2,_error$data2$error;throw new Error(((_error$data2=error.data)===null||_error$data2===void 0?void 0:(_error$data2$error=_error$data2.error)===null||_error$data2$error===void 0?void 0:_error$data2$error.message)||error.message||'Registration failed');}throw new Error(error.message||'Registration failed');}}/**\n   * Verify OTP for admin registration\n   */static async verifyOtp(data){try{var _response$error3;const response=await httpClient.post(API_ENDPOINTS.AUTH.VERIFY_OTP,data);// The response already contains the full structure from the backend\nif(response.success&&response.data){return{success:true,message:response.message||'OTP verification successful',data:response.data};}throw new Error(((_response$error3=response.error)===null||_response$error3===void 0?void 0:_response$error3.message)||'OTP verification failed');}catch(error){// Handle ApiError from httpClient\nif(error.status&&error.data){var _error$data3,_error$data3$error;throw new Error(((_error$data3=error.data)===null||_error$data3===void 0?void 0:(_error$data3$error=_error$data3.error)===null||_error$data3$error===void 0?void 0:_error$data3$error.message)||error.message||'OTP verification failed');}throw new Error(error.message||'OTP verification failed');}}/**\n   * Resend OTP for admin registration\n   */static async resendOtp(email){try{var _response$error4;const response=await httpClient.post(API_ENDPOINTS.AUTH.RESEND_OTP,{email});if(response.success){return response;}throw new Error(((_response$error4=response.error)===null||_response$error4===void 0?void 0:_response$error4.message)||'Failed to resend OTP');}catch(error){// Handle ApiError from httpClient\nif(error.status&&error.data){var _error$data4,_error$data4$error;throw new Error(((_error$data4=error.data)===null||_error$data4===void 0?void 0:(_error$data4$error=_error$data4.error)===null||_error$data4$error===void 0?void 0:_error$data4$error.message)||error.message||'Failed to resend OTP');}throw new Error(error.message||'Failed to resend OTP');}}/**\n   * Refresh access token\n   */static async refreshToken(){try{var _response$error5;const response=await httpClient.post(API_ENDPOINTS.AUTH.REFRESH);if(response.success&&response.data){// Update stored tokens\ntokenManager.setToken(response.data.accessToken);if(response.data.refreshToken){localStorage.setItem(REFRESH_TOKEN_KEY,response.data.refreshToken);}return{success:true,message:response.message,data:response.data};}throw new Error(((_response$error5=response.error)===null||_response$error5===void 0?void 0:_response$error5.message)||'Token refresh failed');}catch(error){throw new Error(error.message||'Token refresh failed');}}/**\n   * Get current user profile\n   */static async getProfile(){try{var _response$error6;const response=await httpClient.get(API_ENDPOINTS.AUTH.PROFILE);if(response.success&&response.data){// Update stored user data\nlocalStorage.setItem(USER_DATA_KEY,JSON.stringify(response.data.user));return response.data.user;}throw new Error(((_response$error6=response.error)===null||_response$error6===void 0?void 0:_response$error6.message)||'Failed to get profile');}catch(error){throw new Error(error.message||'Failed to get profile');}}/**\n   * Logout user\n   */static async logout(){try{console.log('🚪 AuthService - Calling server logout endpoint');// Call logout endpoint to invalidate token on server\nawait httpClient.post(API_ENDPOINTS.AUTH.LOGOUT);console.log('✅ AuthService - Server logout successful');}catch(error){// Continue with local logout even if server call fails\nconsole.warn('⚠️ AuthService - Server logout failed, continuing with local logout:',error);}finally{// Always clear local storage\nconsole.log('🧹 AuthService - Clearing all local storage');this.clearLocalStorage();}}/**\n   * Clear local storage and tokens\n   */static clearLocalStorage(){console.log('🧹 AuthService - Clearing all authentication data');// Remove tokens using token manager\ntokenManager.removeToken();// Remove user data and refresh token\nlocalStorage.removeItem(USER_DATA_KEY);localStorage.removeItem(REFRESH_TOKEN_KEY);// Clear any other auth-related items (comprehensive cleanup)\nconst authKeys=['vcba_auth_token','vcba_user_data','vcba_refresh_token','auth_token','user_data','refresh_token'];authKeys.forEach(key=>{localStorage.removeItem(key);console.log(\"\\uD83D\\uDDD1\\uFE0F Removed: \".concat(key));});// Clear any session storage as well\nsessionStorage.clear();console.log('✅ AuthService - All authentication data cleared');}/**\n   * Check if user is authenticated\n   */static isAuthenticated(){const token=tokenManager.getToken();const userData=this.getStoredUser();return!!(token&&userData);}/**\n   * Get stored user data\n   */static getStoredUser(){try{const userData=localStorage.getItem(USER_DATA_KEY);return userData?JSON.parse(userData):null;}catch(error){console.error('Error parsing stored user data:',error);return null;}}/**\n   * Validate current session - DISABLED TO PREVENT LOGOUTS\n   */static async validateSession(){// ALWAYS RETURN TRUE - No validation to prevent unwanted logouts\nconsole.log('AuthService.validateSession - DISABLED, always returning true');return true;}/**\n   * Get user role\n   */static getUserRole(){const user=this.getStoredUser();return(user===null||user===void 0?void 0:user.role)||null;}/**\n   * Check if user has specific role\n   */static hasRole(role){return this.getUserRole()===role;}}export default AuthService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}