{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.686 15A14.5 14.5 0 0 1 12 22a14.5 14.5 0 0 1 0-20 10 10 0 1 0 9.542 13\",\n  key: \"qkt0x6\"\n}], [\"path\", {\n  d: \"M2 12h8.5\",\n  key: \"ovaggd\"\n}], [\"path\", {\n  d: \"M20 6V4a2 2 0 1 0-4 0v2\",\n  key: \"1of5e8\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"14\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"1fmf51\"\n}]];\nconst GlobeLock = createLucideIcon(\"globe-lock\", __iconNode);\nexport { __iconNode, GlobeLock as default };\n//# sourceMappingURL=globe-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}