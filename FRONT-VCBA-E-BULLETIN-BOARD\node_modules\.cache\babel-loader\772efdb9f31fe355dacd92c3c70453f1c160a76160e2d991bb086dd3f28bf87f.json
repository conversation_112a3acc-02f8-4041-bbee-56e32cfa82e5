{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport CommentSection from '../../components/student/CommentSection';\nimport StudentNotificationBell from '../../components/student/NotificationBell';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport { getImageUrl, API_BASE_URL, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Home, Search, Pin, Calendar, MessageSquare, Heart, Filter, MapPin, BookOpen, Users, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('[SUCCESS] Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('[ERROR] Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Reusable CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('[SUCCESS] Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n\n// Facebook-style image gallery component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst FacebookImageGallery = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n  const getImageStyle = (index, total) => {\n    const baseStyle = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n    return baseStyle;\n  };\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n  const renderOverlay = (index, count) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        },\n        children: [\"+\", count]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0],\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '40%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getContainerStyle(actualIndex, visibleImages.length),\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this), renderOverlay(actualIndex, remainingCount), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FacebookImageGallery;\nconst StudentNewsfeed = () => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    notificationId,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const {\n    categories\n  } = useCategories();\n\n  // Get student user context\n  const {\n    user: studentUser,\n    logout\n  } = useStudentAuth();\n\n  // Use the announcements hook for proper state management\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({\n    length: 2\n  }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      console.log('🔍 STUDENT: Starting to fetch calendar events...');\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      console.log('🔍 STUDENT: Token exists?', !!token);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('🔍 STUDENT: Response status:', response.status);\n      const data = await response.json();\n      console.log('🔍 STUDENT: Response data:', data);\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n        console.log('🔍 STUDENT: Events data:', eventsData);\n        console.log('🔍 STUDENT: Number of events:', eventsData.length);\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem(STUDENT_AUTH_TOKEN_KEY)}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        console.log('🔍 STUDENT: Final events with images:', eventsWithImages);\n        setCalendarEvents(eventsWithImages);\n      } else {\n        console.log('🔍 STUDENT: API call failed or no data');\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for announcement:', announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log('[DEBUG] Student user context:', {\n        id: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n        role: 'student'\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log('[DEBUG] Student removing reaction...');\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log('[DEBUG] Student adding reaction...');\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log('[SUCCESS] Student reaction toggled successfully');\n    } catch (error) {\n      console.error('[ERROR] Error toggling student like:', error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for calendar event:', event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log('[DEBUG] Student user context:', {\n        id: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n        role: 'student'\n      });\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: !event.user_has_reacted,\n          reaction_count: event.user_has_reacted ? (event.reaction_count || 1) - 1 : (event.reaction_count || 0) + 1\n        } : e));\n        console.log('[SUCCESS] Calendar event reaction toggled successfully');\n      }\n    } catch (error) {\n      console.error('[ERROR] Student error toggling calendar like:', error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Filter announcements based on search term, category, and grade level\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Grade level filtering - show announcements for selected grade or all grades (null)\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel || announcement.grade_level === null; // Show all-grade announcements regardless of filter\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // For students, show events from the last 7 days and future events\n    // Only show published and active events\n    const sevenDaysAgo = new Date();\n    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0);\n    const isEventDateValid = eventDate >= sevenDaysAgo;\n    const isPublished = event.is_published === 1;\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventDateValid && isPublished && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n  console.log('🔍 Student Final Results:', {\n    totalCalendarEvents: calendarEvents.length,\n    filteredCalendarEvents: displayEvents.length,\n    displayEvents: displayEvents.map(e => ({\n      title: e.title,\n      date: e.event_date\n    }))\n  });\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 729,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-full\",\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          style: {\n            background: 'white',\n            borderBottom: '1px solid #e5e7eb',\n            position: 'sticky',\n            top: 0,\n            zIndex: 100,\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              padding: '0 2rem',\n              height: '72px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1.5rem',\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/vcba1.png\",\n                alt: \"VCBA Logo\",\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.5rem',\n                    fontWeight: '700',\n                    color: '#111827',\n                    lineHeight: '1.2'\n                  },\n                  children: \"VCBA E-Bulletin Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: \"Student Newsfeed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                maxWidth: '500px',\n                margin: '0 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20,\n                  style: {\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search post\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  style: {\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  },\n                  onFocus: e => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  },\n                  onBlur: e => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                minWidth: '400px',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterCategory,\n                  onChange: e => setFilterCategory(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Categories\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 19\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category.category_id.toString(),\n                    children: category.name\n                  }, category.category_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterGradeLevel,\n                  onChange: e => setFilterGradeLevel(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Grades\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 19\n                  }, this), gradeLevels.map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade.toString(),\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 17\n                }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  },\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: '#ef4444',\n                    color: 'white',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#dc2626';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#ef4444';\n                  },\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative'\n                  },\n                  \"data-dropdown\": \"user-dropdown\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowUserDropdown(!showUserDropdown),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.1)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(User, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: (studentUser === null || studentUser === void 0 ? void 0 : studentUser.firstName) || 'Student'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                      size: 14,\n                      style: {\n                        transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                        transition: 'transform 0.2s ease'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1019,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 19\n                  }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        },\n                        children: [studentUser === null || studentUser === void 0 ? void 0 : studentUser.firstName, \" \", studentUser === null || studentUser === void 0 ? void 0 : studentUser.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1045,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        },\n                        children: studentUser === null || studentUser === void 0 ? void 0 : studentUser.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1052,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.5rem 0'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          navigate('/student/dashboard');\n                          setShowUserDropdown(false);\n                        },\n                        style: {\n                          width: '100%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '0.75rem 1rem',\n                          background: 'transparent',\n                          border: 'none',\n                          color: '#374151',\n                          fontSize: '0.875rem',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = '#f3f4f6';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'transparent';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Home, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1086,\n                          columnNumber: 27\n                        }, this), \"Dashboard\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1061,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          handleLogout();\n                          setShowUserDropdown(false);\n                        },\n                        style: {\n                          width: '100%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '0.75rem 1rem',\n                          background: 'transparent',\n                          border: 'none',\n                          color: '#ef4444',\n                          fontSize: '0.875rem',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = '#fef2f2';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'transparent';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1115,\n                          columnNumber: 27\n                        }, this), \"Logout\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1090,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1060,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '1.5rem',\n            padding: '1.5rem 2rem',\n            background: 'transparent',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '280px',\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                borderRadius: '14px',\n                border: '1px solid #e5e7eb',\n                overflow: 'hidden',\n                position: 'sticky',\n                top: '80px',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem 1.25rem 0.75rem',\n                  borderBottom: '1px solid #f3f4f6',\n                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 20,\n                    style: {\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1162,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: 'white'\n                    },\n                    children: \"Important Updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1163,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: 'rgba(255, 255, 255, 0.8)'\n                  },\n                  children: \"Don't miss these announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1172,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '0.75rem'\n                },\n                children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                    // Handle alert announcements with special styling\n                    let colors;\n                    if (announcement.is_alert) {\n                      colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                      // Create gradient background based on category\n                      const gradientColors = {\n                        'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                        'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                        'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                        'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                        'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                        'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                      };\n                      colors = gradientColors[categoryName] || gradientColors['GENERAL'];\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.75rem',\n                        background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                        borderRadius: '10px',\n                        border: `1px solid ${colors[2]}`,\n                        marginBottom: '0.75rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = 'none';\n                      },\n                      onClick: () => setSelectedPinnedPost(announcement),\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'flex-start',\n                          gap: '0.75rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '10px',\n                            height: '10px',\n                            background: colors[2],\n                            borderRadius: '50%',\n                            marginTop: '0.5rem',\n                            flexShrink: 0,\n                            boxShadow: `0 0 0 3px ${colors[2]}30`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1233,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1243,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.8rem',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.content.length > 60 ? `${announcement.content.substring(0, 60)}...` : announcement.content\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1252,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              fontSize: '0.75rem',\n                              color: colors[2]\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                              size: 12\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1269,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: new Date(announcement.created_at).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1270,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1262,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1242,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 27\n                      }, this)\n                    }, announcement.announcement_id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 25\n                    }, this);\n                  }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                      color: '#3b82f6',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                      e.currentTarget.style.borderColor = '#e5e7eb';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    },\n                    children: [\"\\uD83D\\uDCCC View All \", pinnedAnnouncements.length, \" Important Updates\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 24,\n                    style: {\n                      marginBottom: '0.5rem',\n                      opacity: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '0.875rem'\n                    },\n                    children: \"No pinned posts available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1313,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1182,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              minWidth: 0\n            },\n            children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                padding: '3rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '2.5rem',\n                  height: '2.5rem',\n                  border: '3px solid #e5e7eb',\n                  borderTop: '3px solid #3b82f6',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1332,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 13\n            }, this), (error || calendarError) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(239, 68, 68, 0.1)',\n                border: '1px solid rgba(239, 68, 68, 0.2)',\n                borderRadius: '12px',\n                padding: '1rem',\n                marginBottom: '1.5rem',\n                color: '#dc2626'\n              },\n              children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Announcements: \", error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 25\n              }, this), calendarError && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Calendar: \", calendarError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1354,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1345,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.8)',\n                borderRadius: '16px',\n                padding: '3rem',\n                textAlign: 'center',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(Filter, {\n                  size: 48,\n                  color: \"#9ca3af\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#374151',\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  margin: '0 0 0.5rem 0'\n                },\n                children: \"No updates found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: searchTerm || filterCategory || filterGradeLevel ? 'Try adjusting your filters to see more content.' : 'Check back later for new updates.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              },\n              children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                      const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                      const IconComponent = holidayStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '50px',\n                          height: '50px',\n                          borderRadius: '12px',\n                          background: holidayStyle.background,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 20,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1447,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1438,\n                        columnNumber: 29\n                      }, this);\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: (() => {\n                          const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: holidayStyle.background,\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1475,\n                              columnNumber: 35\n                            }, this), holidayTypeName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1464,\n                            columnNumber: 33\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1452,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1481,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: event.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 23\n                  }, this), (() => {\n                    // Get event images if they exist\n                    const eventImageUrls = [];\n                    if (event.images && event.images.length > 0) {\n                      event.images.forEach(img => {\n                        if (img.file_path) {\n                          // Convert file_path to full URL\n                          const imageUrl = getImageUrl(img.file_path);\n                          if (imageUrl) {\n                            eventImageUrls.push(imageUrl);\n                          }\n                        }\n                      });\n                    }\n                    return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '1rem'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: eventImageUrls.filter(Boolean),\n                        altPrefix: event.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          const filteredImages = eventImageUrls.filter(Boolean);\n                          openLightbox(filteredImages, index);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1524,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0 0 1rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    children: event.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1539,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                          size: 14,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1565,\n                          columnNumber: 29\n                        }, this), \"School Event\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1564,\n                        columnNumber: 27\n                      }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Until \", new Date(event.end_date).toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1569,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1557,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleCalendarLikeToggle(event),\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'none',\n                          border: 'none',\n                          color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                          cursor: 'pointer',\n                          padding: '0.5rem',\n                          borderRadius: '8px',\n                          transition: 'all 0.2s ease',\n                          fontSize: '0.875rem',\n                          fontWeight: '500'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'none';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Heart, {\n                          size: 18,\n                          fill: event.user_has_reacted ? '#ef4444' : 'none'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1608,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: event.reaction_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1612,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1585,\n                        columnNumber: 27\n                      }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'none',\n                          border: 'none',\n                          color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                          cursor: 'pointer',\n                          padding: '0.5rem',\n                          borderRadius: '8px',\n                          transition: 'all 0.2s ease',\n                          fontSize: '0.875rem',\n                          fontWeight: '500'\n                        },\n                        onMouseEnter: e => {\n                          e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                        },\n                        onMouseLeave: e => {\n                          e.currentTarget.style.background = 'none';\n                          e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                          size: 18\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1644,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: event.comment_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1645,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1617,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1576,\n                      columnNumber: 25\n                    }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CommentSection, {\n                        calendarId: event.calendar_id,\n                        allowComments: event.allow_comments,\n                        currentUserId: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n                        currentUserType: \"student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1652,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1550,\n                    columnNumber: 23\n                  }, this)]\n                }, `event-${event.calendar_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: `announcement-${announcement.announcement_id}`,\n                  className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: announcement.is_alert ? '2px solid rgba(239, 68, 68, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: announcement.is_alert ? '0 4px 20px rgba(239, 68, 68, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = announcement.is_alert ? '0 8px 30px rgba(239, 68, 68, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = announcement.is_alert ? '0 4px 20px rgba(239, 68, 68, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      if (announcement.is_alert) {\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '50px',\n                            height: '50px',\n                            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                            borderRadius: '12px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            flexShrink: 0\n                          },\n                          children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                            size: 20,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1725,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1715,\n                          columnNumber: 31\n                        }, this);\n                      } else {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                        const categoryStyle = getCategoryStyle(categoryName);\n                        const IconComponent = categoryStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '50px',\n                            height: '50px',\n                            borderRadius: '12px',\n                            background: categoryStyle.background,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 20,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1743,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1734,\n                          columnNumber: 31\n                        }, this);\n                      }\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: [(() => {\n                          if (announcement.is_alert) {\n                            return /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                color: 'white',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                textTransform: 'uppercase',\n                                letterSpacing: '0.5px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                size: 12,\n                                color: \"white\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1771,\n                                columnNumber: 37\n                              }, this), \"Alert\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1758,\n                              columnNumber: 35\n                            }, this);\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n                            return /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                background: categoryStyle.background,\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                                size: 12,\n                                color: \"white\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1792,\n                                columnNumber: 37\n                              }, this), categoryName]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1781,\n                              columnNumber: 35\n                            }, this);\n                          }\n                        })(), announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                            color: 'white',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '12px',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Pin, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1810,\n                            columnNumber: 33\n                          }, this), \"PINNED\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1799,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1749,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: [\"By \", announcement.author_name, \" \\u2022 \", announcement.published_at ? new Date(announcement.published_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric',\n                          hour: '2-digit',\n                          minute: '2-digit'\n                        }) : 'Unknown date']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1815,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1748,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1706,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: announcement.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1832,\n                    columnNumber: 23\n                  }, this), (() => {\n                    // Get images from multiple sources\n                    const imageUrls = [];\n\n                    // Add images from attachments (new multiple image system)\n                    if (announcement.attachments && announcement.attachments.length > 0) {\n                      announcement.attachments.forEach(img => {\n                        if (img.file_path) {\n                          // Use getImageUrl to construct the full URL\n                          const fullUrl = getImageUrl(img.file_path);\n                          if (fullUrl) {\n                            imageUrls.push(fullUrl);\n                          }\n                        }\n                      });\n                    }\n\n                    // Fallback to legacy single image\n                    if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                      const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                      if (legacyUrl) {\n                        imageUrls.push(legacyUrl);\n                      }\n                    }\n                    return imageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: imageUrls.filter(Boolean),\n                      altPrefix: announcement.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = imageUrls.filter(Boolean);\n                        openLightbox(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1869,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '0 0 1.5rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: announcement.content\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1881,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleLikeToggle(announcement),\n                        style: {\n                          background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                          color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                          border: 'none',\n                          borderRadius: '8px',\n                          padding: '0.5rem 1rem',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          if (!announcement.user_reaction) {\n                            e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                            e.currentTarget.style.color = '#dc2626';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (!announcement.user_reaction) {\n                            e.currentTarget.style.background = 'transparent';\n                            e.currentTarget.style.color = '#6b7280';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Heart, {\n                          size: 16,\n                          color: announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\",\n                          fill: announcement.user_reaction ? \"#dc2626\" : \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1933,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: announcement.reaction_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1938,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1904,\n                        columnNumber: 27\n                      }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                        style: {\n                          background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                          color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                          border: 'none',\n                          borderRadius: '8px',\n                          padding: '0.5rem 1rem',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseEnter: e => {\n                          if (showComments !== announcement.announcement_id) {\n                            e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            e.currentTarget.style.color = '#3b82f6';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (showComments !== announcement.announcement_id) {\n                            e.currentTarget.style.background = 'transparent';\n                            e.currentTarget.style.color = '#6b7280';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                          size: 16,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1974,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: announcement.comment_count || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1975,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1943,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1898,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1891,\n                    columnNumber: 23\n                  }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '1rem',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(CommentSection, {\n                      announcementId: announcement.announcement_id,\n                      allowComments: announcement.allow_comments,\n                      currentUserId: studentUser === null || studentUser === void 0 ? void 0 : studentUser.id,\n                      currentUserType: \"student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1993,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1985,\n                    columnNumber: 25\n                  }, this)]\n                }, `announcement-${announcement.announcement_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1675,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1394,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1128,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 7\n      }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        },\n        onClick: () => setSelectedPinnedPost(null),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 20,\n                style: {\n                  color: '#22c55e'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2052,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                },\n                children: \"Pinned Post\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2053,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2047,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedPinnedPost(null),\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#6b7280',\n                cursor: 'pointer',\n                padding: '0.25rem',\n                borderRadius: '4px',\n                transition: 'color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.color = '#374151';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.color = '#6b7280';\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2062,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2040,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              },\n              children: [(() => {\n                if (selectedPinnedPost.is_alert) {\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2109,\n                      columnNumber: 25\n                    }, this), \"Alert\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2096,\n                    columnNumber: 23\n                  }, this);\n                } else {\n                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  const IconComponent = categoryStyle.icon;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: categoryStyle.background,\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2132,\n                      columnNumber: 25\n                    }, this), categoryName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2119,\n                    columnNumber: 23\n                  }, this);\n                }\n              })(), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2150,\n                  columnNumber: 19\n                }, this), \"PINNED\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2087,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              },\n              children: selectedPinnedPost.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              },\n              children: selectedPinnedPost.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2165,\n              columnNumber: 15\n            }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                images: selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean),\n                altPrefix: selectedPinnedPost.title,\n                onImageClick: index => {\n                  const imageUrls = selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean);\n                  openLightbox(imageUrls, index);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2177,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2197,\n                columnNumber: 17\n              }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"By: \", selectedPinnedPost.author_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2206,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2086,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2028,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2013,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n        images: lightboxImages,\n        initialIndex: lightboxInitialIndex,\n        isOpen: lightboxOpen,\n        onClose: () => setLightboxOpen(false),\n        altPrefix: \"Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2217,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 766,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s3(StudentNewsfeed, \"i1ctYoT3Z1LBh34YA0aEpS6Wjak=\", false, function () {\n  return [useNavigate, useNotificationTarget, useCategories, useStudentAuth, useAnnouncements];\n});\n_c3 = StudentNewsfeed;\nexport default StudentNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"FacebookImageGallery\");\n$RefreshReg$(_c3, \"StudentNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAnnouncements", "useCategories", "useNotificationTarget", "useStudentAuth", "calendarReactionService", "CommentSection", "StudentNotificationBell", "ImageLightbox", "getImageUrl", "API_BASE_URL", "STUDENT_AUTH_TOKEN_KEY", "Home", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Filter", "MapPin", "BookOpen", "Users", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "FacebookImageGallery", "images", "altPrefix", "maxVisible", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "getImageStyle", "index", "total", "baseStyle", "width", "height", "objectFit", "cursor", "transition", "borderRadius", "getContainerStyle", "position", "overflow", "renderOverlay", "count", "top", "left", "right", "bottom", "gap", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "_c2", "StudentNewsfeed", "_s3", "navigate", "isFromNotification", "notificationId", "scrollTarget", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "pinnedAnnouncements", "setPinnedAnnouncements", "categories", "user", "studentUser", "logout", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "gradeLevels", "Array", "from", "_", "i", "openLightbox", "imageUrls", "initialIndex", "pinned", "filter", "ann", "is_pinned", "fetchCalendarEvents", "undefined", "token", "localStorage", "getItem", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "handleCalendarLikeToggle", "user_has_reacted", "toggleLike", "prevEvents", "reaction_count", "handleLogout", "href", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "filteredAnnouncements", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "sevenDaysAgo", "Date", "setDate", "getDate", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "isActive", "is_active", "displayAnnouncements", "displayEvents", "totalCalendarEvents", "date", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "scroll<PERSON>eh<PERSON>or", "flex", "borderBottom", "zIndex", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "grade", "firstName", "lastName", "email", "flexShrink", "colors", "is_alert", "category_name", "toUpperCase", "gradientColors", "substring", "toLocaleDateString", "opacity", "borderTop", "animation", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "file_path", "push", "Boolean", "filteredImages", "paddingTop", "end_date", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "categoryStyle", "textTransform", "letterSpacing", "author_name", "published_at", "hour", "minute", "image_url", "image_path", "legacyUrl", "dangerouslySetInnerHTML", "__html", "announcementId", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { announcementService, calendarService } from '../../services';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport CommentSection from '../../components/student/CommentSection';\nimport StudentNotificationBell from '../../components/student/NotificationBell';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport type { Announcement } from '../../types/announcement.types';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Home,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Filter,\n  MapPin,\n  BookOpen,\n  Users,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('[SUCCESS] Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('[ERROR] Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Reusable CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('[SUCCESS] Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Facebook-style image gallery component\ninterface FacebookImageGalleryProps {\n  images: string[];\n  altPrefix: string;\n  maxVisible?: number;\n  onImageClick?: (index: number) => void;\n}\n\nconst FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n\n    return baseStyle;\n  };\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n\n  const renderOverlay = (index: number, count: number) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        }}>\n          +{count}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0]}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images (for 2+ images) */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '40%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            return (\n              <div\n                key={actualIndex}\n                style={getContainerStyle(actualIndex, visibleImages.length)}\n              >\n                <ImageDisplay\n                  imagePath={image}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                {renderOverlay(actualIndex, remainingCount)}\n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst StudentNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, notificationId, scrollTarget } = useNotificationTarget();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n  \n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n\n  const { categories } = useCategories();\n\n  // Get student user context\n  const { user: studentUser, logout } = useStudentAuth();\n\n  // Use the announcements hook for proper state management\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({ length: 2 }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      console.log('🔍 STUDENT: Starting to fetch calendar events...');\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      console.log('🔍 STUDENT: Token exists?', !!token);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      console.log('🔍 STUDENT: Response status:', response.status);\n      const data = await response.json();\n      console.log('🔍 STUDENT: Response data:', data);\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n        console.log('🔍 STUDENT: Events data:', eventsData);\n        console.log('🔍 STUDENT: Number of events:', eventsData.length);\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem(STUDENT_AUTH_TOKEN_KEY)}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        console.log('🔍 STUDENT: Final events with images:', eventsWithImages);\n        setCalendarEvents(eventsWithImages);\n      } else {\n        console.log('🔍 STUDENT: API call failed or no data');\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n  }, []);\n\n\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for announcement:', announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log('[DEBUG] Student user context:', { id: studentUser?.id, role: 'student' });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log('[DEBUG] Student removing reaction...');\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log('[DEBUG] Student adding reaction...');\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log('[SUCCESS] Student reaction toggled successfully');\n    } catch (error) {\n      console.error('[ERROR] Error toggling student like:', error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log('[DEBUG] Student toggling reaction for calendar event:', event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log('[DEBUG] Student user context:', { id: studentUser?.id, role: 'student' });\n\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: !event.user_has_reacted,\n                  reaction_count: event.user_has_reacted\n                    ? (event.reaction_count || 1) - 1\n                    : (event.reaction_count || 0) + 1\n                }\n              : e\n          )\n        );\n        console.log('[SUCCESS] Calendar event reaction toggled successfully');\n      }\n    } catch (error) {\n      console.error('[ERROR] Student error toggling calendar like:', error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      window.location.href = '/student/login';\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n\n\n  // Filter announcements based on search term, category, and grade level\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Grade level filtering - show announcements for selected grade or all grades (null)\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel ||\n      announcement.grade_level === null; // Show all-grade announcements regardless of filter\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // For students, show events from the last 7 days and future events\n    // Only show published and active events\n    const sevenDaysAgo = new Date();\n    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0);\n\n    const isEventDateValid = eventDate >= sevenDaysAgo;\n    const isPublished = (event as any).is_published === 1;\n    const isActive = (event as any).is_active !== 0;\n\n\n\n    return matchesSearch && isEventDateValid && isPublished && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  console.log('🔍 Student Final Results:', {\n    totalCalendarEvents: calendarEvents.length,\n    filteredCalendarEvents: displayEvents.length,\n    displayEvents: displayEvents.map(e => ({ title: e.title, date: e.event_date }))\n  });\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <>\n      {/* CSS Animations */}\n      <style>\n        {`\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `}\n      </style>\n\n      <div style={{\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      }}>\n\n      {/* Main Content Area */}\n      <div\n        className=\"mobile-full\"\n        style={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        }}>\n        {/* Modern Student Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            width: '100%',\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Student Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Filters + Actions */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  {gradeLevels.map(grade => (\n                    <option key={grade} value={grade.toString()}>\n                      Grade {grade}\n                    </option>\n                  ))}\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell */}\n                <StudentNotificationBell />\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    <User size={16} />\n                    <span>{studentUser?.firstName || 'Student'}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        }}>\n                          {studentUser?.firstName} {studentUser?.lastName}\n                        </div>\n                        <div style={{\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          {studentUser?.email}\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        <button\n                          onClick={() => {\n                            navigate('/student/dashboard');\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#374151',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f3f4f6';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <Home size={16} />\n                          Dashboard\n                        </button>\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content Layout */}\n        <div style={{\n          flex: 1,\n          display: 'flex',\n          gap: '1.5rem',\n          padding: '1.5rem 2rem',\n          background: 'transparent',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '280px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '14px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '80px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1rem 1.25rem 0.75rem',\n                borderBottom: '1px solid #f3f4f6',\n                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: 'white' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: 'white'\n                  }}>\n                    Important Updates\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: 'rgba(255, 255, 255, 0.8)'\n                }}>\n                  Don't miss these announcements\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '0.75rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                      // Handle alert announcements with special styling\n                      let colors: [string, string, string];\n                      if (announcement.is_alert) {\n                        colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                      } else {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                        // Create gradient background based on category\n                        const gradientColors: Record<string, [string, string, string]> = {\n                          'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                          'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                          'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                          'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                          'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                          'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                        };\n\n                        colors = gradientColors[categoryName] || gradientColors['GENERAL'];\n                      }\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '0.75rem',\n                            background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                            borderRadius: '10px',\n                            border: `1px solid ${colors[2]}`,\n                            marginBottom: '0.75rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.transform = 'translateY(-2px)';\n                            e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.transform = 'translateY(0)';\n                            e.currentTarget.style.boxShadow = 'none';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '10px',\n                              height: '10px',\n                              background: colors[2],\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0,\n                              boxShadow: `0 0 0 3px ${colors[2]}30`\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 60\n                                  ? `${announcement.content.substring(0, 60)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: colors[2]\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                        color: '#3b82f6',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                        e.currentTarget.style.borderColor = '#3b82f6';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}>\n                        📌 View All {pinnedAnnouncements.length} Important Updates\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: '3rem'\n            }}>\n              <div style={{\n                width: '2.5rem',\n                height: '2.5rem',\n                border: '3px solid #e5e7eb',\n                borderTop: '3px solid #3b82f6',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></div>\n            </div>\n          )}\n\n          {/* Error Messages */}\n          {(error || calendarError) && (\n            <div style={{\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '12px',\n              padding: '1rem',\n              marginBottom: '1.5rem',\n              color: '#dc2626'\n            }}>\n              {error && <div>Announcements: {error}</div>}\n              {calendarError && <div>Calendar: {calendarError}</div>}\n            </div>\n          )}\n\n          {/* No Content */}\n          {!loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.8)',\n              borderRadius: '16px',\n              padding: '3rem',\n              textAlign: 'center',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)'\n            }}>\n              <div style={{\n                marginBottom: '1rem'\n              }}>\n                <Filter size={48} color=\"#9ca3af\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                margin: '0 0 0.5rem 0'\n              }}>\n                No updates found\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: 0\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'Try adjusting your filters to see more content.'\n                  : 'Check back later for new updates.'}\n              </p>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              maxWidth: '1200px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n\n                          return (\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: holidayStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}>\n                              <IconComponent size={20} color=\"white\" />\n                            </div>\n                          );\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = (event.category_name || 'SCHOOL EVENT').toUpperCase();\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            {new Date(event.event_date).toLocaleDateString('en-US', {\n                              weekday: 'long',\n                              year: 'numeric',\n                              month: 'long',\n                              day: 'numeric'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {event.title}\n                      </h3>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightbox(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {event.description && (\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6'\n                        }}>\n                          {event.description}\n                        </p>\n                      )}\n\n                      {/* Event Footer */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                            <MapPin size={14} color=\"#6b7280\" />\n                            School Event\n                          </span>\n                          {event.end_date && event.end_date !== event.event_date && (\n                            <span>\n                              Until {new Date(event.end_date).toLocaleDateString()}\n                            </span>\n                          )}\n                        </div>\n\n                        {/* Calendar Event Interaction Section */}\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleCalendarLikeToggle(event)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                            />\n                            <span>{(event as any).reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {(event as any).allow_comments && (\n                            <button\n                              onClick={() => setShowCalendarComments(\n                                showCalendarComments === event.calendar_id ? null : event.calendar_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{(event as any).comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n                        {/* Calendar Event Comments Section */}\n                        {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                          <div style={{\n                            marginTop: '1rem',\n                            paddingTop: '1rem',\n                            borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                          }}>\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={studentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_alert\n                          ? '2px solid rgba(239, 68, 68, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_alert\n                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_alert\n                          ? '0 8px 30px rgba(239, 68, 68, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_alert\n                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={20} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                borderRadius: '12px',\n                                background: categoryStyle.background,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                              }}>\n                                <IconComponent size={20} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n                            {announcement.is_pinned && (\n                              <span style={{\n                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              }}>\n                                <Pin size={12} color=\"white\" />\n                                PINNED\n                              </span>\n                            )}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            By {announcement.author_name} • {announcement.published_at ? new Date(announcement.published_at).toLocaleDateString('en-US', {\n                              weekday: 'short',\n                              year: 'numeric',\n                              month: 'short',\n                              day: 'numeric',\n                              hour: '2-digit',\n                              minute: '2-digit'\n                            }) : 'Unknown date'}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {announcement.title}\n                      </h3>\n\n                      {/* Images - Facebook-style Gallery */}\n                      {(() => {\n                        // Get images from multiple sources\n                        const imageUrls: string[] = [];\n\n                        // Add images from attachments (new multiple image system)\n                        if (announcement.attachments && announcement.attachments.length > 0) {\n                          announcement.attachments.forEach((img: AnnouncementAttachment) => {\n                            if (img.file_path) {\n                              // Use getImageUrl to construct the full URL\n                              const fullUrl = getImageUrl(img.file_path);\n                              if (fullUrl) {\n                                imageUrls.push(fullUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        // Fallback to legacy single image\n                        if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                          const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                          if (legacyUrl) {\n                            imageUrls.push(legacyUrl);\n                          }\n                        }\n\n                        return imageUrls.length > 0 ? (\n                          <FacebookImageGallery\n                            images={imageUrls.filter(Boolean) as string[]}\n                            altPrefix={announcement.title}\n                            maxVisible={4}\n                            onImageClick={(index) => {\n                              const filteredImages = imageUrls.filter(Boolean) as string[];\n                              openLightbox(filteredImages, index);\n                            }}\n                          />\n                        ) : null;\n                      })()}\n\n                      <div style={{\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      }}\n                      dangerouslySetInnerHTML={{ __html: announcement.content }}\n                      />\n\n                      {/* Announcement Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                              color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                              border: 'none',\n                              borderRadius: '8px',\n                              padding: '0.5rem 1rem',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                                e.currentTarget.style.color = '#dc2626';\n                              }\n                            }}\n                            onMouseLeave={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'transparent';\n                                e.currentTarget.style.color = '#6b7280';\n                              }\n                            }}\n                          >\n                            <Heart\n                              size={16}\n                              color={announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\"}\n                              fill={announcement.user_reaction ? \"#dc2626\" : \"none\"}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comment Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                                color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                transition: 'all 0.2s ease'\n                              }}\n                              onMouseEnter={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                                  e.currentTarget.style.color = '#3b82f6';\n                                }\n                              }}\n                              onMouseLeave={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'transparent';\n                                  e.currentTarget.style.color = '#6b7280';\n                                }\n                              }}\n                            >\n                              <MessageSquare size={16} color=\"#6b7280\" />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n\n\n                          {/* Existing Comments */}\n                          <CommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserId={studentUser?.id}\n                            currentUserType=\"student\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <FacebookImageGallery\n                    images={selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean)}\n                    altPrefix={selectedPinnedPost.title}\n                    onImageClick={(index) => {\n                      const imageUrls = selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean);\n                      openLightbox(imageUrls, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Image\"\n      />\n    </div>\n    </>\n  );\n};\n\nexport default StudentNewsfeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,cAAc,QAAQ,mCAAmC;AAElE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,uBAAuB,MAAM,2CAA2C;AAC/E,OAAOC,aAAa,MAAM,uCAAuC;AAIjE,SAASC,WAAW,EAAEC,YAAY,EAAEC,sBAAsB,QAAQ,wBAAwB;AAC1F,OAAO,wCAAwC;AAC/C,SACEC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACwC,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGvC,WAAW,CAAC8B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CrB,WAAW,CAACsB,SAAS,CAAC;QAEtBd,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAE9D,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEsB,GAAG,CAAC;QACjDrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA/DMF,cAAc;AA0EpB,MAAMiC,YAAyC,GAAGA,CAAC;EACjDhC,SAAS;EACTiC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAErC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEN,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/E9C,KAAK,iBACJV,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzEvC;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IACE4D,GAAG,EAAEtD,QAAS;IACd+B,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CArEMP,YAAyC;EAAA,QASRjC,cAAc;AAAA;AAAA2D,EAAA,GAT/C1B,YAAyC;AA6E/C,MAAM2B,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,SAAS;EACTC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;EACjD,MAAMK,cAAc,GAAGP,MAAM,CAACI,MAAM,GAAGF,UAAU;EAEjD,MAAMM,aAAa,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC3E,MAAMC,SAA8B,GAAG;MACrCC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,uCAAuC;MACnDC,YAAY,EAAER,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG;IACtD,CAAC;IAED,OAAOC,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCQ,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBrC,eAAe,EAAE;IACnB,CAAC;IAED,IAAI2B,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UACL,GAAGE,SAAS;UACZC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGN,SAAS;UACZC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGE,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL,GAAGN,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE;MAChB,CAAC;IACH;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAACZ,KAAa,EAAEa,KAAa,KAAK;IACtD,IAAIb,KAAK,KAAKP,UAAU,GAAG,CAAC,IAAIoB,KAAK,GAAG,CAAC,EAAE;MACzC,oBACEtF,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdI,QAAQ,EAAE,QAAQ;UAClBM,UAAU,EAAE,KAAK;UACjBuB,YAAY,EAAE;QAChB,CAAE;QAAAhC,QAAA,GAAC,GACA,EAACqC,KAAK;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACExD,OAAA;IAAKsC,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf+C,GAAG,EAAE,KAAK;MACVf,KAAK,EAAE,MAAM;MACbzB,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAjD,OAAA;MAAKsC,KAAK,EAAE4C,iBAAiB,CAAC,CAAC,EAAEb,aAAa,CAACD,MAAM,CAAE;MAAAnB,QAAA,gBACrDjD,OAAA,CAACoC,YAAY;QACXhC,SAAS,EAAEiE,aAAa,CAAC,CAAC,CAAE;QAC5BhC,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAEkC,aAAa,CAAC,CAAC,EAAEH,aAAa,CAACD,MAAM,CAAE;QAC9C3B,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;QACjD,CAAE;QACFnD,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDW,YAAY,iBACXnE,OAAA;QACEsC,KAAK,EAAE;UACL6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTX,MAAM,EAAE;QACV,CAAE;QACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,CAAC;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLa,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBpE,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE,QAAQ;QACvBJ,GAAG,EAAE,KAAK;QACVf,KAAK,EAAEP,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAnB,QAAA,EACCoB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,oBACElG,OAAA;UAEEsC,KAAK,EAAE4C,iBAAiB,CAACiB,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;UAAAnB,QAAA,gBAE5DjD,OAAA,CAACoC,YAAY;YACXhC,SAAS,EAAE6F,KAAM;YACjB5D,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAEkC,aAAa,CAAC2B,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;YACxD3B,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;YACjD,CAAE;YACFnD,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD6B,aAAa,CAACc,WAAW,EAAE5B,cAAc,CAAC,EAC1CJ,YAAY,iBACXnE,OAAA;YACEsC,KAAK,EAAE;cACL6C,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTX,MAAM,EAAE;YACV,CAAE;YACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACgC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GA3BI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Bb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4C,GAAA,GAhMIrC,oBAAyD;AAkM/D,MAAMsC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtC,MAAMC,QAAQ,GAAG1I,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAE2I,kBAAkB;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAG1I,qBAAqB,CAAC,CAAC;;EAEpF;EACA,MAAM2I,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9H;MACR,CAAC;MACD,SAAS,EAAE;QACT6H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE7H;MACR,CAAC;MACD,QAAQ,EAAE;QACR4H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE5H;MACR,CAAC;MACD,WAAW,EAAE;QACX2H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE3H;MACR,CAAC;MACD,QAAQ,EAAE;QACR0H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzH;MACR,CAAC;MACD,WAAW,EAAE;QACXwH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE1H;MACR;IACF,CAAC;IAED,OAAOwH,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtH;MACR,CAAC;MACD,cAAc,EAAE;QACdqH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvH;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBsH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErH;MACR,CAAC;MACD,cAAc,EAAE;QACdoH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzH;MACR,CAAC;MACD,YAAY,EAAE;QACZwH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpH;MACR,CAAC;MACD,SAAS,EAAE;QACTmH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExH;MACR;IACF,CAAC;IAED,OAAOsH,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACyJ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1J,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC2J,UAAU,EAAEC,aAAa,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAAC6J,YAAY,EAAEC,eAAe,CAAC,GAAG9J,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhK,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACiK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlK,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAACmK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACqK,YAAY,EAAEC,eAAe,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuK,cAAc,EAAEC,iBAAiB,CAAC,GAAGxK,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACyK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1K,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC2K,cAAc,EAAEC,iBAAiB,CAAC,GAAG5K,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAAC6K,eAAe,EAAEC,kBAAkB,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+K,aAAa,EAAEC,gBAAgB,CAAC,GAAGhL,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAACiL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlL,QAAQ,CAAQ,EAAE,CAAC;EAEzE,MAAM;IAAEmL;EAAW,CAAC,GAAG/K,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEgL,IAAI,EAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGhL,cAAc,CAAC,CAAC;;EAEtD;EACA,MAAM;IACJiL,aAAa;IACb1I,OAAO;IACPE,KAAK;IACLyI,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAGxL,gBAAgB,CAAC;IACnB4D,MAAM,EAAE,WAAW;IACnB6H,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEzF,MAAM,EAAE;EAAE,CAAC,EAAE,CAAC0F,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMC,YAAY,GAAGA,CAACC,SAAmB,EAAEC,YAAoB,KAAK;IAClE/B,iBAAiB,CAAC8B,SAAS,CAAC;IAC5B5B,uBAAuB,CAAC6B,YAAY,CAAC;IACrCjC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACArK,SAAS,CAAC,MAAM;IACd,MAAMuM,MAAM,GAAGjB,aAAa,CAACkB,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtEzB,sBAAsB,CAACsB,MAAM,CAAC;EAChC,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFxJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DyH,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAAC6B,SAAS,CAAC;MAE3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACnM,sBAAsB,CAAC;MAC1DuC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,CAAC,CAACyJ,KAAK,CAAC;MAEjD,MAAMxJ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3C,YAAY,0DAA0D,EAAE;QACtG6C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUqJ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF1J,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAACS,MAAM,CAAC;MAC5D,MAAMkJ,IAAI,GAAG,MAAM3J,QAAQ,CAAC4J,IAAI,CAAC,CAAC;MAClC9J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4J,IAAI,CAAC;MAE/C,IAAIA,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;QACtD7J,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE+J,UAAU,CAAC;QACnDhK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+J,UAAU,CAAC3G,MAAM,CAAC;;QAE/D;QACA,MAAM6G,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAAC/E,GAAG,CAAC,MAAOoF,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMnK,KAAK,CAAC,GAAG3C,YAAY,iBAAiB6M,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FlK,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUsJ,YAAY,CAACC,OAAO,CAACnM,sBAAsB,CAAC,EAAE;gBACzE,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAM+M,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACpH,MAAM,GAAGuH,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACpH,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAOyH,MAAM,EAAE;YACf1K,OAAO,CAAC2K,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACpH,MAAM,GAAG,EAAE;UACnB;UACA,OAAOoH,KAAK;QACd,CAAC,CACH,CAAC;QAEDrK,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEiK,gBAAgB,CAAC;QACtE1C,iBAAiB,CAAC0C,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLlK,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD2H,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAO3G,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEsB,GAAG,CAAC;MACrD2G,gBAAgB,CAAC3G,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRwG,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA7K,SAAS,CAAC,MAAM;IACd2M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA,MAAMoB,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF7K,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE4K,YAAY,CAACC,eAAe,CAAC;MAChG9K,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4K,YAAY,CAACE,aAAa,CAAC;MACzE/K,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAE+K,EAAE,EAAE/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;MAEtF,IAAIJ,YAAY,CAACE,aAAa,EAAE;QAC9B;QACA/K,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,MAAMoI,kBAAkB,CAACwC,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACA9K,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAMmI,gBAAgB,CAACyC,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEA9K,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAChE,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAMuL,wBAAwB,GAAG,MAAOb,KAAU,IAAK;IACrD,IAAI;MACFrK,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEoK,KAAK,CAACE,WAAW,CAAC;MACvFvK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoK,KAAK,CAACc,gBAAgB,CAAC;MACxEnL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAAE+K,EAAE,EAAE/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;MAEtF,MAAM/K,QAAQ,GAAG,MAAM/C,uBAAuB,CAACiO,UAAU,CAACf,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACc,gBAAgB,IAAI,KAAK,CAAC;MAE7G,IAAIjL,QAAQ,CAAC6J,OAAO,EAAE;QACpB;QACAvC,iBAAiB,CAAC6D,UAAU,IAC1BA,UAAU,CAACpG,GAAG,CAACnC,CAAC,IACdA,CAAC,CAACyH,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAGzH,CAAC;UACJqI,gBAAgB,EAAE,CAACd,KAAK,CAACc,gBAAgB;UACzCG,cAAc,EAAEjB,KAAK,CAACc,gBAAgB,GAClC,CAACd,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI,CAAC,GAC/B,CAACjB,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI;QACpC,CAAC,GACDxI,CACN,CACF,CAAC;QACD9C,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAM4L,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrD,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACAW,MAAM,CAACC,QAAQ,CAACiL,IAAI,GAAG,gBAAgB;IACzC;EACF,CAAC;;EAED;EACA3O,SAAS,CAAC,MAAM;IACd,MAAM4O,kBAAkB,GAAIpB,KAAiB,IAAK;MAChD,IAAItD,gBAAgB,EAAE;QACpB,MAAM2E,MAAM,GAAGrB,KAAK,CAACqB,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtD3E,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAED4E,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAAC1E,gBAAgB,CAAC,CAAC;;EAItB;EACA,MAAMgF,qBAAqB,GAAG5D,aAAa,CAACkB,MAAM,CAACwB,YAAY,IAAI;IAAA,IAAAmB,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAAC1F,UAAU,IAC/BsE,YAAY,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC,IACnEtB,YAAY,CAACwB,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAACnG,cAAc,IAAI0E,YAAY,CAAC0B,WAAW,CAACC,QAAQ,CAAC,CAAC,KAAKrG,cAAc;;IAEjG;IACA,MAAMsG,iBAAiB,GAAG,CAACpG,gBAAgB,IACzC,EAAA2F,qBAAA,GAAAnB,YAAY,CAAC6B,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAKnG,gBAAgB,IACzDwE,YAAY,CAAC6B,WAAW,KAAK,IAAI,CAAC,CAAC;;IAErC,OAAOT,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGpF,cAAc,CAAC8B,MAAM,CAACgB,KAAK,IAAI;IAC5D,MAAM4B,aAAa,GAAG,CAAC1F,UAAU,IAC/B8D,KAAK,CAAC6B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAC,IAC3D9B,KAAK,CAACuC,WAAW,IAAIvC,KAAK,CAACuC,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7F,UAAU,CAAC4F,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,YAAY,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC/BD,YAAY,CAACE,OAAO,CAACF,YAAY,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChDH,YAAY,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEjC,MAAMC,SAAS,GAAG,IAAIJ,IAAI,CAACzC,KAAK,CAAC8C,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE9B,MAAMG,gBAAgB,GAAGF,SAAS,IAAIL,YAAY;IAClD,MAAMQ,WAAW,GAAIhD,KAAK,CAASiD,YAAY,KAAK,CAAC;IACrD,MAAMC,QAAQ,GAAIlD,KAAK,CAASmD,SAAS,KAAK,CAAC;IAI/C,OAAOvB,aAAa,IAAImB,gBAAgB,IAAIC,WAAW,IAAIE,QAAQ;EACrE,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAG1B,qBAAqB;EAClD,MAAM2B,aAAa,GAAGf,sBAAsB;EAE5C3M,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IACvC0N,mBAAmB,EAAEpG,cAAc,CAAClE,MAAM;IAC1CsJ,sBAAsB,EAAEe,aAAa,CAACrK,MAAM;IAC5CqK,aAAa,EAAEA,aAAa,CAACzI,GAAG,CAACnC,CAAC,KAAK;MAAEoJ,KAAK,EAAEpJ,CAAC,CAACoJ,KAAK;MAAE0B,IAAI,EAAE9K,CAAC,CAACqK;IAAW,CAAC,CAAC;EAChF,CAAC,CAAC;;EAEF;EACA,MAAMU,eAAe,GAAG,CACtB,GAAGJ,oBAAoB,CAACxI,GAAG,CAAC6I,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIlB,IAAI,CAACgB,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGP,aAAa,CAACzI,GAAG,CAAC6I,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIlB,IAAI,CAACgB,IAAI,CAACX,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACEpP,OAAA,CAAAE,SAAA;IAAA+C,QAAA,gBAEEjD,OAAA;MAAAiD,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERxD,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfyM,SAAS,EAAE,OAAO;QAClBvI,UAAU,EAAE,mDAAmD;QAC/DwI,cAAc,EAAE;MAClB,CAAE;MAAArM,QAAA,gBAGFjD,OAAA;QACEuC,SAAS,EAAC,aAAa;QACvBD,KAAK,EAAE;UACLiN,IAAI,EAAE,CAAC;UACP3M,OAAO,EAAE,MAAM;UACfmD,aAAa,EAAE,QAAQ;UACvBsJ,SAAS,EAAE,OAAO;UAClBzK,KAAK,EAAE;QACT,CAAE;QAAA3B,QAAA,gBAEFjD,OAAA;UAAQsC,KAAK,EAAE;YACbwE,UAAU,EAAE,OAAO;YACnB0I,YAAY,EAAE,mBAAmB;YACjCrK,QAAQ,EAAE,QAAQ;YAClBI,GAAG,EAAE,CAAC;YACNkK,MAAM,EAAE,GAAG;YACXC,SAAS,EAAE;UACb,CAAE;UAAAzM,QAAA,eACAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,MAAM;cACb+K,OAAO,EAAE,QAAQ;cACjB9K,MAAM,EAAE,MAAM;cACdjC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAEAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,QAAQ;gBACbiK,QAAQ,EAAE;cACZ,CAAE;cAAA3M,QAAA,gBACAjD,OAAA;gBACE4D,GAAG,EAAC,iBAAiB;gBACrBvB,GAAG,EAAC,WAAW;gBACfC,KAAK,EAAE;kBACLsC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,SAAS,EAAE;gBACb;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIsC,KAAK,EAAE;oBACTuN,MAAM,EAAE,CAAC;oBACTzM,QAAQ,EAAE,QAAQ;oBAClBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE,SAAS;oBAChB8M,UAAU,EAAE;kBACd,CAAE;kBAAA7M,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAGsC,KAAK,EAAE;oBACRuN,MAAM,EAAE,CAAC;oBACTzM,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACViN,IAAI,EAAE,CAAC;gBACPQ,QAAQ,EAAE,OAAO;gBACjBF,MAAM,EAAE;cACV,CAAE;cAAA5M,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE6C,QAAQ,EAAE;gBAAW,CAAE;gBAAAlC,QAAA,gBACnCjD,OAAA,CAACtB,MAAM;kBACLsR,IAAI,EAAE,EAAG;kBACT1N,KAAK,EAAE;oBACL6C,QAAQ,EAAE,UAAU;oBACpBK,IAAI,EAAE,MAAM;oBACZD,GAAG,EAAE,KAAK;oBACVM,SAAS,EAAE,kBAAkB;oBAC7B7C,KAAK,EAAE;kBACT;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxD,OAAA;kBACE8O,IAAI,EAAC,MAAM;kBACXmB,WAAW,EAAC,aAAa;kBACzBC,KAAK,EAAE5I,UAAW;kBAClB6I,QAAQ,EAAGtM,CAAC,IAAK0D,aAAa,CAAC1D,CAAC,CAAC4I,MAAM,CAACyD,KAAK,CAAE;kBAC/C5N,KAAK,EAAE;oBACLsC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACd8K,OAAO,EAAE,eAAe;oBACxBlM,MAAM,EAAE,mBAAmB;oBAC3BwB,YAAY,EAAE,MAAM;oBACpB6B,UAAU,EAAE,SAAS;oBACrB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBgN,OAAO,EAAE,MAAM;oBACfpL,UAAU,EAAE;kBACd,CAAE;kBACFqL,OAAO,EAAGxM,CAAC,IAAK;oBACdA,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACgO,WAAW,GAAG,SAAS;oBACtCzM,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACwE,UAAU,GAAG,OAAO;oBACnCjD,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACoN,SAAS,GAAG,mCAAmC;kBAChE,CAAE;kBACFa,MAAM,EAAG1M,CAAC,IAAK;oBACbA,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACgO,WAAW,GAAG,SAAS;oBACtCzM,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACwE,UAAU,GAAG,SAAS;oBACrCjD,CAAC,CAAC4I,MAAM,CAACnK,KAAK,CAACoN,SAAS,GAAG,MAAM;kBACnC;gBAAE;kBAAArM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXiK,QAAQ,EAAE,OAAO;gBACjB9M,cAAc,EAAE;cAClB,CAAE;cAAAG,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACbgK,OAAO,EAAE,QAAQ;kBACjB7I,UAAU,EAAE,SAAS;kBACrB7B,YAAY,EAAE,MAAM;kBACpBxB,MAAM,EAAE;gBACV,CAAE;gBAAAR,QAAA,gBACAjD,OAAA;kBACEkQ,KAAK,EAAEhJ,cAAe;kBACtBiJ,QAAQ,EAAGtM,CAAC,IAAKsD,iBAAiB,CAACtD,CAAC,CAAC4I,MAAM,CAACyD,KAAK,CAAE;kBACnD5N,KAAK,EAAE;oBACLqN,OAAO,EAAE,gBAAgB;oBACzBlM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,OAAO;oBACnB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBgN,OAAO,EAAE,MAAM;oBACfrL,MAAM,EAAE,SAAS;oBACjB6K,QAAQ,EAAE;kBACZ,CAAE;kBAAA3M,QAAA,gBAEFjD,OAAA;oBAAQkQ,KAAK,EAAC,EAAE;oBAAAjN,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvCsF,UAAU,CAAC9C,GAAG,CAACwK,QAAQ,iBACtBxQ,OAAA;oBAAmCkQ,KAAK,EAAEM,QAAQ,CAAClD,WAAW,CAACC,QAAQ,CAAC,CAAE;oBAAAtK,QAAA,EACvEuN,QAAQ,CAACC;kBAAI,GADHD,QAAQ,CAAClD,WAAW;oBAAAjK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAETxD,OAAA;kBACEkQ,KAAK,EAAE9I,gBAAiB;kBACxB+I,QAAQ,EAAGtM,CAAC,IAAKwD,mBAAmB,CAACxD,CAAC,CAAC4I,MAAM,CAACyD,KAAK,CAAE;kBACrD5N,KAAK,EAAE;oBACLqN,OAAO,EAAE,gBAAgB;oBACzBlM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,OAAO;oBACnB9D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBgN,OAAO,EAAE,MAAM;oBACfrL,MAAM,EAAE,SAAS;oBACjB6K,QAAQ,EAAE;kBACZ,CAAE;kBAAA3M,QAAA,gBAEFjD,OAAA;oBAAQkQ,KAAK,EAAC,EAAE;oBAAAjN,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnCmG,WAAW,CAAC3D,GAAG,CAAC0K,KAAK,iBACpB1Q,OAAA;oBAAoBkQ,KAAK,EAAEQ,KAAK,CAACnD,QAAQ,CAAC,CAAE;oBAAAtK,QAAA,GAAC,QACrC,EAACyN,KAAK;kBAAA,GADDA,KAAK;oBAAArN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EAER,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDpH,OAAA;kBACE8F,OAAO,EAAEA,CAAA,KAAM;oBACbyB,aAAa,CAAC,EAAE,CAAC;oBACjBJ,iBAAiB,CAAC,EAAE,CAAC;oBACrBE,mBAAmB,CAAC,EAAE,CAAC;kBACzB,CAAE;kBACF/E,KAAK,EAAE;oBACLqN,OAAO,EAAE,gBAAgB;oBACzBlM,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB6B,UAAU,EAAE,SAAS;oBACrB9D,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBqB,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBACFpE,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBAAA7D,QAAA,EACH;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAKNxD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBAEAjD,OAAA,CAAC5B,uBAAuB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG3BxD,OAAA;kBAAKsC,KAAK,EAAE;oBAAE6C,QAAQ,EAAE;kBAAW,CAAE;kBAAC,iBAAc,eAAe;kBAAAlC,QAAA,gBACjEjD,OAAA;oBACE8F,OAAO,EAAEA,CAAA,KAAMiC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;oBACtDxF,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,QAAQ;sBACbgK,OAAO,EAAE,cAAc;sBACvB7I,UAAU,EAAE,OAAO;sBACnBrD,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpBjC,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBqB,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE,eAAe;sBAC3B0K,SAAS,EAAE;oBACb,CAAE;oBACFjN,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACgO,WAAW,GAAG,SAAS;sBAC7CzM,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,mCAAmC;oBACvE,CAAE;oBACFhN,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACgO,WAAW,GAAG,SAAS;sBAC7CzM,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,8BAA8B;oBAClE,CAAE;oBAAAzM,QAAA,gBAEFjD,OAAA,CAACH,IAAI;sBAACmQ,IAAI,EAAE;oBAAG;sBAAA3M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClBxD,OAAA;sBAAAiD,QAAA,EAAO,CAAA+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2H,SAAS,KAAI;oBAAS;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClDxD,OAAA,CAACJ,WAAW;sBAACoQ,IAAI,EAAE,EAAG;sBAAC1N,KAAK,EAAE;wBAC5BuD,SAAS,EAAEiC,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;wBAC/D9C,UAAU,EAAE;sBACd;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EAGRsE,gBAAgB,iBACf9H,OAAA;oBAAKsC,KAAK,EAAE;sBACV6C,QAAQ,EAAE,UAAU;sBACpBI,GAAG,EAAE,MAAM;sBACXE,KAAK,EAAE,CAAC;sBACR9B,SAAS,EAAE,QAAQ;sBACnBmD,UAAU,EAAE,OAAO;sBACnBrD,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpByK,SAAS,EAAE,iCAAiC;sBAC5CE,QAAQ,EAAE,OAAO;sBACjBH,MAAM,EAAE,IAAI;sBACZrK,QAAQ,EAAE;oBACZ,CAAE;oBAAAnC,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVqN,OAAO,EAAE,cAAc;wBACvBH,YAAY,EAAE,mBAAmB;wBACjC1I,UAAU,EAAE;sBACd,CAAE;sBAAA7D,QAAA,gBACAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVc,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBV,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACC+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2H,SAAS,EAAC,GAAC,EAAC3H,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4H,QAAQ;sBAAA;wBAAAvN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVc,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,EACC+F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6H;sBAAK;wBAAAxN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEqN,OAAO,EAAE;sBAAW,CAAE;sBAAA1M,QAAA,gBAClCjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM;0BACbS,QAAQ,CAAC,oBAAoB,CAAC;0BAC9BwB,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFzF,KAAK,EAAE;0BACLsC,KAAK,EAAE,MAAM;0BACbhC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,SAAS;0BACdgK,OAAO,EAAE,cAAc;0BACvB7I,UAAU,EAAE,aAAa;0BACzBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,UAAU;0BACpB2B,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;wBAC9C,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;wBAClD,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAACvB,IAAI;0BAACuR,IAAI,EAAE;wBAAG;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,aAEpB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAETxD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM;0BACbwG,YAAY,CAAC,CAAC;0BACdvE,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFzF,KAAK,EAAE;0BACLsC,KAAK,EAAE,MAAM;0BACbhC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,SAAS;0BACdgK,OAAO,EAAE,cAAc;0BACvB7I,UAAU,EAAE,aAAa;0BACzBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,UAAU;0BACpB2B,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,SAAS;wBAC9C,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;wBAClD,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAACF,MAAM;0BAACkQ,IAAI,EAAE;wBAAG;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAEtB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTxD,OAAA;UAAKsC,KAAK,EAAE;YACViN,IAAI,EAAE,CAAC;YACP3M,OAAO,EAAE,MAAM;YACf+C,GAAG,EAAE,QAAQ;YACbgK,OAAO,EAAE,aAAa;YACtB7I,UAAU,EAAE,aAAa;YACzBjE,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACdkM,UAAU,EAAE;YACd,CAAE;YAAA7N,QAAA,eACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,OAAO;gBACnB7B,YAAY,EAAE,MAAM;gBACpBxB,MAAM,EAAE,mBAAmB;gBAC3B2B,QAAQ,EAAE,QAAQ;gBAClBD,QAAQ,EAAE,QAAQ;gBAClBI,GAAG,EAAE,MAAM;gBACXmK,SAAS,EAAE;cACb,CAAE;cAAAzM,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVqN,OAAO,EAAE,sBAAsB;kBAC/BH,YAAY,EAAE,mBAAmB;kBACjC1I,UAAU,EAAE;gBACd,CAAE;gBAAA7D,QAAA,gBACAjD,OAAA;kBAAKsC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB8C,GAAG,EAAE,SAAS;oBACdxC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAjD,OAAA,CAACrB,GAAG;oBAACqR,IAAI,EAAE,EAAG;oBAAC1N,KAAK,EAAE;sBAAEU,KAAK,EAAE;oBAAQ;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CxD,OAAA;oBAAIsC,KAAK,EAAE;sBACTuN,MAAM,EAAE,CAAC;sBACTzM,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNxD,OAAA;kBAAGsC,KAAK,EAAE;oBACRuN,MAAM,EAAE,CAAC;oBACTzM,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNxD,OAAA;gBAAKsC,KAAK,EAAE;kBAAEqN,OAAO,EAAE;gBAAU,CAAE;gBAAA1M,QAAA,EAChC2F,mBAAmB,CAACxE,MAAM,GAAG,CAAC,gBAC7BpE,OAAA,CAAAE,SAAA;kBAAA+C,QAAA,GACG2F,mBAAmB,CAACtE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAAC4F,YAAY,EAAEnH,KAAK,KAAK;oBAC5D;oBACA,IAAIsM,MAAgC;oBACpC,IAAInF,YAAY,CAACoF,QAAQ,EAAE;sBACzBD,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC9C,CAAC,MAAM;sBACL,MAAMnK,YAAY,GAAG,CAACgF,YAAY,CAACqF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;;sBAE5E;sBACA,MAAMC,cAAwD,GAAG;wBAC/D,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC7C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBAC7C,CAAC;sBAEDJ,MAAM,GAAGI,cAAc,CAACvK,YAAY,CAAC,IAAIuK,cAAc,CAAC,SAAS,CAAC;oBACpE;oBAEA,oBACEnR,OAAA;sBAEEsC,KAAK,EAAE;wBACLqN,OAAO,EAAE,SAAS;wBAClB7I,UAAU,EAAE,2BAA2BiK,MAAM,CAAC,CAAC,CAAC,QAAQA,MAAM,CAAC,CAAC,CAAC,QAAQ;wBACzE9L,YAAY,EAAE,MAAM;wBACpBxB,MAAM,EAAE,aAAasN,MAAM,CAAC,CAAC,CAAC,EAAE;wBAChC5N,YAAY,EAAE,SAAS;wBACvB4B,MAAM,EAAE,SAAS;wBACjBC,UAAU,EAAE;sBACd,CAAE;sBACFvC,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;wBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,cAAcqB,MAAM,CAAC,CAAC,CAAC,IAAI;sBAC/D,CAAE;sBACFrO,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;wBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,MAAM;sBAC1C,CAAE;sBACF5J,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC+D,YAAY,CAAE;sBAAA3I,QAAA,eAEnDjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,YAAY;0BACxB8C,GAAG,EAAE;wBACP,CAAE;wBAAA1C,QAAA,gBACAjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdiC,UAAU,EAAEiK,MAAM,CAAC,CAAC,CAAC;4BACrB9L,YAAY,EAAE,KAAK;4BACnBtB,SAAS,EAAE,QAAQ;4BACnBmN,UAAU,EAAE,CAAC;4BACbpB,SAAS,EAAE,aAAaqB,MAAM,CAAC,CAAC,CAAC;0BACnC;wBAAE;0BAAA1N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLxD,OAAA;0BAAKsC,KAAK,EAAE;4BAAEiN,IAAI,EAAE;0BAAE,CAAE;0BAAAtM,QAAA,gBACtBjD,OAAA;4BAAIsC,KAAK,EAAE;8BACTuN,MAAM,EAAE,cAAc;8BACtBzM,QAAQ,EAAE,UAAU;8BACpBM,UAAU,EAAE,KAAK;8BACjBV,KAAK,EAAE+N,MAAM,CAAC,CAAC,CAAC;8BAChBjB,UAAU,EAAE;4BACd,CAAE;4BAAA7M,QAAA,EACC2I,YAAY,CAACqB;0BAAK;4BAAA5J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACLxD,OAAA;4BAAGsC,KAAK,EAAE;8BACRuN,MAAM,EAAE,cAAc;8BACtBzM,QAAQ,EAAE,QAAQ;8BAClBJ,KAAK,EAAE+N,MAAM,CAAC,CAAC,CAAC;8BAChBjB,UAAU,EAAE;4BACd,CAAE;4BAAA7M,QAAA,EACC2I,YAAY,CAACwB,OAAO,CAAChJ,MAAM,GAAG,EAAE,GAC7B,GAAGwH,YAAY,CAACwB,OAAO,CAACgE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7CxF,YAAY,CAACwB;0BAAO;4BAAA/J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACJxD,OAAA;4BAAKsC,KAAK,EAAE;8BACVM,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE,QAAQ;8BACbvC,QAAQ,EAAE,SAAS;8BACnBJ,KAAK,EAAE+N,MAAM,CAAC,CAAC;4BACjB,CAAE;4BAAA9N,QAAA,gBACAjD,OAAA,CAACpB,QAAQ;8BAACoR,IAAI,EAAE;4BAAG;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACtBxD,OAAA;8BAAAiD,QAAA,EAAO,IAAI4K,IAAI,CAACjC,YAAY,CAACoD,UAAU,CAAC,CAACqC,kBAAkB,CAAC;4BAAC;8BAAAhO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAjEDoI,YAAY,CAACC,eAAe;sBAAAxI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkE9B,CAAC;kBAEV,CAAC,CAAC,EAGDoF,mBAAmB,CAACxE,MAAM,GAAG,CAAC,iBAC7BpE,OAAA;oBAAQsC,KAAK,EAAE;sBACbsC,KAAK,EAAE,MAAM;sBACb+K,OAAO,EAAE,SAAS;sBAClBlM,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpB6B,UAAU,EAAE,mDAAmD;sBAC/D9D,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBqB,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE;oBACd,CAAE;oBACFvC,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,mDAAmD;sBACtFjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACgO,WAAW,GAAG,SAAS;sBAC7CzM,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACtD,CAAE;oBACFnD,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,mDAAmD;sBACtFjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACgO,WAAW,GAAG,SAAS;sBAC7CzM,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACnD,CAAE;oBAAA5C,QAAA,GAAC,wBACW,EAAC2F,mBAAmB,CAACxE,MAAM,EAAC,oBAC1C;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,eACD,CAAC,gBAEHxD,OAAA;kBAAKsC,KAAK,EAAE;oBACVqN,OAAO,EAAE,WAAW;oBACpBzM,SAAS,EAAE,QAAQ;oBACnBF,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,gBACAjD,OAAA,CAACrB,GAAG;oBAACqR,IAAI,EAAE,EAAG;oBAAC1N,KAAK,EAAE;sBAAEa,YAAY,EAAE,QAAQ;sBAAEmO,OAAO,EAAE;oBAAI;kBAAE;oBAAAjO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClExD,OAAA;oBAAGsC,KAAK,EAAE;sBAAEuN,MAAM,EAAE,CAAC;sBAAEzM,QAAQ,EAAE;oBAAW,CAAE;oBAAAH,QAAA,EAAC;kBAE/C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAEiN,IAAI,EAAE,CAAC;cAAEK,QAAQ,EAAE;YAAE,CAAE;YAAA3M,QAAA,GAEpC,CAACzC,OAAO,IAAIgI,eAAe,kBAC1BxI,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,QAAQ;gBACxBD,UAAU,EAAE,QAAQ;gBACpB8M,OAAO,EAAE;cACX,CAAE;cAAA1M,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVsC,KAAK,EAAE,QAAQ;kBACfC,MAAM,EAAE,QAAQ;kBAChBpB,MAAM,EAAE,mBAAmB;kBAC3B8N,SAAS,EAAE,mBAAmB;kBAC9BtM,YAAY,EAAE,KAAK;kBACnBuM,SAAS,EAAE;gBACb;cAAE;gBAAAnO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA,CAAC9C,KAAK,IAAIgI,aAAa,kBACtB1I,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,wBAAwB;gBACpCrD,MAAM,EAAE,kCAAkC;gBAC1CwB,YAAY,EAAE,MAAM;gBACpB0K,OAAO,EAAE,MAAM;gBACfxM,YAAY,EAAE,QAAQ;gBACtBH,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,GACCvC,KAAK,iBAAIV,OAAA;gBAAAiD,QAAA,GAAK,iBAAe,EAACvC,KAAK;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CkF,aAAa,iBAAI1I,OAAA;gBAAAiD,QAAA,GAAK,YAAU,EAACyF,aAAa;cAAA;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACgI,eAAe,IAAIgG,oBAAoB,CAACpK,MAAM,KAAK,CAAC,IAAIqK,aAAa,CAACrK,MAAM,KAAK,CAAC,iBAC9FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVwE,UAAU,EAAE,0BAA0B;gBACtC7B,YAAY,EAAE,MAAM;gBACpB0K,OAAO,EAAE,MAAM;gBACfzM,SAAS,EAAE,QAAQ;gBACnBO,MAAM,EAAE,8BAA8B;gBACtCgO,cAAc,EAAE;cAClB,CAAE;cAAAxO,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVa,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,eACAjD,OAAA,CAACjB,MAAM;kBAACiR,IAAI,EAAE,EAAG;kBAAChN,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNxD,OAAA;gBAAIsC,KAAK,EAAE;kBACTU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBmM,MAAM,EAAE;gBACV,CAAE;gBAAA5M,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxD,OAAA;gBAAGsC,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChB6M,MAAM,EAAE;gBACV,CAAE;gBAAA5M,QAAA,EACCqE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,iDAAiD,GACjD;cAAmC;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACgI,eAAe,KAAKgG,oBAAoB,CAACpK,MAAM,GAAG,CAAC,IAAIqK,aAAa,CAACrK,MAAM,GAAG,CAAC,CAAC,iBAC5FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVyN,QAAQ,EAAE,QAAQ;gBAClBF,MAAM,EAAE,QAAQ;gBAChBjN,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBJ,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,GAECwL,aAAa,CAACrK,MAAM,GAAG,CAAC,iBACvBpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGwL,aAAa,CAACzI,GAAG,CAACoF,KAAK,iBACtBpL,OAAA;kBAEEsC,KAAK,EAAE;oBACLwE,UAAU,EAAE,2BAA2B;oBACvC7B,YAAY,EAAE,MAAM;oBACpB0K,OAAO,EAAE,QAAQ;oBACjBlM,MAAM,EAAE,8BAA8B;oBACtCgO,cAAc,EAAE,YAAY;oBAC5B/B,SAAS,EAAE,gCAAgC;oBAC3C1K,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBACFhN,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBAAAzM,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,MAAMgE,eAAe,GAAG,CAACmE,KAAK,CAAC6F,aAAa,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;sBAC7E,MAAMQ,YAAY,GAAG1K,mBAAmB,CAACC,eAAe,CAAC;sBACzD,MAAM0K,aAAa,GAAGD,YAAY,CAAC3K,IAAI;sBAEvC,oBACE/G,OAAA;wBAAKsC,KAAK,EAAE;0BACVsC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdI,YAAY,EAAE,MAAM;0BACpB6B,UAAU,EAAE4K,YAAY,CAAC5K,UAAU;0BACnClE,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE;wBAClB,CAAE;wBAAAG,QAAA,eACAjD,OAAA,CAAC2R,aAAa;0BAAC3B,IAAI,EAAE,EAAG;0BAAChN,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEiN,IAAI,EAAE;sBAAE,CAAE;sBAAAtM,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,EACC,CAAC,MAAM;0BACN,MAAMgE,eAAe,GAAG,CAACmE,KAAK,CAAC6F,aAAa,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;0BAC7E,MAAMQ,YAAY,GAAG1K,mBAAmB,CAACC,eAAe,CAAC;0BACzD,MAAM0K,aAAa,GAAGD,YAAY,CAAC3K,IAAI;0BAEvC,oBACE/G,OAAA;4BAAMsC,KAAK,EAAE;8BACXwE,UAAU,EAAE4K,YAAY,CAAC5K,UAAU;8BACnC9D,KAAK,EAAE,OAAO;8BACd2M,OAAO,EAAE,iBAAiB;8BAC1B1K,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAAC2R,aAAa;8BAAC3B,IAAI,EAAE,EAAG;8BAAChN,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCyD,eAAe;0BAAA;4BAAA5D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC;wBAEX,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAI4K,IAAI,CAACzC,KAAK,CAAC8C,UAAU,CAAC,CAACmD,kBAAkB,CAAC,OAAO,EAAE;0BACtDO,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA1O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACTuN,MAAM,EAAE,eAAe;sBACvB7M,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBoM,UAAU,EAAE;oBACd,CAAE;oBAAA7M,QAAA,EACCmI,KAAK,CAAC6B;kBAAK;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAGJ,CAAC,MAAM;oBACN;oBACA,MAAMwO,cAAwB,GAAG,EAAE;oBAEnC,IAAK5G,KAAK,CAASpH,MAAM,IAAKoH,KAAK,CAASpH,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;sBAC5DgH,KAAK,CAASpH,MAAM,CAACiO,OAAO,CAAEC,GAAQ,IAAK;wBAC1C,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAM7R,QAAQ,GAAGhC,WAAW,CAAC4T,GAAG,CAACC,SAAS,CAAC;0BAC3C,IAAI7R,QAAQ,EAAE;4BACZ0R,cAAc,CAACI,IAAI,CAAC9R,QAAQ,CAAC;0BAC/B;wBACF;sBACF,CAAC,CAAC;oBACJ;oBAEA,OAAO0R,cAAc,CAAC5N,MAAM,GAAG,CAAC,gBAC9BpE,OAAA;sBAAKsC,KAAK,EAAE;wBAAEa,YAAY,EAAE;sBAAO,CAAE;sBAAAF,QAAA,eACnCjD,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEgO,cAAc,CAAC5H,MAAM,CAACiI,OAAO,CAAc;wBACnDpO,SAAS,EAAEmH,KAAK,CAAC6B,KAAM;wBACvB/I,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB,MAAM6N,cAAc,GAAGN,cAAc,CAAC5H,MAAM,CAACiI,OAAO,CAAa;0BACjErI,YAAY,CAACsI,cAAc,EAAE7N,KAAK,CAAC;wBACrC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACJ,IAAI;kBACV,CAAC,EAAE,CAAC,EAEH4H,KAAK,CAACuC,WAAW,iBAChB3N,OAAA;oBAAGsC,KAAK,EAAE;sBACRuN,MAAM,EAAE,YAAY;sBACpB7M,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnB0M,UAAU,EAAE;oBACd,CAAE;oBAAA7M,QAAA,EACCmI,KAAK,CAACuC;kBAAW;oBAAAtK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACJ,eAGDxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/ByP,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAAtO,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACX3C,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,gBACAjD,OAAA;wBAAMsC,KAAK,EAAE;0BAAEM,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAE8C,GAAG,EAAE;wBAAU,CAAE;wBAAA1C,QAAA,gBACrEjD,OAAA,CAAChB,MAAM;0BAACgR,IAAI,EAAE,EAAG;0BAAChN,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACN4H,KAAK,CAACoH,QAAQ,IAAIpH,KAAK,CAACoH,QAAQ,KAAKpH,KAAK,CAAC8C,UAAU,iBACpDlO,OAAA;wBAAAiD,QAAA,GAAM,QACE,EAAC,IAAI4K,IAAI,CAACzC,KAAK,CAACoH,QAAQ,CAAC,CAACnB,kBAAkB,CAAC,CAAC;sBAAA;wBAAAhO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNxD,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjB4O,UAAU,EAAE,MAAM;wBAClBhB,SAAS,EAAE,8BAA8B;wBACzC3O,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE;sBACP,CAAE;sBAAA1C,QAAA,gBAEAjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAMmG,wBAAwB,CAACb,KAAK,CAAE;wBAC/C9I,KAAK,EAAE;0BACLM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbmB,UAAU,EAAE,MAAM;0BAClBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAGoI,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG,SAAS;0BAC9DnH,MAAM,EAAE,SAAS;0BACjB4K,OAAO,EAAE,QAAQ;0BACjB1K,YAAY,EAAE,KAAK;0BACnBD,UAAU,EAAE,eAAe;0BAC3B5B,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE;wBACd,CAAE;wBACFjB,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,qBAAqB;wBAC1D,CAAE;wBACFpE,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,MAAM;wBAC3C,CAAE;wBAAA7D,QAAA,gBAEFjD,OAAA,CAAClB,KAAK;0BACJkR,IAAI,EAAE,EAAG;0BACTyC,IAAI,EAAGrH,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG;wBAAO;0BAAA7I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,eACFxD,OAAA;0BAAAiD,QAAA,EAAQmI,KAAK,CAASiB,cAAc,IAAI;wBAAC;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,EAGP4H,KAAK,CAASsH,cAAc,iBAC5B1S,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM6B,uBAAuB,CACpCD,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;wBACFhJ,KAAK,EAAE;0BACLM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbmB,UAAU,EAAE,MAAM;0BAClBrD,MAAM,EAAE,MAAM;0BACdT,KAAK,EAAE0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;0BACzEvG,MAAM,EAAE,SAAS;0BACjB4K,OAAO,EAAE,QAAQ;0BACjB1K,YAAY,EAAE,KAAK;0BACnBD,UAAU,EAAE,eAAe;0BAC3B5B,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE;wBACd,CAAE;wBACFjB,YAAY,EAAGoB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,qBAAqB;0BACxDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClG,CAAE;wBACF5I,YAAY,EAAGmB,CAAC,IAAK;0BACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,MAAM;0BACzCjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG0E,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClG,CAAE;wBAAArI,QAAA,gBAEFjD,OAAA,CAACnB,aAAa;0BAACmR,IAAI,EAAE;wBAAG;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3BxD,OAAA;0BAAAiD,QAAA,EAAQmI,KAAK,CAASuH,aAAa,IAAI;wBAAC;0BAAAtP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,EAGLkE,oBAAoB,KAAK0D,KAAK,CAACE,WAAW,IAAKF,KAAK,CAASsH,cAAc,iBAC1E1S,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjB4O,UAAU,EAAE,MAAM;wBAClBhB,SAAS,EAAE;sBACb,CAAE;sBAAAtO,QAAA,eACAjD,OAAA,CAAC7B,cAAc;wBACbyU,UAAU,EAAExH,KAAK,CAACE,WAAY;wBAC9BuH,aAAa,EAAGzH,KAAK,CAASsH,cAAe;wBAC7CI,aAAa,EAAE9J,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAG;wBAC/BgH,eAAe,EAAC;sBAAS;wBAAA1P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAnQD,SAAS4H,KAAK,CAACE,WAAW,EAAE;kBAAAjI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoQ9B,CACN;cAAC,gBACF,CACH,EAGAgL,oBAAoB,CAACpK,MAAM,GAAG,CAAC,iBAC9BpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGuL,oBAAoB,CAACxI,GAAG,CAAC4F,YAAY,iBACpC5L,OAAA;kBAEE+L,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;kBACnDtJ,SAAS,EAAEiE,kBAAkB,IAAIE,YAAY,KAAK,gBAAgBkF,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;kBAC9IvJ,KAAK,EAAE;oBACLwE,UAAU,EAAE,2BAA2B;oBACvC7B,YAAY,EAAE,MAAM;oBACpB0K,OAAO,EAAE,QAAQ;oBACjBlM,MAAM,EAAEmI,YAAY,CAACoF,QAAQ,GACzB,kCAAkC,GAClC,8BAA8B;oBAClCS,cAAc,EAAE,YAAY;oBAC5B/B,SAAS,EAAE9D,YAAY,CAACoF,QAAQ,GAC5B,oCAAoC,GACpC,gCAAgC;oBACpChM,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG9D,YAAY,CAACoF,QAAQ,GACnD,oCAAoC,GACpC,gCAAgC;kBACtC,CAAE;kBACFtO,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACoN,SAAS,GAAG9D,YAAY,CAACoF,QAAQ,GACnD,oCAAoC,GACpC,gCAAgC;kBACtC,CAAE;kBAAA/N,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,IAAI2I,YAAY,CAACoF,QAAQ,EAAE;wBACzB,oBACEhR,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdiC,UAAU,EAAE,mDAAmD;4BAC/D7B,YAAY,EAAE,MAAM;4BACpBrC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE,QAAQ;4BACxBgO,UAAU,EAAE;0BACd,CAAE;0BAAA7N,QAAA,eACAjD,OAAA,CAACZ,aAAa;4BAAC4Q,IAAI,EAAE,EAAG;4BAAChN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAEV,CAAC,MAAM;wBACL,MAAMoD,YAAY,GAAG,CAACgF,YAAY,CAACqF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;wBAC5E,MAAM8B,aAAa,GAAGrM,gBAAgB,CAACC,YAAY,CAAC;wBACpD,MAAM+K,aAAa,GAAGqB,aAAa,CAACjM,IAAI;wBAExC,oBACE/G,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdI,YAAY,EAAE,MAAM;4BACpB6B,UAAU,EAAEkM,aAAa,CAAClM,UAAU;4BACpClE,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE;0BAClB,CAAE;0BAAAG,QAAA,eACAjD,OAAA,CAAC2R,aAAa;4BAAC3B,IAAI,EAAE,EAAG;4BAAChN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAEV;oBACF,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEiN,IAAI,EAAE;sBAAE,CAAE;sBAAAtM,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,GACC,CAAC,MAAM;0BACN,IAAI2I,YAAY,CAACoF,QAAQ,EAAE;4BACzB,oBACEhR,OAAA;8BAAMsC,KAAK,EAAE;gCACXwE,UAAU,EAAE,mDAAmD;gCAC/D9D,KAAK,EAAE,OAAO;gCACdI,QAAQ,EAAE,SAAS;gCACnBM,UAAU,EAAE,KAAK;gCACjBiM,OAAO,EAAE,iBAAiB;gCAC1B1K,YAAY,EAAE,MAAM;gCACpBgO,aAAa,EAAE,WAAW;gCAC1BC,aAAa,EAAE,OAAO;gCACtBtQ,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB8C,GAAG,EAAE;8BACP,CAAE;8BAAA1C,QAAA,gBACAjD,OAAA,CAACZ,aAAa;gCAAC4Q,IAAI,EAAE,EAAG;gCAAChN,KAAK,EAAC;8BAAO;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,SAE3C;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAEX,CAAC,MAAM;4BACL,MAAMoD,YAAY,GAAG,CAACgF,YAAY,CAACqF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;4BAC5E,MAAM8B,aAAa,GAAGrM,gBAAgB,CAACC,YAAY,CAAC;4BACpD,MAAM+K,aAAa,GAAGqB,aAAa,CAACjM,IAAI;4BAExC,oBACE/G,OAAA;8BAAMsC,KAAK,EAAE;gCACXwE,UAAU,EAAEkM,aAAa,CAAClM,UAAU;gCACpC9D,KAAK,EAAE,OAAO;gCACd2M,OAAO,EAAE,iBAAiB;gCAC1B1K,YAAY,EAAE,MAAM;gCACpB7B,QAAQ,EAAE,SAAS;gCACnBM,UAAU,EAAE,KAAK;gCACjBd,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB8C,GAAG,EAAE;8BACP,CAAE;8BAAA1C,QAAA,gBACAjD,OAAA,CAAC2R,aAAa;gCAAC3B,IAAI,EAAE,EAAG;gCAAChN,KAAK,EAAC;8BAAO;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACxCoD,YAAY;4BAAA;8BAAAvD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC;0BAEX;wBACF,CAAC,EAAE,CAAC,EACHoI,YAAY,CAACtB,SAAS,iBACrBtK,OAAA;0BAAMsC,KAAK,EAAE;4BACXwE,UAAU,EAAE,mDAAmD;4BAC/D9D,KAAK,EAAE,OAAO;4BACd2M,OAAO,EAAE,iBAAiB;4BAC1B1K,YAAY,EAAE,MAAM;4BACpB7B,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjBd,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE;0BACP,CAAE;0BAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;4BAACqR,IAAI,EAAE,EAAG;4BAAChN,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,UAEjC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,GAAC,KACE,EAAC2I,YAAY,CAACuH,WAAW,EAAC,UAAG,EAACvH,YAAY,CAACwH,YAAY,GAAG,IAAIvF,IAAI,CAACjC,YAAY,CAACwH,YAAY,CAAC,CAAC/B,kBAAkB,CAAC,OAAO,EAAE;0BAC3HO,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE,SAAS;0BACdsB,IAAI,EAAE,SAAS;0BACfC,MAAM,EAAE;wBACV,CAAC,CAAC,GAAG,cAAc;sBAAA;wBAAAjQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACTuN,MAAM,EAAE,eAAe;sBACvB7M,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBoM,UAAU,EAAE;oBACd,CAAE;oBAAA7M,QAAA,EACC2I,YAAY,CAACqB;kBAAK;oBAAA5J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,EAGJ,CAAC,MAAM;oBACN;oBACA,MAAMyG,SAAmB,GAAG,EAAE;;oBAE9B;oBACA,IAAI2B,YAAY,CAACJ,WAAW,IAAII,YAAY,CAACJ,WAAW,CAACpH,MAAM,GAAG,CAAC,EAAE;sBACnEwH,YAAY,CAACJ,WAAW,CAACyG,OAAO,CAAEC,GAA2B,IAAK;wBAChE,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAMtR,OAAO,GAAGvC,WAAW,CAAC4T,GAAG,CAACC,SAAS,CAAC;0BAC1C,IAAItR,OAAO,EAAE;4BACXoJ,SAAS,CAACmI,IAAI,CAACvR,OAAO,CAAC;0BACzB;wBACF;sBACF,CAAC,CAAC;oBACJ;;oBAEA;oBACA,IAAIoJ,SAAS,CAAC7F,MAAM,KAAK,CAAC,KAAKwH,YAAY,CAAC2H,SAAS,IAAI3H,YAAY,CAAC4H,UAAU,CAAC,EAAE;sBACjF,MAAMC,SAAS,GAAGnV,WAAW,CAACsN,YAAY,CAAC2H,SAAS,IAAI3H,YAAY,CAAC4H,UAAU,CAAC;sBAChF,IAAIC,SAAS,EAAE;wBACbxJ,SAAS,CAACmI,IAAI,CAACqB,SAAS,CAAC;sBAC3B;oBACF;oBAEA,OAAOxJ,SAAS,CAAC7F,MAAM,GAAG,CAAC,gBACzBpE,OAAA,CAAC+D,oBAAoB;sBACnBC,MAAM,EAAEiG,SAAS,CAACG,MAAM,CAACiI,OAAO,CAAc;sBAC9CpO,SAAS,EAAE2H,YAAY,CAACqB,KAAM;sBAC9B/I,UAAU,EAAE,CAAE;sBACdC,YAAY,EAAGM,KAAK,IAAK;wBACvB,MAAM6N,cAAc,GAAGrI,SAAS,CAACG,MAAM,CAACiI,OAAO,CAAa;wBAC5DrI,YAAY,CAACsI,cAAc,EAAE7N,KAAK,CAAC;sBACrC;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GACA,IAAI;kBACV,CAAC,EAAE,CAAC,eAEJxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVuN,MAAM,EAAE,cAAc;sBACtB7M,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnB0M,UAAU,EAAE;oBACd,CAAE;oBACF4D,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/H,YAAY,CAACwB;oBAAQ;kBAAE;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eAGFxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/ByP,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAAtO,QAAA,eACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE;sBACP,CAAE;sBAAA1C,QAAA,gBAEAjD,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM6F,gBAAgB,CAACC,YAAY,CAAE;wBAC9CtJ,KAAK,EAAE;0BACLwE,UAAU,EAAE8E,YAAY,CAACE,aAAa,GAAG,wBAAwB,GAAG,aAAa;0BACjF9I,KAAK,EAAE4I,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;0BACzDrI,MAAM,EAAE,MAAM;0BACdwB,YAAY,EAAE,KAAK;0BACnB0K,OAAO,EAAE,aAAa;0BACtBvM,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBqB,MAAM,EAAE,SAAS;0BACjBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbX,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnB,IAAI,CAAC+H,YAAY,CAACE,aAAa,EAAE;4BAC/BjI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,wBAAwB;4BAC3DjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBACFN,YAAY,EAAGmB,CAAC,IAAK;0BACnB,IAAI,CAAC+H,YAAY,CAACE,aAAa,EAAE;4BAC/BjI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;4BAChDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBAAAC,QAAA,gBAEFjD,OAAA,CAAClB,KAAK;0BACJkR,IAAI,EAAE,EAAG;0BACThN,KAAK,EAAE4I,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAU;0BAC1D2G,IAAI,EAAE7G,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;wBAAO;0BAAAzI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC,eACFxD,OAAA;0BAAAiD,QAAA,EAAO2I,YAAY,CAACS,cAAc,IAAI;wBAAC;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC,EAGRoI,YAAY,CAAC8G,cAAc,iBAC1B1S,OAAA;wBACE8F,OAAO,EAAEA,CAAA,KAAM2B,eAAe,CAC5BD,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;wBACFvJ,KAAK,EAAE;0BACLwE,UAAU,EAAEU,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,yBAAyB,GAAG,aAAa;0BACrG7I,KAAK,EAAEwE,YAAY,KAAKoE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;0BAC5EpI,MAAM,EAAE,MAAM;0BACdwB,YAAY,EAAE,KAAK;0BACnB0K,OAAO,EAAE,aAAa;0BACtBvM,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBqB,MAAM,EAAE,SAAS;0BACjBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbX,UAAU,EAAE;wBACd,CAAE;wBACFvC,YAAY,EAAGoB,CAAC,IAAK;0BACnB,IAAI2D,YAAY,KAAKoE,YAAY,CAACC,eAAe,EAAE;4BACjDhI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,yBAAyB;4BAC5DjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBACFN,YAAY,EAAGmB,CAAC,IAAK;0BACnB,IAAI2D,YAAY,KAAKoE,YAAY,CAACC,eAAe,EAAE;4BACjDhI,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwE,UAAU,GAAG,aAAa;4BAChDjD,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;0BACzC;wBACF,CAAE;wBAAAC,QAAA,gBAEFjD,OAAA,CAACnB,aAAa;0BAACmR,IAAI,EAAE,EAAG;0BAAChN,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3CxD,OAAA;0BAAAiD,QAAA,EAAO2I,YAAY,CAAC+G,aAAa,IAAI;wBAAC;0BAAAtP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGH,CAAC,EAGLgE,YAAY,KAAKoE,YAAY,CAACC,eAAe,IAAID,YAAY,CAAC8G,cAAc,iBAC3E1S,OAAA;oBAAKsC,KAAK,EAAE;sBACVqB,SAAS,EAAE,MAAM;sBACjB4O,UAAU,EAAE,MAAM;sBAClBhB,SAAS,EAAE;oBACb,CAAE;oBAAAtO,QAAA,eAIAjD,OAAA,CAAC7B,cAAc;sBACbyV,cAAc,EAAEhI,YAAY,CAACC,eAAgB;sBAC7CgH,aAAa,EAAEjH,YAAY,CAAC8G,cAAe;sBAC3CI,aAAa,EAAE9J,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+C,EAAG;sBAC/BgH,eAAe,EAAC;oBAAS;sBAAA1P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA,GApUI,gBAAgBoI,YAAY,CAACC,eAAe,EAAE;kBAAAxI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqUhD,CACN;cAAC,gBACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLoE,kBAAkB,iBACjB5H,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,OAAO;UACjBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB2M,MAAM,EAAE,IAAI;UACZE,OAAO,EAAE;QACX,CAAE;QACF7J,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC,IAAI,CAAE;QAAA5E,QAAA,eAEzCjD,OAAA;UAAKsC,KAAK,EAAE;YACVS,eAAe,EAAE,OAAO;YACxBkC,YAAY,EAAE,MAAM;YACpB8K,QAAQ,EAAE,OAAO;YACjBnL,KAAK,EAAE,MAAM;YACbiP,SAAS,EAAE,MAAM;YACjBzO,QAAQ,EAAE,MAAM;YAChBsK,SAAS,EAAE;UACb,CAAE;UACF5J,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACiQ,eAAe,CAAC,CAAE;UAAA7Q,QAAA,gBAGlCjD,OAAA;YAAKsC,KAAK,EAAE;cACVqN,OAAO,EAAE,QAAQ;cACjBH,YAAY,EAAE,mBAAmB;cACjC5M,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;gBAACqR,IAAI,EAAE,EAAG;gBAAC1N,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAU;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CxD,OAAA;gBAAIsC,KAAK,EAAE;kBACTuN,MAAM,EAAE,CAAC;kBACTzM,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNxD,OAAA;cACE8F,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAAC,IAAI,CAAE;cAC3CvF,KAAK,EAAE;gBACLwE,UAAU,EAAE,MAAM;gBAClBrD,MAAM,EAAE,MAAM;gBACdL,QAAQ,EAAE,QAAQ;gBAClBJ,KAAK,EAAE,SAAS;gBAChB+B,MAAM,EAAE,SAAS;gBACjB4K,OAAO,EAAE,SAAS;gBAClB1K,YAAY,EAAE,KAAK;gBACnBD,UAAU,EAAE;cACd,CAAE;cACFvC,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cACFN,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cAAAC,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAEqN,OAAO,EAAE;YAAS,CAAE;YAAA1M,QAAA,gBAChCjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,SAAS;gBACdxC,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,GACC,CAAC,MAAM;gBACN,IAAI2E,kBAAkB,CAACoJ,QAAQ,EAAE;kBAC/B,oBACEhR,OAAA;oBAAMsC,KAAK,EAAE;sBACXwE,UAAU,EAAE,mDAAmD;sBAC/D9D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBiM,OAAO,EAAE,iBAAiB;sBAC1B1K,YAAY,EAAE,MAAM;sBACpBgO,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtBtQ,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAACZ,aAAa;sBAAC4Q,IAAI,EAAE,EAAG;sBAAChN,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAEX,CAAC,MAAM;kBACL,MAAMoD,YAAY,GAAG,CAACgB,kBAAkB,CAACqJ,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAClF,MAAM8B,aAAa,GAAGrM,gBAAgB,CAACC,YAAY,CAAC;kBACpD,MAAM+K,aAAa,GAAGqB,aAAa,CAACjM,IAAI;kBAExC,oBACE/G,OAAA;oBAAMsC,KAAK,EAAE;sBACXwE,UAAU,EAAEkM,aAAa,CAAClM,UAAU;sBACpC9D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBiM,OAAO,EAAE,iBAAiB;sBAC1B1K,YAAY,EAAE,MAAM;sBACpBgO,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtBtQ,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAAC2R,aAAa;sBAAC3B,IAAI,EAAE,EAAG;sBAAChN,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACxCoD,YAAY;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAEX;cACF,CAAC,EAAE,CAAC,eAEJxD,OAAA;gBAAMsC,KAAK,EAAE;kBACXwE,UAAU,EAAE,mDAAmD;kBAC/D9D,KAAK,EAAE,OAAO;kBACd2M,OAAO,EAAE,iBAAiB;kBAC1B1K,YAAY,EAAE,MAAM;kBACpB7B,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBd,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAACrB,GAAG;kBAACqR,IAAI,EAAE;gBAAG;kBAAA3M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxD,OAAA;cAAIsC,KAAK,EAAE;gBACTuN,MAAM,EAAE,YAAY;gBACpBzM,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE,SAAS;gBAChB8M,UAAU,EAAE;cACd,CAAE;cAAA7M,QAAA,EACC2E,kBAAkB,CAACqF;YAAK;cAAA5J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAELxD,OAAA;cAAKsC,KAAK,EAAE;gBACVU,KAAK,EAAE,SAAS;gBAChBI,QAAQ,EAAE,MAAM;gBAChB0M,UAAU,EAAE,KAAK;gBACjB3M,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,EACC2E,kBAAkB,CAACwF;YAAO;cAAA/J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EAGLoE,kBAAkB,CAAC4D,WAAW,IAAI5D,kBAAkB,CAAC4D,WAAW,CAACpH,MAAM,GAAG,CAAC,iBAC1EpE,OAAA;cAAKsC,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,eACrCjD,OAAA,CAAC+D,oBAAoB;gBACnBC,MAAM,EAAE4D,kBAAkB,CAAC4D,WAAW,CAACxF,GAAG,CAAEkM,GAAQ,IAAK5T,WAAW,CAAC4T,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC/H,MAAM,CAACiI,OAAO,CAAE;gBACrGpO,SAAS,EAAE2D,kBAAkB,CAACqF,KAAM;gBACpC9I,YAAY,EAAGM,KAAK,IAAK;kBACvB,MAAMwF,SAAS,GAAGrC,kBAAkB,CAAC4D,WAAW,CAACxF,GAAG,CAAEkM,GAAQ,IAAK5T,WAAW,CAAC4T,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC/H,MAAM,CAACiI,OAAO,CAAC;kBAC9GrI,YAAY,CAACC,SAAS,EAAExF,KAAK,CAAC;gBAChC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXvC,QAAQ,EAAE,UAAU;gBACpBJ,KAAK,EAAE,SAAS;gBAChBuP,UAAU,EAAE,MAAM;gBAClBhB,SAAS,EAAE;cACb,CAAE;cAAAtO,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAACpB,QAAQ;kBAACoR,IAAI,EAAE;gBAAG;kBAAA3M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtBxD,OAAA;kBAAAiD,QAAA,GAAM,aAAW,EAAC,IAAI4K,IAAI,CAACjG,kBAAkB,CAACoH,UAAU,CAAC,CAACqC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAhO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,EACLoE,kBAAkB,CAACuL,WAAW,iBAC7BnT,OAAA;gBAAAiD,QAAA,GAAK,MACC,EAAC2E,kBAAkB,CAACuL,WAAW;cAAA;gBAAA9P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxD,OAAA,CAAC3B,aAAa;QACZ2F,MAAM,EAAEkE,cAAe;QACvBgC,YAAY,EAAE9B,oBAAqB;QACnC2L,MAAM,EAAE/L,YAAa;QACrBgM,OAAO,EAAEA,CAAA,KAAM/L,eAAe,CAAC,KAAK,CAAE;QACtChE,SAAS,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC8C,GAAA,CAhzDID,eAAyB;EAAA,QACZxI,WAAW,EAGiCG,qBAAqB,EAwF3DD,aAAa,EAGEE,cAAc,EAUhDH,gBAAgB;AAAA;AAAAmW,GAAA,GAzGhB5N,eAAyB;AAkzD/B,eAAeA,eAAe;AAAC,IAAAvC,EAAA,EAAAsC,GAAA,EAAA6N,GAAA;AAAAC,YAAA,CAAApQ,EAAA;AAAAoQ,YAAA,CAAA9N,GAAA;AAAA8N,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}