{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 8h20\",\n  key: \"d11cs7\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M6 16h12\",\n  key: \"u522kt\"\n}]];\nconst Dock = createLucideIcon(\"dock\", __iconNode);\nexport { __iconNode, Dock as default };\n//# sourceMappingURL=dock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}