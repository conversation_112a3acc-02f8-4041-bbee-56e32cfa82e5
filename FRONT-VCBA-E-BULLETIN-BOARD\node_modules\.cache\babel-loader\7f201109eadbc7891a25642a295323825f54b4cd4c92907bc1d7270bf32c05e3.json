{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n  key: \"vwvbt9\"\n}], [\"path\", {\n  d: \"M15 8h6\",\n  key: \"8ybuxh\"\n}], [\"path\", {\n  d: \"M18 5v6\",\n  key: \"g5ayrv\"\n}], [\"path\", {\n  d: \"M20.002 14.464a9 9 0 0 0 .738.863A1 1 0 0 1 20 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 8.75-5.332\",\n  key: \"1abcvy\"\n}]];\nconst BellPlus = createLucideIcon(\"bell-plus\", __iconNode);\nexport { __iconNode, BellPlus as default };\n//# sourceMappingURL=bell-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}