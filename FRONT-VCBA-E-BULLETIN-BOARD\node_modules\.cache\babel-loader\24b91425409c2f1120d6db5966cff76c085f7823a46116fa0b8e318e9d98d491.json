{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"circle\", {\n  cx: \"11.5\",\n  cy: \"14.5\",\n  r: \"2.5\",\n  key: \"1bq0ko\"\n}], [\"path\", {\n  d: \"M13.3 16.3 15 18\",\n  key: \"2quom7\"\n}]];\nconst FileSearch2 = createLucideIcon(\"file-search-2\", __iconNode);\nexport { __iconNode, FileSearch2 as default };\n//# sourceMappingURL=file-search-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}