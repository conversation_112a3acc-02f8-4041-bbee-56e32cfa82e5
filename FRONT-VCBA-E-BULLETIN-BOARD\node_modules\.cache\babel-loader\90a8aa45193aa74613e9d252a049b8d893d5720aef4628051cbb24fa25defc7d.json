{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{X,Calendar,User,Tag,Eye,MessageCircle,Heart,Pin,AlertTriangle}from'lucide-react';import{getImageUrl}from'../../../config/constants';// Custom hook for CORS-safe image loading\nimport{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}console.log('🔄 Fetching image via CORS-safe method:',fullUrl);// Fetch image as blob to bypass CORS restrictions\nconst response=await fetch(fullUrl,{method:'GET',headers:{'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);setImageUrl(objectUrl);console.log('✅ Image loaded successfully via fetch');}catch(err){console.error('❌ Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// Facebook-style image gallery component\nconst FacebookImageGallery=_ref=>{let{images,altPrefix,maxVisible=4,onImageClick}=_ref;// Call hooks at the top level for maximum possible images (Rules of Hooks compliance)\nconst imageLoader0=useImageLoader((images===null||images===void 0?void 0:images[0])||null);const imageLoader1=useImageLoader((images===null||images===void 0?void 0:images[1])||null);const imageLoader2=useImageLoader((images===null||images===void 0?void 0:images[2])||null);const imageLoader3=useImageLoader((images===null||images===void 0?void 0:images[3])||null);// Create array of results for easy access\nconst imageLoaderResults=[imageLoader0,imageLoader1,imageLoader2,imageLoader3];if(!images||images.length===0)return null;const visibleImages=images.slice(0,maxVisible);const remainingCount=images.length-maxVisible;const getImageStyle=(index,total)=>{const baseStyle={width:'100%',height:'100%',objectFit:'cover',cursor:'pointer',transition:'transform 0.2s ease, filter 0.2s ease',borderRadius:index===0&&total===1?'12px':'8px'};return baseStyle;};const getContainerStyle=(index,total)=>{const baseStyle={position:'relative',overflow:'hidden',backgroundColor:'#f3f4f6'};if(total===1){return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'400px',borderRadius:'12px'});}if(total===2){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'300px',borderRadius:'8px'});}if(total===3){if(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'60%',height:'300px',borderRadius:'8px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'148px',borderRadius:'8px'});}}// 4+ images\nif(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'60%',height:'300px',borderRadius:'8px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'96px',borderRadius:'8px'});}};const renderOverlay=(index,count)=>{if(index===maxVisible-1&&count>0){return/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'1.5rem',fontWeight:'600',borderRadius:'8px'},children:[\"+\",count]});}return null;};// Get the first image loader result\nconst firstImageResult=imageLoaderResults[0];const{imageUrl:firstImageUrl,loading:firstImageLoading,error:firstImageError}=firstImageResult;return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'4px',width:'100%',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(0,visibleImages.length),children:[firstImageLoading&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',height:'100%',color:'#6b7280'},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'2rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Loading image...\"})]})}),firstImageError&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',height:'100%',background:'#f3f4f6',color:'#6b7280',borderRadius:'12px',fontSize:'0.875rem',border:'2px dashed #d1d5db'},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'2rem'},children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500'},children:\"Image unavailable\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',marginTop:'0.25rem',color:'#9ca3af'},children:firstImageError})]})}),firstImageUrl&&!firstImageLoading&&!firstImageError&&/*#__PURE__*/_jsx(\"img\",{src:firstImageUrl,alt:\"\".concat(altPrefix,\" - Image 1\"),style:getImageStyle(0,visibleImages.length),onLoad:e=>{e.currentTarget.style.opacity='1';console.log('✅ Image rendered successfully via CORS-safe method');},onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(0)})]}),visibleImages.length>1&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'4px',width:visibleImages.length===2?'50%':'40%'},children:visibleImages.slice(1).map((_,idx)=>{const actualIndex=idx+1;// Use the pre-loaded image loader result instead of calling the hook\nconst{imageUrl,loading,error}=imageLoaderResults[actualIndex];return/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(actualIndex,visibleImages.length),children:[loading&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',height:'100%',color:'#6b7280'},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem'},children:\"Loading...\"})]})}),error&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',height:'100%',background:'#f3f4f6',color:'#6b7280',borderRadius:'8px',fontSize:'0.75rem',border:'2px dashed #d1d5db'},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.25rem',fontSize:'1.5rem'},children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Error\"})]})}),imageUrl&&!loading&&!error&&/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:\"\".concat(altPrefix,\" - Image \").concat(actualIndex+1),style:getImageStyle(actualIndex,visibleImages.length),onLoad:e=>{e.currentTarget.style.opacity='1';console.log('✅ Image rendered successfully via CORS-safe method');},onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),renderOverlay(actualIndex,remainingCount),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(actualIndex)})]},actualIndex);})})]});};const AnnouncementViewDialog=_ref2=>{let{isOpen,onClose,announcement}=_ref2;if(!isOpen||!announcement)return null;const formatDate=dateString=>{if(!dateString)return'Not set';try{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});}catch(error){return'Invalid date';}};const getStatusColor=status=>{switch(status){case'published':return'#10b981';case'draft':return'#6b7280';case'scheduled':return'#f59e0b';case'archived':return'#ef4444';default:return'#6b7280';}};const getStatusText=status=>{return status.charAt(0).toUpperCase()+status.slice(1);};return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},onClick:onClose,children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'12px',width:'100%',maxWidth:'800px',maxHeight:'90vh',overflow:'hidden',boxShadow:'0 25px 50px -12px rgba(0, 0, 0, 0.25)'},onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',borderBottom:'1px solid #e5e7eb',display:'flex',justifyContent:'space-between',alignItems:'flex-start'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,marginRight:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{fontSize:'1.5rem',fontWeight:'600',color:'#111827',margin:0,marginRight:'1rem'},children:announcement.title}),/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:getStatusColor(announcement.status),color:'white',padding:'0.25rem 0.75rem',borderRadius:'9999px',fontSize:'0.75rem',fontWeight:'500',textTransform:'uppercase'},children:getStatusText(announcement.status)})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem',flexWrap:'wrap'},children:[announcement.is_pinned&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',backgroundColor:'#fef3c7',color:'#92400e',padding:'0.25rem 0.5rem',borderRadius:'6px',fontSize:'0.75rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(Pin,{size:12}),\"Pinned\"]}),announcement.is_alert&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',backgroundColor:'#fee2e2',color:'#dc2626',padding:'0.25rem 0.5rem',borderRadius:'6px',fontSize:'0.75rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:12}),\"Alert\"]})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,style:{padding:'0.5rem',backgroundColor:'transparent',border:'none',borderRadius:'6px',cursor:'pointer',color:'#6b7280',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(X,{size:20})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',maxHeight:'calc(90vh - 200px)',overflowY:'auto'},children:[(announcement.attachments&&announcement.attachments.length>0||announcement.images&&announcement.images.length>0||announcement.image_url||announcement.image_path)&&/*#__PURE__*/_jsx(FacebookImageGallery,{images:// Use attachments/images array if available, otherwise fallback to single image\nannouncement.attachments&&announcement.attachments.length>0?announcement.attachments.map(att=>att.file_path):announcement.images&&announcement.images.length>0?announcement.images.map(img=>img.file_path):[announcement.image_url||announcement.image_path].filter(Boolean),altPrefix:announcement.title,maxVisible:4,onImageClick:index=>{console.log(\"Clicked image \".concat(index+1,\" for announcement: \").concat(announcement.title));// Future: Open image viewer/lightbox\n}}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'1rem',lineHeight:'1.6',color:'#374151',marginBottom:'2rem',whiteSpace:'pre-wrap'},children:announcement.content}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(250px, 1fr))',gap:'1rem',padding:'1.5rem',backgroundColor:'#f9fafb',borderRadius:'8px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(User,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Author\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:announcement.author_name||'Unknown'})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Tag,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Category\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:announcement.category_name||'Uncategorized'})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Created\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:formatDate(announcement.created_at)})]})]}),announcement.published_at&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Published\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:formatDate(announcement.published_at)})]})]}),announcement.scheduled_publish_at&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Scheduled\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:formatDate(announcement.scheduled_publish_at)})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Eye,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Views\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:(announcement.view_count||0).toLocaleString()})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Heart,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Reactions\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:(announcement.reaction_count||0).toLocaleString()})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:16,style:{color:'#6b7280'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:0},children:\"Comments\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#111827',margin:0},children:(announcement.comment_count||0).toLocaleString()})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1.5rem',padding:'1rem',backgroundColor:'#f3f4f6',borderRadius:'8px'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#111827',margin:'0 0 0.75rem 0'},children:\"Settings\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'1.5rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:announcement.allow_comments?'#10b981':'#ef4444'}}),/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'0.875rem',color:'#374151'},children:[\"Comments \",announcement.allow_comments?'Enabled':'Disabled']})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:announcement.allow_sharing?'#10b981':'#ef4444'}}),/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'0.875rem',color:'#374151'},children:[\"Sharing \",announcement.allow_sharing?'Enabled':'Disabled']})]})]})]})]})]})});};export default AnnouncementViewDialog;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}