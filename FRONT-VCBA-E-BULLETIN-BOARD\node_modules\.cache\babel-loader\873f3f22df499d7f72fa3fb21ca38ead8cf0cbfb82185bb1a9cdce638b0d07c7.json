{"ast": null, "code": "import React,{useState,useRef,useCallback,useEffect}from'react';import{Upload,X,Camera,AlertCircle,CheckCircle,Edit3,Trash2}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProfilePictureUpload=_ref=>{let{currentPicture,userInitials='U',onUpload,onRemove,isLoading=false,className='',size=120,showActions=true}=_ref;const[preview,setPreview]=useState(null);const[isDragOver,setIsDragOver]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);const[showDropdown,setShowDropdown]=useState(false);const[pendingFile,setPendingFile]=useState(null);const[isHovered,setIsHovered]=useState(false);const fileInputRef=useRef(null);const dropdownRef=useRef(null);const avatarRef=useRef(null);// Click outside to close dropdown\nuseEffect(()=>{const handleClickOutside=event=>{if(dropdownRef.current&&!dropdownRef.current.contains(event.target)&&avatarRef.current&&!avatarRef.current.contains(event.target)){setShowDropdown(false);}};if(showDropdown){document.addEventListener('mousedown',handleClickOutside);return()=>document.removeEventListener('mousedown',handleClickOutside);}},[showDropdown]);// File validation\nconst validateFile=useCallback(file=>{const maxSize=2*1024*1024;// 2MB\nconst allowedTypes=['image/jpeg','image/jpg','image/png','image/webp'];if(!allowedTypes.includes(file.type)){return'Please select a valid image file (JPEG, PNG, or WebP)';}if(file.size>maxSize){return'File size must be less than 2MB';}return null;},[]);// Handle file selection (now just sets preview and pending file)\nconst handleFileSelect=useCallback(file=>{setError(null);setSuccess(null);setShowDropdown(false);const validationError=validateFile(file);if(validationError){setError(validationError);return;}// Create preview\nconst reader=new FileReader();reader.onload=e=>{var _e$target;setPreview((_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result);setPendingFile(file);};reader.onerror=()=>{setError('Failed to read file');};reader.readAsDataURL(file);},[validateFile]);// Handle save (upload the pending file)\nconst handleSave=useCallback(async()=>{if(!pendingFile)return;try{await onUpload(pendingFile);setSuccess('Profile picture updated successfully!');setPendingFile(null);setTimeout(()=>setSuccess(null),3000);}catch(err){setError(err.message||'Failed to upload profile picture');setPreview(null);setPendingFile(null);}},[pendingFile,onUpload]);// Handle cancel (discard preview and pending file)\nconst handleCancel=useCallback(()=>{setPreview(null);setPendingFile(null);setError(null);},[]);// Handle file input change\nconst handleInputChange=e=>{var _e$target$files;const file=(_e$target$files=e.target.files)===null||_e$target$files===void 0?void 0:_e$target$files[0];if(file){handleFileSelect(file);}// Reset input value to allow selecting the same file again\ne.target.value='';};// Handle drag and drop\nconst handleDragOver=e=>{e.preventDefault();setIsDragOver(true);};const handleDragLeave=e=>{e.preventDefault();setIsDragOver(false);};const handleDrop=e=>{e.preventDefault();setIsDragOver(false);const file=e.dataTransfer.files[0];if(file){handleFileSelect(file);}};// Handle camera icon click\nconst handleCameraClick=e=>{e.stopPropagation();setShowDropdown(!showDropdown);};// Handle change photo\nconst handleChangePhoto=()=>{var _fileInputRef$current;setShowDropdown(false);(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();};// Handle remove photo\nconst handleRemovePhoto=async()=>{setShowDropdown(false);const confirmed=window.confirm('Are you sure you want to remove your profile picture? You can always upload a new one later.');if(!confirmed)return;setError(null);setSuccess(null);setPreview(null);setPendingFile(null);try{await onRemove();setSuccess('Profile picture removed successfully!');setTimeout(()=>setSuccess(null),3000);}catch(err){setError(err.message||'Failed to remove profile picture');}};// Get display image\nconst displayImage=preview||currentPicture;const hasImage=Boolean(displayImage);const hasPendingChanges=Boolean(pendingFile);// Debug logging\nconsole.log('🔍 ProfilePictureUpload - Props:',{currentPicture,preview,displayImage,hasImage,hasPendingChanges});return/*#__PURE__*/_jsxs(\"div\",{className:\"profile-picture-upload \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',display:'inline-block'},children:[/*#__PURE__*/_jsxs(\"div\",{ref:avatarRef,style:{position:'relative',width:\"\".concat(size,\"px\"),height:\"\".concat(size,\"px\"),borderRadius:'50%',overflow:'hidden',border:isDragOver?'4px solid #22c55e':hasPendingChanges?'4px solid #facc15':'4px solid #e8f5e8',transition:'all 0.3s ease',cursor:'pointer',boxShadow:isHovered?'0 8px 25px rgba(0, 0, 0, 0.15)':'0 4px 15px rgba(0, 0, 0, 0.1)',transform:isHovered?'scale(1.02)':'scale(1)'},onDragOver:handleDragOver,onDragLeave:handleDragLeave,onDrop:handleDrop,onMouseEnter:()=>setIsHovered(true),onMouseLeave:()=>setIsHovered(false),children:[hasImage?/*#__PURE__*/_jsx(\"img\",{src:displayImage,alt:\"Profile\",onLoad:()=>console.log('✅ Image loaded successfully:',displayImage),onError:e=>console.error('❌ Image failed to load:',displayImage,e),style:{width:'100%',height:'100%',objectFit:'cover',transition:'filter 0.3s ease',filter:isHovered?'brightness(0.9)':'brightness(1)'}}):/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'100%',background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'700',fontSize:\"\".concat(size*0.3,\"px\"),transition:'filter 0.3s ease',filter:isHovered?'brightness(0.9)':'brightness(1)'},children:userInitials}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'rgba(34, 197, 94, 0.8)',display:'flex',alignItems:'center',justifyContent:'center',opacity:isDragOver?1:0,transition:'opacity 0.3s ease',borderRadius:'50%'},children:/*#__PURE__*/_jsx(Upload,{size:size*0.2,color:\"white\"})}),isLoading&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'rgba(255, 255, 255, 0.9)',display:'flex',alignItems:'center',justifyContent:'center',borderRadius:'50%'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:\"\".concat(size*0.2,\"px\"),height:\"\".concat(size*0.2,\"px\"),border:'3px solid #e8f5e8',borderTop:'3px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}})})]}),/*#__PURE__*/_jsx(\"div\",{onClick:handleCameraClick,style:{position:'absolute',bottom:'8px',right:'8px',width:\"\".concat(size*0.25,\"px\"),height:\"\".concat(size*0.25,\"px\"),background:'#ffffff',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',cursor:'pointer',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.15)',border:'2px solid #ffffff',transition:'all 0.2s ease',transform:isHovered?'scale(1.1)':'scale(1)',zIndex:10},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.background='#ffffff';},children:/*#__PURE__*/_jsx(Camera,{size:size*0.12,color:\"#374151\"})}),showDropdown&&/*#__PURE__*/_jsxs(\"div\",{ref:dropdownRef,style:{position:'absolute',top:\"\".concat(size+10,\"px\"),right:'0',background:'white',borderRadius:'12px',boxShadow:'0 10px 40px rgba(0, 0, 0, 0.15)',border:'1px solid #e8f5e8',minWidth:'180px',zIndex:1000,overflow:'hidden',animation:'dropdownFadeIn 0.2s ease-out'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleChangePhoto,disabled:isLoading,style:{width:'100%',padding:'12px 16px',border:'none',background:'transparent',textAlign:'left',cursor:isLoading?'not-allowed':'pointer',display:'flex',alignItems:'center',gap:'12px',fontSize:'14px',fontWeight:'500',color:'#374151',transition:'background-color 0.2s ease',opacity:isLoading?0.6:1},onMouseEnter:e=>{if(!isLoading)e.currentTarget.style.background='#f9fafb';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(Edit3,{size:16}),\"Change Photo\"]}),hasImage&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleRemovePhoto,disabled:isLoading,style:{width:'100%',padding:'12px 16px',border:'none',background:'transparent',textAlign:'left',cursor:isLoading?'not-allowed':'pointer',display:'flex',alignItems:'center',gap:'12px',fontSize:'14px',fontWeight:'500',color:'#dc2626',transition:'background-color 0.2s ease',opacity:isLoading?0.6:1,borderTop:'1px solid #f3f4f6'},onMouseEnter:e=>{if(!isLoading)e.currentTarget.style.background='#fef2f2';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(Trash2,{size:16}),\"Remove Photo\"]})]})]}),hasPendingChanges&&showActions&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',display:'flex',gap:'0.75rem',animation:'fadeIn 0.3s ease-out'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleSave,disabled:isLoading,style:{background:'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontWeight:'600',cursor:isLoading?'not-allowed':'pointer',opacity:isLoading?0.6:1,display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'14px',transition:'all 0.2s ease',boxShadow:'0 2px 8px rgba(34, 197, 94, 0.2)'},onMouseEnter:e=>{if(!isLoading){e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';}},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 2px 8px rgba(34, 197, 94, 0.2)';},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16}),\"Save Changes\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleCancel,disabled:isLoading,style:{background:'transparent',border:'2px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:isLoading?'not-allowed':'pointer',color:'#6b7280',opacity:isLoading?0.6:1,display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'14px',fontWeight:'600',transition:'all 0.2s ease'},onMouseEnter:e=>{if(!isLoading){e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.background='#f9fafb';}},onMouseLeave:e=>{e.currentTarget.style.borderColor='#e8f5e8';e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(X,{size:16}),\"Cancel\"]})]}),/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\"image/jpeg,image/jpg,image/png,image/webp\",onChange:handleInputChange,style:{display:'none'}}),error&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'1rem',background:'#fef2f2',border:'1px solid #fecaca',borderRadius:'12px',color:'#dc2626',display:'flex',alignItems:'flex-start',gap:'0.75rem',animation:'slideDown 0.3s ease-out'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:20,style:{marginTop:'1px',flexShrink:0}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',marginBottom:'0.25rem'},children:\"Error\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',lineHeight:'1.4'},children:error})]})]}),success&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1rem',padding:'1rem',background:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'12px',color:'#16a34a',display:'flex',alignItems:'flex-start',gap:'0.75rem',animation:'slideDown 0.3s ease-out'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:20,style:{marginTop:'1px',flexShrink:0}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',marginBottom:'0.25rem'},children:\"Success\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',lineHeight:'1.4'},children:success})]})]}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n\\n        @keyframes fadeIn {\\n          0% { opacity: 0; }\\n          100% { opacity: 1; }\\n        }\\n\\n        @keyframes slideUp {\\n          0% {\\n            opacity: 0;\\n            transform: translateY(20px) scale(0.95);\\n          }\\n          100% {\\n            opacity: 1;\\n            transform: translateY(0) scale(1);\\n          }\\n        }\\n\\n        @keyframes slideDown {\\n          0% {\\n            opacity: 0;\\n            transform: translateY(-10px);\\n          }\\n          100% {\\n            opacity: 1;\\n            transform: translateY(0);\\n          }\\n        }\\n\\n        @keyframes dropdownFadeIn {\\n          0% {\\n            opacity: 0;\\n            transform: translateY(-10px) scale(0.95);\\n          }\\n          100% {\\n            opacity: 1;\\n            transform: translateY(0) scale(1);\\n          }\\n        }\\n      \"})]});};export default ProfilePictureUpload;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}