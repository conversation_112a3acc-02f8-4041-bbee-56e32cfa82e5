{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 20h4\",\n  key: \"ni2waw\"\n}], [\"path\", {\n  d: \"M12 16v6\",\n  key: \"c8a4gj\"\n}], [\"path\", {\n  d: \"M17 2h4v4\",\n  key: \"vhe59\"\n}], [\"path\", {\n  d: \"m21 2-5.46 5.46\",\n  key: \"19kypf\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"11\",\n  r: \"5\",\n  key: \"16gxyc\"\n}]];\nconst VenusAndMars = createLucideIcon(\"venus-and-mars\", __iconNode);\nexport { __iconNode, VenusAndMars as default };\n//# sourceMappingURL=venus-and-mars.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}