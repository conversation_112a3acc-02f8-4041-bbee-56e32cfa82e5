{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18.518 17.347A7 7 0 0 1 14 19\",\n  key: \"1emhpo\"\n}], [\"path\", {\n  d: \"M18.8 4A11 11 0 0 1 20 9\",\n  key: \"127b67\"\n}], [\"path\", {\n  d: \"M9 9h.01\",\n  key: \"1q5me6\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1v9bxh\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"7\",\n  key: \"p2h5vp\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"16\",\n  width: \"10\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"bfnviv\"\n}]];\nconst BellElectric = createLucideIcon(\"bell-electric\", __iconNode);\nexport { __iconNode, BellElectric as default };\n//# sourceMappingURL=bell-electric.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}