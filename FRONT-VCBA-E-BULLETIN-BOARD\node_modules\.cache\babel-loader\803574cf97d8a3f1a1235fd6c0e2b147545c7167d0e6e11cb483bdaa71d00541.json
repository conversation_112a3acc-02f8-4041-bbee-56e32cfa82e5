{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 12h8\",\n  key: \"h98zly\"\n}], [\"path\", {\n  d: \"M13 18h8\",\n  key: \"oe0vm4\"\n}], [\"path\", {\n  d: \"M13 6h8\",\n  key: \"15sg57\"\n}], [\"path\", {\n  d: \"M3 12h1\",\n  key: \"lp3yf2\"\n}], [\"path\", {\n  d: \"M3 18h1\",\n  key: \"1eiwyy\"\n}], [\"path\", {\n  d: \"M3 6h1\",\n  key: \"rgxa97\"\n}], [\"path\", {\n  d: \"M8 12h1\",\n  key: \"1con00\"\n}], [\"path\", {\n  d: \"M8 18h1\",\n  key: \"13wk12\"\n}], [\"path\", {\n  d: \"M8 6h1\",\n  key: \"tn6mkg\"\n}]];\nconst Logs = createLucideIcon(\"logs\", __iconNode);\nexport { __iconNode, Logs as default };\n//# sourceMappingURL=logs.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}