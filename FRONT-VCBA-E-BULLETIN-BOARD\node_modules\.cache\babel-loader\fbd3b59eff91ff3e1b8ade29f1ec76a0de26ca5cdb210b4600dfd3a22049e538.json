{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle, Camera, Upload, AlertTriangle, Eye, EyeOff, Edit3, Trash2 } from 'lucide-react';\nimport { AdminAuthService } from '../../services/admin-auth.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user,\n    checkAuthStatus\n  } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n\n  // Profile picture states\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n  const [profilePicturePreview, setProfilePicturePreview] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [showRemoveConfirmation, setShowRemoveConfirmation] = useState(false);\n  const [showUpdateConfirmation, setShowUpdateConfirmation] = useState(false);\n  const [showOptionsMenu, setShowOptionsMenu] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // Password change states\n  const [showPasswordSection, setShowPasswordSection] = useState(false);\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile Settings',\n    icon: User\n  }, {\n    key: 'system',\n    label: 'System Settings',\n    icon: SettingsIcon\n  }, {\n    key: 'security',\n    label: 'Security',\n    icon: Lock\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }];\n\n  // Profile picture handlers\n  const handleFileSelect = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      // Validate file\n      const maxSize = 2 * 1024 * 1024; // 2MB\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        alert('Please select a valid image file (JPEG, PNG, or WebP)');\n        return;\n      }\n      if (file.size > maxSize) {\n        alert('File size must be less than 2MB');\n        return;\n      }\n      setSelectedFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        setProfilePicturePreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSaveProfilePicture = () => {\n    setShowUpdateConfirmation(true);\n  };\n  const confirmUpdateProfilePicture = async () => {\n    if (!selectedFile) return;\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.uploadProfilePicture(selectedFile);\n      await checkAuthStatus();\n      setSelectedFile(null);\n      setProfilePicturePreview(null);\n      setShowUpdateConfirmation(false);\n    } catch (error) {\n      alert(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handleCancelProfilePicture = () => {\n    setSelectedFile(null);\n    setProfilePicturePreview(null);\n  };\n  const handleRemoveProfilePicture = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      await checkAuthStatus();\n      setShowRemoveConfirmation(false);\n    } catch (error) {\n      alert(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handlePasswordChange = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('New passwords do not match');\n      return;\n    }\n    if (passwordData.newPassword.length < 6) {\n      alert('Password must be at least 6 characters long');\n      return;\n    }\n    setIsChangingPassword(true);\n    try {\n      // Add your password change API call here\n      // await AdminAuthService.changePassword(passwordData.currentPassword, passwordData.newPassword);\n      alert('Password changed successfully');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n      setShowPasswordSection(false);\n    } catch (error) {\n      alert(error.message || 'Failed to change password');\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n\n  // Close options menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => {\n      if (showOptionsMenu) {\n        setShowOptionsMenu(false);\n      }\n    };\n    if (showOptionsMenu) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showOptionsMenu]);\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 2rem 0',\n            color: '#2d5016',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '2rem',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                width: '120px',\n                height: '120px',\n                borderRadius: '50%',\n                overflow: 'hidden',\n                border: '4px solid #e5e7eb',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n              },\n              children: [profilePicturePreview || user !== null && user !== void 0 && user.profilePicture && `http://localhost:5000${user.profilePicture}` ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: profilePicturePreview || `http://localhost:5000${(user === null || user === void 0 ? void 0 : user.profilePicture) || ''}`,\n                alt: \"Profile\",\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  background: 'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontWeight: '700',\n                  fontSize: '2.5rem'\n                },\n                children: `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => setShowOptionsMenu(!showOptionsMenu),\n                style: {\n                  position: 'absolute',\n                  bottom: '4px',\n                  right: '4px',\n                  width: '32px',\n                  height: '32px',\n                  borderRadius: '50%',\n                  background: '#374151',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  border: '3px solid white',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#1f2937';\n                  e.currentTarget.style.transform = 'scale(1.1)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#374151';\n                  e.currentTarget.style.transform = 'scale(1)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Camera, {\n                  size: 16,\n                  color: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 15\n              }, this), showOptionsMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  bottom: '45px',\n                  right: '4px',\n                  background: 'white',\n                  borderRadius: '12px',\n                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',\n                  border: '1px solid #e5e7eb',\n                  minWidth: '160px',\n                  zIndex: 10,\n                  overflow: 'hidden'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var _fileInputRef$current;\n                    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                    setShowOptionsMenu(false);\n                  },\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem 1rem',\n                    background: 'none',\n                    border: 'none',\n                    textAlign: 'left',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    fontSize: '0.875rem',\n                    color: '#374151',\n                    transition: 'background-color 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.backgroundColor = '#f3f4f6';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Edit3, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 21\n                  }, this), \"Change Photo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setShowRemoveConfirmation(true);\n                    setShowOptionsMenu(false);\n                  },\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem 1rem',\n                    background: 'none',\n                    border: 'none',\n                    textAlign: 'left',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    fontSize: '0.875rem',\n                    color: '#dc2626',\n                    transition: 'background-color 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.backgroundColor = '#fef2f2';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this), \"Remove Photo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), isUploadingPicture && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  bottom: 0,\n                  background: 'rgba(255, 255, 255, 0.9)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '24px',\n                    height: '24px',\n                    border: '2px solid #e8f5e8',\n                    borderTop: '2px solid #22c55e',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n              onChange: handleFileSelect,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 13\n            }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.75rem',\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSaveProfilePicture,\n                disabled: isUploadingPicture,\n                style: {\n                  background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1,\n                  fontSize: '0.875rem',\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  if (!isUploadingPicture) {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.3)';\n                },\n                children: \"Save Changes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleCancelProfilePicture,\n                disabled: isUploadingPicture,\n                style: {\n                  background: 'white',\n                  color: '#6b7280',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1,\n                  fontSize: '0.875rem',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  if (!isUploadingPicture) {\n                    e.currentTarget.style.borderColor = '#9ca3af';\n                    e.currentTarget.style.color = '#374151';\n                  }\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.borderColor = '#e5e7eb';\n                  e.currentTarget.style.color = '#6b7280';\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  margin: 0,\n                  textAlign: 'center'\n                },\n                children: \"Click the camera icon to change your photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              paddingLeft: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    color: '#2d5016',\n                    fontSize: '1.5rem',\n                    fontWeight: '700'\n                  },\n                  children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '0.75rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#6b7280',\n                        minWidth: '80px'\n                      },\n                      children: \"Email:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: '#374151',\n                        fontWeight: '500'\n                      },\n                      children: (user === null || user === void 0 ? void 0 : user.email) || 'Not set'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#6b7280',\n                        minWidth: '80px'\n                      },\n                      children: \"Role:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                        color: 'white',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '12px',\n                        fontSize: '0.75rem',\n                        fontWeight: '600'\n                      },\n                      children: \"Administrator\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#6b7280',\n                        minWidth: '80px'\n                      },\n                      children: \"Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: '#16a34a',\n                        fontWeight: '600',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          borderRadius: '50%',\n                          background: '#16a34a'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 500,\n                        columnNumber: 23\n                      }, this), \"Active\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.25rem',\n              fontWeight: '600'\n            },\n            children: \"Password & Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 11\n          }, this), !showPasswordSection && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowPasswordSection(true),\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), \"Change Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 9\n        }, this), showPasswordSection ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '400px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Current Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.current ? 'text' : 'password',\n                  value: passwordData.currentPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    currentPassword: e.target.value\n                  })),\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    paddingRight: '2.5rem',\n                    border: '1px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'border-color 0.2s ease'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPasswords(prev => ({\n                    ...prev,\n                    current: !prev.current\n                  })),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280'\n                  },\n                  children: showPasswords.current ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 46\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 69\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.new ? 'text' : 'password',\n                  value: passwordData.newPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    newPassword: e.target.value\n                  })),\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    paddingRight: '2.5rem',\n                    border: '1px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'border-color 0.2s ease'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPasswords(prev => ({\n                    ...prev,\n                    new: !prev.new\n                  })),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280'\n                  },\n                  children: showPasswords.new ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 42\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 65\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Confirm New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.confirm ? 'text' : 'password',\n                  value: passwordData.confirmPassword,\n                  onChange: e => setPasswordData(prev => ({\n                    ...prev,\n                    confirmPassword: e.target.value\n                  })),\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    paddingRight: '2.5rem',\n                    border: '1px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'border-color 0.2s ease'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPasswords(prev => ({\n                    ...prev,\n                    confirm: !prev.confirm\n                  })),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280'\n                  },\n                  children: showPasswords.confirm ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 46\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 69\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '1rem',\n                marginTop: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handlePasswordChange,\n                disabled: isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword,\n                style: {\n                  background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword ? 'not-allowed' : 'pointer',\n                  opacity: isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword ? 0.6 : 1\n                },\n                children: isChangingPassword ? 'Changing...' : 'Change Password'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setShowPasswordSection(false);\n                  setPasswordData({\n                    currentPassword: '',\n                    newPassword: '',\n                    confirmPassword: ''\n                  });\n                },\n                disabled: isChangingPassword,\n                style: {\n                  background: 'white',\n                  color: '#6b7280',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isChangingPassword ? 'not-allowed' : 'pointer',\n                  opacity: isChangingPassword ? 0.6 : 1\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Keep your account secure by regularly updating your password.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 7\n      }, this), showUpdateConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            maxWidth: '400px',\n            width: '90%',\n            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                size: 48,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 0.5rem 0',\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Update Profile Picture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: 0\n              },\n              children: \"Are you sure you want to update your profile picture with the selected image?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: confirmUpdateProfilePicture,\n              disabled: isUploadingPicture,\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                opacity: isUploadingPicture ? 0.6 : 1\n              },\n              children: isUploadingPicture ? 'Updating...' : 'Yes, Update'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUpdateConfirmation(false),\n              disabled: isUploadingPicture,\n              style: {\n                background: 'white',\n                color: '#6b7280',\n                border: '1px solid #e8f5e8',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                opacity: isUploadingPicture ? 0.6 : 1\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this), showRemoveConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            maxWidth: '400px',\n            width: '90%',\n            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                size: 48,\n                color: \"#f59e0b\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 0.5rem 0',\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Remove Profile Picture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: 0\n              },\n              children: \"Are you sure you want to remove your profile picture? This action cannot be undone.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRemoveProfilePicture,\n              disabled: isUploadingPicture,\n              style: {\n                background: '#dc2626',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                opacity: isUploadingPicture ? 0.6 : 1\n              },\n              children: isUploadingPicture ? 'Removing...' : 'Yes, Remove'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowRemoveConfirmation(false),\n              disabled: isUploadingPicture,\n              style: {\n                background: 'white',\n                color: '#6b7280',\n                border: '1px solid #e8f5e8',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                opacity: isUploadingPicture ? 0.6 : 1\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 5\n    }, this);\n  };\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"General Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Maintenance Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Enable maintenance mode to restrict access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Auto-approve Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Automatically approve new posts without review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Email Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Send email notifications for important events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              defaultChecked: true,\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"System Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1010,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"System Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1021,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Last Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"June 28, 2025\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Database Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1044,\n                columnNumber: 17\n              }, this), \"Connected\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Server Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1051,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 17\n              }, this), \"Online\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 906,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Lock, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1082,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Security Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1085,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Security settings panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1088,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1074,\n          columnNumber: 11\n        }, this);\n      case 'notifications':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bell, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Notification Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Notification preferences panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1123,\n      columnNumber: 7\n    }, this), renderContent(), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1120,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"iQ4/ombHszHosatUu40M92xXTU8=\", false, function () {\n  return [useAdminAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useAdminAuth", "User", "Settings", "SettingsIcon", "Lock", "Bell", "CheckCircle", "Camera", "Upload", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Eye", "Eye<PERSON>ff", "Edit3", "Trash2", "AdminAuthService", "jsxDEV", "_jsxDEV", "_s", "user", "checkAuthStatus", "activeTab", "setActiveTab", "isUploadingPicture", "setIsUploadingPicture", "profilePicturePreview", "setProfilePicturePreview", "selectedFile", "setSelectedFile", "showRemoveConfirmation", "setShowRemoveConfirmation", "showUpdateConfirmation", "setShowUpdateConfirmation", "showOptionsMenu", "setShowOptionsMenu", "fileInputRef", "showPasswordSection", "setShowPasswordSection", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "confirm", "isChangingPassword", "setIsChangingPassword", "tabs", "key", "label", "icon", "handleFileSelect", "e", "_e$target$files", "file", "target", "files", "maxSize", "allowedTypes", "includes", "type", "alert", "size", "reader", "FileReader", "onload", "_e$target", "result", "readAsDataURL", "handleSaveProfilePicture", "confirmUpdateProfilePicture", "uploadProfilePicture", "error", "message", "handleCancelProfilePicture", "handleRemoveProfilePicture", "removeProfilePicture", "handlePasswordChange", "length", "handleClickOutside", "document", "addEventListener", "removeEventListener", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "background", "borderRadius", "padding", "boxShadow", "border", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alignItems", "position", "width", "height", "overflow", "cursor", "transition", "profilePicture", "src", "alt", "objectFit", "justifyContent", "firstName", "char<PERSON>t", "lastName", "onClick", "bottom", "right", "onMouseEnter", "currentTarget", "transform", "onMouseLeave", "min<PERSON><PERSON><PERSON>", "zIndex", "_fileInputRef$current", "click", "textAlign", "backgroundColor", "top", "left", "borderTop", "animation", "ref", "accept", "onChange", "disabled", "opacity", "borderColor", "flex", "paddingLeft", "email", "marginBottom", "max<PERSON><PERSON><PERSON>", "value", "prev", "paddingRight", "outline", "onFocus", "onBlur", "marginTop", "renderSystemSettings", "defaultChecked", "gridTemplateColumns", "renderContent", "flexWrap", "map", "tab", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Settings.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle, Camera, Upload, X, <PERSON>ert<PERSON>riangle, Eye, EyeOff, Edit3, Trash2 } from 'lucide-react';\nimport { AdminAuthService } from '../../services/admin-auth.service';\n\nconst Settings: React.FC = () => {\n  const { user, checkAuthStatus } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'system' | 'security' | 'notifications'>('profile');\n\n  // Profile picture states\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [showRemoveConfirmation, setShowRemoveConfirmation] = useState(false);\n  const [showUpdateConfirmation, setShowUpdateConfirmation] = useState(false);\n  const [showOptionsMenu, setShowOptionsMenu] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Password change states\n  const [showPasswordSection, setShowPasswordSection] = useState(false);\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile Settings', icon: User },\n    { key: 'system', label: 'System Settings', icon: SettingsIcon },\n    { key: 'security', label: 'Security', icon: Lock },\n    { key: 'notifications', label: 'Notifications', icon: Bell }\n  ];\n\n  // Profile picture handlers\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      // Validate file\n      const maxSize = 2 * 1024 * 1024; // 2MB\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n      if (!allowedTypes.includes(file.type)) {\n        alert('Please select a valid image file (JPEG, PNG, or WebP)');\n        return;\n      }\n\n      if (file.size > maxSize) {\n        alert('File size must be less than 2MB');\n        return;\n      }\n\n      setSelectedFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfilePicturePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSaveProfilePicture = () => {\n    setShowUpdateConfirmation(true);\n  };\n\n  const confirmUpdateProfilePicture = async () => {\n    if (!selectedFile) return;\n\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.uploadProfilePicture(selectedFile);\n      await checkAuthStatus();\n      setSelectedFile(null);\n      setProfilePicturePreview(null);\n      setShowUpdateConfirmation(false);\n    } catch (error: any) {\n      alert(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handleCancelProfilePicture = () => {\n    setSelectedFile(null);\n    setProfilePicturePreview(null);\n  };\n\n  const handleRemoveProfilePicture = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      await checkAuthStatus();\n      setShowRemoveConfirmation(false);\n    } catch (error: any) {\n      alert(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handlePasswordChange = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('New passwords do not match');\n      return;\n    }\n\n    if (passwordData.newPassword.length < 6) {\n      alert('Password must be at least 6 characters long');\n      return;\n    }\n\n    setIsChangingPassword(true);\n    try {\n      // Add your password change API call here\n      // await AdminAuthService.changePassword(passwordData.currentPassword, passwordData.newPassword);\n      alert('Password changed successfully');\n      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n      setShowPasswordSection(false);\n    } catch (error: any) {\n      alert(error.message || 'Failed to change password');\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n\n  // Close options menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => {\n      if (showOptionsMenu) {\n        setShowOptionsMenu(false);\n      }\n    };\n\n    if (showOptionsMenu) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showOptionsMenu]);\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Section */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 2rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Profile Information\n        </h3>\n\n        <div style={{ display: 'flex', gap: '2rem', alignItems: 'flex-start' }}>\n          {/* Profile Picture */}\n          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>\n            <div\n              style={{\n                position: 'relative',\n                width: '120px',\n                height: '120px',\n                borderRadius: '50%',\n                overflow: 'hidden',\n                border: '4px solid #e5e7eb',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n              }}\n            >\n              {profilePicturePreview || (user?.profilePicture && `http://localhost:5000${user.profilePicture}`) ? (\n                <img\n                  src={profilePicturePreview || `http://localhost:5000${user?.profilePicture || ''}`}\n                  alt=\"Profile\"\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover'\n                  }}\n                />\n              ) : (\n                <div\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    background: 'linear-gradient(135deg, #6b7280 0%, #9ca3af 100%)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontWeight: '700',\n                    fontSize: '2.5rem'\n                  }}\n                >\n                  {`${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`}\n                </div>\n              )}\n\n              {/* Camera Icon in Bottom Right */}\n              <div\n                onClick={() => setShowOptionsMenu(!showOptionsMenu)}\n                style={{\n                  position: 'absolute',\n                  bottom: '4px',\n                  right: '4px',\n                  width: '32px',\n                  height: '32px',\n                  borderRadius: '50%',\n                  background: '#374151',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  border: '3px solid white',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = '#1f2937';\n                  e.currentTarget.style.transform = 'scale(1.1)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = '#374151';\n                  e.currentTarget.style.transform = 'scale(1)';\n                }}\n              >\n                <Camera size={16} color=\"white\" />\n              </div>\n\n              {/* Options Dropdown Menu from Camera Icon */}\n              {showOptionsMenu && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '45px',\n                  right: '4px',\n                  background: 'white',\n                  borderRadius: '12px',\n                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',\n                  border: '1px solid #e5e7eb',\n                  minWidth: '160px',\n                  zIndex: 10,\n                  overflow: 'hidden'\n                }}>\n                  <button\n                    onClick={() => {\n                      fileInputRef.current?.click();\n                      setShowOptionsMenu(false);\n                    }}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem 1rem',\n                      background: 'none',\n                      border: 'none',\n                      textAlign: 'left',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      fontSize: '0.875rem',\n                      color: '#374151',\n                      transition: 'background-color 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.backgroundColor = '#f3f4f6';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.backgroundColor = 'transparent';\n                    }}\n                  >\n                    <Edit3 size={16} />\n                    Change Photo\n                  </button>\n                  <button\n                    onClick={() => {\n                      setShowRemoveConfirmation(true);\n                      setShowOptionsMenu(false);\n                    }}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem 1rem',\n                      background: 'none',\n                      border: 'none',\n                      textAlign: 'left',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      fontSize: '0.875rem',\n                      color: '#dc2626',\n                      transition: 'background-color 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.backgroundColor = '#fef2f2';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.backgroundColor = 'transparent';\n                    }}\n                  >\n                    <Trash2 size={16} />\n                    Remove Photo\n                  </button>\n                </div>\n              )}\n\n              {isUploadingPicture && (\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(255, 255, 255, 0.9)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  <div\n                    style={{\n                      width: '24px',\n                      height: '24px',\n                      border: '2px solid #e8f5e8',\n                      borderTop: '2px solid #22c55e',\n                      borderRadius: '50%',\n                      animation: 'spin 1s linear infinite'\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n              onChange={handleFileSelect}\n              style={{ display: 'none' }}\n            />\n\n            {/* Profile Picture Actions */}\n            {selectedFile ? (\n              <div style={{ display: 'flex', gap: '0.75rem', justifyContent: 'center' }}>\n                <button\n                  onClick={handleSaveProfilePicture}\n                  disabled={isUploadingPicture}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontWeight: '600',\n                    cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                    opacity: isUploadingPicture ? 0.6 : 1,\n                    fontSize: '0.875rem',\n                    boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    if (!isUploadingPicture) {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.4)';\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.3)';\n                  }}\n                >\n                  Save Changes\n                </button>\n                <button\n                  onClick={handleCancelProfilePicture}\n                  disabled={isUploadingPicture}\n                  style={{\n                    background: 'white',\n                    color: '#6b7280',\n                    border: '2px solid #e5e7eb',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontWeight: '600',\n                    cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                    opacity: isUploadingPicture ? 0.6 : 1,\n                    fontSize: '0.875rem',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    if (!isUploadingPicture) {\n                      e.currentTarget.style.borderColor = '#9ca3af';\n                      e.currentTarget.style.color = '#374151';\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                    e.currentTarget.style.color = '#6b7280';\n                  }}\n                >\n                  Cancel\n                </button>\n              </div>\n            ) : (\n              <div style={{ display: 'flex', justifyContent: 'center' }}>\n                <p style={{\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  margin: 0,\n                  textAlign: 'center'\n                }}>\n                  Click the camera icon to change your photo\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Profile Details */}\n          <div style={{ flex: 1, paddingLeft: '1rem' }}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n              <div>\n                <h4 style={{\n                  margin: '0 0 1rem 0',\n                  color: '#2d5016',\n                  fontSize: '1.5rem',\n                  fontWeight: '700'\n                }}>\n                  {user?.firstName} {user?.lastName}\n                </h4>\n\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      minWidth: '80px'\n                    }}>\n                      Email:\n                    </span>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      color: '#374151',\n                      fontWeight: '500'\n                    }}>\n                      {user?.email || 'Not set'}\n                    </span>\n                  </div>\n\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      minWidth: '80px'\n                    }}>\n                      Role:\n                    </span>\n                    <span style={{\n                      background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                      color: 'white',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '12px',\n                      fontSize: '0.75rem',\n                      fontWeight: '600'\n                    }}>\n                      Administrator\n                    </span>\n                  </div>\n\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      color: '#6b7280',\n                      minWidth: '80px'\n                    }}>\n                      Status:\n                    </span>\n                    <span style={{\n                      fontSize: '0.875rem',\n                      color: '#16a34a',\n                      fontWeight: '600',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    }}>\n                      <div style={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        background: '#16a34a'\n                      }} />\n                      Active\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Password Change Section */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n          <h3 style={{\n            margin: 0,\n            color: '#2d5016',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          }}>\n            Password & Security\n          </h3>\n\n          {!showPasswordSection && (\n            <button\n              onClick={() => setShowPasswordSection(true)}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Lock size={16} />\n              Change Password\n            </button>\n          )}\n        </div>\n\n        {showPasswordSection ? (\n          <div style={{ maxWidth: '400px' }}>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  Current Password\n                </label>\n                <div style={{ position: 'relative' }}>\n                  <input\n                    type={showPasswords.current ? 'text' : 'password'}\n                    value={passwordData.currentPassword}\n                    onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      paddingRight: '2.5rem',\n                      border: '1px solid #e8f5e8',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      outline: 'none',\n                      transition: 'border-color 0.2s ease'\n                    }}\n                    onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                    onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}\n                    style={{\n                      position: 'absolute',\n                      right: '0.75rem',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    {showPasswords.current ? <EyeOff size={16} /> : <Eye size={16} />}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  New Password\n                </label>\n                <div style={{ position: 'relative' }}>\n                  <input\n                    type={showPasswords.new ? 'text' : 'password'}\n                    value={passwordData.newPassword}\n                    onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      paddingRight: '2.5rem',\n                      border: '1px solid #e8f5e8',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      outline: 'none',\n                      transition: 'border-color 0.2s ease'\n                    }}\n                    onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                    onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}\n                    style={{\n                      position: 'absolute',\n                      right: '0.75rem',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    {showPasswords.new ? <EyeOff size={16} /> : <Eye size={16} />}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  Confirm New Password\n                </label>\n                <div style={{ position: 'relative' }}>\n                  <input\n                    type={showPasswords.confirm ? 'text' : 'password'}\n                    value={passwordData.confirmPassword}\n                    onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      paddingRight: '2.5rem',\n                      border: '1px solid #e8f5e8',\n                      borderRadius: '8px',\n                      fontSize: '0.875rem',\n                      outline: 'none',\n                      transition: 'border-color 0.2s ease'\n                    }}\n                    onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                    onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}\n                    style={{\n                      position: 'absolute',\n                      right: '0.75rem',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    {showPasswords.confirm ? <EyeOff size={16} /> : <Eye size={16} />}\n                  </button>\n                </div>\n              </div>\n\n              <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>\n                <button\n                  onClick={handlePasswordChange}\n                  disabled={isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '8px',\n                    padding: '0.75rem 1.5rem',\n                    fontWeight: '600',\n                    cursor: (isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) ? 'not-allowed' : 'pointer',\n                    opacity: (isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) ? 0.6 : 1\n                  }}\n                >\n                  {isChangingPassword ? 'Changing...' : 'Change Password'}\n                </button>\n\n                <button\n                  onClick={() => {\n                    setShowPasswordSection(false);\n                    setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n                  }}\n                  disabled={isChangingPassword}\n                  style={{\n                    background: 'white',\n                    color: '#6b7280',\n                    border: '1px solid #e8f5e8',\n                    borderRadius: '8px',\n                    padding: '0.75rem 1.5rem',\n                    fontWeight: '600',\n                    cursor: isChangingPassword ? 'not-allowed' : 'pointer',\n                    opacity: isChangingPassword ? 0.6 : 1\n                  }}\n                >\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <p style={{ color: '#6b7280', margin: 0 }}>\n            Keep your account secure by regularly updating your password.\n          </p>\n        )}\n      </div>\n\n      {/* Update Profile Picture Confirmation Modal */}\n      {showUpdateConfirmation && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            maxWidth: '400px',\n            width: '90%',\n            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'\n          }}>\n            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>\n              <div style={{ marginBottom: '1rem' }}>\n                <Upload size={48} color=\"#22c55e\" />\n              </div>\n              <h3 style={{\n                margin: '0 0 0.5rem 0',\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Update Profile Picture\n              </h3>\n              <p style={{ color: '#6b7280', margin: 0 }}>\n                Are you sure you want to update your profile picture with the selected image?\n              </p>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>\n              <button\n                onClick={confirmUpdateProfilePicture}\n                disabled={isUploadingPicture}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1\n                }}\n              >\n                {isUploadingPicture ? 'Updating...' : 'Yes, Update'}\n              </button>\n\n              <button\n                onClick={() => setShowUpdateConfirmation(false)}\n                disabled={isUploadingPicture}\n                style={{\n                  background: 'white',\n                  color: '#6b7280',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1\n                }}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Remove Profile Picture Confirmation Modal */}\n      {showRemoveConfirmation && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            maxWidth: '400px',\n            width: '90%',\n            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'\n          }}>\n            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>\n              <div style={{ marginBottom: '1rem' }}>\n                <AlertTriangle size={48} color=\"#f59e0b\" />\n              </div>\n              <h3 style={{\n                margin: '0 0 0.5rem 0',\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Remove Profile Picture\n              </h3>\n              <p style={{ color: '#6b7280', margin: 0 }}>\n                Are you sure you want to remove your profile picture? This action cannot be undone.\n              </p>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>\n              <button\n                onClick={handleRemoveProfilePicture}\n                disabled={isUploadingPicture}\n                style={{\n                  background: '#dc2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1\n                }}\n              >\n                {isUploadingPicture ? 'Removing...' : 'Yes, Remove'}\n              </button>\n\n              <button\n                onClick={() => setShowRemoveConfirmation(false)}\n                disabled={isUploadingPicture}\n                style={{\n                  background: 'white',\n                  color: '#6b7280',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isUploadingPicture ? 'not-allowed' : 'pointer',\n                  opacity: isUploadingPicture ? 0.6 : 1\n                }}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* General Settings */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          General Settings\n        </h3>\n        \n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Maintenance Mode\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Enable maintenance mode to restrict access\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Auto-approve Posts\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Automatically approve new posts without review\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Email Notifications\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Send email notifications for important events\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* System Information */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          System Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              System Version\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              v1.0.0\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Last Updated\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              June 28, 2025\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Database Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Connected\n              </span>\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Server Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Online\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Lock size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Security Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Security settings panel coming soon\n            </p>\n          </div>\n        );\n      case 'notifications':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Bell size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Notification Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Notification preferences panel coming soon\n            </p>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div>\n\n      {/* Tabs */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key \n                  ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              <tab.icon size={16} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      {renderContent()}\n\n      {/* CSS for animations */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,IAAI,EAAEC,QAAQ,IAAIC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAKC,aAAa,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AACpJ,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMd,QAAkB,GAAGA,CAAA,KAAM;EAAAe,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGnB,YAAY,CAAC,CAAC;EAChD,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAsD,SAAS,CAAC;;EAE1G;EACA,MAAM,CAACyB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvF,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC+B,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACiC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMqC,YAAY,GAAGpC,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC;IAC/C0C,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC;IACjD+C,OAAO,EAAE,KAAK;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMoD,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEnD;EAAK,CAAC,EACzD;IAAEiD,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAEjD;EAAa,CAAC,EAC/D;IAAE+C,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEhD;EAAK,CAAC,EAClD;IAAE8C,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE/C;EAAK,CAAC,CAC7D;;EAED;EACA,MAAMgD,gBAAgB,GAAIC,CAAsC,IAAK;IAAA,IAAAC,eAAA;IACnE,MAAMC,IAAI,IAAAD,eAAA,GAAGD,CAAC,CAACG,MAAM,CAACC,KAAK,cAAAH,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIC,IAAI,EAAE;MACR;MACA,MAAMG,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;MACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;MAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;QACrCC,KAAK,CAAC,uDAAuD,CAAC;QAC9D;MACF;MAEA,IAAIP,IAAI,CAACQ,IAAI,GAAGL,OAAO,EAAE;QACvBI,KAAK,CAAC,iCAAiC,CAAC;QACxC;MACF;MAEApC,eAAe,CAAC6B,IAAI,CAAC;;MAErB;MACA,MAAMS,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIb,CAAC,IAAK;QAAA,IAAAc,SAAA;QACrB3C,wBAAwB,EAAA2C,SAAA,GAACd,CAAC,CAACG,MAAM,cAAAW,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACtD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACd,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMe,wBAAwB,GAAGA,CAAA,KAAM;IACrCxC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMyC,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAAC9C,YAAY,EAAE;IAEnBH,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMT,gBAAgB,CAAC2D,oBAAoB,CAAC/C,YAAY,CAAC;MACzD,MAAMP,eAAe,CAAC,CAAC;MACvBQ,eAAe,CAAC,IAAI,CAAC;MACrBF,wBAAwB,CAAC,IAAI,CAAC;MAC9BM,yBAAyB,CAAC,KAAK,CAAC;IAClC,CAAC,CAAC,OAAO2C,KAAU,EAAE;MACnBX,KAAK,CAACW,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC5D,CAAC,SAAS;MACRpD,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqD,0BAA0B,GAAGA,CAAA,KAAM;IACvCjD,eAAe,CAAC,IAAI,CAAC;IACrBF,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMoD,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7CtD,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMT,gBAAgB,CAACgE,oBAAoB,CAAC,CAAC;MAC7C,MAAM3D,eAAe,CAAC,CAAC;MACvBU,yBAAyB,CAAC,KAAK,CAAC;IAClC,CAAC,CAAC,OAAO6C,KAAU,EAAE;MACnBX,KAAK,CAACW,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC5D,CAAC,SAAS;MACRpD,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI1C,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC7DsB,KAAK,CAAC,4BAA4B,CAAC;MACnC;IACF;IAEA,IAAI1B,YAAY,CAACG,WAAW,CAACwC,MAAM,GAAG,CAAC,EAAE;MACvCjB,KAAK,CAAC,6CAA6C,CAAC;MACpD;IACF;IAEAf,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF;MACA;MACAe,KAAK,CAAC,+BAA+B,CAAC;MACtCzB,eAAe,CAAC;QAAEC,eAAe,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,eAAe,EAAE;MAAG,CAAC,CAAC;MAC9EL,sBAAsB,CAAC,KAAK,CAAC;IAC/B,CAAC,CAAC,OAAOsC,KAAU,EAAE;MACnBX,KAAK,CAACW,KAAK,CAACC,OAAO,IAAI,2BAA2B,CAAC;IACrD,CAAC,SAAS;MACR3B,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACd,MAAMkF,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAIjD,eAAe,EAAE;QACnBC,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC;IAED,IAAID,eAAe,EAAE;MACnBkD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEF,kBAAkB,CAAC;IAC5D;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEH,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACjD,eAAe,CAAC,CAAC;EAErB,MAAMqD,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5BvE,OAAA;MAAKwE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpE5E,OAAA;QAAKwE,KAAK,EAAE;UACVK,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAL,QAAA,gBACA5E,OAAA;UAAIwE,KAAK,EAAE;YACTU,MAAM,EAAE,YAAY;YACpBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELzF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE,MAAM;YAAEe,UAAU,EAAE;UAAa,CAAE;UAAAd,QAAA,gBAErE5E,OAAA;YAAKwE,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAEgB,UAAU,EAAE,QAAQ;cAAEf,GAAG,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC1F5E,OAAA;cACEwE,KAAK,EAAE;gBACLmB,QAAQ,EAAE,UAAU;gBACpBC,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACff,YAAY,EAAE,KAAK;gBACnBgB,QAAQ,EAAE,QAAQ;gBAClBb,MAAM,EAAE,mBAAmB;gBAC3Bc,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,eAAe;gBAC3BhB,SAAS,EAAE;cACb,CAAE;cAAAJ,QAAA,GAEDpE,qBAAqB,IAAKN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+F,cAAc,IAAI,wBAAwB/F,IAAI,CAAC+F,cAAc,EAAG,gBAC/FjG,OAAA;gBACEkG,GAAG,EAAE1F,qBAAqB,IAAI,wBAAwB,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,cAAc,KAAI,EAAE,EAAG;gBACnFE,GAAG,EAAC,SAAS;gBACb3B,KAAK,EAAE;kBACLoB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdO,SAAS,EAAE;gBACb;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEFzF,OAAA;gBACEwE,KAAK,EAAE;kBACLoB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdhB,UAAU,EAAE,mDAAmD;kBAC/DJ,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBW,cAAc,EAAE,QAAQ;kBACxBlB,KAAK,EAAE,OAAO;kBACdE,UAAU,EAAE,KAAK;kBACjBD,QAAQ,EAAE;gBACZ,CAAE;gBAAAR,QAAA,EAED,GAAG,CAAA1E,IAAI,aAAJA,IAAI,wBAAAoE,eAAA,GAAJpE,IAAI,CAAEoG,SAAS,cAAAhC,eAAA,uBAAfA,eAAA,CAAiBiC,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAArG,IAAI,aAAJA,IAAI,wBAAAqE,cAAA,GAAJrE,IAAI,CAAEsG,QAAQ,cAAAjC,cAAA,uBAAdA,cAAA,CAAgBgC,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACN,eAGDzF,OAAA;gBACEyG,OAAO,EAAEA,CAAA,KAAMxF,kBAAkB,CAAC,CAACD,eAAe,CAAE;gBACpDwD,KAAK,EAAE;kBACLmB,QAAQ,EAAE,UAAU;kBACpBe,MAAM,EAAE,KAAK;kBACbC,KAAK,EAAE,KAAK;kBACZf,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdf,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBJ,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBW,cAAc,EAAE,QAAQ;kBACxBN,MAAM,EAAE,SAAS;kBACjBd,MAAM,EAAE,iBAAiB;kBACzBD,SAAS,EAAE,8BAA8B;kBACzCgB,UAAU,EAAE;gBACd,CAAE;gBACFY,YAAY,EAAGtE,CAAC,IAAK;kBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACK,UAAU,GAAG,SAAS;kBAC5CvC,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACsC,SAAS,GAAG,YAAY;gBAChD,CAAE;gBACFC,YAAY,EAAGzE,CAAC,IAAK;kBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACK,UAAU,GAAG,SAAS;kBAC5CvC,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACsC,SAAS,GAAG,UAAU;gBAC9C,CAAE;gBAAAlC,QAAA,eAEF5E,OAAA,CAACT,MAAM;kBAACyD,IAAI,EAAE,EAAG;kBAACmC,KAAK,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EAGLzE,eAAe,iBACdhB,OAAA;gBAAKwE,KAAK,EAAE;kBACVmB,QAAQ,EAAE,UAAU;kBACpBe,MAAM,EAAE,MAAM;kBACdC,KAAK,EAAE,KAAK;kBACZ9B,UAAU,EAAE,OAAO;kBACnBC,YAAY,EAAE,MAAM;kBACpBE,SAAS,EAAE,gCAAgC;kBAC3CC,MAAM,EAAE,mBAAmB;kBAC3B+B,QAAQ,EAAE,OAAO;kBACjBC,MAAM,EAAE,EAAE;kBACVnB,QAAQ,EAAE;gBACZ,CAAE;gBAAAlB,QAAA,gBACA5E,OAAA;kBACEyG,OAAO,EAAEA,CAAA,KAAM;oBAAA,IAAAS,qBAAA;oBACb,CAAAA,qBAAA,GAAAhG,YAAY,CAACU,OAAO,cAAAsF,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;oBAC7BlG,kBAAkB,CAAC,KAAK,CAAC;kBAC3B,CAAE;kBACFuD,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,cAAc;oBACvBF,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACdmC,SAAS,EAAE,MAAM;oBACjBrB,MAAM,EAAE,SAAS;oBACjBtB,OAAO,EAAE,MAAM;oBACfiB,UAAU,EAAE,QAAQ;oBACpBf,GAAG,EAAE,SAAS;oBACdS,QAAQ,EAAE,UAAU;oBACpBD,KAAK,EAAE,SAAS;oBAChBa,UAAU,EAAE;kBACd,CAAE;kBACFY,YAAY,EAAGtE,CAAC,IAAK;oBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAAC6C,eAAe,GAAG,SAAS;kBACnD,CAAE;kBACFN,YAAY,EAAGzE,CAAC,IAAK;oBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAAC6C,eAAe,GAAG,aAAa;kBACvD,CAAE;kBAAAzC,QAAA,gBAEF5E,OAAA,CAACJ,KAAK;oBAACoD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAErB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzF,OAAA;kBACEyG,OAAO,EAAEA,CAAA,KAAM;oBACb5F,yBAAyB,CAAC,IAAI,CAAC;oBAC/BI,kBAAkB,CAAC,KAAK,CAAC;kBAC3B,CAAE;kBACFuD,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,cAAc;oBACvBF,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACdmC,SAAS,EAAE,MAAM;oBACjBrB,MAAM,EAAE,SAAS;oBACjBtB,OAAO,EAAE,MAAM;oBACfiB,UAAU,EAAE,QAAQ;oBACpBf,GAAG,EAAE,SAAS;oBACdS,QAAQ,EAAE,UAAU;oBACpBD,KAAK,EAAE,SAAS;oBAChBa,UAAU,EAAE;kBACd,CAAE;kBACFY,YAAY,EAAGtE,CAAC,IAAK;oBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAAC6C,eAAe,GAAG,SAAS;kBACnD,CAAE;kBACFN,YAAY,EAAGzE,CAAC,IAAK;oBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAAC6C,eAAe,GAAG,aAAa;kBACvD,CAAE;kBAAAzC,QAAA,gBAEF5E,OAAA,CAACH,MAAM;oBAACmD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEtB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAEAnF,kBAAkB,iBACjBN,OAAA;gBACEwE,KAAK,EAAE;kBACLmB,QAAQ,EAAE,UAAU;kBACpB2B,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPZ,KAAK,EAAE,CAAC;kBACRD,MAAM,EAAE,CAAC;kBACT7B,UAAU,EAAE,0BAA0B;kBACtCJ,OAAO,EAAE,MAAM;kBACfiB,UAAU,EAAE,QAAQ;kBACpBW,cAAc,EAAE;gBAClB,CAAE;gBAAAzB,QAAA,eAEF5E,OAAA;kBACEwE,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdZ,MAAM,EAAE,mBAAmB;oBAC3BuC,SAAS,EAAE,mBAAmB;oBAC9B1C,YAAY,EAAE,KAAK;oBACnB2C,SAAS,EAAE;kBACb;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzF,OAAA;cACE0H,GAAG,EAAExG,YAAa;cAClB4B,IAAI,EAAC,MAAM;cACX6E,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAEvF,gBAAiB;cAC3BmC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO;YAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAGD/E,YAAY,gBACXV,OAAA;cAAKwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE,SAAS;gBAAE0B,cAAc,EAAE;cAAS,CAAE;cAAAzB,QAAA,gBACxE5E,OAAA;gBACEyG,OAAO,EAAElD,wBAAyB;gBAClCsE,QAAQ,EAAEvH,kBAAmB;gBAC7BkE,KAAK,EAAE;kBACLK,UAAU,EAAE,mDAAmD;kBAC/DM,KAAK,EAAE,OAAO;kBACdF,MAAM,EAAE,MAAM;kBACdH,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,gBAAgB;kBACzBM,UAAU,EAAE,KAAK;kBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;kBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG,CAAC;kBACrC8E,QAAQ,EAAE,UAAU;kBACpBJ,SAAS,EAAE,kCAAkC;kBAC7CgB,UAAU,EAAE;gBACd,CAAE;gBACFY,YAAY,EAAGtE,CAAC,IAAK;kBACnB,IAAI,CAAChC,kBAAkB,EAAE;oBACvBgC,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACsC,SAAS,GAAG,kBAAkB;oBACpDxE,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACQ,SAAS,GAAG,mCAAmC;kBACvE;gBACF,CAAE;gBACF+B,YAAY,EAAGzE,CAAC,IAAK;kBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACsC,SAAS,GAAG,eAAe;kBACjDxE,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACQ,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBAAAJ,QAAA,EACH;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzF,OAAA;gBACEyG,OAAO,EAAE7C,0BAA2B;gBACpCiE,QAAQ,EAAEvH,kBAAmB;gBAC7BkE,KAAK,EAAE;kBACLK,UAAU,EAAE,OAAO;kBACnBM,KAAK,EAAE,SAAS;kBAChBF,MAAM,EAAE,mBAAmB;kBAC3BH,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,gBAAgB;kBACzBM,UAAU,EAAE,KAAK;kBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;kBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG,CAAC;kBACrC8E,QAAQ,EAAE,UAAU;kBACpBY,UAAU,EAAE;gBACd,CAAE;gBACFY,YAAY,EAAGtE,CAAC,IAAK;kBACnB,IAAI,CAAChC,kBAAkB,EAAE;oBACvBgC,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACuD,WAAW,GAAG,SAAS;oBAC7CzF,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACW,KAAK,GAAG,SAAS;kBACzC;gBACF,CAAE;gBACF4B,YAAY,EAAGzE,CAAC,IAAK;kBACnBA,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACuD,WAAW,GAAG,SAAS;kBAC7CzF,CAAC,CAACuE,aAAa,CAACrC,KAAK,CAACW,KAAK,GAAG,SAAS;gBACzC,CAAE;gBAAAP,QAAA,EACH;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENzF,OAAA;cAAKwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE4B,cAAc,EAAE;cAAS,CAAE;cAAAzB,QAAA,eACxD5E,OAAA;gBAAGwE,KAAK,EAAE;kBACRY,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBD,MAAM,EAAE,CAAC;kBACTkC,SAAS,EAAE;gBACb,CAAE;gBAAAxC,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzF,OAAA;YAAKwE,KAAK,EAAE;cAAEwD,IAAI,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAO,CAAE;YAAArD,QAAA,eAC3C5E,OAAA;cAAKwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAS,CAAE;cAAAC,QAAA,eACtE5E,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAIwE,KAAK,EAAE;oBACTU,MAAM,EAAE,YAAY;oBACpBC,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,QAAQ;oBAClBC,UAAU,EAAE;kBACd,CAAE;kBAAAT,QAAA,GACC1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoG,SAAS,EAAC,GAAC,EAACpG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsG,QAAQ;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eAELzF,OAAA;kBAAKwE,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,aAAa,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAU,CAAE;kBAAAC,QAAA,gBACvE5E,OAAA;oBAAKwE,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEiB,UAAU,EAAE,QAAQ;sBAAEf,GAAG,EAAE;oBAAS,CAAE;oBAAAC,QAAA,gBACnE5E,OAAA;sBAAMwE,KAAK,EAAE;wBACXY,QAAQ,EAAE,UAAU;wBACpBC,UAAU,EAAE,KAAK;wBACjBF,KAAK,EAAE,SAAS;wBAChB6B,QAAQ,EAAE;sBACZ,CAAE;sBAAApC,QAAA,EAAC;oBAEH;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzF,OAAA;sBAAMwE,KAAK,EAAE;wBACXY,QAAQ,EAAE,UAAU;wBACpBD,KAAK,EAAE,SAAS;wBAChBE,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACC,CAAA1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI;oBAAS;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENzF,OAAA;oBAAKwE,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEiB,UAAU,EAAE,QAAQ;sBAAEf,GAAG,EAAE;oBAAS,CAAE;oBAAAC,QAAA,gBACnE5E,OAAA;sBAAMwE,KAAK,EAAE;wBACXY,QAAQ,EAAE,UAAU;wBACpBC,UAAU,EAAE,KAAK;wBACjBF,KAAK,EAAE,SAAS;wBAChB6B,QAAQ,EAAE;sBACZ,CAAE;sBAAApC,QAAA,EAAC;oBAEH;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzF,OAAA;sBAAMwE,KAAK,EAAE;wBACXK,UAAU,EAAE,mDAAmD;wBAC/DM,KAAK,EAAE,OAAO;wBACdJ,OAAO,EAAE,iBAAiB;wBAC1BD,YAAY,EAAE,MAAM;wBACpBM,QAAQ,EAAE,SAAS;wBACnBC,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EAAC;oBAEH;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENzF,OAAA;oBAAKwE,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEiB,UAAU,EAAE,QAAQ;sBAAEf,GAAG,EAAE;oBAAS,CAAE;oBAAAC,QAAA,gBACnE5E,OAAA;sBAAMwE,KAAK,EAAE;wBACXY,QAAQ,EAAE,UAAU;wBACpBC,UAAU,EAAE,KAAK;wBACjBF,KAAK,EAAE,SAAS;wBAChB6B,QAAQ,EAAE;sBACZ,CAAE;sBAAApC,QAAA,EAAC;oBAEH;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPzF,OAAA;sBAAMwE,KAAK,EAAE;wBACXY,QAAQ,EAAE,UAAU;wBACpBD,KAAK,EAAE,SAAS;wBAChBE,UAAU,EAAE,KAAK;wBACjBZ,OAAO,EAAE,MAAM;wBACfiB,UAAU,EAAE,QAAQ;wBACpBf,GAAG,EAAE;sBACP,CAAE;sBAAAC,QAAA,gBACA5E,OAAA;wBAAKwE,KAAK,EAAE;0BACVoB,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbf,YAAY,EAAE,KAAK;0BACnBD,UAAU,EAAE;wBACd;sBAAE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzF,OAAA;QAAKwE,KAAK,EAAE;UACVK,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAL,QAAA,gBACA5E,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE4B,cAAc,EAAE,eAAe;YAAEX,UAAU,EAAE,QAAQ;YAAEyC,YAAY,EAAE;UAAS,CAAE;UAAAvD,QAAA,gBAC7G5E,OAAA;YAAIwE,KAAK,EAAE;cACTU,MAAM,EAAE,CAAC;cACTC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJ,CAACtE,mBAAmB,iBACnBnB,OAAA;YACEyG,OAAO,EAAEA,CAAA,KAAMrF,sBAAsB,CAAC,IAAI,CAAE;YAC5CoD,KAAK,EAAE;cACLK,UAAU,EAAE,mDAAmD;cAC/DM,KAAK,EAAE,OAAO;cACdF,MAAM,EAAE,MAAM;cACdH,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,gBAAgB;cACzBM,UAAU,EAAE,KAAK;cACjBU,MAAM,EAAE,SAAS;cACjBtB,OAAO,EAAE,MAAM;cACfiB,UAAU,EAAE,QAAQ;cACpBf,GAAG,EAAE;YACP,CAAE;YAAAC,QAAA,gBAEF5E,OAAA,CAACZ,IAAI;cAAC4D,IAAI,EAAE;YAAG;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELtE,mBAAmB,gBAClBnB,OAAA;UAAKwE,KAAK,EAAE;YAAE4D,QAAQ,EAAE;UAAQ,CAAE;UAAAxD,QAAA,eAChC5E,OAAA;YAAKwE,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACpE5E,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAOwE,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBW,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBgD,YAAY,EAAE;gBAChB,CAAE;gBAAAvD,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzF,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmB,QAAQ,EAAE;gBAAW,CAAE;gBAAAf,QAAA,gBACnC5E,OAAA;kBACE8C,IAAI,EAAEpB,aAAa,CAACE,OAAO,GAAG,MAAM,GAAG,UAAW;kBAClDyG,KAAK,EAAEhH,YAAY,CAACE,eAAgB;kBACpCqG,QAAQ,EAAGtF,CAAC,IAAKhB,eAAe,CAACgH,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE/G,eAAe,EAAEe,CAAC,CAACG,MAAM,CAAC4F;kBAAM,CAAC,CAAC,CAAE;kBACzF7D,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,SAAS;oBAClBwD,YAAY,EAAE,QAAQ;oBACtBtD,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnBM,QAAQ,EAAE,UAAU;oBACpBoD,OAAO,EAAE,MAAM;oBACfxC,UAAU,EAAE;kBACd,CAAE;kBACFyC,OAAO,EAAGnG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG,SAAU;kBACvDW,MAAM,EAAGpG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG;gBAAU;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFzF,OAAA;kBACE8C,IAAI,EAAC,QAAQ;kBACb2D,OAAO,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC2G,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1G,OAAO,EAAE,CAAC0G,IAAI,CAAC1G;kBAAQ,CAAC,CAAC,CAAE;kBAC/E4C,KAAK,EAAE;oBACLmB,QAAQ,EAAE,UAAU;oBACpBgB,KAAK,EAAE,SAAS;oBAChBW,GAAG,EAAE,KAAK;oBACVR,SAAS,EAAE,kBAAkB;oBAC7BjC,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACdc,MAAM,EAAE,SAAS;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAP,QAAA,EAEDlD,aAAa,CAACE,OAAO,gBAAG5B,OAAA,CAACL,MAAM;oBAACqD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGzF,OAAA,CAACN,GAAG;oBAACsD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAOwE,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBW,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBgD,YAAY,EAAE;gBAChB,CAAE;gBAAAvD,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzF,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmB,QAAQ,EAAE;gBAAW,CAAE;gBAAAf,QAAA,gBACnC5E,OAAA;kBACE8C,IAAI,EAAEpB,aAAa,CAACG,GAAG,GAAG,MAAM,GAAG,UAAW;kBAC9CwG,KAAK,EAAEhH,YAAY,CAACG,WAAY;kBAChCoG,QAAQ,EAAGtF,CAAC,IAAKhB,eAAe,CAACgH,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9G,WAAW,EAAEc,CAAC,CAACG,MAAM,CAAC4F;kBAAM,CAAC,CAAC,CAAE;kBACrF7D,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,SAAS;oBAClBwD,YAAY,EAAE,QAAQ;oBACtBtD,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnBM,QAAQ,EAAE,UAAU;oBACpBoD,OAAO,EAAE,MAAM;oBACfxC,UAAU,EAAE;kBACd,CAAE;kBACFyC,OAAO,EAAGnG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG,SAAU;kBACvDW,MAAM,EAAGpG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG;gBAAU;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFzF,OAAA;kBACE8C,IAAI,EAAC,QAAQ;kBACb2D,OAAO,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC2G,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEzG,GAAG,EAAE,CAACyG,IAAI,CAACzG;kBAAI,CAAC,CAAC,CAAE;kBACvE2C,KAAK,EAAE;oBACLmB,QAAQ,EAAE,UAAU;oBACpBgB,KAAK,EAAE,SAAS;oBAChBW,GAAG,EAAE,KAAK;oBACVR,SAAS,EAAE,kBAAkB;oBAC7BjC,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACdc,MAAM,EAAE,SAAS;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAP,QAAA,EAEDlD,aAAa,CAACG,GAAG,gBAAG7B,OAAA,CAACL,MAAM;oBAACqD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGzF,OAAA,CAACN,GAAG;oBAACsD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAOwE,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBW,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBgD,YAAY,EAAE;gBAChB,CAAE;gBAAAvD,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzF,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmB,QAAQ,EAAE;gBAAW,CAAE;gBAAAf,QAAA,gBACnC5E,OAAA;kBACE8C,IAAI,EAAEpB,aAAa,CAACI,OAAO,GAAG,MAAM,GAAG,UAAW;kBAClDuG,KAAK,EAAEhH,YAAY,CAACI,eAAgB;kBACpCmG,QAAQ,EAAGtF,CAAC,IAAKhB,eAAe,CAACgH,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7G,eAAe,EAAEa,CAAC,CAACG,MAAM,CAAC4F;kBAAM,CAAC,CAAC,CAAE;kBACzF7D,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,SAAS;oBAClBwD,YAAY,EAAE,QAAQ;oBACtBtD,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnBM,QAAQ,EAAE,UAAU;oBACpBoD,OAAO,EAAE,MAAM;oBACfxC,UAAU,EAAE;kBACd,CAAE;kBACFyC,OAAO,EAAGnG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG,SAAU;kBACvDW,MAAM,EAAGpG,CAAC,IAAKA,CAAC,CAACG,MAAM,CAAC+B,KAAK,CAACuD,WAAW,GAAG;gBAAU;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACFzF,OAAA;kBACE8C,IAAI,EAAC,QAAQ;kBACb2D,OAAO,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC2G,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAExG,OAAO,EAAE,CAACwG,IAAI,CAACxG;kBAAQ,CAAC,CAAC,CAAE;kBAC/E0C,KAAK,EAAE;oBACLmB,QAAQ,EAAE,UAAU;oBACpBgB,KAAK,EAAE,SAAS;oBAChBW,GAAG,EAAE,KAAK;oBACVR,SAAS,EAAE,kBAAkB;oBAC7BjC,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACdc,MAAM,EAAE,SAAS;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAP,QAAA,EAEDlD,aAAa,CAACI,OAAO,gBAAG9B,OAAA,CAACL,MAAM;oBAACqD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGzF,OAAA,CAACN,GAAG;oBAACsD,IAAI,EAAE;kBAAG;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzF,OAAA;cAAKwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE,MAAM;gBAAEgE,SAAS,EAAE;cAAO,CAAE;cAAA/D,QAAA,gBAC9D5E,OAAA;gBACEyG,OAAO,EAAE1C,oBAAqB;gBAC9B8D,QAAQ,EAAE9F,kBAAkB,IAAI,CAACV,YAAY,CAACE,eAAe,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACI,eAAgB;gBAC5H+C,KAAK,EAAE;kBACLK,UAAU,EAAE,mDAAmD;kBAC/DM,KAAK,EAAE,OAAO;kBACdF,MAAM,EAAE,MAAM;kBACdH,YAAY,EAAE,KAAK;kBACnBC,OAAO,EAAE,gBAAgB;kBACzBM,UAAU,EAAE,KAAK;kBACjBU,MAAM,EAAGhE,kBAAkB,IAAI,CAACV,YAAY,CAACE,eAAe,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACI,eAAe,GAAI,aAAa,GAAG,SAAS;kBACvJqG,OAAO,EAAG/F,kBAAkB,IAAI,CAACV,YAAY,CAACE,eAAe,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACI,eAAe,GAAI,GAAG,GAAG;gBACvI,CAAE;gBAAAmD,QAAA,EAED7C,kBAAkB,GAAG,aAAa,GAAG;cAAiB;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAETzF,OAAA;gBACEyG,OAAO,EAAEA,CAAA,KAAM;kBACbrF,sBAAsB,CAAC,KAAK,CAAC;kBAC7BE,eAAe,CAAC;oBAAEC,eAAe,EAAE,EAAE;oBAAEC,WAAW,EAAE,EAAE;oBAAEC,eAAe,EAAE;kBAAG,CAAC,CAAC;gBAChF,CAAE;gBACFoG,QAAQ,EAAE9F,kBAAmB;gBAC7ByC,KAAK,EAAE;kBACLK,UAAU,EAAE,OAAO;kBACnBM,KAAK,EAAE,SAAS;kBAChBF,MAAM,EAAE,mBAAmB;kBAC3BH,YAAY,EAAE,KAAK;kBACnBC,OAAO,EAAE,gBAAgB;kBACzBM,UAAU,EAAE,KAAK;kBACjBU,MAAM,EAAEhE,kBAAkB,GAAG,aAAa,GAAG,SAAS;kBACtD+F,OAAO,EAAE/F,kBAAkB,GAAG,GAAG,GAAG;gBACtC,CAAE;gBAAA6C,QAAA,EACH;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENzF,OAAA;UAAGwE,KAAK,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAED,MAAM,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAE3C;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL3E,sBAAsB,iBACrBd,OAAA;QAAKwE,KAAK,EAAE;UACVmB,QAAQ,EAAE,OAAO;UACjB2B,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPZ,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE,CAAC;UACT7B,UAAU,EAAE,oBAAoB;UAChCJ,OAAO,EAAE,MAAM;UACfiB,UAAU,EAAE,QAAQ;UACpBW,cAAc,EAAE,QAAQ;UACxBY,MAAM,EAAE;QACV,CAAE;QAAArC,QAAA,eACA5E,OAAA;UAAKwE,KAAK,EAAE;YACVK,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfqD,QAAQ,EAAE,OAAO;YACjBxC,KAAK,EAAE,KAAK;YACZZ,SAAS,EAAE;UACb,CAAE;UAAAJ,QAAA,gBACA5E,OAAA;YAAKwE,KAAK,EAAE;cAAE4C,SAAS,EAAE,QAAQ;cAAEe,YAAY,EAAE;YAAS,CAAE;YAAAvD,QAAA,gBAC1D5E,OAAA;cAAKwE,KAAK,EAAE;gBAAE2D,YAAY,EAAE;cAAO,CAAE;cAAAvD,QAAA,eACnC5E,OAAA,CAACR,MAAM;gBAACwD,IAAI,EAAE,EAAG;gBAACmC,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNzF,OAAA;cAAIwE,KAAK,EAAE;gBACTU,MAAM,EAAE,cAAc;gBACtBC,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzF,OAAA;cAAGwE,KAAK,EAAE;gBAAEW,KAAK,EAAE,SAAS;gBAAED,MAAM,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAE3C;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENzF,OAAA;YAAKwE,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,MAAM;cAAE0B,cAAc,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBACrE5E,OAAA;cACEyG,OAAO,EAAEjD,2BAA4B;cACrCqE,QAAQ,EAAEvH,kBAAmB;cAC7BkE,KAAK,EAAE;gBACLK,UAAU,EAAE,mDAAmD;gBAC/DM,KAAK,EAAE,OAAO;gBACdF,MAAM,EAAE,MAAM;gBACdH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBM,UAAU,EAAE,KAAK;gBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;gBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG;cACtC,CAAE;cAAAsE,QAAA,EAEDtE,kBAAkB,GAAG,aAAa,GAAG;YAAa;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eAETzF,OAAA;cACEyG,OAAO,EAAEA,CAAA,KAAM1F,yBAAyB,CAAC,KAAK,CAAE;cAChD8G,QAAQ,EAAEvH,kBAAmB;cAC7BkE,KAAK,EAAE;gBACLK,UAAU,EAAE,OAAO;gBACnBM,KAAK,EAAE,SAAS;gBAChBF,MAAM,EAAE,mBAAmB;gBAC3BH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBM,UAAU,EAAE,KAAK;gBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;gBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG;cACtC,CAAE;cAAAsE,QAAA,EACH;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA7E,sBAAsB,iBACrBZ,OAAA;QAAKwE,KAAK,EAAE;UACVmB,QAAQ,EAAE,OAAO;UACjB2B,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPZ,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE,CAAC;UACT7B,UAAU,EAAE,oBAAoB;UAChCJ,OAAO,EAAE,MAAM;UACfiB,UAAU,EAAE,QAAQ;UACpBW,cAAc,EAAE,QAAQ;UACxBY,MAAM,EAAE;QACV,CAAE;QAAArC,QAAA,eACA5E,OAAA;UAAKwE,KAAK,EAAE;YACVK,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfqD,QAAQ,EAAE,OAAO;YACjBxC,KAAK,EAAE,KAAK;YACZZ,SAAS,EAAE;UACb,CAAE;UAAAJ,QAAA,gBACA5E,OAAA;YAAKwE,KAAK,EAAE;cAAE4C,SAAS,EAAE,QAAQ;cAAEe,YAAY,EAAE;YAAS,CAAE;YAAAvD,QAAA,gBAC1D5E,OAAA;cAAKwE,KAAK,EAAE;gBAAE2D,YAAY,EAAE;cAAO,CAAE;cAAAvD,QAAA,eACnC5E,OAAA,CAACP,aAAa;gBAACuD,IAAI,EAAE,EAAG;gBAACmC,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNzF,OAAA;cAAIwE,KAAK,EAAE;gBACTU,MAAM,EAAE,cAAc;gBACtBC,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzF,OAAA;cAAGwE,KAAK,EAAE;gBAAEW,KAAK,EAAE,SAAS;gBAAED,MAAM,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAE3C;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENzF,OAAA;YAAKwE,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,MAAM;cAAE0B,cAAc,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBACrE5E,OAAA;cACEyG,OAAO,EAAE5C,0BAA2B;cACpCgE,QAAQ,EAAEvH,kBAAmB;cAC7BkE,KAAK,EAAE;gBACLK,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE,OAAO;gBACdF,MAAM,EAAE,MAAM;gBACdH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBM,UAAU,EAAE,KAAK;gBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;gBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG;cACtC,CAAE;cAAAsE,QAAA,EAEDtE,kBAAkB,GAAG,aAAa,GAAG;YAAa;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eAETzF,OAAA;cACEyG,OAAO,EAAEA,CAAA,KAAM5F,yBAAyB,CAAC,KAAK,CAAE;cAChDgH,QAAQ,EAAEvH,kBAAmB;cAC7BkE,KAAK,EAAE;gBACLK,UAAU,EAAE,OAAO;gBACnBM,KAAK,EAAE,SAAS;gBAChBF,MAAM,EAAE,mBAAmB;gBAC3BH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBM,UAAU,EAAE,KAAK;gBACjBU,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;gBACtDwH,OAAO,EAAExH,kBAAkB,GAAG,GAAG,GAAG;cACtC,CAAE;cAAAsE,QAAA,EACH;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMmD,oBAAoB,GAAGA,CAAA,kBAC3B5I,OAAA;IAAKwE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEpE5E,OAAA;MAAKwE,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACA5E,OAAA;QAAIwE,KAAK,EAAE;UACTU,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELzF,OAAA;QAAKwE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACtE5E,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE4B,cAAc,EAAE,eAAe;YAAEX,UAAU,EAAE;UAAS,CAAE;UAAAd,QAAA,gBACrF5E,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAKwE,KAAK,EAAE;gBAAEa,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEgD,YAAY,EAAE;cAAU,CAAE;cAAAvD,QAAA,EAAC;YAE9E;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNzF,OAAA;cAAKwE,KAAK,EAAE;gBAAEW,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAR,QAAA,EAAC;YAExD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzF,OAAA;YAAOwE,KAAK,EAAE;cAAEmB,QAAQ,EAAE,UAAU;cAAElB,OAAO,EAAE,cAAc;cAAEmB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAC7F5E,OAAA;cAAO8C,IAAI,EAAC,UAAU;cAAC0B,KAAK,EAAE;gBAAEsD,OAAO,EAAE,CAAC;gBAAElC,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEzF,OAAA;cAAMwE,KAAK,EAAE;gBACXmB,QAAQ,EAAE,UAAU;gBACpBI,MAAM,EAAE,SAAS;gBACjBuB,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPZ,KAAK,EAAE,CAAC;gBACRD,MAAM,EAAE,CAAC;gBACT7B,UAAU,EAAE,MAAM;gBAClBmB,UAAU,EAAE,MAAM;gBAClBlB,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENzF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE4B,cAAc,EAAE,eAAe;YAAEX,UAAU,EAAE;UAAS,CAAE;UAAAd,QAAA,gBACrF5E,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAKwE,KAAK,EAAE;gBAAEa,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEgD,YAAY,EAAE;cAAU,CAAE;cAAAvD,QAAA,EAAC;YAE9E;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNzF,OAAA;cAAKwE,KAAK,EAAE;gBAAEW,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAR,QAAA,EAAC;YAExD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzF,OAAA;YAAOwE,KAAK,EAAE;cAAEmB,QAAQ,EAAE,UAAU;cAAElB,OAAO,EAAE,cAAc;cAAEmB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAC7F5E,OAAA;cAAO8C,IAAI,EAAC,UAAU;cAAC0B,KAAK,EAAE;gBAAEsD,OAAO,EAAE,CAAC;gBAAElC,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEzF,OAAA;cAAMwE,KAAK,EAAE;gBACXmB,QAAQ,EAAE,UAAU;gBACpBI,MAAM,EAAE,SAAS;gBACjBuB,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPZ,KAAK,EAAE,CAAC;gBACRD,MAAM,EAAE,CAAC;gBACT7B,UAAU,EAAE,SAAS;gBACrBmB,UAAU,EAAE,MAAM;gBAClBlB,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENzF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE4B,cAAc,EAAE,eAAe;YAAEX,UAAU,EAAE;UAAS,CAAE;UAAAd,QAAA,gBACrF5E,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAKwE,KAAK,EAAE;gBAAEa,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAEgD,YAAY,EAAE;cAAU,CAAE;cAAAvD,QAAA,EAAC;YAE9E;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNzF,OAAA;cAAKwE,KAAK,EAAE;gBAAEW,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAR,QAAA,EAAC;YAExD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzF,OAAA;YAAOwE,KAAK,EAAE;cAAEmB,QAAQ,EAAE,UAAU;cAAElB,OAAO,EAAE,cAAc;cAAEmB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAC7F5E,OAAA;cAAO8C,IAAI,EAAC,UAAU;cAAC+F,cAAc;cAACrE,KAAK,EAAE;gBAAEsD,OAAO,EAAE,CAAC;gBAAElC,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFzF,OAAA;cAAMwE,KAAK,EAAE;gBACXmB,QAAQ,EAAE,UAAU;gBACpBI,MAAM,EAAE,SAAS;gBACjBuB,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPZ,KAAK,EAAE,CAAC;gBACRD,MAAM,EAAE,CAAC;gBACT7B,UAAU,EAAE,SAAS;gBACrBmB,UAAU,EAAE,MAAM;gBAClBlB,YAAY,EAAE;cAChB;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzF,OAAA;MAAKwE,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACA5E,OAAA;QAAIwE,KAAK,EAAE;UACTU,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELzF,OAAA;QAAKwE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEqE,mBAAmB,EAAE,SAAS;UAAEnE,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAC7E5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAKwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAE+C,YAAY,EAAE;YAAU,CAAE;YAAAvD,QAAA,EAAC;UAEjF;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzF,OAAA;YAAKwE,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAKwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAE+C,YAAY,EAAE;YAAU,CAAE;YAAAvD,QAAA,EAAC;UAEjF;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzF,OAAA;YAAKwE,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAKwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAE+C,YAAY,EAAE;YAAU,CAAE;YAAAvD,QAAA,EAAC;UAEjF;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzF,OAAA;YAAKwE,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,eAClD5E,OAAA;cAAMwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAEf,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE5E,OAAA,CAACV,WAAW;gBAAC0D,IAAI,EAAE,EAAG;gBAACmC,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzF,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAKwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAE+C,YAAY,EAAE;YAAU,CAAE;YAAAvD,QAAA,EAAC;UAEjF;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzF,OAAA;YAAKwE,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,eAClD5E,OAAA;cAAMwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAEf,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE5E,OAAA,CAACV,WAAW;gBAAC0D,IAAI,EAAE,EAAG;gBAACmC,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMsD,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQ3I,SAAS;MACf,KAAK,SAAS;QACZ,OAAOiE,qBAAqB,CAAC,CAAC;MAChC,KAAK,QAAQ;QACX,OAAOuE,oBAAoB,CAAC,CAAC;MAC/B,KAAK,UAAU;QACb,oBACE5I,OAAA;UAAKwE,KAAK,EAAE;YACVK,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3BmC,SAAS,EAAE;UACb,CAAE;UAAAxC,QAAA,gBACA5E,OAAA;YAAKwE,KAAK,EAAE;cAAE2D,YAAY,EAAE;YAAO,CAAE;YAAAvD,QAAA,eACnC5E,OAAA,CAACZ,IAAI;cAAC4D,IAAI,EAAE,EAAG;cAACmC,KAAK,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNzF,OAAA;YAAIwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAE8C,YAAY,EAAE;YAAS,CAAE;YAAAvD,QAAA,EAAC;UAEhG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzF,OAAA;YAAGwE,KAAK,EAAE;cAAEW,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,EAAC;UAEhC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV,KAAK,eAAe;QAClB,oBACEzF,OAAA;UAAKwE,KAAK,EAAE;YACVK,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3BmC,SAAS,EAAE;UACb,CAAE;UAAAxC,QAAA,gBACA5E,OAAA;YAAKwE,KAAK,EAAE;cAAE2D,YAAY,EAAE;YAAO,CAAE;YAAAvD,QAAA,eACnC5E,OAAA,CAACX,IAAI;cAAC2D,IAAI,EAAE,EAAG;cAACmC,KAAK,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNzF,OAAA;YAAIwE,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAE8C,YAAY,EAAE;YAAS,CAAE;YAAAvD,QAAA,EAAC;UAEhG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzF,OAAA;YAAGwE,KAAK,EAAE;cAAEW,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,EAAC;UAEhC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEzF,OAAA;IAAA4E,QAAA,gBAGE5E,OAAA;MAAKwE,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBoD,YAAY,EAAE,MAAM;QACpBnD,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,eACA5E,OAAA;QAAKwE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEqE,QAAQ,EAAE;QAAO,CAAE;QAAApE,QAAA,EAC5D3C,IAAI,CAACgH,GAAG,CAACC,GAAG,iBACXlJ,OAAA;UAEEyG,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC6I,GAAG,CAAChH,GAAU,CAAE;UAC5CsC,KAAK,EAAE;YACLK,UAAU,EAAEzE,SAAS,KAAK8I,GAAG,CAAChH,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjBiD,KAAK,EAAE/E,SAAS,KAAK8I,GAAG,CAAChH,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD+C,MAAM,EAAE7E,SAAS,KAAK8I,GAAG,CAAChH,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5D4C,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzBgB,MAAM,EAAE,SAAS;YACjBV,UAAU,EAAE,KAAK;YACjBZ,OAAO,EAAE,MAAM;YACfiB,UAAU,EAAE,QAAQ;YACpBf,GAAG,EAAE,QAAQ;YACbqB,UAAU,EAAE;UACd,CAAE;UAAApB,QAAA,gBAEF5E,OAAA,CAACkJ,GAAG,CAAC9G,IAAI;YAACY,IAAI,EAAE;UAAG;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrByD,GAAG,CAAC/G,KAAK;QAAA,GAnBL+G,GAAG,CAAChH,GAAG;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLsD,aAAa,CAAC,CAAC,eAGhB/I,OAAA;MAAA4E,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACxF,EAAA,CA7oCIf,QAAkB;EAAA,QACYF,YAAY;AAAA;AAAAmK,EAAA,GAD1CjK,QAAkB;AA+oCxB,eAAeA,QAAQ;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}