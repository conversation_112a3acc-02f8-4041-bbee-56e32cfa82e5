{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"4\",\n  x2: \"4\",\n  y1: \"21\",\n  y2: \"14\",\n  key: \"1p332r\"\n}], [\"line\", {\n  x1: \"4\",\n  x2: \"4\",\n  y1: \"10\",\n  y2: \"3\",\n  key: \"gb41h5\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"21\",\n  y2: \"12\",\n  key: \"hf2csr\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"3\",\n  key: \"1kfi7u\"\n}], [\"line\", {\n  x1: \"20\",\n  x2: \"20\",\n  y1: \"21\",\n  y2: \"16\",\n  key: \"1lhrwl\"\n}], [\"line\", {\n  x1: \"20\",\n  x2: \"20\",\n  y1: \"12\",\n  y2: \"3\",\n  key: \"16vvfq\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"6\",\n  y1: \"14\",\n  y2: \"14\",\n  key: \"1uebub\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"14\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"1yglbp\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"22\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"1jxqpz\"\n}]];\nconst SlidersVertical = createLucideIcon(\"sliders-vertical\", __iconNode);\nexport { __iconNode, SlidersVertical as default };\n//# sourceMappingURL=sliders-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}