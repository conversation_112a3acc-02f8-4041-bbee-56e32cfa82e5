{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 3a2 2 0 0 1 2 2v6a1 1 0 0 1-20 0V5a2 2 0 0 1 2-2z\",\n  key: \"1uodqw\"\n}], [\"path\", {\n  d: \"m8 10 4 4 4-4\",\n  key: \"1mxd5q\"\n}]];\nconst Pocket = createLucideIcon(\"pocket\", __iconNode);\nexport { __iconNode, Pocket as default };\n//# sourceMappingURL=pocket.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}