{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 15 5 5 5-5\",\n  key: \"1hpjnr\"\n}], [\"path\", {\n  d: \"M4 4h7a4 4 0 0 1 4 4v12\",\n  key: \"wcbgct\"\n}]];\nconst CornerRightDown = createLucideIcon(\"corner-right-down\", __iconNode);\nexport { __iconNode, CornerRightDown as default };\n//# sourceMappingURL=corner-right-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}