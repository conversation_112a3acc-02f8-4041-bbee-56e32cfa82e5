{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 22a5 5 0 0 1-2-4\",\n  key: \"umushi\"\n}], [\"path\", {\n  d: \"M3.3 14A6.8 6.8 0 0 1 2 10c0-4.4 4.5-8 10-8s10 3.6 10 8-4.5 8-10 8a12 12 0 0 1-5-1\",\n  key: \"146dds\"\n}], [\"path\", {\n  d: \"M5 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4z\",\n  key: \"bq3ynw\"\n}]];\nconst Lasso = createLucideIcon(\"lasso\", __iconNode);\nexport { __iconNode, Lasso as default };\n//# sourceMappingURL=lasso.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}