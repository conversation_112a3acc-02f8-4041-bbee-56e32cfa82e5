{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 15h14\",\n  key: \"m0yey3\"\n}], [\"path\", {\n  d: \"M5 9h14\",\n  key: \"7tsvo6\"\n}], [\"path\", {\n  d: \"m14 20-5-5 6-6-5-5\",\n  key: \"1jo42i\"\n}]];\nconst RailSymbol = createLucideIcon(\"rail-symbol\", __iconNode);\nexport { __iconNode, RailSymbol as default };\n//# sourceMappingURL=rail-symbol.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}