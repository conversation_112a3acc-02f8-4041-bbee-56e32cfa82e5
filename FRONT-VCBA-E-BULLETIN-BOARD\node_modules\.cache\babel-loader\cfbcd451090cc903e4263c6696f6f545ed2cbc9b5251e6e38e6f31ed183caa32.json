{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}]];\nconst Dice1 = createLucideIcon(\"dice-1\", __iconNode);\nexport { __iconNode, Dice1 as default };\n//# sourceMappingURL=dice-1.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}