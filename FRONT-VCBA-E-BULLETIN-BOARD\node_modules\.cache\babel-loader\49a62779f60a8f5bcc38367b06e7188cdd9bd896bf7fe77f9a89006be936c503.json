{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\CascadingCategoryDropdown.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { ChevronDown, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CascadingCategoryDropdown = ({\n  categories,\n  selectedCategoryId,\n  selectedSubcategoryId,\n  onCategoryChange,\n  onSubcategoryChange,\n  placeholder = 'Select Category',\n  disabled = false,\n  error,\n  required = false,\n  style,\n  className\n}) => {\n  _s();\n  var _categories$find, _categories$find$subc;\n  const [isOpen, setIsOpen] = useState(false);\n  const [hoveredCategory, setHoveredCategory] = useState(null);\n  const [submenuPosition, setSubmenuPosition] = useState(null);\n  const dropdownRef = useRef(null);\n  const submenuRef = useRef(null);\n  const hoverTimeoutRef = useRef(null);\n\n  // Debug categories\n  useEffect(() => {\n    console.log('🔍 CascadingCategoryDropdown - Categories received:', {\n      count: (categories === null || categories === void 0 ? void 0 : categories.length) || 0,\n      categories: categories === null || categories === void 0 ? void 0 : categories.map(cat => {\n        var _cat$subcategories;\n        return {\n          id: cat.category_id,\n          name: cat.name,\n          hasSubcategories: ((_cat$subcategories = cat.subcategories) === null || _cat$subcategories === void 0 ? void 0 : _cat$subcategories.length) || 0\n        };\n      })\n    });\n  }, [categories]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n        setHoveredCategory(null);\n        setSubmenuPosition(null);\n        // Clear any pending timeout\n        if (hoverTimeoutRef.current) {\n          clearTimeout(hoverTimeoutRef.current);\n          hoverTimeoutRef.current = null;\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      // Cleanup timeout on unmount\n      if (hoverTimeoutRef.current) {\n        clearTimeout(hoverTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Get display text for selected items\n  const getDisplayText = () => {\n    if (!selectedCategoryId) return placeholder;\n    const category = categories.find(cat => cat.category_id === selectedCategoryId);\n    if (!category) return placeholder;\n    if (selectedSubcategoryId && category.subcategories) {\n      const subcategory = category.subcategories.find(sub => sub.subcategory_id === selectedSubcategoryId);\n      if (subcategory) {\n        return `${category.name} > ${subcategory.name}`;\n      }\n    }\n    return category.name;\n  };\n  const handleCategorySelect = categoryId => {\n    console.log('🎯 CascadingCategoryDropdown - Category selected:', categoryId);\n    onCategoryChange(categoryId);\n    onSubcategoryChange(null); // Clear subcategory when category changes\n    setIsOpen(false);\n    setHoveredCategory(null);\n  };\n  const handleSubcategorySelect = subcategoryId => {\n    // Find the parent category of the selected subcategory\n    const parentCategory = categories.find(cat => {\n      var _cat$subcategories2;\n      return (_cat$subcategories2 = cat.subcategories) === null || _cat$subcategories2 === void 0 ? void 0 : _cat$subcategories2.some(sub => sub.subcategory_id === subcategoryId);\n    });\n    if (parentCategory) {\n      // Automatically select the parent category when subcategory is selected\n      onCategoryChange(parentCategory.category_id);\n    }\n    onSubcategoryChange(subcategoryId);\n    setIsOpen(false);\n    setHoveredCategory(null);\n  };\n  const handleCategoryHover = (categoryId, event) => {\n    // Clear any existing timeout\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n      hoverTimeoutRef.current = null;\n    }\n    const category = categories.find(cat => cat.category_id === categoryId);\n    if (category && category.subcategories && category.subcategories.length > 0) {\n      var _dropdownRef$current;\n      console.log('🎯 Hovering category with subcategories:', category.name, category.subcategories.length);\n      setHoveredCategory(categoryId);\n\n      // Calculate submenu position\n      const rect = event.currentTarget.getBoundingClientRect();\n      const dropdownRect = (_dropdownRef$current = dropdownRef.current) === null || _dropdownRef$current === void 0 ? void 0 : _dropdownRef$current.getBoundingClientRect();\n      if (dropdownRect) {\n        const viewportWidth = window.innerWidth;\n        const submenuWidth = 200; // Approximate submenu width\n        let leftPosition = dropdownRect.width + 4; // Add 4px gap between main menu and submenu\n\n        // Check if submenu would go off-screen and adjust if needed\n        if (dropdownRect.left + leftPosition + submenuWidth > viewportWidth) {\n          leftPosition = -submenuWidth - 4; // Position to the left instead\n        }\n        setSubmenuPosition({\n          top: Math.max(0, rect.top - dropdownRect.top),\n          // Ensure it doesn't go above dropdown\n          left: leftPosition\n        });\n      }\n    } else {\n      console.log('🎯 Hovering category without subcategories:', (category === null || category === void 0 ? void 0 : category.name) || 'Unknown');\n      setHoveredCategory(null);\n      setSubmenuPosition(null);\n    }\n  };\n  const handleCategoryLeave = () => {\n    console.log('🚪 Leaving category, setting timeout...');\n    // Clear any existing timeout\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n    }\n\n    // Set a longer delay to allow moving to submenu\n    hoverTimeoutRef.current = setTimeout(() => {\n      console.log('⏰ Timeout triggered, closing submenu');\n      setHoveredCategory(null);\n      setSubmenuPosition(null);\n    }, 300); // Increased delay to 300ms\n  };\n  const handleSubmenuEnter = () => {\n    console.log('🎯 Entering submenu, clearing timeout');\n    // Clear timeout when entering submenu\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n      hoverTimeoutRef.current = null;\n    }\n  };\n  const handleSubmenuLeave = () => {\n    console.log('🚪 Leaving submenu, closing immediately');\n    // Immediately close submenu when leaving it\n    setHoveredCategory(null);\n    setSubmenuPosition(null);\n  };\n\n  // Styles\n  const dropdownStyle = {\n    position: 'relative',\n    display: 'inline-block',\n    width: '100%',\n    ...style\n  };\n  const triggerStyle = {\n    width: '100%',\n    padding: '0.75rem 1rem',\n    border: error ? '2px solid #ef4444' : '1px solid #d1d5db',\n    borderRadius: '8px',\n    backgroundColor: disabled ? '#f9fafb' : 'white',\n    cursor: disabled ? 'not-allowed' : 'pointer',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    fontSize: '0.875rem',\n    color: selectedCategoryId ? '#374151' : '#9ca3af',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    boxShadow: isOpen ? '0 0 0 3px rgba(59, 130, 246, 0.1)' : 'none'\n  };\n  const menuStyle = {\n    position: 'absolute',\n    top: '100%',\n    left: 0,\n    right: 0,\n    backgroundColor: 'white',\n    border: '1px solid #e5e7eb',\n    borderRadius: '8px',\n    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    zIndex: 1000,\n    marginTop: '4px',\n    overflow: 'hidden' // Remove scrollbar completely\n  };\n  const menuItemStyle = {\n    padding: '0.875rem 1rem',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    fontSize: '0.875rem',\n    color: '#374151',\n    borderBottom: '1px solid #f3f4f6',\n    transition: 'background-color 0.15s ease',\n    position: 'relative'\n  };\n  const submenuStyle = {\n    position: 'absolute',\n    top: (submenuPosition === null || submenuPosition === void 0 ? void 0 : submenuPosition.top) || 0,\n    left: (submenuPosition === null || submenuPosition === void 0 ? void 0 : submenuPosition.left) || 0,\n    backgroundColor: 'white',\n    border: '1px solid #e5e7eb',\n    borderRadius: '8px',\n    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    zIndex: 1001,\n    minWidth: '200px',\n    overflow: 'hidden' // Remove scrollbar from submenu as well\n  };\n  const submenuItemStyle = {\n    padding: '0.75rem 1rem',\n    cursor: 'pointer',\n    fontSize: '0.875rem',\n    color: '#374151',\n    borderBottom: '1px solid #f3f4f6',\n    transition: 'background-color 0.15s ease'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: dropdownRef,\n    style: dropdownStyle,\n    className: className,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: triggerStyle,\n      onClick: () => !disabled && setIsOpen(!isOpen),\n      onKeyDown: e => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          e.preventDefault();\n          !disabled && setIsOpen(!isOpen);\n        }\n      },\n      tabIndex: disabled ? -1 : 0,\n      role: \"button\",\n      \"aria-expanded\": isOpen,\n      \"aria-haspopup\": \"listbox\",\n      \"aria-required\": required,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: getDisplayText()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 16,\n        style: {\n          transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',\n          transition: 'transform 0.2s ease'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: menuStyle,\n      role: \"listbox\",\n      children: categories.map(category => {\n        const hasSubcategories = category.subcategories && category.subcategories.length > 0;\n        const isSelected = selectedCategoryId === category.category_id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...menuItemStyle,\n            backgroundColor: isSelected ? '#eff6ff' : hoveredCategory === category.category_id ? '#f9fafb' : 'transparent'\n          },\n          onClick: () => handleCategorySelect(category.category_id),\n          onMouseEnter: e => handleCategoryHover(category.category_id, e),\n          onMouseLeave: handleCategoryLeave,\n          role: \"option\",\n          \"aria-selected\": isSelected,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: category.color_code,\n                marginRight: '0.75rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), hasSubcategories && /*#__PURE__*/_jsxDEV(ChevronRight, {\n            size: 14,\n            style: {\n              color: '#9ca3af'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 19\n          }, this)]\n        }, category.category_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 9\n    }, this), isOpen && hoveredCategory && submenuPosition && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: submenuPosition.top,\n          left: submenuPosition.left > 0 ? menuStyle.right || 0 : submenuPosition.left + 200,\n          width: '8px',\n          height: '40px',\n          zIndex: 1000,\n          backgroundColor: 'transparent'\n        },\n        onMouseEnter: handleSubmenuEnter\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: submenuRef,\n        style: submenuStyle,\n        onMouseEnter: handleSubmenuEnter,\n        onMouseLeave: handleSubmenuLeave,\n        children: (_categories$find = categories.find(cat => cat.category_id === hoveredCategory)) === null || _categories$find === void 0 ? void 0 : (_categories$find$subc = _categories$find.subcategories) === null || _categories$find$subc === void 0 ? void 0 : _categories$find$subc.map(subcategory => {\n          const isSelected = selectedSubcategoryId === subcategory.subcategory_id;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...submenuItemStyle,\n              backgroundColor: isSelected ? '#eff6ff' : 'transparent'\n            },\n            onClick: () => handleSubcategorySelect(subcategory.subcategory_id),\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = isSelected ? '#eff6ff' : '#f9fafb';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = isSelected ? '#eff6ff' : 'transparent';\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '8px',\n                  height: '8px',\n                  borderRadius: '50%',\n                  backgroundColor: subcategory.color_code,\n                  marginRight: '0.75rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: subcategory.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this)\n          }, subcategory.subcategory_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '0.25rem',\n        fontSize: '0.75rem',\n        color: '#ef4444'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(CascadingCategoryDropdown, \"b+9SVSK+KUoW/oM6TK81fgdEg2w=\");\n_c = CascadingCategoryDropdown;\nexport default CascadingCategoryDropdown;\nvar _c;\n$RefreshReg$(_c, \"CascadingCategoryDropdown\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ChevronDown", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CascadingCategoryDropdown", "categories", "selectedCategoryId", "selectedSubcategoryId", "onCategoryChange", "onSubcategoryChange", "placeholder", "disabled", "error", "required", "style", "className", "_s", "_categories$find", "_categories$find$subc", "isOpen", "setIsOpen", "hoveredCategory", "setHoveredCategory", "submenuPosition", "setSubmenuPosition", "dropdownRef", "submenuRef", "hoverTimeoutRef", "console", "log", "count", "length", "map", "cat", "_cat$subcategories", "id", "category_id", "name", "hasSubcategories", "subcategories", "handleClickOutside", "event", "current", "contains", "target", "clearTimeout", "document", "addEventListener", "removeEventListener", "getDisplayText", "category", "find", "subcategory", "sub", "subcategory_id", "handleCategorySelect", "categoryId", "handleSubcategorySelect", "subcategoryId", "parentCategory", "_cat$subcategories2", "some", "handleCategoryHover", "_dropdownRef$current", "rect", "currentTarget", "getBoundingClientRect", "dropdownRect", "viewportWidth", "window", "innerWidth", "submenuWidth", "leftPosition", "width", "left", "top", "Math", "max", "handleCategoryLeave", "setTimeout", "handleSubmenuEnter", "handleSubmenuLeave", "dropdownStyle", "position", "display", "triggerStyle", "padding", "border", "borderRadius", "backgroundColor", "cursor", "justifyContent", "alignItems", "fontSize", "color", "outline", "transition", "boxShadow", "menuStyle", "right", "zIndex", "marginTop", "overflow", "menuItemStyle", "borderBottom", "submenuStyle", "min<PERSON><PERSON><PERSON>", "submenuItemStyle", "ref", "children", "onClick", "onKeyDown", "e", "key", "preventDefault", "tabIndex", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "transform", "isSelected", "onMouseEnter", "onMouseLeave", "height", "color_code", "marginRight", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/CascadingCategoryDropdown.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { ChevronDown, ChevronRight } from 'lucide-react';\nimport { Category } from '../../services/announcementService';\n\ninterface CascadingCategoryDropdownProps {\n  categories: Category[];\n  selectedCategoryId?: number;\n  selectedSubcategoryId?: number;\n  onCategoryChange: (categoryId: number | null) => void;\n  onSubcategoryChange: (subcategoryId: number | null) => void;\n  placeholder?: string;\n  disabled?: boolean;\n  error?: string;\n  required?: boolean;\n  style?: React.CSSProperties;\n  className?: string;\n}\n\nconst CascadingCategoryDropdown: React.FC<CascadingCategoryDropdownProps> = ({\n  categories,\n  selectedCategoryId,\n  selectedSubcategoryId,\n  onCategoryChange,\n  onSubcategoryChange,\n  placeholder = 'Select Category',\n  disabled = false,\n  error,\n  required = false,\n  style,\n  className\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [hoveredCategory, setHoveredCategory] = useState<number | null>(null);\n  const [submenuPosition, setSubmenuPosition] = useState<{ top: number; left: number } | null>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const submenuRef = useRef<HTMLDivElement>(null);\n  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Debug categories\n  useEffect(() => {\n    console.log('🔍 CascadingCategoryDropdown - Categories received:', {\n      count: categories?.length || 0,\n      categories: categories?.map(cat => ({ id: cat.category_id, name: cat.name, hasSubcategories: cat.subcategories?.length || 0 }))\n    });\n  }, [categories]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setHoveredCategory(null);\n        setSubmenuPosition(null);\n        // Clear any pending timeout\n        if (hoverTimeoutRef.current) {\n          clearTimeout(hoverTimeoutRef.current);\n          hoverTimeoutRef.current = null;\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      // Cleanup timeout on unmount\n      if (hoverTimeoutRef.current) {\n        clearTimeout(hoverTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Get display text for selected items\n  const getDisplayText = () => {\n    if (!selectedCategoryId) return placeholder;\n    \n    const category = categories.find(cat => cat.category_id === selectedCategoryId);\n    if (!category) return placeholder;\n    \n    if (selectedSubcategoryId && category.subcategories) {\n      const subcategory = category.subcategories.find(sub => sub.subcategory_id === selectedSubcategoryId);\n      if (subcategory) {\n        return `${category.name} > ${subcategory.name}`;\n      }\n    }\n    \n    return category.name;\n  };\n\n  const handleCategorySelect = (categoryId: number) => {\n    console.log('🎯 CascadingCategoryDropdown - Category selected:', categoryId);\n    onCategoryChange(categoryId);\n    onSubcategoryChange(null); // Clear subcategory when category changes\n    setIsOpen(false);\n    setHoveredCategory(null);\n  };\n\n  const handleSubcategorySelect = (subcategoryId: number) => {\n    // Find the parent category of the selected subcategory\n    const parentCategory = categories.find(cat =>\n      cat.subcategories?.some(sub => sub.subcategory_id === subcategoryId)\n    );\n\n    if (parentCategory) {\n      // Automatically select the parent category when subcategory is selected\n      onCategoryChange(parentCategory.category_id);\n    }\n\n    onSubcategoryChange(subcategoryId);\n    setIsOpen(false);\n    setHoveredCategory(null);\n  };\n\n  const handleCategoryHover = (categoryId: number, event: React.MouseEvent<HTMLDivElement>) => {\n    // Clear any existing timeout\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n      hoverTimeoutRef.current = null;\n    }\n\n    const category = categories.find(cat => cat.category_id === categoryId);\n    if (category && category.subcategories && category.subcategories.length > 0) {\n      console.log('🎯 Hovering category with subcategories:', category.name, category.subcategories.length);\n      setHoveredCategory(categoryId);\n\n      // Calculate submenu position\n      const rect = event.currentTarget.getBoundingClientRect();\n      const dropdownRect = dropdownRef.current?.getBoundingClientRect();\n\n      if (dropdownRect) {\n        const viewportWidth = window.innerWidth;\n        const submenuWidth = 200; // Approximate submenu width\n        let leftPosition = dropdownRect.width + 4; // Add 4px gap between main menu and submenu\n\n        // Check if submenu would go off-screen and adjust if needed\n        if (dropdownRect.left + leftPosition + submenuWidth > viewportWidth) {\n          leftPosition = -submenuWidth - 4; // Position to the left instead\n        }\n\n        setSubmenuPosition({\n          top: Math.max(0, rect.top - dropdownRect.top), // Ensure it doesn't go above dropdown\n          left: leftPosition\n        });\n      }\n    } else {\n      console.log('🎯 Hovering category without subcategories:', category?.name || 'Unknown');\n      setHoveredCategory(null);\n      setSubmenuPosition(null);\n    }\n  };\n\n  const handleCategoryLeave = () => {\n    console.log('🚪 Leaving category, setting timeout...');\n    // Clear any existing timeout\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n    }\n\n    // Set a longer delay to allow moving to submenu\n    hoverTimeoutRef.current = setTimeout(() => {\n      console.log('⏰ Timeout triggered, closing submenu');\n      setHoveredCategory(null);\n      setSubmenuPosition(null);\n    }, 300); // Increased delay to 300ms\n  };\n\n  const handleSubmenuEnter = () => {\n    console.log('🎯 Entering submenu, clearing timeout');\n    // Clear timeout when entering submenu\n    if (hoverTimeoutRef.current) {\n      clearTimeout(hoverTimeoutRef.current);\n      hoverTimeoutRef.current = null;\n    }\n  };\n\n  const handleSubmenuLeave = () => {\n    console.log('🚪 Leaving submenu, closing immediately');\n    // Immediately close submenu when leaving it\n    setHoveredCategory(null);\n    setSubmenuPosition(null);\n  };\n\n  // Styles\n  const dropdownStyle: React.CSSProperties = {\n    position: 'relative',\n    display: 'inline-block',\n    width: '100%',\n    ...style\n  };\n\n  const triggerStyle: React.CSSProperties = {\n    width: '100%',\n    padding: '0.75rem 1rem',\n    border: error ? '2px solid #ef4444' : '1px solid #d1d5db',\n    borderRadius: '8px',\n    backgroundColor: disabled ? '#f9fafb' : 'white',\n    cursor: disabled ? 'not-allowed' : 'pointer',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    fontSize: '0.875rem',\n    color: selectedCategoryId ? '#374151' : '#9ca3af',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    boxShadow: isOpen ? '0 0 0 3px rgba(59, 130, 246, 0.1)' : 'none'\n  };\n\n  const menuStyle: React.CSSProperties = {\n    position: 'absolute',\n    top: '100%',\n    left: 0,\n    right: 0,\n    backgroundColor: 'white',\n    border: '1px solid #e5e7eb',\n    borderRadius: '8px',\n    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    zIndex: 1000,\n    marginTop: '4px',\n    overflow: 'hidden' // Remove scrollbar completely\n  };\n\n  const menuItemStyle: React.CSSProperties = {\n    padding: '0.875rem 1rem',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    fontSize: '0.875rem',\n    color: '#374151',\n    borderBottom: '1px solid #f3f4f6',\n    transition: 'background-color 0.15s ease',\n    position: 'relative'\n  };\n\n  const submenuStyle: React.CSSProperties = {\n    position: 'absolute',\n    top: submenuPosition?.top || 0,\n    left: submenuPosition?.left || 0,\n    backgroundColor: 'white',\n    border: '1px solid #e5e7eb',\n    borderRadius: '8px',\n    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    zIndex: 1001,\n    minWidth: '200px',\n    overflow: 'hidden' // Remove scrollbar from submenu as well\n  };\n\n  const submenuItemStyle: React.CSSProperties = {\n    padding: '0.75rem 1rem',\n    cursor: 'pointer',\n    fontSize: '0.875rem',\n    color: '#374151',\n    borderBottom: '1px solid #f3f4f6',\n    transition: 'background-color 0.15s ease'\n  };\n\n  return (\n    <div ref={dropdownRef} style={dropdownStyle} className={className}>\n      {/* Trigger Button */}\n      <div\n        style={triggerStyle}\n        onClick={() => !disabled && setIsOpen(!isOpen)}\n        onKeyDown={(e) => {\n          if (e.key === 'Enter' || e.key === ' ') {\n            e.preventDefault();\n            !disabled && setIsOpen(!isOpen);\n          }\n        }}\n        tabIndex={disabled ? -1 : 0}\n        role=\"button\"\n        aria-expanded={isOpen}\n        aria-haspopup=\"listbox\"\n        aria-required={required}\n      >\n        <span>{getDisplayText()}</span>\n        <ChevronDown \n          size={16} \n          style={{ \n            transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',\n            transition: 'transform 0.2s ease'\n          }} \n        />\n      </div>\n\n      {/* Main Menu */}\n      {isOpen && (\n        <div style={menuStyle} role=\"listbox\">\n          {categories.map((category) => {\n            const hasSubcategories = category.subcategories && category.subcategories.length > 0;\n            const isSelected = selectedCategoryId === category.category_id;\n            \n            return (\n              <div\n                key={category.category_id}\n                style={{\n                  ...menuItemStyle,\n                  backgroundColor: isSelected ? '#eff6ff' : \n                                 hoveredCategory === category.category_id ? '#f9fafb' : 'transparent'\n                }}\n                onClick={() => handleCategorySelect(category.category_id)}\n                onMouseEnter={(e) => handleCategoryHover(category.category_id, e)}\n                onMouseLeave={handleCategoryLeave}\n                role=\"option\"\n                aria-selected={isSelected}\n              >\n                <div style={{ display: 'flex', alignItems: 'center' }}>\n                  <div\n                    style={{\n                      width: '12px',\n                      height: '12px',\n                      borderRadius: '50%',\n                      backgroundColor: category.color_code,\n                      marginRight: '0.75rem'\n                    }}\n                  />\n                  <span>{category.name}</span>\n                </div>\n                {hasSubcategories && (\n                  <ChevronRight size={14} style={{ color: '#9ca3af' }} />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* Submenu */}\n      {isOpen && hoveredCategory && submenuPosition && (\n        <>\n          {/* Invisible bridge to prevent submenu from disappearing */}\n          <div\n            style={{\n              position: 'absolute',\n              top: submenuPosition.top,\n              left: submenuPosition.left > 0 ? menuStyle.right || 0 : (submenuPosition.left + 200),\n              width: '8px',\n              height: '40px',\n              zIndex: 1000,\n              backgroundColor: 'transparent'\n            }}\n            onMouseEnter={handleSubmenuEnter}\n          />\n          <div\n            ref={submenuRef}\n            style={submenuStyle}\n            onMouseEnter={handleSubmenuEnter}\n            onMouseLeave={handleSubmenuLeave}\n          >\n          {categories\n            .find(cat => cat.category_id === hoveredCategory)\n            ?.subcategories?.map((subcategory) => {\n              const isSelected = selectedSubcategoryId === subcategory.subcategory_id;\n              \n              return (\n                <div\n                  key={subcategory.subcategory_id}\n                  style={{\n                    ...submenuItemStyle,\n                    backgroundColor: isSelected ? '#eff6ff' : 'transparent'\n                  }}\n                  onClick={() => handleSubcategorySelect(subcategory.subcategory_id)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = isSelected ? '#eff6ff' : '#f9fafb';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = isSelected ? '#eff6ff' : 'transparent';\n                  }}\n                >\n                  <div style={{ display: 'flex', alignItems: 'center' }}>\n                    <div\n                      style={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        backgroundColor: subcategory.color_code,\n                        marginRight: '0.75rem'\n                      }}\n                    />\n                    <span>{subcategory.name}</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div style={{\n          marginTop: '0.25rem',\n          fontSize: '0.75rem',\n          color: '#ef4444'\n        }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CascadingCategoryDropdown;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAiBzD,MAAMC,yBAAmE,GAAGA,CAAC;EAC3EC,UAAU;EACVC,kBAAkB;EAClBC,qBAAqB;EACrBC,gBAAgB;EAChBC,mBAAmB;EACnBC,WAAW,GAAG,iBAAiB;EAC/BC,QAAQ,GAAG,KAAK;EAChBC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAuC,IAAI,CAAC;EAClG,MAAM8B,WAAW,GAAG7B,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAM8B,UAAU,GAAG9B,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM+B,eAAe,GAAG/B,MAAM,CAAwB,IAAI,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd+B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;MACjEC,KAAK,EAAE,CAAAzB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0B,MAAM,KAAI,CAAC;MAC9B1B,UAAU,EAAEA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE2B,GAAG,CAACC,GAAG;QAAA,IAAAC,kBAAA;QAAA,OAAK;UAAEC,EAAE,EAAEF,GAAG,CAACG,WAAW;UAAEC,IAAI,EAAEJ,GAAG,CAACI,IAAI;UAAEC,gBAAgB,EAAE,EAAAJ,kBAAA,GAAAD,GAAG,CAACM,aAAa,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBH,MAAM,KAAI;QAAE,CAAC;MAAA,CAAC;IAChI,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1B,UAAU,CAAC,CAAC;;EAEhB;EACAR,SAAS,CAAC,MAAM;IACd,MAAM2C,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIhB,WAAW,CAACiB,OAAO,IAAI,CAACjB,WAAW,CAACiB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC9ExB,SAAS,CAAC,KAAK,CAAC;QAChBE,kBAAkB,CAAC,IAAI,CAAC;QACxBE,kBAAkB,CAAC,IAAI,CAAC;QACxB;QACA,IAAIG,eAAe,CAACe,OAAO,EAAE;UAC3BG,YAAY,CAAClB,eAAe,CAACe,OAAO,CAAC;UACrCf,eAAe,CAACe,OAAO,GAAG,IAAI;QAChC;MACF;IACF,CAAC;IAEDI,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXM,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAER,kBAAkB,CAAC;MAC7D;MACA,IAAIb,eAAe,CAACe,OAAO,EAAE;QAC3BG,YAAY,CAAClB,eAAe,CAACe,OAAO,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC3C,kBAAkB,EAAE,OAAOI,WAAW;IAE3C,MAAMwC,QAAQ,GAAG7C,UAAU,CAAC8C,IAAI,CAAClB,GAAG,IAAIA,GAAG,CAACG,WAAW,KAAK9B,kBAAkB,CAAC;IAC/E,IAAI,CAAC4C,QAAQ,EAAE,OAAOxC,WAAW;IAEjC,IAAIH,qBAAqB,IAAI2C,QAAQ,CAACX,aAAa,EAAE;MACnD,MAAMa,WAAW,GAAGF,QAAQ,CAACX,aAAa,CAACY,IAAI,CAACE,GAAG,IAAIA,GAAG,CAACC,cAAc,KAAK/C,qBAAqB,CAAC;MACpG,IAAI6C,WAAW,EAAE;QACf,OAAO,GAAGF,QAAQ,CAACb,IAAI,MAAMe,WAAW,CAACf,IAAI,EAAE;MACjD;IACF;IAEA,OAAOa,QAAQ,CAACb,IAAI;EACtB,CAAC;EAED,MAAMkB,oBAAoB,GAAIC,UAAkB,IAAK;IACnD5B,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE2B,UAAU,CAAC;IAC5EhD,gBAAgB,CAACgD,UAAU,CAAC;IAC5B/C,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3BW,SAAS,CAAC,KAAK,CAAC;IAChBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmC,uBAAuB,GAAIC,aAAqB,IAAK;IACzD;IACA,MAAMC,cAAc,GAAGtD,UAAU,CAAC8C,IAAI,CAAClB,GAAG;MAAA,IAAA2B,mBAAA;MAAA,QAAAA,mBAAA,GACxC3B,GAAG,CAACM,aAAa,cAAAqB,mBAAA,uBAAjBA,mBAAA,CAAmBC,IAAI,CAACR,GAAG,IAAIA,GAAG,CAACC,cAAc,KAAKI,aAAa,CAAC;IAAA,CACtE,CAAC;IAED,IAAIC,cAAc,EAAE;MAClB;MACAnD,gBAAgB,CAACmD,cAAc,CAACvB,WAAW,CAAC;IAC9C;IAEA3B,mBAAmB,CAACiD,aAAa,CAAC;IAClCtC,SAAS,CAAC,KAAK,CAAC;IAChBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwC,mBAAmB,GAAGA,CAACN,UAAkB,EAAEf,KAAuC,KAAK;IAC3F;IACA,IAAId,eAAe,CAACe,OAAO,EAAE;MAC3BG,YAAY,CAAClB,eAAe,CAACe,OAAO,CAAC;MACrCf,eAAe,CAACe,OAAO,GAAG,IAAI;IAChC;IAEA,MAAMQ,QAAQ,GAAG7C,UAAU,CAAC8C,IAAI,CAAClB,GAAG,IAAIA,GAAG,CAACG,WAAW,KAAKoB,UAAU,CAAC;IACvE,IAAIN,QAAQ,IAAIA,QAAQ,CAACX,aAAa,IAAIW,QAAQ,CAACX,aAAa,CAACR,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAgC,oBAAA;MAC3EnC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEqB,QAAQ,CAACb,IAAI,EAAEa,QAAQ,CAACX,aAAa,CAACR,MAAM,CAAC;MACrGT,kBAAkB,CAACkC,UAAU,CAAC;;MAE9B;MACA,MAAMQ,IAAI,GAAGvB,KAAK,CAACwB,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,YAAY,IAAAJ,oBAAA,GAAGtC,WAAW,CAACiB,OAAO,cAAAqB,oBAAA,uBAAnBA,oBAAA,CAAqBG,qBAAqB,CAAC,CAAC;MAEjE,IAAIC,YAAY,EAAE;QAChB,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;QACvC,MAAMC,YAAY,GAAG,GAAG,CAAC,CAAC;QAC1B,IAAIC,YAAY,GAAGL,YAAY,CAACM,KAAK,GAAG,CAAC,CAAC,CAAC;;QAE3C;QACA,IAAIN,YAAY,CAACO,IAAI,GAAGF,YAAY,GAAGD,YAAY,GAAGH,aAAa,EAAE;UACnEI,YAAY,GAAG,CAACD,YAAY,GAAG,CAAC,CAAC,CAAC;QACpC;QAEA/C,kBAAkB,CAAC;UACjBmD,GAAG,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEb,IAAI,CAACW,GAAG,GAAGR,YAAY,CAACQ,GAAG,CAAC;UAAE;UAC/CD,IAAI,EAAEF;QACR,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL5C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,CAAAqB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEb,IAAI,KAAI,SAAS,CAAC;MACvFf,kBAAkB,CAAC,IAAI,CAAC;MACxBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAMsD,mBAAmB,GAAGA,CAAA,KAAM;IAChClD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD;IACA,IAAIF,eAAe,CAACe,OAAO,EAAE;MAC3BG,YAAY,CAAClB,eAAe,CAACe,OAAO,CAAC;IACvC;;IAEA;IACAf,eAAe,CAACe,OAAO,GAAGqC,UAAU,CAAC,MAAM;MACzCnD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDP,kBAAkB,CAAC,IAAI,CAAC;MACxBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;EAED,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD;IACA,IAAIF,eAAe,CAACe,OAAO,EAAE;MAC3BG,YAAY,CAAClB,eAAe,CAACe,OAAO,CAAC;MACrCf,eAAe,CAACe,OAAO,GAAG,IAAI;IAChC;EACF,CAAC;EAED,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtD;IACAP,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM0D,aAAkC,GAAG;IACzCC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,cAAc;IACvBX,KAAK,EAAE,MAAM;IACb,GAAG3D;EACL,CAAC;EAED,MAAMuE,YAAiC,GAAG;IACxCZ,KAAK,EAAE,MAAM;IACba,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE3E,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;IACzD4E,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE9E,QAAQ,GAAG,SAAS,GAAG,OAAO;IAC/C+E,MAAM,EAAE/E,QAAQ,GAAG,aAAa,GAAG,SAAS;IAC5CyE,OAAO,EAAE,MAAM;IACfO,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAExF,kBAAkB,GAAG,SAAS,GAAG,SAAS;IACjDyF,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,eAAe;IAC3BC,SAAS,EAAE9E,MAAM,GAAG,mCAAmC,GAAG;EAC5D,CAAC;EAED,MAAM+E,SAA8B,GAAG;IACrCf,QAAQ,EAAE,UAAU;IACpBR,GAAG,EAAE,MAAM;IACXD,IAAI,EAAE,CAAC;IACPyB,KAAK,EAAE,CAAC;IACRV,eAAe,EAAE,OAAO;IACxBF,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBS,SAAS,EAAE,yEAAyE;IACpFG,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMC,aAAkC,GAAG;IACzCjB,OAAO,EAAE,eAAe;IACxBI,MAAM,EAAE,SAAS;IACjBN,OAAO,EAAE,MAAM;IACfQ,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,eAAe;IAC/BE,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChBU,YAAY,EAAE,mBAAmB;IACjCR,UAAU,EAAE,6BAA6B;IACzCb,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMsB,YAAiC,GAAG;IACxCtB,QAAQ,EAAE,UAAU;IACpBR,GAAG,EAAE,CAAApD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoD,GAAG,KAAI,CAAC;IAC9BD,IAAI,EAAE,CAAAnD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmD,IAAI,KAAI,CAAC;IAChCe,eAAe,EAAE,OAAO;IACxBF,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBS,SAAS,EAAE,yEAAyE;IACpFG,MAAM,EAAE,IAAI;IACZM,QAAQ,EAAE,OAAO;IACjBJ,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMK,gBAAqC,GAAG;IAC5CrB,OAAO,EAAE,cAAc;IACvBI,MAAM,EAAE,SAAS;IACjBG,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChBU,YAAY,EAAE,mBAAmB;IACjCR,UAAU,EAAE;EACd,CAAC;EAED,oBACE/F,OAAA;IAAK2G,GAAG,EAAEnF,WAAY;IAACX,KAAK,EAAEoE,aAAc;IAACnE,SAAS,EAAEA,SAAU;IAAA8F,QAAA,gBAEhE5G,OAAA;MACEa,KAAK,EAAEuE,YAAa;MACpByB,OAAO,EAAEA,CAAA,KAAM,CAACnG,QAAQ,IAAIS,SAAS,CAAC,CAACD,MAAM,CAAE;MAC/C4F,SAAS,EAAGC,CAAC,IAAK;QAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;UACtCD,CAAC,CAACE,cAAc,CAAC,CAAC;UAClB,CAACvG,QAAQ,IAAIS,SAAS,CAAC,CAACD,MAAM,CAAC;QACjC;MACF,CAAE;MACFgG,QAAQ,EAAExG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAE;MAC5ByG,IAAI,EAAC,QAAQ;MACb,iBAAejG,MAAO;MACtB,iBAAc,SAAS;MACvB,iBAAeN,QAAS;MAAAgG,QAAA,gBAExB5G,OAAA;QAAA4G,QAAA,EAAO5D,cAAc,CAAC;MAAC;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BvH,OAAA,CAACH,WAAW;QACV2H,IAAI,EAAE,EAAG;QACT3G,KAAK,EAAE;UACL4G,SAAS,EAAEvG,MAAM,GAAG,gBAAgB,GAAG,cAAc;UACrD6E,UAAU,EAAE;QACd;MAAE;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLrG,MAAM,iBACLlB,OAAA;MAAKa,KAAK,EAAEoF,SAAU;MAACkB,IAAI,EAAC,SAAS;MAAAP,QAAA,EAClCxG,UAAU,CAAC2B,GAAG,CAAEkB,QAAQ,IAAK;QAC5B,MAAMZ,gBAAgB,GAAGY,QAAQ,CAACX,aAAa,IAAIW,QAAQ,CAACX,aAAa,CAACR,MAAM,GAAG,CAAC;QACpF,MAAM4F,UAAU,GAAGrH,kBAAkB,KAAK4C,QAAQ,CAACd,WAAW;QAE9D,oBACEnC,OAAA;UAEEa,KAAK,EAAE;YACL,GAAGyF,aAAa;YAChBd,eAAe,EAAEkC,UAAU,GAAG,SAAS,GACxBtG,eAAe,KAAK6B,QAAQ,CAACd,WAAW,GAAG,SAAS,GAAG;UACxE,CAAE;UACF0E,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAACL,QAAQ,CAACd,WAAW,CAAE;UAC1DwF,YAAY,EAAGZ,CAAC,IAAKlD,mBAAmB,CAACZ,QAAQ,CAACd,WAAW,EAAE4E,CAAC,CAAE;UAClEa,YAAY,EAAE/C,mBAAoB;UAClCsC,IAAI,EAAC,QAAQ;UACb,iBAAeO,UAAW;UAAAd,QAAA,gBAE1B5G,OAAA;YAAKa,KAAK,EAAE;cAAEsE,OAAO,EAAE,MAAM;cAAEQ,UAAU,EAAE;YAAS,CAAE;YAAAiB,QAAA,gBACpD5G,OAAA;cACEa,KAAK,EAAE;gBACL2D,KAAK,EAAE,MAAM;gBACbqD,MAAM,EAAE,MAAM;gBACdtC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAEvC,QAAQ,CAAC6E,UAAU;gBACpCC,WAAW,EAAE;cACf;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFvH,OAAA;cAAA4G,QAAA,EAAO3D,QAAQ,CAACb;YAAI;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EACLlF,gBAAgB,iBACfrC,OAAA,CAACF,YAAY;YAAC0H,IAAI,EAAE,EAAG;YAAC3G,KAAK,EAAE;cAAEgF,KAAK,EAAE;YAAU;UAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACvD;QAAA,GA1BItE,QAAQ,CAACd,WAAW;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BtB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGArG,MAAM,IAAIE,eAAe,IAAIE,eAAe,iBAC3CtB,OAAA,CAAAE,SAAA;MAAA0G,QAAA,gBAEE5G,OAAA;QACEa,KAAK,EAAE;UACLqE,QAAQ,EAAE,UAAU;UACpBR,GAAG,EAAEpD,eAAe,CAACoD,GAAG;UACxBD,IAAI,EAAEnD,eAAe,CAACmD,IAAI,GAAG,CAAC,GAAGwB,SAAS,CAACC,KAAK,IAAI,CAAC,GAAI5E,eAAe,CAACmD,IAAI,GAAG,GAAI;UACpFD,KAAK,EAAE,KAAK;UACZqD,MAAM,EAAE,MAAM;UACd1B,MAAM,EAAE,IAAI;UACZX,eAAe,EAAE;QACnB,CAAE;QACFmC,YAAY,EAAE5C;MAAmB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACFvH,OAAA;QACE2G,GAAG,EAAElF,UAAW;QAChBZ,KAAK,EAAE2F,YAAa;QACpBmB,YAAY,EAAE5C,kBAAmB;QACjC6C,YAAY,EAAE5C,kBAAmB;QAAA4B,QAAA,GAAA5F,gBAAA,GAElCZ,UAAU,CACR8C,IAAI,CAAClB,GAAG,IAAIA,GAAG,CAACG,WAAW,KAAKf,eAAe,CAAC,cAAAJ,gBAAA,wBAAAC,qBAAA,GADlDD,gBAAA,CAEGsB,aAAa,cAAArB,qBAAA,uBAFhBA,qBAAA,CAEkBc,GAAG,CAAEoB,WAAW,IAAK;UACpC,MAAMuE,UAAU,GAAGpH,qBAAqB,KAAK6C,WAAW,CAACE,cAAc;UAEvE,oBACErD,OAAA;YAEEa,KAAK,EAAE;cACL,GAAG6F,gBAAgB;cACnBlB,eAAe,EAAEkC,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFb,OAAO,EAAEA,CAAA,KAAMrD,uBAAuB,CAACL,WAAW,CAACE,cAAc,CAAE;YACnEsE,YAAY,EAAGZ,CAAC,IAAK;cACnBA,CAAC,CAAC/C,aAAa,CAACnD,KAAK,CAAC2E,eAAe,GAAGkC,UAAU,GAAG,SAAS,GAAG,SAAS;YAC5E,CAAE;YACFE,YAAY,EAAGb,CAAC,IAAK;cACnBA,CAAC,CAAC/C,aAAa,CAACnD,KAAK,CAAC2E,eAAe,GAAGkC,UAAU,GAAG,SAAS,GAAG,aAAa;YAChF,CAAE;YAAAd,QAAA,eAEF5G,OAAA;cAAKa,KAAK,EAAE;gBAAEsE,OAAO,EAAE,MAAM;gBAAEQ,UAAU,EAAE;cAAS,CAAE;cAAAiB,QAAA,gBACpD5G,OAAA;gBACEa,KAAK,EAAE;kBACL2D,KAAK,EAAE,KAAK;kBACZqD,MAAM,EAAE,KAAK;kBACbtC,YAAY,EAAE,KAAK;kBACnBC,eAAe,EAAErC,WAAW,CAAC2E,UAAU;kBACvCC,WAAW,EAAE;gBACf;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFvH,OAAA;gBAAA4G,QAAA,EAAOzD,WAAW,CAACf;cAAI;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC,GAxBDpE,WAAW,CAACE,cAAc;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyB5B,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACN,CACH,EAGA5G,KAAK,iBACJX,OAAA;MAAKa,KAAK,EAAE;QACVuF,SAAS,EAAE,SAAS;QACpBR,QAAQ,EAAE,SAAS;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAe,QAAA,EACCjG;IAAK;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxG,EAAA,CA5XIZ,yBAAmE;AAAA6H,EAAA,GAAnE7H,yBAAmE;AA8XzE,eAAeA,yBAAyB;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}