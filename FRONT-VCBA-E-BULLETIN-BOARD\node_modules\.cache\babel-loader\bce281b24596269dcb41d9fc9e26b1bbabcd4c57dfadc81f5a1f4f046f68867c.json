{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 12h6\",\n  key: \"upa0zy\"\n}], [\"path\", {\n  d: \"M15 6h6\",\n  key: \"1jlkvy\"\n}], [\"path\", {\n  d: \"m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13\",\n  key: \"blevx4\"\n}], [\"path\", {\n  d: \"M3 18h18\",\n  key: \"1h113x\"\n}], [\"path\", {\n  d: \"M3.92 11h6.16\",\n  key: \"1bqo8m\"\n}]];\nconst LetterText = createLucideIcon(\"letter-text\", __iconNode);\nexport { __iconNode, LetterText as default };\n//# sourceMappingURL=letter-text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}