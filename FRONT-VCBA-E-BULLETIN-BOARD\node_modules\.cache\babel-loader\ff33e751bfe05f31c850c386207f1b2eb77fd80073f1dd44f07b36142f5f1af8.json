{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 2v2\",\n  key: \"scm5qe\"\n}], [\"path\", {\n  d: \"M7 22v-2a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2\",\n  key: \"1waht3\"\n}], [\"path\", {\n  d: \"M8 2v2\",\n  key: \"pbkmx\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"11\",\n  r: \"3\",\n  key: \"itu57m\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"4\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"12vinp\"\n}]];\nconst Contact = createLucideIcon(\"contact\", __iconNode);\nexport { __iconNode, Contact as default };\n//# sourceMappingURL=contact.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}