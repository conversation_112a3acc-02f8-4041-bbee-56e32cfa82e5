{"ast": null, "code": "import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { API_BASE_URL } from '../config/constants';\nclass CalendarReactionService {\n  constructor() {\n    this.client = void 0;\n    // Determine which client to use based on authentication context\n    this.client = this.getAuthenticatedClient();\n  }\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId) {\n    try {\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n      const response = await this.client.post(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          added: false\n        }\n      };\n    } catch (error) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId) {\n    try {\n      const {\n        useStudentAuth,\n        token\n      } = this.getCurrentUserAuth();\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar unlike failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || {\n            removed: false\n          }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.delete(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || {\n            removed: false\n          }\n        };\n      }\n    } catch (error) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["httpClient", "adminHttpClient", "studentHttpClient", "API_BASE_URL", "CalendarReactionService", "constructor", "client", "getAuthenticatedClient", "studentToken", "localStorage", "getItem", "studentUser", "adminToken", "adminUser", "console", "log", "likeEvent", "eventId", "clientType", "response", "post", "success", "message", "data", "added", "error", "Error", "unlikeEvent", "useStudentAuth", "token", "getCurrentUserAuth", "fetch", "method", "headers", "ok", "errorData", "json", "status", "result", "removed", "delete", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { ApiResponse } from '../types/common.types';\nimport { API_BASE_URL } from '../config/constants';\n\nexport interface CalendarReactionData {\n  added?: boolean;\n  removed?: boolean;\n}\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: CalendarReactionData;\n}\n\nclass CalendarReactionService {\n  private client: any;\n\n  constructor() {\n    // Determine which client to use based on authentication context\n    this.client = this.getAuthenticatedClient();\n  }\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  private getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n\n      const response = await this.client.post<CalendarReactionData>(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { added: false }\n      };\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const { useStudentAuth, token } = this.getCurrentUserAuth();\n\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar unlike failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || { removed: false }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.delete<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || { removed: false }\n        };\n      }\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAE9E,SAASC,YAAY,QAAQ,qBAAqB;AAalD,MAAMC,uBAAuB,CAAC;EAG5BC,WAAWA,CAAA,EAAG;IAAA,KAFNC,MAAM;IAGZ;IACA,IAAI,CAACA,MAAM,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;EACUA,sBAAsBA,CAAA,EAAG;IAC/B,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;;IAEnD;IACA,IAAIF,YAAY,IAAIG,WAAW,EAAE;MAC/BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOb,iBAAiB;IAC1B,CAAC,MAAM,IAAIU,UAAU,IAAIC,SAAS,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,OAAOd,eAAe;IACxB,CAAC,MAAM;MACLa,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOf,UAAU;IACnB;EACF;;EAEA;EACA,MAAMgB,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACFH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QACjEE,OAAO;QACPC,UAAU,EAAE,IAAI,CAACZ,MAAM,KAAKL,eAAe,GAAG,OAAO,GAAG,IAAI,CAACK,MAAM,KAAKJ,iBAAiB,GAAG,SAAS,GAAG;MAC1G,CAAC,CAAC;MAEF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACb,MAAM,CAACc,IAAI,CAAuB,iBAAiBH,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;MAClG,OAAO;QACLI,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEC,KAAK,EAAE;QAAM;MACxC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,+BAA+B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMK,WAAWA,CAACV,OAAe,EAAqC;IACpE,IAAI;MACF,MAAM;QAAEW,cAAc;QAAEC;MAAM,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAE3D,IAAIF,cAAc,IAAIC,KAAK,EAAE;QAC3B;QACA,MAAMV,QAAQ,GAAG,MAAMY,KAAK,CAAC,GAAG5B,YAAY,iBAAiBc,OAAO,OAAO,EAAE;UAC3Ee,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUJ,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACV,QAAQ,CAACe,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC;UACvCtB,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEU,SAAS,CAAC;UAC3D,MAAM,IAAIT,KAAK,CAAC,uBAAuBP,QAAQ,CAACkB,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMnB,QAAQ,CAACiB,IAAI,CAAC,CAAC;QACpC,OAAO;UACLf,OAAO,EAAEiB,MAAM,CAACjB,OAAO;UACvBC,OAAO,EAAEgB,MAAM,CAAChB,OAAO;UACvBC,IAAI,EAAEe,MAAM,CAACf,IAAI,IAAI;YAAEgB,OAAO,EAAE;UAAM;QACxC,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMpB,QAAQ,GAAG,MAAMnB,UAAU,CAACwC,MAAM,CAAuB,iBAAiBvB,OAAO,OAAO,CAAC;QAC/F,OAAO;UACLI,OAAO,EAAEF,QAAQ,CAACE,OAAO;UACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;UACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;YAAEgB,OAAO,EAAE;UAAM;QAC1C,CAAC;MACH;IACF,CAAC,CAAC,OAAOd,KAAU,EAAE;MACnBX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,iCAAiC,CAAC;IACrE;EACF;;EAEA;EACA,MAAMmB,UAAUA,CAACxB,OAAe,EAAEyB,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACf,WAAW,CAACV,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAM0B,uBAAuB,GAAG,IAAIvC,uBAAuB,CAAC,CAAC;AACpE,eAAeuC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}