{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 16h6\",\n  key: \"100bgy\"\n}], [\"path\", {\n  d: \"M19 13v6\",\n  key: \"85cyf1\"\n}], [\"path\", {\n  d: \"M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14\",\n  key: \"e7tb2h\"\n}], [\"path\", {\n  d: \"m7.5 4.27 9 5.15\",\n  key: \"1c824w\"\n}], [\"polyline\", {\n  points: \"3.29 7 12 12 20.71 7\",\n  key: \"ousv84\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"12\",\n  key: \"a4e8g8\"\n}]];\nconst PackagePlus = createLucideIcon(\"package-plus\", __iconNode);\nexport { __iconNode, PackagePlus as default };\n//# sourceMappingURL=package-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}