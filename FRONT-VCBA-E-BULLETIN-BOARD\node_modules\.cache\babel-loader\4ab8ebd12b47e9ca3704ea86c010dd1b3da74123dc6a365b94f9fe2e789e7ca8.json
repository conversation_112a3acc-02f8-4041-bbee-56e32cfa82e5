{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M9 13v-1h6v1\",\n  key: \"1bb014\"\n}], [\"path\", {\n  d: \"M12 12v6\",\n  key: \"3ahymv\"\n}], [\"path\", {\n  d: \"M11 18h2\",\n  key: \"12mj7e\"\n}]];\nconst FileType = createLucideIcon(\"file-type\", __iconNode);\nexport { __iconNode, FileType as default };\n//# sourceMappingURL=file-type.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}