{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z\",\n  key: \"17jzev\"\n}]];\nconst Thermometer = createLucideIcon(\"thermometer\", __iconNode);\nexport { __iconNode, Thermometer as default };\n//# sourceMappingURL=thermometer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}