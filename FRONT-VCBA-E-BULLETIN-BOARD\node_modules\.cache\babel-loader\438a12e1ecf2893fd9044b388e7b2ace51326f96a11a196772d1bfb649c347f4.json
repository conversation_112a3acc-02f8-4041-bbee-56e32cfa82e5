{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"path\", {\n  d: \"M11 17V8h4\",\n  key: \"1bfq6y\"\n}], [\"path\", {\n  d: \"M11 12h3\",\n  key: \"2eqnfz\"\n}], [\"path\", {\n  d: \"M9 16h4\",\n  key: \"1skf3a\"\n}]];\nconst BadgeSwissFranc = createLucideIcon(\"badge-swiss-franc\", __iconNode);\nexport { __iconNode, BadgeSwissFranc as default };\n//# sourceMappingURL=badge-swiss-franc.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}