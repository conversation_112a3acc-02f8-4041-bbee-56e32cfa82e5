{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12.99 6.74 1.93 3.44\",\n  key: \"iwagvd\"\n}], [\"path\", {\n  d: \"M19.136 12a10 10 0 0 1-14.271 0\",\n  key: \"ppmlo4\"\n}], [\"path\", {\n  d: \"m21 21-2.16-3.84\",\n  key: \"vylbct\"\n}], [\"path\", {\n  d: \"m3 21 8.02-14.26\",\n  key: \"1ssaw4\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"f1ur92\"\n}]];\nconst DraftingCompass = createLucideIcon(\"drafting-compass\", __iconNode);\nexport { __iconNode, DraftingCompass as default };\n//# sourceMappingURL=drafting-compass.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}