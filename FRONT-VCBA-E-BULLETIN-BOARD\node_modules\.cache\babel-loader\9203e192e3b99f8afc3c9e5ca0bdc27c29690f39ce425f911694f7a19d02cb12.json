{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"15\",\n  r: \"4\",\n  key: \"vux9w4\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"15\",\n  r: \"4\",\n  key: \"18o8ve\"\n}], [\"path\", {\n  d: \"M14 15a2 2 0 0 0-2-2 2 2 0 0 0-2 2\",\n  key: \"1ag4bs\"\n}], [\"path\", {\n  d: \"M2.5 13 5 7c.7-1.3 1.4-2 3-2\",\n  key: \"1hm1gs\"\n}], [\"path\", {\n  d: \"M21.5 13 19 7c-.7-1.3-1.5-2-3-2\",\n  key: \"1r31ai\"\n}]];\nconst Glasses = createLucideIcon(\"glasses\", __iconNode);\nexport { __iconNode, Glasses as default };\n//# sourceMappingURL=glasses.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}