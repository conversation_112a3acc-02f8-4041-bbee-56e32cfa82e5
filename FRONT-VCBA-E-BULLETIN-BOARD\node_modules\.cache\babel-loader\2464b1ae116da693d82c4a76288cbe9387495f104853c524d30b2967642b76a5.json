{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 7v10\",\n  key: \"a2pl2d\"\n}], [\"path\", {\n  d: \"M6 5v14\",\n  key: \"1kq3d7\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"18\",\n  x: \"10\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"13i7bc\"\n}]];\nconst GalleryHorizontalEnd = createLucideIcon(\"gallery-horizontal-end\", __iconNode);\nexport { __iconNode, GalleryHorizontalEnd as default };\n//# sourceMappingURL=gallery-horizontal-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}