{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v13\",\n  key: \"1km8f5\"\n}], [\"path\", {\n  d: \"m16 6-4-4-4 4\",\n  key: \"13yo43\"\n}], [\"path\", {\n  d: \"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\",\n  key: \"1b2hhj\"\n}]];\nconst Share = createLucideIcon(\"share\", __iconNode);\nexport { __iconNode, Share as default };\n//# sourceMappingURL=share.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}