{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21 16-4 4-4-4\",\n  key: \"f6ql7i\"\n}], [\"path\", {\n  d: \"M17 20V4\",\n  key: \"1ejh1v\"\n}], [\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}]];\nconst ArrowUpDown = createLucideIcon(\"arrow-up-down\", __iconNode);\nexport { __iconNode, ArrowUpDown as default };\n//# sourceMappingURL=arrow-up-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}