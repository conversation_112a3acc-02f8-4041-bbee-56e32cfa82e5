{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z\",\n  key: \"sobvz5\"\n}], [\"path\", {\n  d: \"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z\",\n  key: \"11i496\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M12 22v-2\",\n  key: \"1osdcq\"\n}], [\"path\", {\n  d: \"m17 20.66-1-1.73\",\n  key: \"eq3orb\"\n}], [\"path\", {\n  d: \"M11 10.27 7 3.34\",\n  key: \"16pf9h\"\n}], [\"path\", {\n  d: \"m20.66 17-1.73-1\",\n  key: \"sg0v6f\"\n}], [\"path\", {\n  d: \"m3.34 7 1.73 1\",\n  key: \"1ulond\"\n}], [\"path\", {\n  d: \"M14 12h8\",\n  key: \"4f43i9\"\n}], [\"path\", {\n  d: \"M2 12h2\",\n  key: \"1t8f8n\"\n}], [\"path\", {\n  d: \"m20.66 7-1.73 1\",\n  key: \"1ow05n\"\n}], [\"path\", {\n  d: \"m3.34 17 1.73-1\",\n  key: \"nuk764\"\n}], [\"path\", {\n  d: \"m17 3.34-1 1.73\",\n  key: \"2wel8s\"\n}], [\"path\", {\n  d: \"m11 13.73-4 6.93\",\n  key: \"794ttg\"\n}]];\nconst Cog = createLucideIcon(\"cog\", __iconNode);\nexport { __iconNode, Cog as default };\n//# sourceMappingURL=cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}