{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 16 4 4 4-4\",\n  key: \"1co6wj\"\n}], [\"path\", {\n  d: \"M7 20V4\",\n  key: \"1yoxec\"\n}], [\"path\", {\n  d: \"M11 4h10\",\n  key: \"1w87gc\"\n}], [\"path\", {\n  d: \"M11 8h7\",\n  key: \"djye34\"\n}], [\"path\", {\n  d: \"M11 12h4\",\n  key: \"q8tih4\"\n}]];\nconst ArrowDownWideNarrow = createLucideIcon(\"arrow-down-wide-narrow\", __iconNode);\nexport { __iconNode, ArrowDownWideNarrow as default };\n//# sourceMappingURL=arrow-down-wide-narrow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}