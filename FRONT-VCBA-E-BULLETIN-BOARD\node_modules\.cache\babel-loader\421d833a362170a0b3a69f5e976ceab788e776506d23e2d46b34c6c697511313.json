{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2h4\",\n  key: \"n1abiw\"\n}], [\"path\", {\n  d: \"m21 8-2 2-1.5-3.7A2 2 0 0 0 15.646 5H8.4a2 2 0 0 0-1.903 1.257L5 10 3 8\",\n  key: \"1imjwt\"\n}], [\"path\", {\n  d: \"M7 14h.01\",\n  key: \"1qa3f1\"\n}], [\"path\", {\n  d: \"M17 14h.01\",\n  key: \"7oqj8z\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"8\",\n  x: \"3\",\n  y: \"10\",\n  rx: \"2\",\n  key: \"a7itu8\"\n}], [\"path\", {\n  d: \"M5 18v2\",\n  key: \"ppbyun\"\n}], [\"path\", {\n  d: \"M19 18v2\",\n  key: \"gy7782\"\n}]];\nconst CarTaxiFront = createLucideIcon(\"car-taxi-front\", __iconNode);\nexport { __iconNode, CarTaxiFront as default };\n//# sourceMappingURL=car-taxi-front.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}