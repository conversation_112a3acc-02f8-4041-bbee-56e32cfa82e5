{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 8.82a15 15 0 0 1 20 0\",\n  key: \"dnpr2z\"\n}], [\"path\", {\n  d: \"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"1817ys\"\n}], [\"path\", {\n  d: \"M5 12.859a10 10 0 0 1 10.5-2.222\",\n  key: \"rpb7oy\"\n}], [\"path\", {\n  d: \"M8.5 16.429a5 5 0 0 1 3-1.406\",\n  key: \"r8bmzl\"\n}]];\nconst WifiPen = createLucideIcon(\"wifi-pen\", __iconNode);\nexport { __iconNode, WifiPen as default };\n//# sourceMappingURL=wifi-pen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}