{"ast": null, "code": "import { adminHttpClient, studentHttpClient } from './api.service';\nexport class ProfilePictureService {\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadAdminProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    const response = await adminHttpClient.post('/admin/profile/upload-picture', formData);\n    return response.data;\n  }\n\n  /**\n   * Upload student profile picture\n   */\n  static async uploadStudentProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    const response = await studentHttpClient.post('/student/profile/upload-picture', formData);\n    return response.data;\n  }\n\n  /**\n   * Get full profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath) {\n    if (!profilePicturePath) return null;\n\n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n\n    // Construct full URL with API base URL\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return `${baseUrl}${profilePicturePath}`;\n  }\n\n  /**\n   * Generate user initials from name\n   */\n  static getUserInitials(firstName, lastName) {\n    var _firstName$charAt, _lastName$charAt;\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName$charAt = firstName.charAt(0)) === null || _firstName$charAt === void 0 ? void 0 : _firstName$charAt.toUpperCase()) || '';\n    const last = (lastName === null || lastName === void 0 ? void 0 : (_lastName$charAt = lastName.charAt(0)) === null || _lastName$charAt === void 0 ? void 0 : _lastName$charAt.toUpperCase()) || '';\n    return `${first}${last}` || 'U';\n  }\n}", "map": {"version": 3, "names": ["adminHttpClient", "studentHttpClient", "ProfilePictureService", "uploadAdminProfilePicture", "file", "formData", "FormData", "append", "response", "post", "data", "uploadStudentProfilePicture", "getProfilePictureUrl", "profilePicture<PERSON>ath", "startsWith", "baseUrl", "process", "env", "REACT_APP_API_URL", "getUserInitials", "firstName", "lastName", "_firstName$charAt", "_lastName$charAt", "first", "char<PERSON>t", "toUpperCase", "last"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/profile-picture.service.ts"], "sourcesContent": ["import { adminHttpClient, studentHttpClient } from './api.service';\n\nexport interface ProfilePictureUploadResponse {\n  success: boolean;\n  message: string;\n  data: {\n    admin?: any;\n    student?: any;\n    profilePicture: {\n      path: string;\n      filename: string;\n      size: number;\n    };\n  };\n}\n\nexport class ProfilePictureService {\n  /**\n   * Upload admin profile picture\n   */\n  static async uploadAdminProfilePicture(file: File): Promise<ProfilePictureUploadResponse> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n\n    const response = await adminHttpClient.post<ProfilePictureUploadResponse>('/admin/profile/upload-picture', formData);\n    return response.data;\n  }\n\n  /**\n   * Upload student profile picture\n   */\n  static async uploadStudentProfilePicture(file: File): Promise<ProfilePictureUploadResponse> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n\n    const response = await studentHttpClient.post<ProfilePictureUploadResponse>('/student/profile/upload-picture', formData);\n    return response.data;\n  }\n\n  /**\n   * Get full profile picture URL\n   */\n  static getProfilePictureUrl(profilePicturePath?: string): string | null {\n    if (!profilePicturePath) return null;\n    \n    // If already a full URL, return as is\n    if (profilePicturePath.startsWith('http')) {\n      return profilePicturePath;\n    }\n    \n    // Construct full URL with API base URL\n    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    return `${baseUrl}${profilePicturePath}`;\n  }\n\n  /**\n   * Generate user initials from name\n   */\n  static getUserInitials(firstName?: string, lastName?: string): string {\n    const first = firstName?.charAt(0)?.toUpperCase() || '';\n    const last = lastName?.charAt(0)?.toUpperCase() || '';\n    return `${first}${last}` || 'U';\n  }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAgBlE,OAAO,MAAMC,qBAAqB,CAAC;EACjC;AACF;AACA;EACE,aAAaC,yBAAyBA,CAACC,IAAU,EAAyC;IACxF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,MAAMI,QAAQ,GAAG,MAAMR,eAAe,CAACS,IAAI,CAA+B,+BAA+B,EAAEJ,QAAQ,CAAC;IACpH,OAAOG,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,aAAaC,2BAA2BA,CAACP,IAAU,EAAyC;IAC1F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,MAAMI,QAAQ,GAAG,MAAMP,iBAAiB,CAACQ,IAAI,CAA+B,iCAAiC,EAAEJ,QAAQ,CAAC;IACxH,OAAOG,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;EACE,OAAOE,oBAAoBA,CAACC,kBAA2B,EAAiB;IACtE,IAAI,CAACA,kBAAkB,EAAE,OAAO,IAAI;;IAEpC;IACA,IAAIA,kBAAkB,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACzC,OAAOD,kBAAkB;IAC3B;;IAEA;IACA,MAAME,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IACxE,OAAO,GAAGH,OAAO,GAAGF,kBAAkB,EAAE;EAC1C;;EAEA;AACF;AACA;EACE,OAAOM,eAAeA,CAACC,SAAkB,EAAEC,QAAiB,EAAU;IAAA,IAAAC,iBAAA,EAAAC,gBAAA;IACpE,MAAMC,KAAK,GAAG,CAAAJ,SAAS,aAATA,SAAS,wBAAAE,iBAAA,GAATF,SAAS,CAAEK,MAAM,CAAC,CAAC,CAAC,cAAAH,iBAAA,uBAApBA,iBAAA,CAAsBI,WAAW,CAAC,CAAC,KAAI,EAAE;IACvD,MAAMC,IAAI,GAAG,CAAAN,QAAQ,aAARA,QAAQ,wBAAAE,gBAAA,GAARF,QAAQ,CAAEI,MAAM,CAAC,CAAC,CAAC,cAAAF,gBAAA,uBAAnBA,gBAAA,CAAqBG,WAAW,CAAC,CAAC,KAAI,EAAE;IACrD,OAAO,GAAGF,KAAK,GAAGG,IAAI,EAAE,IAAI,GAAG;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}