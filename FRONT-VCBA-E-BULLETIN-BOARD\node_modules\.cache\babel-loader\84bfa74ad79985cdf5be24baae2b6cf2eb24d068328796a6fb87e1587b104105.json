{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l4 2\",\n  key: \"mmk7yg\"\n}], [\"path\", {\n  d: \"M20 12v5\",\n  key: \"12wsvk\"\n}], [\"path\", {\n  d: \"M20 21h.01\",\n  key: \"1p6o6n\"\n}], [\"path\", {\n  d: \"M21.25 8.2A10 10 0 1 0 16 21.16\",\n  key: \"17fp9f\"\n}]];\nconst ClockAlert = createLucideIcon(\"clock-alert\", __iconNode);\nexport { __iconNode, ClockAlert as default };\n//# sourceMappingURL=clock-alert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}