{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M6 8h.01\",\n  key: \"x9i8wu\"\n}], [\"path\", {\n  d: \"M10 8h.01\",\n  key: \"1r9ogq\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}]];\nconst AppWindowMac = createLucideIcon(\"app-window-mac\", __iconNode);\nexport { __iconNode, AppWindowMac as default };\n//# sourceMappingURL=app-window-mac.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}