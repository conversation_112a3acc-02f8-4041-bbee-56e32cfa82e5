{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m5 12 7-7 7 7\",\n  key: \"hav0vg\"\n}], [\"path\", {\n  d: \"M12 19V5\",\n  key: \"x0mq9r\"\n}]];\nconst ArrowUp = createLucideIcon(\"arrow-up\", __iconNode);\nexport { __iconNode, ArrowUp as default };\n//# sourceMappingURL=arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}