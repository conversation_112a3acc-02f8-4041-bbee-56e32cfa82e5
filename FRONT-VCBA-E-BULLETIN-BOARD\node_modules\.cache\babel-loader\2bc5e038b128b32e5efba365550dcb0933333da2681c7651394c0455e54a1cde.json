{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16h.01\",\n  key: \"1drbdi\"\n}], [\"path\", {\n  d: \"M16 16h.01\",\n  key: \"1f9h7w\"\n}], [\"path\", {\n  d: \"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z\",\n  key: \"1iv0i2\"\n}], [\"path\", {\n  d: \"M8 16h.01\",\n  key: \"18s6g9\"\n}]];\nconst Factory = createLucideIcon(\"factory\", __iconNode);\nexport { __iconNode, Factory as default };\n//# sourceMappingURL=factory.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}