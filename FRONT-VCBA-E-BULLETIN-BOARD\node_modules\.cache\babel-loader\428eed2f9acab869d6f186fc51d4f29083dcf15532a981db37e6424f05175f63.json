{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 3.1c-.5 0-1-.1-1.5-.1s-1 .1-1.5.1\",\n  key: \"16ll65\"\n}], [\"path\", {\n  d: \"M19.3 6.8a10.45 10.45 0 0 0-2.1-2.1\",\n  key: \"1nq77a\"\n}], [\"path\", {\n  d: \"M20.9 13.5c.1-.5.1-1 .1-1.5s-.1-1-.1-1.5\",\n  key: \"1sf7wn\"\n}], [\"path\", {\n  d: \"M17.2 19.3a10.45 10.45 0 0 0 2.1-2.1\",\n  key: \"x1hs5g\"\n}], [\"path\", {\n  d: \"M10.5 20.9c.5.1 1 .1 1.5.1s1-.1 1.5-.1\",\n  key: \"19m18z\"\n}], [\"path\", {\n  d: \"M3.5 17.5 2 22l4.5-1.5\",\n  key: \"1f36qi\"\n}], [\"path\", {\n  d: \"M3.1 10.5c0 .5-.1 1-.1 1.5s.1 1 .1 1.5\",\n  key: \"1vz3ju\"\n}], [\"path\", {\n  d: \"M6.8 4.7a10.45 10.45 0 0 0-2.1 2.1\",\n  key: \"19f9do\"\n}]];\nconst MessageCircleDashed = createLucideIcon(\"message-circle-dashed\", __iconNode);\nexport { __iconNode, MessageCircleDashed as default };\n//# sourceMappingURL=message-circle-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}