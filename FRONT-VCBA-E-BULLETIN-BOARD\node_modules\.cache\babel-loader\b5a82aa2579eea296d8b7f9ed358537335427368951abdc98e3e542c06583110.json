{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{User,Settings as SettingsIcon,Lock,Bell,CheckCircle,Eye,EyeOff,AlertCircle}from'lucide-react';import ProfilePictureUpload from'../../components/admin/ProfilePictureUpload';import{AdminAuthService}from'../../services/admin-auth.service';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Settings=()=>{const{user,checkAuthStatus}=useAdminAuth();const[activeTab,setActiveTab]=useState('profile');const[isUploadingPicture,setIsUploadingPicture]=useState(false);// Password change state\nconst[passwordData,setPasswordData]=useState({currentPassword:'',newPassword:'',confirmPassword:''});const[showPasswords,setShowPasswords]=useState({current:false,new:false,confirm:false});const[passwordErrors,setPasswordErrors]=useState([]);const[passwordSuccess,setPasswordSuccess]=useState(null);const[isChangingPassword,setIsChangingPassword]=useState(false);const tabs=[{key:'profile',label:'Profile Settings',icon:User},{key:'system',label:'System Settings',icon:SettingsIcon},{key:'security',label:'Security',icon:Lock},{key:'notifications',label:'Notifications',icon:Bell}];// Profile picture handlers\nconst handleProfilePictureUpload=async file=>{setIsUploadingPicture(true);try{console.log('🔍 Settings - Starting profile picture upload...');const result=await AdminAuthService.uploadProfilePicture(file);console.log('🔍 Settings - Upload result:',result);// Refresh user data to get updated profile picture\nconsole.log('🔍 Settings - Refreshing auth status...');await checkAuthStatus();console.log('🔍 Settings - Auth status refreshed, new user:',user);}catch(error){console.error('❌ Settings - Upload failed:',error);throw new Error(error.message||'Failed to upload profile picture');}finally{setIsUploadingPicture(false);}};const handleProfilePictureRemove=async()=>{setIsUploadingPicture(true);try{await AdminAuthService.removeProfilePicture();// Refresh user data to remove profile picture\nawait checkAuthStatus();}catch(error){throw new Error(error.message||'Failed to remove profile picture');}finally{setIsUploadingPicture(false);}};// Password validation\nconst validatePassword=password=>{const errors=[];if(password.length<8){errors.push('Password must be at least 8 characters long');}if(!/(?=.*[a-z])/.test(password)){errors.push('Password must contain at least one lowercase letter');}if(!/(?=.*[A-Z])/.test(password)){errors.push('Password must contain at least one uppercase letter');}if(!/(?=.*\\d)/.test(password)){errors.push('Password must contain at least one number');}if(!/(?=.*[@$!%*?&])/.test(password)){errors.push('Password must contain at least one special character (@$!%*?&)');}return errors;};// Handle password input change\nconst handlePasswordChange=(field,value)=>{setPasswordData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));setPasswordErrors([]);setPasswordSuccess(null);};// Toggle password visibility\nconst togglePasswordVisibility=field=>{setShowPasswords(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:!prev[field]}));};// Handle password change submission\nconst handlePasswordSubmit=async e=>{e.preventDefault();setPasswordErrors([]);setPasswordSuccess(null);// Validation\nconst errors=[];if(!passwordData.currentPassword){errors.push('Current password is required');}if(!passwordData.newPassword){errors.push('New password is required');}else{const passwordValidationErrors=validatePassword(passwordData.newPassword);errors.push(...passwordValidationErrors);}if(!passwordData.confirmPassword){errors.push('Password confirmation is required');}else if(passwordData.newPassword!==passwordData.confirmPassword){errors.push('New password and confirmation do not match');}if(passwordData.currentPassword===passwordData.newPassword){errors.push('New password must be different from current password');}if(errors.length>0){setPasswordErrors(errors);return;}setIsChangingPassword(true);try{// Call API to change password\nawait AdminAuthService.changePassword({currentPassword:passwordData.currentPassword,newPassword:passwordData.newPassword});setPasswordSuccess('Password changed successfully!');setPasswordData({currentPassword:'',newPassword:'',confirmPassword:''});// Clear success message after 5 seconds\nsetTimeout(()=>setPasswordSuccess(null),5000);}catch(error){setPasswordErrors([error.message||'Failed to change password']);}finally{setIsChangingPassword(false);}};const renderProfileSettings=()=>{var _user$firstName,_user$lastName;return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'2rem'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"settings-card hover-lift\",style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"profile-layout\",style:{display:'flex',gap:'2rem',alignItems:'flex-start',marginBottom:'2rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"div\",{style:{flexShrink:0},children:/*#__PURE__*/_jsx(ProfilePictureUpload,{currentPicture:user!==null&&user!==void 0&&user.profilePicture?\"http://localhost:5000\".concat(user.profilePicture):undefined,userInitials:\"\".concat((user===null||user===void 0?void 0:(_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName=user.lastName)===null||_user$lastName===void 0?void 0:_user$lastName.charAt(0))||''),onUpload:handleProfilePictureUpload,onRemove:handleProfilePictureRemove,isLoading:isUploadingPicture,size:140})}),/*#__PURE__*/_jsx(\"div\",{className:\"profile-details\",style:{flex:1,minWidth:'300px',display:'flex',flexDirection:'column',gap:'1.5rem',paddingTop:'0.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{color:'#111827',fontSize:'1.5rem',fontWeight:'700',marginBottom:'0.5rem'},children:[\"\".concat((user===null||user===void 0?void 0:user.firstName)||'',\" \").concat((user===null||user===void 0?void 0:user.lastName)||''),(user===null||user===void 0?void 0:user.suffix)&&/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'400',color:'#6b7280'},children:[\" \",user.suffix]})]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'1rem',fontWeight:'500'},children:/*#__PURE__*/_jsx(\"span\",{children:(user===null||user===void 0?void 0:user.email)||'Not provided'})})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-card hover-lift\",style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsxs(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Lock,{size:20}),\"Change Password\"]}),/*#__PURE__*/_jsx(\"form\",{onSubmit:handlePasswordSubmit,children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'600',color:'#374151',marginBottom:'0.5rem'},children:\"Current Password\"}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(\"input\",{type:showPasswords.current?'text':'password',value:passwordData.currentPassword,onChange:e=>handlePasswordChange('currentPassword',e.target.value),required:true,style:{width:'100%',padding:'0.75rem 3rem 0.75rem 1rem',border:'2px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',transition:'border-color 0.2s ease',outline:'none'},onFocus:e=>e.target.style.borderColor='#22c55e',onBlur:e=>e.target.style.borderColor='#e8f5e8',placeholder:\"Enter your current password\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>togglePasswordVisibility('current'),style:{position:'absolute',right:'0.75rem',top:'50%',transform:'translateY(-50%)',background:'none',border:'none',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:showPasswords.current?/*#__PURE__*/_jsx(EyeOff,{size:20}):/*#__PURE__*/_jsx(Eye,{size:20})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'600',color:'#374151',marginBottom:'0.5rem'},children:\"New Password\"}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(\"input\",{type:showPasswords.new?'text':'password',value:passwordData.newPassword,onChange:e=>handlePasswordChange('newPassword',e.target.value),required:true,style:{width:'100%',padding:'0.75rem 3rem 0.75rem 1rem',border:'2px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',transition:'border-color 0.2s ease',outline:'none'},onFocus:e=>e.target.style.borderColor='#22c55e',onBlur:e=>e.target.style.borderColor='#e8f5e8',placeholder:\"Enter your new password\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>togglePasswordVisibility('new'),style:{position:'absolute',right:'0.75rem',top:'50%',transform:'translateY(-50%)',background:'none',border:'none',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:showPasswords.new?/*#__PURE__*/_jsx(EyeOff,{size:20}):/*#__PURE__*/_jsx(Eye,{size:20})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'600',color:'#374151',marginBottom:'0.5rem'},children:\"Confirm New Password\"}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(\"input\",{type:showPasswords.confirm?'text':'password',value:passwordData.confirmPassword,onChange:e=>handlePasswordChange('confirmPassword',e.target.value),required:true,style:{width:'100%',padding:'0.75rem 3rem 0.75rem 1rem',border:'2px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',transition:'border-color 0.2s ease',outline:'none'},onFocus:e=>e.target.style.borderColor='#22c55e',onBlur:e=>e.target.style.borderColor='#e8f5e8',placeholder:\"Confirm your new password\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>togglePasswordVisibility('confirm'),style:{position:'absolute',right:'0.75rem',top:'50%',transform:'translateY(-50%)',background:'none',border:'none',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:showPasswords.confirm?/*#__PURE__*/_jsx(EyeOff,{size:20}):/*#__PURE__*/_jsx(Eye,{size:20})})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'8px',padding:'1rem'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 0.5rem 0',fontSize:'0.875rem',fontWeight:'600',color:'#16a34a'},children:\"Password Requirements:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{margin:0,paddingLeft:'1.25rem',fontSize:'0.75rem',color:'#16a34a',lineHeight:'1.5'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"At least 8 characters long\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Contains uppercase and lowercase letters\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Contains at least one number\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Contains at least one special character (@$!%*?&)\"})]})]}),passwordErrors.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'#fef2f2',border:'1px solid #fecaca',borderRadius:'8px',padding:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:16,color:\"#dc2626\"}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#dc2626'},children:\"Please fix the following errors:\"})]}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:0,paddingLeft:'1.25rem',fontSize:'0.75rem',color:'#dc2626',lineHeight:'1.5'},children:passwordErrors.map((error,index)=>/*#__PURE__*/_jsx(\"li\",{children:error},index))})]}),passwordSuccess&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'8px',padding:'1rem',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16,color:\"#16a34a\"}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#16a34a'},children:passwordSuccess})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isChangingPassword,style:{background:isChangingPassword?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.875rem 2rem',fontSize:'1rem',fontWeight:'600',cursor:isChangingPassword?'not-allowed':'pointer',display:'flex',alignItems:'center',justifyContent:'center',gap:'0.5rem',transition:'all 0.2s ease',boxShadow:isChangingPassword?'none':'0 2px 8px rgba(34, 197, 94, 0.2)',alignSelf:'flex-start'},onMouseEnter:e=>{if(!isChangingPassword){e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';}},onMouseLeave:e=>{if(!isChangingPassword){e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 2px 8px rgba(34, 197, 94, 0.2)';}},children:isChangingPassword?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'16px',height:'16px',border:'2px solid #ffffff',borderTop:'2px solid transparent',borderRadius:'50%',animation:'spin 1s linear infinite'}}),\"Changing Password...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Lock,{size:16}),\"Change Password\"]})})]})})]})]});};const renderSystemSettings=()=>/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"General Settings\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Maintenance Mode\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Enable maintenance mode to restrict access\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#ccc',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Auto-approve Posts\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Automatically approve new posts without review\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#22c55e',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Email Notifications\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Send email notifications for important events\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#22c55e',transition:'0.4s',borderRadius:'34px'}})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"System Information\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"System Version\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#374151'},children:\"v1.0.0\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Last Updated\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#374151'},children:\"June 28, 2025\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Database Status\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#22c55e'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16,color:\"#22c55e\"}),\"Connected\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Server Status\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#22c55e'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16,color:\"#22c55e\"}),\"Online\"]})})]})]})]})]});const renderContent=()=>{switch(activeTab){case'profile':return renderProfileSettings();case'system':return renderSystemSettings();case'security':return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'4rem 2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Lock,{size:48,color:\"#2d5016\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#2d5016',fontSize:'1.5rem',fontWeight:'600',marginBottom:'0.5rem'},children:\"Security Settings\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280'},children:\"Security settings panel coming soon\"})]});case'notifications':return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'4rem 2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Bell,{size:48,color:\"#2d5016\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#2d5016',fontSize:'1.5rem',fontWeight:'600',marginBottom:'0.5rem'},children:\"Notification Settings\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280'},children:\"Notification preferences panel coming soon\"})]});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"style\",{children:\"\\n        @media (max-width: 768px) {\\n          .profile-layout {\\n            flex-direction: column !important;\\n            align-items: center !important;\\n            text-align: center;\\n          }\\n\\n          .profile-details {\\n            min-width: unset !important;\\n            width: 100% !important;\\n          }\\n\\n          .profile-grid {\\n            grid-template-columns: 1fr !important;\\n          }\\n\\n          .role-status-grid {\\n            grid-template-columns: 1fr !important;\\n          }\\n        }\\n\\n        @media (max-width: 640px) {\\n          .settings-container {\\n            padding: 1rem !important;\\n          }\\n\\n          .settings-card {\\n            padding: 1.5rem !important;\\n          }\\n\\n          .tab-container {\\n            padding: 1rem !important;\\n          }\\n\\n          .tab-button {\\n            padding: 0.5rem 1rem !important;\\n            font-size: 0.875rem !important;\\n          }\\n        }\\n\\n        .fade-in {\\n          animation: fadeInUp 0.5s ease-out;\\n        }\\n\\n        @keyframes fadeInUp {\\n          0% {\\n            opacity: 0;\\n            transform: translateY(20px);\\n          }\\n          100% {\\n            opacity: 1;\\n            transform: translateY(0);\\n          }\\n        }\\n\\n        .hover-lift {\\n          transition: all 0.3s ease;\\n        }\\n\\n        .hover-lift:hover {\\n          transform: translateY(-2px);\\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\\n        }\\n      \"}),/*#__PURE__*/_jsx(\"div\",{className:\"fade-in\",style:{background:'white',borderRadius:'16px',padding:'1.5rem',marginBottom:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:/*#__PURE__*/_jsx(\"div\",{className:\"tab-container\",style:{display:'flex',gap:'1rem',flexWrap:'wrap'},children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{className:\"tab-button\",onClick:()=>setActiveTab(tab.key),style:{background:activeTab===tab.key?'linear-gradient(135deg, #22c55e 0%, #facc15 100%)':'transparent',color:activeTab===tab.key?'white':'#6b7280',border:activeTab===tab.key?'none':'1px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:'pointer',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.5rem',transition:'all 0.3s ease',transform:activeTab===tab.key?'translateY(-1px)':'translateY(0)',boxShadow:activeTab===tab.key?'0 4px 15px rgba(34, 197, 94, 0.3)':'none'},onMouseEnter:e=>{if(activeTab!==tab.key){e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.background='#f9fafb';e.currentTarget.style.transform='translateY(-1px)';}},onMouseLeave:e=>{if(activeTab!==tab.key){e.currentTarget.style.borderColor='#e8f5e8';e.currentTarget.style.background='transparent';e.currentTarget.style.transform='translateY(0)';}},children:[/*#__PURE__*/_jsx(tab.icon,{size:16}),tab.label]},tab.key))})}),/*#__PURE__*/_jsx(\"div\",{className:\"fade-in\",children:renderContent()})]});};export default Settings;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}