{"ast": null, "code": "import{useState,useEffect,useCallback}from'react';import{calendarService}from'../services';// Hook for managing calendar events\nexport const useCalendar=initialDate=>{const[events,setEvents]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const[currentDate,setCurrentDate]=useState(initialDate||new Date());const[view,setView]=useState('month');const[calendarData,setCalendarData]=useState({events:{}});const fetchCalendarData=useCallback(async()=>{try{setLoading(true);setError(undefined);const currentYear=currentDate.getFullYear();// Fetch events for multiple years (current year ± 2 years)\nconst yearsToFetch=[currentYear-2,currentYear-1,currentYear,currentYear+1,currentYear+2];// console.log(`📅 Fetching calendar data for years: ${yearsToFetch.join(', ')}...`);\n// Fetch events for all years in parallel\nconst responses=await Promise.all(yearsToFetch.map(year=>calendarService.getCalendarView(year,undefined)));// Merge all successful responses\nconst mergedEvents={};let totalEvents=0;responses.forEach((response,index)=>{if(response.success&&response.data){// Merge events from this year into the combined events object\nObject.entries(response.data.events).forEach(_ref=>{let[date,dayEvents]=_ref;if(!mergedEvents[date]){mergedEvents[date]=[];}mergedEvents[date].push(...dayEvents);totalEvents+=dayEvents.length;});}else{console.warn(\"\\u26A0\\uFE0F Failed to fetch data for year \".concat(yearsToFetch[index],\":\"),response.message);}});// Set the merged calendar data\nsetCalendarData({events:mergedEvents});// Convert grouped events to flat array for easier manipulation\nconst flatEvents=Object.values(mergedEvents).flat();setEvents(flatEvents);// console.log(`✅ Calendar data loaded for years ${yearsToFetch.join(', ')}: ${totalEvents} events`);\nif(totalEvents===0){setError('No calendar data found for the requested years');}}catch(err){// console.error('❌ Error fetching calendar data:', err);\nlet errorMessage='An error occurred while fetching calendar data';if(err.message.includes('Network connection failed')){errorMessage='Unable to connect to server. Please check your connection and try again.';}else if(err.message){errorMessage=err.message;}setError(errorMessage);}finally{setLoading(false);}},[currentDate.getFullYear()]);// Only depend on year since we fetch entire year\nconst refresh=useCallback(async()=>{await fetchCalendarData();},[fetchCalendarData]);const createEvent=useCallback(async data=>{try{setLoading(true);setError(undefined);const response=await calendarService.createEvent(data);if(response.success){// Refresh calendar data to get the new event\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to create event');}}catch(err){setError(err.message||'An error occurred while creating event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const updateEvent=useCallback(async(id,data)=>{try{setLoading(true);setError(undefined);const response=await calendarService.updateEvent(id,data);if(response.success&&response.data){// Refresh the calendar data to ensure multi-day events are properly handled\n// This is more reliable than trying to manually update the complex calendar state\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to update event');}}catch(err){setError(err.message||'An error occurred while updating event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const deleteEvent=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await calendarService.deleteEvent(id);if(response.success){// Refresh the calendar data to ensure consistency\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to delete event');}}catch(err){setError(err.message||'An error occurred while deleting event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const getEventsForDate=useCallback(date=>{// Format date manually to avoid timezone issues\nconst year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const dateKey=\"\".concat(year,\"-\").concat(month,\"-\").concat(day);return calendarData.events[dateKey]||[];},[calendarData]);const getEventsForDateRange=useCallback(async(startDate,endDate)=>{try{// Format dates manually to avoid timezone issues\nconst formatDate=date=>{const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day);};const start=formatDate(startDate);const end=formatDate(endDate);const response=await calendarService.getEventsByDateRange(start,end);if(response.success&&response.data){return response.data.events;}else{throw new Error(response.message||'Failed to fetch events for date range');}}catch(err){setError(err.message||'An error occurred while fetching events for date range');return[];}},[]);useEffect(()=>{fetchCalendarData();},[fetchCalendarData]);return{events,loading,error,currentDate,view,calendarData,setCurrentDate,setView,refresh,createEvent,updateEvent,deleteEvent,getEventsForDate,getEventsForDateRange};};// Hook for managing calendar categories\nexport const useCalendarCategories=()=>{const[categories,setCategories]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const fetchCategories=useCallback(async()=>{try{setLoading(true);setError(undefined);const response=await calendarService.getCategoriesWithSubcategories();if(response.success&&response.data){setCategories(response.data.categories||[]);}else{setError(response.message||'Failed to fetch categories');}}catch(err){setError(err.message||'An error occurred while fetching categories');}finally{setLoading(false);}},[]);useEffect(()=>{fetchCategories();},[fetchCategories]);const refresh=useCallback(async()=>{await fetchCategories();},[fetchCategories]);return{categories,loading,error,refresh};};// Utility functions for calendar operations\nexport const getCalendarDays=(year,month)=>{const firstDay=new Date(year,month,1);const lastDay=new Date(year,month+1,0);const startDate=new Date(firstDay);const endDate=new Date(lastDay);// Adjust to start from Sunday (or Monday based on preference)\nstartDate.setDate(startDate.getDate()-startDate.getDay());// Adjust to end on Saturday (or Sunday based on preference)\nendDate.setDate(endDate.getDate()+(6-endDate.getDay()));const days=[];const currentDate=new Date(startDate);while(currentDate<=endDate){days.push(new Date(currentDate));currentDate.setDate(currentDate.getDate()+1);}return days;};export const isToday=date=>{const today=new Date();return date.toDateString()===today.toDateString();};export const isSameMonth=(date,month,year)=>{return date.getMonth()===month&&date.getFullYear()===year;};export const formatDateForDisplay=date=>{return date.toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'});};export const formatTimeForDisplay=date=>{return date.toLocaleTimeString('en-US',{hour:'2-digit',minute:'2-digit'});};export const getMonthName=month=>{const monthNames=['January','February','March','April','May','June','July','August','September','October','November','December'];return monthNames[month];};export const getDayName=day=>{const dayNames=['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];return dayNames[day];};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}