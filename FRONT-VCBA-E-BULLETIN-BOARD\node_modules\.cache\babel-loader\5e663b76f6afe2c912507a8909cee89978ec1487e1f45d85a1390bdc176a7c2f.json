{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.17 7.83 2 22\",\n  key: \"t58vo8\"\n}], [\"path\", {\n  d: \"M4.02 12a2.827 2.827 0 1 1 3.81-4.17A2.827 2.827 0 1 1 12 4.02a2.827 2.827 0 1 1 4.17 3.81A2.827 2.827 0 1 1 19.98 12a2.827 2.827 0 1 1-3.81 4.17A2.827 2.827 0 1 1 12 19.98a2.827 2.827 0 1 1-4.17-3.81A1 1 0 1 1 4 12\",\n  key: \"17k36q\"\n}], [\"path\", {\n  d: \"m7.83 7.83 8.34 8.34\",\n  key: \"1d7sxk\"\n}]];\nconst Clover = createLucideIcon(\"clover\", __iconNode);\nexport { __iconNode, Clover as default };\n//# sourceMappingURL=clover.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}