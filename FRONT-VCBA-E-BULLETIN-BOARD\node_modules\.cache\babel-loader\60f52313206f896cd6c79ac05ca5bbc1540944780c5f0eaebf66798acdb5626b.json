{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14 10 7-7\",\n  key: \"oa77jy\"\n}], [\"path\", {\n  d: \"M20 10h-6V4\",\n  key: \"mjg0md\"\n}], [\"path\", {\n  d: \"m3 21 7-7\",\n  key: \"tjx5ai\"\n}], [\"path\", {\n  d: \"M4 14h6v6\",\n  key: \"rmj7iw\"\n}]];\nconst Minimize2 = createLucideIcon(\"minimize-2\", __iconNode);\nexport { __iconNode, Minimize2 as default };\n//# sourceMappingURL=minimize-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}