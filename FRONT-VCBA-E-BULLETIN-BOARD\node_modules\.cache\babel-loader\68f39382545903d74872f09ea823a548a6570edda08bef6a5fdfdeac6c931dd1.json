{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.25 17.25h1.5L12 18z\",\n  key: \"1wmwwj\"\n}], [\"path\", {\n  d: \"m15 12 2 2\",\n  key: \"k60wz4\"\n}], [\"path\", {\n  d: \"M18 6.5a.5.5 0 0 0-.5-.5\",\n  key: \"1ch4h4\"\n}], [\"path\", {\n  d: \"M20.69 9.67a4.5 4.5 0 1 0-7.04-5.5 8.35 8.35 0 0 0-3.3 0 4.5 4.5 0 1 0-7.04 5.5C2.49 11.2 2 12.88 2 14.5 2 19.47 6.48 22 12 22s10-2.53 10-7.5c0-1.62-.48-3.3-1.3-4.83\",\n  key: \"1c660l\"\n}], [\"path\", {\n  d: \"M6 6.5a.495.495 0 0 1 .5-.5\",\n  key: \"eviuep\"\n}], [\"path\", {\n  d: \"m9 12-2 2\",\n  key: \"326nkw\"\n}]];\nconst Panda = createLucideIcon(\"panda\", __iconNode);\nexport { __iconNode, Panda as default };\n//# sourceMappingURL=panda.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}