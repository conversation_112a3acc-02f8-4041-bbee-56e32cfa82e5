{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"15\",\n  rx: \"2\",\n  key: \"1w91an\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"17wqzy\"\n}], [\"path\", {\n  d: \"M2 21h20\",\n  key: \"1nyx9w\"\n}], [\"path\", {\n  d: \"M2 3h20\",\n  key: \"91anmk\"\n}]];\nconst AlignVerticalSpaceBetween = createLucideIcon(\"align-vertical-space-between\", __iconNode);\nexport { __iconNode, AlignVerticalSpaceBetween as default };\n//# sourceMappingURL=align-vertical-space-between.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}