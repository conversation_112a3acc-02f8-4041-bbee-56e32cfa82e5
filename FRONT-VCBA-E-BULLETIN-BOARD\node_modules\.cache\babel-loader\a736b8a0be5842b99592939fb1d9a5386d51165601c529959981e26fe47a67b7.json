{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\auth\\\\UnifiedLogin\\\\UnifiedLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useUnifiedAuth } from '../../../contexts/UnifiedAuthContext';\nimport { VALIDATION_RULES } from '../../../config/constants';\nimport './UnifiedLogin.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnifiedLogin = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    error,\n    isLoading,\n    clearError\n  } = useUnifiedAuth();\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: '',\n    userType: 'auto',\n    remember: false\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [detectedUserType, setDetectedUserType] = useState('unknown');\n\n  // Detect user type based on identifier\n  useEffect(() => {\n    const identifier = formData.identifier.trim();\n    if (!identifier) {\n      setDetectedUserType('unknown');\n      return;\n    }\n    const isEmail = identifier.includes('@');\n    const isStudentNumber = /^[0-9-]+$/.test(identifier);\n    if (isEmail && !isStudentNumber) {\n      setDetectedUserType('admin'); // Email suggests admin (though could be student)\n    } else if (isStudentNumber && !isEmail) {\n      setDetectedUserType('student'); // Student number is definitely student\n    } else {\n      setDetectedUserType('unknown');\n    }\n  }, [formData.identifier]);\n  const validateForm = () => {\n    const errors = {};\n\n    // Identifier validation\n    if (!formData.identifier.trim()) {\n      errors.identifier = 'Email or student number is required';\n    } else {\n      const identifier = formData.identifier.trim();\n      const isEmail = identifier.includes('@');\n      const isStudentNumber = /^[0-9-]+$/.test(identifier);\n      if (isEmail && !VALIDATION_RULES.EMAIL.PATTERN.test(identifier)) {\n        errors.identifier = 'Please enter a valid email address';\n      } else if (!isEmail && !isStudentNumber && identifier.length > 0) {\n        // If it's not clearly an email or student number, it might still be valid\n        // Let the backend handle the validation\n      }\n    }\n\n    // Password validation\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    const checked = e.target.checked;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n\n    // Clear global error\n    if (error) {\n      clearError();\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      const credentials = {\n        identifier: formData.identifier.trim(),\n        password: formData.password,\n        userType: formData.userType === 'auto' ? undefined : formData.userType,\n        remember: formData.remember\n      };\n      console.log('🔐 UnifiedLogin - Attempting login with:', {\n        identifier: credentials.identifier,\n        userType: credentials.userType || 'auto-detect',\n        remember: credentials.remember\n      });\n      await login(credentials);\n\n      // The UnifiedAuthContext will handle redirection based on the user's role\n      console.log('✅ UnifiedLogin - Login successful, context will handle redirection');\n    } catch (error) {\n      console.error('❌ UnifiedLogin - Login failed:', error);\n      // Error is handled by the auth context\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const getUserTypeDisplayName = type => {\n    switch (type) {\n      case 'admin':\n        return 'Administrator';\n      case 'student':\n        return 'Student';\n      case 'auto':\n        return 'Auto-detect';\n      default:\n        return type;\n    }\n  };\n  const getPlaceholderText = () => {\n    if (formData.userType === 'admin') {\n      return 'Enter your email address';\n    } else if (formData.userType === 'student') {\n      return 'Enter your email or student number';\n    } else {\n      return 'Enter your email or student number';\n    }\n  };\n  const getDetectionHint = () => {\n    if (detectedUserType === 'admin') {\n      return '📧 Email detected - will try admin login first';\n    } else if (detectedUserType === 'student') {\n      return '🎓 Student number detected - will try student login';\n    }\n    return '';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"unified-login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"unified-login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unified-login__form-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"unified-login__form-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unified-login__form-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              className: \"unified-login__form-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"unified-login__form-title\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"unified-login__form-subtitle\",\n              children: \"Unified Login Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unified-login__error\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"unified-login__form\",\n            noValidate: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"unified-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"userType\",\n                className: \"unified-login__label\",\n                children: \"Login As\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"userType\",\n                name: \"userType\",\n                value: formData.userType,\n                onChange: handleInputChange,\n                className: \"unified-login__select\",\n                disabled: isLoading,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"auto\",\n                  children: \"Auto-detect from input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"admin\",\n                  children: \"Administrator\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"student\",\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), formErrors.userType && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unified-login__error-text\",\n                children: formErrors.userType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"unified-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"identifier\",\n                className: \"unified-login__label\",\n                children: formData.userType === 'admin' ? 'Email Address' : 'Email or Student Number'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"identifier\",\n                name: \"identifier\",\n                value: formData.identifier,\n                onChange: handleInputChange,\n                placeholder: getPlaceholderText(),\n                className: `unified-login__input ${formErrors.identifier ? 'error' : ''}`,\n                disabled: isLoading,\n                autoComplete: \"username\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), formErrors.identifier && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unified-login__error-text\",\n                children: formErrors.identifier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 43\n              }, this), formData.userType === 'auto' && getDetectionHint() && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unified-login__hint\",\n                children: getDetectionHint()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"unified-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"unified-login__label\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"unified-login__password-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  placeholder: \"Enter your password\",\n                  className: `unified-login__input ${formErrors.password ? 'error' : ''}`,\n                  disabled: isLoading,\n                  autoComplete: \"current-password\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: togglePasswordVisibility,\n                  className: \"unified-login__password-toggle\",\n                  disabled: isLoading,\n                  children: showPassword ? '👁️' : '👁️‍🗨️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), formErrors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unified-login__error-text\",\n                children: formErrors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"unified-login__remember\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"remember\",\n                name: \"remember\",\n                checked: formData.remember,\n                onChange: handleInputChange,\n                className: \"unified-login__checkbox\",\n                disabled: isLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember\",\n                className: \"unified-login__remember-label\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"unified-login__submit-btn\",\n              disabled: isLoading,\n              children: isLoading ? 'Signing in...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unified-login__signup-link\",\n            children: [\"Need admin access? \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/register\",\n              children: \"Register as Administrator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unified-login__info-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"unified-login__info-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"unified-login__school-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/ebb1.png\",\n              alt: \"E-Bulletin Board Logo\",\n              className: \"unified-login__school-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"unified-login__school-name\",\n              children: \"VCBA E-BULLETIN BOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"unified-login__school-description\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"unified-login__features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"unified-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/megaphone.png\",\n                    alt: \"Unified Access\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Unified Access\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Single login portal for both administrators and students\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"unified-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/message.png\",\n                    alt: \"Smart Detection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Smart Detection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Automatically detects whether you're an admin or student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"unified-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/heart.png\",\n                    alt: \"Secure & Simple\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"unified-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Secure & Simple\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Enhanced security with simplified user experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedLogin, \"Hx9CbukY22UOJtORxFfLwvXmI3g=\", false, function () {\n  return [useNavigate, useLocation, useUnifiedAuth];\n});\n_c = UnifiedLogin;\nexport default UnifiedLogin;\nvar _c;\n$RefreshReg$(_c, \"UnifiedLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useLocation", "useUnifiedAuth", "VALIDATION_RULES", "jsxDEV", "_jsxDEV", "UnifiedLogin", "_s", "navigate", "location", "login", "error", "isLoading", "clearError", "formData", "setFormData", "identifier", "password", "userType", "remember", "formErrors", "setFormErrors", "showPassword", "setShowPassword", "detectedUserType", "setDetectedUserType", "trim", "isEmail", "includes", "isStudentNumber", "test", "validateForm", "errors", "EMAIL", "PATTERN", "length", "Object", "keys", "handleInputChange", "e", "name", "value", "type", "target", "checked", "prev", "undefined", "handleSubmit", "preventDefault", "credentials", "console", "log", "togglePasswordVisibility", "getUserTypeDisplayName", "getPlaceholderText", "getDetectionHint", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onSubmit", "noValidate", "htmlFor", "id", "onChange", "disabled", "placeholder", "autoComplete", "required", "onClick", "to", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/auth/UnifiedLogin/UnifiedLogin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useUnifiedAuth } from '../../../contexts/UnifiedAuthContext';\nimport { VALIDATION_RULES } from '../../../config/constants';\nimport './UnifiedLogin.css';\n\ninterface FormData {\n  identifier: string; // Can be email or student number\n  password: string;\n  userType: 'admin' | 'student' | 'auto';\n  remember: boolean;\n}\n\ninterface FormErrors {\n  identifier?: string;\n  password?: string;\n  userType?: string;\n}\n\nconst UnifiedLogin: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, error, isLoading, clearError } = useUnifiedAuth();\n\n  const [formData, setFormData] = useState<FormData>({\n    identifier: '',\n    password: '',\n    userType: 'auto',\n    remember: false,\n  });\n\n  const [formErrors, setFormErrors] = useState<FormErrors>({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [detectedUserType, setDetectedUserType] = useState<'admin' | 'student' | 'unknown'>('unknown');\n\n  // Detect user type based on identifier\n  useEffect(() => {\n    const identifier = formData.identifier.trim();\n    if (!identifier) {\n      setDetectedUserType('unknown');\n      return;\n    }\n\n    const isEmail = identifier.includes('@');\n    const isStudentNumber = /^[0-9-]+$/.test(identifier);\n\n    if (isEmail && !isStudentNumber) {\n      setDetectedUserType('admin'); // Email suggests admin (though could be student)\n    } else if (isStudentNumber && !isEmail) {\n      setDetectedUserType('student'); // Student number is definitely student\n    } else {\n      setDetectedUserType('unknown');\n    }\n  }, [formData.identifier]);\n\n  const validateForm = (): boolean => {\n    const errors: FormErrors = {};\n\n    // Identifier validation\n    if (!formData.identifier.trim()) {\n      errors.identifier = 'Email or student number is required';\n    } else {\n      const identifier = formData.identifier.trim();\n      const isEmail = identifier.includes('@');\n      const isStudentNumber = /^[0-9-]+$/.test(identifier);\n\n      if (isEmail && !VALIDATION_RULES.EMAIL.PATTERN.test(identifier)) {\n        errors.identifier = 'Please enter a valid email address';\n      } else if (!isEmail && !isStudentNumber && identifier.length > 0) {\n        // If it's not clearly an email or student number, it might still be valid\n        // Let the backend handle the validation\n      }\n    }\n\n    // Password validation\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = (e.target as HTMLInputElement).checked;\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name as keyof FormErrors]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined,\n      }));\n    }\n\n    // Clear global error\n    if (error) {\n      clearError();\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      const credentials = {\n        identifier: formData.identifier.trim(),\n        password: formData.password,\n        userType: formData.userType === 'auto' ? undefined : formData.userType,\n        remember: formData.remember,\n      };\n\n      console.log('🔐 UnifiedLogin - Attempting login with:', {\n        identifier: credentials.identifier,\n        userType: credentials.userType || 'auto-detect',\n        remember: credentials.remember,\n      });\n\n      await login(credentials);\n\n      // The UnifiedAuthContext will handle redirection based on the user's role\n      console.log('✅ UnifiedLogin - Login successful, context will handle redirection');\n\n    } catch (error) {\n      console.error('❌ UnifiedLogin - Login failed:', error);\n      // Error is handled by the auth context\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const getUserTypeDisplayName = (type: 'admin' | 'student' | 'auto') => {\n    switch (type) {\n      case 'admin': return 'Administrator';\n      case 'student': return 'Student';\n      case 'auto': return 'Auto-detect';\n      default: return type;\n    }\n  };\n\n  const getPlaceholderText = () => {\n    if (formData.userType === 'admin') {\n      return 'Enter your email address';\n    } else if (formData.userType === 'student') {\n      return 'Enter your email or student number';\n    } else {\n      return 'Enter your email or student number';\n    }\n  };\n\n  const getDetectionHint = () => {\n    if (detectedUserType === 'admin') {\n      return '📧 Email detected - will try admin login first';\n    } else if (detectedUserType === 'student') {\n      return '🎓 Student number detected - will try student login';\n    }\n    return '';\n  };\n\n  return (\n    <div className=\"unified-login\">\n      <div className=\"unified-login__container\">\n        {/* Left Panel - Login Form */}\n        <div className=\"unified-login__form-section\">\n          <div className=\"unified-login__form-container\">\n            {/* Login Form Header */}\n            <div className=\"unified-login__form-header\">\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                className=\"unified-login__form-logo\"\n              />\n              <h1 className=\"unified-login__form-title\">Villamor College of Business and Arts, Inc.</h1>\n              <p className=\"unified-login__form-subtitle\">Unified Login Portal</p>\n            </div>\n\n            {/* Error Display */}\n            {error && (\n              <div className=\"unified-login__error\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n                {error}\n              </div>\n            )}\n\n            {/* Login Form */}\n            <form onSubmit={handleSubmit} className=\"unified-login__form\" noValidate>\n              {/* User Type Selection */}\n              <div className=\"unified-login__form-group\">\n                <label htmlFor=\"userType\" className=\"unified-login__label\">Login As</label>\n                <select\n                  id=\"userType\"\n                  name=\"userType\"\n                  value={formData.userType}\n                  onChange={handleInputChange}\n                  className=\"unified-login__select\"\n                  disabled={isLoading}\n                >\n                  <option value=\"auto\">Auto-detect from input</option>\n                  <option value=\"admin\">Administrator</option>\n                  <option value=\"student\">Student</option>\n                </select>\n                {formErrors.userType && <span className=\"unified-login__error-text\">{formErrors.userType}</span>}\n              </div>\n\n              {/* Identifier Input */}\n              <div className=\"unified-login__form-group\">\n                <label htmlFor=\"identifier\" className=\"unified-login__label\">\n                  {formData.userType === 'admin' ? 'Email Address' : 'Email or Student Number'}\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"identifier\"\n                  name=\"identifier\"\n                  value={formData.identifier}\n                  onChange={handleInputChange}\n                  placeholder={getPlaceholderText()}\n                  className={`unified-login__input ${formErrors.identifier ? 'error' : ''}`}\n                  disabled={isLoading}\n                  autoComplete=\"username\"\n                  required\n                />\n                {formErrors.identifier && <span className=\"unified-login__error-text\">{formErrors.identifier}</span>}\n                {formData.userType === 'auto' && getDetectionHint() && (\n                  <span className=\"unified-login__hint\">{getDetectionHint()}</span>\n                )}\n              </div>\n\n              {/* Password Input */}\n              <div className=\"unified-login__form-group\">\n                <label htmlFor=\"password\" className=\"unified-login__label\">Password</label>\n                <div className=\"unified-login__password-container\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"Enter your password\"\n                    className={`unified-login__input ${formErrors.password ? 'error' : ''}`}\n                    disabled={isLoading}\n                    autoComplete=\"current-password\"\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={togglePasswordVisibility}\n                    className=\"unified-login__password-toggle\"\n                    disabled={isLoading}\n                  >\n                    {showPassword ? '👁️' : '👁️‍🗨️'}\n                  </button>\n                </div>\n                {formErrors.password && <span className=\"unified-login__error-text\">{formErrors.password}</span>}\n              </div>\n\n              {/* Remember Me */}\n              <div className=\"unified-login__remember\">\n                <input\n                  type=\"checkbox\"\n                  id=\"remember\"\n                  name=\"remember\"\n                  checked={formData.remember}\n                  onChange={handleInputChange}\n                  className=\"unified-login__checkbox\"\n                  disabled={isLoading}\n                />\n                <label htmlFor=\"remember\" className=\"unified-login__remember-label\">Remember me</label>\n              </div>\n\n              {/* Submit Button */}\n              <button\n                type=\"submit\"\n                className=\"unified-login__submit-btn\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </button>\n            </form>\n\n            {/* Admin Registration Link */}\n            <div className=\"unified-login__signup-link\">\n              Need admin access? <Link to=\"/admin/register\">Register as Administrator</Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Right Panel - Information Section */}\n        <div className=\"unified-login__info-section\">\n          <div className=\"unified-login__info-content\">\n            {/* School Information */}\n            <div className=\"unified-login__school-info\">\n              <img\n                src=\"/logo/ebb1.png\"\n                alt=\"E-Bulletin Board Logo\"\n                className=\"unified-login__school-logo\"\n              />\n              <h3 className=\"unified-login__school-name\">\n                VCBA E-BULLETIN BOARD\n              </h3>\n              <p className=\"unified-login__school-description\">\n                Villamor College of Business and Arts, Inc.\n              </p>\n\n              {/* Features */}\n              <div className=\"unified-login__features\">\n                <div className=\"unified-login__feature\">\n                  <div className=\"unified-login__feature-icon\">\n                    <img src=\"/icons/megaphone.png\" alt=\"Unified Access\" />\n                  </div>\n                  <div className=\"unified-login__feature-content\">\n                    <h4>Unified Access</h4>\n                    <p>Single login portal for both administrators and students</p>\n                  </div>\n                </div>\n\n                <div className=\"unified-login__feature\">\n                  <div className=\"unified-login__feature-icon\">\n                    <img src=\"/icons/message.png\" alt=\"Smart Detection\" />\n                  </div>\n                  <div className=\"unified-login__feature-content\">\n                    <h4>Smart Detection</h4>\n                    <p>Automatically detects whether you're an admin or student</p>\n                  </div>\n                </div>\n\n                <div className=\"unified-login__feature\">\n                  <div className=\"unified-login__feature-icon\">\n                    <img src=\"/icons/heart.png\" alt=\"Secure & Simple\" />\n                  </div>\n                  <div className=\"unified-login__feature-content\">\n                    <h4>Secure & Simple</h4>\n                    <p>Enhanced security with simplified user experience</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe5B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,KAAK;IAAEC,KAAK;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGX,cAAc,CAAC,CAAC;EAEhE,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAW;IACjDmB,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAa,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAkC,SAAS,CAAC;;EAEpG;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAGF,QAAQ,CAACE,UAAU,CAACU,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACV,UAAU,EAAE;MACfS,mBAAmB,CAAC,SAAS,CAAC;MAC9B;IACF;IAEA,MAAME,OAAO,GAAGX,UAAU,CAACY,QAAQ,CAAC,GAAG,CAAC;IACxC,MAAMC,eAAe,GAAG,WAAW,CAACC,IAAI,CAACd,UAAU,CAAC;IAEpD,IAAIW,OAAO,IAAI,CAACE,eAAe,EAAE;MAC/BJ,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM,IAAII,eAAe,IAAI,CAACF,OAAO,EAAE;MACtCF,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;IAClC,CAAC,MAAM;MACLA,mBAAmB,CAAC,SAAS,CAAC;IAChC;EACF,CAAC,EAAE,CAACX,QAAQ,CAACE,UAAU,CAAC,CAAC;EAEzB,MAAMe,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,MAAkB,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAI,CAAClB,QAAQ,CAACE,UAAU,CAACU,IAAI,CAAC,CAAC,EAAE;MAC/BM,MAAM,CAAChB,UAAU,GAAG,qCAAqC;IAC3D,CAAC,MAAM;MACL,MAAMA,UAAU,GAAGF,QAAQ,CAACE,UAAU,CAACU,IAAI,CAAC,CAAC;MAC7C,MAAMC,OAAO,GAAGX,UAAU,CAACY,QAAQ,CAAC,GAAG,CAAC;MACxC,MAAMC,eAAe,GAAG,WAAW,CAACC,IAAI,CAACd,UAAU,CAAC;MAEpD,IAAIW,OAAO,IAAI,CAACxB,gBAAgB,CAAC8B,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACd,UAAU,CAAC,EAAE;QAC/DgB,MAAM,CAAChB,UAAU,GAAG,oCAAoC;MAC1D,CAAC,MAAM,IAAI,CAACW,OAAO,IAAI,CAACE,eAAe,IAAIb,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAE;QAChE;QACA;MAAA;IAEJ;;IAEA;IACA,IAAI,CAACrB,QAAQ,CAACG,QAAQ,EAAE;MACtBe,MAAM,CAACf,QAAQ,GAAG,sBAAsB;IAC1C;IAEAI,aAAa,CAACW,MAAM,CAAC;IACrB,OAAOI,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGH,CAAC,CAACI,MAAM;IACtC,MAAMC,OAAO,GAAIL,CAAC,CAACI,MAAM,CAAsBC,OAAO;IAEtD7B,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGE,OAAO,GAAGH;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrB,UAAU,CAACoB,IAAI,CAAqB,EAAE;MACxCnB,aAAa,CAACwB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACL,IAAI,GAAGM;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAInC,KAAK,EAAE;MACTE,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMkC,YAAY,GAAG,MAAOR,CAAkB,IAAK;IACjDA,CAAC,CAACS,cAAc,CAAC,CAAC;IAElB,IAAI,CAACjB,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMkB,WAAW,GAAG;QAClBjC,UAAU,EAAEF,QAAQ,CAACE,UAAU,CAACU,IAAI,CAAC,CAAC;QACtCT,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ,KAAK,MAAM,GAAG4B,SAAS,GAAGhC,QAAQ,CAACI,QAAQ;QACtEC,QAAQ,EAAEL,QAAQ,CAACK;MACrB,CAAC;MAED+B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtDnC,UAAU,EAAEiC,WAAW,CAACjC,UAAU;QAClCE,QAAQ,EAAE+B,WAAW,CAAC/B,QAAQ,IAAI,aAAa;QAC/CC,QAAQ,EAAE8B,WAAW,CAAC9B;MACxB,CAAC,CAAC;MAEF,MAAMT,KAAK,CAACuC,WAAW,CAAC;;MAExB;MACAC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;IAEnF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAMyC,wBAAwB,GAAGA,CAAA,KAAM;IACrC7B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAM+B,sBAAsB,GAAIX,IAAkC,IAAK;IACrE,QAAQA,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,eAAe;MACpC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMY,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIxC,QAAQ,CAACI,QAAQ,KAAK,OAAO,EAAE;MACjC,OAAO,0BAA0B;IACnC,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,KAAK,SAAS,EAAE;MAC1C,OAAO,oCAAoC;IAC7C,CAAC,MAAM;MACL,OAAO,oCAAoC;IAC7C;EACF,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/B,gBAAgB,KAAK,OAAO,EAAE;MAChC,OAAO,gDAAgD;IACzD,CAAC,MAAM,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MACzC,OAAO,qDAAqD;IAC9D;IACA,OAAO,EAAE;EACX,CAAC;EAED,oBACEnB,OAAA;IAAKmD,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BpD,OAAA;MAAKmD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAEvCpD,OAAA;QAAKmD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CpD,OAAA;UAAKmD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAE5CpD,OAAA;YAAKmD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCpD,OAAA;cACEqD,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,WAAW;cACfH,SAAS,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACF1D,OAAA;cAAImD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1F1D,OAAA;cAAGmD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,EAGLpD,KAAK,iBACJN,OAAA;YAAKmD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCpD,OAAA;cAAK2D,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAV,QAAA,eACzDpD,OAAA;gBAAM+D,CAAC,EAAC,uIAAuI;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO,CAAC,EACLpD,KAAK;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD1D,OAAA;YAAMoE,QAAQ,EAAE1B,YAAa;YAACS,SAAS,EAAC,qBAAqB;YAACkB,UAAU;YAAAjB,QAAA,gBAEtEpD,OAAA;cAAKmD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpD,OAAA;gBAAOsE,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3E1D,OAAA;gBACEuE,EAAE,EAAC,UAAU;gBACbpC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE3B,QAAQ,CAACI,QAAS;gBACzB2D,QAAQ,EAAEvC,iBAAkB;gBAC5BkB,SAAS,EAAC,uBAAuB;gBACjCsB,QAAQ,EAAElE,SAAU;gBAAA6C,QAAA,gBAEpBpD,OAAA;kBAAQoC,KAAK,EAAC,MAAM;kBAAAgB,QAAA,EAAC;gBAAsB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD1D,OAAA;kBAAQoC,KAAK,EAAC,OAAO;kBAAAgB,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1D,OAAA;kBAAQoC,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACR3C,UAAU,CAACF,QAAQ,iBAAIb,OAAA;gBAAMmD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAErC,UAAU,CAACF;cAAQ;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eAGN1D,OAAA;cAAKmD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpD,OAAA;gBAAOsE,OAAO,EAAC,YAAY;gBAACnB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EACzD3C,QAAQ,CAACI,QAAQ,KAAK,OAAO,GAAG,eAAe,GAAG;cAAyB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACR1D,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXkC,EAAE,EAAC,YAAY;gBACfpC,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE3B,QAAQ,CAACE,UAAW;gBAC3B6D,QAAQ,EAAEvC,iBAAkB;gBAC5ByC,WAAW,EAAEzB,kBAAkB,CAAC,CAAE;gBAClCE,SAAS,EAAE,wBAAwBpC,UAAU,CAACJ,UAAU,GAAG,OAAO,GAAG,EAAE,EAAG;gBAC1E8D,QAAQ,EAAElE,SAAU;gBACpBoE,YAAY,EAAC,UAAU;gBACvBC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACD3C,UAAU,CAACJ,UAAU,iBAAIX,OAAA;gBAAMmD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAErC,UAAU,CAACJ;cAAU;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACnGjD,QAAQ,CAACI,QAAQ,KAAK,MAAM,IAAIqC,gBAAgB,CAAC,CAAC,iBACjDlD,OAAA;gBAAMmD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEF,gBAAgB,CAAC;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACjE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1D,OAAA;cAAKmD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpD,OAAA;gBAAOsE,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3E1D,OAAA;gBAAKmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpD,OAAA;kBACEqC,IAAI,EAAEpB,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCsD,EAAE,EAAC,UAAU;kBACbpC,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE3B,QAAQ,CAACG,QAAS;kBACzB4D,QAAQ,EAAEvC,iBAAkB;kBAC5ByC,WAAW,EAAC,qBAAqB;kBACjCvB,SAAS,EAAE,wBAAwBpC,UAAU,CAACH,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;kBACxE6D,QAAQ,EAAElE,SAAU;kBACpBoE,YAAY,EAAC,kBAAkB;kBAC/BC,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF1D,OAAA;kBACEqC,IAAI,EAAC,QAAQ;kBACbwC,OAAO,EAAE9B,wBAAyB;kBAClCI,SAAS,EAAC,gCAAgC;kBAC1CsB,QAAQ,EAAElE,SAAU;kBAAA6C,QAAA,EAEnBnC,YAAY,GAAG,KAAK,GAAG;gBAAS;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACL3C,UAAU,CAACH,QAAQ,iBAAIZ,OAAA;gBAAMmD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAErC,UAAU,CAACH;cAAQ;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eAGN1D,OAAA;cAAKmD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCpD,OAAA;gBACEqC,IAAI,EAAC,UAAU;gBACfkC,EAAE,EAAC,UAAU;gBACbpC,IAAI,EAAC,UAAU;gBACfI,OAAO,EAAE9B,QAAQ,CAACK,QAAS;gBAC3B0D,QAAQ,EAAEvC,iBAAkB;gBAC5BkB,SAAS,EAAC,yBAAyB;gBACnCsB,QAAQ,EAAElE;cAAU;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACF1D,OAAA;gBAAOsE,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAGN1D,OAAA;cACEqC,IAAI,EAAC,QAAQ;cACbc,SAAS,EAAC,2BAA2B;cACrCsB,QAAQ,EAAElE,SAAU;cAAA6C,QAAA,EAEnB7C,SAAS,GAAG,eAAe,GAAG;YAAS;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGP1D,OAAA;YAAKmD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,qBACvB,eAAApD,OAAA,CAACN,IAAI;cAACoF,EAAE,EAAC,iBAAiB;cAAA1B,QAAA,EAAC;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKmD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CpD,OAAA;UAAKmD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAE1CpD,OAAA;YAAKmD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCpD,OAAA;cACEqD,GAAG,EAAC,gBAAgB;cACpBC,GAAG,EAAC,uBAAuB;cAC3BH,SAAS,EAAC;YAA4B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACF1D,OAAA;cAAImD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1D,OAAA;cAAGmD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEjD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJ1D,OAAA;cAAKmD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCpD,OAAA;gBAAKmD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpD,OAAA;kBAAKmD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CpD,OAAA;oBAAKqD,GAAG,EAAC,sBAAsB;oBAACC,GAAG,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACN1D,OAAA;kBAAKmD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpD,OAAA;oBAAAoD,QAAA,EAAI;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB1D,OAAA;oBAAAoD,QAAA,EAAG;kBAAwD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1D,OAAA;gBAAKmD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpD,OAAA;kBAAKmD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CpD,OAAA;oBAAKqD,GAAG,EAAC,oBAAoB;oBAACC,GAAG,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN1D,OAAA;kBAAKmD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpD,OAAA;oBAAAoD,QAAA,EAAI;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB1D,OAAA;oBAAAoD,QAAA,EAAG;kBAAwD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1D,OAAA;gBAAKmD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpD,OAAA;kBAAKmD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CpD,OAAA;oBAAKqD,GAAG,EAAC,kBAAkB;oBAACC,GAAG,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN1D,OAAA;kBAAKmD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpD,OAAA;oBAAAoD,QAAA,EAAI;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB1D,OAAA;oBAAAoD,QAAA,EAAG;kBAAiD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CA/UID,YAAsB;EAAA,QACTN,WAAW,EACXC,WAAW,EACoBC,cAAc;AAAA;AAAAkF,EAAA,GAH1D9E,YAAsB;AAiV5B,eAAeA,YAAY;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}