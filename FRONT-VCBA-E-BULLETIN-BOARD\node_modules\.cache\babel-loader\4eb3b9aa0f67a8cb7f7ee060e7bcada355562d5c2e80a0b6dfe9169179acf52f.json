{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 20v2\",\n  key: \"1n8e1g\"\n}], [\"path\", {\n  d: \"M14 20v2\",\n  key: \"1lq872\"\n}], [\"path\", {\n  d: \"M18 20v2\",\n  key: \"10uadw\"\n}], [\"path\", {\n  d: \"M21 20H3\",\n  key: \"kdqkdp\"\n}], [\"path\", {\n  d: \"M6 20v2\",\n  key: \"a9bc87\"\n}], [\"path\", {\n  d: \"M8 16V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v12\",\n  key: \"17n9tx\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"6\",\n  width: \"16\",\n  height: \"10\",\n  rx: \"2\",\n  key: \"1097i5\"\n}]];\nconst BriefcaseConveyorBelt = createLucideIcon(\"briefcase-conveyor-belt\", __iconNode);\nexport { __iconNode, BriefcaseConveyorBelt as default };\n//# sourceMappingURL=briefcase-conveyor-belt.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}