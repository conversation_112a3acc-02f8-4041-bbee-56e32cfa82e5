{"ast": null, "code": "var _UnifiedStorageManager;\nimport { ADMIN_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, ADMIN_REFRESH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY, STUDENT_USER_DATA_KEY, STUDENT_REFRESH_TOKEN_KEY } from '../config/constants';\n/**\n * Unified storage utility that manages role-aware authentication data\n * while maintaining isolation between admin and student sessions\n */\nexport class UnifiedStorageManager {\n  constructor() {}\n  static getInstance() {\n    if (!UnifiedStorageManager.instance) {\n      UnifiedStorageManager.instance = new UnifiedStorageManager();\n    }\n    return UnifiedStorageManager.instance;\n  }\n\n  /**\n   * Get storage keys for a specific role\n   */\n  getStorageKeys(role) {\n    switch (role) {\n      case 'admin':\n        return {\n          tokenKey: ADMIN_AUTH_TOKEN_KEY,\n          userDataKey: ADMIN_USER_DATA_KEY,\n          refreshTokenKey: ADMIN_REFRESH_TOKEN_KEY\n        };\n      case 'student':\n        return {\n          tokenKey: STUDENT_AUTH_TOKEN_KEY,\n          userDataKey: STUDENT_USER_DATA_KEY,\n          refreshTokenKey: STUDENT_REFRESH_TOKEN_KEY\n        };\n      default:\n        throw new Error(`Invalid role: ${role}`);\n    }\n  }\n\n  /**\n   * Store authentication data for a specific role\n   */\n  storeAuthData(role, token, user, refreshToken) {\n    const keys = this.getStorageKeys(role);\n    try {\n      localStorage.setItem(keys.tokenKey, token);\n      localStorage.setItem(keys.userDataKey, JSON.stringify(user));\n      if (refreshToken) {\n        localStorage.setItem(keys.refreshTokenKey, refreshToken);\n      }\n      console.log(`✅ UnifiedStorage - Stored auth data for ${role}:`, user.email);\n    } catch (error) {\n      console.error(`❌ UnifiedStorage - Failed to store auth data for ${role}:`, error);\n      throw new Error(`Failed to store authentication data for ${role}`);\n    }\n  }\n\n  /**\n   * Get authentication token for a specific role\n   */\n  getToken(role) {\n    const keys = this.getStorageKeys(role);\n    return localStorage.getItem(keys.tokenKey);\n  }\n\n  /**\n   * Get user data for a specific role\n   */\n  getUserData(role) {\n    const keys = this.getStorageKeys(role);\n    try {\n      const userData = localStorage.getItem(keys.userDataKey);\n      if (!userData) return null;\n      const user = JSON.parse(userData);\n\n      // Validate that the stored user has the correct role\n      if (user.role !== role) {\n        console.warn(`⚠️ UnifiedStorage - Role mismatch for ${role}. Expected: ${role}, Got: ${user.role}`);\n        this.clearAuthData(role); // Clear invalid data\n        return null;\n      }\n      return user;\n    } catch (error) {\n      console.error(`❌ UnifiedStorage - Failed to parse user data for ${role}:`, error);\n      this.clearAuthData(role); // Clear corrupted data\n      return null;\n    }\n  }\n\n  /**\n   * Get refresh token for a specific role\n   */\n  getRefreshToken(role) {\n    const keys = this.getStorageKeys(role);\n    return localStorage.getItem(keys.refreshTokenKey);\n  }\n\n  /**\n   * Check if user is authenticated for a specific role\n   */\n  isAuthenticated(role) {\n    const token = this.getToken(role);\n    const userData = this.getUserData(role);\n    return !!(token && userData && userData.role === role);\n  }\n\n  /**\n   * Clear authentication data for a specific role\n   */\n  clearAuthData(role) {\n    const keys = this.getStorageKeys(role);\n    localStorage.removeItem(keys.tokenKey);\n    localStorage.removeItem(keys.userDataKey);\n    localStorage.removeItem(keys.refreshTokenKey);\n    console.log(`🧹 UnifiedStorage - Cleared auth data for ${role}`);\n  }\n\n  /**\n   * Clear all authentication data (both admin and student)\n   */\n  clearAllAuthData() {\n    this.clearAuthData('admin');\n    this.clearAuthData('student');\n    console.log('🧹 UnifiedStorage - Cleared all auth data');\n  }\n\n  /**\n   * Get all available authenticated roles\n   */\n  getAvailableRoles() {\n    const roles = [];\n    if (this.isAuthenticated('admin')) {\n      roles.push('admin');\n    }\n    if (this.isAuthenticated('student')) {\n      roles.push('student');\n    }\n    return roles;\n  }\n\n  /**\n   * Get the most recently used role (based on last login time)\n   */\n  getMostRecentRole() {\n    const adminUser = this.getUserData('admin');\n    const studentUser = this.getUserData('student');\n    if (!adminUser && !studentUser) return null;\n    if (adminUser && !studentUser) return 'admin';\n    if (!adminUser && studentUser) return 'student';\n\n    // Both exist, compare last login times\n    const adminLastLogin = adminUser.lastLogin ? new Date(adminUser.lastLogin).getTime() : 0;\n    const studentLastLogin = studentUser.lastLogin ? new Date(studentUser.lastLogin).getTime() : 0;\n    return adminLastLogin >= studentLastLogin ? 'admin' : 'student';\n  }\n\n  /**\n   * Update user data for a specific role\n   */\n  updateUserData(role, user) {\n    if (user.role !== role) {\n      throw new Error(`Role mismatch: Cannot store ${user.role} data in ${role} storage`);\n    }\n    const keys = this.getStorageKeys(role);\n    localStorage.setItem(keys.userDataKey, JSON.stringify(user));\n    console.log(`✅ UnifiedStorage - Updated user data for ${role}:`, user.email);\n  }\n\n  /**\n   * Get debug information about stored authentication data\n   */\n  getDebugInfo() {\n    return {\n      admin: {\n        hasToken: !!this.getToken('admin'),\n        hasUserData: !!this.getUserData('admin'),\n        hasRefreshToken: !!this.getRefreshToken('admin'),\n        isAuthenticated: this.isAuthenticated('admin')\n      },\n      student: {\n        hasToken: !!this.getToken('student'),\n        hasUserData: !!this.getUserData('student'),\n        hasRefreshToken: !!this.getRefreshToken('student'),\n        isAuthenticated: this.isAuthenticated('student')\n      },\n      availableRoles: this.getAvailableRoles(),\n      mostRecentRole: this.getMostRecentRole()\n    };\n  }\n}\n\n// Export singleton instance\n_UnifiedStorageManager = UnifiedStorageManager;\nUnifiedStorageManager.instance = void 0;\nexport const unifiedStorage = UnifiedStorageManager.getInstance();", "map": {"version": 3, "names": ["ADMIN_AUTH_TOKEN_KEY", "ADMIN_USER_DATA_KEY", "ADMIN_REFRESH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "STUDENT_USER_DATA_KEY", "STUDENT_REFRESH_TOKEN_KEY", "UnifiedStorageManager", "constructor", "getInstance", "instance", "getStorageKeys", "role", "<PERSON><PERSON><PERSON>", "userDataKey", "refreshT<PERSON><PERSON><PERSON>", "Error", "storeAuthData", "token", "user", "refreshToken", "keys", "localStorage", "setItem", "JSON", "stringify", "console", "log", "email", "error", "getToken", "getItem", "getUserData", "userData", "parse", "warn", "clearAuthData", "getRefreshToken", "isAuthenticated", "removeItem", "clearAllAuthData", "getAvailableRoles", "roles", "push", "getMostRecentRole", "adminUser", "studentUser", "adminLastLogin", "lastLogin", "Date", "getTime", "studentLastLogin", "updateUserData", "getDebugInfo", "admin", "hasToken", "hasUserData", "hasRefreshToken", "student", "availableRoles", "mostRecentRole", "_UnifiedStorageManager", "unifiedStorage"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/unifiedStorage.ts"], "sourcesContent": ["import { User } from '../types/auth.types';\nimport { \n  ADMIN_AUTH_TOKEN_KEY, \n  ADMIN_USER_DATA_KEY, \n  ADMIN_REFRESH_TOKEN_KEY,\n  STUDENT_AUTH_TOKEN_KEY, \n  STUDENT_USER_DATA_KEY, \n  STUDENT_REFRESH_TOKEN_KEY \n} from '../config/constants';\n\nexport type UserRole = 'admin' | 'student';\n\ninterface StorageKeys {\n  tokenKey: string;\n  userDataKey: string;\n  refreshTokenKey: string;\n}\n\n/**\n * Unified storage utility that manages role-aware authentication data\n * while maintaining isolation between admin and student sessions\n */\nexport class UnifiedStorageManager {\n  private static instance: UnifiedStorageManager;\n\n  private constructor() {}\n\n  public static getInstance(): UnifiedStorageManager {\n    if (!UnifiedStorageManager.instance) {\n      UnifiedStorageManager.instance = new UnifiedStorageManager();\n    }\n    return UnifiedStorageManager.instance;\n  }\n\n  /**\n   * Get storage keys for a specific role\n   */\n  private getStorageKeys(role: UserRole): StorageKeys {\n    switch (role) {\n      case 'admin':\n        return {\n          tokenKey: ADMIN_AUTH_TOKEN_KEY,\n          userDataKey: ADMIN_USER_DATA_KEY,\n          refreshTokenKey: ADMIN_REFRESH_TOKEN_KEY,\n        };\n      case 'student':\n        return {\n          tokenKey: STUDENT_AUTH_TOKEN_KEY,\n          userDataKey: STUDENT_USER_DATA_KEY,\n          refreshTokenKey: STUDENT_REFRESH_TOKEN_KEY,\n        };\n      default:\n        throw new Error(`Invalid role: ${role}`);\n    }\n  }\n\n  /**\n   * Store authentication data for a specific role\n   */\n  public storeAuthData(role: UserRole, token: string, user: User, refreshToken?: string): void {\n    const keys = this.getStorageKeys(role);\n    \n    try {\n      localStorage.setItem(keys.tokenKey, token);\n      localStorage.setItem(keys.userDataKey, JSON.stringify(user));\n      \n      if (refreshToken) {\n        localStorage.setItem(keys.refreshTokenKey, refreshToken);\n      }\n\n      console.log(`✅ UnifiedStorage - Stored auth data for ${role}:`, user.email);\n    } catch (error) {\n      console.error(`❌ UnifiedStorage - Failed to store auth data for ${role}:`, error);\n      throw new Error(`Failed to store authentication data for ${role}`);\n    }\n  }\n\n  /**\n   * Get authentication token for a specific role\n   */\n  public getToken(role: UserRole): string | null {\n    const keys = this.getStorageKeys(role);\n    return localStorage.getItem(keys.tokenKey);\n  }\n\n  /**\n   * Get user data for a specific role\n   */\n  public getUserData(role: UserRole): User | null {\n    const keys = this.getStorageKeys(role);\n    \n    try {\n      const userData = localStorage.getItem(keys.userDataKey);\n      if (!userData) return null;\n      \n      const user = JSON.parse(userData) as User;\n      \n      // Validate that the stored user has the correct role\n      if (user.role !== role) {\n        console.warn(`⚠️ UnifiedStorage - Role mismatch for ${role}. Expected: ${role}, Got: ${user.role}`);\n        this.clearAuthData(role); // Clear invalid data\n        return null;\n      }\n      \n      return user;\n    } catch (error) {\n      console.error(`❌ UnifiedStorage - Failed to parse user data for ${role}:`, error);\n      this.clearAuthData(role); // Clear corrupted data\n      return null;\n    }\n  }\n\n  /**\n   * Get refresh token for a specific role\n   */\n  public getRefreshToken(role: UserRole): string | null {\n    const keys = this.getStorageKeys(role);\n    return localStorage.getItem(keys.refreshTokenKey);\n  }\n\n  /**\n   * Check if user is authenticated for a specific role\n   */\n  public isAuthenticated(role: UserRole): boolean {\n    const token = this.getToken(role);\n    const userData = this.getUserData(role);\n    return !!(token && userData && userData.role === role);\n  }\n\n  /**\n   * Clear authentication data for a specific role\n   */\n  public clearAuthData(role: UserRole): void {\n    const keys = this.getStorageKeys(role);\n    \n    localStorage.removeItem(keys.tokenKey);\n    localStorage.removeItem(keys.userDataKey);\n    localStorage.removeItem(keys.refreshTokenKey);\n    \n    console.log(`🧹 UnifiedStorage - Cleared auth data for ${role}`);\n  }\n\n  /**\n   * Clear all authentication data (both admin and student)\n   */\n  public clearAllAuthData(): void {\n    this.clearAuthData('admin');\n    this.clearAuthData('student');\n    console.log('🧹 UnifiedStorage - Cleared all auth data');\n  }\n\n  /**\n   * Get all available authenticated roles\n   */\n  public getAvailableRoles(): UserRole[] {\n    const roles: UserRole[] = [];\n    \n    if (this.isAuthenticated('admin')) {\n      roles.push('admin');\n    }\n    \n    if (this.isAuthenticated('student')) {\n      roles.push('student');\n    }\n    \n    return roles;\n  }\n\n  /**\n   * Get the most recently used role (based on last login time)\n   */\n  public getMostRecentRole(): UserRole | null {\n    const adminUser = this.getUserData('admin');\n    const studentUser = this.getUserData('student');\n    \n    if (!adminUser && !studentUser) return null;\n    if (adminUser && !studentUser) return 'admin';\n    if (!adminUser && studentUser) return 'student';\n    \n    // Both exist, compare last login times\n    const adminLastLogin = adminUser!.lastLogin ? new Date(adminUser!.lastLogin).getTime() : 0;\n    const studentLastLogin = studentUser!.lastLogin ? new Date(studentUser!.lastLogin).getTime() : 0;\n    \n    return adminLastLogin >= studentLastLogin ? 'admin' : 'student';\n  }\n\n  /**\n   * Update user data for a specific role\n   */\n  public updateUserData(role: UserRole, user: User): void {\n    if (user.role !== role) {\n      throw new Error(`Role mismatch: Cannot store ${user.role} data in ${role} storage`);\n    }\n    \n    const keys = this.getStorageKeys(role);\n    localStorage.setItem(keys.userDataKey, JSON.stringify(user));\n    console.log(`✅ UnifiedStorage - Updated user data for ${role}:`, user.email);\n  }\n\n  /**\n   * Get debug information about stored authentication data\n   */\n  public getDebugInfo(): Record<string, any> {\n    return {\n      admin: {\n        hasToken: !!this.getToken('admin'),\n        hasUserData: !!this.getUserData('admin'),\n        hasRefreshToken: !!this.getRefreshToken('admin'),\n        isAuthenticated: this.isAuthenticated('admin'),\n      },\n      student: {\n        hasToken: !!this.getToken('student'),\n        hasUserData: !!this.getUserData('student'),\n        hasRefreshToken: !!this.getRefreshToken('student'),\n        isAuthenticated: this.isAuthenticated('student'),\n      },\n      availableRoles: this.getAvailableRoles(),\n      mostRecentRole: this.getMostRecentRole(),\n    };\n  }\n}\n\n// Export singleton instance\nexport const unifiedStorage = UnifiedStorageManager.getInstance();\n"], "mappings": ";AACA,SACEA,oBAAoB,EACpBC,mBAAmB,EACnBC,uBAAuB,EACvBC,sBAAsB,EACtBC,qBAAqB,EACrBC,yBAAyB,QACpB,qBAAqB;AAU5B;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,CAAC;EAGzBC,WAAWA,CAAA,EAAG,CAAC;EAEvB,OAAcC,WAAWA,CAAA,EAA0B;IACjD,IAAI,CAACF,qBAAqB,CAACG,QAAQ,EAAE;MACnCH,qBAAqB,CAACG,QAAQ,GAAG,IAAIH,qBAAqB,CAAC,CAAC;IAC9D;IACA,OAAOA,qBAAqB,CAACG,QAAQ;EACvC;;EAEA;AACF;AACA;EACUC,cAAcA,CAACC,IAAc,EAAe;IAClD,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UACLC,QAAQ,EAAEZ,oBAAoB;UAC9Ba,WAAW,EAAEZ,mBAAmB;UAChCa,eAAe,EAAEZ;QACnB,CAAC;MACH,KAAK,SAAS;QACZ,OAAO;UACLU,QAAQ,EAAET,sBAAsB;UAChCU,WAAW,EAAET,qBAAqB;UAClCU,eAAe,EAAET;QACnB,CAAC;MACH;QACE,MAAM,IAAIU,KAAK,CAAC,iBAAiBJ,IAAI,EAAE,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACSK,aAAaA,CAACL,IAAc,EAAEM,KAAa,EAAEC,IAAU,EAAEC,YAAqB,EAAQ;IAC3F,MAAMC,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IAEtC,IAAI;MACFU,YAAY,CAACC,OAAO,CAACF,IAAI,CAACR,QAAQ,EAAEK,KAAK,CAAC;MAC1CI,YAAY,CAACC,OAAO,CAACF,IAAI,CAACP,WAAW,EAAEU,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;MAE5D,IAAIC,YAAY,EAAE;QAChBE,YAAY,CAACC,OAAO,CAACF,IAAI,CAACN,eAAe,EAAEK,YAAY,CAAC;MAC1D;MAEAM,OAAO,CAACC,GAAG,CAAC,2CAA2Cf,IAAI,GAAG,EAAEO,IAAI,CAACS,KAAK,CAAC;IAC7E,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oDAAoDjB,IAAI,GAAG,EAAEiB,KAAK,CAAC;MACjF,MAAM,IAAIb,KAAK,CAAC,2CAA2CJ,IAAI,EAAE,CAAC;IACpE;EACF;;EAEA;AACF;AACA;EACSkB,QAAQA,CAAClB,IAAc,EAAiB;IAC7C,MAAMS,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IACtC,OAAOU,YAAY,CAACS,OAAO,CAACV,IAAI,CAACR,QAAQ,CAAC;EAC5C;;EAEA;AACF;AACA;EACSmB,WAAWA,CAACpB,IAAc,EAAe;IAC9C,MAAMS,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IAEtC,IAAI;MACF,MAAMqB,QAAQ,GAAGX,YAAY,CAACS,OAAO,CAACV,IAAI,CAACP,WAAW,CAAC;MACvD,IAAI,CAACmB,QAAQ,EAAE,OAAO,IAAI;MAE1B,MAAMd,IAAI,GAAGK,IAAI,CAACU,KAAK,CAACD,QAAQ,CAAS;;MAEzC;MACA,IAAId,IAAI,CAACP,IAAI,KAAKA,IAAI,EAAE;QACtBc,OAAO,CAACS,IAAI,CAAC,yCAAyCvB,IAAI,eAAeA,IAAI,UAAUO,IAAI,CAACP,IAAI,EAAE,CAAC;QACnG,IAAI,CAACwB,aAAa,CAACxB,IAAI,CAAC,CAAC,CAAC;QAC1B,OAAO,IAAI;MACb;MAEA,OAAOO,IAAI;IACb,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oDAAoDjB,IAAI,GAAG,EAAEiB,KAAK,CAAC;MACjF,IAAI,CAACO,aAAa,CAACxB,IAAI,CAAC,CAAC,CAAC;MAC1B,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACSyB,eAAeA,CAACzB,IAAc,EAAiB;IACpD,MAAMS,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IACtC,OAAOU,YAAY,CAACS,OAAO,CAACV,IAAI,CAACN,eAAe,CAAC;EACnD;;EAEA;AACF;AACA;EACSuB,eAAeA,CAAC1B,IAAc,EAAW;IAC9C,MAAMM,KAAK,GAAG,IAAI,CAACY,QAAQ,CAAClB,IAAI,CAAC;IACjC,MAAMqB,QAAQ,GAAG,IAAI,CAACD,WAAW,CAACpB,IAAI,CAAC;IACvC,OAAO,CAAC,EAAEM,KAAK,IAAIe,QAAQ,IAAIA,QAAQ,CAACrB,IAAI,KAAKA,IAAI,CAAC;EACxD;;EAEA;AACF;AACA;EACSwB,aAAaA,CAACxB,IAAc,EAAQ;IACzC,MAAMS,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IAEtCU,YAAY,CAACiB,UAAU,CAAClB,IAAI,CAACR,QAAQ,CAAC;IACtCS,YAAY,CAACiB,UAAU,CAAClB,IAAI,CAACP,WAAW,CAAC;IACzCQ,YAAY,CAACiB,UAAU,CAAClB,IAAI,CAACN,eAAe,CAAC;IAE7CW,OAAO,CAACC,GAAG,CAAC,6CAA6Cf,IAAI,EAAE,CAAC;EAClE;;EAEA;AACF;AACA;EACS4B,gBAAgBA,CAAA,EAAS;IAC9B,IAAI,CAACJ,aAAa,CAAC,OAAO,CAAC;IAC3B,IAAI,CAACA,aAAa,CAAC,SAAS,CAAC;IAC7BV,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;;EAEA;AACF;AACA;EACSc,iBAAiBA,CAAA,EAAe;IACrC,MAAMC,KAAiB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAACJ,eAAe,CAAC,OAAO,CAAC,EAAE;MACjCI,KAAK,CAACC,IAAI,CAAC,OAAO,CAAC;IACrB;IAEA,IAAI,IAAI,CAACL,eAAe,CAAC,SAAS,CAAC,EAAE;MACnCI,KAAK,CAACC,IAAI,CAAC,SAAS,CAAC;IACvB;IAEA,OAAOD,KAAK;EACd;;EAEA;AACF;AACA;EACSE,iBAAiBA,CAAA,EAAoB;IAC1C,MAAMC,SAAS,GAAG,IAAI,CAACb,WAAW,CAAC,OAAO,CAAC;IAC3C,MAAMc,WAAW,GAAG,IAAI,CAACd,WAAW,CAAC,SAAS,CAAC;IAE/C,IAAI,CAACa,SAAS,IAAI,CAACC,WAAW,EAAE,OAAO,IAAI;IAC3C,IAAID,SAAS,IAAI,CAACC,WAAW,EAAE,OAAO,OAAO;IAC7C,IAAI,CAACD,SAAS,IAAIC,WAAW,EAAE,OAAO,SAAS;;IAE/C;IACA,MAAMC,cAAc,GAAGF,SAAS,CAAEG,SAAS,GAAG,IAAIC,IAAI,CAACJ,SAAS,CAAEG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC;IAC1F,MAAMC,gBAAgB,GAAGL,WAAW,CAAEE,SAAS,GAAG,IAAIC,IAAI,CAACH,WAAW,CAAEE,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC;IAEhG,OAAOH,cAAc,IAAII,gBAAgB,GAAG,OAAO,GAAG,SAAS;EACjE;;EAEA;AACF;AACA;EACSC,cAAcA,CAACxC,IAAc,EAAEO,IAAU,EAAQ;IACtD,IAAIA,IAAI,CAACP,IAAI,KAAKA,IAAI,EAAE;MACtB,MAAM,IAAII,KAAK,CAAC,+BAA+BG,IAAI,CAACP,IAAI,YAAYA,IAAI,UAAU,CAAC;IACrF;IAEA,MAAMS,IAAI,GAAG,IAAI,CAACV,cAAc,CAACC,IAAI,CAAC;IACtCU,YAAY,CAACC,OAAO,CAACF,IAAI,CAACP,WAAW,EAAEU,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;IAC5DO,OAAO,CAACC,GAAG,CAAC,4CAA4Cf,IAAI,GAAG,EAAEO,IAAI,CAACS,KAAK,CAAC;EAC9E;;EAEA;AACF;AACA;EACSyB,YAAYA,CAAA,EAAwB;IACzC,OAAO;MACLC,KAAK,EAAE;QACLC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAACzB,QAAQ,CAAC,OAAO,CAAC;QAClC0B,WAAW,EAAE,CAAC,CAAC,IAAI,CAACxB,WAAW,CAAC,OAAO,CAAC;QACxCyB,eAAe,EAAE,CAAC,CAAC,IAAI,CAACpB,eAAe,CAAC,OAAO,CAAC;QAChDC,eAAe,EAAE,IAAI,CAACA,eAAe,CAAC,OAAO;MAC/C,CAAC;MACDoB,OAAO,EAAE;QACPH,QAAQ,EAAE,CAAC,CAAC,IAAI,CAACzB,QAAQ,CAAC,SAAS,CAAC;QACpC0B,WAAW,EAAE,CAAC,CAAC,IAAI,CAACxB,WAAW,CAAC,SAAS,CAAC;QAC1CyB,eAAe,EAAE,CAAC,CAAC,IAAI,CAACpB,eAAe,CAAC,SAAS,CAAC;QAClDC,eAAe,EAAE,IAAI,CAACA,eAAe,CAAC,SAAS;MACjD,CAAC;MACDqB,cAAc,EAAE,IAAI,CAAClB,iBAAiB,CAAC,CAAC;MACxCmB,cAAc,EAAE,IAAI,CAAChB,iBAAiB,CAAC;IACzC,CAAC;EACH;AACF;;AAEA;AAAAiB,sBAAA,GAxMatD,qBAAqB;AAArBA,qBAAqB,CACjBG,QAAQ;AAwMzB,OAAO,MAAMoD,cAAc,GAAGvD,qBAAqB,CAACE,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}