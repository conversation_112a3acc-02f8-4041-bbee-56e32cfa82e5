{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 20a6 6 0 0 0-12 0\",\n  key: \"1qehca\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"4\",\n  key: \"1h16sb\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst CircleUserRound = createLucideIcon(\"circle-user-round\", __iconNode);\nexport { __iconNode, CircleUserRound as default };\n//# sourceMappingURL=circle-user-round.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}