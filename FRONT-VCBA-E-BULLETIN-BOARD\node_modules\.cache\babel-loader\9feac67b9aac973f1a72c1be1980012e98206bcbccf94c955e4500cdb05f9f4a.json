{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"14\",\n  x: \"3\",\n  y: \"5\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"12ruh7\"\n}], [\"path\", {\n  d: \"M7 15h4M15 15h2M7 11h2M13 11h4\",\n  key: \"1ueiar\"\n}]];\nconst Captions = createLucideIcon(\"captions\", __iconNode);\nexport { __iconNode, Captions as default };\n//# sourceMappingURL=captions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}