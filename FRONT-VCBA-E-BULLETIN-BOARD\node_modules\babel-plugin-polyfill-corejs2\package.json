{"name": "babel-plugin-polyfill-corejs2", "version": "0.4.14", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "repository": {"type": "git", "url": "https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b"}