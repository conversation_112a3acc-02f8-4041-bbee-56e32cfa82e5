{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12H11\",\n  key: \"wd7e0v\"\n}], [\"path\", {\n  d: \"M21 18H11\",\n  key: \"4wu86t\"\n}], [\"path\", {\n  d: \"M21 6H11\",\n  key: \"6dy1d6\"\n}], [\"path\", {\n  d: \"m7 8-4 4 4 4\",\n  key: \"o5hrat\"\n}]];\nconst IndentDecrease = createLucideIcon(\"indent-decrease\", __iconNode);\nexport { __iconNode, IndentDecrease as default };\n//# sourceMappingURL=indent-decrease.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}