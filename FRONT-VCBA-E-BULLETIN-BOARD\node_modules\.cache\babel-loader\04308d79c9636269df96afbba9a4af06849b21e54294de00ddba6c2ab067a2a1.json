{"ast": null, "code": "/**\n * React Router DOM v6.0.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { useRef, useState, useLayoutEffect, createElement, forwardRef, useCallback, useMemo } from 'react';\nimport { createBrowserHistory, createHashHistory, createPath } from 'history';\nimport { Router, useHref, useLocation, useResolvedPath, useNavigate } from 'react-router';\nexport { MemoryRouter, Navigate, Outlet, Route, Router, Routes, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, createRoutesFromChildren, generatePath, matchPath, matchRoutes, renderMatches, resolvePath, useHref, useInRouterContext, useLocation, useMatch, useNavigate, useNavigationType, useOutlet, useParams, useResolvedPath, useRoutes } from 'react-router';\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst _excluded = [\"onClick\", \"replace\", \"state\", \"target\", \"to\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\"];\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} ////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A <Router> for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref;\n  let historyRef = useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\n/**\n * A <Router> for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref2;\n  let historyRef = useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nconst Link = /*#__PURE__*/forwardRef(function LinkWithRef(_ref3, ref) {\n  let {\n      onClick,\n      replace = false,\n      state,\n      target,\n      to\n    } = _ref3,\n    rest = _objectWithoutPropertiesLoose(_ref3, _excluded);\n  let href = useHref(to);\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return (/*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    createElement(\"a\", _extends({}, rest, {\n      href: href,\n      onClick: handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/forwardRef(function NavLinkWithRef(_ref4, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to\n    } = _ref4,\n    rest = _objectWithoutPropertiesLoose(_ref4, _excluded2);\n  let location = useLocation();\n  let path = useResolvedPath(to);\n  let locationPathname = location.pathname;\n  let toPathname = path.pathname;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    toPathname = toPathname.toLowerCase();\n  }\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive\n  }) : styleProp;\n  return /*#__PURE__*/createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n} ////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` compoments with the same click behavior we\n * use in our exported `<Link>`.\n */\n\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to);\n  return useCallback(event => {\n    if (event.button === 0 && (\n    // Ignore everything but left clicks\n    !target || target === \"_self\") &&\n    // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n    ) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here.\n\n      let replace = !!replaceProp || createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = useRef(createSearchParams(defaultInit));\n  let location = useLocation();\n  let searchParams = useMemo(() => {\n    let searchParams = createSearchParams(location.search);\n    for (let key of defaultSearchParamsRef.current.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParamsRef.current.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n    return searchParams;\n  }, [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = useCallback((nextInit, navigateOptions) => {\n    navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n  }, [navigate]);\n  return [searchParams, setSearchParams];\n}\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nexport { BrowserRouter, HashRouter, Link, NavLink, createSearchParams, useLinkClickHandler, useSearchParams };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}