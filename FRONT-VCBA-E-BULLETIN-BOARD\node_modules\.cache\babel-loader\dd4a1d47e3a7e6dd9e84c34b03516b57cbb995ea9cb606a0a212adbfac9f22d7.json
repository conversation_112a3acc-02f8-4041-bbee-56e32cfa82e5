{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M13 6h3a2 2 0 0 1 2 2v7\",\n  key: \"1yeb86\"\n}], [\"path\", {\n  d: \"M11 18H8a2 2 0 0 1-2-2V9\",\n  key: \"19pyzm\"\n}]];\nconst GitCompare = createLucideIcon(\"git-compare\", __iconNode);\nexport { __iconNode, GitCompare as default };\n//# sourceMappingURL=git-compare.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}