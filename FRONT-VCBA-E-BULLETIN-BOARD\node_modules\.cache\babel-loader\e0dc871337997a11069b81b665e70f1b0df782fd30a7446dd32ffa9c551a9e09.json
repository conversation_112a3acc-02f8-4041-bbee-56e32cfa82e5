{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"11\",\n  cy: \"13\",\n  r: \"9\",\n  key: \"hd149\"\n}], [\"path\", {\n  d: \"M14.35 4.65 16.3 2.7a2.41 2.41 0 0 1 3.4 0l1.6 1.6a2.4 2.4 0 0 1 0 3.4l-1.95 1.95\",\n  key: \"jp4j1b\"\n}], [\"path\", {\n  d: \"m22 2-1.5 1.5\",\n  key: \"ay92ug\"\n}]];\nconst Bomb = createLucideIcon(\"bomb\", __iconNode);\nexport { __iconNode, Bomb as default };\n//# sourceMappingURL=bomb.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}