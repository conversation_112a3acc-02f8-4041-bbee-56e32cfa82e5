{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"m16 12-4-4-4 4\",\n  key: \"177agl\"\n}], [\"path\", {\n  d: \"M12 16V8\",\n  key: \"1sbj14\"\n}]];\nconst SquareArrowUp = createLucideIcon(\"square-arrow-up\", __iconNode);\nexport { __iconNode, SquareArrowUp as default };\n//# sourceMappingURL=square-arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}