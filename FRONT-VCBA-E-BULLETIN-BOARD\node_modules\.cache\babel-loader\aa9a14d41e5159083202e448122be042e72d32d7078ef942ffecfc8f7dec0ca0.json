{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 16-2 2 2 2\",\n  key: \"kkc6pm\"\n}], [\"path\", {\n  d: \"M3 12h15a3 3 0 1 1 0 6h-4\",\n  key: \"1cl7v7\"\n}], [\"path\", {\n  d: \"M3 18h7\",\n  key: \"sq21v6\"\n}], [\"path\", {\n  d: \"M3 6h18\",\n  key: \"d0wm0j\"\n}]];\nconst WrapText = createLucideIcon(\"wrap-text\", __iconNode);\nexport { __iconNode, WrapText as default };\n//# sourceMappingURL=wrap-text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}