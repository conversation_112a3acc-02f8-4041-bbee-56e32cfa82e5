{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 7.5V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-1.5\",\n  key: \"1yk7aj\"\n}], [\"path\", {\n  d: \"M2 13h10\",\n  key: \"pgb2dq\"\n}], [\"path\", {\n  d: \"m5 10-3 3 3 3\",\n  key: \"1r8ie0\"\n}]];\nconst FolderOutput = createLucideIcon(\"folder-output\", __iconNode);\nexport { __iconNode, FolderOutput as default };\n//# sourceMappingURL=folder-output.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}