{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M12 6h5a2 2 0 0 1 2 2v7\",\n  key: \"1yj91y\"\n}], [\"path\", {\n  d: \"m15 9-3-3 3-3\",\n  key: \"1lwv8l\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1qljk2\"\n}], [\"path\", {\n  d: \"M12 18H7a2 2 0 0 1-2-2V9\",\n  key: \"16sdep\"\n}], [\"path\", {\n  d: \"m9 15 3 3-3 3\",\n  key: \"1m3kbl\"\n}]];\nconst GitCompareArrows = createLucideIcon(\"git-compare-arrows\", __iconNode);\nexport { __iconNode, GitCompareArrows as default };\n//# sourceMappingURL=git-compare-arrows.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}