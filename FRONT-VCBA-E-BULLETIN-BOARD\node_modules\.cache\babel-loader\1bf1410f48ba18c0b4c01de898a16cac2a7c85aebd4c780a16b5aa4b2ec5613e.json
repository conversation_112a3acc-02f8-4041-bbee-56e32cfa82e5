{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 11H4\",\n  key: \"6ut86h\"\n}], [\"path\", {\n  d: \"M20 7H4\",\n  key: \"zbl0bi\"\n}], [\"path\", {\n  d: \"M7 21V4a1 1 0 0 1 1-1h4a1 1 0 0 1 0 12H7\",\n  key: \"1ana5r\"\n}]];\nconst PhilippinePeso = createLucideIcon(\"philippine-peso\", __iconNode);\nexport { __iconNode, PhilippinePeso as default };\n//# sourceMappingURL=philippine-peso.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}