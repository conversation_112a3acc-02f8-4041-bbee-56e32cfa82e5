{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.75 12h3.632a1 1 0 0 1 .894 1.447l-2.034 4.069a1 1 0 0 1-1.708.134l-2.124-2.97\",\n  key: \"ir91b5\"\n}], [\"path\", {\n  d: \"M17.106 9.053a1 1 0 0 1 .447 1.341l-3.106 6.211a1 1 0 0 1-1.342.447L3.61 12.3a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3z\",\n  key: \"jlp8i1\"\n}], [\"path\", {\n  d: \"M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15\",\n  key: \"19bib8\"\n}], [\"path\", {\n  d: \"M2 21v-4\",\n  key: \"l40lih\"\n}], [\"path\", {\n  d: \"M7 9h.01\",\n  key: \"19b3jx\"\n}]];\nconst Cctv = createLucideIcon(\"cctv\", __iconNode);\nexport { __iconNode, Cctv as default };\n//# sourceMappingURL=cctv.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}