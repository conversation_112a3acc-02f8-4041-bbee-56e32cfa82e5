{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.97 12.92A2 2 0 0 0 2 14.63v3.24a2 2 0 0 0 .97 1.71l3 1.8a2 2 0 0 0 2.06 0L12 19v-5.5l-5-3-4.03 2.42Z\",\n  key: \"lc1i9w\"\n}], [\"path\", {\n  d: \"m7 16.5-4.74-2.85\",\n  key: \"1o9zyk\"\n}], [\"path\", {\n  d: \"m7 16.5 5-3\",\n  key: \"va8pkn\"\n}], [\"path\", {\n  d: \"M7 16.5v5.17\",\n  key: \"jnp8gn\"\n}], [\"path\", {\n  d: \"M12 13.5V19l3.97 2.38a2 2 0 0 0 2.06 0l3-1.8a2 2 0 0 0 .97-1.71v-3.24a2 2 0 0 0-.97-1.71L17 10.5l-5 3Z\",\n  key: \"8zsnat\"\n}], [\"path\", {\n  d: \"m17 16.5-5-3\",\n  key: \"8arw3v\"\n}], [\"path\", {\n  d: \"m17 16.5 4.74-2.85\",\n  key: \"8rfmw\"\n}], [\"path\", {\n  d: \"M17 16.5v5.17\",\n  key: \"k6z78m\"\n}], [\"path\", {\n  d: \"M7.97 4.42A2 2 0 0 0 7 6.13v4.37l5 3 5-3V6.13a2 2 0 0 0-.97-1.71l-3-1.8a2 2 0 0 0-2.06 0l-3 1.8Z\",\n  key: \"1xygjf\"\n}], [\"path\", {\n  d: \"M12 8 7.26 5.15\",\n  key: \"1vbdud\"\n}], [\"path\", {\n  d: \"m12 8 4.74-2.85\",\n  key: \"3rx089\"\n}], [\"path\", {\n  d: \"M12 13.5V8\",\n  key: \"1io7kd\"\n}]];\nconst Boxes = createLucideIcon(\"boxes\", __iconNode);\nexport { __iconNode, Boxes as default };\n//# sourceMappingURL=boxes.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}