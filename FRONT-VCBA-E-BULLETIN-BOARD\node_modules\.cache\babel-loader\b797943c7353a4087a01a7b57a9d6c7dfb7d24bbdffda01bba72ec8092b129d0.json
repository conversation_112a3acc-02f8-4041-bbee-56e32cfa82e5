{"ast": null, "code": "import React,{useState}from'react';import{Outlet}from'react-router-dom';import AdminSidebar from'./AdminSidebar';import AdminHeader from'./AdminHeader';import'../admin.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLayout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(true);const toggleSidebar=()=>{setSidebarOpen(!sidebarOpen);};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',minHeight:'100vh',background:'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'},children:[/*#__PURE__*/_jsx(AdminSidebar,{isOpen:sidebarOpen,onToggle:toggleSidebar}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',marginLeft:sidebarOpen?'280px':'80px',transition:'margin-left 0.3s ease',minHeight:'100vh'},children:[/*#__PURE__*/_jsx(AdminHeader,{onToggleSidebar:toggleSidebar}),/*#__PURE__*/_jsx(\"main\",{style:{flex:1,padding:'2rem',background:'transparent',overflow:'auto'},children:children||/*#__PURE__*/_jsx(Outlet,{})})]})]});};export default AdminLayout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}