{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"20\",\n  y2: \"4\",\n  key: \"cun8e5\"\n}], [\"polygon\", {\n  points: \"14,20 4,12 14,4\",\n  key: \"ypakod\"\n}]];\nconst StepBack = createLucideIcon(\"step-back\", __iconNode);\nexport { __iconNode, StepBack as default };\n//# sourceMappingURL=step-back.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}