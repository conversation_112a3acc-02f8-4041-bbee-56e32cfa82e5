{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\",\n  key: \"1pljnt\"\n}], [\"path\", {\n  d: \"M16 14v2\",\n  key: \"a1is7l\"\n}], [\"path\", {\n  d: \"M8 14v2\",\n  key: \"1e9m6t\"\n}], [\"path\", {\n  d: \"M16 20h.01\",\n  key: \"xwek51\"\n}], [\"path\", {\n  d: \"M8 20h.01\",\n  key: \"1vjney\"\n}], [\"path\", {\n  d: \"M12 16v2\",\n  key: \"z66u1j\"\n}], [\"path\", {\n  d: \"M12 22h.01\",\n  key: \"1urd7a\"\n}]];\nconst CloudHail = createLucideIcon(\"cloud-hail\", __iconNode);\nexport { __iconNode, CloudHail as default };\n//# sourceMappingURL=cloud-hail.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}