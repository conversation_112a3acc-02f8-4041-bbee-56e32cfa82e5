{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"16lz6z\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M3 13.1a2 2 0 0 0-1 1.76v3.24a2 2 0 0 0 .97 1.78L6 21.7a2 2 0 0 0 2.03.01L11 19.9a2 2 0 0 0 1-1.76V14.9a2 2 0 0 0-.97-1.78L8 11.3a2 2 0 0 0-2.03-.01Z\",\n  key: \"99pj1s\"\n}], [\"path\", {\n  d: \"M7 17v5\",\n  key: \"1yj1jh\"\n}], [\"path\", {\n  d: \"M11.7 14.2 7 17l-4.7-2.8\",\n  key: \"1yk8tc\"\n}]];\nconst FileBox = createLucideIcon(\"file-box\", __iconNode);\nexport { __iconNode, FileBox as default };\n//# sourceMappingURL=file-box.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}