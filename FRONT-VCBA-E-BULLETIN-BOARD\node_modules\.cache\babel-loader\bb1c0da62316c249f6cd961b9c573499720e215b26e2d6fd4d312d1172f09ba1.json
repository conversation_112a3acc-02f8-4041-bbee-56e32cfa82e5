{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 14 4-4\",\n  key: \"9kzdfg\"\n}], [\"path\", {\n  d: \"M3.34 19a10 10 0 1 1 17.32 0\",\n  key: \"19p75a\"\n}]];\nconst Gauge = createLucideIcon(\"gauge\", __iconNode);\nexport { __iconNode, Gauge as default };\n//# sourceMappingURL=gauge.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}