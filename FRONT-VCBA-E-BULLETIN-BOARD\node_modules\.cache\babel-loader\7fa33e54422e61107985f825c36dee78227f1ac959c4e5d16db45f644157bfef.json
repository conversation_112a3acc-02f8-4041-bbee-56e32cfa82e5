{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\PostManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useToast } from '../../contexts/ToastContext';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport AnnouncementModal from '../../components/admin/modals/AnnouncementModal';\nimport AnnouncementViewDialog from '../../components/admin/modals/AnnouncementViewDialog';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport { CheckCircle, Clock, Calendar, X, MessageSquare, Search, RefreshCw, Pin, AlertTriangle, User, Eye, Heart, MessageCircle, Edit, Send, Trash2 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PostManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\n  const [selectedCategoryId, setSelectedCategoryId] = useState(undefined);\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingAnnouncement, setEditingAnnouncement] = useState(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n\n  // Toast notifications\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n\n  // View dialog state\n  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);\n  const [viewingAnnouncement, setViewingAnnouncement] = useState(null);\n\n  // Initialize filters with grade level filtering for grade-specific admins\n  const [filters, setFilters] = useState(() => {\n    const baseFilters = {\n      page: 1,\n      limit: 10,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    };\n\n    // Add grade level filter for grade-specific admins\n    if (user !== null && user !== void 0 && user.grade_level) {\n      baseFilters.grade_level = user.grade_level;\n      console.log('🎯 PostManagement - Initial grade level filter set:', {\n        userGradeLevel: user.grade_level,\n        userEmail: user.email,\n        userRole: user.role\n      });\n    } else {\n      console.log('🌐 PostManagement - No grade level filter (system admin):', {\n        userGradeLevel: user === null || user === void 0 ? void 0 : user.grade_level,\n        userEmail: user === null || user === void 0 ? void 0 : user.email,\n        userRole: user === null || user === void 0 ? void 0 : user.role\n      });\n    }\n    return baseFilters;\n  });\n  const {\n    announcements,\n    loading,\n    error,\n    pagination,\n    refresh,\n    createAnnouncement,\n    updateAnnouncement,\n    deleteAnnouncement,\n    publishAnnouncement,\n    unpublishAnnouncement,\n    setFilters: updateFilters\n  } = useAnnouncements(filters, true); // Use admin service\n\n  const {\n    categories\n  } = useCategories();\n\n  // Handle category selection\n  const handleCategoryChange = categoryId => {\n    setSelectedCategoryId(categoryId || undefined);\n  };\n\n  // Clear all filters\n  const clearFilters = () => {\n    setSearchTerm('');\n    setDebouncedSearchTerm('');\n    setSelectedCategoryId(undefined);\n    setSelectedStatus('');\n  };\n\n  // Get current filter summary\n  const getFilterSummary = () => {\n    const filters = [];\n    if (debouncedSearchTerm) {\n      filters.push(`Search: \"${debouncedSearchTerm}\"`);\n    }\n    if (selectedCategoryId) {\n      const category = categories.find(cat => cat.category_id === selectedCategoryId);\n      if (category) {\n        filters.push(`Category: ${category.name}`);\n      }\n    }\n    if (selectedStatus) {\n      filters.push(`Status: ${selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}`);\n    }\n    return filters;\n  };\n\n  // Debounced search - update debounced term after delay\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n    }, 500);\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  // Update filters when debounced search term or other filter values change\n  useEffect(() => {\n    const newFilters = {\n      ...filters,\n      page: 1,\n      // Reset to first page when filtering\n      search: debouncedSearchTerm || undefined,\n      category_id: selectedCategoryId,\n      status: selectedStatus || undefined\n    };\n\n    // Ensure grade level filter is preserved for grade-specific admins\n    if (user !== null && user !== void 0 && user.grade_level) {\n      newFilters.grade_level = user.grade_level;\n    }\n    console.log('🔍 PostManagement - Updating filters:', {\n      userGradeLevel: user === null || user === void 0 ? void 0 : user.grade_level,\n      appliedGradeLevel: newFilters.grade_level,\n      allFilters: newFilters\n    });\n    updateFilters(newFilters);\n  }, [debouncedSearchTerm, selectedCategoryId, selectedStatus, user === null || user === void 0 ? void 0 : user.grade_level]);\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage]);\n  const handleCreateAnnouncement = () => {\n    console.log('🆕 Creating new announcement - clearing all data');\n\n    // Ensure editing announcement is completely cleared\n    setEditingAnnouncement(null);\n\n    // Clear any existing success/error messages\n    setSuccessMessage('');\n    setErrorMessage('');\n\n    // Small delay to ensure state is cleared before opening modal\n    setTimeout(() => {\n      setShowModal(true);\n      console.log('✅ Modal opened for new announcement creation');\n    }, 10);\n  };\n  const handleEditAnnouncement = announcement => {\n    console.log('✏️ Editing announcement:', {\n      id: announcement.announcement_id,\n      title: announcement.title,\n      is_pinned: announcement.is_pinned,\n      is_alert: announcement.is_alert,\n      allow_comments: announcement.allow_comments,\n      allow_sharing: announcement.allow_sharing\n    });\n    setEditingAnnouncement(announcement);\n    setShowModal(true);\n  };\n  const handleDeleteAnnouncement = async announcementId => {\n    const announcement = announcements.find(a => a.announcement_id === announcementId);\n    const confirmMessage = `Are you sure you want to delete \"${announcement === null || announcement === void 0 ? void 0 : announcement.title}\"? This action can be undone from the admin panel.`;\n    if (window.confirm(confirmMessage)) {\n      try {\n        await deleteAnnouncement(announcementId);\n        showSuccess('Announcement Deleted', `\"${announcement === null || announcement === void 0 ? void 0 : announcement.title}\" has been moved to trash successfully.`, 3000);\n      } catch (error) {\n        const errorMsg = error.message || 'Failed to delete announcement';\n        setErrorMessage(errorMsg);\n        showError('Delete Failed', errorMsg, 4000);\n      }\n    }\n  };\n  const handlePublishAnnouncement = async announcementId => {\n    try {\n      await publishAnnouncement(announcementId);\n      const announcement = announcements.find(a => a.announcement_id === announcementId);\n      showSuccess('Announcement Published', `\"${announcement === null || announcement === void 0 ? void 0 : announcement.title}\" is now live and visible to all users.`, 3000);\n    } catch (error) {\n      const errorMsg = error.message || 'Failed to publish announcement';\n      setErrorMessage(errorMsg);\n      showError('Publish Failed', errorMsg, 4000);\n    }\n  };\n  const handleUnpublishAnnouncement = async announcementId => {\n    try {\n      await unpublishAnnouncement(announcementId);\n      const announcement = announcements.find(a => a.announcement_id === announcementId);\n      showSuccess('Announcement Unpublished', `\"${announcement === null || announcement === void 0 ? void 0 : announcement.title}\" has been moved back to draft status.`, 3000);\n    } catch (error) {\n      const errorMsg = error.message || 'Failed to unpublish announcement';\n      setErrorMessage(errorMsg);\n      showError('Unpublish Failed', errorMsg, 4000);\n    }\n  };\n  const handleViewAnnouncement = announcementId => {\n    const announcement = announcements.find(a => a.announcement_id === announcementId);\n    if (announcement) {\n      setViewingAnnouncement(announcement);\n      setIsViewDialogOpen(true);\n    }\n  };\n  const handleCloseViewDialog = () => {\n    setIsViewDialogOpen(false);\n    setViewingAnnouncement(null);\n  };\n  const handleSaveAnnouncement = async (data, applyPendingDeletes, onComplete) => {\n    try {\n      const isEditing = !!editingAnnouncement;\n      if (isEditing) {\n        await updateAnnouncement(editingAnnouncement.announcement_id, data);\n\n        // Apply pending image deletions AFTER successful update\n        if (applyPendingDeletes) {\n          console.log('🗑️ Applying pending image deletions after successful update');\n          await applyPendingDeletes();\n        }\n      } else {\n        // For new announcements, automatically set grade level for grade-specific admins\n        let announcementData = data;\n        if (user !== null && user !== void 0 && user.grade_level && !(data instanceof FormData)) {\n          // If admin has a specific grade level and data is not FormData, set grade_level\n          announcementData = {\n            ...data,\n            grade_level: user.grade_level\n          };\n        } else if (user !== null && user !== void 0 && user.grade_level && data instanceof FormData) {\n          // If data is FormData, append grade_level\n          data.append('grade_level', user.grade_level.toString());\n        }\n        await createAnnouncement(announcementData);\n      }\n\n      // Execute completion callback (image uploads, etc.) BEFORE refreshing\n      if (onComplete) {\n        await onComplete();\n      }\n\n      // Explicitly refresh the list to ensure new/updated announcements appear immediately\n      console.log('🔄 Refreshing announcements list after save...');\n      await refresh();\n      console.log('✅ Announcements list refreshed');\n\n      // Clear any existing error messages\n      setErrorMessage('');\n\n      // Close modal first\n      setShowModal(false);\n      setEditingAnnouncement(null);\n\n      // Then show success message after a brief delay to ensure modal is closed\n      setTimeout(() => {\n        if (isEditing) {\n          alert('✅ Announcement updated successfully! Changes are now visible.');\n        } else {\n          alert('✅ Announcement created successfully!');\n        }\n      }, 100);\n    } catch (error) {\n      const errorMsg = error.message || 'Failed to save announcement';\n      setErrorMessage(errorMsg);\n      showError('Save Failed', errorMsg, 5000);\n      throw error; // Re-throw to let modal handle it\n    } finally {\n      // Always clean up modal state\n      setShowModal(false);\n      setEditingAnnouncement(null);\n    }\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingAnnouncement(null);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get category color\n  const getCategoryColor = announcement => {\n    // Find the category color from the categories list\n    if (announcement.category_id && categories && categories.length > 0) {\n      const category = categories.find(cat => cat.category_id === announcement.category_id);\n      if (category && category.color_code) {\n        return category.color_code;\n      }\n    }\n    // Fallback to announcement's category_color if available\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    // Default fallback color\n    return '#22c55e';\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      published: {\n        color: 'bg-green-100 text-green-800',\n        icon: CheckCircle\n      },\n      draft: {\n        color: 'bg-yellow-100 text-yellow-800',\n        icon: Clock\n      },\n      scheduled: {\n        color: 'bg-blue-100 text-blue-800',\n        icon: Calendar\n      },\n      archived: {\n        color: 'bg-gray-100 text-gray-800',\n        icon: X\n      }\n    };\n    const config = statusConfig[status] || statusConfig.draft;\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`,\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        size: 12,\n        className: \"mr-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), status.charAt(0).toUpperCase() + status.slice(1)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this);\n  };\n  const handlePageChange = page => {\n    updateFilters({\n      ...filters,\n      page\n    });\n  };\n  const handleItemsPerPageChange = limit => {\n    updateFilters({\n      ...filters,\n      limit,\n      page: 1\n    }); // Reset to first page when changing items per page\n  };\n\n  // Enhanced pagination component - Always visible\n  const EnhancedPagination = () => {\n    // Always show pagination if we have pagination data, even for single page\n    if (!pagination) return null;\n    const getPageNumbers = () => {\n      const pages = [];\n      const maxVisiblePages = 5;\n      const currentPage = pagination.page;\n      const totalPages = Math.max(pagination.totalPages, 1); // Ensure at least 1 page\n\n      if (totalPages <= maxVisiblePages) {\n        for (let i = 1; i <= totalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        if (currentPage <= 3) {\n          pages.push(1, 2, 3, 4, '...', totalPages);\n        } else if (currentPage >= totalPages - 2) {\n          pages.push(1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);\n        } else {\n          pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);\n        }\n      }\n      return pages;\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '2rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        border: '1px solid #e5e7eb',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',\n        flexWrap: 'wrap',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"Show:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.limit,\n          onChange: e => handleItemsPerPageChange(Number(e.target.value)),\n          style: {\n            padding: '0.25rem 0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            fontSize: '0.875rem',\n            backgroundColor: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10,\n            children: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 20,\n            children: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 50,\n            children: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 100,\n            children: \"100\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"per page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [\"Showing \", (pagination.page - 1) * filters.limit + 1, \"-\", Math.min(pagination.page * filters.limit, pagination.total), \" of \", pagination.total, \" posts\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.25rem',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(1),\n          disabled: pagination.page === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: pagination.page === 1 ? '#f3f4f6' : 'white',\n            color: pagination.page === 1 ? '#9ca3af' : '#374151',\n            cursor: pagination.page === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"First\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(pagination.page - 1),\n          disabled: !pagination.hasPrev,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: !pagination.hasPrev ? '#f3f4f6' : 'white',\n            color: !pagination.hasPrev ? '#9ca3af' : '#374151',\n            cursor: !pagination.hasPrev ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), getPageNumbers().map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => typeof page === 'number' && handlePageChange(page),\n          disabled: page === '...',\n          style: {\n            padding: '0.5rem 0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: page === pagination.page ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\n            color: page === pagination.page ? 'white' : page === '...' ? '#9ca3af' : '#374151',\n            cursor: page === '...' ? 'default' : 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: page === pagination.page ? '600' : '400'\n          },\n          children: page\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(pagination.page + 1),\n          disabled: !pagination.hasNext,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: !pagination.hasNext ? '#f3f4f6' : 'white',\n            color: !pagination.hasNext ? '#9ca3af' : '#374151',\n            cursor: !pagination.hasNext ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handlePageChange(pagination.totalPages),\n          disabled: pagination.page === pagination.totalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: pagination.page === pagination.totalPages ? '#f3f4f6' : 'white',\n            color: pagination.page === pagination.totalPages ? '#9ca3af' : '#374151',\n            cursor: pagination.page === pagination.totalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Last\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading && (!announcements || announcements.length === 0)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        color: '#166534',\n        borderRadius: '8px'\n      },\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '8px'\n      },\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: '0 0 0.5rem 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n              size: 20,\n              color: \"#1e40af\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), \"Post Management\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            margin: 0,\n            fontSize: '1.1rem'\n          },\n          children: [\"Create, edit, and manage announcement posts with advanced filtering\", (user === null || user === void 0 ? void 0 : user.grade_level) && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'inline-block',\n              marginLeft: '0.5rem',\n              padding: '0.25rem 0.5rem',\n              backgroundColor: '#dbeafe',\n              color: '#1e40af',\n              borderRadius: '0.375rem',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: [\"Grade \", user.grade_level, \" Only\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreateAnnouncement,\n        style: {\n          display: 'inline-flex',\n          alignItems: 'center',\n          padding: '0.75rem 1.5rem',\n          background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          fontWeight: '600',\n          fontSize: '0.95rem',\n          transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n        },\n        onMouseOver: e => {\n          e.currentTarget.style.transform = 'translateY(-1px)';\n          e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n        },\n        onMouseOut: e => {\n          e.currentTarget.style.transform = 'translateY(0)';\n          e.currentTarget.style.boxShadow = 'none';\n        },\n        children: \"+ Create Announcement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '1.5rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              size: 16,\n              color: \"#9ca3af\",\n              style: {\n                position: 'absolute',\n                left: '0.75rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search announcements...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              },\n              onFocus: e => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              },\n              onBlur: e => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Category Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategoryId || '',\n            onChange: e => handleCategoryChange(e.target.value ? parseInt(e.target.value) : null),\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white',\n              transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n            },\n            onFocus: e => {\n              e.currentTarget.style.borderColor = '#22c55e';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n            },\n            onBlur: e => {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 15\n            }, this), categories && categories.length > 0 ? categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.category_id,\n              children: category.name\n            }, category.category_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 17\n            }, this)) : /*#__PURE__*/_jsxDEV(\"option\", {\n              disabled: true,\n              children: \"Loading categories...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedStatus,\n            onChange: e => setSelectedStatus(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white',\n              transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n            },\n            onFocus: e => {\n              e.currentTarget.style.borderColor = '#22c55e';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n            },\n            onBlur: e => {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"published\",\n              children: \"Published\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"draft\",\n              children: \"Draft\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'end',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refresh,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this), \"Refresh\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearFilters,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              backgroundColor: '#fef3c7',\n              color: '#92400e',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#fde68a',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#fef3c7',\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this), \"Clear\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this), getFilterSummary().length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f0f9ff',\n        border: '1px solid #bae6fd',\n        borderRadius: '12px',\n        padding: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          marginBottom: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          size: 16,\n          style: {\n            color: '#0369a1'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#0369a1'\n          },\n          children: \"Active Filters:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5rem'\n        },\n        children: getFilterSummary().map((filter, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: '#dbeafe',\n            color: '#1e40af',\n            padding: '0.25rem 0.75rem',\n            borderRadius: '20px',\n            fontSize: '0.75rem',\n            fontWeight: '500'\n          },\n          children: filter\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 858,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          padding: '3rem 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '2rem',\n            height: '2rem',\n            border: '2px solid #e5e7eb',\n            borderTop: '2px solid #22c55e',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 912,\n        columnNumber: 11\n      }, this) : announcements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            color: '#9ca3af',\n            marginBottom: '1rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n            size: 20,\n            color: \"#1e40af\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0.5rem 0',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#111827'\n          },\n          children: \"No announcements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: '0.25rem 0 1.5rem 0',\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"Get started by creating a new announcement.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreateAnnouncement,\n          style: {\n            display: 'inline-flex',\n            alignItems: 'center',\n            padding: '0.75rem 1.5rem',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontWeight: '600',\n            fontSize: '0.95rem',\n            transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n          },\n          onMouseOver: e => {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n          },\n          onMouseOut: e => {\n            e.currentTarget.style.transform = 'translateY(0)';\n            e.currentTarget.style.boxShadow = 'none';\n          },\n          children: \"+ Create Announcement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: announcements && announcements.length > 0 ? announcements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            background: '#ffffff',\n            border: '1px solid #e5e7eb',\n            borderRadius: '12px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',\n            transition: 'all 0.2s ease'\n          },\n          onMouseOver: e => {\n            e.currentTarget.style.backgroundColor = '#f9fafb';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';\n            e.currentTarget.style.transform = 'translateY(-1px)';\n          },\n          onMouseOut: e => {\n            e.currentTarget.style.backgroundColor = '#ffffff';\n            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.04)';\n            e.currentTarget.style.transform = 'translateY(0)';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'start',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                minWidth: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827',\n                    margin: 0,\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: announcement.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 23\n                }, this), announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: getCategoryColor(announcement),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600'\n                  },\n                  children: announcement.category_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 25\n                }, this), announcement.is_pinned && /*#__PURE__*/_jsxDEV(Pin, {\n                  size: 16,\n                  color: \"#eab308\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 25\n                }, this), announcement.is_alert && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 25\n                }, this), getStatusBadge(announcement.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '0.875rem',\n                  marginBottom: '0.75rem',\n                  lineHeight: '1.5',\n                  display: '-webkit-box',\n                  WebkitLineClamp: 2,\n                  WebkitBoxOrient: 'vertical',\n                  overflow: 'hidden'\n                },\n                children: announcement.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 21\n              }, this), (announcement.attachments && announcement.attachments.length > 0 || announcement.images && announcement.images.length > 0 || announcement.image_url || announcement.image_path) && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.75rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                  images:\n                  // Use attachments/images array if available, otherwise fallback to single image\n                  announcement.attachments && announcement.attachments.length > 0 ? announcement.attachments.map(att => att.file_path) : announcement.images && announcement.images.length > 0 ? announcement.images.map(img => img.file_path) : [announcement.image_url || announcement.image_path].filter(Boolean),\n                  altPrefix: announcement.title,\n                  maxVisible: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  flexWrap: 'wrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(User, {\n                    size: 14,\n                    style: {\n                      marginRight: '0.25rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this), announcement.author_name || 'Unknown']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 14,\n                    style: {\n                      marginRight: '0.25rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 25\n                  }, this), formatDate(announcement.created_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Heart, {\n                    size: 14,\n                    style: {\n                      marginRight: '0.25rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 25\n                  }, this), announcement.reaction_count || 0, \" reactions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                    size: 14,\n                    style: {\n                      marginRight: '0.25rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 25\n                  }, this), announcement.comment_count || 0, \" comments\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleViewAnnouncement(announcement.announcement_id),\n                style: {\n                  padding: '0.5rem',\n                  color: '#ffffff',\n                  background: '#6b7280',\n                  border: 'none',\n                  cursor: 'pointer',\n                  borderRadius: '6px',\n                  transition: 'all 0.2s ease'\n                },\n                title: \"View\",\n                onMouseOver: e => {\n                  e.currentTarget.style.background = '#4b5563';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                },\n                onMouseOut: e => {\n                  e.currentTarget.style.background = '#6b7280';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditAnnouncement(announcement),\n                style: {\n                  padding: '0.5rem',\n                  color: '#ffffff',\n                  background: '#3b82f6',\n                  border: 'none',\n                  cursor: 'pointer',\n                  borderRadius: '6px',\n                  transition: 'all 0.2s ease'\n                },\n                title: \"Edit\",\n                onMouseOver: e => {\n                  e.currentTarget.style.background = '#2563eb';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                },\n                onMouseOut: e => {\n                  e.currentTarget.style.background = '#3b82f6';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 21\n              }, this), announcement.status === 'draft' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handlePublishAnnouncement(announcement.announcement_id),\n                style: {\n                  padding: '0.5rem',\n                  color: '#ffffff',\n                  background: '#22c55e',\n                  border: 'none',\n                  cursor: 'pointer',\n                  borderRadius: '6px',\n                  transition: 'all 0.2s ease'\n                },\n                title: \"Publish\",\n                onMouseOver: e => {\n                  e.currentTarget.style.background = '#16a34a';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                },\n                onMouseOut: e => {\n                  e.currentTarget.style.background = '#22c55e';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Send, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1184,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 23\n              }, this), announcement.status === 'published' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleUnpublishAnnouncement(announcement.announcement_id),\n                style: {\n                  padding: '0.5rem',\n                  color: '#ffffff',\n                  background: '#eab308',\n                  border: 'none',\n                  cursor: 'pointer',\n                  borderRadius: '6px',\n                  transition: 'all 0.2s ease'\n                },\n                title: \"Unpublish\",\n                onMouseOver: e => {\n                  e.currentTarget.style.background = '#ca8a04';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                },\n                onMouseOut: e => {\n                  e.currentTarget.style.background = '#eab308';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1209,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteAnnouncement(announcement.announcement_id),\n                style: {\n                  padding: '0.5rem',\n                  color: '#ffffff',\n                  background: '#ef4444',\n                  border: 'none',\n                  cursor: 'pointer',\n                  borderRadius: '6px',\n                  transition: 'all 0.2s ease'\n                },\n                title: \"Delete\",\n                onMouseOver: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                },\n                onMouseOut: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                },\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1233,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 17\n          }, this)\n        }, announcement.announcement_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '2rem',\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: loading ? 'Loading announcements...' : 'No announcements found.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1239,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 982,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EnhancedPagination, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnnouncementModal, {\n      isOpen: showModal,\n      onClose: handleCloseModal,\n      onSave: handleSaveAnnouncement,\n      announcement: editingAnnouncement,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnnouncementViewDialog, {\n      isOpen: isViewDialogOpen,\n      onClose: handleCloseViewDialog,\n      announcement: viewingAnnouncement\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1264,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 571,\n    columnNumber: 5\n  }, this);\n};\n_s(PostManagement, \"qzyRowR1qBi1FiBXEYmTwrjFiwM=\", false, function () {\n  return [useAdminAuth, useToast, useAnnouncements, useCategories];\n});\n_c = PostManagement;\nexport default PostManagement;\nvar _c;\n$RefreshReg$(_c, \"PostManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAnnouncements", "useCategories", "useToast", "useAdminAuth", "AnnouncementModal", "AnnouncementViewDialog", "FacebookImageGallery", "CheckCircle", "Clock", "Calendar", "X", "MessageSquare", "Search", "RefreshCw", "<PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User", "Eye", "Heart", "MessageCircle", "Edit", "Send", "Trash2", "jsxDEV", "_jsxDEV", "PostManagement", "_s", "user", "searchTerm", "setSearchTerm", "debouncedSearchTerm", "setDebouncedSearchTerm", "selectedCategoryId", "setSelectedCategoryId", "undefined", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingAnnouncement", "setEditingAnnouncement", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "showSuccess", "showError", "isViewDialogOpen", "setIsViewDialogOpen", "viewingAnnouncement", "setViewingAnnouncement", "filters", "setFilters", "baseFilters", "page", "limit", "sort_by", "sort_order", "grade_level", "console", "log", "userGradeLevel", "userEmail", "email", "userRole", "role", "announcements", "loading", "error", "pagination", "refresh", "createAnnouncement", "updateAnnouncement", "deleteAnnouncement", "publishAnnouncement", "unpublishAnnouncement", "updateFilters", "categories", "handleCategoryChange", "categoryId", "clearFilters", "getFilter<PERSON><PERSON>mary", "push", "category", "find", "cat", "category_id", "name", "char<PERSON>t", "toUpperCase", "slice", "timeoutId", "setTimeout", "clearTimeout", "newFilters", "search", "status", "appliedGradeLevel", "allFilters", "timer", "handleCreateAnnouncement", "handleEditAnnouncement", "announcement", "id", "announcement_id", "title", "is_pinned", "is_alert", "allow_comments", "allow_sharing", "handleDeleteAnnouncement", "announcementId", "a", "confirmMessage", "window", "confirm", "errorMsg", "message", "handlePublishAnnouncement", "handleUnpublishAnnouncement", "handleViewAnnouncement", "handleCloseViewDialog", "handleSaveAnnouncement", "data", "applyPendingDeletes", "onComplete", "isEditing", "announcementData", "FormData", "append", "toString", "alert", "handleCloseModal", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getCategoryColor", "length", "color_code", "category_color", "getStatusBadge", "statusConfig", "published", "color", "icon", "draft", "scheduled", "archived", "config", "IconComponent", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handlePageChange", "handleItemsPerPageChange", "EnhancedPagination", "getPageNumbers", "pages", "maxVisiblePages", "currentPage", "totalPages", "Math", "max", "i", "style", "display", "justifyContent", "alignItems", "marginTop", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "boxShadow", "flexWrap", "gap", "fontSize", "value", "onChange", "e", "Number", "target", "min", "total", "onClick", "disabled", "cursor", "has<PERSON>rev", "map", "index", "fontWeight", "hasNext", "max<PERSON><PERSON><PERSON>", "margin", "marginLeft", "background", "transition", "onMouseOver", "currentTarget", "transform", "onMouseOut", "gridTemplateColumns", "position", "left", "top", "pointerEvents", "type", "placeholder", "width", "outline", "onFocus", "borderColor", "onBlur", "parseInt", "flex", "filter", "height", "borderTop", "animation", "textAlign", "flexDirection", "min<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "category_name", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "content", "attachments", "images", "image_url", "image_path", "att", "file_path", "img", "Boolean", "altPrefix", "maxVisible", "marginRight", "author_name", "created_at", "reaction_count", "comment_count", "isOpen", "onClose", "onSave", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/PostManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { useToast } from '../../contexts/ToastContext';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport AnnouncementModal from '../../components/admin/modals/AnnouncementModal';\nimport AnnouncementViewDialog from '../../components/admin/modals/AnnouncementViewDialog';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\n\nimport type {\n  Announcement,\n  CreateAnnouncementData,\n  UpdateAnnouncementData,\n  AnnouncementFilters\n} from '../../types/announcement.types';\nimport {\n  CheckCircle,\n  Clock,\n  Calendar,\n  X,\n  MessageSquare,\n  Search,\n  RefreshCw,\n  Pin,\n  AlertTriangle,\n  User,\n  Eye,\n  Heart,\n  MessageCircle,\n  Edit,\n  Send,\n  Trash2\n} from 'lucide-react';\n\nconst PostManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\n  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined);\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n\n  // Toast notifications\n  const { showSuccess, showError } = useToast();\n\n  // View dialog state\n  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);\n  const [viewingAnnouncement, setViewingAnnouncement] = useState<Announcement | null>(null);\n\n  // Initialize filters with grade level filtering for grade-specific admins\n  const [filters, setFilters] = useState<AnnouncementFilters>(() => {\n    const baseFilters: AnnouncementFilters = {\n      page: 1,\n      limit: 10,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    };\n\n    // Add grade level filter for grade-specific admins\n    if (user?.grade_level) {\n      baseFilters.grade_level = user.grade_level;\n      console.log('🎯 PostManagement - Initial grade level filter set:', {\n        userGradeLevel: user.grade_level,\n        userEmail: user.email,\n        userRole: user.role\n      });\n    } else {\n      console.log('🌐 PostManagement - No grade level filter (system admin):', {\n        userGradeLevel: user?.grade_level,\n        userEmail: user?.email,\n        userRole: user?.role\n      });\n    }\n\n    return baseFilters;\n  });\n\n  const {\n    announcements,\n    loading,\n    error,\n    pagination,\n    refresh,\n    createAnnouncement,\n    updateAnnouncement,\n    deleteAnnouncement,\n    publishAnnouncement,\n    unpublishAnnouncement,\n    setFilters: updateFilters\n  } = useAnnouncements(filters, true); // Use admin service\n\n  const { categories } = useCategories();\n\n  // Handle category selection\n  const handleCategoryChange = (categoryId: number | null) => {\n    setSelectedCategoryId(categoryId || undefined);\n  };\n\n  // Clear all filters\n  const clearFilters = () => {\n    setSearchTerm('');\n    setDebouncedSearchTerm('');\n    setSelectedCategoryId(undefined);\n    setSelectedStatus('');\n  };\n\n  // Get current filter summary\n  const getFilterSummary = () => {\n    const filters = [];\n\n    if (debouncedSearchTerm) {\n      filters.push(`Search: \"${debouncedSearchTerm}\"`);\n    }\n\n    if (selectedCategoryId) {\n      const category = categories.find(cat => cat.category_id === selectedCategoryId);\n      if (category) {\n        filters.push(`Category: ${category.name}`);\n      }\n    }\n\n    if (selectedStatus) {\n      filters.push(`Status: ${selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}`);\n    }\n\n    return filters;\n  };\n\n  // Debounced search - update debounced term after delay\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  // Update filters when debounced search term or other filter values change\n  useEffect(() => {\n    const newFilters: AnnouncementFilters = {\n      ...filters,\n      page: 1, // Reset to first page when filtering\n      search: debouncedSearchTerm || undefined,\n      category_id: selectedCategoryId,\n      status: selectedStatus || undefined\n    };\n\n    // Ensure grade level filter is preserved for grade-specific admins\n    if (user?.grade_level) {\n      newFilters.grade_level = user.grade_level;\n    }\n\n    console.log('🔍 PostManagement - Updating filters:', {\n      userGradeLevel: user?.grade_level,\n      appliedGradeLevel: newFilters.grade_level,\n      allFilters: newFilters\n    });\n\n    updateFilters(newFilters);\n  }, [debouncedSearchTerm, selectedCategoryId, selectedStatus, user?.grade_level]);\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage]);\n\n  const handleCreateAnnouncement = () => {\n    console.log('🆕 Creating new announcement - clearing all data');\n\n    // Ensure editing announcement is completely cleared\n    setEditingAnnouncement(null);\n\n    // Clear any existing success/error messages\n    setSuccessMessage('');\n    setErrorMessage('');\n\n    // Small delay to ensure state is cleared before opening modal\n    setTimeout(() => {\n      setShowModal(true);\n      console.log('✅ Modal opened for new announcement creation');\n    }, 10);\n  };\n\n  const handleEditAnnouncement = (announcement: Announcement) => {\n    console.log('✏️ Editing announcement:', {\n      id: announcement.announcement_id,\n      title: announcement.title,\n      is_pinned: announcement.is_pinned,\n      is_alert: announcement.is_alert,\n      allow_comments: announcement.allow_comments,\n      allow_sharing: announcement.allow_sharing\n    });\n    setEditingAnnouncement(announcement);\n    setShowModal(true);\n  };\n\n  const handleDeleteAnnouncement = async (announcementId: number) => {\n    const announcement = announcements.find(a => a.announcement_id === announcementId);\n    const confirmMessage = `Are you sure you want to delete \"${announcement?.title}\"? This action can be undone from the admin panel.`;\n\n    if (window.confirm(confirmMessage)) {\n      try {\n        await deleteAnnouncement(announcementId);\n        showSuccess(\n          'Announcement Deleted',\n          `\"${announcement?.title}\" has been moved to trash successfully.`,\n          3000\n        );\n      } catch (error: any) {\n        const errorMsg = error.message || 'Failed to delete announcement';\n        setErrorMessage(errorMsg);\n        showError('Delete Failed', errorMsg, 4000);\n      }\n    }\n  };\n\n  const handlePublishAnnouncement = async (announcementId: number) => {\n    try {\n      await publishAnnouncement(announcementId);\n      const announcement = announcements.find(a => a.announcement_id === announcementId);\n      showSuccess(\n        'Announcement Published',\n        `\"${announcement?.title}\" is now live and visible to all users.`,\n        3000\n      );\n    } catch (error: any) {\n      const errorMsg = error.message || 'Failed to publish announcement';\n      setErrorMessage(errorMsg);\n      showError('Publish Failed', errorMsg, 4000);\n    }\n  };\n\n  const handleUnpublishAnnouncement = async (announcementId: number) => {\n    try {\n      await unpublishAnnouncement(announcementId);\n      const announcement = announcements.find(a => a.announcement_id === announcementId);\n      showSuccess(\n        'Announcement Unpublished',\n        `\"${announcement?.title}\" has been moved back to draft status.`,\n        3000\n      );\n    } catch (error: any) {\n      const errorMsg = error.message || 'Failed to unpublish announcement';\n      setErrorMessage(errorMsg);\n      showError('Unpublish Failed', errorMsg, 4000);\n    }\n  };\n\n  const handleViewAnnouncement = (announcementId: number) => {\n    const announcement = announcements.find(a => a.announcement_id === announcementId);\n    if (announcement) {\n      setViewingAnnouncement(announcement);\n      setIsViewDialogOpen(true);\n    }\n  };\n\n  const handleCloseViewDialog = () => {\n    setIsViewDialogOpen(false);\n    setViewingAnnouncement(null);\n  };\n\n  const handleSaveAnnouncement = async (data: CreateAnnouncementData | UpdateAnnouncementData | FormData, applyPendingDeletes?: () => Promise<void>, onComplete?: () => Promise<void>) => {\n    try {\n      const isEditing = !!editingAnnouncement;\n\n      if (isEditing) {\n        await updateAnnouncement(editingAnnouncement.announcement_id, data as UpdateAnnouncementData | FormData);\n\n        // Apply pending image deletions AFTER successful update\n        if (applyPendingDeletes) {\n          console.log('🗑️ Applying pending image deletions after successful update');\n          await applyPendingDeletes();\n        }\n      } else {\n        // For new announcements, automatically set grade level for grade-specific admins\n        let announcementData = data as CreateAnnouncementData | FormData;\n\n        if (user?.grade_level && !(data instanceof FormData)) {\n          // If admin has a specific grade level and data is not FormData, set grade_level\n          announcementData = {\n            ...data as CreateAnnouncementData,\n            grade_level: user.grade_level\n          };\n        } else if (user?.grade_level && data instanceof FormData) {\n          // If data is FormData, append grade_level\n          data.append('grade_level', user.grade_level.toString());\n        }\n\n        await createAnnouncement(announcementData);\n      }\n\n      // Execute completion callback (image uploads, etc.) BEFORE refreshing\n      if (onComplete) {\n        await onComplete();\n      }\n\n      // Explicitly refresh the list to ensure new/updated announcements appear immediately\n      console.log('🔄 Refreshing announcements list after save...');\n      await refresh();\n      console.log('✅ Announcements list refreshed');\n\n      // Clear any existing error messages\n      setErrorMessage('');\n\n      // Close modal first\n      setShowModal(false);\n      setEditingAnnouncement(null);\n\n      // Then show success message after a brief delay to ensure modal is closed\n      setTimeout(() => {\n        if (isEditing) {\n          alert('✅ Announcement updated successfully! Changes are now visible.');\n        } else {\n          alert('✅ Announcement created successfully!');\n        }\n      }, 100);\n\n    } catch (error: any) {\n      const errorMsg = error.message || 'Failed to save announcement';\n      setErrorMessage(errorMsg);\n      showError(\n        'Save Failed',\n        errorMsg,\n        5000\n      );\n      throw error; // Re-throw to let modal handle it\n    } finally {\n      // Always clean up modal state\n      setShowModal(false);\n      setEditingAnnouncement(null);\n    }\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingAnnouncement(null);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get category color\n  const getCategoryColor = (announcement: Announcement) => {\n    // Find the category color from the categories list\n    if (announcement.category_id && categories && categories.length > 0) {\n      const category = categories.find(cat => cat.category_id === announcement.category_id);\n      if (category && category.color_code) {\n        return category.color_code;\n      }\n    }\n    // Fallback to announcement's category_color if available\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    // Default fallback color\n    return '#22c55e';\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      published: { color: 'bg-green-100 text-green-800', icon: CheckCircle },\n      draft: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },\n      scheduled: { color: 'bg-blue-100 text-blue-800', icon: Calendar },\n      archived: { color: 'bg-gray-100 text-gray-800', icon: X }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n\n    const IconComponent = config.icon;\n\n    return (\n      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>\n        <IconComponent size={12} className=\"mr-1\" />\n        {status.charAt(0).toUpperCase() + status.slice(1)}\n      </span>\n    );\n  };\n\n  const handlePageChange = (page: number) => {\n    updateFilters({ ...filters, page });\n  };\n\n  const handleItemsPerPageChange = (limit: number) => {\n    updateFilters({ ...filters, limit, page: 1 }); // Reset to first page when changing items per page\n  };\n\n  // Enhanced pagination component - Always visible\n  const EnhancedPagination = () => {\n    // Always show pagination if we have pagination data, even for single page\n    if (!pagination) return null;\n\n    const getPageNumbers = () => {\n      const pages = [];\n      const maxVisiblePages = 5;\n      const currentPage = pagination.page;\n      const totalPages = Math.max(pagination.totalPages, 1); // Ensure at least 1 page\n\n      if (totalPages <= maxVisiblePages) {\n        for (let i = 1; i <= totalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        if (currentPage <= 3) {\n          pages.push(1, 2, 3, 4, '...', totalPages);\n        } else if (currentPage >= totalPages - 2) {\n          pages.push(1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);\n        } else {\n          pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);\n        }\n      }\n\n      return pages;\n    };\n\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '2rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        border: '1px solid #e5e7eb',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',\n        flexWrap: 'wrap',\n        gap: '1rem'\n      }}>\n        {/* Items per page selector */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Show:</span>\n          <select\n            value={filters.limit}\n            onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}\n            style={{\n              padding: '0.25rem 0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '4px',\n              fontSize: '0.875rem',\n              backgroundColor: 'white'\n            }}\n          >\n            <option value={10}>10</option>\n            <option value={20}>20</option>\n            <option value={50}>50</option>\n            <option value={100}>100</option>\n          </select>\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>per page</span>\n        </div>\n\n        {/* Page info */}\n        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n          Showing {((pagination.page - 1) * filters.limit!) + 1}-{Math.min(pagination.page * filters.limit!, pagination.total)} of {pagination.total} posts\n        </div>\n\n        {/* Page navigation */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>\n          <button\n            onClick={() => handlePageChange(1)}\n            disabled={pagination.page === 1}\n            style={{\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '4px',\n              backgroundColor: pagination.page === 1 ? '#f3f4f6' : 'white',\n              color: pagination.page === 1 ? '#9ca3af' : '#374151',\n              cursor: pagination.page === 1 ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            }}\n          >\n            First\n          </button>\n\n          <button\n            onClick={() => handlePageChange(pagination.page - 1)}\n            disabled={!pagination.hasPrev}\n            style={{\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '4px',\n              backgroundColor: !pagination.hasPrev ? '#f3f4f6' : 'white',\n              color: !pagination.hasPrev ? '#9ca3af' : '#374151',\n              cursor: !pagination.hasPrev ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            }}\n          >\n            Previous\n          </button>\n\n          {getPageNumbers().map((page, index) => (\n            <button\n              key={index}\n              onClick={() => typeof page === 'number' && handlePageChange(page)}\n              disabled={page === '...'}\n              style={{\n                padding: '0.5rem 0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '4px',\n                backgroundColor: page === pagination.page ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\n                color: page === pagination.page ? 'white' : page === '...' ? '#9ca3af' : '#374151',\n                cursor: page === '...' ? 'default' : 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: page === pagination.page ? '600' : '400'\n              }}\n            >\n              {page}\n            </button>\n          ))}\n\n          <button\n            onClick={() => handlePageChange(pagination.page + 1)}\n            disabled={!pagination.hasNext}\n            style={{\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '4px',\n              backgroundColor: !pagination.hasNext ? '#f3f4f6' : 'white',\n              color: !pagination.hasNext ? '#9ca3af' : '#374151',\n              cursor: !pagination.hasNext ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            }}\n          >\n            Next\n          </button>\n\n          <button\n            onClick={() => handlePageChange(pagination.totalPages)}\n            disabled={pagination.page === pagination.totalPages}\n            style={{\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '4px',\n              backgroundColor: pagination.page === pagination.totalPages ? '#f3f4f6' : 'white',\n              color: pagination.page === pagination.totalPages ? '#9ca3af' : '#374151',\n              cursor: pagination.page === pagination.totalPages ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            }}\n          >\n            Last\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading && (!announcements || announcements.length === 0)) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Success/Error Messages */}\n      {successMessage && (\n        <div style={{\n          marginBottom: '1rem',\n          padding: '1rem',\n          backgroundColor: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          color: '#166534',\n          borderRadius: '8px'\n        }}>\n          {successMessage}\n        </div>\n      )}\n      {errorMessage && (\n        <div style={{\n          marginBottom: '1rem',\n          padding: '1rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          color: '#dc2626',\n          borderRadius: '8px'\n        }}>\n          {errorMessage}\n        </div>\n      )}\n\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '1.5rem'\n      }}>\n        <div>\n          <h1 style={{\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: '0 0 0.5rem 0'\n          }}>\n            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <MessageSquare size={20} color=\"#1e40af\" />\n              Post Management\n            </span>\n          </h1>\n          <p style={{\n            color: '#6b7280',\n            margin: 0,\n            fontSize: '1.1rem'\n          }}>\n            Create, edit, and manage announcement posts with advanced filtering\n            {user?.grade_level && (\n              <span style={{\n                display: 'inline-block',\n                marginLeft: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                backgroundColor: '#dbeafe',\n                color: '#1e40af',\n                borderRadius: '0.375rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}>\n                Grade {user.grade_level} Only\n              </span>\n            )}\n          </p>\n        </div>\n        <button\n          onClick={handleCreateAnnouncement}\n          style={{\n            display: 'inline-flex',\n            alignItems: 'center',\n            padding: '0.75rem 1.5rem',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontWeight: '600',\n            fontSize: '0.95rem',\n            transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n          }}\n          onMouseOver={(e) => {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n          }}\n          onMouseOut={(e) => {\n            e.currentTarget.style.transform = 'translateY(0)';\n            e.currentTarget.style.boxShadow = 'none';\n          }}\n        >\n          + Create Announcement\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '1.5rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        }}>\n          <div>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Search\n            </label>\n            <div style={{ position: 'relative' }}>\n              <Search\n                size={16}\n                color=\"#9ca3af\"\n                style={{\n                  position: 'absolute',\n                  left: '0.75rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  pointerEvents: 'none'\n                }}\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Search announcements...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n                }}\n                onFocus={(e) => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                }}\n                onBlur={(e) => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              />\n            </div>\n          </div>\n          <div>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Category Filter\n            </label>\n            <select\n              value={selectedCategoryId || ''}\n              onChange={(e) => handleCategoryChange(e.target.value ? parseInt(e.target.value) : null)}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                backgroundColor: 'white',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              }}\n              onFocus={(e) => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              }}\n              onBlur={(e) => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }}\n            >\n              <option value=\"\">All Categories</option>\n              {categories && categories.length > 0 ? categories.map((category) => (\n                <option key={category.category_id} value={category.category_id}>\n                  {category.name}\n                </option>\n              )) : (\n                <option disabled>Loading categories...</option>\n              )}\n            </select>\n          </div>\n          <div>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Status\n            </label>\n            <select\n              value={selectedStatus}\n              onChange={(e) => setSelectedStatus(e.target.value)}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                backgroundColor: 'white',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              }}\n              onFocus={(e) => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              }}\n              onBlur={(e) => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }}\n            >\n              <option value=\"\">All Status</option>\n              <option value=\"published\">Published</option>\n              <option value=\"draft\">Draft</option>\n            </select>\n          </div>\n          <div style={{ display: 'flex', alignItems: 'end', gap: '0.5rem' }}>\n            <button\n              onClick={refresh}\n              style={{\n                flex: 1,\n                padding: '0.75rem',\n                backgroundColor: '#f3f4f6',\n                color: '#374151',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontWeight: '500',\n                fontSize: '0.875rem',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\n            >\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>\n                <RefreshCw size={16} />\n                Refresh\n              </span>\n            </button>\n            <button\n              onClick={clearFilters}\n              style={{\n                flex: 1,\n                padding: '0.75rem',\n                backgroundColor: '#fef3c7',\n                color: '#92400e',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontWeight: '500',\n                fontSize: '0.875rem',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#fde68a'}\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#fef3c7'}\n            >\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>\n                <X size={16} />\n                Clear\n              </span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Active Filters Summary */}\n      {getFilterSummary().length > 0 && (\n        <div style={{\n          background: '#f0f9ff',\n          border: '1px solid #bae6fd',\n          borderRadius: '12px',\n          padding: '1rem',\n          marginBottom: '1rem'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem'\n          }}>\n            <Search size={16} style={{ color: '#0369a1' }} />\n            <span style={{\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#0369a1'\n            }}>\n              Active Filters:\n            </span>\n          </div>\n          <div style={{\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '0.5rem'\n          }}>\n            {getFilterSummary().map((filter, index) => (\n              <span\n                key={index}\n                style={{\n                  background: '#dbeafe',\n                  color: '#1e40af',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '20px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500'\n                }}\n              >\n                {filter}\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Announcements List */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        {loading ? (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '3rem 0'\n          }}>\n            <div style={{\n              width: '2rem',\n              height: '2rem',\n              border: '2px solid #e5e7eb',\n              borderTop: '2px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }}></div>\n          </div>\n        ) : announcements.length === 0 ? (\n          <div style={{\n            textAlign: 'center',\n            padding: '3rem'\n          }}>\n            <div style={{\n              fontSize: '3rem',\n              color: '#9ca3af',\n              marginBottom: '1rem'\n            }}>\n              <MessageSquare size={20} color=\"#1e40af\" />\n            </div>\n            <h3 style={{\n              margin: '0.5rem 0',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#111827'\n            }}>\n              No announcements\n            </h3>\n            <p style={{\n              margin: '0.25rem 0 1.5rem 0',\n              fontSize: '0.875rem',\n              color: '#6b7280'\n            }}>\n              Get started by creating a new announcement.\n            </p>\n            <button\n              onClick={handleCreateAnnouncement}\n              style={{\n                display: 'inline-flex',\n                alignItems: 'center',\n                padding: '0.75rem 1.5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontWeight: '600',\n                fontSize: '0.95rem',\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n              }}\n              onMouseOver={(e) => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              }}\n              onMouseOut={(e) => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              }}\n            >\n              + Create Announcement\n            </button>\n          </div>\n        ) : (\n          <div style={{ padding: '1rem', display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n            {announcements && announcements.length > 0 ? announcements.map((announcement) => (\n              <div\n                key={announcement.announcement_id}\n                style={{\n                  padding: '1.5rem',\n                  background: '#ffffff',\n                  border: '1px solid #e5e7eb',\n                  borderRadius: '12px',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseOver={(e) => {\n                  e.currentTarget.style.backgroundColor = '#f9fafb';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                }}\n                onMouseOut={(e) => {\n                  e.currentTarget.style.backgroundColor = '#ffffff';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.04)';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                }}\n              >\n                <div style={{ display: 'flex', alignItems: 'start', justifyContent: 'space-between' }}>\n                  <div style={{ flex: 1, minWidth: 0 }}>\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      marginBottom: '0.5rem'\n                    }}>\n                      <h3 style={{\n                        fontSize: '1.125rem',\n                        fontWeight: '600',\n                        color: '#111827',\n                        margin: 0,\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        whiteSpace: 'nowrap'\n                      }}>\n                        {announcement.title}\n                      </h3>\n                      {announcement.category_name && (\n                        <span style={{\n                          background: getCategoryColor(announcement),\n                          color: 'white',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600'\n                        }}>\n                          {announcement.category_name}\n                        </span>\n                      )}\n                      {announcement.is_pinned && (\n                        <Pin size={16} color=\"#eab308\" />\n                      )}\n                      {announcement.is_alert && (\n                        <AlertTriangle size={16} color=\"#ef4444\" />\n                      )}\n                      {getStatusBadge(announcement.status)}\n                    </div>\n\n                    <p style={{\n                      color: '#6b7280',\n                      fontSize: '0.875rem',\n                      marginBottom: '0.75rem',\n                      lineHeight: '1.5',\n                      display: '-webkit-box',\n                      WebkitLineClamp: 2,\n                      WebkitBoxOrient: 'vertical',\n                      overflow: 'hidden'\n                    }}>\n                      {announcement.content}\n                    </p>\n\n                    {/* Display images if available */}\n                    {((announcement.attachments && announcement.attachments.length > 0) ||\n                      (announcement.images && announcement.images.length > 0) ||\n                      announcement.image_url || announcement.image_path) && (\n                      <div style={{ marginBottom: '0.75rem' }}>\n                        <FacebookImageGallery\n                          images={\n                            // Use attachments/images array if available, otherwise fallback to single image\n                            (announcement.attachments && announcement.attachments.length > 0)\n                              ? announcement.attachments.map(att => att.file_path)\n                              : (announcement.images && announcement.images.length > 0)\n                                ? announcement.images.map(img => img.file_path)\n                                : [announcement.image_url || announcement.image_path].filter(Boolean) as string[]\n                          }\n                          altPrefix={announcement.title}\n                          maxVisible={3}\n                        />\n                      </div>\n                    )}\n\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.875rem',\n                      color: '#6b7280',\n                      flexWrap: 'wrap'\n                    }}>\n                      <span style={{ display: 'flex', alignItems: 'center' }}>\n                        <User size={14} style={{ marginRight: '0.25rem' }} />\n                        {announcement.author_name || 'Unknown'}\n                      </span>\n                      <span style={{ display: 'flex', alignItems: 'center' }}>\n                        <Calendar size={14} style={{ marginRight: '0.25rem' }} />\n                        {formatDate(announcement.created_at)}\n                      </span>\n                      {/* I will comment 'Views' for the mean time and I will uncomment this in the future */}\n                      {/* <span style={{ display: 'flex', alignItems: 'center' }}>\n                        <Eye size={14} style={{ marginRight: '0.25rem' }} />\n                        {announcement.view_count || 0} views\n                      </span> */}\n                      <span style={{ display: 'flex', alignItems: 'center' }}>\n                        <Heart size={14} style={{ marginRight: '0.25rem' }} />\n                        {announcement.reaction_count || 0} reactions\n                      </span>\n                      <span style={{ display: 'flex', alignItems: 'center' }}>\n                        <MessageCircle size={14} style={{ marginRight: '0.25rem' }} />\n                        {announcement.comment_count || 0} comments\n                      </span>\n                    </div>\n                  </div>\n\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginLeft: '1rem'\n                  }}>\n                    <button\n                      onClick={() => handleViewAnnouncement(announcement.announcement_id)}\n                      style={{\n                        padding: '0.5rem',\n                        color: '#ffffff',\n                        background: '#6b7280',\n                        border: 'none',\n                        cursor: 'pointer',\n                        borderRadius: '6px',\n                        transition: 'all 0.2s ease'\n                      }}\n                      title=\"View\"\n                      onMouseOver={(e) => {\n                        e.currentTarget.style.background = '#4b5563';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseOut={(e) => {\n                        e.currentTarget.style.background = '#6b7280';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}\n                    >\n                      <Eye size={16} />\n                    </button>\n                    <button\n                      onClick={() => handleEditAnnouncement(announcement)}\n                      style={{\n                        padding: '0.5rem',\n                        color: '#ffffff',\n                        background: '#3b82f6',\n                        border: 'none',\n                        cursor: 'pointer',\n                        borderRadius: '6px',\n                        transition: 'all 0.2s ease'\n                      }}\n                      title=\"Edit\"\n                      onMouseOver={(e) => {\n                        e.currentTarget.style.background = '#2563eb';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseOut={(e) => {\n                        e.currentTarget.style.background = '#3b82f6';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}\n                    >\n                      <Edit size={16} />\n                    </button>\n                    {announcement.status === 'draft' && (\n                      <button\n                        onClick={() => handlePublishAnnouncement(announcement.announcement_id)}\n                        style={{\n                          padding: '0.5rem',\n                          color: '#ffffff',\n                          background: '#22c55e',\n                          border: 'none',\n                          cursor: 'pointer',\n                          borderRadius: '6px',\n                          transition: 'all 0.2s ease'\n                        }}\n                        title=\"Publish\"\n                        onMouseOver={(e) => {\n                          e.currentTarget.style.background = '#16a34a';\n                          e.currentTarget.style.transform = 'translateY(-1px)';\n                        }}\n                        onMouseOut={(e) => {\n                          e.currentTarget.style.background = '#22c55e';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <Send size={16} />\n                      </button>\n                    )}\n                    {announcement.status === 'published' && (\n                      <button\n                        onClick={() => handleUnpublishAnnouncement(announcement.announcement_id)}\n                        style={{\n                          padding: '0.5rem',\n                          color: '#ffffff',\n                          background: '#eab308',\n                          border: 'none',\n                          cursor: 'pointer',\n                          borderRadius: '6px',\n                          transition: 'all 0.2s ease'\n                        }}\n                        title=\"Unpublish\"\n                        onMouseOver={(e) => {\n                          e.currentTarget.style.background = '#ca8a04';\n                          e.currentTarget.style.transform = 'translateY(-1px)';\n                        }}\n                        onMouseOut={(e) => {\n                          e.currentTarget.style.background = '#eab308';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <Clock size={16} />\n                      </button>\n                    )}\n                    <button\n                      onClick={() => handleDeleteAnnouncement(announcement.announcement_id)}\n                      style={{\n                        padding: '0.5rem',\n                        color: '#ffffff',\n                        background: '#ef4444',\n                        border: 'none',\n                        cursor: 'pointer',\n                        borderRadius: '6px',\n                        transition: 'all 0.2s ease'\n                      }}\n                      title=\"Delete\"\n                      onMouseOver={(e) => {\n                        e.currentTarget.style.background = '#dc2626';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseOut={(e) => {\n                        e.currentTarget.style.background = '#ef4444';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}\n                    >\n                      <Trash2 size={16} />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )) : (\n              <div style={{\n                padding: '2rem',\n                textAlign: 'center',\n                color: '#6b7280'\n              }}>\n                {loading ? 'Loading announcements...' : 'No announcements found.'}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Pagination */}\n      <EnhancedPagination />\n\n      {/* Announcement Modal */}\n      <AnnouncementModal\n        isOpen={showModal}\n        onClose={handleCloseModal}\n        onSave={handleSaveAnnouncement}\n        announcement={editingAnnouncement}\n        loading={loading}\n      />\n\n      {/* View Dialog */}\n      <AnnouncementViewDialog\n        isOpen={isViewDialogOpen}\n        onClose={handleCloseViewDialog}\n        announcement={viewingAnnouncement}\n      />\n    </div>\n  );\n};\n\nexport default PostManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,8BAA8B;AAC9E,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,OAAOC,iBAAiB,MAAM,iDAAiD;AAC/E,OAAOC,sBAAsB,MAAM,sDAAsD;AACzF,OAAOC,oBAAoB,MAAM,8CAA8C;AAQ/E,SACEC,WAAW,EACXC,KAAK,EACLC,QAAQ,EACRC,CAAC,EACDC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,aAAa,EACbC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGxB,YAAY,CAAC,CAAC;EAC/B,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAqBoC,SAAS,CAAC;EAC3F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAsB,IAAI,CAAC;EACzF,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM;IAAE+C,WAAW;IAAEC;EAAU,CAAC,GAAG5C,QAAQ,CAAC,CAAC;;EAE7C;EACA,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAAsB,IAAI,CAAC;;EAEzF;EACA,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAsB,MAAM;IAChE,MAAMuD,WAAgC,GAAG;MACvCC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,IAAI9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,WAAW,EAAE;MACrBL,WAAW,CAACK,WAAW,GAAG/B,IAAI,CAAC+B,WAAW;MAC1CC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QACjEC,cAAc,EAAElC,IAAI,CAAC+B,WAAW;QAChCI,SAAS,EAAEnC,IAAI,CAACoC,KAAK;QACrBC,QAAQ,EAAErC,IAAI,CAACsC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLN,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAE;QACvEC,cAAc,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,WAAW;QACjCI,SAAS,EAAEnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK;QACtBC,QAAQ,EAAErC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC;MAClB,CAAC,CAAC;IACJ;IAEA,OAAOZ,WAAW;EACpB,CAAC,CAAC;EAEF,MAAM;IACJa,aAAa;IACbC,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC,OAAO;IACPC,kBAAkB;IAClBC,kBAAkB;IAClBC,kBAAkB;IAClBC,mBAAmB;IACnBC,qBAAqB;IACrBvB,UAAU,EAAEwB;EACd,CAAC,GAAG5E,gBAAgB,CAACmD,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;;EAErC,MAAM;IAAE0B;EAAW,CAAC,GAAG5E,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM6E,oBAAoB,GAAIC,UAAyB,IAAK;IAC1D9C,qBAAqB,CAAC8C,UAAU,IAAI7C,SAAS,CAAC;EAChD,CAAC;;EAED;EACA,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzBnD,aAAa,CAAC,EAAE,CAAC;IACjBE,sBAAsB,CAAC,EAAE,CAAC;IAC1BE,qBAAqB,CAACC,SAAS,CAAC;IAChCE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAM6C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAM9B,OAAO,GAAG,EAAE;IAElB,IAAIrB,mBAAmB,EAAE;MACvBqB,OAAO,CAAC+B,IAAI,CAAC,YAAYpD,mBAAmB,GAAG,CAAC;IAClD;IAEA,IAAIE,kBAAkB,EAAE;MACtB,MAAMmD,QAAQ,GAAGN,UAAU,CAACO,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,KAAKtD,kBAAkB,CAAC;MAC/E,IAAImD,QAAQ,EAAE;QACZhC,OAAO,CAAC+B,IAAI,CAAC,aAAaC,QAAQ,CAACI,IAAI,EAAE,CAAC;MAC5C;IACF;IAEA,IAAIpD,cAAc,EAAE;MAClBgB,OAAO,CAAC+B,IAAI,CAAC,WAAW/C,cAAc,CAACqD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtD,cAAc,CAACuD,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7F;IAEA,OAAOvC,OAAO;EAChB,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd,MAAM4F,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC7D,sBAAsB,CAACH,UAAU,CAAC;IACpC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMiE,YAAY,CAACF,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC/D,UAAU,CAAC,CAAC;;EAEhB;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM+F,UAA+B,GAAG;MACtC,GAAG3C,OAAO;MACVG,IAAI,EAAE,CAAC;MAAE;MACTyC,MAAM,EAAEjE,mBAAmB,IAAII,SAAS;MACxCoD,WAAW,EAAEtD,kBAAkB;MAC/BgE,MAAM,EAAE7D,cAAc,IAAID;IAC5B,CAAC;;IAED;IACA,IAAIP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,WAAW,EAAE;MACrBoC,UAAU,CAACpC,WAAW,GAAG/B,IAAI,CAAC+B,WAAW;IAC3C;IAEAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MACnDC,cAAc,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,WAAW;MACjCuC,iBAAiB,EAAEH,UAAU,CAACpC,WAAW;MACzCwC,UAAU,EAAEJ;IACd,CAAC,CAAC;IAEFlB,aAAa,CAACkB,UAAU,CAAC;EAC3B,CAAC,EAAE,CAAChE,mBAAmB,EAAEE,kBAAkB,EAAEG,cAAc,EAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,WAAW,CAAC,CAAC;;EAEhF;EACA3D,SAAS,CAAC,MAAM;IACd,IAAI0C,cAAc,IAAIE,YAAY,EAAE;MAClC,MAAMwD,KAAK,GAAGP,UAAU,CAAC,MAAM;QAC7BlD,iBAAiB,CAAC,EAAE,CAAC;QACrBE,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMiD,YAAY,CAACM,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC1D,cAAc,EAAEE,YAAY,CAAC,CAAC;EAElC,MAAMyD,wBAAwB,GAAGA,CAAA,KAAM;IACrCzC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;IAE/D;IACApB,sBAAsB,CAAC,IAAI,CAAC;;IAE5B;IACAE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;;IAEnB;IACAgD,UAAU,CAAC,MAAM;MACftD,YAAY,CAAC,IAAI,CAAC;MAClBqB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC7D,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EAED,MAAMyC,sBAAsB,GAAIC,YAA0B,IAAK;IAC7D3C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACtC2C,EAAE,EAAED,YAAY,CAACE,eAAe;MAChCC,KAAK,EAAEH,YAAY,CAACG,KAAK;MACzBC,SAAS,EAAEJ,YAAY,CAACI,SAAS;MACjCC,QAAQ,EAAEL,YAAY,CAACK,QAAQ;MAC/BC,cAAc,EAAEN,YAAY,CAACM,cAAc;MAC3CC,aAAa,EAAEP,YAAY,CAACO;IAC9B,CAAC,CAAC;IACFrE,sBAAsB,CAAC8D,YAAY,CAAC;IACpChE,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwE,wBAAwB,GAAG,MAAOC,cAAsB,IAAK;IACjE,MAAMT,YAAY,GAAGpC,aAAa,CAACkB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACR,eAAe,KAAKO,cAAc,CAAC;IAClF,MAAME,cAAc,GAAG,oCAAoCX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,KAAK,oDAAoD;IAElI,IAAIS,MAAM,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;MAClC,IAAI;QACF,MAAMxC,kBAAkB,CAACsC,cAAc,CAAC;QACxClE,WAAW,CACT,sBAAsB,EACtB,IAAIyD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,KAAK,yCAAyC,EAChE,IACF,CAAC;MACH,CAAC,CAAC,OAAOrC,KAAU,EAAE;QACnB,MAAMgD,QAAQ,GAAGhD,KAAK,CAACiD,OAAO,IAAI,+BAA+B;QACjEzE,eAAe,CAACwE,QAAQ,CAAC;QACzBtE,SAAS,CAAC,eAAe,EAAEsE,QAAQ,EAAE,IAAI,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAME,yBAAyB,GAAG,MAAOP,cAAsB,IAAK;IAClE,IAAI;MACF,MAAMrC,mBAAmB,CAACqC,cAAc,CAAC;MACzC,MAAMT,YAAY,GAAGpC,aAAa,CAACkB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACR,eAAe,KAAKO,cAAc,CAAC;MAClFlE,WAAW,CACT,wBAAwB,EACxB,IAAIyD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,KAAK,yCAAyC,EAChE,IACF,CAAC;IACH,CAAC,CAAC,OAAOrC,KAAU,EAAE;MACnB,MAAMgD,QAAQ,GAAGhD,KAAK,CAACiD,OAAO,IAAI,gCAAgC;MAClEzE,eAAe,CAACwE,QAAQ,CAAC;MACzBtE,SAAS,CAAC,gBAAgB,EAAEsE,QAAQ,EAAE,IAAI,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,2BAA2B,GAAG,MAAOR,cAAsB,IAAK;IACpE,IAAI;MACF,MAAMpC,qBAAqB,CAACoC,cAAc,CAAC;MAC3C,MAAMT,YAAY,GAAGpC,aAAa,CAACkB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACR,eAAe,KAAKO,cAAc,CAAC;MAClFlE,WAAW,CACT,0BAA0B,EAC1B,IAAIyD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,KAAK,wCAAwC,EAC/D,IACF,CAAC;IACH,CAAC,CAAC,OAAOrC,KAAU,EAAE;MACnB,MAAMgD,QAAQ,GAAGhD,KAAK,CAACiD,OAAO,IAAI,kCAAkC;MACpEzE,eAAe,CAACwE,QAAQ,CAAC;MACzBtE,SAAS,CAAC,kBAAkB,EAAEsE,QAAQ,EAAE,IAAI,CAAC;IAC/C;EACF,CAAC;EAED,MAAMI,sBAAsB,GAAIT,cAAsB,IAAK;IACzD,MAAMT,YAAY,GAAGpC,aAAa,CAACkB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACR,eAAe,KAAKO,cAAc,CAAC;IAClF,IAAIT,YAAY,EAAE;MAChBpD,sBAAsB,CAACoD,YAAY,CAAC;MACpCtD,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyE,qBAAqB,GAAGA,CAAA,KAAM;IAClCzE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMwE,sBAAsB,GAAG,MAAAA,CAAOC,IAAgE,EAAEC,mBAAyC,EAAEC,UAAgC,KAAK;IACtL,IAAI;MACF,MAAMC,SAAS,GAAG,CAAC,CAACvF,mBAAmB;MAEvC,IAAIuF,SAAS,EAAE;QACb,MAAMtD,kBAAkB,CAACjC,mBAAmB,CAACiE,eAAe,EAAEmB,IAAyC,CAAC;;QAExG;QACA,IAAIC,mBAAmB,EAAE;UACvBjE,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3E,MAAMgE,mBAAmB,CAAC,CAAC;QAC7B;MACF,CAAC,MAAM;QACL;QACA,IAAIG,gBAAgB,GAAGJ,IAAyC;QAEhE,IAAIhG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,WAAW,IAAI,EAAEiE,IAAI,YAAYK,QAAQ,CAAC,EAAE;UACpD;UACAD,gBAAgB,GAAG;YACjB,GAAGJ,IAA8B;YACjCjE,WAAW,EAAE/B,IAAI,CAAC+B;UACpB,CAAC;QACH,CAAC,MAAM,IAAI/B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,WAAW,IAAIiE,IAAI,YAAYK,QAAQ,EAAE;UACxD;UACAL,IAAI,CAACM,MAAM,CAAC,aAAa,EAAEtG,IAAI,CAAC+B,WAAW,CAACwE,QAAQ,CAAC,CAAC,CAAC;QACzD;QAEA,MAAM3D,kBAAkB,CAACwD,gBAAgB,CAAC;MAC5C;;MAEA;MACA,IAAIF,UAAU,EAAE;QACd,MAAMA,UAAU,CAAC,CAAC;MACpB;;MAEA;MACAlE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D,MAAMU,OAAO,CAAC,CAAC;MACfX,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACAhB,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACAN,YAAY,CAAC,KAAK,CAAC;MACnBE,sBAAsB,CAAC,IAAI,CAAC;;MAE5B;MACAoD,UAAU,CAAC,MAAM;QACf,IAAIkC,SAAS,EAAE;UACbK,KAAK,CAAC,+DAA+D,CAAC;QACxE,CAAC,MAAM;UACLA,KAAK,CAAC,sCAAsC,CAAC;QAC/C;MACF,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAO/D,KAAU,EAAE;MACnB,MAAMgD,QAAQ,GAAGhD,KAAK,CAACiD,OAAO,IAAI,6BAA6B;MAC/DzE,eAAe,CAACwE,QAAQ,CAAC;MACzBtE,SAAS,CACP,aAAa,EACbsE,QAAQ,EACR,IACF,CAAC;MACD,MAAMhD,KAAK,CAAC,CAAC;IACf,CAAC,SAAS;MACR;MACA9B,YAAY,CAAC,KAAK,CAAC;MACnBE,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAM4F,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9F,YAAY,CAAC,KAAK,CAAC;IACnBE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM6F,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIxC,YAA0B,IAAK;IACvD;IACA,IAAIA,YAAY,CAAChB,WAAW,IAAIT,UAAU,IAAIA,UAAU,CAACkE,MAAM,GAAG,CAAC,EAAE;MACnE,MAAM5D,QAAQ,GAAGN,UAAU,CAACO,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,KAAKgB,YAAY,CAAChB,WAAW,CAAC;MACrF,IAAIH,QAAQ,IAAIA,QAAQ,CAAC6D,UAAU,EAAE;QACnC,OAAO7D,QAAQ,CAAC6D,UAAU;MAC5B;IACF;IACA;IACA,IAAI1C,YAAY,CAAC2C,cAAc,EAAE;MAC/B,OAAO3C,YAAY,CAAC2C,cAAc;IACpC;IACA;IACA,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,cAAc,GAAIlD,MAAc,IAAK;IACzC,MAAMmD,YAAY,GAAG;MACnBC,SAAS,EAAE;QAAEC,KAAK,EAAE,6BAA6B;QAAEC,IAAI,EAAE/I;MAAY,CAAC;MACtEgJ,KAAK,EAAE;QAAEF,KAAK,EAAE,+BAA+B;QAAEC,IAAI,EAAE9I;MAAM,CAAC;MAC9DgJ,SAAS,EAAE;QAAEH,KAAK,EAAE,2BAA2B;QAAEC,IAAI,EAAE7I;MAAS,CAAC;MACjEgJ,QAAQ,EAAE;QAAEJ,KAAK,EAAE,2BAA2B;QAAEC,IAAI,EAAE5I;MAAE;IAC1D,CAAC;IAED,MAAMgJ,MAAM,GAAGP,YAAY,CAACnD,MAAM,CAA8B,IAAImD,YAAY,CAACI,KAAK;IAEtF,MAAMI,aAAa,GAAGD,MAAM,CAACJ,IAAI;IAEjC,oBACE9H,OAAA;MAAMoI,SAAS,EAAE,uEAAuEF,MAAM,CAACL,KAAK,EAAG;MAAAQ,QAAA,gBACrGrI,OAAA,CAACmI,aAAa;QAACG,IAAI,EAAE,EAAG;QAACF,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC3ClE,MAAM,CAACR,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGO,MAAM,CAACN,KAAK,CAAC,CAAC,CAAC;IAAA;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEX,CAAC;EAED,MAAMC,gBAAgB,GAAI7G,IAAY,IAAK;IACzCsB,aAAa,CAAC;MAAE,GAAGzB,OAAO;MAAEG;IAAK,CAAC,CAAC;EACrC,CAAC;EAED,MAAM8G,wBAAwB,GAAI7G,KAAa,IAAK;IAClDqB,aAAa,CAAC;MAAE,GAAGzB,OAAO;MAAEI,KAAK;MAAED,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAM+G,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,IAAI,CAAChG,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMiG,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,KAAK,GAAG,EAAE;MAChB,MAAMC,eAAe,GAAG,CAAC;MACzB,MAAMC,WAAW,GAAGpG,UAAU,CAACf,IAAI;MACnC,MAAMoH,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACvG,UAAU,CAACqG,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEvD,IAAIA,UAAU,IAAIF,eAAe,EAAE;QACjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,UAAU,EAAEG,CAAC,EAAE,EAAE;UACpCN,KAAK,CAACrF,IAAI,CAAC2F,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACL,IAAIJ,WAAW,IAAI,CAAC,EAAE;UACpBF,KAAK,CAACrF,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAEwF,UAAU,CAAC;QAC3C,CAAC,MAAM,IAAID,WAAW,IAAIC,UAAU,GAAG,CAAC,EAAE;UACxCH,KAAK,CAACrF,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEwF,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC;QAClF,CAAC,MAAM;UACLH,KAAK,CAACrF,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEuF,WAAW,GAAG,CAAC,EAAEA,WAAW,EAAEA,WAAW,GAAG,CAAC,EAAE,KAAK,EAAEC,UAAU,CAAC;QACxF;MACF;MAEA,OAAOH,KAAK;IACd,CAAC;IAED,oBACE/I,OAAA;MAAKsJ,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBC,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,+BAA+B;QAC1CC,QAAQ,EAAE,MAAM;QAChBC,GAAG,EAAE;MACP,CAAE;MAAA7B,QAAA,gBAEArI,OAAA;QAAKsJ,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAES,GAAG,EAAE;QAAS,CAAE;QAAA7B,QAAA,gBACnErI,OAAA;UAAMsJ,KAAK,EAAE;YAAEa,QAAQ,EAAE,UAAU;YAAEtC,KAAK,EAAE;UAAU,CAAE;UAAAQ,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrE1I,OAAA;UACEoK,KAAK,EAAEzI,OAAO,CAACI,KAAM;UACrBsI,QAAQ,EAAGC,CAAC,IAAK1B,wBAAwB,CAAC2B,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;UAClEd,KAAK,EAAE;YACLM,OAAO,EAAE,gBAAgB;YACzBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBK,QAAQ,EAAE,UAAU;YACpBN,eAAe,EAAE;UACnB,CAAE;UAAAxB,QAAA,gBAEFrI,OAAA;YAAQoK,KAAK,EAAE,EAAG;YAAA/B,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B1I,OAAA;YAAQoK,KAAK,EAAE,EAAG;YAAA/B,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B1I,OAAA;YAAQoK,KAAK,EAAE,EAAG;YAAA/B,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B1I,OAAA;YAAQoK,KAAK,EAAE,GAAI;YAAA/B,QAAA,EAAC;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACT1I,OAAA;UAAMsJ,KAAK,EAAE;YAAEa,QAAQ,EAAE,UAAU;YAAEtC,KAAK,EAAE;UAAU,CAAE;UAAAQ,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAGN1I,OAAA;QAAKsJ,KAAK,EAAE;UAAEa,QAAQ,EAAE,UAAU;UAAEtC,KAAK,EAAE;QAAU,CAAE;QAAAQ,QAAA,GAAC,UAC9C,EAAE,CAACxF,UAAU,CAACf,IAAI,GAAG,CAAC,IAAIH,OAAO,CAACI,KAAM,GAAI,CAAC,EAAC,GAAC,EAACoH,IAAI,CAACsB,GAAG,CAAC5H,UAAU,CAACf,IAAI,GAAGH,OAAO,CAACI,KAAM,EAAEc,UAAU,CAAC6H,KAAK,CAAC,EAAC,MAAI,EAAC7H,UAAU,CAAC6H,KAAK,EAAC,QAC7I;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGN1I,OAAA;QAAKsJ,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAES,GAAG,EAAE,SAAS;UAAED,QAAQ,EAAE;QAAO,CAAE;QAAA5B,QAAA,gBACtFrI,OAAA;UACE2K,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC,CAAC,CAAE;UACnCiC,QAAQ,EAAE/H,UAAU,CAACf,IAAI,KAAK,CAAE;UAChCwH,KAAK,EAAE;YACLM,OAAO,EAAE,QAAQ;YACjBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEhH,UAAU,CAACf,IAAI,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YAC5D+F,KAAK,EAAEhF,UAAU,CAACf,IAAI,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACpD+I,MAAM,EAAEhI,UAAU,CAACf,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YACzDqI,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1I,OAAA;UACE2K,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC9F,UAAU,CAACf,IAAI,GAAG,CAAC,CAAE;UACrD8I,QAAQ,EAAE,CAAC/H,UAAU,CAACiI,OAAQ;UAC9BxB,KAAK,EAAE;YACLM,OAAO,EAAE,QAAQ;YACjBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,CAAChH,UAAU,CAACiI,OAAO,GAAG,SAAS,GAAG,OAAO;YAC1DjD,KAAK,EAAE,CAAChF,UAAU,CAACiI,OAAO,GAAG,SAAS,GAAG,SAAS;YAClDD,MAAM,EAAE,CAAChI,UAAU,CAACiI,OAAO,GAAG,aAAa,GAAG,SAAS;YACvDX,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERI,cAAc,CAAC,CAAC,CAACiC,GAAG,CAAC,CAACjJ,IAAI,EAAEkJ,KAAK,kBAChChL,OAAA;UAEE2K,OAAO,EAAEA,CAAA,KAAM,OAAO7I,IAAI,KAAK,QAAQ,IAAI6G,gBAAgB,CAAC7G,IAAI,CAAE;UAClE8I,QAAQ,EAAE9I,IAAI,KAAK,KAAM;UACzBwH,KAAK,EAAE;YACLM,OAAO,EAAE,gBAAgB;YACzBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE/H,IAAI,KAAKe,UAAU,CAACf,IAAI,GAAG,SAAS,GAAGA,IAAI,KAAK,KAAK,GAAG,aAAa,GAAG,OAAO;YAChG+F,KAAK,EAAE/F,IAAI,KAAKe,UAAU,CAACf,IAAI,GAAG,OAAO,GAAGA,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YAClF+I,MAAM,EAAE/I,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YAC9CqI,QAAQ,EAAE,UAAU;YACpBc,UAAU,EAAEnJ,IAAI,KAAKe,UAAU,CAACf,IAAI,GAAG,KAAK,GAAG;UACjD,CAAE;UAAAuG,QAAA,EAEDvG;QAAI,GAdAkJ,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeJ,CACT,CAAC,eAEF1I,OAAA;UACE2K,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC9F,UAAU,CAACf,IAAI,GAAG,CAAC,CAAE;UACrD8I,QAAQ,EAAE,CAAC/H,UAAU,CAACqI,OAAQ;UAC9B5B,KAAK,EAAE;YACLM,OAAO,EAAE,QAAQ;YACjBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,CAAChH,UAAU,CAACqI,OAAO,GAAG,SAAS,GAAG,OAAO;YAC1DrD,KAAK,EAAE,CAAChF,UAAU,CAACqI,OAAO,GAAG,SAAS,GAAG,SAAS;YAClDL,MAAM,EAAE,CAAChI,UAAU,CAACqI,OAAO,GAAG,aAAa,GAAG,SAAS;YACvDf,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1I,OAAA;UACE2K,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC9F,UAAU,CAACqG,UAAU,CAAE;UACvD0B,QAAQ,EAAE/H,UAAU,CAACf,IAAI,KAAKe,UAAU,CAACqG,UAAW;UACpDI,KAAK,EAAE;YACLM,OAAO,EAAE,QAAQ;YACjBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEhH,UAAU,CAACf,IAAI,KAAKe,UAAU,CAACqG,UAAU,GAAG,SAAS,GAAG,OAAO;YAChFrB,KAAK,EAAEhF,UAAU,CAACf,IAAI,KAAKe,UAAU,CAACqG,UAAU,GAAG,SAAS,GAAG,SAAS;YACxE2B,MAAM,EAAEhI,UAAU,CAACf,IAAI,KAAKe,UAAU,CAACqG,UAAU,GAAG,aAAa,GAAG,SAAS;YAC7EiB,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAI/F,OAAO,KAAK,CAACD,aAAa,IAAIA,aAAa,CAAC6E,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7D,oBACEvH,OAAA;MAAKoI,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDrI,OAAA;QAAKoI,SAAS,EAAC;MAA+D;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC;EAEV;EAEA,oBACE1I,OAAA;IAAKsJ,KAAK,EAAE;MAAE6B,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAA/C,QAAA,GAElDpH,cAAc,iBACbjB,OAAA;MAAKsJ,KAAK,EAAE;QACVK,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BlC,KAAK,EAAE,SAAS;QAChBiC,YAAY,EAAE;MAChB,CAAE;MAAAzB,QAAA,EACCpH;IAAc;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,EACAvH,YAAY,iBACXnB,OAAA;MAAKsJ,KAAK,EAAE;QACVK,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BlC,KAAK,EAAE,SAAS;QAChBiC,YAAY,EAAE;MAChB,CAAE;MAAAzB,QAAA,EACClH;IAAY;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD1I,OAAA;MAAKsJ,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBE,YAAY,EAAE;MAChB,CAAE;MAAAtB,QAAA,gBACArI,OAAA;QAAAqI,QAAA,gBACErI,OAAA;UAAIsJ,KAAK,EAAE;YACTa,QAAQ,EAAE,MAAM;YAChBc,UAAU,EAAE,KAAK;YACjBpD,KAAK,EAAE,SAAS;YAChBuD,MAAM,EAAE;UACV,CAAE;UAAA/C,QAAA,eACArI,OAAA;YAAMsJ,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAES,GAAG,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACpErI,OAAA,CAACb,aAAa;cAACmJ,IAAI,EAAE,EAAG;cAACT,KAAK,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACL1I,OAAA;UAAGsJ,KAAK,EAAE;YACRzB,KAAK,EAAE,SAAS;YAChBuD,MAAM,EAAE,CAAC;YACTjB,QAAQ,EAAE;UACZ,CAAE;UAAA9B,QAAA,GAAC,qEAED,EAAC,CAAAlI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,WAAW,kBAChBlC,OAAA;YAAMsJ,KAAK,EAAE;cACXC,OAAO,EAAE,cAAc;cACvB8B,UAAU,EAAE,QAAQ;cACpBzB,OAAO,EAAE,gBAAgB;cACzBC,eAAe,EAAE,SAAS;cAC1BhC,KAAK,EAAE,SAAS;cAChBiC,YAAY,EAAE,UAAU;cACxBK,QAAQ,EAAE,UAAU;cACpBc,UAAU,EAAE;YACd,CAAE;YAAA5C,QAAA,GAAC,QACK,EAAClI,IAAI,CAAC+B,WAAW,EAAC,OAC1B;UAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1I,OAAA;QACE2K,OAAO,EAAE/F,wBAAyB;QAClC0E,KAAK,EAAE;UACLC,OAAO,EAAE,aAAa;UACtBE,UAAU,EAAE,QAAQ;UACpBG,OAAO,EAAE,gBAAgB;UACzB0B,UAAU,EAAE,mDAAmD;UAC/DzD,KAAK,EAAE,OAAO;UACdkC,MAAM,EAAE,MAAM;UACdD,YAAY,EAAE,KAAK;UACnBe,MAAM,EAAE,SAAS;UACjBI,UAAU,EAAE,KAAK;UACjBd,QAAQ,EAAE,SAAS;UACnBoB,UAAU,EAAE;QACd,CAAE;QACFC,WAAW,EAAGlB,CAAC,IAAK;UAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;UACpDpB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,mCAAmC;QACvE,CAAE;QACF2B,UAAU,EAAGrB,CAAC,IAAK;UACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;UACjDpB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,MAAM;QAC1C,CAAE;QAAA3B,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1I,OAAA;MAAKsJ,KAAK,EAAE;QACVgC,UAAU,EAAE,OAAO;QACnBxB,YAAY,EAAE,MAAM;QACpBF,OAAO,EAAE,QAAQ;QACjBD,YAAY,EAAE,QAAQ;QACtBK,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,eACArI,OAAA;QAAKsJ,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfqC,mBAAmB,EAAE,sCAAsC;UAC3D1B,GAAG,EAAE;QACP,CAAE;QAAA7B,QAAA,gBACArI,OAAA;UAAAqI,QAAA,gBACErI,OAAA;YAAOsJ,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBY,QAAQ,EAAE,UAAU;cACpBc,UAAU,EAAE,KAAK;cACjBpD,KAAK,EAAE,SAAS;cAChB8B,YAAY,EAAE;YAChB,CAAE;YAAAtB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1I,OAAA;YAAKsJ,KAAK,EAAE;cAAEuC,QAAQ,EAAE;YAAW,CAAE;YAAAxD,QAAA,gBACnCrI,OAAA,CAACZ,MAAM;cACLkJ,IAAI,EAAE,EAAG;cACTT,KAAK,EAAC,SAAS;cACfyB,KAAK,EAAE;gBACLuC,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACVL,SAAS,EAAE,kBAAkB;gBAC7BM,aAAa,EAAE;cACjB;YAAE;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1I,OAAA;cACEiM,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yBAAyB;cACrC9B,KAAK,EAAEhK,UAAW;cAClBiK,QAAQ,EAAGC,CAAC,IAAKjK,aAAa,CAACiK,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cAC/Cd,KAAK,EAAE;gBACL6C,KAAK,EAAE,MAAM;gBACbvC,OAAO,EAAE,gCAAgC;gBACzCG,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,KAAK;gBACnBK,QAAQ,EAAE,UAAU;gBACpBiC,OAAO,EAAE,MAAM;gBACfb,UAAU,EAAE;cACd,CAAE;cACFc,OAAO,EAAG/B,CAAC,IAAK;gBACdA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;gBAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,kCAAkC;cACtE,CAAE;cACFuC,MAAM,EAAGjC,CAAC,IAAK;gBACbA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;gBAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,MAAM;cAC1C;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1I,OAAA;UAAAqI,QAAA,gBACErI,OAAA;YAAOsJ,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBY,QAAQ,EAAE,UAAU;cACpBc,UAAU,EAAE,KAAK;cACjBpD,KAAK,EAAE,SAAS;cAChB8B,YAAY,EAAE;YAChB,CAAE;YAAAtB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1I,OAAA;YACEoK,KAAK,EAAE5J,kBAAkB,IAAI,EAAG;YAChC6J,QAAQ,EAAGC,CAAC,IAAKhH,oBAAoB,CAACgH,CAAC,CAACE,MAAM,CAACJ,KAAK,GAAGoC,QAAQ,CAAClC,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,GAAG,IAAI,CAAE;YACxFd,KAAK,EAAE;cACL6C,KAAK,EAAE,MAAM;cACbvC,OAAO,EAAE,SAAS;cAClBG,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,KAAK;cACnBK,QAAQ,EAAE,UAAU;cACpBiC,OAAO,EAAE,MAAM;cACfvC,eAAe,EAAE,OAAO;cACxB0B,UAAU,EAAE;YACd,CAAE;YACFc,OAAO,EAAG/B,CAAC,IAAK;cACdA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;cAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFuC,MAAM,EAAGjC,CAAC,IAAK;cACbA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;cAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAA3B,QAAA,gBAEFrI,OAAA;cAAQoK,KAAK,EAAC,EAAE;cAAA/B,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvCrF,UAAU,IAAIA,UAAU,CAACkE,MAAM,GAAG,CAAC,GAAGlE,UAAU,CAAC0H,GAAG,CAAEpH,QAAQ,iBAC7D3D,OAAA;cAAmCoK,KAAK,EAAEzG,QAAQ,CAACG,WAAY;cAAAuE,QAAA,EAC5D1E,QAAQ,CAACI;YAAI,GADHJ,QAAQ,CAACG,WAAW;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzB,CACT,CAAC,gBACA1I,OAAA;cAAQ4K,QAAQ;cAAAvC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAC/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAAqI,QAAA,gBACErI,OAAA;YAAOsJ,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBY,QAAQ,EAAE,UAAU;cACpBc,UAAU,EAAE,KAAK;cACjBpD,KAAK,EAAE,SAAS;cAChB8B,YAAY,EAAE;YAChB,CAAE;YAAAtB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1I,OAAA;YACEoK,KAAK,EAAEzJ,cAAe;YACtB0J,QAAQ,EAAGC,CAAC,IAAK1J,iBAAiB,CAAC0J,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YACnDd,KAAK,EAAE;cACL6C,KAAK,EAAE,MAAM;cACbvC,OAAO,EAAE,SAAS;cAClBG,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,KAAK;cACnBK,QAAQ,EAAE,UAAU;cACpBiC,OAAO,EAAE,MAAM;cACfvC,eAAe,EAAE,OAAO;cACxB0B,UAAU,EAAE;YACd,CAAE;YACFc,OAAO,EAAG/B,CAAC,IAAK;cACdA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;cAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFuC,MAAM,EAAGjC,CAAC,IAAK;cACbA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgD,WAAW,GAAG,SAAS;cAC7ChC,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAA3B,QAAA,gBAEFrI,OAAA;cAAQoK,KAAK,EAAC,EAAE;cAAA/B,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC1I,OAAA;cAAQoK,KAAK,EAAC,WAAW;cAAA/B,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C1I,OAAA;cAAQoK,KAAK,EAAC,OAAO;cAAA/B,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAKsJ,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,KAAK;YAAES,GAAG,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBAChErI,OAAA;YACE2K,OAAO,EAAE7H,OAAQ;YACjBwG,KAAK,EAAE;cACLmD,IAAI,EAAE,CAAC;cACP7C,OAAO,EAAE,SAAS;cAClBC,eAAe,EAAE,SAAS;cAC1BhC,KAAK,EAAE,SAAS;cAChBkC,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE,SAAS;cACjBI,UAAU,EAAE,KAAK;cACjBd,QAAQ,EAAE,UAAU;cACpBoB,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGlB,CAAC,IAAKA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAU;YACtE8B,UAAU,EAAGrB,CAAC,IAAKA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAU;YAAAxB,QAAA,eAErErI,OAAA;cAAMsJ,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAES,GAAG,EAAE,QAAQ;gBAAEV,cAAc,EAAE;cAAS,CAAE;cAAAnB,QAAA,gBAC9FrI,OAAA,CAACX,SAAS;gBAACiJ,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEzB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACT1I,OAAA;YACE2K,OAAO,EAAEnH,YAAa;YACtB8F,KAAK,EAAE;cACLmD,IAAI,EAAE,CAAC;cACP7C,OAAO,EAAE,SAAS;cAClBC,eAAe,EAAE,SAAS;cAC1BhC,KAAK,EAAE,SAAS;cAChBkC,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE,SAAS;cACjBI,UAAU,EAAE,KAAK;cACjBd,QAAQ,EAAE,UAAU;cACpBoB,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGlB,CAAC,IAAKA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAU;YACtE8B,UAAU,EAAGrB,CAAC,IAAKA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAU;YAAAxB,QAAA,eAErErI,OAAA;cAAMsJ,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAES,GAAG,EAAE,QAAQ;gBAAEV,cAAc,EAAE;cAAS,CAAE;cAAAnB,QAAA,gBAC9FrI,OAAA,CAACd,CAAC;gBAACoJ,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjF,gBAAgB,CAAC,CAAC,CAAC8D,MAAM,GAAG,CAAC,iBAC5BvH,OAAA;MAAKsJ,KAAK,EAAE;QACVgC,UAAU,EAAE,SAAS;QACrBvB,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,MAAM;QACpBF,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE;MAChB,CAAE;MAAAtB,QAAA,gBACArI,OAAA;QAAKsJ,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBS,GAAG,EAAE,QAAQ;UACbP,YAAY,EAAE;QAChB,CAAE;QAAAtB,QAAA,gBACArI,OAAA,CAACZ,MAAM;UAACkJ,IAAI,EAAE,EAAG;UAACgB,KAAK,EAAE;YAAEzB,KAAK,EAAE;UAAU;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjD1I,OAAA;UAAMsJ,KAAK,EAAE;YACXa,QAAQ,EAAE,UAAU;YACpBc,UAAU,EAAE,KAAK;YACjBpD,KAAK,EAAE;UACT,CAAE;UAAAQ,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1I,OAAA;QAAKsJ,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfU,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE;QACP,CAAE;QAAA7B,QAAA,EACC5E,gBAAgB,CAAC,CAAC,CAACsH,GAAG,CAAC,CAAC2B,MAAM,EAAE1B,KAAK,kBACpChL,OAAA;UAEEsJ,KAAK,EAAE;YACLgC,UAAU,EAAE,SAAS;YACrBzD,KAAK,EAAE,SAAS;YAChB+B,OAAO,EAAE,iBAAiB;YAC1BE,YAAY,EAAE,MAAM;YACpBK,QAAQ,EAAE,SAAS;YACnBc,UAAU,EAAE;UACd,CAAE;UAAA5C,QAAA,EAEDqE;QAAM,GAVF1B,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWN,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1I,OAAA;MAAKsJ,KAAK,EAAE;QACVgC,UAAU,EAAE,OAAO;QACnBxB,YAAY,EAAE,MAAM;QACpBE,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,EACC1F,OAAO,gBACN3C,OAAA;QAAKsJ,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBI,OAAO,EAAE;QACX,CAAE;QAAAvB,QAAA,eACArI,OAAA;UAAKsJ,KAAK,EAAE;YACV6C,KAAK,EAAE,MAAM;YACbQ,MAAM,EAAE,MAAM;YACd5C,MAAM,EAAE,mBAAmB;YAC3B6C,SAAS,EAAE,mBAAmB;YAC9B9C,YAAY,EAAE,KAAK;YACnB+C,SAAS,EAAE;UACb;QAAE;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhG,aAAa,CAAC6E,MAAM,KAAK,CAAC,gBAC5BvH,OAAA;QAAKsJ,KAAK,EAAE;UACVwD,SAAS,EAAE,QAAQ;UACnBlD,OAAO,EAAE;QACX,CAAE;QAAAvB,QAAA,gBACArI,OAAA;UAAKsJ,KAAK,EAAE;YACVa,QAAQ,EAAE,MAAM;YAChBtC,KAAK,EAAE,SAAS;YAChB8B,YAAY,EAAE;UAChB,CAAE;UAAAtB,QAAA,eACArI,OAAA,CAACb,aAAa;YAACmJ,IAAI,EAAE,EAAG;YAACT,KAAK,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACN1I,OAAA;UAAIsJ,KAAK,EAAE;YACT8B,MAAM,EAAE,UAAU;YAClBjB,QAAQ,EAAE,UAAU;YACpBc,UAAU,EAAE,KAAK;YACjBpD,KAAK,EAAE;UACT,CAAE;UAAAQ,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1I,OAAA;UAAGsJ,KAAK,EAAE;YACR8B,MAAM,EAAE,oBAAoB;YAC5BjB,QAAQ,EAAE,UAAU;YACpBtC,KAAK,EAAE;UACT,CAAE;UAAAQ,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ1I,OAAA;UACE2K,OAAO,EAAE/F,wBAAyB;UAClC0E,KAAK,EAAE;YACLC,OAAO,EAAE,aAAa;YACtBE,UAAU,EAAE,QAAQ;YACpBG,OAAO,EAAE,gBAAgB;YACzB0B,UAAU,EAAE,mDAAmD;YAC/DzD,KAAK,EAAE,OAAO;YACdkC,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE,SAAS;YACjBI,UAAU,EAAE,KAAK;YACjBd,QAAQ,EAAE,SAAS;YACnBoB,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGlB,CAAC,IAAK;YAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;YACpDpB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,mCAAmC;UACvE,CAAE;UACF2B,UAAU,EAAGrB,CAAC,IAAK;YACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;YACjDpB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,MAAM;UAC1C,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAEN1I,OAAA;QAAKsJ,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEL,OAAO,EAAE,MAAM;UAAEwD,aAAa,EAAE,QAAQ;UAAE7C,GAAG,EAAE;QAAO,CAAE;QAAA7B,QAAA,EACpF3F,aAAa,IAAIA,aAAa,CAAC6E,MAAM,GAAG,CAAC,GAAG7E,aAAa,CAACqI,GAAG,CAAEjG,YAAY,iBAC1E9E,OAAA;UAEEsJ,KAAK,EAAE;YACLM,OAAO,EAAE,QAAQ;YACjB0B,UAAU,EAAE,SAAS;YACrBvB,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,MAAM;YACpBE,SAAS,EAAE,+BAA+B;YAC1CuB,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGlB,CAAC,IAAK;YAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAS;YACjDS,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,gCAAgC;YAClEM,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;UACtD,CAAE;UACFC,UAAU,EAAGrB,CAAC,IAAK;YACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACO,eAAe,GAAG,SAAS;YACjDS,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACU,SAAS,GAAG,+BAA+B;YACjEM,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;UACnD,CAAE;UAAArD,QAAA,eAEFrI,OAAA;YAAKsJ,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,OAAO;cAAED,cAAc,EAAE;YAAgB,CAAE;YAAAnB,QAAA,gBACpFrI,OAAA;cAAKsJ,KAAK,EAAE;gBAAEmD,IAAI,EAAE,CAAC;gBAAEO,QAAQ,EAAE;cAAE,CAAE;cAAA3E,QAAA,gBACnCrI,OAAA;gBAAKsJ,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,SAAS;kBACdP,YAAY,EAAE;gBAChB,CAAE;gBAAAtB,QAAA,gBACArI,OAAA;kBAAIsJ,KAAK,EAAE;oBACTa,QAAQ,EAAE,UAAU;oBACpBc,UAAU,EAAE,KAAK;oBACjBpD,KAAK,EAAE,SAAS;oBAChBuD,MAAM,EAAE,CAAC;oBACT6B,QAAQ,EAAE,QAAQ;oBAClBC,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBAAA9E,QAAA,EACCvD,YAAY,CAACG;gBAAK;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,EACJ5D,YAAY,CAACsI,aAAa,iBACzBpN,OAAA;kBAAMsJ,KAAK,EAAE;oBACXgC,UAAU,EAAEhE,gBAAgB,CAACxC,YAAY,CAAC;oBAC1C+C,KAAK,EAAE,OAAO;oBACd+B,OAAO,EAAE,gBAAgB;oBACzBE,YAAY,EAAE,MAAM;oBACpBK,QAAQ,EAAE,SAAS;oBACnBc,UAAU,EAAE;kBACd,CAAE;kBAAA5C,QAAA,EACCvD,YAAY,CAACsI;gBAAa;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACP,EACA5D,YAAY,CAACI,SAAS,iBACrBlF,OAAA,CAACV,GAAG;kBAACgJ,IAAI,EAAE,EAAG;kBAACT,KAAK,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACjC,EACA5D,YAAY,CAACK,QAAQ,iBACpBnF,OAAA,CAACT,aAAa;kBAAC+I,IAAI,EAAE,EAAG;kBAACT,KAAK,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC3C,EACAhB,cAAc,CAAC5C,YAAY,CAACN,MAAM,CAAC;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEN1I,OAAA;gBAAGsJ,KAAK,EAAE;kBACRzB,KAAK,EAAE,SAAS;kBAChBsC,QAAQ,EAAE,UAAU;kBACpBR,YAAY,EAAE,SAAS;kBACvB0D,UAAU,EAAE,KAAK;kBACjB9D,OAAO,EAAE,aAAa;kBACtB+D,eAAe,EAAE,CAAC;kBAClBC,eAAe,EAAE,UAAU;kBAC3BN,QAAQ,EAAE;gBACZ,CAAE;gBAAA5E,QAAA,EACCvD,YAAY,CAAC0I;cAAO;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EAGH,CAAE5D,YAAY,CAAC2I,WAAW,IAAI3I,YAAY,CAAC2I,WAAW,CAAClG,MAAM,GAAG,CAAC,IAC/DzC,YAAY,CAAC4I,MAAM,IAAI5I,YAAY,CAAC4I,MAAM,CAACnG,MAAM,GAAG,CAAE,IACvDzC,YAAY,CAAC6I,SAAS,IAAI7I,YAAY,CAAC8I,UAAU,kBACjD5N,OAAA;gBAAKsJ,KAAK,EAAE;kBAAEK,YAAY,EAAE;gBAAU,CAAE;gBAAAtB,QAAA,eACtCrI,OAAA,CAAClB,oBAAoB;kBACnB4O,MAAM;kBACJ;kBACC5I,YAAY,CAAC2I,WAAW,IAAI3I,YAAY,CAAC2I,WAAW,CAAClG,MAAM,GAAG,CAAC,GAC5DzC,YAAY,CAAC2I,WAAW,CAAC1C,GAAG,CAAC8C,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,GACjDhJ,YAAY,CAAC4I,MAAM,IAAI5I,YAAY,CAAC4I,MAAM,CAACnG,MAAM,GAAG,CAAC,GACpDzC,YAAY,CAAC4I,MAAM,CAAC3C,GAAG,CAACgD,GAAG,IAAIA,GAAG,CAACD,SAAS,CAAC,GAC7C,CAAChJ,YAAY,CAAC6I,SAAS,IAAI7I,YAAY,CAAC8I,UAAU,CAAC,CAAClB,MAAM,CAACsB,OAAO,CACzE;kBACDC,SAAS,EAAEnJ,YAAY,CAACG,KAAM;kBAC9BiJ,UAAU,EAAE;gBAAE;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAED1I,OAAA;gBAAKsJ,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,MAAM;kBACXC,QAAQ,EAAE,UAAU;kBACpBtC,KAAK,EAAE,SAAS;kBAChBoC,QAAQ,EAAE;gBACZ,CAAE;gBAAA5B,QAAA,gBACArI,OAAA;kBAAMsJ,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAApB,QAAA,gBACrDrI,OAAA,CAACR,IAAI;oBAAC8I,IAAI,EAAE,EAAG;oBAACgB,KAAK,EAAE;sBAAE6E,WAAW,EAAE;oBAAU;kBAAE;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACpD5D,YAAY,CAACsJ,WAAW,IAAI,SAAS;gBAAA;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACP1I,OAAA;kBAAMsJ,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAApB,QAAA,gBACrDrI,OAAA,CAACf,QAAQ;oBAACqJ,IAAI,EAAE,EAAG;oBAACgB,KAAK,EAAE;sBAAE6E,WAAW,EAAE;oBAAU;kBAAE;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxD7B,UAAU,CAAC/B,YAAY,CAACuJ,UAAU,CAAC;gBAAA;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eAMP1I,OAAA;kBAAMsJ,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAApB,QAAA,gBACrDrI,OAAA,CAACN,KAAK;oBAAC4I,IAAI,EAAE,EAAG;oBAACgB,KAAK,EAAE;sBAAE6E,WAAW,EAAE;oBAAU;kBAAE;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrD5D,YAAY,CAACwJ,cAAc,IAAI,CAAC,EAAC,YACpC;gBAAA;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1I,OAAA;kBAAMsJ,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAApB,QAAA,gBACrDrI,OAAA,CAACL,aAAa;oBAAC2I,IAAI,EAAE,EAAG;oBAACgB,KAAK,EAAE;sBAAE6E,WAAW,EAAE;oBAAU;kBAAE;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC7D5D,YAAY,CAACyJ,aAAa,IAAI,CAAC,EAAC,WACnC;gBAAA;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1I,OAAA;cAAKsJ,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,QAAQ;gBACbmB,UAAU,EAAE;cACd,CAAE;cAAAhD,QAAA,gBACArI,OAAA;gBACE2K,OAAO,EAAEA,CAAA,KAAM3E,sBAAsB,CAAClB,YAAY,CAACE,eAAe,CAAE;gBACpEsE,KAAK,EAAE;kBACLM,OAAO,EAAE,QAAQ;kBACjB/B,KAAK,EAAE,SAAS;kBAChByD,UAAU,EAAE,SAAS;kBACrBvB,MAAM,EAAE,MAAM;kBACdc,MAAM,EAAE,SAAS;kBACjBf,YAAY,EAAE,KAAK;kBACnByB,UAAU,EAAE;gBACd,CAAE;gBACFtG,KAAK,EAAC,MAAM;gBACZuG,WAAW,EAAGlB,CAAC,IAAK;kBAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;gBACtD,CAAE;gBACFC,UAAU,EAAGrB,CAAC,IAAK;kBACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;gBACnD,CAAE;gBAAArD,QAAA,eAEFrI,OAAA,CAACP,GAAG;kBAAC6I,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACT1I,OAAA;gBACE2K,OAAO,EAAEA,CAAA,KAAM9F,sBAAsB,CAACC,YAAY,CAAE;gBACpDwE,KAAK,EAAE;kBACLM,OAAO,EAAE,QAAQ;kBACjB/B,KAAK,EAAE,SAAS;kBAChByD,UAAU,EAAE,SAAS;kBACrBvB,MAAM,EAAE,MAAM;kBACdc,MAAM,EAAE,SAAS;kBACjBf,YAAY,EAAE,KAAK;kBACnByB,UAAU,EAAE;gBACd,CAAE;gBACFtG,KAAK,EAAC,MAAM;gBACZuG,WAAW,EAAGlB,CAAC,IAAK;kBAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;gBACtD,CAAE;gBACFC,UAAU,EAAGrB,CAAC,IAAK;kBACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;gBACnD,CAAE;gBAAArD,QAAA,eAEFrI,OAAA,CAACJ,IAAI;kBAAC0I,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,EACR5D,YAAY,CAACN,MAAM,KAAK,OAAO,iBAC9BxE,OAAA;gBACE2K,OAAO,EAAEA,CAAA,KAAM7E,yBAAyB,CAAChB,YAAY,CAACE,eAAe,CAAE;gBACvEsE,KAAK,EAAE;kBACLM,OAAO,EAAE,QAAQ;kBACjB/B,KAAK,EAAE,SAAS;kBAChByD,UAAU,EAAE,SAAS;kBACrBvB,MAAM,EAAE,MAAM;kBACdc,MAAM,EAAE,SAAS;kBACjBf,YAAY,EAAE,KAAK;kBACnByB,UAAU,EAAE;gBACd,CAAE;gBACFtG,KAAK,EAAC,SAAS;gBACfuG,WAAW,EAAGlB,CAAC,IAAK;kBAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;gBACtD,CAAE;gBACFC,UAAU,EAAGrB,CAAC,IAAK;kBACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;gBACnD,CAAE;gBAAArD,QAAA,eAEFrI,OAAA,CAACH,IAAI;kBAACyI,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACT,EACA5D,YAAY,CAACN,MAAM,KAAK,WAAW,iBAClCxE,OAAA;gBACE2K,OAAO,EAAEA,CAAA,KAAM5E,2BAA2B,CAACjB,YAAY,CAACE,eAAe,CAAE;gBACzEsE,KAAK,EAAE;kBACLM,OAAO,EAAE,QAAQ;kBACjB/B,KAAK,EAAE,SAAS;kBAChByD,UAAU,EAAE,SAAS;kBACrBvB,MAAM,EAAE,MAAM;kBACdc,MAAM,EAAE,SAAS;kBACjBf,YAAY,EAAE,KAAK;kBACnByB,UAAU,EAAE;gBACd,CAAE;gBACFtG,KAAK,EAAC,WAAW;gBACjBuG,WAAW,EAAGlB,CAAC,IAAK;kBAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;gBACtD,CAAE;gBACFC,UAAU,EAAGrB,CAAC,IAAK;kBACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;gBACnD,CAAE;gBAAArD,QAAA,eAEFrI,OAAA,CAAChB,KAAK;kBAACsJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,eACD1I,OAAA;gBACE2K,OAAO,EAAEA,CAAA,KAAMrF,wBAAwB,CAACR,YAAY,CAACE,eAAe,CAAE;gBACtEsE,KAAK,EAAE;kBACLM,OAAO,EAAE,QAAQ;kBACjB/B,KAAK,EAAE,SAAS;kBAChByD,UAAU,EAAE,SAAS;kBACrBvB,MAAM,EAAE,MAAM;kBACdc,MAAM,EAAE,SAAS;kBACjBf,YAAY,EAAE,KAAK;kBACnByB,UAAU,EAAE;gBACd,CAAE;gBACFtG,KAAK,EAAC,QAAQ;gBACduG,WAAW,EAAGlB,CAAC,IAAK;kBAClBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,kBAAkB;gBACtD,CAAE;gBACFC,UAAU,EAAGrB,CAAC,IAAK;kBACjBA,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACgC,UAAU,GAAG,SAAS;kBAC5ChB,CAAC,CAACmB,aAAa,CAACnC,KAAK,CAACoC,SAAS,GAAG,eAAe;gBACnD,CAAE;gBAAArD,QAAA,eAEFrI,OAAA,CAACF,MAAM;kBAACwI,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3PD5D,YAAY,CAACE,eAAe;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4P9B,CACN,CAAC,gBACA1I,OAAA;UAAKsJ,KAAK,EAAE;YACVM,OAAO,EAAE,MAAM;YACfkD,SAAS,EAAE,QAAQ;YACnBjF,KAAK,EAAE;UACT,CAAE;UAAAQ,QAAA,EACC1F,OAAO,GAAG,0BAA0B,GAAG;QAAyB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1I,OAAA,CAAC6I,kBAAkB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtB1I,OAAA,CAACpB,iBAAiB;MAChB4P,MAAM,EAAE3N,SAAU;MAClB4N,OAAO,EAAE7H,gBAAiB;MAC1B8H,MAAM,EAAExI,sBAAuB;MAC/BpB,YAAY,EAAE/D,mBAAoB;MAClC4B,OAAO,EAAEA;IAAQ;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGF1I,OAAA,CAACnB,sBAAsB;MACrB2P,MAAM,EAAEjN,gBAAiB;MACzBkN,OAAO,EAAExI,qBAAsB;MAC/BnB,YAAY,EAAErD;IAAoB;MAAA8G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxI,EAAA,CArtCID,cAAwB;EAAA,QACXtB,YAAY,EAWMD,QAAQ,EA8CvCF,gBAAgB,EAEGC,aAAa;AAAA;AAAAkQ,EAAA,GA5DhC1O,cAAwB;AAutC9B,eAAeA,cAAc;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}