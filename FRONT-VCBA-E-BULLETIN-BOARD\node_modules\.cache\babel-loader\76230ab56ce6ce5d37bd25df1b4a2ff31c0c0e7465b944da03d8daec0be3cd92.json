{"ast": null, "code": "import React,{useState}from'react';import{useComments,formatCommentDate}from'../../hooks/useComments';import{Heart,MessageCircle,AlertCircle,ArrowRight}from'lucide-react';import{shouldShowReplyButton,calculateIndentation,getDepthLimitMessage,getCommentDepthClasses,COMMENT_DEPTH_CONFIG}from'../../utils/commentDepth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CommentItem=_ref=>{let{comment,onReply,onLike,onUnlike,currentUserId,currentUserType,depth=0}=_ref;const[showReplyForm,setShowReplyForm]=useState(false);const[showDepthWarning,setShowDepthWarning]=useState(false);const hasUserReacted=comment.user_reaction!==undefined&&comment.user_reaction!==null;// Calculate depth-related properties\nconst canReply=shouldShowReplyButton(depth);const indentation=calculateIndentation(depth);const depthClasses=getCommentDepthClasses(depth);const depthLimitMessage=getDepthLimitMessage(depth);const isAtMaxDepth=depth>=COMMENT_DEPTH_CONFIG.MAX_DEPTH;const handleReactionToggle=()=>{if(hasUserReacted){onUnlike(comment.comment_id);}else{onLike(comment.comment_id);}};const handleReplyClick=()=>{if(isAtMaxDepth){setShowDepthWarning(true);setTimeout(()=>setShowDepthWarning(false),3000);}else{setShowReplyForm(true);}};return/*#__PURE__*/_jsx(\"div\",{id:\"comment-\".concat(comment.comment_id),className:depthClasses.join(' '),style:{marginLeft:\"\".concat(indentation,\"px\"),marginBottom:'1rem',padding:'1rem',backgroundColor:depth>0?'#f9fafb':'white',borderRadius:'8px',border:'1px solid #e5e7eb',position:'relative'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'start',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'2.5rem',height:'2.5rem',borderRadius:'50%',backgroundColor:'#22c55e',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'600',fontSize:'0.875rem',flexShrink:0},children:comment.author_name?comment.author_name.charAt(0).toUpperCase():'?'}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600',color:'#374151',fontSize:'0.875rem'},children:comment.is_anonymous?'Anonymous':comment.author_name||'Unknown User'}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#6b7280',fontSize:'0.75rem'},children:formatCommentDate(comment.created_at)}),comment.user_type==='admin'&&/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:'#22c55e',color:'white',padding:'0.125rem 0.5rem',borderRadius:'12px',fontSize:'0.625rem',fontWeight:'500'},children:\"Admin\"})]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#374151',fontSize:'0.875rem',lineHeight:'1.5',margin:'0 0 0.75rem 0',whiteSpace:'pre-wrap'},children:comment.comment_text}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.75rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleReactionToggle,style:{display:'flex',alignItems:'center',gap:'0.25rem',background:'none',border:'none',cursor:'pointer',color:hasUserReacted?'#ef4444':'#6b7280',fontSize:'0.75rem',padding:'0.25rem 0.5rem',borderRadius:'4px',transition:'color 0.2s ease'},onMouseOver:e=>{if(!hasUserReacted){e.currentTarget.style.color='#374151';}},onMouseOut:e=>{e.currentTarget.style.color=hasUserReacted?'#ef4444':'#6b7280';},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Heart,{size:14,color:hasUserReacted?'#ef4444':'#6b7280',fill:hasUserReacted?'#ef4444':'none'}),comment.reaction_count||0]})}),canReply?/*#__PURE__*/_jsx(\"button\",{onClick:handleReplyClick,style:{background:'none',border:'none',cursor:'pointer',color:'#6b7280',fontSize:'0.75rem',padding:'0.25rem 0.5rem',borderRadius:'4px',transition:'color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.color='#374151',onMouseOut:e=>e.currentTarget.style.color='#6b7280',children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:12}),\"Reply\"]})}):/*#__PURE__*/// Show depth limit message for max depth comments\n_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.75rem',color:'#f59e0b',fontStyle:'italic'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:12}),/*#__PURE__*/_jsx(\"span\",{children:\"Reply depth limit reached\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{// Scroll to top-level comment for continuing thread\nconst rootElement=document.getElementById(\"comment-\".concat(comment.comment_id));if(rootElement){rootElement.scrollIntoView({behavior:'smooth',block:'center'});}},style:{background:'none',border:'none',cursor:'pointer',color:'#3b82f6',fontSize:'0.75rem',textDecoration:'underline',padding:'0'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[\"Continue thread\",/*#__PURE__*/_jsx(ArrowRight,{size:10})]})})]})]}),showDepthWarning&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'0.5rem',padding:'0.5rem',backgroundColor:'#fef3c7',border:'1px solid #f59e0b',borderRadius:'4px',fontSize:'0.75rem',color:'#92400e',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:14}),/*#__PURE__*/_jsx(\"span\",{children:depthLimitMessage})]}),showReplyForm&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'0.75rem'},children:/*#__PURE__*/_jsx(CommentForm,{announcementId:comment.announcement_id,parentCommentId:comment.comment_id,onSubmit:()=>setShowReplyForm(false),onCancel:()=>setShowReplyForm(false),placeholder:\"Write a reply...\"})}),comment.replies&&comment.replies.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem'},children:comment.replies.map(reply=>/*#__PURE__*/_jsx(CommentItem,{comment:reply,onReply:onReply,onLike:onLike,onUnlike:onUnlike,currentUserId:currentUserId,currentUserType:currentUserType,depth:depth+1},reply.comment_id))})]})]})});};const CommentForm=_ref2=>{let{announcementId,parentCommentId,onSubmit,onCancel,placeholder=\"Write a comment...\"}=_ref2;const[commentText,setCommentText]=useState('');const[isAnonymous,setIsAnonymous]=useState(false);const[isSubmitting,setIsSubmitting]=useState(false);const{createComment}=useComments(announcementId,'student');// Student service\nconst handleSubmit=async e=>{e.preventDefault();if(!commentText.trim())return;try{setIsSubmitting(true);const commentData={announcement_id:announcementId,comment_text:commentText.trim(),is_anonymous:isAnonymous};if(parentCommentId){commentData.parent_comment_id=parentCommentId;}await createComment(commentData);setCommentText('');setIsAnonymous(false);onSubmit();}catch(error){console.error('Error creating comment:',error);}finally{setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,style:{padding:'1rem',backgroundColor:'#f9fafb',borderRadius:'8px',border:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(\"textarea\",{value:commentText,onChange:e=>setCommentText(e.target.value),placeholder:placeholder,rows:3,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'6px',fontSize:'0.875rem',outline:'none',resize:'vertical',marginBottom:'0.75rem'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';}}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.75rem',color:'#6b7280',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isAnonymous,onChange:e=>setIsAnonymous(e.target.checked),style:{marginRight:'0.5rem'}}),\"Post anonymously\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem'},children:[onCancel&&/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,style:{padding:'0.5rem 1rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'6px',cursor:'pointer',fontSize:'0.75rem',fontWeight:'500'},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:!commentText.trim()||isSubmitting,style:{padding:'0.5rem 1rem',background:!commentText.trim()||isSubmitting?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'6px',cursor:!commentText.trim()||isSubmitting?'not-allowed':'pointer',fontSize:'0.75rem',fontWeight:'600'},children:isSubmitting?'Posting...':'Post'})]})]})]});};const CommentSection=_ref3=>{let{announcementId,allowComments=true,currentUserId,currentUserType='student'}=_ref3;const{comments,loading,error,refresh,likeComment,unlikeComment}=useComments(announcementId,'student');// Explicitly use student service\nconst handleReply=parentId=>{// This could trigger a scroll to the reply form or other UI feedback\nconsole.log('Reply to comment:',parentId);};if(!allowComments){return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',textAlign:'center',color:'#6b7280',fontSize:'0.875rem',fontStyle:'italic'},children:\"Comments are disabled for this announcement.\"});}return/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"h3\",{style:{fontSize:'1.125rem',fontWeight:'600',color:'#374151',marginBottom:'1rem'},children:[\"Comments (\",comments.length,\")\"]}),error&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',color:'#dc2626',borderRadius:'6px',marginBottom:'1rem',fontSize:'0.875rem'},children:error}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(CommentForm,{announcementId:announcementId,onSubmit:refresh,placeholder:\"Share your thoughts...\"})}),loading?/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',padding:'2rem'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'1.5rem',height:'1.5rem',border:'2px solid #e5e7eb',borderTop:'2px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}})}):comments.length===0?/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#6b7280',fontSize:'0.875rem'},children:\"No comments yet. Be the first to share your thoughts!\"}):/*#__PURE__*/_jsx(\"div\",{children:comments.map(comment=>/*#__PURE__*/_jsx(CommentItem,{comment:comment,onReply:handleReply,onLike:likeComment,onUnlike:unlikeComment,currentUserId:currentUserId,currentUserType:currentUserType},comment.comment_id))})]});};export default CommentSection;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}