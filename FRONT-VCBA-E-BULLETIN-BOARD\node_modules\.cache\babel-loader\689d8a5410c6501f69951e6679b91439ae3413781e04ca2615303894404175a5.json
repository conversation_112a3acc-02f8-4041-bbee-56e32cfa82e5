{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n  key: \"1yyitq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"22\",\n  y1: \"8\",\n  y2: \"13\",\n  key: \"3nzzx3\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"17\",\n  y1: \"8\",\n  y2: \"13\",\n  key: \"1swrse\"\n}]];\nconst UserX = createLucideIcon(\"user-x\", __iconNode);\nexport { __iconNode, UserX as default };\n//# sourceMappingURL=user-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}