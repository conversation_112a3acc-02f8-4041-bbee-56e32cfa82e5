{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 2-2.3 2.3a3 3 0 0 0 0 4.2l1.8 1.8a3 3 0 0 0 4.2 0L22 8\",\n  key: \"n7qcjb\"\n}], [\"path\", {\n  d: \"M15 15 3.3 3.3a4.2 4.2 0 0 0 0 6l7.3 7.3c.7.7 2 .7 2.8 0L15 15Zm0 0 7 7\",\n  key: \"d0u48b\"\n}], [\"path\", {\n  d: \"m2.1 21.8 6.4-6.3\",\n  key: \"yn04lh\"\n}], [\"path\", {\n  d: \"m19 5-7 7\",\n  key: \"194lzd\"\n}]];\nconst UtensilsCrossed = createLucideIcon(\"utensils-crossed\", __iconNode);\nexport { __iconNode, UtensilsCrossed as default };\n//# sourceMappingURL=utensils-crossed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}