{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 17h6v-6\",\n  key: \"t6n2it\"\n}], [\"path\", {\n  d: \"m22 17-8.5-8.5-5 5L2 7\",\n  key: \"x473p\"\n}]];\nconst TrendingDown = createLucideIcon(\"trending-down\", __iconNode);\nexport { __iconNode, TrendingDown as default };\n//# sourceMappingURL=trending-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}