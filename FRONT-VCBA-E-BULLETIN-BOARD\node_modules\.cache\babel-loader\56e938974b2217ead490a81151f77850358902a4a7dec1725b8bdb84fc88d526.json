{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"1pf5j1\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"6\",\n  x: \"2\",\n  y: \"12\",\n  rx: \"2\",\n  key: \"jm304g\"\n}], [\"path\", {\n  d: \"M10 12h2v6\",\n  key: \"12zw74\"\n}], [\"path\", {\n  d: \"M10 18h4\",\n  key: \"1ulq68\"\n}]];\nconst FileDigit = createLucideIcon(\"file-digit\", __iconNode);\nexport { __iconNode, FileDigit as default };\n//# sourceMappingURL=file-digit.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}