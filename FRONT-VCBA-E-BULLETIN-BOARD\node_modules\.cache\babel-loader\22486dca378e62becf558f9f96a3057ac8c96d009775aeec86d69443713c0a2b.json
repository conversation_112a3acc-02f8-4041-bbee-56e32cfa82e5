{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate,useLocation}from'react-router-dom';import{useStudentAuth}from'../../../contexts/StudentAuthContext';import'./StudentLogin.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentLogin=()=>{var _location$state,_location$state$from;const navigate=useNavigate();const location=useLocation();const{login,error,isLoading,clearError}=useStudentAuth();const[formData,setFormData]=useState({identifier:'',password:'',remember:false});const[formErrors,setFormErrors]=useState({});const[showPassword,setShowPassword]=useState(false);// Get the intended destination or default to student newsfeed (landing page)\nconst from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||'/student/newsfeed';// Handle input changes\nconst handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));// Clear field error when user starts typing\nif(formErrors[name]){setFormErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:undefined}));}// Clear global error\nif(error){clearError();}};// Validate form\nconst validateForm=()=>{const errors={};// Identifier validation (email or student number)\nif(!formData.identifier.trim()){errors.identifier='Email or student number is required';}// Password validation\nif(!formData.password){errors.password='Password is required';}setFormErrors(errors);return Object.keys(errors).length===0;};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{console.log('🔐 StudentLogin - Attempting login with:',{identifier:formData.identifier,userType:'student',redirectTo:from});await login({email:formData.identifier,// Backend handles both email and student number\npassword:formData.password,userType:'student'});console.log('✅ StudentLogin - Login successful, redirecting to:',from);// Redirect to intended destination\nnavigate(from,{replace:true});}catch(error){// Error is handled by the auth context\nconsole.error('❌ StudentLogin - Login failed:',error);}};// Toggle password visibility\nconst togglePasswordVisibility=()=>{setShowPassword(!showPassword);};// Determine if input looks like email or student number\nconst isEmail=formData.identifier.includes('@');return/*#__PURE__*/_jsx(\"div\",{className:\"student-login\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"student-login__form-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__form-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__form-header\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",className:\"student-login__form-logo\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"student-login__form-title\",children:\"STUDENT LOGIN\"}),/*#__PURE__*/_jsx(\"p\",{className:\"student-login__form-subtitle\",children:\"Villamor College of Business and Arts, Inc.\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__error\",children:[/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})}),error]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"student-login__form\",noValidate:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"identifier\",className:\"student-login__label\",children:\"Email or Student Number\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"identifier\",name:\"identifier\",value:formData.identifier,onChange:handleInputChange,placeholder:\"Enter your email or student number\",className:\"student-login__input \".concat(formErrors.identifier?'error':''),disabled:isLoading,autoComplete:\"username\",required:true}),formErrors.identifier&&/*#__PURE__*/_jsx(\"span\",{className:\"student-login__error-text\",children:formErrors.identifier})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"student-login__label\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:showPassword?'text':'password',id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,placeholder:\"Enter your password\",className:\"student-login__input \".concat(formErrors.password?'error':''),disabled:isLoading,autoComplete:\"current-password\",required:true}),formErrors.password&&/*#__PURE__*/_jsx(\"span\",{className:\"student-login__error-text\",children:formErrors.password})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__remember\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"remember\",name:\"remember\",checked:formData.remember,onChange:handleInputChange,className:\"student-login__checkbox\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"remember\",className:\"student-login__remember-label\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"student-login__submit-btn\",disabled:isLoading,children:isLoading?'Signing in...':'Sign In'})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"student-login__info-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"student-login__info-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__school-info\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/ebb1.png\",alt:\"E-Bulletin Board Logo\",className:\"student-login__school-logo\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"student-login__school-name\",children:\"VCBA E-BULLETIN BOARD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"student-login__school-description\",children:\"Villamor College of Business and Arts, Inc.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"student-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/megaphone.png\",alt:\"Categorized Contents\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Categorized Contents\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Organized announcements by departments, clubs, events, and more\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"student-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/message.png\",alt:\"Centralized Platform\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Centralized Platform\"}),/*#__PURE__*/_jsx(\"p\",{children:\"All school announcements in one place \\u2014 accessible anytime, anywhere\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"student-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/heart.png\",alt:\"User-Friendly Environment\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"student-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"User-Friendly Environment\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Simple design with smooth navigation and accessibility support\"})]})]})]})]})})})]})});};export default StudentLogin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}