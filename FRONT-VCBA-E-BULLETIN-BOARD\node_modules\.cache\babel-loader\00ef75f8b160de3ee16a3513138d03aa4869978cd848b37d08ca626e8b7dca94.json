{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.5 21a7.5 7.5 0 1 1 7.35-9\",\n  key: \"1gyj8k\"\n}], [\"path\", {\n  d: \"M13 12V3\",\n  key: \"18om2a\"\n}], [\"path\", {\n  d: \"M4 21h16\",\n  key: \"1h09gz\"\n}], [\"path\", {\n  d: \"M9 12V3\",\n  key: \"geutu0\"\n}]];\nconst GeorgianLari = createLucideIcon(\"georgian-lari\", __iconNode);\nexport { __iconNode, GeorgianLari as default };\n//# sourceMappingURL=georgian-lari.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}