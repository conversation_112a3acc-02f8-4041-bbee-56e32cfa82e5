{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 6h3\",\n  key: \"155dbl\"\n}], [\"path\", {\n  d: \"M17 6h.01\",\n  key: \"e2y6kg\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"20\",\n  x: \"3\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"od3kk9\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"5\",\n  key: \"nlbqau\"\n}], [\"path\", {\n  d: \"M12 18a2.5 2.5 0 0 0 0-5 2.5 2.5 0 0 1 0-5\",\n  key: \"17lach\"\n}]];\nconst WashingMachine = createLucideIcon(\"washing-machine\", __iconNode);\nexport { __iconNode, WashingMachine as default };\n//# sourceMappingURL=washing-machine.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}