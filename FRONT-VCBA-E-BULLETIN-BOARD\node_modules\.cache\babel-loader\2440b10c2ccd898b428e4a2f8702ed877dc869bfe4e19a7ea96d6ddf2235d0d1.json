{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.5 21H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z\",\n  key: \"gqqjvc\"\n}], [\"path\", {\n  d: \"M22 10a3 3 0 0 0-3-3h-2.207a5.502 5.502 0 0 0-10.702.5\",\n  key: \"1p2s76\"\n}]];\nconst Cloudy = createLucideIcon(\"cloudy\", __iconNode);\nexport { __iconNode, Cloudy as default };\n//# sourceMappingURL=cloudy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}