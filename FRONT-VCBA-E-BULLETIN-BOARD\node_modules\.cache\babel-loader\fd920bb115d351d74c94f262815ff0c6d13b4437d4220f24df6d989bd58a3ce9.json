{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z\",\n  key: \"l5xja\"\n}], [\"path\", {\n  d: \"M9 13a4.5 4.5 0 0 0 3-4\",\n  key: \"10igwf\"\n}], [\"path\", {\n  d: \"M6.003 5.125A3 3 0 0 0 6.401 6.5\",\n  key: \"105sqy\"\n}], [\"path\", {\n  d: \"M3.477 10.896a4 4 0 0 1 .585-.396\",\n  key: \"ql3yin\"\n}], [\"path\", {\n  d: \"M6 18a4 4 0 0 1-1.967-.516\",\n  key: \"2e4loj\"\n}], [\"path\", {\n  d: \"M12 13h4\",\n  key: \"1ku699\"\n}], [\"path\", {\n  d: \"M12 18h6a2 2 0 0 1 2 2v1\",\n  key: \"105ag5\"\n}], [\"path\", {\n  d: \"M12 8h8\",\n  key: \"1lhi5i\"\n}], [\"path\", {\n  d: \"M16 8V5a2 2 0 0 1 2-2\",\n  key: \"u6izg6\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"13\",\n  r: \".5\",\n  key: \"ry7gng\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"3\",\n  r: \".5\",\n  key: \"1aiba7\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"21\",\n  r: \".5\",\n  key: \"yhc1fs\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"8\",\n  r: \".5\",\n  key: \"1e43v0\"\n}]];\nconst BrainCircuit = createLucideIcon(\"brain-circuit\", __iconNode);\nexport { __iconNode, BrainCircuit as default };\n//# sourceMappingURL=brain-circuit.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}