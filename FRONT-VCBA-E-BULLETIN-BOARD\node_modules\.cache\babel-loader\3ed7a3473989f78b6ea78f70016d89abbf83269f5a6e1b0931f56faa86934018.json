{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 15V9\",\n  key: \"1hci5f\"\n}], [\"path\", {\n  d: \"M15 15h-3v4l-7-7 7-7v4h3v6z\",\n  key: \"16tjna\"\n}]];\nconst ArrowBigLeftDash = createLucideIcon(\"arrow-big-left-dash\", __iconNode);\nexport { __iconNode, ArrowBigLeftDash as default };\n//# sourceMappingURL=arrow-big-left-dash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}