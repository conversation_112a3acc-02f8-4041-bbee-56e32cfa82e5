{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"m9 15 2 2 4-4\",\n  key: \"1grp1n\"\n}]];\nconst FileCheck = createLucideIcon(\"file-check\", __iconNode);\nexport { __iconNode, FileCheck as default };\n//# sourceMappingURL=file-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}