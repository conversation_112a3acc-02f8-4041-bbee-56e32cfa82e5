{"version": 3, "file": "accessible-name-and-description.js", "names": ["asFlatString", "s", "trim", "replace", "isHidden", "node", "getComputedStyleImplementation", "isElement", "hasAttribute", "getAttribute", "style", "getPropertyValue", "isControl", "hasAnyConcreteRoles", "hasAbstractRole", "role", "TypeError", "querySelectorAllSubtree", "element", "selectors", "elements", "ArrayFrom", "querySelectorAll", "queryIdRefs", "for<PERSON>ach", "root", "push", "apply", "querySelectedOptions", "listbox", "isHTMLSelectElement", "selectedOptions", "isMarkedPresentational", "isNativeHostLanguageTextAlternativeElement", "isHTMLTableCaptionElement", "allowsNameFromContent", "isDescendantOfNativeHostLanguageTextAlternativeElement", "getValueOfTextbox", "isHTMLInputElement", "isHTMLTextAreaElement", "value", "textContent", "getTextualContent", "declaration", "content", "test", "slice", "isLabelableElement", "localName", "getLocalName", "findLabelableElement", "labelableElement", "childNodes", "childNode", "descendantLabelableElement", "getControlOfLabel", "label", "control", "undefined", "htmlFor", "ownerDocument", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "labelsProperty", "labels", "document", "filter", "getSlotContents", "slot", "assignedNodes", "length", "computeTextAlternative", "options", "consultedNodes", "SetLike", "window", "safeWindow", "compute", "computedStyleSupportsPseudoElements", "getComputedStyle", "bind", "hidden", "computeMiscTextAlternative", "context", "accumulatedText", "pseudoBefore", "beforeContent", "isHTMLSlotElement", "concat", "child", "result", "isEmbeddedInLabel", "isReferenced", "recursion", "display", "separator", "pseudoAfter", "afterContent", "useAttribute", "attributeName", "attribute", "getAttributeNode", "has", "add", "computeTooltipAttributeValue", "computeElementTextAlternative", "isHTMLFieldSetElement", "children", "i", "isHTMLLegendElement", "isHTMLTableElement", "isSVGSVGElement", "isSVGTitleElement", "nameFromAlt", "isHTMLOptGroupElement", "nameFromLabel", "type", "nameFromValue", "map", "join", "nameFromTitle", "nameFromSubTree", "current", "labelAttributeNode", "labelElements", "skipToStep2E", "aria<PERSON><PERSON><PERSON>", "elementTextAlternative", "selectedOption", "accumulatedText2F", "nodeType", "TEXT_NODE", "tooltipAttributeValue"], "sources": ["../sources/accessible-name-and-description.ts"], "sourcesContent": ["/**\n * implements https://w3c.github.io/accname/\n */\nimport ArrayFrom from \"./polyfills/array.from\";\nimport SetLike from \"./polyfills/SetLike\";\nimport {\n\thasAnyConcreteRoles,\n\tisElement,\n\tisHTMLTableCaptionElement,\n\tisHTMLInputElement,\n\tisHTMLSelectElement,\n\tisHTMLTextAreaElement,\n\tsafeWindow,\n\tisHTMLFieldSetElement,\n\tisHTMLLegendElement,\n\tisHTMLOptGroupElement,\n\tisHTMLTableElement,\n\tisHTMLSlotElement,\n\tisSVGSVGElement,\n\tisSVGTitleElement,\n\tqueryIdRefs,\n\tgetLocalName,\n} from \"./util\";\n\n/**\n *  A string of characters where all carriage returns, newlines, tabs, and form-feeds are replaced with a single space, and multiple spaces are reduced to a single space. The string contains only character data; it does not contain any markup.\n */\ntype FlatString = string & {\n\t__flat: true;\n};\n\n/**\n * interface for an options-bag where `window.getComputedStyle` can be mocked\n */\nexport interface ComputeTextAlternativeOptions {\n\tcompute?: \"description\" | \"name\";\n\t/**\n\t * Set to true if window.computedStyle supports the second argument.\n\t * This should be false in JSDOM. Otherwise JSDOM will log console errors.\n\t */\n\tcomputedStyleSupportsPseudoElements?: boolean;\n\t/**\n\t * mock window.getComputedStyle. Needs `content`, `display` and `visibility`\n\t */\n\tgetComputedStyle?: typeof window.getComputedStyle;\n\t/**\n\t * Set to `true` if you want to include hidden elements in the accessible name and description computation.\n\t * Skips 2A in https://w3c.github.io/accname/#computation-steps.\n\t * @default false\n\t */\n\thidden?: boolean;\n}\n\n/**\n *\n * @param {string} string -\n * @returns {FlatString} -\n */\nfunction asFlatString(s: string): FlatString {\n\treturn s.trim().replace(/\\s\\s+/g, \" \") as FlatString;\n}\n\n/**\n *\n * @param node -\n * @param options - These are not optional to prevent accidentally calling it without options in `computeAccessibleName`\n * @returns {boolean} -\n */\nfunction isHidden(\n\tnode: Node,\n\tgetComputedStyleImplementation: typeof window.getComputedStyle\n): node is Element {\n\tif (!isElement(node)) {\n\t\treturn false;\n\t}\n\n\tif (\n\t\tnode.hasAttribute(\"hidden\") ||\n\t\tnode.getAttribute(\"aria-hidden\") === \"true\"\n\t) {\n\t\treturn true;\n\t}\n\n\tconst style = getComputedStyleImplementation(node);\n\treturn (\n\t\tstyle.getPropertyValue(\"display\") === \"none\" ||\n\t\tstyle.getPropertyValue(\"visibility\") === \"hidden\"\n\t);\n}\n\n/**\n * @param {Node} node -\n * @returns {boolean} - As defined in step 2E of https://w3c.github.io/accname/#mapping_additional_nd_te\n */\nfunction isControl(node: Node): boolean {\n\treturn (\n\t\thasAnyConcreteRoles(node, [\"button\", \"combobox\", \"listbox\", \"textbox\"]) ||\n\t\thasAbstractRole(node, \"range\")\n\t);\n}\n\nfunction hasAbstractRole(node: Node, role: string): node is Element {\n\tif (!isElement(node)) {\n\t\treturn false;\n\t}\n\n\tswitch (role) {\n\t\tcase \"range\":\n\t\t\treturn hasAnyConcreteRoles(node, [\n\t\t\t\t\"meter\",\n\t\t\t\t\"progressbar\",\n\t\t\t\t\"scrollbar\",\n\t\t\t\t\"slider\",\n\t\t\t\t\"spinbutton\",\n\t\t\t]);\n\t\tdefault:\n\t\t\tthrow new TypeError(\n\t\t\t\t`No knowledge about abstract role '${role}'. This is likely a bug :(`\n\t\t\t);\n\t}\n}\n\n/**\n * element.querySelectorAll but also considers owned tree\n * @param element\n * @param selectors\n */\nfunction querySelectorAllSubtree(\n\telement: Element,\n\tselectors: string\n): Element[] {\n\tconst elements = ArrayFrom(element.querySelectorAll(selectors));\n\n\tqueryIdRefs(element, \"aria-owns\").forEach((root) => {\n\t\t// babel transpiles this assuming an iterator\n\t\telements.push.apply(elements, ArrayFrom(root.querySelectorAll(selectors)));\n\t});\n\n\treturn elements;\n}\n\nfunction querySelectedOptions(listbox: Element): ArrayLike<Element> {\n\tif (isHTMLSelectElement(listbox)) {\n\t\t// IE11 polyfill\n\t\treturn (\n\t\t\tlistbox.selectedOptions || querySelectorAllSubtree(listbox, \"[selected]\")\n\t\t);\n\t}\n\treturn querySelectorAllSubtree(listbox, '[aria-selected=\"true\"]');\n}\n\nfunction isMarkedPresentational(node: Node): node is Element {\n\treturn hasAnyConcreteRoles(node, [\"none\", \"presentation\"]);\n}\n\n/**\n * Elements specifically listed in html-aam\n *\n * We don't need this for `label` or `legend` elements.\n * Their implicit roles already allow \"naming from content\".\n *\n * sources:\n *\n * - https://w3c.github.io/html-aam/#table-element\n */\nfunction isNativeHostLanguageTextAlternativeElement(\n\tnode: Node\n): node is Element {\n\treturn isHTMLTableCaptionElement(node);\n}\n\n/**\n * https://w3c.github.io/aria/#namefromcontent\n */\nfunction allowsNameFromContent(node: Node): boolean {\n\treturn hasAnyConcreteRoles(node, [\n\t\t\"button\",\n\t\t\"cell\",\n\t\t\"checkbox\",\n\t\t\"columnheader\",\n\t\t\"gridcell\",\n\t\t\"heading\",\n\t\t\"label\",\n\t\t\"legend\",\n\t\t\"link\",\n\t\t\"menuitem\",\n\t\t\"menuitemcheckbox\",\n\t\t\"menuitemradio\",\n\t\t\"option\",\n\t\t\"radio\",\n\t\t\"row\",\n\t\t\"rowheader\",\n\t\t\"switch\",\n\t\t\"tab\",\n\t\t\"tooltip\",\n\t\t\"treeitem\",\n\t]);\n}\n\n/**\n * TODO https://github.com/eps1lon/dom-accessibility-api/issues/100\n */\nfunction isDescendantOfNativeHostLanguageTextAlternativeElement(\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars -- not implemented yet\n\tnode: Node\n): boolean {\n\treturn false;\n}\n\nfunction getValueOfTextbox(element: Element): string {\n\tif (isHTMLInputElement(element) || isHTMLTextAreaElement(element)) {\n\t\treturn element.value;\n\t}\n\t// https://github.com/eps1lon/dom-accessibility-api/issues/4\n\treturn element.textContent || \"\";\n}\n\nfunction getTextualContent(declaration: CSSStyleDeclaration): string {\n\tconst content = declaration.getPropertyValue(\"content\");\n\tif (/^[\"'].*[\"']$/.test(content)) {\n\t\treturn content.slice(1, -1);\n\t}\n\treturn \"\";\n}\n\n/**\n * https://html.spec.whatwg.org/multipage/forms.html#category-label\n * TODO: form-associated custom elements\n * @param element\n */\nfunction isLabelableElement(element: Element): boolean {\n\tconst localName = getLocalName(element);\n\n\treturn (\n\t\tlocalName === \"button\" ||\n\t\t(localName === \"input\" && element.getAttribute(\"type\") !== \"hidden\") ||\n\t\tlocalName === \"meter\" ||\n\t\tlocalName === \"output\" ||\n\t\tlocalName === \"progress\" ||\n\t\tlocalName === \"select\" ||\n\t\tlocalName === \"textarea\"\n\t);\n}\n\n/**\n * > [...], then the first such descendant in tree order is the label element's labeled control.\n * -- https://html.spec.whatwg.org/multipage/forms.html#labeled-control\n * @param element\n */\nfunction findLabelableElement(element: Element): Element | null {\n\tif (isLabelableElement(element)) {\n\t\treturn element;\n\t}\n\tlet labelableElement: Element | null = null;\n\telement.childNodes.forEach((childNode) => {\n\t\tif (labelableElement === null && isElement(childNode)) {\n\t\t\tconst descendantLabelableElement = findLabelableElement(childNode);\n\t\t\tif (descendantLabelableElement !== null) {\n\t\t\t\tlabelableElement = descendantLabelableElement;\n\t\t\t}\n\t\t}\n\t});\n\n\treturn labelableElement;\n}\n\n/**\n * Polyfill of HTMLLabelElement.control\n * https://html.spec.whatwg.org/multipage/forms.html#labeled-control\n * @param label\n */\nfunction getControlOfLabel(label: HTMLLabelElement): Element | null {\n\tif (label.control !== undefined) {\n\t\treturn label.control;\n\t}\n\n\tconst htmlFor = label.getAttribute(\"for\");\n\tif (htmlFor !== null) {\n\t\treturn label.ownerDocument.getElementById(htmlFor);\n\t}\n\n\treturn findLabelableElement(label);\n}\n\n/**\n * Polyfill of HTMLInputElement.labels\n * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/labels\n * @param element\n */\nfunction getLabels(element: Element): HTMLLabelElement[] | null {\n\tconst labelsProperty = (element as HTMLInputElement).labels as\n\t\t| HTMLInputElement[\"labels\"]\n\t\t| undefined;\n\n\tif (labelsProperty === null) {\n\t\treturn labelsProperty;\n\t}\n\tif (labelsProperty !== undefined) {\n\t\treturn ArrayFrom(labelsProperty);\n\t}\n\n\t// polyfill\n\tif (!isLabelableElement(element)) {\n\t\treturn null;\n\t}\n\tconst document = element.ownerDocument;\n\n\treturn ArrayFrom(document.querySelectorAll(\"label\")).filter((label) => {\n\t\treturn getControlOfLabel(label) === element;\n\t});\n}\n\n/**\n * Gets the contents of a slot used for computing the accname\n * @param slot\n */\nfunction getSlotContents(slot: HTMLSlotElement): Node[] {\n\t// Computing the accessible name for elements containing slots is not\n\t// currently defined in the spec. This implementation reflects the\n\t// behavior of NVDA 2020.2/Firefox 81 and iOS VoiceOver/Safari 13.6.\n\tconst assignedNodes = slot.assignedNodes();\n\tif (assignedNodes.length === 0) {\n\t\t// if no nodes are assigned to the slot, it displays the default content\n\t\treturn ArrayFrom(slot.childNodes);\n\t}\n\treturn assignedNodes;\n}\n\n/**\n * implements https://w3c.github.io/accname/#mapping_additional_nd_te\n * @param root\n * @param options\n * @returns\n */\nexport function computeTextAlternative(\n\troot: Element,\n\toptions: ComputeTextAlternativeOptions = {}\n): string {\n\tconst consultedNodes = new SetLike<Node>();\n\n\tconst window = safeWindow(root);\n\tconst {\n\t\tcompute = \"name\",\n\t\tcomputedStyleSupportsPseudoElements = options.getComputedStyle !==\n\t\t\tundefined,\n\t\t// This might be overengineered. I don't know what happens if I call\n\t\t// window.getComputedStyle(elementFromAnotherWindow) or if I don't bind it\n\t\t// the type declarations don't require a `this`\n\t\t// eslint-disable-next-line no-restricted-properties\n\t\tgetComputedStyle = window.getComputedStyle.bind(window),\n\t\thidden = false,\n\t} = options;\n\n\t// 2F.i\n\tfunction computeMiscTextAlternative(\n\t\tnode: Node,\n\t\tcontext: { isEmbeddedInLabel: boolean; isReferenced: boolean }\n\t): string {\n\t\tlet accumulatedText = \"\";\n\t\tif (isElement(node) && computedStyleSupportsPseudoElements) {\n\t\t\tconst pseudoBefore = getComputedStyle(node, \"::before\");\n\t\t\tconst beforeContent = getTextualContent(pseudoBefore);\n\t\t\taccumulatedText = `${beforeContent} ${accumulatedText}`;\n\t\t}\n\n\t\t// FIXME: Including aria-owns is not defined in the spec\n\t\t// But it is required in the web-platform-test\n\t\tconst childNodes = isHTMLSlotElement(node)\n\t\t\t? getSlotContents(node)\n\t\t\t: ArrayFrom(node.childNodes).concat(queryIdRefs(node, \"aria-owns\"));\n\t\tchildNodes.forEach((child) => {\n\t\t\tconst result = computeTextAlternative(child, {\n\t\t\t\tisEmbeddedInLabel: context.isEmbeddedInLabel,\n\t\t\t\tisReferenced: false,\n\t\t\t\trecursion: true,\n\t\t\t});\n\t\t\t// TODO: Unclear why display affects delimiter\n\t\t\t// see https://github.com/w3c/accname/issues/3\n\t\t\tconst display = isElement(child)\n\t\t\t\t? getComputedStyle(child).getPropertyValue(\"display\")\n\t\t\t\t: \"inline\";\n\t\t\tconst separator = display !== \"inline\" ? \" \" : \"\";\n\t\t\t// trailing separator for wpt tests\n\t\t\taccumulatedText += `${separator}${result}${separator}`;\n\t\t});\n\n\t\tif (isElement(node) && computedStyleSupportsPseudoElements) {\n\t\t\tconst pseudoAfter = getComputedStyle(node, \"::after\");\n\t\t\tconst afterContent = getTextualContent(pseudoAfter);\n\t\t\taccumulatedText = `${accumulatedText} ${afterContent}`;\n\t\t}\n\n\t\treturn accumulatedText.trim();\n\t}\n\n\t/**\n\t *\n\t * @param element\n\t * @param attributeName\n\t * @returns A string non-empty string or `null`\n\t */\n\tfunction useAttribute(\n\t\telement: Element,\n\t\tattributeName: string\n\t): string | null {\n\t\tconst attribute = element.getAttributeNode(attributeName);\n\t\tif (\n\t\t\tattribute !== null &&\n\t\t\t!consultedNodes.has(attribute) &&\n\t\t\tattribute.value.trim() !== \"\"\n\t\t) {\n\t\t\tconsultedNodes.add(attribute);\n\t\t\treturn attribute.value;\n\t\t}\n\t\treturn null;\n\t}\n\n\tfunction computeTooltipAttributeValue(node: Node): string | null {\n\t\tif (!isElement(node)) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn useAttribute(node, \"title\");\n\t}\n\n\tfunction computeElementTextAlternative(node: Node): string | null {\n\t\tif (!isElement(node)) {\n\t\t\treturn null;\n\t\t}\n\n\t\t// https://w3c.github.io/html-aam/#fieldset-and-legend-elements\n\t\tif (isHTMLFieldSetElement(node)) {\n\t\t\tconsultedNodes.add(node);\n\t\t\tconst children = ArrayFrom(node.childNodes);\n\t\t\tfor (let i = 0; i < children.length; i += 1) {\n\t\t\t\tconst child = children[i];\n\t\t\t\tif (isHTMLLegendElement(child)) {\n\t\t\t\t\treturn computeTextAlternative(child, {\n\t\t\t\t\t\tisEmbeddedInLabel: false,\n\t\t\t\t\t\tisReferenced: false,\n\t\t\t\t\t\trecursion: false,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (isHTMLTableElement(node)) {\n\t\t\t// https://w3c.github.io/html-aam/#table-element\n\t\t\tconsultedNodes.add(node);\n\t\t\tconst children = ArrayFrom(node.childNodes);\n\t\t\tfor (let i = 0; i < children.length; i += 1) {\n\t\t\t\tconst child = children[i];\n\t\t\t\tif (isHTMLTableCaptionElement(child)) {\n\t\t\t\t\treturn computeTextAlternative(child, {\n\t\t\t\t\t\tisEmbeddedInLabel: false,\n\t\t\t\t\t\tisReferenced: false,\n\t\t\t\t\t\trecursion: false,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (isSVGSVGElement(node)) {\n\t\t\t// https://www.w3.org/TR/svg-aam-1.0/\n\t\t\tconsultedNodes.add(node);\n\t\t\tconst children = ArrayFrom(node.childNodes);\n\t\t\tfor (let i = 0; i < children.length; i += 1) {\n\t\t\t\tconst child = children[i];\n\t\t\t\tif (isSVGTitleElement(child)) {\n\t\t\t\t\treturn child.textContent;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null;\n\t\t} else if (getLocalName(node) === \"img\" || getLocalName(node) === \"area\") {\n\t\t\t// https://w3c.github.io/html-aam/#area-element\n\t\t\t// https://w3c.github.io/html-aam/#img-element\n\t\t\tconst nameFromAlt = useAttribute(node, \"alt\");\n\t\t\tif (nameFromAlt !== null) {\n\t\t\t\treturn nameFromAlt;\n\t\t\t}\n\t\t} else if (isHTMLOptGroupElement(node)) {\n\t\t\tconst nameFromLabel = useAttribute(node, \"label\");\n\t\t\tif (nameFromLabel !== null) {\n\t\t\t\treturn nameFromLabel;\n\t\t\t}\n\t\t}\n\n\t\tif (\n\t\t\tisHTMLInputElement(node) &&\n\t\t\t(node.type === \"button\" ||\n\t\t\t\tnode.type === \"submit\" ||\n\t\t\t\tnode.type === \"reset\")\n\t\t) {\n\t\t\t// https://w3c.github.io/html-aam/#input-type-text-input-type-password-input-type-search-input-type-tel-input-type-email-input-type-url-and-textarea-element-accessible-description-computation\n\t\t\tconst nameFromValue = useAttribute(node, \"value\");\n\t\t\tif (nameFromValue !== null) {\n\t\t\t\treturn nameFromValue;\n\t\t\t}\n\n\t\t\t// TODO: l10n\n\t\t\tif (node.type === \"submit\") {\n\t\t\t\treturn \"Submit\";\n\t\t\t}\n\t\t\t// TODO: l10n\n\t\t\tif (node.type === \"reset\") {\n\t\t\t\treturn \"Reset\";\n\t\t\t}\n\t\t}\n\n\t\tconst labels = getLabels(node);\n\t\tif (labels !== null && labels.length !== 0) {\n\t\t\tconsultedNodes.add(node);\n\t\t\treturn ArrayFrom(labels)\n\t\t\t\t.map((element) => {\n\t\t\t\t\treturn computeTextAlternative(element, {\n\t\t\t\t\t\tisEmbeddedInLabel: true,\n\t\t\t\t\t\tisReferenced: false,\n\t\t\t\t\t\trecursion: true,\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.filter((label) => {\n\t\t\t\t\treturn label.length > 0;\n\t\t\t\t})\n\t\t\t\t.join(\" \");\n\t\t}\n\n\t\t// https://w3c.github.io/html-aam/#input-type-image-accessible-name-computation\n\t\t// TODO: wpt test consider label elements but html-aam does not mention them\n\t\t// We follow existing implementations over spec\n\t\tif (isHTMLInputElement(node) && node.type === \"image\") {\n\t\t\tconst nameFromAlt = useAttribute(node, \"alt\");\n\t\t\tif (nameFromAlt !== null) {\n\t\t\t\treturn nameFromAlt;\n\t\t\t}\n\n\t\t\tconst nameFromTitle = useAttribute(node, \"title\");\n\t\t\tif (nameFromTitle !== null) {\n\t\t\t\treturn nameFromTitle;\n\t\t\t}\n\n\t\t\t// TODO: l10n\n\t\t\treturn \"Submit Query\";\n\t\t}\n\n\t\tif (hasAnyConcreteRoles(node, [\"button\"])) {\n\t\t\t// https://www.w3.org/TR/html-aam-1.0/#button-element\n\t\t\tconst nameFromSubTree = computeMiscTextAlternative(node, {\n\t\t\t\tisEmbeddedInLabel: false,\n\t\t\t\tisReferenced: false,\n\t\t\t});\n\t\t\tif (nameFromSubTree !== \"\") {\n\t\t\t\treturn nameFromSubTree;\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tfunction computeTextAlternative(\n\t\tcurrent: Node,\n\t\tcontext: {\n\t\t\tisEmbeddedInLabel: boolean;\n\t\t\tisReferenced: boolean;\n\t\t\trecursion: boolean;\n\t\t}\n\t): string {\n\t\tif (consultedNodes.has(current)) {\n\t\t\treturn \"\";\n\t\t}\n\n\t\t// 2A\n\t\tif (\n\t\t\t!hidden &&\n\t\t\tisHidden(current, getComputedStyle) &&\n\t\t\t!context.isReferenced\n\t\t) {\n\t\t\tconsultedNodes.add(current);\n\t\t\treturn \"\" as FlatString;\n\t\t}\n\n\t\t// 2B\n\t\tconst labelAttributeNode = isElement(current)\n\t\t\t? current.getAttributeNode(\"aria-labelledby\")\n\t\t\t: null;\n\t\t// TODO: Do we generally need to block query IdRefs of attributes we have already consulted?\n\t\tconst labelElements =\n\t\t\tlabelAttributeNode !== null && !consultedNodes.has(labelAttributeNode)\n\t\t\t\t? queryIdRefs(current, \"aria-labelledby\")\n\t\t\t\t: [];\n\t\tif (\n\t\t\tcompute === \"name\" &&\n\t\t\t!context.isReferenced &&\n\t\t\tlabelElements.length > 0\n\t\t) {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- Can't be null here otherwise labelElements would be empty\n\t\t\tconsultedNodes.add(labelAttributeNode!);\n\n\t\t\treturn labelElements\n\t\t\t\t.map((element) => {\n\t\t\t\t\t// TODO: Chrome will consider repeated values i.e. use a node multiple times while we'll bail out in computeTextAlternative.\n\t\t\t\t\treturn computeTextAlternative(element, {\n\t\t\t\t\t\tisEmbeddedInLabel: context.isEmbeddedInLabel,\n\t\t\t\t\t\tisReferenced: true,\n\t\t\t\t\t\t// this isn't recursion as specified, otherwise we would skip\n\t\t\t\t\t\t// `aria-label` in\n\t\t\t\t\t\t// <input id=\"myself\" aria-label=\"foo\" aria-labelledby=\"myself\"\n\t\t\t\t\t\trecursion: false,\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.join(\" \");\n\t\t}\n\n\t\t// 2C\n\t\t// Changed from the spec in anticipation of https://github.com/w3c/accname/issues/64\n\t\t// spec says we should only consider skipping if we have a non-empty label\n\t\tconst skipToStep2E =\n\t\t\tcontext.recursion && isControl(current) && compute === \"name\";\n\t\tif (!skipToStep2E) {\n\t\t\tconst ariaLabel = (\n\t\t\t\t(isElement(current) && current.getAttribute(\"aria-label\")) ||\n\t\t\t\t\"\"\n\t\t\t).trim();\n\t\t\tif (ariaLabel !== \"\" && compute === \"name\") {\n\t\t\t\tconsultedNodes.add(current);\n\t\t\t\treturn ariaLabel;\n\t\t\t}\n\n\t\t\t// 2D\n\t\t\tif (!isMarkedPresentational(current)) {\n\t\t\t\tconst elementTextAlternative = computeElementTextAlternative(current);\n\t\t\t\tif (elementTextAlternative !== null) {\n\t\t\t\t\tconsultedNodes.add(current);\n\t\t\t\t\treturn elementTextAlternative;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// special casing, cheating to make tests pass\n\t\t// https://github.com/w3c/accname/issues/67\n\t\tif (hasAnyConcreteRoles(current, [\"menu\"])) {\n\t\t\tconsultedNodes.add(current);\n\t\t\treturn \"\";\n\t\t}\n\n\t\t// 2E\n\t\tif (skipToStep2E || context.isEmbeddedInLabel || context.isReferenced) {\n\t\t\tif (hasAnyConcreteRoles(current, [\"combobox\", \"listbox\"])) {\n\t\t\t\tconsultedNodes.add(current);\n\t\t\t\tconst selectedOptions = querySelectedOptions(current);\n\t\t\t\tif (selectedOptions.length === 0) {\n\t\t\t\t\t// defined per test `name_heading_combobox`\n\t\t\t\t\treturn isHTMLInputElement(current) ? current.value : \"\";\n\t\t\t\t}\n\t\t\t\treturn ArrayFrom(selectedOptions)\n\t\t\t\t\t.map((selectedOption) => {\n\t\t\t\t\t\treturn computeTextAlternative(selectedOption, {\n\t\t\t\t\t\t\tisEmbeddedInLabel: context.isEmbeddedInLabel,\n\t\t\t\t\t\t\tisReferenced: false,\n\t\t\t\t\t\t\trecursion: true,\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.join(\" \");\n\t\t\t}\n\t\t\tif (hasAbstractRole(current, \"range\")) {\n\t\t\t\tconsultedNodes.add(current);\n\t\t\t\tif (current.hasAttribute(\"aria-valuetext\")) {\n\t\t\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- safe due to hasAttribute guard\n\t\t\t\t\treturn current.getAttribute(\"aria-valuetext\")!;\n\t\t\t\t}\n\t\t\t\tif (current.hasAttribute(\"aria-valuenow\")) {\n\t\t\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- safe due to hasAttribute guard\n\t\t\t\t\treturn current.getAttribute(\"aria-valuenow\")!;\n\t\t\t\t}\n\t\t\t\t// Otherwise, use the value as specified by a host language attribute.\n\t\t\t\treturn current.getAttribute(\"value\") || \"\";\n\t\t\t}\n\t\t\tif (hasAnyConcreteRoles(current, [\"textbox\"])) {\n\t\t\t\tconsultedNodes.add(current);\n\t\t\t\treturn getValueOfTextbox(current);\n\t\t\t}\n\t\t}\n\n\t\t// 2F: https://w3c.github.io/accname/#step2F\n\t\tif (\n\t\t\tallowsNameFromContent(current) ||\n\t\t\t(isElement(current) && context.isReferenced) ||\n\t\t\tisNativeHostLanguageTextAlternativeElement(current) ||\n\t\t\tisDescendantOfNativeHostLanguageTextAlternativeElement(current)\n\t\t) {\n\t\t\tconst accumulatedText2F = computeMiscTextAlternative(current, {\n\t\t\t\tisEmbeddedInLabel: context.isEmbeddedInLabel,\n\t\t\t\tisReferenced: false,\n\t\t\t});\n\t\t\tif (accumulatedText2F !== \"\") {\n\t\t\t\tconsultedNodes.add(current);\n\t\t\t\treturn accumulatedText2F;\n\t\t\t}\n\t\t}\n\n\t\tif (current.nodeType === current.TEXT_NODE) {\n\t\t\tconsultedNodes.add(current);\n\t\t\treturn current.textContent || \"\";\n\t\t}\n\n\t\tif (context.recursion) {\n\t\t\tconsultedNodes.add(current);\n\t\t\treturn computeMiscTextAlternative(current, {\n\t\t\t\tisEmbeddedInLabel: context.isEmbeddedInLabel,\n\t\t\t\tisReferenced: false,\n\t\t\t});\n\t\t}\n\n\t\tconst tooltipAttributeValue = computeTooltipAttributeValue(current);\n\t\tif (tooltipAttributeValue !== null) {\n\t\t\tconsultedNodes.add(current);\n\t\t\treturn tooltipAttributeValue;\n\t\t}\n\n\t\t// TODO should this be reachable?\n\t\tconsultedNodes.add(current);\n\t\treturn \"\";\n\t}\n\n\treturn asFlatString(\n\t\tcomputeTextAlternative(root, {\n\t\t\tisEmbeddedInLabel: false,\n\t\t\t// by spec computeAccessibleDescription starts with the referenced elements as roots\n\t\t\tisReferenced: compute === \"description\",\n\t\t\trecursion: false,\n\t\t})\n\t);\n}\n"], "mappings": ";;;;AAGA;AACA;AACA;AAiBgB;AAtBhB;AACA;AACA;;AAmDA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,CAACC,CAAS,EAAc;EAC5C,OAAOA,CAAC,CAACC,IAAI,EAAE,CAACC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQ,CAChBC,IAAU,EACVC,8BAA8D,EAC5C;EAClB,IAAI,CAAC,IAAAC,eAAS,EAACF,IAAI,CAAC,EAAE;IACrB,OAAO,KAAK;EACb;EAEA,IACCA,IAAI,CAACG,YAAY,CAAC,QAAQ,CAAC,IAC3BH,IAAI,CAACI,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAC1C;IACD,OAAO,IAAI;EACZ;EAEA,IAAMC,KAAK,GAAGJ,8BAA8B,CAACD,IAAI,CAAC;EAClD,OACCK,KAAK,CAACC,gBAAgB,CAAC,SAAS,CAAC,KAAK,MAAM,IAC5CD,KAAK,CAACC,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ;AAEnD;;AAEA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAACP,IAAU,EAAW;EACvC,OACC,IAAAQ,yBAAmB,EAACR,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,IACvES,eAAe,CAACT,IAAI,EAAE,OAAO,CAAC;AAEhC;AAEA,SAASS,eAAe,CAACT,IAAU,EAAEU,IAAY,EAAmB;EACnE,IAAI,CAAC,IAAAR,eAAS,EAACF,IAAI,CAAC,EAAE;IACrB,OAAO,KAAK;EACb;EAEA,QAAQU,IAAI;IACX,KAAK,OAAO;MACX,OAAO,IAAAF,yBAAmB,EAACR,IAAI,EAAE,CAChC,OAAO,EACP,aAAa,EACb,WAAW,EACX,QAAQ,EACR,YAAY,CACZ,CAAC;IACH;MACC,MAAM,IAAIW,SAAS,6CACmBD,IAAI,gCACzC;EAAC;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuB,CAC/BC,OAAgB,EAChBC,SAAiB,EACL;EACZ,IAAMC,QAAQ,GAAG,IAAAC,cAAS,EAACH,OAAO,CAACI,gBAAgB,CAACH,SAAS,CAAC,CAAC;EAE/D,IAAAI,iBAAW,EAACL,OAAO,EAAE,WAAW,CAAC,CAACM,OAAO,CAAC,UAACC,IAAI,EAAK;IACnD;IACAL,QAAQ,CAACM,IAAI,CAACC,KAAK,CAACP,QAAQ,EAAE,IAAAC,cAAS,EAACI,IAAI,CAACH,gBAAgB,CAACH,SAAS,CAAC,CAAC,CAAC;EAC3E,CAAC,CAAC;EAEF,OAAOC,QAAQ;AAChB;AAEA,SAASQ,oBAAoB,CAACC,OAAgB,EAAsB;EACnE,IAAI,IAAAC,yBAAmB,EAACD,OAAO,CAAC,EAAE;IACjC;IACA,OACCA,OAAO,CAACE,eAAe,IAAId,uBAAuB,CAACY,OAAO,EAAE,YAAY,CAAC;EAE3E;EACA,OAAOZ,uBAAuB,CAACY,OAAO,EAAE,wBAAwB,CAAC;AAClE;AAEA,SAASG,sBAAsB,CAAC3B,IAAU,EAAmB;EAC5D,OAAO,IAAAQ,yBAAmB,EAACR,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,0CAA0C,CAClD5B,IAAU,EACQ;EAClB,OAAO,IAAA6B,+BAAyB,EAAC7B,IAAI,CAAC;AACvC;;AAEA;AACA;AACA;AACA,SAAS8B,qBAAqB,CAAC9B,IAAU,EAAW;EACnD,OAAO,IAAAQ,yBAAmB,EAACR,IAAI,EAAE,CAChC,QAAQ,EACR,MAAM,EACN,UAAU,EACV,cAAc,EACd,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,MAAM,EACN,UAAU,EACV,kBAAkB,EAClB,eAAe,EACf,QAAQ,EACR,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,KAAK,EACL,SAAS,EACT,UAAU,CACV,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAAS+B,sDAAsD;AAC9D;AACA/B,IAAU,EACA;EACV,OAAO,KAAK;AACb;AAEA,SAASgC,iBAAiB,CAACnB,OAAgB,EAAU;EACpD,IAAI,IAAAoB,wBAAkB,EAACpB,OAAO,CAAC,IAAI,IAAAqB,2BAAqB,EAACrB,OAAO,CAAC,EAAE;IAClE,OAAOA,OAAO,CAACsB,KAAK;EACrB;EACA;EACA,OAAOtB,OAAO,CAACuB,WAAW,IAAI,EAAE;AACjC;AAEA,SAASC,iBAAiB,CAACC,WAAgC,EAAU;EACpE,IAAMC,OAAO,GAAGD,WAAW,CAAChC,gBAAgB,CAAC,SAAS,CAAC;EACvD,IAAI,cAAc,CAACkC,IAAI,CAACD,OAAO,CAAC,EAAE;IACjC,OAAOA,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5B;EACA,OAAO,EAAE;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkB,CAAC7B,OAAgB,EAAW;EACtD,IAAM8B,SAAS,GAAG,IAAAC,kBAAY,EAAC/B,OAAO,CAAC;EAEvC,OACC8B,SAAS,KAAK,QAAQ,IACrBA,SAAS,KAAK,OAAO,IAAI9B,OAAO,CAACT,YAAY,CAAC,MAAM,CAAC,KAAK,QAAS,IACpEuC,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,QAAQ,IACtBA,SAAS,KAAK,UAAU,IACxBA,SAAS,KAAK,QAAQ,IACtBA,SAAS,KAAK,UAAU;AAE1B;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,oBAAoB,CAAChC,OAAgB,EAAkB;EAC/D,IAAI6B,kBAAkB,CAAC7B,OAAO,CAAC,EAAE;IAChC,OAAOA,OAAO;EACf;EACA,IAAIiC,gBAAgC,GAAG,IAAI;EAC3CjC,OAAO,CAACkC,UAAU,CAAC5B,OAAO,CAAC,UAAC6B,SAAS,EAAK;IACzC,IAAIF,gBAAgB,KAAK,IAAI,IAAI,IAAA5C,eAAS,EAAC8C,SAAS,CAAC,EAAE;MACtD,IAAMC,0BAA0B,GAAGJ,oBAAoB,CAACG,SAAS,CAAC;MAClE,IAAIC,0BAA0B,KAAK,IAAI,EAAE;QACxCH,gBAAgB,GAAGG,0BAA0B;MAC9C;IACD;EACD,CAAC,CAAC;EAEF,OAAOH,gBAAgB;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,iBAAiB,CAACC,KAAuB,EAAkB;EACnE,IAAIA,KAAK,CAACC,OAAO,KAAKC,SAAS,EAAE;IAChC,OAAOF,KAAK,CAACC,OAAO;EACrB;EAEA,IAAME,OAAO,GAAGH,KAAK,CAAC/C,YAAY,CAAC,KAAK,CAAC;EACzC,IAAIkD,OAAO,KAAK,IAAI,EAAE;IACrB,OAAOH,KAAK,CAACI,aAAa,CAACC,cAAc,CAACF,OAAO,CAAC;EACnD;EAEA,OAAOT,oBAAoB,CAACM,KAAK,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASM,SAAS,CAAC5C,OAAgB,EAA6B;EAC/D,IAAM6C,cAAc,GAAI7C,OAAO,CAAsB8C,MAEzC;EAEZ,IAAID,cAAc,KAAK,IAAI,EAAE;IAC5B,OAAOA,cAAc;EACtB;EACA,IAAIA,cAAc,KAAKL,SAAS,EAAE;IACjC,OAAO,IAAArC,cAAS,EAAC0C,cAAc,CAAC;EACjC;;EAEA;EACA,IAAI,CAAChB,kBAAkB,CAAC7B,OAAO,CAAC,EAAE;IACjC,OAAO,IAAI;EACZ;EACA,IAAM+C,QAAQ,GAAG/C,OAAO,CAAC0C,aAAa;EAEtC,OAAO,IAAAvC,cAAS,EAAC4C,QAAQ,CAAC3C,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC4C,MAAM,CAAC,UAACV,KAAK,EAAK;IACtE,OAAOD,iBAAiB,CAACC,KAAK,CAAC,KAAKtC,OAAO;EAC5C,CAAC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASiD,eAAe,CAACC,IAAqB,EAAU;EACvD;EACA;EACA;EACA,IAAMC,aAAa,GAAGD,IAAI,CAACC,aAAa,EAAE;EAC1C,IAAIA,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;IAC/B;IACA,OAAO,IAAAjD,cAAS,EAAC+C,IAAI,CAAChB,UAAU,CAAC;EAClC;EACA,OAAOiB,aAAa;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,sBAAsB,CACrC9C,IAAa,EAEJ;EAAA,IADT+C,OAAsC,uEAAG,CAAC,CAAC;EAE3C,IAAMC,cAAc,GAAG,IAAIC,gBAAO,EAAQ;EAE1C,IAAMC,MAAM,GAAG,IAAAC,gBAAU,EAACnD,IAAI,CAAC;EAC/B,uBAUI+C,OAAO,CATVK,OAAO;IAAPA,OAAO,iCAAG,MAAM;IAAA,wBASbL,OAAO,CARVM,mCAAmC;IAAnCA,mCAAmC,sCAAGN,OAAO,CAACO,gBAAgB,KAC7DrB,SAAS;IAAA,wBAOPc,OAAO,CAFVO,gBAAgB;IAAhBA,gBAAgB,sCAAGJ,MAAM,CAACI,gBAAgB,CAACC,IAAI,CAACL,MAAM,CAAC;IAAA,kBAEpDH,OAAO,CADVS,MAAM;IAANA,MAAM,gCAAG,KAAK;;EAGf;EACA,SAASC,0BAA0B,CAClC7E,IAAU,EACV8E,OAA8D,EACrD;IACT,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAI,IAAA7E,eAAS,EAACF,IAAI,CAAC,IAAIyE,mCAAmC,EAAE;MAC3D,IAAMO,YAAY,GAAGN,gBAAgB,CAAC1E,IAAI,EAAE,UAAU,CAAC;MACvD,IAAMiF,aAAa,GAAG5C,iBAAiB,CAAC2C,YAAY,CAAC;MACrDD,eAAe,aAAME,aAAa,cAAIF,eAAe,CAAE;IACxD;;IAEA;IACA;IACA,IAAMhC,UAAU,GAAG,IAAAmC,uBAAiB,EAAClF,IAAI,CAAC,GACvC8D,eAAe,CAAC9D,IAAI,CAAC,GACrB,IAAAgB,cAAS,EAAChB,IAAI,CAAC+C,UAAU,CAAC,CAACoC,MAAM,CAAC,IAAAjE,iBAAW,EAAClB,IAAI,EAAE,WAAW,CAAC,CAAC;IACpE+C,UAAU,CAAC5B,OAAO,CAAC,UAACiE,KAAK,EAAK;MAC7B,IAAMC,MAAM,GAAGnB,sBAAsB,CAACkB,KAAK,EAAE;QAC5CE,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;QAC5CC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACZ,CAAC,CAAC;MACF;MACA;MACA,IAAMC,OAAO,GAAG,IAAAvF,eAAS,EAACkF,KAAK,CAAC,GAC7BV,gBAAgB,CAACU,KAAK,CAAC,CAAC9E,gBAAgB,CAAC,SAAS,CAAC,GACnD,QAAQ;MACX,IAAMoF,SAAS,GAAGD,OAAO,KAAK,QAAQ,GAAG,GAAG,GAAG,EAAE;MACjD;MACAV,eAAe,cAAOW,SAAS,SAAGL,MAAM,SAAGK,SAAS,CAAE;IACvD,CAAC,CAAC;IAEF,IAAI,IAAAxF,eAAS,EAACF,IAAI,CAAC,IAAIyE,mCAAmC,EAAE;MAC3D,IAAMkB,WAAW,GAAGjB,gBAAgB,CAAC1E,IAAI,EAAE,SAAS,CAAC;MACrD,IAAM4F,YAAY,GAAGvD,iBAAiB,CAACsD,WAAW,CAAC;MACnDZ,eAAe,aAAMA,eAAe,cAAIa,YAAY,CAAE;IACvD;IAEA,OAAOb,eAAe,CAAClF,IAAI,EAAE;EAC9B;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,SAASgG,YAAY,CACpBhF,OAAgB,EAChBiF,aAAqB,EACL;IAChB,IAAMC,SAAS,GAAGlF,OAAO,CAACmF,gBAAgB,CAACF,aAAa,CAAC;IACzD,IACCC,SAAS,KAAK,IAAI,IAClB,CAAC3B,cAAc,CAAC6B,GAAG,CAACF,SAAS,CAAC,IAC9BA,SAAS,CAAC5D,KAAK,CAACtC,IAAI,EAAE,KAAK,EAAE,EAC5B;MACDuE,cAAc,CAAC8B,GAAG,CAACH,SAAS,CAAC;MAC7B,OAAOA,SAAS,CAAC5D,KAAK;IACvB;IACA,OAAO,IAAI;EACZ;EAEA,SAASgE,4BAA4B,CAACnG,IAAU,EAAiB;IAChE,IAAI,CAAC,IAAAE,eAAS,EAACF,IAAI,CAAC,EAAE;MACrB,OAAO,IAAI;IACZ;IAEA,OAAO6F,YAAY,CAAC7F,IAAI,EAAE,OAAO,CAAC;EACnC;EAEA,SAASoG,6BAA6B,CAACpG,IAAU,EAAiB;IACjE,IAAI,CAAC,IAAAE,eAAS,EAACF,IAAI,CAAC,EAAE;MACrB,OAAO,IAAI;IACZ;;IAEA;IACA,IAAI,IAAAqG,2BAAqB,EAACrG,IAAI,CAAC,EAAE;MAChCoE,cAAc,CAAC8B,GAAG,CAAClG,IAAI,CAAC;MACxB,IAAMsG,QAAQ,GAAG,IAAAtF,cAAS,EAAChB,IAAI,CAAC+C,UAAU,CAAC;MAC3C,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACrC,MAAM,EAAEsC,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAMnB,KAAK,GAAGkB,QAAQ,CAACC,CAAC,CAAC;QACzB,IAAI,IAAAC,yBAAmB,EAACpB,KAAK,CAAC,EAAE;UAC/B,OAAOlB,sBAAsB,CAACkB,KAAK,EAAE;YACpCE,iBAAiB,EAAE,KAAK;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACZ,CAAC,CAAC;QACH;MACD;IACD,CAAC,MAAM,IAAI,IAAAiB,wBAAkB,EAACzG,IAAI,CAAC,EAAE;MACpC;MACAoE,cAAc,CAAC8B,GAAG,CAAClG,IAAI,CAAC;MACxB,IAAMsG,SAAQ,GAAG,IAAAtF,cAAS,EAAChB,IAAI,CAAC+C,UAAU,CAAC;MAC3C,KAAK,IAAIwD,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGD,SAAQ,CAACrC,MAAM,EAAEsC,EAAC,IAAI,CAAC,EAAE;QAC5C,IAAMnB,MAAK,GAAGkB,SAAQ,CAACC,EAAC,CAAC;QACzB,IAAI,IAAA1E,+BAAyB,EAACuD,MAAK,CAAC,EAAE;UACrC,OAAOlB,sBAAsB,CAACkB,MAAK,EAAE;YACpCE,iBAAiB,EAAE,KAAK;YACxBC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACZ,CAAC,CAAC;QACH;MACD;IACD,CAAC,MAAM,IAAI,IAAAkB,qBAAe,EAAC1G,IAAI,CAAC,EAAE;MACjC;MACAoE,cAAc,CAAC8B,GAAG,CAAClG,IAAI,CAAC;MACxB,IAAMsG,UAAQ,GAAG,IAAAtF,cAAS,EAAChB,IAAI,CAAC+C,UAAU,CAAC;MAC3C,KAAK,IAAIwD,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGD,UAAQ,CAACrC,MAAM,EAAEsC,GAAC,IAAI,CAAC,EAAE;QAC5C,IAAMnB,OAAK,GAAGkB,UAAQ,CAACC,GAAC,CAAC;QACzB,IAAI,IAAAI,uBAAiB,EAACvB,OAAK,CAAC,EAAE;UAC7B,OAAOA,OAAK,CAAChD,WAAW;QACzB;MACD;MACA,OAAO,IAAI;IACZ,CAAC,MAAM,IAAI,IAAAQ,kBAAY,EAAC5C,IAAI,CAAC,KAAK,KAAK,IAAI,IAAA4C,kBAAY,EAAC5C,IAAI,CAAC,KAAK,MAAM,EAAE;MACzE;MACA;MACA,IAAM4G,WAAW,GAAGf,YAAY,CAAC7F,IAAI,EAAE,KAAK,CAAC;MAC7C,IAAI4G,WAAW,KAAK,IAAI,EAAE;QACzB,OAAOA,WAAW;MACnB;IACD,CAAC,MAAM,IAAI,IAAAC,2BAAqB,EAAC7G,IAAI,CAAC,EAAE;MACvC,IAAM8G,aAAa,GAAGjB,YAAY,CAAC7F,IAAI,EAAE,OAAO,CAAC;MACjD,IAAI8G,aAAa,KAAK,IAAI,EAAE;QAC3B,OAAOA,aAAa;MACrB;IACD;IAEA,IACC,IAAA7E,wBAAkB,EAACjC,IAAI,CAAC,KACvBA,IAAI,CAAC+G,IAAI,KAAK,QAAQ,IACtB/G,IAAI,CAAC+G,IAAI,KAAK,QAAQ,IACtB/G,IAAI,CAAC+G,IAAI,KAAK,OAAO,CAAC,EACtB;MACD;MACA,IAAMC,aAAa,GAAGnB,YAAY,CAAC7F,IAAI,EAAE,OAAO,CAAC;MACjD,IAAIgH,aAAa,KAAK,IAAI,EAAE;QAC3B,OAAOA,aAAa;MACrB;;MAEA;MACA,IAAIhH,IAAI,CAAC+G,IAAI,KAAK,QAAQ,EAAE;QAC3B,OAAO,QAAQ;MAChB;MACA;MACA,IAAI/G,IAAI,CAAC+G,IAAI,KAAK,OAAO,EAAE;QAC1B,OAAO,OAAO;MACf;IACD;IAEA,IAAMpD,MAAM,GAAGF,SAAS,CAACzD,IAAI,CAAC;IAC9B,IAAI2D,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3CG,cAAc,CAAC8B,GAAG,CAAClG,IAAI,CAAC;MACxB,OAAO,IAAAgB,cAAS,EAAC2C,MAAM,CAAC,CACtBsD,GAAG,CAAC,UAACpG,OAAO,EAAK;QACjB,OAAOqD,sBAAsB,CAACrD,OAAO,EAAE;UACtCyE,iBAAiB,EAAE,IAAI;UACvBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACZ,CAAC,CAAC;MACH,CAAC,CAAC,CACD3B,MAAM,CAAC,UAACV,KAAK,EAAK;QAClB,OAAOA,KAAK,CAACc,MAAM,GAAG,CAAC;MACxB,CAAC,CAAC,CACDiD,IAAI,CAAC,GAAG,CAAC;IACZ;;IAEA;IACA;IACA;IACA,IAAI,IAAAjF,wBAAkB,EAACjC,IAAI,CAAC,IAAIA,IAAI,CAAC+G,IAAI,KAAK,OAAO,EAAE;MACtD,IAAMH,YAAW,GAAGf,YAAY,CAAC7F,IAAI,EAAE,KAAK,CAAC;MAC7C,IAAI4G,YAAW,KAAK,IAAI,EAAE;QACzB,OAAOA,YAAW;MACnB;MAEA,IAAMO,aAAa,GAAGtB,YAAY,CAAC7F,IAAI,EAAE,OAAO,CAAC;MACjD,IAAImH,aAAa,KAAK,IAAI,EAAE;QAC3B,OAAOA,aAAa;MACrB;;MAEA;MACA,OAAO,cAAc;IACtB;IAEA,IAAI,IAAA3G,yBAAmB,EAACR,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE;MAC1C;MACA,IAAMoH,eAAe,GAAGvC,0BAA0B,CAAC7E,IAAI,EAAE;QACxDsF,iBAAiB,EAAE,KAAK;QACxBC,YAAY,EAAE;MACf,CAAC,CAAC;MACF,IAAI6B,eAAe,KAAK,EAAE,EAAE;QAC3B,OAAOA,eAAe;MACvB;IACD;IAEA,OAAO,IAAI;EACZ;EAEA,SAASlD,sBAAsB,CAC9BmD,OAAa,EACbvC,OAIC,EACQ;IACT,IAAIV,cAAc,CAAC6B,GAAG,CAACoB,OAAO,CAAC,EAAE;MAChC,OAAO,EAAE;IACV;;IAEA;IACA,IACC,CAACzC,MAAM,IACP7E,QAAQ,CAACsH,OAAO,EAAE3C,gBAAgB,CAAC,IACnC,CAACI,OAAO,CAACS,YAAY,EACpB;MACDnB,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;MAC3B,OAAO,EAAE;IACV;;IAEA;IACA,IAAMC,kBAAkB,GAAG,IAAApH,eAAS,EAACmH,OAAO,CAAC,GAC1CA,OAAO,CAACrB,gBAAgB,CAAC,iBAAiB,CAAC,GAC3C,IAAI;IACP;IACA,IAAMuB,aAAa,GAClBD,kBAAkB,KAAK,IAAI,IAAI,CAAClD,cAAc,CAAC6B,GAAG,CAACqB,kBAAkB,CAAC,GACnE,IAAApG,iBAAW,EAACmG,OAAO,EAAE,iBAAiB,CAAC,GACvC,EAAE;IACN,IACC7C,OAAO,KAAK,MAAM,IAClB,CAACM,OAAO,CAACS,YAAY,IACrBgC,aAAa,CAACtD,MAAM,GAAG,CAAC,EACvB;MACD;MACAG,cAAc,CAAC8B,GAAG,CAACoB,kBAAkB,CAAE;MAEvC,OAAOC,aAAa,CAClBN,GAAG,CAAC,UAACpG,OAAO,EAAK;QACjB;QACA,OAAOqD,sBAAsB,CAACrD,OAAO,EAAE;UACtCyE,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;UAC5CC,YAAY,EAAE,IAAI;UAClB;UACA;UACA;UACAC,SAAS,EAAE;QACZ,CAAC,CAAC;MACH,CAAC,CAAC,CACD0B,IAAI,CAAC,GAAG,CAAC;IACZ;;IAEA;IACA;IACA;IACA,IAAMM,YAAY,GACjB1C,OAAO,CAACU,SAAS,IAAIjF,SAAS,CAAC8G,OAAO,CAAC,IAAI7C,OAAO,KAAK,MAAM;IAC9D,IAAI,CAACgD,YAAY,EAAE;MAClB,IAAMC,SAAS,GAAG,CAChB,IAAAvH,eAAS,EAACmH,OAAO,CAAC,IAAIA,OAAO,CAACjH,YAAY,CAAC,YAAY,CAAC,IACzD,EAAE,EACDP,IAAI,EAAE;MACR,IAAI4H,SAAS,KAAK,EAAE,IAAIjD,OAAO,KAAK,MAAM,EAAE;QAC3CJ,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;QAC3B,OAAOI,SAAS;MACjB;;MAEA;MACA,IAAI,CAAC9F,sBAAsB,CAAC0F,OAAO,CAAC,EAAE;QACrC,IAAMK,sBAAsB,GAAGtB,6BAA6B,CAACiB,OAAO,CAAC;QACrE,IAAIK,sBAAsB,KAAK,IAAI,EAAE;UACpCtD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;UAC3B,OAAOK,sBAAsB;QAC9B;MACD;IACD;;IAEA;IACA;IACA,IAAI,IAAAlH,yBAAmB,EAAC6G,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;MAC3CjD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;MAC3B,OAAO,EAAE;IACV;;IAEA;IACA,IAAIG,YAAY,IAAI1C,OAAO,CAACQ,iBAAiB,IAAIR,OAAO,CAACS,YAAY,EAAE;MACtE,IAAI,IAAA/E,yBAAmB,EAAC6G,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE;QAC1DjD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;QAC3B,IAAM3F,eAAe,GAAGH,oBAAoB,CAAC8F,OAAO,CAAC;QACrD,IAAI3F,eAAe,CAACuC,MAAM,KAAK,CAAC,EAAE;UACjC;UACA,OAAO,IAAAhC,wBAAkB,EAACoF,OAAO,CAAC,GAAGA,OAAO,CAAClF,KAAK,GAAG,EAAE;QACxD;QACA,OAAO,IAAAnB,cAAS,EAACU,eAAe,CAAC,CAC/BuF,GAAG,CAAC,UAACU,cAAc,EAAK;UACxB,OAAOzD,sBAAsB,CAACyD,cAAc,EAAE;YAC7CrC,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;YAC5CC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACZ,CAAC,CAAC;QACH,CAAC,CAAC,CACD0B,IAAI,CAAC,GAAG,CAAC;MACZ;MACA,IAAIzG,eAAe,CAAC4G,OAAO,EAAE,OAAO,CAAC,EAAE;QACtCjD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;QAC3B,IAAIA,OAAO,CAAClH,YAAY,CAAC,gBAAgB,CAAC,EAAE;UAC3C;UACA,OAAOkH,OAAO,CAACjH,YAAY,CAAC,gBAAgB,CAAC;QAC9C;QACA,IAAIiH,OAAO,CAAClH,YAAY,CAAC,eAAe,CAAC,EAAE;UAC1C;UACA,OAAOkH,OAAO,CAACjH,YAAY,CAAC,eAAe,CAAC;QAC7C;QACA;QACA,OAAOiH,OAAO,CAACjH,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MAC3C;MACA,IAAI,IAAAI,yBAAmB,EAAC6G,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;QAC9CjD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;QAC3B,OAAOrF,iBAAiB,CAACqF,OAAO,CAAC;MAClC;IACD;;IAEA;IACA,IACCvF,qBAAqB,CAACuF,OAAO,CAAC,IAC7B,IAAAnH,eAAS,EAACmH,OAAO,CAAC,IAAIvC,OAAO,CAACS,YAAa,IAC5C3D,0CAA0C,CAACyF,OAAO,CAAC,IACnDtF,sDAAsD,CAACsF,OAAO,CAAC,EAC9D;MACD,IAAMO,iBAAiB,GAAG/C,0BAA0B,CAACwC,OAAO,EAAE;QAC7D/B,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;QAC5CC,YAAY,EAAE;MACf,CAAC,CAAC;MACF,IAAIqC,iBAAiB,KAAK,EAAE,EAAE;QAC7BxD,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;QAC3B,OAAOO,iBAAiB;MACzB;IACD;IAEA,IAAIP,OAAO,CAACQ,QAAQ,KAAKR,OAAO,CAACS,SAAS,EAAE;MAC3C1D,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;MAC3B,OAAOA,OAAO,CAACjF,WAAW,IAAI,EAAE;IACjC;IAEA,IAAI0C,OAAO,CAACU,SAAS,EAAE;MACtBpB,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;MAC3B,OAAOxC,0BAA0B,CAACwC,OAAO,EAAE;QAC1C/B,iBAAiB,EAAER,OAAO,CAACQ,iBAAiB;QAC5CC,YAAY,EAAE;MACf,CAAC,CAAC;IACH;IAEA,IAAMwC,qBAAqB,GAAG5B,4BAA4B,CAACkB,OAAO,CAAC;IACnE,IAAIU,qBAAqB,KAAK,IAAI,EAAE;MACnC3D,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;MAC3B,OAAOU,qBAAqB;IAC7B;;IAEA;IACA3D,cAAc,CAAC8B,GAAG,CAACmB,OAAO,CAAC;IAC3B,OAAO,EAAE;EACV;EAEA,OAAO1H,YAAY,CAClBuE,sBAAsB,CAAC9C,IAAI,EAAE;IAC5BkE,iBAAiB,EAAE,KAAK;IACxB;IACAC,YAAY,EAAEf,OAAO,KAAK,aAAa;IACvCgB,SAAS,EAAE;EACZ,CAAC,CAAC,CACF;AACF"}