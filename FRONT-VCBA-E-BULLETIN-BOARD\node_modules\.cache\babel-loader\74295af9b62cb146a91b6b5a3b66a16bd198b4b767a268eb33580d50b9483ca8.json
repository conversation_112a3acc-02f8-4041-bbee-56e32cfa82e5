{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"16\",\n  rx: \"2\",\n  key: \"1i8z2d\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"13squh\"\n}], [\"path\", {\n  d: \"M2 2h20\",\n  key: \"1ennik\"\n}]];\nconst AlignVerticalJustifyStart = createLucideIcon(\"align-vertical-justify-start\", __iconNode);\nexport { __iconNode, AlignVerticalJustifyStart as default };\n//# sourceMappingURL=align-vertical-justify-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}