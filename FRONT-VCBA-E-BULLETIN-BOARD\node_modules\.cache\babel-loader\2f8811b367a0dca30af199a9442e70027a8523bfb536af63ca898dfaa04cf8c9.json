{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4.2 4.2A2 2 0 0 0 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 1.82-1.18\",\n  key: \"16swn3\"\n}], [\"path\", {\n  d: \"M21 15.5V6a2 2 0 0 0-2-2H9.5\",\n  key: \"yhw86o\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M3 10h7\",\n  key: \"1wap6i\"\n}], [\"path\", {\n  d: \"M21 10h-5.5\",\n  key: \"quycpq\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst CalendarOff = createLucideIcon(\"calendar-off\", __iconNode);\nexport { __iconNode, CalendarOff as default };\n//# sourceMappingURL=calendar-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}