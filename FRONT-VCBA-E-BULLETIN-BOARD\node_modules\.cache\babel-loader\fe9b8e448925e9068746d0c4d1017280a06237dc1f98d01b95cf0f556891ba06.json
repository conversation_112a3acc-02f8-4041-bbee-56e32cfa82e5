{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5\",\n  key: \"16nib6\"\n}], [\"path\", {\n  d: \"m17 17 5 5\",\n  key: \"p7ous7\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}], [\"path\", {\n  d: \"m22 17-5 5\",\n  key: \"gqnmv0\"\n}], [\"path\", {\n  d: \"M6 12h.01\",\n  key: \"c2rlol\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}]];\nconst BanknoteX = createLucideIcon(\"banknote-x\", __iconNode);\nexport { __iconNode, BanknoteX as default };\n//# sourceMappingURL=banknote-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}