{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 16-5 5\",\n  key: \"j5f7ct\"\n}], [\"path\", {\n  d: \"M11 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6.5\",\n  key: \"7s81lt\"\n}], [\"path\", {\n  d: \"M15.765 22a.5.5 0 0 1-.765-.424V13.38a.5.5 0 0 1 .765-.424l5.878 3.674a1 1 0 0 1 0 1.696z\",\n  key: \"1omb6s\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}]];\nconst ImagePlay = createLucideIcon(\"image-play\", __iconNode);\nexport { __iconNode, ImagePlay as default };\n//# sourceMappingURL=image-play.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}