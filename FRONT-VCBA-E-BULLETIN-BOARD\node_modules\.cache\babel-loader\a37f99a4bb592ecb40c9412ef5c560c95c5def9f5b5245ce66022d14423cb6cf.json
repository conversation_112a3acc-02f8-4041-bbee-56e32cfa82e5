{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v5.632c0 .424-.272.795-.653.982A6 6 0 0 0 6 14c.006 4 3 7 5 8\",\n  key: \"1h8rid\"\n}], [\"path\", {\n  d: \"M10 5H8a2 2 0 0 0 0 4h.68\",\n  key: \"3ezsi6\"\n}], [\"path\", {\n  d: \"M14 2v5.632c0 .424.272.795.652.982A6 6 0 0 1 18 14c0 4-3 7-5 8\",\n  key: \"yt6q09\"\n}], [\"path\", {\n  d: \"M14 5h2a2 2 0 0 1 0 4h-.68\",\n  key: \"8f95yk\"\n}], [\"path\", {\n  d: \"M18 22H6\",\n  key: \"mg6kv4\"\n}], [\"path\", {\n  d: \"M9 2h6\",\n  key: \"1jrp98\"\n}]];\nconst Amphora = createLucideIcon(\"amphora\", __iconNode);\nexport { __iconNode, Amphora as default };\n//# sourceMappingURL=amphora.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}