{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 7.13V6a3 3 0 0 0-5.14-2.1L8 2\",\n  key: \"vl8zik\"\n}], [\"path\", {\n  d: \"M14.12 3.88 16 2\",\n  key: \"qol33r\"\n}], [\"path\", {\n  d: \"M22 13h-4v-2a4 4 0 0 0-4-4h-1.3\",\n  key: \"1ou0bd\"\n}], [\"path\", {\n  d: \"M20.97 5c0 2.1-1.6 3.8-3.5 4\",\n  key: \"18gb23\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M7.7 7.7A4 4 0 0 0 6 11v3a6 6 0 0 0 11.13 3.13\",\n  key: \"1njkjs\"\n}], [\"path\", {\n  d: \"M12 20v-8\",\n  key: \"i3yub9\"\n}], [\"path\", {\n  d: \"M6 13H2\",\n  key: \"82j7cp\"\n}], [\"path\", {\n  d: \"M3 21c0-2.1 1.7-3.9 3.8-4\",\n  key: \"4p0ekp\"\n}]];\nconst BugOff = createLucideIcon(\"bug-off\", __iconNode);\nexport { __iconNode, BugOff as default };\n//# sourceMappingURL=bug-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}