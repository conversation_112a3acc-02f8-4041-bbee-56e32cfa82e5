{"ast": null, "code": "import React,{useState}from'react';import{useLocation,useNavigate}from'react-router-dom';import{useAdminAuth}from'../../../contexts/AdminAuthContext';import NotificationBell from'../NotificationBell';import{BarChart3,Calendar,Newspaper,Users,Settings,School,Menu,User,LogOut,Rss}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminHeader=_ref=>{var _user$firstName2,_user$lastName2,_user$firstName4,_user$lastName4;let{onToggleSidebar}=_ref;const{user,logout}=useAdminAuth();const location=useLocation();const navigate=useNavigate();const[showUserMenu,setShowUserMenu]=useState(false);const handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout failed:',error);}};const getCurrentTime=()=>{return new Date().toLocaleString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});};const getPageInfo=()=>{const path=location.pathname;switch(path){case'/admin':case'/admin/dashboard':return{title:'Dashboard',subtitle:'Overview & Analytics',icon:BarChart3,description:'Welcome to your admin dashboard'};case'/admin/newsfeed':return{title:'Admin Newsfeed',subtitle:'Monitor & Engage',icon:Rss,description:'Monitor announcements, events, and community engagement'};case'/admin/calendar':return{title:'Calendar & Events',subtitle:'Schedule Management',icon:Calendar,description:'Manage academic calendar, events, and announcements'};case'/admin/posts':return{title:'Post Management',subtitle:'Content Publishing',icon:Newspaper,description:'Create and manage announcements, news, and bulletin posts'};case'/admin/student-management':return{title:'Student Management',subtitle:'User Administration',icon:Users,description:'Manage student accounts, profiles, and academic information'};case'/admin/settings':return{title:'Settings',subtitle:'System Configuration',icon:Settings,description:'Manage your profile, system preferences, and security settings'};default:return{title:'Admin Panel',subtitle:'VCBA E-Bulletin Board',icon:School,description:'Villamor College of Business and Arts, Inc.'};}};const pageInfo=getPageInfo();return/*#__PURE__*/_jsxs(\"header\",{style:{background:'white',borderBottom:'1px solid #e8f5e8',padding:'1rem 2rem',display:'flex',alignItems:'center',justifyContent:'space-between',boxShadow:'0 2px 10px rgba(0, 0, 0, 0.05)',position:'sticky',top:0,zIndex:100},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:onToggleSidebar,style:{background:'none',border:'none',padding:'0.5rem',borderRadius:'8px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center',transition:'background-color 0.2s ease',fontSize:'1.25rem'},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:/*#__PURE__*/_jsx(Menu,{size:20,color:\"#2d5016\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.25rem'},children:[/*#__PURE__*/_jsx(pageInfo.icon,{size:24,color:\"#2d5016\"}),/*#__PURE__*/_jsx(\"h1\",{style:{margin:0,color:'#2d5016',fontSize:'1.5rem',fontWeight:'700'},children:pageInfo.title}),/*#__PURE__*/_jsx(\"span\",{style:{background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600'},children:pageInfo.subtitle})]}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:pageInfo.description}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,color:'#9ca3af',fontSize:'0.75rem'},children:getCurrentTime()})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(NotificationBell,{}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowUserMenu(!showUserMenu),style:{background:'none',border:'none',padding:'0.5rem',borderRadius:'12px',cursor:'pointer',display:'flex',alignItems:'center',gap:'0.75rem',transition:'background-color 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{if(!showUserMenu){e.currentTarget.style.background='none';}},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'40px',borderRadius:'50%',background:user!==null&&user!==void 0&&user.profilePicture?'transparent':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'600',fontSize:'1rem',overflow:'hidden',border:'2px solid #e8f5e8'},children:user!==null&&user!==void 0&&user.profilePicture?/*#__PURE__*/_jsx(\"img\",{src:\"http://localhost:5000\".concat(user.profilePicture),alt:\"\".concat(user.firstName,\" \").concat(user.lastName),style:{width:'100%',height:'100%',objectFit:'cover',borderRadius:'50%'},onError:e=>{// Fallback to initials if image fails to load\nconsole.log('Profile picture failed to load, falling back to initials');const target=e.target;target.style.display='none';const parent=target.parentElement;if(parent){var _user$firstName,_user$lastName;parent.style.background='linear-gradient(135deg, #22c55e 0%, #facc15 100%)';parent.innerHTML=\"\".concat((user===null||user===void 0?void 0:(_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName=user.lastName)===null||_user$lastName===void 0?void 0:_user$lastName.charAt(0))||'');}}}):\"\".concat((user===null||user===void 0?void 0:(_user$firstName2=user.firstName)===null||_user$firstName2===void 0?void 0:_user$firstName2.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName2=user.lastName)===null||_user$lastName2===void 0?void 0:_user$lastName2.charAt(0))||'')}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'left'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{color:'#2d5016',fontWeight:'600',fontSize:'0.9rem',lineHeight:'1.2'},children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.75rem',lineHeight:'1.2'},children:(user===null||user===void 0?void 0:user.position)||'Administrator'})]}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#6b7280',fontSize:'0.75rem',transform:showUserMenu?'rotate(180deg)':'rotate(0deg)',transition:'transform 0.2s ease'},children:\"\\u25BC\"})]}),showUserMenu&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'100%',right:0,marginTop:'0.5rem',background:'white',borderRadius:'12px',boxShadow:'0 10px 40px rgba(0, 0, 0, 0.15)',border:'1px solid #e8f5e8',minWidth:'200px',zIndex:1000},children:/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'50px',borderRadius:'50%',background:user!==null&&user!==void 0&&user.profilePicture?'transparent':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'600',fontSize:'1.1rem',overflow:'hidden',border:'2px solid #e8f5e8',flexShrink:0},children:user!==null&&user!==void 0&&user.profilePicture?/*#__PURE__*/_jsx(\"img\",{src:\"http://localhost:5000\".concat(user.profilePicture),alt:\"\".concat(user.firstName,\" \").concat(user.lastName),style:{width:'100%',height:'100%',objectFit:'cover',borderRadius:'50%'},onError:e=>{// Fallback to initials if image fails to load\nconsole.log('Dropdown profile picture failed to load, falling back to initials');const target=e.target;target.style.display='none';const parent=target.parentElement;if(parent){var _user$firstName3,_user$lastName3;parent.style.background='linear-gradient(135deg, #22c55e 0%, #facc15 100%)';parent.innerHTML=\"\".concat((user===null||user===void 0?void 0:(_user$firstName3=user.firstName)===null||_user$firstName3===void 0?void 0:_user$firstName3.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName3=user.lastName)===null||_user$lastName3===void 0?void 0:_user$lastName3.charAt(0))||'');}}}):\"\".concat((user===null||user===void 0?void 0:(_user$firstName4=user.firstName)===null||_user$firstName4===void 0?void 0:_user$firstName4.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName4=user.lastName)===null||_user$lastName4===void 0?void 0:_user$lastName4.charAt(0))||'')}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsxs(\"div\",{style:{color:'#2d5016',fontWeight:'600',marginBottom:'0.25rem',fontSize:'0.95rem'},children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.8rem'},children:user===null||user===void 0?void 0:user.email})]})]}),/*#__PURE__*/_jsx(\"hr\",{style:{border:'none',borderTop:'1px solid #e8f5e8',margin:'1rem 0'}}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowUserMenu(false);navigate('/admin/settings');},style:{width:'100%',background:'none',border:'none',padding:'0.75rem',borderRadius:'8px',cursor:'pointer',textAlign:'left',color:'#374151',fontSize:'0.875rem',marginBottom:'0.5rem',transition:'background-color 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(User,{size:16,color:\"#6b7280\"}),\"Profile Settings\"]})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowUserMenu(false);handleLogout();},style:{width:'100%',background:'none',border:'none',padding:'0.75rem',borderRadius:'8px',cursor:'pointer',textAlign:'left',color:'#dc2626',fontSize:'0.875rem',transition:'background-color 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#fef2f2';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(LogOut,{size:16,color:\"#ef4444\"}),\"Logout\"]})})]})})]})]}),showUserMenu&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,zIndex:999},onClick:()=>setShowUserMenu(false)})]});};export default AdminHeader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}