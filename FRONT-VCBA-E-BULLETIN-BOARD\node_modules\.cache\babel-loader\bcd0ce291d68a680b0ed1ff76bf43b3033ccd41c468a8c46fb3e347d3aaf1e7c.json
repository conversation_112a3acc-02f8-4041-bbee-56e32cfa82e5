{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 6a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-4a2 2 0 0 1-1.6-.8l-1.6-2.13a1 1 0 0 0-1.6 0L9.6 17.2A2 2 0 0 1 8 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z\",\n  key: \"d5y1f\"\n}]];\nconst RectangleGoggles = createLucideIcon(\"rectangle-goggles\", __iconNode);\nexport { __iconNode, RectangleGoggles as default };\n//# sourceMappingURL=rectangle-goggles.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}