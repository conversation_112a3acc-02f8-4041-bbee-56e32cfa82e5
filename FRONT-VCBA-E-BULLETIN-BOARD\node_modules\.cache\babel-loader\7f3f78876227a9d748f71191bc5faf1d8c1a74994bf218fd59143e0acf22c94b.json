{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n    setIsProcessing(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n    } finally {\n      setIsProcessing(false);\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard changes)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    setSuccess(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setIsProcessing(true);\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasChanges = Boolean(preview && selectedFile);\n  const isDisabled = isLoading || isProcessing;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasChanges,\n    selectedFile: selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.name\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: 0,\n          marginBottom: '0.5rem',\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151'\n        },\n        children: \"Profile Picture\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: \"Upload a photo to personalize your profile. Max 2MB. JPEG, PNG, WebP only.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '120px',\n          height: '120px',\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: hasChanges ? '3px solid #22c55e' : isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n          transition: 'border-color 0.2s ease',\n          cursor: isDisabled ? 'not-allowed' : 'pointer',\n          opacity: isDisabled ? 0.6 : 1\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return !isDisabled && ((_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click());\n        },\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(Camera, {\n            size: 28,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), hasChanges && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '-8px',\n            right: '-8px',\n            width: '24px',\n            height: '24px',\n            background: '#22c55e',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '2px solid white',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '8px',\n              height: '8px',\n              background: 'white',\n              borderRadius: '50%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), isDisabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '28px',\n              height: '28px',\n              border: '3px solid #e8f5e8',\n              borderTop: '3px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"wLjFFyL467mYWvk9kOgGnGjzZjI=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "_s", "preview", "setPreview", "selectedFile", "setSelectedFile", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "isProcessing", "setIsProcessing", "fileInputRef", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "size", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "handleSave", "setTimeout", "err", "message", "handleCancel", "current", "value", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemove", "displayImage", "hasImage", "Boolean", "has<PERSON><PERSON><PERSON>", "isDisabled", "console", "log", "name", "children", "style", "marginBottom", "margin", "fontSize", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "opacity", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "click", "src", "alt", "onLoad", "onError", "objectFit", "background", "justifyContent", "top", "left", "right", "bottom", "boxShadow", "borderTop", "animation", "flexDirection", "_fileInputRef$current2", "disabled", "padding", "ref", "accept", "onChange", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Save, RotateCcw } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n\n    setIsProcessing(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n    } finally {\n      setIsProcessing(false);\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard changes)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    setSuccess(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setIsProcessing(true);\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasChanges = Boolean(preview && selectedFile);\n  const isDisabled = isLoading || isProcessing;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasChanges,\n    selectedFile: selectedFile?.name\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      {/* Header */}\n      <div style={{ marginBottom: '1.5rem' }}>\n        <h3 style={{\n          margin: 0,\n          marginBottom: '0.5rem',\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151'\n        }}>\n          Profile Picture\n        </h3>\n        <p style={{\n          margin: 0,\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        }}>\n          Upload a photo to personalize your profile. Max 2MB. JPEG, PNG, WebP only.\n        </p>\n      </div>\n\n      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div\n          style={{\n            position: 'relative',\n            width: '120px',\n            height: '120px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: hasChanges\n              ? '3px solid #22c55e'\n              : isDragOver\n                ? '3px dashed #22c55e'\n                : '3px solid #e8f5e8',\n            transition: 'border-color 0.2s ease',\n            cursor: isDisabled ? 'not-allowed' : 'pointer',\n            opacity: isDisabled ? 0.6 : 1\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => !isDisabled && fileInputRef.current?.click()}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: '2rem'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Upload Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(0, 0, 0, 0.5)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.2s ease'\n            }}\n          >\n            <Camera size={28} color=\"white\" />\n          </div>\n\n          {/* Changes Indicator */}\n          {hasChanges && (\n            <div\n              style={{\n                position: 'absolute',\n                top: '-8px',\n                right: '-8px',\n                width: '24px',\n                height: '24px',\n                background: '#22c55e',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '2px solid white',\n                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <div style={{\n                width: '8px',\n                height: '8px',\n                background: 'white',\n                borderRadius: '50%'\n              }} />\n            </div>\n          )}\n\n          {/* Loading Overlay */}\n          {isDisabled && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              <div\n                style={{\n                  width: '28px',\n                  height: '28px',\n                  border: '3px solid #e8f5e8',\n                  borderTop: '3px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.6 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAyB,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW5F,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAG7B,WAAW,CAAE8B,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,gBAAgB,GAAGpC,WAAW,CAAE8B,IAAU,IAAK;IACnDP,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMY,eAAe,GAAGR,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIO,eAAe,EAAE;MACnBd,QAAQ,CAACc,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBzB,UAAU,EAAAyB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;MACtCzB,eAAe,CAACW,IAAI,CAAC;IACvB,CAAC;IACDQ,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBtB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDe,MAAM,CAACQ,aAAa,CAAChB,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkB,UAAU,GAAG/C,WAAW,CAAC,YAAY;IACzC,IAAI,CAACkB,YAAY,EAAE;IAEnBS,eAAe,CAAC,IAAI,CAAC;IACrBJ,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMd,QAAQ,CAACO,YAAY,CAAC;MAC5BO,UAAU,CAAC,uCAAuC,CAAC;MACnDN,eAAe,CAAC,IAAI,CAAC;MACrB6B,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D,CAAC,SAAS;MACRvB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACT,YAAY,EAAEP,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAGnD,WAAW,CAAC,MAAM;IACrCiB,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBI,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIG,YAAY,CAACwB,OAAO,EAAE;MACxBxB,YAAY,CAACwB,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAIb,CAAsC,IAAK;IAAA,IAAAc,eAAA;IACpE,MAAMzB,IAAI,IAAAyB,eAAA,GAAGd,CAAC,CAACE,MAAM,CAACa,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIzB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAIhB,CAAkB,IAAK;IAC7CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMsC,eAAe,GAAIlB,CAAkB,IAAK;IAC9CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuC,UAAU,GAAInB,CAAkB,IAAK;IACzCA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMS,IAAI,GAAGW,CAAC,CAACoB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAI1B,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BnC,eAAe,CAAC,IAAI,CAAC;IACrBJ,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBR,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMP,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDuB,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D,CAAC,SAAS;MACRvB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMoC,YAAY,GAAG/C,OAAO,IAAIP,cAAc;EAC9C,MAAMuD,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;EACtC,MAAMG,UAAU,GAAGD,OAAO,CAACjD,OAAO,IAAIE,YAAY,CAAC;EACnD,MAAMiD,UAAU,GAAGtD,SAAS,IAAIa,YAAY;;EAE5C;EACA0C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C5D,cAAc;IACdO,OAAO;IACP+C,YAAY;IACZC,QAAQ;IACRE,UAAU;IACVhD,YAAY,EAAEA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoD;EAC9B,CAAC,CAAC;EAEF,oBACE/D,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAyD,QAAA,gBAEpDhE,OAAA;MAAKiE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,gBACrChE,OAAA;QAAIiE,KAAK,EAAE;UACTE,MAAM,EAAE,CAAC;UACTD,YAAY,EAAE,QAAQ;UACtBE,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE;QACT,CAAE;QAAAN,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL1E,OAAA;QAAGiE,KAAK,EAAE;UACRE,MAAM,EAAE,CAAC;UACTC,QAAQ,EAAE,UAAU;UACpBE,KAAK,EAAE;QACT,CAAE;QAAAN,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN1E,OAAA;MAAKiE,KAAK,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,YAAY;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAb,QAAA,gBAErEhE,OAAA;QACEiE,KAAK,EAAE;UACLa,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAExB,UAAU,GACd,mBAAmB,GACnB9C,UAAU,GACR,oBAAoB,GACpB,mBAAmB;UACzBuE,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAEzB,UAAU,GAAG,aAAa,GAAG,SAAS;UAC9C0B,OAAO,EAAE1B,UAAU,GAAG,GAAG,GAAG;QAC9B,CAAE;QACF2B,UAAU,EAAErC,cAAe;QAC3BsC,WAAW,EAAEpC,eAAgB;QAC7BqC,MAAM,EAAEpC,UAAW;QACnBqC,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,OAAM,CAAC/B,UAAU,MAAA+B,qBAAA,GAAItE,YAAY,CAACwB,OAAO,cAAA8C,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;QAAA,CAAC;QAAA5B,QAAA,GAE3DP,QAAQ,gBACPzD,OAAA;UACE6F,GAAG,EAAErC,YAAa;UAClBsC,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMlC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,YAAY,CAAE;UACxEwC,OAAO,EAAG9D,CAAC,IAAK2B,OAAO,CAAC9C,KAAK,CAAC,yBAAyB,EAAEyC,YAAY,EAAEtB,CAAC,CAAE;UAC1E+B,KAAK,EAAE;YACLc,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE;UACb;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF1E,OAAA;UACEiE,KAAK,EAAE;YACLc,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdkB,UAAU,EAAE,mDAAmD;YAC/DvB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBuB,cAAc,EAAE,QAAQ;YACxB7B,KAAK,EAAE,OAAO;YACdD,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAJ,QAAA,EAED7D;QAAY;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD1E,OAAA;UACEiE,KAAK,EAAE;YACLa,QAAQ,EAAE,UAAU;YACpBsB,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTL,UAAU,EAAE,oBAAoB;YAChCvB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBuB,cAAc,EAAE,QAAQ;YACxBb,OAAO,EAAEzE,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3BuE,UAAU,EAAE;UACd,CAAE;UAAApB,QAAA,eAEFhE,OAAA,CAACJ,MAAM;YAACgC,IAAI,EAAE,EAAG;YAAC0C,KAAK,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAGLf,UAAU,iBACT3D,OAAA;UACEiE,KAAK,EAAE;YACLa,QAAQ,EAAE,UAAU;YACpBsB,GAAG,EAAE,MAAM;YACXE,KAAK,EAAE,MAAM;YACbvB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdkB,UAAU,EAAE,SAAS;YACrBjB,YAAY,EAAE,KAAK;YACnBN,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBuB,cAAc,EAAE,QAAQ;YACxBhB,MAAM,EAAE,iBAAiB;YACzBqB,SAAS,EAAE;UACb,CAAE;UAAAxC,QAAA,eAEFhE,OAAA;YAAKiE,KAAK,EAAE;cACVc,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE,KAAK;cACbkB,UAAU,EAAE,OAAO;cACnBjB,YAAY,EAAE;YAChB;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN,EAGAd,UAAU,iBACT5D,OAAA;UACEiE,KAAK,EAAE;YACLa,QAAQ,EAAE,UAAU;YACpBsB,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTL,UAAU,EAAE,0BAA0B;YACtCvB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBuB,cAAc,EAAE;UAClB,CAAE;UAAAnC,QAAA,eAEFhE,OAAA;YACEiE,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,MAAM,EAAE,mBAAmB;cAC3BsB,SAAS,EAAE,mBAAmB;cAC9BxB,YAAY,EAAE,KAAK;cACnByB,SAAS,EAAE;YACb;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN1E,OAAA;QAAKiE,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEgC,aAAa,EAAE,QAAQ;UAAE9B,GAAG,EAAE;QAAO,CAAE;QAAAb,QAAA,gBACpEhE,OAAA;UAAKiE,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAC3ChE,OAAA;YACE0F,OAAO,EAAEA,CAAA;cAAA,IAAAkB,sBAAA;cAAA,QAAAA,sBAAA,GAAMvF,YAAY,CAACwB,OAAO,cAAA+D,sBAAA,uBAApBA,sBAAA,CAAsBhB,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CiB,QAAQ,EAAEvG,SAAU;YACpB2D,KAAK,EAAE;cACLiC,UAAU,EAAE,mDAAmD;cAC/D5B,KAAK,EAAE,OAAO;cACda,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnB6B,OAAO,EAAE,gBAAgB;cACzBzC,UAAU,EAAE,KAAK;cACjBgB,MAAM,EAAE/E,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CgF,OAAO,EAAEhF,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BqE,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,gBAEFhE,OAAA,CAACN,MAAM;cAACkC,IAAI,EAAE;YAAG;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBjB,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,EAERjB,QAAQ,iBACPzD,OAAA;YACE0F,OAAO,EAAEnC,YAAa;YACtBsD,QAAQ,EAAEvG,SAAU;YACpB2D,KAAK,EAAE;cACLiC,UAAU,EAAE,MAAM;cAClBf,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnB6B,OAAO,EAAE,gBAAgB;cACzBzB,MAAM,EAAE/E,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CgE,KAAK,EAAE,SAAS;cAChBgB,OAAO,EAAEhF,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BqE,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,gBAEFhE,OAAA,CAACL,CAAC;cAACiC,IAAI,EAAE;YAAG;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN1E,OAAA;UACE+G,GAAG,EAAE1F,YAAa;UAClBM,IAAI,EAAC,MAAM;UACXqF,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAElE,iBAAkB;UAC5BkB,KAAK,EAAE;YAAEU,OAAO,EAAE;UAAO;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF1E,OAAA;UAAGiE,KAAK,EAAE;YACRG,QAAQ,EAAE,UAAU;YACpBE,KAAK,EAAE,SAAS;YAChBH,MAAM,EAAE;UACV,CAAE;UAAAH,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3D,KAAK,iBACJf,OAAA;MAAKiE,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBJ,OAAO,EAAE,SAAS;QAClBZ,UAAU,EAAE,SAAS;QACrBf,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBX,KAAK,EAAE,SAAS;QAChBK,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAb,QAAA,gBACAhE,OAAA,CAACH,WAAW;QAAC+B,IAAI,EAAE;MAAG;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB3D,KAAK;IAAA;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAzD,OAAO,iBACNjB,OAAA;MAAKiE,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBJ,OAAO,EAAE,SAAS;QAClBZ,UAAU,EAAE,SAAS;QACrBf,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBX,KAAK,EAAE,SAAS;QAChBK,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAb,QAAA,gBACAhE,OAAA,CAACF,WAAW;QAAC8B,IAAI,EAAE;MAAG;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBzD,OAAO;IAAA;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD1E,OAAA;MAAAgE,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClE,EAAA,CA3ZIP,oBAAyD;AAAAkH,EAAA,GAAzDlH,oBAAyD;AA6Z/D,eAAeA,oBAAoB;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}