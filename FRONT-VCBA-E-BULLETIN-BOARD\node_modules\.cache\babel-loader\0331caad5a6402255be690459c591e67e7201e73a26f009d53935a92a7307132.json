{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 16v4a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4\",\n  key: \"1r120k\"\n}], [\"path\", {\n  d: \"M7 2h11v4c0 2-2 2-2 4v1\",\n  key: \"dz1920\"\n}], [\"line\", {\n  x1: \"11\",\n  x2: \"18\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"bi1vpe\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst FlashlightOff = createLucideIcon(\"flashlight-off\", __iconNode);\nexport { __iconNode, FlashlightOff as default };\n//# sourceMappingURL=flashlight-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}