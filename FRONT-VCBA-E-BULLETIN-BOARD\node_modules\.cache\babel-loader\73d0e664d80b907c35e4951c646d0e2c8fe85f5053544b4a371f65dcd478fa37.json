{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"16\",\n  x: \"4\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"1n4dg1\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"9\",\n  x: \"14\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"17khns\"\n}], [\"path\", {\n  d: \"M22 2H2\",\n  key: \"fhrpnj\"\n}]];\nconst AlignStartHorizontal = createLucideIcon(\"align-start-horizontal\", __iconNode);\nexport { __iconNode, AlignStartHorizontal as default };\n//# sourceMappingURL=align-start-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}