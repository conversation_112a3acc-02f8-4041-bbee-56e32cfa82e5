{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"1pf5j1\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M2 15h10\",\n  key: \"jfw4w8\"\n}], [\"path\", {\n  d: \"m9 18 3-3-3-3\",\n  key: \"112psh\"\n}]];\nconst FileInput = createLucideIcon(\"file-input\", __iconNode);\nexport { __iconNode, FileInput as default };\n//# sourceMappingURL=file-input.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}