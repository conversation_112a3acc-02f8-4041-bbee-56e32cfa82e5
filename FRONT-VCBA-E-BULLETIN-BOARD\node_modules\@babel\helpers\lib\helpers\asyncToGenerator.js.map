{"version": 3, "names": ["asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined"], "sources": ["../../src/helpers/asyncToGenerator.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nfunction asyncGeneratorStep<TYield, TReturn>(\n  gen: Generator<TYield, TReturn, Awaited<TYield>>,\n  resolve: (value: TReturn) => void,\n  reject: (error: unknown) => void,\n  _next: (value: Awaited<TYield> | undefined) => void,\n  _throw: (err: unknown) => void,\n  key: \"next\",\n  arg: Awaited<TYield> | undefined,\n): void;\nfunction asyncGeneratorStep<TYield, TReturn>(\n  gen: Generator<TYield, TReturn, Awaited<TYield>>,\n  resolve: (value: TReturn) => void,\n  reject: (error: unknown) => void,\n  _next: (value: Awaited<TYield> | undefined) => void,\n  _throw: (err: unknown) => void,\n  key: \"throw\",\n  arg: unknown,\n): void;\nfunction asyncGeneratorStep<TYield, TReturn>(\n  gen: Generator<TYield, TReturn, Awaited<TYield>>,\n  resolve: (value: TReturn) => void,\n  reject: (error: unknown) => void,\n  _next: (value: Awaited<TYield> | undefined) => void,\n  _throw: (err: unknown) => void,\n  key: \"next\" | \"throw\",\n  arg: any,\n): void {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n\n  if (info.done) {\n    // The \"value\" variable is defined above before the \"info.done\" guard\n    // So TypeScript can't narrowing \"value\" to TReturn here\n    // If we use \"info.value\" here the type is narrowed correctly.\n    // Still requires manual casting for the smaller bundle size.\n    resolve(value as TReturn);\n  } else {\n    // Same as above, TypeScript can't narrow \"value\" to TYield here\n    Promise.resolve(value as TYield).then(_next, _throw);\n  }\n}\n\nexport default function _asyncToGenerator<\n  This,\n  Args extends unknown[],\n  TYield,\n  TReturn,\n>(\n  fn: (\n    this: This,\n    ...args: Args\n  ) => Generator<TYield, TReturn, Awaited<TYield>>,\n): (this: This, ...args: Args) => Promise<TReturn> {\n  return function (this: any) {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      // Casting \"args\" to \"Args\" is intentional since we are trying to avoid the spread operator (not ES5)\n      var gen = fn.apply(self, args as any as Args);\n      function _next(value: Awaited<TYield> | undefined) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err: unknown) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n\n      _next(undefined);\n    });\n  };\n}\n"], "mappings": ";;;;;;AAoBA,SAASA,kBAAkBA,CACzBC,GAAgD,EAChDC,OAAiC,EACjCC,MAAgC,EAChCC,KAAmD,EACnDC,MAA8B,EAC9BC,GAAqB,EACrBC,GAAQ,EACF;EACN,IAAI;IACF,IAAIC,IAAI,GAAGP,GAAG,CAACK,GAAG,CAAC,CAACC,GAAG,CAAC;IACxB,IAAIE,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdP,MAAM,CAACO,KAAK,CAAC;IACb;EACF;EAEA,IAAIF,IAAI,CAACG,IAAI,EAAE;IAKbT,OAAO,CAACO,KAAgB,CAAC;EAC3B,CAAC,MAAM;IAELG,OAAO,CAACV,OAAO,CAACO,KAAe,CAAC,CAACI,IAAI,CAACT,KAAK,EAAEC,MAAM,CAAC;EACtD;AACF;AAEe,SAASS,iBAAiBA,CAMvCC,EAGgD,EACC;EACjD,OAAO,YAAqB;IAC1B,IAAIC,IAAI,GAAG,IAAI;MACbC,IAAI,GAAGC,SAAS;IAClB,OAAO,IAAIN,OAAO,CAAC,UAAUV,OAAO,EAAEC,MAAM,EAAE;MAE5C,IAAIF,GAAG,GAAGc,EAAE,CAACI,KAAK,CAACH,IAAI,EAAEC,IAAmB,CAAC;MAC7C,SAASb,KAAKA,CAACK,KAAkC,EAAE;QACjDT,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAEI,KAAK,CAAC;MACxE;MACA,SAASJ,MAAMA,CAACe,GAAY,EAAE;QAC5BpB,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,OAAO,EAAEe,GAAG,CAAC;MACvE;MAEAhB,KAAK,CAACiB,SAAS,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}