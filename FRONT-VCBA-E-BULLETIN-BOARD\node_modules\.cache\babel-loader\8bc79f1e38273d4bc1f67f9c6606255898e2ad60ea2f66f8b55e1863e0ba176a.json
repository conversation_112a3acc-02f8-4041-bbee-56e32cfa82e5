{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"16\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"z5wdxg\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"9\",\n  x: \"14\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"um7a8w\"\n}], [\"path\", {\n  d: \"M22 22H2\",\n  key: \"19qnx5\"\n}]];\nconst AlignEndHorizontal = createLucideIcon(\"align-end-horizontal\", __iconNode);\nexport { __iconNode, AlignEndHorizontal as default };\n//# sourceMappingURL=align-end-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}