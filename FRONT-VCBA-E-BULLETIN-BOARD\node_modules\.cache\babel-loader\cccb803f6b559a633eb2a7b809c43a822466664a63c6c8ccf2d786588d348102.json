{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"1hopcy\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"m14 14-4 4\",\n  key: \"rymu2i\"\n}], [\"path\", {\n  d: \"m10 14 4 4\",\n  key: \"3sz06r\"\n}]];\nconst CalendarX = createLucideIcon(\"calendar-x\", __iconNode);\nexport { __iconNode, CalendarX as default };\n//# sourceMappingURL=calendar-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}