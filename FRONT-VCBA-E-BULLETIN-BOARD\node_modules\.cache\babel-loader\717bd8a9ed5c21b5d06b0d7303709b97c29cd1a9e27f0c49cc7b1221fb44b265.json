{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{adminNotificationService}from'../../services/notificationService';import{useNotificationNavigation}from'../../hooks/useNotificationNavigation';import{Bell,Megaphone,AlertTriangle,MessageCircle,Heart,Settings,Clock}from'lucide-react';import'../../styles/notificationHighlight.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NotificationBell=_ref=>{let{className=''}=_ref;const[notifications,setNotifications]=useState([]);const[unreadCount,setUnreadCount]=useState(0);const[isOpen,setIsOpen]=useState(false);const[loading,setLoading]=useState(false);// Initialize notification navigation\nconst{handleNotificationClick}=useNotificationNavigation({userRole:'admin',onNavigationStart:notification=>{console.log('🚀 Starting navigation for notification:',notification.title);setIsOpen(false);// Close dropdown when navigating\n},onNavigationComplete:(notification,success)=>{if(success){console.log('✅ Navigation completed successfully');// Update notification as read in local state\nsetNotifications(prev=>prev.map(notif=>notif.notification_id===notification.notification_id?_objectSpread(_objectSpread({},notif),{},{is_read:true}):notif));setUnreadCount(prev=>Math.max(0,prev-1));}else{console.error('❌ Navigation failed');}},onNavigationError:(error,notification)=>{console.error('🚨 Navigation error:',error,'for notification:',notification.title);// Could show user-friendly error message here\n}});// Fetch notifications and unread count\nconst fetchNotifications=async()=>{try{setLoading(true);console.log('🔔 Fetching notifications...');const[notificationsResponse,unreadResponse]=await Promise.all([adminNotificationService.getNotifications({limit:10,sort_by:'created_at',sort_order:'DESC'}),adminNotificationService.getUnreadCount()]);if(notificationsResponse.success&&notificationsResponse.data){setNotifications(notificationsResponse.data.notifications);console.log('✅ Notifications loaded:',notificationsResponse.data.notifications.length);}else{console.warn('⚠️ Notifications response not successful:',notificationsResponse);}if(unreadResponse.success&&unreadResponse.data){setUnreadCount(unreadResponse.data.unreadCount);console.log('✅ Unread count loaded:',unreadResponse.data.unreadCount);}else{console.warn('⚠️ Unread count response not successful:',unreadResponse);}}catch(error){console.error('❌ Error fetching notifications:',error);// Show user-friendly error message\nif(error instanceof Error&&error.message.includes('Network connection failed')){console.error('🚨 Backend connection issue detected. Please check if the server is running.');}}finally{setLoading(false);}};// Handle notification click with navigation\nconst handleNotificationItemClick=async notification=>{try{console.log('🔔 Admin notification clicked:',notification);await handleNotificationClick(notification);}catch(error){console.error('Error handling notification click:',error);}};// Mark notification as read (for mark all read functionality)\nconst markAsRead=async notificationId=>{try{await adminNotificationService.markAsRead(notificationId);setNotifications(prev=>prev.map(notif=>notif.notification_id===notificationId?_objectSpread(_objectSpread({},notif),{},{is_read:true}):notif));setUnreadCount(prev=>Math.max(0,prev-1));}catch(error){console.error('Error marking notification as read:',error);}};// Mark all as read\nconst markAllAsRead=async()=>{try{await adminNotificationService.markAllAsRead();setNotifications(prev=>prev.map(notif=>_objectSpread(_objectSpread({},notif),{},{is_read:true})));setUnreadCount(0);}catch(error){console.error('Error marking all notifications as read:',error);}};// Format time ago\nconst getTimeAgo=dateString=>{const date=new Date(dateString);const now=new Date();const diffInSeconds=Math.floor((now.getTime()-date.getTime())/1000);if(diffInSeconds<60)return'Just now';if(diffInSeconds<3600)return\"\".concat(Math.floor(diffInSeconds/60),\"m ago\");if(diffInSeconds<86400)return\"\".concat(Math.floor(diffInSeconds/3600),\"h ago\");return\"\".concat(Math.floor(diffInSeconds/86400),\"d ago\");};// Get notification icon\nconst getNotificationIcon=type=>{const icons={1:Megaphone,// announcement\n2:AlertTriangle,// alert\n3:MessageCircle,// comment\n4:Heart,// reaction\n5:Settings,// system\n6:Clock// reminder\n};return icons[type]||Bell;};useEffect(()=>{fetchNotifications();// Poll for new notifications every 30 seconds\nconst interval=setInterval(fetchNotifications,30000);return()=>clearInterval(interval);},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"relative \".concat(className),children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsOpen(!isOpen),style:{position:'relative',background:'none',border:'none',cursor:'pointer',padding:'0.5rem',borderRadius:'50%',transition:'background-color 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.backgroundColor='#f3f4f6';},onMouseOut:e=>{e.currentTarget.style.backgroundColor='transparent';},children:[/*#__PURE__*/_jsx(Bell,{size:24,color:\"#2d5016\"}),unreadCount>0&&/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',top:'0.25rem',right:'0.25rem',background:'#ef4444',color:'white',borderRadius:'50%',width:'1.25rem',height:'1.25rem',display:'flex',alignItems:'center',justifyContent:'center',fontSize:'0.75rem',fontWeight:'600'},children:unreadCount>99?'99+':unreadCount})]}),isOpen&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'100%',right:0,width:'400px',maxHeight:'500px',background:'white',border:'1px solid #e5e7eb',borderRadius:'12px',boxShadow:'0 10px 25px rgba(0, 0, 0, 0.15)',zIndex:1000,overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem',borderBottom:'1px solid #e5e7eb',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.125rem',fontWeight:'600',color:'#1f2937'},children:\"Notifications\"}),unreadCount>0&&/*#__PURE__*/_jsx(\"button\",{onClick:markAllAsRead,style:{background:'none',border:'none',color:'#22c55e',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer'},children:\"Mark all read\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'400px',overflowY:'auto'},children:loading?/*#__PURE__*/_jsx(\"div\",{style:{padding:'2rem',textAlign:'center',color:'#6b7280'},children:\"Loading notifications...\"}):notifications.length===0?/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem',textAlign:'center',color:'#6b7280'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'block',marginBottom:'0.5rem'},children:/*#__PURE__*/_jsx(Bell,{size:48,color:\"#6b7280\"})}),\"No notifications yet\"]}):notifications.map(notification=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>handleNotificationItemClick(notification),style:{padding:'1rem',borderBottom:'1px solid #f3f4f6',cursor:'pointer',background:notification.is_read?'white':'#f0f9ff',transition:'all 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.backgroundColor=notification.is_read?'#f9fafb':'#e0f2fe';e.currentTarget.style.transform='translateX(2px)';},onMouseOut:e=>{e.currentTarget.style.backgroundColor=notification.is_read?'white':'#f0f9ff';e.currentTarget.style.transform='translateX(0)';},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.75rem',alignItems:'flex-start'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/React.createElement(getNotificationIcon(notification.notification_type_id),{size:20,color:'#6b7280'})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:notification.is_read?'400':'600',color:'#1f2937',marginBottom:'0.25rem'},children:notification.title}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.8rem',color:'#6b7280',marginBottom:'0.5rem'},children:notification.message}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#9ca3af'},children:getTimeAgo(notification.created_at)})]}),!notification.is_read&&/*#__PURE__*/_jsx(\"div\",{style:{width:'8px',height:'8px',borderRadius:'50%',background:'#22c55e'}})]})},notification.notification_id))}),notifications.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',borderTop:'1px solid #e5e7eb',textAlign:'center'},children:/*#__PURE__*/_jsx(\"button\",{style:{background:'none',border:'none',color:'#22c55e',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer'},children:\"View all notifications\"})})]}),isOpen&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,zIndex:999},onClick:()=>setIsOpen(false)})]});};export default NotificationBell;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}