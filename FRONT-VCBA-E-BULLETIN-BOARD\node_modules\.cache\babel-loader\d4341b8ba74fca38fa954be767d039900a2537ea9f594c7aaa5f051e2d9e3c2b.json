{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\CommentSection.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport { Heart, MessageCircle, AlertCircle, ArrowRight } from 'lucide-react';\nimport ProfileAvatar from '../common/ProfileAvatar';\nimport { shouldShowReplyButton, calculateIndentation, getDepthLimitMessage, getCommentDepthClasses, COMMENT_DEPTH_CONFIG } from '../../utils/commentDepth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CommentItem = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  _s();\n  var _comment$author_name, _comment$author_name2;\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: `comment-${comment.comment_id}`,\n    className: depthClasses.join(' '),\n    style: {\n      marginLeft: `${indentation}px`,\n      marginBottom: '1rem',\n      padding: '1rem',\n      backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb',\n      position: 'relative'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'start',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ProfileAvatar, {\n        profilePicture: comment.author_picture,\n        firstName: (_comment$author_name = comment.author_name) === null || _comment$author_name === void 0 ? void 0 : _comment$author_name.split(' ')[0],\n        lastName: (_comment$author_name2 = comment.author_name) === null || _comment$author_name2 === void 0 ? void 0 : _comment$author_name2.split(' ')[1],\n        size: \"large\",\n        gradientColors: {\n          from: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n          to: comment.user_type === 'admin' ? '#1e40af' : '#16a34a'\n        },\n        style: {\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            },\n            children: comment.is_anonymous ? 'Anonymous' : comment.author_name || 'Unknown User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            },\n            children: formatCommentDate(comment.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#22c55e',\n              color: 'white',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '12px',\n              fontSize: '0.625rem',\n              fontWeight: '500'\n            },\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            margin: '0 0 0.75rem 0',\n            whiteSpace: 'pre-wrap'\n          },\n          children: comment.comment_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReactionToggle,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: hasUserReacted ? '#ef4444' : '#6b7280',\n              fontSize: '0.75rem',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => {\n              if (!hasUserReacted) {\n                e.currentTarget.style.color = '#374151';\n              }\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.color = hasUserReacted ? '#ef4444' : '#6b7280';\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Heart, {\n                size: 14,\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                fill: hasUserReacted ? '#ef4444' : 'none'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), comment.reaction_count || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), canReply ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReplyClick,\n            style: {\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), \"Reply\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Show depth limit message for max depth comments\n          _jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.75rem',\n              color: '#f59e0b',\n              fontStyle: 'italic'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reply depth limit reached\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                // Scroll to top-level comment for continuing thread\n                const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                if (rootElement) {\n                  rootElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center'\n                  });\n                }\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: '#3b82f6',\n                fontSize: '0.75rem',\n                textDecoration: 'underline',\n                padding: '0'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [\"Continue thread\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), showDepthWarning && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.5rem',\n            backgroundColor: '#fef3c7',\n            border: '1px solid #f59e0b',\n            borderRadius: '4px',\n            fontSize: '0.75rem',\n            color: '#92400e',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: depthLimitMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), showReplyForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.75rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(CommentForm, {\n            announcementId: comment.announcement_id,\n            parentCommentId: comment.comment_id,\n            onSubmit: () => setShowReplyForm(false),\n            onCancel: () => setShowReplyForm(false),\n            placeholder: \"Write a reply...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), comment.replies && comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem'\n          },\n          children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(CommentItem, {\n            comment: reply,\n            onReply: onReply,\n            onLike: onLike,\n            onUnlike: onUnlike,\n            currentUserId: currentUserId,\n            currentUserType: currentUserType,\n            depth: depth + 1\n          }, reply.comment_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(CommentItem, \"WiAqH9Fgopt6/hc1TKGdzirvGys=\");\n_c = CommentItem;\nconst CommentForm = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  _s2();\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    createComment\n  } = useComments(announcementId, 'student'); // Student service\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!commentText.trim()) return;\n    try {\n      setIsSubmitting(true);\n      const commentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        is_anonymous: isAnonymous\n      };\n      if (parentCommentId) {\n        commentData.parent_comment_id = parentCommentId;\n      }\n      await createComment(commentData);\n      setCommentText('');\n      setIsAnonymous(false);\n      onSubmit();\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      padding: '1rem',\n      backgroundColor: '#f9fafb',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n      value: commentText,\n      onChange: e => setCommentText(e.target.value),\n      placeholder: placeholder,\n      rows: 3,\n      style: {\n        width: '100%',\n        padding: '0.75rem',\n        border: '1px solid #d1d5db',\n        borderRadius: '6px',\n        fontSize: '0.875rem',\n        outline: 'none',\n        resize: 'vertical',\n        marginBottom: '0.75rem'\n      },\n      onFocus: e => {\n        e.currentTarget.style.borderColor = '#22c55e';\n        e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n      },\n      onBlur: e => {\n        e.currentTarget.style.borderColor = '#d1d5db';\n        e.currentTarget.style.boxShadow = 'none';\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: '0.75rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: isAnonymous,\n          onChange: e => setIsAnonymous(e.target.checked),\n          style: {\n            marginRight: '0.5rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), \"Post anonymously\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '0.5rem'\n        },\n        children: [onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#f3f4f6',\n            color: '#374151',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            fontSize: '0.75rem',\n            fontWeight: '500'\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !commentText.trim() || isSubmitting,\n          style: {\n            padding: '0.5rem 1rem',\n            background: !commentText.trim() || isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n            fontSize: '0.75rem',\n            fontWeight: '600'\n          },\n          children: isSubmitting ? 'Posting...' : 'Post'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 348,\n    columnNumber: 5\n  }, this);\n};\n_s2(CommentForm, \"BbE2Nc5wJlPq9At3ZSo/OwA4M08=\", false, function () {\n  return [useComments];\n});\n_c2 = CommentForm;\nconst CommentSection = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'student'\n}) => {\n  _s3();\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment\n  } = useComments(announcementId, 'student'); // Explicitly use student service\n\n  const handleReply = parentId => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n  if (!allowComments) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic'\n      },\n      children: \"Comments are disabled for this announcement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '600',\n        color: '#374151',\n        marginBottom: '1rem'\n      },\n      children: [\"Comments (\", comments.length, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '0.75rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '6px',\n        marginBottom: '1rem',\n        fontSize: '0.875rem'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(CommentForm, {\n        announcementId: announcementId,\n        onSubmit: refresh,\n        placeholder: \"Share your thoughts...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '1.5rem',\n          height: '1.5rem',\n          border: '2px solid #e5e7eb',\n          borderTop: '2px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 9\n    }, this) : comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      },\n      children: \"No comments yet. Be the first to share your thoughts!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: comments.map(comment => /*#__PURE__*/_jsxDEV(CommentItem, {\n        comment: comment,\n        onReply: handleReply,\n        onLike: likeComment,\n        onUnlike: unlikeComment,\n        currentUserId: currentUserId,\n        currentUserType: currentUserType\n      }, comment.comment_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 476,\n    columnNumber: 5\n  }, this);\n};\n_s3(CommentSection, \"u+7JKYie/psx+0/aFIFQzzd4MZM=\", false, function () {\n  return [useComments];\n});\n_c3 = CommentSection;\nexport default CommentSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CommentItem\");\n$RefreshReg$(_c2, \"CommentForm\");\n$RefreshReg$(_c3, \"CommentSection\");", "map": {"version": 3, "names": ["React", "useState", "useComments", "formatCommentDate", "Heart", "MessageCircle", "AlertCircle", "ArrowRight", "ProfileAvatar", "shouldShowReplyButton", "calculateIndentation", "getDepthLimitMessage", "getCommentDepthClasses", "COMMENT_DEPTH_CONFIG", "jsxDEV", "_jsxDEV", "CommentItem", "comment", "onReply", "onLike", "onUnlike", "currentUserId", "currentUserType", "depth", "_s", "_comment$author_name", "_comment$author_name2", "showReplyForm", "setShowReplyForm", "showDepthWarning", "setShowDepthWarning", "hasUserReacted", "user_reaction", "undefined", "canReply", "indentation", "depthClasses", "depthLimitMessage", "isAtMaxDepth", "MAX_DEPTH", "handleReactionToggle", "comment_id", "handleReplyClick", "setTimeout", "id", "className", "join", "style", "marginLeft", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "position", "children", "display", "alignItems", "gap", "profilePicture", "author_picture", "firstName", "author_name", "split", "lastName", "size", "gradientColors", "from", "user_type", "to", "flexShrink", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "min<PERSON><PERSON><PERSON>", "fontWeight", "color", "fontSize", "is_anonymous", "created_at", "lineHeight", "margin", "whiteSpace", "comment_text", "onClick", "background", "cursor", "transition", "onMouseOver", "e", "currentTarget", "onMouseOut", "fill", "reaction_count", "fontStyle", "rootElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "textDecoration", "marginTop", "CommentForm", "announcementId", "announcement_id", "parentCommentId", "onSubmit", "onCancel", "placeholder", "replies", "length", "map", "reply", "_c", "_s2", "commentText", "setCommentText", "isAnonymous", "setIsAnonymous", "isSubmitting", "setIsSubmitting", "createComment", "handleSubmit", "preventDefault", "trim", "commentData", "parent_comment_id", "error", "console", "value", "onChange", "target", "rows", "width", "outline", "resize", "onFocus", "borderColor", "boxShadow", "onBlur", "justifyContent", "type", "checked", "marginRight", "disabled", "_c2", "CommentSection", "allowComments", "_s3", "comments", "loading", "refresh", "likeComment", "unlikeComment", "handleReply", "parentId", "log", "textAlign", "height", "borderTop", "animation", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/CommentSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport type { Comment, CreateCommentData } from '../../services/commentService';\nimport { Heart, MessageCircle, AlertCircle, ArrowRight } from 'lucide-react';\nimport ProfileAvatar from '../common/ProfileAvatar';\nimport {\n  shouldShowReplyButton,\n  calculateIndentation,\n  getDepthLimitMessage,\n  getCommentDepthClasses,\n  COMMENT_DEPTH_CONFIG\n} from '../../utils/commentDepth';\n\ninterface CommentSectionProps {\n  announcementId: number;\n  allowComments?: boolean;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n}\n\ninterface CommentItemProps {\n  comment: Comment;\n  onReply: (parentId: number) => void;\n  onLike: (id: number) => void;\n  onUnlike: (id: number) => void;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n  depth?: number;\n}\n\nconst CommentItem: React.FC<CommentItemProps> = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n\n  return (\n    <div\n      id={`comment-${comment.comment_id}`}\n      className={depthClasses.join(' ')}\n      style={{\n        marginLeft: `${indentation}px`,\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb',\n        position: 'relative'\n      }}>\n      <div style={{ display: 'flex', alignItems: 'start', gap: '0.75rem' }}>\n        {/* Avatar */}\n        <ProfileAvatar\n          profilePicture={comment.author_picture}\n          firstName={comment.author_name?.split(' ')[0]}\n          lastName={comment.author_name?.split(' ')[1]}\n          size=\"large\"\n          gradientColors={{\n            from: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n            to: comment.user_type === 'admin' ? '#1e40af' : '#16a34a'\n          }}\n          style={{ flexShrink: 0 }}\n        />\n\n        <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Header */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem'\n          }}>\n            <span style={{\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            }}>\n              {comment.is_anonymous ? 'Anonymous' : (comment.author_name || 'Unknown User')}\n            </span>\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            }}>\n              {formatCommentDate(comment.created_at)}\n            </span>\n            {comment.user_type === 'admin' && (\n              <span style={{\n                backgroundColor: '#22c55e',\n                color: 'white',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '12px',\n                fontSize: '0.625rem',\n                fontWeight: '500'\n              }}>\n                Admin\n              </span>\n            )}\n          </div>\n\n          {/* Content */}\n          <p style={{\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            margin: '0 0 0.75rem 0',\n            whiteSpace: 'pre-wrap'\n          }}>\n            {comment.comment_text}\n          </p>\n\n          {/* Actions */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          }}>\n            <button\n              onClick={handleReactionToggle}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                fontSize: '0.75rem',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                transition: 'color 0.2s ease'\n              }}\n              onMouseOver={(e) => {\n                if (!hasUserReacted) {\n                  e.currentTarget.style.color = '#374151';\n                }\n              }}\n              onMouseOut={(e) => {\n                e.currentTarget.style.color = hasUserReacted ? '#ef4444' : '#6b7280';\n              }}\n            >\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <Heart\n                  size={14}\n                  color={hasUserReacted ? '#ef4444' : '#6b7280'}\n                  fill={hasUserReacted ? '#ef4444' : 'none'}\n                />\n                {comment.reaction_count || 0}\n              </span>\n            </button>\n\n            {/* Reply Button with Depth Limiting */}\n            {canReply ? (\n              <button\n                onClick={handleReplyClick}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: '#6b7280',\n                  fontSize: '0.75rem',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\n                onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\n              >\n                <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                  <MessageCircle size={12} />\n                  Reply\n                </span>\n              </button>\n            ) : (\n              // Show depth limit message for max depth comments\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.75rem',\n                color: '#f59e0b',\n                fontStyle: 'italic'\n              }}>\n                <AlertCircle size={12} />\n                <span>Reply depth limit reached</span>\n                <button\n                  onClick={() => {\n                    // Scroll to top-level comment for continuing thread\n                    const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                    if (rootElement) {\n                      rootElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                    }\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#3b82f6',\n                    fontSize: '0.75rem',\n                    textDecoration: 'underline',\n                    padding: '0'\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                    Continue thread\n                    <ArrowRight size={10} />\n                  </span>\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Depth Warning Message */}\n          {showDepthWarning && (\n            <div style={{\n              marginTop: '0.5rem',\n              padding: '0.5rem',\n              backgroundColor: '#fef3c7',\n              border: '1px solid #f59e0b',\n              borderRadius: '4px',\n              fontSize: '0.75rem',\n              color: '#92400e',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <AlertCircle size={14} />\n              <span>{depthLimitMessage}</span>\n            </div>\n          )}\n\n          {/* Reply Form */}\n          {showReplyForm && (\n            <div style={{ marginTop: '0.75rem' }}>\n              <CommentForm\n                announcementId={comment.announcement_id}\n                parentCommentId={comment.comment_id}\n                onSubmit={() => setShowReplyForm(false)}\n                onCancel={() => setShowReplyForm(false)}\n                placeholder=\"Write a reply...\"\n              />\n            </div>\n          )}\n\n          {/* Replies */}\n          {comment.replies && comment.replies.length > 0 && (\n            <div style={{ marginTop: '1rem' }}>\n              {comment.replies.map((reply) => (\n                <CommentItem\n                  key={reply.comment_id}\n                  comment={reply}\n                  onReply={onReply}\n                  onLike={onLike}\n                  onUnlike={onUnlike}\n                  currentUserId={currentUserId}\n                  currentUserType={currentUserType}\n                  depth={depth + 1}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface CommentFormProps {\n  announcementId: number;\n  parentCommentId?: number;\n  onSubmit: () => void;\n  onCancel?: () => void;\n  placeholder?: string;\n}\n\nconst CommentForm: React.FC<CommentFormProps> = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { createComment } = useComments(announcementId, 'student'); // Student service\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!commentText.trim()) return;\n\n    try {\n      setIsSubmitting(true);\n      \n      const commentData: CreateCommentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        is_anonymous: isAnonymous\n      };\n\n      if (parentCommentId) {\n        commentData.parent_comment_id = parentCommentId;\n      }\n\n      await createComment(commentData);\n      setCommentText('');\n      setIsAnonymous(false);\n      onSubmit();\n    } catch (error) {\n      console.error('Error creating comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} style={{\n      padding: '1rem',\n      backgroundColor: '#f9fafb',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb'\n    }}>\n      <textarea\n        value={commentText}\n        onChange={(e) => setCommentText(e.target.value)}\n        placeholder={placeholder}\n        rows={3}\n        style={{\n          width: '100%',\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          outline: 'none',\n          resize: 'vertical',\n          marginBottom: '0.75rem'\n        }}\n        onFocus={(e) => {\n          e.currentTarget.style.borderColor = '#22c55e';\n          e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n        }}\n        onBlur={(e) => {\n          e.currentTarget.style.borderColor = '#d1d5db';\n          e.currentTarget.style.boxShadow = 'none';\n        }}\n      />\n      \n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      }}>\n        <label style={{\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: '0.75rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        }}>\n          <input\n            type=\"checkbox\"\n            checked={isAnonymous}\n            onChange={(e) => setIsAnonymous(e.target.checked)}\n            style={{ marginRight: '0.5rem' }}\n          />\n          Post anonymously\n        </label>\n\n        <div style={{ display: 'flex', gap: '0.5rem' }}>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              style={{\n                padding: '0.5rem 1rem',\n                backgroundColor: '#f3f4f6',\n                color: '#374151',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '0.75rem',\n                fontWeight: '500'\n              }}\n            >\n              Cancel\n            </button>\n          )}\n          <button\n            type=\"submit\"\n            disabled={!commentText.trim() || isSubmitting}\n            style={{\n              padding: '0.5rem 1rem',\n              background: (!commentText.trim() || isSubmitting) ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: (!commentText.trim() || isSubmitting) ? 'not-allowed' : 'pointer',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            }}\n          >\n            {isSubmitting ? 'Posting...' : 'Post'}\n          </button>\n        </div>\n      </div>\n    </form>\n  );\n};\n\nconst CommentSection: React.FC<CommentSectionProps> = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'student'\n}) => {\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment\n  } = useComments(announcementId, 'student'); // Explicitly use student service\n\n  const handleReply = (parentId: number) => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n\n  if (!allowComments) {\n    return (\n      <div style={{\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic'\n      }}>\n        Comments are disabled for this announcement.\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ marginTop: '1.5rem' }}>\n      <h3 style={{\n        fontSize: '1.125rem',\n        fontWeight: '600',\n        color: '#374151',\n        marginBottom: '1rem'\n      }}>\n        Comments ({comments.length})\n      </h3>\n\n      {error && (\n        <div style={{\n          padding: '0.75rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          color: '#dc2626',\n          borderRadius: '6px',\n          marginBottom: '1rem',\n          fontSize: '0.875rem'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* Comment Form */}\n      <div style={{ marginBottom: '1.5rem' }}>\n        <CommentForm\n          announcementId={announcementId}\n          onSubmit={refresh}\n          placeholder=\"Share your thoughts...\"\n        />\n      </div>\n\n      {/* Comments List */}\n      {loading ? (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '2rem'\n        }}>\n          <div style={{\n            width: '1.5rem',\n            height: '1.5rem',\n            border: '2px solid #e5e7eb',\n            borderTop: '2px solid #22c55e',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></div>\n        </div>\n      ) : comments.length === 0 ? (\n        <div style={{\n          textAlign: 'center',\n          padding: '2rem',\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        }}>\n          No comments yet. Be the first to share your thoughts!\n        </div>\n      ) : (\n        <div>\n          {comments.map((comment) => (\n            <CommentItem\n              key={comment.comment_id}\n              comment={comment}\n              onReply={handleReply}\n              onLike={likeComment}\n              onUnlike={unlikeComment}\n              currentUserId={currentUserId}\n              currentUserType={currentUserType}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CommentSection;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,yBAAyB;AAExE,SAASC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAC5E,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SACEC,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,QACf,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBlC,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACbC,eAAe;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM8B,cAAc,GAAGd,OAAO,CAACe,aAAa,KAAKC,SAAS,IAAIhB,OAAO,CAACe,aAAa,KAAK,IAAI;;EAE5F;EACA,MAAME,QAAQ,GAAGzB,qBAAqB,CAACc,KAAK,CAAC;EAC7C,MAAMY,WAAW,GAAGzB,oBAAoB,CAACa,KAAK,CAAC;EAC/C,MAAMa,YAAY,GAAGxB,sBAAsB,CAACW,KAAK,CAAC;EAClD,MAAMc,iBAAiB,GAAG1B,oBAAoB,CAACY,KAAK,CAAC;EACrD,MAAMe,YAAY,GAAGf,KAAK,IAAIV,oBAAoB,CAAC0B,SAAS;EAE5D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIT,cAAc,EAAE;MAClBX,QAAQ,CAACH,OAAO,CAACwB,UAAU,CAAC;IAC9B,CAAC,MAAM;MACLtB,MAAM,CAACF,OAAO,CAACwB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIJ,YAAY,EAAE;MAChBR,mBAAmB,CAAC,IAAI,CAAC;MACzBa,UAAU,CAAC,MAAMb,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM;MACLF,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,oBACEb,OAAA;IACE6B,EAAE,EAAE,WAAW3B,OAAO,CAACwB,UAAU,EAAG;IACpCI,SAAS,EAAET,YAAY,CAACU,IAAI,CAAC,GAAG,CAAE;IAClCC,KAAK,EAAE;MACLC,UAAU,EAAE,GAAGb,WAAW,IAAI;MAC9Bc,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE5B,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;MAChD6B,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eACFxC,OAAA;MAAKgC,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAE;MAAAH,QAAA,gBAEnExC,OAAA,CAACP,aAAa;QACZmD,cAAc,EAAE1C,OAAO,CAAC2C,cAAe;QACvCC,SAAS,GAAApC,oBAAA,GAAER,OAAO,CAAC6C,WAAW,cAAArC,oBAAA,uBAAnBA,oBAAA,CAAqBsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;QAC9CC,QAAQ,GAAAtC,qBAAA,GAAET,OAAO,CAAC6C,WAAW,cAAApC,qBAAA,uBAAnBA,qBAAA,CAAqBqC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;QAC7CE,IAAI,EAAC,OAAO;QACZC,cAAc,EAAE;UACdC,IAAI,EAAElD,OAAO,CAACmD,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;UAC3DC,EAAE,EAAEpD,OAAO,CAACmD,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG;QAClD,CAAE;QACFrB,KAAK,EAAE;UAAEuB,UAAU,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAEF3D,OAAA;QAAKgC,KAAK,EAAE;UAAE4B,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAEnCxC,OAAA;UAAKgC,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbT,YAAY,EAAE;UAChB,CAAE;UAAAM,QAAA,gBACAxC,OAAA;YAAMgC,KAAK,EAAE;cACX8B,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE;YACZ,CAAE;YAAAxB,QAAA,EACCtC,OAAO,CAAC+D,YAAY,GAAG,WAAW,GAAI/D,OAAO,CAAC6C,WAAW,IAAI;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACP3D,OAAA;YAAMgC,KAAK,EAAE;cACX+B,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE;YACZ,CAAE;YAAAxB,QAAA,EACCpD,iBAAiB,CAACc,OAAO,CAACgE,UAAU;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACNzD,OAAO,CAACmD,SAAS,KAAK,OAAO,iBAC5BrD,OAAA;YAAMgC,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1B2B,KAAK,EAAE,OAAO;cACd5B,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,MAAM;cACpB2B,QAAQ,EAAE,UAAU;cACpBF,UAAU,EAAE;YACd,CAAE;YAAAtB,QAAA,EAAC;UAEH;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3D,OAAA;UAAGgC,KAAK,EAAE;YACR+B,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,UAAU;YACpBG,UAAU,EAAE,KAAK;YACjBC,MAAM,EAAE,eAAe;YACvBC,UAAU,EAAE;UACd,CAAE;UAAA7B,QAAA,EACCtC,OAAO,CAACoE;QAAY;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAGJ3D,OAAA;UAAKgC,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,MAAM;YACXqB,QAAQ,EAAE;UACZ,CAAE;UAAAxB,QAAA,gBACAxC,OAAA;YACEuE,OAAO,EAAE9C,oBAAqB;YAC9BO,KAAK,EAAE;cACLS,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACd6B,UAAU,EAAE,MAAM;cAClBlC,MAAM,EAAE,MAAM;cACdmC,MAAM,EAAE,SAAS;cACjBV,KAAK,EAAE/C,cAAc,GAAG,SAAS,GAAG,SAAS;cAC7CgD,QAAQ,EAAE,SAAS;cACnB7B,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,KAAK;cACnBqC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAK;cAClB,IAAI,CAAC5D,cAAc,EAAE;gBACnB4D,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+B,KAAK,GAAG,SAAS;cACzC;YACF,CAAE;YACFe,UAAU,EAAGF,CAAC,IAAK;cACjBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+B,KAAK,GAAG/C,cAAc,GAAG,SAAS,GAAG,SAAS;YACtE,CAAE;YAAAwB,QAAA,eAEFxC,OAAA;cAAMgC,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAU,CAAE;cAAAH,QAAA,gBACrExC,OAAA,CAACX,KAAK;gBACJ6D,IAAI,EAAE,EAAG;gBACTa,KAAK,EAAE/C,cAAc,GAAG,SAAS,GAAG,SAAU;gBAC9C+D,IAAI,EAAE/D,cAAc,GAAG,SAAS,GAAG;cAAO;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACDzD,OAAO,CAAC8E,cAAc,IAAI,CAAC;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGRxC,QAAQ,gBACPnB,OAAA;YACEuE,OAAO,EAAE5C,gBAAiB;YAC1BK,KAAK,EAAE;cACLwC,UAAU,EAAE,MAAM;cAClBlC,MAAM,EAAE,MAAM;cACdmC,MAAM,EAAE,SAAS;cACjBV,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,SAAS;cACnB7B,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,KAAK;cACnBqC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+B,KAAK,GAAG,SAAU;YAC5De,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+B,KAAK,GAAG,SAAU;YAAAvB,QAAA,eAE3DxC,OAAA;cAAMgC,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAU,CAAE;cAAAH,QAAA,gBACrExC,OAAA,CAACV,aAAa;gBAAC4D,IAAI,EAAE;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;UAAA;UAET;UACA3D,OAAA;YAAKgC,KAAK,EAAE;cACVS,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,QAAQ;cACbqB,QAAQ,EAAE,SAAS;cACnBD,KAAK,EAAE,SAAS;cAChBkB,SAAS,EAAE;YACb,CAAE;YAAAzC,QAAA,gBACAxC,OAAA,CAACT,WAAW;cAAC2D,IAAI,EAAE;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzB3D,OAAA;cAAAwC,QAAA,EAAM;YAAyB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC3D,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMW,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWlF,OAAO,CAACwB,UAAU,EAAE,CAAC;gBAC5E,IAAIwD,WAAW,EAAE;kBACfA,WAAW,CAACG,cAAc,CAAC;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC,CAAC;gBACrE;cACF,CAAE;cACFvD,KAAK,EAAE;gBACLwC,UAAU,EAAE,MAAM;gBAClBlC,MAAM,EAAE,MAAM;gBACdmC,MAAM,EAAE,SAAS;gBACjBV,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,SAAS;gBACnBwB,cAAc,EAAE,WAAW;gBAC3BrD,OAAO,EAAE;cACX,CAAE;cAAAK,QAAA,eAEFxC,OAAA;gBAAMgC,KAAK,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAU,CAAE;gBAAAH,QAAA,GAAC,iBAEtE,eAAAxC,OAAA,CAACR,UAAU;kBAAC0D,IAAI,EAAE;gBAAG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL7C,gBAAgB,iBACfd,OAAA;UAAKgC,KAAK,EAAE;YACVyD,SAAS,EAAE,QAAQ;YACnBtD,OAAO,EAAE,QAAQ;YACjBC,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnB2B,QAAQ,EAAE,SAAS;YACnBD,KAAK,EAAE,SAAS;YAChBtB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAH,QAAA,gBACAxC,OAAA,CAACT,WAAW;YAAC2D,IAAI,EAAE;UAAG;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB3D,OAAA;YAAAwC,QAAA,EAAOlB;UAAiB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN,EAGA/C,aAAa,iBACZZ,OAAA;UAAKgC,KAAK,EAAE;YAAEyD,SAAS,EAAE;UAAU,CAAE;UAAAjD,QAAA,eACnCxC,OAAA,CAAC0F,WAAW;YACVC,cAAc,EAAEzF,OAAO,CAAC0F,eAAgB;YACxCC,eAAe,EAAE3F,OAAO,CAACwB,UAAW;YACpCoE,QAAQ,EAAEA,CAAA,KAAMjF,gBAAgB,CAAC,KAAK,CAAE;YACxCkF,QAAQ,EAAEA,CAAA,KAAMlF,gBAAgB,CAAC,KAAK,CAAE;YACxCmF,WAAW,EAAC;UAAkB;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAzD,OAAO,CAAC+F,OAAO,IAAI/F,OAAO,CAAC+F,OAAO,CAACC,MAAM,GAAG,CAAC,iBAC5ClG,OAAA;UAAKgC,KAAK,EAAE;YAAEyD,SAAS,EAAE;UAAO,CAAE;UAAAjD,QAAA,EAC/BtC,OAAO,CAAC+F,OAAO,CAACE,GAAG,CAAEC,KAAK,iBACzBpG,OAAA,CAACC,WAAW;YAEVC,OAAO,EAAEkG,KAAM;YACfjG,OAAO,EAAEA,OAAQ;YACjBC,MAAM,EAAEA,MAAO;YACfC,QAAQ,EAAEA,QAAS;YACnBC,aAAa,EAAEA,aAAc;YAC7BC,eAAe,EAAEA,eAAgB;YACjCC,KAAK,EAAEA,KAAK,GAAG;UAAE,GAPZ4F,KAAK,CAAC1E,UAAU;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQtB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAzQIR,WAAuC;AAAAoG,EAAA,GAAvCpG,WAAuC;AAmR7C,MAAMyF,WAAuC,GAAGA,CAAC;EAC/CC,cAAc;EACdE,eAAe;EACfC,QAAQ;EACRC,QAAQ;EACRC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAM,GAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuH,WAAW,EAAEC,cAAc,CAAC,GAAGxH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyH,YAAY,EAAEC,eAAe,CAAC,GAAG1H,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAE2H;EAAc,CAAC,GAAG1H,WAAW,CAACwG,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;;EAElE,MAAMmB,YAAY,GAAG,MAAOlC,CAAkB,IAAK;IACjDA,CAAC,CAACmC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFJ,eAAe,CAAC,IAAI,CAAC;MAErB,MAAMK,WAA8B,GAAG;QACrCrB,eAAe,EAAED,cAAc;QAC/BrB,YAAY,EAAEiC,WAAW,CAACS,IAAI,CAAC,CAAC;QAChC/C,YAAY,EAAEwC;MAChB,CAAC;MAED,IAAIZ,eAAe,EAAE;QACnBoB,WAAW,CAACC,iBAAiB,GAAGrB,eAAe;MACjD;MAEA,MAAMgB,aAAa,CAACI,WAAW,CAAC;MAChCT,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,KAAK,CAAC;MACrBZ,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRP,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE5G,OAAA;IAAM8F,QAAQ,EAAEgB,YAAa;IAAC9E,KAAK,EAAE;MACnCG,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE;IACV,CAAE;IAAAE,QAAA,gBACAxC,OAAA;MACEqH,KAAK,EAAEd,WAAY;MACnBe,QAAQ,EAAG1C,CAAC,IAAK4B,cAAc,CAAC5B,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;MAChDrB,WAAW,EAAEA,WAAY;MACzBwB,IAAI,EAAE,CAAE;MACRxF,KAAK,EAAE;QACLyF,KAAK,EAAE,MAAM;QACbtF,OAAO,EAAE,SAAS;QAClBG,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,KAAK;QACnB2B,QAAQ,EAAE,UAAU;QACpB0D,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,UAAU;QAClBzF,YAAY,EAAE;MAChB,CAAE;MACF0F,OAAO,EAAGhD,CAAC,IAAK;QACdA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC6F,WAAW,GAAG,SAAS;QAC7CjD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC8F,SAAS,GAAG,kCAAkC;MACtE,CAAE;MACFC,MAAM,EAAGnD,CAAC,IAAK;QACbA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC6F,WAAW,GAAG,SAAS;QAC7CjD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC8F,SAAS,GAAG,MAAM;MAC1C;IAAE;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF3D,OAAA;MAAKgC,KAAK,EAAE;QACVS,OAAO,EAAE,MAAM;QACfuF,cAAc,EAAE,eAAe;QAC/BtF,UAAU,EAAE;MACd,CAAE;MAAAF,QAAA,gBACAxC,OAAA;QAAOgC,KAAK,EAAE;UACZS,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBsB,QAAQ,EAAE,SAAS;UACnBD,KAAK,EAAE,SAAS;UAChBU,MAAM,EAAE;QACV,CAAE;QAAAjC,QAAA,gBACAxC,OAAA;UACEiI,IAAI,EAAC,UAAU;UACfC,OAAO,EAAEzB,WAAY;UACrBa,QAAQ,EAAG1C,CAAC,IAAK8B,cAAc,CAAC9B,CAAC,CAAC2C,MAAM,CAACW,OAAO,CAAE;UAClDlG,KAAK,EAAE;YAAEmG,WAAW,EAAE;UAAS;QAAE;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,oBAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAER3D,OAAA;QAAKgC,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,GAC5CuD,QAAQ,iBACP/F,OAAA;UACEiI,IAAI,EAAC,QAAQ;UACb1D,OAAO,EAAEwB,QAAS;UAClB/D,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBC,eAAe,EAAE,SAAS;YAC1B2B,KAAK,EAAE,SAAS;YAChBzB,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBoC,MAAM,EAAE,SAAS;YACjBT,QAAQ,EAAE,SAAS;YACnBF,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,EACH;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACD3D,OAAA;UACEiI,IAAI,EAAC,QAAQ;UACbG,QAAQ,EAAE,CAAC7B,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAa;UAC9C3E,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBqC,UAAU,EAAG,CAAC+B,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAY,GAAI,SAAS,GAAG,mDAAmD;YACnH5C,KAAK,EAAE,OAAO;YACdzB,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBoC,MAAM,EAAG,CAAC8B,WAAW,CAACS,IAAI,CAAC,CAAC,IAAIL,YAAY,GAAI,aAAa,GAAG,SAAS;YACzE3C,QAAQ,EAAE,SAAS;YACnBF,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,EAEDmE,YAAY,GAAG,YAAY,GAAG;QAAM;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC2C,GAAA,CArIIZ,WAAuC;EAAA,QAUjBvG,WAAW;AAAA;AAAAkJ,GAAA,GAVjC3C,WAAuC;AAuI7C,MAAM4C,cAA6C,GAAGA,CAAC;EACrD3C,cAAc;EACd4C,aAAa,GAAG,IAAI;EACpBjI,aAAa;EACbC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAiI,GAAA;EACJ,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPvB,KAAK;IACLwB,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAG1J,WAAW,CAACwG,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;;EAE5C,MAAMmD,WAAW,GAAIC,QAAgB,IAAK;IACxC;IACA3B,OAAO,CAAC4B,GAAG,CAAC,mBAAmB,EAAED,QAAQ,CAAC;EAC5C,CAAC;EAED,IAAI,CAACR,aAAa,EAAE;IAClB,oBACEvI,OAAA;MAAKgC,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACf8G,SAAS,EAAE,QAAQ;QACnBlF,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,UAAU;QACpBiB,SAAS,EAAE;MACb,CAAE;MAAAzC,QAAA,EAAC;IAEH;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACE3D,OAAA;IAAKgC,KAAK,EAAE;MAAEyD,SAAS,EAAE;IAAS,CAAE;IAAAjD,QAAA,gBAClCxC,OAAA;MAAIgC,KAAK,EAAE;QACTgC,QAAQ,EAAE,UAAU;QACpBF,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAE,SAAS;QAChB7B,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,GAAC,YACS,EAACiG,QAAQ,CAACvC,MAAM,EAAC,GAC7B;IAAA;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEJwD,KAAK,iBACJnH,OAAA;MAAKgC,KAAK,EAAE;QACVG,OAAO,EAAE,SAAS;QAClBC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3ByB,KAAK,EAAE,SAAS;QAChB1B,YAAY,EAAE,KAAK;QACnBH,YAAY,EAAE,MAAM;QACpB8B,QAAQ,EAAE;MACZ,CAAE;MAAAxB,QAAA,EACC2E;IAAK;MAAA3D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3D,OAAA;MAAKgC,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAS,CAAE;MAAAM,QAAA,eACrCxC,OAAA,CAAC0F,WAAW;QACVC,cAAc,EAAEA,cAAe;QAC/BG,QAAQ,EAAE6C,OAAQ;QAClB3C,WAAW,EAAC;MAAwB;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL+E,OAAO,gBACN1I,OAAA;MAAKgC,KAAK,EAAE;QACVS,OAAO,EAAE,MAAM;QACfuF,cAAc,EAAE,QAAQ;QACxB7F,OAAO,EAAE;MACX,CAAE;MAAAK,QAAA,eACAxC,OAAA;QAAKgC,KAAK,EAAE;UACVyF,KAAK,EAAE,QAAQ;UACfyB,MAAM,EAAE,QAAQ;UAChB5G,MAAM,EAAE,mBAAmB;UAC3B6G,SAAS,EAAE,mBAAmB;UAC9B9G,YAAY,EAAE,KAAK;UACnB+G,SAAS,EAAE;QACb;MAAE;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ8E,QAAQ,CAACvC,MAAM,KAAK,CAAC,gBACvBlG,OAAA;MAAKgC,KAAK,EAAE;QACViH,SAAS,EAAE,QAAQ;QACnB9G,OAAO,EAAE,MAAM;QACf4B,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAxB,QAAA,EAAC;IAEH;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAEN3D,OAAA;MAAAwC,QAAA,EACGiG,QAAQ,CAACtC,GAAG,CAAEjG,OAAO,iBACpBF,OAAA,CAACC,WAAW;QAEVC,OAAO,EAAEA,OAAQ;QACjBC,OAAO,EAAE2I,WAAY;QACrB1I,MAAM,EAAEwI,WAAY;QACpBvI,QAAQ,EAAEwI,aAAc;QACxBvI,aAAa,EAAEA,aAAc;QAC7BC,eAAe,EAAEA;MAAgB,GAN5BL,OAAO,CAACwB,UAAU;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOxB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC6E,GAAA,CA9GIF,cAA6C;EAAA,QAa7CnJ,WAAW;AAAA;AAAAkK,GAAA,GAbXf,cAA6C;AAgHnD,eAAeA,cAAc;AAAC,IAAAjC,EAAA,EAAAgC,GAAA,EAAAgB,GAAA;AAAAC,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}