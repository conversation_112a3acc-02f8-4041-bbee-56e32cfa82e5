{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef(null);\n  const dropdownRef = useRef(null);\n  const avatarRef = useRef(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && avatarRef.current && !avatarRef.current.contains(event.target)) {\n        setShowDropdown(false);\n      }\n    };\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n    try {\n      await onUpload(pendingFile);\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = e => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    var _fileInputRef$current;\n    setShowDropdown(false);\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = () => {\n    setShowDropdown(false);\n    setShowConfirmDialog(true);\n  };\n\n  // Confirm remove\n  const confirmRemove = async () => {\n    setShowConfirmDialog(false);\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: avatarRef,\n        style: {\n          position: 'relative',\n          width: `${size}px`,\n          height: `${size}px`,\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n          transition: 'all 0.3s ease',\n          cursor: 'pointer',\n          boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n          transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: `${size * 0.3}px`,\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(34, 197, 94, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.3s ease',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            size: size * 0.2,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.9)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${size * 0.2}px`,\n              height: `${size * 0.2}px`,\n              border: '3px solid #e8f5e8',\n              borderTop: '3px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"hf1+8iBE83NDv1vdNE9j1GHP3a0=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "Upload", "X", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "size", "showActions", "_s", "preview", "setPreview", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showDropdown", "setShowDropdown", "showConfirmDialog", "setShowConfirmDialog", "pendingFile", "setPendingFile", "isHovered", "setIsHovered", "fileInputRef", "dropdownRef", "avatar<PERSON><PERSON>", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "result", "onerror", "readAsDataURL", "handleSave", "setTimeout", "err", "message", "handleCancel", "handleInputChange", "_e$target$files", "files", "value", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleCameraClick", "stopPropagation", "handleChangePhoto", "_fileInputRef$current", "click", "handleRemovePhoto", "confirmRemove", "displayImage", "hasImage", "Boolean", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "children", "style", "position", "display", "ref", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "boxShadow", "transform", "onDragOver", "onDragLeave", "onDrop", "onMouseEnter", "onMouseLeave", "src", "alt", "onLoad", "onError", "objectFit", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "alignItems", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "flexDirection", "gap", "onClick", "_fileInputRef$current2", "disabled", "padding", "handleRemove", "accept", "onChange", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, MoreVertical, Edit3, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n  size?: number; // Size in pixels for the circular avatar\n  showActions?: boolean; // Whether to show action buttons\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [pendingFile, setPendingFile] = useState<File | null>(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const avatarRef = useRef<HTMLDivElement>(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&\n          avatarRef.current && !avatarRef.current.contains(event.target as Node)) {\n        setShowDropdown(false);\n      }\n    };\n\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n\n    try {\n      await onUpload(pendingFile);\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    setShowDropdown(false);\n    fileInputRef.current?.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = () => {\n    setShowDropdown(false);\n    setShowConfirmDialog(true);\n  };\n\n  // Confirm remove\n  const confirmRemove = async () => {\n    setShowConfirmDialog(false);\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      {/* Facebook-style Circular Avatar with Camera Overlay */}\n      <div style={{ position: 'relative', display: 'inline-block' }}>\n        <div\n          ref={avatarRef}\n          style={{\n            position: 'relative',\n            width: `${size}px`,\n            height: `${size}px`,\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n            transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: `${size * 0.3}px`,\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Drag Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(34, 197, 94, 0.8)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.3s ease',\n              borderRadius: '50%'\n            }}\n          >\n            <Upload size={size * 0.2} color=\"white\" />\n          </div>\n\n          {/* Loading Overlay */}\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.9)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: '50%'\n              }}\n            >\n              <div\n                style={{\n                  width: `${size * 0.2}px`,\n                  height: `${size * 0.2}px`,\n                  border: '3px solid #e8f5e8',\n                  borderTop: '3px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.6 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAUC,WAAW,EAAEC,WAAW,QAAqC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAaxG,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,GAAG;EACVC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMoC,YAAY,GAAGnC,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMoC,WAAW,GAAGpC,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMqC,SAAS,GAAGrC,MAAM,CAAiB,IAAI,CAAC;;EAE9C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMoC,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,IAC1EL,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC1Ed,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBgB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC5E;EACF,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMmB,YAAY,GAAG7C,WAAW,CAAE8C,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAAC/B,IAAI,GAAGgC,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,gBAAgB,GAAGnD,WAAW,CAAE8C,IAAU,IAAK;IACnDvB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,KAAK,CAAC;IAEtB,MAAMyB,eAAe,GAAGP,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIM,eAAe,EAAE;MACnB7B,QAAQ,CAAC6B,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBtC,UAAU,EAAAsC,SAAA,GAACD,CAAC,CAACf,MAAM,cAAAgB,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACtC3B,cAAc,CAACe,IAAI,CAAC;IACtB,CAAC;IACDO,MAAM,CAACM,OAAO,GAAG,MAAM;MACrBpC,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACD8B,MAAM,CAACO,aAAa,CAACd,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgB,UAAU,GAAG7D,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC8B,WAAW,EAAE;IAElB,IAAI;MACF,MAAMnB,QAAQ,CAACmB,WAAW,CAAC;MAC3BL,UAAU,CAAC,uCAAuC,CAAC;MACnDM,cAAc,CAAC,IAAI,CAAC;MACpB+B,UAAU,CAAC,MAAMrC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOsC,GAAQ,EAAE;MACjBxC,QAAQ,CAACwC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3D7C,UAAU,CAAC,IAAI,CAAC;MAChBY,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACD,WAAW,EAAEnB,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAMsD,YAAY,GAAGjE,WAAW,CAAC,MAAM;IACrCmB,UAAU,CAAC,IAAI,CAAC;IAChBY,cAAc,CAAC,IAAI,CAAC;IACpBR,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,iBAAiB,GAAIV,CAAsC,IAAK;IAAA,IAAAW,eAAA;IACpE,MAAMrB,IAAI,IAAAqB,eAAA,GAAGX,CAAC,CAACf,MAAM,CAAC2B,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIrB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;IACA;IACAU,CAAC,CAACf,MAAM,CAAC4B,KAAK,GAAG,EAAE;EACrB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAId,CAAkB,IAAK;IAC7CA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBlD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmD,eAAe,GAAIhB,CAAkB,IAAK;IAC9CA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBlD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMoD,UAAU,GAAIjB,CAAkB,IAAK;IACzCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBlD,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMyB,IAAI,GAAGU,CAAC,CAACkB,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC;IACpC,IAAItB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAInB,CAAmB,IAAK;IACjDA,CAAC,CAACoB,eAAe,CAAC,CAAC;IACnBjD,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA,MAAMmD,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC9BnD,eAAe,CAAC,KAAK,CAAC;IACtB,CAAAmD,qBAAA,GAAA5C,YAAY,CAACK,OAAO,cAAAuC,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrD,eAAe,CAAC,KAAK,CAAC;IACtBE,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMoD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCpD,oBAAoB,CAAC,KAAK,CAAC;IAC3BN,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBN,UAAU,CAAC,IAAI,CAAC;IAChBY,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMnB,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDqC,UAAU,CAAC,MAAMrC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOsC,GAAQ,EAAE;MACjBxC,QAAQ,CAACwC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMkB,YAAY,GAAGhE,OAAO,IAAIT,cAAc;EAC9C,MAAM0E,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;EACtC,MAAMG,iBAAiB,GAAGD,OAAO,CAACtD,WAAW,CAAC;;EAE9C;EACAwD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C9E,cAAc;IACdS,OAAO;IACPgE,YAAY;IACZC,QAAQ;IACRE;EACF,CAAC,CAAC;EAEF,oBACE9E,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAA0E,QAAA,gBAEpDjF,OAAA;MAAKkF,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAe,CAAE;MAAAH,QAAA,gBAC5DjF,OAAA;QACEqF,GAAG,EAAExD,SAAU;QACfqD,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBG,KAAK,EAAE,GAAG9E,IAAI,IAAI;UAClB+E,MAAM,EAAE,GAAG/E,IAAI,IAAI;UACnBgF,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE7E,UAAU,GAAG,mBAAmB,GAAGiE,iBAAiB,GAAG,mBAAmB,GAAG,mBAAmB;UACxGa,UAAU,EAAE,eAAe;UAC3BC,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAEpE,SAAS,GAAG,gCAAgC,GAAG,+BAA+B;UACzFqE,SAAS,EAAErE,SAAS,GAAG,aAAa,GAAG;QACzC,CAAE;QACFsE,UAAU,EAAEhC,cAAe;QAC3BiC,WAAW,EAAE/B,eAAgB;QAC7BgC,MAAM,EAAE/B,UAAW;QACnBgC,YAAY,EAAEA,CAAA,KAAMxE,YAAY,CAAC,IAAI,CAAE;QACvCyE,YAAY,EAAEA,CAAA,KAAMzE,YAAY,CAAC,KAAK,CAAE;QAAAuD,QAAA,GAEvCL,QAAQ,gBACP5E,OAAA;UACEoG,GAAG,EAAEzB,YAAa;UAClB0B,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMvB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEL,YAAY,CAAE;UACxE4B,OAAO,EAAGtD,CAAC,IAAK8B,OAAO,CAAChE,KAAK,CAAC,yBAAyB,EAAE4D,YAAY,EAAE1B,CAAC,CAAE;UAC1EiC,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE,OAAO;YAClBb,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAEhF,SAAS,GAAG,iBAAiB,GAAG;UAC1C;QAAE;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF7G,OAAA;UACEkF,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduB,UAAU,EAAE,mDAAmD;YAC/D1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,GAAG3G,IAAI,GAAG,GAAG,IAAI;YAC3BmF,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAEhF,SAAS,GAAG,iBAAiB,GAAG;UAC1C,CAAE;UAAAwD,QAAA,EAED9E;QAAY;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD7G,OAAA;UACEkF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBiC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,wBAAwB;YACpC1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAE3G,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3B8E,UAAU,EAAE,mBAAmB;YAC/BH,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFjF,OAAA,CAACL,MAAM;YAACa,IAAI,EAAEA,IAAI,GAAG,GAAI;YAACyG,KAAK,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EAGLvG,SAAS,iBACRN,OAAA;UACEkF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBiC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,0BAA0B;YACtC1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBxB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFjF,OAAA;YACEkF,KAAK,EAAE;cACLI,KAAK,EAAE,GAAG9E,IAAI,GAAG,GAAG,IAAI;cACxB+E,MAAM,EAAE,GAAG/E,IAAI,GAAG,GAAG,IAAI;cACzBkF,MAAM,EAAE,mBAAmB;cAC3B+B,SAAS,EAAE,mBAAmB;cAC9BjC,YAAY,EAAE,KAAK;cACnBkC,SAAS,EAAE;YACb;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA;QAAKkF,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEuC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAA3C,QAAA,gBACpEjF,OAAA;UAAKkF,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEwC,GAAG,EAAE;UAAO,CAAE;UAAA3C,QAAA,gBAC3CjF,OAAA;YACE6H,OAAO,EAAEA,CAAA;cAAA,IAAAC,sBAAA;cAAA,QAAAA,sBAAA,GAAMnG,YAAY,CAACK,OAAO,cAAA8F,sBAAA,uBAApBA,sBAAA,CAAsBtD,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CuD,QAAQ,EAAEzH,SAAU;YACpB4E,KAAK,EAAE;cACL4B,UAAU,EAAE,mDAAmD;cAC/DG,KAAK,EAAE,OAAO;cACdvB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBwC,OAAO,EAAE,gBAAgB;cACzBd,UAAU,EAAE,KAAK;cACjBtB,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CkH,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B8E,OAAO,EAAE,MAAM;cACf2B,UAAU,EAAE,QAAQ;cACpBa,GAAG,EAAE;YACP,CAAE;YAAA3C,QAAA,gBAEFjF,OAAA,CAACL,MAAM;cAACa,IAAI,EAAE;YAAG;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBjC,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,EAERjC,QAAQ,iBACP5E,OAAA;YACE6H,OAAO,EAAEI,YAAa;YACtBF,QAAQ,EAAEzH,SAAU;YACpB4E,KAAK,EAAE;cACL4B,UAAU,EAAE,MAAM;cAClBpB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBwC,OAAO,EAAE,gBAAgB;cACzBpC,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C2G,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B8E,OAAO,EAAE,MAAM;cACf2B,UAAU,EAAE,QAAQ;cACpBa,GAAG,EAAE;YACP,CAAE;YAAA3C,QAAA,gBAEFjF,OAAA,CAACJ,CAAC;cAACY,IAAI,EAAE;YAAG;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7G,OAAA;UACEqF,GAAG,EAAE1D,YAAa;UAClBgB,IAAI,EAAC,MAAM;UACXuF,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAExE,iBAAkB;UAC5BuB,KAAK,EAAE;YAAEE,OAAO,EAAE;UAAO;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF7G,OAAA;UAAGkF,KAAK,EAAE;YACRiC,QAAQ,EAAE,UAAU;YACpBF,KAAK,EAAE,SAAS;YAChBmB,MAAM,EAAE;UACV,CAAE;UAAAnD,QAAA,EAAC;QAEH;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9F,KAAK,iBACJf,OAAA;MAAKkF,KAAK,EAAE;QACVmD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBlB,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnByB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACf2B,UAAU,EAAE,QAAQ;QACpBa,GAAG,EAAE;MACP,CAAE;MAAA3C,QAAA,gBACAjF,OAAA,CAACH,WAAW;QAACW,IAAI,EAAE;MAAG;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB9F,KAAK;IAAA;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5F,OAAO,iBACNjB,OAAA;MAAKkF,KAAK,EAAE;QACVmD,SAAS,EAAE,MAAM;QACjBL,OAAO,EAAE,SAAS;QAClBlB,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnByB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACf2B,UAAU,EAAE,QAAQ;QACpBa,GAAG,EAAE;MACP,CAAE;MAAA3C,QAAA,gBACAjF,OAAA,CAACF,WAAW;QAACU,IAAI,EAAE;MAAG;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB5F,OAAO;IAAA;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD7G,OAAA;MAAAiF,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnG,EAAA,CA/YIT,oBAAyD;AAAAqI,EAAA,GAAzDrI,oBAAyD;AAiZ/D,eAAeA,oBAAoB;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}