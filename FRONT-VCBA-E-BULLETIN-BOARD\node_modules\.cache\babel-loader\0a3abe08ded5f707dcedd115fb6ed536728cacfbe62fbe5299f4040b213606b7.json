{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 14 4 4-4 4\",\n  key: \"hkso8o\"\n}], [\"path\", {\n  d: \"M20 10a8 8 0 1 0-8 8h8\",\n  key: \"1bik7b\"\n}]];\nconst IterationCcw = createLucideIcon(\"iteration-ccw\", __iconNode);\nexport { __iconNode, IterationCcw as default };\n//# sourceMappingURL=iteration-ccw.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}