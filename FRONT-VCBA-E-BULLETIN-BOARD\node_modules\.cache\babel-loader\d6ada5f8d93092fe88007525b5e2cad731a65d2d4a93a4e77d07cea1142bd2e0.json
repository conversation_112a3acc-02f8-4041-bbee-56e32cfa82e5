{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  key: \"1oijnt\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-.5\",\n  key: \"1but9f\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 1.73 1\",\n  key: \"1p8n7l\"\n}], [\"path\", {\n  d: \"M8 18h1\",\n  key: \"13wk12\"\n}], [\"path\", {\n  d: \"M21.378 12.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"2t3380\"\n}]];\nconst ClipboardPenLine = createLucideIcon(\"clipboard-pen-line\", __iconNode);\nexport { __iconNode, ClipboardPenLine as default };\n//# sourceMappingURL=clipboard-pen-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}