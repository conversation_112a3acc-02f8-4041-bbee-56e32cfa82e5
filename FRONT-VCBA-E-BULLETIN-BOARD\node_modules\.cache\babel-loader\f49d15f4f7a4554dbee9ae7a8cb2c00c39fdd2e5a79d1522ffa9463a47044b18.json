{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ImageLightbox.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Custom hook for CORS-safe image loading\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        console.log('🔄 Fetching image via CORS-safe method:', imagePath);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(imagePath, {\n          method: 'GET',\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component for lightbox\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst SafeImage = ({\n  src,\n  alt,\n  style,\n  onClick,\n  onLoad\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(src);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'rgba(0, 0, 0, 0.3)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'rgba(0, 0, 0, 0.3)',\n        color: 'white',\n        border: '2px dashed rgba(255, 255, 255, 0.3)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#ccc'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    onClick: onClick,\n    onLoad: () => {\n      console.log('✅ Image rendered successfully in lightbox');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad();\n    },\n    draggable: false\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s2(SafeImage, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = SafeImage;\nconst ImageLightbox = ({\n  images,\n  initialIndex,\n  isOpen,\n  onClose,\n  altPrefix = 'Image'\n}) => {\n  _s3();\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [isZoomed, setIsZoomed] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  // Reset state when lightbox opens\n  useEffect(() => {\n    if (isOpen) {\n      setCurrentIndex(initialIndex);\n      setIsZoomed(false);\n      setImageLoaded(false);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, initialIndex]);\n\n  // Keyboard navigation\n  const handleKeyDown = useCallback(e => {\n    if (!isOpen) return;\n    switch (e.key) {\n      case 'Escape':\n        onClose();\n        break;\n      case 'ArrowLeft':\n        goToPrevious();\n        break;\n      case 'ArrowRight':\n        goToNext();\n        break;\n    }\n  }, [isOpen, onClose]);\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleKeyDown]);\n  const goToNext = () => {\n    setCurrentIndex(prev => (prev + 1) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const goToPrevious = () => {\n    setCurrentIndex(prev => (prev - 1 + images.length) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const goToImage = index => {\n    setCurrentIndex(index);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const toggleZoom = () => {\n    setIsZoomed(!isZoomed);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      zIndex: 9999,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '2rem'\n    },\n    onClick: onClose,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClose,\n      style: {\n        position: 'absolute',\n        top: '1rem',\n        right: '1rem',\n        background: 'rgba(255, 255, 255, 0.1)',\n        border: 'none',\n        borderRadius: '50%',\n        width: '48px',\n        height: '48px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease',\n        zIndex: 10001\n      },\n      onMouseEnter: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n      },\n      onMouseLeave: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n      },\n      children: /*#__PURE__*/_jsxDEV(X, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '1rem',\n        left: '1rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '0.5rem 1rem',\n        borderRadius: '20px',\n        fontSize: '0.875rem',\n        fontWeight: '500',\n        zIndex: 10001\n      },\n      children: [currentIndex + 1, \" / \", images.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        maxWidth: '90vw',\n        maxHeight: '80vh',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onClick: e => e.stopPropagation(),\n      children: [images.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: goToPrevious,\n        style: {\n          position: 'absolute',\n          left: '-4rem',\n          top: '50%',\n          transform: 'translateY(-50%)',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '56px',\n          height: '56px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n        },\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          size: 28\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          overflow: isZoomed ? 'auto' : 'hidden',\n          borderRadius: '12px',\n          maxWidth: '100%',\n          maxHeight: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(SafeImage, {\n          src: images[currentIndex],\n          alt: `${altPrefix} ${currentIndex + 1}`,\n          style: {\n            maxWidth: isZoomed ? 'none' : '90vw',\n            maxHeight: isZoomed ? 'none' : '80vh',\n            width: isZoomed ? '150%' : 'auto',\n            height: 'auto',\n            borderRadius: '12px',\n            transition: 'all 0.3s ease',\n            cursor: isZoomed ? 'grab' : 'zoom-in'\n          },\n          onLoad: () => setImageLoaded(true),\n          onClick: toggleZoom\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: goToNext,\n        style: {\n          position: 'absolute',\n          right: '-4rem',\n          top: '50%',\n          transform: 'translateY(-50%)',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '56px',\n          height: '56px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n        },\n        children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n          size: 28\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '2rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'flex',\n        gap: '0.5rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        padding: '1rem',\n        borderRadius: '12px',\n        maxWidth: '90vw',\n        overflowX: 'auto'\n      },\n      onClick: e => e.stopPropagation(),\n      children: images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '60px',\n          height: '60px',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          opacity: index === currentIndex ? 1 : 0.6,\n          border: index === currentIndex ? '2px solid white' : '2px solid transparent',\n          transition: 'all 0.2s ease',\n          overflow: 'hidden'\n        },\n        onClick: () => goToImage(index),\n        onMouseEnter: e => {\n          if (index !== currentIndex) {\n            e.currentTarget.style.opacity = '0.8';\n          }\n        },\n        onMouseLeave: e => {\n          if (index !== currentIndex) {\n            e.currentTarget.style.opacity = '0.6';\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(SafeImage, {\n          src: image,\n          alt: `${altPrefix} ${index + 1} thumbnail`,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s3(ImageLightbox, \"r+YnGvEpx51Jo2FEh2/XXE+LOEU=\");\n_c2 = ImageLightbox;\nexport default ImageLightbox;\nvar _c, _c2;\n$RefreshReg$(_c, \"SafeImage\");\n$RefreshReg$(_c2, \"ImageLightbox\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "X", "ChevronLeft", "ChevronRight", "jsxDEV", "_jsxDEV", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "console", "log", "response", "fetch", "method", "mode", "ok", "Error", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "SafeImage", "src", "alt", "style", "onClick", "onLoad", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "draggable", "_c", "ImageLightbox", "images", "initialIndex", "isOpen", "onClose", "altPrefix", "_s3", "currentIndex", "setCurrentIndex", "isZoomed", "set<PERSON>s<PERSON><PERSON>ed", "imageLoaded", "setImageLoaded", "document", "body", "overflow", "handleKeyDown", "e", "key", "goToPrevious", "goToNext", "addEventListener", "removeEventListener", "prev", "length", "goToImage", "index", "toggleZoom", "position", "top", "left", "right", "bottom", "zIndex", "padding", "background", "borderRadius", "width", "height", "cursor", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "size", "max<PERSON><PERSON><PERSON>", "maxHeight", "stopPropagation", "transform", "gap", "overflowX", "map", "image", "opacity", "objectFit", "_c2", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ImageLightbox.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { getImageUrl } from '../../config/constants';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        console.log('🔄 Fetching image via CORS-safe method:', imagePath);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(imagePath, {\n          method: 'GET',\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component for lightbox\ninterface SafeImageProps {\n  src: string;\n  alt: string;\n  style?: React.CSSProperties;\n  onClick?: () => void;\n  onLoad?: () => void;\n}\n\nconst SafeImage: React.FC<SafeImageProps> = ({ src, alt, style, onClick, onLoad }) => {\n  const { imageUrl, loading, error } = useImageLoader(src);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'rgba(0, 0, 0, 0.3)',\n        color: 'white'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'rgba(0, 0, 0, 0.3)',\n        color: 'white',\n        border: '2px dashed rgba(255, 255, 255, 0.3)'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#ccc' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      onClick={onClick}\n      onLoad={() => {\n        console.log('✅ Image rendered successfully in lightbox');\n        onLoad?.();\n      }}\n      draggable={false}\n    />\n  );\n};\n\ninterface ImageLightboxProps {\n  images: string[];\n  initialIndex: number;\n  isOpen: boolean;\n  onClose: () => void;\n  altPrefix?: string;\n}\n\nconst ImageLightbox: React.FC<ImageLightboxProps> = ({\n  images,\n  initialIndex,\n  isOpen,\n  onClose,\n  altPrefix = 'Image'\n}) => {\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [isZoomed, setIsZoomed] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  // Reset state when lightbox opens\n  useEffect(() => {\n    if (isOpen) {\n      setCurrentIndex(initialIndex);\n      setIsZoomed(false);\n      setImageLoaded(false);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, initialIndex]);\n\n  // Keyboard navigation\n  const handleKeyDown = useCallback((e: KeyboardEvent) => {\n    if (!isOpen) return;\n\n    switch (e.key) {\n      case 'Escape':\n        onClose();\n        break;\n      case 'ArrowLeft':\n        goToPrevious();\n        break;\n      case 'ArrowRight':\n        goToNext();\n        break;\n    }\n  }, [isOpen, onClose]);\n\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleKeyDown]);\n\n  const goToNext = () => {\n    setCurrentIndex((prev) => (prev + 1) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const goToPrevious = () => {\n    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const goToImage = (index: number) => {\n    setCurrentIndex(index);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const toggleZoom = () => {\n    setIsZoomed(!isZoomed);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.9)',\n        zIndex: 9999,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '2rem'\n      }}\n      onClick={onClose}\n    >\n      {/* Close Button */}\n      <button\n        onClick={onClose}\n        style={{\n          position: 'absolute',\n          top: '1rem',\n          right: '1rem',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '48px',\n          height: '48px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n        }}\n      >\n        <X size={24} />\n      </button>\n\n      {/* Image Counter */}\n      <div\n        style={{\n          position: 'absolute',\n          top: '1rem',\n          left: '1rem',\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          padding: '0.5rem 1rem',\n          borderRadius: '20px',\n          fontSize: '0.875rem',\n          fontWeight: '500',\n          zIndex: 10001\n        }}\n      >\n        {currentIndex + 1} / {images.length}\n      </div>\n\n\n\n      {/* Main Image Container */}\n      <div\n        style={{\n          position: 'relative',\n          maxWidth: '90vw',\n          maxHeight: '80vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Previous Button */}\n        {images.length > 1 && (\n          <button\n            onClick={goToPrevious}\n            style={{\n              position: 'absolute',\n              left: '-4rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              borderRadius: '50%',\n              width: '56px',\n              height: '56px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              zIndex: 10001\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n            }}\n          >\n            <ChevronLeft size={28} />\n          </button>\n        )}\n\n        {/* Main Image */}\n        <div\n          style={{\n            position: 'relative',\n            overflow: isZoomed ? 'auto' : 'hidden',\n            borderRadius: '12px',\n            maxWidth: '100%',\n            maxHeight: '100%'\n          }}\n        >\n          <SafeImage\n            src={images[currentIndex]}\n            alt={`${altPrefix} ${currentIndex + 1}`}\n            style={{\n              maxWidth: isZoomed ? 'none' : '90vw',\n              maxHeight: isZoomed ? 'none' : '80vh',\n              width: isZoomed ? '150%' : 'auto',\n              height: 'auto',\n              borderRadius: '12px',\n              transition: 'all 0.3s ease',\n              cursor: isZoomed ? 'grab' : 'zoom-in'\n            }}\n            onLoad={() => setImageLoaded(true)}\n            onClick={toggleZoom}\n          />\n        </div>\n\n        {/* Next Button */}\n        {images.length > 1 && (\n          <button\n            onClick={goToNext}\n            style={{\n              position: 'absolute',\n              right: '-4rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              borderRadius: '50%',\n              width: '56px',\n              height: '56px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              zIndex: 10001\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n            }}\n          >\n            <ChevronRight size={28} />\n          </button>\n        )}\n      </div>\n\n      {/* Thumbnail Navigation */}\n      {images.length > 1 && (\n        <div\n          style={{\n            position: 'absolute',\n            bottom: '2rem',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            display: 'flex',\n            gap: '0.5rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            padding: '1rem',\n            borderRadius: '12px',\n            maxWidth: '90vw',\n            overflowX: 'auto'\n          }}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {images.map((image, index) => (\n            <div\n              key={index}\n              style={{\n                width: '60px',\n                height: '60px',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                opacity: index === currentIndex ? 1 : 0.6,\n                border: index === currentIndex ? '2px solid white' : '2px solid transparent',\n                transition: 'all 0.2s ease',\n                overflow: 'hidden'\n              }}\n              onClick={() => goToImage(index)}\n              onMouseEnter={(e) => {\n                if (index !== currentIndex) {\n                  e.currentTarget.style.opacity = '0.8';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (index !== currentIndex) {\n                  e.currentTarget.style.opacity = '0.6';\n                }\n              }}\n            >\n              <SafeImage\n                src={image}\n                alt={`${altPrefix} ${index + 1} thumbnail`}\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImageLightbox;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,CAAC,EAAEC,WAAW,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5D;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACFE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEV,SAAS,CAAC;;QAEjE;QACA,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAACZ,SAAS,EAAE;UACtCa,MAAM,EAAE,KAAK;UACbC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,QAAQL,QAAQ,CAACM,MAAM,KAAKN,QAAQ,CAACO,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3ChB,WAAW,CAACiB,SAAS,CAAC;QAEtBX,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOa,GAAG,EAAE;QACZd,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEiB,GAAG,CAAC;QAC3ChB,QAAQ,CAACgB,GAAG,YAAYP,KAAK,GAAGO,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRnB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAACuB,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAACxB,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CAvDMF,cAAc;AAgEpB,MAAM4B,SAAmC,GAAGA,CAAC;EAAEC,GAAG;EAAEC,GAAG;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,GAAA;EACpF,MAAM;IAAE/B,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAAC6B,GAAG,CAAC;EAExD,IAAIxB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKgC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRI,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,oBAAoB;QACrCC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,eACAzC,OAAA;QAAKgC,KAAK,EAAE;UAAEU,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCzC,OAAA;UAAKgC,KAAK,EAAE;YAAEW,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnEhD,OAAA;UAAKgC,KAAK,EAAE;YAAEY,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIxC,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEJ,OAAA;MAAKgC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRI,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,oBAAoB;QACrCC,KAAK,EAAE,OAAO;QACdS,MAAM,EAAE;MACV,CAAE;MAAAR,QAAA,eACAzC,OAAA;QAAKgC,KAAK,EAAE;UAAEU,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCzC,OAAA;UAAKgC,KAAK,EAAE;YAAEW,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrEhD,OAAA;UAAKgC,KAAK,EAAE;YAAEY,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/ExC,KAAK,iBACJR,OAAA;UAAKgC,KAAK,EAAE;YAAEY,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EACtEjC;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhD,OAAA;IACE8B,GAAG,EAAE1B,QAAS;IACd2B,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,OAAO,EAAEA,OAAQ;IACjBC,MAAM,EAAEA,CAAA,KAAM;MACZvB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDsB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC;IACZ,CAAE;IACFkB,SAAS,EAAE;EAAM;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEN,CAAC;AAACb,GAAA,CA1DIN,SAAmC;EAAA,QACF5B,cAAc;AAAA;AAAAoD,EAAA,GAD/CxB,SAAmC;AAoEzC,MAAMyB,aAA2C,GAAGA,CAAC;EACnDC,MAAM;EACNC,YAAY;EACZC,MAAM;EACNC,OAAO;EACPC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC+D,YAAY,CAAC;EAC9D,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,IAAI+D,MAAM,EAAE;MACVK,eAAe,CAACN,YAAY,CAAC;MAC7BQ,WAAW,CAAC,KAAK,CAAC;MAClBE,cAAc,CAAC,KAAK,CAAC;MACrBC,QAAQ,CAACC,IAAI,CAACpC,KAAK,CAACqC,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLF,QAAQ,CAACC,IAAI,CAACpC,KAAK,CAACqC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXF,QAAQ,CAACC,IAAI,CAACpC,KAAK,CAACqC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACZ,MAAM,EAAED,YAAY,CAAC,CAAC;;EAE1B;EACA,MAAMc,aAAa,GAAG3E,WAAW,CAAE4E,CAAgB,IAAK;IACtD,IAAI,CAACd,MAAM,EAAE;IAEb,QAAQc,CAAC,CAACC,GAAG;MACX,KAAK,QAAQ;QACXd,OAAO,CAAC,CAAC;QACT;MACF,KAAK,WAAW;QACde,YAAY,CAAC,CAAC;QACd;MACF,KAAK,YAAY;QACfC,QAAQ,CAAC,CAAC;QACV;IACJ;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErBhE,SAAS,CAAC,MAAM;IACdyE,QAAQ,CAACQ,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAMH,QAAQ,CAACS,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;EACrE,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrBZ,eAAe,CAAEe,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAItB,MAAM,CAACuB,MAAM,CAAC;IACrDZ,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBX,eAAe,CAAEe,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGtB,MAAM,CAACuB,MAAM,IAAIvB,MAAM,CAACuB,MAAM,CAAC;IACrEZ,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMe,SAAS,GAAIC,KAAa,IAAK;IACnClB,eAAe,CAACkB,KAAK,CAAC;IACtBd,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvBjB,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;EAED,IAAI,CAACN,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEzD,OAAA;IACEgC,KAAK,EAAE;MACLkD,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACT/C,eAAe,EAAE,oBAAoB;MACrCgD,MAAM,EAAE,IAAI;MACZnD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBkD,OAAO,EAAE;IACX,CAAE;IACFvD,OAAO,EAAEyB,OAAQ;IAAAjB,QAAA,gBAGjBzC,OAAA;MACEiC,OAAO,EAAEyB,OAAQ;MACjB1B,KAAK,EAAE;QACLkD,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbI,UAAU,EAAE,0BAA0B;QACtCxC,MAAM,EAAE,MAAM;QACdyC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdxD,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBE,KAAK,EAAE,OAAO;QACdqD,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,eAAe;QAC3BP,MAAM,EAAE;MACV,CAAE;MACFQ,YAAY,EAAGxB,CAAC,IAAK;QACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MACFQ,YAAY,EAAG1B,CAAC,IAAK;QACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MAAAhD,QAAA,eAEFzC,OAAA,CAACJ,CAAC;QAACsG,IAAI,EAAE;MAAG;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGThD,OAAA;MACEgC,KAAK,EAAE;QACLkD,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZK,UAAU,EAAE,oBAAoB;QAChCjD,KAAK,EAAE,OAAO;QACdgD,OAAO,EAAE,aAAa;QACtBE,YAAY,EAAE,MAAM;QACpB9C,QAAQ,EAAE,UAAU;QACpBM,UAAU,EAAE,KAAK;QACjBqC,MAAM,EAAE;MACV,CAAE;MAAA9C,QAAA,GAEDoB,YAAY,GAAG,CAAC,EAAC,KAAG,EAACN,MAAM,CAACuB,MAAM;IAAA;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAKNhD,OAAA;MACEgC,KAAK,EAAE;QACLkD,QAAQ,EAAE,UAAU;QACpBiB,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,MAAM;QACjBhE,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFL,OAAO,EAAGsC,CAAC,IAAKA,CAAC,CAAC8B,eAAe,CAAC,CAAE;MAAA5D,QAAA,GAGnCc,MAAM,CAACuB,MAAM,GAAG,CAAC,iBAChB9E,OAAA;QACEiC,OAAO,EAAEwC,YAAa;QACtBzC,KAAK,EAAE;UACLkD,QAAQ,EAAE,UAAU;UACpBE,IAAI,EAAE,OAAO;UACbD,GAAG,EAAE,KAAK;UACVmB,SAAS,EAAE,kBAAkB;UAC7Bb,UAAU,EAAE,0BAA0B;UACtCxC,MAAM,EAAE,MAAM;UACdyC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdxD,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdqD,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3BP,MAAM,EAAE;QACV,CAAE;QACFQ,YAAY,EAAGxB,CAAC,IAAK;UACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;UAC7DlB,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACsE,SAAS,GAAG,6BAA6B;QACjE,CAAE;QACFL,YAAY,EAAG1B,CAAC,IAAK;UACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;UAC7DlB,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACsE,SAAS,GAAG,2BAA2B;QAC/D,CAAE;QAAA7D,QAAA,eAEFzC,OAAA,CAACH,WAAW;UAACqG,IAAI,EAAE;QAAG;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACT,eAGDhD,OAAA;QACEgC,KAAK,EAAE;UACLkD,QAAQ,EAAE,UAAU;UACpBb,QAAQ,EAAEN,QAAQ,GAAG,MAAM,GAAG,QAAQ;UACtC2B,YAAY,EAAE,MAAM;UACpBS,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAE;QAAA3D,QAAA,eAEFzC,OAAA,CAAC6B,SAAS;UACRC,GAAG,EAAEyB,MAAM,CAACM,YAAY,CAAE;UAC1B9B,GAAG,EAAE,GAAG4B,SAAS,IAAIE,YAAY,GAAG,CAAC,EAAG;UACxC7B,KAAK,EAAE;YACLmE,QAAQ,EAAEpC,QAAQ,GAAG,MAAM,GAAG,MAAM;YACpCqC,SAAS,EAAErC,QAAQ,GAAG,MAAM,GAAG,MAAM;YACrC4B,KAAK,EAAE5B,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjC6B,MAAM,EAAE,MAAM;YACdF,YAAY,EAAE,MAAM;YACpBI,UAAU,EAAE,eAAe;YAC3BD,MAAM,EAAE9B,QAAQ,GAAG,MAAM,GAAG;UAC9B,CAAE;UACF7B,MAAM,EAAEA,CAAA,KAAMgC,cAAc,CAAC,IAAI,CAAE;UACnCjC,OAAO,EAAEgD;QAAW;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLO,MAAM,CAACuB,MAAM,GAAG,CAAC,iBAChB9E,OAAA;QACEiC,OAAO,EAAEyC,QAAS;QAClB1C,KAAK,EAAE;UACLkD,QAAQ,EAAE,UAAU;UACpBG,KAAK,EAAE,OAAO;UACdF,GAAG,EAAE,KAAK;UACVmB,SAAS,EAAE,kBAAkB;UAC7Bb,UAAU,EAAE,0BAA0B;UACtCxC,MAAM,EAAE,MAAM;UACdyC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdxD,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdqD,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3BP,MAAM,EAAE;QACV,CAAE;QACFQ,YAAY,EAAGxB,CAAC,IAAK;UACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;UAC7DlB,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACsE,SAAS,GAAG,6BAA6B;QACjE,CAAE;QACFL,YAAY,EAAG1B,CAAC,IAAK;UACnBA,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACyD,UAAU,GAAG,0BAA0B;UAC7DlB,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAACsE,SAAS,GAAG,2BAA2B;QAC/D,CAAE;QAAA7D,QAAA,eAEFzC,OAAA,CAACF,YAAY;UAACoG,IAAI,EAAE;QAAG;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLO,MAAM,CAACuB,MAAM,GAAG,CAAC,iBAChB9E,OAAA;MACEgC,KAAK,EAAE;QACLkD,QAAQ,EAAE,UAAU;QACpBI,MAAM,EAAE,MAAM;QACdF,IAAI,EAAE,KAAK;QACXkB,SAAS,EAAE,kBAAkB;QAC7BlE,OAAO,EAAE,MAAM;QACfmE,GAAG,EAAE,QAAQ;QACbd,UAAU,EAAE,oBAAoB;QAChCD,OAAO,EAAE,MAAM;QACfE,YAAY,EAAE,MAAM;QACpBS,QAAQ,EAAE,MAAM;QAChBK,SAAS,EAAE;MACb,CAAE;MACFvE,OAAO,EAAGsC,CAAC,IAAKA,CAAC,CAAC8B,eAAe,CAAC,CAAE;MAAA5D,QAAA,EAEnCc,MAAM,CAACkD,GAAG,CAAC,CAACC,KAAK,EAAE1B,KAAK,kBACvBhF,OAAA;QAEEgC,KAAK,EAAE;UACL2D,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnBG,MAAM,EAAE,SAAS;UACjBc,OAAO,EAAE3B,KAAK,KAAKnB,YAAY,GAAG,CAAC,GAAG,GAAG;UACzCZ,MAAM,EAAE+B,KAAK,KAAKnB,YAAY,GAAG,iBAAiB,GAAG,uBAAuB;UAC5EiC,UAAU,EAAE,eAAe;UAC3BzB,QAAQ,EAAE;QACZ,CAAE;QACFpC,OAAO,EAAEA,CAAA,KAAM8C,SAAS,CAACC,KAAK,CAAE;QAChCe,YAAY,EAAGxB,CAAC,IAAK;UACnB,IAAIS,KAAK,KAAKnB,YAAY,EAAE;YAC1BU,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAAC2E,OAAO,GAAG,KAAK;UACvC;QACF,CAAE;QACFV,YAAY,EAAG1B,CAAC,IAAK;UACnB,IAAIS,KAAK,KAAKnB,YAAY,EAAE;YAC1BU,CAAC,CAACyB,aAAa,CAAChE,KAAK,CAAC2E,OAAO,GAAG,KAAK;UACvC;QACF,CAAE;QAAAlE,QAAA,eAEFzC,OAAA,CAAC6B,SAAS;UACRC,GAAG,EAAE4E,KAAM;UACX3E,GAAG,EAAE,GAAG4B,SAAS,IAAIqB,KAAK,GAAG,CAAC,YAAa;UAC3ChD,KAAK,EAAE;YACL2D,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdgB,SAAS,EAAE;UACb;QAAE;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA/BGgC,KAAK;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACY,GAAA,CApTIN,aAA2C;AAAAuD,GAAA,GAA3CvD,aAA2C;AAsTjD,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}