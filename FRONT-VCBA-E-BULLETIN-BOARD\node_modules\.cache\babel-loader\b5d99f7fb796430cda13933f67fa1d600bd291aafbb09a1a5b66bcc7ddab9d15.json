{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 8V4H8\",\n  key: \"hb8ula\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"12\",\n  x: \"4\",\n  y: \"8\",\n  rx: \"2\",\n  key: \"enze0r\"\n}], [\"path\", {\n  d: \"M2 14h2\",\n  key: \"vft8re\"\n}], [\"path\", {\n  d: \"M20 14h2\",\n  key: \"4cs60a\"\n}], [\"path\", {\n  d: \"M15 13v2\",\n  key: \"1xurst\"\n}], [\"path\", {\n  d: \"M9 13v2\",\n  key: \"rq6x2g\"\n}]];\nconst Bot = createLucideIcon(\"bot\", __iconNode);\nexport { __iconNode, Bot as default };\n//# sourceMappingURL=bot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}