{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2\",\n  key: \"4jdomd\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v4\",\n  key: \"3hqy98\"\n}], [\"path\", {\n  d: \"M21 14H11\",\n  key: \"1bme5i\"\n}], [\"path\", {\n  d: \"m15 10-4 4 4 4\",\n  key: \"5dvupr\"\n}]];\nconst ClipboardCopy = createLucideIcon(\"clipboard-copy\", __iconNode);\nexport { __iconNode, ClipboardCopy as default };\n//# sourceMappingURL=clipboard-copy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}