{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 5v4\",\n  key: \"14uxtq\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"6\",\n  x: \"7\",\n  y: \"9\",\n  rx: \"1\",\n  key: \"f4fvz0\"\n}], [\"path\", {\n  d: \"M9 15v2\",\n  key: \"r5rk32\"\n}], [\"path\", {\n  d: \"M17 3v2\",\n  key: \"1l2re6\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"8\",\n  x: \"15\",\n  y: \"5\",\n  rx: \"1\",\n  key: \"z38je5\"\n}], [\"path\", {\n  d: \"M17 13v3\",\n  key: \"5l0wba\"\n}], [\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}]];\nconst ChartCandlestick = createLucideIcon(\"chart-candlestick\", __iconNode);\nexport { __iconNode, ChartCandlestick as default };\n//# sourceMappingURL=chart-candlestick.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}