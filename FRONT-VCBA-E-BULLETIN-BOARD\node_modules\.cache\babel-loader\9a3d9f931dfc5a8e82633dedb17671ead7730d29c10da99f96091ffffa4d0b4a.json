{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m4.9 4.9 14.2 14.2\",\n  key: \"1m5liu\"\n}]];\nconst Ban = createLucideIcon(\"ban\", __iconNode);\nexport { __iconNode, Ban as default };\n//# sourceMappingURL=ban.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}