{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 8 6-3-6-3v10\",\n  key: \"mvpnpy\"\n}], [\"path\", {\n  d: \"m8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12\",\n  key: \"ek95tt\"\n}], [\"path\", {\n  d: \"m6.49 12.85 11.02 6.3\",\n  key: \"1kt42w\"\n}], [\"path\", {\n  d: \"M17.51 12.85 6.5 19.15\",\n  key: \"v55bdg\"\n}]];\nconst LandPlot = createLucideIcon(\"land-plot\", __iconNode);\nexport { __iconNode, LandPlot as default };\n//# sourceMappingURL=land-plot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}