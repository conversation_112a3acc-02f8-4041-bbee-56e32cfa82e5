{"ast": null, "code": "/**\r\n * Form utility functions for consistent form handling across the application\r\n */\n\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n\n  // Debug logging\n  console.log('🔧 createFormData input:', {\n    formData,\n    files: files.length,\n    options\n  });\n\n  // Validate required fields before processing\n  const requiredFields = ['title', 'content', 'category_id'];\n  const missingFields = requiredFields.filter(field => {\n    const value = formData[field];\n    return !value || typeof value === 'string' && !value.trim();\n  });\n  if (missingFields.length > 0) {\n    console.error('❌ Missing required fields:', missingFields);\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n  }\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    console.log(`🔧 Processing field: ${key} = ${value} (type: ${typeof value})`);\n    if (key === 'category_id' && typeof value === 'string') {\n      // Send as integer for proper validation\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue)) {\n          console.log(`✅ Adding category_id: ${parsedValue}`);\n          formDataToSubmit.append(key, parsedValue.toString());\n        } else {\n          console.log(`❌ Invalid category_id: ${value}`);\n        }\n      } else {\n        console.log(`❌ Empty category_id - this will cause validation error`);\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and send as integer\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue)) {\n          console.log(`✅ Adding subcategory_id: ${parsedValue}`);\n          formDataToSubmit.append(key, parsedValue.toString());\n        } else {\n          console.log(`❌ Invalid subcategory_id: ${value}`);\n        }\n      } else {\n        console.log(`⏭️ Skipping empty subcategory_id`);\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      console.log(`⏭️ Skipping scheduled_publish_at (not scheduling)`);\n      return;\n    } else if (typeof value === 'boolean') {\n      // Send boolean values as proper boolean strings for express-validator\n      console.log(`✅ Adding boolean ${key}: ${value.toString()}`);\n      formDataToSubmit.append(key, value.toString());\n    } else if (value !== null && value !== undefined) {\n      if (typeof value === 'string' && value.trim() === '') {\n        console.log(`⚠️ Adding empty string for ${key} - this might cause validation issues`);\n      } else {\n        console.log(`✅ Adding ${key}: ${value.toString()}`);\n      }\n      formDataToSubmit.append(key, value.toString());\n    } else {\n      console.log(`⏭️ Skipping ${key}: null/undefined`);\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    console.log(`🔧 Adding ${files.length} files with field name: ${fileFieldName}`);\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  // Debug: Log final FormData contents\n  console.log('🔧 Final FormData contents:');\n  const finalEntries = [];\n  formDataToSubmit.forEach((value, key) => {\n    finalEntries.push(`  ${key}: ${value}`);\n  });\n  console.log(finalEntries.join('\\n'));\n  return formDataToSubmit;\n};\n\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\r\n * Common validation rules for announcements\r\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "console", "log", "length", "requiredFields", "missingFields", "filter", "field", "value", "trim", "error", "Error", "join", "Object", "entries", "for<PERSON>ach", "key", "parsedValue", "parseInt", "isNaN", "append", "toString", "status", "undefined", "file", "finalEntries", "push", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "replace", "max", "validator", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\r\n * Form utility functions for consistent form handling across the application\r\n */\r\n\r\nexport interface FormField {\r\n  [key: string]: string | number | boolean | null | undefined;\r\n}\r\n\r\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\r\nexport const createFormData = (\r\n  formData: FormField,\r\n  files: File[] = [],\r\n  options: {\r\n    skipScheduledDate?: boolean;\r\n    fileFieldName?: string;\r\n  } = {}\r\n): FormData => {\r\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\r\n  const formDataToSubmit = new FormData();\r\n\r\n  // Debug logging\r\n  console.log('🔧 createFormData input:', { formData, files: files.length, options });\r\n\r\n  // Validate required fields before processing\r\n  const requiredFields = ['title', 'content', 'category_id'];\r\n  const missingFields = requiredFields.filter(field => {\r\n    const value = formData[field];\r\n    return !value || (typeof value === 'string' && !value.trim());\r\n  });\r\n\r\n  if (missingFields.length > 0) {\r\n    console.error('❌ Missing required fields:', missingFields);\r\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\r\n  }\r\n\r\n  // Add all form fields\r\n  Object.entries(formData).forEach(([key, value]) => {\r\n    console.log(`🔧 Processing field: ${key} = ${value} (type: ${typeof value})`);\r\n\r\n    if (key === 'category_id' && typeof value === 'string') {\r\n      // Send as integer for proper validation\r\n      if (value.trim() !== '') {\r\n        const parsedValue = parseInt(value);\r\n        if (!isNaN(parsedValue)) {\r\n          console.log(`✅ Adding category_id: ${parsedValue}`);\r\n          formDataToSubmit.append(key, parsedValue.toString());\r\n        } else {\r\n          console.log(`❌ Invalid category_id: ${value}`);\r\n        }\r\n      } else {\r\n        console.log(`❌ Empty category_id - this will cause validation error`);\r\n      }\r\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\r\n      // Only append subcategory_id if it's not empty and send as integer\r\n      if (value.trim() !== '') {\r\n        const parsedValue = parseInt(value);\r\n        if (!isNaN(parsedValue)) {\r\n          console.log(`✅ Adding subcategory_id: ${parsedValue}`);\r\n          formDataToSubmit.append(key, parsedValue.toString());\r\n        } else {\r\n          console.log(`❌ Invalid subcategory_id: ${value}`);\r\n        }\r\n      } else {\r\n        console.log(`⏭️ Skipping empty subcategory_id`);\r\n      }\r\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\r\n      // Skip scheduled_publish_at if not scheduling\r\n      console.log(`⏭️ Skipping scheduled_publish_at (not scheduling)`);\r\n      return;\r\n    } else if (typeof value === 'boolean') {\r\n      // Send boolean values as proper boolean strings for express-validator\r\n      console.log(`✅ Adding boolean ${key}: ${value.toString()}`);\r\n      formDataToSubmit.append(key, value.toString());\r\n    } else if (value !== null && value !== undefined) {\r\n      if (typeof value === 'string' && value.trim() === '') {\r\n        console.log(`⚠️ Adding empty string for ${key} - this might cause validation issues`);\r\n      } else {\r\n        console.log(`✅ Adding ${key}: ${value.toString()}`);\r\n      }\r\n      formDataToSubmit.append(key, value.toString());\r\n    } else {\r\n      console.log(`⏭️ Skipping ${key}: null/undefined`);\r\n    }\r\n  });\r\n\r\n  // Add files\r\n  if (files.length > 0) {\r\n    console.log(`🔧 Adding ${files.length} files with field name: ${fileFieldName}`);\r\n    files.forEach((file) => {\r\n      formDataToSubmit.append(fileFieldName, file);\r\n    });\r\n  }\r\n\r\n  // Debug: Log final FormData contents\r\n  console.log('🔧 Final FormData contents:');\r\n  const finalEntries: string[] = [];\r\n  formDataToSubmit.forEach((value, key) => {\r\n    finalEntries.push(`  ${key}: ${value}`);\r\n  });\r\n  console.log(finalEntries.join('\\n'));\r\n\r\n  return formDataToSubmit;\r\n};\r\n\r\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\r\nexport const validateFormFields = (\r\n  formData: FormField,\r\n  rules: {\r\n    required?: string[];\r\n    maxLength?: { [key: string]: number };\r\n    custom?: { [key: string]: (value: any) => string | null };\r\n  } = {}\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n  const { required = [], maxLength = {}, custom = {} } = rules;\r\n\r\n  // Check required fields\r\n  required.forEach(field => {\r\n    const value = formData[field];\r\n    if (!value || (typeof value === 'string' && !value.trim())) {\r\n      errors[field] = `${field.replace('_', ' ')} is required`;\r\n    }\r\n  });\r\n\r\n  // Check max length\r\n  Object.entries(maxLength).forEach(([field, max]) => {\r\n    const value = formData[field];\r\n    if (typeof value === 'string' && value.length > max) {\r\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\r\n    }\r\n  });\r\n\r\n  // Apply custom validation\r\n  Object.entries(custom).forEach(([field, validator]) => {\r\n    const value = formData[field];\r\n    const error = validator(value);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n/**\r\n * Common validation rules for announcements\r\n */\r\nexport const announcementValidationRules = {\r\n  required: ['title', 'content', 'category_id'],\r\n  maxLength: { title: 255 },\r\n  custom: {\r\n    scheduled_publish_at: (value: any, formData?: FormField) => {\r\n      if (formData?.status === 'scheduled' && !value) {\r\n        return 'Scheduled publish date is required for scheduled announcements';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\r\nexport const validateFile = (\r\n  file: File,\r\n  options: {\r\n    maxSize?: number;\r\n    allowedTypes?: string[];\r\n  } = {}\r\n): string | null => {\r\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\r\n\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  if (file.size > maxSize) {\r\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;IAAER,QAAQ;IAAEC,KAAK,EAAEA,KAAK,CAACQ,MAAM;IAAEP;EAAQ,CAAC,CAAC;;EAEnF;EACA,MAAMQ,cAAc,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC1D,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI;IACnD,MAAMC,KAAK,GAAGd,QAAQ,CAACa,KAAK,CAAC;IAC7B,OAAO,CAACC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAE;EAC/D,CAAC,CAAC;EAEF,IAAIJ,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;IAC5BF,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEL,aAAa,CAAC;IAC1D,MAAM,IAAIM,KAAK,CAAC,4BAA4BN,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzE;;EAEA;EACAC,MAAM,CAACC,OAAO,CAACpB,QAAQ,CAAC,CAACqB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAER,KAAK,CAAC,KAAK;IACjDP,OAAO,CAACC,GAAG,CAAC,wBAAwBc,GAAG,MAAMR,KAAK,WAAW,OAAOA,KAAK,GAAG,CAAC;IAE7E,IAAIQ,GAAG,KAAK,aAAa,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;MACtD;MACA,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMQ,WAAW,GAAGC,QAAQ,CAACV,KAAK,CAAC;QACnC,IAAI,CAACW,KAAK,CAACF,WAAW,CAAC,EAAE;UACvBhB,OAAO,CAACC,GAAG,CAAC,yBAAyBe,WAAW,EAAE,CAAC;UACnDlB,gBAAgB,CAACqB,MAAM,CAACJ,GAAG,EAAEC,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC,MAAM;UACLpB,OAAO,CAACC,GAAG,CAAC,0BAA0BM,KAAK,EAAE,CAAC;QAChD;MACF,CAAC,MAAM;QACLP,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE;IACF,CAAC,MAAM,IAAIc,GAAG,KAAK,gBAAgB,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMQ,WAAW,GAAGC,QAAQ,CAACV,KAAK,CAAC;QACnC,IAAI,CAACW,KAAK,CAACF,WAAW,CAAC,EAAE;UACvBhB,OAAO,CAACC,GAAG,CAAC,4BAA4Be,WAAW,EAAE,CAAC;UACtDlB,gBAAgB,CAACqB,MAAM,CAACJ,GAAG,EAAEC,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC,MAAM;UACLpB,OAAO,CAACC,GAAG,CAAC,6BAA6BM,KAAK,EAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACLP,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD;IACF,CAAC,MAAM,IAAIc,GAAG,KAAK,sBAAsB,IAAInB,iBAAiB,IAAIH,QAAQ,CAAC4B,MAAM,KAAK,WAAW,EAAE;MACjG;MACArB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE;IACF,CAAC,MAAM,IAAI,OAAOM,KAAK,KAAK,SAAS,EAAE;MACrC;MACAP,OAAO,CAACC,GAAG,CAAC,oBAAoBc,GAAG,KAAKR,KAAK,CAACa,QAAQ,CAAC,CAAC,EAAE,CAAC;MAC3DtB,gBAAgB,CAACqB,MAAM,CAACJ,GAAG,EAAER,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC,MAAM,IAAIb,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKe,SAAS,EAAE;MAChD,IAAI,OAAOf,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACpDR,OAAO,CAACC,GAAG,CAAC,8BAA8Bc,GAAG,uCAAuC,CAAC;MACvF,CAAC,MAAM;QACLf,OAAO,CAACC,GAAG,CAAC,YAAYc,GAAG,KAAKR,KAAK,CAACa,QAAQ,CAAC,CAAC,EAAE,CAAC;MACrD;MACAtB,gBAAgB,CAACqB,MAAM,CAACJ,GAAG,EAAER,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC,MAAM;MACLpB,OAAO,CAACC,GAAG,CAAC,eAAec,GAAG,kBAAkB,CAAC;IACnD;EACF,CAAC,CAAC;;EAEF;EACA,IAAIrB,KAAK,CAACQ,MAAM,GAAG,CAAC,EAAE;IACpBF,OAAO,CAACC,GAAG,CAAC,aAAaP,KAAK,CAACQ,MAAM,2BAA2BL,aAAa,EAAE,CAAC;IAChFH,KAAK,CAACoB,OAAO,CAAES,IAAI,IAAK;MACtBzB,gBAAgB,CAACqB,MAAM,CAACtB,aAAa,EAAE0B,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACAvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAC1C,MAAMuB,YAAsB,GAAG,EAAE;EACjC1B,gBAAgB,CAACgB,OAAO,CAAC,CAACP,KAAK,EAAEQ,GAAG,KAAK;IACvCS,YAAY,CAACC,IAAI,CAAC,KAAKV,GAAG,KAAKR,KAAK,EAAE,CAAC;EACzC,CAAC,CAAC;EACFP,OAAO,CAACC,GAAG,CAACuB,YAAY,CAACb,IAAI,CAAC,IAAI,CAAC,CAAC;EAEpC,OAAOb,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,kBAAkB,GAAGA,CAChCjC,QAAmB,EACnBkC,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACf,OAAO,CAACR,KAAK,IAAI;IACxB,MAAMC,KAAK,GAAGd,QAAQ,CAACa,KAAK,CAAC;IAC7B,IAAI,CAACC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAE,EAAE;MAC1DoB,MAAM,CAACtB,KAAK,CAAC,GAAG,GAAGA,KAAK,CAAC0B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACApB,MAAM,CAACC,OAAO,CAACiB,SAAS,CAAC,CAAChB,OAAO,CAAC,CAAC,CAACR,KAAK,EAAE2B,GAAG,CAAC,KAAK;IAClD,MAAM1B,KAAK,GAAGd,QAAQ,CAACa,KAAK,CAAC;IAC7B,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACL,MAAM,GAAG+B,GAAG,EAAE;MACnDL,MAAM,CAACtB,KAAK,CAAC,GAAG,GAAGA,KAAK,CAAC0B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACArB,MAAM,CAACC,OAAO,CAACkB,MAAM,CAAC,CAACjB,OAAO,CAAC,CAAC,CAACR,KAAK,EAAE4B,SAAS,CAAC,KAAK;IACrD,MAAM3B,KAAK,GAAGd,QAAQ,CAACa,KAAK,CAAC;IAC7B,MAAMG,KAAK,GAAGyB,SAAS,CAAC3B,KAAK,CAAC;IAC9B,IAAIE,KAAK,EAAE;MACTmB,MAAM,CAACtB,KAAK,CAAC,GAAGG,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOmB,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,2BAA2B,GAAG;EACzCN,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEM,KAAK,EAAE;EAAI,CAAC;EACzBL,MAAM,EAAE;IACNM,oBAAoB,EAAEA,CAAC9B,KAAU,EAAEd,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4B,MAAM,MAAK,WAAW,IAAI,CAACd,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC5C,GAAG,CAACsC,KAAK,CAAC,GAAGM,IAAI,CAAC5C,GAAG,CAACwC,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGM,IAAI,CAACG,GAAG,CAACP,CAAC,EAAEG,CAAC,CAAC,EAAEK,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAGA,CAC1B3B,IAAU,EACV5B,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEwD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGzD,OAAO;EAEpH,IAAI,CAACyD,YAAY,CAACC,QAAQ,CAAC9B,IAAI,CAAC+B,IAAI,CAAC,EAAE;IACrC,OAAO,aAAa/B,IAAI,CAAC+B,IAAI,qCAAqCF,YAAY,CAACzC,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIY,IAAI,CAACgC,IAAI,GAAGJ,OAAO,EAAE;IACvB,OAAO,aAAab,cAAc,CAACf,IAAI,CAACgC,IAAI,CAAC,oCAAoCjB,cAAc,CAACa,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}