{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"6\",\n  x: \"8\",\n  y: \"12\",\n  rx: \"1\",\n  key: \"3yr8at\"\n}], [\"path\", {\n  d: \"M10 12v-2a2 2 0 1 1 4 0v2\",\n  key: \"j4i8d\"\n}]];\nconst FileLock = createLucideIcon(\"file-lock\", __iconNode);\nexport { __iconNode, FileLock as default };\n//# sourceMappingURL=file-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}