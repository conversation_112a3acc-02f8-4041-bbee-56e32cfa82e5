{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.1 2.182a10 10 0 0 1 3.8 0\",\n  key: \"5ilxe3\"\n}], [\"path\", {\n  d: \"M13.9 21.818a10 10 0 0 1-3.8 0\",\n  key: \"11zvb9\"\n}], [\"path\", {\n  d: \"M17.609 3.721a10 10 0 0 1 2.69 2.7\",\n  key: \"1iw5b2\"\n}], [\"path\", {\n  d: \"M2.182 13.9a10 10 0 0 1 0-3.8\",\n  key: \"c0bmvh\"\n}], [\"path\", {\n  d: \"M20.279 17.609a10 10 0 0 1-2.7 2.69\",\n  key: \"1ruxm7\"\n}], [\"path\", {\n  d: \"M21.818 10.1a10 10 0 0 1 0 3.8\",\n  key: \"qkgqxc\"\n}], [\"path\", {\n  d: \"M3.721 6.391a10 10 0 0 1 2.7-2.69\",\n  key: \"1mcia2\"\n}], [\"path\", {\n  d: \"M6.391 20.279a10 10 0 0 1-2.69-2.7\",\n  key: \"1fvljs\"\n}]];\nconst CircleDashed = createLucideIcon(\"circle-dashed\", __iconNode);\nexport { __iconNode, CircleDashed as default };\n//# sourceMappingURL=circle-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}