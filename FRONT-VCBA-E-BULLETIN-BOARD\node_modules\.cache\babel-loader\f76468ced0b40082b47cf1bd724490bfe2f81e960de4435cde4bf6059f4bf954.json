{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18v-7\",\n  key: \"wt116b\"\n}], [\"path\", {\n  d: \"M11.12 2.198a2 2 0 0 1 1.76.006l7.866 3.847c.476.233.31.949-.22.949H3.474c-.53 0-.695-.716-.22-.949z\",\n  key: \"1m329m\"\n}], [\"path\", {\n  d: \"M14 18v-7\",\n  key: \"vav6t3\"\n}], [\"path\", {\n  d: \"M18 18v-7\",\n  key: \"aexdmj\"\n}], [\"path\", {\n  d: \"M3 22h18\",\n  key: \"8prr45\"\n}], [\"path\", {\n  d: \"M6 18v-7\",\n  key: \"1ivflk\"\n}]];\nconst Landmark = createLucideIcon(\"landmark\", __iconNode);\nexport { __iconNode, Landmark as default };\n//# sourceMappingURL=landmark.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}