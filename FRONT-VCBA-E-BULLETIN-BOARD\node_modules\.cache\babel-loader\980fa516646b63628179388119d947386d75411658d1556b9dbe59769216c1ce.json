{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 8L2 12L6 16\",\n  key: \"kyvwex\"\n}], [\"path\", {\n  d: \"M2 12H22\",\n  key: \"1m8cig\"\n}]];\nconst MoveLeft = createLucideIcon(\"move-left\", __iconNode);\nexport { __iconNode, MoveLeft as default };\n//# sourceMappingURL=move-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}