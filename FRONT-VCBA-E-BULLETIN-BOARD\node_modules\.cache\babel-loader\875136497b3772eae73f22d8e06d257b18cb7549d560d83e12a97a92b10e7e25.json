{"ast": null, "code": "import { httpClient } from './api.service';\nclass CalendarReactionService {\n  // Like a calendar event\n  async likeEvent(eventId) {\n    try {\n      const response = await httpClient.post(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          added: false\n        }\n      };\n    } catch (error) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId) {\n    try {\n      const response = await httpClient.delete(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          removed: false\n        }\n      };\n    } catch (error) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["httpClient", "CalendarReactionService", "likeEvent", "eventId", "response", "post", "success", "message", "data", "added", "error", "console", "Error", "unlikeEvent", "delete", "removed", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { ApiResponse } from '../types/common.types';\nimport { API_BASE_URL } from '../config/constants';\n\nexport interface CalendarReactionData {\n  added?: boolean;\n  removed?: boolean;\n}\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: CalendarReactionData;\n}\n\nclass CalendarReactionService {\n  // Like a calendar event\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const response = await httpClient.post<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { added: false }\n      };\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const response = await httpClient.delete<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { removed: false }\n      };\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAe1C,MAAMC,uBAAuB,CAAC;EAC5B;EACA,MAAMC,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,UAAU,CAACK,IAAI,CAAuB,iBAAiBF,OAAO,OAAO,CAAC;MAC7F,OAAO;QACLG,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEC,KAAK,EAAE;QAAM;MACxC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIE,KAAK,CAACF,KAAK,CAACH,OAAO,IAAI,+BAA+B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMM,WAAWA,CAACV,OAAe,EAAqC;IACpE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,UAAU,CAACc,MAAM,CAAuB,iBAAiBX,OAAO,OAAO,CAAC;MAC/F,OAAO;QACLG,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEO,OAAO,EAAE;QAAM;MAC1C,CAAC;IACH,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIE,KAAK,CAACF,KAAK,CAACH,OAAO,IAAI,iCAAiC,CAAC;IACrE;EACF;;EAEA;EACA,MAAMS,UAAUA,CAACb,OAAe,EAAEc,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACJ,WAAW,CAACV,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAMe,uBAAuB,GAAG,IAAIjB,uBAAuB,CAAC,CAAC;AACpE,eAAeiB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}