{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.8 11.2c.8-.9 1.2-2 1.2-3.2a6 6 0 0 0-9.3-5\",\n  key: \"1fkcox\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M6.3 6.3a4.67 4.67 0 0 0 1.2 5.2c.7.7 1.3 1.5 1.5 2.5\",\n  key: \"10m8kw\"\n}], [\"path\", {\n  d: \"M9 18h6\",\n  key: \"x1upvd\"\n}], [\"path\", {\n  d: \"M10 22h4\",\n  key: \"ceow96\"\n}]];\nconst LightbulbOff = createLucideIcon(\"lightbulb-off\", __iconNode);\nexport { __iconNode, LightbulbOff as default };\n//# sourceMappingURL=lightbulb-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}