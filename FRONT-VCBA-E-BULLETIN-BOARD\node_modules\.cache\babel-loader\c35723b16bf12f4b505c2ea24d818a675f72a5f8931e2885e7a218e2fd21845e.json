{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1g98yp\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"14\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"6d4xhi\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"14\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"nxv5o0\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"3\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"1bb6yr\"\n}]];\nconst LayoutGrid = createLucideIcon(\"layout-grid\", __iconNode);\nexport { __iconNode, LayoutGrid as default };\n//# sourceMappingURL=layout-grid.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}