{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 13V7\",\n  key: \"1u13u9\"\n}], [\"path\", {\n  d: \"M14 13V7\",\n  key: \"1vj9om\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}]];\nconst MonitorPause = createLucideIcon(\"monitor-pause\", __iconNode);\nexport { __iconNode, MonitorPause as default };\n//# sourceMappingURL=monitor-pause.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}