{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"5\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1wp1u1\"\n}], [\"path\", {\n  d: \"M4 8v11a2 2 0 0 0 2 2h2\",\n  key: \"tvwodi\"\n}], [\"path\", {\n  d: \"M20 8v11a2 2 0 0 1-2 2h-2\",\n  key: \"1gkqxj\"\n}], [\"path\", {\n  d: \"m9 15 3-3 3 3\",\n  key: \"1pd0qc\"\n}], [\"path\", {\n  d: \"M12 12v9\",\n  key: \"192myk\"\n}]];\nconst ArchiveRestore = createLucideIcon(\"archive-restore\", __iconNode);\nexport { __iconNode, ArchiveRestore as default };\n//# sourceMappingURL=archive-restore.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}