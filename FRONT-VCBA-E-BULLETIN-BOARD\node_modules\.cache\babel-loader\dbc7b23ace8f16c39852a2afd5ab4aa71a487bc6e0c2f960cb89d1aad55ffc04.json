{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12v4\",\n  key: \"tww15h\"\n}], [\"path\", {\n  d: \"M12 20h.01\",\n  key: \"zekei9\"\n}], [\"path\", {\n  d: \"M17 18h.5a1 1 0 0 0 0-9h-1.79A7 7 0 1 0 7 17.708\",\n  key: \"xsb5ju\"\n}]];\nconst CloudAlert = createLucideIcon(\"cloud-alert\", __iconNode);\nexport { __iconNode, CloudAlert as default };\n//# sourceMappingURL=cloud-alert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}