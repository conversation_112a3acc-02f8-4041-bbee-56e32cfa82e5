{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3\",\n  key: \"14bfxa\"\n}], [\"path\", {\n  d: \"M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3\",\n  key: \"14rx03\"\n}], [\"path\", {\n  d: \"M4 12H2\",\n  key: \"rhcxmi\"\n}], [\"path\", {\n  d: \"M10 12H8\",\n  key: \"s88cx1\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}]];\nconst FlipVertical = createLucideIcon(\"flip-vertical\", __iconNode);\nexport { __iconNode, FlipVertical as default };\n//# sourceMappingURL=flip-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}