{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 5a2 2 0 0 0-2 2v11\",\n  key: \"s41o68\"\n}], [\"path\", {\n  d: \"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\",\n  key: \"rd2r6e\"\n}], [\"path\", {\n  d: \"M7 13h10\",\n  key: \"1rwob1\"\n}], [\"path\", {\n  d: \"M7 9h10\",\n  key: \"12czzb\"\n}], [\"path\", {\n  d: \"M9 5a2 2 0 0 0-2 2v11\",\n  key: \"x0q4gh\"\n}]];\nconst WavesLadder = createLucideIcon(\"waves-ladder\", __iconNode);\nexport { __iconNode, WavesLadder as default };\n//# sourceMappingURL=waves-ladder.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}