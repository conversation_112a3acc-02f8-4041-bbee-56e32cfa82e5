{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"19 20 9 12 19 4 19 20\",\n  key: \"o2sva\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"5\",\n  y1: \"19\",\n  y2: \"5\",\n  key: \"1ocqjk\"\n}]];\nconst SkipBack = createLucideIcon(\"skip-back\", __iconNode);\nexport { __iconNode, SkipBack as default };\n//# sourceMappingURL=skip-back.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}