{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M9 14v1\",\n  key: \"askpd8\"\n}], [\"path\", {\n  d: \"M9 19v2\",\n  key: \"16tejx\"\n}], [\"path\", {\n  d: \"M9 3v2\",\n  key: \"1noubl\"\n}], [\"path\", {\n  d: \"M9 9v1\",\n  key: \"19ebxg\"\n}]];\nconst PanelLeftDashed = createLucideIcon(\"panel-left-dashed\", __iconNode);\nexport { __iconNode, PanelLeftDashed as default };\n//# sourceMappingURL=panel-left-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}