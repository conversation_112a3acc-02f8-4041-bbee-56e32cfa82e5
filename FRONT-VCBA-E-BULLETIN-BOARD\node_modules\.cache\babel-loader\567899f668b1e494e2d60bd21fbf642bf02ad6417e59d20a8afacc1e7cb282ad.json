{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\",\n  key: \"116196\"\n}], [\"path\", {\n  d: \"M9 14h6\",\n  key: \"159ibu\"\n}], [\"path\", {\n  d: \"M12 17v-6\",\n  key: \"1y8rbf\"\n}]];\nconst ClipboardPlus = createLucideIcon(\"clipboard-plus\", __iconNode);\nexport { __iconNode, ClipboardPlus as default };\n//# sourceMappingURL=clipboard-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}