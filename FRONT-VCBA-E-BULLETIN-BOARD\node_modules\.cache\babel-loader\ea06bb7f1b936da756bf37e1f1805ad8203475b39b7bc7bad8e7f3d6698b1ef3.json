{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z\",\n  key: \"q8bfy3\"\n}], [\"path\", {\n  d: \"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14\",\n  key: \"1853fq\"\n}], [\"path\", {\n  d: \"M8 6v8\",\n  key: \"15ugcq\"\n}]];\nconst Megaphone = createLucideIcon(\"megaphone\", __iconNode);\nexport { __iconNode, Megaphone as default };\n//# sourceMappingURL=megaphone.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}