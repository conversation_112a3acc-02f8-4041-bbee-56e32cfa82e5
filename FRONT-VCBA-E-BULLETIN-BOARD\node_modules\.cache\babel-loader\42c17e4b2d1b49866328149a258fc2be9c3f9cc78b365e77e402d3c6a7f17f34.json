{"ast": null, "code": "import { API_BASE_URL, AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\n// API configuration\nconst API_CONFIG = {\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n};\n\n// Custom error class for API errors\nexport class ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.status = void 0;\n    this.data = void 0;\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\n// Base token manager interface\n\n// Generic token manager (legacy - for backward compatibility)\nexport const tokenManager = {\n  getToken() {\n    // First try the general auth token\n    let token = localStorage.getItem(AUTH_TOKEN_KEY);\n\n    // If not found, prioritize admin token over student token\n    // This ensures admin sessions take precedence when both tokens exist\n    if (!token) {\n      token = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    }\n\n    // If still not found, try student-specific token as fallback\n    if (!token) {\n      token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    }\n    return token;\n  },\n  setToken(token) {\n    localStorage.setItem(AUTH_TOKEN_KEY, token);\n  },\n  removeToken() {\n    localStorage.removeItem(AUTH_TOKEN_KEY);\n  },\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n  }\n};\n\n// Admin-specific token manager\nexport const adminTokenManager = {\n  getToken() {\n    const token = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    console.log('🔑 Admin Token Manager - Getting token:', token ? `${token.substring(0, 20)}...` : 'null');\n    return token;\n  },\n  setToken(token) {\n    console.log('🔑 Admin Token Manager - Setting token:', `${token.substring(0, 20)}...`);\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  },\n  removeToken() {\n    console.log('🔑 Admin Token Manager - Removing token');\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  },\n  getAuthHeaders() {\n    const token = this.getToken();\n    const headers = token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n    console.log('🔑 Admin Token Manager - Auth headers:', token ? 'Bearer token present' : 'No token');\n    return headers;\n  }\n};\n\n// Student-specific token manager\nexport const studentTokenManager = {\n  getToken() {\n    const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    console.log('🎓 Student Token Manager - Getting token:', token ? `${token.substring(0, 20)}...` : 'null');\n    return token;\n  },\n  setToken(token) {\n    console.log('🎓 Student Token Manager - Setting token:', `${token.substring(0, 20)}...`);\n    localStorage.setItem(STUDENT_AUTH_TOKEN_KEY, token);\n  },\n  removeToken() {\n    console.log('🎓 Student Token Manager - Removing token');\n    localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);\n  },\n  getAuthHeaders() {\n    const token = this.getToken();\n    const headers = token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n    console.log('🎓 Student Token Manager - Auth headers:', token ? 'Bearer token present' : 'No token');\n    return headers;\n  }\n};\n\n// HTTP client class\nclass HttpClient {\n  constructor(baseURL, defaultHeaders = {}, customTokenManager) {\n    this.baseURL = void 0;\n    this.defaultHeaders = void 0;\n    this.tokenManager = void 0;\n    this.baseURL = baseURL;\n    this.defaultHeaders = defaultHeaders;\n    this.tokenManager = customTokenManager || tokenManager; // Use custom or default token manager\n  }\n  async request(endpoint, options = {}, includeAuth = true) {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Build headers properly handling different header types\n    const headers = {\n      ...this.defaultHeaders\n    };\n\n    // Handle options.headers which could be Headers object or plain object\n    if (options.headers) {\n      if (options.headers instanceof Headers) {\n        // Convert Headers object to plain object\n        options.headers.forEach((value, key) => {\n          headers[key] = value;\n        });\n      } else {\n        // Plain object - spread it\n        Object.assign(headers, options.headers);\n      }\n    }\n\n    // Only add auth headers if requested\n    if (includeAuth) {\n      Object.assign(headers, this.tokenManager.getAuthHeaders());\n    }\n    const config = {\n      ...options,\n      credentials: 'include',\n      // Include cookies for refresh token\n      headers\n    };\n    try {\n      const response = await fetch(url, config);\n\n      // Handle different content types\n      let data;\n      const contentType = response.headers.get('content-type');\n      if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n\n      // Handle HTTP errors\n      if (!response.ok) {\n        var _data, _data$error, _data2;\n        const errorMessage = ((_data = data) === null || _data === void 0 ? void 0 : (_data$error = _data.error) === null || _data$error === void 0 ? void 0 : _data$error.message) || ((_data2 = data) === null || _data2 === void 0 ? void 0 : _data2.message) || `HTTP ${response.status}`;\n        throw new ApiError(errorMessage, response.status, data);\n      }\n      return data;\n    } catch (error) {\n      // Handle network errors\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Handle specific network connection errors\n      if (error instanceof TypeError) {\n        if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {\n          throw new ApiError(`Unable to connect to server at ${this.baseURL}. Please ensure the backend server is running.`, 0, {\n            originalError: error.message,\n            endpoint\n          });\n        }\n      }\n\n      // Handle other network errors\n      if (error && typeof error === 'object') {\n        const errorObj = error;\n        if (errorObj.name === 'NetworkError' || errorObj.message && typeof errorObj.message === 'string' && errorObj.message.includes('ERR_CONNECTION_REFUSED')) {\n          throw new ApiError(`Connection refused to ${this.baseURL}. Please check if the server is running on the correct port.`, 0, {\n            originalError: errorObj.message || String(error),\n            endpoint\n          });\n        }\n      }\n      throw new ApiError('An unexpected error occurred.', 0, {\n        originalError: String(error),\n        endpoint\n      });\n    }\n  }\n  async get(endpoint, params) {\n    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n    return this.request(url, {\n      method: 'GET'\n    });\n  }\n\n  // Public GET method without authentication\n  async getPublic(endpoint, params) {\n    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n    return this.request(url, {\n      method: 'GET'\n    }, false);\n  }\n  async post(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async put(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async patch(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n}\n\n// Create and export HTTP client instances\nexport const httpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers); // Default/legacy client\nexport const adminHttpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers, adminTokenManager);\nexport const studentHttpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers, studentTokenManager);\n\n// Setup response interceptor for a specific client\nconst setupClientInterceptor = (client, clientTokenManager, onUnauthorized) => {\n  const originalRequest = client['request'].bind(client);\n  client['request'] = async function (endpoint, options = {}) {\n    try {\n      return await originalRequest(endpoint, options);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        // Handle unauthorized access\n        if (error.status === 401 && onUnauthorized) {\n          clientTokenManager.removeToken();\n          onUnauthorized();\n        }\n\n        // DISABLED TOKEN REFRESH - Just log the error and continue\n        if (error.status === 401) {\n          console.warn('API Service - 401 error detected but token refresh DISABLED:', endpoint);\n          // Don't attempt refresh, just let the error propagate\n        }\n      }\n      throw error;\n    }\n  };\n};\n\n// Setup response interceptor for all clients\nexport const setupResponseInterceptor = onUnauthorized => {\n  setupClientInterceptor(httpClient, tokenManager, onUnauthorized);\n  setupClientInterceptor(adminHttpClient, adminTokenManager, onUnauthorized);\n  setupClientInterceptor(studentHttpClient, studentTokenManager, onUnauthorized);\n};\nexport default httpClient;", "map": {"version": 3, "names": ["API_BASE_URL", "AUTH_TOKEN_KEY", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "API_CONFIG", "baseURL", "timeout", "headers", "ApiError", "Error", "constructor", "message", "status", "data", "name", "tokenManager", "getToken", "token", "localStorage", "getItem", "setToken", "setItem", "removeToken", "removeItem", "getAuthHeaders", "Authorization", "adminTokenManager", "console", "log", "substring", "studentTokenManager", "HttpClient", "defaultHeaders", "customTokenManager", "request", "endpoint", "options", "<PERSON><PERSON><PERSON>", "url", "Headers", "for<PERSON>ach", "value", "key", "Object", "assign", "config", "credentials", "response", "fetch", "contentType", "get", "includes", "json", "text", "ok", "_data", "_data$error", "_data2", "errorMessage", "error", "TypeError", "originalError", "errorObj", "String", "params", "URLSearchParams", "method", "getPublic", "post", "body", "JSON", "stringify", "undefined", "put", "patch", "delete", "httpClient", "adminHttpClient", "studentHttpClient", "setupClientInterceptor", "client", "clientTokenManager", "onUnauthorized", "originalRequest", "bind", "warn", "setupResponseInterceptor"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/api.service.ts"], "sourcesContent": ["import { API_BASE_URL, AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\nimport { ApiResponse } from '../types';\n\n// API configuration\nconst API_CONFIG = {\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n};\n\n// Custom error class for API errors\nexport class ApiError extends Error {\n  public status: number;\n  public data: any;\n\n  constructor(message: string, status: number, data?: any) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\n// Base token manager interface\ninterface TokenManager {\n  getToken(): string | null;\n  setToken(token: string): void;\n  removeToken(): void;\n  getAuthHeaders(): Record<string, string>;\n}\n\n// Generic token manager (legacy - for backward compatibility)\nexport const tokenManager: TokenManager = {\n  getToken(): string | null {\n    // First try the general auth token\n    let token = localStorage.getItem(AUTH_TOKEN_KEY);\n\n    // If not found, prioritize admin token over student token\n    // This ensures admin sessions take precedence when both tokens exist\n    if (!token) {\n      token = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    }\n\n    // If still not found, try student-specific token as fallback\n    if (!token) {\n      token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    }\n\n    return token;\n  },\n\n  setToken(token: string): void {\n    localStorage.setItem(AUTH_TOKEN_KEY, token);\n  },\n\n  removeToken(): void {\n    localStorage.removeItem(AUTH_TOKEN_KEY);\n  },\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    return token ? { Authorization: `Bearer ${token}` } : {};\n  },\n};\n\n// Admin-specific token manager\nexport const adminTokenManager: TokenManager = {\n  getToken(): string | null {\n    const token = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    console.log('🔑 Admin Token Manager - Getting token:', token ? `${token.substring(0, 20)}...` : 'null');\n    return token;\n  },\n\n  setToken(token: string): void {\n    console.log('🔑 Admin Token Manager - Setting token:', `${token.substring(0, 20)}...`);\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  },\n\n  removeToken(): void {\n    console.log('🔑 Admin Token Manager - Removing token');\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  },\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    const headers: Record<string, string> = token ? { Authorization: `Bearer ${token}` } : {};\n    console.log('🔑 Admin Token Manager - Auth headers:', token ? 'Bearer token present' : 'No token');\n    return headers;\n  },\n};\n\n// Student-specific token manager\nexport const studentTokenManager: TokenManager = {\n  getToken(): string | null {\n    const token = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    console.log('🎓 Student Token Manager - Getting token:', token ? `${token.substring(0, 20)}...` : 'null');\n    return token;\n  },\n\n  setToken(token: string): void {\n    console.log('🎓 Student Token Manager - Setting token:', `${token.substring(0, 20)}...`);\n    localStorage.setItem(STUDENT_AUTH_TOKEN_KEY, token);\n  },\n\n  removeToken(): void {\n    console.log('🎓 Student Token Manager - Removing token');\n    localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);\n  },\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    const headers: Record<string, string> = token ? { Authorization: `Bearer ${token}` } : {};\n    console.log('🎓 Student Token Manager - Auth headers:', token ? 'Bearer token present' : 'No token');\n    return headers;\n  },\n};\n\n// HTTP client class\nclass HttpClient {\n  private baseURL: string;\n  private defaultHeaders: Record<string, string>;\n  private tokenManager: TokenManager;\n\n  constructor(baseURL: string, defaultHeaders: Record<string, string> = {}, customTokenManager?: TokenManager) {\n    this.baseURL = baseURL;\n    this.defaultHeaders = defaultHeaders;\n    this.tokenManager = customTokenManager || tokenManager; // Use custom or default token manager\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {},\n    includeAuth: boolean = true\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Build headers properly handling different header types\n    const headers: Record<string, string> = {\n      ...this.defaultHeaders,\n    };\n\n    // Handle options.headers which could be Headers object or plain object\n    if (options.headers) {\n      if (options.headers instanceof Headers) {\n        // Convert Headers object to plain object\n        options.headers.forEach((value, key) => {\n          headers[key] = value;\n        });\n      } else {\n        // Plain object - spread it\n        Object.assign(headers, options.headers);\n      }\n    }\n\n    // Only add auth headers if requested\n    if (includeAuth) {\n      Object.assign(headers, this.tokenManager.getAuthHeaders());\n    }\n\n    const config: RequestInit = {\n      ...options,\n      credentials: 'include', // Include cookies for refresh token\n      headers,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      // Handle different content types\n      let data: any;\n      const contentType = response.headers.get('content-type');\n\n      if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n      } else {\n        data = await response.text();\n      }\n\n      // Handle HTTP errors\n      if (!response.ok) {\n        const errorMessage = data?.error?.message || data?.message || `HTTP ${response.status}`;\n        throw new ApiError(errorMessage, response.status, data);\n      }\n\n      return data;\n    } catch (error) {\n      // Handle network errors\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      // Handle specific network connection errors\n      if (error instanceof TypeError) {\n        if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {\n          throw new ApiError(\n            `Unable to connect to server at ${this.baseURL}. Please ensure the backend server is running.`,\n            0,\n            { originalError: error.message, endpoint }\n          );\n        }\n      }\n\n      // Handle other network errors\n      if (error && typeof error === 'object') {\n        const errorObj = error as any;\n        if (errorObj.name === 'NetworkError' ||\n            (errorObj.message && typeof errorObj.message === 'string' && errorObj.message.includes('ERR_CONNECTION_REFUSED'))) {\n          throw new ApiError(\n            `Connection refused to ${this.baseURL}. Please check if the server is running on the correct port.`,\n            0,\n            { originalError: errorObj.message || String(error), endpoint }\n          );\n        }\n      }\n\n      throw new ApiError('An unexpected error occurred.', 0, { originalError: String(error), endpoint });\n    }\n  }\n\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\n    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n    return this.request<T>(url, { method: 'GET' });\n  }\n\n  // Public GET method without authentication\n  async getPublic<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\n    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n    return this.request<T>(url, { method: 'GET' }, false);\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'DELETE' });\n  }\n}\n\n// Create and export HTTP client instances\nexport const httpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers); // Default/legacy client\nexport const adminHttpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers, adminTokenManager);\nexport const studentHttpClient = new HttpClient(API_CONFIG.baseURL, API_CONFIG.headers, studentTokenManager);\n\n// Setup response interceptor for a specific client\nconst setupClientInterceptor = (client: HttpClient, clientTokenManager: TokenManager, onUnauthorized?: () => void) => {\n  const originalRequest = client['request'].bind(client);\n\n  client['request'] = async function<T>(endpoint: string, options: RequestInit = {}) {\n    try {\n      return await originalRequest<T>(endpoint, options);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        // Handle unauthorized access\n        if (error.status === 401 && onUnauthorized) {\n          clientTokenManager.removeToken();\n          onUnauthorized();\n        }\n\n        // DISABLED TOKEN REFRESH - Just log the error and continue\n        if (error.status === 401) {\n          console.warn('API Service - 401 error detected but token refresh DISABLED:', endpoint);\n          // Don't attempt refresh, just let the error propagate\n        }\n      }\n\n      throw error;\n    }\n  };\n};\n\n// Setup response interceptor for all clients\nexport const setupResponseInterceptor = (onUnauthorized?: () => void) => {\n  setupClientInterceptor(httpClient, tokenManager, onUnauthorized);\n  setupClientInterceptor(adminHttpClient, adminTokenManager, onUnauthorized);\n  setupClientInterceptor(studentHttpClient, studentTokenManager, onUnauthorized);\n};\n\nexport default httpClient;\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,qBAAqB;AAGhH;AACA,MAAMC,UAAU,GAAG;EACjBC,OAAO,EAAEL,YAAY;EACrBM,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,SAASC,KAAK,CAAC;EAIlCC,WAAWA,CAACC,OAAe,EAAEC,MAAc,EAAEC,IAAU,EAAE;IACvD,KAAK,CAACF,OAAO,CAAC;IAAC,KAJVC,MAAM;IAAA,KACNC,IAAI;IAIT,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;AACF;;AAEA;;AAQA;AACA,OAAO,MAAME,YAA0B,GAAG;EACxCC,QAAQA,CAAA,EAAkB;IACxB;IACA,IAAIC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAClB,cAAc,CAAC;;IAEhD;IACA;IACA,IAAI,CAACgB,KAAK,EAAE;MACVA,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACjB,oBAAoB,CAAC;IACpD;;IAEA;IACA,IAAI,CAACe,KAAK,EAAE;MACVA,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAChB,sBAAsB,CAAC;IACtD;IAEA,OAAOc,KAAK;EACd,CAAC;EAEDG,QAAQA,CAACH,KAAa,EAAQ;IAC5BC,YAAY,CAACG,OAAO,CAACpB,cAAc,EAAEgB,KAAK,CAAC;EAC7C,CAAC;EAEDK,WAAWA,CAAA,EAAS;IAClBJ,YAAY,CAACK,UAAU,CAACtB,cAAc,CAAC;EACzC,CAAC;EAEDuB,cAAcA,CAAA,EAA2B;IACvC,MAAMP,KAAK,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC;IAC7B,OAAOC,KAAK,GAAG;MAAEQ,aAAa,EAAE,UAAUR,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,iBAA+B,GAAG;EAC7CV,QAAQA,CAAA,EAAkB;IACxB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACjB,oBAAoB,CAAC;IACxDyB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEX,KAAK,GAAG,GAAGA,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;IACvG,OAAOZ,KAAK;EACd,CAAC;EAEDG,QAAQA,CAACH,KAAa,EAAQ;IAC5BU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE,GAAGX,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IACtFX,YAAY,CAACG,OAAO,CAACnB,oBAAoB,EAAEe,KAAK,CAAC;EACnD,CAAC;EAEDK,WAAWA,CAAA,EAAS;IAClBK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtDV,YAAY,CAACK,UAAU,CAACrB,oBAAoB,CAAC;EAC/C,CAAC;EAEDsB,cAAcA,CAAA,EAA2B;IACvC,MAAMP,KAAK,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC;IAC7B,MAAMT,OAA+B,GAAGU,KAAK,GAAG;MAAEQ,aAAa,EAAE,UAAUR,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;IACzFU,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEX,KAAK,GAAG,sBAAsB,GAAG,UAAU,CAAC;IAClG,OAAOV,OAAO;EAChB;AACF,CAAC;;AAED;AACA,OAAO,MAAMuB,mBAAiC,GAAG;EAC/Cd,QAAQA,CAAA,EAAkB;IACxB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAChB,sBAAsB,CAAC;IAC1DwB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEX,KAAK,GAAG,GAAGA,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;IACzG,OAAOZ,KAAK;EACd,CAAC;EAEDG,QAAQA,CAACH,KAAa,EAAQ;IAC5BU,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,GAAGX,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IACxFX,YAAY,CAACG,OAAO,CAAClB,sBAAsB,EAAEc,KAAK,CAAC;EACrD,CAAC;EAEDK,WAAWA,CAAA,EAAS;IAClBK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxDV,YAAY,CAACK,UAAU,CAACpB,sBAAsB,CAAC;EACjD,CAAC;EAEDqB,cAAcA,CAAA,EAA2B;IACvC,MAAMP,KAAK,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC;IAC7B,MAAMT,OAA+B,GAAGU,KAAK,GAAG;MAAEQ,aAAa,EAAE,UAAUR,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;IACzFU,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEX,KAAK,GAAG,sBAAsB,GAAG,UAAU,CAAC;IACpG,OAAOV,OAAO;EAChB;AACF,CAAC;;AAED;AACA,MAAMwB,UAAU,CAAC;EAKfrB,WAAWA,CAACL,OAAe,EAAE2B,cAAsC,GAAG,CAAC,CAAC,EAAEC,kBAAiC,EAAE;IAAA,KAJrG5B,OAAO;IAAA,KACP2B,cAAc;IAAA,KACdjB,YAAY;IAGlB,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2B,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACjB,YAAY,GAAGkB,kBAAkB,IAAIlB,YAAY,CAAC,CAAC;EAC1D;EAEA,MAAcmB,OAAOA,CACnBC,QAAgB,EAChBC,OAAoB,GAAG,CAAC,CAAC,EACzBC,WAAoB,GAAG,IAAI,EACF;IACzB,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACjC,OAAO,GAAG8B,QAAQ,EAAE;;IAExC;IACA,MAAM5B,OAA+B,GAAG;MACtC,GAAG,IAAI,CAACyB;IACV,CAAC;;IAED;IACA,IAAII,OAAO,CAAC7B,OAAO,EAAE;MACnB,IAAI6B,OAAO,CAAC7B,OAAO,YAAYgC,OAAO,EAAE;QACtC;QACAH,OAAO,CAAC7B,OAAO,CAACiC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;UACtCnC,OAAO,CAACmC,GAAG,CAAC,GAAGD,KAAK;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAE,MAAM,CAACC,MAAM,CAACrC,OAAO,EAAE6B,OAAO,CAAC7B,OAAO,CAAC;MACzC;IACF;;IAEA;IACA,IAAI8B,WAAW,EAAE;MACfM,MAAM,CAACC,MAAM,CAACrC,OAAO,EAAE,IAAI,CAACQ,YAAY,CAACS,cAAc,CAAC,CAAC,CAAC;IAC5D;IAEA,MAAMqB,MAAmB,GAAG;MAC1B,GAAGT,OAAO;MACVU,WAAW,EAAE,SAAS;MAAE;MACxBvC;IACF,CAAC;IAED,IAAI;MACF,MAAMwC,QAAQ,GAAG,MAAMC,KAAK,CAACV,GAAG,EAAEO,MAAM,CAAC;;MAEzC;MACA,IAAIhC,IAAS;MACb,MAAMoC,WAAW,GAAGF,QAAQ,CAACxC,OAAO,CAAC2C,GAAG,CAAC,cAAc,CAAC;MAExD,IAAID,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC3DtC,IAAI,GAAG,MAAMkC,QAAQ,CAACK,IAAI,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLvC,IAAI,GAAG,MAAMkC,QAAQ,CAACM,IAAI,CAAC,CAAC;MAC9B;;MAEA;MACA,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAAA,IAAAC,KAAA,EAAAC,WAAA,EAAAC,MAAA;QAChB,MAAMC,YAAY,GAAG,EAAAH,KAAA,GAAA1C,IAAI,cAAA0C,KAAA,wBAAAC,WAAA,GAAJD,KAAA,CAAMI,KAAK,cAAAH,WAAA,uBAAXA,WAAA,CAAa7C,OAAO,OAAA8C,MAAA,GAAI5C,IAAI,cAAA4C,MAAA,uBAAJA,MAAA,CAAM9C,OAAO,KAAI,QAAQoC,QAAQ,CAACnC,MAAM,EAAE;QACvF,MAAM,IAAIJ,QAAQ,CAACkD,YAAY,EAAEX,QAAQ,CAACnC,MAAM,EAAEC,IAAI,CAAC;MACzD;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACd;MACA,IAAIA,KAAK,YAAYnD,QAAQ,EAAE;QAC7B,MAAMmD,KAAK;MACb;;MAEA;MACA,IAAIA,KAAK,YAAYC,SAAS,EAAE;QAC9B,IAAID,KAAK,CAAChD,OAAO,CAACwC,QAAQ,CAAC,OAAO,CAAC,IAAIQ,KAAK,CAAChD,OAAO,CAACwC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAChF,MAAM,IAAI3C,QAAQ,CAChB,kCAAkC,IAAI,CAACH,OAAO,gDAAgD,EAC9F,CAAC,EACD;YAAEwD,aAAa,EAAEF,KAAK,CAAChD,OAAO;YAAEwB;UAAS,CAC3C,CAAC;QACH;MACF;;MAEA;MACA,IAAIwB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACtC,MAAMG,QAAQ,GAAGH,KAAY;QAC7B,IAAIG,QAAQ,CAAChD,IAAI,KAAK,cAAc,IAC/BgD,QAAQ,CAACnD,OAAO,IAAI,OAAOmD,QAAQ,CAACnD,OAAO,KAAK,QAAQ,IAAImD,QAAQ,CAACnD,OAAO,CAACwC,QAAQ,CAAC,wBAAwB,CAAE,EAAE;UACrH,MAAM,IAAI3C,QAAQ,CAChB,yBAAyB,IAAI,CAACH,OAAO,8DAA8D,EACnG,CAAC,EACD;YAAEwD,aAAa,EAAEC,QAAQ,CAACnD,OAAO,IAAIoD,MAAM,CAACJ,KAAK,CAAC;YAAExB;UAAS,CAC/D,CAAC;QACH;MACF;MAEA,MAAM,IAAI3B,QAAQ,CAAC,+BAA+B,EAAE,CAAC,EAAE;QAAEqD,aAAa,EAAEE,MAAM,CAACJ,KAAK,CAAC;QAAExB;MAAS,CAAC,CAAC;IACpG;EACF;EAEA,MAAMe,GAAGA,CAAIf,QAAgB,EAAE6B,MAA4B,EAA2B;IACpF,MAAM1B,GAAG,GAAG0B,MAAM,GAAG,GAAG7B,QAAQ,IAAI,IAAI8B,eAAe,CAACD,MAAM,CAAC,EAAE,GAAG7B,QAAQ;IAC5E,OAAO,IAAI,CAACD,OAAO,CAAII,GAAG,EAAE;MAAE4B,MAAM,EAAE;IAAM,CAAC,CAAC;EAChD;;EAEA;EACA,MAAMC,SAASA,CAAIhC,QAAgB,EAAE6B,MAA4B,EAA2B;IAC1F,MAAM1B,GAAG,GAAG0B,MAAM,GAAG,GAAG7B,QAAQ,IAAI,IAAI8B,eAAe,CAACD,MAAM,CAAC,EAAE,GAAG7B,QAAQ;IAC5E,OAAO,IAAI,CAACD,OAAO,CAAII,GAAG,EAAE;MAAE4B,MAAM,EAAE;IAAM,CAAC,EAAE,KAAK,CAAC;EACvD;EAEA,MAAME,IAAIA,CAAIjC,QAAgB,EAAEtB,IAAU,EAA2B;IACnE,OAAO,IAAI,CAACqB,OAAO,CAAIC,QAAQ,EAAE;MAC/B+B,MAAM,EAAE,MAAM;MACdG,IAAI,EAAExD,IAAI,GAAGyD,IAAI,CAACC,SAAS,CAAC1D,IAAI,CAAC,GAAG2D;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMC,GAAGA,CAAItC,QAAgB,EAAEtB,IAAU,EAA2B;IAClE,OAAO,IAAI,CAACqB,OAAO,CAAIC,QAAQ,EAAE;MAC/B+B,MAAM,EAAE,KAAK;MACbG,IAAI,EAAExD,IAAI,GAAGyD,IAAI,CAACC,SAAS,CAAC1D,IAAI,CAAC,GAAG2D;IACtC,CAAC,CAAC;EACJ;EAEA,MAAME,KAAKA,CAAIvC,QAAgB,EAAEtB,IAAU,EAA2B;IACpE,OAAO,IAAI,CAACqB,OAAO,CAAIC,QAAQ,EAAE;MAC/B+B,MAAM,EAAE,OAAO;MACfG,IAAI,EAAExD,IAAI,GAAGyD,IAAI,CAACC,SAAS,CAAC1D,IAAI,CAAC,GAAG2D;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMG,MAAMA,CAAIxC,QAAgB,EAA2B;IACzD,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAAE+B,MAAM,EAAE;IAAS,CAAC,CAAC;EACxD;AACF;;AAEA;AACA,OAAO,MAAMU,UAAU,GAAG,IAAI7C,UAAU,CAAC3B,UAAU,CAACC,OAAO,EAAED,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC;AAClF,OAAO,MAAMsE,eAAe,GAAG,IAAI9C,UAAU,CAAC3B,UAAU,CAACC,OAAO,EAAED,UAAU,CAACG,OAAO,EAAEmB,iBAAiB,CAAC;AACxG,OAAO,MAAMoD,iBAAiB,GAAG,IAAI/C,UAAU,CAAC3B,UAAU,CAACC,OAAO,EAAED,UAAU,CAACG,OAAO,EAAEuB,mBAAmB,CAAC;;AAE5G;AACA,MAAMiD,sBAAsB,GAAGA,CAACC,MAAkB,EAAEC,kBAAgC,EAAEC,cAA2B,KAAK;EACpH,MAAMC,eAAe,GAAGH,MAAM,CAAC,SAAS,CAAC,CAACI,IAAI,CAACJ,MAAM,CAAC;EAEtDA,MAAM,CAAC,SAAS,CAAC,GAAG,gBAAkB7C,QAAgB,EAAEC,OAAoB,GAAG,CAAC,CAAC,EAAE;IACjF,IAAI;MACF,OAAO,MAAM+C,eAAe,CAAIhD,QAAQ,EAAEC,OAAO,CAAC;IACpD,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYnD,QAAQ,EAAE;QAC7B;QACA,IAAImD,KAAK,CAAC/C,MAAM,KAAK,GAAG,IAAIsE,cAAc,EAAE;UAC1CD,kBAAkB,CAAC3D,WAAW,CAAC,CAAC;UAChC4D,cAAc,CAAC,CAAC;QAClB;;QAEA;QACA,IAAIvB,KAAK,CAAC/C,MAAM,KAAK,GAAG,EAAE;UACxBe,OAAO,CAAC0D,IAAI,CAAC,8DAA8D,EAAElD,QAAQ,CAAC;UACtF;QACF;MACF;MAEA,MAAMwB,KAAK;IACb;EACF,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAM2B,wBAAwB,GAAIJ,cAA2B,IAAK;EACvEH,sBAAsB,CAACH,UAAU,EAAE7D,YAAY,EAAEmE,cAAc,CAAC;EAChEH,sBAAsB,CAACF,eAAe,EAAEnD,iBAAiB,EAAEwD,cAAc,CAAC;EAC1EH,sBAAsB,CAACD,iBAAiB,EAAEhD,mBAAmB,EAAEoD,cAAc,CAAC;AAChF,CAAC;AAED,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}