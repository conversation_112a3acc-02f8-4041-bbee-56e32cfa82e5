{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.82 16.12c1.69.6 3.91.79 5.18.85.28.01.53-.09.7-.27\",\n  key: \"qyzcap\"\n}], [\"path\", {\n  d: \"M11.14 20.57c.52.24 2.44 1.12 4.08 1.37.46.06.86-.25.9-.71.12-1.52-.3-3.43-.5-4.28\",\n  key: \"y078lb\"\n}], [\"path\", {\n  d: \"M16.13 21.05c1.65.63 3.68.84 4.87.91a.9.9 0 0 0 .7-.26\",\n  key: \"1utre3\"\n}], [\"path\", {\n  d: \"M17.99 5.52a20.83 20.83 0 0 1 3.15 4.5.8.8 0 0 1-.68 1.13c-1.17.1-2.5.02-3.9-.25\",\n  key: \"17o9hm\"\n}], [\"path\", {\n  d: \"M20.57 11.14c.24.52 1.12 2.44 1.37 4.08.04.3-.08.59-.31.75\",\n  key: \"1d1n4p\"\n}], [\"path\", {\n  d: \"M4.93 4.93a10 10 0 0 0-.67 13.4c.35.43.96.4 1.17-.12.69-1.71 1.07-5.07 1.07-6.71 1.34.45 3.1.9 4.88.62a.85.85 0 0 0 .48-.24\",\n  key: \"9uv3tt\"\n}], [\"path\", {\n  d: \"M5.52 17.99c1.05.95 2.91 2.42 4.5 3.15a.8.8 0 0 0 1.13-.68c.2-2.34-.33-5.3-1.57-8.28\",\n  key: \"1292wz\"\n}], [\"path\", {\n  d: \"M8.35 2.68a10 10 0 0 1 9.98 1.58c.43.35.4.96-.12 1.17-1.5.6-4.3.98-6.07 1.05\",\n  key: \"7ozu9p\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst HopOff = createLucideIcon(\"hop-off\", __iconNode);\nexport { __iconNode, HopOff as default };\n//# sourceMappingURL=hop-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}