{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v20\",\n  key: \"t6zp3m\"\n}], [\"path\", {\n  d: \"M8 10H4a2 2 0 0 1-2-2V6c0-1.1.9-2 2-2h4\",\n  key: \"14d6g8\"\n}], [\"path\", {\n  d: \"M16 10h4a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-4\",\n  key: \"1e2lrw\"\n}], [\"path\", {\n  d: \"M8 20H7a2 2 0 0 1-2-2v-2c0-1.1.9-2 2-2h1\",\n  key: \"1fkdwx\"\n}], [\"path\", {\n  d: \"M16 14h1a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-1\",\n  key: \"1euafb\"\n}]];\nconst AlignCenterVertical = createLucideIcon(\"align-center-vertical\", __iconNode);\nexport { __iconNode, AlignCenterVertical as default };\n//# sourceMappingURL=align-center-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}