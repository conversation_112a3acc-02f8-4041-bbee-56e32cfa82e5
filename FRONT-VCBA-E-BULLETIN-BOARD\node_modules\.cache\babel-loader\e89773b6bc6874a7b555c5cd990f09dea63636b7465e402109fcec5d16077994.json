{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 22V2l10 5-10 5\",\n  key: \"17n18y\"\n}]];\nconst FlagTriangleRight = createLucideIcon(\"flag-triangle-right\", __iconNode);\nexport { __iconNode, FlagTriangleRight as default };\n//# sourceMappingURL=flag-triangle-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}