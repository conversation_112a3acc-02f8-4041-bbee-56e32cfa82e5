{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\WelcomePage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport '../styles/welcome.css';\n\n/**\n * WelcomePage Component\n * \n * A mobile-first, responsive welcome page for William College General Santos City.\n * Features:\n * - Full-screen background image with overlay\n * - Responsive school logo\n * - Mobile-optimized layout with touch-friendly buttons\n * - Professional typography and spacing\n * - Smooth animations and transitions\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative min-h-screen flex flex-col\",\n      style: {\n        zIndex: 10\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"welcome-header flex-shrink-0 pt-8 pb-4 px-4 sm:pt-12 sm:pb-6 sm:px-6 lg:pt-16 lg:pb-8 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 sm:mb-8 lg:mb-10\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"William College General Santos City Logo\",\n              className: \"welcome-logo mx-auto h-20 w-auto sm:h-24 md:h-28 lg:h-32 xl:h-36 drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"welcome-text-shadow text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-3 sm:mb-4 lg:mb-6\",\n            children: \"William College\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"welcome-text-shadow text-white text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-semibold leading-tight mb-2 sm:mb-3 lg:mb-4\",\n            children: \"General Santos City\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"welcome-text-shadow text-white text-sm sm:text-base md:text-lg lg:text-xl font-medium opacity-90 max-w-2xl mx-auto leading-relaxed\",\n            children: \"Excellence in Education \\u2022 Innovation in Learning \\u2022 Building Tomorrow's Leaders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"welcome-main flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full space-y-6 sm:space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"welcome-text-shadow text-white text-xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4\",\n              children: \"Welcome to Our Digital Campus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"welcome-text-shadow text-white text-sm sm:text-base md:text-lg opacity-90 leading-relaxed\",\n              children: \"Access your student portal to stay connected with announcements, events, and important updates from your college community.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-slide-up\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleStudentLogin,\n              className: \"welcome-button w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-4 px-6 sm:py-5 sm:px-8 rounded-xl text-base sm:text-lg md:text-xl transform hover:scale-105 hover:shadow-2xl focus:outline-none focus:ring-4 focus:ring-primary-300 focus:ring-opacity-50 shadow-lg drop-shadow-lg\",\n              \"aria-label\": \"Access Student Login Portal\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 sm:w-6 sm:h-6\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Student Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center animate-fade-in\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"welcome-text-shadow text-white text-xs sm:text-sm opacity-75\",\n              children: \"New to our digital platform? Contact the registrar's office for assistance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"welcome-footer flex-shrink-0 pb-6 px-4 sm:pb-8 sm:px-6 lg:pb-10 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"welcome-text-shadow text-white text-xs sm:text-sm opacity-75\",\n            children: \"\\xA9 2024 William College General Santos City. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomePage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = WelcomePage;\nexport default WelcomePage;\nvar _c;\n$RefreshReg$(_c, \"WelcomePage\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "WelcomePage", "_s", "navigate", "handleStudentLogin", "className", "children", "style", "zIndex", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/WelcomePage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport '../styles/welcome.css';\n\n/**\n * WelcomePage Component\n * \n * A mobile-first, responsive welcome page for William College General Santos City.\n * Features:\n * - Full-screen background image with overlay\n * - Responsive school logo\n * - Mobile-optimized layout with touch-friendly buttons\n * - Professional typography and spacing\n * - Smooth animations and transitions\n */\nconst WelcomePage: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n\n  return (\n    <div className=\"welcome-container\">\n      {/* Content Container */}\n      <div className=\"relative min-h-screen flex flex-col\"\n           style={{ zIndex: 10 }}>\n        {/* Header Section */}\n        <header className=\"welcome-header flex-shrink-0 pt-8 pb-4 px-4 sm:pt-12 sm:pb-6 sm:px-6 lg:pt-16 lg:pb-8 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            {/* School Logo */}\n            <div className=\"mb-6 sm:mb-8 lg:mb-10\">\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"William College General Santos City Logo\"\n                className=\"welcome-logo mx-auto h-20 w-auto sm:h-24 md:h-28 lg:h-32 xl:h-36 drop-shadow-lg\"\n              />\n            </div>\n\n            {/* School Name */}\n            <h1 className=\"welcome-text-shadow text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-3 sm:mb-4 lg:mb-6\">\n              William College\n            </h1>\n\n            <h2 className=\"welcome-text-shadow text-white text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-semibold leading-tight mb-2 sm:mb-3 lg:mb-4\">\n              General Santos City\n            </h2>\n\n            {/* Subtitle */}\n            <p className=\"welcome-text-shadow text-white text-sm sm:text-base md:text-lg lg:text-xl font-medium opacity-90 max-w-2xl mx-auto leading-relaxed\">\n              Excellence in Education • Innovation in Learning • Building Tomorrow's Leaders\n            </p>\n          </div>\n        </header>\n\n        {/* Main Content Area */}\n        <main className=\"welcome-main flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"max-w-md w-full space-y-6 sm:space-y-8\">\n            {/* Welcome Message */}\n            <div className=\"text-center animate-fade-in\">\n              <h3 className=\"welcome-text-shadow text-white text-xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4\">\n                Welcome to Our Digital Campus\n              </h3>\n              <p className=\"welcome-text-shadow text-white text-sm sm:text-base md:text-lg opacity-90\n                          leading-relaxed\">\n                Access your student portal to stay connected with announcements,\n                events, and important updates from your college community.\n              </p>\n            </div>\n\n            {/* Student Login Button */}\n            <div className=\"animate-slide-up\">\n              <button\n                onClick={handleStudentLogin}\n                className=\"welcome-button w-full bg-primary-600 hover:bg-primary-700\n                         text-white font-bold py-4 px-6 sm:py-5 sm:px-8\n                         rounded-xl text-base sm:text-lg md:text-xl\n                         transform hover:scale-105 hover:shadow-2xl\n                         focus:outline-none focus:ring-4 focus:ring-primary-300 focus:ring-opacity-50\n                         shadow-lg drop-shadow-lg\"\n                aria-label=\"Access Student Login Portal\"\n              >\n                <span className=\"flex items-center justify-center space-x-2\">\n                  <svg\n                    className=\"w-5 h-5 sm:w-6 sm:h-6\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    aria-hidden=\"true\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n                    />\n                  </svg>\n                  <span>Student Login</span>\n                </span>\n              </button>\n            </div>\n\n            {/* Additional Info */}\n            <div className=\"text-center animate-fade-in\">\n              <p className=\"welcome-text-shadow text-white text-xs sm:text-sm opacity-75\">\n                New to our digital platform? Contact the registrar's office for assistance.\n              </p>\n            </div>\n          </div>\n        </main>\n\n        {/* Footer */}\n        <footer className=\"welcome-footer flex-shrink-0 pb-6 px-4 sm:pb-8 sm:px-6 lg:pb-10 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <p className=\"welcome-text-shadow text-white text-xs sm:text-sm opacity-75\">\n              © 2024 William College General Santos City. All rights reserved.\n            </p>\n          </div>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,uBAAuB;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,oBACEH,OAAA;IAAKK,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAEhCN,OAAA;MAAKK,SAAS,EAAC,qCAAqC;MAC/CE,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAE;MAAAF,QAAA,gBAEzBN,OAAA;QAAQK,SAAS,EAAC,+FAA+F;QAAAC,QAAA,eAC/GN,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAE5CN,OAAA;YAAKK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCN,OAAA;cACES,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,0CAA0C;cAC9CL,SAAS,EAAC;YAAiF;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNd,OAAA;YAAIK,SAAS,EAAC,sIAAsI;YAAAC,QAAA,EAAC;UAErJ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELd,OAAA;YAAIK,SAAS,EAAC,wIAAwI;YAAAC,QAAA,EAAC;UAEvJ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLd,OAAA;YAAGK,SAAS,EAAC,oIAAoI;YAAAC,QAAA,EAAC;UAElJ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTd,OAAA;QAAMK,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eACjGN,OAAA;UAAKK,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDN,OAAA;YAAKK,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CN,OAAA;cAAIK,SAAS,EAAC,uFAAuF;cAAAC,QAAA,EAAC;YAEtG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLd,OAAA;cAAGK,SAAS,EAAC,2FACe;cAAAC,QAAA,EAAC;YAG7B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNd,OAAA;YAAKK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BN,OAAA;cACEe,OAAO,EAAEX,kBAAmB;cAC5BC,SAAS,EAAC,sSAKwB;cAClC,cAAW,6BAA6B;cAAAC,QAAA,eAExCN,OAAA;gBAAMK,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAC1DN,OAAA;kBACEK,SAAS,EAAC,uBAAuB;kBACjCW,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBACnB,eAAY,MAAM;kBAAAZ,QAAA,eAElBN,OAAA;oBACEmB,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAA8F;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNd,OAAA;kBAAAM,QAAA,EAAM;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNd,OAAA;YAAKK,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CN,OAAA;cAAGK,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EAAC;YAE5E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPd,OAAA;QAAQK,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACzFN,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAC5CN,OAAA;YAAGK,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EAAC;UAE5E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CA3GID,WAAqB;EAAA,QACRH,WAAW;AAAA;AAAAyB,EAAA,GADxBtB,WAAqB;AA6G3B,eAAeA,WAAW;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}