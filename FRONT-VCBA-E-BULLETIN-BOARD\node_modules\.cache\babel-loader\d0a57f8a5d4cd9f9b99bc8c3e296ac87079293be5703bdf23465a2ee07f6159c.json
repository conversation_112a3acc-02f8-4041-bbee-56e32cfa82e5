{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 21h10\",\n  key: \"1b0cd5\"\n}], [\"path\", {\n  d: \"M12 21a9 9 0 0 0 9-9H3a9 9 0 0 0 9 9Z\",\n  key: \"4rw317\"\n}], [\"path\", {\n  d: \"M11.38 12a2.4 2.4 0 0 1-.4-4.77 2.4 2.4 0 0 1 3.2-2.77 2.4 2.4 0 0 1 3.47-.63 2.4 2.4 0 0 1 3.37 3.37 2.4 2.4 0 0 1-1.1 3.7 2.51 2.51 0 0 1 .03 1.1\",\n  key: \"10xrj0\"\n}], [\"path\", {\n  d: \"m13 12 4-4\",\n  key: \"1hckqy\"\n}], [\"path\", {\n  d: \"M10.9 7.25A3.99 3.99 0 0 0 4 10c0 .73.2 1.41.54 2\",\n  key: \"1p4srx\"\n}]];\nconst Salad = createLucideIcon(\"salad\", __iconNode);\nexport { __iconNode, Salad as default };\n//# sourceMappingURL=salad.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}