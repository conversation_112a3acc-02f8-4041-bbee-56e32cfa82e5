{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"13\",\n  width: \"9\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"1iip1u\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"5\",\n  width: \"12\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"1anskk\"\n}]];\nconst ChartBarBig = createLucideIcon(\"chart-bar-big\", __iconNode);\nexport { __iconNode, ChartBarBig as default };\n//# sourceMappingURL=chart-bar-big.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}