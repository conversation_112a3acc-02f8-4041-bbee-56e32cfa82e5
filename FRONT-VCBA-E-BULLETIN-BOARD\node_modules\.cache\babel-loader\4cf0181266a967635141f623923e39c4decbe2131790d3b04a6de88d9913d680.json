{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m5 8 6 6\",\n  key: \"1wu5hv\"\n}], [\"path\", {\n  d: \"m4 14 6-6 2-3\",\n  key: \"1k1g8d\"\n}], [\"path\", {\n  d: \"M2 5h12\",\n  key: \"or177f\"\n}], [\"path\", {\n  d: \"M7 2h1\",\n  key: \"1t2jsx\"\n}], [\"path\", {\n  d: \"m22 22-5-10-5 10\",\n  key: \"don7ne\"\n}], [\"path\", {\n  d: \"M14 18h6\",\n  key: \"1m8k6r\"\n}]];\nconst Languages = createLucideIcon(\"languages\", __iconNode);\nexport { __iconNode, Languages as default };\n//# sourceMappingURL=languages.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}