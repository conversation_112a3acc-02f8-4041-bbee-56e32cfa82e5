{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"ellipse\", {\n  cx: \"12\",\n  cy: \"5\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"msslwz\"\n}], [\"path\", {\n  d: \"M3 5V19A9 3 0 0 0 15 21.84\",\n  key: \"14ibmq\"\n}], [\"path\", {\n  d: \"M21 5V8\",\n  key: \"1marbg\"\n}], [\"path\", {\n  d: \"M21 12L18 17H22L19 22\",\n  key: \"zafso\"\n}], [\"path\", {\n  d: \"M3 12A9 3 0 0 0 14.59 14.87\",\n  key: \"1y4wr8\"\n}]];\nconst DatabaseZap = createLucideIcon(\"database-zap\", __iconNode);\nexport { __iconNode, DatabaseZap as default };\n//# sourceMappingURL=database-zap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}