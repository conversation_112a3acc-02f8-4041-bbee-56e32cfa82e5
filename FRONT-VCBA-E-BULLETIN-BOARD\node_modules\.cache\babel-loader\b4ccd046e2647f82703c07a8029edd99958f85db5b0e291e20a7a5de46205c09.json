{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M6 12c0-1.7.7-3.2 1.8-4.2\",\n  key: \"oqkarx\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}], [\"path\", {\n  d: \"M18 12c0 1.7-.7 3.2-1.8 4.2\",\n  key: \"1eah9h\"\n}]];\nconst Disc3 = createLucideIcon(\"disc-3\", __iconNode);\nexport { __iconNode, Disc3 as default };\n//# sourceMappingURL=disc-3.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}