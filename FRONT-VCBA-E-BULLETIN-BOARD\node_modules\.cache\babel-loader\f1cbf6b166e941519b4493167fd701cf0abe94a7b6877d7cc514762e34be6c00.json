{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 5v14\",\n  key: \"1nt18q\"\n}], [\"path\", {\n  d: \"M21 12H7\",\n  key: \"13ipq5\"\n}], [\"path\", {\n  d: \"m15 18 6-6-6-6\",\n  key: \"6tx3qv\"\n}]];\nconst ArrowRightFromLine = createLucideIcon(\"arrow-right-from-line\", __iconNode);\nexport { __iconNode, ArrowRightFromLine as default };\n//# sourceMappingURL=arrow-right-from-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}