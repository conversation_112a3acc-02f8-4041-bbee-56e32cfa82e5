{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528\",\n  key: \"1q158e\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M4 22V4\",\n  key: \"1plyxx\"\n}], [\"path\", {\n  d: \"M7.656 2H8c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10.347\",\n  key: \"xj1b71\"\n}]];\nconst FlagOff = createLucideIcon(\"flag-off\", __iconNode);\nexport { __iconNode, FlagOff as default };\n//# sourceMappingURL=flag-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}