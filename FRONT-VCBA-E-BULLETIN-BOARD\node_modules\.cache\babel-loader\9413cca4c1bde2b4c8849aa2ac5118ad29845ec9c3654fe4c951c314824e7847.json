{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 7.5 8 10l2 2.5\",\n  key: \"xb17xw\"\n}], [\"path\", {\n  d: \"m14 7.5 2 2.5-2 2.5\",\n  key: \"5rap1v\"\n}], [\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}]];\nconst MessageSquareCode = createLucideIcon(\"message-square-code\", __iconNode);\nexport { __iconNode, MessageSquareCode as default };\n//# sourceMappingURL=message-square-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}