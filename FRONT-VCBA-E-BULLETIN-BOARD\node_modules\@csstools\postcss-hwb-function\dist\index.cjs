"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-value-parser"));function n(e){const t=e[0];let n=e[1],r=e[2];if(n/=100,r/=100,n+r>=1){const e=n/(n+r);return[e,e,e].map((e=>Math.round(255*e)))}const u=function(e){let t=e[0],n=e[1],r=e[2];t%=360,t<0&&(t+=360);function u(e){const u=(e+t/30)%12,o=n*Math.min(r,1-r);return r-o*Math.max(-1,Math.min(u-3,9-u,1))}return n/=100,r/=100,[u(0),u(8),u(4)]}([t,100,50]);for(let e=0;e<3;e++)u[e]*=1-n-r,u[e]+=n;return u.map((e=>Math.round(255*e)))}function r(e){const r=e.nodes.slice().filter((e=>"comment"!==e.type&&"space"!==e.type)),s=function(e){if(!function(e){if(!e||"word"!==e.type)return!1;if(!i(e))return!1;const n=t.default.unit(e.value);if(!n)return!1;const r=n.unit.toLowerCase();return!!n.number&&("deg"===r||"grad"===r||"rad"===r||"turn"===r||""===r)}(e[0]))return null;if(!u(e[1]))return null;if(!u(e[2]))return null;const n={h:t.default.unit(e[0].value),hNode:e[0],w:t.default.unit(e[1].value),wNode:e[1],b:t.default.unit(e[2].value),bNode:e[2]};if(function(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=(.9*parseFloat(e.number)).toString());case"turn":e.unit="",e.number=(360*parseFloat(e.number)).toString()}}(n.h),""!==n.h.unit)return null;a(n.w),a(n.b),function(e){return e&&"div"===e.type&&"/"===e.value}(e[3])&&(n.slash=e[3]);(u(e[4])||function(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}(e[4])||function(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}(e[4]))&&(n.alpha=e[4]);return n}(r);if(!s)return;if(r.length>3&&(!s.slash||!s.alpha))return;e.value="rgb",function(e,n,r){if(!n||!r)return;if(e.value="rgba",n.value=",",n.before="",!function(e){if(!e||"word"!==e.type)return!1;if(!i(e))return!1;const n=t.default.unit(e.value);if(!n)return!1;return!!n.number}(r))return;const u=t.default.unit(r.value);if(!u)return;"%"===u.unit&&(u.number=String(parseFloat(u.number)/100),r.value=String(u.number))}(e,s.slash,s.alpha);const[l,c,f]=[(d=s).hNode,d.wNode,d.bNode];var d;const[p,v,b]=function(e){return[e.h,e.w,e.b]}(s),h=n([p.number,v.number,b.number].map((e=>parseFloat(e))));e.nodes.splice(e.nodes.indexOf(l)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),e.nodes.splice(e.nodes.indexOf(c)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),o(e.nodes,l,{...l,value:String(h[0])}),o(e.nodes,c,{...c,value:String(h[1])}),o(e.nodes,f,{...f,value:String(h[2])})}function u(e){if(!e||"word"!==e.type)return!1;if(!i(e))return!1;const n=t.default.unit(e.value);return!!n&&("%"===n.unit||""===n.unit)}function o(e,t,n){const r=e.indexOf(t);e[r]=n}function a(e){if("%"!==e.unit)return e.unit="%",void(e.number=(100*parseFloat(e.number)).toString())}function i(e){if(!e||!e.value)return!1;try{return!1!==t.default.unit(e.value)}catch(e){return!1}}const s=e=>{const n="preserve"in Object(e)&&Boolean(e.preserve);return{postcssPlugin:"postcss-hwb-function",Declaration:(e,{result:u,postcss:o})=>{if(n&&function(e){let t=e.parent;for(;t;)if("atrule"===t.type){if("supports"===t.name.toLowerCase()&&-1!==t.params.toLowerCase().indexOf("(color: hwb(0% 0 0))"))return!0;t=t.parent}else t=t.parent;return!1}(e))return;const a=e.value;if(!a.toLowerCase().includes("hwb"))return;const i=function(e,n,u){let o;try{o=t.default(e)}catch(t){n.warn(u,`Failed to parse value '${e}' as a hwb function. Leaving the original value intact.`)}if(void 0===o)return;o.walk((e=>{e.type&&"function"===e.type&&"hwb"===e.value.toLowerCase()&&r(e)}));const a=String(o);if(a===e)return;return a}(a,e,u);if(void 0!==i)if(e.variable&&n){const t=e.parent,n=o.atRule({name:"supports",params:"(color: hwb(0% 0 0))",source:e.source}),r=t.clone();r.removeAll(),r.append(e.clone()),n.append(r),function(e,t,n){let r=t,u=t.next();for(;r&&u&&"atrule"===u.type&&"supports"===u.name.toLowerCase()&&u.params===n;)r=u,u=u.next();r.after(e)}(n,t,"(color: hwb(0% 0 0))"),e.replaceWith(e.clone({value:i}))}else n?e.cloneBefore({value:i}):e.replaceWith(e.clone({value:i}))}}};s.postcss=!0,module.exports=s;
