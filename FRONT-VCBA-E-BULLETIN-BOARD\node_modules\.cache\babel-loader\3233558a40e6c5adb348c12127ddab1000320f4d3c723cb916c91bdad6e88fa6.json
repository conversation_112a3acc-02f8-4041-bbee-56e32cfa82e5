{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8\",\n  key: \"3spt84\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"m17 22 5-5\",\n  key: \"1k6ppv\"\n}], [\"path\", {\n  d: \"m17 17 5 5\",\n  key: \"p7ous7\"\n}]];\nconst CalendarX2 = createLucideIcon(\"calendar-x-2\", __iconNode);\nexport { __iconNode, CalendarX2 as default };\n//# sourceMappingURL=calendar-x-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}