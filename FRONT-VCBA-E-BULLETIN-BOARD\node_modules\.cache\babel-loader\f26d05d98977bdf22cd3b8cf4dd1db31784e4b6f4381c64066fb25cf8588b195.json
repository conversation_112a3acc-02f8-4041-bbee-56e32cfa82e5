{"ast": null, "code": "import _objectSpread from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nlet e = -1;\nconst t = t => {\n    addEventListener(\"pageshow\", n => {\n      n.persisted && (e = n.timeStamp, t(n));\n    }, !0);\n  },\n  n = (e, t, n, i) => {\n    let o, s;\n    return r => {\n      t.value >= 0 && (r || i) && (s = t.value - (o !== null && o !== void 0 ? o : 0), (s || void 0 === o) && (o = t.value, t.delta = s, t.rating = ((e, t) => e > t[1] ? \"poor\" : e > t[0] ? \"needs-improvement\" : \"good\")(t.value, n), e(t)));\n    };\n  },\n  i = e => {\n    requestAnimationFrame(() => requestAnimationFrame(() => e()));\n  },\n  o = () => {\n    const e = performance.getEntriesByType(\"navigation\")[0];\n    if (e && e.responseStart > 0 && e.responseStart < performance.now()) return e;\n  },\n  s = () => {\n    var _e$activationStart;\n    const e = o();\n    return (_e$activationStart = e === null || e === void 0 ? void 0 : e.activationStart) !== null && _e$activationStart !== void 0 ? _e$activationStart : 0;\n  },\n  r = function (t) {\n    let n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : -1;\n    const i = o();\n    let r = \"navigate\";\n    e >= 0 ? r = \"back-forward-cache\" : i && (document.prerendering || s() > 0 ? r = \"prerender\" : document.wasDiscarded ? r = \"restore\" : i.type && (r = i.type.replace(/_/g, \"-\")));\n    return {\n      name: t,\n      value: n,\n      rating: \"good\",\n      delta: 0,\n      entries: [],\n      id: \"v5-\".concat(Date.now(), \"-\").concat(Math.floor(8999999999999 * Math.random()) + 1e12),\n      navigationType: r\n    };\n  },\n  c = new WeakMap();\nfunction a(e, t) {\n  return c.get(e) || c.set(e, new t()), c.get(e);\n}\nclass d {\n  constructor() {\n    _defineProperty(this, \"t\", void 0);\n    _defineProperty(this, \"i\", 0);\n    _defineProperty(this, \"o\", []);\n  }\n  h(e) {\n    var _this$t;\n    if (e.hadRecentInput) return;\n    const t = this.o[0],\n      n = this.o.at(-1);\n    this.i && t && n && e.startTime - n.startTime < 1e3 && e.startTime - t.startTime < 5e3 ? (this.i += e.value, this.o.push(e)) : (this.i = e.value, this.o = [e]), (_this$t = this.t) === null || _this$t === void 0 ? void 0 : _this$t.call(this, e);\n  }\n}\nconst h = function (e, t) {\n    let n = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    try {\n      if (PerformanceObserver.supportedEntryTypes.includes(e)) {\n        const i = new PerformanceObserver(e => {\n          Promise.resolve().then(() => {\n            t(e.getEntries());\n          });\n        });\n        return i.observe(_objectSpread({\n          type: e,\n          buffered: !0\n        }, n)), i;\n      }\n    } catch (_unused) {}\n  },\n  f = e => {\n    let t = !1;\n    return () => {\n      t || (e(), t = !0);\n    };\n  };\nlet u = -1;\nconst l = () => \"hidden\" !== document.visibilityState || document.prerendering ? 1 / 0 : 0,\n  m = e => {\n    \"hidden\" === document.visibilityState && u > -1 && (u = \"visibilitychange\" === e.type ? e.timeStamp : 0, v());\n  },\n  g = () => {\n    addEventListener(\"visibilitychange\", m, !0), addEventListener(\"prerenderingchange\", m, !0);\n  },\n  v = () => {\n    removeEventListener(\"visibilitychange\", m, !0), removeEventListener(\"prerenderingchange\", m, !0);\n  },\n  p = () => {\n    if (u < 0) {\n      var _globalThis$performan;\n      const e = s(),\n        n = document.prerendering ? void 0 : (_globalThis$performan = globalThis.performance.getEntriesByType(\"visibility-state\").filter(t => \"hidden\" === t.name && t.startTime > e)[0]) === null || _globalThis$performan === void 0 ? void 0 : _globalThis$performan.startTime;\n      u = n !== null && n !== void 0 ? n : l(), g(), t(() => {\n        setTimeout(() => {\n          u = l(), g();\n        });\n      });\n    }\n    return {\n      get firstHiddenTime() {\n        return u;\n      }\n    };\n  },\n  y = e => {\n    document.prerendering ? addEventListener(\"prerenderingchange\", () => e(), !0) : e();\n  },\n  b = [1800, 3e3],\n  P = function (e) {\n    let o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    y(() => {\n      const c = p();\n      let a,\n        d = r(\"FCP\");\n      const f = h(\"paint\", e => {\n        for (const t of e) \"first-contentful-paint\" === t.name && (f.disconnect(), t.startTime < c.firstHiddenTime && (d.value = Math.max(t.startTime - s(), 0), d.entries.push(t), a(!0)));\n      });\n      f && (a = n(e, d, b, o.reportAllChanges), t(t => {\n        d = r(\"FCP\"), a = n(e, d, b, o.reportAllChanges), i(() => {\n          d.value = performance.now() - t.timeStamp, a(!0);\n        });\n      }));\n    });\n  },\n  T = [.1, .25],\n  E = function (e) {\n    let o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    P(f(() => {\n      let s,\n        c = r(\"CLS\", 0);\n      const f = a(o, d),\n        u = e => {\n          for (const t of e) f.h(t);\n          f.i > c.value && (c.value = f.i, c.entries = f.o, s());\n        },\n        l = h(\"layout-shift\", u);\n      l && (s = n(e, c, T, o.reportAllChanges), document.addEventListener(\"visibilitychange\", () => {\n        \"hidden\" === document.visibilityState && (u(l.takeRecords()), s(!0));\n      }), t(() => {\n        f.i = 0, c = r(\"CLS\", 0), s = n(e, c, T, o.reportAllChanges), i(() => s());\n      }), setTimeout(s));\n    }));\n  };\nlet _ = 0,\n  L = 1 / 0,\n  M = 0;\nconst C = e => {\n  for (const t of e) t.interactionId && (L = Math.min(L, t.interactionId), M = Math.max(M, t.interactionId), _ = M ? (M - L) / 7 + 1 : 0);\n};\nlet I;\nconst w = () => {\n    var _performance$interact;\n    return I ? _ : (_performance$interact = performance.interactionCount) !== null && _performance$interact !== void 0 ? _performance$interact : 0;\n  },\n  F = () => {\n    \"interactionCount\" in performance || I || (I = h(\"event\", C, {\n      type: \"event\",\n      buffered: !0,\n      durationThreshold: 0\n    }));\n  };\nlet k = 0;\nclass A {\n  constructor() {\n    _defineProperty(this, \"u\", []);\n    _defineProperty(this, \"l\", new Map());\n    _defineProperty(this, \"m\", void 0);\n    _defineProperty(this, \"v\", void 0);\n  }\n  p() {\n    k = w(), this.u.length = 0, this.l.clear();\n  }\n  P() {\n    const e = Math.min(this.u.length - 1, Math.floor((w() - k) / 50));\n    return this.u[e];\n  }\n  h(e) {\n    var _this$m;\n    if ((_this$m = this.m) !== null && _this$m !== void 0 && _this$m.call(this, e), !e.interactionId && \"first-input\" !== e.entryType) return;\n    const t = this.u.at(-1);\n    let n = this.l.get(e.interactionId);\n    if (n || this.u.length < 10 || e.duration > t.T) {\n      var _this$v;\n      if (n ? e.duration > n.T ? (n.entries = [e], n.T = e.duration) : e.duration === n.T && e.startTime === n.entries[0].startTime && n.entries.push(e) : (n = {\n        id: e.interactionId,\n        entries: [e],\n        T: e.duration\n      }, this.l.set(n.id, n), this.u.push(n)), this.u.sort((e, t) => t.T - e.T), this.u.length > 10) {\n        const e = this.u.splice(10);\n        for (const t of e) this.l.delete(t.id);\n      }\n      (_this$v = this.v) === null || _this$v === void 0 || _this$v.call(this, n);\n    }\n  }\n}\nconst B = e => {\n    const t = globalThis.requestIdleCallback || setTimeout;\n    \"hidden\" === document.visibilityState ? e() : (e = f(e), document.addEventListener(\"visibilitychange\", e, {\n      once: !0\n    }), t(() => {\n      e(), document.removeEventListener(\"visibilitychange\", e);\n    }));\n  },\n  N = [200, 500],\n  S = function (e) {\n    let i = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    globalThis.PerformanceEventTiming && \"interactionId\" in PerformanceEventTiming.prototype && y(() => {\n      var _i$durationThreshold;\n      F();\n      let o,\n        s = r(\"INP\");\n      const c = a(i, A),\n        d = e => {\n          B(() => {\n            for (const t of e) c.h(t);\n            const t = c.P();\n            t && t.T !== s.value && (s.value = t.T, s.entries = t.entries, o());\n          });\n        },\n        f = h(\"event\", d, {\n          durationThreshold: (_i$durationThreshold = i.durationThreshold) !== null && _i$durationThreshold !== void 0 ? _i$durationThreshold : 40\n        });\n      o = n(e, s, N, i.reportAllChanges), f && (f.observe({\n        type: \"first-input\",\n        buffered: !0\n      }), document.addEventListener(\"visibilitychange\", () => {\n        \"hidden\" === document.visibilityState && (d(f.takeRecords()), o(!0));\n      }), t(() => {\n        c.p(), s = r(\"INP\"), o = n(e, s, N, i.reportAllChanges);\n      }));\n    });\n  };\nclass q {\n  constructor() {\n    _defineProperty(this, \"m\", void 0);\n  }\n  h(e) {\n    var _this$m2;\n    (_this$m2 = this.m) === null || _this$m2 === void 0 || _this$m2.call(this, e);\n  }\n}\nconst x = [2500, 4e3],\n  O = function (e) {\n    let o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    y(() => {\n      const c = p();\n      let d,\n        u = r(\"LCP\");\n      const l = a(o, q),\n        m = e => {\n          o.reportAllChanges || (e = e.slice(-1));\n          for (const t of e) l.h(t), t.startTime < c.firstHiddenTime && (u.value = Math.max(t.startTime - s(), 0), u.entries = [t], d());\n        },\n        g = h(\"largest-contentful-paint\", m);\n      if (g) {\n        d = n(e, u, x, o.reportAllChanges);\n        const s = f(() => {\n          m(g.takeRecords()), g.disconnect(), d(!0);\n        });\n        for (const e of [\"keydown\", \"click\", \"visibilitychange\"]) addEventListener(e, () => B(s), {\n          capture: !0,\n          once: !0\n        });\n        t(t => {\n          u = r(\"LCP\"), d = n(e, u, x, o.reportAllChanges), i(() => {\n            u.value = performance.now() - t.timeStamp, d(!0);\n          });\n        });\n      }\n    });\n  },\n  $ = [800, 1800],\n  D = e => {\n    document.prerendering ? y(() => D(e)) : \"complete\" !== document.readyState ? addEventListener(\"load\", () => D(e), !0) : setTimeout(e);\n  },\n  H = function (e) {\n    let i = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let c = r(\"TTFB\"),\n      a = n(e, c, $, i.reportAllChanges);\n    D(() => {\n      const d = o();\n      d && (c.value = Math.max(d.responseStart - s(), 0), c.entries = [d], a(!0), t(() => {\n        c = r(\"TTFB\", 0), a = n(e, c, $, i.reportAllChanges), a(!0);\n      }));\n    });\n  };\nexport { T as CLSThresholds, b as FCPThresholds, N as INPThresholds, x as LCPThresholds, $ as TTFBThresholds, E as onCLS, P as onFCP, S as onINP, O as onLCP, H as onTTFB };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}