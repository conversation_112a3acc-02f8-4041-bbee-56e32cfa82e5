{"ast": null, "code": "import { httpClient } from './api.service';\nclass HolidayService {\n  constructor() {\n    this.baseUrl = '/api/holidays';\n  }\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidays(year, category, countryCode) {\n    var _response$data;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    if (category) params.append('category', category);\n    if (countryCode) params.append('country_code', countryCode);\n    const response = await httpClient.get(`${this.baseUrl}?${params.toString()}`);\n    return ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.holidays) || [];\n  }\n\n  /**\n   * Get Philippine holidays for a specific year\n   */\n  async getPhilippineHolidays(year) {\n    var _response$data2;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/philippine?${params.toString()}`);\n    return ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.holidays) || [];\n  }\n\n  /**\n   * Get international holidays for a specific year\n   */\n  async getInternationalHolidays(year) {\n    var _response$data3;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/international?${params.toString()}`);\n    return ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.holidays) || [];\n  }\n\n  /**\n   * Get religious holidays for a specific year\n   */\n  async getReligiousHolidays(year) {\n    var _response$data4;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/religious?${params.toString()}`);\n    return ((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.holidays) || [];\n  }\n\n  /**\n   * Get all holidays from API sources (not database)\n   */\n  async getAllHolidaysFromAPI(year) {\n    var _response$data5;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/api-source?${params.toString()}`);\n    return ((_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.holidays) || [];\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year) {\n    var _response$data6;\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/stats?${params.toString()}`);\n    if (!((_response$data6 = response.data) !== null && _response$data6 !== void 0 && _response$data6.stats)) {\n      throw new Error('Failed to get holiday statistics');\n    }\n    return response.data.stats;\n  }\n\n  /**\n   * Preview holidays before syncing (admin only)\n   */\n  async previewHolidays(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/preview?${params.toString()}`);\n    return response.data.preview;\n  }\n\n  /**\n   * Sync holidays to database (admin only)\n   */\n  async syncHolidays(year, force = false) {\n    const response = await httpClient.post(`${this.baseUrl}/sync`, {\n      year: year || new Date().getFullYear(),\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Sync holidays for multiple years (admin only)\n   */\n  async syncMultipleYears(years, force = false) {\n    const response = await httpClient.post(`${this.baseUrl}/sync-multiple`, {\n      years,\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year (admin only)\n   */\n  async deleteAutoGeneratedHolidays(year) {\n    const response = await httpClient.delete(`${this.baseUrl}/auto-generated/${year}`);\n    return response.data.deletedCount;\n  }\n\n  /**\n   * Check if a date is a holiday\n   */\n  async isHoliday(date) {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.some(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error checking if date is holiday:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holidays for a specific date\n   */\n  async getHolidaysForDate(date) {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.filter(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error getting holidays for date:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get upcoming holidays (next 30 days)\n   */\n  async getUpcomingHolidays(days = 30) {\n    try {\n      const today = new Date();\n      const endDate = new Date();\n      endDate.setDate(today.getDate() + days);\n      const currentYear = today.getFullYear();\n      const endYear = endDate.getFullYear();\n      let holidays = [];\n\n      // Get holidays for current year\n      holidays = await this.getHolidays(currentYear);\n\n      // If the end date is in the next year, also get holidays for next year\n      if (endYear > currentYear) {\n        const nextYearHolidays = await this.getHolidays(endYear);\n        holidays = [...holidays, ...nextYearHolidays];\n      }\n\n      // Filter holidays within the date range\n      const todayStr = today.toISOString().split('T')[0];\n      const endDateStr = endDate.toISOString().split('T')[0];\n      return holidays.filter(holiday => holiday.event_date >= todayStr && holiday.event_date <= endDateStr).sort((a, b) => a.event_date.localeCompare(b.event_date));\n    } catch (error) {\n      console.error('Error getting upcoming holidays:', error);\n      return [];\n    }\n  }\n}\nexport const holidayService = new HolidayService();", "map": {"version": 3, "names": ["httpClient", "HolidayService", "constructor", "baseUrl", "getHolidays", "year", "category", "countryCode", "_response$data", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "holidays", "getPhilippineHolidays", "_response$data2", "getInternationalHolidays", "_response$data3", "getReligiousHolidays", "_response$data4", "getAllHolidaysFromAPI", "_response$data5", "getHolidayStats", "_response$data6", "stats", "Error", "previewHolidays", "preview", "syncHolidays", "force", "post", "Date", "getFullYear", "results", "syncMultipleYears", "years", "deleteAutoGeneratedHolidays", "delete", "deletedCount", "isHoliday", "date", "some", "holiday", "event_date", "error", "console", "getHolidaysForDate", "filter", "getUpcomingHolidays", "days", "today", "endDate", "setDate", "getDate", "currentYear", "endYear", "nextYearHolidays", "todayStr", "toISOString", "split", "endDateStr", "sort", "a", "b", "localeCompare", "holidayService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/holidayService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { ApiResponse } from '../types';\n\nexport interface Holiday {\n  calendar_id: number;\n  title: string;\n  description: string;\n  event_date: string;\n  category_id: number;\n  category_name: string;\n  category_color: string;\n  holiday_type: 'local' | 'international' | 'school';\n  country_code: string;\n  is_auto_generated: boolean;\n  api_source: string;\n  local_name: string;\n  holiday_types: string;\n  is_global: boolean;\n  is_fixed: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface HolidayStats {\n  total: number;\n  byCategory: Record<string, number>;\n  byCountry: Record<string, number>;\n  byType: Record<string, number>;\n  autoGenerated: number;\n  manual: number;\n}\n\nexport interface SyncResults {\n  created: number;\n  updated: number;\n  skipped: number;\n  errors: Array<{ holiday: string; error: string }>;\n}\n\nexport interface HolidayPreview {\n  new: Holiday[];\n  existing: Holiday[];\n  toUpdate: Holiday[];\n}\n\nclass HolidayService {\n  private baseUrl = '/api/holidays';\n\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidays(year?: number, category?: string, countryCode?: string): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    if (category) params.append('category', category);\n    if (countryCode) params.append('country_code', countryCode);\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}?${params.toString()}`);\n    return response.data?.holidays || [];\n  }\n\n  /**\n   * Get Philippine holidays for a specific year\n   */\n  async getPhilippineHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/philippine?${params.toString()}`);\n    return response.data?.holidays || [];\n  }\n\n  /**\n   * Get international holidays for a specific year\n   */\n  async getInternationalHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/international?${params.toString()}`);\n    return response.data?.holidays || [];\n  }\n\n  /**\n   * Get religious holidays for a specific year\n   */\n  async getReligiousHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/religious?${params.toString()}`);\n    return response.data?.holidays || [];\n  }\n\n  /**\n   * Get all holidays from API sources (not database)\n   */\n  async getAllHolidaysFromAPI(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/api-source?${params.toString()}`);\n    return response.data?.holidays || [];\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year?: number): Promise<HolidayStats> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ stats: HolidayStats }>(`${this.baseUrl}/stats?${params.toString()}`);\n    if (!response.data?.stats) {\n      throw new Error('Failed to get holiday statistics');\n    }\n    return response.data.stats;\n  }\n\n  /**\n   * Preview holidays before syncing (admin only)\n   */\n  async previewHolidays(year?: number): Promise<HolidayPreview> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ preview: HolidayPreview }>(`${this.baseUrl}/preview?${params.toString()}`);\n    return response.data.preview;\n  }\n\n  /**\n   * Sync holidays to database (admin only)\n   */\n  async syncHolidays(year?: number, force = false): Promise<SyncResults> {\n    const response = await httpClient.post<{ results: SyncResults }>(`${this.baseUrl}/sync`, {\n      year: year || new Date().getFullYear(),\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Sync holidays for multiple years (admin only)\n   */\n  async syncMultipleYears(years: number[], force = false): Promise<Record<number, SyncResults>> {\n    const response = await httpClient.post<{ results: Record<number, SyncResults> }>(`${this.baseUrl}/sync-multiple`, {\n      years,\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year (admin only)\n   */\n  async deleteAutoGeneratedHolidays(year: number): Promise<number> {\n    const response = await httpClient.delete<{ deletedCount: number }>(`${this.baseUrl}/auto-generated/${year}`);\n    return response.data.deletedCount;\n  }\n\n  /**\n   * Check if a date is a holiday\n   */\n  async isHoliday(date: string): Promise<boolean> {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.some(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error checking if date is holiday:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holidays for a specific date\n   */\n  async getHolidaysForDate(date: string): Promise<Holiday[]> {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.filter(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error getting holidays for date:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get upcoming holidays (next 30 days)\n   */\n  async getUpcomingHolidays(days = 30): Promise<Holiday[]> {\n    try {\n      const today = new Date();\n      const endDate = new Date();\n      endDate.setDate(today.getDate() + days);\n\n      const currentYear = today.getFullYear();\n      const endYear = endDate.getFullYear();\n\n      let holidays: Holiday[] = [];\n\n      // Get holidays for current year\n      holidays = await this.getHolidays(currentYear);\n\n      // If the end date is in the next year, also get holidays for next year\n      if (endYear > currentYear) {\n        const nextYearHolidays = await this.getHolidays(endYear);\n        holidays = [...holidays, ...nextYearHolidays];\n      }\n\n      // Filter holidays within the date range\n      const todayStr = today.toISOString().split('T')[0];\n      const endDateStr = endDate.toISOString().split('T')[0];\n\n      return holidays.filter(holiday => \n        holiday.event_date >= todayStr && holiday.event_date <= endDateStr\n      ).sort((a, b) => a.event_date.localeCompare(b.event_date));\n    } catch (error) {\n      console.error('Error getting upcoming holidays:', error);\n      return [];\n    }\n  }\n}\n\nexport const holidayService = new HolidayService();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AA6C1C,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,eAAe;EAAA;EAEjC;AACF;AACA;EACE,MAAMC,WAAWA,CAACC,IAAa,EAAEC,QAAiB,EAAEC,WAAoB,EAAsB;IAAA,IAAAC,cAAA;IAC5F,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAChD,IAAIN,QAAQ,EAAEG,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEL,QAAQ,CAAC;IACjD,IAAIC,WAAW,EAAEE,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEJ,WAAW,CAAC;IAE3D,MAAMM,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,IAAIM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACtG,OAAO,EAAAJ,cAAA,GAAAK,QAAQ,CAACE,IAAI,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,QAAQ,KAAI,EAAE;EACtC;;EAEA;AACF;AACA;EACE,MAAMC,qBAAqBA,CAACZ,IAAa,EAAsB;IAAA,IAAAa,eAAA;IAC7D,MAAMT,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,eAAeM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACjH,OAAO,EAAAM,eAAA,GAAAL,QAAQ,CAACE,IAAI,cAAAG,eAAA,uBAAbA,eAAA,CAAeF,QAAQ,KAAI,EAAE;EACtC;;EAEA;AACF;AACA;EACE,MAAMG,wBAAwBA,CAACd,IAAa,EAAsB;IAAA,IAAAe,eAAA;IAChE,MAAMX,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,kBAAkBM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACpH,OAAO,EAAAQ,eAAA,GAAAP,QAAQ,CAACE,IAAI,cAAAK,eAAA,uBAAbA,eAAA,CAAeJ,QAAQ,KAAI,EAAE;EACtC;;EAEA;AACF;AACA;EACE,MAAMK,oBAAoBA,CAAChB,IAAa,EAAsB;IAAA,IAAAiB,eAAA;IAC5D,MAAMb,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,cAAcM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChH,OAAO,EAAAU,eAAA,GAAAT,QAAQ,CAACE,IAAI,cAAAO,eAAA,uBAAbA,eAAA,CAAeN,QAAQ,KAAI,EAAE;EACtC;;EAEA;AACF;AACA;EACE,MAAMO,qBAAqBA,CAAClB,IAAa,EAAsB;IAAA,IAAAmB,eAAA;IAC7D,MAAMf,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,eAAeM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACjH,OAAO,EAAAY,eAAA,GAAAX,QAAQ,CAACE,IAAI,cAAAS,eAAA,uBAAbA,eAAA,CAAeR,QAAQ,KAAI,EAAE;EACtC;;EAEA;AACF;AACA;EACE,MAAMS,eAAeA,CAACpB,IAAa,EAAyB;IAAA,IAAAqB,eAAA;IAC1D,MAAMjB,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA0B,GAAG,IAAI,CAACX,OAAO,UAAUM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC5G,IAAI,GAAAc,eAAA,GAACb,QAAQ,CAACE,IAAI,cAAAW,eAAA,eAAbA,eAAA,CAAeC,KAAK,GAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IACA,OAAOf,QAAQ,CAACE,IAAI,CAACY,KAAK;EAC5B;;EAEA;AACF;AACA;EACE,MAAME,eAAeA,CAACxB,IAAa,EAA2B;IAC5D,MAAMI,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMb,UAAU,CAACc,GAAG,CAA8B,GAAG,IAAI,CAACX,OAAO,YAAYM,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAClH,OAAOC,QAAQ,CAACE,IAAI,CAACe,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMC,YAAYA,CAAC1B,IAAa,EAAE2B,KAAK,GAAG,KAAK,EAAwB;IACrE,MAAMnB,QAAQ,GAAG,MAAMb,UAAU,CAACiC,IAAI,CAA2B,GAAG,IAAI,CAAC9B,OAAO,OAAO,EAAE;MACvFE,IAAI,EAAEA,IAAI,IAAI,IAAI6B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACtCH;IACF,CAAC,CAAC;IACF,OAAOnB,QAAQ,CAACE,IAAI,CAACqB,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAACC,KAAe,EAAEN,KAAK,GAAG,KAAK,EAAwC;IAC5F,MAAMnB,QAAQ,GAAG,MAAMb,UAAU,CAACiC,IAAI,CAA2C,GAAG,IAAI,CAAC9B,OAAO,gBAAgB,EAAE;MAChHmC,KAAK;MACLN;IACF,CAAC,CAAC;IACF,OAAOnB,QAAQ,CAACE,IAAI,CAACqB,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMG,2BAA2BA,CAAClC,IAAY,EAAmB;IAC/D,MAAMQ,QAAQ,GAAG,MAAMb,UAAU,CAACwC,MAAM,CAA2B,GAAG,IAAI,CAACrC,OAAO,mBAAmBE,IAAI,EAAE,CAAC;IAC5G,OAAOQ,QAAQ,CAACE,IAAI,CAAC0B,YAAY;EACnC;;EAEA;AACF;AACA;EACE,MAAMC,SAASA,CAACC,IAAY,EAAoB;IAC9C,IAAI;MACF,MAAMtC,IAAI,GAAG,IAAI6B,IAAI,CAACS,IAAI,CAAC,CAACR,WAAW,CAAC,CAAC;MACzC,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAACZ,WAAW,CAACC,IAAI,CAAC;MAC7C,OAAOW,QAAQ,CAAC4B,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,UAAU,KAAKH,IAAI,CAAC;IAC9D,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAME,kBAAkBA,CAACN,IAAY,EAAsB;IACzD,IAAI;MACF,MAAMtC,IAAI,GAAG,IAAI6B,IAAI,CAACS,IAAI,CAAC,CAACR,WAAW,CAAC,CAAC;MACzC,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAACZ,WAAW,CAACC,IAAI,CAAC;MAC7C,OAAOW,QAAQ,CAACkC,MAAM,CAACL,OAAO,IAAIA,OAAO,CAACC,UAAU,KAAKH,IAAI,CAAC;IAChE,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMI,mBAAmBA,CAACC,IAAI,GAAG,EAAE,EAAsB;IACvD,IAAI;MACF,MAAMC,KAAK,GAAG,IAAInB,IAAI,CAAC,CAAC;MACxB,MAAMoB,OAAO,GAAG,IAAIpB,IAAI,CAAC,CAAC;MAC1BoB,OAAO,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAAC;MAEvC,MAAMK,WAAW,GAAGJ,KAAK,CAAClB,WAAW,CAAC,CAAC;MACvC,MAAMuB,OAAO,GAAGJ,OAAO,CAACnB,WAAW,CAAC,CAAC;MAErC,IAAInB,QAAmB,GAAG,EAAE;;MAE5B;MACAA,QAAQ,GAAG,MAAM,IAAI,CAACZ,WAAW,CAACqD,WAAW,CAAC;;MAE9C;MACA,IAAIC,OAAO,GAAGD,WAAW,EAAE;QACzB,MAAME,gBAAgB,GAAG,MAAM,IAAI,CAACvD,WAAW,CAACsD,OAAO,CAAC;QACxD1C,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAG2C,gBAAgB,CAAC;MAC/C;;MAEA;MACA,MAAMC,QAAQ,GAAGP,KAAK,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD,MAAMC,UAAU,GAAGT,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEtD,OAAO9C,QAAQ,CAACkC,MAAM,CAACL,OAAO,IAC5BA,OAAO,CAACC,UAAU,IAAIc,QAAQ,IAAIf,OAAO,CAACC,UAAU,IAAIiB,UAC1D,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,UAAU,CAACqB,aAAa,CAACD,CAAC,CAACpB,UAAU,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,EAAE;IACX;EACF;AACF;AAEA,OAAO,MAAMqB,cAAc,GAAG,IAAInE,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}