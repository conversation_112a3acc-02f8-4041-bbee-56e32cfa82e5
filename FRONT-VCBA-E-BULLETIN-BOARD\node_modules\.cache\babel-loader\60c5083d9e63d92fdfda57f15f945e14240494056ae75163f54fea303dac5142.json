{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}], [\"path\", {\n  d: \"M11.5 13.5a2.5 2.5 0 0 1 0 3\",\n  key: \"1fccat\"\n}], [\"path\", {\n  d: \"M15 12a5 5 0 0 1 0 6\",\n  key: \"ps46cm\"\n}]];\nconst FileVolume2 = createLucideIcon(\"file-volume-2\", __iconNode);\nexport { __iconNode, FileVolume2 as default };\n//# sourceMappingURL=file-volume-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}