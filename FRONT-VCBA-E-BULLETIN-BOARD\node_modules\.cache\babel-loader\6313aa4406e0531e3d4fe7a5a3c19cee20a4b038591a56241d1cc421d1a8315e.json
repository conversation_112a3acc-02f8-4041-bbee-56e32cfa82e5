{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useCallback,useRef}from'react';import{announcementService}from'../services';import{adminAnnouncementServiceWithToken,studentAnnouncementServiceWithToken}from'../services/announcementService';import{ADMIN_AUTH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY}from'../config/constants';// Hook for managing announcements\nexport const useAnnouncements=function(initialFilters){let useAdminService=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const[announcements,setAnnouncements]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const[pagination,setPagination]=useState({page:1,totalPages:0,total:0,hasNext:false,hasPrev:false});const[filters,setFilters]=useState(initialFilters||{page:1,limit:20,sort_by:'created_at',sort_order:'DESC'});// Track current user context to detect changes\nconst currentUserContextRef=useRef('');// Choose the appropriate service based on the parameter - use role-specific services with proper tokens\nconst service=useAdminService?adminAnnouncementServiceWithToken:studentAnnouncementServiceWithToken;// Function to get current user context identifier\nconst getCurrentUserContext=useCallback(()=>{const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);// Create a unique identifier for the current user context\n// Priority: if both tokens exist, determine which one is currently active\n// by checking which service we're using\nif(useAdminService&&adminToken){return\"admin:\".concat(adminToken.substring(0,10));}else if(!useAdminService&&studentToken){return\"student:\".concat(studentToken.substring(0,10));}else if(adminToken&&!studentToken){return\"admin:\".concat(adminToken.substring(0,10));}else if(studentToken&&!adminToken){return\"student:\".concat(studentToken.substring(0,10));}return'anonymous';},[useAdminService]);// Function to clear cache when user context changes\nconst clearCacheIfUserChanged=useCallback(()=>{const currentContext=getCurrentUserContext();if(currentUserContextRef.current&&currentUserContextRef.current!==currentContext){console.log('🔄 User context changed, clearing announcement cache',{previous:currentUserContextRef.current,current:currentContext});// Clear all cached data when user context changes\nsetAnnouncements([]);setPagination({page:1,totalPages:0,total:0,hasNext:false,hasPrev:false});setError(undefined);}currentUserContextRef.current=currentContext;},[getCurrentUserContext]);const fetchAnnouncements=useCallback(async()=>{try{// Clear cache if user context changed\nclearCacheIfUserChanged();setLoading(true);setError(undefined);console.log('🔍 Fetching announcements with context:',{useAdminService,currentContext:getCurrentUserContext(),filters});const response=await service.getAnnouncements(filters);if(response.success&&response.data){var _response$data$announ;console.log('✅ Announcements fetched successfully:',{count:((_response$data$announ=response.data.announcements)===null||_response$data$announ===void 0?void 0:_response$data$announ.length)||0,userContext:getCurrentUserContext()});setAnnouncements(response.data.announcements||[]);setPagination(response.data.pagination);}else{setError(response.message||'Failed to fetch announcements');}}catch(err){console.error('❌ Error fetching announcements:',err);setError(err.message||'An error occurred while fetching announcements');}finally{setLoading(false);}},[JSON.stringify(filters),useAdminService,clearCacheIfUserChanged,getCurrentUserContext,service]);const refresh=useCallback(async()=>{await fetchAnnouncements();},[fetchAnnouncements]);const createAnnouncement=useCallback(async data=>{try{setLoading(true);setError(undefined);// Type assertion based on service type - admin service supports FormData\nconst response=useAdminService?await service.createAnnouncement(data):await service.createAnnouncement(data);if(response.success){// Refresh the list to get the new announcement\nawait fetchAnnouncements();}else{throw new Error(response.message||'Failed to create announcement');}}catch(err){setError(err.message||'An error occurred while creating announcement');throw err;}finally{setLoading(false);}},[fetchAnnouncements,service,useAdminService]);const updateAnnouncement=useCallback(async(id,data)=>{try{setLoading(true);setError(undefined);// Type assertion based on service type - admin service supports FormData\nconst response=useAdminService?await service.updateAnnouncement(id,data):await service.updateAnnouncement(id,data);if(response.success&&response.data){// Update the announcement in the local state\nsetAnnouncements(prev=>prev.map(announcement=>{var _response$data;return announcement.announcement_id===id?_objectSpread(_objectSpread({},announcement),(_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.announcement):announcement;}));}else{throw new Error(response.message||'Failed to update announcement');}}catch(err){setError(err.message||'An error occurred while updating announcement');throw err;}finally{setLoading(false);}},[service,useAdminService]);const deleteAnnouncement=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await service.deleteAnnouncement(id);if(response.success){// Remove the announcement from local state\nsetAnnouncements(prev=>prev.filter(announcement=>announcement.announcement_id!==id));}else{throw new Error(response.message||'Failed to delete announcement');}}catch(err){setError(err.message||'An error occurred while deleting announcement');throw err;}finally{setLoading(false);}},[service]);const publishAnnouncement=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await service.publishAnnouncement(id);if(response.success&&response.data){// Update the announcement status in local state\nsetAnnouncements(prev=>prev.map(announcement=>{var _response$data2;return announcement.announcement_id===id?_objectSpread(_objectSpread({},announcement),(_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.announcement):announcement;}));}else{throw new Error(response.message||'Failed to publish announcement');}}catch(err){setError(err.message||'An error occurred while publishing announcement');throw err;}finally{setLoading(false);}},[service]);const unpublishAnnouncement=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await service.unpublishAnnouncement(id);if(response.success&&response.data){// Update the announcement status in local state\nsetAnnouncements(prev=>prev.map(announcement=>{var _response$data3;return announcement.announcement_id===id?_objectSpread(_objectSpread({},announcement),(_response$data3=response.data)===null||_response$data3===void 0?void 0:_response$data3.announcement):announcement;}));}else{throw new Error(response.message||'Failed to unpublish announcement');}}catch(err){setError(err.message||'An error occurred while unpublishing announcement');throw err;}finally{setLoading(false);}},[service]);const likeAnnouncement=useCallback(async function(id){let reactionId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;try{setError(undefined);const response=await service.addReaction(id,reactionId);if(response.success){// Update the announcement reaction in local state\nsetAnnouncements(prev=>prev.map(announcement=>announcement.announcement_id===id?_objectSpread(_objectSpread({},announcement),{},{reaction_count:(announcement.reaction_count||0)+(announcement.user_reaction?0:1),user_reaction:{reaction_id:reactionId,reaction_name:'like',reaction_emoji:'❤️'}}):announcement));}else{throw new Error(response.message||'Failed to like announcement');}}catch(err){setError(err.message||'An error occurred while liking announcement');throw err;}},[service]);const unlikeAnnouncement=useCallback(async id=>{try{setError(undefined);const response=await service.removeReaction(id);if(response.success){// Update the announcement reaction in local state\nsetAnnouncements(prev=>prev.map(announcement=>announcement.announcement_id===id?_objectSpread(_objectSpread({},announcement),{},{reaction_count:Math.max((announcement.reaction_count||0)-(announcement.user_reaction?1:0),0),user_reaction:undefined}):announcement));}else{throw new Error(response.message||'Failed to unlike announcement');}}catch(err){setError(err.message||'An error occurred while unliking announcement');throw err;}},[service]);// Update filters and refetch\nconst updateFilters=useCallback(newFilters=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),newFilters));},[]);// Force refresh when service type changes (admin vs student)\nuseEffect(()=>{console.log('🔄 Service type changed, forcing refresh:',{useAdminService});clearCacheIfUserChanged();fetchAnnouncements();},[useAdminService,clearCacheIfUserChanged]);useEffect(()=>{fetchAnnouncements();},[fetchAnnouncements]);return{announcements,loading,error,pagination,filters,setFilters:updateFilters,refresh,createAnnouncement,updateAnnouncement,deleteAnnouncement,publishAnnouncement,unpublishAnnouncement,likeAnnouncement,unlikeAnnouncement};};// Hook for managing categories\nexport const useCategories=()=>{const[categories,setCategories]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const fetchCategories=useCallback(async()=>{try{setLoading(true);setError(undefined);const response=await announcementService.getCategories();if(response.success&&response.data){setCategories(response.data.categories);}else{setError(response.message||'Failed to fetch categories');}}catch(err){setError(err.message||'An error occurred while fetching categories');}finally{setLoading(false);}},[]);const refresh=useCallback(async()=>{await fetchCategories();},[fetchCategories]);useEffect(()=>{fetchCategories();},[fetchCategories]);return{categories,loading,error,refresh};};// Hook for managing hierarchical categories (categories with subcategories)\nexport const useHierarchicalCategories=()=>{const[categories,setCategories]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const fetchCategories=useCallback(async()=>{try{setLoading(true);setError(undefined);console.log('🔧 Fetching categories with subcategories from API');// Use the working hierarchical endpoint\nconst categoriesResponse=await announcementService.getCategoriesWithSubcategories();if(categoriesResponse.success&&categoriesResponse.data){const categories=categoriesResponse.data.categories;// Use real subcategories from API\nsetCategories(categories);console.log('✅ Categories loaded with real subcategories from API:',{count:categories.length,categoriesWithSubcategories:categories.filter(cat=>cat.subcategories&&cat.subcategories.length>0).length,categories:categories.map(cat=>{var _cat$subcategories;return{id:cat.category_id,name:cat.name,subcategoriesCount:((_cat$subcategories=cat.subcategories)===null||_cat$subcategories===void 0?void 0:_cat$subcategories.length)||0};})});}else{setError('Failed to fetch categories');}}catch(err){console.error('Error fetching categories:',err);setError(err.message||'An error occurred while fetching categories');}finally{setLoading(false);}},[]);const refresh=useCallback(async()=>{await fetchCategories();},[fetchCategories]);useEffect(()=>{fetchCategories();},[fetchCategories]);return{categories,loading,error,refresh};};// Hook for managing reaction types\nexport const useReactionTypes=()=>{const[reactionTypes,setReactionTypes]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const fetchReactionTypes=useCallback(async()=>{try{setLoading(true);setError(undefined);const response=await announcementService.getReactionTypes();if(response.success&&response.data){setReactionTypes(response.data.reactionTypes);}else{setError(response.message||'Failed to fetch reaction types');}}catch(err){setError(err.message||'An error occurred while fetching reaction types');}finally{setLoading(false);}},[]);const refresh=useCallback(async()=>{await fetchReactionTypes();},[fetchReactionTypes]);useEffect(()=>{fetchReactionTypes();},[fetchReactionTypes]);return{reactionTypes,loading,error,refresh};};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}