{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"2obqm\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"14\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"6d4xhi\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"7\",\n  x: \"14\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"nxv5o0\"\n}]];\nconst LayoutPanelLeft = createLucideIcon(\"layout-panel-left\", __iconNode);\nexport { __iconNode, LayoutPanelLeft as default };\n//# sourceMappingURL=layout-panel-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}