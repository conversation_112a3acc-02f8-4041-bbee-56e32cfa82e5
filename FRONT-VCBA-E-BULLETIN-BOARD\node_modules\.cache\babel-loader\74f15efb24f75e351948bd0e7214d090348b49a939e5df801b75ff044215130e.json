{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useCallback,useRef}from'react';import{adminCommentServiceWithToken,studentCommentServiceWithToken}from'../services/commentService';import{ADMIN_AUTH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY}from'../config/constants';// Hook return type\n// Hook for managing comments\nexport const useComments=(announcementId,calendarId,currentUserType)=>{// Ensure either announcementId or calendarId is provided, but not both\nif(!announcementId&&!calendarId){throw new Error('Either announcementId or calendarId must be provided');}if(announcementId&&calendarId){throw new Error('Cannot provide both announcementId and calendarId');}// Determine the appropriate service based on current user context\nconst getService=useCallback(()=>{// If user type is explicitly provided, use that\nif(currentUserType==='admin'){console.log('🎯 useComments - Using admin service (explicit)');return adminCommentServiceWithToken;}if(currentUserType==='student'){console.log('🎯 useComments - Using student service (explicit)');return studentCommentServiceWithToken;}// Auto-detect based on current page context and available tokens\nconst currentPath=window.location.pathname;const isAdminPage=currentPath.includes('/admin');const isStudentPage=currentPath.includes('/student');const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);if(isAdminPage&&adminToken){console.log('🎯 useComments - Using admin service (admin page context)');return adminCommentServiceWithToken;}if(isStudentPage&&studentToken){console.log('🎯 useComments - Using student service (student page context)');return studentCommentServiceWithToken;}// Fallback: prioritize student service if student token exists\nif(studentToken){console.log('🎯 useComments - Using student service (fallback)');return studentCommentServiceWithToken;}// Last resort: use admin service\nconsole.log('🎯 useComments - Using admin service (fallback)');return adminCommentServiceWithToken;},[currentUserType]);const service=getService();const[comments,setComments]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const[pagination,setPagination]=useState({page:1,totalPages:0,total:0,hasNext:false,hasPrev:false});// Track current user context to detect changes\nconst currentUserContextRef=useRef('');// Function to get current user context identifier\nconst getCurrentUserContext=useCallback(()=>{const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);// Create a unique identifier for the current user context\nif(adminToken){return\"admin:\".concat(adminToken.substring(0,10));}else if(studentToken){return\"student:\".concat(studentToken.substring(0,10));}return'anonymous';},[]);// Function to clear cache when user context changes\nconst clearCacheIfUserChanged=useCallback(()=>{const currentContext=getCurrentUserContext();if(currentUserContextRef.current&&currentUserContextRef.current!==currentContext){console.log('🔄 User context changed, clearing comment cache',{previous:currentUserContextRef.current,current:currentContext,announcementId});setComments([]);setPagination({page:1,totalPages:0,total:0,hasNext:false,hasPrev:false});}currentUserContextRef.current=currentContext;},[getCurrentUserContext,announcementId,calendarId]);const fetchComments=useCallback(async()=>{if(!announcementId&&!calendarId)return;try{// Clear cache if user context changed\nclearCacheIfUserChanged();setLoading(true);setError(undefined);let response;if(announcementId){response=await service.getCommentsByAnnouncement(announcementId,{page:1,limit:50,sort_by:'created_at',sort_order:'ASC'});}else if(calendarId){response=await service.getCommentsByCalendar(calendarId,{page:1,limit:50,sort_by:'created_at',sort_order:'ASC'});}else{// This should never happen due to validation above, but handle it gracefully\nsetError('No valid ID provided for fetching comments');return;}if(response&&response.success&&response.data){setComments(response.data.comments||[]);setPagination(response.data.pagination);}else{var _response;setError(((_response=response)===null||_response===void 0?void 0:_response.message)||'Failed to fetch comments');}}catch(err){setError(err.message||'An error occurred while fetching comments');}finally{setLoading(false);}},[announcementId,calendarId,clearCacheIfUserChanged,service]);const refresh=useCallback(async()=>{await fetchComments();},[fetchComments]);const createComment=useCallback(async data=>{try{setLoading(true);setError(undefined);const response=await service.createComment(data);if(response.success){// Refresh the list to get the new comment\nawait fetchComments();}else{throw new Error(response.message||'Failed to create comment');}}catch(err){setError(err.message||'An error occurred while creating comment');throw err;}finally{setLoading(false);}},[fetchComments,service]);const createReply=useCallback(async(parentCommentId,data)=>{try{setLoading(true);setError(undefined);const response=await service.createReply(parentCommentId,data);if(response.success){// Refresh the list to get the new reply\nawait fetchComments();}else{throw new Error(response.message||'Failed to create reply');}}catch(err){setError(err.message||'An error occurred while creating reply');throw err;}finally{setLoading(false);}},[fetchComments,service]);const updateComment=useCallback(async(id,data)=>{try{setLoading(true);setError(undefined);const response=await service.updateComment(id,data);if(response.success&&response.data){// Update the comment in the local state\nsetComments(prev=>prev.map(comment=>{var _response$data;return comment.comment_id===id?_objectSpread(_objectSpread({},comment),(_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.comment):comment;}));}else{throw new Error(response.message||'Failed to update comment');}}catch(err){setError(err.message||'An error occurred while updating comment');throw err;}finally{setLoading(false);}},[service]);const deleteComment=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await service.deleteComment(id);if(response.success){// Remove the comment from local state\nsetComments(prev=>prev.filter(comment=>comment.comment_id!==id));}else{throw new Error(response.message||'Failed to delete comment');}}catch(err){setError(err.message||'An error occurred while deleting comment');throw err;}finally{setLoading(false);}},[service]);const likeComment=useCallback(async function(id){let reactionId=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;try{setError(undefined);const response=await service.addReaction(id,reactionId);if(response.success){// Update the comment reaction count and user reaction in local state\nsetComments(prev=>prev.map(comment=>{if(comment.comment_id===id){// Only increment count if user hasn't reacted before\nconst newCount=comment.user_reaction?comment.reaction_count||0// Already reacted, don't change count\n:(comment.reaction_count||0)+1;// New reaction, increment count\nreturn _objectSpread(_objectSpread({},comment),{},{reaction_count:newCount,user_reaction:{reaction_id:reactionId,reaction_name:'like',reaction_emoji:'❤️'}});}return comment;}));}else{throw new Error(response.message||'Failed to like comment');}}catch(err){setError(err.message||'An error occurred while liking comment');throw err;}},[service]);const unlikeComment=useCallback(async id=>{try{setError(undefined);const response=await service.removeReaction(id);if(response.success){// Update the comment reaction count and remove user reaction in local state\nsetComments(prev=>prev.map(comment=>{if(comment.comment_id===id){// Only decrement count if user had reacted before\nconst newCount=comment.user_reaction?Math.max((comment.reaction_count||0)-1,0)// Had reaction, decrement count\n:comment.reaction_count||0;// No reaction, don't change count\nreturn _objectSpread(_objectSpread({},comment),{},{reaction_count:newCount,user_reaction:undefined});}return comment;}));}else{throw new Error(response.message||'Failed to unlike comment');}}catch(err){setError(err.message||'An error occurred while unliking comment');throw err;}},[service]);const flagComment=useCallback(async(id,reason)=>{try{setError(undefined);const response=await service.flagComment(id,reason);if(response.success){// Update the comment flag status in local state\nsetComments(prev=>prev.map(comment=>comment.comment_id===id?_objectSpread(_objectSpread({},comment),{},{is_flagged:true}):comment));}else{throw new Error(response.message||'Failed to flag comment');}}catch(err){setError(err.message||'An error occurred while flagging comment');throw err;}},[service]);useEffect(()=>{fetchComments();},[fetchComments]);return{comments,loading,error,pagination,refresh,createComment,createReply,updateComment,deleteComment,likeComment,unlikeComment,flagComment};};// Utility functions for comment operations\nexport const formatCommentDate=dateString=>{const date=new Date(dateString);const now=new Date();const diffInSeconds=Math.floor((now.getTime()-date.getTime())/1000);if(diffInSeconds<60){return'Just now';}else if(diffInSeconds<3600){const minutes=Math.floor(diffInSeconds/60);return\"\".concat(minutes,\" minute\").concat(minutes>1?'s':'',\" ago\");}else if(diffInSeconds<86400){const hours=Math.floor(diffInSeconds/3600);return\"\".concat(hours,\" hour\").concat(hours>1?'s':'',\" ago\");}else if(diffInSeconds<604800){const days=Math.floor(diffInSeconds/86400);return\"\".concat(days,\" day\").concat(days>1?'s':'',\" ago\");}else{return date.toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric'});}};export const getCommentDepth=(comment,allComments)=>{let depth=0;let currentComment=comment;while(currentComment.parent_comment_id){depth++;const parentComment=allComments.find(c=>c.comment_id===currentComment.parent_comment_id);if(!parentComment)break;currentComment=parentComment;}return depth;};export const buildCommentTree=comments=>{const commentMap=new Map();const rootComments=[];// Initialize all comments with empty replies array\ncomments.forEach(comment=>{commentMap.set(comment.comment_id,_objectSpread(_objectSpread({},comment),{},{replies:[]}));});// Build the tree structure\ncomments.forEach(comment=>{const commentWithReplies=commentMap.get(comment.comment_id);if(comment.parent_comment_id){const parent=commentMap.get(comment.parent_comment_id);if(parent){parent.replies.push(commentWithReplies);}}else{rootComments.push(commentWithReplies);}});return rootComments;};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}