{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 2 8 8\",\n  key: \"1v6059\"\n}], [\"path\", {\n  d: \"m22 2-8 8\",\n  key: \"173r8a\"\n}], [\"ellipse\", {\n  cx: \"12\",\n  cy: \"9\",\n  rx: \"10\",\n  ry: \"5\",\n  key: \"liohsx\"\n}], [\"path\", {\n  d: \"M7 13.4v7.9\",\n  key: \"1yi6u9\"\n}], [\"path\", {\n  d: \"M12 14v8\",\n  key: \"1tn2tj\"\n}], [\"path\", {\n  d: \"M17 13.4v7.9\",\n  key: \"eqz2v3\"\n}], [\"path\", {\n  d: \"M2 9v8a10 5 0 0 0 20 0V9\",\n  key: \"1750ul\"\n}]];\nconst Drum = createLucideIcon(\"drum\", __iconNode);\nexport { __iconNode, Drum as default };\n//# sourceMappingURL=drum.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}