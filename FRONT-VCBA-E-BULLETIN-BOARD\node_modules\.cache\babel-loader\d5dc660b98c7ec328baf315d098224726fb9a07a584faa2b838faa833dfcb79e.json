{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21\",\n  key: \"9csbqa\"\n}], [\"path\", {\n  d: \"m14 19.5 3-3 3 3\",\n  key: \"9vmjn0\"\n}], [\"path\", {\n  d: \"M17 22v-5.5\",\n  key: \"1aa6fl\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}]];\nconst ImageUp = createLucideIcon(\"image-up\", __iconNode);\nexport { __iconNode, ImageUp as default };\n//# sourceMappingURL=image-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}