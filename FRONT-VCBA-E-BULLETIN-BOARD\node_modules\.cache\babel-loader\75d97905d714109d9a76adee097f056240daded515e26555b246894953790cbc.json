{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"14\",\n  y: \"14\",\n  width: \"4\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"p02svl\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"4\",\n  width: \"4\",\n  height: \"6\",\n  rx: \"2\",\n  key: \"xm4xkj\"\n}], [\"path\", {\n  d: \"M6 20h4\",\n  key: \"1i6q5t\"\n}], [\"path\", {\n  d: \"M14 10h4\",\n  key: \"ru81e7\"\n}], [\"path\", {\n  d: \"M6 14h2v6\",\n  key: \"16z9wg\"\n}], [\"path\", {\n  d: \"M14 4h2v6\",\n  key: \"1idq9u\"\n}]];\nconst Binary = createLucideIcon(\"binary\", __iconNode);\nexport { __iconNode, Binary as default };\n//# sourceMappingURL=binary.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}