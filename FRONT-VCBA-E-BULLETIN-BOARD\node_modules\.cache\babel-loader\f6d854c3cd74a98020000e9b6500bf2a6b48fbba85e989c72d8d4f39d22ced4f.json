{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z\",\n  key: \"1wis1t\"\n}], [\"path\", {\n  d: \"M14 3v4a2 2 0 0 0 2 2h4\",\n  key: \"36rjfy\"\n}], [\"path\", {\n  d: \"M8 13h.01\",\n  key: \"1sbv64\"\n}], [\"path\", {\n  d: \"M16 13h.01\",\n  key: \"wip0gl\"\n}], [\"path\", {\n  d: \"M10 16s.8 1 2 1c1.3 0 2-1 2-1\",\n  key: \"1vvgv3\"\n}]];\nconst Sticker = createLucideIcon(\"sticker\", __iconNode);\nexport { __iconNode, Sticker as default };\n//# sourceMappingURL=sticker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}