{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"2\",\n  key: \"1305pl\"\n}], [\"path\", {\n  d: \"M7.2 7.9 3 11v9c0 .6.4 1 1 1h16c.6 0 1-.4 1-1v-9c0-2-3-6-7-8l-3.6 2.6\",\n  key: \"xle13f\"\n}], [\"path\", {\n  d: \"M16 13H3\",\n  key: \"1wpj08\"\n}], [\"path\", {\n  d: \"M16 17H3\",\n  key: \"3lvfcd\"\n}]];\nconst CakeSlice = createLucideIcon(\"cake-slice\", __iconNode);\nexport { __iconNode, CakeSlice as default };\n//# sourceMappingURL=cake-slice.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}