{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 6 6 18\",\n  key: \"1bl5f8\"\n}], [\"path\", {\n  d: \"m6 6 12 12\",\n  key: \"d8bk6v\"\n}]];\nconst X = createLucideIcon(\"x\", __iconNode);\nexport { __iconNode, X as default };\n//# sourceMappingURL=x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}