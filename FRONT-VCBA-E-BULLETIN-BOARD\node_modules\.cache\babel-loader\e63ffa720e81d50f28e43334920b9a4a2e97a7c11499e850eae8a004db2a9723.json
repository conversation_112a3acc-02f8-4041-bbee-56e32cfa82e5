{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2\",\n  key: \"1nmvlm\"\n}], [\"circle\", {\n  cx: \"14\",\n  cy: \"15\",\n  r: \"1\",\n  key: \"1gm4qj\"\n}]];\nconst FolderOpenDot = createLucideIcon(\"folder-open-dot\", __iconNode);\nexport { __iconNode, FolderOpenDot as default };\n//# sourceMappingURL=folder-open-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}