{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.67 8H18a2 2 0 0 1 2 2v4.33\",\n  key: \"7az073\"\n}], [\"path\", {\n  d: \"M2 14h2\",\n  key: \"vft8re\"\n}], [\"path\", {\n  d: \"M20 14h2\",\n  key: \"4cs60a\"\n}], [\"path\", {\n  d: \"M22 22 2 2\",\n  key: \"1r8tn9\"\n}], [\"path\", {\n  d: \"M8 8H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 1.414-.586\",\n  key: \"s09a7a\"\n}], [\"path\", {\n  d: \"M9 13v2\",\n  key: \"rq6x2g\"\n}], [\"path\", {\n  d: \"M9.67 4H12v2.33\",\n  key: \"110xot\"\n}]];\nconst BotOff = createLucideIcon(\"bot-off\", __iconNode);\nexport { __iconNode, BotOff as default };\n//# sourceMappingURL=bot-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}