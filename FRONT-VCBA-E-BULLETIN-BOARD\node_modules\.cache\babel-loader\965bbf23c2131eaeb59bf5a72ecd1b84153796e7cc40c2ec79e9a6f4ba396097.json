{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 20v-8a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v8\",\n  key: \"1wm6mi\"\n}], [\"path\", {\n  d: \"M5 10V6a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v4\",\n  key: \"4k93s5\"\n}], [\"path\", {\n  d: \"M3 18h18\",\n  key: \"1h113x\"\n}]];\nconst BedSingle = createLucideIcon(\"bed-single\", __iconNode);\nexport { __iconNode, BedSingle as default };\n//# sourceMappingURL=bed-single.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}