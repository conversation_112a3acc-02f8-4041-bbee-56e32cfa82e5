{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProfileAvatar.tsx\";\nimport React from 'react';\nimport { ProfilePictureService } from '../../services/profile-picture.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileAvatar = ({\n  profilePicture,\n  firstName,\n  lastName,\n  size = 'medium',\n  gradientColors = {\n    from: '#3b82f6',\n    to: '#fbbf24'\n  },\n  className = '',\n  style = {}\n}) => {\n  const getSizeStyles = () => {\n    switch (size) {\n      case 'small':\n        return {\n          width: '32px',\n          height: '32px',\n          fontSize: '0.875rem'\n        };\n      case 'large':\n        return {\n          width: '64px',\n          height: '64px',\n          fontSize: '1.5rem'\n        };\n      case 'medium':\n      default:\n        return {\n          width: '40px',\n          height: '40px',\n          fontSize: '1rem'\n        };\n    }\n  };\n  const sizeStyles = getSizeStyles();\n  const profilePictureUrl = ProfilePictureService.getProfilePictureUrl(profilePicture);\n  const initials = ProfilePictureService.getUserInitials(firstName, lastName);\n  const avatarStyle = {\n    ...sizeStyles,\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: '600',\n    color: 'white',\n    overflow: 'hidden',\n    flexShrink: 0,\n    ...style\n  };\n  if (profilePictureUrl) {\n    avatarStyle.backgroundImage = `url(${profilePictureUrl})`;\n    avatarStyle.backgroundSize = 'cover';\n    avatarStyle.backgroundPosition = 'center';\n    avatarStyle.backgroundRepeat = 'no-repeat';\n  } else {\n    avatarStyle.background = `linear-gradient(135deg, ${gradientColors.from} 0%, ${gradientColors.to} 100%)`;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    style: avatarStyle,\n    children: !profilePictureUrl && initials\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_c = ProfileAvatar;\nexport default ProfileAvatar;\nvar _c;\n$RefreshReg$(_c, \"ProfileAvatar\");", "map": {"version": 3, "names": ["React", "ProfilePictureService", "jsxDEV", "_jsxDEV", "ProfileAvatar", "profilePicture", "firstName", "lastName", "size", "gradientColors", "from", "to", "className", "style", "getSizeStyles", "width", "height", "fontSize", "sizeStyles", "profilePictureUrl", "getProfilePictureUrl", "initials", "getUserInitials", "avatarS<PERSON>le", "borderRadius", "display", "alignItems", "justifyContent", "fontWeight", "color", "overflow", "flexShrink", "backgroundImage", "backgroundSize", "backgroundPosition", "backgroundRepeat", "background", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProfileAvatar.tsx"], "sourcesContent": ["import React from 'react';\nimport { ProfilePictureService } from '../../services/profile-picture.service';\n\ninterface ProfileAvatarProps {\n  profilePicture?: string;\n  firstName?: string;\n  lastName?: string;\n  size?: 'small' | 'medium' | 'large';\n  gradientColors?: {\n    from: string;\n    to: string;\n  };\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst ProfileAvatar: React.FC<ProfileAvatarProps> = ({\n  profilePicture,\n  firstName,\n  lastName,\n  size = 'medium',\n  gradientColors = {\n    from: '#3b82f6',\n    to: '#fbbf24'\n  },\n  className = '',\n  style = {}\n}) => {\n  const getSizeStyles = () => {\n    switch (size) {\n      case 'small':\n        return {\n          width: '32px',\n          height: '32px',\n          fontSize: '0.875rem'\n        };\n      case 'large':\n        return {\n          width: '64px',\n          height: '64px',\n          fontSize: '1.5rem'\n        };\n      case 'medium':\n      default:\n        return {\n          width: '40px',\n          height: '40px',\n          fontSize: '1rem'\n        };\n    }\n  };\n\n  const sizeStyles = getSizeStyles();\n  const profilePictureUrl = ProfilePictureService.getProfilePictureUrl(profilePicture);\n  const initials = ProfilePictureService.getUserInitials(firstName, lastName);\n\n  const avatarStyle: React.CSSProperties = {\n    ...sizeStyles,\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: '600',\n    color: 'white',\n    overflow: 'hidden',\n    flexShrink: 0,\n    ...style\n  };\n\n  if (profilePictureUrl) {\n    avatarStyle.backgroundImage = `url(${profilePictureUrl})`;\n    avatarStyle.backgroundSize = 'cover';\n    avatarStyle.backgroundPosition = 'center';\n    avatarStyle.backgroundRepeat = 'no-repeat';\n  } else {\n    avatarStyle.background = `linear-gradient(135deg, ${gradientColors.from} 0%, ${gradientColors.to} 100%)`;\n  }\n\n  return (\n    <div className={className} style={avatarStyle}>\n      {!profilePictureUrl && initials}\n    </div>\n  );\n};\n\nexport default ProfileAvatar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,QAAQ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe/E,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,cAAc;EACdC,SAAS;EACTC,QAAQ;EACRC,IAAI,GAAG,QAAQ;EACfC,cAAc,GAAG;IACfC,IAAI,EAAE,SAAS;IACfC,EAAE,EAAE;EACN,CAAC;EACDC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQN,IAAI;MACV,KAAK,OAAO;QACV,OAAO;UACLO,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,QAAQ,EAAE;QACZ,CAAC;MACH,KAAK,OAAO;QACV,OAAO;UACLF,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,QAAQ,EAAE;QACZ,CAAC;MACH,KAAK,QAAQ;MACb;QACE,OAAO;UACLF,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,QAAQ,EAAE;QACZ,CAAC;IACL;EACF,CAAC;EAED,MAAMC,UAAU,GAAGJ,aAAa,CAAC,CAAC;EAClC,MAAMK,iBAAiB,GAAGlB,qBAAqB,CAACmB,oBAAoB,CAACf,cAAc,CAAC;EACpF,MAAMgB,QAAQ,GAAGpB,qBAAqB,CAACqB,eAAe,CAAChB,SAAS,EAAEC,QAAQ,CAAC;EAE3E,MAAMgB,WAAgC,GAAG;IACvC,GAAGL,UAAU;IACbM,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC;IACb,GAAGlB;EACL,CAAC;EAED,IAAIM,iBAAiB,EAAE;IACrBI,WAAW,CAACS,eAAe,GAAG,OAAOb,iBAAiB,GAAG;IACzDI,WAAW,CAACU,cAAc,GAAG,OAAO;IACpCV,WAAW,CAACW,kBAAkB,GAAG,QAAQ;IACzCX,WAAW,CAACY,gBAAgB,GAAG,WAAW;EAC5C,CAAC,MAAM;IACLZ,WAAW,CAACa,UAAU,GAAG,2BAA2B3B,cAAc,CAACC,IAAI,QAAQD,cAAc,CAACE,EAAE,QAAQ;EAC1G;EAEA,oBACER,OAAA;IAAKS,SAAS,EAAEA,SAAU;IAACC,KAAK,EAAEU,WAAY;IAAAc,QAAA,EAC3C,CAAClB,iBAAiB,IAAIE;EAAQ;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEV,CAAC;AAACC,EAAA,GAnEItC,aAA2C;AAqEjD,eAAeA,aAAa;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}