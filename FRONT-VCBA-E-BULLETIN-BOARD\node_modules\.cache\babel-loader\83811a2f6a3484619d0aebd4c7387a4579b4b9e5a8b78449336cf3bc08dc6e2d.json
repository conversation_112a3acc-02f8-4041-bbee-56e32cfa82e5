{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 3h12l4 6-10 13L2 9Z\",\n  key: \"1pcd5k\"\n}], [\"path\", {\n  d: \"M11 3 8 9l4 13 4-13-3-6\",\n  key: \"1fcu3u\"\n}], [\"path\", {\n  d: \"M2 9h20\",\n  key: \"16fsjt\"\n}]];\nconst Gem = createLucideIcon(\"gem\", __iconNode);\nexport { __iconNode, Gem as default };\n//# sourceMappingURL=gem.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}