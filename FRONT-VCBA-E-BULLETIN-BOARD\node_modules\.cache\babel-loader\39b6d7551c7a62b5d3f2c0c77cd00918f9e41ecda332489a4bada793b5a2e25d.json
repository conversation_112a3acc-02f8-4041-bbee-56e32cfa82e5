{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3h.01\",\n  key: \"159qn6\"\n}], [\"path\", {\n  d: \"M7 5h.01\",\n  key: \"1hq22a\"\n}], [\"path\", {\n  d: \"M11 7h.01\",\n  key: \"1osv80\"\n}], [\"path\", {\n  d: \"M3 7h.01\",\n  key: \"1xzrh3\"\n}], [\"path\", {\n  d: \"M7 9h.01\",\n  key: \"19b3jx\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"4\",\n  x: \"15\",\n  y: \"5\",\n  key: \"mri9e4\"\n}], [\"path\", {\n  d: \"m19 9 2 2v10c0 .6-.4 1-1 1h-6c-.6 0-1-.4-1-1V11l2-2\",\n  key: \"aib6hk\"\n}], [\"path\", {\n  d: \"m13 14 8-2\",\n  key: \"1d7bmk\"\n}], [\"path\", {\n  d: \"m13 19 8-2\",\n  key: \"1y2vml\"\n}]];\nconst SprayCan = createLucideIcon(\"spray-can\", __iconNode);\nexport { __iconNode, SprayCan as default };\n//# sourceMappingURL=spray-can.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}