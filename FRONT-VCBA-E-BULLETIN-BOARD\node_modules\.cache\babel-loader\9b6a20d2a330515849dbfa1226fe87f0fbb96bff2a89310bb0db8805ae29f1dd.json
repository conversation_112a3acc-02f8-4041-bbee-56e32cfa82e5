{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 9 9 4 4 9\",\n  key: \"1af5af\"\n}], [\"path\", {\n  d: \"M20 20h-7a4 4 0 0 1-4-4V4\",\n  key: \"1blwi3\"\n}]];\nconst CornerLeftUp = createLucideIcon(\"corner-left-up\", __iconNode);\nexport { __iconNode, CornerLeftUp as default };\n//# sourceMappingURL=corner-left-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}