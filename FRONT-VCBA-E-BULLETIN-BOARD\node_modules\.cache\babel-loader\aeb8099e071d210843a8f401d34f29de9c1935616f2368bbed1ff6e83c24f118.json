{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 16a3 3 0 0 1 2.24 5\",\n  key: \"1epib5\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}], [\"path\", {\n  d: \"M18 21h-8a4 4 0 0 1-4-4 7 7 0 0 1 7-7h.2L9.6 6.4a1 1 0 1 1 2.8-2.8L15.8 7h.2c3.3 0 6 2.7 6 6v1a2 2 0 0 1-2 2h-1a3 3 0 0 0-3 3\",\n  key: \"ue9ozu\"\n}], [\"path\", {\n  d: \"M20 8.54V4a2 2 0 1 0-4 0v3\",\n  key: \"49iql8\"\n}], [\"path\", {\n  d: \"M7.612 12.524a3 3 0 1 0-1.6 4.3\",\n  key: \"1e33i0\"\n}]];\nconst Rabbit = createLucideIcon(\"rabbit\", __iconNode);\nexport { __iconNode, Rabbit as default };\n//# sourceMappingURL=rabbit.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}