{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"path\", {\n  d: \"M17 10V4h-2\",\n  key: \"zcsr5x\"\n}], [\"path\", {\n  d: \"M15 10h4\",\n  key: \"id2lce\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"14\",\n  width: \"4\",\n  height: \"6\",\n  ry: \"2\",\n  key: \"33xykx\"\n}]];\nconst ArrowUp10 = createLucideIcon(\"arrow-up-1-0\", __iconNode);\nexport { __iconNode, ArrowUp10 as default };\n//# sourceMappingURL=arrow-up-1-0.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}