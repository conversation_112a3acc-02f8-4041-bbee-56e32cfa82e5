{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(({\n    className,\n    ...props\n  }, ref) => createElement(Icon, {\n    ref,\n    iconNode,\n    className: mergeClasses(`lucide-${toKebabCase(toPascalCase(iconName))}`, `lucide-${iconName}`, className),\n    ...props\n  }));\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}