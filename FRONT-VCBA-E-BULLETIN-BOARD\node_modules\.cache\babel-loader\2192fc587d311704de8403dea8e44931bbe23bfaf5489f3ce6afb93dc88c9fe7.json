{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Edit3, Trash2 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef(null);\n  const dropdownRef = useRef(null);\n  const avatarRef = useRef(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && avatarRef.current && !avatarRef.current.contains(event.target)) {\n        setShowDropdown(false);\n      }\n    };\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n    try {\n      await onUpload(pendingFile);\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = e => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    var _fileInputRef$current;\n    setShowDropdown(false);\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = async () => {\n    setShowDropdown(false);\n    const confirmed = window.confirm('Are you sure you want to remove your profile picture? You can always upload a new one later.');\n    if (!confirmed) return;\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: avatarRef,\n        style: {\n          position: 'relative',\n          width: `${size}px`,\n          height: `${size}px`,\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n          transition: 'all 0.3s ease',\n          cursor: 'pointer',\n          boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n          transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: `${size * 0.3}px`,\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(34, 197, 94, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.3s ease',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            size: size * 0.2,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.9)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${size * 0.2}px`,\n              height: `${size * 0.2}px`,\n              border: '3px solid #e8f5e8',\n              borderTop: '3px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: handleCameraClick,\n        style: {\n          position: 'absolute',\n          bottom: '8px',\n          right: '8px',\n          width: `${size * 0.25}px`,\n          height: `${size * 0.25}px`,\n          background: '#ffffff',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n          border: '2px solid #ffffff',\n          transition: 'all 0.2s ease',\n          transform: isHovered ? 'scale(1.1)' : 'scale(1)',\n          zIndex: 10\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#f3f4f6';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = '#ffffff';\n        },\n        children: /*#__PURE__*/_jsxDEV(Camera, {\n          size: size * 0.12,\n          color: \"#374151\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), showDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: dropdownRef,\n        style: {\n          position: 'absolute',\n          top: `${size + 10}px`,\n          right: '0',\n          background: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n          border: '1px solid #e8f5e8',\n          minWidth: '180px',\n          zIndex: 1000,\n          overflow: 'hidden',\n          animation: 'dropdownFadeIn 0.2s ease-out'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleChangePhoto,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '12px 16px',\n            border: 'none',\n            background: 'transparent',\n            textAlign: 'left',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#374151',\n            transition: 'background-color 0.2s ease',\n            opacity: isLoading ? 0.6 : 1\n          },\n          onMouseEnter: e => {\n            if (!isLoading) e.currentTarget.style.background = '#f9fafb';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.background = 'transparent';\n          },\n          children: [/*#__PURE__*/_jsxDEV(Edit3, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), \"Change Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRemovePhoto,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '12px 16px',\n            border: 'none',\n            background: 'transparent',\n            textAlign: 'left',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#dc2626',\n            transition: 'background-color 0.2s ease',\n            opacity: isLoading ? 0.6 : 1,\n            borderTop: '1px solid #f3f4f6'\n          },\n          onMouseEnter: e => {\n            if (!isLoading) e.currentTarget.style.background = '#fef2f2';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.background = 'transparent';\n          },\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this), \"Remove Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), hasPendingChanges && showActions && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        display: 'flex',\n        gap: '0.75rem',\n        animation: 'fadeIn 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSave,\n        disabled: isLoading,\n        style: {\n          background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          padding: '0.75rem 1.5rem',\n          fontWeight: '600',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          opacity: isLoading ? 0.6 : 1,\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '14px',\n          transition: 'all 0.2s ease',\n          boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n        },\n        onMouseEnter: e => {\n          if (!isLoading) {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n          }\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'translateY(0)';\n          e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this), \"Save Changes\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCancel,\n        disabled: isLoading,\n        style: {\n          background: 'transparent',\n          border: '2px solid #e8f5e8',\n          borderRadius: '8px',\n          padding: '0.75rem 1.5rem',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          color: '#6b7280',\n          opacity: isLoading ? 0.6 : 1,\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '14px',\n          fontWeight: '600',\n          transition: 'all 0.2s ease'\n        },\n        onMouseEnter: e => {\n          if (!isLoading) {\n            e.currentTarget.style.borderColor = '#d1d5db';\n            e.currentTarget.style.background = '#f9fafb';\n          }\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.borderColor = '#e8f5e8';\n          e.currentTarget.style.background = 'transparent';\n        },\n        children: [/*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), \"Cancel\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n      onChange: handleInputChange,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '1rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '12px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem',\n        animation: 'slideDown 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 20,\n        style: {\n          marginTop: '1px',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          },\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            lineHeight: '1.4'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '1rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '12px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem',\n        animation: 'slideDown 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 20,\n        style: {\n          marginTop: '1px',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          },\n          children: \"Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            lineHeight: '1.4'\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        @keyframes fadeIn {\n          0% { opacity: 0; }\n          100% { opacity: 1; }\n        }\n\n        @keyframes slideUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes slideDown {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        @keyframes dropdownFadeIn {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"qCz3Y8HHscP+zcY9DtnLUxcMExg=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "Edit3", "Trash2", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "size", "showActions", "_s", "preview", "setPreview", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showDropdown", "setShowDropdown", "pendingFile", "setPendingFile", "isHovered", "setIsHovered", "fileInputRef", "dropdownRef", "avatar<PERSON><PERSON>", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "result", "onerror", "readAsDataURL", "handleSave", "setTimeout", "err", "message", "handleCancel", "handleInputChange", "_e$target$files", "files", "value", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleCameraClick", "stopPropagation", "handleChangePhoto", "_fileInputRef$current", "click", "handleRemovePhoto", "confirmed", "window", "confirm", "displayImage", "hasImage", "Boolean", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "children", "style", "position", "display", "ref", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "boxShadow", "transform", "onDragOver", "onDragLeave", "onDrop", "onMouseEnter", "onMouseLeave", "src", "alt", "onLoad", "onError", "objectFit", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "alignItems", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "onClick", "zIndex", "currentTarget", "min<PERSON><PERSON><PERSON>", "disabled", "padding", "textAlign", "gap", "marginTop", "borderColor", "accept", "onChange", "flexShrink", "marginBottom", "lineHeight", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Edit3, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n  size?: number; // Size in pixels for the circular avatar\n  showActions?: boolean; // Whether to show action buttons\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n\n  const [pendingFile, setPendingFile] = useState<File | null>(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const avatarRef = useRef<HTMLDivElement>(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&\n          avatarRef.current && !avatarRef.current.contains(event.target as Node)) {\n        setShowDropdown(false);\n      }\n    };\n\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n\n    try {\n      await onUpload(pendingFile);\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    setShowDropdown(false);\n    fileInputRef.current?.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = async () => {\n    setShowDropdown(false);\n\n    const confirmed = window.confirm('Are you sure you want to remove your profile picture? You can always upload a new one later.');\n\n    if (!confirmed) return;\n\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      {/* Facebook-style Circular Avatar with Camera Overlay */}\n      <div style={{ position: 'relative', display: 'inline-block' }}>\n        <div\n          ref={avatarRef}\n          style={{\n            position: 'relative',\n            width: `${size}px`,\n            height: `${size}px`,\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n            transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: `${size * 0.3}px`,\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Drag Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(34, 197, 94, 0.8)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.3s ease',\n              borderRadius: '50%'\n            }}\n          >\n            <Upload size={size * 0.2} color=\"white\" />\n          </div>\n\n          {/* Loading Overlay */}\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.9)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: '50%'\n              }}\n            >\n              <div\n                style={{\n                  width: `${size * 0.2}px`,\n                  height: `${size * 0.2}px`,\n                  border: '3px solid #e8f5e8',\n                  borderTop: '3px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Facebook-style Camera Icon Overlay (Bottom-right) */}\n        <div\n          onClick={handleCameraClick}\n          style={{\n            position: 'absolute',\n            bottom: '8px',\n            right: '8px',\n            width: `${size * 0.25}px`,\n            height: `${size * 0.25}px`,\n            background: '#ffffff',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n            border: '2px solid #ffffff',\n            transition: 'all 0.2s ease',\n            transform: isHovered ? 'scale(1.1)' : 'scale(1)',\n            zIndex: 10\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#f3f4f6';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = '#ffffff';\n          }}\n        >\n          <Camera size={size * 0.12} color=\"#374151\" />\n        </div>\n\n        {/* Dropdown Menu */}\n        {showDropdown && (\n          <div\n            ref={dropdownRef}\n            style={{\n              position: 'absolute',\n              top: `${size + 10}px`,\n              right: '0',\n              background: 'white',\n              borderRadius: '12px',\n              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n              border: '1px solid #e8f5e8',\n              minWidth: '180px',\n              zIndex: 1000,\n              overflow: 'hidden',\n              animation: 'dropdownFadeIn 0.2s ease-out'\n            }}\n          >\n            <button\n              onClick={handleChangePhoto}\n              disabled={isLoading}\n              style={{\n                width: '100%',\n                padding: '12px 16px',\n                border: 'none',\n                background: 'transparent',\n                textAlign: 'left',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '12px',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: '#374151',\n                transition: 'background-color 0.2s ease',\n                opacity: isLoading ? 0.6 : 1\n              }}\n              onMouseEnter={(e) => {\n                if (!isLoading) e.currentTarget.style.background = '#f9fafb';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.background = 'transparent';\n              }}\n            >\n              <Edit3 size={16} />\n              Change Photo\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemovePhoto}\n                disabled={isLoading}\n                style={{\n                  width: '100%',\n                  padding: '12px 16px',\n                  border: 'none',\n                  background: 'transparent',\n                  textAlign: 'left',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  color: '#dc2626',\n                  transition: 'background-color 0.2s ease',\n                  opacity: isLoading ? 0.6 : 1,\n                  borderTop: '1px solid #f3f4f6'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isLoading) e.currentTarget.style.background = '#fef2f2';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = 'transparent';\n                }}\n              >\n                <Trash2 size={16} />\n                Remove Photo\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Save/Cancel Buttons (only show when there are pending changes) */}\n      {hasPendingChanges && showActions && (\n        <div style={{\n          marginTop: '1rem',\n          display: 'flex',\n          gap: '0.75rem',\n          animation: 'fadeIn 0.3s ease-out'\n        }}>\n          <button\n            onClick={handleSave}\n            disabled={isLoading}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '14px',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n            }}\n            onMouseEnter={(e) => {\n              if (!isLoading) {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              }\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n            }}\n          >\n            <CheckCircle size={16} />\n            Save Changes\n          </button>\n\n          <button\n            onClick={handleCancel}\n            disabled={isLoading}\n            style={{\n              background: 'transparent',\n              border: '2px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '14px',\n              fontWeight: '600',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              if (!isLoading) {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.background = '#f9fafb';\n              }\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.borderColor = '#e8f5e8';\n              e.currentTarget.style.background = 'transparent';\n            }}\n          >\n            <X size={16} />\n            Cancel\n          </button>\n        </div>\n      )}\n\n      {/* File Input */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n        onChange={handleInputChange}\n        style={{ display: 'none' }}\n      />\n\n\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '12px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'flex-start',\n          gap: '0.75rem',\n          animation: 'slideDown 0.3s ease-out'\n        }}>\n          <AlertCircle size={20} style={{ marginTop: '1px', flexShrink: 0 }} />\n          <div>\n            <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>Error</div>\n            <div style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>{error}</div>\n          </div>\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '12px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'flex-start',\n          gap: '0.75rem',\n          animation: 'slideDown 0.3s ease-out'\n        }}>\n          <CheckCircle size={20} style={{ marginTop: '1px', flexShrink: 0 }} />\n          <div>\n            <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>Success</div>\n            <div style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>{success}</div>\n          </div>\n        </div>\n      )}\n\n      {/* CSS Animations */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        @keyframes fadeIn {\n          0% { opacity: 0; }\n          100% { opacity: 1; }\n        }\n\n        @keyframes slideUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes slideDown {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        @keyframes dropdownFadeIn {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa1F,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,GAAG;EACVC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMqC,YAAY,GAAGpC,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMqC,WAAW,GAAGrC,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMsC,SAAS,GAAGtC,MAAM,CAAiB,IAAI,CAAC;;EAE9C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMqC,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,IAC1EL,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC1EZ,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC5E;EACF,CAAC,EAAE,CAACT,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMiB,YAAY,GAAG9C,WAAW,CAAE+C,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAAC7B,IAAI,GAAG8B,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,gBAAgB,GAAGpD,WAAW,CAAE+C,IAAU,IAAK;IACnDrB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,KAAK,CAAC;IAEtB,MAAMuB,eAAe,GAAGP,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIM,eAAe,EAAE;MACnB3B,QAAQ,CAAC2B,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBpC,UAAU,EAAAoC,SAAA,GAACD,CAAC,CAACf,MAAM,cAAAgB,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACtC3B,cAAc,CAACe,IAAI,CAAC;IACtB,CAAC;IACDO,MAAM,CAACM,OAAO,GAAG,MAAM;MACrBlC,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACD4B,MAAM,CAACO,aAAa,CAACd,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgB,UAAU,GAAG9D,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC+B,WAAW,EAAE;IAElB,IAAI;MACF,MAAMjB,QAAQ,CAACiB,WAAW,CAAC;MAC3BH,UAAU,CAAC,uCAAuC,CAAC;MACnDI,cAAc,CAAC,IAAI,CAAC;MACpB+B,UAAU,CAAC,MAAMnC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOoC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3D3C,UAAU,CAAC,IAAI,CAAC;MAChBU,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACD,WAAW,EAAEjB,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAMoD,YAAY,GAAGlE,WAAW,CAAC,MAAM;IACrCsB,UAAU,CAAC,IAAI,CAAC;IAChBU,cAAc,CAAC,IAAI,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyC,iBAAiB,GAAIV,CAAsC,IAAK;IAAA,IAAAW,eAAA;IACpE,MAAMrB,IAAI,IAAAqB,eAAA,GAAGX,CAAC,CAACf,MAAM,CAAC2B,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIrB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;IACA;IACAU,CAAC,CAACf,MAAM,CAAC4B,KAAK,GAAG,EAAE;EACrB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAId,CAAkB,IAAK;IAC7CA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBhD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMiD,eAAe,GAAIhB,CAAkB,IAAK;IAC9CA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBhD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMkD,UAAU,GAAIjB,CAAkB,IAAK;IACzCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBhD,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMuB,IAAI,GAAGU,CAAC,CAACkB,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC;IACpC,IAAItB,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAInB,CAAmB,IAAK;IACjDA,CAAC,CAACoB,eAAe,CAAC,CAAC;IACnB/C,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC9BjD,eAAe,CAAC,KAAK,CAAC;IACtB,CAAAiD,qBAAA,GAAA5C,YAAY,CAACK,OAAO,cAAAuC,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCnD,eAAe,CAAC,KAAK,CAAC;IAEtB,MAAMoD,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,8FAA8F,CAAC;IAEhI,IAAI,CAACF,SAAS,EAAE;IAEhBxD,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBN,UAAU,CAAC,IAAI,CAAC;IAChBU,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMjB,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDmC,UAAU,CAAC,MAAMnC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOoC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAGhE,OAAO,IAAIT,cAAc;EAC9C,MAAM0E,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;EACtC,MAAMG,iBAAiB,GAAGD,OAAO,CAACxD,WAAW,CAAC;;EAE9C;EACA0D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C9E,cAAc;IACdS,OAAO;IACPgE,YAAY;IACZC,QAAQ;IACRE;EACF,CAAC,CAAC;EAEF,oBACE9E,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAA0E,QAAA,gBAEpDjF,OAAA;MAAKkF,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAe,CAAE;MAAAH,QAAA,gBAC5DjF,OAAA;QACEqF,GAAG,EAAE1D,SAAU;QACfuD,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBG,KAAK,EAAE,GAAG9E,IAAI,IAAI;UAClB+E,MAAM,EAAE,GAAG/E,IAAI,IAAI;UACnBgF,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE7E,UAAU,GAAG,mBAAmB,GAAGiE,iBAAiB,GAAG,mBAAmB,GAAG,mBAAmB;UACxGa,UAAU,EAAE,eAAe;UAC3BC,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAEtE,SAAS,GAAG,gCAAgC,GAAG,+BAA+B;UACzFuE,SAAS,EAAEvE,SAAS,GAAG,aAAa,GAAG;QACzC,CAAE;QACFwE,UAAU,EAAElC,cAAe;QAC3BmC,WAAW,EAAEjC,eAAgB;QAC7BkC,MAAM,EAAEjC,UAAW;QACnBkC,YAAY,EAAEA,CAAA,KAAM1E,YAAY,CAAC,IAAI,CAAE;QACvC2E,YAAY,EAAEA,CAAA,KAAM3E,YAAY,CAAC,KAAK,CAAE;QAAAyD,QAAA,GAEvCL,QAAQ,gBACP5E,OAAA;UACEoG,GAAG,EAAEzB,YAAa;UAClB0B,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMvB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEL,YAAY,CAAE;UACxE4B,OAAO,EAAGxD,CAAC,IAAKgC,OAAO,CAAChE,KAAK,CAAC,yBAAyB,EAAE4D,YAAY,EAAE5B,CAAC,CAAE;UAC1EmC,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE,OAAO;YAClBb,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAElF,SAAS,GAAG,iBAAiB,GAAG;UAC1C;QAAE;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF7G,OAAA;UACEkF,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduB,UAAU,EAAE,mDAAmD;YAC/D1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,GAAG3G,IAAI,GAAG,GAAG,IAAI;YAC3BmF,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAElF,SAAS,GAAG,iBAAiB,GAAG;UAC1C,CAAE;UAAA0D,QAAA,EAED9E;QAAY;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD7G,OAAA;UACEkF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBiC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,wBAAwB;YACpC1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAE3G,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3B8E,UAAU,EAAE,mBAAmB;YAC/BH,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFjF,OAAA,CAACR,MAAM;YAACgB,IAAI,EAAEA,IAAI,GAAG,GAAI;YAACyG,KAAK,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EAGLvG,SAAS,iBACRN,OAAA;UACEkF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBiC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,0BAA0B;YACtC1B,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBxB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFjF,OAAA;YACEkF,KAAK,EAAE;cACLI,KAAK,EAAE,GAAG9E,IAAI,GAAG,GAAG,IAAI;cACxB+E,MAAM,EAAE,GAAG/E,IAAI,GAAG,GAAG,IAAI;cACzBkF,MAAM,EAAE,mBAAmB;cAC3B+B,SAAS,EAAE,mBAAmB;cAC9BjC,YAAY,EAAE,KAAK;cACnBkC,SAAS,EAAE;YACb;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA;QACE2H,OAAO,EAAEzD,iBAAkB;QAC3BgB,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBoC,MAAM,EAAE,KAAK;UACbD,KAAK,EAAE,KAAK;UACZhC,KAAK,EAAE,GAAG9E,IAAI,GAAG,IAAI,IAAI;UACzB+E,MAAM,EAAE,GAAG/E,IAAI,GAAG,IAAI,IAAI;UAC1BsG,UAAU,EAAE,SAAS;UACrBtB,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,MAAM;UACf2B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBpB,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAE,+BAA+B;UAC1CH,MAAM,EAAE,mBAAmB;UAC3BC,UAAU,EAAE,eAAe;UAC3BG,SAAS,EAAEvE,SAAS,GAAG,YAAY,GAAG,UAAU;UAChDqG,MAAM,EAAE;QACV,CAAE;QACF1B,YAAY,EAAGnD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,SAAS;QAC9C,CAAE;QACFX,YAAY,EAAGpD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,SAAS;QAC9C,CAAE;QAAA7B,QAAA,eAEFjF,OAAA,CAACN,MAAM;UAACc,IAAI,EAAEA,IAAI,GAAG,IAAK;UAACyG,KAAK,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EAGL1F,YAAY,iBACXnB,OAAA;QACEqF,GAAG,EAAE3D,WAAY;QACjBwD,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBiC,GAAG,EAAE,GAAG5G,IAAI,GAAG,EAAE,IAAI;UACrB8G,KAAK,EAAE,GAAG;UACVR,UAAU,EAAE,OAAO;UACnBtB,YAAY,EAAE,MAAM;UACpBK,SAAS,EAAE,iCAAiC;UAC5CH,MAAM,EAAE,mBAAmB;UAC3BoC,QAAQ,EAAE,OAAO;UACjBF,MAAM,EAAE,IAAI;UACZnC,QAAQ,EAAE,QAAQ;UAClBiC,SAAS,EAAE;QACb,CAAE;QAAAzC,QAAA,gBAEFjF,OAAA;UACE2H,OAAO,EAAEvD,iBAAkB;UAC3B2D,QAAQ,EAAEzH,SAAU;UACpB4E,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACb0C,OAAO,EAAE,WAAW;YACpBtC,MAAM,EAAE,MAAM;YACdoB,UAAU,EAAE,aAAa;YACzBmB,SAAS,EAAE,MAAM;YACjBrC,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC7C8E,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBmB,GAAG,EAAE,MAAM;YACXf,QAAQ,EAAE,MAAM;YAChBD,UAAU,EAAE,KAAK;YACjBD,KAAK,EAAE,SAAS;YAChBtB,UAAU,EAAE,4BAA4B;YACxC6B,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG;UAC7B,CAAE;UACF4F,YAAY,EAAGnD,CAAC,IAAK;YACnB,IAAI,CAACzC,SAAS,EAAEyC,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,SAAS;UAC9D,CAAE;UACFX,YAAY,EAAGpD,CAAC,IAAK;YACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,aAAa;UAClD,CAAE;UAAA7B,QAAA,gBAEFjF,OAAA,CAACH,KAAK;YAACW,IAAI,EAAE;UAAG;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERjC,QAAQ,iBACP5E,OAAA;UACE2H,OAAO,EAAEpD,iBAAkB;UAC3BwD,QAAQ,EAAEzH,SAAU;UACpB4E,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACb0C,OAAO,EAAE,WAAW;YACpBtC,MAAM,EAAE,MAAM;YACdoB,UAAU,EAAE,aAAa;YACzBmB,SAAS,EAAE,MAAM;YACjBrC,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC7C8E,OAAO,EAAE,MAAM;YACf2B,UAAU,EAAE,QAAQ;YACpBmB,GAAG,EAAE,MAAM;YACXf,QAAQ,EAAE,MAAM;YAChBD,UAAU,EAAE,KAAK;YACjBD,KAAK,EAAE,SAAS;YAChBtB,UAAU,EAAE,4BAA4B;YACxC6B,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;YAC5BmH,SAAS,EAAE;UACb,CAAE;UACFvB,YAAY,EAAGnD,CAAC,IAAK;YACnB,IAAI,CAACzC,SAAS,EAAEyC,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,SAAS;UAC9D,CAAE;UACFX,YAAY,EAAGpD,CAAC,IAAK;YACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,aAAa;UAClD,CAAE;UAAA7B,QAAA,gBAEFjF,OAAA,CAACF,MAAM;YAACU,IAAI,EAAE;UAAG;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL/B,iBAAiB,IAAIrE,WAAW,iBAC/BT,OAAA;MAAKkF,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjB/C,OAAO,EAAE,MAAM;QACf8C,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAzC,QAAA,gBACAjF,OAAA;QACE2H,OAAO,EAAEvE,UAAW;QACpB2E,QAAQ,EAAEzH,SAAU;QACpB4E,KAAK,EAAE;UACL4B,UAAU,EAAE,mDAAmD;UAC/DG,KAAK,EAAE,OAAO;UACdvB,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnBwC,OAAO,EAAE,gBAAgB;UACzBd,UAAU,EAAE,KAAK;UACjBtB,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7CkH,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;UAC5B8E,OAAO,EAAE,MAAM;UACf2B,UAAU,EAAE,QAAQ;UACpBmB,GAAG,EAAE,QAAQ;UACbf,QAAQ,EAAE,MAAM;UAChBxB,UAAU,EAAE,eAAe;UAC3BE,SAAS,EAAE;QACb,CAAE;QACFK,YAAY,EAAGnD,CAAC,IAAK;UACnB,IAAI,CAACzC,SAAS,EAAE;YACdyC,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACY,SAAS,GAAG,kBAAkB;YACpD/C,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACW,SAAS,GAAG,mCAAmC;UACvE;QACF,CAAE;QACFM,YAAY,EAAGpD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACY,SAAS,GAAG,eAAe;UACjD/C,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACW,SAAS,GAAG,kCAAkC;QACtE,CAAE;QAAAZ,QAAA,gBAEFjF,OAAA,CAACJ,WAAW;UAACY,IAAI,EAAE;QAAG;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET7G,OAAA;QACE2H,OAAO,EAAEnE,YAAa;QACtBuE,QAAQ,EAAEzH,SAAU;QACpB4E,KAAK,EAAE;UACL4B,UAAU,EAAE,aAAa;UACzBpB,MAAM,EAAE,mBAAmB;UAC3BF,YAAY,EAAE,KAAK;UACnBwC,OAAO,EAAE,gBAAgB;UACzBpC,MAAM,EAAEtF,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7C2G,KAAK,EAAE,SAAS;UAChBO,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;UAC5B8E,OAAO,EAAE,MAAM;UACf2B,UAAU,EAAE,QAAQ;UACpBmB,GAAG,EAAE,QAAQ;UACbf,QAAQ,EAAE,MAAM;UAChBD,UAAU,EAAE,KAAK;UACjBvB,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGnD,CAAC,IAAK;UACnB,IAAI,CAACzC,SAAS,EAAE;YACdyC,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACkD,WAAW,GAAG,SAAS;YAC7CrF,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,SAAS;UAC9C;QACF,CAAE;QACFX,YAAY,EAAGpD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAACkD,WAAW,GAAG,SAAS;UAC7CrF,CAAC,CAAC8E,aAAa,CAAC3C,KAAK,CAAC4B,UAAU,GAAG,aAAa;QAClD,CAAE;QAAA7B,QAAA,gBAEFjF,OAAA,CAACP,CAAC;UAACe,IAAI,EAAE;QAAG;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,UAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGD7G,OAAA;MACEqF,GAAG,EAAE5D,YAAa;MAClBgB,IAAI,EAAC,MAAM;MACX4F,MAAM,EAAC,2CAA2C;MAClDC,QAAQ,EAAE7E,iBAAkB;MAC5ByB,KAAK,EAAE;QAAEE,OAAO,EAAE;MAAO;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,EAKD9F,KAAK,iBACJf,OAAA;MAAKkF,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBH,OAAO,EAAE,MAAM;QACflB,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,MAAM;QACpByB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACf2B,UAAU,EAAE,YAAY;QACxBmB,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAzC,QAAA,gBACAjF,OAAA,CAACL,WAAW;QAACa,IAAI,EAAE,EAAG;QAAC0E,KAAK,EAAE;UAAEiD,SAAS,EAAE,KAAK;UAAEI,UAAU,EAAE;QAAE;MAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrE7G,OAAA;QAAAiF,QAAA,gBACEjF,OAAA;UAAKkF,KAAK,EAAE;YAAEgC,UAAU,EAAE,KAAK;YAAEsB,YAAY,EAAE;UAAU,CAAE;UAAAvD,QAAA,EAAC;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvE7G,OAAA;UAAKkF,KAAK,EAAE;YAAEiC,QAAQ,EAAE,UAAU;YAAEsB,UAAU,EAAE;UAAM,CAAE;UAAAxD,QAAA,EAAElE;QAAK;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5F,OAAO,iBACNjB,OAAA;MAAKkF,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBH,OAAO,EAAE,MAAM;QACflB,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,MAAM;QACpByB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACf2B,UAAU,EAAE,YAAY;QACxBmB,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAzC,QAAA,gBACAjF,OAAA,CAACJ,WAAW;QAACY,IAAI,EAAE,EAAG;QAAC0E,KAAK,EAAE;UAAEiD,SAAS,EAAE,KAAK;UAAEI,UAAU,EAAE;QAAE;MAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrE7G,OAAA;QAAAiF,QAAA,gBACEjF,OAAA;UAAKkF,KAAK,EAAE;YAAEgC,UAAU,EAAE,KAAK;YAAEsB,YAAY,EAAE;UAAU,CAAE;UAAAvD,QAAA,EAAC;QAAO;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzE7G,OAAA;UAAKkF,KAAK,EAAE;YAAEiC,QAAQ,EAAE,UAAU;YAAEsB,UAAU,EAAE;UAAM,CAAE;UAAAxD,QAAA,EAAEhE;QAAO;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7G,OAAA;MAAAiF,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnG,EAAA,CApkBIT,oBAAyD;AAAAyI,EAAA,GAAzDzI,oBAAyD;AAskB/D,eAAeA,oBAAoB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}