{"version": 3, "file": "predicates.js", "sourceRoot": "", "sources": ["../../../src/ast-utils/eslint-utils/predicates.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4EAA8D;AAmB9D,MAAM,YAAY,GAChB,WAAW,CAAC,YAAwD,CAAC;AAuDrE,oCAAY;AAtDd,MAAM,eAAe,GACnB,WAAW,CAAC,eAA8D,CAAC;AA4D3E,0CAAe;AA1DjB,MAAM,mBAAmB,GACvB,WAAW,CAAC,mBAA8D,CAAC;AAmD3E,kDAAmB;AAlDrB,MAAM,sBAAsB,GAC1B,WAAW,CAAC,sBAAoE,CAAC;AAwDjF,wDAAsB;AAtDxB,MAAM,qBAAqB,GACzB,WAAW,CAAC,qBAAgE,CAAC;AA+C7E,sDAAqB;AA9CvB,MAAM,wBAAwB,GAC5B,WAAW,CAAC,wBAAsE,CAAC;AAoDnF,4DAAwB;AAlD1B,MAAM,mBAAmB,GACvB,WAAW,CAAC,mBAA8D,CAAC;AA2C3E,kDAAmB;AA1CrB,MAAM,sBAAsB,GAC1B,WAAW,CAAC,sBAAoE,CAAC;AAgDjF,wDAAsB;AA9CxB,MAAM,YAAY,GAChB,WAAW,CAAC,YAAuD,CAAC;AAuCpE,oCAAY;AAtCd,MAAM,eAAe,GACnB,WAAW,CAAC,eAA6D,CAAC;AA4C1E,0CAAe;AA1CjB,MAAM,YAAY,GAChB,WAAW,CAAC,YAAuD,CAAC;AAmCpE,oCAAY;AAlCd,MAAM,eAAe,GACnB,WAAW,CAAC,eAA6D,CAAC;AAwC1E,0CAAe;AAtCjB,MAAM,cAAc,GAClB,WAAW,CAAC,cAA2D,CAAC;AA+BxE,wCAAc;AA9BhB,MAAM,iBAAiB,GACrB,WAAW,CAAC,iBAAiE,CAAC;AAoC9E,8CAAiB;AAlCnB,MAAM,mBAAmB,GACvB,WAAW,CAAC,mBAA8D,CAAC;AAsC3E,kDAAmB;AArCrB,MAAM,sBAAsB,GAC1B,WAAW,CAAC,sBAAoE,CAAC;AAgCjF,wDAAsB;AA9BxB,MAAM,qBAAqB,GACzB,WAAW,CAAC,qBAAgE,CAAC;AAkC7E,sDAAqB;AAjCvB,MAAM,wBAAwB,GAC5B,WAAW,CAAC,wBAAsE,CAAC;AA4BnF,4DAAwB;AA1B1B,MAAM,mBAAmB,GACvB,WAAW,CAAC,mBAA8D,CAAC;AA8B3E,kDAAmB;AA7BrB,MAAM,sBAAsB,GAC1B,WAAW,CAAC,sBAAoE,CAAC;AAwBjF,wDAAsB;AAtBxB,MAAM,gBAAgB,GACpB,WAAW,CAAC,gBAA2D,CAAC;AA0BxE,4CAAgB;AAzBlB,MAAM,mBAAmB,GACvB,WAAW,CAAC,mBAAiE,CAAC;AAoB9E,kDAAmB"}