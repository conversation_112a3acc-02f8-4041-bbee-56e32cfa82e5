{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 7 10 10\",\n  key: \"1fmybs\"\n}], [\"path\", {\n  d: \"M17 7v10H7\",\n  key: \"6fjiku\"\n}]];\nconst ArrowDownRight = createLucideIcon(\"arrow-down-right\", __iconNode);\nexport { __iconNode, ArrowDownRight as default };\n//# sourceMappingURL=arrow-down-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}