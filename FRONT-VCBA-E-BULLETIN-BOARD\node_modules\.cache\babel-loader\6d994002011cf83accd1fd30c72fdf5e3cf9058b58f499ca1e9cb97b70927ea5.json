{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 10v12\",\n  key: \"6ubwww\"\n}], [\"path\", {\n  d: \"M17.929 7.629A1 1 0 0 1 17 9H7a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 9 2h6a1 1 0 0 1 .928.629z\",\n  key: \"1o95gh\"\n}], [\"path\", {\n  d: \"M9 22h6\",\n  key: \"1rlq3v\"\n}]];\nconst LampFloor = createLucideIcon(\"lamp-floor\", __iconNode);\nexport { __iconNode, LampFloor as default };\n//# sourceMappingURL=lamp-floor.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}