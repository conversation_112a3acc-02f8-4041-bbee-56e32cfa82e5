{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3\",\n  key: \"11za1p\"\n}], [\"path\", {\n  d: \"m16 16 5 5\",\n  key: \"8tpb07\"\n}], [\"path\", {\n  d: \"m16 21 5-5\",\n  key: \"193jll\"\n}]];\nconst Grid2x2X = createLucideIcon(\"grid-2x2-x\", __iconNode);\nexport { __iconNode, Grid2x2X as default };\n//# sourceMappingURL=grid-2x2-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}