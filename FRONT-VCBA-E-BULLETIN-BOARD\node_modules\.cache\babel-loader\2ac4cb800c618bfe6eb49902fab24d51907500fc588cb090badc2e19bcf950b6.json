{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\",\n  key: \"143wyd\"\n}], [\"path\", {\n  d: \"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6\",\n  key: \"1itne7\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"14\",\n  width: \"12\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"1ue0tg\"\n}]];\nconst Printer = createLucideIcon(\"printer\", __iconNode);\nexport { __iconNode, Printer as default };\n//# sourceMappingURL=printer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}