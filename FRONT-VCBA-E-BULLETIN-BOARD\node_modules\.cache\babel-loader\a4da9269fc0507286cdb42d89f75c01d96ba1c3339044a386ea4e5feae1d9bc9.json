{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"jmoj9s\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"aza5on\"\n}], [\"path\", {\n  d: \"M2 14h20\",\n  key: \"myj16y\"\n}], [\"path\", {\n  d: \"M2 4h20\",\n  key: \"mda7wb\"\n}]];\nconst AlignVerticalDistributeStart = createLucideIcon(\"align-vertical-distribute-start\", __iconNode);\nexport { __iconNode, AlignVerticalDistributeStart as default };\n//# sourceMappingURL=align-vertical-distribute-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}