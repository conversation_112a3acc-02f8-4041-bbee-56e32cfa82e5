{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 18-6-6 6-6\",\n  key: \"1wnfg3\"\n}]];\nconst ChevronLeft = createLucideIcon(\"chevron-left\", __iconNode);\nexport { __iconNode, ChevronLeft as default };\n//# sourceMappingURL=chevron-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}