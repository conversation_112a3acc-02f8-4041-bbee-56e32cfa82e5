{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock } from 'lucide-react';\nimport StudentProfileService from '../../services/studentProfileService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentSettings = () => {\n  _s();\n  const {\n    user\n  } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile',\n    icon: User\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }, {\n    key: 'privacy',\n    label: 'Privacy',\n    icon: Lock\n  }];\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n\n  // Handle profile picture selection\n  const handleProfilePictureSelect = file => {\n    setSelectedFile(file);\n    setProfilePictureError(null);\n  };\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async () => {\n    if (!selectedFile) return;\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n    try {\n      const response = await StudentProfileService.uploadProfilePicture(selectedFile);\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        setSelectedFile(null);\n        // You might want to refresh the user context here\n      } else {\n        var _response$error;\n        setProfilePictureError(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Failed to upload profile picture');\n      }\n    } catch (error) {\n      setProfilePictureError(error.message || 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n    try {\n      const response = await StudentProfileService.deleteProfilePicture();\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // You might want to refresh the user context here\n      } else {\n        var _response$error2;\n        setProfilePictureError(((_response$error2 = response.error) === null || _response$error2 === void 0 ? void 0 : _response$error2.message) || 'Failed to remove profile picture');\n      }\n    } catch (error) {\n      setProfilePictureError(error.message || 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          borderRadius: '16px',\n          padding: '2rem',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(10px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#1e40af',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Profile Picture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100px',\n              height: '100px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '700',\n              fontSize: '2rem'\n            },\n            children: [user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0), user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                marginRight: '1rem'\n              },\n              children: \"Upload New Photo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                background: 'none',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                color: '#6b7280'\n              },\n              children: \"Remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          borderRadius: '16px',\n          padding: '2rem',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(10px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#1e40af',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Personal Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.firstName,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.lastName,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridColumn: '1 / -1'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.email,\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                margin: '0.5rem 0 0 0'\n              },\n              children: \"Email address cannot be changed. Contact admin for assistance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Student ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"VCBA-2025-001\",\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"BS Business Administration\",\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '2rem',\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 2rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            },\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: 'none',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              padding: '0.75rem 2rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 5\n    }, this);\n  };\n  const renderNotificationSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Notification Preferences\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Email Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive announcements via email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Push Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Get instant notifications on your device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Alert Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive urgent alerts and important notices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n  const renderPrivacySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Privacy Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Profile Visibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Allow other students to see your profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Activity Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Show when you're online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 419,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 18,\n            color: activeTab === tab.key ? '#22c55e' : '#6b7280'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), renderContent()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentSettings, \"3p2LfGkLozTl42edp2ytAptDCcw=\", false, function () {\n  return [useStudentAuth];\n});\n_c = StudentSettings;\nexport default StudentSettings;\nvar _c;\n$RefreshReg$(_c, \"StudentSettings\");", "map": {"version": 3, "names": ["React", "useState", "useStudentAuth", "User", "Bell", "Lock", "StudentProfileService", "jsxDEV", "_jsxDEV", "StudentSettings", "_s", "user", "activeTab", "setActiveTab", "profilePictureLoading", "setProfilePictureLoading", "profilePictureError", "setProfilePictureError", "successMessage", "setSuccessMessage", "selectedFile", "setSelectedFile", "tabs", "key", "label", "icon", "useEffect", "timer", "setTimeout", "clearTimeout", "handleProfilePictureSelect", "file", "handleProfilePictureUpload", "response", "uploadProfilePicture", "success", "_response$error", "error", "message", "handleProfilePictureRemove", "deleteProfilePicture", "_response$error2", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "background", "borderRadius", "padding", "border", "<PERSON><PERSON>ilter", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alignItems", "width", "height", "justifyContent", "firstName", "char<PERSON>t", "lastName", "cursor", "marginRight", "gridTemplateColumns", "marginBottom", "type", "defaultValue", "outline", "gridColumn", "email", "disabled", "marginTop", "renderNotificationSettings", "boxShadow", "position", "defaultChecked", "opacity", "top", "left", "right", "bottom", "transition", "renderPrivacySettings", "renderContent", "flexWrap", "map", "tab", "onClick", "size", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentSettings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/common/ProfilePictureUpload';\nimport StudentProfileService from '../../services/studentProfileService';\n\nconst StudentSettings: React.FC = () => {\n  const { user } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'privacy'>('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile', icon: User },\n    { key: 'notifications', label: 'Notifications', icon: Bell },\n    { key: 'privacy', label: 'Privacy', icon: Lock }\n  ];\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n\n  // Handle profile picture selection\n  const handleProfilePictureSelect = (file: File | null) => {\n    setSelectedFile(file);\n    setProfilePictureError(null);\n  };\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async () => {\n    if (!selectedFile) return;\n\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n\n    try {\n      const response = await StudentProfileService.uploadProfilePicture(selectedFile);\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        setSelectedFile(null);\n        // You might want to refresh the user context here\n      } else {\n        setProfilePictureError(response.error?.message || 'Failed to upload profile picture');\n      }\n    } catch (error: any) {\n      setProfilePictureError(error.message || 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n\n    try {\n      const response = await StudentProfileService.deleteProfilePicture();\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // You might want to refresh the user context here\n      } else {\n        setProfilePictureError(response.error?.message || 'Failed to remove profile picture');\n      }\n    } catch (error: any) {\n      setProfilePictureError(error.message || 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Picture */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Profile Picture\n        </h3>\n        \n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          <div style={{\n            width: '100px',\n            height: '100px',\n            borderRadius: '50%',\n            background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          }}>\n            {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}\n          </div>\n          \n          <div>\n            <button style={{\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'pointer',\n              marginRight: '1rem'\n            }}>\n              Upload New Photo\n            </button>\n            <button style={{\n              background: 'none',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            }}>\n              Remove\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Personal Information */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Personal Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              First Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.firstName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Last Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.lastName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div style={{ gridColumn: '1 / -1' }}>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              defaultValue={user?.email}\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n            <p style={{\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              margin: '0.5rem 0 0 0'\n            }}>\n              Email address cannot be changed. Contact admin for assistance.\n            </p>\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Student ID\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"VCBA-2025-001\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Course\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"BS Business Administration\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n        </div>\n        \n        <div style={{ marginTop: '2rem', display: 'flex', gap: '1rem' }}>\n          <button style={{\n            background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          }}>\n            Save Changes\n          </button>\n          <button style={{\n            background: 'none',\n            border: '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            cursor: 'pointer',\n            color: '#6b7280'\n          }}>\n            Cancel\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Notification Preferences\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Email Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive announcements via email\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Push Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Get instant notifications on your device\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Alert Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive urgent alerts and important notices\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPrivacySettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Privacy Settings\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Profile Visibility\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Allow other students to see your profile\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Activity Status\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Show when you're online\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div style={{ width: '100%', padding: '2rem' }}>\n      {/* Tabs */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      }}>\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key \n                  ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              <tab.icon size={18} color={activeTab === tab.key ? '#22c55e' : '#6b7280'} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      {renderContent()}\n    </div>\n  );\n};\n\nexport default StudentSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,QAAqB,cAAc;AAE5D,OAAOC,qBAAqB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGT,cAAc,CAAC,CAAC;EACjC,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAA0C,SAAS,CAAC;EAC9F,MAAM,CAACa,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAc,IAAI,CAAC;EAEnE,MAAMqB,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEtB;EAAK,CAAC,EAChD;IAAEoB,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAErB;EAAK,CAAC,EAC5D;IAAEmB,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEpB;EAAK,CAAC,CACjD;;EAED;EACAL,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIR,cAAc,IAAIF,mBAAmB,EAAE;MACzC,MAAMW,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BT,iBAAiB,CAAC,IAAI,CAAC;QACvBF,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMY,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACT,cAAc,EAAEF,mBAAmB,CAAC,CAAC;;EAEzC;EACA,MAAMc,0BAA0B,GAAIC,IAAiB,IAAK;IACxDV,eAAe,CAACU,IAAI,CAAC;IACrBd,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMe,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAACZ,YAAY,EAAE;IAEnBL,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM3B,qBAAqB,CAAC4B,oBAAoB,CAACd,YAAY,CAAC;MAC/E,IAAIa,QAAQ,CAACE,OAAO,EAAE;QACpBhB,iBAAiB,CAAC,wCAAwC,CAAC;QAC3DE,eAAe,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM;QAAA,IAAAe,eAAA;QACLnB,sBAAsB,CAAC,EAAAmB,eAAA,GAAAH,QAAQ,CAACI,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,OAAO,KAAI,kCAAkC,CAAC;MACvF;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBpB,sBAAsB,CAACoB,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7E,CAAC,SAAS;MACRvB,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMwB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7CxB,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM3B,qBAAqB,CAACkC,oBAAoB,CAAC,CAAC;MACnE,IAAIP,QAAQ,CAACE,OAAO,EAAE;QACpBhB,iBAAiB,CAAC,uCAAuC,CAAC;QAC1D;MACF,CAAC,MAAM;QAAA,IAAAsB,gBAAA;QACLxB,sBAAsB,CAAC,EAAAwB,gBAAA,GAAAR,QAAQ,CAACI,KAAK,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBH,OAAO,KAAI,kCAAkC,CAAC;MACvF;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBpB,sBAAsB,CAACoB,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7E,CAAC,SAAS;MACRvB,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5BpC,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpEzC,OAAA;QAAKqC,KAAK,EAAE;UACVK,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE;QAClB,CAAE;QAAAL,QAAA,gBACAzC,OAAA;UAAIqC,KAAK,EAAE;YACTU,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtD,OAAA;UAAKqC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEiB,UAAU,EAAE,QAAQ;YAAEf,GAAG,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjEzC,OAAA;YAAKqC,KAAK,EAAE;cACVmB,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,OAAO;cACfd,YAAY,EAAE,KAAK;cACnBD,UAAU,EAAE,mDAAmD;cAC/DJ,OAAO,EAAE,MAAM;cACfiB,UAAU,EAAE,QAAQ;cACpBG,cAAc,EAAE,QAAQ;cACxBV,KAAK,EAAE,OAAO;cACdE,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,GACCtC,IAAI,aAAJA,IAAI,wBAAAgC,eAAA,GAAJhC,IAAI,CAAEwD,SAAS,cAAAxB,eAAA,uBAAfA,eAAA,CAAiByB,MAAM,CAAC,CAAC,CAAC,EAAEzD,IAAI,aAAJA,IAAI,wBAAAiC,cAAA,GAAJjC,IAAI,CAAE0D,QAAQ,cAAAzB,cAAA,uBAAdA,cAAA,CAAgBwB,MAAM,CAAC,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAQqC,KAAK,EAAE;gBACbK,UAAU,EAAE,mDAAmD;gBAC/DM,KAAK,EAAE,OAAO;gBACdH,MAAM,EAAE,MAAM;gBACdF,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBM,UAAU,EAAE,KAAK;gBACjBY,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE;cACf,CAAE;cAAAtB,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA;cAAQqC,KAAK,EAAE;gBACbK,UAAU,EAAE,MAAM;gBAClBG,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,gBAAgB;gBACzBkB,MAAM,EAAE,SAAS;gBACjBd,KAAK,EAAE;cACT,CAAE;cAAAP,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKqC,KAAK,EAAE;UACVK,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE;QAClB,CAAE;QAAAL,QAAA,gBACAzC,OAAA;UAAIqC,KAAK,EAAE;YACTU,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtD,OAAA;UAAKqC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE0B,mBAAmB,EAAE,SAAS;YAAExB,GAAG,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAC7EzC,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChB2B,YAAY,EAAE,QAAQ;gBACtBjB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXC,YAAY,EAAEhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,SAAU;cAC9BtB,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbZ,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChBmB,OAAO,EAAE;cACX;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChB2B,YAAY,EAAE,QAAQ;gBACtBjB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXC,YAAY,EAAEhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,QAAS;cAC7BxB,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbZ,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChBmB,OAAO,EAAE;cACX;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAKqC,KAAK,EAAE;cAAEgC,UAAU,EAAE;YAAS,CAAE;YAAA5B,QAAA,gBACnCzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChB2B,YAAY,EAAE,QAAQ;gBACtBjB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEkE,IAAI,EAAC,OAAO;cACZC,YAAY,EAAEhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,KAAM;cAC1BC,QAAQ;cACRlC,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbZ,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChBmB,OAAO,EAAE,MAAM;gBACf1B,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFtD,OAAA;cAAGqC,KAAK,EAAE;gBACRY,QAAQ,EAAE,SAAS;gBACnBD,KAAK,EAAE,SAAS;gBAChBD,MAAM,EAAE;cACV,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChB2B,YAAY,EAAE,QAAQ;gBACtBjB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,eAAe;cAC5BI,QAAQ;cACRlC,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbZ,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChBmB,OAAO,EAAE,MAAM;gBACf1B,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChB2B,YAAY,EAAE,QAAQ;gBACtBjB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,4BAA4B;cACzCI,QAAQ;cACRlC,KAAK,EAAE;gBACLmB,KAAK,EAAE,MAAM;gBACbZ,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChBmB,OAAO,EAAE,MAAM;gBACf1B,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAKqC,KAAK,EAAE;YAAEmC,SAAS,EAAE,MAAM;YAAElC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAC,QAAA,gBAC9DzC,OAAA;YAAQqC,KAAK,EAAE;cACbK,UAAU,EAAE,mDAAmD;cAC/DM,KAAK,EAAE,OAAO;cACdH,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,cAAc;cACvBM,UAAU,EAAE,KAAK;cACjBY,MAAM,EAAE;YACV,CAAE;YAAArB,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtD,OAAA;YAAQqC,KAAK,EAAE;cACbK,UAAU,EAAE,MAAM;cAClBG,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,cAAc;cACvBkB,MAAM,EAAE,SAAS;cACjBd,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAMmB,0BAA0B,GAAGA,CAAA,kBACjCzE,OAAA;IAAKqC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACf8B,SAAS,EAAE,gCAAgC;MAC3C7B,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACAzC,OAAA;MAAIqC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELtD,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtEzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEH,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAU,CAAE;YAAAxB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEsC,QAAQ,EAAE,UAAU;YAAErC,OAAO,EAAE,cAAc;YAAEkB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBAC7FzC,OAAA;YAAOkE,IAAI,EAAC,UAAU;YAACU,cAAc;YAACvC,KAAK,EAAE;cAAEwC,OAAO,EAAE,CAAC;cAAErB,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXsC,QAAQ,EAAE,UAAU;cACpBb,MAAM,EAAE,SAAS;cACjBgB,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvC,UAAU,EAAE,SAAS;cACrBwC,UAAU,EAAE,MAAM;cAClBvC,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEH,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAU,CAAE;YAAAxB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEsC,QAAQ,EAAE,UAAU;YAAErC,OAAO,EAAE,cAAc;YAAEkB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBAC7FzC,OAAA;YAAOkE,IAAI,EAAC,UAAU;YAACU,cAAc;YAACvC,KAAK,EAAE;cAAEwC,OAAO,EAAE,CAAC;cAAErB,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXsC,QAAQ,EAAE,UAAU;cACpBb,MAAM,EAAE,SAAS;cACjBgB,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvC,UAAU,EAAE,SAAS;cACrBwC,UAAU,EAAE,MAAM;cAClBvC,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEH,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAU,CAAE;YAAAxB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEsC,QAAQ,EAAE,UAAU;YAAErC,OAAO,EAAE,cAAc;YAAEkB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBAC7FzC,OAAA;YAAOkE,IAAI,EAAC,UAAU;YAACU,cAAc;YAACvC,KAAK,EAAE;cAAEwC,OAAO,EAAE,CAAC;cAAErB,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXsC,QAAQ,EAAE,UAAU;cACpBb,MAAM,EAAE,SAAS;cACjBgB,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvC,UAAU,EAAE,SAAS;cACrBwC,UAAU,EAAE,MAAM;cAClBvC,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM6B,qBAAqB,GAAGA,CAAA,kBAC5BnF,OAAA;IAAKqC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACf8B,SAAS,EAAE,gCAAgC;MAC3C7B,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACAzC,OAAA;MAAIqC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELtD,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtEzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEH,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAU,CAAE;YAAAxB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEsC,QAAQ,EAAE,UAAU;YAAErC,OAAO,EAAE,cAAc;YAAEkB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBAC7FzC,OAAA;YAAOkE,IAAI,EAAC,UAAU;YAACU,cAAc;YAACvC,KAAK,EAAE;cAAEwC,OAAO,EAAE,CAAC;cAAErB,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXsC,QAAQ,EAAE,UAAU;cACpBb,MAAM,EAAE,SAAS;cACjBgB,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvC,UAAU,EAAE,SAAS;cACrBwC,UAAU,EAAE,MAAM;cAClBvC,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEH,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAU,CAAE;YAAAxB,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEsC,QAAQ,EAAE,UAAU;YAAErC,OAAO,EAAE,cAAc;YAAEkB,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBAC7FzC,OAAA;YAAOkE,IAAI,EAAC,UAAU;YAAC7B,KAAK,EAAE;cAAEwC,OAAO,EAAE,CAAC;cAAErB,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEtD,OAAA;YAAMqC,KAAK,EAAE;cACXsC,QAAQ,EAAE,UAAU;cACpBb,MAAM,EAAE,SAAS;cACjBgB,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTvC,UAAU,EAAE,MAAM;cAClBwC,UAAU,EAAE,MAAM;cAClBvC,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM8B,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQhF,SAAS;MACf,KAAK,SAAS;QACZ,OAAO8B,qBAAqB,CAAC,CAAC;MAChC,KAAK,eAAe;QAClB,OAAOuC,0BAA0B,CAAC,CAAC;MACrC,KAAK,SAAS;QACZ,OAAOU,qBAAqB,CAAC,CAAC;MAChC;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEnF,OAAA;IAAKqC,KAAK,EAAE;MAAEmB,KAAK,EAAE,MAAM;MAAEZ,OAAO,EAAE;IAAO,CAAE;IAAAH,QAAA,gBAE7CzC,OAAA;MAAKqC,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBqB,YAAY,EAAE,MAAM;QACpBS,SAAS,EAAE,gCAAgC;QAC3C7B,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,eACAzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAE6C,QAAQ,EAAE;QAAO,CAAE;QAAA5C,QAAA,EAC5D3B,IAAI,CAACwE,GAAG,CAACC,GAAG,iBACXvF,OAAA;UAEEwF,OAAO,EAAEA,CAAA,KAAMnF,YAAY,CAACkF,GAAG,CAACxE,GAAU,CAAE;UAC5CsB,KAAK,EAAE;YACLK,UAAU,EAAEtC,SAAS,KAAKmF,GAAG,CAACxE,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjBiC,KAAK,EAAE5C,SAAS,KAAKmF,GAAG,CAACxE,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD8B,MAAM,EAAEzC,SAAS,KAAKmF,GAAG,CAACxE,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5D4B,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzBkB,MAAM,EAAE,SAAS;YACjBZ,UAAU,EAAE,KAAK;YACjBZ,OAAO,EAAE,MAAM;YACfiB,UAAU,EAAE,QAAQ;YACpBf,GAAG,EAAE,QAAQ;YACb0C,UAAU,EAAE;UACd,CAAE;UAAAzC,QAAA,gBAEFzC,OAAA,CAACuF,GAAG,CAACtE,IAAI;YAACwE,IAAI,EAAE,EAAG;YAACzC,KAAK,EAAE5C,SAAS,KAAKmF,GAAG,CAACxE,GAAG,GAAG,SAAS,GAAG;UAAU;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC3EiC,GAAG,CAACvE,KAAK;QAAA,GAnBLuE,GAAG,CAACxE,GAAG;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL8B,aAAa,CAAC,CAAC;EAAA;IAAAjC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACpD,EAAA,CA1hBID,eAAyB;EAAA,QACZP,cAAc;AAAA;AAAAgG,EAAA,GAD3BzF,eAAyB;AA4hB/B,eAAeA,eAAe;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}