{"version": 3, "file": "no-base-to-string.js", "sourceRoot": "", "sources": ["../../src/rules/no-base-to-string.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,8CAAgC;AAEhC,IAAK,UAIJ;AAJD,WAAK,UAAU;IACb,+CAAM,CAAA;IACN,4BAAc,CAAA;IACd,+BAAiB,CAAA;AACnB,CAAC,EAJI,UAAU,KAAV,UAAU,QAId;AASD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,sGAAsG;YACxG,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EACV,0EAA0E;SAC7E;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,CAAC;SAChE;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAC5D,MAAM,gBAAgB,GAAG,MAAA,MAAM,CAAC,gBAAgB,mCAAI,EAAE,CAAC;QAEvD,SAAS,eAAe,CAAC,IAAyB,EAAE,IAAc;YAChE,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;gBACxC,OAAO;aACR;YAED,MAAM,SAAS,GAAG,wBAAwB,CACxC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GACF,WAAW,CAAC,iBAAiB,CAC3B,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAC/C,CACJ,CAAC;YACF,IAAI,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE;gBACnC,OAAO;aACR;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE;oBACJ,SAAS;oBACT,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;iBAC5C;gBACD,SAAS,EAAE,cAAc;gBACzB,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAa;YAC7C,MAAM,QAAQ,GAAG,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,eAAe,EAAE,CAAC;YACjD,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3D,OAAO,UAAU,CAAC,MAAM,CAAC;aAC1B;YAED,mFAAmF;YACnF,IACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;gBACjC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,EACxC;gBACA,OAAO,UAAU,CAAC,MAAM,CAAC;aAC1B;YAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE;gBAClE,OAAO,UAAU,CAAC,MAAM,CAAC;aAC1B;YAED,IACE,YAAY,CAAC,KAAK,CAChB,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CACb,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CACtE,EACD;gBACA,OAAO,UAAU,CAAC,MAAM,CAAC;aAC1B;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;gBACzB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;oBAChC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAE5D,IAAI,iBAAiB,KAAK,UAAU,CAAC,MAAM,EAAE;wBAC3C,OAAO,UAAU,CAAC,MAAM,CAAC;qBAC1B;iBACF;gBAED,OAAO,UAAU,CAAC,KAAK,CAAC;aACzB;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;gBACnB,OAAO,UAAU,CAAC,KAAK,CAAC;aACzB;YAED,IAAI,iBAAiB,GAAG,IAAI,CAAC;YAC7B,IAAI,iBAAiB,GAAG,KAAK,CAAC;YAE9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;gBAChC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;gBAE5D,IAAI,iBAAiB,KAAK,UAAU,CAAC,MAAM,IAAI,iBAAiB,EAAE;oBAChE,iBAAiB,GAAG,KAAK,CAAC;iBAC3B;gBAED,IAAI,iBAAiB,KAAK,UAAU,CAAC,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAChE,iBAAiB,GAAG,IAAI,CAAC;iBAC1B;aACF;YAED,IAAI,iBAAiB,IAAI,iBAAiB,EAAE;gBAC1C,OAAO,UAAU,CAAC,MAAM,CAAC;aAC1B;YAED,IAAI,iBAAiB,EAAE;gBACrB,OAAO,UAAU,CAAC,SAAS,CAAC;aAC7B;YAED,OAAO,UAAU,CAAC,KAAK,CAAC;QAC1B,CAAC;QAED,OAAO;YACL,yEAAyE,CACvE,IAA+D;gBAE/D,MAAM,QAAQ,GAAG,WAAW,CAAC,iBAAiB,CAC5C,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CACpD,CAAC;gBACF,MAAM,SAAS,GAAG,WAAW,CAAC,iBAAiB,CAC7C,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CACrD,CAAC;gBAEF,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBACxD,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;iBACxC;qBAAM,IACL,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,KAAK,QAAQ;oBACrD,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACnD;oBACA,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACtC;YACH,CAAC;YACD,mFAAmF,CACjF,IAAyB;gBAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAmC,CAAC;gBAC5D,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YACD,eAAe,CAAC,IAA8B;gBAC5C,IACE,IAAI,CAAC,MAAM;oBACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAC5D;oBACA,OAAO;iBACR;gBACD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;oBACzC,eAAe,CAAC,UAAU,CAAC,CAAC;iBAC7B;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}