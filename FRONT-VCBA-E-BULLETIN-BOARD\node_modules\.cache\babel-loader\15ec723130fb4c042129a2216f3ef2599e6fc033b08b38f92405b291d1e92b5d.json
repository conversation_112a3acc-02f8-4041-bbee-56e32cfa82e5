{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\modals\\\\CalendarEventModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useHierarchicalCategories } from '../../../hooks/useAnnouncements';\nimport { useCalendarImageUpload } from '../../../hooks/useCalendarImageUpload';\nimport CalendarImageUpload from '../CalendarImageUpload';\nimport CascadingCategoryDropdown from '../../common/CascadingCategoryDropdown';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CalendarEventModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  event,\n  selectedDate,\n  loading = false\n}) => {\n  _s();\n  const {\n    categories,\n    loading: categoriesLoading,\n    error: categoriesError\n  } = useHierarchicalCategories(); // Use public service (categories should be public)\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    event_date: '',\n    end_date: '',\n    category_id: '',\n    subcategory_id: '',\n    is_recurring: false,\n    recurrence_pattern: '',\n    is_active: true,\n    is_published: false,\n    allow_comments: true,\n    is_alert: false\n  });\n  const [errors, setErrors] = useState({});\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n\n  // Check if the current event is a holiday\n  const isHoliday = ['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes((event === null || event === void 0 ? void 0 : event.category_name) || '') || (event === null || event === void 0 ? void 0 : event.is_holiday);\n\n  // Debug categories loading\n  useEffect(() => {\n    console.log('🔍 CalendarEventModal - Categories state:', {\n      categories: (categories === null || categories === void 0 ? void 0 : categories.length) || 0,\n      categoriesLoading,\n      categoriesError,\n      categoriesData: categories\n    });\n  }, [categories, categoriesLoading, categoriesError]);\n\n  // Image upload hook\n  const {\n    existingImages,\n    loading: imageLoading,\n    error: imageError,\n    uploadImages,\n    setPrimaryImage,\n    refreshImages,\n    clearError: clearImageError,\n    // Pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  } = useCalendarImageUpload({\n    calendarId: event === null || event === void 0 ? void 0 : event.calendar_id,\n    onSuccess: message => setSuccessMessage(message),\n    onError: error => setErrorMessage(error)\n  });\n\n  // Initialize form data when event or selectedDate changes\n  useEffect(() => {\n    if (event) {\n      var _event$category_id, _event$subcategory_id;\n      // Helper function to extract date part without timezone issues\n      const extractDatePart = dateString => {\n        if (!dateString) return '';\n        // If it's already in YYYY-MM-DD format, return as-is\n        if (dateString.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n          return dateString;\n        }\n        // Otherwise, extract the date part from ISO string\n        return dateString.split('T')[0];\n      };\n      setFormData({\n        title: event.title || '',\n        description: event.description || '',\n        event_date: extractDatePart(event.event_date),\n        end_date: event.end_date ? extractDatePart(event.end_date) : '',\n        // Use category_id/subcategory_id if available, otherwise leave empty for manual selection\n        category_id: ((_event$category_id = event.category_id) === null || _event$category_id === void 0 ? void 0 : _event$category_id.toString()) || '',\n        subcategory_id: ((_event$subcategory_id = event.subcategory_id) === null || _event$subcategory_id === void 0 ? void 0 : _event$subcategory_id.toString()) || '',\n        is_recurring: event.is_recurring || false,\n        recurrence_pattern: event.recurrence_pattern || '',\n        is_active: event.is_active !== false,\n        is_published: event.is_published || false,\n        allow_comments: Boolean(event.allow_comments),\n        is_alert: event.is_alert || false\n      });\n    } else {\n      // Format selected date properly to avoid timezone issues\n      const formatLocalDate = date => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n      const dateString = selectedDate ? formatLocalDate(selectedDate) : '';\n      setFormData({\n        title: '',\n        description: '',\n        event_date: dateString,\n        end_date: '',\n        category_id: '',\n        subcategory_id: '',\n        is_recurring: false,\n        recurrence_pattern: '',\n        is_active: true,\n        is_published: false,\n        allow_comments: true,\n        is_alert: false\n      });\n\n      // Clear image state for new events\n      clearAllImageState();\n      setSelectedImages([]);\n    }\n    setErrors({});\n    setSuccessMessage('');\n    setErrorMessage('');\n    clearImageError();\n  }, [event, selectedDate, clearImageError, clearAllImageState]);\n\n  // Separate effect to handle modal open/close state\n  useEffect(() => {\n    if (!isOpen) {\n      // Clear pending deletes when modal closes\n      clearPendingDeletes();\n    }\n  }, [isOpen, clearPendingDeletes]);\n\n  // Enhanced close handler that ensures everything is cleared\n  const handleClose = useCallback(() => {\n    console.log('🚪 Closing calendar modal - clearing all data');\n\n    // Clear pending deletes before closing\n    clearPendingDeletes();\n\n    // Clear other state\n    setErrors({});\n    setSuccessMessage('');\n    setErrorMessage('');\n    clearImageError();\n\n    // Call parent's onClose\n    onClose();\n  }, [clearPendingDeletes, clearImageError, onClose]);\n\n  // Handle Escape key to close modal and clear data\n  useEffect(() => {\n    const handleEscapeKey = event => {\n      if (event.key === 'Escape' && isOpen) {\n        handleClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      return () => document.removeEventListener('keydown', handleEscapeKey);\n    }\n  }, [isOpen, handleClose]);\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage]);\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    } else if (formData.title.length > 255) {\n      newErrors.title = 'Title must be less than 255 characters';\n    }\n    if (!formData.event_date) {\n      newErrors.event_date = 'Event date is required';\n    }\n    if (!formData.category_id) {\n      newErrors.category_id = 'Category is required';\n    }\n    if (formData.end_date && formData.event_date && formData.end_date < formData.event_date) {\n      newErrors.end_date = 'End date cannot be before start date';\n    }\n    if (formData.is_recurring && !formData.recurrence_pattern) {\n      newErrors.recurrence_pattern = 'Recurrence pattern is required for recurring events';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      // Format dates to ensure they're sent as local dates without timezone conversion\n      const formatDateForSubmission = dateString => {\n        if (!dateString) return undefined;\n        // Simply return the date string as-is if it's already in YYYY-MM-DD format\n        // This prevents any timezone conversion issues\n        return dateString;\n      };\n\n      // Build submit data with only the fields that should be updated\n      const submitData = {};\n\n      // Always include basic fields that are being edited\n      if (formData.title !== undefined) submitData.title = formData.title;\n      if (formData.description !== undefined) submitData.description = formData.description;\n      if (formData.event_date !== undefined) submitData.event_date = formatDateForSubmission(formData.event_date);\n      if (formData.category_id !== undefined) submitData.category_id = parseInt(formData.category_id);\n\n      // Only include subcategory_id if it has a valid value\n      if (formData.subcategory_id && formData.subcategory_id !== '' && formData.subcategory_id !== '0') {\n        submitData.subcategory_id = parseInt(formData.subcategory_id);\n      }\n\n      // Only include end_date if it has a value\n      if (formData.end_date && formData.end_date !== '') {\n        submitData.end_date = formatDateForSubmission(formData.end_date);\n      }\n\n      // Include boolean fields\n      if (formData.is_recurring !== undefined) submitData.is_recurring = Boolean(formData.is_recurring);\n      if (formData.is_active !== undefined) submitData.is_active = Boolean(formData.is_active);\n      if (formData.is_published !== undefined) submitData.is_published = Boolean(formData.is_published);\n      if (formData.allow_comments !== undefined) submitData.allow_comments = Boolean(formData.allow_comments);\n      if (formData.is_alert !== undefined) submitData.is_alert = Boolean(formData.is_alert);\n\n      // Only include recurrence_pattern if recurring is enabled and pattern is set\n      if (formData.is_recurring && formData.recurrence_pattern) {\n        submitData.recurrence_pattern = formData.recurrence_pattern;\n      }\n\n      // Create completion callback for additional operations\n      const onComplete = async () => {\n        // If we're editing and have images, upload them separately\n        if (event && selectedImages.length > 0) {\n          try {\n            await uploadImages(selectedImages);\n            setSelectedImages([]); // Clear selected images after upload\n          } catch (uploadError) {\n            console.error('Error uploading additional images:', uploadError);\n            // Don't throw here as the main event was saved successfully\n          }\n        }\n\n        // Refresh images to show updates immediately\n        if (event !== null && event !== void 0 && event.calendar_id) {\n          await refreshImages();\n        }\n\n        // Clear pending deletes after successful update\n        clearPendingDeletes();\n      };\n      console.log('🚀 Submitting calendar event:', submitData); // Debug log\n      console.log('📋 Data types check:', {\n        title: typeof submitData.title,\n        description: typeof submitData.description,\n        event_date: typeof submitData.event_date,\n        category_id: typeof submitData.category_id,\n        subcategory_id: typeof submitData.subcategory_id,\n        is_recurring: typeof submitData.is_recurring,\n        recurrence_pattern: typeof submitData.recurrence_pattern,\n        is_active: typeof submitData.is_active,\n        is_published: typeof submitData.is_published,\n        allow_comments: typeof submitData.allow_comments,\n        is_alert: typeof submitData.is_alert\n      });\n      console.log('📋 Pending deletes before save:', pendingDeletes);\n      await onSave(submitData, pendingDeletes.length > 0 ? applyPendingDeletes : undefined, onComplete);\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error('❌ Error saving event:', error);\n      console.error('❌ Error details:', {\n        message: error.message,\n        response: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data,\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status,\n        statusText: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.statusText\n      });\n    }\n  };\n\n  // Handle category selection\n  const handleCategoryChange = categoryId => {\n    console.log('🧪 CalendarEventModal - Category changed:', categoryId);\n    console.log('🧪 CalendarEventModal - Available categories:', categories === null || categories === void 0 ? void 0 : categories.map(cat => ({\n      id: cat.category_id,\n      name: cat.name\n    })));\n    setFormData(prev => ({\n      ...prev,\n      category_id: (categoryId === null || categoryId === void 0 ? void 0 : categoryId.toString()) || '',\n      subcategory_id: '' // Clear subcategory when category changes\n    }));\n\n    // Clear category error\n    if (errors.category_id) {\n      setErrors(prev => ({\n        ...prev,\n        category_id: ''\n      }));\n    }\n  };\n\n  // Handle subcategory selection\n  const handleSubcategoryChange = subcategoryId => {\n    console.log('🧪 CalendarEventModal - Subcategory changed:', subcategoryId);\n    setFormData(prev => ({\n      ...prev,\n      subcategory_id: (subcategoryId === null || subcategoryId === void 0 ? void 0 : subcategoryId.toString()) || ''\n    }));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    if (type === 'checkbox') {\n      const checked = e.target.checked;\n      setFormData(prev => ({\n        ...prev,\n        [name]: checked\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        width: '100%',\n        maxWidth: '500px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '1.5rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: 0\n          },\n          children: [isHoliday ? 'Holiday Details' : event ? 'Edit Event' : 'Create New Event', isHoliday && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '0.5rem',\n              padding: '0.25rem 0.5rem',\n              backgroundColor: '#dbeafe',\n              color: '#1e40af',\n              fontSize: '0.75rem',\n              fontWeight: '500',\n              borderRadius: '4px',\n              border: '1px solid #bfdbfe'\n            },\n            children: \"Holiday\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          style: {\n            background: 'none',\n            border: 'none',\n            fontSize: '1.5rem',\n            cursor: 'pointer',\n            color: '#6b7280',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), isHoliday ?\n      /*#__PURE__*/\n      // Holiday View - Show title (read-only), editable description, and editable image\n      _jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.25rem',\n              fontWeight: '600',\n              color: '#2d5016',\n              margin: '0 0 0.5rem 0',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: (event === null || event === void 0 ? void 0 : event.category_color) || '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this), event === null || event === void 0 ? void 0 : event.title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: event === null || event === void 0 ? void 0 : event.category_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this), (event === null || event === void 0 ? void 0 : event.event_date) && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: new Date(event.event_date).toLocaleDateString('en-US', {\n                  weekday: 'long',\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              marginTop: '0.5rem',\n              fontStyle: 'italic'\n            },\n            children: \"Holiday name and date cannot be changed. You can edit the description and add images.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [\"Description\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                fontWeight: '400',\n                marginLeft: '0.5rem'\n              },\n              children: \"(You can add local context or additional information)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 4,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease',\n              resize: 'vertical'\n            },\n            placeholder: \"Add description, local context, or additional information about this holiday...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [\"Event Images\", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                fontWeight: '400',\n                marginLeft: '0.5rem'\n              },\n              children: \"(Add images to make this holiday more engaging)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this), pendingDeletes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '0.5rem 0.75rem',\n              backgroundColor: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '6px',\n              marginBottom: '0.75rem',\n              color: '#dc2626',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 19\n            }, this), pendingDeletes.length, \" image\", pendingDeletes.length > 1 ? 's' : '', \" will be deleted\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CalendarImageUpload, {\n            onImagesChange: setSelectedImages,\n            existingImages: existingImages,\n            onSetPrimary: setPrimaryImage,\n            maxImages: 10,\n            onMarkForDeletion: markForDeletion,\n            onUnmarkForDeletion: unmarkForDeletion,\n            pendingDeletes: pendingDeletes,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            paddingTop: '1rem',\n            borderTop: '1px solid #e5e7eb',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleClose,\n            style: {\n              padding: '0.75rem 1.5rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontWeight: '600',\n              fontSize: '0.875rem'\n            },\n            children: loading ? 'Saving...' : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // Regular Event Form\n      _jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Title *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"title\",\n            value: formData.title,\n            onChange: handleInputChange,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: `1px solid ${errors.title ? '#ef4444' : '#d1d5db'}`,\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease'\n            },\n            placeholder: \"Enter event title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ef4444',\n              fontSize: '0.75rem',\n              marginTop: '0.25rem'\n            },\n            children: errors.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 3,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease',\n              resize: 'vertical'\n            },\n            placeholder: \"Enter event description (optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Start Date *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"event_date\",\n              value: formData.event_date,\n              onChange: handleInputChange,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.event_date ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this), errors.event_date && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#ef4444',\n                fontSize: '0.75rem',\n                marginTop: '0.25rem'\n              },\n              children: errors.event_date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"End Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"end_date\",\n              value: formData.end_date,\n              onChange: handleInputChange,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.end_date ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), errors.end_date && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#ef4444',\n                fontSize: '0.75rem',\n                marginTop: '0.25rem'\n              },\n              children: errors.end_date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Category *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this), categoriesError ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.75rem',\n              backgroundColor: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '0.375rem',\n              color: '#dc2626',\n              fontSize: '0.875rem'\n            },\n            children: [\"Error loading categories: \", categoriesError, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => window.location.reload(),\n              style: {\n                marginTop: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                backgroundColor: '#dc2626',\n                color: 'white',\n                border: 'none',\n                borderRadius: '0.25rem',\n                fontSize: '0.75rem',\n                cursor: 'pointer'\n              },\n              children: \"Reload Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this) : categoriesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.75rem',\n              backgroundColor: '#f0f9ff',\n              border: '1px solid #bae6fd',\n              borderRadius: '0.375rem',\n              color: '#0369a1',\n              fontSize: '0.875rem'\n            },\n            children: \"Loading categories...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this) : !categories || categories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.75rem',\n              backgroundColor: '#fffbeb',\n              border: '1px solid #fed7aa',\n              borderRadius: '0.375rem',\n              color: '#ea580c',\n              fontSize: '0.875rem'\n            },\n            children: \"No categories available. Please contact administrator.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(CascadingCategoryDropdown, {\n            categories: categories === null || categories === void 0 ? void 0 : categories.filter(category =>\n            // Hide holiday categories from event creation/editing\n            !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)),\n            selectedCategoryId: formData.category_id ? parseInt(formData.category_id) : undefined,\n            selectedSubcategoryId: formData.subcategory_id ? parseInt(formData.subcategory_id) : undefined,\n            onCategoryChange: handleCategoryChange,\n            onSubcategoryChange: handleSubcategoryChange,\n            placeholder: \"Select Category\",\n            required: true,\n            error: errors.category_id,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              fontSize: '0.875rem',\n              color: '#374151',\n              cursor: 'pointer',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              name: \"is_recurring\",\n              checked: formData.is_recurring,\n              onChange: handleInputChange,\n              style: {\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this), \"Recurring Event\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 13\n          }, this), formData.is_recurring && /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"recurrence_pattern\",\n            value: formData.recurrence_pattern,\n            onChange: handleInputChange,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: `1px solid ${errors.recurrence_pattern ? '#ef4444' : '#d1d5db'}`,\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Recurrence Pattern\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"weekly\",\n              children: \"Weekly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"monthly\",\n              children: \"Monthly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"yearly\",\n              children: \"Yearly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 15\n          }, this), errors.recurrence_pattern && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ef4444',\n              fontSize: '0.75rem',\n              marginTop: '0.25rem'\n            },\n            children: errors.recurrence_pattern\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_active\",\n                checked: formData.is_active,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 17\n              }, this), \"Active Event\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"allow_comments\",\n                checked: formData.allow_comments,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this), \"Allow comments\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_alert\",\n                checked: formData.is_alert,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this), \"Mark as alert\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                margin: 0\n              },\n              children: \"Event Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this), pendingDeletes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem 0.75rem',\n                marginTop: '0.5rem',\n                backgroundColor: '#fef2f2',\n                borderRadius: '6px',\n                border: '1px solid #fecaca',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 19\n              }, this), pendingDeletes.length, \" image\", pendingDeletes.length > 1 ? 's' : '', \" will be deleted\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CalendarImageUpload, {\n            onImagesChange: setSelectedImages,\n            existingImages: existingImages,\n            onSetPrimary: setPrimaryImage,\n            maxImages: 10,\n            disabled: imageLoading,\n            pendingDeletes: pendingDeletes,\n            onMarkForDeletion: markForDeletion,\n            onUnmarkForDeletion: unmarkForDeletion\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc2626',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: imageError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#f0fdf4',\n            border: '1px solid #bbf7d0',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            marginBottom: '1rem',\n            color: '#15803d',\n            fontSize: '0.875rem'\n          },\n          children: successMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 13\n        }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fef2f2',\n            border: '1px solid #fecaca',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            marginBottom: '1rem',\n            color: '#dc2626',\n            fontSize: '0.875rem'\n          },\n          children: errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleClose,\n            style: {\n              padding: '0.75rem 1.5rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontWeight: '600',\n              fontSize: '0.875rem'\n            },\n            children: loading ? 'Saving...' : event ? 'Update' : 'Create'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendarEventModal, \"vDNP6bO1rPWeEaohmhGSo/429jQ=\", false, function () {\n  return [useHierarchicalCategories, useCalendarImageUpload];\n});\n_c = CalendarEventModal;\nexport default CalendarEventModal;\nvar _c;\n$RefreshReg$(_c, \"CalendarEventModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useHierarchicalCategories", "useCalendarImageUpload", "CalendarImageUpload", "CascadingCategoryDropdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarEventModal", "isOpen", "onClose", "onSave", "event", "selectedDate", "loading", "_s", "categories", "categoriesLoading", "error", "categoriesError", "formData", "setFormData", "title", "description", "event_date", "end_date", "category_id", "subcategory_id", "is_recurring", "recurrence_pattern", "is_active", "is_published", "allow_comments", "is_alert", "errors", "setErrors", "selectedImages", "setSelectedImages", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "isHoliday", "includes", "category_name", "is_holiday", "console", "log", "length", "categoriesData", "existingImages", "imageLoading", "imageError", "uploadImages", "setPrimaryImage", "refreshImages", "clearError", "clearImageError", "pendingDeletes", "markForDeletion", "unmarkForDeletion", "applyPendingDeletes", "clearPendingDeletes", "clearAllImageState", "calendarId", "calendar_id", "onSuccess", "message", "onError", "_event$category_id", "_event$subcategory_id", "extractDatePart", "dateString", "match", "split", "toString", "Boolean", "formatLocalDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "handleClose", "handleEscapeKey", "key", "document", "addEventListener", "removeEventListener", "timer", "setTimeout", "clearTimeout", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "e", "preventDefault", "formatDateForSubmission", "undefined", "submitData", "parseInt", "onComplete", "uploadError", "_error$response", "_error$response2", "_error$response3", "response", "data", "status", "statusText", "handleCategoryChange", "categoryId", "map", "cat", "id", "name", "prev", "handleSubcategoryChange", "subcategoryId", "handleInputChange", "value", "type", "target", "checked", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "alignItems", "justifyContent", "zIndex", "padding", "children", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "marginBottom", "fontSize", "fontWeight", "color", "margin", "marginLeft", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "background", "cursor", "onSubmit", "gap", "height", "category_color", "Date", "toLocaleDateString", "weekday", "marginTop", "fontStyle", "onChange", "rows", "outline", "transition", "resize", "placeholder", "onImagesChange", "onSetPrimary", "maxImages", "onMarkForDeletion", "onUnmarkForDeletion", "disabled", "paddingTop", "borderTop", "gridTemplateColumns", "window", "location", "reload", "filter", "category", "selectedCategoryId", "selectedSubcategoryId", "onCategoryChange", "onSubcategoryChange", "required", "marginRight", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/modals/CalendarEventModal.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useHierarchicalCategories } from '../../../hooks/useAnnouncements';\nimport { useCalendarImageUpload } from '../../../hooks/useCalendarImageUpload';\nimport CalendarImageUpload from '../CalendarImageUpload';\nimport CascadingCategoryDropdown from '../../common/CascadingCategoryDropdown';\nimport type { CalendarEvent, CreateEventData, UpdateEventData } from '../../../types/calendar.types';\n\ninterface CalendarEventModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (data: CreateEventData | UpdateEventData, applyPendingDeletes?: () => Promise<void>, onComplete?: () => Promise<void>) => Promise<void>;\n  event?: CalendarEvent | null;\n  selectedDate?: Date | null;\n  loading?: boolean;\n}\n\nconst CalendarEventModal: React.FC<CalendarEventModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  event,\n  selectedDate,\n  loading = false\n}) => {\n  const { categories, loading: categoriesLoading, error: categoriesError } = useHierarchicalCategories(); // Use public service (categories should be public)\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    event_date: '',\n    end_date: '',\n    category_id: '',\n    subcategory_id: '',\n    is_recurring: false,\n    recurrence_pattern: '' as '' | 'yearly' | 'monthly' | 'weekly',\n    is_active: true,\n    is_published: false,\n    allow_comments: true,\n    is_alert: false\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n\n  // Check if the current event is a holiday\n  const isHoliday = ['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(event?.category_name || '') ||\n    event?.is_holiday;\n\n  // Debug categories loading\n  useEffect(() => {\n    console.log('🔍 CalendarEventModal - Categories state:', {\n      categories: categories?.length || 0,\n      categoriesLoading,\n      categoriesError,\n      categoriesData: categories\n    });\n  }, [categories, categoriesLoading, categoriesError]);\n\n  // Image upload hook\n  const {\n    existingImages,\n    loading: imageLoading,\n    error: imageError,\n    uploadImages,\n    setPrimaryImage,\n    refreshImages,\n    clearError: clearImageError,\n    // Pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  } = useCalendarImageUpload({\n    calendarId: event?.calendar_id,\n    onSuccess: (message) => setSuccessMessage(message),\n    onError: (error) => setErrorMessage(error)\n  });\n\n  // Initialize form data when event or selectedDate changes\n  useEffect(() => {\n    if (event) {\n      // Helper function to extract date part without timezone issues\n      const extractDatePart = (dateString: string) => {\n        if (!dateString) return '';\n        // If it's already in YYYY-MM-DD format, return as-is\n        if (dateString.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n          return dateString;\n        }\n        // Otherwise, extract the date part from ISO string\n        return dateString.split('T')[0];\n      };\n\n      setFormData({\n        title: event.title || '',\n        description: event.description || '',\n        event_date: extractDatePart(event.event_date),\n        end_date: event.end_date ? extractDatePart(event.end_date) : '',\n        // Use category_id/subcategory_id if available, otherwise leave empty for manual selection\n        category_id: event.category_id?.toString() || '',\n        subcategory_id: event.subcategory_id?.toString() || '',\n        is_recurring: event.is_recurring || false,\n        recurrence_pattern: event.recurrence_pattern || '',\n        is_active: event.is_active !== false,\n        is_published: (event as any).is_published || false,\n        allow_comments: Boolean((event as any).allow_comments),\n        is_alert: (event as any).is_alert || false\n      });\n    } else {\n      // Format selected date properly to avoid timezone issues\n      const formatLocalDate = (date: Date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const dateString = selectedDate ? formatLocalDate(selectedDate) : '';\n      setFormData({\n        title: '',\n        description: '',\n        event_date: dateString,\n        end_date: '',\n        category_id: '',\n        subcategory_id: '',\n        is_recurring: false,\n        recurrence_pattern: '',\n        is_active: true,\n        is_published: false,\n        allow_comments: true,\n        is_alert: false\n      });\n\n      // Clear image state for new events\n      clearAllImageState();\n      setSelectedImages([]);\n    }\n    setErrors({});\n    setSuccessMessage('');\n    setErrorMessage('');\n    clearImageError();\n  }, [event, selectedDate, clearImageError, clearAllImageState]);\n\n  // Separate effect to handle modal open/close state\n  useEffect(() => {\n    if (!isOpen) {\n      // Clear pending deletes when modal closes\n      clearPendingDeletes();\n    }\n  }, [isOpen, clearPendingDeletes]);\n\n  // Enhanced close handler that ensures everything is cleared\n  const handleClose = useCallback(() => {\n    console.log('🚪 Closing calendar modal - clearing all data');\n\n    // Clear pending deletes before closing\n    clearPendingDeletes();\n\n    // Clear other state\n    setErrors({});\n    setSuccessMessage('');\n    setErrorMessage('');\n    clearImageError();\n\n    // Call parent's onClose\n    onClose();\n  }, [clearPendingDeletes, clearImageError, onClose]);\n\n  // Handle Escape key to close modal and clear data\n  useEffect(() => {\n    const handleEscapeKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        handleClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      return () => document.removeEventListener('keydown', handleEscapeKey);\n    }\n  }, [isOpen, handleClose]);\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage]);\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    } else if (formData.title.length > 255) {\n      newErrors.title = 'Title must be less than 255 characters';\n    }\n\n    if (!formData.event_date) {\n      newErrors.event_date = 'Event date is required';\n    }\n\n    if (!formData.category_id) {\n      newErrors.category_id = 'Category is required';\n    }\n\n    if (formData.end_date && formData.event_date && formData.end_date < formData.event_date) {\n      newErrors.end_date = 'End date cannot be before start date';\n    }\n\n    if (formData.is_recurring && !formData.recurrence_pattern) {\n      newErrors.recurrence_pattern = 'Recurrence pattern is required for recurring events';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      // Format dates to ensure they're sent as local dates without timezone conversion\n      const formatDateForSubmission = (dateString: string) => {\n        if (!dateString) return undefined;\n        // Simply return the date string as-is if it's already in YYYY-MM-DD format\n        // This prevents any timezone conversion issues\n        return dateString;\n      };\n\n      // Build submit data with only the fields that should be updated\n      const submitData: any = {};\n\n      // Always include basic fields that are being edited\n      if (formData.title !== undefined) submitData.title = formData.title;\n      if (formData.description !== undefined) submitData.description = formData.description;\n      if (formData.event_date !== undefined) submitData.event_date = formatDateForSubmission(formData.event_date);\n      if (formData.category_id !== undefined) submitData.category_id = parseInt(formData.category_id);\n\n      // Only include subcategory_id if it has a valid value\n      if (formData.subcategory_id && formData.subcategory_id !== '' && formData.subcategory_id !== '0') {\n        submitData.subcategory_id = parseInt(formData.subcategory_id);\n      }\n\n      // Only include end_date if it has a value\n      if (formData.end_date && formData.end_date !== '') {\n        submitData.end_date = formatDateForSubmission(formData.end_date);\n      }\n\n      // Include boolean fields\n      if (formData.is_recurring !== undefined) submitData.is_recurring = Boolean(formData.is_recurring);\n      if (formData.is_active !== undefined) submitData.is_active = Boolean(formData.is_active);\n      if (formData.is_published !== undefined) submitData.is_published = Boolean(formData.is_published);\n      if (formData.allow_comments !== undefined) submitData.allow_comments = Boolean(formData.allow_comments);\n      if (formData.is_alert !== undefined) submitData.is_alert = Boolean(formData.is_alert);\n\n      // Only include recurrence_pattern if recurring is enabled and pattern is set\n      if (formData.is_recurring && formData.recurrence_pattern) {\n        submitData.recurrence_pattern = formData.recurrence_pattern as 'yearly' | 'monthly' | 'weekly';\n      }\n\n      // Create completion callback for additional operations\n      const onComplete = async () => {\n        // If we're editing and have images, upload them separately\n        if (event && selectedImages.length > 0) {\n          try {\n            await uploadImages(selectedImages);\n            setSelectedImages([]); // Clear selected images after upload\n          } catch (uploadError) {\n            console.error('Error uploading additional images:', uploadError);\n            // Don't throw here as the main event was saved successfully\n          }\n        }\n\n        // Refresh images to show updates immediately\n        if (event?.calendar_id) {\n          await refreshImages();\n        }\n\n        // Clear pending deletes after successful update\n        clearPendingDeletes();\n      };\n\n      console.log('🚀 Submitting calendar event:', submitData); // Debug log\n      console.log('📋 Data types check:', {\n        title: typeof submitData.title,\n        description: typeof submitData.description,\n        event_date: typeof submitData.event_date,\n        category_id: typeof submitData.category_id,\n        subcategory_id: typeof submitData.subcategory_id,\n        is_recurring: typeof submitData.is_recurring,\n        recurrence_pattern: typeof submitData.recurrence_pattern,\n        is_active: typeof submitData.is_active,\n        is_published: typeof submitData.is_published,\n        allow_comments: typeof submitData.allow_comments,\n        is_alert: typeof submitData.is_alert\n      });\n      console.log('📋 Pending deletes before save:', pendingDeletes);\n\n      await onSave(\n        submitData,\n        pendingDeletes.length > 0 ? applyPendingDeletes : undefined,\n        onComplete\n      );\n      handleClose();\n    } catch (error) {\n      console.error('❌ Error saving event:', error);\n      console.error('❌ Error details:', {\n        message: error.message,\n        response: error.response?.data,\n        status: error.response?.status,\n        statusText: error.response?.statusText\n      });\n    }\n  };\n\n  // Handle category selection\n  const handleCategoryChange = (categoryId: number | null) => {\n    console.log('🧪 CalendarEventModal - Category changed:', categoryId);\n    console.log('🧪 CalendarEventModal - Available categories:', categories?.map(cat => ({ id: cat.category_id, name: cat.name })));\n    setFormData(prev => ({\n      ...prev,\n      category_id: categoryId?.toString() || '',\n      subcategory_id: '' // Clear subcategory when category changes\n    }));\n\n    // Clear category error\n    if (errors.category_id) {\n      setErrors(prev => ({ ...prev, category_id: '' }));\n    }\n  };\n\n  // Handle subcategory selection\n  const handleSubcategoryChange = (subcategoryId: number | null) => {\n    console.log('🧪 CalendarEventModal - Subcategory changed:', subcategoryId);\n    setFormData(prev => ({\n      ...prev,\n      subcategory_id: subcategoryId?.toString() || ''\n    }));\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n\n    if (type === 'checkbox') {\n      const checked = (e.target as HTMLInputElement).checked;\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    }}>\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        width: '100%',\n        maxWidth: '500px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      }}>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        }}>\n          <h2 style={{\n            fontSize: '1.5rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: 0\n          }}>\n            {isHoliday ? 'Holiday Details' : (event ? 'Edit Event' : 'Create New Event')}\n            {isHoliday && (\n              <span style={{\n                marginLeft: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                backgroundColor: '#dbeafe',\n                color: '#1e40af',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                borderRadius: '4px',\n                border: '1px solid #bfdbfe'\n              }}>\n                Holiday\n              </span>\n            )}\n          </h2>\n          <button\n            onClick={handleClose}\n            style={{\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280',\n              padding: '0.25rem'\n            }}\n          >\n            ×\n          </button>\n        </div>\n\n        {/* Conditional rendering based on whether it's a holiday */}\n        {isHoliday ? (\n          // Holiday View - Show title (read-only), editable description, and editable image\n          <form onSubmit={handleSubmit}>\n            {/* Holiday Title */}\n            <div style={{ marginBottom: '1.5rem' }}>\n              <h3 style={{\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 0.5rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <span style={{\n                  width: '12px',\n                  height: '12px',\n                  borderRadius: '50%',\n                  backgroundColor: event?.category_color || '#22c55e'\n                }}></span>\n                {event?.title}\n              </h3>\n              <div style={{\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <span>{event?.category_name}</span>\n                {event?.event_date && (\n                  <>\n                    <span>•</span>\n                    <span>{new Date(event.event_date).toLocaleDateString('en-US', {\n                      weekday: 'long',\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric'\n                    })}</span>\n                  </>\n                )}\n              </div>\n              <div style={{\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              }}>\n                Holiday name and date cannot be changed. You can edit the description and add images.\n              </div>\n            </div>\n\n            {/* Holiday Description - Editable */}\n            <div style={{ marginBottom: '1.5rem' }}>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Description\n                <span style={{\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  fontWeight: '400',\n                  marginLeft: '0.5rem'\n                }}>\n                  (You can add local context or additional information)\n                </span>\n              </label>\n              <textarea\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                rows={4}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'border-color 0.2s ease',\n                  resize: 'vertical'\n                }}\n                placeholder=\"Add description, local context, or additional information about this holiday...\"\n              />\n            </div>\n\n            {/* Holiday Image - Editable */}\n            <div style={{ marginBottom: '1.5rem' }}>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Event Images\n                <span style={{\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  fontWeight: '400',\n                  marginLeft: '0.5rem'\n                }}>\n                  (Add images to make this holiday more engaging)\n                </span>\n              </label>\n              {pendingDeletes.length > 0 && (\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 0.75rem',\n                  backgroundColor: '#fef2f2',\n                  border: '1px solid #fecaca',\n                  borderRadius: '6px',\n                  marginBottom: '0.75rem',\n                  color: '#dc2626',\n                  fontSize: '0.875rem',\n                  fontWeight: '500'\n                }}>\n                  <span>⚠️</span>\n                  {pendingDeletes.length} image{pendingDeletes.length > 1 ? 's' : ''} will be deleted\n                </div>\n              )}\n              <CalendarImageUpload\n                onImagesChange={setSelectedImages}\n                existingImages={existingImages}\n                onSetPrimary={setPrimaryImage}\n                maxImages={10}\n                onMarkForDeletion={markForDeletion}\n                onUnmarkForDeletion={unmarkForDeletion}\n                pendingDeletes={pendingDeletes}\n                disabled={loading}\n              />\n            </div>\n\n            {/* Action Buttons for Holiday View */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb',\n              gap: '1rem'\n            }}>\n              <button\n                type=\"button\"\n                onClick={handleClose}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  fontWeight: '500',\n                  fontSize: '0.875rem'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: loading ? 'not-allowed' : 'pointer',\n                  fontWeight: '600',\n                  fontSize: '0.875rem'\n                }}\n              >\n                {loading ? 'Saving...' : 'Save Changes'}\n              </button>\n            </div>\n          </form>\n        ) : (\n          // Regular Event Form\n          <form onSubmit={handleSubmit}>\n          {/* Title */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.title ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease'\n              }}\n              placeholder=\"Enter event title\"\n            />\n            {errors.title && (\n              <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                {errors.title}\n              </p>\n            )}\n          </div>\n\n          {/* Description */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Description\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={3}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease',\n                resize: 'vertical'\n              }}\n              placeholder=\"Enter event description (optional)\"\n            />\n          </div>\n\n          {/* Date Range */}\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1rem'\n          }}>\n            {/* Start Date */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Start Date *\n              </label>\n              <input\n                type=\"date\"\n                name=\"event_date\"\n                value={formData.event_date}\n                onChange={handleInputChange}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.event_date ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none'\n                }}\n              />\n              {errors.event_date && (\n                <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                  {errors.event_date}\n                </p>\n              )}\n            </div>\n\n            {/* End Date */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                End Date\n              </label>\n              <input\n                type=\"date\"\n                name=\"end_date\"\n                value={formData.end_date}\n                onChange={handleInputChange}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.end_date ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none'\n                }}\n              />\n              {errors.end_date && (\n                <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                  {errors.end_date}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Category */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Category *\n            </label>\n            {categoriesError ? (\n              <div style={{\n                padding: '0.75rem',\n                backgroundColor: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '0.375rem',\n                color: '#dc2626',\n                fontSize: '0.875rem'\n              }}>\n                Error loading categories: {categoriesError}\n                <br />\n                <button\n                  type=\"button\"\n                  onClick={() => window.location.reload()}\n                  style={{\n                    marginTop: '0.5rem',\n                    padding: '0.25rem 0.5rem',\n                    backgroundColor: '#dc2626',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '0.25rem',\n                    fontSize: '0.75rem',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Reload Page\n                </button>\n              </div>\n            ) : categoriesLoading ? (\n              <div style={{\n                padding: '0.75rem',\n                backgroundColor: '#f0f9ff',\n                border: '1px solid #bae6fd',\n                borderRadius: '0.375rem',\n                color: '#0369a1',\n                fontSize: '0.875rem'\n              }}>\n                Loading categories...\n              </div>\n            ) : !categories || categories.length === 0 ? (\n              <div style={{\n                padding: '0.75rem',\n                backgroundColor: '#fffbeb',\n                border: '1px solid #fed7aa',\n                borderRadius: '0.375rem',\n                color: '#ea580c',\n                fontSize: '0.875rem'\n              }}>\n                No categories available. Please contact administrator.\n              </div>\n            ) : (\n              <CascadingCategoryDropdown\n                categories={categories?.filter(category =>\n                  // Hide holiday categories from event creation/editing\n                  !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                )}\n                selectedCategoryId={formData.category_id ? parseInt(formData.category_id) : undefined}\n                selectedSubcategoryId={formData.subcategory_id ? parseInt(formData.subcategory_id) : undefined}\n                onCategoryChange={handleCategoryChange}\n                onSubcategoryChange={handleSubcategoryChange}\n                placeholder=\"Select Category\"\n                required={true}\n                error={errors.category_id}\n                disabled={loading}\n              />\n            )}\n          </div>\n\n          {/* Recurring Options */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'flex',\n              alignItems: 'center',\n              fontSize: '0.875rem',\n              color: '#374151',\n              cursor: 'pointer',\n              marginBottom: '0.5rem'\n            }}>\n              <input\n                type=\"checkbox\"\n                name=\"is_recurring\"\n                checked={formData.is_recurring}\n                onChange={handleInputChange}\n                style={{ marginRight: '0.5rem' }}\n              />\n              Recurring Event\n            </label>\n\n            {formData.is_recurring && (\n              <select\n                name=\"recurrence_pattern\"\n                value={formData.recurrence_pattern}\n                onChange={handleInputChange}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.recurrence_pattern ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  backgroundColor: 'white'\n                }}\n              >\n                <option value=\"\">Select Recurrence Pattern</option>\n                <option value=\"weekly\">Weekly</option>\n                <option value=\"monthly\">Monthly</option>\n                <option value=\"yearly\">Yearly</option>\n              </select>\n            )}\n            {errors.recurrence_pattern && (\n              <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                {errors.recurrence_pattern}\n              </p>\n            )}\n          </div>\n\n          {/* Event Options */}\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1.5rem'\n          }}>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"is_active\"\n                  checked={formData.is_active}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Active Event\n              </label>\n            </div>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"allow_comments\"\n                  checked={formData.allow_comments}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Allow comments\n              </label>\n            </div>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"is_alert\"\n                  checked={formData.is_alert}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Mark as alert\n              </label>\n            </div>\n          </div>\n\n          {/* Image Upload Section */}\n          <div style={{ marginBottom: '1.5rem' }}>\n            <div style={{ marginBottom: '0.5rem' }}>\n              <h4 style={{\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                margin: 0\n              }}>\n                Event Images\n              </h4>\n              {pendingDeletes.length > 0 && (\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 0.75rem',\n                  marginTop: '0.5rem',\n                  backgroundColor: '#fef2f2',\n                  borderRadius: '6px',\n                  border: '1px solid #fecaca',\n                  color: '#dc2626',\n                  fontSize: '0.875rem',\n                  fontWeight: '500'\n                }}>\n                  <span>⚠️</span>\n                  {pendingDeletes.length} image{pendingDeletes.length > 1 ? 's' : ''} will be deleted\n                </div>\n              )}\n            </div>\n            <CalendarImageUpload\n              onImagesChange={setSelectedImages}\n              existingImages={existingImages}\n              onSetPrimary={setPrimaryImage}\n              maxImages={10}\n              disabled={imageLoading}\n              pendingDeletes={pendingDeletes}\n              onMarkForDeletion={markForDeletion}\n              onUnmarkForDeletion={unmarkForDeletion}\n            />\n            {imageError && (\n              <div style={{\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                marginTop: '0.5rem'\n              }}>\n                {imageError}\n              </div>\n            )}\n          </div>\n\n          {/* Success/Error Messages */}\n          {successMessage && (\n            <div style={{\n              backgroundColor: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '6px',\n              padding: '0.75rem',\n              marginBottom: '1rem',\n              color: '#15803d',\n              fontSize: '0.875rem'\n            }}>\n              {successMessage}\n            </div>\n          )}\n\n          {errorMessage && (\n            <div style={{\n              backgroundColor: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '6px',\n              padding: '0.75rem',\n              marginBottom: '1rem',\n              color: '#dc2626',\n              fontSize: '0.875rem'\n            }}>\n              {errorMessage}\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div style={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem'\n          }}>\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              style={{\n                padding: '0.75rem 1.5rem',\n                backgroundColor: '#f3f4f6',\n                color: '#374151',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontWeight: '500',\n                fontSize: '0.875rem'\n              }}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontWeight: '600',\n                fontSize: '0.875rem'\n              }}\n            >\n              {loading ? 'Saving...' : (event ? 'Update' : 'Create')}\n            </button>\n          </div>\n        </form>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CalendarEventModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,yBAAyB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/E,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,MAAM;EACNC,OAAO;EACPC,MAAM;EACNC,KAAK;EACLC,YAAY;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,UAAU;IAAEF,OAAO,EAAEG,iBAAiB;IAAEC,KAAK,EAAEC;EAAgB,CAAC,GAAGnB,yBAAyB,CAAC,CAAC,CAAC,CAAC;EACxG,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,KAAK;IACnBC,kBAAkB,EAAE,EAA0C;IAC9DC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM6C,SAAS,GAAG,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAACC,QAAQ,CAAC,CAAA/B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC,aAAa,KAAI,EAAE,CAAC,KAC5HhC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiC,UAAU;;EAEnB;EACA/C,SAAS,CAAC,MAAM;IACdgD,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvD/B,UAAU,EAAE,CAAAA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgC,MAAM,KAAI,CAAC;MACnC/B,iBAAiB;MACjBE,eAAe;MACf8B,cAAc,EAAEjC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,UAAU,EAAEC,iBAAiB,EAAEE,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM;IACJ+B,cAAc;IACdpC,OAAO,EAAEqC,YAAY;IACrBjC,KAAK,EAAEkC,UAAU;IACjBC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,UAAU,EAAEC,eAAe;IAC3B;IACAC,cAAc;IACdC,eAAe;IACfC,iBAAiB;IACjBC,mBAAmB;IACnBC,mBAAmB;IACnB;IACAC;EACF,CAAC,GAAG9D,sBAAsB,CAAC;IACzB+D,UAAU,EAAEpD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqD,WAAW;IAC9BC,SAAS,EAAGC,OAAO,IAAK5B,iBAAiB,CAAC4B,OAAO,CAAC;IAClDC,OAAO,EAAGlD,KAAK,IAAKuB,eAAe,CAACvB,KAAK;EAC3C,CAAC,CAAC;;EAEF;EACApB,SAAS,CAAC,MAAM;IACd,IAAIc,KAAK,EAAE;MAAA,IAAAyD,kBAAA,EAAAC,qBAAA;MACT;MACA,MAAMC,eAAe,GAAIC,UAAkB,IAAK;QAC9C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;QAC1B;QACA,IAAIA,UAAU,CAACC,KAAK,CAAC,qBAAqB,CAAC,EAAE;UAC3C,OAAOD,UAAU;QACnB;QACA;QACA,OAAOA,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;MAEDrD,WAAW,CAAC;QACVC,KAAK,EAAEV,KAAK,CAACU,KAAK,IAAI,EAAE;QACxBC,WAAW,EAAEX,KAAK,CAACW,WAAW,IAAI,EAAE;QACpCC,UAAU,EAAE+C,eAAe,CAAC3D,KAAK,CAACY,UAAU,CAAC;QAC7CC,QAAQ,EAAEb,KAAK,CAACa,QAAQ,GAAG8C,eAAe,CAAC3D,KAAK,CAACa,QAAQ,CAAC,GAAG,EAAE;QAC/D;QACAC,WAAW,EAAE,EAAA2C,kBAAA,GAAAzD,KAAK,CAACc,WAAW,cAAA2C,kBAAA,uBAAjBA,kBAAA,CAAmBM,QAAQ,CAAC,CAAC,KAAI,EAAE;QAChDhD,cAAc,EAAE,EAAA2C,qBAAA,GAAA1D,KAAK,CAACe,cAAc,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBK,QAAQ,CAAC,CAAC,KAAI,EAAE;QACtD/C,YAAY,EAAEhB,KAAK,CAACgB,YAAY,IAAI,KAAK;QACzCC,kBAAkB,EAAEjB,KAAK,CAACiB,kBAAkB,IAAI,EAAE;QAClDC,SAAS,EAAElB,KAAK,CAACkB,SAAS,KAAK,KAAK;QACpCC,YAAY,EAAGnB,KAAK,CAASmB,YAAY,IAAI,KAAK;QAClDC,cAAc,EAAE4C,OAAO,CAAEhE,KAAK,CAASoB,cAAc,CAAC;QACtDC,QAAQ,EAAGrB,KAAK,CAASqB,QAAQ,IAAI;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAM4C,eAAe,GAAIC,IAAU,IAAK;QACtC,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;QAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;MAClC,CAAC;MAED,MAAMb,UAAU,GAAG3D,YAAY,GAAGgE,eAAe,CAAChE,YAAY,CAAC,GAAG,EAAE;MACpEQ,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAEgD,UAAU;QACtB/C,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,KAAK;QACnBC,kBAAkB,EAAE,EAAE;QACtBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,KAAK;QACnBC,cAAc,EAAE,IAAI;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA8B,kBAAkB,CAAC,CAAC;MACpB1B,iBAAiB,CAAC,EAAE,CAAC;IACvB;IACAF,SAAS,CAAC,CAAC,CAAC,CAAC;IACbI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;IACnBgB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC7C,KAAK,EAAEC,YAAY,EAAE4C,eAAe,EAAEM,kBAAkB,CAAC,CAAC;;EAE9D;EACAjE,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,MAAM,EAAE;MACX;MACAqD,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACrD,MAAM,EAAEqD,mBAAmB,CAAC,CAAC;;EAEjC;EACA,MAAMyB,WAAW,GAAGxF,WAAW,CAAC,MAAM;IACpC+C,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;IAE5D;IACAe,mBAAmB,CAAC,CAAC;;IAErB;IACA3B,SAAS,CAAC,CAAC,CAAC,CAAC;IACbI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,EAAE,CAAC;IACnBgB,eAAe,CAAC,CAAC;;IAEjB;IACA/C,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACoD,mBAAmB,EAAEL,eAAe,EAAE/C,OAAO,CAAC,CAAC;;EAEnD;EACAZ,SAAS,CAAC,MAAM;IACd,MAAM0F,eAAe,GAAI5E,KAAoB,IAAK;MAChD,IAAIA,KAAK,CAAC6E,GAAG,KAAK,QAAQ,IAAIhF,MAAM,EAAE;QACpC8E,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAED,IAAI9E,MAAM,EAAE;MACViF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEH,eAAe,CAAC;MACrD,OAAO,MAAME,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEJ,eAAe,CAAC;IACvE;EACF,CAAC,EAAE,CAAC/E,MAAM,EAAE8E,WAAW,CAAC,CAAC;;EAEzB;EACAzF,SAAS,CAAC,MAAM;IACd,IAAIwC,cAAc,IAAIE,YAAY,EAAE;MAClC,MAAMqD,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BvD,iBAAiB,CAAC,EAAE,CAAC;QACrBE,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMsD,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACvD,cAAc,EAAEE,YAAY,CAAC,CAAC;EAElC,MAAMwD,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC7E,QAAQ,CAACE,KAAK,CAAC4E,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC3E,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAIF,QAAQ,CAACE,KAAK,CAAC0B,MAAM,GAAG,GAAG,EAAE;MACtCiD,SAAS,CAAC3E,KAAK,GAAG,wCAAwC;IAC5D;IAEA,IAAI,CAACF,QAAQ,CAACI,UAAU,EAAE;MACxByE,SAAS,CAACzE,UAAU,GAAG,wBAAwB;IACjD;IAEA,IAAI,CAACJ,QAAQ,CAACM,WAAW,EAAE;MACzBuE,SAAS,CAACvE,WAAW,GAAG,sBAAsB;IAChD;IAEA,IAAIN,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACI,UAAU,IAAIJ,QAAQ,CAACK,QAAQ,GAAGL,QAAQ,CAACI,UAAU,EAAE;MACvFyE,SAAS,CAACxE,QAAQ,GAAG,sCAAsC;IAC7D;IAEA,IAAIL,QAAQ,CAACQ,YAAY,IAAI,CAACR,QAAQ,CAACS,kBAAkB,EAAE;MACzDoE,SAAS,CAACpE,kBAAkB,GAAG,qDAAqD;IACtF;IAEAM,SAAS,CAAC8D,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACjD,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMqD,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF;MACA,MAAMQ,uBAAuB,GAAIhC,UAAkB,IAAK;QACtD,IAAI,CAACA,UAAU,EAAE,OAAOiC,SAAS;QACjC;QACA;QACA,OAAOjC,UAAU;MACnB,CAAC;;MAED;MACA,MAAMkC,UAAe,GAAG,CAAC,CAAC;;MAE1B;MACA,IAAItF,QAAQ,CAACE,KAAK,KAAKmF,SAAS,EAAEC,UAAU,CAACpF,KAAK,GAAGF,QAAQ,CAACE,KAAK;MACnE,IAAIF,QAAQ,CAACG,WAAW,KAAKkF,SAAS,EAAEC,UAAU,CAACnF,WAAW,GAAGH,QAAQ,CAACG,WAAW;MACrF,IAAIH,QAAQ,CAACI,UAAU,KAAKiF,SAAS,EAAEC,UAAU,CAAClF,UAAU,GAAGgF,uBAAuB,CAACpF,QAAQ,CAACI,UAAU,CAAC;MAC3G,IAAIJ,QAAQ,CAACM,WAAW,KAAK+E,SAAS,EAAEC,UAAU,CAAChF,WAAW,GAAGiF,QAAQ,CAACvF,QAAQ,CAACM,WAAW,CAAC;;MAE/F;MACA,IAAIN,QAAQ,CAACO,cAAc,IAAIP,QAAQ,CAACO,cAAc,KAAK,EAAE,IAAIP,QAAQ,CAACO,cAAc,KAAK,GAAG,EAAE;QAChG+E,UAAU,CAAC/E,cAAc,GAAGgF,QAAQ,CAACvF,QAAQ,CAACO,cAAc,CAAC;MAC/D;;MAEA;MACA,IAAIP,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACK,QAAQ,KAAK,EAAE,EAAE;QACjDiF,UAAU,CAACjF,QAAQ,GAAG+E,uBAAuB,CAACpF,QAAQ,CAACK,QAAQ,CAAC;MAClE;;MAEA;MACA,IAAIL,QAAQ,CAACQ,YAAY,KAAK6E,SAAS,EAAEC,UAAU,CAAC9E,YAAY,GAAGgD,OAAO,CAACxD,QAAQ,CAACQ,YAAY,CAAC;MACjG,IAAIR,QAAQ,CAACU,SAAS,KAAK2E,SAAS,EAAEC,UAAU,CAAC5E,SAAS,GAAG8C,OAAO,CAACxD,QAAQ,CAACU,SAAS,CAAC;MACxF,IAAIV,QAAQ,CAACW,YAAY,KAAK0E,SAAS,EAAEC,UAAU,CAAC3E,YAAY,GAAG6C,OAAO,CAACxD,QAAQ,CAACW,YAAY,CAAC;MACjG,IAAIX,QAAQ,CAACY,cAAc,KAAKyE,SAAS,EAAEC,UAAU,CAAC1E,cAAc,GAAG4C,OAAO,CAACxD,QAAQ,CAACY,cAAc,CAAC;MACvG,IAAIZ,QAAQ,CAACa,QAAQ,KAAKwE,SAAS,EAAEC,UAAU,CAACzE,QAAQ,GAAG2C,OAAO,CAACxD,QAAQ,CAACa,QAAQ,CAAC;;MAErF;MACA,IAAIb,QAAQ,CAACQ,YAAY,IAAIR,QAAQ,CAACS,kBAAkB,EAAE;QACxD6E,UAAU,CAAC7E,kBAAkB,GAAGT,QAAQ,CAACS,kBAAqD;MAChG;;MAEA;MACA,MAAM+E,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B;QACA,IAAIhG,KAAK,IAAIwB,cAAc,CAACY,MAAM,GAAG,CAAC,EAAE;UACtC,IAAI;YACF,MAAMK,YAAY,CAACjB,cAAc,CAAC;YAClCC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,OAAOwE,WAAW,EAAE;YACpB/D,OAAO,CAAC5B,KAAK,CAAC,oCAAoC,EAAE2F,WAAW,CAAC;YAChE;UACF;QACF;;QAEA;QACA,IAAIjG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEqD,WAAW,EAAE;UACtB,MAAMV,aAAa,CAAC,CAAC;QACvB;;QAEA;QACAO,mBAAmB,CAAC,CAAC;MACvB,CAAC;MAEDhB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2D,UAAU,CAAC,CAAC,CAAC;MAC1D5D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClCzB,KAAK,EAAE,OAAOoF,UAAU,CAACpF,KAAK;QAC9BC,WAAW,EAAE,OAAOmF,UAAU,CAACnF,WAAW;QAC1CC,UAAU,EAAE,OAAOkF,UAAU,CAAClF,UAAU;QACxCE,WAAW,EAAE,OAAOgF,UAAU,CAAChF,WAAW;QAC1CC,cAAc,EAAE,OAAO+E,UAAU,CAAC/E,cAAc;QAChDC,YAAY,EAAE,OAAO8E,UAAU,CAAC9E,YAAY;QAC5CC,kBAAkB,EAAE,OAAO6E,UAAU,CAAC7E,kBAAkB;QACxDC,SAAS,EAAE,OAAO4E,UAAU,CAAC5E,SAAS;QACtCC,YAAY,EAAE,OAAO2E,UAAU,CAAC3E,YAAY;QAC5CC,cAAc,EAAE,OAAO0E,UAAU,CAAC1E,cAAc;QAChDC,QAAQ,EAAE,OAAOyE,UAAU,CAACzE;MAC9B,CAAC,CAAC;MACFa,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,cAAc,CAAC;MAE9D,MAAM/C,MAAM,CACV+F,UAAU,EACVhD,cAAc,CAACV,MAAM,GAAG,CAAC,GAAGa,mBAAmB,GAAG4C,SAAS,EAC3DG,UACF,CAAC;MACDrB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOrE,KAAK,EAAE;MAAA,IAAA4F,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdlE,OAAO,CAAC5B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C4B,OAAO,CAAC5B,KAAK,CAAC,kBAAkB,EAAE;QAChCiD,OAAO,EAAEjD,KAAK,CAACiD,OAAO;QACtB8C,QAAQ,GAAAH,eAAA,GAAE5F,KAAK,CAAC+F,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,IAAI;QAC9BC,MAAM,GAAAJ,gBAAA,GAAE7F,KAAK,CAAC+F,QAAQ,cAAAF,gBAAA,uBAAdA,gBAAA,CAAgBI,MAAM;QAC9BC,UAAU,GAAAJ,gBAAA,GAAE9F,KAAK,CAAC+F,QAAQ,cAAAD,gBAAA,uBAAdA,gBAAA,CAAgBI;MAC9B,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,UAAyB,IAAK;IAC1DxE,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEuE,UAAU,CAAC;IACpExE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE/B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuG,GAAG,CAACC,GAAG,KAAK;MAAEC,EAAE,EAAED,GAAG,CAAC9F,WAAW;MAAEgG,IAAI,EAAEF,GAAG,CAACE;IAAK,CAAC,CAAC,CAAC,CAAC;IAC/HrG,WAAW,CAACsG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjG,WAAW,EAAE,CAAA4F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE3C,QAAQ,CAAC,CAAC,KAAI,EAAE;MACzChD,cAAc,EAAE,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIO,MAAM,CAACR,WAAW,EAAE;MACtBS,SAAS,CAACwF,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjG,WAAW,EAAE;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMkG,uBAAuB,GAAIC,aAA4B,IAAK;IAChE/E,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE8E,aAAa,CAAC;IAC1ExG,WAAW,CAACsG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhG,cAAc,EAAE,CAAAkG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAElD,QAAQ,CAAC,CAAC,KAAI;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMmD,iBAAiB,GAAIxB,CAAgF,IAAK;IAC9G,MAAM;MAAEoB,IAAI;MAAEK,KAAK;MAAEC;IAAK,CAAC,GAAG1B,CAAC,CAAC2B,MAAM;IAEtC,IAAID,IAAI,KAAK,UAAU,EAAE;MACvB,MAAME,OAAO,GAAI5B,CAAC,CAAC2B,MAAM,CAAsBC,OAAO;MACtD7G,WAAW,CAACsG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAGQ;MAAQ,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM;MACL7G,WAAW,CAACsG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAGK;MAAM,CAAC,CAAC,CAAC;IACnD;;IAEA;IACA,IAAI7F,MAAM,CAACwF,IAAI,CAAC,EAAE;MAChBvF,SAAS,CAACwF,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,IAAI,CAACjH,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK8H,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,oBAAoB;MACrCC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACA1I,OAAA;MAAK8H,KAAK,EAAE;QACVM,eAAe,EAAE,OAAO;QACxBO,YAAY,EAAE,MAAM;QACpBF,OAAO,EAAE,MAAM;QACfG,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBACA1I,OAAA;QAAK8H,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBW,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACA1I,OAAA;UAAI8H,KAAK,EAAE;YACToB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE;UACV,CAAE;UAAAX,QAAA,GACCrG,SAAS,GAAG,iBAAiB,GAAI9B,KAAK,GAAG,YAAY,GAAG,kBAAmB,EAC3E8B,SAAS,iBACRrC,OAAA;YAAM8H,KAAK,EAAE;cACXwB,UAAU,EAAE,QAAQ;cACpBb,OAAO,EAAE,gBAAgB;cACzBL,eAAe,EAAE,SAAS;cAC1BgB,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE,KAAK;cACjBR,YAAY,EAAE,KAAK;cACnBY,MAAM,EAAE;YACV,CAAE;YAAAb,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACL3J,OAAA;UACE4J,OAAO,EAAE1E,WAAY;UACrB4C,KAAK,EAAE;YACL+B,UAAU,EAAE,MAAM;YAClBN,MAAM,EAAE,MAAM;YACdL,QAAQ,EAAE,QAAQ;YAClBY,MAAM,EAAE,SAAS;YACjBV,KAAK,EAAE,SAAS;YAChBX,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLtH,SAAS;MAAA;MACR;MACArC,OAAA;QAAM+J,QAAQ,EAAE/D,YAAa;QAAA0C,QAAA,gBAE3B1I,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrC1I,OAAA;YAAI8H,KAAK,EAAE;cACToB,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBC,MAAM,EAAE,cAAc;cACtBhB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0B,GAAG,EAAE;YACP,CAAE;YAAAtB,QAAA,gBACA1I,OAAA;cAAM8H,KAAK,EAAE;gBACXc,KAAK,EAAE,MAAM;gBACbqB,MAAM,EAAE,MAAM;gBACdtB,YAAY,EAAE,KAAK;gBACnBP,eAAe,EAAE,CAAA7H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2J,cAAc,KAAI;cAC5C;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACTpJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,KAAK;UAAA;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACL3J,OAAA;YAAK8H,KAAK,EAAE;cACVoB,QAAQ,EAAE,UAAU;cACpBE,KAAK,EAAE,SAAS;cAChBf,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0B,GAAG,EAAE;YACP,CAAE;YAAAtB,QAAA,gBACA1I,OAAA;cAAA0I,QAAA,EAAOnI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC;YAAa;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAClC,CAAApJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,UAAU,kBAChBnB,OAAA,CAAAE,SAAA;cAAAwI,QAAA,gBACE1I,OAAA;gBAAA0I,QAAA,EAAM;cAAC;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACd3J,OAAA;gBAAA0I,QAAA,EAAO,IAAIyB,IAAI,CAAC5J,KAAK,CAACY,UAAU,CAAC,CAACiJ,kBAAkB,CAAC,OAAO,EAAE;kBAC5DC,OAAO,EAAE,MAAM;kBACf3F,IAAI,EAAE,SAAS;kBACfE,KAAK,EAAE,MAAM;kBACbI,GAAG,EAAE;gBACP,CAAC;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,eACV,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN3J,OAAA;YAAK8H,KAAK,EAAE;cACVoB,QAAQ,EAAE,SAAS;cACnBE,KAAK,EAAE,SAAS;cAChBkB,SAAS,EAAE,QAAQ;cACnBC,SAAS,EAAE;YACb,CAAE;YAAA7B,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,GAAC,aAED,eAAA1I,OAAA;cAAM8H,KAAK,EAAE;gBACXoB,QAAQ,EAAE,SAAS;gBACnBE,KAAK,EAAE,SAAS;gBAChBD,UAAU,EAAE,KAAK;gBACjBG,UAAU,EAAE;cACd,CAAE;cAAAZ,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACR3J,OAAA;YACEqH,IAAI,EAAC,aAAa;YAClBK,KAAK,EAAE3G,QAAQ,CAACG,WAAY;YAC5BsJ,QAAQ,EAAE/C,iBAAkB;YAC5BgD,IAAI,EAAE,CAAE;YACR3C,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBc,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBwB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,wBAAwB;cACpCC,MAAM,EAAE;YACV,CAAE;YACFC,WAAW,EAAC;UAAiF;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,GAAC,cAED,eAAA1I,OAAA;cAAM8H,KAAK,EAAE;gBACXoB,QAAQ,EAAE,SAAS;gBACnBE,KAAK,EAAE,SAAS;gBAChBD,UAAU,EAAE,KAAK;gBACjBG,UAAU,EAAE;cACd,CAAE;cAAAZ,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EACPtG,cAAc,CAACV,MAAM,GAAG,CAAC,iBACxB3C,OAAA;YAAK8H,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0B,GAAG,EAAE,QAAQ;cACbvB,OAAO,EAAE,gBAAgB;cACzBL,eAAe,EAAE,SAAS;cAC1BmB,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,KAAK;cACnBM,YAAY,EAAE,SAAS;cACvBG,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,gBACA1I,OAAA;cAAA0I,QAAA,EAAM;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACdtG,cAAc,CAACV,MAAM,EAAC,QAAM,EAACU,cAAc,CAACV,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,kBACrE;UAAA;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,eACD3J,OAAA,CAACH,mBAAmB;YAClBiL,cAAc,EAAE9I,iBAAkB;YAClCa,cAAc,EAAEA,cAAe;YAC/BkI,YAAY,EAAE9H,eAAgB;YAC9B+H,SAAS,EAAE,EAAG;YACdC,iBAAiB,EAAE3H,eAAgB;YACnC4H,mBAAmB,EAAE3H,iBAAkB;YACvCF,cAAc,EAAEA,cAAe;YAC/B8H,QAAQ,EAAE1K;UAAQ;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,eAAe;YAC/B6C,UAAU,EAAE,MAAM;YAClBC,SAAS,EAAE,mBAAmB;YAC9BrB,GAAG,EAAE;UACP,CAAE;UAAAtB,QAAA,gBACA1I,OAAA;YACE2H,IAAI,EAAC,QAAQ;YACbiC,OAAO,EAAE1E,WAAY;YACrB4C,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBL,eAAe,EAAE,SAAS;cAC1BgB,KAAK,EAAE,SAAS;cAChBG,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAE,SAAS;cACjBX,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3J,OAAA;YACE2H,IAAI,EAAC,QAAQ;YACbwD,QAAQ,EAAE1K,OAAQ;YAClBqH,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBoB,UAAU,EAAEpJ,OAAO,GAAG,SAAS,GAAG,mDAAmD;cACrF2I,KAAK,EAAE,OAAO;cACdG,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAErJ,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3C0I,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EAEDjI,OAAO,GAAG,WAAW,GAAG;UAAc;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;MAAA;MAEP;MACA3J,OAAA;QAAM+J,QAAQ,EAAE/D,YAAa;QAAA0C,QAAA,gBAE7B1I,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3J,OAAA;YACE2H,IAAI,EAAC,MAAM;YACXN,IAAI,EAAC,OAAO;YACZK,KAAK,EAAE3G,QAAQ,CAACE,KAAM;YACtBuJ,QAAQ,EAAE/C,iBAAkB;YAC5BK,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBc,MAAM,EAAE,aAAa1H,MAAM,CAACZ,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;cAC3D0H,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBwB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,WAAW,EAAC;UAAmB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,EACD9H,MAAM,CAACZ,KAAK,iBACXjB,OAAA;YAAG8H,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEoB,SAAS,EAAE;YAAU,CAAE;YAAA5B,QAAA,EACvE7G,MAAM,CAACZ;UAAK;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3J,OAAA;YACEqH,IAAI,EAAC,aAAa;YAClBK,KAAK,EAAE3G,QAAQ,CAACG,WAAY;YAC5BsJ,QAAQ,EAAE/C,iBAAkB;YAC5BgD,IAAI,EAAE,CAAE;YACR3C,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBc,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBwB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,wBAAwB;cACpCC,MAAM,EAAE;YACV,CAAE;YACFC,WAAW,EAAC;UAAoC;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfiD,mBAAmB,EAAE,SAAS;YAC9BtB,GAAG,EAAE,MAAM;YACXf,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBAEA1I,OAAA;YAAA0I,QAAA,gBACE1I,OAAA;cAAO8H,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBa,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBH,YAAY,EAAE;cAChB,CAAE;cAAAP,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3J,OAAA;cACE2H,IAAI,EAAC,MAAM;cACXN,IAAI,EAAC,YAAY;cACjBK,KAAK,EAAE3G,QAAQ,CAACI,UAAW;cAC3BqJ,QAAQ,EAAE/C,iBAAkB;cAC5BK,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBc,MAAM,EAAE,aAAa1H,MAAM,CAACV,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;gBAChEwH,YAAY,EAAE,KAAK;gBACnBO,QAAQ,EAAE,UAAU;gBACpBwB,OAAO,EAAE;cACX;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD9H,MAAM,CAACV,UAAU,iBAChBnB,OAAA;cAAG8H,KAAK,EAAE;gBAAEsB,KAAK,EAAE,SAAS;gBAAEF,QAAQ,EAAE,SAAS;gBAAEoB,SAAS,EAAE;cAAU,CAAE;cAAA5B,QAAA,EACvE7G,MAAM,CAACV;YAAU;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN3J,OAAA;YAAA0I,QAAA,gBACE1I,OAAA;cAAO8H,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBa,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBH,YAAY,EAAE;cAChB,CAAE;cAAAP,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3J,OAAA;cACE2H,IAAI,EAAC,MAAM;cACXN,IAAI,EAAC,UAAU;cACfK,KAAK,EAAE3G,QAAQ,CAACK,QAAS;cACzBoJ,QAAQ,EAAE/C,iBAAkB;cAC5BK,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBc,MAAM,EAAE,aAAa1H,MAAM,CAACT,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;gBAC9DuH,YAAY,EAAE,KAAK;gBACnBO,QAAQ,EAAE,UAAU;gBACpBwB,OAAO,EAAE;cACX;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD9H,MAAM,CAACT,QAAQ,iBACdpB,OAAA;cAAG8H,KAAK,EAAE;gBAAEsB,KAAK,EAAE,SAAS;gBAAEF,QAAQ,EAAE,SAAS;gBAAEoB,SAAS,EAAE;cAAU,CAAE;cAAA5B,QAAA,EACvE7G,MAAM,CAACT;YAAQ;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACP7I,eAAe,gBACdd,OAAA;YAAK8H,KAAK,EAAE;cACVW,OAAO,EAAE,SAAS;cAClBL,eAAe,EAAE,SAAS;cAC1BmB,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,UAAU;cACxBS,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,GAAC,4BACyB,EAAC5H,eAAe,eAC1Cd,OAAA;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3J,OAAA;cACE2H,IAAI,EAAC,QAAQ;cACbiC,OAAO,EAAEA,CAAA,KAAM2B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxC3D,KAAK,EAAE;gBACLwC,SAAS,EAAE,QAAQ;gBACnB7B,OAAO,EAAE,gBAAgB;gBACzBL,eAAe,EAAE,SAAS;gBAC1BgB,KAAK,EAAE,OAAO;gBACdG,MAAM,EAAE,MAAM;gBACdZ,YAAY,EAAE,SAAS;gBACvBO,QAAQ,EAAE,SAAS;gBACnBY,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,EACH;YAED;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,GACJ/I,iBAAiB,gBACnBZ,OAAA;YAAK8H,KAAK,EAAE;cACVW,OAAO,EAAE,SAAS;cAClBL,eAAe,EAAE,SAAS;cAC1BmB,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,UAAU;cACxBS,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJ,CAAChJ,UAAU,IAAIA,UAAU,CAACgC,MAAM,KAAK,CAAC,gBACxC3C,OAAA;YAAK8H,KAAK,EAAE;cACVW,OAAO,EAAE,SAAS;cAClBL,eAAe,EAAE,SAAS;cAC1BmB,MAAM,EAAE,mBAAmB;cAC3BZ,YAAY,EAAE,UAAU;cACxBS,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAEN3J,OAAA,CAACF,yBAAyB;YACxBa,UAAU,EAAEA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+K,MAAM,CAACC,QAAQ;YACrC;YACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAACrJ,QAAQ,CAACqJ,QAAQ,CAACtE,IAAI,CACjG,CAAE;YACFuE,kBAAkB,EAAE7K,QAAQ,CAACM,WAAW,GAAGiF,QAAQ,CAACvF,QAAQ,CAACM,WAAW,CAAC,GAAG+E,SAAU;YACtFyF,qBAAqB,EAAE9K,QAAQ,CAACO,cAAc,GAAGgF,QAAQ,CAACvF,QAAQ,CAACO,cAAc,CAAC,GAAG8E,SAAU;YAC/F0F,gBAAgB,EAAE9E,oBAAqB;YACvC+E,mBAAmB,EAAExE,uBAAwB;YAC7CsD,WAAW,EAAC,iBAAiB;YAC7BmB,QAAQ,EAAE,IAAK;YACfnL,KAAK,EAAEgB,MAAM,CAACR,WAAY;YAC1B8J,QAAQ,EAAE1K;UAAQ;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC1I,OAAA;YAAO8H,KAAK,EAAE;cACZO,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBY,QAAQ,EAAE,UAAU;cACpBE,KAAK,EAAE,SAAS;cAChBU,MAAM,EAAE,SAAS;cACjBb,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,gBACA1I,OAAA;cACE2H,IAAI,EAAC,UAAU;cACfN,IAAI,EAAC,cAAc;cACnBQ,OAAO,EAAE9G,QAAQ,CAACQ,YAAa;cAC/BiJ,QAAQ,EAAE/C,iBAAkB;cAC5BK,KAAK,EAAE;gBAAEmE,WAAW,EAAE;cAAS;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,mBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEP5I,QAAQ,CAACQ,YAAY,iBACpBvB,OAAA;YACEqH,IAAI,EAAC,oBAAoB;YACzBK,KAAK,EAAE3G,QAAQ,CAACS,kBAAmB;YACnCgJ,QAAQ,EAAE/C,iBAAkB;YAC5BK,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBc,MAAM,EAAE,aAAa1H,MAAM,CAACL,kBAAkB,GAAG,SAAS,GAAG,SAAS,EAAE;cACxEmH,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBwB,OAAO,EAAE,MAAM;cACftC,eAAe,EAAE;YACnB,CAAE;YAAAM,QAAA,gBAEF1I,OAAA;cAAQ0H,KAAK,EAAC,EAAE;cAAAgB,QAAA,EAAC;YAAyB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnD3J,OAAA;cAAQ0H,KAAK,EAAC,QAAQ;cAAAgB,QAAA,EAAC;YAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3J,OAAA;cAAQ0H,KAAK,EAAC,SAAS;cAAAgB,QAAA,EAAC;YAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC3J,OAAA;cAAQ0H,KAAK,EAAC,QAAQ;cAAAgB,QAAA,EAAC;YAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACT,EACA9H,MAAM,CAACL,kBAAkB,iBACxBxB,OAAA;YAAG8H,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEoB,SAAS,EAAE;YAAU,CAAE;YAAA5B,QAAA,EACvE7G,MAAM,CAACL;UAAkB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfiD,mBAAmB,EAAE,SAAS;YAC9BtB,GAAG,EAAE,MAAM;YACXf,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACA1I,OAAA;YAAA0I,QAAA,eACE1I,OAAA;cAAO8H,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBU,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBACA1I,OAAA;gBACE2H,IAAI,EAAC,UAAU;gBACfN,IAAI,EAAC,WAAW;gBAChBQ,OAAO,EAAE9G,QAAQ,CAACU,SAAU;gBAC5B+I,QAAQ,EAAE/C,iBAAkB;gBAC5BK,KAAK,EAAE;kBAAEmE,WAAW,EAAE;gBAAS;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,gBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN3J,OAAA;YAAA0I,QAAA,eACE1I,OAAA;cAAO8H,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBU,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBACA1I,OAAA;gBACE2H,IAAI,EAAC,UAAU;gBACfN,IAAI,EAAC,gBAAgB;gBACrBQ,OAAO,EAAE9G,QAAQ,CAACY,cAAe;gBACjC6I,QAAQ,EAAE/C,iBAAkB;gBAC5BK,KAAK,EAAE;kBAAEmE,WAAW,EAAE;gBAAS;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,kBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN3J,OAAA;YAAA0I,QAAA,eACE1I,OAAA;cAAO8H,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBU,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBACA1I,OAAA;gBACE2H,IAAI,EAAC,UAAU;gBACfN,IAAI,EAAC,UAAU;gBACfQ,OAAO,EAAE9G,QAAQ,CAACa,QAAS;gBAC3B4I,QAAQ,EAAE/C,iBAAkB;gBAC5BK,KAAK,EAAE;kBAAEmE,WAAW,EAAE;gBAAS;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,iBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3J,OAAA;UAAK8H,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrC1I,OAAA;YAAK8H,KAAK,EAAE;cAAEmB,YAAY,EAAE;YAAS,CAAE;YAAAP,QAAA,gBACrC1I,OAAA;cAAI8H,KAAK,EAAE;gBACToB,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBC,MAAM,EAAE;cACV,CAAE;cAAAX,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJtG,cAAc,CAACV,MAAM,GAAG,CAAC,iBACxB3C,OAAA;cAAK8H,KAAK,EAAE;gBACVO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0B,GAAG,EAAE,QAAQ;gBACbvB,OAAO,EAAE,gBAAgB;gBACzB6B,SAAS,EAAE,QAAQ;gBACnBlC,eAAe,EAAE,SAAS;gBAC1BO,YAAY,EAAE,KAAK;gBACnBY,MAAM,EAAE,mBAAmB;gBAC3BH,KAAK,EAAE,SAAS;gBAChBF,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,gBACA1I,OAAA;gBAAA0I,QAAA,EAAM;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACdtG,cAAc,CAACV,MAAM,EAAC,QAAM,EAACU,cAAc,CAACV,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,kBACrE;YAAA;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN3J,OAAA,CAACH,mBAAmB;YAClBiL,cAAc,EAAE9I,iBAAkB;YAClCa,cAAc,EAAEA,cAAe;YAC/BkI,YAAY,EAAE9H,eAAgB;YAC9B+H,SAAS,EAAE,EAAG;YACdG,QAAQ,EAAErI,YAAa;YACvBO,cAAc,EAAEA,cAAe;YAC/B4H,iBAAiB,EAAE3H,eAAgB;YACnC4H,mBAAmB,EAAE3H;UAAkB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACD5G,UAAU,iBACT/C,OAAA;YAAK8H,KAAK,EAAE;cACVsB,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,UAAU;cACpBoB,SAAS,EAAE;YACb,CAAE;YAAA5B,QAAA,EACC3F;UAAU;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL1H,cAAc,iBACbjC,OAAA;UAAK8H,KAAK,EAAE;YACVM,eAAe,EAAE,SAAS;YAC1BmB,MAAM,EAAE,mBAAmB;YAC3BZ,YAAY,EAAE,KAAK;YACnBF,OAAO,EAAE,SAAS;YAClBQ,YAAY,EAAE,MAAM;YACpBG,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,EACCzG;QAAc;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACN,EAEAxH,YAAY,iBACXnC,OAAA;UAAK8H,KAAK,EAAE;YACVM,eAAe,EAAE,SAAS;YAC1BmB,MAAM,EAAE,mBAAmB;YAC3BZ,YAAY,EAAE,KAAK;YACnBF,OAAO,EAAE,SAAS;YAClBQ,YAAY,EAAE,MAAM;YACpBG,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,EACCvG;QAAY;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD3J,OAAA;UAAK8H,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,UAAU;YAC1ByB,GAAG,EAAE;UACP,CAAE;UAAAtB,QAAA,gBACA1I,OAAA;YACE2H,IAAI,EAAC,QAAQ;YACbiC,OAAO,EAAE1E,WAAY;YACrB4C,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBL,eAAe,EAAE,SAAS;cAC1BgB,KAAK,EAAE,SAAS;cAChBG,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAE,SAAS;cACjBX,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3J,OAAA;YACE2H,IAAI,EAAC,QAAQ;YACbwD,QAAQ,EAAE1K,OAAQ;YAClBqH,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBoB,UAAU,EAAEpJ,OAAO,GAAG,SAAS,GAAG,mDAAmD;cACrF2I,KAAK,EAAE,OAAO;cACdG,MAAM,EAAE,MAAM;cACdZ,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAErJ,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3C0I,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EAEDjI,OAAO,GAAG,WAAW,GAAIF,KAAK,GAAG,QAAQ,GAAG;UAAS;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjJ,EAAA,CA/hCIP,kBAAqD;EAAA,QAQkBR,yBAAyB,EAmDhGC,sBAAsB;AAAA;AAAAsM,EAAA,GA3DtB/L,kBAAqD;AAiiC3D,eAAeA,kBAAkB;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}