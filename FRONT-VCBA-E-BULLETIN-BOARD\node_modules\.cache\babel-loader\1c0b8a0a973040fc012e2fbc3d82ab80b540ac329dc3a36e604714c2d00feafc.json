{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"path\", {\n  d: \"M8 12h4\",\n  key: \"qz6y1c\"\n}], [\"path\", {\n  d: \"M10 16V9.5a2.5 2.5 0 0 1 5 0\",\n  key: \"3mlbjk\"\n}], [\"path\", {\n  d: \"M8 16h7\",\n  key: \"sbedsn\"\n}]];\nconst BadgePoundSterling = createLucideIcon(\"badge-pound-sterling\", __iconNode);\nexport { __iconNode, BadgePoundSterling as default };\n//# sourceMappingURL=badge-pound-sterling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}