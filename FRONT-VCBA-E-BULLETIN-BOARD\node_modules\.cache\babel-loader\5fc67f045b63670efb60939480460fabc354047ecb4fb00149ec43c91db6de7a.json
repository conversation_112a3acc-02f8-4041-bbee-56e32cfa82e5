{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 22h8\",\n  key: \"rmew8v\"\n}], [\"path\", {\n  d: \"M12 11v11\",\n  key: \"ur9y6a\"\n}], [\"path\", {\n  d: \"m19 3-7 8-7-8Z\",\n  key: \"1sgpiw\"\n}]];\nconst Martini = createLucideIcon(\"martini\", __iconNode);\nexport { __iconNode, <PERSON>i as default };\n//# sourceMappingURL=martini.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}