{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\",\n  key: \"116196\"\n}], [\"path\", {\n  d: \"M12 11h4\",\n  key: \"1jrz19\"\n}], [\"path\", {\n  d: \"M12 16h4\",\n  key: \"n85exb\"\n}], [\"path\", {\n  d: \"M8 11h.01\",\n  key: \"1dfujw\"\n}], [\"path\", {\n  d: \"M8 16h.01\",\n  key: \"18s6g9\"\n}]];\nconst ClipboardList = createLucideIcon(\"clipboard-list\", __iconNode);\nexport { __iconNode, ClipboardList as default };\n//# sourceMappingURL=clipboard-list.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}