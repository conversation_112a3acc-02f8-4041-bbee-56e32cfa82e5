{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 12a10 10 0 1 1 10 10\",\n  key: \"1yn6ov\"\n}], [\"path\", {\n  d: \"m2 22 10-10\",\n  key: \"28ilpk\"\n}], [\"path\", {\n  d: \"M8 22H2v-6\",\n  key: \"sulq54\"\n}]];\nconst CircleArrowOutDownLeft = createLucideIcon(\"circle-arrow-out-down-left\", __iconNode);\nexport { __iconNode, CircleArrowOutDownLeft as default };\n//# sourceMappingURL=circle-arrow-out-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}