{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 19h6\",\n  key: \"456am0\"\n}], [\"path\", {\n  d: \"M9 15v-3H5l7-7 7 7h-4v3H9z\",\n  key: \"1r2uve\"\n}]];\nconst ArrowBigUpDash = createLucideIcon(\"arrow-big-up-dash\", __iconNode);\nexport { __iconNode, ArrowBigUpDash as default };\n//# sourceMappingURL=arrow-big-up-dash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}