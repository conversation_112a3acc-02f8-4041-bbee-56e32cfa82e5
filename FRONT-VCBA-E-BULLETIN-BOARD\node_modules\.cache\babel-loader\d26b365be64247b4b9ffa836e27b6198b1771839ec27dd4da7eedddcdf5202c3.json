{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"8\",\n  x: \"5\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"wc9tft\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"w68u3i\"\n}], [\"path\", {\n  d: \"M6 18h2\",\n  key: \"rwmk9e\"\n}], [\"path\", {\n  d: \"M12 18h6\",\n  key: \"aqd8w3\"\n}]];\nconst Computer = createLucideIcon(\"computer\", __iconNode);\nexport { __iconNode, Computer as default };\n//# sourceMappingURL=computer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}