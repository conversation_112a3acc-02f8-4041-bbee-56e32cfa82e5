{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\",\n  key: \"1m0v6g\"\n}], [\"path\", {\n  d: \"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\",\n  key: \"ohrbg2\"\n}]];\nconst SquarePen = createLucideIcon(\"square-pen\", __iconNode);\nexport { __iconNode, SquarePen as default };\n//# sourceMappingURL=square-pen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}