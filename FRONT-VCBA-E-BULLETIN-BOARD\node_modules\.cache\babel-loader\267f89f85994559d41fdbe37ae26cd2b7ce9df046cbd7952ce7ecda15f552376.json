{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"17\",\n  cy: \"4\",\n  r: \"2\",\n  key: \"y5j2s2\"\n}], [\"path\", {\n  d: \"M15.59 5.41 5.41 15.59\",\n  key: \"l0vprr\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"17\",\n  r: \"2\",\n  key: \"9p4efm\"\n}], [\"path\", {\n  d: \"M12 22s-4-9-1.5-11.5S22 12 22 12\",\n  key: \"1twk4o\"\n}]];\nconst Tangent = createLucideIcon(\"tangent\", __iconNode);\nexport { __iconNode, Tangent as default };\n//# sourceMappingURL=tangent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}