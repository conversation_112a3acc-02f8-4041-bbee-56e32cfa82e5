{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.188 8.5A6 6 0 0 1 16 4a1 1 0 0 0 6 6 6 6 0 0 1-3 5.197\",\n  key: \"erj67n\"\n}], [\"path\", {\n  d: \"M11 20v2\",\n  key: \"174qtz\"\n}], [\"path\", {\n  d: \"M3 20a5 5 0 1 1 8.9-4H13a3 3 0 0 1 2 5.24\",\n  key: \"1qmrp3\"\n}], [\"path\", {\n  d: \"M7 19v2\",\n  key: \"12npes\"\n}]];\nconst CloudMoonRain = createLucideIcon(\"cloud-moon-rain\", __iconNode);\nexport { __iconNode, CloudMoonRain as default };\n//# sourceMappingURL=cloud-moon-rain.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}