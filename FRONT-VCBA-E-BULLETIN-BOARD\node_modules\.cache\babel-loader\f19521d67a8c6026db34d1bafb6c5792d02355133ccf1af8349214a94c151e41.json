{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z\",\n  key: \"l5xja\"\n}], [\"path\", {\n  d: \"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z\",\n  key: \"ep3f8r\"\n}], [\"path\", {\n  d: \"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4\",\n  key: \"1p4c4q\"\n}], [\"path\", {\n  d: \"M17.599 6.5a3 3 0 0 0 .399-1.375\",\n  key: \"tmeiqw\"\n}], [\"path\", {\n  d: \"M6.003 5.125A3 3 0 0 0 6.401 6.5\",\n  key: \"105sqy\"\n}], [\"path\", {\n  d: \"M3.477 10.896a4 4 0 0 1 .585-.396\",\n  key: \"ql3yin\"\n}], [\"path\", {\n  d: \"M19.938 10.5a4 4 0 0 1 .585.396\",\n  key: \"1qfode\"\n}], [\"path\", {\n  d: \"M6 18a4 4 0 0 1-1.967-.516\",\n  key: \"2e4loj\"\n}], [\"path\", {\n  d: \"M19.967 17.484A4 4 0 0 1 18 18\",\n  key: \"159ez6\"\n}]];\nconst Brain = createLucideIcon(\"brain\", __iconNode);\nexport { __iconNode, Brain as default };\n//# sourceMappingURL=brain.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}