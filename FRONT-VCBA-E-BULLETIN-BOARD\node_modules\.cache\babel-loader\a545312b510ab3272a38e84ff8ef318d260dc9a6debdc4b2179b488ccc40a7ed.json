{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M2 8h20\",\n  key: \"d11cs7\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"14\",\n  r: \"2\",\n  key: \"1k2qr5\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"14\",\n  r: \"2\",\n  key: \"14k7lr\"\n}]];\nconst Videotape = createLucideIcon(\"videotape\", __iconNode);\nexport { __iconNode, Videotape as default };\n//# sourceMappingURL=videotape.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}