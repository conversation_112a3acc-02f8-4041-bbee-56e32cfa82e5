{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8\",\n  key: \"u0tga0\"\n}], [\"path\", {\n  d: \"m4 8 16-4\",\n  key: \"16g0ng\"\n}], [\"path\", {\n  d: \"m8.86 6.78-.45-1.81a2 2 0 0 1 1.45-2.43l1.94-.48a2 2 0 0 1 2.43 1.46l.45 1.8\",\n  key: \"12cejc\"\n}]];\nconst CookingPot = createLucideIcon(\"cooking-pot\", __iconNode);\nexport { __iconNode, CookingPot as default };\n//# sourceMappingURL=cooking-pot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}