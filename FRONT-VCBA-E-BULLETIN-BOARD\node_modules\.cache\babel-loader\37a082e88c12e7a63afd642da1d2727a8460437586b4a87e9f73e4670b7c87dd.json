{"ast": null, "code": "import{useEffect,useCallback}from'react';import{useNavigate,useLocation}from'react-router-dom';import{notificationNavigationService}from'../services/notificationNavigationService';/**\n * Professional React Hook for Notification Navigation\n * Handles notification click behavior and deep linking\n * Following Google/OpenAI React best practices\n */export const useNotificationNavigation=options=>{const navigate=useNavigate();const location=useLocation();const{userRole,onNavigationStart,onNavigationComplete,onNavigationError}=options;// Initialize navigation service\nuseEffect(()=>{notificationNavigationService.initialize(navigate,userRole);},[navigate,userRole]);// Handle URL parameters for deep linking\nuseEffect(()=>{const handleDeepLink=async()=>{const urlParams=new URLSearchParams(location.search);const notificationId=urlParams.get('notification');const focusType=urlParams.get('focus');const targetId=urlParams.get('id');const commentId=urlParams.get('comment');if(notificationId||focusType&&targetId){console.log('🔗 Deep link detected:',{notificationId,focusType,targetId,commentId});// Handle highlighting and scrolling for deep links\nsetTimeout(()=>{handleDeepLinkHighlighting(focusType,targetId,commentId);},500);// Allow page to render first\n}};handleDeepLink();},[location]);// Handle deep link highlighting and scrolling\nconst handleDeepLinkHighlighting=useCallback((focusType,targetId,commentId)=>{try{let targetElement=null;// Find target element based on focus type\nif(focusType==='announcement'&&targetId){targetElement=document.getElementById(\"announcement-\".concat(targetId));}else if(focusType==='comment'&&commentId){targetElement=document.getElementById(\"comment-\".concat(commentId));}else if(focusType==='event'&&targetId){targetElement=document.getElementById(\"event-\".concat(targetId));}if(targetElement){// Smooth scroll to target\ntargetElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});// Add highlight effect\ntargetElement.classList.add('notification-highlight');// Remove highlight after animation\nsetTimeout(()=>{var _targetElement;(_targetElement=targetElement)===null||_targetElement===void 0?void 0:_targetElement.classList.remove('notification-highlight');},3000);console.log('✨ Highlighted target element:',targetElement.id);}else{console.warn('🎯 Target element not found for deep link');}}catch(error){console.error('Error handling deep link highlighting:',error);}},[]);// Main notification click handler\nconst handleNotificationClick=useCallback(async notification=>{try{console.log('🔔 Handling notification click:',notification);// Trigger navigation start callback\nonNavigationStart===null||onNavigationStart===void 0?void 0:onNavigationStart(notification);// Use navigation service to handle the click\nconst success=await notificationNavigationService.handleNotificationClick(notification,{markAsRead:true,highlightTarget:true,scrollBehavior:'smooth'});// Trigger navigation complete callback\nonNavigationComplete===null||onNavigationComplete===void 0?void 0:onNavigationComplete(notification,success);if(!success){throw new Error('Navigation failed');}}catch(error){console.error('Error in notification click handler:',error);onNavigationError===null||onNavigationError===void 0?void 0:onNavigationError(error,notification);// Fallback navigation\nconst fallbackRoute=userRole==='admin'?'/admin/dashboard':'/student/dashboard';navigate(fallbackRoute);}},[navigate,userRole,onNavigationStart,onNavigationComplete,onNavigationError]);return{handleNotificationClick,isNavigating:false,// Could be enhanced with loading state\nlastNavigatedNotification:null// Could be enhanced with state tracking\n};};// Define the navigation state interface\n/**\n * Hook for handling notification-triggered page behavior\n * Use this in pages that can be targets of notification navigation\n */export const useNotificationTarget=()=>{const location=useLocation();useEffect(()=>{// Check if we arrived here from a notification\nconst state=location.state;if(state!==null&&state!==void 0&&state.fromNotification){console.log('📍 Page loaded from notification:',state);// Handle highlighting and scrolling\nif(state.scrollTo&&state.highlightTarget){setTimeout(()=>{const targetElement=document.getElementById(state.scrollTo);if(targetElement){// Scroll to target\ntargetElement.scrollIntoView({behavior:state.scrollBehavior||'smooth',block:'center',inline:'nearest'});// Add highlight effect\ntargetElement.classList.add('notification-highlight');// Remove highlight after animation\nsetTimeout(()=>{targetElement.classList.remove('notification-highlight');},3000);console.log('✨ Auto-highlighted notification target:',state.scrollTo);}},300);// Allow page to render\n}}},[location]);const state=location.state;return{isFromNotification:(state===null||state===void 0?void 0:state.fromNotification)||false,notificationId:(state===null||state===void 0?void 0:state.notificationId)||null,scrollTarget:(state===null||state===void 0?void 0:state.scrollTo)||null};};/**\n * Hook for managing notification read status\n */export const useNotificationReadStatus=()=>{const markAsRead=useCallback(async(notificationId,userRole)=>{try{// Import notification service dynamically\nconst{studentNotificationService,adminNotificationService}=await import('../services/notificationService');const service=userRole==='admin'?adminNotificationService:studentNotificationService;await service.markAsRead(notificationId);console.log('✅ Marked notification as read:',notificationId);return true;}catch(error){console.error('Failed to mark notification as read:',error);return false;}},[]);const markAllAsRead=useCallback(async userRole=>{try{// Import notification service dynamically\nconst{studentNotificationService,adminNotificationService}=await import('../services/notificationService');const service=userRole==='admin'?adminNotificationService:studentNotificationService;await service.markAllAsRead();console.log('✅ Marked all notifications as read');return true;}catch(error){console.error('Failed to mark all notifications as read:',error);return false;}},[]);return{markAsRead,markAllAsRead};};export default useNotificationNavigation;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}