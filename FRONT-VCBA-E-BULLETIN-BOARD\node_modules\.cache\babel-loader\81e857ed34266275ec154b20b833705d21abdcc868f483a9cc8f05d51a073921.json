{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 10H6a4 4 0 0 1-4-4 1 1 0 0 1 1-1h4\",\n  key: \"1hjpb6\"\n}], [\"path\", {\n  d: \"M7 5a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1 7 7 0 0 1-7 7H8a1 1 0 0 1-1-1z\",\n  key: \"1qn45f\"\n}], [\"path\", {\n  d: \"M9 12v5\",\n  key: \"3anwtq\"\n}], [\"path\", {\n  d: \"M15 12v5\",\n  key: \"5xh3zn\"\n}], [\"path\", {\n  d: \"M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3 1 1 0 0 1-1 1H6a1 1 0 0 1-1-1\",\n  key: \"1fi4x8\"\n}]];\nconst Anvil = createLucideIcon(\"anvil\", __iconNode);\nexport { __iconNode, Anvil as default };\n//# sourceMappingURL=anvil.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}