{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 18-6-6 6-6\",\n  key: \"1yerx2\"\n}], [\"path\", {\n  d: \"M7 6v12\",\n  key: \"1p53r6\"\n}]];\nconst ChevronFirst = createLucideIcon(\"chevron-first\", __iconNode);\nexport { __iconNode, ChevronFirst as default };\n//# sourceMappingURL=chevron-first.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}