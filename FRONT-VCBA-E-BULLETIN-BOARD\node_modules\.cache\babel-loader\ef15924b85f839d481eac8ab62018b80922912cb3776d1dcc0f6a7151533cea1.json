{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 11-5-5-5 5\",\n  key: \"e8nh98\"\n}], [\"path\", {\n  d: \"m17 18-5-5-5 5\",\n  key: \"2avn1x\"\n}]];\nconst ChevronsUp = createLucideIcon(\"chevrons-up\", __iconNode);\nexport { __iconNode, ChevronsUp as default };\n//# sourceMappingURL=chevrons-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}