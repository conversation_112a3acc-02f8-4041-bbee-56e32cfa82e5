import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, Users, BookOpen, Calendar, Award, ArrowRight } from 'lucide-react';

const WelcomePage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);

    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleStudentLogin = () => {
    navigate('/student/login');
  };

  const features = [
    {
      icon: <BookOpen size={24} />,
      title: "Academic Excellence",
      description: "Access to quality education and comprehensive learning resources"
    },
    {
      icon: <Users size={24} />,
      title: "Vibrant Community",
      description: "Join a diverse community of students, faculty, and staff"
    },
    {
      icon: <Calendar size={24} />,
      title: "Campus Events",
      description: "Stay updated with the latest announcements and campus activities"
    },
    {
      icon: <Award size={24} />,
      title: "Achievement Recognition",
      description: "Celebrate academic and extracurricular accomplishments"
    }
  ];

  return (
    <div className="welcome-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-background">
          <div className="hero-overlay"></div>
          <img
            src="/villamor-image/villamor-collge-BG-landscape.jpg"
            alt="Villamor College Campus"
            className="hero-image"
            style={{
              transform: `translateY(${scrollY * 0.5}px)`,
            }}
          />
        </div>
        
        <div className="hero-content">
          <div className="container">
            <div className="hero-inner">
              {/* Logo Section */}
              <div className={`logo-section ${isLoaded ? 'animate-fade-in' : ''}`}>
                <img 
                  src="/logo/vcba1.png" 
                  alt="Villamor College of Business and Arts" 
                  className="college-logo"
                />
              </div>

              {/* Main Content */}
              <div className={`main-content ${isLoaded ? 'animate-fade-in' : ''}`}>
                <h1 className="hero-title">
                  Welcome to
                  <span className="title-highlight">Villamor College</span>
                  of Business and Arts
                </h1>
                
                <p className="hero-subtitle">
                  Your gateway to academic excellence and personal growth. 
                  Stay connected with the latest announcements, events, and opportunities 
                  that shape your educational journey.
                </p>

                {/* CTA Button */}
                <div className="cta-section">
                  <button 
                    onClick={handleStudentLogin}
                    className="cta-button"
                  >
                    <span>Student Portal</span>
                    <ArrowRight size={20} />
                  </button>
                  
                  <p className="cta-subtitle">
                    Access your personalized dashboard, announcements, and campus updates
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="scroll-indicator">
          <div className="scroll-arrow"></div>
          <span>Scroll to explore</span>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container">
          <div className="features-header">
            <h2>Why Choose Villamor College?</h2>
            <p>Discover what makes our institution a leader in business and arts education</p>
          </div>
          
          <div className="features-grid">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className={`feature-card ${isLoaded ? 'animate-fade-in' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="feature-icon">
                  {feature.icon}
                </div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="final-cta-section">
        <div className="container">
          <div className="final-cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of students who trust Villamor College for their educational journey</p>
            <button 
              onClick={handleStudentLogin}
              className="secondary-cta-button"
            >
              Access Student Portal
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </section>

      <style>{`
        .welcome-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        /* Hero Section */
        .hero-section {
          position: relative;
          min-height: 100vh;
          display: flex;
          align-items: center;
          overflow: hidden;
        }

        .hero-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1;
        }

        .hero-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }

        .hero-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            rgba(34, 197, 94, 0.9) 0%,
            rgba(21, 128, 61, 0.8) 50%,
            rgba(20, 83, 45, 0.9) 100%
          );
          z-index: 2;
        }

        .hero-content {
          position: relative;
          z-index: 3;
          width: 100%;
          padding: 2rem 0;
        }

        /* Scroll Indicator */
        .scroll-indicator {
          position: absolute;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.875rem;
          z-index: 4;
          animation: bounce 2s infinite;
        }

        .scroll-arrow {
          width: 24px;
          height: 24px;
          border: 2px solid rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          position: relative;
        }

        .scroll-arrow::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(45deg);
          width: 8px;
          height: 8px;
          border-right: 2px solid rgba(255, 255, 255, 0.8);
          border-bottom: 2px solid rgba(255, 255, 255, 0.8);
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateX(-50%) translateY(0);
          }
          40% {
            transform: translateX(-50%) translateY(-10px);
          }
          60% {
            transform: translateX(-50%) translateY(-5px);
          }
        }

        .container {
          max-width: 1280px;
          margin: 0 auto;
          padding: 0 1rem;
        }

        .hero-inner {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          gap: 3rem;
        }

        /* Logo Section */
        .logo-section {
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.8s ease-out;
        }

        .logo-section.animate-fade-in {
          opacity: 1;
          transform: translateY(0);
        }

        .college-logo {
          height: 120px;
          width: auto;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        /* Main Content */
        .main-content {
          max-width: 800px;
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.8s ease-out 0.2s;
        }

        .main-content.animate-fade-in {
          opacity: 1;
          transform: translateY(0);
        }

        .hero-title {
          font-size: 3.5rem;
          font-weight: 700;
          color: white;
          margin-bottom: 1.5rem;
          line-height: 1.1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .title-highlight {
          display: block;
          color: #fbbf24;
          font-size: 4rem;
          margin: 0.5rem 0;
        }

        .hero-subtitle {
          font-size: 1.25rem;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 3rem;
          line-height: 1.6;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* CTA Section */
        .cta-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
        }

        .cta-button {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
          color: #1f2937;
          font-size: 1.125rem;
          font-weight: 600;
          padding: 1rem 2rem;
          border: none;
          border-radius: 50px;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .cta-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(251, 191, 36, 0.6);
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .cta-subtitle {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.95rem;
          text-align: center;
          max-width: 400px;
        }

        /* Features Section */
        .features-section {
          padding: 6rem 0;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
          position: relative;
          overflow: hidden;
        }

        .features-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background:
            radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);
          pointer-events: none;
        }

        .features-header {
          text-align: center;
          margin-bottom: 4rem;
        }

        .features-header h2 {
          font-size: 2.5rem;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .features-header p {
          font-size: 1.125rem;
          color: #6b7280;
          max-width: 600px;
          margin: 0 auto;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          margin-top: 3rem;
        }

        .feature-card {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          padding: 2rem;
          border-radius: 20px;
          text-align: center;
          box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 1px 0 rgba(255, 255, 255, 0.5) inset;
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          opacity: 0;
          transform: translateY(30px);
          position: relative;
          overflow: hidden;
        }

        .feature-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          transition: left 0.5s ease;
        }

        .feature-card:hover::before {
          left: 100%;
        }

        .feature-card.animate-fade-in {
          opacity: 1;
          transform: translateY(0);
        }

        .feature-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 1px 0 rgba(255, 255, 255, 0.6) inset;
          border-color: rgba(34, 197, 94, 0.3);
        }

        .feature-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
          color: white;
        }

        .feature-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .feature-description {
          color: #6b7280;
          line-height: 1.6;
        }

        /* Final CTA Section */
        .final-cta-section {
          padding: 4rem 0;
          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }

        .final-cta-content {
          text-align: center;
          color: white;
        }

        .final-cta-content h2 {
          font-size: 2.25rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: white;
        }

        .final-cta-content p {
          font-size: 1.125rem;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 2rem;
          max-width: 500px;
          margin-left: auto;
          margin-right: auto;
        }

        .secondary-cta-button {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          background: transparent;
          color: #22c55e;
          font-size: 1.125rem;
          font-weight: 600;
          padding: 1rem 2rem;
          border: 2px solid #22c55e;
          border-radius: 50px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .secondary-cta-button:hover {
          background: #22c55e;
          color: white;
          transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
          .hero-title {
            font-size: 3rem;
          }

          .title-highlight {
            font-size: 3.5rem;
          }

          .features-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (max-width: 768px) {
          .container {
            padding: 0 1rem;
          }

          .hero-inner {
            gap: 2rem;
          }

          .hero-title {
            font-size: 2.5rem;
          }

          .title-highlight {
            font-size: 3rem;
          }

          .hero-subtitle {
            font-size: 1.125rem;
            margin-bottom: 2rem;
          }

          .college-logo {
            height: 80px;
          }

          .features-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .features-header h2 {
            font-size: 2rem;
          }

          .final-cta-content h2 {
            font-size: 1.875rem;
          }

          .features-section {
            padding: 4rem 0;
          }

          .final-cta-section {
            padding: 3rem 0;
          }
        }

        @media (max-width: 480px) {
          .hero-section {
            min-height: 100vh;
            padding: 1rem 0;
          }

          .hero-inner {
            gap: 1.5rem;
          }

          .hero-title {
            font-size: 2rem;
            line-height: 1.2;
          }

          .title-highlight {
            font-size: 2.5rem;
            margin: 0.25rem 0;
          }

          .hero-subtitle {
            font-size: 1rem;
            margin-bottom: 1.5rem;
          }

          .cta-button {
            font-size: 1rem;
            padding: 0.875rem 1.5rem;
            width: 100%;
            max-width: 280px;
          }

          .college-logo {
            height: 60px;
          }

          .feature-card {
            padding: 1.5rem;
          }

          .features-header {
            margin-bottom: 2rem;
          }

          .features-header h2 {
            font-size: 1.75rem;
          }

          .final-cta-content h2 {
            font-size: 1.5rem;
          }

          .secondary-cta-button {
            width: 100%;
            max-width: 280px;
            justify-content: center;
          }
        }

        @media (max-width: 360px) {
          .hero-title {
            font-size: 1.75rem;
          }

          .title-highlight {
            font-size: 2.25rem;
          }

          .hero-subtitle {
            font-size: 0.95rem;
          }

          .college-logo {
            height: 50px;
          }

          .cta-button {
            font-size: 0.95rem;
            padding: 0.75rem 1.25rem;
          }
        }

        /* Enhanced animations and interactions */
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        .college-logo {
          animation: float 6s ease-in-out infinite;
        }

        @keyframes pulse {
          0%, 100% { box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4); }
          50% { box-shadow: 0 4px 25px rgba(251, 191, 36, 0.6); }
        }

        .cta-button {
          animation: pulse 2s ease-in-out infinite;
        }

        .cta-button:hover {
          animation: none;
        }

        /* Improved accessibility */
        @media (prefers-reduced-motion: reduce) {
          .college-logo,
          .cta-button,
          .feature-card,
          .main-content,
          .logo-section {
            animation: none !important;
            transition: none !important;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .hero-overlay {
            background: rgba(0, 0, 0, 0.8);
          }

          .cta-button {
            border: 2px solid #000;
          }

          .feature-card {
            border: 2px solid #000;
          }
        }
      `}</style>
    </div>
  );
};

export default WelcomePage;
