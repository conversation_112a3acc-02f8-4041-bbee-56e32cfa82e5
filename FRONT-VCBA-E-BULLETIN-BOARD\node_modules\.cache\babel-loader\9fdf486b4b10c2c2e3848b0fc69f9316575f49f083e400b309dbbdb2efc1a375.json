{"ast": null, "code": "import{ADMIN_AUTH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY,ADMIN_USER_DATA_KEY,STUDENT_USER_DATA_KEY}from'../config/constants';/**\n * Detects the current user's role and authentication status\n * by checking available tokens and user data in localStorage\n */export const detectUserContext=()=>{const adminToken=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);const studentToken=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);const adminUserData=localStorage.getItem(ADMIN_USER_DATA_KEY);const studentUserData=localStorage.getItem(STUDENT_USER_DATA_KEY);// Check current path to help determine context\nconst currentPath=window.location.pathname;const isAdminPath=currentPath.includes('/admin');const isStudentPath=currentPath.includes('/student');// Priority 1: If we're on an admin path and have admin auth\nif(isAdminPath&&adminToken&&adminUserData){try{const user=JSON.parse(adminUserData);if(user.role==='admin'){return{role:'admin',user,token:adminToken,isAuthenticated:true};}}catch(error){console.warn('Failed to parse admin user data:',error);}}// Priority 2: If we're on a student path and have student auth\nif(isStudentPath&&studentToken&&studentUserData){try{const user=JSON.parse(studentUserData);if(user.role==='student'){return{role:'student',user,token:studentToken,isAuthenticated:true};}}catch(error){console.warn('Failed to parse student user data:',error);}}// Priority 3: Check admin auth regardless of path\nif(adminToken&&adminUserData){try{const user=JSON.parse(adminUserData);if(user.role==='admin'){return{role:'admin',user,token:adminToken,isAuthenticated:true};}}catch(error){console.warn('Failed to parse admin user data:',error);}}// Priority 4: Check student auth regardless of path\nif(studentToken&&studentUserData){try{const user=JSON.parse(studentUserData);if(user.role==='student'){return{role:'student',user,token:studentToken,isAuthenticated:true};}}catch(error){console.warn('Failed to parse student user data:',error);}}// No valid authentication found\nreturn{role:null,user:null,token:null,isAuthenticated:false};};/**\n * Gets the appropriate authentication context hook based on detected role\n */export const getAuthContextForRole=role=>{if(role==='admin'){return'admin';}else if(role==='student'){return'student';}return null;};/**\n * Determines if the current user has admin privileges\n */export const isAdminUser=()=>{const context=detectUserContext();return context.role==='admin'&&context.isAuthenticated;};/**\n * Determines if the current user has student privileges\n */export const isStudentUser=()=>{const context=detectUserContext();return context.role==='student'&&context.isAuthenticated;};/**\n * Gets the current user's role as a string\n */export const getCurrentUserRole=()=>{const context=detectUserContext();return context.role;};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}