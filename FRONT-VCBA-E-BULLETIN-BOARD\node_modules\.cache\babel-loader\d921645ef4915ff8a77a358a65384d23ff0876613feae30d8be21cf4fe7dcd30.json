{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\Toast.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { CheckCircle, AlertCircle, X, Info } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Toast = ({\n  id,\n  type,\n  title,\n  message,\n  duration = 4000,\n  onClose\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  const [isExiting, setIsExiting] = useState(false);\n  useEffect(() => {\n    // Trigger entrance animation\n    const timer = setTimeout(() => setIsVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration]);\n  const handleClose = () => {\n    setIsExiting(true);\n    setTimeout(() => {\n      onClose(id);\n    }, 300); // Match exit animation duration\n  };\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 20,\n          color: \"#22c55e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20,\n          color: \"#ef4444\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20,\n          color: \"#f59e0b\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 16\n        }, this);\n      case 'info':\n      default:\n        return /*#__PURE__*/_jsxDEV(Info, {\n          size: 20,\n          color: \"#3b82f6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getBackgroundColor = () => {\n    switch (type) {\n      case 'success':\n        return '#f0fdf4';\n      case 'error':\n        return '#fef2f2';\n      case 'warning':\n        return '#fffbeb';\n      case 'info':\n      default:\n        return '#eff6ff';\n    }\n  };\n  const getBorderColor = () => {\n    switch (type) {\n      case 'success':\n        return '#bbf7d0';\n      case 'error':\n        return '#fecaca';\n      case 'warning':\n        return '#fed7aa';\n      case 'info':\n      default:\n        return '#bfdbfe';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '1rem',\n      right: '1rem',\n      zIndex: 9999,\n      minWidth: '320px',\n      maxWidth: '480px',\n      backgroundColor: getBackgroundColor(),\n      border: `1px solid ${getBorderColor()}`,\n      borderRadius: '8px',\n      padding: '1rem',\n      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n      transform: `translateX(${isVisible && !isExiting ? '0' : '100%'})`,\n      opacity: isVisible && !isExiting ? 1 : 0,\n      transition: 'all 0.3s ease-in-out'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flexShrink: 0,\n          marginTop: '0.125rem'\n        },\n        children: getIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            color: '#1f2937',\n            marginBottom: message ? '0.25rem' : 0\n          },\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.8rem',\n            color: '#6b7280',\n            lineHeight: '1.4'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleClose,\n        style: {\n          background: 'none',\n          border: 'none',\n          cursor: 'pointer',\n          padding: '0.25rem',\n          borderRadius: '4px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#6b7280',\n          transition: 'color 0.2s ease'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.color = '#374151';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.color = '#6b7280';\n        },\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Toast, \"JUsyJdT9Wv4QGINXJnvwXNh4GwQ=\");\n_c = Toast;\nexport default Toast;\nvar _c;\n$RefreshReg$(_c, \"Toast\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "CheckCircle", "AlertCircle", "X", "Info", "jsxDEV", "_jsxDEV", "Toast", "id", "type", "title", "message", "duration", "onClose", "_s", "isVisible", "setIsVisible", "isExiting", "setIsExiting", "timer", "setTimeout", "clearTimeout", "handleClose", "getIcon", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getBackgroundColor", "getBorderColor", "style", "position", "top", "right", "zIndex", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "backgroundColor", "border", "borderRadius", "padding", "boxShadow", "transform", "opacity", "transition", "children", "display", "alignItems", "gap", "flexShrink", "marginTop", "flex", "fontSize", "fontWeight", "marginBottom", "lineHeight", "onClick", "background", "cursor", "justifyContent", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/Toast.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { CheckCircle, AlertCircle, X, Info } from 'lucide-react';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\nexport interface ToastProps {\n  id: string;\n  type: ToastType;\n  title: string;\n  message?: string;\n  duration?: number;\n  onClose: (id: string) => void;\n}\n\nconst Toast: React.FC<ToastProps> = ({\n  id,\n  type,\n  title,\n  message,\n  duration = 4000,\n  onClose\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isExiting, setIsExiting] = useState(false);\n\n  useEffect(() => {\n    // Trigger entrance animation\n    const timer = setTimeout(() => setIsVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration]);\n\n  const handleClose = () => {\n    setIsExiting(true);\n    setTimeout(() => {\n      onClose(id);\n    }, 300); // Match exit animation duration\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle size={20} color=\"#22c55e\" />;\n      case 'error':\n        return <AlertCircle size={20} color=\"#ef4444\" />;\n      case 'warning':\n        return <AlertCircle size={20} color=\"#f59e0b\" />;\n      case 'info':\n      default:\n        return <Info size={20} color=\"#3b82f6\" />;\n    }\n  };\n\n  const getBackgroundColor = () => {\n    switch (type) {\n      case 'success':\n        return '#f0fdf4';\n      case 'error':\n        return '#fef2f2';\n      case 'warning':\n        return '#fffbeb';\n      case 'info':\n      default:\n        return '#eff6ff';\n    }\n  };\n\n  const getBorderColor = () => {\n    switch (type) {\n      case 'success':\n        return '#bbf7d0';\n      case 'error':\n        return '#fecaca';\n      case 'warning':\n        return '#fed7aa';\n      case 'info':\n      default:\n        return '#bfdbfe';\n    }\n  };\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: '1rem',\n        right: '1rem',\n        zIndex: 9999,\n        minWidth: '320px',\n        maxWidth: '480px',\n        backgroundColor: getBackgroundColor(),\n        border: `1px solid ${getBorderColor()}`,\n        borderRadius: '8px',\n        padding: '1rem',\n        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n        transform: `translateX(${isVisible && !isExiting ? '0' : '100%'})`,\n        opacity: isVisible && !isExiting ? 1 : 0,\n        transition: 'all 0.3s ease-in-out'\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem'\n      }}>\n        <div style={{ flexShrink: 0, marginTop: '0.125rem' }}>\n          {getIcon()}\n        </div>\n        \n        <div style={{ flex: 1 }}>\n          <div style={{\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            color: '#1f2937',\n            marginBottom: message ? '0.25rem' : 0\n          }}>\n            {title}\n          </div>\n          \n          {message && (\n            <div style={{\n              fontSize: '0.8rem',\n              color: '#6b7280',\n              lineHeight: '1.4'\n            }}>\n              {message}\n            </div>\n          )}\n        </div>\n        \n        <button\n          onClick={handleClose}\n          style={{\n            background: 'none',\n            border: 'none',\n            cursor: 'pointer',\n            padding: '0.25rem',\n            borderRadius: '4px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#6b7280',\n            transition: 'color 0.2s ease'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.color = '#374151';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.color = '#6b7280';\n          }}\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Toast;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,EAAEC,CAAC,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAajE,MAAMC,KAA2B,GAAGA,CAAC;EACnCC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC,QAAQ,GAAG,IAAI;EACfC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd;IACA,MAAMoB,KAAK,GAAGC,UAAU,CAAC,MAAMJ,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACtD,OAAO,MAAMK,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAENpB,SAAS,CAAC,MAAM;IACd,IAAIa,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BE,WAAW,CAAC,CAAC;MACf,CAAC,EAAEV,QAAQ,CAAC;MACZ,OAAO,MAAMS,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACxBJ,YAAY,CAAC,IAAI,CAAC;IAClBE,UAAU,CAAC,MAAM;MACfP,OAAO,CAACL,EAAE,CAAC;IACb,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;EAED,MAAMe,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQd,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOH,OAAA,CAACL,WAAW;UAACuB,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,OAAO;QACV,oBAAOvB,OAAA,CAACJ,WAAW;UAACsB,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,SAAS;QACZ,oBAAOvB,OAAA,CAACJ,WAAW;UAACsB,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,MAAM;MACX;QACE,oBAAOvB,OAAA,CAACF,IAAI;UAACoB,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQrB,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;MACX;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQtB,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;MACX;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEH,OAAA;IACE0B,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,eAAe,EAAET,kBAAkB,CAAC,CAAC;MACrCU,MAAM,EAAE,aAAaT,cAAc,CAAC,CAAC,EAAE;MACvCU,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,gCAAgC;MAC3CC,SAAS,EAAE,cAAc7B,SAAS,IAAI,CAACE,SAAS,GAAG,GAAG,GAAG,MAAM,GAAG;MAClE4B,OAAO,EAAE9B,SAAS,IAAI,CAACE,SAAS,GAAG,CAAC,GAAG,CAAC;MACxC6B,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eAEFzC,OAAA;MAAK0B,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE;MACP,CAAE;MAAAH,QAAA,gBACAzC,OAAA;QAAK0B,KAAK,EAAE;UAAEmB,UAAU,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAW,CAAE;QAAAL,QAAA,EAClDxB,OAAO,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAENvB,OAAA;QAAK0B,KAAK,EAAE;UAAEqB,IAAI,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACtBzC,OAAA;UAAK0B,KAAK,EAAE;YACVsB,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjB9B,KAAK,EAAE,SAAS;YAChB+B,YAAY,EAAE7C,OAAO,GAAG,SAAS,GAAG;UACtC,CAAE;UAAAoC,QAAA,EACCrC;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELlB,OAAO,iBACNL,OAAA;UAAK0B,KAAK,EAAE;YACVsB,QAAQ,EAAE,QAAQ;YAClB7B,KAAK,EAAE,SAAS;YAChBgC,UAAU,EAAE;UACd,CAAE;UAAAV,QAAA,EACCpC;QAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENvB,OAAA;QACEoD,OAAO,EAAEpC,WAAY;QACrBU,KAAK,EAAE;UACL2B,UAAU,EAAE,MAAM;UAClBnB,MAAM,EAAE,MAAM;UACdoB,MAAM,EAAE,SAAS;UACjBlB,OAAO,EAAE,SAAS;UAClBD,YAAY,EAAE,KAAK;UACnBO,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBY,cAAc,EAAE,QAAQ;UACxBpC,KAAK,EAAE,SAAS;UAChBqB,UAAU,EAAE;QACd,CAAE;QACFgB,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACP,KAAK,GAAG,SAAS;QACzC,CAAE;QACFwC,YAAY,EAAGF,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACP,KAAK,GAAG,SAAS;QACzC,CAAE;QAAAsB,QAAA,eAEFzC,OAAA,CAACH,CAAC;UAACqB,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CAtJIP,KAA2B;AAAA2D,EAAA,GAA3B3D,KAA2B;AAwJjC,eAAeA,KAAK;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}