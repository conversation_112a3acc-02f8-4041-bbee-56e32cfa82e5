{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12H3a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h13\",\n  key: \"1gdiyg\"\n}], [\"path\", {\n  d: \"M18 8c0-2.5-2-2.5-2-5\",\n  key: \"1il607\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M21 12a1 1 0 0 1 1 1v2a1 1 0 0 1-.5.866\",\n  key: \"166zjj\"\n}], [\"path\", {\n  d: \"M22 8c0-2.5-2-2.5-2-5\",\n  key: \"1gah44\"\n}], [\"path\", {\n  d: \"M7 12v4\",\n  key: \"jqww69\"\n}]];\nconst CigaretteOff = createLucideIcon(\"cigarette-off\", __iconNode);\nexport { __iconNode, CigaretteOff as default };\n//# sourceMappingURL=cigarette-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}