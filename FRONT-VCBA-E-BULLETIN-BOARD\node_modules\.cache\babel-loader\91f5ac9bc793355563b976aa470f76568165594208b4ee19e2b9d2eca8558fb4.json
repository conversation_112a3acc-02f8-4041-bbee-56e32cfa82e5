{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526\",\n  key: \"1yiouv\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"6\",\n  key: \"1vp47v\"\n}]];\nconst Award = createLucideIcon(\"award\", __iconNode);\nexport { __iconNode, Award as default };\n//# sourceMappingURL=award.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}