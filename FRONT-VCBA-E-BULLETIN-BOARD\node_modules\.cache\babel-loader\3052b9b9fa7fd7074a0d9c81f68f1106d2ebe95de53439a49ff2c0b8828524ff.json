{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\MultipleImageUpload.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\nimport { Upload, X, AlertCircle, CheckCircle, RotateCcw } from 'lucide-react';\nimport { validateFile, formatFileSize } from '../../utils/formUtils';\nimport { getImageUrl } from '../../config/constants';\n\n// Reusable delete button component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeleteButton = ({\n  onClick,\n  isMarkedForDeletion = false,\n  title\n}) => /*#__PURE__*/_jsxDEV(\"button\", {\n  type: \"button\",\n  onClick: onClick,\n  style: {\n    padding: '0.25rem',\n    backgroundColor: isMarkedForDeletion ? 'rgba(34, 197, 94, 0.9)' // Green for undo\n    : 'rgba(239, 68, 68, 0.9)',\n    // Red for delete\n    color: 'white',\n    border: 'none',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  title: title || (isMarkedForDeletion ? \"Undo deletion\" : \"Delete\"),\n  children: isMarkedForDeletion ? /*#__PURE__*/_jsxDEV(RotateCcw, {\n    size: 14\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 28\n  }, this) : /*#__PURE__*/_jsxDEV(X, {\n    size: 14\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 54\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this);\n\n// Custom hook for CORS-safe image loading\n_c = DeleteButton;\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      setLoading(false);\n      setError(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Loading image via CORS-safe method:', fullUrl);\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        console.log('✅ Image loaded successfully via CORS-safe method');\n        setImageUrl(objectUrl);\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst SafeImage = ({\n  imagePath,\n  alt,\n  style,\n  className\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Image loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: () => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s2(SafeImage, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c2 = SafeImage;\nconst MultipleImageUpload = ({\n  onImagesChange,\n  existingImages = [],\n  onExistingImageDelete,\n  onSetPrimary,\n  maxImages = 10,\n  maxFileSize = 5 * 1024 * 1024,\n  // 5MB\n  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  pendingDeletes = [],\n  onMarkForDeletion,\n  onUnmarkForDeletion\n}) => {\n  _s3();\n  const [images, setImages] = useState([]);\n  const [dragActive, setDragActive] = useState(false);\n  const [error, setError] = useState(null);\n  const fileInputRef = useRef(null);\n  const dragCounter = useRef(0);\n\n  // Debug existing images\n  useEffect(() => {\n    console.log('🖼️ MultipleImageUpload - Existing images received:', {\n      count: existingImages.length,\n      images: existingImages.map(img => ({\n        id: img.attachment_id,\n        name: img.file_name\n      }))\n    });\n  }, [existingImages]);\n\n  // Calculate total images (existing + new)\n  const totalImages = existingImages.length + images.length;\n  const canAddMore = totalImages < maxImages && !disabled;\n\n  // Validate file using utility function\n  const validateFileLocal = useCallback(file => {\n    return validateFile(file, {\n      maxSize: maxFileSize,\n      allowedTypes: acceptedTypes\n    });\n  }, [acceptedTypes, maxFileSize]);\n\n  // Process files\n  const processFiles = useCallback(fileList => {\n    const files = Array.from(fileList);\n    const newImages = [];\n    let hasErrors = false;\n\n    // Check if adding these files would exceed the limit\n    if (totalImages + files.length > maxImages) {\n      setError(`Cannot add ${files.length} images. Maximum ${maxImages} images allowed (${totalImages} already selected).`);\n      return;\n    }\n    files.forEach((file, index) => {\n      const validationError = validateFileLocal(file);\n      if (validationError) {\n        hasErrors = true;\n        setError(validationError);\n        return;\n      }\n      const imageFile = {\n        id: `${Date.now()}-${index}`,\n        file,\n        preview: URL.createObjectURL(file),\n        status: 'pending',\n        progress: 0\n      };\n      newImages.push(imageFile);\n    });\n    if (!hasErrors && newImages.length > 0) {\n      setError(null);\n      setImages(prev => [...prev, ...newImages]);\n\n      // Notify parent component\n      const allFiles = [...images.map(img => img.file), ...newImages.map(img => img.file)];\n      onImagesChange(allFiles);\n    }\n  }, [images, totalImages, maxImages, validateFileLocal, onImagesChange]);\n\n  // Handle file input change\n  const handleFileSelect = useCallback(e => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, [processFiles]);\n\n  // Handle drag events\n  const handleDrag = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n  const handleDragIn = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setDragActive(true);\n    }\n  }, []);\n  const handleDragOut = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setDragActive(false);\n    }\n  }, []);\n  const handleDrop = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    dragCounter.current = 0;\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n      processFiles(e.dataTransfer.files);\n    }\n  }, [processFiles]);\n\n  // Remove image\n  const removeImage = useCallback(imageId => {\n    setImages(prev => {\n      const updated = prev.filter(img => img.id !== imageId);\n      // Clean up object URL\n      const removedImage = prev.find(img => img.id === imageId);\n      if (removedImage) {\n        URL.revokeObjectURL(removedImage.preview);\n      }\n\n      // Notify parent component\n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [onImagesChange]);\n\n  // Toggle existing image for deletion\n  const toggleExistingImageDeletion = useCallback(attachmentId => {\n    const isMarkedForDeletion = pendingDeletes.includes(attachmentId);\n    if (isMarkedForDeletion) {\n      onUnmarkForDeletion === null || onUnmarkForDeletion === void 0 ? void 0 : onUnmarkForDeletion(attachmentId);\n    } else {\n      onMarkForDeletion === null || onMarkForDeletion === void 0 ? void 0 : onMarkForDeletion(attachmentId);\n    }\n  }, [pendingDeletes, onMarkForDeletion, onUnmarkForDeletion]);\n\n  // Remove existing image (fallback to pending system)\n  const removeExistingImage = useCallback(attachmentId => {\n    toggleExistingImageDeletion(attachmentId);\n  }, [toggleExistingImageDeletion]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback(attachmentId => {\n    if (onSetPrimary) {\n      onSetPrimary(attachmentId);\n    }\n  }, [onSetPrimary]);\n\n  // Cleanup object URLs on unmount\n  useEffect(() => {\n    return () => {\n      images.forEach(image => {\n        URL.revokeObjectURL(image.preview);\n      });\n    };\n  }, []);\n\n  // Clear error after 5 seconds\n  useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => setError(null), 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [error]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `multiple-image-upload ${className}`,\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        padding: '0.75rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        fontSize: '0.875rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 9\n    }, this), canAddMore && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        border: `2px dashed ${dragActive ? '#22c55e' : '#d1d5db'}`,\n        borderRadius: '12px',\n        padding: '2rem',\n        textAlign: 'center',\n        backgroundColor: dragActive ? '#f0fdf4' : '#fafafa',\n        cursor: disabled ? 'not-allowed' : 'pointer',\n        transition: 'all 0.2s ease',\n        marginBottom: '1.5rem'\n      },\n      onClick: () => {\n        var _fileInputRef$current;\n        return !disabled && ((_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click());\n      },\n      onDragEnter: handleDragIn,\n      onDragLeave: handleDragOut,\n      onDragOver: handleDrag,\n      onDrop: handleDrop,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ref: fileInputRef,\n        type: \"file\",\n        multiple: true,\n        accept: acceptedTypes.join(','),\n        onChange: handleFileSelect,\n        style: {\n          display: 'none'\n        },\n        disabled: disabled\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Upload, {\n        size: 48,\n        style: {\n          color: dragActive ? '#22c55e' : '#9ca3af',\n          marginBottom: '1rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151',\n          marginBottom: '0.5rem'\n        },\n        children: dragActive ? 'Drop images here' : 'Upload Images'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280',\n          marginBottom: '0.5rem'\n        },\n        children: \"Drag and drop images here, or click to select files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: '0.75rem',\n          color: '#9ca3af'\n        },\n        children: [\"Maximum \", maxImages, \" images \\u2022 \", formatFileSize(maxFileSize), \" per file\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), totalImages, \"/\", maxImages, \" images selected\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 9\n    }, this), (existingImages.length > 0 || images.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',\n        gap: '1rem',\n        marginTop: '1rem'\n      },\n      children: [existingImages.map(image => {\n        const isMarkedForDeletion = pendingDeletes.includes(image.attachment_id);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            aspectRatio: '1',\n            borderRadius: '8px',\n            overflow: 'hidden',\n            border: image.is_primary ? '3px solid #22c55e' : '1px solid #e5e7eb',\n            backgroundColor: '#f9fafb',\n            opacity: isMarkedForDeletion ? 0.5 : 1,\n            filter: isMarkedForDeletion ? 'grayscale(100%)' : 'none',\n            transition: 'opacity 0.2s ease, filter 0.2s ease'\n          },\n          children: [isMarkedForDeletion && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              inset: 0,\n              backgroundColor: 'rgba(239, 68, 68, 0.3)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: 'rgba(239, 68, 68, 0.9)',\n                color: 'white',\n                padding: '0.5rem 1rem',\n                borderRadius: '4px',\n                fontSize: '0.875rem',\n                fontWeight: '600'\n              },\n              children: \"Will be deleted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(SafeImage, {\n            imagePath: image.file_path,\n            alt: image.file_name,\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '0.5rem',\n              right: '0.5rem',\n              display: 'flex',\n              gap: '0.25rem'\n            },\n            children: [!image.is_primary && onSetPrimary && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setPrimaryImage(image.attachment_id),\n              style: {\n                padding: '0.25rem',\n                backgroundColor: 'rgba(0, 0, 0, 0.7)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"Set as primary\",\n              children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 19\n            }, this), (() => {\n              const hasPendingProps = onMarkForDeletion && onUnmarkForDeletion;\n              const hasImmediateProps = onExistingImageDelete;\n              console.log('🔍 Button render for image', image.attachment_id, {\n                hasPendingProps,\n                hasImmediateProps,\n                isMarkedForDeletion\n              });\n              if (hasPendingProps) {\n                return /*#__PURE__*/_jsxDEV(DeleteButton, {\n                  onClick: () => toggleExistingImageDeletion(image.attachment_id),\n                  isMarkedForDeletion: isMarkedForDeletion,\n                  title: isMarkedForDeletion ? \"Undo deletion\" : \"Mark for deletion\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this);\n              } else if (hasImmediateProps) {\n                return /*#__PURE__*/_jsxDEV(DeleteButton, {\n                  onClick: () => removeExistingImage(image.attachment_id),\n                  title: \"Remove image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 23\n                }, this);\n              }\n              return null;\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)]\n        }, `existing-${image.attachment_id}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 15\n        }, this);\n      }), images.map(image => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          aspectRatio: '1',\n          borderRadius: '8px',\n          overflow: 'hidden',\n          border: '1px solid #e5e7eb',\n          backgroundColor: '#f9fafb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.preview,\n          alt: image.file.name,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            opacity: image.status === 'uploading' ? 0.7 : 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 15\n        }, this), image.status === 'uploading' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            inset: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontSize: '0.875rem'\n          },\n          children: \"Uploading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '0.5rem',\n            right: '0.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(DeleteButton, {\n            onClick: () => removeImage(image.id),\n            title: \"Remove image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 15\n        }, this)]\n      }, image.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 386,\n    columnNumber: 5\n  }, this);\n};\n_s3(MultipleImageUpload, \"A+bLm93qReCm5Mwf3dq9jsRIrgM=\");\n_c3 = MultipleImageUpload;\nexport default MultipleImageUpload;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DeleteButton\");\n$RefreshReg$(_c2, \"SafeImage\");\n$RefreshReg$(_c3, \"MultipleImageUpload\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useEffect", "Upload", "X", "AlertCircle", "CheckCircle", "RotateCcw", "validateFile", "formatFileSize", "getImageUrl", "jsxDEV", "_jsxDEV", "DeleteButton", "onClick", "isMarkedForDeletion", "title", "type", "style", "padding", "backgroundColor", "color", "border", "borderRadius", "cursor", "display", "alignItems", "justifyContent", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "SafeImage", "alt", "className", "_s2", "textAlign", "marginBottom", "fontSize", "src", "onLoad", "_c2", "MultipleImageUpload", "onImagesChange", "existingImages", "onExistingImageDelete", "onSetPrimary", "maxImages", "maxFileSize", "acceptedTypes", "disabled", "pendingDeletes", "onMarkForDeletion", "onUnmarkForDeletion", "_s3", "images", "setImages", "dragActive", "setDragActive", "fileInputRef", "dragCounter", "count", "length", "map", "img", "id", "attachment_id", "name", "file_name", "totalImages", "canAddMore", "validateFileLocal", "file", "maxSize", "allowedTypes", "processFiles", "fileList", "files", "Array", "from", "newImages", "hasErrors", "for<PERSON>ach", "index", "validationError", "imageFile", "Date", "now", "preview", "progress", "push", "prev", "allFiles", "handleFileSelect", "e", "target", "current", "value", "handleDrag", "preventDefault", "stopPropagation", "handleDragIn", "dataTransfer", "items", "handleDragOut", "handleDrop", "removeImage", "imageId", "updated", "filter", "removedImage", "find", "toggleExistingImageDeletion", "attachmentId", "includes", "removeExistingImage", "setPrimaryImage", "image", "timer", "setTimeout", "clearTimeout", "gap", "position", "transition", "_fileInputRef$current", "click", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "ref", "multiple", "accept", "join", "onChange", "fontWeight", "gridTemplateColumns", "marginTop", "aspectRatio", "overflow", "is_primary", "opacity", "inset", "zIndex", "file_path", "width", "height", "objectFit", "top", "right", "hasPendingProps", "hasImmediateProps", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/MultipleImageUpload.tsx"], "sourcesContent": ["import React, { useState, useCallback, useRef, useEffect } from 'react';\nimport { Upload, X, AlertCircle, CheckCircle, RotateCcw } from 'lucide-react';\nimport { validateFile, formatFileSize } from '../../utils/formUtils';\nimport { getImageUrl } from '../../config/constants';\n\n// Reusable delete button component\ninterface DeleteButtonProps {\n  onClick: () => void;\n  isMarkedForDeletion?: boolean;\n  title?: string;\n}\n\nconst DeleteButton: React.FC<DeleteButtonProps> = ({\n  onClick,\n  isMarkedForDeletion = false,\n  title\n}) => (\n  <button\n    type=\"button\"\n    onClick={onClick}\n    style={{\n      padding: '0.25rem',\n      backgroundColor: isMarkedForDeletion\n        ? 'rgba(34, 197, 94, 0.9)' // Green for undo\n        : 'rgba(239, 68, 68, 0.9)', // Red for delete\n      color: 'white',\n      border: 'none',\n      borderRadius: '4px',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}\n    title={title || (isMarkedForDeletion ? \"Undo deletion\" : \"Delete\")}\n  >\n    {isMarkedForDeletion ? <RotateCcw size={14} /> : <X size={14} />}\n  </button>\n);\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      setLoading(false);\n      setError(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Loading image via CORS-safe method:', fullUrl);\n\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n\n        console.log('✅ Image loaded successfully via CORS-safe method');\n        setImageUrl(objectUrl);\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface SafeImageProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n}\n\nconst SafeImage: React.FC<SafeImageProps> = ({ imagePath, alt, style, className }) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Image loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={() => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n      }}\n    />\n  );\n};\n\ninterface ImageFile {\n  id: string;\n  file: File;\n  preview: string;\n  status: 'uploading' | 'success' | 'error' | 'pending';\n  progress: number;\n  error?: string;\n}\n\ninterface ExistingImage {\n  attachment_id: number;\n  file_name: string;\n  file_path: string;\n  file_url: string;\n  display_order: number;\n  is_primary: boolean;\n}\n\ninterface MultipleImageUploadProps {\n  onImagesChange: (files: File[]) => void;\n  existingImages?: ExistingImage[];\n  onExistingImageDelete?: (attachmentId: number) => void;\n\n  onSetPrimary?: (attachmentId: number) => void;\n  maxImages?: number;\n  maxFileSize?: number; // in bytes\n  acceptedTypes?: string[];\n  className?: string;\n  disabled?: boolean;\n  // New props for pending deletion\n  pendingDeletes?: number[];\n  onMarkForDeletion?: (attachmentId: number) => void;\n  onUnmarkForDeletion?: (attachmentId: number) => void;\n}\n\nconst MultipleImageUpload: React.FC<MultipleImageUploadProps> = ({\n  onImagesChange,\n  existingImages = [],\n  onExistingImageDelete,\n  onSetPrimary,\n  maxImages = 10,\n  maxFileSize = 5 * 1024 * 1024, // 5MB\n  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  pendingDeletes = [],\n  onMarkForDeletion,\n  onUnmarkForDeletion\n}) => {\n  const [images, setImages] = useState<ImageFile[]>([]);\n  const [dragActive, setDragActive] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dragCounter = useRef(0);\n\n  // Debug existing images\n  useEffect(() => {\n    console.log('🖼️ MultipleImageUpload - Existing images received:', {\n      count: existingImages.length,\n      images: existingImages.map(img => ({ id: img.attachment_id, name: img.file_name }))\n    });\n  }, [existingImages]);\n\n  // Calculate total images (existing + new)\n  const totalImages = existingImages.length + images.length;\n  const canAddMore = totalImages < maxImages && !disabled;\n\n  // Validate file using utility function\n  const validateFileLocal = useCallback((file: File): string | null => {\n    return validateFile(file, {\n      maxSize: maxFileSize,\n      allowedTypes: acceptedTypes\n    });\n  }, [acceptedTypes, maxFileSize]);\n\n  // Process files\n  const processFiles = useCallback((fileList: FileList | File[]) => {\n    const files = Array.from(fileList);\n    const newImages: ImageFile[] = [];\n    let hasErrors = false;\n\n    // Check if adding these files would exceed the limit\n    if (totalImages + files.length > maxImages) {\n      setError(`Cannot add ${files.length} images. Maximum ${maxImages} images allowed (${totalImages} already selected).`);\n      return;\n    }\n\n    files.forEach((file, index) => {\n      const validationError = validateFileLocal(file);\n      \n      if (validationError) {\n        hasErrors = true;\n        setError(validationError);\n        return;\n      }\n\n      const imageFile: ImageFile = {\n        id: `${Date.now()}-${index}`,\n        file,\n        preview: URL.createObjectURL(file),\n        status: 'pending',\n        progress: 0\n      };\n\n      newImages.push(imageFile);\n    });\n\n    if (!hasErrors && newImages.length > 0) {\n      setError(null);\n      setImages(prev => [...prev, ...newImages]);\n      \n      // Notify parent component\n      const allFiles = [...images.map(img => img.file), ...newImages.map(img => img.file)];\n      onImagesChange(allFiles);\n    }\n  }, [images, totalImages, maxImages, validateFileLocal, onImagesChange]);\n\n  // Handle file input change\n  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, [processFiles]);\n\n  // Handle drag events\n  const handleDrag = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDragIn = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setDragActive(true);\n    }\n  }, []);\n\n  const handleDragOut = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    dragCounter.current = 0;\n\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n      processFiles(e.dataTransfer.files);\n    }\n  }, [processFiles]);\n\n  // Remove image\n  const removeImage = useCallback((imageId: string) => {\n    setImages(prev => {\n      const updated = prev.filter(img => img.id !== imageId);\n      // Clean up object URL\n      const removedImage = prev.find(img => img.id === imageId);\n      if (removedImage) {\n        URL.revokeObjectURL(removedImage.preview);\n      }\n      \n      // Notify parent component\n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [onImagesChange]);\n\n  // Toggle existing image for deletion\n  const toggleExistingImageDeletion = useCallback((attachmentId: number) => {\n    const isMarkedForDeletion = pendingDeletes.includes(attachmentId);\n\n    if (isMarkedForDeletion) {\n      onUnmarkForDeletion?.(attachmentId);\n    } else {\n      onMarkForDeletion?.(attachmentId);\n    }\n  }, [pendingDeletes, onMarkForDeletion, onUnmarkForDeletion]);\n\n  // Remove existing image (fallback to pending system)\n  const removeExistingImage = useCallback((attachmentId: number) => {\n    toggleExistingImageDeletion(attachmentId);\n  }, [toggleExistingImageDeletion]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback((attachmentId: number) => {\n    if (onSetPrimary) {\n      onSetPrimary(attachmentId);\n    }\n  }, [onSetPrimary]);\n\n  // Cleanup object URLs on unmount\n  useEffect(() => {\n    return () => {\n      images.forEach(image => {\n        URL.revokeObjectURL(image.preview);\n      });\n    };\n  }, []);\n\n  // Clear error after 5 seconds\n  useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => setError(null), 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [error]);\n\n  return (\n    <div className={`multiple-image-upload ${className}`}>\n      {/* Error Display */}\n      {error && (\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          fontSize: '0.875rem',\n          marginBottom: '1rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {/* Upload Area */}\n      {canAddMore && (\n        <div\n          style={{\n            position: 'relative',\n            border: `2px dashed ${dragActive ? '#22c55e' : '#d1d5db'}`,\n            borderRadius: '12px',\n            padding: '2rem',\n            textAlign: 'center',\n            backgroundColor: dragActive ? '#f0fdf4' : '#fafafa',\n            cursor: disabled ? 'not-allowed' : 'pointer',\n            transition: 'all 0.2s ease',\n            marginBottom: '1.5rem'\n          }}\n          onClick={() => !disabled && fileInputRef.current?.click()}\n          onDragEnter={handleDragIn}\n          onDragLeave={handleDragOut}\n          onDragOver={handleDrag}\n          onDrop={handleDrop}\n        >\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            multiple\n            accept={acceptedTypes.join(',')}\n            onChange={handleFileSelect}\n            style={{ display: 'none' }}\n            disabled={disabled}\n          />\n          \n          <Upload \n            size={48} \n            style={{ \n              color: dragActive ? '#22c55e' : '#9ca3af',\n              marginBottom: '1rem'\n            }} \n          />\n          \n          <h3 style={{\n            fontSize: '1.125rem',\n            fontWeight: '600',\n            color: '#374151',\n            marginBottom: '0.5rem'\n          }}>\n            {dragActive ? 'Drop images here' : 'Upload Images'}\n          </h3>\n          \n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            marginBottom: '0.5rem'\n          }}>\n            Drag and drop images here, or click to select files\n          </p>\n          \n          <p style={{\n            fontSize: '0.75rem',\n            color: '#9ca3af'\n          }}>\n            Maximum {maxImages} images • {formatFileSize(maxFileSize)} per file\n            <br />\n            {totalImages}/{maxImages} images selected\n          </p>\n        </div>\n      )}\n\n      {/* Image Grid */}\n      {(existingImages.length > 0 || images.length > 0) && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',\n          gap: '1rem',\n          marginTop: '1rem'\n        }}>\n          {/* Existing Images */}\n          {existingImages.map((image) => {\n            const isMarkedForDeletion = pendingDeletes.includes(image.attachment_id);\n\n            return (\n              <div\n                key={`existing-${image.attachment_id}`}\n                style={{\n                  position: 'relative',\n                  aspectRatio: '1',\n                  borderRadius: '8px',\n                  overflow: 'hidden',\n                  border: image.is_primary ? '3px solid #22c55e' : '1px solid #e5e7eb',\n                  backgroundColor: '#f9fafb',\n                  opacity: isMarkedForDeletion ? 0.5 : 1,\n                  filter: isMarkedForDeletion ? 'grayscale(100%)' : 'none',\n                  transition: 'opacity 0.2s ease, filter 0.2s ease'\n                }}\n              >\n                {/* Deletion overlay */}\n                {isMarkedForDeletion && (\n                  <div style={{\n                    position: 'absolute',\n                    inset: 0,\n                    backgroundColor: 'rgba(239, 68, 68, 0.3)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1\n                  }}>\n                    <div style={{\n                      backgroundColor: 'rgba(239, 68, 68, 0.9)',\n                      color: 'white',\n                      padding: '0.5rem 1rem',\n                      borderRadius: '4px',\n                      fontSize: '0.875rem',\n                      fontWeight: '600'\n                    }}>\n                      Will be deleted\n                    </div>\n                  </div>\n                )}\n              <SafeImage\n                imagePath={image.file_path}\n                alt={image.file_name}\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n              />\n              \n\n              \n              {/* Action Buttons */}\n              <div style={{\n                position: 'absolute',\n                top: '0.5rem',\n                right: '0.5rem',\n                display: 'flex',\n                gap: '0.25rem'\n              }}>\n                {!image.is_primary && onSetPrimary && (\n                  <button\n                    onClick={() => setPrimaryImage(image.attachment_id)}\n                    style={{\n                      padding: '0.25rem',\n                      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    }}\n                    title=\"Set as primary\"\n                  >\n                    <CheckCircle size={14} />\n                  </button>\n                )}\n                \n                {/* Delete/Undelete Button */}\n                {(() => {\n                  const hasPendingProps = onMarkForDeletion && onUnmarkForDeletion;\n                  const hasImmediateProps = onExistingImageDelete;\n\n                  console.log('🔍 Button render for image', image.attachment_id, {\n                    hasPendingProps,\n                    hasImmediateProps,\n                    isMarkedForDeletion\n                  });\n\n                  if (hasPendingProps) {\n                    return (\n                      <DeleteButton\n                        onClick={() => toggleExistingImageDeletion(image.attachment_id)}\n                        isMarkedForDeletion={isMarkedForDeletion}\n                        title={isMarkedForDeletion ? \"Undo deletion\" : \"Mark for deletion\"}\n                      />\n                    );\n                  } else if (hasImmediateProps) {\n                    return (\n                      <DeleteButton\n                        onClick={() => removeExistingImage(image.attachment_id)}\n                        title=\"Remove image\"\n                      />\n                    );\n                  }\n                  return null;\n                })()}\n              </div>\n            </div>\n          );\n          })}\n\n          {/* New Images */}\n          {images.map((image) => (\n            <div\n              key={image.id}\n              style={{\n                position: 'relative',\n                aspectRatio: '1',\n                borderRadius: '8px',\n                overflow: 'hidden',\n                border: '1px solid #e5e7eb',\n                backgroundColor: '#f9fafb'\n              }}\n            >\n              <img\n                src={image.preview}\n                alt={image.file.name}\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover',\n                  opacity: image.status === 'uploading' ? 0.7 : 1\n                }}\n              />\n              \n              {/* Status Overlay */}\n              {image.status === 'uploading' && (\n                <div style={{\n                  position: 'absolute',\n                  inset: 0,\n                  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontSize: '0.875rem'\n                }}>\n                  Uploading...\n                </div>\n              )}\n              \n              {/* Remove Button */}\n              <div style={{\n                position: 'absolute',\n                top: '0.5rem',\n                right: '0.5rem'\n              }}>\n                <DeleteButton\n                  onClick={() => removeImage(image.id)}\n                  title=\"Remove image\"\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MultipleImageUpload;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,cAAc;AAC7E,SAASC,YAAY,EAAEC,cAAc,QAAQ,uBAAuB;AACpE,SAASC,WAAW,QAAQ,wBAAwB;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,OAAO;EACPC,mBAAmB,GAAG,KAAK;EAC3BC;AACF,CAAC,kBACCJ,OAAA;EACEK,IAAI,EAAC,QAAQ;EACbH,OAAO,EAAEA,OAAQ;EACjBI,KAAK,EAAE;IACLC,OAAO,EAAE,SAAS;IAClBC,eAAe,EAAEL,mBAAmB,GAChC,wBAAwB,CAAC;IAAA,EACzB,wBAAwB;IAAE;IAC9BM,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAE;EACFX,KAAK,EAAEA,KAAK,KAAKD,mBAAmB,GAAG,eAAe,GAAG,QAAQ,CAAE;EAAAa,QAAA,EAElEb,mBAAmB,gBAAGH,OAAA,CAACL,SAAS;IAACsB,IAAI,EAAE;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGrB,OAAA,CAACR,CAAC;IAACyB,IAAI,EAAE;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1D,CACT;;AAED;AAAAC,EAAA,GA3BMrB,YAAyC;AA4B/C,MAAMsB,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EAEvDG,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjBE,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,IAAI,CAAC;MACd;IACF;IAEA,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGnC,WAAW,CAAC0B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEH,OAAO,CAAC;QAE9D,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAE3Cb,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/DT,WAAW,CAACsB,SAAS,CAAC;MAExB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEsB,GAAG,CAAC;QAC3CrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CAhEMF,cAAc;AAwEpB,MAAMiC,SAAmC,GAAGA,CAAC;EAAEhC,SAAS;EAAEiC,GAAG;EAAEnD,KAAK;EAAEoD;AAAU,CAAC,KAAK;EAAAC,GAAA;EACpF,MAAM;IAAEjC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACE5B,OAAA;MAAKM,KAAK,EAAE;QACV,GAAGA,KAAK;QACRO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBP,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACiD,SAAS,EAAEA,SAAU;MAAA1C,QAAA,eACtBhB,OAAA;QAAKM,KAAK,EAAE;UAAEsD,SAAS,EAAE;QAAS,CAAE;QAAA5C,QAAA,gBAClChB,OAAA;UAAKM,KAAK,EAAE;YAAEuD,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAA9C,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnErB,OAAA;UAAKM,KAAK,EAAE;YAAEwD,QAAQ,EAAE;UAAW,CAAE;UAAA9C,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIS,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACE1B,OAAA;MAAKM,KAAK,EAAE;QACV,GAAGA,KAAK;QACRO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBP,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACiD,SAAS,EAAEA,SAAU;MAAA1C,QAAA,eACtBhB,OAAA;QAAKM,KAAK,EAAE;UAAEsD,SAAS,EAAE;QAAS,CAAE;QAAA5C,QAAA,gBAClChB,OAAA;UAAKM,KAAK,EAAE;YAAEuD,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAA9C,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnErB,OAAA;UAAKM,KAAK,EAAE;YAAEwD,QAAQ,EAAE;UAAW,CAAE;UAAA9C,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErB,OAAA;IACE+D,GAAG,EAAErC,QAAS;IACd+B,GAAG,EAAEA,GAAI;IACTnD,KAAK,EAAEA,KAAM;IACboD,SAAS,EAAEA,SAAU;IACrBM,MAAM,EAAEA,CAAA,KAAM;MACZ7B,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACnE;EAAE;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAACsC,GAAA,CAlDIH,SAAmC;EAAA,QACFjC,cAAc;AAAA;AAAA0C,GAAA,GAD/CT,SAAmC;AAuFzC,MAAMU,mBAAuD,GAAGA,CAAC;EAC/DC,cAAc;EACdC,cAAc,GAAG,EAAE;EACnBC,qBAAqB;EACrBC,YAAY;EACZC,SAAS,GAAG,EAAE;EACdC,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EAC/BC,aAAa,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACtEf,SAAS,GAAG,EAAE;EACdgB,QAAQ,GAAG,KAAK;EAChBC,cAAc,GAAG,EAAE;EACnBC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7F,QAAQ,CAAc,EAAE,CAAC;EACrD,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMgG,YAAY,GAAG9F,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAM+F,WAAW,GAAG/F,MAAM,CAAC,CAAC,CAAC;;EAE7B;EACAC,SAAS,CAAC,MAAM;IACd6C,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;MACjEiD,KAAK,EAAEjB,cAAc,CAACkB,MAAM;MAC5BP,MAAM,EAAEX,cAAc,CAACmB,GAAG,CAACC,GAAG,KAAK;QAAEC,EAAE,EAAED,GAAG,CAACE,aAAa;QAAEC,IAAI,EAAEH,GAAG,CAACI;MAAU,CAAC,CAAC;IACpF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMyB,WAAW,GAAGzB,cAAc,CAACkB,MAAM,GAAGP,MAAM,CAACO,MAAM;EACzD,MAAMQ,UAAU,GAAGD,WAAW,GAAGtB,SAAS,IAAI,CAACG,QAAQ;;EAEvD;EACA,MAAMqB,iBAAiB,GAAG3G,WAAW,CAAE4G,IAAU,IAAoB;IACnE,OAAOpG,YAAY,CAACoG,IAAI,EAAE;MACxBC,OAAO,EAAEzB,WAAW;MACpB0B,YAAY,EAAEzB;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,aAAa,EAAED,WAAW,CAAC,CAAC;;EAEhC;EACA,MAAM2B,YAAY,GAAG/G,WAAW,CAAEgH,QAA2B,IAAK;IAChE,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC;IAClC,MAAMI,SAAsB,GAAG,EAAE;IACjC,IAAIC,SAAS,GAAG,KAAK;;IAErB;IACA,IAAIZ,WAAW,GAAGQ,KAAK,CAACf,MAAM,GAAGf,SAAS,EAAE;MAC1CxC,QAAQ,CAAC,cAAcsE,KAAK,CAACf,MAAM,oBAAoBf,SAAS,oBAAoBsB,WAAW,qBAAqB,CAAC;MACrH;IACF;IAEAQ,KAAK,CAACK,OAAO,CAAC,CAACV,IAAI,EAAEW,KAAK,KAAK;MAC7B,MAAMC,eAAe,GAAGb,iBAAiB,CAACC,IAAI,CAAC;MAE/C,IAAIY,eAAe,EAAE;QACnBH,SAAS,GAAG,IAAI;QAChB1E,QAAQ,CAAC6E,eAAe,CAAC;QACzB;MACF;MAEA,MAAMC,SAAoB,GAAG;QAC3BpB,EAAE,EAAE,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIJ,KAAK,EAAE;QAC5BX,IAAI;QACJgB,OAAO,EAAE9D,GAAG,CAACC,eAAe,CAAC6C,IAAI,CAAC;QAClClD,MAAM,EAAE,SAAS;QACjBmE,QAAQ,EAAE;MACZ,CAAC;MAEDT,SAAS,CAACU,IAAI,CAACL,SAAS,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAI,CAACJ,SAAS,IAAID,SAAS,CAAClB,MAAM,GAAG,CAAC,EAAE;MACtCvD,QAAQ,CAAC,IAAI,CAAC;MACdiD,SAAS,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGX,SAAS,CAAC,CAAC;;MAE1C;MACA,MAAMY,QAAQ,GAAG,CAAC,GAAGrC,MAAM,CAACQ,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACQ,IAAI,CAAC,EAAE,GAAGQ,SAAS,CAACjB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACQ,IAAI,CAAC,CAAC;MACpF7B,cAAc,CAACiD,QAAQ,CAAC;IAC1B;EACF,CAAC,EAAE,CAACrC,MAAM,EAAEc,WAAW,EAAEtB,SAAS,EAAEwB,iBAAiB,EAAE5B,cAAc,CAAC,CAAC;;EAEvE;EACA,MAAMkD,gBAAgB,GAAGjI,WAAW,CAAEkI,CAAsC,IAAK;IAC/E,MAAMjB,KAAK,GAAGiB,CAAC,CAACC,MAAM,CAAClB,KAAK;IAC5B,IAAIA,KAAK,IAAIA,KAAK,CAACf,MAAM,GAAG,CAAC,EAAE;MAC7Ba,YAAY,CAACE,KAAK,CAAC;IACrB;IACA;IACA,IAAIlB,YAAY,CAACqC,OAAO,EAAE;MACxBrC,YAAY,CAACqC,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;EACF,CAAC,EAAE,CAACtB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMuB,UAAU,GAAGtI,WAAW,CAAEkI,CAAkB,IAAK;IACrDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACM,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAGzI,WAAW,CAAEkI,CAAkB,IAAK;IACvDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBxC,WAAW,CAACoC,OAAO,EAAE;IACrB,IAAIF,CAAC,CAACQ,YAAY,CAACC,KAAK,IAAIT,CAAC,CAACQ,YAAY,CAACC,KAAK,CAACzC,MAAM,GAAG,CAAC,EAAE;MAC3DJ,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8C,aAAa,GAAG5I,WAAW,CAAEkI,CAAkB,IAAK;IACxDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBxC,WAAW,CAACoC,OAAO,EAAE;IACrB,IAAIpC,WAAW,CAACoC,OAAO,KAAK,CAAC,EAAE;MAC7BtC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+C,UAAU,GAAG7I,WAAW,CAAEkI,CAAkB,IAAK;IACrDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB1C,aAAa,CAAC,KAAK,CAAC;IACpBE,WAAW,CAACoC,OAAO,GAAG,CAAC;IAEvB,IAAIF,CAAC,CAACQ,YAAY,CAACzB,KAAK,IAAIiB,CAAC,CAACQ,YAAY,CAACzB,KAAK,CAACf,MAAM,GAAG,CAAC,EAAE;MAC3Da,YAAY,CAACmB,CAAC,CAACQ,YAAY,CAACzB,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM+B,WAAW,GAAG9I,WAAW,CAAE+I,OAAe,IAAK;IACnDnD,SAAS,CAACmC,IAAI,IAAI;MAChB,MAAMiB,OAAO,GAAGjB,IAAI,CAACkB,MAAM,CAAC7C,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAK0C,OAAO,CAAC;MACtD;MACA,MAAMG,YAAY,GAAGnB,IAAI,CAACoB,IAAI,CAAC/C,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAK0C,OAAO,CAAC;MACzD,IAAIG,YAAY,EAAE;QAChBpF,GAAG,CAACK,eAAe,CAAC+E,YAAY,CAACtB,OAAO,CAAC;MAC3C;;MAEA;MACA7C,cAAc,CAACiE,OAAO,CAAC7C,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACQ,IAAI,CAAC,CAAC;MAC5C,OAAOoC,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjE,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMqE,2BAA2B,GAAGpJ,WAAW,CAAEqJ,YAAoB,IAAK;IACxE,MAAMtI,mBAAmB,GAAGwE,cAAc,CAAC+D,QAAQ,CAACD,YAAY,CAAC;IAEjE,IAAItI,mBAAmB,EAAE;MACvB0E,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAG4D,YAAY,CAAC;IACrC,CAAC,MAAM;MACL7D,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG6D,YAAY,CAAC;IACnC;EACF,CAAC,EAAE,CAAC9D,cAAc,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAM8D,mBAAmB,GAAGvJ,WAAW,CAAEqJ,YAAoB,IAAK;IAChED,2BAA2B,CAACC,YAAY,CAAC;EAC3C,CAAC,EAAE,CAACD,2BAA2B,CAAC,CAAC;;EAEjC;EACA,MAAMI,eAAe,GAAGxJ,WAAW,CAAEqJ,YAAoB,IAAK;IAC5D,IAAInE,YAAY,EAAE;MAChBA,YAAY,CAACmE,YAAY,CAAC;IAC5B;EACF,CAAC,EAAE,CAACnE,YAAY,CAAC,CAAC;;EAElB;EACAhF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXyF,MAAM,CAAC2B,OAAO,CAACmC,KAAK,IAAI;QACtB3F,GAAG,CAACK,eAAe,CAACsF,KAAK,CAAC7B,OAAO,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1H,SAAS,CAAC,MAAM;IACd,IAAIwC,KAAK,EAAE;MACT,MAAMgH,KAAK,GAAGC,UAAU,CAAC,MAAMhH,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MACpD,OAAO,MAAMiH,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAChH,KAAK,CAAC,CAAC;EAEX,oBACE9B,OAAA;IAAK0D,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAAA1C,QAAA,GAElDc,KAAK,iBACJ9B,OAAA;MAAKM,KAAK,EAAE;QACVO,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBmI,GAAG,EAAE,QAAQ;QACb1I,OAAO,EAAE,SAAS;QAClBC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,KAAK,EAAE,SAAS;QAChBqD,QAAQ,EAAE,UAAU;QACpBD,YAAY,EAAE;MAChB,CAAE;MAAA7C,QAAA,gBACAhB,OAAA,CAACP,WAAW;QAACwB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBS,KAAK;IAAA;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAyE,UAAU,iBACT9F,OAAA;MACEM,KAAK,EAAE;QACL4I,QAAQ,EAAE,UAAU;QACpBxI,MAAM,EAAE,cAAcuE,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;QAC1DtE,YAAY,EAAE,MAAM;QACpBJ,OAAO,EAAE,MAAM;QACfqD,SAAS,EAAE,QAAQ;QACnBpD,eAAe,EAAEyE,UAAU,GAAG,SAAS,GAAG,SAAS;QACnDrE,MAAM,EAAE8D,QAAQ,GAAG,aAAa,GAAG,SAAS;QAC5CyE,UAAU,EAAE,eAAe;QAC3BtF,YAAY,EAAE;MAChB,CAAE;MACF3D,OAAO,EAAEA,CAAA;QAAA,IAAAkJ,qBAAA;QAAA,OAAM,CAAC1E,QAAQ,MAAA0E,qBAAA,GAAIjE,YAAY,CAACqC,OAAO,cAAA4B,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;MAAA,CAAC;MAC1DC,WAAW,EAAEzB,YAAa;MAC1B0B,WAAW,EAAEvB,aAAc;MAC3BwB,UAAU,EAAE9B,UAAW;MACvB+B,MAAM,EAAExB,UAAW;MAAAjH,QAAA,gBAEnBhB,OAAA;QACE0J,GAAG,EAAEvE,YAAa;QAClB9E,IAAI,EAAC,MAAM;QACXsJ,QAAQ;QACRC,MAAM,EAAEnF,aAAa,CAACoF,IAAI,CAAC,GAAG,CAAE;QAChCC,QAAQ,EAAEzC,gBAAiB;QAC3B/G,KAAK,EAAE;UAAEO,OAAO,EAAE;QAAO,CAAE;QAC3B6D,QAAQ,EAAEA;MAAS;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEFrB,OAAA,CAACT,MAAM;QACL0B,IAAI,EAAE,EAAG;QACTX,KAAK,EAAE;UACLG,KAAK,EAAEwE,UAAU,GAAG,SAAS,GAAG,SAAS;UACzCpB,YAAY,EAAE;QAChB;MAAE;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFrB,OAAA;QAAIM,KAAK,EAAE;UACTwD,QAAQ,EAAE,UAAU;UACpBiG,UAAU,EAAE,KAAK;UACjBtJ,KAAK,EAAE,SAAS;UAChBoD,YAAY,EAAE;QAChB,CAAE;QAAA7C,QAAA,EACCiE,UAAU,GAAG,kBAAkB,GAAG;MAAe;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eAELrB,OAAA;QAAGM,KAAK,EAAE;UACRwD,QAAQ,EAAE,UAAU;UACpBrD,KAAK,EAAE,SAAS;UAChBoD,YAAY,EAAE;QAChB,CAAE;QAAA7C,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJrB,OAAA;QAAGM,KAAK,EAAE;UACRwD,QAAQ,EAAE,SAAS;UACnBrD,KAAK,EAAE;QACT,CAAE;QAAAO,QAAA,GAAC,UACO,EAACuD,SAAS,EAAC,iBAAU,EAAC1E,cAAc,CAAC2E,WAAW,CAAC,EAAC,WAC1D,eAAAxE,OAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACLwE,WAAW,EAAC,GAAC,EAACtB,SAAS,EAAC,kBAC3B;MAAA;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGA,CAAC+C,cAAc,CAACkB,MAAM,GAAG,CAAC,IAAIP,MAAM,CAACO,MAAM,GAAG,CAAC,kBAC9CtF,OAAA;MAAKM,KAAK,EAAE;QACVO,OAAO,EAAE,MAAM;QACfmJ,mBAAmB,EAAE,uCAAuC;QAC5Df,GAAG,EAAE,MAAM;QACXgB,SAAS,EAAE;MACb,CAAE;MAAAjJ,QAAA,GAECoD,cAAc,CAACmB,GAAG,CAAEsD,KAAK,IAAK;QAC7B,MAAM1I,mBAAmB,GAAGwE,cAAc,CAAC+D,QAAQ,CAACG,KAAK,CAACnD,aAAa,CAAC;QAExE,oBACE1F,OAAA;UAEEM,KAAK,EAAE;YACL4I,QAAQ,EAAE,UAAU;YACpBgB,WAAW,EAAE,GAAG;YAChBvJ,YAAY,EAAE,KAAK;YACnBwJ,QAAQ,EAAE,QAAQ;YAClBzJ,MAAM,EAAEmI,KAAK,CAACuB,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;YACpE5J,eAAe,EAAE,SAAS;YAC1B6J,OAAO,EAAElK,mBAAmB,GAAG,GAAG,GAAG,CAAC;YACtCkI,MAAM,EAAElI,mBAAmB,GAAG,iBAAiB,GAAG,MAAM;YACxDgJ,UAAU,EAAE;UACd,CAAE;UAAAnI,QAAA,GAGDb,mBAAmB,iBAClBH,OAAA;YAAKM,KAAK,EAAE;cACV4I,QAAQ,EAAE,UAAU;cACpBoB,KAAK,EAAE,CAAC;cACR9J,eAAe,EAAE,wBAAwB;cACzCK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBwJ,MAAM,EAAE;YACV,CAAE;YAAAvJ,QAAA,eACAhB,OAAA;cAAKM,KAAK,EAAE;gBACVE,eAAe,EAAE,wBAAwB;gBACzCC,KAAK,EAAE,OAAO;gBACdF,OAAO,EAAE,aAAa;gBACtBI,YAAY,EAAE,KAAK;gBACnBmD,QAAQ,EAAE,UAAU;gBACpBiG,UAAU,EAAE;cACd,CAAE;cAAA/I,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACHrB,OAAA,CAACwD,SAAS;YACRhC,SAAS,EAAEqH,KAAK,CAAC2B,SAAU;YAC3B/G,GAAG,EAAEoF,KAAK,CAACjD,SAAU;YACrBtF,KAAK,EAAE;cACLmK,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,SAAS,EAAE;YACb;UAAE;YAAAzJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKFrB,OAAA;YAAKM,KAAK,EAAE;cACV4I,QAAQ,EAAE,UAAU;cACpB0B,GAAG,EAAE,QAAQ;cACbC,KAAK,EAAE,QAAQ;cACfhK,OAAO,EAAE,MAAM;cACfoI,GAAG,EAAE;YACP,CAAE;YAAAjI,QAAA,GACC,CAAC6H,KAAK,CAACuB,UAAU,IAAI9F,YAAY,iBAChCtE,OAAA;cACEE,OAAO,EAAEA,CAAA,KAAM0I,eAAe,CAACC,KAAK,CAACnD,aAAa,CAAE;cACpDpF,KAAK,EAAE;gBACLC,OAAO,EAAE,SAAS;gBAClBC,eAAe,EAAE,oBAAoB;gBACrCC,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFX,KAAK,EAAC,gBAAgB;cAAAY,QAAA,eAEtBhB,OAAA,CAACN,WAAW;gBAACuB,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACT,EAGA,CAAC,MAAM;cACN,MAAMyJ,eAAe,GAAGlG,iBAAiB,IAAIC,mBAAmB;cAChE,MAAMkG,iBAAiB,GAAG1G,qBAAqB;cAE/ClC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyG,KAAK,CAACnD,aAAa,EAAE;gBAC7DoF,eAAe;gBACfC,iBAAiB;gBACjB5K;cACF,CAAC,CAAC;cAEF,IAAI2K,eAAe,EAAE;gBACnB,oBACE9K,OAAA,CAACC,YAAY;kBACXC,OAAO,EAAEA,CAAA,KAAMsI,2BAA2B,CAACK,KAAK,CAACnD,aAAa,CAAE;kBAChEvF,mBAAmB,EAAEA,mBAAoB;kBACzCC,KAAK,EAAED,mBAAmB,GAAG,eAAe,GAAG;gBAAoB;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAEN,CAAC,MAAM,IAAI0J,iBAAiB,EAAE;gBAC5B,oBACE/K,OAAA,CAACC,YAAY;kBACXC,OAAO,EAAEA,CAAA,KAAMyI,mBAAmB,CAACE,KAAK,CAACnD,aAAa,CAAE;kBACxDtF,KAAK,EAAC;gBAAc;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAEN;cACA,OAAO,IAAI;YACb,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAzGC,YAAYwH,KAAK,CAACnD,aAAa,EAAE;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0GrC,CAAC;MAER,CAAC,CAAC,EAGD0D,MAAM,CAACQ,GAAG,CAAEsD,KAAK,iBAChB7I,OAAA;QAEEM,KAAK,EAAE;UACL4I,QAAQ,EAAE,UAAU;UACpBgB,WAAW,EAAE,GAAG;UAChBvJ,YAAY,EAAE,KAAK;UACnBwJ,QAAQ,EAAE,QAAQ;UAClBzJ,MAAM,EAAE,mBAAmB;UAC3BF,eAAe,EAAE;QACnB,CAAE;QAAAQ,QAAA,gBAEFhB,OAAA;UACE+D,GAAG,EAAE8E,KAAK,CAAC7B,OAAQ;UACnBvD,GAAG,EAAEoF,KAAK,CAAC7C,IAAI,CAACL,IAAK;UACrBrF,KAAK,EAAE;YACLmK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE,OAAO;YAClBN,OAAO,EAAExB,KAAK,CAAC/F,MAAM,KAAK,WAAW,GAAG,GAAG,GAAG;UAChD;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGDwH,KAAK,CAAC/F,MAAM,KAAK,WAAW,iBAC3B9C,OAAA;UAAKM,KAAK,EAAE;YACV4I,QAAQ,EAAE,UAAU;YACpBoB,KAAK,EAAE,CAAC;YACR9J,eAAe,EAAE,oBAAoB;YACrCK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBN,KAAK,EAAE,OAAO;YACdqD,QAAQ,EAAE;UACZ,CAAE;UAAA9C,QAAA,EAAC;QAEH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAGDrB,OAAA;UAAKM,KAAK,EAAE;YACV4I,QAAQ,EAAE,UAAU;YACpB0B,GAAG,EAAE,QAAQ;YACbC,KAAK,EAAE;UACT,CAAE;UAAA7J,QAAA,eACAhB,OAAA,CAACC,YAAY;YACXC,OAAO,EAAEA,CAAA,KAAMgI,WAAW,CAACW,KAAK,CAACpD,EAAE,CAAE;YACrCrF,KAAK,EAAC;UAAc;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GA/CDwH,KAAK,CAACpD,EAAE;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDV,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACyD,GAAA,CArcIZ,mBAAuD;AAAA8G,GAAA,GAAvD9G,mBAAuD;AAuc7D,eAAeA,mBAAmB;AAAC,IAAA5C,EAAA,EAAA2C,GAAA,EAAA+G,GAAA;AAAAC,YAAA,CAAA3J,EAAA;AAAA2J,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}