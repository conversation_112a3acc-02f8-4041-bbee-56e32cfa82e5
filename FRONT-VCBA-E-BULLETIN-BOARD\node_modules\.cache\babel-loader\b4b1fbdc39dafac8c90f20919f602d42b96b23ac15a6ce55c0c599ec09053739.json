{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.586 12.586 19 19\",\n  key: \"ea5xo7\"\n}], [\"path\", {\n  d: \"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z\",\n  key: \"277e5u\"\n}]];\nconst MousePointer = createLucideIcon(\"mouse-pointer\", __iconNode);\nexport { __iconNode, MousePointer as default };\n//# sourceMappingURL=mouse-pointer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}