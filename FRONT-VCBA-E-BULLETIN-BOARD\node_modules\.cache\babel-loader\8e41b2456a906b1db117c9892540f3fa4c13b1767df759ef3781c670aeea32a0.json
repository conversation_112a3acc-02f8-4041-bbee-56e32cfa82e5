{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.66 6H14a2 2 0 0 1 2 2v2.5l5.248-3.062A.5.5 0 0 1 22 7.87v8.196\",\n  key: \"w8jjjt\"\n}], [\"path\", {\n  d: \"M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2\",\n  key: \"1xawa7\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst VideoOff = createLucideIcon(\"video-off\", __iconNode);\nexport { __iconNode, VideoOff as default };\n//# sourceMappingURL=video-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}