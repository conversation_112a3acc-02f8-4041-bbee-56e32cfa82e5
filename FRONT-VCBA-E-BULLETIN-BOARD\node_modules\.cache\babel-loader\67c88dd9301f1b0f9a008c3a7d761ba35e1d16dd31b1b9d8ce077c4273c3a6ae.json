{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.065 12.493-6.18 1.318a.934.934 0 0 1-1.108-.702l-.537-2.15a1.07 1.07 0 0 1 .691-1.265l13.504-4.44\",\n  key: \"k4qptu\"\n}], [\"path\", {\n  d: \"m13.56 11.747 4.332-.924\",\n  key: \"19l80z\"\n}], [\"path\", {\n  d: \"m16 21-3.105-6.21\",\n  key: \"7oh9d\"\n}], [\"path\", {\n  d: \"M16.485 5.94a2 2 0 0 1 1.455-2.425l1.09-.272a1 1 0 0 1 1.212.727l1.515 6.06a1 1 0 0 1-.727 1.213l-1.09.272a2 2 0 0 1-2.425-1.455z\",\n  key: \"m7xp4m\"\n}], [\"path\", {\n  d: \"m6.158 8.633 1.114 4.456\",\n  key: \"74o979\"\n}], [\"path\", {\n  d: \"m8 21 3.105-6.21\",\n  key: \"1fvxut\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"2\",\n  key: \"1c1ljs\"\n}]];\nconst Telescope = createLucideIcon(\"telescope\", __iconNode);\nexport { __iconNode, Telescope as default };\n//# sourceMappingURL=telescope.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}