{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 11.22C11 9.997 10 9 10 8a2 2 0 0 1 4 0c0 1-.998 2.002-2.01 3.22\",\n  key: \"1rnhq3\"\n}], [\"path\", {\n  d: \"m12 18 2.57-3.5\",\n  key: \"116vt7\"\n}], [\"path\", {\n  d: \"M6.243 9.016a7 7 0 0 1 11.507-.009\",\n  key: \"10dq0b\"\n}], [\"path\", {\n  d: \"M9.35 14.53 12 11.22\",\n  key: \"tdsyp2\"\n}], [\"path\", {\n  d: \"M9.35 14.53C7.728 12.246 6 10.221 6 7a6 5 0 0 1 12 0c-.005 3.22-1.778 5.235-3.43 7.5l3.557 4.527a1 1 0 0 1-.203 1.43l-1.894 1.36a1 1 0 0 1-1.384-.215L12 18l-2.679 3.593a1 1 0 0 1-1.39.213l-1.865-1.353a1 1 0 0 1-.203-1.422z\",\n  key: \"nmifey\"\n}]];\nconst Ribbon = createLucideIcon(\"ribbon\", __iconNode);\nexport { __iconNode, Ribbon as default };\n//# sourceMappingURL=ribbon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}