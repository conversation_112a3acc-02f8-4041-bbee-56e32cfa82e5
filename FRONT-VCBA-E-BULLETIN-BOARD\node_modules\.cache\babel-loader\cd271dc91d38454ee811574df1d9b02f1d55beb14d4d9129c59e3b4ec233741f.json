{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22V7a1 1 0 0 0-1-1H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5a1 1 0 0 0-1-1H2\",\n  key: \"1ah6g2\"\n}], [\"rect\", {\n  x: \"14\",\n  y: \"2\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"88lufb\"\n}]];\nconst Blocks = createLucideIcon(\"blocks\", __iconNode);\nexport { __iconNode, Blocks as default };\n//# sourceMappingURL=blocks.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}