{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v2\",\n  key: \"7u0qdc\"\n}], [\"path\", {\n  d: \"M14 2v2\",\n  key: \"6buw04\"\n}], [\"path\", {\n  d: \"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1\",\n  key: \"pwadti\"\n}], [\"path\", {\n  d: \"M6 2v2\",\n  key: \"colzsn\"\n}]];\nconst Coffee = createLucideIcon(\"coffee\", __iconNode);\nexport { __iconNode, Coffee as default };\n//# sourceMappingURL=coffee.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}