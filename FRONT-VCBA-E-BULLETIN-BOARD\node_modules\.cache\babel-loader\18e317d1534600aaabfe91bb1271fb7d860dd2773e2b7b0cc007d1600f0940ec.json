{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 16-4 4-4-4\",\n  key: \"3dv8je\"\n}], [\"path\", {\n  d: \"M3 12h18\",\n  key: \"1i2n21\"\n}], [\"path\", {\n  d: \"m8 8 4-4 4 4\",\n  key: \"2bscm2\"\n}]];\nconst SeparatorHorizontal = createLucideIcon(\"separator-horizontal\", __iconNode);\nexport { __iconNode, SeparatorHorizontal as default };\n//# sourceMappingURL=separator-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}