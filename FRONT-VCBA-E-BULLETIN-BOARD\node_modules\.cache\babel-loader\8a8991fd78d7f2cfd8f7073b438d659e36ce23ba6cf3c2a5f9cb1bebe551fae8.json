{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.714 3.048a.498.498 0 0 0-.683.627l2.843 7.627a2 2 0 0 1 0 1.396l-2.842 7.627a.498.498 0 0 0 .682.627l18-8.5a.5.5 0 0 0 0-.904z\",\n  key: \"117uat\"\n}], [\"path\", {\n  d: \"M6 12h16\",\n  key: \"s4cdu5\"\n}]];\nconst SendHorizontal = createLucideIcon(\"send-horizontal\", __iconNode);\nexport { __iconNode, SendHorizontal as default };\n//# sourceMappingURL=send-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}