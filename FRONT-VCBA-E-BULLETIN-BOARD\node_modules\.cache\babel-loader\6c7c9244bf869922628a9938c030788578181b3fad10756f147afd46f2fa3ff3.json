{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{studentService}from'../../services/studentService';import{AlertTriangle,RefreshCw,Edit,Key,Trash2,Info,User}from'lucide-react';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentManagement=()=>{// Auth context\nconst{user}=useAdminAuth();// State management\nconst[students,setStudents]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[filterStatus,setFilterStatus]=useState('active');const[currentPage,setCurrentPage]=useState(1);const[totalPages,setTotalPages]=useState(1);const[totalStudents,setTotalStudents]=useState(0);// Get available grade levels based on admin's assigned grade\nconst getAvailableGradeLevels=()=>{if(user!==null&&user!==void 0&&user.grade_level){// Grade-specific admin can only manage their assigned grade\nreturn[user.grade_level];}else{// System admin can manage grades 11 and 12 only\nreturn[11,12];}};// Modal states\nconst[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showDeleteModal,setShowDeleteModal]=useState(false);const[selectedStudent,setSelectedStudent]=useState(null);// Form states for creating/editing student\nconst[formData,setFormData]=useState({studentNumber:'',email:'',firstName:'',middleName:'',lastName:'',suffix:'',phoneNumber:'',gradeLevel:(user===null||user===void 0?void 0:user.grade_level)||11,section:'',parentGuardianName:'',parentGuardianPhone:'',address:''});const[isSubmitting,setIsSubmitting]=useState(false);const[error,setError]=useState(null);// State for debounced search term\nconst[debouncedSearchTerm,setDebouncedSearchTerm]=useState('');// Load students data - SIMPLIFIED WITHOUT AUTH GUARDS\nconst loadStudents=useCallback(async()=>{try{setLoading(true);setError(null);// Build filter parameters\nconst params={page:currentPage,limit:10,search:debouncedSearchTerm||undefined};// Add status filter if not 'all'\nif(filterStatus==='active'){params.is_active=true;}else if(filterStatus==='inactive'){params.is_active=false;}// If filterStatus === 'all', don't add is_active parameter\n// Add grade level filter for grade-specific admins\nif(user!==null&&user!==void 0&&user.grade_level){params.grade_level=user.grade_level;}// System admins (grade_level = null) can see all students\nconsole.log('Loading students with params:',params);console.log('Filter status:',filterStatus,'is_active param:',params.is_active);const response=await studentService.getStudents(params);console.log('API Response received:',response);console.log('Students loaded:',response.students.map(s=>{var _s$profile;return{name:((_s$profile=s.profile)===null||_s$profile===void 0?void 0:_s$profile.full_name)||'No name',email:s.email,is_active:s.is_active,status:s.is_active?'Active':'Inactive'};}));// Additional debug: Check what we're about to set in state\nconsole.log('About to set students state with:',response.students.length,'students');setStudents(response.students);setTotalPages(response.pagination.totalPages);setTotalStudents(response.pagination.total);}catch(error){console.error('Error loading students:',error);setError('Failed to load students. Please check if the backend is running.');setStudents([]);}finally{setLoading(false);}},[currentPage,debouncedSearchTerm,filterStatus,user]);// Load students when dependencies change\nuseEffect(()=>{loadStudents();},[loadStudents]);// Debounced search - update debounced term after delay\nuseEffect(()=>{const timeoutId=setTimeout(()=>{setDebouncedSearchTerm(searchTerm);setCurrentPage(1);// Reset to first page when searching\n},500);return()=>clearTimeout(timeoutId);},[searchTerm]);// Utility functions\nconst getStatusColor=isActive=>{return isActive?'#22c55e':'#f59e0b';};const getStatusText=isActive=>{return isActive?'Active':'Inactive';};// Search handler\nconst handleSearchChange=e=>{setSearchTerm(e.target.value);};// Email generation function\nconst generateEmail=(studentNumber,gradeLevel,section,lastName,firstName,middleName)=>{if(!studentNumber||!gradeLevel||!section||!lastName||!firstName){return'';}const firstLetter=firstName.charAt(0).toUpperCase();const middleInitial=middleName?middleName.charAt(0).toUpperCase():'';const cleanLastName=lastName.replace(/\\s+/g,'').toLowerCase();const cleanSection=section.replace(/\\s+/g,'').toUpperCase();return\"\".concat(studentNumber,\"_\").concat(gradeLevel,\"_\").concat(cleanSection,\"_\").concat(cleanLastName,\"_\").concat(firstLetter,\"_\").concat(middleInitial,\"@gmail.com\");};// Form handlers\nconst handleInputChange=e=>{const{name,value}=e.target;const newFormData=_objectSpread(_objectSpread({},formData),{},{[name]:name==='gradeLevel'?parseInt(value):value});// Auto-generate email when required fields are filled\nif(['studentNumber','gradeLevel','section','lastName','firstName','middleName'].includes(name)){const generatedEmail=generateEmail(newFormData.studentNumber,newFormData.gradeLevel.toString(),newFormData.section,newFormData.lastName,newFormData.firstName,newFormData.middleName);if(generatedEmail){newFormData.email=generatedEmail;}}setFormData(newFormData);};const resetForm=()=>{setFormData({studentNumber:'',email:'',firstName:'',middleName:'',lastName:'',suffix:'',phoneNumber:'',gradeLevel:(user===null||user===void 0?void 0:user.grade_level)||11,section:'',parentGuardianName:'',parentGuardianPhone:'',address:''});};// CRUD Operations\nconst handleCreateStudent=async()=>{setIsSubmitting(true);setError(null);try{// Validate required fields\nif(!formData.studentNumber||!formData.email||!formData.firstName||!formData.lastName||!formData.phoneNumber||!formData.section||!formData.gradeLevel){throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Grade Level, Section)');}// Validate email format\nconst emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(formData.email)){throw new Error('Please enter a valid email address');}// Prepare student data for API\nconst studentData={// Account data\nstudent_number:formData.studentNumber,email:formData.email,password:'Student123',// Default password\nis_active:true,created_by:(user===null||user===void 0?void 0:user.id)||1,// Current admin ID\n// Profile data\nfirst_name:formData.firstName,middle_name:formData.middleName||undefined,last_name:formData.lastName,suffix:formData.suffix||undefined,phone_number:formData.phoneNumber,grade_level:formData.gradeLevel,section:formData.section,parent_guardian_name:formData.parentGuardianName||undefined,parent_guardian_phone:formData.parentGuardianPhone||undefined,address:formData.address||undefined};// Debug: Log the data being sent\nconsole.log('Sending student data:',studentData);// Call API to create student\nconst createdStudent=await studentService.createStudent(studentData);alert(\"Student account created successfully!\\n\\nStudent Details:\\nName: \".concat(createdStudent.profile.full_name,\"\\nStudent Number: \").concat(createdStudent.student_number,\"\\nEmail: \").concat(createdStudent.email,\"\\n\\nLogin Credentials:\\nEmail: \").concat(createdStudent.email,\"\\nPassword: Student123\\n\\nPlease share these credentials with the student and ask them to change the password on first login.\"));resetForm();setShowCreateModal(false);// Refresh the students list\nawait loadStudents();}catch(error){console.error('Error creating student:',error);setError(error.message||'Failed to create student account. Please try again.');}finally{setIsSubmitting(false);}};const handleEditStudent=student=>{setSelectedStudent(student);setFormData({studentNumber:student.student_number,email:student.email,firstName:student.profile.first_name,middleName:student.profile.middle_name||'',lastName:student.profile.last_name,suffix:student.profile.suffix||'',phoneNumber:student.profile.phone_number,gradeLevel:student.profile.grade_level,section:student.profile.section,parentGuardianName:student.profile.parent_guardian_name||'',parentGuardianPhone:student.profile.parent_guardian_phone||'',address:student.profile.address||''});setShowEditModal(true);};const handleUpdateStudent=async()=>{if(!selectedStudent)return;setIsSubmitting(true);setError(null);try{// Validate required fields\nif(!formData.studentNumber||!formData.email||!formData.firstName||!formData.lastName||!formData.phoneNumber||!formData.section){throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Section)');}// Validate email format\nconst emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(formData.email)){throw new Error('Please enter a valid email address');}// Prepare update data\nconst updateData={student_number:formData.studentNumber,email:formData.email,first_name:formData.firstName,middle_name:formData.middleName||undefined,last_name:formData.lastName,suffix:formData.suffix||undefined,phone_number:formData.phoneNumber,grade_level:formData.gradeLevel,section:formData.section,parent_guardian_name:formData.parentGuardianName||undefined,parent_guardian_phone:formData.parentGuardianPhone||undefined,address:formData.address||undefined};// Call API to update student\nawait studentService.updateStudent(selectedStudent.student_id.toString(),updateData);alert('Student information updated successfully!');resetForm();setShowEditModal(false);setSelectedStudent(null);// Refresh the students list\nawait loadStudents();}catch(error){console.error('Error updating student:',error);setError(error.message||'Failed to update student information. Please try again.');}finally{setIsSubmitting(false);}};const handleDeleteStudent=student=>{setSelectedStudent(student);setShowDeleteModal(true);};const handleResetPassword=async student=>{if(!window.confirm(\"Are you sure you want to reset the password for \".concat(student.profile.full_name,\"?\\n\\nThe password will be reset to: Student123\"))){return;}try{setIsSubmitting(true);await studentService.resetStudentPassword(student.student_id.toString());// Show success message\nalert(\"Password reset successfully for \".concat(student.profile.full_name,\"!\\n\\nNew password: Student123\"));}catch(error){console.error('Error resetting password:',error);alert(\"Failed to reset password: \".concat(error.message));}finally{setIsSubmitting(false);}};const confirmDeleteStudent=async()=>{if(!selectedStudent)return;setIsSubmitting(true);setError(null);try{// Call API to soft delete student (deactivate)\nawait studentService.deleteStudent(selectedStudent.student_id.toString());alert('Student account has been deactivated successfully!');setShowDeleteModal(false);setSelectedStudent(null);// Refresh the students list\nawait loadStudents();}catch(error){console.error('Error deleting student:',error);setError(error.message||'Failed to deactivate student account. Please try again.');}finally{setIsSubmitting(false);}};const handlePageChange=page=>{setCurrentPage(page);};const handleStatusFilterChange=value=>{setFilterStatus(value);setCurrentPage(1);};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontSize:'2rem',fontWeight:'700',color:'#2d5016',margin:'0 0 0.5rem 0'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(User,{size:20,color:\"#1e40af\"}),\"Student Management\"]})}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'1.1rem'},children:[\"Create, edit, and manage student accounts\",(user===null||user===void 0?void 0:user.grade_level)&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'inline-block',marginLeft:'0.5rem',padding:'0.25rem 0.5rem',backgroundColor:'#dbeafe',color:'#1e40af',borderRadius:'0.375rem',fontSize:'0.875rem',fontWeight:'500'},children:[\"Grade \",user.grade_level,\" Only\"]})]})]})}),/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',padding:'1.5rem',marginBottom:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'1rem',alignItems:'center',flexWrap:'wrap'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateModal(true),disabled:loading,style:{background:'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.5rem 1rem',fontSize:'0.9rem',fontWeight:'600',cursor:loading?'not-allowed':'pointer',opacity:loading?0.6:1,transition:'all 0.2s ease',display:'flex',alignItems:'center',gap:'0.4rem',whiteSpace:'nowrap'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'1rem'},children:\"+\"}),\"Create Student Account\"]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,minWidth:'300px'},children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search students\",value:searchTerm,onChange:handleSearchChange,style:{width:'100%',padding:'0.75rem 1rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.9rem',color:'#666',marginRight:'0.5rem'},children:\"Filter:\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleStatusFilterChange('all'),style:{padding:'0.5rem 1rem',border:filterStatus==='all'?'2px solid #4CAF50':'1px solid #ddd',borderRadius:'6px',fontSize:'0.85rem',backgroundColor:filterStatus==='all'?'#4CAF50':'#fff',color:filterStatus==='all'?'#fff':'#333',cursor:'pointer',fontWeight:filterStatus==='all'?'bold':'normal',transition:'all 0.2s ease'},children:\"All\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleStatusFilterChange('active'),style:{padding:'0.5rem 1rem',border:filterStatus==='active'?'2px solid #4CAF50':'1px solid #ddd',borderRadius:'6px',fontSize:'0.85rem',backgroundColor:filterStatus==='active'?'#4CAF50':'#fff',color:filterStatus==='active'?'#fff':'#333',cursor:'pointer',fontWeight:filterStatus==='active'?'bold':'normal',transition:'all 0.2s ease'},children:\"Active\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleStatusFilterChange('inactive'),style:{padding:'0.5rem 1rem',border:filterStatus==='inactive'?'2px solid #f44336':'1px solid #ddd',borderRadius:'6px',fontSize:'0.85rem',backgroundColor:filterStatus==='inactive'?'#f44336':'#fff',color:filterStatus==='inactive'?'#fff':'#333',cursor:'pointer',fontWeight:filterStatus==='inactive'?'bold':'normal',transition:'all 0.2s ease'},children:\"Inactive\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:loadStudents,disabled:loading,style:{background:'#f8fdf8',border:'1px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 1rem',fontSize:'1rem',cursor:loading?'not-allowed':'pointer',opacity:loading?0.6:1,transition:'all 0.2s ease'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(RefreshCw,{size:16}),\"Refresh\"]})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f8fdf8',padding:'1rem 1.5rem',borderBottom:'1px solid #e8f5e8',display:'grid',gridTemplateColumns:'1fr 2fr 2fr 1fr 1fr 1fr 150px',gap:'1rem',fontWeight:'600',color:'#2d5016',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"div\",{children:\"Student #\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Email\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Grade & Section\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Phone\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"div\",{children:\"Actions\"})]}),loading?/*#__PURE__*/_jsx(\"div\",{style:{padding:'2rem',textAlign:'center',color:'#6b7280'},children:\"Loading students...\"}):students.length===0?/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem',textAlign:'center',color:'#6b7280'},children:[\"No students found. \",searchTerm&&'Try adjusting your search criteria.']}):students.map(student=>/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem 1.5rem',borderBottom:'1px solid #f3f4f6',display:'grid',gridTemplateColumns:'1fr 2fr 2fr 1fr 1fr 1fr 150px',gap:'1rem',alignItems:'center',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#2d5016'},children:student.student_number}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#374151'},children:student.profile.full_name}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280'},children:student.email}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#374151'},children:[\"Grade \",student.profile.grade_level,\" - \",student.profile.section]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#374151'},children:student.profile.phone_number}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{style:{background:getStatusColor(student.is_active),color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600'},children:getStatusText(student.is_active)})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditStudent(student),title:\"Edit Student\",style:{background:'#3b82f6',color:'white',border:'none',borderRadius:'6px',padding:'0.5rem',cursor:'pointer',fontSize:'0.75rem'},children:/*#__PURE__*/_jsx(Edit,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleResetPassword(student),title:\"Reset Password to Default (Student123)\",style:{background:'#f59e0b',color:'white',border:'none',borderRadius:'6px',padding:'0.5rem',cursor:'pointer',fontSize:'0.75rem'},children:/*#__PURE__*/_jsx(Key,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteStudent(student),title:\"Deactivate Student\",style:{background:'#ef4444',color:'white',border:'none',borderRadius:'6px',padding:'0.5rem',cursor:'pointer',fontSize:'0.75rem'},children:/*#__PURE__*/_jsx(Trash2,{size:16})})]})]},student.student_id)),totalPages>1&&/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem 1.5rem',borderTop:'1px solid #f3f4f6',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:[\"Page \",currentPage,\" of \",totalPages]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(currentPage-1),disabled:currentPage===1,style:{background:currentPage===1?'#f3f4f6':'#2d5016',color:currentPage===1?'#9ca3af':'white',border:'none',borderRadius:'6px',padding:'0.5rem 1rem',cursor:currentPage===1?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Previous\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(currentPage+1),disabled:currentPage===totalPages,style:{background:currentPage===totalPages?'#f3f4f6':'#2d5016',color:currentPage===totalPages?'#9ca3af':'white',border:'none',borderRadius:'6px',padding:'0.5rem 1rem',cursor:currentPage===totalPages?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Next\"})]})]})]}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,background:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',width:'95%',maxWidth:'1200px',maxHeight:'95vh',overflow:'hidden',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'2rem'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{margin:0,color:'#2d5016',fontSize:'1.5rem',fontWeight:'700'},children:\"Create New Student Account\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowCreateModal(false);resetForm();},style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280'},children:\"\\xD7\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{style:{background:'#fef2f2',border:'1px solid #fecaca',borderRadius:'8px',padding:'1rem',marginBottom:'1rem',color:'#dc2626'},children:error}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,overflow:'auto',paddingRight:'0.5rem'},children:/*#__PURE__*/_jsxs(\"form\",{onSubmit:e=>{e.preventDefault();handleCreateStudent();},style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'2rem',height:'fit-content'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Student Number *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"studentNumber\",value:formData.studentNumber,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"e.g., 2025-0001\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Email Address * (Auto-generated)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,readOnly:true,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',backgroundColor:'#f9fafb',color:'#6b7280'},placeholder:\"Email will be auto-generated based on student details\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"First Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Juan\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Last Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Cruz\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Middle Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"middleName\",value:formData.middleName,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Dela\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Suffix\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"suffix\",value:formData.suffix,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Jr., Sr., III\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Phone Number *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"phoneNumber\",value:formData.phoneNumber,onChange:handleInputChange,onInput:e=>{// Allow only numbers\ne.currentTarget.value=e.currentTarget.value.replace(/[^0-9]/g,'');},required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"09123456789\",maxLength:11})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Grade Level *\"}),/*#__PURE__*/_jsx(\"select\",{name:\"gradeLevel\",value:formData.gradeLevel,onChange:handleInputChange,required:true,disabled:getAvailableGradeLevels().length===1// Disable if only one option\n,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',backgroundColor:'white'},children:getAvailableGradeLevels().map(grade=>/*#__PURE__*/_jsxs(\"option\",{value:grade,children:[\"Grade \",grade]},grade))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Section *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"section\",value:formData.section,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"A, B, C, etc.\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Parent/Guardian Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"parentGuardianName\",value:formData.parentGuardianName,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Parent/Guardian Name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Parent/Guardian Phone\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"parentGuardianPhone\",value:formData.parentGuardianPhone,onChange:handleInputChange,onInput:e=>{// Allow only numbers\ne.currentTarget.value=e.currentTarget.value.replace(/[^0-9]/g,'');},style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"09123456789\",maxLength:11})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Address\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"address\",value:formData.address,onChange:handleInputChange,rows:4,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',resize:'vertical'},placeholder:\"Complete address\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f0f9ff',border:'1px solid #bae6fd',borderRadius:'8px',padding:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(Info,{size:20,color:\"#0369a1\"}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600',color:'#0369a1'},children:\"Default Login Credentials\"})]}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:0,color:'#0369a1',fontSize:'0.875rem'},children:[\"The student account will be created with the default password: \",/*#__PURE__*/_jsx(\"strong\",{children:\"Student123\"}),/*#__PURE__*/_jsx(\"br\",{}),\"Please share these credentials with the student and ask them to change the password on first login.\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{gridColumn:'1 / -1',display:'flex',justifyContent:'flex-end',gap:'1rem',marginTop:'1rem',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>{setShowCreateModal(false);resetForm();},disabled:isSubmitting,style:{background:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',opacity:isSubmitting?0.6:1},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isSubmitting,style:{background:isSubmitting?'#9ca3af':'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',fontWeight:'600'},children:isSubmitting?'Creating...':'Create Student Account'})]})]})})]})}),showEditModal&&selectedStudent&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,background:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',width:'95%',maxWidth:'1200px',maxHeight:'95vh',overflow:'hidden',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'2rem'},children:[/*#__PURE__*/_jsxs(\"h2\",{style:{margin:0,color:'#2d5016',fontSize:'1.5rem',fontWeight:'700'},children:[\"Edit Student: \",selectedStudent.profile.full_name]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowEditModal(false);setSelectedStudent(null);resetForm();},style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280'},children:\"\\xD7\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{style:{background:'#fef2f2',border:'1px solid #fecaca',borderRadius:'8px',padding:'1rem',marginBottom:'1rem',color:'#dc2626'},children:error}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,overflow:'auto',paddingRight:'0.5rem'},children:/*#__PURE__*/_jsxs(\"form\",{onSubmit:e=>{e.preventDefault();handleUpdateStudent();},style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'2rem',height:'fit-content'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Student Number *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"studentNumber\",value:formData.studentNumber,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"e.g., 2025-0001\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Email Address * (Auto-generated)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,readOnly:true,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',backgroundColor:'#f9fafb',color:'#6b7280'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"First Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Juan\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Last Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Cruz\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Middle Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"middleName\",value:formData.middleName,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Dela\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Suffix\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"suffix\",value:formData.suffix,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},placeholder:\"Jr., Sr., III\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Phone Number *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"phoneNumber\",value:formData.phoneNumber,onChange:handleInputChange,onInput:e=>{// Allow only numbers\ne.currentTarget.value=e.currentTarget.value.replace(/[^0-9]/g,'');},required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},maxLength:11})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Grade Level *\"}),/*#__PURE__*/_jsx(\"select\",{name:\"gradeLevel\",value:formData.gradeLevel,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',backgroundColor:'white'},children:getAvailableGradeLevels().map(grade=>/*#__PURE__*/_jsxs(\"option\",{value:grade,children:[\"Grade \",grade]},grade))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Section *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"section\",value:formData.section,onChange:handleInputChange,required:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Parent/Guardian Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"parentGuardianName\",value:formData.parentGuardianName,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Parent/Guardian Phone\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"parentGuardianPhone\",value:formData.parentGuardianPhone,onChange:handleInputChange,onInput:e=>{// Allow only numbers\ne.currentTarget.value=e.currentTarget.value.replace(/[^0-9]/g,'');},style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem'},maxLength:11})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',fontWeight:'600',color:'#374151'},children:\"Address\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"address\",value:formData.address,onChange:handleInputChange,rows:4,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',resize:'vertical'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{gridColumn:'1 / -1',display:'flex',justifyContent:'flex-end',gap:'1rem',marginTop:'1rem',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>{setShowEditModal(false);setSelectedStudent(null);resetForm();},disabled:isSubmitting,style:{background:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',opacity:isSubmitting?0.6:1},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isSubmitting,style:{background:isSubmitting?'#9ca3af':'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',fontWeight:'600'},children:isSubmitting?'Updating...':'Update Student'})]})]})})]})}),showDeleteModal&&selectedStudent&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,background:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',width:'90%',maxWidth:'500px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{margin:0,color:'#dc2626',fontSize:'1.5rem',fontWeight:'700'},children:\"Deactivate Student Account\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowDeleteModal(false);setSelectedStudent(null);},style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280'},children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2rem'},children:[/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 1rem 0',color:'#374151'},children:\"Are you sure you want to deactivate the account for:\"}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f9fafb',border:'1px solid #e5e7eb',borderRadius:'8px',padding:'1rem'},children:[/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontWeight:'600',color:'#2d5016'},children:selectedStudent.profile.full_name}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:'0.25rem 0 0 0',color:'#6b7280',fontSize:'0.875rem'},children:[\"Student Number: \",selectedStudent.student_number]}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:'0.25rem 0 0 0',color:'#6b7280',fontSize:'0.875rem'},children:[\"Email: \",selectedStudent.email]})]}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'1rem 0 0 0',color:'#dc2626',fontSize:'0.875rem'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'flex-start',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:16,color:\"#dc2626\",style:{marginTop:'0.125rem',flexShrink:0}}),\"This action will deactivate the student's account. They will not be able to log in until reactivated.\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'flex-end',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowDeleteModal(false);setSelectedStudent(null);},disabled:isSubmitting,style:{background:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',opacity:isSubmitting?0.6:1},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:confirmDeleteStudent,disabled:isSubmitting,style:{background:isSubmitting?'#9ca3af':'#dc2626',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontSize:'1rem',cursor:isSubmitting?'not-allowed':'pointer',opacity:isSubmitting?0.6:1},children:isSubmitting?'Deactivating...':'Deactivate Account'})]})]})})]});};export default StudentManagement;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}