{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 21v-3a2 2 0 0 0-4 0v3\",\n  key: \"1rgiei\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}], [\"path\", {\n  d: \"M18 16h.01\",\n  key: \"plv8zi\"\n}], [\"path\", {\n  d: \"M22 7a1 1 0 0 0-1-1h-2a2 2 0 0 1-1.143-.359L13.143 2.36a2 2 0 0 0-2.286-.001L6.143 5.64A2 2 0 0 1 5 6H3a1 1 0 0 0-1 1v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2z\",\n  key: \"1ogmi3\"\n}], [\"path\", {\n  d: \"M6 12h.01\",\n  key: \"c2rlol\"\n}], [\"path\", {\n  d: \"M6 16h.01\",\n  key: \"1pmjb7\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"2\",\n  key: \"1yojzk\"\n}]];\nconst University = createLucideIcon(\"university\", __iconNode);\nexport { __iconNode, University as default };\n//# sourceMappingURL=university.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}