{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m6 9 6 6 6-6\",\n  key: \"qrunsl\"\n}]];\nconst ChevronDown = createLucideIcon(\"chevron-down\", __iconNode);\nexport { __iconNode, ChevronDown as default };\n//# sourceMappingURL=chevron-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}