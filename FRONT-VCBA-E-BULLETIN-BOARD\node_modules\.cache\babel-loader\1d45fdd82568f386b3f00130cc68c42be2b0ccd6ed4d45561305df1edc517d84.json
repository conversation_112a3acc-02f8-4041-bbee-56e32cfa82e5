{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v8.4\",\n  key: \"1d3kfm\"\n}], [\"path\", {\n  d: \"M8 18v-7.7L16 9v7\",\n  key: \"1oie6o\"\n}], [\"circle\", {\n  cx: \"14\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1bzzi3\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1fncim\"\n}]];\nconst FileMusic = createLucideIcon(\"file-music\", __iconNode);\nexport { __iconNode, FileMusic as default };\n//# sourceMappingURL=file-music.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}