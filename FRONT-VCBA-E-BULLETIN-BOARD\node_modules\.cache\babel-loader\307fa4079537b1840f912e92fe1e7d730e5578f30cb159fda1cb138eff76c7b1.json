{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n  key: \"vwvbt9\"\n}], [\"path\", {\n  d: \"M15 8h6\",\n  key: \"8ybuxh\"\n}], [\"path\", {\n  d: \"M16.243 3.757A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673A9.4 9.4 0 0 1 18.667 12\",\n  key: \"bdwj86\"\n}]];\nconst BellMinus = createLucideIcon(\"bell-minus\", __iconNode);\nexport { __iconNode, BellMinus as default };\n//# sourceMappingURL=bell-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}