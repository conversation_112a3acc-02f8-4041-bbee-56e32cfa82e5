{"ast": null, "code": "/**\n * @license React\n * scheduler.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  a: for (; 0 < index;) {\n    var parentIndex = index - 1 >>> 1,\n      parent = heap[parentIndex];\n    if (0 < compare(parent, node)) heap[parentIndex] = node, heap[index] = parent, index = parentIndex;else break a;\n  }\n}\nfunction peek(heap) {\n  return 0 === heap.length ? null : heap[0];\n}\nfunction pop(heap) {\n  if (0 === heap.length) return null;\n  var first = heap[0],\n    last = heap.pop();\n  if (last !== first) {\n    heap[0] = last;\n    a: for (var index = 0, length = heap.length, halfLength = length >>> 1; index < halfLength;) {\n      var leftIndex = 2 * (index + 1) - 1,\n        left = heap[leftIndex],\n        rightIndex = leftIndex + 1,\n        right = heap[rightIndex];\n      if (0 > compare(left, last)) rightIndex < length && 0 > compare(right, left) ? (heap[index] = right, heap[rightIndex] = last, index = rightIndex) : (heap[index] = left, heap[leftIndex] = last, index = leftIndex);else if (rightIndex < length && 0 > compare(right, last)) heap[index] = right, heap[rightIndex] = last, index = rightIndex;else break a;\n    }\n  }\n  return first;\n}\nfunction compare(a, b) {\n  var diff = a.sortIndex - b.sortIndex;\n  return 0 !== diff ? diff : a.id - b.id;\n}\nexports.unstable_now = void 0;\nif (\"object\" === typeof performance && \"function\" === typeof performance.now) {\n  var localPerformance = performance;\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date,\n    initialTime = localDate.now();\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n}\nvar taskQueue = [],\n  timerQueue = [],\n  taskIdCounter = 1,\n  currentTask = null,\n  currentPriorityLevel = 3,\n  isPerformingWork = !1,\n  isHostCallbackScheduled = !1,\n  isHostTimeoutScheduled = !1,\n  needsPaint = !1,\n  localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n  localClearTimeout = \"function\" === typeof clearTimeout ? clearTimeout : null,\n  localSetImmediate = \"undefined\" !== typeof setImmediate ? setImmediate : null;\nfunction advanceTimers(currentTime) {\n  for (var timer = peek(timerQueue); null !== timer;) {\n    if (null === timer.callback) pop(timerQueue);else if (timer.startTime <= currentTime) pop(timerQueue), timer.sortIndex = timer.expirationTime, push(taskQueue, timer);else break;\n    timer = peek(timerQueue);\n  }\n}\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = !1;\n  advanceTimers(currentTime);\n  if (!isHostCallbackScheduled) if (null !== peek(taskQueue)) isHostCallbackScheduled = !0, isMessageLoopRunning || (isMessageLoopRunning = !0, schedulePerformWorkUntilDeadline());else {\n    var firstTimer = peek(timerQueue);\n    null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n  }\n}\nvar isMessageLoopRunning = !1,\n  taskTimeoutID = -1,\n  frameInterval = 5,\n  startTime = -1;\nfunction shouldYieldToHost() {\n  return needsPaint ? !0 : exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n}\nfunction performWorkUntilDeadline() {\n  needsPaint = !1;\n  if (isMessageLoopRunning) {\n    var currentTime = exports.unstable_now();\n    startTime = currentTime;\n    var hasMoreWork = !0;\n    try {\n      a: {\n        isHostCallbackScheduled = !1;\n        isHostTimeoutScheduled && (isHostTimeoutScheduled = !1, localClearTimeout(taskTimeoutID), taskTimeoutID = -1);\n        isPerformingWork = !0;\n        var previousPriorityLevel = currentPriorityLevel;\n        try {\n          b: {\n            advanceTimers(currentTime);\n            for (currentTask = peek(taskQueue); null !== currentTask && !(currentTask.expirationTime > currentTime && shouldYieldToHost());) {\n              var callback = currentTask.callback;\n              if (\"function\" === typeof callback) {\n                currentTask.callback = null;\n                currentPriorityLevel = currentTask.priorityLevel;\n                var continuationCallback = callback(currentTask.expirationTime <= currentTime);\n                currentTime = exports.unstable_now();\n                if (\"function\" === typeof continuationCallback) {\n                  currentTask.callback = continuationCallback;\n                  advanceTimers(currentTime);\n                  hasMoreWork = !0;\n                  break b;\n                }\n                currentTask === peek(taskQueue) && pop(taskQueue);\n                advanceTimers(currentTime);\n              } else pop(taskQueue);\n              currentTask = peek(taskQueue);\n            }\n            if (null !== currentTask) hasMoreWork = !0;else {\n              var firstTimer = peek(timerQueue);\n              null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n              hasMoreWork = !1;\n            }\n          }\n          break a;\n        } finally {\n          currentTask = null, currentPriorityLevel = previousPriorityLevel, isPerformingWork = !1;\n        }\n        hasMoreWork = void 0;\n      }\n    } finally {\n      hasMoreWork ? schedulePerformWorkUntilDeadline() : isMessageLoopRunning = !1;\n    }\n  }\n}\nvar schedulePerformWorkUntilDeadline;\nif (\"function\" === typeof localSetImmediate) schedulePerformWorkUntilDeadline = function () {\n  localSetImmediate(performWorkUntilDeadline);\n};else if (\"undefined\" !== typeof MessageChannel) {\n  var channel = new MessageChannel(),\n    port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n  schedulePerformWorkUntilDeadline = function () {\n    port.postMessage(null);\n  };\n} else schedulePerformWorkUntilDeadline = function () {\n  localSetTimeout(performWorkUntilDeadline, 0);\n};\nfunction requestHostTimeout(callback, ms) {\n  taskTimeoutID = localSetTimeout(function () {\n    callback(exports.unstable_now());\n  }, ms);\n}\nexports.unstable_IdlePriority = 5;\nexports.unstable_ImmediatePriority = 1;\nexports.unstable_LowPriority = 4;\nexports.unstable_NormalPriority = 3;\nexports.unstable_Profiling = null;\nexports.unstable_UserBlockingPriority = 2;\nexports.unstable_cancelCallback = function (task) {\n  task.callback = null;\n};\nexports.unstable_forceFrameRate = function (fps) {\n  0 > fps || 125 < fps ? console.error(\"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\") : frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5;\n};\nexports.unstable_getCurrentPriorityLevel = function () {\n  return currentPriorityLevel;\n};\nexports.unstable_next = function (eventHandler) {\n  switch (currentPriorityLevel) {\n    case 1:\n    case 2:\n    case 3:\n      var priorityLevel = 3;\n      break;\n    default:\n      priorityLevel = currentPriorityLevel;\n  }\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n};\nexports.unstable_requestPaint = function () {\n  needsPaint = !0;\n};\nexports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case 1:\n    case 2:\n    case 3:\n    case 4:\n    case 5:\n      break;\n    default:\n      priorityLevel = 3;\n  }\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n};\nexports.unstable_scheduleCallback = function (priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  \"object\" === typeof options && null !== options ? (options = options.delay, options = \"number\" === typeof options && 0 < options ? currentTime + options : currentTime) : options = currentTime;\n  switch (priorityLevel) {\n    case 1:\n      var timeout = -1;\n      break;\n    case 2:\n      timeout = 250;\n      break;\n    case 5:\n      timeout = 1073741823;\n      break;\n    case 4:\n      timeout = 1e4;\n      break;\n    default:\n      timeout = 5e3;\n  }\n  timeout = options + timeout;\n  priorityLevel = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: options,\n    expirationTime: timeout,\n    sortIndex: -1\n  };\n  options > currentTime ? (priorityLevel.sortIndex = options, push(timerQueue, priorityLevel), null === peek(taskQueue) && priorityLevel === peek(timerQueue) && (isHostTimeoutScheduled ? (localClearTimeout(taskTimeoutID), taskTimeoutID = -1) : isHostTimeoutScheduled = !0, requestHostTimeout(handleTimeout, options - currentTime))) : (priorityLevel.sortIndex = timeout, push(taskQueue, priorityLevel), isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, isMessageLoopRunning || (isMessageLoopRunning = !0, schedulePerformWorkUntilDeadline())));\n  return priorityLevel;\n};\nexports.unstable_shouldYield = shouldYieldToHost;\nexports.unstable_wrapCallback = function (callback) {\n  var parentPriorityLevel = currentPriorityLevel;\n  return function () {\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}