{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M12 18a6 6 0 0 0 0-12v12z\",\n  key: \"j4l70d\"\n}]];\nconst Contrast = createLucideIcon(\"contrast\", __iconNode);\nexport { __iconNode, Contrast as default };\n//# sourceMappingURL=contrast.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}