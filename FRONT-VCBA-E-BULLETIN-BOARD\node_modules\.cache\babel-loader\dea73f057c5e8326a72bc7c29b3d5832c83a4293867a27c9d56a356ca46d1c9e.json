{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M7 3v18\",\n  key: \"bbkbws\"\n}], [\"path\", {\n  d: \"M3 7.5h4\",\n  key: \"zfgn84\"\n}], [\"path\", {\n  d: \"M3 12h18\",\n  key: \"1i2n21\"\n}], [\"path\", {\n  d: \"M3 16.5h4\",\n  key: \"1230mu\"\n}], [\"path\", {\n  d: \"M17 3v18\",\n  key: \"in4fa5\"\n}], [\"path\", {\n  d: \"M17 7.5h4\",\n  key: \"myr1c1\"\n}], [\"path\", {\n  d: \"M17 16.5h4\",\n  key: \"go4c1d\"\n}]];\nconst Film = createLucideIcon(\"film\", __iconNode);\nexport { __iconNode, Film as default };\n//# sourceMappingURL=film.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}