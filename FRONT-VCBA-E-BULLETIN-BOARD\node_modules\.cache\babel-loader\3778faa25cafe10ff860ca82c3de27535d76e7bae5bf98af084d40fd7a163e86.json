{"ast": null, "code": "/**\r\n * Form utility functions for consistent form handling across the application\r\n */\n\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\n/**\r\n * ULTRA PERMISSIVE: Creates FormData that works with NO VALIDATION backend\r\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n  console.log('🔧 createFormData - Input data:', formData);\n\n  // ULTRA PERMISSIVE: Send ALL fields, let backend handle them\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip scheduled_publish_at logic\n    if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      return;\n    }\n\n    // SEND EVERYTHING - let backend decide what to do\n    if (key === 'category_id') {\n      // Always send category_id, even if empty (backend will handle defaults)\n      if (value && value !== '' && value !== '0') {\n        const parsed = parseInt(String(value), 10);\n        if (!isNaN(parsed) && parsed > 0) {\n          formDataToSubmit.append(key, parsed.toString());\n        } else {\n          formDataToSubmit.append(key, '1'); // Default to category 1\n        }\n      } else {\n        formDataToSubmit.append(key, '1'); // Default to category 1\n      }\n    } else if (key === 'subcategory_id') {\n      // Send subcategory_id only if it's valid, otherwise skip completely\n      if (value && value !== '' && value !== '0' && value !== 'null' && value !== 'undefined') {\n        const parsed = parseInt(String(value), 10);\n        if (!isNaN(parsed) && parsed > 0) {\n          formDataToSubmit.append(key, parsed.toString());\n        }\n      }\n      // Skip empty subcategory_id completely\n    } else if (key === 'is_pinned' || key === 'is_alert' || key === 'allow_comments' || key === 'allow_sharing') {\n      // Always send boolean fields as 0 or 1\n      const stringValue = String(value);\n      if (value === true || stringValue === 'true' || stringValue === '1' || value === 1) {\n        formDataToSubmit.append(key, '1');\n      } else {\n        formDataToSubmit.append(key, '0');\n      }\n    } else {\n      // Send ALL other fields, even if empty - backend will handle\n      if (value !== null && value !== undefined) {\n        formDataToSubmit.append(key, String(value));\n      }\n    }\n  });\n\n  // Add files\n  if (files && files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  // Debug log what we're sending\n  console.log('📤 createFormData - FormData entries:');\n  formDataToSubmit.forEach((value, key) => {\n    console.log(`  ${key}: ${value} (${typeof value})`);\n  });\n  return formDataToSubmit;\n};\n\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\r\n * ULTRA PERMISSIVE validation rules - minimal requirements only\r\n */\nexport const announcementValidationRules = {\n  required: ['title'],\n  // Only title is truly required\n  maxLength: {\n    title: 1000\n  },\n  // Very generous limit\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      // No validation - always return null (no error)\n      return null;\n    }\n  }\n};\n\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "console", "log", "Object", "entries", "for<PERSON>ach", "key", "value", "status", "parsed", "parseInt", "String", "isNaN", "append", "toString", "stringValue", "undefined", "length", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "trim", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "join", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\r\n * Form utility functions for consistent form handling across the application\r\n */\r\n\r\nexport interface FormField {\r\n  [key: string]: string | number | boolean | null | undefined;\r\n}\r\n\r\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\r\n/**\r\n * ULTRA PERMISSIVE: Creates FormData that works with NO VALIDATION backend\r\n */\r\nexport const createFormData = (\r\n  formData: FormField,\r\n  files: File[] = [],\r\n  options: {\r\n    skipScheduledDate?: boolean;\r\n    fileFieldName?: string;\r\n  } = {}\r\n): FormData => {\r\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\r\n  const formDataToSubmit = new FormData();\r\n\r\n  console.log('🔧 createFormData - Input data:', formData);\r\n\r\n  // ULTRA PERMISSIVE: Send ALL fields, let backend handle them\r\n  Object.entries(formData).forEach(([key, value]) => {\r\n    // Skip scheduled_publish_at logic\r\n    if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\r\n      return;\r\n    }\r\n\r\n    // SEND EVERYTHING - let backend decide what to do\r\n    if (key === 'category_id') {\r\n      // Always send category_id, even if empty (backend will handle defaults)\r\n      if (value && value !== '' && value !== '0') {\r\n        const parsed = parseInt(String(value), 10);\r\n        if (!isNaN(parsed) && parsed > 0) {\r\n          formDataToSubmit.append(key, parsed.toString());\r\n        } else {\r\n          formDataToSubmit.append(key, '1'); // Default to category 1\r\n        }\r\n      } else {\r\n        formDataToSubmit.append(key, '1'); // Default to category 1\r\n      }\r\n    } else if (key === 'subcategory_id') {\r\n      // Send subcategory_id only if it's valid, otherwise skip completely\r\n      if (value && value !== '' && value !== '0' && value !== 'null' && value !== 'undefined') {\r\n        const parsed = parseInt(String(value), 10);\r\n        if (!isNaN(parsed) && parsed > 0) {\r\n          formDataToSubmit.append(key, parsed.toString());\r\n        }\r\n      }\r\n      // Skip empty subcategory_id completely\r\n    } else if (key === 'is_pinned' || key === 'is_alert' || key === 'allow_comments' || key === 'allow_sharing') {\r\n      // Always send boolean fields as 0 or 1\r\n      const stringValue = String(value);\r\n      if (value === true || stringValue === 'true' || stringValue === '1' || value === 1) {\r\n        formDataToSubmit.append(key, '1');\r\n      } else {\r\n        formDataToSubmit.append(key, '0');\r\n      }\r\n    } else {\r\n      // Send ALL other fields, even if empty - backend will handle\r\n      if (value !== null && value !== undefined) {\r\n        formDataToSubmit.append(key, String(value));\r\n      }\r\n    }\r\n  });\r\n\r\n  // Add files\r\n  if (files && files.length > 0) {\r\n    files.forEach((file) => {\r\n      formDataToSubmit.append(fileFieldName, file);\r\n    });\r\n  }\r\n\r\n  // Debug log what we're sending\r\n  console.log('📤 createFormData - FormData entries:');\r\n  formDataToSubmit.forEach((value, key) => {\r\n    console.log(`  ${key}: ${value} (${typeof value})`);\r\n  });\r\n\r\n  return formDataToSubmit;\r\n};\r\n\r\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\r\nexport const validateFormFields = (\r\n  formData: FormField,\r\n  rules: {\r\n    required?: string[];\r\n    maxLength?: { [key: string]: number };\r\n    custom?: { [key: string]: (value: any) => string | null };\r\n  } = {}\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n  const { required = [], maxLength = {}, custom = {} } = rules;\r\n\r\n  // Check required fields\r\n  required.forEach(field => {\r\n    const value = formData[field];\r\n    if (!value || (typeof value === 'string' && !value.trim())) {\r\n      errors[field] = `${field.replace('_', ' ')} is required`;\r\n    }\r\n  });\r\n\r\n  // Check max length\r\n  Object.entries(maxLength).forEach(([field, max]) => {\r\n    const value = formData[field];\r\n    if (typeof value === 'string' && value.length > max) {\r\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\r\n    }\r\n  });\r\n\r\n  // Apply custom validation\r\n  Object.entries(custom).forEach(([field, validator]) => {\r\n    const value = formData[field];\r\n    const error = validator(value);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n/**\r\n * ULTRA PERMISSIVE validation rules - minimal requirements only\r\n */\r\nexport const announcementValidationRules = {\r\n  required: ['title'], // Only title is truly required\r\n  maxLength: { title: 1000 }, // Very generous limit\r\n  custom: {\r\n    scheduled_publish_at: (value: any, formData?: FormField) => {\r\n      // No validation - always return null (no error)\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\r\nexport const validateFile = (\r\n  file: File,\r\n  options: {\r\n    maxSize?: number;\r\n    allowedTypes?: string[];\r\n  } = {}\r\n): string | null => {\r\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\r\n\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  if (file.size > maxSize) {\r\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAEvCC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAER,QAAQ,CAAC;;EAExD;EACAS,MAAM,CAACC,OAAO,CAACV,QAAQ,CAAC,CAACW,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD;IACA,IAAID,GAAG,KAAK,sBAAsB,IAAIT,iBAAiB,IAAIH,QAAQ,CAACc,MAAM,KAAK,WAAW,EAAE;MAC1F;IACF;;IAEA;IACA,IAAIF,GAAG,KAAK,aAAa,EAAE;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,GAAG,EAAE;QAC1C,MAAME,MAAM,GAAGC,QAAQ,CAACC,MAAM,CAACJ,KAAK,CAAC,EAAE,EAAE,CAAC;QAC1C,IAAI,CAACK,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;UAChCV,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAEG,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC;QACjD,CAAC,MAAM;UACLf,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACrC;MACF,CAAC,MAAM;QACLP,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,MAAM,IAAIA,GAAG,KAAK,gBAAgB,EAAE;MACnC;MACA,IAAIC,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,WAAW,EAAE;QACvF,MAAME,MAAM,GAAGC,QAAQ,CAACC,MAAM,CAACJ,KAAK,CAAC,EAAE,EAAE,CAAC;QAC1C,IAAI,CAACK,KAAK,CAACH,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;UAChCV,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAEG,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC;QACjD;MACF;MACA;IACF,CAAC,MAAM,IAAIR,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,gBAAgB,IAAIA,GAAG,KAAK,eAAe,EAAE;MAC3G;MACA,MAAMS,WAAW,GAAGJ,MAAM,CAACJ,KAAK,CAAC;MACjC,IAAIA,KAAK,KAAK,IAAI,IAAIQ,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,GAAG,IAAIR,KAAK,KAAK,CAAC,EAAE;QAClFR,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAE,GAAG,CAAC;MACnC,CAAC,MAAM;QACLP,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAE,GAAG,CAAC;MACnC;IACF,CAAC,MAAM;MACL;MACA,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKS,SAAS,EAAE;QACzCjB,gBAAgB,CAACc,MAAM,CAACP,GAAG,EAAEK,MAAM,CAACJ,KAAK,CAAC,CAAC;MAC7C;IACF;EACF,CAAC,CAAC;;EAEF;EACA,IAAIZ,KAAK,IAAIA,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;IAC7BtB,KAAK,CAACU,OAAO,CAAEa,IAAI,IAAK;MACtBnB,gBAAgB,CAACc,MAAM,CAACf,aAAa,EAAEoB,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;;EAEA;EACAjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACpDH,gBAAgB,CAACM,OAAO,CAAC,CAACE,KAAK,EAAED,GAAG,KAAK;IACvCL,OAAO,CAACC,GAAG,CAAC,KAAKI,GAAG,KAAKC,KAAK,KAAK,OAAOA,KAAK,GAAG,CAAC;EACrD,CAAC,CAAC;EAEF,OAAOR,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,kBAAkB,GAAGA,CAChCzB,QAAmB,EACnB0B,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACjB,OAAO,CAACoB,KAAK,IAAI;IACxB,MAAMlB,KAAK,GAAGb,QAAQ,CAAC+B,KAAK,CAAC;IAC7B,IAAI,CAAClB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACmB,IAAI,CAAC,CAAE,EAAE;MAC1DL,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACAxB,MAAM,CAACC,OAAO,CAACmB,SAAS,CAAC,CAAClB,OAAO,CAAC,CAAC,CAACoB,KAAK,EAAEG,GAAG,CAAC,KAAK;IAClD,MAAMrB,KAAK,GAAGb,QAAQ,CAAC+B,KAAK,CAAC;IAC7B,IAAI,OAAOlB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACU,MAAM,GAAGW,GAAG,EAAE;MACnDP,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACAzB,MAAM,CAACC,OAAO,CAACoB,MAAM,CAAC,CAACnB,OAAO,CAAC,CAAC,CAACoB,KAAK,EAAEI,SAAS,CAAC,KAAK;IACrD,MAAMtB,KAAK,GAAGb,QAAQ,CAAC+B,KAAK,CAAC;IAC7B,MAAMK,KAAK,GAAGD,SAAS,CAACtB,KAAK,CAAC;IAC9B,IAAIuB,KAAK,EAAE;MACTT,MAAM,CAACI,KAAK,CAAC,GAAGK,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOT,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,2BAA2B,GAAG;EACzCT,QAAQ,EAAE,CAAC,OAAO,CAAC;EAAE;EACrBC,SAAS,EAAE;IAAES,KAAK,EAAE;EAAK,CAAC;EAAE;EAC5BR,MAAM,EAAE;IACNS,oBAAoB,EAAEA,CAAC1B,KAAU,EAAEb,QAAoB,KAAK;MAC1D;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwC,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACvC,GAAG,CAACiC,KAAK,CAAC,GAAGM,IAAI,CAACvC,GAAG,CAACmC,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGM,IAAI,CAACG,GAAG,CAACP,CAAC,EAAEG,CAAC,CAAC,EAAEK,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAGA,CAC1B5B,IAAU,EACVtB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEmD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGpD,OAAO;EAEpH,IAAI,CAACoD,YAAY,CAACC,QAAQ,CAAC/B,IAAI,CAACgC,IAAI,CAAC,EAAE;IACrC,OAAO,aAAahC,IAAI,CAACgC,IAAI,qCAAqCF,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIjC,IAAI,CAACkC,IAAI,GAAGL,OAAO,EAAE;IACvB,OAAO,aAAab,cAAc,CAAChB,IAAI,CAACkC,IAAI,CAAC,oCAAoClB,cAAc,CAACa,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}