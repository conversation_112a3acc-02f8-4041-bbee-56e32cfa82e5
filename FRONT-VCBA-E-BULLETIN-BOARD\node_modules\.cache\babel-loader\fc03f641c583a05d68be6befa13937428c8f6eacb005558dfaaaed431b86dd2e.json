{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useMemo}from'react';import{useHierarchicalCategories}from'../../../hooks/useAnnouncements';import{useMultipleImageUpload}from'../../../hooks/useMultipleImageUpload';import MultipleImageUpload from'../MultipleImageUpload';import CascadingCategoryDropdown from'../../common/CascadingCategoryDropdown';import{createFormData,validateFormFields,announcementValidationRules}from'../../../utils/formUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnnouncementModal=_ref=>{let{isOpen,onClose,onSave,announcement,loading=false}=_ref;const{categories,loading:categoriesLoading,error:categoriesError}=useHierarchicalCategories();// Use public service (categories should be public)\nconst[formData,setFormData]=useState({title:'',content:'',category_id:'',subcategory_id:'',status:'draft',is_pinned:false,is_alert:false,allow_comments:true,allow_sharing:true,scheduled_publish_at:''});const[selectedImages,setSelectedImages]=useState([]);const[errors,setErrors]=useState({});const[successMessage,setSuccessMessage]=useState(null);// Debug categories loading\nuseEffect(()=>{console.log('🔍 AnnouncementModal - Categories state:',{categories:(categories===null||categories===void 0?void 0:categories.length)||0,categoriesLoading,categoriesError,categoriesData:categories});},[categories,categoriesLoading,categoriesError]);// Memoized validation using utility function\nconst validationErrors=useMemo(()=>{return validateFormFields(formData,_objectSpread(_objectSpread({},announcementValidationRules),{},{custom:_objectSpread(_objectSpread({},announcementValidationRules.custom),{},{scheduled_publish_at:value=>announcementValidationRules.custom.scheduled_publish_at(value,formData)})}));},[formData]);// Multiple image upload hook\nconst{existingImages,loading:imageLoading,error:imageError,uploadImages,setPrimaryImage,refreshImages,clearError:clearImageError,// New pending operations\npendingDeletes,markForDeletion,unmarkForDeletion,applyPendingDeletes,clearPendingDeletes,// Clear all image state\nclearAllImageState}=useMultipleImageUpload({announcementId:announcement===null||announcement===void 0?void 0:announcement.announcement_id,onSuccess:message=>setSuccessMessage(message),onError:error=>console.error('Image upload error:',error)});// Comprehensive reset function for new announcements\nconst resetModalForNewAnnouncement=useCallback(()=>{console.log('🧹 Resetting modal for new announcement');// Reset form data to initial state\nsetFormData({title:'',content:'',category_id:'',subcategory_id:'',status:'draft',is_pinned:false,is_alert:false,allow_comments:true,allow_sharing:true,scheduled_publish_at:''});// Clear all images and image-related state\nsetSelectedImages([]);// Clear all errors and messages\nsetErrors({});setSuccessMessage(null);// Clear image upload related state\nclearImageError();clearPendingDeletes();clearAllImageState();// This will clear existing images from previous announcements\nconsole.log('✅ Modal reset complete - ready for new announcement');},[clearImageError,clearPendingDeletes,clearAllImageState]);// Enhanced close handler that ensures everything is cleared\nconst handleClose=useCallback(()=>{console.log('🚪 Closing modal - clearing all data');// Reset everything when closing\nresetModalForNewAnnouncement();// Call the parent's onClose\nonClose();},[onClose,resetModalForNewAnnouncement]);// Initialize form data when announcement changes\nuseEffect(()=>{if(announcement){var _announcement$categor,_announcement$subcate;console.log('📝 Loading announcement for editing:',announcement.announcement_id);setFormData({title:announcement.title||'',content:announcement.content||'',category_id:((_announcement$categor=announcement.category_id)===null||_announcement$categor===void 0?void 0:_announcement$categor.toString())||'',subcategory_id:((_announcement$subcate=announcement.subcategory_id)===null||_announcement$subcate===void 0?void 0:_announcement$subcate.toString())||'',status:announcement.status||'draft',is_pinned:Boolean(announcement.is_pinned),is_alert:Boolean(announcement.is_alert),allow_comments:Boolean(announcement.allow_comments),allow_sharing:Boolean(announcement.allow_sharing),scheduled_publish_at:announcement.scheduled_publish_at||''});// Clear selected images for editing\nsetSelectedImages([]);// Load existing images for editing\nif(announcement.announcement_id){refreshImages();}}else if(isOpen){// Modal is open for creating new announcement\nconsole.log('🆕 Modal opened for new announcement - resetting all data');resetModalForNewAnnouncement();}// Always clear errors and messages when modal state changes\nsetErrors({});setSuccessMessage(null);clearImageError();clearPendingDeletes();// Clear any pending deletions when modal opens/closes\n},[announcement===null||announcement===void 0?void 0:announcement.announcement_id,isOpen,clearImageError,clearPendingDeletes,resetModalForNewAnnouncement]);// Handle Escape key to close modal and clear data\nuseEffect(()=>{const handleEscapeKey=event=>{if(event.key==='Escape'&&isOpen){handleClose();}};if(isOpen){document.addEventListener('keydown',handleEscapeKey);return()=>document.removeEventListener('keydown',handleEscapeKey);}},[isOpen,handleClose]);// Optimized validation using memoized errors\nconst validateForm=useCallback(()=>{const newErrors=_objectSpread({},validationErrors);// Add additional validation rules\nif(formData.title.length>255){newErrors.title='Title must be less than 255 characters';}setErrors(newErrors);return Object.keys(newErrors).length===0;},[validationErrors,formData.title]);const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{// Create completion callback for additional operations\nconst onComplete=async()=>{// If we're editing and have images, upload them separately\nif(announcement&&selectedImages.length>0){try{await uploadImages(selectedImages);setSelectedImages([]);// Clear selected images after upload\n}catch(uploadError){console.error('Error uploading additional images:',uploadError);// Don't throw here as the main announcement was saved successfully\n}}// Refresh images to show updates immediately\nif(announcement!==null&&announcement!==void 0&&announcement.announcement_id){await refreshImages();}// Clear pending deletes after successful update\nclearPendingDeletes();};// Debug logging for subcategory testing\nconsole.log('🧪 Form submission data:',{category_id:formData.category_id,subcategory_id:formData.subcategory_id,title:formData.title,status:formData.status});// Use utility function for form data creation\nconst formDataToSubmit=createFormData(formData,selectedImages);// Debug: Log what's being sent to backend\nconsole.log('🧪 FormData entries being sent:');const formDataEntries=[];formDataToSubmit.forEach((value,key)=>{formDataEntries.push(\"  \".concat(key,\": \").concat(value));});console.log(formDataEntries.join('\\n'));// Call onSave with completion callback - parent will handle modal closing and success message\nawait onSave(formDataToSubmit,pendingDeletes.length>0?applyPendingDeletes:undefined,onComplete);// Parent component will handle:\n// 1. Executing onComplete (image uploads)\n// 2. Refreshing announcements list\n// 3. Closing modal\n// 4. Showing success message\n}catch(error){console.error('Error saving announcement:',error);setErrors({submit:'Failed to save announcement. Please try again.'});}};// Handle category selection\nconst handleCategoryChange=categoryId=>{console.log('🧪 AnnouncementModal - Category changed:',categoryId);console.log('🧪 AnnouncementModal - Available categories:',categories===null||categories===void 0?void 0:categories.map(cat=>({id:cat.category_id,name:cat.name})));setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{category_id:(categoryId===null||categoryId===void 0?void 0:categoryId.toString())||'',subcategory_id:''// Clear subcategory when category changes\n}));// Clear category error\nif(errors.category_id){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{category_id:''}));}};// Handle subcategory selection\nconst handleSubcategoryChange=subcategoryId=>{console.log('🧪 Subcategory changed:',subcategoryId);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{subcategory_id:(subcategoryId===null||subcategoryId===void 0?void 0:subcategoryId.toString())||''}));};const handleInputChange=e=>{const{name,value,type}=e.target;if(type==='checkbox'){const checked=e.target.checked;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:checked}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));}// Clear error when user starts typing\nif(errors[name]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',padding:'2rem',width:'100%',maxWidth:'600px',maxHeight:'90vh',overflow:'auto',boxShadow:'0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{fontSize:'1.5rem',fontWeight:'700',color:'#2d5016',margin:0},children:announcement?'Edit Announcement':'Create New Announcement'}),/*#__PURE__*/_jsx(\"button\",{onClick:handleClose,style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Title *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"title\",value:formData.title,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.title?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease'},placeholder:\"Enter announcement title\"}),errors.title&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.title})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Content *\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"content\",value:formData.content,onChange:handleInputChange,rows:6,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.content?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease',resize:'vertical'},placeholder:\"Enter announcement content\"}),errors.content&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.content})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Category *\"}),categoriesError?/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'0.375rem',color:'#dc2626',fontSize:'0.875rem'},children:[\"Error loading categories: \",categoriesError,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>window.location.reload(),style:{marginTop:'0.5rem',padding:'0.25rem 0.5rem',backgroundColor:'#dc2626',color:'white',border:'none',borderRadius:'0.25rem',fontSize:'0.75rem',cursor:'pointer'},children:\"Reload Page\"})]}):categoriesLoading?/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'#f0f9ff',border:'1px solid #bae6fd',borderRadius:'0.375rem',color:'#0369a1',fontSize:'0.875rem'},children:\"Loading categories...\"}):!categories||categories.length===0?/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fffbeb',border:'1px solid #fed7aa',borderRadius:'0.375rem',color:'#ea580c',fontSize:'0.875rem'},children:\"No categories available. Please contact administrator.\"}):/*#__PURE__*/_jsx(CascadingCategoryDropdown,{categories:categories,selectedCategoryId:formData.category_id?parseInt(formData.category_id):undefined,selectedSubcategoryId:formData.subcategory_id?parseInt(formData.subcategory_id):undefined,onCategoryChange:handleCategoryChange,onSubcategoryChange:handleSubcategoryChange,placeholder:\"Select Category\",required:true,error:errors.category_id,disabled:loading})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"status\",value:formData.status,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"draft\",children:\"Draft\"}),/*#__PURE__*/_jsx(\"option\",{value:\"published\",children:\"Published\"}),/*#__PURE__*/_jsx(\"option\",{value:\"scheduled\",children:\"Scheduled\"})]})]})]}),successMessage&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.75rem',backgroundColor:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'8px',color:'#166534',fontSize:'0.875rem',marginBottom:'1rem'},children:[\"\\u2713 \",successMessage]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem',padding:'16px',backgroundColor:'#F8FAFC',borderRadius:'8px',border:'1px solid #E2E8F0'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{fontSize:'1rem',fontWeight:'600',color:'#374151',margin:0},children:\"Images\"}),pendingDeletes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem 0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'6px',color:'#dc2626',fontSize:'0.875rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A0\\uFE0F\"}),pendingDeletes.length,\" image\",pendingDeletes.length>1?'s':'',\" will be deleted\"]})]}),/*#__PURE__*/_jsx(MultipleImageUpload,{onImagesChange:setSelectedImages,existingImages:existingImages,onSetPrimary:setPrimaryImage,maxImages:10,disabled:imageLoading,pendingDeletes:pendingDeletes,onMarkForDeletion:markForDeletion,onUnmarkForDeletion:unmarkForDeletion}),imageError&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc2626',fontSize:'0.875rem',marginTop:'0.5rem'},children:imageError})]}),formData.status==='scheduled'&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Scheduled Publish Date *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",name:\"scheduled_publish_at\",value:formData.scheduled_publish_at,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.scheduled_publish_at?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none'}}),errors.scheduled_publish_at&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.scheduled_publish_at})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_pinned\",checked:formData.is_pinned,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Pin this announcement\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_alert\",checked:formData.is_alert,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Mark as alert\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"allow_comments\",checked:formData.allow_comments,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Allow comments\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"allow_sharing\",checked:formData.allow_sharing,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Allow sharing\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'flex-end',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleClose,style:{padding:'0.75rem 1.5rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem'},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,style:{padding:'0.75rem 1.5rem',background:loading?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:loading?'not-allowed':'pointer',fontWeight:'600',fontSize:'0.875rem'},children:loading?'Saving...':announcement?'Update':'Create'})]})]})]})});};export default AnnouncementModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}