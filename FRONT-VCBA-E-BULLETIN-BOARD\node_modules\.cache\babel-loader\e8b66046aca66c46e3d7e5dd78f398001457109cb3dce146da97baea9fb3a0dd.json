{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 14v2.2l1.6 1\",\n  key: \"fo4ql5\"\n}], [\"path\", {\n  d: \"M7 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2\",\n  key: \"1urifu\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"qoo3c4\"\n}]];\nconst FolderClock = createLucideIcon(\"folder-clock\", __iconNode);\nexport { __iconNode, FolderClock as default };\n//# sourceMappingURL=folder-clock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}