{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 10v3\",\n  key: \"1fnikh\"\n}], [\"path\", {\n  d: \"M6 6v11\",\n  key: \"11sgs0\"\n}], [\"path\", {\n  d: \"M10 3v18\",\n  key: \"yhl04a\"\n}], [\"path\", {\n  d: \"M14 8v7\",\n  key: \"3a1oy3\"\n}], [\"path\", {\n  d: \"M18 5v13\",\n  key: \"123xd1\"\n}], [\"path\", {\n  d: \"M22 10v3\",\n  key: \"154ddg\"\n}]];\nconst AudioLines = createLucideIcon(\"audio-lines\", __iconNode);\nexport { __iconNode, AudioLines as default };\n//# sourceMappingURL=audio-lines.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}