{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"2\",\n  y1: \"2\",\n  x2: \"22\",\n  y2: \"22\",\n  key: \"1w4vcy\"\n}], [\"path\", {\n  d: \"M16.5 16.5 12 21l-7-7c-1.5-1.45-3-3.2-3-5.5a5.5 5.5 0 0 1 2.14-4.35\",\n  key: \"3mpagl\"\n}], [\"path\", {\n  d: \"M8.76 3.1c1.15.22 2.13.78 3.24 1.9 1.5-1.5 2.74-2 4.5-2A5.5 5.5 0 0 1 22 8.5c0 2.12-1.3 3.78-2.67 5.17\",\n  key: \"1gh3v3\"\n}]];\nconst HeartOff = createLucideIcon(\"heart-off\", __iconNode);\nexport { __iconNode, HeartOff as default };\n//# sourceMappingURL=heart-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}