{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 2v6\",\n  key: \"17ngun\"\n}], [\"path\", {\n  d: \"M15 2v6\",\n  key: \"s7yy2p\"\n}], [\"path\", {\n  d: \"M12 17v5\",\n  key: \"bb1du9\"\n}], [\"path\", {\n  d: \"M5 8h14\",\n  key: \"pcz4l3\"\n}], [\"path\", {\n  d: \"M6 11V8h12v3a6 6 0 1 1-12 0Z\",\n  key: \"wtfw2c\"\n}]];\nconst Plug2 = createLucideIcon(\"plug-2\", __iconNode);\nexport { __iconNode, Plug2 as default };\n//# sourceMappingURL=plug-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}