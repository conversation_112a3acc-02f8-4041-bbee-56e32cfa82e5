{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 22-1-4\",\n  key: \"1ow2iv\"\n}], [\"path\", {\n  d: \"M19 13.99a1 1 0 0 0 1-1V12a2 2 0 0 0-2-2h-3a1 1 0 0 1-1-1V4a2 2 0 0 0-4 0v5a1 1 0 0 1-1 1H6a2 2 0 0 0-2 2v.99a1 1 0 0 0 1 1\",\n  key: \"iw8jdu\"\n}], [\"path\", {\n  d: \"M5 14h14l1.973 6.767A1 1 0 0 1 20 22H4a1 1 0 0 1-.973-1.233z\",\n  key: \"1soew8\"\n}], [\"path\", {\n  d: \"m8 22 1-4\",\n  key: \"s3unb\"\n}]];\nconst BrushCleaning = createLucideIcon(\"brush-cleaning\", __iconNode);\nexport { __iconNode, BrushCleaning as default };\n//# sourceMappingURL=brush-cleaning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}