{"ast": null, "code": "// Application constants\nexport const API_BASE_URL=process.env.REACT_APP_API_BASE_URL||'http://localhost:5000';export const WEBSOCKET_URL=process.env.REACT_APP_WEBSOCKET_URL||'http://localhost:5000';// Utility function to get full image URL\nexport const getImageUrl=function(imagePath){let bustCache=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(!imagePath)return null;// If already a full URL, return as is\nif(imagePath.startsWith('http')){return bustCache?\"\".concat(imagePath,\"?t=\").concat(Date.now()):imagePath;}// Construct full URL with API base\nconst cleanPath=imagePath.startsWith('/')?imagePath:\"/\".concat(imagePath);let fullUrl=\"\".concat(API_BASE_URL).concat(cleanPath);// Add cache busting parameter if requested\nif(bustCache){fullUrl+=\"?t=\".concat(Date.now());}// Debug logging in development\nif(process.env.NODE_ENV==='development'){console.log('🖼️ Image URL constructed:',{imagePath,cleanPath,fullUrl,bustCache});}return fullUrl;};// Authentication constants - Separate keys for admin and student isolation\nexport const AUTH_TOKEN_KEY='vcba_auth_token';export const USER_DATA_KEY='vcba_user_data';export const REFRESH_TOKEN_KEY='vcba_refresh_token';// Admin-specific authentication keys\nexport const ADMIN_AUTH_TOKEN_KEY='vcba_admin_auth_token';export const ADMIN_USER_DATA_KEY='vcba_admin_user_data';export const ADMIN_REFRESH_TOKEN_KEY='vcba_admin_refresh_token';// Student-specific authentication keys\nexport const STUDENT_AUTH_TOKEN_KEY='vcba_student_auth_token';export const STUDENT_USER_DATA_KEY='vcba_student_user_data';export const STUDENT_REFRESH_TOKEN_KEY='vcba_student_refresh_token';// API endpoints\nexport const API_ENDPOINTS={AUTH:{LOGIN:'/api/auth/login',REFRESH:'/api/auth/refresh',LOGOUT:'/api/auth/logout',PROFILE:'/api/auth/me',VALIDATE_TOKEN:'/api/auth/validate-token',ADMIN_REGISTER:'/api/auth/admin/register',VERIFY_OTP:'/api/auth/admin/verify-otp',RESEND_OTP:'/api/auth/admin/resend-otp'},ADMIN:{STUDENTS:'/api/admin/students',STUDENT_BY_ID:id=>\"/api/admin/students/\".concat(id),RESET_STUDENT_PASSWORD:id=>\"/api/admin/students/\".concat(id,\"/reset-password\")},ANNOUNCEMENTS:{BASE:'/api/announcements',FEATURED:'/api/announcements/featured',BY_ID:id=>\"/api/announcements/\".concat(id),PUBLISH:id=>\"/api/announcements/\".concat(id,\"/publish\"),UNPUBLISH:id=>\"/api/announcements/\".concat(id,\"/unpublish\"),VIEW:id=>\"/api/announcements/\".concat(id,\"/view\"),LIKE:id=>\"/api/announcements/\".concat(id,\"/like\"),REACTIONS:id=>\"/api/announcements/\".concat(id,\"/reactions\"),CATEGORIES:'/api/announcements/categories',CATEGORIES_WITH_SUBCATEGORIES:'/api/announcements/categories/with-subcategories',SUBCATEGORIES:'/api/announcements/subcategories',SUBCATEGORIES_BY_CATEGORY:categoryId=>\"/api/announcements/categories/\".concat(categoryId,\"/subcategories\"),REACTION_TYPES:'/api/announcements/reaction-types'},COMMENTS:{BASE:'/api/comments',BY_ID:id=>\"/api/comments/\".concat(id),LIKE:id=>\"/api/comments/\".concat(id,\"/like\"),FLAG:id=>\"/api/comments/\".concat(id,\"/flag\"),APPROVE:id=>\"/api/comments/\".concat(id,\"/approve\"),REJECT:id=>\"/api/comments/\".concat(id,\"/reject\"),REACTIONS:id=>\"/api/comments/\".concat(id,\"/reactions\"),FLAGGED:'/api/comments/admin/flagged'},CALENDAR:{BASE:'/api/calendar',BY_ID:id=>\"/api/calendar/\".concat(id),VIEW:'/api/calendar/view',CURRENT_MONTH:'/api/calendar/current-month',UPCOMING:'/api/calendar/upcoming',DATE_RANGE:'/api/calendar/date-range',BY_DATE:date=>\"/api/calendar/date/\".concat(date)},NOTIFICATIONS:{BASE:'/api/notifications',UNREAD_COUNT:'/api/notifications/unread-count',MARK_READ:id=>\"/api/notifications/\".concat(id,\"/read\"),MARK_ALL_READ:'/api/notifications/mark-all-read',DELETE:id=>\"/api/notifications/\".concat(id)}};// Form validation constants\nexport const VALIDATION_RULES={PASSWORD:{MIN_LENGTH:8,PATTERN:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/},OTP:{LENGTH:6,PATTERN:/^\\d{6}$/},EMAIL:{PATTERN:/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/},NAME:{MAX_LENGTH:50}};// UI constants\nexport const ROUTES={HOME:'/',ADMIN:{LOGIN:'/admin/login',REGISTER:'/admin/register',DASHBOARD:'/admin/dashboard'},STUDENT:{LOGIN:'/student/login',DASHBOARD:'/student/dashboard'}};// Theme constants\nexport const BREAKPOINTS={xs:'320px',sm:'640px',md:'768px',lg:'1024px',xl:'1280px','2xl':'1536px'};// Error messages\nexport const ERROR_MESSAGES={NETWORK_ERROR:'Network error. Please check your connection and try again.',UNAUTHORIZED:'You are not authorized to access this resource.',FORBIDDEN:'Access denied.',NOT_FOUND:'The requested resource was not found.',SERVER_ERROR:'An internal server error occurred. Please try again later.',VALIDATION_ERROR:'Please check your input and try again.',INVALID_CREDENTIALS:'Invalid email or password.',ACCOUNT_LOCKED:'Your account has been temporarily locked.',ACCOUNT_INACTIVE:'Your account is inactive. Please contact support.',TOKEN_EXPIRED:'Your session has expired. Please log in again.',OTP_INVALID:'Invalid OTP. Please check and try again.',OTP_EXPIRED:'OTP has expired. Please request a new one.'};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}