{"version": 3, "file": "consistent-type-definitions.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-type-definitions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,2EAA2E;YAC7E,WAAW,EAAE,QAAQ;SACtB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,yCAAyC;YAC5D,iBAAiB,EAAE,yCAAyC;SAC7D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B;SACF;QACD,OAAO,EAAE,MAAM;KAChB;IACD,cAAc,EAAE,CAAC,WAAW,CAAC;IAC7B,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C;;;WAGG;QACH,SAAS,+CAA+C;YACtD,OAAO,OAAO;iBACX,YAAY,EAAE;iBACd,IAAI,CACH,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,OAAO;gBACZ,IAAI,CAAC,MAAM,CACd,CAAC;QACN,CAAC;QAED,uCACK,CAAC,MAAM,KAAK,WAAW,IAAI;YAC5B,6DAA6D,CAC3D,IAAqC;gBAErC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,SAAS,EAAE,mBAAmB;oBAC9B,GAAG,CAAC,KAAK;;wBACP,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,cAAc,mCAAI,IAAI,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,GAAuB,EAAE,CAAC;wBAErC,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACtD,IAAI,UAAU,EAAE;4BACd,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;4BACvD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,gBAAgB,CACpB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACjD,GAAG,CACJ,CACF,CAAC;yBACH;wBAED,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACjE,IACE,UAAU;4BACV,UAAU,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;4BAC9C,UAAU,CAAC,KAAK,KAAK,GAAG,EACxB;4BACA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;yBACtC;wBAED,OAAO,KAAK,CAAC;oBACf,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC,GACC,CAAC,MAAM,KAAK,MAAM,IAAI;YACvB,sBAAsB,CAAC,IAAI;gBACzB,MAAM,GAAG,GAAG,+CAA+C,EAAE;oBAC3D,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,CAAC,KAAyB,EAAsB,EAAE;;wBAChD,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,cAAc,mCAAI,IAAI,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,GAAuB,EAAE,CAAC;wBAErC,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACtD,IAAI,UAAU,EAAE;4BACd,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;4BAClD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,gBAAgB,CACpB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACvC,KAAK,CACN,CACF,CAAC;yBACH;wBAED,IAAI,IAAI,CAAC,OAAO,EAAE;4BAChB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gCAC9B,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gCACpD,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,cAAc,EAAE,CAAC,CACzD,CAAC;4BACJ,CAAC,CAAC,CAAC;yBACJ;wBAED,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,wBAAwB,EAC7D;4BACA,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACxD,KAAK,CAAC,eAAe,CACnB,IAAI,CAAC,IAAI,EACT,oBAAoB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CACnC,CACF,CAAC;yBACH;wBAED,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC;gBACN,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,SAAS,EAAE,mBAAmB;oBAC9B;;;uBAGG;oBACH,GAAG;iBACJ,CAAC,CAAC;YACL,CAAC;SACF,CAAC,EACF;IACJ,CAAC;CACF,CAAC,CAAC"}