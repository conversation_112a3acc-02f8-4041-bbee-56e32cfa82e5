{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v5\",\n  key: \"1w6njk\"\n}], [\"circle\", {\n  cx: \"13\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1j92g6\"\n}], [\"path\", {\n  d: \"M18 19c-2.8 0-5-2.2-5-5v8\",\n  key: \"pkpw2h\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"1obnsp\"\n}]];\nconst FolderGit2 = createLucideIcon(\"folder-git-2\", __iconNode);\nexport { __iconNode, FolderGit2 as default };\n//# sourceMappingURL=folder-git-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}