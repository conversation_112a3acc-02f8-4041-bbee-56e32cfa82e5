{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, Camera, AlertCircle, CheckCircle, Save, RotateCcw, Trash2 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard preview)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove confirmation\n  const handleRemoveConfirm = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n    setShowRemoveConfirm(false);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasCurrentImage = Boolean(currentPicture);\n  const hasPreview = Boolean(preview);\n  const hasChanges = hasPreview && selectedFile;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasCurrentImage,\n    hasPreview,\n    hasChanges\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '120px',\n            height: '120px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '3px dashed #22c55e' : hasPreview ? '3px solid #22c55e' : '3px solid #e8f5e8',\n            transition: 'all 0.2s ease',\n            cursor: hasChanges ? 'default' : 'pointer',\n            boxShadow: hasPreview ? '0 8px 25px rgba(34, 197, 94, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)'\n          },\n          onDragOver: !hasChanges ? handleDragOver : undefined,\n          onDragLeave: !hasChanges ? handleDragLeave : undefined,\n          onDrop: !hasChanges ? handleDrop : undefined,\n          onClick: !hasChanges ? () => {\n            var _fileInputRef$current;\n            return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n          } : undefined,\n          children: [displayImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: displayImage,\n            alt: \"Profile\",\n            onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n            onError: e => console.error('❌ Image failed to load:', displayImage, e),\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '100%',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '700',\n              fontSize: '2.5rem'\n            },\n            children: userInitials\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), hasPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '-8px',\n              right: '-8px',\n              background: '#22c55e',\n              color: 'white',\n              borderRadius: '50%',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '12px',\n              fontWeight: '600',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), !hasChanges && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(0, 0, 0, 0.5)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(Camera, {\n              size: 28,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(255, 255, 255, 0.9)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                border: '3px solid #e8f5e8',\n                borderTop: '3px solid #22c55e',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#2d5016',\n              fontSize: '1.1rem',\n              fontWeight: '600'\n            },\n            children: hasPreview ? 'New Profile Picture' : hasCurrentImage ? 'Current Profile Picture' : 'No Profile Picture'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              margin: '0 0 1rem 0',\n              lineHeight: '1.4'\n            },\n            children: hasPreview ? 'Preview of your new profile picture. Click Save to apply changes.' : hasCurrentImage ? 'Click \"Change Photo\" to upload a new picture or \"Remove\" to delete current one.' : 'Upload a profile picture to personalize your account. Drag and drop or click to browse.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              background: '#f9fafb',\n              padding: '0.5rem 0.75rem',\n              borderRadius: '6px',\n              border: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Requirements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), \" Max 2MB \\u2022 JPEG, PNG, WebP \\u2022 Square images work best\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [hasChanges ?\n        /*#__PURE__*/\n        /* Save/Cancel Buttons for Preview */\n        _jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            padding: '1rem',\n            background: '#f0fdf4',\n            border: '1px solid #bbf7d0',\n            borderRadius: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              flex: 1,\n              justifyContent: 'center',\n              transition: 'all 0.2s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), isLoading ? 'Saving...' : 'Save Changes']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCancel,\n            disabled: isLoading,\n            style: {\n              background: 'white',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontWeight: '500',\n              transition: 'all 0.2s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), \"Cancel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        /* Upload/Remove Buttons */\n        _jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              transition: 'all 0.2s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), hasCurrentImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), hasCurrentImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowRemoveConfirm(true),\n            disabled: isLoading,\n            style: {\n              background: 'white',\n              border: '1px solid #fca5a5',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#dc2626',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontWeight: '500',\n              transition: 'all 0.2s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Trash2, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), showRemoveConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            maxWidth: '400px',\n            width: '90%',\n            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '48px',\n                height: '48px',\n                background: '#fef2f2',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                size: 24,\n                color: \"#dc2626\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 0.5rem 0',\n                color: '#1f2937',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Remove Profile Picture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                lineHeight: '1.4'\n              },\n              children: \"Are you sure you want to remove your profile picture? This action cannot be undone.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowRemoveConfirm(false),\n              style: {\n                flex: 1,\n                background: 'white',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                padding: '0.75rem 1rem',\n                cursor: 'pointer',\n                color: '#6b7280',\n                fontWeight: '500',\n                transition: 'all 0.2s ease'\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRemoveConfirm,\n              disabled: isLoading,\n              style: {\n                flex: 1,\n                background: '#dc2626',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1rem',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                color: 'white',\n                fontWeight: '600',\n                opacity: isLoading ? 0.6 : 1,\n                transition: 'all 0.2s ease'\n              },\n              children: isLoading ? 'Removing...' : 'Remove'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"8Tu3RyHS4UDLHKJowR6hJhNAexY=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "Camera", "AlertCircle", "CheckCircle", "Save", "RotateCcw", "Trash2", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "_s", "preview", "setPreview", "selectedFile", "setSelectedFile", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showRemoveConfirm", "setShowRemoveConfirm", "fileInputRef", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "size", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "handleSave", "setTimeout", "err", "message", "handleCancel", "current", "value", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemoveConfirm", "displayImage", "hasCurrentImage", "Boolean", "hasPreview", "has<PERSON><PERSON><PERSON>", "console", "log", "children", "style", "display", "flexDirection", "gap", "alignItems", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "boxShadow", "onDragOver", "undefined", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "click", "src", "alt", "onLoad", "onError", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "justifyContent", "color", "fontWeight", "fontSize", "top", "right", "left", "bottom", "opacity", "borderTop", "animation", "flex", "margin", "lineHeight", "padding", "disabled", "_fileInputRef$current2", "ref", "accept", "onChange", "zIndex", "max<PERSON><PERSON><PERSON>", "textAlign", "marginBottom", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Save, RotateCcw, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard preview)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove confirmation\n  const handleRemoveConfirm = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n    setShowRemoveConfirm(false);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasCurrentImage = Boolean(currentPicture);\n  const hasPreview = Boolean(preview);\n  const hasChanges = hasPreview && selectedFile;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasCurrentImage,\n    hasPreview,\n    hasChanges\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        {/* Profile Picture Display Section */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Profile Picture Display */}\n          <div\n            style={{\n              position: 'relative',\n              width: '120px',\n              height: '120px',\n              borderRadius: '50%',\n              overflow: 'hidden',\n              border: isDragOver ? '3px dashed #22c55e' : hasPreview ? '3px solid #22c55e' : '3px solid #e8f5e8',\n              transition: 'all 0.2s ease',\n              cursor: hasChanges ? 'default' : 'pointer',\n              boxShadow: hasPreview ? '0 8px 25px rgba(34, 197, 94, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)'\n            }}\n            onDragOver={!hasChanges ? handleDragOver : undefined}\n            onDragLeave={!hasChanges ? handleDragLeave : undefined}\n            onDrop={!hasChanges ? handleDrop : undefined}\n            onClick={!hasChanges ? () => fileInputRef.current?.click() : undefined}\n          >\n            {displayImage ? (\n              <img\n                src={displayImage}\n                alt=\"Profile\"\n                onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n                onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n              />\n            ) : (\n              <div\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontWeight: '700',\n                  fontSize: '2.5rem'\n                }}\n              >\n                {userInitials}\n              </div>\n            )}\n\n            {/* Preview Badge */}\n            {hasPreview && (\n              <div\n                style={{\n                  position: 'absolute',\n                  top: '-8px',\n                  right: '-8px',\n                  background: '#22c55e',\n                  color: 'white',\n                  borderRadius: '50%',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n                }}\n              >\n                ✓\n              </div>\n            )}\n\n            {/* Upload Overlay */}\n            {!hasChanges && (\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  bottom: 0,\n                  background: 'rgba(0, 0, 0, 0.5)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  opacity: isDragOver ? 1 : 0,\n                  transition: 'opacity 0.2s ease'\n                }}\n              >\n                <Camera size={28} color=\"white\" />\n              </div>\n            )}\n\n            {/* Loading Overlay */}\n            {isLoading && (\n              <div\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  bottom: 0,\n                  background: 'rgba(255, 255, 255, 0.9)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}\n              >\n                <div\n                  style={{\n                    width: '32px',\n                    height: '32px',\n                    border: '3px solid #e8f5e8',\n                    borderTop: '3px solid #22c55e',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Picture Info */}\n          <div style={{ flex: 1 }}>\n            <h4 style={{\n              margin: '0 0 0.5rem 0',\n              color: '#2d5016',\n              fontSize: '1.1rem',\n              fontWeight: '600'\n            }}>\n              {hasPreview ? 'New Profile Picture' : hasCurrentImage ? 'Current Profile Picture' : 'No Profile Picture'}\n            </h4>\n            <p style={{\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              margin: '0 0 1rem 0',\n              lineHeight: '1.4'\n            }}>\n              {hasPreview\n                ? 'Preview of your new profile picture. Click Save to apply changes.'\n                : hasCurrentImage\n                ? 'Click \"Change Photo\" to upload a new picture or \"Remove\" to delete current one.'\n                : 'Upload a profile picture to personalize your account. Drag and drop or click to browse.'\n              }\n            </p>\n\n            {/* File Requirements */}\n            <div style={{\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              background: '#f9fafb',\n              padding: '0.5rem 0.75rem',\n              borderRadius: '6px',\n              border: '1px solid #e5e7eb'\n            }}>\n              <strong>Requirements:</strong> Max 2MB • JPEG, PNG, WebP • Square images work best\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          {hasChanges ? (\n            /* Save/Cancel Buttons for Preview */\n            <div style={{\n              display: 'flex',\n              gap: '1rem',\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '12px'\n            }}>\n              <button\n                onClick={handleSave}\n                disabled={isLoading}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  flex: 1,\n                  justifyContent: 'center',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <Save size={16} />\n                {isLoading ? 'Saving...' : 'Save Changes'}\n              </button>\n\n              <button\n                onClick={handleCancel}\n                disabled={isLoading}\n                style={{\n                  background: 'white',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  fontWeight: '500',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <RotateCcw size={16} />\n                Cancel\n              </button>\n            </div>\n          ) : (\n            /* Upload/Remove Buttons */\n            <div style={{ display: 'flex', gap: '1rem' }}>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                disabled={isLoading}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <Upload size={16} />\n                {hasCurrentImage ? 'Change Photo' : 'Upload Photo'}\n              </button>\n\n              {hasCurrentImage && (\n                <button\n                  onClick={() => setShowRemoveConfirm(true)}\n                  disabled={isLoading}\n                  style={{\n                    background: 'white',\n                    border: '1px solid #fca5a5',\n                    borderRadius: '8px',\n                    padding: '0.75rem 1.5rem',\n                    cursor: isLoading ? 'not-allowed' : 'pointer',\n                    color: '#dc2626',\n                    opacity: isLoading ? 0.6 : 1,\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    fontWeight: '500',\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  <Trash2 size={16} />\n                  Remove\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n        </div>\n\n        {/* Remove Confirmation Modal */}\n        {showRemoveConfirm && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 1000\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              padding: '2rem',\n              maxWidth: '400px',\n              width: '90%',\n              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n            }}>\n              <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>\n                <div style={{\n                  width: '48px',\n                  height: '48px',\n                  background: '#fef2f2',\n                  borderRadius: '50%',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  margin: '0 auto 1rem auto'\n                }}>\n                  <Trash2 size={24} color=\"#dc2626\" />\n                </div>\n                <h3 style={{ margin: '0 0 0.5rem 0', color: '#1f2937', fontSize: '1.25rem', fontWeight: '600' }}>\n                  Remove Profile Picture\n                </h3>\n                <p style={{ margin: 0, color: '#6b7280', fontSize: '0.875rem', lineHeight: '1.4' }}>\n                  Are you sure you want to remove your profile picture? This action cannot be undone.\n                </p>\n              </div>\n\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button\n                  onClick={() => setShowRemoveConfirm(false)}\n                  style={{\n                    flex: 1,\n                    background: 'white',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    padding: '0.75rem 1rem',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    fontWeight: '500',\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleRemoveConfirm}\n                  disabled={isLoading}\n                  style={{\n                    flex: 1,\n                    background: '#dc2626',\n                    border: 'none',\n                    borderRadius: '8px',\n                    padding: '0.75rem 1rem',\n                    cursor: isLoading ? 'not-allowed' : 'pointer',\n                    color: 'white',\n                    fontWeight: '600',\n                    opacity: isLoading ? 0.6 : 1,\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  {isLoading ? 'Removing...' : 'Remove'}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAKC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpG,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMgC,YAAY,GAAG/B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAMgC,YAAY,GAAG/B,WAAW,CAAEgC,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,gBAAgB,GAAGtC,WAAW,CAAEgC,IAAU,IAAK;IACnDP,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMY,eAAe,GAAGR,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIO,eAAe,EAAE;MACnBd,QAAQ,CAACc,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBzB,UAAU,EAAAyB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;MACtCzB,eAAe,CAACW,IAAI,CAAC;IACvB,CAAC;IACDQ,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBtB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDe,MAAM,CAACQ,aAAa,CAAChB,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkB,UAAU,GAAGjD,WAAW,CAAC,YAAY;IACzC,IAAI,CAACoB,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMP,QAAQ,CAACO,YAAY,CAAC;MAC5BO,UAAU,CAAC,uCAAuC,CAAC;MACnDN,eAAe,CAAC,IAAI,CAAC;MACrB6B,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChC,YAAY,EAAEP,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAGrD,WAAW,CAAC,MAAM;IACrCmB,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAIK,YAAY,CAACwB,OAAO,EAAE;MACxBxB,YAAY,CAACwB,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAIb,CAAsC,IAAK;IAAA,IAAAc,eAAA;IACpE,MAAMzB,IAAI,IAAAyB,eAAA,GAAGd,CAAC,CAACE,MAAM,CAACa,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIzB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAIhB,CAAkB,IAAK;IAC7CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMsC,eAAe,GAAIlB,CAAkB,IAAK;IAC9CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuC,UAAU,GAAInB,CAAkB,IAAK;IACzCA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMS,IAAI,GAAGW,CAAC,CAACoB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAI1B,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCvC,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBR,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBQ,oBAAoB,CAAC,KAAK,CAAC;IAE3B,IAAI;MACF,MAAMf,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDuB,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMa,YAAY,GAAG/C,OAAO,IAAIP,cAAc;EAC9C,MAAMuD,eAAe,GAAGC,OAAO,CAACxD,cAAc,CAAC;EAC/C,MAAMyD,UAAU,GAAGD,OAAO,CAACjD,OAAO,CAAC;EACnC,MAAMmD,UAAU,GAAGD,UAAU,IAAIhD,YAAY;;EAE7C;EACAkD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C5D,cAAc;IACdO,OAAO;IACP+C,YAAY;IACZC,eAAe;IACfE,UAAU;IACVC;EACF,CAAC,CAAC;EAEF,oBACE5D,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAwD,QAAA,gBACpD/D,OAAA;MAAKgE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAJ,QAAA,gBAEtE/D,OAAA;QAAKgE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEG,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAEjE/D,OAAA;UACEgE,KAAK,EAAE;YACLK,QAAQ,EAAE,UAAU;YACpBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfC,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,QAAQ;YAClBC,MAAM,EAAE7D,UAAU,GAAG,oBAAoB,GAAG8C,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;YAClGgB,UAAU,EAAE,eAAe;YAC3BC,MAAM,EAAEhB,UAAU,GAAG,SAAS,GAAG,SAAS;YAC1CiB,SAAS,EAAElB,UAAU,GAAG,oCAAoC,GAAG;UACjE,CAAE;UACFmB,UAAU,EAAE,CAAClB,UAAU,GAAGV,cAAc,GAAG6B,SAAU;UACrDC,WAAW,EAAE,CAACpB,UAAU,GAAGR,eAAe,GAAG2B,SAAU;UACvDE,MAAM,EAAE,CAACrB,UAAU,GAAGP,UAAU,GAAG0B,SAAU;UAC7CG,OAAO,EAAE,CAACtB,UAAU,GAAG;YAAA,IAAAuB,qBAAA;YAAA,QAAAA,qBAAA,GAAM9D,YAAY,CAACwB,OAAO,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;UAAA,IAAGL,SAAU;UAAAhB,QAAA,GAEtEP,YAAY,gBACXxD,OAAA;YACEqF,GAAG,EAAE7B,YAAa;YAClB8B,GAAG,EAAC,SAAS;YACbC,MAAM,EAAEA,CAAA,KAAM1B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,YAAY,CAAE;YACxEgC,OAAO,EAAGtD,CAAC,IAAK2B,OAAO,CAAC9C,KAAK,CAAC,yBAAyB,EAAEyC,YAAY,EAAEtB,CAAC,CAAE;YAC1E8B,KAAK,EAAE;cACLM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdkB,SAAS,EAAE;YACb;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF7F,OAAA;YACEgE,KAAK,EAAE;cACLM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACduB,UAAU,EAAE,mDAAmD;cAC/D7B,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpB2B,cAAc,EAAE,QAAQ;cACxBC,KAAK,EAAE,OAAO;cACdC,UAAU,EAAE,KAAK;cACjBC,QAAQ,EAAE;YACZ,CAAE;YAAAnC,QAAA,EAED5D;UAAY;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EAGAlC,UAAU,iBACT3D,OAAA;YACEgE,KAAK,EAAE;cACLK,QAAQ,EAAE,UAAU;cACpB8B,GAAG,EAAE,MAAM;cACXC,KAAK,EAAE,MAAM;cACbN,UAAU,EAAE,SAAS;cACrBE,KAAK,EAAE,OAAO;cACdxB,YAAY,EAAE,KAAK;cACnBF,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdN,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpB2B,cAAc,EAAE,QAAQ;cACxBG,QAAQ,EAAE,MAAM;cAChBD,UAAU,EAAE,KAAK;cACjBpB,SAAS,EAAE;YACb,CAAE;YAAAd,QAAA,EACH;UAED;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGA,CAACjC,UAAU,iBACV5D,OAAA;YACEgE,KAAK,EAAE;cACLK,QAAQ,EAAE,UAAU;cACpB8B,GAAG,EAAE,CAAC;cACNE,IAAI,EAAE,CAAC;cACPD,KAAK,EAAE,CAAC;cACRE,MAAM,EAAE,CAAC;cACTR,UAAU,EAAE,oBAAoB;cAChC7B,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpB2B,cAAc,EAAE,QAAQ;cACxBQ,OAAO,EAAE1F,UAAU,GAAG,CAAC,GAAG,CAAC;cAC3B8D,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,eAEF/D,OAAA,CAACP,MAAM;cAACmC,IAAI,EAAE,EAAG;cAACoE,KAAK,EAAC;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CACN,EAGAvF,SAAS,iBACRN,OAAA;YACEgE,KAAK,EAAE;cACLK,QAAQ,EAAE,UAAU;cACpB8B,GAAG,EAAE,CAAC;cACNE,IAAI,EAAE,CAAC;cACPD,KAAK,EAAE,CAAC;cACRE,MAAM,EAAE,CAAC;cACTR,UAAU,EAAE,0BAA0B;cACtC7B,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpB2B,cAAc,EAAE;YAClB,CAAE;YAAAhC,QAAA,eAEF/D,OAAA;cACEgE,KAAK,EAAE;gBACLM,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdG,MAAM,EAAE,mBAAmB;gBAC3B8B,SAAS,EAAE,mBAAmB;gBAC9BhC,YAAY,EAAE,KAAK;gBACnBiC,SAAS,EAAE;cACb;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7F,OAAA;UAAKgE,KAAK,EAAE;YAAE0C,IAAI,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBACtB/D,OAAA;YAAIgE,KAAK,EAAE;cACT2C,MAAM,EAAE,cAAc;cACtBX,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE,QAAQ;cAClBD,UAAU,EAAE;YACd,CAAE;YAAAlC,QAAA,EACCJ,UAAU,GAAG,qBAAqB,GAAGF,eAAe,GAAG,yBAAyB,GAAG;UAAoB;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eACL7F,OAAA;YAAGgE,KAAK,EAAE;cACRkC,QAAQ,EAAE,UAAU;cACpBF,KAAK,EAAE,SAAS;cAChBW,MAAM,EAAE,YAAY;cACpBC,UAAU,EAAE;YACd,CAAE;YAAA7C,QAAA,EACCJ,UAAU,GACP,mEAAmE,GACnEF,eAAe,GACf,iFAAiF,GACjF;UAAyF;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5F,CAAC,eAGJ7F,OAAA;YAAKgE,KAAK,EAAE;cACVkC,QAAQ,EAAE,SAAS;cACnBF,KAAK,EAAE,SAAS;cAChBF,UAAU,EAAE,SAAS;cACrBe,OAAO,EAAE,gBAAgB;cACzBrC,YAAY,EAAE,KAAK;cACnBE,MAAM,EAAE;YACV,CAAE;YAAAX,QAAA,gBACA/D,OAAA;cAAA+D,QAAA,EAAQ;YAAa;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kEAChC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA;QAAKgE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,GACnEH,UAAU;QAAA;QACT;QACA5D,OAAA;UAAKgE,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfE,GAAG,EAAE,MAAM;YACX0C,OAAO,EAAE,MAAM;YACff,UAAU,EAAE,SAAS;YACrBpB,MAAM,EAAE,mBAAmB;YAC3BF,YAAY,EAAE;UAChB,CAAE;UAAAT,QAAA,gBACA/D,OAAA;YACEkF,OAAO,EAAE1C,UAAW;YACpBsE,QAAQ,EAAExG,SAAU;YACpB0D,KAAK,EAAE;cACL8B,UAAU,EAAE,mDAAmD;cAC/DE,KAAK,EAAE,OAAO;cACdtB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBqC,OAAO,EAAE,gBAAgB;cACzBZ,UAAU,EAAE,KAAK;cACjBrB,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CiG,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,QAAQ;cACbuC,IAAI,EAAE,CAAC;cACPX,cAAc,EAAE,QAAQ;cACxBpB,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,gBAEF/D,OAAA,CAACJ,IAAI;cAACgC,IAAI,EAAE;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACjBvF,SAAS,GAAG,WAAW,GAAG,cAAc;UAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAET7F,OAAA;YACEkF,OAAO,EAAEtC,YAAa;YACtBkE,QAAQ,EAAExG,SAAU;YACpB0D,KAAK,EAAE;cACL8B,UAAU,EAAE,OAAO;cACnBpB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBqC,OAAO,EAAE,gBAAgB;cACzBjC,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C0F,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,QAAQ;cACb8B,UAAU,EAAE,KAAK;cACjBtB,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,gBAEF/D,OAAA,CAACH,SAAS;cAAC+B,IAAI,EAAE;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;QAAA;QAEN;QACA7F,OAAA;UAAKgE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAC3C/D,OAAA;YACEkF,OAAO,EAAEA,CAAA;cAAA,IAAA6B,sBAAA;cAAA,QAAAA,sBAAA,GAAM1F,YAAY,CAACwB,OAAO,cAAAkE,sBAAA,uBAApBA,sBAAA,CAAsB3B,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7C0B,QAAQ,EAAExG,SAAU;YACpB0D,KAAK,EAAE;cACL8B,UAAU,EAAE,mDAAmD;cAC/DE,KAAK,EAAE,OAAO;cACdtB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBqC,OAAO,EAAE,gBAAgB;cACzBZ,UAAU,EAAE,KAAK;cACjBrB,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CiG,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,QAAQ;cACbQ,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,gBAEF/D,OAAA,CAACR,MAAM;cAACoC,IAAI,EAAE;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBpC,eAAe,GAAG,cAAc,GAAG,cAAc;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,EAERpC,eAAe,iBACdzD,OAAA;YACEkF,OAAO,EAAEA,CAAA,KAAM9D,oBAAoB,CAAC,IAAI,CAAE;YAC1C0F,QAAQ,EAAExG,SAAU;YACpB0D,KAAK,EAAE;cACL8B,UAAU,EAAE,OAAO;cACnBpB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBqC,OAAO,EAAE,gBAAgB;cACzBjC,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C0F,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,QAAQ;cACb8B,UAAU,EAAE,KAAK;cACjBtB,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,gBAEF/D,OAAA,CAACF,MAAM;cAAC8B,IAAI,EAAE;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGD7F,OAAA;UACEgH,GAAG,EAAE3F,YAAa;UAClBM,IAAI,EAAC,MAAM;UACXsF,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAEnE,iBAAkB;UAC5BiB,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL1E,iBAAiB,iBAChBnB,OAAA;QAAKgE,KAAK,EAAE;UACVK,QAAQ,EAAE,OAAO;UACjB8B,GAAG,EAAE,CAAC;UACNE,IAAI,EAAE,CAAC;UACPD,KAAK,EAAE,CAAC;UACRE,MAAM,EAAE,CAAC;UACTR,UAAU,EAAE,oBAAoB;UAChC7B,OAAO,EAAE,MAAM;UACfG,UAAU,EAAE,QAAQ;UACpB2B,cAAc,EAAE,QAAQ;UACxBoB,MAAM,EAAE;QACV,CAAE;QAAApD,QAAA,eACA/D,OAAA;UAAKgE,KAAK,EAAE;YACV8B,UAAU,EAAE,OAAO;YACnBtB,YAAY,EAAE,MAAM;YACpBqC,OAAO,EAAE,MAAM;YACfO,QAAQ,EAAE,OAAO;YACjB9C,KAAK,EAAE,KAAK;YACZO,SAAS,EAAE;UACb,CAAE;UAAAd,QAAA,gBACA/D,OAAA;YAAKgE,KAAK,EAAE;cAAEqD,SAAS,EAAE,QAAQ;cAAEC,YAAY,EAAE;YAAS,CAAE;YAAAvD,QAAA,gBAC1D/D,OAAA;cAAKgE,KAAK,EAAE;gBACVM,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACduB,UAAU,EAAE,SAAS;gBACrBtB,YAAY,EAAE,KAAK;gBACnBP,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpB2B,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE;cACV,CAAE;cAAA5C,QAAA,eACA/D,OAAA,CAACF,MAAM;gBAAC8B,IAAI,EAAE,EAAG;gBAACoE,KAAK,EAAC;cAAS;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN7F,OAAA;cAAIgE,KAAK,EAAE;gBAAE2C,MAAM,EAAE,cAAc;gBAAEX,KAAK,EAAE,SAAS;gBAAEE,QAAQ,EAAE,SAAS;gBAAED,UAAU,EAAE;cAAM,CAAE;cAAAlC,QAAA,EAAC;YAEjG;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7F,OAAA;cAAGgE,KAAK,EAAE;gBAAE2C,MAAM,EAAE,CAAC;gBAAEX,KAAK,EAAE,SAAS;gBAAEE,QAAQ,EAAE,UAAU;gBAAEU,UAAU,EAAE;cAAM,CAAE;cAAA7C,QAAA,EAAC;YAEpF;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN7F,OAAA;YAAKgE,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE;YAAO,CAAE;YAAAJ,QAAA,gBAC3C/D,OAAA;cACEkF,OAAO,EAAEA,CAAA,KAAM9D,oBAAoB,CAAC,KAAK,CAAE;cAC3C4C,KAAK,EAAE;gBACL0C,IAAI,EAAE,CAAC;gBACPZ,UAAU,EAAE,OAAO;gBACnBpB,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBqC,OAAO,EAAE,cAAc;gBACvBjC,MAAM,EAAE,SAAS;gBACjBoB,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBtB,UAAU,EAAE;cACd,CAAE;cAAAZ,QAAA,EACH;YAED;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7F,OAAA;cACEkF,OAAO,EAAE3B,mBAAoB;cAC7BuD,QAAQ,EAAExG,SAAU;cACpB0D,KAAK,EAAE;gBACL0C,IAAI,EAAE,CAAC;gBACPZ,UAAU,EAAE,SAAS;gBACrBpB,MAAM,EAAE,MAAM;gBACdF,YAAY,EAAE,KAAK;gBACnBqC,OAAO,EAAE,cAAc;gBACvBjC,MAAM,EAAEtE,SAAS,GAAG,aAAa,GAAG,SAAS;gBAC7C0F,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAE,KAAK;gBACjBM,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;gBAC5BqE,UAAU,EAAE;cACd,CAAE;cAAAZ,QAAA,EAEDzD,SAAS,GAAG,aAAa,GAAG;YAAQ;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL9E,KAAK,iBACJf,OAAA;MAAKgE,KAAK,EAAE;QACVuD,SAAS,EAAE,MAAM;QACjBV,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBwB,KAAK,EAAE,SAAS;QAChB/B,OAAO,EAAE,MAAM;QACfG,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACA/D,OAAA,CAACN,WAAW;QAACkC,IAAI,EAAE;MAAG;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB9E,KAAK;IAAA;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5E,OAAO,iBACNjB,OAAA;MAAKgE,KAAK,EAAE;QACVuD,SAAS,EAAE,MAAM;QACjBV,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBpB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBwB,KAAK,EAAE,SAAS;QAChB/B,OAAO,EAAE,MAAM;QACfG,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACA/D,OAAA,CAACL,WAAW;QAACiC,IAAI,EAAE;MAAG;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB5E,OAAO;IAAA;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD7F,OAAA;MAAA+D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACrF,EAAA,CAziBIP,oBAAyD;AAAAuH,EAAA,GAAzDvH,oBAAyD;AA2iB/D,eAAeA,oBAAoB;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}