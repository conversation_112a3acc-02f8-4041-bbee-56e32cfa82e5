{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13 13.5 2-2.5-2-2.5\",\n  key: \"1rvxrh\"\n}], [\"path\", {\n  d: \"m21 21-4.3-4.3\",\n  key: \"1qie3q\"\n}], [\"path\", {\n  d: \"M9 8.5 7 11l2 2.5\",\n  key: \"6ffwbx\"\n}], [\"circle\", {\n  cx: \"11\",\n  cy: \"11\",\n  r: \"8\",\n  key: \"4ej97u\"\n}]];\nconst SearchCode = createLucideIcon(\"search-code\", __iconNode);\nexport { __iconNode, SearchCode as default };\n//# sourceMappingURL=search-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}