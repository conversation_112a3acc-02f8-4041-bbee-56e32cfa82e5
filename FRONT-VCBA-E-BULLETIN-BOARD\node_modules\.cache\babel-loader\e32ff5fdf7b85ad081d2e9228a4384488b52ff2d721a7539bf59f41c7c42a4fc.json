{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 12a1 1 0 0 1-10 0 1 1 0 0 0-10 0\",\n  key: \"1lzz15\"\n}], [\"path\", {\n  d: \"M7 20.7a1 1 0 1 1 5-8.7 1 1 0 1 0 5-8.6\",\n  key: \"1gnrpi\"\n}], [\"path\", {\n  d: \"M7 3.3a1 1 0 1 1 5 8.6 1 1 0 1 0 5 8.6\",\n  key: \"u9yy5q\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst LoaderPinwheel = createLucideIcon(\"loader-pinwheel\", __iconNode);\nexport { __iconNode, LoaderPinwheel as default };\n//# sourceMappingURL=loader-pinwheel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}