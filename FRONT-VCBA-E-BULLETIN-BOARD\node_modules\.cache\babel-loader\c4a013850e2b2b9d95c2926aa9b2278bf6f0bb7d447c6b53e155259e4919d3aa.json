{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14 15-5 5-5-5\",\n  key: \"1eia93\"\n}], [\"path\", {\n  d: \"M20 4h-7a4 4 0 0 0-4 4v12\",\n  key: \"nbpdq2\"\n}]];\nconst CornerLeftDown = createLucideIcon(\"corner-left-down\", __iconNode);\nexport { __iconNode, CornerLeftDown as default };\n//# sourceMappingURL=corner-left-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}