{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.5 21 14 3\",\n  key: \"1szst5\"\n}], [\"path\", {\n  d: \"M20.5 21 10 3\",\n  key: \"1310c3\"\n}], [\"path\", {\n  d: \"M15.5 21 12 15l-3.5 6\",\n  key: \"1ddtfw\"\n}], [\"path\", {\n  d: \"M2 21h20\",\n  key: \"1nyx9w\"\n}]];\nconst Tent = createLucideIcon(\"tent\", __iconNode);\nexport { __iconNode, Tent as default };\n//# sourceMappingURL=tent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}