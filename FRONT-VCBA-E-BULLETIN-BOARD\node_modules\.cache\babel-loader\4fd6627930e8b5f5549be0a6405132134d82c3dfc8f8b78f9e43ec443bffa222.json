{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\UnifiedNewsfeed.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport UnifiedCommentSection from './UnifiedCommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart, Edit, Users, AlertTriangle, ChevronDown, User, LogOut } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UnifiedNewsfeed = ({\n  forceRole\n}) => {\n  _s();\n  var _currentAuth$user3, _currentAuth$user4;\n  const navigate = useNavigate();\n\n  // Detect user context\n  const userContext = useMemo(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return {\n        ...context,\n        role: forceRole\n      };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Get appropriate auth contexts\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine which auth context to use\n  const currentAuth = useMemo(() => {\n    if (userContext.role === 'admin') {\n      return {\n        user: adminAuth.user,\n        logout: adminAuth.logout,\n        userType: 'admin'\n      };\n    } else if (userContext.role === 'student') {\n      return {\n        user: studentAuth.user,\n        logout: studentAuth.logout,\n        userType: 'student'\n      };\n    }\n    return null;\n  }, [userContext.role, adminAuth, studentAuth]);\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!userContext.isAuthenticated || !currentAuth) {\n      const loginPath = userContext.role === 'student' ? '/student/login' : '/admin/login';\n      navigate(loginPath);\n      return;\n    }\n  }, [userContext.isAuthenticated, currentAuth, navigate, userContext.role]);\n\n  // UI States\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [showFilters, setShowFilters] = useState(false);\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [recentStudents, setRecentStudents] = useState([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n  const {\n    categories\n  } = useCategories();\n\n  // Use the announcements hook with role-appropriate service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, userContext.role === 'admin'); // true for admin service, false for student\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    notificationId,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const tokenKey = userContext.role === 'admin' ? ADMIN_AUTH_TOKEN_KEY : STUDENT_AUTH_TOKEN_KEY;\n      const response = await fetch(`${API_BASE_URL}/api/calendar/events`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to fetch calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent students (admin only)\n  const fetchRecentStudents = async () => {\n    if (userContext.role !== 'admin') return;\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    if (userContext.role === 'admin') {\n      // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n    }\n  }, [userContext.role]);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async announcement => {\n    try {\n      var _currentAuth$user;\n      console.log(`[DEBUG] ${userContext.role} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${userContext.role} user context:`, {\n        id: currentAuth === null || currentAuth === void 0 ? void 0 : (_currentAuth$user = currentAuth.user) === null || _currentAuth$user === void 0 ? void 0 : _currentAuth$user.id,\n        role: userContext.role\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${userContext.role} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${userContext.role} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log(`[SUCCESS] ${userContext.role} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${userContext.role} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      var _currentAuth$user2;\n      console.log(`[DEBUG] ${userContext.role} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${userContext.role} user context:`, {\n        id: currentAuth === null || currentAuth === void 0 ? void 0 : (_currentAuth$user2 = currentAuth.user) === null || _currentAuth$user2 === void 0 ? void 0 : _currentAuth$user2.id,\n        role: userContext.role\n      });\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: !e.user_has_reacted,\n          total_reactions: e.user_has_reacted ? (e.total_reactions || 1) - 1 : (e.total_reactions || 0) + 1\n        } : e));\n        console.log(`[SUCCESS] ${userContext.role} calendar reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${userContext.role} error toggling calendar like:`, error);\n    }\n  };\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Don't render if not authenticated\n  if (!userContext.isAuthenticated || !currentAuth) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '2rem',\n      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem',\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        backdropFilter: 'blur(10px)',\n        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '48px',\n            height: '48px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '12px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Newspaper, {\n            size: 24,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              fontSize: '1.875rem',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text'\n            },\n            children: userContext.role === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: userContext.role === 'admin' ? 'Monitor and manage announcements & events' : 'Stay updated with latest announcements & events'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem'\n        },\n        children: [userContext.role === 'admin' ? /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            padding: '0.5rem 1rem',\n            background: 'rgba(103, 126, 234, 0.1)',\n            borderRadius: '12px',\n            border: '1px solid rgba(103, 126, 234, 0.2)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(User, {\n              size: 16,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151'\n              },\n              children: [(_currentAuth$user3 = currentAuth.user) === null || _currentAuth$user3 === void 0 ? void 0 : _currentAuth$user3.first_name, \" \", (_currentAuth$user4 = currentAuth.user) === null || _currentAuth$user4 === void 0 ? void 0 : _currentAuth$user4.last_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                textTransform: 'capitalize'\n              },\n              children: userContext.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: currentAuth.logout,\n            style: {\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#6b7280',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n              e.currentTarget.style.color = '#ef4444';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.background = 'none';\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: /*#__PURE__*/_jsxDEV(LogOut, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        backdropFilter: 'blur(10px)',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          marginBottom: showFilters ? '1.5rem' : '0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          size: 20,\n          style: {\n            position: 'absolute',\n            left: '1rem',\n            top: '50%',\n            transform: 'translateY(-50%)',\n            color: '#9ca3af'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search announcements and events...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          style: {\n            width: '100%',\n            padding: '0.875rem 1rem 0.875rem 3rem',\n            border: '2px solid #e5e7eb',\n            borderRadius: '12px',\n            fontSize: '1rem',\n            outline: 'none',\n            transition: 'all 0.2s ease',\n            background: 'white'\n          },\n          onFocus: e => {\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.boxShadow = '0 0 0 3px rgba(103, 126, 234, 0.1)';\n          },\n          onBlur: e => {\n            e.currentTarget.style.borderColor = '#e5e7eb';\n            e.currentTarget.style.boxShadow = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowFilters(!showFilters),\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.5rem 1rem',\n            background: showFilters ? '#667eea' : 'transparent',\n            color: showFilters ? 'white' : '#667eea',\n            border: `2px solid #667eea`,\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            transition: 'all 0.2s ease'\n          },\n          children: [\"Filters\", /*#__PURE__*/_jsxDEV(ChevronDown, {\n            size: 16,\n            style: {\n              transform: showFilters ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1.5rem',\n          padding: '1.5rem',\n          background: '#f9fafb',\n          borderRadius: '12px',\n          border: '1px solid #e5e7eb'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '1rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                background: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.category_id.toString(),\n                children: category.category_name\n              }, category.category_id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this), (loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        padding: '3rem',\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '16px',\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #667eea',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 9\n    }, this), (error || calendarError) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(239, 68, 68, 0.1)',\n        border: '1px solid rgba(239, 68, 68, 0.2)',\n        borderRadius: '12px',\n        padding: '1rem',\n        marginBottom: '2rem',\n        color: '#dc2626'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0.5rem 0 0 0',\n          fontSize: '0.875rem'\n        },\n        children: error || calendarError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 9\n    }, this), !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: userContext.role === 'admin' ? '1fr 300px' : '1fr',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1.5rem'\n        },\n        children: [pinnedAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(255, 255, 255, 0.95)',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            border: '2px solid #fbbf24',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(251, 191, 36, 0.2)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              color: \"#f59e0b\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.125rem',\n                fontWeight: '600',\n                color: '#f59e0b'\n              },\n              children: \"Pinned Announcements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 17\n          }, this), pinnedAnnouncements.map(announcement => {\n            var _announcement$content, _announcement$content2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem',\n                background: '#fffbeb',\n                borderRadius: '12px',\n                border: '1px solid #fde68a',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  margin: '0 0 0.5rem 0',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  color: '#92400e'\n                },\n                children: announcement.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#a16207',\n                  lineHeight: '1.5'\n                },\n                children: [(_announcement$content = announcement.content) === null || _announcement$content === void 0 ? void 0 : _announcement$content.substring(0, 150), ((_announcement$content2 = announcement.content) === null || _announcement$content2 === void 0 ? void 0 : _announcement$content2.length) > 150 && '...']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this)]\n            }, `pinned-${announcement.announcement_id}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 15\n        }, this), announcements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: announcements.filter(ann => ann.is_pinned !== 1) // Exclude pinned announcements\n          .filter(ann => {\n            var _ann$category_id;\n            const matchesSearch = !searchTerm || ann.title.toLowerCase().includes(searchTerm.toLowerCase()) || ann.content.toLowerCase().includes(searchTerm.toLowerCase());\n            const matchesCategory = selectedCategory === 'all' || ((_ann$category_id = ann.category_id) === null || _ann$category_id === void 0 ? void 0 : _ann$category_id.toString()) === selectedCategory;\n            return matchesSearch && matchesCategory;\n          }).map(announcement => {\n            var _currentAuth$user5;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                borderRadius: '16px',\n                padding: '1.5rem',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)',\n                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-2px)';\n                e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: '1rem',\n                  marginBottom: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '48px',\n                    height: '48px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexShrink: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                    size: 24,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1,\n                    minWidth: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.5rem 0',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      color: '#111827',\n                      lineHeight: '1.3'\n                    },\n                    children: announcement.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.875rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\uD83D\\uDCE2 Announcement\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 29\n                    }, this), announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: '#e0e7ff',\n                        color: '#3730a3',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: announcement.category_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem',\n                  padding: '1rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#374151',\n                    lineHeight: '1.6',\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 23\n              }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: `Announcement: ${announcement.title}`,\n                  onImageClick: index => openLightbox(announcement.attachments, index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  paddingTop: '1rem',\n                  borderTop: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleLikeToggle(announcement),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: announcement.user_reaction ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 819,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      children: announcement.total_reactions || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 823,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      children: announcement.total_comments || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.75rem',\n                    color: '#9ca3af'\n                  },\n                  children: new Date(announcement.created_at).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(UnifiedCommentSection, {\n                  announcementId: announcement.announcement_id,\n                  currentUserId: currentAuth === null || currentAuth === void 0 ? void 0 : (_currentAuth$user5 = currentAuth.user) === null || _currentAuth$user5 === void 0 ? void 0 : _currentAuth$user5.id,\n                  currentUserType: userContext.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 23\n              }, this)]\n            }, `announcement-${announcement.announcement_id}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 21\n            }, this);\n          })\n        }, void 0, false), calendarEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: calendarEvents.filter(event => {\n            const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n            // Show events that are currently active (between start and end date)\n            const today = new Date();\n            const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n            const eventStartDate = event.start_date;\n            const eventEndDate = event.end_date || event.start_date;\n            const isActive = todayDateString >= eventStartDate && todayDateString <= eventEndDate;\n            return matchesSearch && isActive;\n          }).map(event => {\n            var _currentAuth$user6;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.95)',\n                borderRadius: '16px',\n                padding: '1.5rem',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)',\n                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-2px)';\n                e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: '1rem',\n                  marginBottom: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '48px',\n                    height: '48px',\n                    background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexShrink: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 24,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1,\n                    minWidth: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.5rem 0',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      color: '#111827',\n                      lineHeight: '1.3'\n                    },\n                    children: event.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.875rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"\\uD83D\\uDCC5 \", new Date(event.start_date).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 27\n                    }, this), event.end_date && event.end_date !== event.start_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"\\u2192 \", new Date(event.end_date).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 21\n              }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem',\n                  padding: '1rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#374151',\n                    lineHeight: '1.6'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 23\n              }, this), event.images && event.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                  images: event.images.map(img => getImageUrl(img.file_path)),\n                  altPrefix: `Event: ${event.title}`,\n                  onImageClick: index => {\n                    const imageUrls = event.images.map(img => getImageUrl(img.file_path));\n                    openLightboxWithUrls(imageUrls, index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  paddingTop: '1rem',\n                  borderTop: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCalendarLikeToggle(event),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: event.user_has_reacted ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      children: event.total_reactions || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1021,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.75rem',\n                    color: '#9ca3af'\n                  },\n                  children: [\"Event \\u2022 \", new Date(event.created_at).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(UnifiedCommentSection, {\n                  calendarId: event.calendar_id,\n                  currentUserId: currentAuth === null || currentAuth === void 0 ? void 0 : (_currentAuth$user6 = currentAuth.user) === null || _currentAuth$user6 === void 0 ? void 0 : _currentAuth$user6.id,\n                  currentUserType: userContext.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 21\n              }, this)]\n            }, `event-${event.calendar_id}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 11\n      }, this), userContext.role === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1.5rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(255, 255, 255, 0.95)',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.125rem',\n              fontWeight: '600',\n              color: '#111827'\n            },\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/posts'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                padding: '0.75rem',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                transition: 'transform 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Edit, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 21\n              }, this), \"Create Post\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/calendar'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                padding: '0.75rem',\n                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                transition: 'transform 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 21\n              }, this), \"Manage Calendar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/student-management'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                padding: '0.75rem',\n                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                transition: 'transform 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 21\n              }, this), \"Manage Students\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1129,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1051,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 9\n    }, this), lightboxOpen && /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      onClose: () => setLightboxOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1164,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedNewsfeed, \"MoKSXjverovxSPx6voUCNLI/KPk=\", true, function () {\n  return [useNavigate, useCategories, useAnnouncements, useNotificationTarget];\n});\n_c = UnifiedNewsfeed;\nexport default UnifiedNewsfeed;\nvar _c;\n$RefreshReg$(_c, \"UnifiedNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useNavigate", "calendarReactionService", "useCategories", "useAnnouncements", "useNotificationTarget", "UnifiedCommentSection", "NotificationBell", "StudentNotificationBell", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Edit", "Users", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UnifiedNewsfeed", "forceRole", "_s", "_currentAuth$user3", "_currentAuth$user4", "navigate", "userContext", "context", "detectUserContext", "role", "adminAuth", "useAdminAuth", "studentAuth", "useStudentAuth", "currentAuth", "user", "logout", "userType", "isAuthenticated", "loginPath", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showFilters", "setShowFilters", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "recentStudents", "setRecentStudents", "studentLoading", "setStudentLoading", "categories", "announcements", "loading", "error", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "status", "page", "limit", "sort_by", "sort_order", "isFromNotification", "notificationId", "scrollTarget", "pinned", "filter", "ann", "is_pinned", "fetchCalendarEvents", "undefined", "<PERSON><PERSON><PERSON>", "response", "fetch", "headers", "localStorage", "getItem", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "map", "event", "imageResponse", "calendar_id", "imageData", "images", "attachments", "imgErr", "console", "warn", "err", "message", "fetchRecentStudents", "students", "handleLikeToggle", "announcement", "_currentAuth$user", "log", "announcement_id", "user_reaction", "id", "handleCalendarLikeToggle", "_currentAuth$user2", "user_has_reacted", "toggleLike", "prevEvents", "e", "total_reactions", "openLightbox", "initialIndex", "imageUrls", "img", "file_path", "Boolean", "openLightboxWithUrls", "style", "minHeight", "background", "padding", "fontFamily", "children", "display", "justifyContent", "alignItems", "marginBottom", "borderRadius", "<PERSON><PERSON>ilter", "boxShadow", "gap", "width", "height", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "border", "first_name", "last_name", "textTransform", "onClick", "cursor", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "position", "left", "top", "transform", "type", "placeholder", "value", "onChange", "target", "outline", "onFocus", "borderColor", "onBlur", "marginTop", "gridTemplateColumns", "category", "category_id", "toString", "category_name", "borderTop", "animation", "flexDirection", "length", "_announcement$content", "_announcement$content2", "title", "lineHeight", "content", "substring", "_ann$category_id", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "_currentAuth$user5", "flexShrink", "flex", "min<PERSON><PERSON><PERSON>", "whiteSpace", "altPrefix", "onImageClick", "index", "paddingTop", "fill", "total_comments", "Date", "created_at", "toLocaleDateString", "announcementId", "currentUserId", "currentUserType", "description", "today", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "start_date", "eventEndDate", "end_date", "isActive", "_currentAuth$user6", "calendarId", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/UnifiedNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useUnifiedAuth } from '../../hooks/useUnifiedAuth';\nimport UnifiedCommentSection from './UnifiedCommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Edit,\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\ninterface UnifiedNewsfeedProps {\n  // Optional props to override role detection\n  forceRole?: 'admin' | 'student';\n}\n\nconst UnifiedNewsfeed: React.FC<UnifiedNewsfeedProps> = ({ forceRole }) => {\n  const navigate = useNavigate();\n\n  // Detect user context\n  const userContext = useMemo<UserContext>(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return { ...context, role: forceRole };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Get appropriate auth contexts\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine which auth context to use\n  const currentAuth = useMemo(() => {\n    if (userContext.role === 'admin') {\n      return {\n        user: adminAuth.user,\n        logout: adminAuth.logout,\n        userType: 'admin' as const\n      };\n    } else if (userContext.role === 'student') {\n      return {\n        user: studentAuth.user,\n        logout: studentAuth.logout,\n        userType: 'student' as const\n      };\n    }\n    return null;\n  }, [userContext.role, adminAuth, studentAuth]);\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!userContext.isAuthenticated || !currentAuth) {\n      const loginPath = userContext.role === 'student' ? '/student/login' : '/admin/login';\n      navigate(loginPath);\n      return;\n    }\n  }, [userContext.isAuthenticated, currentAuth, navigate, userContext.role]);\n\n  // UI States\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [showFilters, setShowFilters] = useState(false);\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [recentStudents, setRecentStudents] = useState<any[]>([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n\n  const { categories } = useCategories();\n\n  // Use the announcements hook with role-appropriate service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, userContext.role === 'admin'); // true for admin service, false for student\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, notificationId, scrollTarget } = useNotificationTarget();\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const tokenKey = userContext.role === 'admin' ? ADMIN_AUTH_TOKEN_KEY : STUDENT_AUTH_TOKEN_KEY;\n      const response = await fetch(`${API_BASE_URL}/api/calendar/events`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to fetch calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent students (admin only)\n  const fetchRecentStudents = async () => {\n    if (userContext.role !== 'admin') return;\n\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err: any) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    if (userContext.role === 'admin') {\n      // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n    }\n  }, [userContext.role]);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log(`[DEBUG] ${userContext.role} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${userContext.role} user context:`, { id: currentAuth?.user?.id, role: userContext.role });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${userContext.role} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${userContext.role} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log(`[SUCCESS] ${userContext.role} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${userContext.role} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log(`[DEBUG] ${userContext.role} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${userContext.role} user context:`, { id: currentAuth?.user?.id, role: userContext.role });\n\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: !e.user_has_reacted,\n                  total_reactions: e.user_has_reacted\n                    ? (e.total_reactions || 1) - 1\n                    : (e.total_reactions || 0) + 1\n                }\n              : e\n          )\n        );\n        console.log(`[SUCCESS] ${userContext.role} calendar reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${userContext.role} error toggling calendar like:`, error);\n    }\n  };\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Don't render if not authenticated\n  if (!userContext.isAuthenticated || !currentAuth) {\n    return null;\n  }\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '2rem',\n      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n    }}>\n      {/* Header with role-appropriate notification bell */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem',\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        backdropFilter: 'blur(10px)',\n        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n          <div style={{\n            width: '48px',\n            height: '48px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '12px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Newspaper size={24} color=\"white\" />\n          </div>\n          <div>\n            <h1 style={{\n              margin: 0,\n              fontSize: '1.875rem',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text'\n            }}>\n              {userContext.role === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'}\n            </h1>\n            <p style={{\n              margin: 0,\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            }}>\n              {userContext.role === 'admin' \n                ? 'Monitor and manage announcements & events' \n                : 'Stay updated with latest announcements & events'\n              }\n            </p>\n          </div>\n        </div>\n\n        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n          {/* Role-appropriate notification bell */}\n          {userContext.role === 'admin' ? (\n            <NotificationBell />\n          ) : (\n            <StudentNotificationBell />\n          )}\n\n          {/* User menu */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            padding: '0.5rem 1rem',\n            background: 'rgba(103, 126, 234, 0.1)',\n            borderRadius: '12px',\n            border: '1px solid rgba(103, 126, 234, 0.2)'\n          }}>\n            <div style={{\n              width: '32px',\n              height: '32px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <User size={16} color=\"white\" />\n            </div>\n            <div>\n              <div style={{\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151'\n              }}>\n                {currentAuth.user?.first_name} {currentAuth.user?.last_name}\n              </div>\n              <div style={{\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                textTransform: 'capitalize'\n              }}>\n                {userContext.role}\n              </div>\n            </div>\n            <button\n              onClick={currentAuth.logout}\n              style={{\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                padding: '0.25rem',\n                borderRadius: '6px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: '#6b7280',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                e.currentTarget.style.color = '#ef4444';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.background = 'none';\n                e.currentTarget.style.color = '#6b7280';\n              }}\n            >\n              <LogOut size={16} />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.95)',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        backdropFilter: 'blur(10px)',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'\n      }}>\n        {/* Search Bar */}\n        <div style={{\n          position: 'relative',\n          marginBottom: showFilters ? '1.5rem' : '0'\n        }}>\n          <Search\n            size={20}\n            style={{\n              position: 'absolute',\n              left: '1rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#9ca3af'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Search announcements and events...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            style={{\n              width: '100%',\n              padding: '0.875rem 1rem 0.875rem 3rem',\n              border: '2px solid #e5e7eb',\n              borderRadius: '12px',\n              fontSize: '1rem',\n              outline: 'none',\n              transition: 'all 0.2s ease',\n              background: 'white'\n            }}\n            onFocus={(e) => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(103, 126, 234, 0.1)';\n            }}\n            onBlur={(e) => {\n              e.currentTarget.style.borderColor = '#e5e7eb';\n              e.currentTarget.style.boxShadow = 'none';\n            }}\n          />\n        </div>\n\n        {/* Filter Toggle */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '0.5rem 1rem',\n              background: showFilters ? '#667eea' : 'transparent',\n              color: showFilters ? 'white' : '#667eea',\n              border: `2px solid #667eea`,\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              transition: 'all 0.2s ease'\n            }}\n          >\n            Filters\n            <ChevronDown\n              size={16}\n              style={{\n                transform: showFilters ? 'rotate(180deg)' : 'rotate(0deg)',\n                transition: 'transform 0.2s ease'\n              }}\n            />\n          </button>\n        </div>\n\n        {/* Filters */}\n        {showFilters && (\n          <div style={{\n            marginTop: '1.5rem',\n            padding: '1.5rem',\n            background: '#f9fafb',\n            borderRadius: '12px',\n            border: '1px solid #e5e7eb'\n          }}>\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Category Filter */}\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  Category\n                </label>\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  style={{\n                    width: '100%',\n                    padding: '0.5rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '0.875rem',\n                    background: 'white'\n                  }}\n                >\n                  <option value=\"all\">All Categories</option>\n                  {categories.map((category) => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.category_name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Loading States */}\n      {(loading || calendarLoading) && (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: '3rem',\n          background: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: '16px',\n          marginBottom: '2rem'\n        }}>\n          <div style={{\n            width: '40px',\n            height: '40px',\n            border: '4px solid #e5e7eb',\n            borderTop: '4px solid #667eea',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }} />\n        </div>\n      )}\n\n      {/* Error States */}\n      {(error || calendarError) && (\n        <div style={{\n          background: 'rgba(239, 68, 68, 0.1)',\n          border: '1px solid rgba(239, 68, 68, 0.2)',\n          borderRadius: '12px',\n          padding: '1rem',\n          marginBottom: '2rem',\n          color: '#dc2626'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n            <AlertTriangle size={20} />\n            <span style={{ fontWeight: '600' }}>Error</span>\n          </div>\n          <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.875rem' }}>\n            {error || calendarError}\n          </p>\n        </div>\n      )}\n\n      {/* Main Content Area */}\n      {!loading && !calendarLoading && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: userContext.role === 'admin' ? '1fr 300px' : '1fr',\n          gap: '2rem'\n        }}>\n          {/* Content Feed */}\n          <div style={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          }}>\n            {/* Pinned Announcements */}\n            {pinnedAnnouncements.length > 0 && (\n              <div style={{\n                background: 'rgba(255, 255, 255, 0.95)',\n                borderRadius: '16px',\n                padding: '1.5rem',\n                border: '2px solid #fbbf24',\n                backdropFilter: 'blur(10px)',\n                boxShadow: '0 4px 20px rgba(251, 191, 36, 0.2)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '1rem'\n                }}>\n                  <Pin size={20} color=\"#f59e0b\" />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#f59e0b'\n                  }}>\n                    Pinned Announcements\n                  </h3>\n                </div>\n                {pinnedAnnouncements.map((announcement) => (\n                  <div\n                    key={`pinned-${announcement.announcement_id}`}\n                    style={{\n                      padding: '1rem',\n                      background: '#fffbeb',\n                      borderRadius: '12px',\n                      border: '1px solid #fde68a',\n                      marginBottom: '0.5rem'\n                    }}\n                  >\n                    <h4 style={{\n                      margin: '0 0 0.5rem 0',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      color: '#92400e'\n                    }}>\n                      {announcement.title}\n                    </h4>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '0.875rem',\n                      color: '#a16207',\n                      lineHeight: '1.5'\n                    }}>\n                      {announcement.content?.substring(0, 150)}\n                      {announcement.content?.length > 150 && '...'}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Regular Announcements */}\n            {announcements.length > 0 && (\n              <>\n                {announcements\n                  .filter((ann: any) => ann.is_pinned !== 1) // Exclude pinned announcements\n                  .filter((ann: any) => {\n                    const matchesSearch = !searchTerm ||\n                      ann.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                      ann.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n                    const matchesCategory = selectedCategory === 'all' ||\n                      ann.category_id?.toString() === selectedCategory;\n\n                    return matchesSearch && matchesCategory;\n                  })\n                  .map((announcement: any) => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Newspaper size={24} color=\"white\" />\n                        </div>\n                        <div style={{ flex: 1, minWidth: 0 }}>\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#111827',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '1rem',\n                            fontSize: '0.875rem',\n                            color: '#6b7280'\n                          }}>\n                            <span>📢 Announcement</span>\n                            {announcement.category_name && (\n                              <span style={{\n                                background: '#e0e7ff',\n                                color: '#3730a3',\n                                padding: '0.25rem 0.5rem',\n                                borderRadius: '6px',\n                                fontSize: '0.75rem',\n                                fontWeight: '500'\n                              }}>\n                                {announcement.category_name}\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        marginBottom: '1rem',\n                        padding: '1rem',\n                        background: '#f9fafb',\n                        borderRadius: '12px',\n                        border: '1px solid #e5e7eb'\n                      }}>\n                        <p style={{\n                          margin: 0,\n                          fontSize: '0.875rem',\n                          color: '#374151',\n                          lineHeight: '1.6',\n                          whiteSpace: 'pre-wrap'\n                        }}>\n                          {announcement.content}\n                        </p>\n                      </div>\n\n                      {/* Announcement Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <div style={{ marginBottom: '1rem' }}>\n                          <FacebookImageGallery\n                            images={announcement.attachments}\n                            altPrefix={`Announcement: ${announcement.title}`}\n                            onImageClick={(index) => openLightbox(announcement.attachments, index)}\n                          />\n                        </div>\n                      )}\n\n                      {/* Announcement Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid #e5e7eb'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>\n                              {announcement.total_reactions || 0}\n                            </span>\n                          </button>\n\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <MessageSquare size={18} />\n                            <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>\n                              {announcement.total_comments || 0}\n                            </span>\n                          </div>\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.75rem',\n                          color: '#9ca3af'\n                        }}>\n                          {new Date(announcement.created_at).toLocaleDateString()}\n                        </div>\n                      </div>\n\n                      {/* Announcement Comments */}\n                      <div style={{ marginTop: '1rem' }}>\n                        <UnifiedCommentSection\n                          announcementId={announcement.announcement_id}\n                          currentUserId={currentAuth?.user?.id}\n                          currentUserType={userContext.role}\n                        />\n                      </div>\n                    </div>\n                  ))}\n              </>\n            )}\n\n            {/* Calendar Events */}\n            {calendarEvents.length > 0 && (\n              <>\n                {calendarEvents\n                  .filter(event => {\n                    const matchesSearch = !searchTerm ||\n                      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n                    // Show events that are currently active (between start and end date)\n                    const today = new Date();\n                    const todayDateString = today.getFullYear() + '-' +\n                      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n                      String(today.getDate()).padStart(2, '0');\n\n                    const eventStartDate = event.start_date;\n                    const eventEndDate = event.end_date || event.start_date;\n\n                    const isActive = todayDateString >= eventStartDate && todayDateString <= eventEndDate;\n\n                    return matchesSearch && isActive;\n                  })\n                  .map(event => (\n                  <div\n                    key={`event-${event.calendar_id}`}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.95)',\n                      borderRadius: '16px',\n                      padding: '1.5rem',\n                      border: '1px solid rgba(0, 0, 0, 0.1)',\n                      backdropFilter: 'blur(10px)',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                      e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                    }}\n                  >\n                    {/* Event Header */}\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    }}>\n                      <div style={{\n                        width: '48px',\n                        height: '48px',\n                        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                        borderRadius: '12px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexShrink: 0\n                      }}>\n                        <Calendar size={24} color=\"white\" />\n                      </div>\n                      <div style={{ flex: 1, minWidth: 0 }}>\n                        <h3 style={{\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '1.25rem',\n                          fontWeight: '700',\n                          color: '#111827',\n                          lineHeight: '1.3'\n                        }}>\n                          {event.title}\n                        </h3>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.875rem',\n                          color: '#6b7280'\n                        }}>\n                          <span>📅 {new Date(event.start_date).toLocaleDateString()}</span>\n                          {event.end_date && event.end_date !== event.start_date && (\n                            <span>→ {new Date(event.end_date).toLocaleDateString()}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Event Description */}\n                    {event.description && (\n                      <div style={{\n                        marginBottom: '1rem',\n                        padding: '1rem',\n                        background: '#f9fafb',\n                        borderRadius: '12px',\n                        border: '1px solid #e5e7eb'\n                      }}>\n                        <p style={{\n                          margin: 0,\n                          fontSize: '0.875rem',\n                          color: '#374151',\n                          lineHeight: '1.6'\n                        }}>\n                          {event.description}\n                        </p>\n                      </div>\n                    )}\n\n                    {/* Event Images */}\n                    {event.images && event.images.length > 0 && (\n                      <div style={{ marginBottom: '1rem' }}>\n                        <FacebookImageGallery\n                          images={event.images.map((img: any) => getImageUrl(img.file_path))}\n                          altPrefix={`Event: ${event.title}`}\n                          onImageClick={(index) => {\n                            const imageUrls = event.images.map((img: any) => getImageUrl(img.file_path));\n                            openLightboxWithUrls(imageUrls, index);\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* Event Actions */}\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid #e5e7eb'\n                    }}>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem'\n                      }}>\n                        <button\n                          onClick={() => handleCalendarLikeToggle(event)}\n                          style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            background: 'none',\n                            border: 'none',\n                            cursor: 'pointer',\n                            padding: '0.5rem',\n                            borderRadius: '8px',\n                            color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'none';\n                          }}\n                        >\n                          <Heart\n                            size={18}\n                            fill={event.user_has_reacted ? '#ef4444' : 'none'}\n                          />\n                          <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>\n                            {event.total_reactions || 0}\n                          </span>\n                        </button>\n                      </div>\n\n                      <div style={{\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      }}>\n                        Event • {new Date(event.created_at).toLocaleDateString()}\n                      </div>\n                    </div>\n\n                    {/* Event Comments */}\n                    <div style={{ marginTop: '1rem' }}>\n                      <UnifiedCommentSection\n                        calendarId={event.calendar_id}\n                        currentUserId={currentAuth?.user?.id}\n                        currentUserType={userContext.role}\n                      />\n                    </div>\n                  </div>\n                ))}\n              </>\n            )}\n          </div>\n\n          {/* Admin Sidebar */}\n          {userContext.role === 'admin' && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Quick Actions */}\n              <div style={{\n                background: 'rgba(255, 255, 255, 0.95)',\n                borderRadius: '16px',\n                padding: '1.5rem',\n                backdropFilter: 'blur(10px)',\n                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'\n              }}>\n                <h3 style={{\n                  margin: '0 0 1rem 0',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Quick Actions\n                </h3>\n                <div style={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '0.75rem'\n                }}>\n                  <button\n                    onClick={() => navigate('/admin/posts')}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '0.75rem',\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      transition: 'transform 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                  >\n                    <Edit size={16} />\n                    Create Post\n                  </button>\n                  <button\n                    onClick={() => navigate('/admin/calendar')}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '0.75rem',\n                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      transition: 'transform 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                  >\n                    <Calendar size={16} />\n                    Manage Calendar\n                  </button>\n                  <button\n                    onClick={() => navigate('/admin/student-management')}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '0.75rem',\n                      background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      transition: 'transform 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                  >\n                    <Users size={16} />\n                    Manage Students\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      {lightboxOpen && (\n        <ImageLightbox\n          images={lightboxImages}\n          initialIndex={lightboxInitialIndex}\n          onClose={() => setLightboxOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default UnifiedNewsfeed;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAE7E,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,uBAAuB,MAAM,6BAA6B;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAG3C,SAASC,WAAW,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAChH,OAAO,wCAAwC;AAC/C,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,KAAK,EAILC,aAAa,EAQbC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOtB,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,kBAAA;EACzE,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoC,WAAW,GAAGrC,OAAO,CAAc,MAAM;IAC7C,IAAIgC,SAAS,EAAE;MACb,MAAMM,OAAO,GAAGC,iBAAiB,CAAC,CAAC;MACnC,OAAO;QAAE,GAAGD,OAAO;QAAEE,IAAI,EAAER;MAAU,CAAC;IACxC;IACA,OAAOO,iBAAiB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMS,SAAS,GAAGC,YAAY,CAAC,CAAC;EAChC,MAAMC,WAAW,GAAGC,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMC,WAAW,GAAG7C,OAAO,CAAC,MAAM;IAChC,IAAIqC,WAAW,CAACG,IAAI,KAAK,OAAO,EAAE;MAChC,OAAO;QACLM,IAAI,EAAEL,SAAS,CAACK,IAAI;QACpBC,MAAM,EAAEN,SAAS,CAACM,MAAM;QACxBC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM,IAAIX,WAAW,CAACG,IAAI,KAAK,SAAS,EAAE;MACzC,OAAO;QACLM,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBC,MAAM,EAAEJ,WAAW,CAACI,MAAM;QAC1BC,QAAQ,EAAE;MACZ,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACX,WAAW,CAACG,IAAI,EAAEC,SAAS,EAAEE,WAAW,CAAC,CAAC;;EAE9C;EACA5C,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,WAAW,CAACY,eAAe,IAAI,CAACJ,WAAW,EAAE;MAChD,MAAMK,SAAS,GAAGb,WAAW,CAACG,IAAI,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc;MACpFJ,QAAQ,CAACc,SAAS,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACb,WAAW,CAACY,eAAe,EAAEJ,WAAW,EAAET,QAAQ,EAAEC,WAAW,CAACG,IAAI,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAS,KAAK,CAAC;EACvE,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC+D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACiE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlE,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM;IAAE6E;EAAW,CAAC,GAAGxE,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IACJyE,aAAa;IACbC,OAAO;IACPC,KAAK;IACLC,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAG9E,gBAAgB,CAAC;IACnB+E,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAElD,WAAW,CAACG,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;;EAElC;EACA,MAAM;IAAEgD,kBAAkB;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAGrF,qBAAqB,CAAC,CAAC;;EAEpF;EACAN,SAAS,CAAC,MAAM;IACd,MAAM4F,MAAM,GAAGf,aAAa,CAACgB,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtE9B,sBAAsB,CAAC2B,MAAM,CAAC;EAChC,CAAC,EAAE,CAACf,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMmB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF3B,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAAC0B,SAAS,CAAC;MAE3B,MAAMC,QAAQ,GAAG5D,WAAW,CAACG,IAAI,KAAK,OAAO,GAAG3B,oBAAoB,GAAGC,sBAAsB;MAC7F,MAAMoF,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvF,YAAY,sBAAsB,EAAE;QAClEwF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;UAC3D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMM,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAACK,GAAG,CAAC,MAAOC,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMd,KAAK,CAAC,GAAGvF,YAAY,iBAAiBoG,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5Fd,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;gBAC3D,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMkB,SAAS,GAAG,MAAMF,aAAa,CAACT,IAAI,CAAC,CAAC;YAE5C,IAAIW,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACZ,IAAI,EAAE;cACvCS,KAAK,CAACI,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLL,KAAK,CAACI,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAOE,MAAM,EAAE;YACfC,OAAO,CAACC,IAAI,CAAC,oCAAoCR,KAAK,CAACE,WAAW,GAAG,EAAEI,MAAM,CAAC;YAC9EN,KAAK,CAACI,MAAM,GAAG,EAAE;UACnB;UACA,OAAOJ,KAAK;QACd,CAAC,CACH,CAAC;QAED9C,iBAAiB,CAAC0C,gBAAgB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOa,GAAQ,EAAE;MACjBF,OAAO,CAACzC,KAAK,CAAC,iCAAiC,EAAE2C,GAAG,CAAC;MACrDnD,gBAAgB,CAACmD,GAAG,CAACC,OAAO,IAAI,iCAAiC,CAAC;IACpE,CAAC,SAAS;MACRtD,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMuD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAItF,WAAW,CAACG,IAAI,KAAK,OAAO,EAAE;IAElC,IAAI;MACFkC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvF,YAAY,iEAAiE,EAAE;QAC7GwF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACzF,oBAAoB,CAAC,EAAE;UACvE,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAM0F,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B/B,iBAAiB,CAAC+B,IAAI,CAACA,IAAI,CAACqB,QAAQ,IAAI,EAAE,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOH,GAAQ,EAAE;MACjBF,OAAO,CAACzC,KAAK,CAAC,iCAAiC,EAAE2C,GAAG,CAAC;IACvD,CAAC,SAAS;MACR/C,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA3E,SAAS,CAAC,MAAM;IACdgG,mBAAmB,CAAC,CAAC;IACrB,IAAI1D,WAAW,CAACG,IAAI,KAAK,OAAO,EAAE;MAChC;IAAA;EAEJ,CAAC,EAAE,CAACH,WAAW,CAACG,IAAI,CAAC,CAAC;;EAEtB;EACA,MAAMqF,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MAAA,IAAAC,iBAAA;MACFR,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,sCAAsC,EAAEsF,YAAY,CAACG,eAAe,CAAC;MAC5GV,OAAO,CAACS,GAAG,CAAC,gCAAgC,EAAEF,YAAY,CAACI,aAAa,CAAC;MACzEX,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,gBAAgB,EAAE;QAAE2F,EAAE,EAAEtF,WAAW,aAAXA,WAAW,wBAAAkF,iBAAA,GAAXlF,WAAW,CAAEC,IAAI,cAAAiF,iBAAA,uBAAjBA,iBAAA,CAAmBI,EAAE;QAAE3F,IAAI,EAAEH,WAAW,CAACG;MAAK,CAAC,CAAC;MAE/G,IAAIsF,YAAY,CAACI,aAAa,EAAE;QAC9B;QACAX,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,uBAAuB,CAAC;QAC/D,MAAMwC,kBAAkB,CAAC8C,YAAY,CAACG,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACAV,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,qBAAqB,CAAC;QAC7D,MAAMuC,gBAAgB,CAAC+C,YAAY,CAACG,eAAe,EAAE,CAAC,CAAC;MACzD;MAEAV,OAAO,CAACS,GAAG,CAAC,aAAa3F,WAAW,CAACG,IAAI,gCAAgC,CAAC;IAC5E,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,WAAWzC,WAAW,CAACG,IAAI,uBAAuB,EAAEsC,KAAK,CAAC;IAC1E;EACF,CAAC;;EAED;EACA,MAAMsD,wBAAwB,GAAG,MAAOpB,KAAU,IAAK;IACrD,IAAI;MAAA,IAAAqB,kBAAA;MACFd,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,wCAAwC,EAAEwE,KAAK,CAACE,WAAW,CAAC;MACnGK,OAAO,CAACS,GAAG,CAAC,mCAAmC,EAAEhB,KAAK,CAACsB,gBAAgB,CAAC;MACxEf,OAAO,CAACS,GAAG,CAAC,WAAW3F,WAAW,CAACG,IAAI,gBAAgB,EAAE;QAAE2F,EAAE,EAAEtF,WAAW,aAAXA,WAAW,wBAAAwF,kBAAA,GAAXxF,WAAW,CAAEC,IAAI,cAAAuF,kBAAA,uBAAjBA,kBAAA,CAAmBF,EAAE;QAAE3F,IAAI,EAAEH,WAAW,CAACG;MAAK,CAAC,CAAC;MAE/G,MAAM0D,QAAQ,GAAG,MAAMhG,uBAAuB,CAACqI,UAAU,CAACvB,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACsB,gBAAgB,IAAI,KAAK,CAAC;MAE7G,IAAIpC,QAAQ,CAACO,OAAO,EAAE;QACpB;QACAvC,iBAAiB,CAACsE,UAAU,IAC1BA,UAAU,CAACzB,GAAG,CAAC0B,CAAC,IACdA,CAAC,CAACvB,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAGuB,CAAC;UACJH,gBAAgB,EAAE,CAACG,CAAC,CAACH,gBAAgB;UACrCI,eAAe,EAAED,CAAC,CAACH,gBAAgB,GAC/B,CAACG,CAAC,CAACC,eAAe,IAAI,CAAC,IAAI,CAAC,GAC5B,CAACD,CAAC,CAACC,eAAe,IAAI,CAAC,IAAI;QACjC,CAAC,GACDD,CACN,CACF,CAAC;QACDlB,OAAO,CAACS,GAAG,CAAC,aAAa3F,WAAW,CAACG,IAAI,yCAAyC,CAAC;MACrF;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdyC,OAAO,CAACzC,KAAK,CAAC,WAAWzC,WAAW,CAACG,IAAI,gCAAgC,EAAEsC,KAAK,CAAC;IACnF;EACF,CAAC;;EAED;EACA,MAAM6D,YAAY,GAAGA,CAACvB,MAAgC,EAAEwB,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAGzB,MAAM,CAACL,GAAG,CAAC+B,GAAG,IAAInI,WAAW,CAACmI,GAAG,CAACC,SAAS,CAAC,CAAC,CAACnD,MAAM,CAACoD,OAAO,CAAa;IAC3FpF,iBAAiB,CAACiF,SAAS,CAAC;IAC5B/E,uBAAuB,CAAC8E,YAAY,CAAC;IACrClF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuF,oBAAoB,GAAGA,CAACJ,SAAmB,EAAED,YAAoB,KAAK;IAC1EhF,iBAAiB,CAACiF,SAAS,CAAC;IAC5B/E,uBAAuB,CAAC8E,YAAY,CAAC;IACrClF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,IAAI,CAACrB,WAAW,CAACY,eAAe,IAAI,CAACJ,WAAW,EAAE;IAChD,OAAO,IAAI;EACb;EAEA,oBACEjB,OAAA;IAAKsH,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBAEA3H,OAAA;MAAKsH,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,MAAM;QACpBP,UAAU,EAAE,2BAA2B;QACvCQ,YAAY,EAAE,MAAM;QACpBP,OAAO,EAAE,QAAQ;QACjBQ,cAAc,EAAE,YAAY;QAC5BC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,gBACA3H,OAAA;QAAKsH,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAO,CAAE;QAAAR,QAAA,gBACjE3H,OAAA;UAAKsH,KAAK,EAAE;YACVc,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdb,UAAU,EAAE,mDAAmD;YAC/DQ,YAAY,EAAE,MAAM;YACpBJ,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE;UAClB,CAAE;UAAAF,QAAA,eACA3H,OAAA,CAACb,SAAS;YAACmJ,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACN3I,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAIsH,KAAK,EAAE;cACTsB,MAAM,EAAE,CAAC;cACTC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBtB,UAAU,EAAE,mDAAmD;cAC/DuB,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCC,cAAc,EAAE;YAClB,CAAE;YAAAtB,QAAA,EACClH,WAAW,CAACG,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG;UAAkB;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACL3I,OAAA;YAAGsH,KAAK,EAAE;cACRsB,MAAM,EAAE,CAAC;cACTL,KAAK,EAAE,SAAS;cAChBM,QAAQ,EAAE;YACZ,CAAE;YAAAlB,QAAA,EACClH,WAAW,CAACG,IAAI,KAAK,OAAO,GACzB,2CAA2C,GAC3C;UAAiD;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3I,OAAA;QAAKsH,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAO,CAAE;QAAAR,QAAA,GAEhElH,WAAW,CAACG,IAAI,KAAK,OAAO,gBAC3BZ,OAAA,CAACrB,gBAAgB;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB3I,OAAA,CAACpB,uBAAuB;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC3B,eAGD3I,OAAA;UAAKsH,KAAK,EAAE;YACVM,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBK,GAAG,EAAE,SAAS;YACdV,OAAO,EAAE,aAAa;YACtBD,UAAU,EAAE,0BAA0B;YACtCQ,YAAY,EAAE,MAAM;YACpBkB,MAAM,EAAE;UACV,CAAE;UAAAvB,QAAA,gBACA3H,OAAA;YAAKsH,KAAK,EAAE;cACVc,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdb,UAAU,EAAE,mDAAmD;cAC/DQ,YAAY,EAAE,KAAK;cACnBJ,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAF,QAAA,eACA3H,OAAA,CAACH,IAAI;cAACyI,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN3I,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAKsH,KAAK,EAAE;gBACVuB,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBP,KAAK,EAAE;cACT,CAAE;cAAAZ,QAAA,IAAArH,kBAAA,GACCW,WAAW,CAACC,IAAI,cAAAZ,kBAAA,uBAAhBA,kBAAA,CAAkB6I,UAAU,EAAC,GAAC,GAAA5I,kBAAA,GAACU,WAAW,CAACC,IAAI,cAAAX,kBAAA,uBAAhBA,kBAAA,CAAkB6I,SAAS;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN3I,OAAA;cAAKsH,KAAK,EAAE;gBACVuB,QAAQ,EAAE,SAAS;gBACnBN,KAAK,EAAE,SAAS;gBAChBc,aAAa,EAAE;cACjB,CAAE;cAAA1B,QAAA,EACClH,WAAW,CAACG;YAAI;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3I,OAAA;YACEsJ,OAAO,EAAErI,WAAW,CAACE,MAAO;YAC5BmG,KAAK,EAAE;cACLE,UAAU,EAAE,MAAM;cAClB0B,MAAM,EAAE,MAAM;cACdK,MAAM,EAAE,SAAS;cACjB9B,OAAO,EAAE,SAAS;cAClBO,YAAY,EAAE,KAAK;cACnBJ,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBU,KAAK,EAAE,SAAS;cAChBiB,UAAU,EAAE;YACd,CAAE;YACFC,YAAY,EAAG5C,CAAC,IAAK;cACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,wBAAwB;cAC3DX,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACiB,KAAK,GAAG,SAAS;YACzC,CAAE;YACFoB,YAAY,EAAG9C,CAAC,IAAK;cACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,MAAM;cACzCX,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACiB,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAZ,QAAA,eAEF3H,OAAA,CAACF,MAAM;cAACwI,IAAI,EAAE;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA;MAAKsH,KAAK,EAAE;QACVE,UAAU,EAAE,2BAA2B;QACvCQ,YAAY,EAAE,MAAM;QACpBP,OAAO,EAAE,QAAQ;QACjBM,YAAY,EAAE,MAAM;QACpBE,cAAc,EAAE,YAAY;QAC5BC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,gBAEA3H,OAAA;QAAKsH,KAAK,EAAE;UACVsC,QAAQ,EAAE,UAAU;UACpB7B,YAAY,EAAEpG,WAAW,GAAG,QAAQ,GAAG;QACzC,CAAE;QAAAgG,QAAA,gBACA3H,OAAA,CAACZ,MAAM;UACLkJ,IAAI,EAAE,EAAG;UACThB,KAAK,EAAE;YACLsC,QAAQ,EAAE,UAAU;YACpBC,IAAI,EAAE,MAAM;YACZC,GAAG,EAAE,KAAK;YACVC,SAAS,EAAE,kBAAkB;YAC7BxB,KAAK,EAAE;UACT;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF3I,OAAA;UACEgK,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,oCAAoC;UAChDC,KAAK,EAAE3I,UAAW;UAClB4I,QAAQ,EAAGtD,CAAC,IAAKrF,aAAa,CAACqF,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;UAC/C5C,KAAK,EAAE;YACLc,KAAK,EAAE,MAAM;YACbX,OAAO,EAAE,6BAA6B;YACtCyB,MAAM,EAAE,mBAAmB;YAC3BlB,YAAY,EAAE,MAAM;YACpBa,QAAQ,EAAE,MAAM;YAChBwB,OAAO,EAAE,MAAM;YACfb,UAAU,EAAE,eAAe;YAC3BhC,UAAU,EAAE;UACd,CAAE;UACF8C,OAAO,EAAGzD,CAAC,IAAK;YACdA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACiD,WAAW,GAAG,SAAS;YAC7C1D,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,oCAAoC;UACxE,CAAE;UACFsC,MAAM,EAAG3D,CAAC,IAAK;YACbA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACiD,WAAW,GAAG,SAAS;YAC7C1D,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,MAAM;UAC1C;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3I,OAAA;QAAKsH,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,eACrF3H,OAAA;UACEsJ,OAAO,EAAEA,CAAA,KAAM1H,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C2F,KAAK,EAAE;YACLM,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBK,GAAG,EAAE,QAAQ;YACbV,OAAO,EAAE,aAAa;YACtBD,UAAU,EAAE7F,WAAW,GAAG,SAAS,GAAG,aAAa;YACnD4G,KAAK,EAAE5G,WAAW,GAAG,OAAO,GAAG,SAAS;YACxCuH,MAAM,EAAE,mBAAmB;YAC3BlB,YAAY,EAAE,KAAK;YACnBuB,MAAM,EAAE,SAAS;YACjBV,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBU,UAAU,EAAE;UACd,CAAE;UAAA7B,QAAA,GACH,SAEC,eAAA3H,OAAA,CAACJ,WAAW;YACV0I,IAAI,EAAE,EAAG;YACThB,KAAK,EAAE;cACLyC,SAAS,EAAEpI,WAAW,GAAG,gBAAgB,GAAG,cAAc;cAC1D6H,UAAU,EAAE;YACd;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLhH,WAAW,iBACV3B,OAAA;QAAKsH,KAAK,EAAE;UACVmD,SAAS,EAAE,QAAQ;UACnBhD,OAAO,EAAE,QAAQ;UACjBD,UAAU,EAAE,SAAS;UACrBQ,YAAY,EAAE,MAAM;UACpBkB,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,eACA3H,OAAA;UAAKsH,KAAK,EAAE;YACVM,OAAO,EAAE,MAAM;YACf8C,mBAAmB,EAAE,sCAAsC;YAC3DvC,GAAG,EAAE;UACP,CAAE;UAAAR,QAAA,eAEA3H,OAAA;YAAA2H,QAAA,gBACE3H,OAAA;cAAOsH,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBiB,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBP,KAAK,EAAE,SAAS;gBAChBR,YAAY,EAAE;cAChB,CAAE;cAAAJ,QAAA,EAAC;YAEH;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3I,OAAA;cACEkK,KAAK,EAAEzI,gBAAiB;cACxB0I,QAAQ,EAAGtD,CAAC,IAAKnF,mBAAmB,CAACmF,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;cACrD5C,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbX,OAAO,EAAE,QAAQ;gBACjByB,MAAM,EAAE,mBAAmB;gBAC3BlB,YAAY,EAAE,KAAK;gBACnBa,QAAQ,EAAE,UAAU;gBACpBrB,UAAU,EAAE;cACd,CAAE;cAAAG,QAAA,gBAEF3H,OAAA;gBAAQkK,KAAK,EAAC,KAAK;gBAAAvC,QAAA,EAAC;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1C5F,UAAU,CAACoC,GAAG,CAAEwF,QAAQ,iBACvB3K,OAAA;gBAAmCkK,KAAK,EAAES,QAAQ,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAE;gBAAAlD,QAAA,EACvEgD,QAAQ,CAACG;cAAa,GADZH,QAAQ,CAACC,WAAW;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC1F,OAAO,IAAIV,eAAe,kBAC1BvC,OAAA;MAAKsH,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBL,OAAO,EAAE,MAAM;QACfD,UAAU,EAAE,2BAA2B;QACvCQ,YAAY,EAAE,MAAM;QACpBD,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,eACA3H,OAAA;QAAKsH,KAAK,EAAE;UACVc,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACda,MAAM,EAAE,mBAAmB;UAC3B6B,SAAS,EAAE,mBAAmB;UAC9B/C,YAAY,EAAE,KAAK;UACnBgD,SAAS,EAAE;QACb;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,EAGA,CAACzF,KAAK,IAAIT,aAAa,kBACtBzC,OAAA;MAAKsH,KAAK,EAAE;QACVE,UAAU,EAAE,wBAAwB;QACpC0B,MAAM,EAAE,kCAAkC;QAC1ClB,YAAY,EAAE,MAAM;QACpBP,OAAO,EAAE,MAAM;QACfM,YAAY,EAAE,MAAM;QACpBQ,KAAK,EAAE;MACT,CAAE;MAAAZ,QAAA,gBACA3H,OAAA;QAAKsH,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAS,CAAE;QAAAR,QAAA,gBACnE3H,OAAA,CAACL,aAAa;UAAC2I,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3B3I,OAAA;UAAMsH,KAAK,EAAE;YAAEwB,UAAU,EAAE;UAAM,CAAE;UAAAnB,QAAA,EAAC;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACN3I,OAAA;QAAGsH,KAAK,EAAE;UAAEsB,MAAM,EAAE,cAAc;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAAlB,QAAA,EACxDzE,KAAK,IAAIT;MAAa;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGA,CAAC1F,OAAO,IAAI,CAACV,eAAe,iBAC3BvC,OAAA;MAAKsH,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACf8C,mBAAmB,EAAEjK,WAAW,CAACG,IAAI,KAAK,OAAO,GAAG,WAAW,GAAG,KAAK;QACvEuH,GAAG,EAAE;MACP,CAAE;MAAAR,QAAA,gBAEA3H,OAAA;QAAKsH,KAAK,EAAE;UACVM,OAAO,EAAE,MAAM;UACfqD,aAAa,EAAE,QAAQ;UACvB9C,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,GAECxF,mBAAmB,CAAC+I,MAAM,GAAG,CAAC,iBAC7BlL,OAAA;UAAKsH,KAAK,EAAE;YACVE,UAAU,EAAE,2BAA2B;YACvCQ,YAAY,EAAE,MAAM;YACpBP,OAAO,EAAE,QAAQ;YACjByB,MAAM,EAAE,mBAAmB;YAC3BjB,cAAc,EAAE,YAAY;YAC5BC,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,gBACA3H,OAAA;YAAKsH,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBK,GAAG,EAAE,QAAQ;cACbJ,YAAY,EAAE;YAChB,CAAE;YAAAJ,QAAA,gBACA3H,OAAA,CAACX,GAAG;cAACiJ,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC3I,OAAA;cAAIsH,KAAK,EAAE;gBACTsB,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBP,KAAK,EAAE;cACT,CAAE;cAAAZ,QAAA,EAAC;YAEH;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EACLxG,mBAAmB,CAACgD,GAAG,CAAEe,YAAY;YAAA,IAAAiF,qBAAA,EAAAC,sBAAA;YAAA,oBACpCpL,OAAA;cAEEsH,KAAK,EAAE;gBACLG,OAAO,EAAE,MAAM;gBACfD,UAAU,EAAE,SAAS;gBACrBQ,YAAY,EAAE,MAAM;gBACpBkB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE;cAChB,CAAE;cAAAJ,QAAA,gBAEF3H,OAAA;gBAAIsH,KAAK,EAAE;kBACTsB,MAAM,EAAE,cAAc;kBACtBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAZ,QAAA,EACCzB,YAAY,CAACmF;cAAK;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACL3I,OAAA;gBAAGsH,KAAK,EAAE;kBACRsB,MAAM,EAAE,CAAC;kBACTC,QAAQ,EAAE,UAAU;kBACpBN,KAAK,EAAE,SAAS;kBAChB+C,UAAU,EAAE;gBACd,CAAE;gBAAA3D,QAAA,IAAAwD,qBAAA,GACCjF,YAAY,CAACqF,OAAO,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsBK,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EACvC,EAAAJ,sBAAA,GAAAlF,YAAY,CAACqF,OAAO,cAAAH,sBAAA,uBAApBA,sBAAA,CAAsBF,MAAM,IAAG,GAAG,IAAI,KAAK;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA,GAzBC,UAAUzC,YAAY,CAACG,eAAe,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0B1C,CAAC;UAAA,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGA3F,aAAa,CAACkI,MAAM,GAAG,CAAC,iBACvBlL,OAAA,CAAAE,SAAA;UAAAyH,QAAA,EACG3E,aAAa,CACXgB,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC,CAAC;UAAA,CAC1CF,MAAM,CAAEC,GAAQ,IAAK;YAAA,IAAAwH,gBAAA;YACpB,MAAMC,aAAa,GAAG,CAACnK,UAAU,IAC/B0C,GAAG,CAACoH,KAAK,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrK,UAAU,CAACoK,WAAW,CAAC,CAAC,CAAC,IAC1D1H,GAAG,CAACsH,OAAO,CAACI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrK,UAAU,CAACoK,WAAW,CAAC,CAAC,CAAC;YAE9D,MAAME,eAAe,GAAGpK,gBAAgB,KAAK,KAAK,IAChD,EAAAgK,gBAAA,GAAAxH,GAAG,CAAC2G,WAAW,cAAAa,gBAAA,uBAAfA,gBAAA,CAAiBZ,QAAQ,CAAC,CAAC,MAAKpJ,gBAAgB;YAElD,OAAOiK,aAAa,IAAIG,eAAe;UACzC,CAAC,CAAC,CACD1G,GAAG,CAAEe,YAAiB;YAAA,IAAA4F,kBAAA;YAAA,oBACrB9L,OAAA;cAEEsH,KAAK,EAAE;gBACLE,UAAU,EAAE,2BAA2B;gBACvCQ,YAAY,EAAE,MAAM;gBACpBP,OAAO,EAAE,QAAQ;gBACjByB,MAAM,EAAE,8BAA8B;gBACtCjB,cAAc,EAAE,YAAY;gBAC5BC,SAAS,EAAE,gCAAgC;gBAC3CsB,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG5C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;gBACpDlD,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,gCAAgC;cACpE,CAAE;cACFyB,YAAY,EAAG9C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,eAAe;gBACjDlD,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,gCAAgC;cACpE,CAAE;cAAAP,QAAA,gBAGF3H,OAAA;gBAAKsH,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBK,GAAG,EAAE,MAAM;kBACXJ,YAAY,EAAE;gBAChB,CAAE;gBAAAJ,QAAA,gBACA3H,OAAA;kBAAKsH,KAAK,EAAE;oBACVc,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAE,mDAAmD;oBAC/DQ,YAAY,EAAE,MAAM;oBACpBJ,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBkE,UAAU,EAAE;kBACd,CAAE;kBAAApE,QAAA,eACA3H,OAAA,CAACb,SAAS;oBAACmJ,IAAI,EAAE,EAAG;oBAACC,KAAK,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACN3I,OAAA;kBAAKsH,KAAK,EAAE;oBAAE0E,IAAI,EAAE,CAAC;oBAAEC,QAAQ,EAAE;kBAAE,CAAE;kBAAAtE,QAAA,gBACnC3H,OAAA;oBAAIsH,KAAK,EAAE;sBACTsB,MAAM,EAAE,cAAc;sBACtBC,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBP,KAAK,EAAE,SAAS;sBAChB+C,UAAU,EAAE;oBACd,CAAE;oBAAA3D,QAAA,EACCzB,YAAY,CAACmF;kBAAK;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACL3I,OAAA;oBAAKsH,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBK,GAAG,EAAE,MAAM;sBACXU,QAAQ,EAAE,UAAU;sBACpBN,KAAK,EAAE;oBACT,CAAE;oBAAAZ,QAAA,gBACA3H,OAAA;sBAAA2H,QAAA,EAAM;oBAAe;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC3BzC,YAAY,CAAC4E,aAAa,iBACzB9K,OAAA;sBAAMsH,KAAK,EAAE;wBACXE,UAAU,EAAE,SAAS;wBACrBe,KAAK,EAAE,SAAS;wBAChBd,OAAO,EAAE,gBAAgB;wBACzBO,YAAY,EAAE,KAAK;wBACnBa,QAAQ,EAAE,SAAS;wBACnBC,UAAU,EAAE;sBACd,CAAE;sBAAAnB,QAAA,EACCzB,YAAY,CAAC4E;oBAAa;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3I,OAAA;gBAAKsH,KAAK,EAAE;kBACVS,YAAY,EAAE,MAAM;kBACpBN,OAAO,EAAE,MAAM;kBACfD,UAAU,EAAE,SAAS;kBACrBQ,YAAY,EAAE,MAAM;kBACpBkB,MAAM,EAAE;gBACV,CAAE;gBAAAvB,QAAA,eACA3H,OAAA;kBAAGsH,KAAK,EAAE;oBACRsB,MAAM,EAAE,CAAC;oBACTC,QAAQ,EAAE,UAAU;oBACpBN,KAAK,EAAE,SAAS;oBAChB+C,UAAU,EAAE,KAAK;oBACjBY,UAAU,EAAE;kBACd,CAAE;kBAAAvE,QAAA,EACCzB,YAAY,CAACqF;gBAAO;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EAGLzC,YAAY,CAACT,WAAW,IAAIS,YAAY,CAACT,WAAW,CAACyF,MAAM,GAAG,CAAC,iBAC9DlL,OAAA;gBAAKsH,KAAK,EAAE;kBAAES,YAAY,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,eACnC3H,OAAA,CAACnB,oBAAoB;kBACnB2G,MAAM,EAAEU,YAAY,CAACT,WAAY;kBACjC0G,SAAS,EAAE,iBAAiBjG,YAAY,CAACmF,KAAK,EAAG;kBACjDe,YAAY,EAAGC,KAAK,IAAKtF,YAAY,CAACb,YAAY,CAACT,WAAW,EAAE4G,KAAK;gBAAE;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAGD3I,OAAA;gBAAKsH,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/ByE,UAAU,EAAE,MAAM;kBAClBvB,SAAS,EAAE;gBACb,CAAE;gBAAApD,QAAA,gBACA3H,OAAA;kBAAKsH,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBK,GAAG,EAAE;kBACP,CAAE;kBAAAR,QAAA,gBACA3H,OAAA;oBACEsJ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,YAAY,CAAE;oBAC9CoB,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBK,GAAG,EAAE,QAAQ;sBACbX,UAAU,EAAE,MAAM;sBAClB0B,MAAM,EAAE,MAAM;sBACdK,MAAM,EAAE,SAAS;sBACjB9B,OAAO,EAAE,QAAQ;sBACjBO,YAAY,EAAE,KAAK;sBACnBO,KAAK,EAAErC,YAAY,CAACI,aAAa,GAAG,SAAS,GAAG,SAAS;sBACzDkD,UAAU,EAAE;oBACd,CAAE;oBACFC,YAAY,EAAG5C,CAAC,IAAK;sBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,wBAAwB;oBAC7D,CAAE;oBACFmC,YAAY,EAAG9C,CAAC,IAAK;sBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAAG,QAAA,gBAEF3H,OAAA,CAACR,KAAK;sBACJ8I,IAAI,EAAE,EAAG;sBACTiE,IAAI,EAAErG,YAAY,CAACI,aAAa,GAAG,SAAS,GAAG;oBAAO;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACF3I,OAAA;sBAAMsH,KAAK,EAAE;wBAAEuB,QAAQ,EAAE,UAAU;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAnB,QAAA,EACtDzB,YAAY,CAACY,eAAe,IAAI;oBAAC;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAET3I,OAAA;oBAAKsH,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBK,GAAG,EAAE,QAAQ;sBACbI,KAAK,EAAE;oBACT,CAAE;oBAAAZ,QAAA,gBACA3H,OAAA,CAACT,aAAa;sBAAC+I,IAAI,EAAE;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B3I,OAAA;sBAAMsH,KAAK,EAAE;wBAAEuB,QAAQ,EAAE,UAAU;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAnB,QAAA,EACtDzB,YAAY,CAACsG,cAAc,IAAI;oBAAC;sBAAAhE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3I,OAAA;kBAAKsH,KAAK,EAAE;oBACVuB,QAAQ,EAAE,SAAS;oBACnBN,KAAK,EAAE;kBACT,CAAE;kBAAAZ,QAAA,EACC,IAAI8E,IAAI,CAACvG,YAAY,CAACwG,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3I,OAAA;gBAAKsH,KAAK,EAAE;kBAAEmD,SAAS,EAAE;gBAAO,CAAE;gBAAA9C,QAAA,eAChC3H,OAAA,CAACtB,qBAAqB;kBACpBkO,cAAc,EAAE1G,YAAY,CAACG,eAAgB;kBAC7CwG,aAAa,EAAE5L,WAAW,aAAXA,WAAW,wBAAA6K,kBAAA,GAAX7K,WAAW,CAAEC,IAAI,cAAA4K,kBAAA,uBAAjBA,kBAAA,CAAmBvF,EAAG;kBACrCuG,eAAe,EAAErM,WAAW,CAACG;gBAAK;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7KD,gBAAgBzC,YAAY,CAACG,eAAe,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8KhD,CAAC;UAAA,CACP;QAAC,gBACJ,CACH,EAGAtG,cAAc,CAAC6I,MAAM,GAAG,CAAC,iBACxBlL,OAAA,CAAAE,SAAA;UAAAyH,QAAA,EACGtF,cAAc,CACZ2B,MAAM,CAACoB,KAAK,IAAI;YACf,MAAMsG,aAAa,GAAG,CAACnK,UAAU,IAC/B6D,KAAK,CAACiG,KAAK,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrK,UAAU,CAACoK,WAAW,CAAC,CAAC,CAAC,IAC3DvG,KAAK,CAAC2H,WAAW,IAAI3H,KAAK,CAAC2H,WAAW,CAACpB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrK,UAAU,CAACoK,WAAW,CAAC,CAAC,CAAE;;YAE3F;YACA,MAAMqB,KAAK,GAAG,IAAIP,IAAI,CAAC,CAAC;YACxB,MAAMQ,eAAe,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACH,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAE1C,MAAME,cAAc,GAAGnI,KAAK,CAACoI,UAAU;YACvC,MAAMC,YAAY,GAAGrI,KAAK,CAACsI,QAAQ,IAAItI,KAAK,CAACoI,UAAU;YAEvD,MAAMG,QAAQ,GAAGV,eAAe,IAAIM,cAAc,IAAIN,eAAe,IAAIQ,YAAY;YAErF,OAAO/B,aAAa,IAAIiC,QAAQ;UAClC,CAAC,CAAC,CACDxI,GAAG,CAACC,KAAK;YAAA,IAAAwI,kBAAA;YAAA,oBACV5N,OAAA;cAEEsH,KAAK,EAAE;gBACLE,UAAU,EAAE,2BAA2B;gBACvCQ,YAAY,EAAE,MAAM;gBACpBP,OAAO,EAAE,QAAQ;gBACjByB,MAAM,EAAE,8BAA8B;gBACtCjB,cAAc,EAAE,YAAY;gBAC5BC,SAAS,EAAE,gCAAgC;gBAC3CsB,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG5C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;gBACpDlD,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,gCAAgC;cACpE,CAAE;cACFyB,YAAY,EAAG9C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,eAAe;gBACjDlD,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACY,SAAS,GAAG,gCAAgC;cACpE,CAAE;cAAAP,QAAA,gBAGF3H,OAAA;gBAAKsH,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBK,GAAG,EAAE,MAAM;kBACXJ,YAAY,EAAE;gBAChB,CAAE;gBAAAJ,QAAA,gBACA3H,OAAA;kBAAKsH,KAAK,EAAE;oBACVc,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAE,mDAAmD;oBAC/DQ,YAAY,EAAE,MAAM;oBACpBJ,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBkE,UAAU,EAAE;kBACd,CAAE;kBAAApE,QAAA,eACA3H,OAAA,CAACV,QAAQ;oBAACgJ,IAAI,EAAE,EAAG;oBAACC,KAAK,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACN3I,OAAA;kBAAKsH,KAAK,EAAE;oBAAE0E,IAAI,EAAE,CAAC;oBAAEC,QAAQ,EAAE;kBAAE,CAAE;kBAAAtE,QAAA,gBACnC3H,OAAA;oBAAIsH,KAAK,EAAE;sBACTsB,MAAM,EAAE,cAAc;sBACtBC,QAAQ,EAAE,SAAS;sBACnBC,UAAU,EAAE,KAAK;sBACjBP,KAAK,EAAE,SAAS;sBAChB+C,UAAU,EAAE;oBACd,CAAE;oBAAA3D,QAAA,EACCvC,KAAK,CAACiG;kBAAK;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACL3I,OAAA;oBAAKsH,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBK,GAAG,EAAE,MAAM;sBACXU,QAAQ,EAAE,UAAU;sBACpBN,KAAK,EAAE;oBACT,CAAE;oBAAAZ,QAAA,gBACA3H,OAAA;sBAAA2H,QAAA,GAAM,eAAG,EAAC,IAAI8E,IAAI,CAACrH,KAAK,CAACoI,UAAU,CAAC,CAACb,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEvD,KAAK,CAACsI,QAAQ,IAAItI,KAAK,CAACsI,QAAQ,KAAKtI,KAAK,CAACoI,UAAU,iBACpDxN,OAAA;sBAAA2H,QAAA,GAAM,SAAE,EAAC,IAAI8E,IAAI,CAACrH,KAAK,CAACsI,QAAQ,CAAC,CAACf,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAC9D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLvD,KAAK,CAAC2H,WAAW,iBAChB/M,OAAA;gBAAKsH,KAAK,EAAE;kBACVS,YAAY,EAAE,MAAM;kBACpBN,OAAO,EAAE,MAAM;kBACfD,UAAU,EAAE,SAAS;kBACrBQ,YAAY,EAAE,MAAM;kBACpBkB,MAAM,EAAE;gBACV,CAAE;gBAAAvB,QAAA,eACA3H,OAAA;kBAAGsH,KAAK,EAAE;oBACRsB,MAAM,EAAE,CAAC;oBACTC,QAAQ,EAAE,UAAU;oBACpBN,KAAK,EAAE,SAAS;oBAChB+C,UAAU,EAAE;kBACd,CAAE;kBAAA3D,QAAA,EACCvC,KAAK,CAAC2H;gBAAW;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EAGAvD,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACI,MAAM,CAAC0F,MAAM,GAAG,CAAC,iBACtClL,OAAA;gBAAKsH,KAAK,EAAE;kBAAES,YAAY,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,eACnC3H,OAAA,CAACnB,oBAAoB;kBACnB2G,MAAM,EAAEJ,KAAK,CAACI,MAAM,CAACL,GAAG,CAAE+B,GAAQ,IAAKnI,WAAW,CAACmI,GAAG,CAACC,SAAS,CAAC,CAAE;kBACnEgF,SAAS,EAAE,UAAU/G,KAAK,CAACiG,KAAK,EAAG;kBACnCe,YAAY,EAAGC,KAAK,IAAK;oBACvB,MAAMpF,SAAS,GAAG7B,KAAK,CAACI,MAAM,CAACL,GAAG,CAAE+B,GAAQ,IAAKnI,WAAW,CAACmI,GAAG,CAACC,SAAS,CAAC,CAAC;oBAC5EE,oBAAoB,CAACJ,SAAS,EAAEoF,KAAK,CAAC;kBACxC;gBAAE;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAGD3I,OAAA;gBAAKsH,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/ByE,UAAU,EAAE,MAAM;kBAClBvB,SAAS,EAAE;gBACb,CAAE;gBAAApD,QAAA,gBACA3H,OAAA;kBAAKsH,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBK,GAAG,EAAE;kBACP,CAAE;kBAAAR,QAAA,eACA3H,OAAA;oBACEsJ,OAAO,EAAEA,CAAA,KAAM9C,wBAAwB,CAACpB,KAAK,CAAE;oBAC/CkC,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBK,GAAG,EAAE,QAAQ;sBACbX,UAAU,EAAE,MAAM;sBAClB0B,MAAM,EAAE,MAAM;sBACdK,MAAM,EAAE,SAAS;sBACjB9B,OAAO,EAAE,QAAQ;sBACjBO,YAAY,EAAE,KAAK;sBACnBO,KAAK,EAAEnD,KAAK,CAACsB,gBAAgB,GAAG,SAAS,GAAG,SAAS;sBACrD8C,UAAU,EAAE;oBACd,CAAE;oBACFC,YAAY,EAAG5C,CAAC,IAAK;sBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,wBAAwB;oBAC7D,CAAE;oBACFmC,YAAY,EAAG9C,CAAC,IAAK;sBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACE,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAAG,QAAA,gBAEF3H,OAAA,CAACR,KAAK;sBACJ8I,IAAI,EAAE,EAAG;sBACTiE,IAAI,EAAEnH,KAAK,CAACsB,gBAAgB,GAAG,SAAS,GAAG;oBAAO;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACF3I,OAAA;sBAAMsH,KAAK,EAAE;wBAAEuB,QAAQ,EAAE,UAAU;wBAAEC,UAAU,EAAE;sBAAM,CAAE;sBAAAnB,QAAA,EACtDvC,KAAK,CAAC0B,eAAe,IAAI;oBAAC;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN3I,OAAA;kBAAKsH,KAAK,EAAE;oBACVuB,QAAQ,EAAE,SAAS;oBACnBN,KAAK,EAAE;kBACT,CAAE;kBAAAZ,QAAA,GAAC,eACO,EAAC,IAAI8E,IAAI,CAACrH,KAAK,CAACsH,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3I,OAAA;gBAAKsH,KAAK,EAAE;kBAAEmD,SAAS,EAAE;gBAAO,CAAE;gBAAA9C,QAAA,eAChC3H,OAAA,CAACtB,qBAAqB;kBACpBmP,UAAU,EAAEzI,KAAK,CAACE,WAAY;kBAC9BuH,aAAa,EAAE5L,WAAW,aAAXA,WAAW,wBAAA2M,kBAAA,GAAX3M,WAAW,CAAEC,IAAI,cAAA0M,kBAAA,uBAAjBA,kBAAA,CAAmBrH,EAAG;kBACrCuG,eAAe,EAAErM,WAAW,CAACG;gBAAK;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA5JD,SAASvD,KAAK,CAACE,WAAW,EAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6J9B,CAAC;UAAA,CACP;QAAC,gBACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlI,WAAW,CAACG,IAAI,KAAK,OAAO,iBAC3BZ,OAAA;QAAKsH,KAAK,EAAE;UACVM,OAAO,EAAE,MAAM;UACfqD,aAAa,EAAE,QAAQ;UACvB9C,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,eAEA3H,OAAA;UAAKsH,KAAK,EAAE;YACVE,UAAU,EAAE,2BAA2B;YACvCQ,YAAY,EAAE,MAAM;YACpBP,OAAO,EAAE,QAAQ;YACjBQ,cAAc,EAAE,YAAY;YAC5BC,SAAS,EAAE;UACb,CAAE;UAAAP,QAAA,gBACA3H,OAAA;YAAIsH,KAAK,EAAE;cACTsB,MAAM,EAAE,YAAY;cACpBC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBP,KAAK,EAAE;YACT,CAAE;YAAAZ,QAAA,EAAC;UAEH;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3I,OAAA;YAAKsH,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfqD,aAAa,EAAE,QAAQ;cACvB9C,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA3H,OAAA;cACEsJ,OAAO,EAAEA,CAAA,KAAM9I,QAAQ,CAAC,cAAc,CAAE;cACxC8G,KAAK,EAAE;gBACLM,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBK,GAAG,EAAE,SAAS;gBACdV,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,mDAAmD;gBAC/De,KAAK,EAAE,OAAO;gBACdW,MAAM,EAAE,MAAM;gBACdlB,YAAY,EAAE,KAAK;gBACnBuB,MAAM,EAAE,SAAS;gBACjBV,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBU,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG5C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;cACtD,CAAE;cACFJ,YAAY,EAAG9C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,eAAe;cACnD,CAAE;cAAApC,QAAA,gBAEF3H,OAAA,CAACP,IAAI;gBAAC6I,IAAI,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3I,OAAA;cACEsJ,OAAO,EAAEA,CAAA,KAAM9I,QAAQ,CAAC,iBAAiB,CAAE;cAC3C8G,KAAK,EAAE;gBACLM,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBK,GAAG,EAAE,SAAS;gBACdV,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,mDAAmD;gBAC/De,KAAK,EAAE,OAAO;gBACdW,MAAM,EAAE,MAAM;gBACdlB,YAAY,EAAE,KAAK;gBACnBuB,MAAM,EAAE,SAAS;gBACjBV,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBU,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG5C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;cACtD,CAAE;cACFJ,YAAY,EAAG9C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,eAAe;cACnD,CAAE;cAAApC,QAAA,gBAEF3H,OAAA,CAACV,QAAQ;gBAACgJ,IAAI,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3I,OAAA;cACEsJ,OAAO,EAAEA,CAAA,KAAM9I,QAAQ,CAAC,2BAA2B,CAAE;cACrD8G,KAAK,EAAE;gBACLM,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBK,GAAG,EAAE,SAAS;gBACdV,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,mDAAmD;gBAC/De,KAAK,EAAE,OAAO;gBACdW,MAAM,EAAE,MAAM;gBACdlB,YAAY,EAAE,KAAK;gBACnBuB,MAAM,EAAE,SAAS;gBACjBV,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBU,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG5C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;cACtD,CAAE;cACFJ,YAAY,EAAG9C,CAAC,IAAK;gBACnBA,CAAC,CAAC6C,aAAa,CAACpC,KAAK,CAACyC,SAAS,GAAG,eAAe;cACnD,CAAE;cAAApC,QAAA,gBAEF3H,OAAA,CAACN,KAAK;gBAAC4I,IAAI,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA9G,YAAY,iBACX7B,OAAA,CAAClB,aAAa;MACZ0G,MAAM,EAAEzD,cAAe;MACvBiF,YAAY,EAAE/E,oBAAqB;MACnC6L,OAAO,EAAEA,CAAA,KAAMhM,eAAe,CAAC,KAAK;IAAE;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtI,EAAA,CArmCIF,eAA+C;EAAA,QAClC9B,WAAW,EA0DLE,aAAa,EAUhCC,gBAAgB,EASyCC,qBAAqB;AAAA;AAAAsP,EAAA,GA9E9E5N,eAA+C;AAumCrD,eAAeA,eAAe;AAAC,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}