### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v1.1.0](https://github.com/ljharb/es-shim-unscopables/compare/v1.0.2...v1.1.0)

> 11 February 2025

- [New] add types [`2b94d6d`](https://github.com/ljharb/es-shim-unscopables/commit/2b94d6da58c272944de33f7e5b601a56dbb025fe)
- [Dev De<PERSON>] update `@ljharb/eslint-config`, `auto-changelog`, `es-value-fixtures`, `for-each`, `npmignore`, `object-inspect`, `tape` [`bc42bcf`](https://github.com/ljharb/es-shim-unscopables/commit/bc42bcf1901ad9c3e6427c8f39d1ac22e7e98310)
- [meta] add missing `engines.node` [`9ffcc49`](https://github.com/ljharb/es-shim-unscopables/commit/9ffcc491132ae0f7681694630910e1c4eb2cd548)

#### [v1.0.2](https://github.com/ljharb/es-shim-unscopables/compare/v1.0.1...v1.0.2)

> 20 October 2023

- [Deps] remove unused `has` [`f9fc3ba`](https://github.com/ljharb/es-shim-unscopables/commit/f9fc3bad9a291450ef1d1470347d0faffc937e14)

#### [v1.0.1](https://github.com/ljharb/es-shim-unscopables/compare/v1.0.0...v1.0.1)

> 20 October 2023

- [meta] use `npmignore` to autogenerate an npmignore file [`4f29eb9`](https://github.com/ljharb/es-shim-unscopables/commit/4f29eb9cfa28cbc08519446027accf8f7e3c472e)
- [Refactor] use `hasown` instead of `has` [`9d2577b`](https://github.com/ljharb/es-shim-unscopables/commit/9d2577b8bf6354d68111a8c20f020303370ef85f)
- [actions] update checkout action [`4525257`](https://github.com/ljharb/es-shim-unscopables/commit/45252572bf21dc5aab948c99ae0397e90f357718)

#### v1.0.0

> 11 April 2022

- Initial implementation, tests [`0313267`](https://github.com/ljharb/es-shim-unscopables/commit/03132672560a06df8a36685c1688793d445ea034)
- Initial commit [`5a68e27`](https://github.com/ljharb/es-shim-unscopables/commit/5a68e27e8f11d8b453c00e9239b35ec8795c850e)
- [meta] do not publish workflow files [`4e29785`](https://github.com/ljharb/es-shim-unscopables/commit/4e2978541c25ce590589d5f23f311af6ca3618a7)
