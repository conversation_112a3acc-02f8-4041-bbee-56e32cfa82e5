{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.165 6.598C9.954 7.478 9.64 8.36 9 9c-.64.64-1.521.954-2.402 1.165A6 6 0 0 0 8 22c7.732 0 14-6.268 14-14a6 6 0 0 0-11.835-1.402Z\",\n  key: \"1tvzk7\"\n}], [\"path\", {\n  d: \"M5.341 10.62a4 4 0 1 0 5.279-5.28\",\n  key: \"2cyri2\"\n}]];\nconst Bean = createLucideIcon(\"bean\", __iconNode);\nexport { __iconNode, Bean as default };\n//# sourceMappingURL=bean.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}