{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 14h-5\",\n  key: \"1vh23k\"\n}], [\"path\", {\n  d: \"M16 16v-3.5a2.5 2.5 0 0 1 5 0V16\",\n  key: \"1wh10o\"\n}], [\"path\", {\n  d: \"M4.5 13h6\",\n  key: \"dfilno\"\n}], [\"path\", {\n  d: \"m3 16 4.5-9 4.5 9\",\n  key: \"2dxa0e\"\n}]];\nconst ALargeSmall = createLucideIcon(\"a-large-small\", __iconNode);\nexport { __iconNode, ALargeSmall as default };\n//# sourceMappingURL=a-large-small.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}