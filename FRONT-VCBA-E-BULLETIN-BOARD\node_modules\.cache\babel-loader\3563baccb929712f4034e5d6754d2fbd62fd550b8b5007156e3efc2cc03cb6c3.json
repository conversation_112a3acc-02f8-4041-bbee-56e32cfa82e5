{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Calendar.tsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\nimport { calendarService } from '../../services/calendarService';\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon } from 'lucide-react';\nimport { getImageUrl } from '../../config/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Calendar = /*#__PURE__*/_s2(/*#__PURE__*/React.memo(_c = _s2(() => {\n  var _s = $RefreshSig$();\n  _s2();\n  const [currentDate, setCurrentDate] = useState(() => new Date());\n  const [selectedDate, setSelectedDate] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [editingEvent, setEditingEvent] = useState(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n\n  // Event attachments state\n  const [eventAttachments, setEventAttachments] = useState({});\n\n  // Use the calendar hook\n  const {\n    events,\n    loading,\n    error,\n    createEvent,\n    updateEvent,\n    getEventsForDate,\n    refresh\n  } = useCalendar(currentDate);\n  const {\n    categories\n  } = useCalendarCategories();\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage || error) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage, error]);\n  const handleCreateEvent = useCallback(date => {\n    setEditingEvent(null);\n    setSelectedDate(date || null);\n    setShowModal(true);\n  }, []);\n  const handleEditEvent = useCallback(event => {\n    setEditingEvent(event);\n    setShowModal(true);\n  }, []);\n  const handleSaveEvent = useCallback(async (data, applyPendingDeletes, onComplete) => {\n    try {\n      if (editingEvent) {\n        await updateEvent(editingEvent.calendar_id, data);\n\n        // Apply pending image deletions AFTER successful update\n        if (applyPendingDeletes) {\n          console.log('🗑️ Applying pending image deletions after successful update');\n          await applyPendingDeletes();\n        }\n        setSuccessMessage('Event updated successfully');\n      } else {\n        await createEvent(data);\n        setSuccessMessage('Event created successfully');\n      }\n\n      // Execute completion callback for additional operations\n      if (onComplete) {\n        await onComplete();\n      }\n      setShowModal(false);\n      setEditingEvent(null);\n      setSelectedDate(null);\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to save event');\n    }\n  }, [editingEvent, updateEvent, createEvent]);\n  const handleCloseModal = useCallback(() => {\n    setShowModal(false);\n    setEditingEvent(null);\n    setSelectedDate(null);\n  }, []);\n\n  // Event management functions\n  const handlePublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.publishEvent(eventId);\n      setSuccessMessage('Event published successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to publish event');\n    }\n  }, [refresh]);\n  const handleUnpublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.unpublishEvent(eventId);\n      setSuccessMessage('Event unpublished successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to unpublish event');\n    }\n  }, [refresh]);\n  const handleDeleteEvent = useCallback(async eventId => {\n    // Use window.confirm to avoid ESLint error\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await calendarService.softDeleteEvent(eventId);\n      setSuccessMessage('Event deleted successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to delete event');\n    }\n  }, [refresh]);\n  const navigateMonth = useCallback(direction => {\n    const newDate = new Date(currentDate);\n    if (direction === 'prev') {\n      newDate.setMonth(currentDate.getMonth() - 1);\n    } else {\n      newDate.setMonth(currentDate.getMonth() + 1);\n    }\n    setCurrentDate(newDate);\n  }, [currentDate]);\n  const goToToday = useCallback(() => {\n    setCurrentDate(new Date());\n  }, []);\n  const handleDateClick = useCallback(date => {\n    setSelectedDate(date);\n    handleCreateEvent(date);\n  }, [handleCreateEvent]);\n  const getEventTypeColor = useCallback(event => {\n    // Use category color if available, otherwise subcategory color, otherwise default\n    return event.category_color || event.subcategory_color || '#22c55e';\n  }, []);\n\n  // Helper function to format event duration\n  const getEventDuration = useCallback(event => {\n    if (!event.end_date || event.end_date === event.event_date) {\n      return 'Single day event';\n    }\n    const startDate = new Date(event.event_date);\n    const endDate = new Date(event.end_date);\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\n\n    return `${diffDays} day event`;\n  }, []);\n\n  // Helper function to get first two words from event title for calendar chip display\n  const getEventChipTitle = useCallback(title => {\n    const words = title.trim().split(/\\s+/);\n    return words.slice(0, 2).join(' ');\n  }, []);\n\n  // Memoize calendar days to prevent infinite re-renders\n  const days = useMemo(() => {\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\n\n  // Get unique events for the event list (deduplicate multi-day events)\n  const uniqueEvents = useMemo(() => {\n    const eventMap = new Map();\n    events.forEach(event => {\n      // Use calendar_id as the unique identifier\n      if (!eventMap.has(event.calendar_id)) {\n        eventMap.set(event.calendar_id, event);\n      }\n    });\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\n      // Sort by event_date, then by title\n      const dateA = new Date(a.event_date);\n      const dateB = new Date(b.event_date);\n      if (dateA.getTime() !== dateB.getTime()) {\n        return dateA.getTime() - dateB.getTime();\n      }\n      return a.title.localeCompare(b.title);\n    });\n\n    // Debug: Log deduplication results\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\n    return uniqueEventsList;\n  }, [events]);\n\n  // Filter events based on search term and holiday type\n  const filteredEvents = useMemo(() => {\n    return uniqueEvents.filter(event => {\n      var _event$description, _event$category_id;\n      const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_event$description = event.description) === null || _event$description === void 0 ? void 0 : _event$description.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesCategory = !selectedCategory || ((_event$category_id = event.category_id) === null || _event$category_id === void 0 ? void 0 : _event$category_id.toString()) === selectedCategory;\n      return matchesSearch && matchesCategory;\n    });\n  }, [uniqueEvents, searchTerm, selectedCategory]);\n\n  // Pagination calculations\n  const totalItems = filteredEvents.length;\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, selectedCategory, itemsPerPage]);\n\n  // Fetch attachments for visible events\n  useEffect(() => {\n    const fetchAttachments = async () => {\n      if (!paginatedEvents || paginatedEvents.length === 0) return;\n      const attachmentPromises = paginatedEvents.map(async event => {\n        if (eventAttachments[event.calendar_id]) return; // Already fetched\n\n        try {\n          var _response$data;\n          const response = await calendarService.getEventAttachments(event.calendar_id);\n          if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.attachments) {\n            setEventAttachments(prev => ({\n              ...prev,\n              [event.calendar_id]: response.data.attachments\n            }));\n          }\n        } catch (error) {\n          console.error(`Failed to fetch attachments for event ${event.calendar_id}:`, error);\n        }\n      });\n      await Promise.all(attachmentPromises);\n    };\n    fetchAttachments();\n  }, [paginatedEvents, eventAttachments]);\n\n  // Component to display event images\n  const EventImages = ({\n    eventId\n  }) => {\n    const attachments = eventAttachments[eventId] || [];\n    const imageAttachments = attachments.filter(att => att.file_type === 'image');\n    if (imageAttachments.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '60px',\n          height: '60px',\n          backgroundColor: '#f3f4f6',\n          borderRadius: '6px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#9ca3af',\n          fontSize: '0.75rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '0.5rem',\n        flexWrap: 'wrap'\n      },\n      children: [imageAttachments.slice(0, 3).map(attachment => /*#__PURE__*/_jsxDEV(EventImageThumbnail, {\n        attachment: attachment\n      }, attachment.attachment_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this)), imageAttachments.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '60px',\n          height: '60px',\n          backgroundColor: '#f3f4f6',\n          borderRadius: '6px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#6b7280',\n          fontSize: '0.75rem',\n          fontWeight: '500'\n        },\n        children: [\"+\", imageAttachments.length - 3]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Component to display individual image thumbnail\n  const EventImageThumbnail = ({\n    attachment\n  }) => {\n    _s();\n    const [imageUrl, setImageUrl] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(false);\n    useEffect(() => {\n      const loadImage = async () => {\n        try {\n          const fullUrl = getImageUrl(attachment.file_path);\n          if (!fullUrl) {\n            throw new Error('Invalid image path');\n          }\n          const response = await fetch(fullUrl, {\n            method: 'GET',\n            headers: {\n              'Origin': window.location.origin\n            },\n            mode: 'cors'\n          });\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}`);\n          }\n          const blob = await response.blob();\n          const objectUrl = URL.createObjectURL(blob);\n          setImageUrl(objectUrl);\n        } catch (err) {\n          console.error('Failed to load image:', err);\n          setError(true);\n        } finally {\n          setLoading(false);\n        }\n      };\n      loadImage();\n      return () => {\n        if (imageUrl) {\n          URL.revokeObjectURL(imageUrl);\n        }\n      };\n    }, [attachment.file_path, imageUrl]);\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '60px',\n          height: '60px',\n          backgroundColor: '#f3f4f6',\n          borderRadius: '6px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#9ca3af'\n        },\n        children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this);\n    }\n    if (error || !imageUrl) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '60px',\n          height: '60px',\n          backgroundColor: '#fef2f2',\n          borderRadius: '6px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#ef4444',\n          fontSize: '0.75rem'\n        },\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        width: '60px',\n        height: '60px',\n        borderRadius: '6px',\n        overflow: 'hidden',\n        border: attachment.is_primary ? '2px solid #22c55e' : '1px solid #e5e7eb'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: attachment.file_name,\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Pagination component - Always visible\n  _s(EventImageThumbnail, \"++2eAeZSfFqW2E2NW4b16UXG/uY=\");\n  const PaginationControls = () => {\n    // Always show pagination controls, even for single page\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\n\n    const getPageNumbers = () => {\n      const pages = [];\n      const maxVisiblePages = 5;\n      if (effectiveTotalPages <= maxVisiblePages) {\n        for (let i = 1; i <= effectiveTotalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        if (effectiveCurrentPage <= 3) {\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\n        } else {\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\n        }\n      }\n      return pages;\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '2rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        border: '1px solid #e5e7eb',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"Show:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: itemsPerPage,\n          onChange: e => setItemsPerPage(Number(e.target.value)),\n          style: {\n            padding: '0.25rem 0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            fontSize: '0.875rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5,\n            children: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10,\n            children: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 20,\n            children: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 50,\n            children: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"per page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [\"Showing \", Math.max(totalItems > 0 ? startIndex + 1 : 0, 0), \"-\", Math.min(endIndex, totalItems), \" of \", totalItems, \" events\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.25rem',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(1),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"First\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.max(1, prev - 1)),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), getPageNumbers().map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => typeof page === 'number' && setCurrentPage(page),\n          disabled: page === '...',\n          style: {\n            padding: '0.5rem 0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\n            color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\n            cursor: page === '...' ? 'default' : 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: page === effectiveCurrentPage ? '600' : '400'\n          },\n          children: page\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1)),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(effectiveTotalPages),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Last\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    \"data-calendar-component\": \"main\",\n    children: [successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        color: '#166534',\n        borderRadius: '8px'\n      },\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '8px'\n      },\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1rem',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n        border: '1px solid #e8f5e8',\n        position: 'relative',\n        zIndex: 10\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '1.25rem',\n              fontWeight: '600',\n              color: '#2d5016',\n              margin: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                size: 16,\n                color: \"#1e40af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), \"School Calendar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280',\n              margin: '0 0 0 0.5rem',\n              fontSize: '0.875rem'\n            },\n            children: [getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('prev'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToToday,\n            style: {\n              padding: '0.25rem 0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('next'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCreateEvent(),\n            style: {\n              display: 'inline-flex',\n              alignItems: 'center',\n              padding: '0.5rem 1rem',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n            },\n            onMouseOver: e => {\n              e.currentTarget.style.transform = 'translateY(-1px)';\n              e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: \"+ Add Event\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)',\n          backgroundColor: '#f9fafb',\n          borderBottom: '1px solid #e5e7eb'\n        },\n        children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            textAlign: 'center',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#374151'\n          },\n          children: day\n        }, day, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)'\n        },\n        children: days.map((date, index) => {\n          const dayEvents = getEventsForDate(date);\n          const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\n          const isTodayDate = isToday(date);\n\n          // Create unique key based on date to prevent React key conflicts\n          // Use date timestamp as key since it's unique and stable across re-renders\n          const dateKey = `calendar-day-${date.getTime()}`;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              minHeight: '120px',\n              padding: '0.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb',\n              // Remove right border on last column\n              cursor: 'pointer',\n              backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\n              color: !isCurrentMonth ? '#9ca3af' : '#374151',\n              transition: 'all 0.2s ease',\n              position: 'relative'\n            },\n            onClick: () => handleDateClick(date),\n            onMouseOver: e => {\n              if (isCurrentMonth) {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n                e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\n              }\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                marginBottom: '0.25rem',\n                color: isTodayDate ? '#2563eb' : 'inherit'\n              },\n              children: date.getDate()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.25rem'\n              },\n              children: [dayEvents.slice(0, 3).map(event => {\n                // Determine styling for multi-day events\n                const isMultiDay = event.isMultiDay;\n                const isStart = event.isEventStart;\n                const isEnd = event.isEventEnd;\n                const isContinuation = isMultiDay && !isStart && !isEnd;\n                const eventColor = getEventTypeColor(event);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.75rem',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: isMultiDay ? isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px' : '6px',\n                    backgroundColor: eventColor + (isContinuation ? '25' : '15'),\n                    border: `1px solid ${eventColor}`,\n                    borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    cursor: 'pointer',\n                    position: 'relative',\n                    color: '#374151',\n                    fontWeight: '500',\n                    transition: 'all 0.2s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleEditEvent(event);\n                  },\n                  onMouseOver: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + '30';\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\n                  },\n                  onMouseOut: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  title: isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title,\n                  children: [isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25B6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 27\n                  }, this), isContinuation && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25AC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 27\n                  }, this), isEnd && isMultiDay && !isStart && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25C0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      flex: 1,\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis'\n                    },\n                    children: getEventChipTitle(event.title)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 25\n                  }, this), isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem',\n                      opacity: 0.7\n                    },\n                    children: \"\\u2192\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 27\n                  }, this)]\n                }, `event-${event.calendar_id}-${date.getTime()}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 23\n                }, this);\n              }), dayEvents.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280'\n                },\n                children: [\"+\", dayEvents.length - 3, \" more\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 17\n            }, this)]\n          }, dateKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        margin: '2rem 0',\n        padding: '1.5rem',\n        backgroundColor: '#f8fafc'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1.5rem',\n          alignItems: 'end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Search Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              size: 16,\n              color: \"#9ca3af\",\n              style: {\n                position: 'absolute',\n                left: '0.75rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search events...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              },\n              onFocus: e => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              },\n              onBlur: e => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 934,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Holiday Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedHolidayType,\n            onChange: e => setSelectedHolidayType(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white',\n              transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\n              minHeight: '2.5rem',\n              lineHeight: '1.2'\n            },\n            onFocus: e => {\n              e.currentTarget.style.borderColor = '#22c55e';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n            },\n            onBlur: e => {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 15\n            }, this), holidayTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type.type_id,\n              children: type.type_name\n            }, type.type_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refresh,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease',\n              height: '2.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#2d5016',\n            margin: 0\n          },\n          children: [\"Events for \", getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: [filteredEvents.length, \" event\", filteredEvents.length !== 1 ? 's' : '', \" found\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1064,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 24,\n          style: {\n            marginBottom: '1rem',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1087,\n        columnNumber: 11\n      }, this) : filteredEvents.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No events found for this month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleCreateEvent(),\n          style: {\n            marginTop: '1rem',\n            padding: '0.75rem 1.5rem',\n            backgroundColor: '#22c55e',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontWeight: '500'\n          },\n          children: \"Create First Event\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1103,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1096,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '1rem'\n        },\n        children: paginatedEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: '1px solid #e5e7eb',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            backgroundColor: 'white',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: '0 0 0.5rem 0'\n                },\n                children: event.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1158,\n                    columnNumber: 25\n                  }, this), event.event_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 23\n                }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2192 \", event.end_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: '#f3f4f6',\n                    color: '#374151',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  },\n                  children: getEventDuration(event)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: getEventTypeColor(event),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem'\n                  },\n                  children: event.holiday_type_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1174,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1150,\n                columnNumber: 21\n              }, this), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  margin: '0.5rem 0 0 0',\n                  lineHeight: '1.5'\n                },\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem',\n                marginLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditEvent(event),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Edit event\",\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1217,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => event.is_published ? handleUnpublishEvent(event.calendar_id) : handlePublishEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: event.is_published ? '#fef3c7' : '#dcfce7',\n                  color: event.is_published ? '#d97706' : '#16a34a',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: event.is_published ? 'Unpublish event' : 'Publish event',\n                children: event.is_published ? /*#__PURE__*/_jsxDEV(Clock, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 54\n                }, this) : /*#__PURE__*/_jsxDEV(Send, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 76\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#fef2f2',\n                  color: '#dc2626',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Delete event\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1197,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1135,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f3f4f6',\n              paddingTop: '1rem',\n              marginTop: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Event Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(EventImages, {\n              eventId: event.calendar_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1274,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1261,\n            columnNumber: 17\n          }, this)]\n        }, `unique-event-${event.calendar_id}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1120,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1056,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaginationControls, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CalendarEventModal, {\n      isOpen: showModal,\n      onClose: handleCloseModal,\n      onSave: handleSaveEvent,\n      event: editingEvent,\n      selectedDate: selectedDate,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 582,\n    columnNumber: 5\n  }, this);\n}, \"8cDCa2/zFU68Ilyj7Z9dTqPJ1vE=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n})), \"8cDCa2/zFU68Ilyj7Z9dTqPJ1vE=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n});\n_c2 = Calendar;\nCalendar.displayName = 'Calendar';\nexport default Calendar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Calendar$React.memo\");\n$RefreshReg$(_c2, \"Calendar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useCalendar", "useCalendarCategories", "getCalendarDays", "isToday", "isSameMonth", "getMonthName", "CalendarEventModal", "calendarService", "Calendar", "CalendarIcon", "Search", "RefreshCw", "Trash2", "Edit", "Send", "Clock", "Image", "ImageIcon", "getImageUrl", "jsxDEV", "_jsxDEV", "_s2", "memo", "_c", "_s", "$RefreshSig$", "currentDate", "setCurrentDate", "Date", "selectedDate", "setSelectedDate", "showModal", "setShowModal", "editingEvent", "setEditingEvent", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "eventAttachments", "setEventAttachments", "events", "loading", "error", "createEvent", "updateEvent", "getEventsForDate", "refresh", "categories", "timer", "setTimeout", "clearTimeout", "handleCreateEvent", "date", "handleEditEvent", "event", "handleSaveEvent", "data", "applyPendingDeletes", "onComplete", "calendar_id", "console", "log", "message", "handleCloseModal", "handlePublishEvent", "eventId", "publishEvent", "handleUnpublishEvent", "unpublishEvent", "handleDeleteEvent", "window", "confirm", "softDeleteEvent", "navigateMonth", "direction", "newDate", "setMonth", "getMonth", "goToToday", "handleDateClick", "getEventTypeColor", "category_color", "subcategory_color", "getEventDuration", "end_date", "event_date", "startDate", "endDate", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "getEventChipTitle", "title", "words", "trim", "split", "slice", "join", "days", "getFullYear", "uniqueEvents", "eventMap", "Map", "for<PERSON>ach", "has", "set", "uniqueEventsList", "Array", "from", "values", "sort", "a", "b", "dateA", "dateB", "localeCompare", "length", "filteredEvents", "filter", "_event$description", "_event$category_id", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "category_id", "toString", "totalItems", "totalPages", "startIndex", "endIndex", "paginatedEvents", "fetchAttachments", "attachmentPromises", "map", "_response$data", "response", "getEventAttachments", "success", "attachments", "prev", "Promise", "all", "EventImages", "imageAttachments", "att", "file_type", "style", "width", "height", "backgroundColor", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontSize", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "flexWrap", "attachment", "EventImageThumbnail", "attachment_id", "fontWeight", "imageUrl", "setImageUrl", "setLoading", "setError", "loadImage", "fullUrl", "file_path", "Error", "fetch", "method", "headers", "location", "origin", "mode", "ok", "status", "blob", "objectUrl", "URL", "createObjectURL", "err", "revokeObjectURL", "position", "overflow", "border", "is_primary", "src", "alt", "file_name", "objectFit", "PaginationControls", "effectiveTotalPages", "max", "effectiveCurrentPage", "getPageNumbers", "pages", "maxVisiblePages", "i", "push", "marginTop", "marginBottom", "padding", "boxShadow", "value", "onChange", "e", "Number", "target", "min", "onClick", "disabled", "cursor", "page", "index", "max<PERSON><PERSON><PERSON>", "margin", "background", "zIndex", "transition", "onMouseOver", "currentTarget", "onMouseOut", "transform", "gridTemplateColumns", "borderBottom", "day", "textAlign", "dayEvents", "isCurrentMonth", "isTodayDate", "<PERSON><PERSON><PERSON>", "minHeight", "borderRight", "getDate", "flexDirection", "isMultiDay", "isStart", "isEventStart", "isEnd", "isEventEnd", "isContinuation", "eventColor", "borderLeft", "textOverflow", "whiteSpace", "stopPropagation", "originalStartDate", "originalEndDate", "flex", "opacity", "left", "top", "pointerEvents", "type", "placeholder", "outline", "onFocus", "borderColor", "onBlur", "selectedHolidayType", "setSelectedHolidayType", "lineHeight", "holidayTypes", "type_id", "type_name", "animation", "className", "holiday_type_name", "marginLeft", "is_published", "borderTop", "paddingTop", "isOpen", "onClose", "onSave", "_c2", "displayName", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Calendar.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\r\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\r\nimport type { CalendarEvent, CreateEventData, UpdateEventData } from '../../types/calendar.types';\r\nimport { calendarService } from '../../services/calendarService';\r\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon } from 'lucide-react';\r\nimport type { CalendarAttachment } from '../../hooks/useCalendarImageUpload';\r\nimport { getImageUrl } from '../../config/constants';\r\n\r\nconst Calendar: React.FC = React.memo(() => {\r\n  const [currentDate, setCurrentDate] = useState(() => new Date());\r\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(10);\r\n\r\n  // Event attachments state\r\n  const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\r\n\r\n  // Use the calendar hook\r\n  const {\r\n    events,\r\n    loading,\r\n    error,\r\n    createEvent,\r\n    updateEvent,\r\n    getEventsForDate,\r\n    refresh\r\n  } = useCalendar(currentDate);\r\n\r\n  const { categories } = useCalendarCategories();\r\n\r\n  // Clear messages after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage || errorMessage || error) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage, errorMessage, error]);\r\n\r\n  const handleCreateEvent = useCallback((date?: Date) => {\r\n    setEditingEvent(null);\r\n    setSelectedDate(date || null);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n  const handleEditEvent = useCallback((event: CalendarEvent) => {\r\n    setEditingEvent(event);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n\r\n\r\n  const handleSaveEvent = useCallback(async (\r\n    data: CreateEventData | UpdateEventData,\r\n    applyPendingDeletes?: () => Promise<void>,\r\n    onComplete?: () => Promise<void>\r\n  ) => {\r\n    try {\r\n      if (editingEvent) {\r\n        await updateEvent(editingEvent.calendar_id, data as UpdateEventData);\r\n\r\n        // Apply pending image deletions AFTER successful update\r\n        if (applyPendingDeletes) {\r\n          console.log('🗑️ Applying pending image deletions after successful update');\r\n          await applyPendingDeletes();\r\n        }\r\n\r\n        setSuccessMessage('Event updated successfully');\r\n      } else {\r\n        await createEvent(data as CreateEventData);\r\n        setSuccessMessage('Event created successfully');\r\n      }\r\n\r\n      // Execute completion callback for additional operations\r\n      if (onComplete) {\r\n        await onComplete();\r\n      }\r\n\r\n      setShowModal(false);\r\n      setEditingEvent(null);\r\n      setSelectedDate(null);\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to save event');\r\n    }\r\n  }, [editingEvent, updateEvent, createEvent]);\r\n\r\n  const handleCloseModal = useCallback(() => {\r\n    setShowModal(false);\r\n    setEditingEvent(null);\r\n    setSelectedDate(null);\r\n  }, []);\r\n\r\n  // Event management functions\r\n  const handlePublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.publishEvent(eventId);\r\n      setSuccessMessage('Event published successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to publish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleUnpublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.unpublishEvent(eventId);\r\n      setSuccessMessage('Event unpublished successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to unpublish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleDeleteEvent = useCallback(async (eventId: number) => {\r\n    // Use window.confirm to avoid ESLint error\r\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await calendarService.softDeleteEvent(eventId);\r\n      setSuccessMessage('Event deleted successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to delete event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const navigateMonth = useCallback((direction: 'prev' | 'next') => {\r\n    const newDate = new Date(currentDate);\r\n    if (direction === 'prev') {\r\n      newDate.setMonth(currentDate.getMonth() - 1);\r\n    } else {\r\n      newDate.setMonth(currentDate.getMonth() + 1);\r\n    }\r\n    setCurrentDate(newDate);\r\n  }, [currentDate]);\r\n\r\n  const goToToday = useCallback(() => {\r\n    setCurrentDate(new Date());\r\n  }, []);\r\n\r\n  const handleDateClick = useCallback((date: Date) => {\r\n    setSelectedDate(date);\r\n    handleCreateEvent(date);\r\n  }, [handleCreateEvent]);\r\n\r\n  const getEventTypeColor = useCallback((event: CalendarEvent) => {\r\n    // Use category color if available, otherwise subcategory color, otherwise default\r\n    return event.category_color || event.subcategory_color || '#22c55e';\r\n  }, []);\r\n\r\n  // Helper function to format event duration\r\n  const getEventDuration = useCallback((event: CalendarEvent) => {\r\n    if (!event.end_date || event.end_date === event.event_date) {\r\n      return 'Single day event';\r\n    }\r\n\r\n    const startDate = new Date(event.event_date);\r\n    const endDate = new Date(event.end_date);\r\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\r\n\r\n    return `${diffDays} day event`;\r\n  }, []);\r\n\r\n  // Helper function to get first two words from event title for calendar chip display\r\n  const getEventChipTitle = useCallback((title: string) => {\r\n    const words = title.trim().split(/\\s+/);\r\n    return words.slice(0, 2).join(' ');\r\n  }, []);\r\n\r\n  // Memoize calendar days to prevent infinite re-renders\r\n  const days = useMemo(() => {\r\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\r\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\r\n\r\n  // Get unique events for the event list (deduplicate multi-day events)\r\n  const uniqueEvents = useMemo(() => {\r\n    const eventMap = new Map();\r\n\r\n    events.forEach(event => {\r\n      // Use calendar_id as the unique identifier\r\n      if (!eventMap.has(event.calendar_id)) {\r\n        eventMap.set(event.calendar_id, event);\r\n      }\r\n    });\r\n\r\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\r\n      // Sort by event_date, then by title\r\n      const dateA = new Date(a.event_date);\r\n      const dateB = new Date(b.event_date);\r\n      if (dateA.getTime() !== dateB.getTime()) {\r\n        return dateA.getTime() - dateB.getTime();\r\n      }\r\n      return a.title.localeCompare(b.title);\r\n    });\r\n\r\n    // Debug: Log deduplication results\r\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\r\n\r\n    return uniqueEventsList;\r\n  }, [events]);\r\n\r\n  // Filter events based on search term and holiday type\r\n  const filteredEvents = useMemo(() => {\r\n    return uniqueEvents.filter(event => {\r\n      const matchesSearch = !searchTerm ||\r\n        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        event.description?.toLowerCase().includes(searchTerm.toLowerCase());\r\n\r\n      const matchesCategory = !selectedCategory ||\r\n        event.category_id?.toString() === selectedCategory;\r\n\r\n      return matchesSearch && matchesCategory;\r\n    });\r\n  }, [uniqueEvents, searchTerm, selectedCategory]);\r\n\r\n  // Pagination calculations\r\n  const totalItems = filteredEvents.length;\r\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\r\n\r\n  // Reset to first page when filters change\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [searchTerm, selectedCategory, itemsPerPage]);\r\n\r\n  // Fetch attachments for visible events\r\n  useEffect(() => {\r\n    const fetchAttachments = async () => {\r\n      if (!paginatedEvents || paginatedEvents.length === 0) return;\r\n\r\n      const attachmentPromises = paginatedEvents.map(async (event) => {\r\n        if (eventAttachments[event.calendar_id]) return; // Already fetched\r\n\r\n        try {\r\n          const response = await calendarService.getEventAttachments(event.calendar_id);\r\n          if (response.success && response.data?.attachments) {\r\n            setEventAttachments(prev => ({\r\n              ...prev,\r\n              [event.calendar_id]: response.data!.attachments\r\n            }));\r\n          }\r\n        } catch (error) {\r\n          console.error(`Failed to fetch attachments for event ${event.calendar_id}:`, error);\r\n        }\r\n      });\r\n\r\n      await Promise.all(attachmentPromises);\r\n    };\r\n\r\n    fetchAttachments();\r\n  }, [paginatedEvents, eventAttachments]);\r\n\r\n  // Component to display event images\r\n  const EventImages: React.FC<{ eventId: number }> = ({ eventId }) => {\r\n    const attachments = eventAttachments[eventId] || [];\r\n    const imageAttachments = attachments.filter(att => att.file_type === 'image');\r\n\r\n    if (imageAttachments.length === 0) {\r\n      return (\r\n        <div style={{\r\n          width: '60px',\r\n          height: '60px',\r\n          backgroundColor: '#f3f4f6',\r\n          borderRadius: '6px',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          color: '#9ca3af',\r\n          fontSize: '0.75rem'\r\n        }}>\r\n          <ImageIcon size={20} />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        gap: '0.5rem',\r\n        flexWrap: 'wrap'\r\n      }}>\r\n        {imageAttachments.slice(0, 3).map((attachment) => (\r\n          <EventImageThumbnail key={attachment.attachment_id} attachment={attachment} />\r\n        ))}\r\n        {imageAttachments.length > 3 && (\r\n          <div style={{\r\n            width: '60px',\r\n            height: '60px',\r\n            backgroundColor: '#f3f4f6',\r\n            borderRadius: '6px',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            color: '#6b7280',\r\n            fontSize: '0.75rem',\r\n            fontWeight: '500'\r\n          }}>\r\n            +{imageAttachments.length - 3}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Component to display individual image thumbnail\r\n  const EventImageThumbnail: React.FC<{ attachment: CalendarAttachment }> = ({ attachment }) => {\r\n    const [imageUrl, setImageUrl] = useState<string | null>(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(false);\r\n\r\n    useEffect(() => {\r\n      const loadImage = async () => {\r\n        try {\r\n          const fullUrl = getImageUrl(attachment.file_path);\r\n          if (!fullUrl) {\r\n            throw new Error('Invalid image path');\r\n          }\r\n\r\n          const response = await fetch(fullUrl, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Origin': window.location.origin,\r\n            },\r\n            mode: 'cors',\r\n          });\r\n\r\n          if (!response.ok) {\r\n            throw new Error(`HTTP ${response.status}`);\r\n          }\r\n\r\n          const blob = await response.blob();\r\n          const objectUrl = URL.createObjectURL(blob);\r\n          setImageUrl(objectUrl);\r\n        } catch (err) {\r\n          console.error('Failed to load image:', err);\r\n          setError(true);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      };\r\n\r\n      loadImage();\r\n\r\n      return () => {\r\n        if (imageUrl) {\r\n          URL.revokeObjectURL(imageUrl);\r\n        }\r\n      };\r\n    }, [attachment.file_path, imageUrl]);\r\n\r\n    if (loading) {\r\n      return (\r\n        <div style={{\r\n          width: '60px',\r\n          height: '60px',\r\n          backgroundColor: '#f3f4f6',\r\n          borderRadius: '6px',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          color: '#9ca3af'\r\n        }}>\r\n          <ImageIcon size={16} />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (error || !imageUrl) {\r\n      return (\r\n        <div style={{\r\n          width: '60px',\r\n          height: '60px',\r\n          backgroundColor: '#fef2f2',\r\n          borderRadius: '6px',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          color: '#ef4444',\r\n          fontSize: '0.75rem'\r\n        }}>\r\n          Error\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div style={{\r\n        position: 'relative',\r\n        width: '60px',\r\n        height: '60px',\r\n        borderRadius: '6px',\r\n        overflow: 'hidden',\r\n        border: attachment.is_primary ? '2px solid #22c55e' : '1px solid #e5e7eb'\r\n      }}>\r\n        <img\r\n          src={imageUrl}\r\n          alt={attachment.file_name}\r\n          style={{\r\n            width: '100%',\r\n            height: '100%',\r\n            objectFit: 'cover'\r\n          }}\r\n        />\r\n\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Pagination component - Always visible\r\n  const PaginationControls = () => {\r\n    // Always show pagination controls, even for single page\r\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\r\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\r\n\r\n    const getPageNumbers = () => {\r\n      const pages = [];\r\n      const maxVisiblePages = 5;\r\n\r\n      if (effectiveTotalPages <= maxVisiblePages) {\r\n        for (let i = 1; i <= effectiveTotalPages; i++) {\r\n          pages.push(i);\r\n        }\r\n      } else {\r\n        if (effectiveCurrentPage <= 3) {\r\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\r\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\r\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\r\n        } else {\r\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\r\n        }\r\n      }\r\n\r\n      return pages;\r\n    };\r\n\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginTop: '2rem',\r\n        marginBottom: '2rem',\r\n        padding: '1.5rem',\r\n        backgroundColor: 'white',\r\n        borderRadius: '12px',\r\n        border: '1px solid #e5e7eb',\r\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\r\n      }}>\r\n        {/* Items per page selector */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Show:</span>\r\n          <select\r\n            value={itemsPerPage}\r\n            onChange={(e) => setItemsPerPage(Number(e.target.value))}\r\n            style={{\r\n              padding: '0.25rem 0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            <option value={5}>5</option>\r\n            <option value={10}>10</option>\r\n            <option value={20}>20</option>\r\n            <option value={50}>50</option>\r\n          </select>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>per page</span>\r\n        </div>\r\n\r\n        {/* Page info */}\r\n        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\r\n          Showing {Math.max(totalItems > 0 ? startIndex + 1 : 0, 0)}-{Math.min(endIndex, totalItems)} of {totalItems} events\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>\r\n          <button\r\n            onClick={() => setCurrentPage(1)}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            First\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Previous\r\n          </button>\r\n\r\n          {getPageNumbers().map((page, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => typeof page === 'number' && setCurrentPage(page)}\r\n              disabled={page === '...'}\r\n              style={{\r\n                padding: '0.5rem 0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '4px',\r\n                backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\r\n                color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\r\n                cursor: page === '...' ? 'default' : 'pointer',\r\n                fontSize: '0.875rem',\r\n                fontWeight: page === effectiveCurrentPage ? '600' : '400'\r\n              }}\r\n            >\r\n              {page}\r\n            </button>\r\n          ))}\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1))}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Next\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(effectiveTotalPages)}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Last\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }} data-calendar-component=\"main\">\r\n      {/* Success/Error Messages */}\r\n      {successMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#f0fdf4',\r\n          border: '1px solid #bbf7d0',\r\n          color: '#166534',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n      {errorMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#fef2f2',\r\n          border: '1px solid #fecaca',\r\n          color: '#dc2626',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {errorMessage}\r\n        </div>\r\n      )}\r\n\r\n      {/* Calendar Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '8px',\r\n        padding: '1rem',\r\n        marginBottom: '1rem',\r\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\r\n        border: '1px solid #e8f5e8',\r\n        position: 'relative',\r\n        zIndex: 10\r\n      }}>\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '0.5rem' }}>\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n            <h1 style={{\r\n              fontSize: '1.25rem',\r\n              fontWeight: '600',\r\n              color: '#2d5016',\r\n              margin: 0\r\n            }}>\r\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n                <CalendarIcon size={16} color=\"#1e40af\" />\r\n                School Calendar\r\n              </span>\r\n            </h1>\r\n            <p style={{\r\n              color: '#6b7280',\r\n              margin: '0 0 0 0.5rem',\r\n              fontSize: '0.875rem'\r\n            }}>\r\n              {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n            <button\r\n              onClick={() => navigateMonth('prev')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              ←\r\n            </button>\r\n\r\n            <button\r\n              onClick={goToToday}\r\n              style={{\r\n                padding: '0.25rem 0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              Today\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => navigateMonth('next')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              →\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                display: 'inline-flex',\r\n                alignItems: 'center',\r\n                padding: '0.5rem 1rem',\r\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(-1px)';\r\n                e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\r\n              }}\r\n              onMouseOut={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(0)';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              + Add Event\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Calendar Grid */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        overflow: 'hidden'\r\n      }}>\r\n        {/* Calendar Header */}\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(7, 1fr)',\r\n          backgroundColor: '#f9fafb',\r\n          borderBottom: '1px solid #e5e7eb'\r\n        }}>\r\n          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (\r\n            <div key={day} style={{\r\n              padding: '1rem',\r\n              textAlign: 'center',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151'\r\n            }}>\r\n              {day}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Calendar Body */}\r\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)' }}>\r\n          {days.map((date, index) => {\r\n            const dayEvents = getEventsForDate(date);\r\n            const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\r\n            const isTodayDate = isToday(date);\r\n\r\n            // Create unique key based on date to prevent React key conflicts\r\n            // Use date timestamp as key since it's unique and stable across re-renders\r\n            const dateKey = `calendar-day-${date.getTime()}`;\r\n\r\n            return (\r\n              <div\r\n                key={dateKey}\r\n                style={{\r\n                  minHeight: '120px',\r\n                  padding: '0.5rem',\r\n                  borderBottom: '1px solid #e5e7eb',\r\n                  borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb', // Remove right border on last column\r\n                  cursor: 'pointer',\r\n                  backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\r\n                  color: !isCurrentMonth ? '#9ca3af' : '#374151',\r\n                  transition: 'all 0.2s ease',\r\n                  position: 'relative'\r\n                }}\r\n                onClick={() => handleDateClick(date)}\r\n                onMouseOver={(e) => {\r\n                  if (isCurrentMonth) {\r\n                    e.currentTarget.style.backgroundColor = '#f3f4f6';\r\n                    e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\r\n                  }\r\n                }}\r\n                onMouseOut={(e) => {\r\n                  e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              >\r\n                <div style={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: '500',\r\n                  marginBottom: '0.25rem',\r\n                  color: isTodayDate ? '#2563eb' : 'inherit'\r\n                }}>\r\n                  {date.getDate()}\r\n                </div>\r\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>\r\n                  {dayEvents.slice(0, 3).map((event) => {\r\n                    // Determine styling for multi-day events\r\n                    const isMultiDay = event.isMultiDay;\r\n                    const isStart = event.isEventStart;\r\n                    const isEnd = event.isEventEnd;\r\n                    const isContinuation = isMultiDay && !isStart && !isEnd;\r\n                    const eventColor = getEventTypeColor(event);\r\n\r\n                    return (\r\n                      <div\r\n                        key={`event-${event.calendar_id}-${date.getTime()}`}\r\n                        style={{\r\n                          fontSize: '0.75rem',\r\n                          padding: '0.25rem 0.5rem',\r\n                          borderRadius: isMultiDay ? (isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px') : '6px',\r\n                          backgroundColor: eventColor + (isContinuation ? '25' : '15'),\r\n                          border: `1px solid ${eventColor}`,\r\n                          borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis',\r\n                          whiteSpace: 'nowrap',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          color: '#374151',\r\n                          fontWeight: '500',\r\n                          transition: 'all 0.2s ease',\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: '0.25rem'\r\n                        }}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleEditEvent(event);\r\n                        }}\r\n                        onMouseOver={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + '30';\r\n                          e.currentTarget.style.transform = 'translateY(-1px)';\r\n                          e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\r\n                        }}\r\n                        onMouseOut={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}\r\n                        title={isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title}\r\n                      >\r\n                        {/* Start indicator */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▶\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Continuation indicator */}\r\n                        {isContinuation && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▬\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* End indicator */}\r\n                        {isEnd && isMultiDay && !isStart && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ◀\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Event title */}\r\n                        <span style={{\r\n                          flex: 1,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis'\r\n                        }}>\r\n                          {getEventChipTitle(event.title)}\r\n                        </span>\r\n\r\n                        {/* End arrow for start day */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem',\r\n                            opacity: 0.7\r\n                          }}>\r\n                            →\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {dayEvents.length > 3 && (\r\n                    <div style={{\r\n                      fontSize: '0.75rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      +{dayEvents.length - 3} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Filters */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        margin: '2rem 0',\r\n        padding: '1.5rem',\r\n        backgroundColor: '#f8fafc'\r\n      }}>\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\r\n          gap: '1.5rem',\r\n          alignItems: 'end'\r\n        }}>\r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Search Events\r\n            </label>\r\n            <div style={{ position: 'relative' }}>\r\n              <Search\r\n                size={16}\r\n                color=\"#9ca3af\"\r\n                style={{\r\n                  position: 'absolute',\r\n                  left: '0.75rem',\r\n                  top: '50%',\r\n                  transform: 'translateY(-50%)',\r\n                  pointerEvents: 'none'\r\n                }}\r\n              />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                style={{\r\n                  width: '100%',\r\n                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',\r\n                  border: '1px solid #d1d5db',\r\n                  borderRadius: '8px',\r\n                  fontSize: '0.875rem',\r\n                  outline: 'none',\r\n                  transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\r\n                }}\r\n                onFocus={(e) => {\r\n                  e.currentTarget.style.borderColor = '#22c55e';\r\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n                }}\r\n                onBlur={(e) => {\r\n                  e.currentTarget.style.borderColor = '#d1d5db';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Holiday Type\r\n            </label>\r\n            <select\r\n              value={selectedHolidayType}\r\n              onChange={(e) => setSelectedHolidayType(e.target.value)}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '8px',\r\n                fontSize: '0.875rem',\r\n                outline: 'none',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\r\n                minHeight: '2.5rem',\r\n                lineHeight: '1.2'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.currentTarget.style.borderColor = '#22c55e';\r\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.currentTarget.style.borderColor = '#d1d5db';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              <option value=\"\">All Types</option>\r\n              {holidayTypes.map((type) => (\r\n                <option key={type.type_id} value={type.type_id}>\r\n                  {type.type_name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n          \r\n          <div>\r\n            <button\r\n              onClick={refresh}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease',\r\n                height: '2.5rem',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                gap: '0.5rem'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              <RefreshCw size={16} />\r\n              Refresh\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Event List Section */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        padding: '2rem',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        marginBottom: '1rem'\r\n      }}>\r\n        <div style={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          marginBottom: '1.5rem'\r\n        }}>\r\n          <h2 style={{\r\n            fontSize: '1.5rem',\r\n            fontWeight: '600',\r\n            color: '#2d5016',\r\n            margin: 0\r\n          }}>\r\n            Events for {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n          </h2>\r\n          <div style={{\r\n            fontSize: '0.875rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found\r\n          </div>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <RefreshCw size={24} style={{ marginBottom: '1rem', animation: 'spin 1s linear infinite' }} />\r\n            <p>Loading events...</p>\r\n          </div>\r\n        ) : filteredEvents.length === 0 ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <CalendarIcon size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\r\n            <p>No events found for this month</p>\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                marginTop: '1rem',\r\n                padding: '0.75rem 1.5rem',\r\n                backgroundColor: '#22c55e',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500'\r\n              }}\r\n            >\r\n              Create First Event\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div style={{\r\n            display: 'grid',\r\n            gap: '1rem'\r\n          }}>\r\n            {paginatedEvents.map((event) => (\r\n              <div\r\n                key={`unique-event-${event.calendar_id}`}\r\n                style={{\r\n                  border: '1px solid #e5e7eb',\r\n                  borderRadius: '12px',\r\n                  padding: '1.5rem',\r\n                  backgroundColor: 'white',\r\n                  transition: 'all 0.2s ease'\r\n                }}\r\n              >\r\n                <div style={{\r\n                  display: 'flex',\r\n                  justifyContent: 'space-between',\r\n                  alignItems: 'flex-start',\r\n                  marginBottom: '1rem'\r\n                }}>\r\n                  <div style={{ flex: 1 }}>\r\n                    <h4 style={{\r\n                      fontSize: '1.125rem',\r\n                      fontWeight: '600',\r\n                      color: '#374151',\r\n                      margin: '0 0 0.5rem 0'\r\n                    }}>\r\n                      {event.title}\r\n                    </h4>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '1rem',\r\n                      fontSize: '0.875rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      <span className=\"flex items-center gap-2\">\r\n                        <CalendarIcon className=\"h-4 w-4\" />\r\n                        {event.event_date}\r\n                      </span>\r\n                      {event.end_date && event.end_date !== event.event_date && (\r\n                        <span>→ {event.end_date}</span>\r\n                      )}\r\n                      <span style={{\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem',\r\n                        fontWeight: '500'\r\n                      }}>\r\n                        {getEventDuration(event)}\r\n                      </span>\r\n                      <span style={{\r\n                        backgroundColor: getEventTypeColor(event),\r\n                        color: 'white',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem'\r\n                      }}>\r\n                        {event.holiday_type_name}\r\n                      </span>\r\n\r\n                    </div>\r\n                    {event.description && (\r\n                      <p style={{\r\n                        fontSize: '0.875rem',\r\n                        color: '#6b7280',\r\n                        margin: '0.5rem 0 0 0',\r\n                        lineHeight: '1.5'\r\n                      }}>\r\n                        {event.description}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div style={{\r\n                    display: 'flex',\r\n                    gap: '0.5rem',\r\n                    marginLeft: '1rem'\r\n                  }}>\r\n                    <button\r\n                      onClick={() => handleEditEvent(event)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Edit event\"\r\n                    >\r\n                      <Edit size={16} />\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => (event as any).is_published\r\n                        ? handleUnpublishEvent(event.calendar_id)\r\n                        : handlePublishEvent(event.calendar_id)\r\n                      }\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: (event as any).is_published ? '#fef3c7' : '#dcfce7',\r\n                        color: (event as any).is_published ? '#d97706' : '#16a34a',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title={(event as any).is_published ? 'Unpublish event' : 'Publish event'}\r\n                    >\r\n                      {(event as any).is_published ? <Clock size={16} /> : <Send size={16} />}\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => handleDeleteEvent(event.calendar_id)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#fef2f2',\r\n                        color: '#dc2626',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Delete event\"\r\n                    >\r\n                      <Trash2 size={16} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div style={{\r\n                  borderTop: '1px solid #f3f4f6',\r\n                  paddingTop: '1rem',\r\n                  marginTop: '1rem'\r\n                }}>\r\n                  <div style={{\r\n                    fontSize: '0.875rem',\r\n                    fontWeight: '500',\r\n                    color: '#374151',\r\n                    marginBottom: '0.5rem'\r\n                  }}>\r\n                    Event Images\r\n                  </div>\r\n                  <EventImages eventId={event.calendar_id} />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination Controls - Outside the event list container */}\r\n      <PaginationControls />\r\n\r\n      {/* Calendar Event Modal */}\r\n      <CalendarEventModal\r\n        isOpen={showModal}\r\n        onClose={handleCloseModal}\r\n        onSave={handleSaveEvent}\r\n        event={editingEvent}\r\n        selectedDate={selectedDate}\r\n        loading={loading}\r\n      />\r\n    </div>\r\n  );\r\n});\r\n\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport default Calendar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,QAAQ,yBAAyB;AACjI,OAAOC,kBAAkB,MAAM,kDAAkD;AAEjF,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,IAAIC,SAAS,QAAQ,cAAc;AAEzH,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMZ,QAAkB,gBAAAa,GAAA,cAAG1B,KAAK,CAAC2B,IAAI,CAAAC,EAAA,GAAAF,GAAA,CAAC,MAAM;EAAA,IAAAG,EAAA,GAAAC,YAAA;EAAAJ,GAAA;EAC1C,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,MAAM,IAAIgC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAuC,CAAC,CAAC,CAAC;;EAElG;EACA,MAAM;IACJqD,MAAM;IACNC,OAAO;IACPC,KAAK;IACLC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC;EACF,CAAC,GAAGvD,WAAW,CAAC0B,WAAW,CAAC;EAE5B,MAAM;IAAE8B;EAAW,CAAC,GAAGvD,qBAAqB,CAAC,CAAC;;EAE9C;EACAJ,SAAS,CAAC,MAAM;IACd,IAAIsC,cAAc,IAAIE,YAAY,IAAIc,KAAK,EAAE;MAC3C,MAAMM,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BtB,iBAAiB,CAAC,EAAE,CAAC;QACrBE,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMqB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACtB,cAAc,EAAEE,YAAY,EAAEc,KAAK,CAAC,CAAC;EAEzC,MAAMS,iBAAiB,GAAG7D,WAAW,CAAE8D,IAAW,IAAK;IACrD3B,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAAC+B,IAAI,IAAI,IAAI,CAAC;IAC7B7B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8B,eAAe,GAAG/D,WAAW,CAAEgE,KAAoB,IAAK;IAC5D7B,eAAe,CAAC6B,KAAK,CAAC;IACtB/B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMgC,eAAe,GAAGjE,WAAW,CAAC,OAClCkE,IAAuC,EACvCC,mBAAyC,EACzCC,UAAgC,KAC7B;IACH,IAAI;MACF,IAAIlC,YAAY,EAAE;QAChB,MAAMoB,WAAW,CAACpB,YAAY,CAACmC,WAAW,EAAEH,IAAuB,CAAC;;QAEpE;QACA,IAAIC,mBAAmB,EAAE;UACvBG,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3E,MAAMJ,mBAAmB,CAAC,CAAC;QAC7B;QAEA9B,iBAAiB,CAAC,4BAA4B,CAAC;MACjD,CAAC,MAAM;QACL,MAAMgB,WAAW,CAACa,IAAuB,CAAC;QAC1C7B,iBAAiB,CAAC,4BAA4B,CAAC;MACjD;;MAEA;MACA,IAAI+B,UAAU,EAAE;QACd,MAAMA,UAAU,CAAC,CAAC;MACpB;MAEAnC,YAAY,CAAC,KAAK,CAAC;MACnBE,eAAe,CAAC,IAAI,CAAC;MACrBJ,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOqB,KAAU,EAAE;MACnBb,eAAe,CAACa,KAAK,CAACoB,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF,CAAC,EAAE,CAACtC,YAAY,EAAEoB,WAAW,EAAED,WAAW,CAAC,CAAC;EAE5C,MAAMoB,gBAAgB,GAAGzE,WAAW,CAAC,MAAM;IACzCiC,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,kBAAkB,GAAG1E,WAAW,CAAC,MAAO2E,OAAe,IAAK;IAChE,IAAI;MACF,MAAMnE,eAAe,CAACoE,YAAY,CAACD,OAAO,CAAC;MAC3CtC,iBAAiB,CAAC,8BAA8B,CAAC;MACjDmB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBb,eAAe,CAACa,KAAK,CAACoB,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMqB,oBAAoB,GAAG7E,WAAW,CAAC,MAAO2E,OAAe,IAAK;IAClE,IAAI;MACF,MAAMnE,eAAe,CAACsE,cAAc,CAACH,OAAO,CAAC;MAC7CtC,iBAAiB,CAAC,gCAAgC,CAAC;MACnDmB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBb,eAAe,CAACa,KAAK,CAACoB,OAAO,IAAI,2BAA2B,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMuB,iBAAiB,GAAG/E,WAAW,CAAC,MAAO2E,OAAe,IAAK;IAC/D;IACA,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,2EAA2E,CAAC,EAAE;MAChG;IACF;IAEA,IAAI;MACF,MAAMzE,eAAe,CAAC0E,eAAe,CAACP,OAAO,CAAC;MAC9CtC,iBAAiB,CAAC,4BAA4B,CAAC;MAC/CmB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBb,eAAe,CAACa,KAAK,CAACoB,OAAO,IAAI,wBAAwB,CAAC;IAC5D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAM2B,aAAa,GAAGnF,WAAW,CAAEoF,SAA0B,IAAK;IAChE,MAAMC,OAAO,GAAG,IAAIxD,IAAI,CAACF,WAAW,CAAC;IACrC,IAAIyD,SAAS,KAAK,MAAM,EAAE;MACxBC,OAAO,CAACC,QAAQ,CAAC3D,WAAW,CAAC4D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLF,OAAO,CAACC,QAAQ,CAAC3D,WAAW,CAAC4D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA3D,cAAc,CAACyD,OAAO,CAAC;EACzB,CAAC,EAAE,CAAC1D,WAAW,CAAC,CAAC;EAEjB,MAAM6D,SAAS,GAAGxF,WAAW,CAAC,MAAM;IAClC4B,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4D,eAAe,GAAGzF,WAAW,CAAE8D,IAAU,IAAK;IAClD/B,eAAe,CAAC+B,IAAI,CAAC;IACrBD,iBAAiB,CAACC,IAAI,CAAC;EACzB,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAM6B,iBAAiB,GAAG1F,WAAW,CAAEgE,KAAoB,IAAK;IAC9D;IACA,OAAOA,KAAK,CAAC2B,cAAc,IAAI3B,KAAK,CAAC4B,iBAAiB,IAAI,SAAS;EACrE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAG7F,WAAW,CAAEgE,KAAoB,IAAK;IAC7D,IAAI,CAACA,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,EAAE;MAC1D,OAAO,kBAAkB;IAC3B;IAEA,MAAMC,SAAS,GAAG,IAAInE,IAAI,CAACmC,KAAK,CAAC+B,UAAU,CAAC;IAC5C,MAAME,OAAO,GAAG,IAAIpE,IAAI,CAACmC,KAAK,CAAC8B,QAAQ,CAAC;IACxC,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAGL,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;IAClE,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAElE,OAAO,GAAGI,QAAQ,YAAY;EAChC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,iBAAiB,GAAGxG,WAAW,CAAEyG,KAAa,IAAK;IACvD,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;IACvC,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,IAAI,GAAGhH,OAAO,CAAC,MAAM;IACzB,OAAOI,eAAe,CAACwB,WAAW,CAACqF,WAAW,CAAC,CAAC,EAAErF,WAAW,CAAC4D,QAAQ,CAAC,CAAC,CAAC;EAC3E,CAAC,EAAE,CAAC5D,WAAW,CAACqF,WAAW,CAAC,CAAC,EAAErF,WAAW,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM0B,YAAY,GAAGlH,OAAO,CAAC,MAAM;IACjC,MAAMmH,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE1BjE,MAAM,CAACkE,OAAO,CAACpD,KAAK,IAAI;MACtB;MACA,IAAI,CAACkD,QAAQ,CAACG,GAAG,CAACrD,KAAK,CAACK,WAAW,CAAC,EAAE;QACpC6C,QAAQ,CAACI,GAAG,CAACtD,KAAK,CAACK,WAAW,EAAEL,KAAK,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,MAAMuD,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACP,QAAQ,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpE;MACA,MAAMC,KAAK,GAAG,IAAIjG,IAAI,CAAC+F,CAAC,CAAC7B,UAAU,CAAC;MACpC,MAAMgC,KAAK,GAAG,IAAIlG,IAAI,CAACgG,CAAC,CAAC9B,UAAU,CAAC;MACpC,IAAI+B,KAAK,CAACzB,OAAO,CAAC,CAAC,KAAK0B,KAAK,CAAC1B,OAAO,CAAC,CAAC,EAAE;QACvC,OAAOyB,KAAK,CAACzB,OAAO,CAAC,CAAC,GAAG0B,KAAK,CAAC1B,OAAO,CAAC,CAAC;MAC1C;MACA,OAAOuB,CAAC,CAACnB,KAAK,CAACuB,aAAa,CAACH,CAAC,CAACpB,KAAK,CAAC;IACvC,CAAC,CAAC;;IAEF;IACAnC,OAAO,CAACC,GAAG,CAAC,2BAA2BrB,MAAM,CAAC+E,MAAM,mBAAmBV,gBAAgB,CAACU,MAAM,gBAAgB,CAAC;IAE/G,OAAOV,gBAAgB;EACzB,CAAC,EAAE,CAACrE,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMgF,cAAc,GAAGnI,OAAO,CAAC,MAAM;IACnC,OAAOkH,YAAY,CAACkB,MAAM,CAACnE,KAAK,IAAI;MAAA,IAAAoE,kBAAA,EAAAC,kBAAA;MAClC,MAAMC,aAAa,GAAG,CAAC9F,UAAU,IAC/BwB,KAAK,CAACyC,KAAK,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChG,UAAU,CAAC+F,WAAW,CAAC,CAAC,CAAC,MAAAH,kBAAA,GAC5DpE,KAAK,CAACyE,WAAW,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChG,UAAU,CAAC+F,WAAW,CAAC,CAAC,CAAC;MAErE,MAAMG,eAAe,GAAG,CAAChG,gBAAgB,IACvC,EAAA2F,kBAAA,GAAArE,KAAK,CAAC2E,WAAW,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBO,QAAQ,CAAC,CAAC,MAAKlG,gBAAgB;MAEpD,OAAO4F,aAAa,IAAII,eAAe;IACzC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,YAAY,EAAEzE,UAAU,EAAEE,gBAAgB,CAAC,CAAC;;EAEhD;EACA,MAAMmG,UAAU,GAAGX,cAAc,CAACD,MAAM;EACxC,MAAMa,UAAU,GAAG3C,IAAI,CAACI,IAAI,CAACsC,UAAU,GAAG/F,YAAY,CAAC;EACvD,MAAMiG,UAAU,GAAG,CAACnG,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMkG,QAAQ,GAAGD,UAAU,GAAGjG,YAAY;EAC1C,MAAMmG,eAAe,GAAGf,cAAc,CAACrB,KAAK,CAACkC,UAAU,EAAEC,QAAQ,CAAC;;EAElE;EACAlJ,SAAS,CAAC,MAAM;IACd+C,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,UAAU,EAAEE,gBAAgB,EAAEI,YAAY,CAAC,CAAC;;EAEhD;EACAhD,SAAS,CAAC,MAAM;IACd,MAAMoJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI,CAACD,eAAe,IAAIA,eAAe,CAAChB,MAAM,KAAK,CAAC,EAAE;MAEtD,MAAMkB,kBAAkB,GAAGF,eAAe,CAACG,GAAG,CAAC,MAAOpF,KAAK,IAAK;QAC9D,IAAIhB,gBAAgB,CAACgB,KAAK,CAACK,WAAW,CAAC,EAAE,OAAO,CAAC;;QAEjD,IAAI;UAAA,IAAAgF,cAAA;UACF,MAAMC,QAAQ,GAAG,MAAM9I,eAAe,CAAC+I,mBAAmB,CAACvF,KAAK,CAACK,WAAW,CAAC;UAC7E,IAAIiF,QAAQ,CAACE,OAAO,KAAAH,cAAA,GAAIC,QAAQ,CAACpF,IAAI,cAAAmF,cAAA,eAAbA,cAAA,CAAeI,WAAW,EAAE;YAClDxG,mBAAmB,CAACyG,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACP,CAAC1F,KAAK,CAACK,WAAW,GAAGiF,QAAQ,CAACpF,IAAI,CAAEuF;YACtC,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC,OAAOrG,KAAK,EAAE;UACdkB,OAAO,CAAClB,KAAK,CAAC,yCAAyCY,KAAK,CAACK,WAAW,GAAG,EAAEjB,KAAK,CAAC;QACrF;MACF,CAAC,CAAC;MAEF,MAAMuG,OAAO,CAACC,GAAG,CAACT,kBAAkB,CAAC;IACvC,CAAC;IAEDD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACD,eAAe,EAAEjG,gBAAgB,CAAC,CAAC;;EAEvC;EACA,MAAM6G,WAA0C,GAAGA,CAAC;IAAElF;EAAQ,CAAC,KAAK;IAClE,MAAM8E,WAAW,GAAGzG,gBAAgB,CAAC2B,OAAO,CAAC,IAAI,EAAE;IACnD,MAAMmF,gBAAgB,GAAGL,WAAW,CAACtB,MAAM,CAAC4B,GAAG,IAAIA,GAAG,CAACC,SAAS,KAAK,OAAO,CAAC;IAE7E,IAAIF,gBAAgB,CAAC7B,MAAM,KAAK,CAAC,EAAE;MACjC,oBACE5G,OAAA;QAAK4I,KAAK,EAAE;UACVC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE;QACZ,CAAE;QAAAC,QAAA,eACAtJ,OAAA,CAACH,SAAS;UAAC0J,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEV;IAEA,oBACE3J,OAAA;MAAK4I,KAAK,EAAE;QACVK,OAAO,EAAE,MAAM;QACfW,GAAG,EAAE,QAAQ;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAP,QAAA,GACCb,gBAAgB,CAACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuC,GAAG,CAAE+B,UAAU,iBAC3C9J,OAAA,CAAC+J,mBAAmB;QAAgCD,UAAU,EAAEA;MAAW,GAAjDA,UAAU,CAACE,aAAa;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAC9E,CAAC,EACDlB,gBAAgB,CAAC7B,MAAM,GAAG,CAAC,iBAC1B5G,OAAA;QAAK4I,KAAK,EAAE;UACVC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBY,UAAU,EAAE;QACd,CAAE;QAAAX,QAAA,GAAC,GACA,EAACb,gBAAgB,CAAC7B,MAAM,GAAG,CAAC;MAAA;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,MAAMI,mBAAiE,GAAGA,CAAC;IAAED;EAAW,CAAC,KAAK;IAAA1J,EAAA;IAC5F,MAAM,CAAC8J,QAAQ,EAAEC,WAAW,CAAC,GAAG3L,QAAQ,CAAgB,IAAI,CAAC;IAC7D,MAAM,CAACsD,OAAO,EAAEsI,UAAU,CAAC,GAAG5L,QAAQ,CAAC,IAAI,CAAC;IAC5C,MAAM,CAACuD,KAAK,EAAEsI,QAAQ,CAAC,GAAG7L,QAAQ,CAAC,KAAK,CAAC;IAEzCC,SAAS,CAAC,MAAM;MACd,MAAM6L,SAAS,GAAG,MAAAA,CAAA,KAAY;QAC5B,IAAI;UACF,MAAMC,OAAO,GAAGzK,WAAW,CAACgK,UAAU,CAACU,SAAS,CAAC;UACjD,IAAI,CAACD,OAAO,EAAE;YACZ,MAAM,IAAIE,KAAK,CAAC,oBAAoB,CAAC;UACvC;UAEA,MAAMxC,QAAQ,GAAG,MAAMyC,KAAK,CAACH,OAAO,EAAE;YACpCI,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,QAAQ,EAAEjH,MAAM,CAACkH,QAAQ,CAACC;YAC5B,CAAC;YACDC,IAAI,EAAE;UACR,CAAC,CAAC;UAEF,IAAI,CAAC9C,QAAQ,CAAC+C,EAAE,EAAE;YAChB,MAAM,IAAIP,KAAK,CAAC,QAAQxC,QAAQ,CAACgD,MAAM,EAAE,CAAC;UAC5C;UAEA,MAAMC,IAAI,GAAG,MAAMjD,QAAQ,CAACiD,IAAI,CAAC,CAAC;UAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;UAC3Cf,WAAW,CAACgB,SAAS,CAAC;QACxB,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZrI,OAAO,CAAClB,KAAK,CAAC,uBAAuB,EAAEuJ,GAAG,CAAC;UAC3CjB,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,SAAS;UACRD,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC;MAEDE,SAAS,CAAC,CAAC;MAEX,OAAO,MAAM;QACX,IAAIJ,QAAQ,EAAE;UACZkB,GAAG,CAACG,eAAe,CAACrB,QAAQ,CAAC;QAC/B;MACF,CAAC;IACH,CAAC,EAAE,CAACJ,UAAU,CAACU,SAAS,EAAEN,QAAQ,CAAC,CAAC;IAEpC,IAAIpI,OAAO,EAAE;MACX,oBACE9B,OAAA;QAAK4I,KAAK,EAAE;UACVC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,eACAtJ,OAAA,CAACH,SAAS;UAAC0J,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEV;IAEA,IAAI5H,KAAK,IAAI,CAACmI,QAAQ,EAAE;MACtB,oBACElK,OAAA;QAAK4I,KAAK,EAAE;UACVC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE;QACZ,CAAE;QAAAC,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAEV;IAEA,oBACE3J,OAAA;MAAK4I,KAAK,EAAE;QACV4C,QAAQ,EAAE,UAAU;QACpB3C,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdE,YAAY,EAAE,KAAK;QACnByC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE5B,UAAU,CAAC6B,UAAU,GAAG,mBAAmB,GAAG;MACxD,CAAE;MAAArC,QAAA,eACAtJ,OAAA;QACE4L,GAAG,EAAE1B,QAAS;QACd2B,GAAG,EAAE/B,UAAU,CAACgC,SAAU;QAC1BlD,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdiD,SAAS,EAAE;QACb;MAAE;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CAAC;EAEV,CAAC;;EAED;EAAAvJ,EAAA,CAvGM2J,mBAAiE;EAwGvE,MAAMiC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,mBAAmB,GAAGnH,IAAI,CAACoH,GAAG,CAACzE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,MAAM0E,oBAAoB,GAAGrH,IAAI,CAACoH,GAAG,CAAC3K,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvD,MAAM6K,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,KAAK,GAAG,EAAE;MAChB,MAAMC,eAAe,GAAG,CAAC;MAEzB,IAAIL,mBAAmB,IAAIK,eAAe,EAAE;QAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,mBAAmB,EAAEM,CAAC,EAAE,EAAE;UAC7CF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACL,IAAIJ,oBAAoB,IAAI,CAAC,EAAE;UAC7BE,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAEP,mBAAmB,CAAC;QACpD,CAAC,MAAM,IAAIE,oBAAoB,IAAIF,mBAAmB,GAAG,CAAC,EAAE;UAC1DI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEP,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,CAAC;QACtH,CAAC,MAAM;UACLI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEL,oBAAoB,GAAG,CAAC,EAAEA,oBAAoB,EAAEA,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAEF,mBAAmB,CAAC;QAC5H;MACF;MAEA,OAAOI,KAAK;IACd,CAAC;IAED,oBACErM,OAAA;MAAK4I,KAAK,EAAE;QACVK,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBuD,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjB5D,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpB0C,MAAM,EAAE,mBAAmB;QAC3BkB,SAAS,EAAE;MACb,CAAE;MAAAtD,QAAA,gBAEAtJ,OAAA;QAAK4I,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEU,GAAG,EAAE;QAAS,CAAE;QAAAN,QAAA,gBACnEtJ,OAAA;UAAM4I,KAAK,EAAE;YAAES,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrE3J,OAAA;UACE6M,KAAK,EAAEpL,YAAa;UACpBqL,QAAQ,EAAGC,CAAC,IAAKrL,eAAe,CAACsL,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;UACzDjE,KAAK,EAAE;YACL+D,OAAO,EAAE,gBAAgB;YACzBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBK,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,gBAEFtJ,OAAA;YAAQ6M,KAAK,EAAE,CAAE;YAAAvD,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5B3J,OAAA;YAAQ6M,KAAK,EAAE,EAAG;YAAAvD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3J,OAAA;YAAQ6M,KAAK,EAAE,EAAG;YAAAvD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3J,OAAA;YAAQ6M,KAAK,EAAE,EAAG;YAAAvD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACT3J,OAAA;UAAM4I,KAAK,EAAE;YAAES,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAGN3J,OAAA;QAAK4I,KAAK,EAAE;UAAES,QAAQ,EAAE,UAAU;UAAED,KAAK,EAAE;QAAU,CAAE;QAAAE,QAAA,GAAC,UAC9C,EAACxE,IAAI,CAACoH,GAAG,CAAC1E,UAAU,GAAG,CAAC,GAAGE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAC,GAAC,EAAC5C,IAAI,CAACoI,GAAG,CAACvF,QAAQ,EAAEH,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,SAC7G;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGN3J,OAAA;QAAK4I,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEU,GAAG,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,gBACtFtJ,OAAA;UACEmN,OAAO,EAAEA,CAAA,KAAM3L,cAAc,CAAC,CAAC,CAAE;UACjC4L,QAAQ,EAAEjB,oBAAoB,KAAK,CAAE;UACrCvD,KAAK,EAAE;YACL+D,OAAO,EAAE,QAAQ;YACjBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEoD,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjE/C,KAAK,EAAE+C,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDkB,MAAM,EAAElB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9D9C,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3J,OAAA;UACEmN,OAAO,EAAEA,CAAA,KAAM3L,cAAc,CAAC6G,IAAI,IAAIvD,IAAI,CAACoH,GAAG,CAAC,CAAC,EAAE7D,IAAI,GAAG,CAAC,CAAC,CAAE;UAC7D+E,QAAQ,EAAEjB,oBAAoB,KAAK,CAAE;UACrCvD,KAAK,EAAE;YACL+D,OAAO,EAAE,QAAQ;YACjBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEoD,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjE/C,KAAK,EAAE+C,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDkB,MAAM,EAAElB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9D9C,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERyC,cAAc,CAAC,CAAC,CAACrE,GAAG,CAAC,CAACuF,IAAI,EAAEC,KAAK,kBAChCvN,OAAA;UAEEmN,OAAO,EAAEA,CAAA,KAAM,OAAOG,IAAI,KAAK,QAAQ,IAAI9L,cAAc,CAAC8L,IAAI,CAAE;UAChEF,QAAQ,EAAEE,IAAI,KAAK,KAAM;UACzB1E,KAAK,EAAE;YACL+D,OAAO,EAAE,gBAAgB;YACzBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEuE,IAAI,KAAKnB,oBAAoB,GAAG,SAAS,GAAGmB,IAAI,KAAK,KAAK,GAAG,aAAa,GAAG,OAAO;YACrGlE,KAAK,EAAEkE,IAAI,KAAKnB,oBAAoB,GAAG,OAAO,GAAGmB,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YACvFD,MAAM,EAAEC,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YAC9CjE,QAAQ,EAAE,UAAU;YACpBY,UAAU,EAAEqD,IAAI,KAAKnB,oBAAoB,GAAG,KAAK,GAAG;UACtD,CAAE;UAAA7C,QAAA,EAEDgE;QAAI,GAdAC,KAAK;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeJ,CACT,CAAC,eAEF3J,OAAA;UACEmN,OAAO,EAAEA,CAAA,KAAM3L,cAAc,CAAC6G,IAAI,IAAIvD,IAAI,CAACoI,GAAG,CAACjB,mBAAmB,EAAE5D,IAAI,GAAG,CAAC,CAAC,CAAE;UAC/E+E,QAAQ,EAAEjB,oBAAoB,KAAKF,mBAAoB;UACvDrD,KAAK,EAAE;YACL+D,OAAO,EAAE,QAAQ;YACjBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEoD,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnF7C,KAAK,EAAE+C,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EoB,MAAM,EAAElB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChF5C,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3J,OAAA;UACEmN,OAAO,EAAEA,CAAA,KAAM3L,cAAc,CAACyK,mBAAmB,CAAE;UACnDmB,QAAQ,EAAEjB,oBAAoB,KAAKF,mBAAoB;UACvDrD,KAAK,EAAE;YACL+D,OAAO,EAAE,QAAQ;YACjBjB,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEoD,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnF7C,KAAK,EAAE+C,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EoB,MAAM,EAAElB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChF5C,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE3J,OAAA;IAAK4I,KAAK,EAAE;MAAE4E,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAC,2BAAwB,MAAM;IAAAnE,QAAA,GAEjFvI,cAAc,iBACbf,OAAA;MAAK4I,KAAK,EAAE;QACV8D,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACf5D,eAAe,EAAE,SAAS;QAC1B2C,MAAM,EAAE,mBAAmB;QAC3BtC,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACCvI;IAAc;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,EACA1I,YAAY,iBACXjB,OAAA;MAAK4I,KAAK,EAAE;QACV8D,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACf5D,eAAe,EAAE,SAAS;QAC1B2C,MAAM,EAAE,mBAAmB;QAC3BtC,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACCrI;IAAY;MAAAuI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD3J,OAAA;MAAK4I,KAAK,EAAE;QACV8E,UAAU,EAAE,OAAO;QACnB1E,YAAY,EAAE,KAAK;QACnB2D,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,MAAM;QACpBE,SAAS,EAAE,gCAAgC;QAC3ClB,MAAM,EAAE,mBAAmB;QAC3BF,QAAQ,EAAE,UAAU;QACpBmC,MAAM,EAAE;MACV,CAAE;MAAArE,QAAA,eACAtJ,OAAA;QAAK4I,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE,QAAQ;UAAEU,GAAG,EAAE;QAAS,CAAE;QAAAN,QAAA,gBACpGtJ,OAAA;UAAK4I,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEU,GAAG,EAAE;UAAU,CAAE;UAAAN,QAAA,gBACpEtJ,OAAA;YAAI4I,KAAK,EAAE;cACTS,QAAQ,EAAE,SAAS;cACnBY,UAAU,EAAE,KAAK;cACjBb,KAAK,EAAE,SAAS;cAChBqE,MAAM,EAAE;YACV,CAAE;YAAAnE,QAAA,eACAtJ,OAAA;cAAM4I,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEU,GAAG,EAAE;cAAU,CAAE;cAAAN,QAAA,gBACrEtJ,OAAA,CAACX,YAAY;gBAACkK,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL3J,OAAA;YAAG4I,KAAK,EAAE;cACRQ,KAAK,EAAE,SAAS;cAChBqE,MAAM,EAAE,cAAc;cACtBpE,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,GACCrK,YAAY,CAACqB,WAAW,CAAC4D,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAC5D,WAAW,CAACqF,WAAW,CAAC,CAAC;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN3J,OAAA;UAAK4I,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEU,GAAG,EAAE;UAAS,CAAE;UAAAN,QAAA,gBACnEtJ,OAAA;YACEmN,OAAO,EAAEA,CAAA,KAAMrJ,aAAa,CAAC,MAAM,CAAE;YACrC8E,KAAK,EAAE;cACL+D,OAAO,EAAE,SAAS;cAClBvD,KAAK,EAAE,SAAS;cAChBsE,UAAU,EAAE,MAAM;cAClBhC,MAAM,EAAE,MAAM;cACd2B,MAAM,EAAE,SAAS;cACjBhE,QAAQ,EAAE,MAAM;cAChBuE,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGd,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACQ,KAAK,GAAG,SAAU;YAC5D2E,UAAU,EAAGhB,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACQ,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3J,OAAA;YACEmN,OAAO,EAAEhJ,SAAU;YACnByE,KAAK,EAAE;cACL+D,OAAO,EAAE,iBAAiB;cAC1B5D,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBsC,MAAM,EAAE,MAAM;cACd1C,YAAY,EAAE,KAAK;cACnBqE,MAAM,EAAE,SAAS;cACjBpD,UAAU,EAAE,KAAK;cACjBZ,QAAQ,EAAE,UAAU;cACpBuE,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGd,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,SAAU;YACtEgF,UAAU,EAAGhB,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,SAAU;YAAAO,QAAA,EACtE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3J,OAAA;YACEmN,OAAO,EAAEA,CAAA,KAAMrJ,aAAa,CAAC,MAAM,CAAE;YACrC8E,KAAK,EAAE;cACL+D,OAAO,EAAE,SAAS;cAClBvD,KAAK,EAAE,SAAS;cAChBsE,UAAU,EAAE,MAAM;cAClBhC,MAAM,EAAE,MAAM;cACd2B,MAAM,EAAE,SAAS;cACjBhE,QAAQ,EAAE,MAAM;cAChBuE,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGd,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACQ,KAAK,GAAG,SAAU;YAC5D2E,UAAU,EAAGhB,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACQ,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3J,OAAA;YACEmN,OAAO,EAAEA,CAAA,KAAM3K,iBAAiB,CAAC,CAAE;YACnCoG,KAAK,EAAE;cACLK,OAAO,EAAE,aAAa;cACtBC,UAAU,EAAE,QAAQ;cACpByD,OAAO,EAAE,aAAa;cACtBe,UAAU,EAAE,mDAAmD;cAC/DtE,KAAK,EAAE,OAAO;cACdsC,MAAM,EAAE,MAAM;cACd1C,YAAY,EAAE,KAAK;cACnBqE,MAAM,EAAE,SAAS;cACjBpD,UAAU,EAAE,KAAK;cACjBZ,QAAQ,EAAE,UAAU;cACpBuE,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGd,CAAC,IAAK;cAClBA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACoF,SAAS,GAAG,kBAAkB;cACpDjB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFmB,UAAU,EAAGhB,CAAC,IAAK;cACjBA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACoF,SAAS,GAAG,eAAe;cACjDjB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAtD,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3J,OAAA;MAAK4I,KAAK,EAAE;QACV8E,UAAU,EAAE,OAAO;QACnB1E,YAAY,EAAE,MAAM;QACpB4D,SAAS,EAAE,gCAAgC;QAC3ClB,MAAM,EAAE,mBAAmB;QAC3BD,QAAQ,EAAE;MACZ,CAAE;MAAAnC,QAAA,gBAEAtJ,OAAA;QAAK4I,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfgF,mBAAmB,EAAE,gBAAgB;UACrClF,eAAe,EAAE,SAAS;UAC1BmF,YAAY,EAAE;QAChB,CAAE;QAAA5E,QAAA,EACC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACvB,GAAG,CAAEoG,GAAG,iBACzDnO,OAAA;UAAe4I,KAAK,EAAE;YACpB+D,OAAO,EAAE,MAAM;YACfyB,SAAS,EAAE,QAAQ;YACnB/E,QAAQ,EAAE,UAAU;YACpBY,UAAU,EAAE,KAAK;YACjBb,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EACC6E;QAAG,GAPIA,GAAG;UAAA3E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3J,OAAA;QAAK4I,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEgF,mBAAmB,EAAE;QAAiB,CAAE;QAAA3E,QAAA,EACpE5D,IAAI,CAACqC,GAAG,CAAC,CAACtF,IAAI,EAAE8K,KAAK,KAAK;UACzB,MAAMc,SAAS,GAAGnM,gBAAgB,CAACO,IAAI,CAAC;UACxC,MAAM6L,cAAc,GAAGtP,WAAW,CAACyD,IAAI,EAAEnC,WAAW,CAAC4D,QAAQ,CAAC,CAAC,EAAE5D,WAAW,CAACqF,WAAW,CAAC,CAAC,CAAC;UAC3F,MAAM4I,WAAW,GAAGxP,OAAO,CAAC0D,IAAI,CAAC;;UAEjC;UACA;UACA,MAAM+L,OAAO,GAAG,gBAAgB/L,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;UAEhD,oBACEhF,OAAA;YAEE4I,KAAK,EAAE;cACL6F,SAAS,EAAE,OAAO;cAClB9B,OAAO,EAAE,QAAQ;cACjBuB,YAAY,EAAE,mBAAmB;cACjCQ,WAAW,EAAEnB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,mBAAmB;cAAE;cAC7DF,MAAM,EAAE,SAAS;cACjBtE,eAAe,EAAE,CAACuF,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cAChFnF,KAAK,EAAE,CAACkF,cAAc,GAAG,SAAS,GAAG,SAAS;cAC9CV,UAAU,EAAE,eAAe;cAC3BpC,QAAQ,EAAE;YACZ,CAAE;YACF2B,OAAO,EAAEA,CAAA,KAAM/I,eAAe,CAAC3B,IAAI,CAAE;YACrCoL,WAAW,EAAGd,CAAC,IAAK;cAClB,IAAIuB,cAAc,EAAE;gBAClBvB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,SAAS;gBACjDgE,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,2BAA2B;cAC/D;YACF,CAAE;YACFmB,UAAU,EAAGhB,CAAC,IAAK;cACjBA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,CAACuF,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cACvGxB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAtD,QAAA,gBAEFtJ,OAAA;cAAK4I,KAAK,EAAE;gBACVS,QAAQ,EAAE,UAAU;gBACpBY,UAAU,EAAE,KAAK;gBACjByC,YAAY,EAAE,SAAS;gBACvBtD,KAAK,EAAEmF,WAAW,GAAG,SAAS,GAAG;cACnC,CAAE;cAAAjF,QAAA,EACC7G,IAAI,CAACkM,OAAO,CAAC;YAAC;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACN3J,OAAA;cAAK4I,KAAK,EAAE;gBAAEK,OAAO,EAAE,MAAM;gBAAE2F,aAAa,EAAE,QAAQ;gBAAEhF,GAAG,EAAE;cAAU,CAAE;cAAAN,QAAA,GACtE+E,SAAS,CAAC7I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuC,GAAG,CAAEpF,KAAK,IAAK;gBACpC;gBACA,MAAMkM,UAAU,GAAGlM,KAAK,CAACkM,UAAU;gBACnC,MAAMC,OAAO,GAAGnM,KAAK,CAACoM,YAAY;gBAClC,MAAMC,KAAK,GAAGrM,KAAK,CAACsM,UAAU;gBAC9B,MAAMC,cAAc,GAAGL,UAAU,IAAI,CAACC,OAAO,IAAI,CAACE,KAAK;gBACvD,MAAMG,UAAU,GAAG9K,iBAAiB,CAAC1B,KAAK,CAAC;gBAE3C,oBACE3C,OAAA;kBAEE4I,KAAK,EAAE;oBACLS,QAAQ,EAAE,SAAS;oBACnBsD,OAAO,EAAE,gBAAgB;oBACzB3D,YAAY,EAAE6F,UAAU,GAAIC,OAAO,GAAG,iBAAiB,GAAGE,KAAK,GAAG,iBAAiB,GAAG,KAAK,GAAI,KAAK;oBACpGjG,eAAe,EAAEoG,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBAC5DxD,MAAM,EAAE,aAAayD,UAAU,EAAE;oBACjCC,UAAU,EAAEN,OAAO,IAAI,CAACD,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBAC1FT,WAAW,EAAEM,KAAK,IAAI,CAACH,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBACzF1D,QAAQ,EAAE,QAAQ;oBAClB4D,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE,QAAQ;oBACpBjC,MAAM,EAAE,SAAS;oBACjB7B,QAAQ,EAAE,UAAU;oBACpBpC,KAAK,EAAE,SAAS;oBAChBa,UAAU,EAAE,KAAK;oBACjB2D,UAAU,EAAE,eAAe;oBAC3B3E,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,GAAG,EAAE;kBACP,CAAE;kBACFuD,OAAO,EAAGJ,CAAC,IAAK;oBACdA,CAAC,CAACwC,eAAe,CAAC,CAAC;oBACnB7M,eAAe,CAACC,KAAK,CAAC;kBACxB,CAAE;kBACFkL,WAAW,EAAGd,CAAC,IAAK;oBAClBA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAGoG,UAAU,GAAG,IAAI;oBACzDpC,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACoF,SAAS,GAAG,kBAAkB;oBACpDjB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,aAAauC,UAAU,IAAI;kBAC/D,CAAE;kBACFpB,UAAU,EAAGhB,CAAC,IAAK;oBACjBA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAGoG,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBACnFnC,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACoF,SAAS,GAAG,eAAe;oBACjDjB,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBACFxH,KAAK,EAAEyJ,UAAU,GAAG,GAAGlM,KAAK,CAACyC,KAAK,KAAKzC,KAAK,CAAC6M,iBAAiB,OAAO7M,KAAK,CAAC8M,eAAe,GAAG,GAAG9M,KAAK,CAACyC,KAAM;kBAAAkE,QAAA,GAG3GwF,OAAO,IAAID,UAAU,iBACpB7O,OAAA;oBAAM4I,KAAK,EAAE;sBACXQ,KAAK,EAAE+F,UAAU;sBACjBlF,UAAU,EAAE,MAAM;sBAClBZ,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGAuF,cAAc,iBACblP,OAAA;oBAAM4I,KAAK,EAAE;sBACXQ,KAAK,EAAE+F,UAAU;sBACjBlF,UAAU,EAAE,MAAM;sBAClBZ,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGAqF,KAAK,IAAIH,UAAU,IAAI,CAACC,OAAO,iBAC9B9O,OAAA;oBAAM4I,KAAK,EAAE;sBACXQ,KAAK,EAAE+F,UAAU;sBACjBlF,UAAU,EAAE,MAAM;sBAClBZ,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eAGD3J,OAAA;oBAAM4I,KAAK,EAAE;sBACX8G,IAAI,EAAE,CAAC;sBACPjE,QAAQ,EAAE,QAAQ;sBAClB4D,YAAY,EAAE;oBAChB,CAAE;oBAAA/F,QAAA,EACCnE,iBAAiB,CAACxC,KAAK,CAACyC,KAAK;kBAAC;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,EAGNmF,OAAO,IAAID,UAAU,iBACpB7O,OAAA;oBAAM4I,KAAK,EAAE;sBACXQ,KAAK,EAAE+F,UAAU;sBACjBlF,UAAU,EAAE,MAAM;sBAClBZ,QAAQ,EAAE,QAAQ;sBAClBsG,OAAO,EAAE;oBACX,CAAE;oBAAArG,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA,GAzFI,SAAShH,KAAK,CAACK,WAAW,IAAIP,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0FhD,CAAC;cAEV,CAAC,CAAC,EACD0E,SAAS,CAACzH,MAAM,GAAG,CAAC,iBACnB5G,OAAA;gBAAK4I,KAAK,EAAE;kBACVS,QAAQ,EAAE,SAAS;kBACnBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,GAAC,GACA,EAAC+E,SAAS,CAACzH,MAAM,GAAG,CAAC,EAAC,OACzB;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAhJD6E,OAAO;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiJT,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3J,OAAA;MAAK4I,KAAK,EAAE;QACV8E,UAAU,EAAE,OAAO;QACnB1E,YAAY,EAAE,MAAM;QACpB4D,SAAS,EAAE,gCAAgC;QAC3ClB,MAAM,EAAE,mBAAmB;QAC3B+B,MAAM,EAAE,QAAQ;QAChBd,OAAO,EAAE,QAAQ;QACjB5D,eAAe,EAAE;MACnB,CAAE;MAAAO,QAAA,eACAtJ,OAAA;QAAK4I,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfgF,mBAAmB,EAAE,sCAAsC;UAC3DrE,GAAG,EAAE,QAAQ;UACbV,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBACAtJ,OAAA;UAAAsJ,QAAA,gBACEtJ,OAAA;YAAO4I,KAAK,EAAE;cACZK,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBY,UAAU,EAAE,KAAK;cACjBb,KAAK,EAAE,SAAS;cAChBsD,YAAY,EAAE;YAChB,CAAE;YAAApD,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3J,OAAA;YAAK4I,KAAK,EAAE;cAAE4C,QAAQ,EAAE;YAAW,CAAE;YAAAlC,QAAA,gBACnCtJ,OAAA,CAACV,MAAM;cACLiK,IAAI,EAAE,EAAG;cACTH,KAAK,EAAC,SAAS;cACfR,KAAK,EAAE;gBACL4C,QAAQ,EAAE,UAAU;gBACpBoE,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACV7B,SAAS,EAAE,kBAAkB;gBAC7B8B,aAAa,EAAE;cACjB;YAAE;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3J,OAAA;cACE+P,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kBAAkB;cAC9BnD,KAAK,EAAE1L,UAAW;cAClB2L,QAAQ,EAAGC,CAAC,IAAK3L,aAAa,CAAC2L,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cAC/CjE,KAAK,EAAE;gBACLC,KAAK,EAAE,MAAM;gBACb8D,OAAO,EAAE,gCAAgC;gBACzCjB,MAAM,EAAE,mBAAmB;gBAC3B1C,YAAY,EAAE,KAAK;gBACnBK,QAAQ,EAAE,UAAU;gBACpB4G,OAAO,EAAE,MAAM;gBACfrC,UAAU,EAAE;cACd,CAAE;cACFsC,OAAO,EAAGnD,CAAC,IAAK;gBACdA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACuH,WAAW,GAAG,SAAS;gBAC7CpD,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,kCAAkC;cACtE,CAAE;cACFwD,MAAM,EAAGrD,CAAC,IAAK;gBACbA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACuH,WAAW,GAAG,SAAS;gBAC7CpD,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,MAAM;cAC1C;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3J,OAAA;UAAAsJ,QAAA,gBACEtJ,OAAA;YAAO4I,KAAK,EAAE;cACZK,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBY,UAAU,EAAE,KAAK;cACjBb,KAAK,EAAE,SAAS;cAChBsD,YAAY,EAAE;YAChB,CAAE;YAAApD,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3J,OAAA;YACE6M,KAAK,EAAEwD,mBAAoB;YAC3BvD,QAAQ,EAAGC,CAAC,IAAKuD,sBAAsB,CAACvD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YACxDjE,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACb8D,OAAO,EAAE,SAAS;cAClBjB,MAAM,EAAE,mBAAmB;cAC3B1C,YAAY,EAAE,KAAK;cACnBK,QAAQ,EAAE,UAAU;cACpB4G,OAAO,EAAE,MAAM;cACflH,eAAe,EAAE,OAAO;cACxB6E,UAAU,EAAE,8CAA8C;cAC1Da,SAAS,EAAE,QAAQ;cACnB8B,UAAU,EAAE;YACd,CAAE;YACFL,OAAO,EAAGnD,CAAC,IAAK;cACdA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACuH,WAAW,GAAG,SAAS;cAC7CpD,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFwD,MAAM,EAAGrD,CAAC,IAAK;cACbA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACuH,WAAW,GAAG,SAAS;cAC7CpD,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACgE,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAtD,QAAA,gBAEFtJ,OAAA;cAAQ6M,KAAK,EAAC,EAAE;cAAAvD,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClC6G,YAAY,CAACzI,GAAG,CAAEgI,IAAI,iBACrB/P,OAAA;cAA2B6M,KAAK,EAAEkD,IAAI,CAACU,OAAQ;cAAAnH,QAAA,EAC5CyG,IAAI,CAACW;YAAS,GADJX,IAAI,CAACU,OAAO;cAAAjH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3J,OAAA;UAAAsJ,QAAA,eACEtJ,OAAA;YACEmN,OAAO,EAAEhL,OAAQ;YACjByG,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACb8D,OAAO,EAAE,SAAS;cAClB5D,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBsC,MAAM,EAAE,MAAM;cACd1C,YAAY,EAAE,KAAK;cACnBqE,MAAM,EAAE,SAAS;cACjBpD,UAAU,EAAE,KAAK;cACjBZ,QAAQ,EAAE,UAAU;cACpBuE,UAAU,EAAE,4BAA4B;cACxC9E,MAAM,EAAE,QAAQ;cAChBG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBS,GAAG,EAAE;YACP,CAAE;YACFiE,WAAW,EAAGd,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,SAAU;YACtEgF,UAAU,EAAGhB,CAAC,IAAKA,CAAC,CAACe,aAAa,CAAClF,KAAK,CAACG,eAAe,GAAG,SAAU;YAAAO,QAAA,gBAErEtJ,OAAA,CAACT,SAAS;cAACgK,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3J,OAAA;MAAK4I,KAAK,EAAE;QACV8E,UAAU,EAAE,OAAO;QACnB1E,YAAY,EAAE,MAAM;QACpB2D,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3ClB,MAAM,EAAE,mBAAmB;QAC3BgB,YAAY,EAAE;MAChB,CAAE;MAAApD,QAAA,gBACAtJ,OAAA;QAAK4I,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBwD,YAAY,EAAE;QAChB,CAAE;QAAApD,QAAA,gBACAtJ,OAAA;UAAI4I,KAAK,EAAE;YACTS,QAAQ,EAAE,QAAQ;YAClBY,UAAU,EAAE,KAAK;YACjBb,KAAK,EAAE,SAAS;YAChBqE,MAAM,EAAE;UACV,CAAE;UAAAnE,QAAA,GAAC,aACU,EAACrK,YAAY,CAACqB,WAAW,CAAC4D,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAC5D,WAAW,CAACqF,WAAW,CAAC,CAAC;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACL3J,OAAA;UAAK4I,KAAK,EAAE;YACVS,QAAQ,EAAE,UAAU;YACpBD,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,GACCzC,cAAc,CAACD,MAAM,EAAC,QAAM,EAACC,cAAc,CAACD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QACvE;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL7H,OAAO,gBACN9B,OAAA;QAAK4I,KAAK,EAAE;UACVwF,SAAS,EAAE,QAAQ;UACnBzB,OAAO,EAAE,MAAM;UACfvD,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACAtJ,OAAA,CAACT,SAAS;UAACgK,IAAI,EAAE,EAAG;UAACX,KAAK,EAAE;YAAE8D,YAAY,EAAE,MAAM;YAAEiE,SAAS,EAAE;UAA0B;QAAE;UAAAnH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9F3J,OAAA;UAAAsJ,QAAA,EAAG;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJ9C,cAAc,CAACD,MAAM,KAAK,CAAC,gBAC7B5G,OAAA;QAAK4I,KAAK,EAAE;UACVwF,SAAS,EAAE,QAAQ;UACnBzB,OAAO,EAAE,MAAM;UACfvD,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACAtJ,OAAA,CAACX,YAAY;UAACkK,IAAI,EAAE,EAAG;UAACX,KAAK,EAAE;YAAE8D,YAAY,EAAE,MAAM;YAAEiD,OAAO,EAAE;UAAI;QAAE;UAAAnG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzE3J,OAAA;UAAAsJ,QAAA,EAAG;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrC3J,OAAA;UACEmN,OAAO,EAAEA,CAAA,KAAM3K,iBAAiB,CAAC,CAAE;UACnCoG,KAAK,EAAE;YACL6D,SAAS,EAAE,MAAM;YACjBE,OAAO,EAAE,gBAAgB;YACzB5D,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACdsC,MAAM,EAAE,MAAM;YACd1C,YAAY,EAAE,KAAK;YACnBqE,MAAM,EAAE,SAAS;YACjBpD,UAAU,EAAE;UACd,CAAE;UAAAX,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAEN3J,OAAA;QAAK4I,KAAK,EAAE;UACVK,OAAO,EAAE,MAAM;UACfW,GAAG,EAAE;QACP,CAAE;QAAAN,QAAA,EACC1B,eAAe,CAACG,GAAG,CAAEpF,KAAK,iBACzB3C,OAAA;UAEE4I,KAAK,EAAE;YACL8C,MAAM,EAAE,mBAAmB;YAC3B1C,YAAY,EAAE,MAAM;YACpB2D,OAAO,EAAE,QAAQ;YACjB5D,eAAe,EAAE,OAAO;YACxB6E,UAAU,EAAE;UACd,CAAE;UAAAtE,QAAA,gBAEFtJ,OAAA;YAAK4I,KAAK,EAAE;cACVK,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBwD,YAAY,EAAE;YAChB,CAAE;YAAApD,QAAA,gBACAtJ,OAAA;cAAK4I,KAAK,EAAE;gBAAE8G,IAAI,EAAE;cAAE,CAAE;cAAApG,QAAA,gBACtBtJ,OAAA;gBAAI4I,KAAK,EAAE;kBACTS,QAAQ,EAAE,UAAU;kBACpBY,UAAU,EAAE,KAAK;kBACjBb,KAAK,EAAE,SAAS;kBAChBqE,MAAM,EAAE;gBACV,CAAE;gBAAAnE,QAAA,EACC3G,KAAK,CAACyC;cAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACL3J,OAAA;gBAAK4I,KAAK,EAAE;kBACVK,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,GAAG,EAAE,MAAM;kBACXP,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,gBACAtJ,OAAA;kBAAM4Q,SAAS,EAAC,yBAAyB;kBAAAtH,QAAA,gBACvCtJ,OAAA,CAACX,YAAY;oBAACuR,SAAS,EAAC;kBAAS;oBAAApH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnChH,KAAK,CAAC+B,UAAU;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNhH,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,iBACpD1E,OAAA;kBAAAsJ,QAAA,GAAM,SAAE,EAAC3G,KAAK,CAAC8B,QAAQ;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/B,eACD3J,OAAA;kBAAM4I,KAAK,EAAE;oBACXG,eAAe,EAAE,SAAS;oBAC1BK,KAAK,EAAE,SAAS;oBAChBuD,OAAO,EAAE,gBAAgB;oBACzB3D,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE,SAAS;oBACnBY,UAAU,EAAE;kBACd,CAAE;kBAAAX,QAAA,EACC9E,gBAAgB,CAAC7B,KAAK;gBAAC;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACP3J,OAAA;kBAAM4I,KAAK,EAAE;oBACXG,eAAe,EAAE1E,iBAAiB,CAAC1B,KAAK,CAAC;oBACzCyG,KAAK,EAAE,OAAO;oBACduD,OAAO,EAAE,gBAAgB;oBACzB3D,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE;kBACZ,CAAE;kBAAAC,QAAA,EACC3G,KAAK,CAACkO;gBAAiB;kBAAArH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEJ,CAAC,EACLhH,KAAK,CAACyE,WAAW,iBAChBpH,OAAA;gBAAG4I,KAAK,EAAE;kBACRS,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBqE,MAAM,EAAE,cAAc;kBACtB8C,UAAU,EAAE;gBACd,CAAE;gBAAAjH,QAAA,EACC3G,KAAK,CAACyE;cAAW;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3J,OAAA;cAAK4I,KAAK,EAAE;gBACVK,OAAO,EAAE,MAAM;gBACfW,GAAG,EAAE,QAAQ;gBACbkH,UAAU,EAAE;cACd,CAAE;cAAAxH,QAAA,gBACAtJ,OAAA;gBACEmN,OAAO,EAAEA,CAAA,KAAMzK,eAAe,CAACC,KAAK,CAAE;gBACtCiG,KAAK,EAAE;kBACL+D,OAAO,EAAE,QAAQ;kBACjB5D,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBsC,MAAM,EAAE,MAAM;kBACd1C,YAAY,EAAE,KAAK;kBACnBqE,MAAM,EAAE,SAAS;kBACjBpE,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACF/D,KAAK,EAAC,YAAY;gBAAAkE,QAAA,eAElBtJ,OAAA,CAACP,IAAI;kBAAC8J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAET3J,OAAA;gBACEmN,OAAO,EAAEA,CAAA,KAAOxK,KAAK,CAASoO,YAAY,GACtCvN,oBAAoB,CAACb,KAAK,CAACK,WAAW,CAAC,GACvCK,kBAAkB,CAACV,KAAK,CAACK,WAAW,CACvC;gBACD4F,KAAK,EAAE;kBACL+D,OAAO,EAAE,QAAQ;kBACjB5D,eAAe,EAAGpG,KAAK,CAASoO,YAAY,GAAG,SAAS,GAAG,SAAS;kBACpE3H,KAAK,EAAGzG,KAAK,CAASoO,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC1DrF,MAAM,EAAE,MAAM;kBACd1C,YAAY,EAAE,KAAK;kBACnBqE,MAAM,EAAE,SAAS;kBACjBpE,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACF/D,KAAK,EAAGzC,KAAK,CAASoO,YAAY,GAAG,iBAAiB,GAAG,eAAgB;gBAAAzH,QAAA,EAEvE3G,KAAK,CAASoO,YAAY,gBAAG/Q,OAAA,CAACL,KAAK;kBAAC4J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3J,OAAA,CAACN,IAAI;kBAAC6J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAET3J,OAAA;gBACEmN,OAAO,EAAEA,CAAA,KAAMzJ,iBAAiB,CAACf,KAAK,CAACK,WAAW,CAAE;gBACpD4F,KAAK,EAAE;kBACL+D,OAAO,EAAE,QAAQ;kBACjB5D,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBsC,MAAM,EAAE,MAAM;kBACd1C,YAAY,EAAE,KAAK;kBACnBqE,MAAM,EAAE,SAAS;kBACjBpE,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACF/D,KAAK,EAAC,cAAc;gBAAAkE,QAAA,eAEpBtJ,OAAA,CAACR,MAAM;kBAAC+J,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3J,OAAA;YAAK4I,KAAK,EAAE;cACVoI,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClBxE,SAAS,EAAE;YACb,CAAE;YAAAnD,QAAA,gBACAtJ,OAAA;cAAK4I,KAAK,EAAE;gBACVS,QAAQ,EAAE,UAAU;gBACpBY,UAAU,EAAE,KAAK;gBACjBb,KAAK,EAAE,SAAS;gBAChBsD,YAAY,EAAE;cAChB,CAAE;cAAApD,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3J,OAAA,CAACwI,WAAW;cAAClF,OAAO,EAAEX,KAAK,CAACK;YAAY;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA,GArJD,gBAAgBhH,KAAK,CAACK,WAAW,EAAE;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsJrC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3J,OAAA,CAACgM,kBAAkB;MAAAxC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtB3J,OAAA,CAACd,kBAAkB;MACjBgS,MAAM,EAAEvQ,SAAU;MAClBwQ,OAAO,EAAE/N,gBAAiB;MAC1BgO,MAAM,EAAExO,eAAgB;MACxBD,KAAK,EAAE9B,YAAa;MACpBJ,YAAY,EAAEA,YAAa;MAC3BqB,OAAO,EAAEA;IAAQ;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;EAAA,QA5uCK/K,WAAW,EAEQC,qBAAqB;AAAA,EA0uC7C,CAAC;EAAA,QA5uCID,WAAW,EAEQC,qBAAqB;AAAA,EA0uC5C;AAACwS,GAAA,GAtwCGjS,QAAkB;AAwwCxBA,QAAQ,CAACkS,WAAW,GAAG,UAAU;AAEjC,eAAelS,QAAQ;AAAC,IAAAe,EAAA,EAAAkR,GAAA;AAAAE,YAAA,CAAApR,EAAA;AAAAoR,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}