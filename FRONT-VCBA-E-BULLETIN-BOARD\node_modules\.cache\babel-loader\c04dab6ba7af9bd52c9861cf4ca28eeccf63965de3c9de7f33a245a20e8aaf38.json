{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12v2a2 2 0 0 1-2 2H9a1 1 0 0 0-1 1v3a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2h0\",\n  key: \"1mcohs\"\n}], [\"path\", {\n  d: \"M4 16a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v3a1 1 0 0 1-1 1h-5a2 2 0 0 0-2 2v2\",\n  key: \"1r1efp\"\n}]];\nconst SquaresExclude = createLucideIcon(\"squares-exclude\", __iconNode);\nexport { __iconNode, SquaresExclude as default };\n//# sourceMappingURL=squares-exclude.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}