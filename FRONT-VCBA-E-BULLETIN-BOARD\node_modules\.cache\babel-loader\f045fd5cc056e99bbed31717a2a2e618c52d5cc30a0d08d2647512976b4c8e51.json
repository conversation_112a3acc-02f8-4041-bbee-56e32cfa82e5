{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M6 21V9a9 9 0 0 0 9 9\",\n  key: \"7kw0sc\"\n}]];\nconst GitMerge = createLucideIcon(\"git-merge\", __iconNode);\nexport { __iconNode, GitMerge as default };\n//# sourceMappingURL=git-merge.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}