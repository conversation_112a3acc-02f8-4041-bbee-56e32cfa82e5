{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m14.31 8 5.74 9.94\",\n  key: \"1y6ab4\"\n}], [\"path\", {\n  d: \"M9.69 8h11.48\",\n  key: \"1wxppr\"\n}], [\"path\", {\n  d: \"m7.38 12 5.74-9.94\",\n  key: \"1grp0k\"\n}], [\"path\", {\n  d: \"M9.69 16 3.95 6.06\",\n  key: \"libnyf\"\n}], [\"path\", {\n  d: \"M14.31 16H2.83\",\n  key: \"x5fava\"\n}], [\"path\", {\n  d: \"m16.62 12-5.74 9.94\",\n  key: \"1vwawt\"\n}]];\nconst Aperture = createLucideIcon(\"aperture\", __iconNode);\nexport { __iconNode, Aperture as default };\n//# sourceMappingURL=aperture.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}