{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"5\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1wp1u1\"\n}], [\"path\", {\n  d: \"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8\",\n  key: \"1s80jp\"\n}], [\"path\", {\n  d: \"M10 12h4\",\n  key: \"a56b0p\"\n}]];\nconst Archive = createLucideIcon(\"archive\", __iconNode);\nexport { __iconNode, Archive as default };\n//# sourceMappingURL=archive.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}