{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 14.347V14c0-6-4-12-8-12-1.078 0-2.157.436-3.157 1.19\",\n  key: \"13g2jy\"\n}], [\"path\", {\n  d: \"M6.206 6.21C4.871 8.4 4 11.2 4 14a8 8 0 0 0 14.568 4.568\",\n  key: \"1581id\"\n}]];\nconst EggOff = createLucideIcon(\"egg-off\", __iconNode);\nexport { __iconNode, EggOff as default };\n//# sourceMappingURL=egg-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}