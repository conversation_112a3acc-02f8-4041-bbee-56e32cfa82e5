{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 17a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3.9a2 2 0 0 1-1.69-.9l-.81-1.2a2 2 0 0 0-1.67-.9H8a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2Z\",\n  key: \"4u7rpt\"\n}], [\"path\", {\n  d: \"M2 8v11a2 2 0 0 0 2 2h14\",\n  key: \"1eicx1\"\n}]];\nconst Folders = createLucideIcon(\"folders\", __iconNode);\nexport { __iconNode, Folders as default };\n//# sourceMappingURL=folders.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}