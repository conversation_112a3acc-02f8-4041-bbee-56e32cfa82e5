{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.5 7a24 24 0 0 1 0 10\",\n  key: \"8n60xe\"\n}], [\"path\", {\n  d: \"M4.5 7a24 24 0 0 0 0 10\",\n  key: \"2lmadr\"\n}], [\"path\", {\n  d: \"M7 19.5a24 24 0 0 0 10 0\",\n  key: \"1q94o2\"\n}], [\"path\", {\n  d: \"M7 4.5a24 24 0 0 1 10 0\",\n  key: \"2z8ypa\"\n}], [\"rect\", {\n  x: \"17\",\n  y: \"17\",\n  width: \"5\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"1ac74s\"\n}], [\"rect\", {\n  x: \"17\",\n  y: \"2\",\n  width: \"5\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"1e7h5j\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"17\",\n  width: \"5\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"1t4eah\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"2\",\n  width: \"5\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"940dhs\"\n}]];\nconst VectorSquare = createLucideIcon(\"vector-square\", __iconNode);\nexport { __iconNode, VectorSquare as default };\n//# sourceMappingURL=vector-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}