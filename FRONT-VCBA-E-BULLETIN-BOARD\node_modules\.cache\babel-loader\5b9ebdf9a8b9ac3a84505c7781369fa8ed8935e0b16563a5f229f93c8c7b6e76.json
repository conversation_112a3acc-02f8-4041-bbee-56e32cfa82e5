{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\",\n  key: \"1357e3\"\n}], [\"path\", {\n  d: \"M3 3v5h5\",\n  key: \"1xhq8a\"\n}], [\"path\", {\n  d: \"M12 7v5l4 2\",\n  key: \"1fdv2h\"\n}]];\nconst History = createLucideIcon(\"history\", __iconNode);\nexport { __iconNode, History as default };\n//# sourceMappingURL=history.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}