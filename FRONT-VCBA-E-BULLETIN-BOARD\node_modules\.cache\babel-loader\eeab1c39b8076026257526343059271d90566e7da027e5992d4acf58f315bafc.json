{"ast": null, "code": "import { httpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS } from '../config/constants';\n\n// Types for calendar\n\nclass CalendarService {\n  // Helper method to determine which HTTP client to use based on user type\n  async makeRequest(method, endpoint, data, params) {\n    // Check if user is admin (has admin token) - check both admin-specific and general auth\n    const isAdminAuth = AdminAuthService.isAuthenticated();\n    const hasGeneralToken = localStorage.getItem('vcba_auth_token');\n    const generalUserData = localStorage.getItem('vcba_user_data');\n\n    // Check if general auth user is admin\n    let isGeneralAdmin = false;\n    if (hasGeneralToken && generalUserData) {\n      try {\n        const userData = JSON.parse(generalUserData);\n        isGeneralAdmin = userData.role === 'admin';\n      } catch (e) {\n        // Ignore parsing errors\n      }\n    }\n    const isAdmin = isAdminAuth || isGeneralAdmin;\n    if (isAdmin) {\n      // For admin requests, prefer AdminAuthService if available, otherwise use httpClient with admin token\n      if (isAdminAuth) {\n        // Use AdminAuthService for admin requests\n        const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n        switch (method) {\n          case 'GET':\n            return AdminAuthService.request('GET', url);\n          case 'POST':\n            return AdminAuthService.request('POST', endpoint, data);\n          case 'PUT':\n            return AdminAuthService.request('PUT', endpoint, data);\n          case 'DELETE':\n            return AdminAuthService.request('DELETE', endpoint);\n          default:\n            throw new Error(`Unsupported method: ${method}`);\n        }\n      } else {\n        // Use regular httpClient with general admin token\n        const urlWithParams = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n        switch (method) {\n          case 'GET':\n            return httpClient.get(urlWithParams);\n          case 'POST':\n            return httpClient.post(endpoint, data);\n          case 'PUT':\n            return httpClient.put(endpoint, data);\n          case 'DELETE':\n            return httpClient.delete(endpoint);\n          default:\n            throw new Error(`Unsupported method: ${method}`);\n        }\n      }\n    } else {\n      // Use regular httpClient for student/public requests\n      const urlWithParams = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n      switch (method) {\n        case 'GET':\n          return httpClient.get(urlWithParams);\n        case 'POST':\n          return httpClient.post(endpoint, data);\n        case 'PUT':\n          return httpClient.put(endpoint, data);\n        case 'DELETE':\n          return httpClient.delete(endpoint);\n        default:\n          throw new Error(`Unsupported method: ${method}`);\n      }\n    }\n  }\n\n  // Get calendar events with filters and pagination\n  async getEvents(filters) {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.BASE, undefined, params);\n  }\n\n  // Get single event by ID\n  async getEventById(id) {\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));\n  }\n\n  // Create new event\n  async createEvent(data) {\n    return this.makeRequest('POST', API_ENDPOINTS.CALENDAR.BASE, data);\n  }\n\n  // Update event\n  async updateEvent(id, data) {\n    return this.makeRequest('PUT', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()), data);\n  }\n\n  // Delete event\n  async deleteEvent(id) {\n    return this.makeRequest('DELETE', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));\n  }\n\n  // Get calendar view (month/year)\n  async getCalendarView(year, month) {\n    const params = {\n      year: year.toString()\n    };\n    if (month) {\n      params.month = month.toString();\n    }\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.VIEW, undefined, params);\n  }\n\n  // Get current month events\n  async getCurrentMonthEvents() {\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.CURRENT_MONTH);\n  }\n\n  // Get upcoming events\n  async getUpcomingEvents(limit) {\n    const params = limit ? {\n      limit: limit.toString()\n    } : undefined;\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.UPCOMING, undefined, params);\n  }\n\n  // Get events by date\n  async getEventsByDate(date) {\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.BY_DATE(date));\n  }\n\n  // Get events by date range\n  async getEventsByDateRange(startDate, endDate) {\n    const params = {\n      start_date: startDate,\n      end_date: endDate\n    };\n    return this.makeRequest('GET', API_ENDPOINTS.CALENDAR.DATE_RANGE, undefined, params);\n  }\n\n  // Helper method to build query parameters\n  buildQueryParams(filters) {\n    const params = {};\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    return params;\n  }\n\n  // Get events for a specific month\n  async getMonthEvents(year, month) {\n    return this.getCalendarView(year, month);\n  }\n\n  // Get events for a specific year\n  async getYearEvents(year) {\n    return this.getCalendarView(year);\n  }\n\n  // Get today's events\n  async getTodayEvents() {\n    // Format today's date manually to avoid timezone issues\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const todayStr = `${year}-${month}-${day}`;\n    return this.getEventsByDate(todayStr);\n  }\n\n  // Get this week's events\n  async getWeekEvents(startDate) {\n    const start = startDate || this.getWeekStart();\n    const end = this.getWeekEnd(start);\n    return this.getEventsByDateRange(start, end);\n  }\n\n  // Get events for admin dashboard\n  async getAdminEvents(filters) {\n    const defaultFilters = {\n      page: 1,\n      limit: 50,\n      sort_by: 'event_date',\n      sort_order: 'ASC',\n      ...filters\n    };\n    return this.getEvents(defaultFilters);\n  }\n\n  // Get active events only\n  async getActiveEvents(filters) {\n    return this.getEvents({\n      is_active: true,\n      ...filters\n    });\n  }\n\n  // Search events\n  async searchEvents(query, filters) {\n    return this.getEvents({\n      search: query,\n      is_active: true,\n      ...filters\n    });\n  }\n\n  // Get recurring events\n  async getRecurringEvents() {\n    return this.getEvents({\n      is_recurring: true,\n      is_active: true,\n      sort_by: 'event_date',\n      sort_order: 'ASC'\n    });\n  }\n\n  // Get events by category\n  async getEventsByCategory(categoryId) {\n    return this.getEvents({\n      category_id: categoryId,\n      is_active: true,\n      sort_by: 'event_date',\n      sort_order: 'ASC'\n    });\n  }\n\n  // Utility methods for date calculations\n  getWeekStart(date) {\n    const d = date ? new Date(date) : new Date();\n    const day = d.getDay();\n    const diff = d.getDate() - day;\n    const weekStart = new Date(d.setDate(diff));\n    return weekStart.toISOString().split('T')[0];\n  }\n  getWeekEnd(startDate) {\n    const start = new Date(startDate);\n    const end = new Date(start);\n    end.setDate(start.getDate() + 6);\n    return end.toISOString().split('T')[0];\n  }\n\n  // Get month boundaries\n  getMonthBoundaries(year, month) {\n    const start = new Date(year, month - 1, 1);\n    const end = new Date(year, month, 0);\n    return {\n      start: start.toISOString().split('T')[0],\n      end: end.toISOString().split('T')[0]\n    };\n  }\n\n  // Format date for display\n  formatDate(date) {\n    return new Date(date).toLocaleDateString();\n  }\n\n  // Check if date is today\n  isToday(date) {\n    const today = new Date().toISOString().split('T')[0];\n    return date === today;\n  }\n\n  // Check if date is in the past\n  isPast(date) {\n    const today = new Date().toISOString().split('T')[0];\n    return date < today;\n  }\n\n  // Check if date is in the future\n  isFuture(date) {\n    const today = new Date().toISOString().split('T')[0];\n    return date > today;\n  }\n\n  // Get categories with subcategories for calendar events\n  async getCategoriesWithSubcategories() {\n    return this.makeRequest('GET', `${API_ENDPOINTS.CALENDAR.BASE}/categories/with-subcategories`);\n  }\n\n  // Calendar attachment methods\n  async getEventAttachments(eventId) {\n    return this.makeRequest('GET', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments`);\n  }\n  async uploadEventAttachments(eventId, formData) {\n    return this.makeRequest('POST', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments`, formData);\n  }\n  async deleteEventAttachment(attachmentId) {\n    return this.makeRequest('DELETE', `${API_ENDPOINTS.CALENDAR.BASE}/attachments/${attachmentId}`);\n  }\n  async setPrimaryAttachment(eventId, attachmentId) {\n    return this.makeRequest('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments/${attachmentId}/primary`);\n  }\n\n  // Event management methods\n  async publishEvent(eventId) {\n    return this.makeRequest('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/publish`);\n  }\n  async unpublishEvent(eventId) {\n    return this.makeRequest('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/unpublish`);\n  }\n  async softDeleteEvent(eventId) {\n    return this.makeRequest('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/soft-delete`);\n  }\n  async restoreEvent(eventId) {\n    return this.makeRequest('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/restore`);\n  }\n}\nexport const calendarService = new CalendarService();\nexport default calendarService;", "map": {"version": 3, "names": ["httpClient", "AdminAuthService", "API_ENDPOINTS", "CalendarService", "makeRequest", "method", "endpoint", "data", "params", "isAdminAuth", "isAuthenticated", "hasGeneralToken", "localStorage", "getItem", "generalUserData", "isGeneralAdmin", "userData", "JSON", "parse", "role", "e", "isAdmin", "url", "URLSearchParams", "request", "Error", "urlWithParams", "get", "post", "put", "delete", "getEvents", "filters", "buildQueryParams", "undefined", "CALENDAR", "BASE", "getEventById", "id", "BY_ID", "toString", "createEvent", "updateEvent", "deleteEvent", "getCalendarView", "year", "month", "VIEW", "getCurrentMonthEvents", "CURRENT_MONTH", "getUpcomingEvents", "limit", "UPCOMING", "getEventsByDate", "date", "BY_DATE", "getEventsByDateRange", "startDate", "endDate", "start_date", "end_date", "DATE_RANGE", "Object", "entries", "for<PERSON>ach", "key", "value", "getMonthEvents", "getYearEvents", "getTodayEvents", "today", "Date", "getFullYear", "String", "getMonth", "padStart", "day", "getDate", "todayStr", "getWeekEvents", "start", "getWeekStart", "end", "getWeekEnd", "getAdminEvents", "defaultFilters", "page", "sort_by", "sort_order", "getActiveEvents", "is_active", "searchEvents", "query", "search", "getRecurringEvents", "is_recurring", "getEventsByCategory", "categoryId", "category_id", "d", "getDay", "diff", "weekStart", "setDate", "toISOString", "split", "getMonthBoundaries", "formatDate", "toLocaleDateString", "isToday", "isPast", "isFuture", "getCategoriesWithSubcategories", "getEventAttachments", "eventId", "uploadEventAttachments", "formData", "deleteEventAttachment", "attachmentId", "setPrimaryAttachment", "publishEvent", "unpublishEvent", "softDeleteEvent", "restoreEvent", "calendarService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS, API_BASE_URL } from '../config/constants';\nimport { ApiResponse } from '../types';\n\n// Types for calendar\nexport interface CalendarEvent {\n  calendar_id: number;\n  title: string;\n  description?: string;\n  event_date: string;\n  end_date?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  category_name?: string;\n  category_color?: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  is_recurring: boolean;\n  recurrence_pattern?: 'yearly' | 'monthly' | 'weekly';\n  is_active: boolean;\n  created_by: number;\n  created_by_name?: string;\n  created_at: string;\n  updated_at: string;\n  // Multi-day event properties (added by backend for calendar view)\n  isMultiDay?: boolean;\n  isEventStart?: boolean;\n  isEventEnd?: boolean;\n  originalStartDate?: string;\n  originalEndDate?: string;\n}\n\nexport interface CreateEventData {\n  title: string;\n  description?: string;\n  event_date: string;\n  end_date?: string;\n  category_id: number;\n  subcategory_id?: number | null;\n  is_recurring?: boolean;\n  recurrence_pattern?: 'yearly' | 'monthly' | 'weekly';\n  is_active?: boolean;\n}\n\nexport interface UpdateEventData {\n  title?: string;\n  description?: string;\n  event_date?: string;\n  end_date?: string;\n  category_id?: number;\n  subcategory_id?: number | null;\n  is_recurring?: boolean;\n  recurrence_pattern?: 'yearly' | 'monthly' | 'weekly';\n  is_active?: boolean;\n}\n\nexport interface EventFilters {\n  page?: number;\n  limit?: number;\n  start_date?: string;\n  end_date?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  is_active?: boolean;\n  is_recurring?: boolean;\n  search?: string;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\n\n\nexport interface PaginatedEventsResponse {\n  events: CalendarEvent[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface CalendarViewResponse {\n  events: Record<string, CalendarEvent[]>; // Grouped by date (YYYY-MM-DD)\n}\n\nclass CalendarService {\n  // Helper method to determine which HTTP client to use based on user type\n  private async makeRequest<T>(\n    method: 'GET' | 'POST' | 'PUT' | 'DELETE',\n    endpoint: string,\n    data?: any,\n    params?: Record<string, string>\n  ): Promise<ApiResponse<T>> {\n    // Check if user is admin (has admin token) - check both admin-specific and general auth\n    const isAdminAuth = AdminAuthService.isAuthenticated();\n    const hasGeneralToken = localStorage.getItem('vcba_auth_token');\n    const generalUserData = localStorage.getItem('vcba_user_data');\n\n    // Check if general auth user is admin\n    let isGeneralAdmin = false;\n    if (hasGeneralToken && generalUserData) {\n      try {\n        const userData = JSON.parse(generalUserData);\n        isGeneralAdmin = userData.role === 'admin';\n      } catch (e) {\n        // Ignore parsing errors\n      }\n    }\n\n    const isAdmin = isAdminAuth || isGeneralAdmin;\n\n    if (isAdmin) {\n      // For admin requests, prefer AdminAuthService if available, otherwise use httpClient with admin token\n      if (isAdminAuth) {\n        // Use AdminAuthService for admin requests\n        const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n\n        switch (method) {\n          case 'GET':\n            return AdminAuthService.request<T>('GET', url);\n          case 'POST':\n            return AdminAuthService.request<T>('POST', endpoint, data);\n          case 'PUT':\n            return AdminAuthService.request<T>('PUT', endpoint, data);\n          case 'DELETE':\n            return AdminAuthService.request<T>('DELETE', endpoint);\n          default:\n            throw new Error(`Unsupported method: ${method}`);\n        }\n      } else {\n        // Use regular httpClient with general admin token\n        const urlWithParams = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n\n        switch (method) {\n          case 'GET':\n            return httpClient.get<T>(urlWithParams);\n          case 'POST':\n            return httpClient.post<T>(endpoint, data);\n          case 'PUT':\n            return httpClient.put<T>(endpoint, data);\n          case 'DELETE':\n            return httpClient.delete<T>(endpoint);\n          default:\n            throw new Error(`Unsupported method: ${method}`);\n        }\n      }\n    } else {\n      // Use regular httpClient for student/public requests\n      const urlWithParams = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;\n\n      switch (method) {\n        case 'GET':\n          return httpClient.get<T>(urlWithParams);\n        case 'POST':\n          return httpClient.post<T>(endpoint, data);\n        case 'PUT':\n          return httpClient.put<T>(endpoint, data);\n        case 'DELETE':\n          return httpClient.delete<T>(endpoint);\n        default:\n          throw new Error(`Unsupported method: ${method}`);\n      }\n    }\n  }\n\n  // Get calendar events with filters and pagination\n  async getEvents(filters?: EventFilters): Promise<ApiResponse<PaginatedEventsResponse>> {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return this.makeRequest<PaginatedEventsResponse>('GET', API_ENDPOINTS.CALENDAR.BASE, undefined, params);\n  }\n\n  // Get single event by ID\n  async getEventById(id: number): Promise<ApiResponse<{ event: CalendarEvent }>> {\n    return this.makeRequest<{ event: CalendarEvent }>('GET', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));\n  }\n\n  // Create new event\n  async createEvent(data: CreateEventData): Promise<ApiResponse<{ event: CalendarEvent }>> {\n    return this.makeRequest<{ event: CalendarEvent }>('POST', API_ENDPOINTS.CALENDAR.BASE, data);\n  }\n\n  // Update event\n  async updateEvent(id: number, data: UpdateEventData): Promise<ApiResponse<{ event: CalendarEvent }>> {\n    return this.makeRequest<{ event: CalendarEvent }>('PUT', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()), data);\n  }\n\n  // Delete event\n  async deleteEvent(id: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('DELETE', API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));\n  }\n\n  // Get calendar view (month/year)\n  async getCalendarView(year: number, month?: number): Promise<ApiResponse<CalendarViewResponse>> {\n    const params: Record<string, string> = { year: year.toString() };\n    if (month) {\n      params.month = month.toString();\n    }\n    return this.makeRequest<CalendarViewResponse>('GET', API_ENDPOINTS.CALENDAR.VIEW, undefined, params);\n  }\n\n  // Get current month events\n  async getCurrentMonthEvents(): Promise<ApiResponse<CalendarViewResponse & { year: number; month: number }>> {\n    return this.makeRequest<CalendarViewResponse & { year: number; month: number }>('GET', API_ENDPOINTS.CALENDAR.CURRENT_MONTH);\n  }\n\n  // Get upcoming events\n  async getUpcomingEvents(limit?: number): Promise<ApiResponse<{ events: CalendarEvent[] }>> {\n    const params = limit ? { limit: limit.toString() } : undefined;\n    return this.makeRequest<{ events: CalendarEvent[] }>('GET', API_ENDPOINTS.CALENDAR.UPCOMING, undefined, params);\n  }\n\n  // Get events by date\n  async getEventsByDate(date: string): Promise<ApiResponse<{ events: CalendarEvent[] }>> {\n    return this.makeRequest<{ events: CalendarEvent[] }>('GET', API_ENDPOINTS.CALENDAR.BY_DATE(date));\n  }\n\n  // Get events by date range\n  async getEventsByDateRange(startDate: string, endDate: string): Promise<ApiResponse<{ events: CalendarEvent[] }>> {\n    const params = { start_date: startDate, end_date: endDate };\n    return this.makeRequest<{ events: CalendarEvent[] }>('GET', API_ENDPOINTS.CALENDAR.DATE_RANGE, undefined, params);\n  }\n\n\n\n  // Helper method to build query parameters\n  private buildQueryParams(filters: EventFilters): Record<string, string> {\n    const params: Record<string, string> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    \n    return params;\n  }\n\n  // Get events for a specific month\n  async getMonthEvents(year: number, month: number): Promise<ApiResponse<CalendarViewResponse>> {\n    return this.getCalendarView(year, month);\n  }\n\n  // Get events for a specific year\n  async getYearEvents(year: number): Promise<ApiResponse<CalendarViewResponse>> {\n    return this.getCalendarView(year);\n  }\n\n  // Get today's events\n  async getTodayEvents(): Promise<ApiResponse<{ events: CalendarEvent[] }>> {\n    // Format today's date manually to avoid timezone issues\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = String(today.getMonth() + 1).padStart(2, '0');\n    const day = String(today.getDate()).padStart(2, '0');\n    const todayStr = `${year}-${month}-${day}`;\n    return this.getEventsByDate(todayStr);\n  }\n\n  // Get this week's events\n  async getWeekEvents(startDate?: string): Promise<ApiResponse<{ events: CalendarEvent[] }>> {\n    const start = startDate || this.getWeekStart();\n    const end = this.getWeekEnd(start);\n    return this.getEventsByDateRange(start, end);\n  }\n\n  // Get events for admin dashboard\n  async getAdminEvents(filters?: Partial<EventFilters>): Promise<ApiResponse<PaginatedEventsResponse>> {\n    const defaultFilters: EventFilters = {\n      page: 1,\n      limit: 50,\n      sort_by: 'event_date',\n      sort_order: 'ASC',\n      ...filters\n    };\n    return this.getEvents(defaultFilters);\n  }\n\n  // Get active events only\n  async getActiveEvents(filters?: Partial<EventFilters>): Promise<ApiResponse<PaginatedEventsResponse>> {\n    return this.getEvents({\n      is_active: true,\n      ...filters\n    });\n  }\n\n  // Search events\n  async searchEvents(query: string, filters?: Partial<EventFilters>): Promise<ApiResponse<PaginatedEventsResponse>> {\n    return this.getEvents({\n      search: query,\n      is_active: true,\n      ...filters\n    });\n  }\n\n  // Get recurring events\n  async getRecurringEvents(): Promise<ApiResponse<PaginatedEventsResponse>> {\n    return this.getEvents({\n      is_recurring: true,\n      is_active: true,\n      sort_by: 'event_date',\n      sort_order: 'ASC'\n    });\n  }\n\n  // Get events by category\n  async getEventsByCategory(categoryId: number): Promise<ApiResponse<PaginatedEventsResponse>> {\n    return this.getEvents({\n      category_id: categoryId,\n      is_active: true,\n      sort_by: 'event_date',\n      sort_order: 'ASC'\n    });\n  }\n\n\n\n  // Utility methods for date calculations\n  private getWeekStart(date?: string): string {\n    const d = date ? new Date(date) : new Date();\n    const day = d.getDay();\n    const diff = d.getDate() - day;\n    const weekStart = new Date(d.setDate(diff));\n    return weekStart.toISOString().split('T')[0];\n  }\n\n  private getWeekEnd(startDate: string): string {\n    const start = new Date(startDate);\n    const end = new Date(start);\n    end.setDate(start.getDate() + 6);\n    return end.toISOString().split('T')[0];\n  }\n\n  // Get month boundaries\n  getMonthBoundaries(year: number, month: number): { start: string; end: string } {\n    const start = new Date(year, month - 1, 1);\n    const end = new Date(year, month, 0);\n    \n    return {\n      start: start.toISOString().split('T')[0],\n      end: end.toISOString().split('T')[0]\n    };\n  }\n\n  // Format date for display\n  formatDate(date: string): string {\n    return new Date(date).toLocaleDateString();\n  }\n\n  // Check if date is today\n  isToday(date: string): boolean {\n    const today = new Date().toISOString().split('T')[0];\n    return date === today;\n  }\n\n  // Check if date is in the past\n  isPast(date: string): boolean {\n    const today = new Date().toISOString().split('T')[0];\n    return date < today;\n  }\n\n  // Check if date is in the future\n  isFuture(date: string): boolean {\n    const today = new Date().toISOString().split('T')[0];\n    return date > today;\n  }\n\n  // Get categories with subcategories for calendar events\n  async getCategoriesWithSubcategories(): Promise<ApiResponse<{ categories: any[] }>> {\n    return this.makeRequest<{ categories: any[] }>('GET', `${API_ENDPOINTS.CALENDAR.BASE}/categories/with-subcategories`);\n  }\n\n  // Calendar attachment methods\n  async getEventAttachments(eventId: number): Promise<ApiResponse<{ attachments: any[] }>> {\n    return this.makeRequest<{ attachments: any[] }>('GET', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments`);\n  }\n\n  async uploadEventAttachments(eventId: number, formData: FormData): Promise<ApiResponse<{ attachments: any[] }>> {\n    return this.makeRequest<{ attachments: any[] }>('POST', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments`, formData);\n  }\n\n  async deleteEventAttachment(attachmentId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('DELETE', `${API_ENDPOINTS.CALENDAR.BASE}/attachments/${attachmentId}`);\n  }\n\n  async setPrimaryAttachment(eventId: number, attachmentId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/attachments/${attachmentId}/primary`);\n  }\n\n  // Event management methods\n  async publishEvent(eventId: number): Promise<ApiResponse<{ event: CalendarEvent }>> {\n    return this.makeRequest<{ event: CalendarEvent }>('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/publish`);\n  }\n\n  async unpublishEvent(eventId: number): Promise<ApiResponse<{ event: CalendarEvent }>> {\n    return this.makeRequest<{ event: CalendarEvent }>('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/unpublish`);\n  }\n\n  async softDeleteEvent(eventId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/soft-delete`);\n  }\n\n  async restoreEvent(eventId: number): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>('PUT', `${API_ENDPOINTS.CALENDAR.BASE}/${eventId}/restore`);\n  }\n}\n\nexport const calendarService = new CalendarService();\nexport default calendarService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,aAAa,QAAsB,qBAAqB;;AAGjE;;AAoFA,MAAMC,eAAe,CAAC;EACpB;EACA,MAAcC,WAAWA,CACvBC,MAAyC,EACzCC,QAAgB,EAChBC,IAAU,EACVC,MAA+B,EACN;IACzB;IACA,MAAMC,WAAW,GAAGR,gBAAgB,CAACS,eAAe,CAAC,CAAC;IACtD,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAC/D,MAAMC,eAAe,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;;IAE9D;IACA,IAAIE,cAAc,GAAG,KAAK;IAC1B,IAAIJ,eAAe,IAAIG,eAAe,EAAE;MACtC,IAAI;QACF,MAAME,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;QAC5CC,cAAc,GAAGC,QAAQ,CAACG,IAAI,KAAK,OAAO;MAC5C,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV;MAAA;IAEJ;IAEA,MAAMC,OAAO,GAAGZ,WAAW,IAAIM,cAAc;IAE7C,IAAIM,OAAO,EAAE;MACX;MACA,IAAIZ,WAAW,EAAE;QACf;QACA,MAAMa,GAAG,GAAGd,MAAM,GAAG,GAAGF,QAAQ,IAAI,IAAIiB,eAAe,CAACf,MAAM,CAAC,EAAE,GAAGF,QAAQ;QAE5E,QAAQD,MAAM;UACZ,KAAK,KAAK;YACR,OAAOJ,gBAAgB,CAACuB,OAAO,CAAI,KAAK,EAAEF,GAAG,CAAC;UAChD,KAAK,MAAM;YACT,OAAOrB,gBAAgB,CAACuB,OAAO,CAAI,MAAM,EAAElB,QAAQ,EAAEC,IAAI,CAAC;UAC5D,KAAK,KAAK;YACR,OAAON,gBAAgB,CAACuB,OAAO,CAAI,KAAK,EAAElB,QAAQ,EAAEC,IAAI,CAAC;UAC3D,KAAK,QAAQ;YACX,OAAON,gBAAgB,CAACuB,OAAO,CAAI,QAAQ,EAAElB,QAAQ,CAAC;UACxD;YACE,MAAM,IAAImB,KAAK,CAAC,uBAAuBpB,MAAM,EAAE,CAAC;QACpD;MACF,CAAC,MAAM;QACL;QACA,MAAMqB,aAAa,GAAGlB,MAAM,GAAG,GAAGF,QAAQ,IAAI,IAAIiB,eAAe,CAACf,MAAM,CAAC,EAAE,GAAGF,QAAQ;QAEtF,QAAQD,MAAM;UACZ,KAAK,KAAK;YACR,OAAOL,UAAU,CAAC2B,GAAG,CAAID,aAAa,CAAC;UACzC,KAAK,MAAM;YACT,OAAO1B,UAAU,CAAC4B,IAAI,CAAItB,QAAQ,EAAEC,IAAI,CAAC;UAC3C,KAAK,KAAK;YACR,OAAOP,UAAU,CAAC6B,GAAG,CAAIvB,QAAQ,EAAEC,IAAI,CAAC;UAC1C,KAAK,QAAQ;YACX,OAAOP,UAAU,CAAC8B,MAAM,CAAIxB,QAAQ,CAAC;UACvC;YACE,MAAM,IAAImB,KAAK,CAAC,uBAAuBpB,MAAM,EAAE,CAAC;QACpD;MACF;IACF,CAAC,MAAM;MACL;MACA,MAAMqB,aAAa,GAAGlB,MAAM,GAAG,GAAGF,QAAQ,IAAI,IAAIiB,eAAe,CAACf,MAAM,CAAC,EAAE,GAAGF,QAAQ;MAEtF,QAAQD,MAAM;QACZ,KAAK,KAAK;UACR,OAAOL,UAAU,CAAC2B,GAAG,CAAID,aAAa,CAAC;QACzC,KAAK,MAAM;UACT,OAAO1B,UAAU,CAAC4B,IAAI,CAAItB,QAAQ,EAAEC,IAAI,CAAC;QAC3C,KAAK,KAAK;UACR,OAAOP,UAAU,CAAC6B,GAAG,CAAIvB,QAAQ,EAAEC,IAAI,CAAC;QAC1C,KAAK,QAAQ;UACX,OAAOP,UAAU,CAAC8B,MAAM,CAAIxB,QAAQ,CAAC;QACvC;UACE,MAAM,IAAImB,KAAK,CAAC,uBAAuBpB,MAAM,EAAE,CAAC;MACpD;IACF;EACF;;EAEA;EACA,MAAM0B,SAASA,CAACC,OAAsB,EAAiD;IACrF,MAAMxB,MAAM,GAAGwB,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,GAAGE,SAAS;IACnE,OAAO,IAAI,CAAC9B,WAAW,CAA0B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACC,IAAI,EAAEF,SAAS,EAAE1B,MAAM,CAAC;EACzG;;EAEA;EACA,MAAM6B,YAAYA,CAACC,EAAU,EAAkD;IAC7E,OAAO,IAAI,CAAClC,WAAW,CAA2B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACI,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvG;;EAEA;EACA,MAAMC,WAAWA,CAAClC,IAAqB,EAAkD;IACvF,OAAO,IAAI,CAACH,WAAW,CAA2B,MAAM,EAAEF,aAAa,CAACiC,QAAQ,CAACC,IAAI,EAAE7B,IAAI,CAAC;EAC9F;;EAEA;EACA,MAAMmC,WAAWA,CAACJ,EAAU,EAAE/B,IAAqB,EAAkD;IACnG,OAAO,IAAI,CAACH,WAAW,CAA2B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACI,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEjC,IAAI,CAAC;EAC7G;;EAEA;EACA,MAAMoC,WAAWA,CAACL,EAAU,EAA8B;IACxD,OAAO,IAAI,CAAClC,WAAW,CAAO,QAAQ,EAAEF,aAAa,CAACiC,QAAQ,CAACI,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtF;;EAEA;EACA,MAAMI,eAAeA,CAACC,IAAY,EAAEC,KAAc,EAA8C;IAC9F,MAAMtC,MAA8B,GAAG;MAAEqC,IAAI,EAAEA,IAAI,CAACL,QAAQ,CAAC;IAAE,CAAC;IAChE,IAAIM,KAAK,EAAE;MACTtC,MAAM,CAACsC,KAAK,GAAGA,KAAK,CAACN,QAAQ,CAAC,CAAC;IACjC;IACA,OAAO,IAAI,CAACpC,WAAW,CAAuB,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACY,IAAI,EAAEb,SAAS,EAAE1B,MAAM,CAAC;EACtG;;EAEA;EACA,MAAMwC,qBAAqBA,CAAA,EAAiF;IAC1G,OAAO,IAAI,CAAC5C,WAAW,CAAyD,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACc,aAAa,CAAC;EAC9H;;EAEA;EACA,MAAMC,iBAAiBA,CAACC,KAAc,EAAqD;IACzF,MAAM3C,MAAM,GAAG2C,KAAK,GAAG;MAAEA,KAAK,EAAEA,KAAK,CAACX,QAAQ,CAAC;IAAE,CAAC,GAAGN,SAAS;IAC9D,OAAO,IAAI,CAAC9B,WAAW,CAA8B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACiB,QAAQ,EAAElB,SAAS,EAAE1B,MAAM,CAAC;EACjH;;EAEA;EACA,MAAM6C,eAAeA,CAACC,IAAY,EAAqD;IACrF,OAAO,IAAI,CAAClD,WAAW,CAA8B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAACoB,OAAO,CAACD,IAAI,CAAC,CAAC;EACnG;;EAEA;EACA,MAAME,oBAAoBA,CAACC,SAAiB,EAAEC,OAAe,EAAqD;IAChH,MAAMlD,MAAM,GAAG;MAAEmD,UAAU,EAAEF,SAAS;MAAEG,QAAQ,EAAEF;IAAQ,CAAC;IAC3D,OAAO,IAAI,CAACtD,WAAW,CAA8B,KAAK,EAAEF,aAAa,CAACiC,QAAQ,CAAC0B,UAAU,EAAE3B,SAAS,EAAE1B,MAAM,CAAC;EACnH;;EAIA;EACQyB,gBAAgBA,CAACD,OAAqB,EAA0B;IACtE,MAAMxB,MAA8B,GAAG,CAAC,CAAC;IAEzCsD,MAAM,CAACC,OAAO,CAAC/B,OAAO,CAAC,CAACgC,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKhC,SAAS,IAAIgC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzD1D,MAAM,CAACyD,GAAG,CAAC,GAAGC,KAAK,CAAC1B,QAAQ,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IAEF,OAAOhC,MAAM;EACf;;EAEA;EACA,MAAM2D,cAAcA,CAACtB,IAAY,EAAEC,KAAa,EAA8C;IAC5F,OAAO,IAAI,CAACF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;EAC1C;;EAEA;EACA,MAAMsB,aAAaA,CAACvB,IAAY,EAA8C;IAC5E,OAAO,IAAI,CAACD,eAAe,CAACC,IAAI,CAAC;EACnC;;EAEA;EACA,MAAMwB,cAAcA,CAAA,EAAsD;IACxE;IACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAM1B,IAAI,GAAGyB,KAAK,CAACE,WAAW,CAAC,CAAC;IAChC,MAAM1B,KAAK,GAAG2B,MAAM,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3D,MAAMC,GAAG,GAAGH,MAAM,CAACH,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMG,QAAQ,GAAG,GAAGjC,IAAI,IAAIC,KAAK,IAAI8B,GAAG,EAAE;IAC1C,OAAO,IAAI,CAACvB,eAAe,CAACyB,QAAQ,CAAC;EACvC;;EAEA;EACA,MAAMC,aAAaA,CAACtB,SAAkB,EAAqD;IACzF,MAAMuB,KAAK,GAAGvB,SAAS,IAAI,IAAI,CAACwB,YAAY,CAAC,CAAC;IAC9C,MAAMC,GAAG,GAAG,IAAI,CAACC,UAAU,CAACH,KAAK,CAAC;IAClC,OAAO,IAAI,CAACxB,oBAAoB,CAACwB,KAAK,EAAEE,GAAG,CAAC;EAC9C;;EAEA;EACA,MAAME,cAAcA,CAACpD,OAA+B,EAAiD;IACnG,MAAMqD,cAA4B,GAAG;MACnCC,IAAI,EAAE,CAAC;MACPnC,KAAK,EAAE,EAAE;MACToC,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,KAAK;MACjB,GAAGxD;IACL,CAAC;IACD,OAAO,IAAI,CAACD,SAAS,CAACsD,cAAc,CAAC;EACvC;;EAEA;EACA,MAAMI,eAAeA,CAACzD,OAA+B,EAAiD;IACpG,OAAO,IAAI,CAACD,SAAS,CAAC;MACpB2D,SAAS,EAAE,IAAI;MACf,GAAG1D;IACL,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM2D,YAAYA,CAACC,KAAa,EAAE5D,OAA+B,EAAiD;IAChH,OAAO,IAAI,CAACD,SAAS,CAAC;MACpB8D,MAAM,EAAED,KAAK;MACbF,SAAS,EAAE,IAAI;MACf,GAAG1D;IACL,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM8D,kBAAkBA,CAAA,EAAkD;IACxE,OAAO,IAAI,CAAC/D,SAAS,CAAC;MACpBgE,YAAY,EAAE,IAAI;MAClBL,SAAS,EAAE,IAAI;MACfH,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMQ,mBAAmBA,CAACC,UAAkB,EAAiD;IAC3F,OAAO,IAAI,CAAClE,SAAS,CAAC;MACpBmE,WAAW,EAAED,UAAU;MACvBP,SAAS,EAAE,IAAI;MACfH,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAIA;EACQP,YAAYA,CAAC3B,IAAa,EAAU;IAC1C,MAAM6C,CAAC,GAAG7C,IAAI,GAAG,IAAIiB,IAAI,CAACjB,IAAI,CAAC,GAAG,IAAIiB,IAAI,CAAC,CAAC;IAC5C,MAAMK,GAAG,GAAGuB,CAAC,CAACC,MAAM,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAGF,CAAC,CAACtB,OAAO,CAAC,CAAC,GAAGD,GAAG;IAC9B,MAAM0B,SAAS,GAAG,IAAI/B,IAAI,CAAC4B,CAAC,CAACI,OAAO,CAACF,IAAI,CAAC,CAAC;IAC3C,OAAOC,SAAS,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEQtB,UAAUA,CAAC1B,SAAiB,EAAU;IAC5C,MAAMuB,KAAK,GAAG,IAAIT,IAAI,CAACd,SAAS,CAAC;IACjC,MAAMyB,GAAG,GAAG,IAAIX,IAAI,CAACS,KAAK,CAAC;IAC3BE,GAAG,CAACqB,OAAO,CAACvB,KAAK,CAACH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChC,OAAOK,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEA;EACAC,kBAAkBA,CAAC7D,IAAY,EAAEC,KAAa,EAAkC;IAC9E,MAAMkC,KAAK,GAAG,IAAIT,IAAI,CAAC1B,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1C,MAAMoC,GAAG,GAAG,IAAIX,IAAI,CAAC1B,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;IAEpC,OAAO;MACLkC,KAAK,EAAEA,KAAK,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxCvB,GAAG,EAAEA,GAAG,CAACsB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;EACH;;EAEA;EACAE,UAAUA,CAACrD,IAAY,EAAU;IAC/B,OAAO,IAAIiB,IAAI,CAACjB,IAAI,CAAC,CAACsD,kBAAkB,CAAC,CAAC;EAC5C;;EAEA;EACAC,OAAOA,CAACvD,IAAY,EAAW;IAC7B,MAAMgB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,OAAOnD,IAAI,KAAKgB,KAAK;EACvB;;EAEA;EACAwC,MAAMA,CAACxD,IAAY,EAAW;IAC5B,MAAMgB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,OAAOnD,IAAI,GAAGgB,KAAK;EACrB;;EAEA;EACAyC,QAAQA,CAACzD,IAAY,EAAW;IAC9B,MAAMgB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,OAAOnD,IAAI,GAAGgB,KAAK;EACrB;;EAEA;EACA,MAAM0C,8BAA8BA,CAAA,EAAgD;IAClF,OAAO,IAAI,CAAC5G,WAAW,CAAwB,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,gCAAgC,CAAC;EACvH;;EAEA;EACA,MAAM6E,mBAAmBA,CAACC,OAAe,EAAgD;IACvF,OAAO,IAAI,CAAC9G,WAAW,CAAyB,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,cAAc,CAAC;EACjH;EAEA,MAAMC,sBAAsBA,CAACD,OAAe,EAAEE,QAAkB,EAAgD;IAC9G,OAAO,IAAI,CAAChH,WAAW,CAAyB,MAAM,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,cAAc,EAAEE,QAAQ,CAAC;EAC5H;EAEA,MAAMC,qBAAqBA,CAACC,YAAoB,EAA8B;IAC5E,OAAO,IAAI,CAAClH,WAAW,CAAO,QAAQ,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,gBAAgBkF,YAAY,EAAE,CAAC;EACvG;EAEA,MAAMC,oBAAoBA,CAACL,OAAe,EAAEI,YAAoB,EAA8B;IAC5F,OAAO,IAAI,CAAClH,WAAW,CAAO,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,gBAAgBI,YAAY,UAAU,CAAC;EACvH;;EAEA;EACA,MAAME,YAAYA,CAACN,OAAe,EAAkD;IAClF,OAAO,IAAI,CAAC9G,WAAW,CAA2B,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,UAAU,CAAC;EAC/G;EAEA,MAAMO,cAAcA,CAACP,OAAe,EAAkD;IACpF,OAAO,IAAI,CAAC9G,WAAW,CAA2B,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,YAAY,CAAC;EACjH;EAEA,MAAMQ,eAAeA,CAACR,OAAe,EAA8B;IACjE,OAAO,IAAI,CAAC9G,WAAW,CAAO,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,cAAc,CAAC;EAC/F;EAEA,MAAMS,YAAYA,CAACT,OAAe,EAA8B;IAC9D,OAAO,IAAI,CAAC9G,WAAW,CAAO,KAAK,EAAE,GAAGF,aAAa,CAACiC,QAAQ,CAACC,IAAI,IAAI8E,OAAO,UAAU,CAAC;EAC3F;AACF;AAEA,OAAO,MAAMU,eAAe,GAAG,IAAIzH,eAAe,CAAC,CAAC;AACpD,eAAeyH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}