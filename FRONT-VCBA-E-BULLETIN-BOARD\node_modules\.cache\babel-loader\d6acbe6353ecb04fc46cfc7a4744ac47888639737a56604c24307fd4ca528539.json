{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 20 3-3h2a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h2l3 3z\",\n  key: \"rbahqx\"\n}], [\"path\", {\n  d: \"M6 8v1\",\n  key: \"1636ez\"\n}], [\"path\", {\n  d: \"M10 8v1\",\n  key: \"1talb4\"\n}], [\"path\", {\n  d: \"M14 8v1\",\n  key: \"1rsfgr\"\n}], [\"path\", {\n  d: \"M18 8v1\",\n  key: \"gnkwox\"\n}]];\nconst EthernetPort = createLucideIcon(\"ethernet-port\", __iconNode);\nexport { __iconNode, EthernetPort as default };\n//# sourceMappingURL=ethernet-port.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}