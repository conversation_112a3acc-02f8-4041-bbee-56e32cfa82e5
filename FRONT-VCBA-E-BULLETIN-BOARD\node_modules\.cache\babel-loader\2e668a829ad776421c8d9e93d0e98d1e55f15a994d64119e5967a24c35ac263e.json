{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.268 21a2 2 0 0 0 3.464 0\",\n  key: \"vwvbt9\"\n}], [\"path\", {\n  d: \"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742\",\n  key: \"178tsu\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05\",\n  key: \"1hqiys\"\n}]];\nconst BellOff = createLucideIcon(\"bell-off\", __iconNode);\nexport { __iconNode, BellOff as default };\n//# sourceMappingURL=bell-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}