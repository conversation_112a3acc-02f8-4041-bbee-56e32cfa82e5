{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 3a41 41 0 0 0 0 18\",\n  key: \"1qcnzb\"\n}], [\"path\", {\n  d: \"M14 3a41 41 0 0 1 0 18\",\n  key: \"547vd4\"\n}], [\"path\", {\n  d: \"M17 3a2 2 0 0 1 1.68.92 15.25 15.25 0 0 1 0 16.16A2 2 0 0 1 17 21H7a2 2 0 0 1-1.68-.92 15.25 15.25 0 0 1 0-16.16A2 2 0 0 1 7 3z\",\n  key: \"1wepyy\"\n}], [\"path\", {\n  d: \"M3.84 17h16.32\",\n  key: \"1wh981\"\n}], [\"path\", {\n  d: \"M3.84 7h16.32\",\n  key: \"19jf4x\"\n}]];\nconst Barrel = createLucideIcon(\"barrel\", __iconNode);\nexport { __iconNode, Barrel as default };\n//# sourceMappingURL=barrel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}