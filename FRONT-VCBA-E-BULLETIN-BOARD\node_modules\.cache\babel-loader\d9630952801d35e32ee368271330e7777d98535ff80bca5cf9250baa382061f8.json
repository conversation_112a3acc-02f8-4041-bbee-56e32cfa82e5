{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v3a1 1 0 0 0 1 1h5\",\n  key: \"1xspal\"\n}], [\"path\", {\n  d: \"M18 18v-6a1 1 0 0 0-1-1h-6a1 1 0 0 0-1 1v6\",\n  key: \"1ra60u\"\n}], [\"path\", {\n  d: \"M18 22H4a2 2 0 0 1-2-2V6\",\n  key: \"pblm9e\"\n}], [\"path\", {\n  d: \"M8 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9.172a2 2 0 0 1 1.414.586l2.828 2.828A2 2 0 0 1 22 6.828V16a2 2 0 0 1-2.01 2z\",\n  key: \"1yve0x\"\n}]];\nconst SaveAll = createLucideIcon(\"save-all\", __iconNode);\nexport { __iconNode, SaveAll as default };\n//# sourceMappingURL=save-all.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}