{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h-1a2 2 0 0 1-2-2 2 2 0 0 1-2 2H6\",\n  key: \"1528k5\"\n}], [\"path\", {\n  d: \"M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7\",\n  key: \"13ksps\"\n}], [\"path\", {\n  d: \"M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1\",\n  key: \"1n9rhb\"\n}], [\"path\", {\n  d: \"M6 4h1a2 2 0 0 1 2 2 2 2 0 0 1 2-2h1\",\n  key: \"1mj8rg\"\n}], [\"path\", {\n  d: \"M9 6v12\",\n  key: \"velyjx\"\n}]];\nconst TextCursorInput = createLucideIcon(\"text-cursor-input\", __iconNode);\nexport { __iconNode, TextCursorInput as default };\n//# sourceMappingURL=text-cursor-input.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}