{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"5\",\n  width: \"4\",\n  height: \"12\",\n  rx: \"1\",\n  key: \"q8uenq\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"8\",\n  width: \"4\",\n  height: \"9\",\n  rx: \"1\",\n  key: \"sr5ea\"\n}]];\nconst ChartColumnBig = createLucideIcon(\"chart-column-big\", __iconNode);\nexport { __iconNode, ChartColumnBig as default };\n//# sourceMappingURL=chart-column-big.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}