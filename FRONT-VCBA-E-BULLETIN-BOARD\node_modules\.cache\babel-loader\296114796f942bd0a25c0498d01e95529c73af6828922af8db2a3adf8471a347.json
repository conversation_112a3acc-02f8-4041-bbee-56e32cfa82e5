{"ast": null, "code": "import { httpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\n\n// Holiday-specific types\n\nclass HolidayService {\n  constructor() {\n    this.baseUrl = '/api/holidays';\n  }\n  /**\n   * Get all holidays with optional filters\n   */\n  async getHolidays(filters) {\n    const params = new URLSearchParams();\n    if (filters !== null && filters !== void 0 && filters.country_code) params.append('country_code', filters.country_code);\n    if (filters !== null && filters !== void 0 && filters.holiday_type) params.append('holiday_type', filters.holiday_type);\n    if (filters !== null && filters !== void 0 && filters.year) params.append('year', filters.year.toString());\n    if ((filters === null || filters === void 0 ? void 0 : filters.is_auto_generated) !== undefined) params.append('is_auto_generated', filters.is_auto_generated.toString());\n    if ((filters === null || filters === void 0 ? void 0 : filters.is_active) !== undefined) params.append('is_active', filters.is_active.toString());\n    const queryString = params.toString();\n    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;\n    return httpClient.get(url);\n  }\n\n  /**\n   * Get available countries from API\n   */\n  async getAvailableCountries() {\n    return httpClient.get(`${this.baseUrl}/countries`);\n  }\n\n  /**\n   * Check if a specific date is a holiday\n   */\n  async checkHoliday(countryCode, date) {\n    return httpClient.get(`${this.baseUrl}/check/${countryCode}/${date}`);\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year) {\n    const params = year ? `?year=${year}` : '';\n    return httpClient.get(`${this.baseUrl}/stats${params}`);\n  }\n\n  /**\n   * Sync holidays from external API (Admin only)\n   */\n  async syncHolidays(request) {\n    return AdminAuthService.post(`${this.baseUrl}/sync`, request);\n  }\n\n  /**\n   * Sync holidays for multiple countries (Admin only)\n   */\n  async syncMultipleCountries(request) {\n    return AdminAuthService.post(`${this.baseUrl}/sync-multiple`, request);\n  }\n\n  /**\n   * Toggle holiday visibility (Admin only)\n   */\n  async toggleHoliday(holidayId) {\n    return AdminAuthService.request('PATCH', `${this.baseUrl}/${holidayId}/toggle`, {});\n  }\n\n  /**\n   * Update holiday settings (Admin only)\n   */\n  async updateHoliday(holidayId, request) {\n    return AdminAuthService.request('PATCH', `${this.baseUrl}/${holidayId}`, request);\n  }\n\n  /**\n   * Check if an event is a holiday\n   */\n  isHoliday(event) {\n    return event.is_holiday === true || event.is_holiday === 1;\n  }\n\n  /**\n   * Check if a holiday is auto-generated\n   */\n  isAutoGenerated(event) {\n    return event.is_auto_generated === true || event.is_auto_generated === 1;\n  }\n\n  /**\n   * Get holiday display name (prefer local name if available)\n   */\n  getHolidayDisplayName(event) {\n    if (this.isHoliday(event)) {\n      return event.local_name || event.title;\n    }\n    return event.title;\n  }\n\n  /**\n   * Get holiday type display text\n   */\n  getHolidayTypeDisplay(holidayType) {\n    switch (holidayType) {\n      case 'local':\n        return 'Local Holiday';\n      case 'international':\n        return 'International Holiday';\n      case 'school':\n        return 'School Holiday';\n      default:\n        return 'Holiday';\n    }\n  }\n\n  /**\n   * Get country flag emoji\n   */\n  getCountryFlag(countryCode) {\n    const flags = {\n      'PH': '🇵🇭',\n      'US': '🇺🇸',\n      'CA': '🇨🇦',\n      'GB': '🇬🇧',\n      'AU': '🇦🇺',\n      'JP': '🇯🇵',\n      'KR': '🇰🇷',\n      'CN': '🇨🇳',\n      'IN': '🇮🇳',\n      'SG': '🇸🇬',\n      'MY': '🇲🇾',\n      'TH': '🇹🇭',\n      'VN': '🇻🇳',\n      'ID': '🇮🇩',\n      'FR': '🇫🇷',\n      'DE': '🇩🇪',\n      'IT': '🇮🇹',\n      'ES': '🇪🇸',\n      'NL': '🇳🇱',\n      'BR': '🇧🇷',\n      'MX': '🇲🇽',\n      'AR': '🇦🇷'\n    };\n    return flags[countryCode || ''] || '🌍';\n  }\n\n  /**\n   * Get holiday CSS classes for styling\n   */\n  getHolidayClasses(event) {\n    const classes = [];\n    if (this.isHoliday(event)) {\n      classes.push('holiday-event');\n      if (event.holiday_type) {\n        classes.push(`holiday-${event.holiday_type}`);\n      }\n      if (this.isAutoGenerated(event)) {\n        classes.push('holiday-auto-generated');\n      }\n      if (event.country_code) {\n        classes.push(`holiday-${event.country_code.toLowerCase()}`);\n      }\n    }\n    return classes;\n  }\n}\nexport const holidayService = new HolidayService();", "map": {"version": 3, "names": ["httpClient", "AdminAuthService", "HolidayService", "constructor", "baseUrl", "getHolidays", "filters", "params", "URLSearchParams", "country_code", "append", "holiday_type", "year", "toString", "is_auto_generated", "undefined", "is_active", "queryString", "url", "get", "getAvailableCountries", "checkHoliday", "countryCode", "date", "getHolidayStats", "syncHolidays", "request", "post", "syncMultipleCountries", "toggleHoliday", "holidayId", "updateHoliday", "isHoliday", "event", "is_holiday", "isAutoGenerated", "getHolidayDisplayName", "local_name", "title", "getHolidayTypeDisplay", "holidayType", "getCountryFlag", "flags", "getHolidayClasses", "classes", "push", "toLowerCase", "holidayService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/holidayService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { ApiResponse } from '../types';\n\n// Holiday-specific types\nexport interface Holiday {\n  calendar_id: number;\n  title: string;\n  description?: string;\n  event_date: string;\n  end_date?: string;\n  category_id: number;\n  subcategory_id?: number;\n  category_name?: string;\n  category_color?: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  is_holiday: boolean;\n  holiday_type: 'local' | 'international' | 'school';\n  country_code?: string;\n  is_auto_generated: boolean;\n  api_source?: string;\n  local_name?: string;\n  holiday_types?: string[];\n  is_global?: boolean;\n  is_fixed?: boolean;\n  is_active: boolean;\n  allow_comments: boolean;\n  is_alert: boolean;\n  created_by: number;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Country {\n  countryCode: string;\n  name: string;\n}\n\nexport interface HolidayFilters {\n  country_code?: string;\n  holiday_type?: 'local' | 'international' | 'school';\n  year?: number;\n  is_auto_generated?: boolean;\n  is_active?: boolean;\n}\n\nexport interface SyncHolidaysRequest {\n  country_code: string;\n  year: number;\n  holiday_type: 'local' | 'international' | 'school';\n}\n\nexport interface SyncMultipleRequest {\n  countries: string[];\n  year: number;\n}\n\nexport interface SyncResult {\n  success: boolean;\n  countryCode: string;\n  year: number;\n  holidayType: string;\n  stats: {\n    total: number;\n    created: number;\n    updated: number;\n    skipped: number;\n    errors: number;\n  };\n  errors: string[];\n}\n\nexport interface HolidayStats {\n  year: number;\n  total: number;\n  by_type: {\n    local: number;\n    international: number;\n    school: number;\n  };\n  by_source: {\n    auto_generated: number;\n    manual: number;\n  };\n  by_status: {\n    active: number;\n    inactive: number;\n  };\n  by_country: Record<string, number>;\n}\n\nexport interface HolidayCheckResult {\n  date: string;\n  country_code: string;\n  is_holiday_api: boolean;\n  is_holiday_db: boolean;\n  db_holidays: Holiday[];\n}\n\nexport interface UpdateHolidayRequest {\n  description?: string;\n  is_active?: boolean;\n  allow_comments?: boolean;\n  is_alert?: boolean;\n}\n\nclass HolidayService {\n  private baseUrl = '/api/holidays';\n\n  /**\n   * Get all holidays with optional filters\n   */\n  async getHolidays(filters?: HolidayFilters): Promise<ApiResponse<{ holidays: Holiday[]; count: number }>> {\n    const params = new URLSearchParams();\n    \n    if (filters?.country_code) params.append('country_code', filters.country_code);\n    if (filters?.holiday_type) params.append('holiday_type', filters.holiday_type);\n    if (filters?.year) params.append('year', filters.year.toString());\n    if (filters?.is_auto_generated !== undefined) params.append('is_auto_generated', filters.is_auto_generated.toString());\n    if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());\n\n    const queryString = params.toString();\n    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;\n\n    return httpClient.get<{ holidays: Holiday[]; count: number }>(url);\n  }\n\n  /**\n   * Get available countries from API\n   */\n  async getAvailableCountries(): Promise<ApiResponse<{ countries: Country[]; count: number }>> {\n    return httpClient.get<{ countries: Country[]; count: number }>(`${this.baseUrl}/countries`);\n  }\n\n  /**\n   * Check if a specific date is a holiday\n   */\n  async checkHoliday(countryCode: string, date: string): Promise<ApiResponse<HolidayCheckResult>> {\n    return httpClient.get<HolidayCheckResult>(`${this.baseUrl}/check/${countryCode}/${date}`);\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year?: number): Promise<ApiResponse<HolidayStats>> {\n    const params = year ? `?year=${year}` : '';\n    return httpClient.get<HolidayStats>(`${this.baseUrl}/stats${params}`);\n  }\n\n  /**\n   * Sync holidays from external API (Admin only)\n   */\n  async syncHolidays(request: SyncHolidaysRequest): Promise<ApiResponse<SyncResult>> {\n    return AdminAuthService.post<SyncResult>(`${this.baseUrl}/sync`, request);\n  }\n\n  /**\n   * Sync holidays for multiple countries (Admin only)\n   */\n  async syncMultipleCountries(request: SyncMultipleRequest): Promise<ApiResponse<{ results: SyncResult[]; errors: any[]; summary: any }>> {\n    return AdminAuthService.post<{ results: SyncResult[]; errors: any[]; summary: any }>(`${this.baseUrl}/sync-multiple`, request);\n  }\n\n  /**\n   * Toggle holiday visibility (Admin only)\n   */\n  async toggleHoliday(holidayId: number): Promise<ApiResponse<{ holiday: Holiday }>> {\n    return AdminAuthService.request<{ holiday: Holiday }>('PATCH', `${this.baseUrl}/${holidayId}/toggle`, {});\n  }\n\n  /**\n   * Update holiday settings (Admin only)\n   */\n  async updateHoliday(holidayId: number, request: UpdateHolidayRequest): Promise<ApiResponse<{ holiday: Holiday }>> {\n    return AdminAuthService.request<{ holiday: Holiday }>('PATCH', `${this.baseUrl}/${holidayId}`, request);\n  }\n\n  /**\n   * Check if an event is a holiday\n   */\n  isHoliday(event: any): boolean {\n    return event.is_holiday === true || event.is_holiday === 1;\n  }\n\n  /**\n   * Check if a holiday is auto-generated\n   */\n  isAutoGenerated(event: any): boolean {\n    return event.is_auto_generated === true || event.is_auto_generated === 1;\n  }\n\n  /**\n   * Get holiday display name (prefer local name if available)\n   */\n  getHolidayDisplayName(event: any): string {\n    if (this.isHoliday(event)) {\n      return event.local_name || event.title;\n    }\n    return event.title;\n  }\n\n  /**\n   * Get holiday type display text\n   */\n  getHolidayTypeDisplay(holidayType?: string): string {\n    switch (holidayType) {\n      case 'local':\n        return 'Local Holiday';\n      case 'international':\n        return 'International Holiday';\n      case 'school':\n        return 'School Holiday';\n      default:\n        return 'Holiday';\n    }\n  }\n\n  /**\n   * Get country flag emoji\n   */\n  getCountryFlag(countryCode?: string): string {\n    const flags: Record<string, string> = {\n      'PH': '🇵🇭',\n      'US': '🇺🇸',\n      'CA': '🇨🇦',\n      'GB': '🇬🇧',\n      'AU': '🇦🇺',\n      'JP': '🇯🇵',\n      'KR': '🇰🇷',\n      'CN': '🇨🇳',\n      'IN': '🇮🇳',\n      'SG': '🇸🇬',\n      'MY': '🇲🇾',\n      'TH': '🇹🇭',\n      'VN': '🇻🇳',\n      'ID': '🇮🇩',\n      'FR': '🇫🇷',\n      'DE': '🇩🇪',\n      'IT': '🇮🇹',\n      'ES': '🇪🇸',\n      'NL': '🇳🇱',\n      'BR': '🇧🇷',\n      'MX': '🇲🇽',\n      'AR': '🇦🇷'\n    };\n    return flags[countryCode || ''] || '🌍';\n  }\n\n  /**\n   * Get holiday CSS classes for styling\n   */\n  getHolidayClasses(event: any): string[] {\n    const classes: string[] = [];\n    \n    if (this.isHoliday(event)) {\n      classes.push('holiday-event');\n      \n      if (event.holiday_type) {\n        classes.push(`holiday-${event.holiday_type}`);\n      }\n      \n      if (this.isAutoGenerated(event)) {\n        classes.push('holiday-auto-generated');\n      }\n      \n      if (event.country_code) {\n        classes.push(`holiday-${event.country_code.toLowerCase()}`);\n      }\n    }\n    \n    return classes;\n  }\n}\n\nexport const holidayService = new HolidayService();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,gBAAgB,QAAQ,sBAAsB;;AAGvD;;AAuGA,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,eAAe;EAAA;EAEjC;AACF;AACA;EACE,MAAMC,WAAWA,CAACC,OAAwB,EAAgE;IACxG,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,YAAY,EAAEF,MAAM,CAACG,MAAM,CAAC,cAAc,EAAEJ,OAAO,CAACG,YAAY,CAAC;IAC9E,IAAIH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,YAAY,EAAEJ,MAAM,CAACG,MAAM,CAAC,cAAc,EAAEJ,OAAO,CAACK,YAAY,CAAC;IAC9E,IAAIL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEM,IAAI,EAAEL,MAAM,CAACG,MAAM,CAAC,MAAM,EAAEJ,OAAO,CAACM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;IACjE,IAAI,CAAAP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,iBAAiB,MAAKC,SAAS,EAAER,MAAM,CAACG,MAAM,CAAC,mBAAmB,EAAEJ,OAAO,CAACQ,iBAAiB,CAACD,QAAQ,CAAC,CAAC,CAAC;IACtH,IAAI,CAAAP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,SAAS,MAAKD,SAAS,EAAER,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEJ,OAAO,CAACU,SAAS,CAACH,QAAQ,CAAC,CAAC,CAAC;IAE9F,MAAMI,WAAW,GAAGV,MAAM,CAACM,QAAQ,CAAC,CAAC;IACrC,MAAMK,GAAG,GAAGD,WAAW,GAAG,GAAG,IAAI,CAACb,OAAO,IAAIa,WAAW,EAAE,GAAG,IAAI,CAACb,OAAO;IAEzE,OAAOJ,UAAU,CAACmB,GAAG,CAAyCD,GAAG,CAAC;EACpE;;EAEA;AACF;AACA;EACE,MAAME,qBAAqBA,CAAA,EAAkE;IAC3F,OAAOpB,UAAU,CAACmB,GAAG,CAA0C,GAAG,IAAI,CAACf,OAAO,YAAY,CAAC;EAC7F;;EAEA;AACF;AACA;EACE,MAAMiB,YAAYA,CAACC,WAAmB,EAAEC,IAAY,EAA4C;IAC9F,OAAOvB,UAAU,CAACmB,GAAG,CAAqB,GAAG,IAAI,CAACf,OAAO,UAAUkB,WAAW,IAAIC,IAAI,EAAE,CAAC;EAC3F;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAACZ,IAAa,EAAsC;IACvE,MAAML,MAAM,GAAGK,IAAI,GAAG,SAASA,IAAI,EAAE,GAAG,EAAE;IAC1C,OAAOZ,UAAU,CAACmB,GAAG,CAAe,GAAG,IAAI,CAACf,OAAO,SAASG,MAAM,EAAE,CAAC;EACvE;;EAEA;AACF;AACA;EACE,MAAMkB,YAAYA,CAACC,OAA4B,EAAoC;IACjF,OAAOzB,gBAAgB,CAAC0B,IAAI,CAAa,GAAG,IAAI,CAACvB,OAAO,OAAO,EAAEsB,OAAO,CAAC;EAC3E;;EAEA;AACF;AACA;EACE,MAAME,qBAAqBA,CAACF,OAA4B,EAAgF;IACtI,OAAOzB,gBAAgB,CAAC0B,IAAI,CAAyD,GAAG,IAAI,CAACvB,OAAO,gBAAgB,EAAEsB,OAAO,CAAC;EAChI;;EAEA;AACF;AACA;EACE,MAAMG,aAAaA,CAACC,SAAiB,EAA8C;IACjF,OAAO7B,gBAAgB,CAACyB,OAAO,CAAuB,OAAO,EAAE,GAAG,IAAI,CAACtB,OAAO,IAAI0B,SAAS,SAAS,EAAE,CAAC,CAAC,CAAC;EAC3G;;EAEA;AACF;AACA;EACE,MAAMC,aAAaA,CAACD,SAAiB,EAAEJ,OAA6B,EAA8C;IAChH,OAAOzB,gBAAgB,CAACyB,OAAO,CAAuB,OAAO,EAAE,GAAG,IAAI,CAACtB,OAAO,IAAI0B,SAAS,EAAE,EAAEJ,OAAO,CAAC;EACzG;;EAEA;AACF;AACA;EACEM,SAASA,CAACC,KAAU,EAAW;IAC7B,OAAOA,KAAK,CAACC,UAAU,KAAK,IAAI,IAAID,KAAK,CAACC,UAAU,KAAK,CAAC;EAC5D;;EAEA;AACF;AACA;EACEC,eAAeA,CAACF,KAAU,EAAW;IACnC,OAAOA,KAAK,CAACnB,iBAAiB,KAAK,IAAI,IAAImB,KAAK,CAACnB,iBAAiB,KAAK,CAAC;EAC1E;;EAEA;AACF;AACA;EACEsB,qBAAqBA,CAACH,KAAU,EAAU;IACxC,IAAI,IAAI,CAACD,SAAS,CAACC,KAAK,CAAC,EAAE;MACzB,OAAOA,KAAK,CAACI,UAAU,IAAIJ,KAAK,CAACK,KAAK;IACxC;IACA,OAAOL,KAAK,CAACK,KAAK;EACpB;;EAEA;AACF;AACA;EACEC,qBAAqBA,CAACC,WAAoB,EAAU;IAClD,QAAQA,WAAW;MACjB,KAAK,OAAO;QACV,OAAO,eAAe;MACxB,KAAK,eAAe;QAClB,OAAO,uBAAuB;MAChC,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,SAAS;IACpB;EACF;;EAEA;AACF;AACA;EACEC,cAAcA,CAACnB,WAAoB,EAAU;IAC3C,MAAMoB,KAA6B,GAAG;MACpC,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE;IACR,CAAC;IACD,OAAOA,KAAK,CAACpB,WAAW,IAAI,EAAE,CAAC,IAAI,IAAI;EACzC;;EAEA;AACF;AACA;EACEqB,iBAAiBA,CAACV,KAAU,EAAY;IACtC,MAAMW,OAAiB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAACZ,SAAS,CAACC,KAAK,CAAC,EAAE;MACzBW,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;MAE7B,IAAIZ,KAAK,CAACtB,YAAY,EAAE;QACtBiC,OAAO,CAACC,IAAI,CAAC,WAAWZ,KAAK,CAACtB,YAAY,EAAE,CAAC;MAC/C;MAEA,IAAI,IAAI,CAACwB,eAAe,CAACF,KAAK,CAAC,EAAE;QAC/BW,OAAO,CAACC,IAAI,CAAC,wBAAwB,CAAC;MACxC;MAEA,IAAIZ,KAAK,CAACxB,YAAY,EAAE;QACtBmC,OAAO,CAACC,IAAI,CAAC,WAAWZ,KAAK,CAACxB,YAAY,CAACqC,WAAW,CAAC,CAAC,EAAE,CAAC;MAC7D;IACF;IAEA,OAAOF,OAAO;EAChB;AACF;AAEA,OAAO,MAAMG,cAAc,GAAG,IAAI7C,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}