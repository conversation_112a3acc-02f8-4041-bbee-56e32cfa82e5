{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"m2.305 15.53.923-.382\",\n  key: \"yfp9st\"\n}], [\"path\", {\n  d: \"m3.228 12.852-.924-.383\",\n  key: \"bckynb\"\n}], [\"path\", {\n  d: \"M4.677 21.5a2 2 0 0 0 1.313.5H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2.5\",\n  key: \"1yo3oz\"\n}], [\"path\", {\n  d: \"m4.852 11.228-.383-.923\",\n  key: \"1j88i9\"\n}], [\"path\", {\n  d: \"m4.852 16.772-.383.924\",\n  key: \"sag1dv\"\n}], [\"path\", {\n  d: \"m7.148 11.228.383-.923\",\n  key: \"rj39hk\"\n}], [\"path\", {\n  d: \"m7.53 17.696-.382-.924\",\n  key: \"1uu5cs\"\n}], [\"path\", {\n  d: \"m8.772 12.852.923-.383\",\n  key: \"13811l\"\n}], [\"path\", {\n  d: \"m8.772 15.148.923.383\",\n  key: \"z1a5l0\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"14\",\n  r: \"3\",\n  key: \"a1xfv6\"\n}]];\nconst FileCog = createLucideIcon(\"file-cog\", __iconNode);\nexport { __iconNode, FileCog as default };\n//# sourceMappingURL=file-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}