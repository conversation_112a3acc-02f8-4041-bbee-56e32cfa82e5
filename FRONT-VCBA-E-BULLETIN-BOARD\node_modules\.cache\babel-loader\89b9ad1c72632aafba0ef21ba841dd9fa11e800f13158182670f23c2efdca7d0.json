{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6\",\n  key: \"14rsvq\"\n}], [\"path\", {\n  d: \"m21 21-9-9\",\n  key: \"1et2py\"\n}], [\"path\", {\n  d: \"M21 15v6h-6\",\n  key: \"1jko0i\"\n}]];\nconst SquareArrowOutDownRight = createLucideIcon(\"square-arrow-out-down-right\", __iconNode);\nexport { __iconNode, SquareArrowOutDownRight as default };\n//# sourceMappingURL=square-arrow-out-down-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}