{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l2 1\",\n  key: \"19cm8n\"\n}], [\"path\", {\n  d: \"M12.337 21.994a10 10 0 1 1 9.588-8.767\",\n  key: \"28moa\"\n}], [\"path\", {\n  d: \"m14 18 4 4 4-4\",\n  key: \"1waygx\"\n}], [\"path\", {\n  d: \"M18 14v8\",\n  key: \"irew45\"\n}]];\nconst ClockArrowDown = createLucideIcon(\"clock-arrow-down\", __iconNode);\nexport { __iconNode, ClockArrowDown as default };\n//# sourceMappingURL=clock-arrow-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}