{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7\",\n  key: \"ztvudi\"\n}], [\"path\", {\n  d: \"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\",\n  key: \"1b2hhj\"\n}], [\"path\", {\n  d: \"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4\",\n  key: \"2ebpfo\"\n}], [\"path\", {\n  d: \"M2 7h20\",\n  key: \"1fcdvo\"\n}], [\"path\", {\n  d: \"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7\",\n  key: \"6c3vgh\"\n}]];\nconst Store = createLucideIcon(\"store\", __iconNode);\nexport { __iconNode, Store as default };\n//# sourceMappingURL=store.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}