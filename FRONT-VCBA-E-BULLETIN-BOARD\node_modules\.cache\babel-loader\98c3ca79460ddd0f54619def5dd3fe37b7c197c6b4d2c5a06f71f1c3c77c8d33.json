{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\",\n  key: \"1jg4f8\"\n}]];\nconst Facebook = createLucideIcon(\"facebook\", __iconNode);\nexport { __iconNode, Facebook as default };\n//# sourceMappingURL=facebook.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}