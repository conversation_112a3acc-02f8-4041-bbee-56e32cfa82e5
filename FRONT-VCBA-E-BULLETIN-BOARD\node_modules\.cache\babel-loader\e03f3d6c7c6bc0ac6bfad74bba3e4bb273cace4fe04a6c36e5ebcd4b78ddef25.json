{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 4.5V4a2 2 0 0 0-2.41-1.957\",\n  key: \"jsi14n\"\n}], [\"path\", {\n  d: \"M13.9 8.4a2 2 0 0 0-1.26-1.295\",\n  key: \"hirc7f\"\n}], [\"path\", {\n  d: \"M21.7 16.2A8 8 0 0 0 22 14v-3a2 2 0 1 0-4 0v-1a2 2 0 0 0-3.63-1.158\",\n  key: \"1jxb2e\"\n}], [\"path\", {\n  d: \"m7 15-1.8-1.8a2 2 0 0 0-2.79 2.86L6 19.7a7.74 7.74 0 0 0 6 2.3h2a8 8 0 0 0 5.657-2.343\",\n  key: \"10r7hm\"\n}], [\"path\", {\n  d: \"M6 6v8\",\n  key: \"tv5xkp\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst PointerOff = createLucideIcon(\"pointer-off\", __iconNode);\nexport { __iconNode, PointerOff as default };\n//# sourceMappingURL=pointer-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}