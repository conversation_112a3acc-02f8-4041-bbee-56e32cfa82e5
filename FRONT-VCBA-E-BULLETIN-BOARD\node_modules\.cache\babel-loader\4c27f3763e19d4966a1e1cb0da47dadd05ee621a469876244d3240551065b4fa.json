{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 12 7 2\",\n  key: \"117k30\"\n}], [\"path\", {\n  d: \"m7 12 5-10\",\n  key: \"1tvx22\"\n}], [\"path\", {\n  d: \"m12 12 5-10\",\n  key: \"ev1o1a\"\n}], [\"path\", {\n  d: \"m17 12 5-10\",\n  key: \"1e4ti3\"\n}], [\"path\", {\n  d: \"M4.5 7h15\",\n  key: \"vlsxkz\"\n}], [\"path\", {\n  d: \"M12 16v6\",\n  key: \"c8a4gj\"\n}]];\nconst Antenna = createLucideIcon(\"antenna\", __iconNode);\nexport { __iconNode, Antenna as default };\n//# sourceMappingURL=antenna.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}