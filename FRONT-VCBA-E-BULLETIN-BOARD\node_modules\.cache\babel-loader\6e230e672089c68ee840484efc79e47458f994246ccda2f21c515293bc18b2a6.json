{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"path\", {\n  d: \"M11 12h10\",\n  key: \"1438ji\"\n}], [\"path\", {\n  d: \"M11 16h7\",\n  key: \"uosisv\"\n}], [\"path\", {\n  d: \"M11 20h4\",\n  key: \"1krc32\"\n}]];\nconst ArrowUpWideNarrow = createLucideIcon(\"arrow-up-wide-narrow\", __iconNode);\nexport { __iconNode, ArrowUpWideNarrow as default };\n//# sourceMappingURL=arrow-up-wide-narrow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}