{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10v4\",\n  key: \"1mb2ec\"\n}], [\"path\", {\n  d: \"M14 10v4\",\n  key: \"1nt88p\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M6 10v4\",\n  key: \"1n77qd\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"16\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"13zb55\"\n}]];\nconst BatteryFull = createLucideIcon(\"battery-full\", __iconNode);\nexport { __iconNode, BatteryFull as default };\n//# sourceMappingURL=battery-full.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}