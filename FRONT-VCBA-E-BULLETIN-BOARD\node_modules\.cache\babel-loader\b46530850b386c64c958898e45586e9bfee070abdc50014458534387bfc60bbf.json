{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 14 5-5-5-5\",\n  key: \"12vg1m\"\n}], [\"path\", {\n  d: \"M4 20v-7a4 4 0 0 1 4-4h12\",\n  key: \"1lu4f8\"\n}]];\nconst CornerUpRight = createLucideIcon(\"corner-up-right\", __iconNode);\nexport { __iconNode, CornerUpRight as default };\n//# sourceMappingURL=corner-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}