{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 12h2\",\n  key: \"quuxs7\"\n}], [\"path\", {\n  d: \"M18 16h2\",\n  key: \"zsn3lv\"\n}], [\"path\", {\n  d: \"M18 20h2\",\n  key: \"9x5y9y\"\n}], [\"path\", {\n  d: \"M18 4h2\",\n  key: \"1luxfb\"\n}], [\"path\", {\n  d: \"M18 8h2\",\n  key: \"nxqzg\"\n}], [\"path\", {\n  d: \"M4 12h2\",\n  key: \"1ltxp0\"\n}], [\"path\", {\n  d: \"M4 16h2\",\n  key: \"8a5zha\"\n}], [\"path\", {\n  d: \"M4 20h2\",\n  key: \"27dk57\"\n}], [\"path\", {\n  d: \"M4 4h2\",\n  key: \"10groj\"\n}], [\"path\", {\n  d: \"M4 8h2\",\n  key: \"18vq6w\"\n}], [\"path\", {\n  d: \"M8 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-1.5c-.276 0-.494.227-.562.495a2 2 0 0 1-3.876 0C9.994 2.227 9.776 2 9.5 2z\",\n  key: \"1681fp\"\n}]];\nconst Microchip = createLucideIcon(\"microchip\", __iconNode);\nexport { __iconNode, Microchip as default };\n//# sourceMappingURL=microchip.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}