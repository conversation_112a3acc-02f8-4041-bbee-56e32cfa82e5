{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.27 21.7s9.87-3.5 12.73-6.36a4.5 4.5 0 0 0-6.36-6.37C5.77 11.84 2.27 21.7 2.27 21.7zM8.64 14l-2.05-2.04M15.34 15l-2.46-2.46\",\n  key: \"rfqxbe\"\n}], [\"path\", {\n  d: \"M22 9s-1.33-2-3.5-2C16.86 7 15 9 15 9s1.33 2 3.5 2S22 9 22 9z\",\n  key: \"6b25w4\"\n}], [\"path\", {\n  d: \"M15 2s-2 1.33-2 3.5S15 9 15 9s2-1.84 2-3.5C17 3.33 15 2 15 2z\",\n  key: \"fn65lo\"\n}]];\nconst Carrot = createLucideIcon(\"carrot\", __iconNode);\nexport { __iconNode, Carrot as default };\n//# sourceMappingURL=carrot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}