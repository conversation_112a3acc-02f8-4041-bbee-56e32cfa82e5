{"ast": null, "code": "var _UnifiedAuthService;\nimport { AdminAuthService } from './admin-auth.service';\nimport { StudentAuthService } from './student-auth.service';\nimport { unifiedStorage } from '../utils/unifiedStorage';\n/**\n * Unified Authentication Service\n * Provides a single interface for both admin and student authentication\n * while maintaining the underlying separation and security\n */\nexport class UnifiedAuthService {\n  constructor() {}\n  static getInstance() {\n    if (!UnifiedAuthService.instance) {\n      UnifiedAuthService.instance = new UnifiedAuthService();\n    }\n    return UnifiedAuthService.instance;\n  }\n\n  /**\n   * Detect user type based on identifier\n   * Email format suggests admin, student number format suggests student\n   */\n  detectUserType(identifier) {\n    const isEmail = identifier.includes('@');\n    const isStudentNumber = /^[0-9-]+$/.test(identifier.trim());\n    if (isEmail && !isStudentNumber) {\n      return 'both'; // Email could be either admin or student\n    } else if (isStudentNumber && !isEmail) {\n      return 'student'; // Student number is definitely student\n    } else if (isEmail) {\n      return 'both'; // Email format, could be either\n    }\n    return 'both'; // Default to trying both\n  }\n\n  /**\n   * Attempt login with admin credentials\n   */\n  async tryAdminLogin(credentials) {\n    try {\n      console.log('🔍 UnifiedAuth - Attempting admin login for:', credentials.identifier);\n      const adminCredentials = {\n        email: credentials.identifier,\n        password: credentials.password,\n        userType: 'admin'\n      };\n      const response = await AdminAuthService.login(adminCredentials);\n      console.log('✅ UnifiedAuth - Admin login successful');\n      return response;\n    } catch (error) {\n      console.log('❌ UnifiedAuth - Admin login failed:', error.message);\n      return null;\n    }\n  }\n\n  /**\n   * Attempt login with student credentials\n   */\n  async tryStudentLogin(credentials) {\n    try {\n      console.log('🔍 UnifiedAuth - Attempting student login for:', credentials.identifier);\n      const studentCredentials = {\n        email: credentials.identifier,\n        // StudentAuthService handles both email and student number\n        password: credentials.password,\n        userType: 'student'\n      };\n      const response = await StudentAuthService.login(studentCredentials);\n      console.log('✅ UnifiedAuth - Student login successful');\n      return response;\n    } catch (error) {\n      console.log('❌ UnifiedAuth - Student login failed:', error.message);\n      return null;\n    }\n  }\n\n  /**\n   * Unified login method that tries both admin and student authentication\n   */\n  async login(credentials) {\n    console.log('🚀 UnifiedAuth - Starting unified login process');\n    const detectedType = this.detectUserType(credentials.identifier);\n    console.log('🔍 UnifiedAuth - Detected user type:', detectedType);\n    let response = null;\n    let lastError = null;\n\n    // If user type is specified, try that first\n    if (credentials.userType) {\n      if (credentials.userType === 'admin') {\n        response = await this.tryAdminLogin(credentials);\n      } else {\n        response = await this.tryStudentLogin(credentials);\n      }\n      if (response) {\n        return response;\n      }\n    }\n\n    // Try based on detected type\n    if (detectedType === 'student') {\n      response = await this.tryStudentLogin(credentials);\n      if (response) return response;\n\n      // If student login fails, try admin as fallback\n      response = await this.tryAdminLogin(credentials);\n      if (response) return response;\n    } else {\n      // Try admin first for email addresses\n      response = await this.tryAdminLogin(credentials);\n      if (response) return response;\n\n      // If admin login fails, try student\n      response = await this.tryStudentLogin(credentials);\n      if (response) return response;\n    }\n\n    // If all attempts failed\n    console.error('❌ UnifiedAuth - All login attempts failed');\n    throw new Error('Invalid credentials. Please check your email/student number and password.');\n  }\n\n  /**\n   * Logout from a specific role\n   */\n  async logout(role) {\n    try {\n      console.log(`🚪 UnifiedAuth - Logging out ${role}`);\n      if (role === 'admin') {\n        await AdminAuthService.logout();\n      } else {\n        await StudentAuthService.logout();\n      }\n      console.log(`✅ UnifiedAuth - ${role} logout successful`);\n    } catch (error) {\n      console.error(`❌ UnifiedAuth - ${role} logout failed:`, error);\n      // Clear local storage even if server logout fails\n      unifiedStorage.clearAuthData(role);\n    }\n  }\n\n  /**\n   * Logout from all roles\n   */\n  async logoutAll() {\n    console.log('🚪 UnifiedAuth - Logging out from all roles');\n    const availableRoles = unifiedStorage.getAvailableRoles();\n    for (const role of availableRoles) {\n      try {\n        await this.logout(role);\n      } catch (error) {\n        console.error(`❌ UnifiedAuth - Failed to logout ${role}:`, error);\n      }\n    }\n\n    // Ensure all local data is cleared\n    unifiedStorage.clearAllAuthData();\n    console.log('✅ UnifiedAuth - Logged out from all roles');\n  }\n\n  /**\n   * Check authentication status for a specific role\n   */\n  isAuthenticated(role) {\n    return unifiedStorage.isAuthenticated(role);\n  }\n\n  /**\n   * Get user data for a specific role\n   */\n  getUser(role) {\n    return unifiedStorage.getUserData(role);\n  }\n\n  /**\n   * Get all available authenticated roles\n   */\n  getAvailableRoles() {\n    return unifiedStorage.getAvailableRoles();\n  }\n\n  /**\n   * Get the most recently used role\n   */\n  getCurrentRole() {\n    return unifiedStorage.getMostRecentRole();\n  }\n\n  /**\n   * Switch to a different authenticated role\n   */\n  async switchRole(targetRole) {\n    console.log(`🔄 UnifiedAuth - Switching to ${targetRole} role`);\n    if (!this.isAuthenticated(targetRole)) {\n      throw new Error(`Cannot switch to ${targetRole}: not authenticated for this role`);\n    }\n    const user = this.getUser(targetRole);\n    if (!user) {\n      throw new Error(`Cannot switch to ${targetRole}: user data not found`);\n    }\n    console.log(`✅ UnifiedAuth - Switched to ${targetRole} role:`, user.email);\n    return user;\n  }\n\n  /**\n   * Admin registration (delegated to AdminAuthService)\n   */\n  async registerAdmin(data) {\n    return AdminAuthService.registerAdmin(data);\n  }\n\n  /**\n   * Verify OTP for admin registration (delegated to AdminAuthService)\n   */\n  async verifyOtp(data) {\n    return AdminAuthService.verifyOtp(data);\n  }\n\n  /**\n   * Resend OTP for admin registration (delegated to AdminAuthService)\n   */\n  async resendOtp(email) {\n    return AdminAuthService.resendOtp(email);\n  }\n\n  /**\n   * Get debug information\n   */\n  getDebugInfo() {\n    return {\n      storage: unifiedStorage.getDebugInfo(),\n      availableRoles: this.getAvailableRoles(),\n      currentRole: this.getCurrentRole()\n    };\n  }\n}\n\n// Export singleton instance\n_UnifiedAuthService = UnifiedAuthService;\nUnifiedAuthService.instance = void 0;\nexport const unifiedAuthService = UnifiedAuthService.getInstance();", "map": {"version": 3, "names": ["AdminAuthService", "StudentAuthService", "unifiedStorage", "UnifiedAuthService", "constructor", "getInstance", "instance", "detectUserType", "identifier", "isEmail", "includes", "isStudentNumber", "test", "trim", "tryAdminLogin", "credentials", "console", "log", "adminCredentials", "email", "password", "userType", "response", "login", "error", "message", "tryStudent<PERSON><PERSON>in", "studentCredentials", "detectedType", "lastError", "Error", "logout", "role", "clearAuthData", "logoutAll", "availableRoles", "getAvailableRoles", "clearAllAuthData", "isAuthenticated", "getUser", "getUserData", "getCurrentRole", "getMostRecentRole", "switchRole", "targetRole", "user", "registerAdmin", "data", "verifyOtp", "resendOtp", "getDebugInfo", "storage", "currentRole", "_UnifiedAuthService", "unifiedAuthService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/unified-auth.service.ts"], "sourcesContent": ["import { AdminAuthService } from './admin-auth.service';\nimport { StudentAuthService } from './student-auth.service';\nimport { unifiedStorage, UserRole } from '../utils/unifiedStorage';\nimport { \n  UnifiedLoginCredentials, \n  AuthResponse, \n  User,\n  AdminRegistrationData,\n  RegistrationResponse,\n  OtpVerificationData\n} from '../types/auth.types';\n\n/**\n * Unified Authentication Service\n * Provides a single interface for both admin and student authentication\n * while maintaining the underlying separation and security\n */\nexport class UnifiedAuthService {\n  private static instance: UnifiedAuthService;\n\n  private constructor() {}\n\n  public static getInstance(): UnifiedAuthService {\n    if (!UnifiedAuthService.instance) {\n      UnifiedAuthService.instance = new UnifiedAuthService();\n    }\n    return UnifiedAuthService.instance;\n  }\n\n  /**\n   * Detect user type based on identifier\n   * Email format suggests admin, student number format suggests student\n   */\n  private detectUserType(identifier: string): 'admin' | 'student' | 'both' {\n    const isEmail = identifier.includes('@');\n    const isStudentNumber = /^[0-9-]+$/.test(identifier.trim());\n\n    if (isEmail && !isStudentNumber) {\n      return 'both'; // Email could be either admin or student\n    } else if (isStudentNumber && !isEmail) {\n      return 'student'; // Student number is definitely student\n    } else if (isEmail) {\n      return 'both'; // Email format, could be either\n    }\n\n    return 'both'; // Default to trying both\n  }\n\n  /**\n   * Attempt login with admin credentials\n   */\n  private async tryAdminLogin(credentials: UnifiedLoginCredentials): Promise<AuthResponse | null> {\n    try {\n      console.log('🔍 UnifiedAuth - Attempting admin login for:', credentials.identifier);\n      \n      const adminCredentials = {\n        email: credentials.identifier,\n        password: credentials.password,\n        userType: 'admin' as const,\n      };\n\n      const response = await AdminAuthService.login(adminCredentials);\n      console.log('✅ UnifiedAuth - Admin login successful');\n      return response;\n    } catch (error: any) {\n      console.log('❌ UnifiedAuth - Admin login failed:', error.message);\n      return null;\n    }\n  }\n\n  /**\n   * Attempt login with student credentials\n   */\n  private async tryStudentLogin(credentials: UnifiedLoginCredentials): Promise<AuthResponse | null> {\n    try {\n      console.log('🔍 UnifiedAuth - Attempting student login for:', credentials.identifier);\n      \n      const studentCredentials = {\n        email: credentials.identifier, // StudentAuthService handles both email and student number\n        password: credentials.password,\n        userType: 'student' as const,\n      };\n\n      const response = await StudentAuthService.login(studentCredentials);\n      console.log('✅ UnifiedAuth - Student login successful');\n      return response;\n    } catch (error: any) {\n      console.log('❌ UnifiedAuth - Student login failed:', error.message);\n      return null;\n    }\n  }\n\n  /**\n   * Unified login method that tries both admin and student authentication\n   */\n  public async login(credentials: UnifiedLoginCredentials): Promise<AuthResponse> {\n    console.log('🚀 UnifiedAuth - Starting unified login process');\n    \n    const detectedType = this.detectUserType(credentials.identifier);\n    console.log('🔍 UnifiedAuth - Detected user type:', detectedType);\n\n    let response: AuthResponse | null = null;\n    let lastError: Error | null = null;\n\n    // If user type is specified, try that first\n    if (credentials.userType) {\n      if (credentials.userType === 'admin') {\n        response = await this.tryAdminLogin(credentials);\n      } else {\n        response = await this.tryStudentLogin(credentials);\n      }\n\n      if (response) {\n        return response;\n      }\n    }\n\n    // Try based on detected type\n    if (detectedType === 'student') {\n      response = await this.tryStudentLogin(credentials);\n      if (response) return response;\n      \n      // If student login fails, try admin as fallback\n      response = await this.tryAdminLogin(credentials);\n      if (response) return response;\n    } else {\n      // Try admin first for email addresses\n      response = await this.tryAdminLogin(credentials);\n      if (response) return response;\n      \n      // If admin login fails, try student\n      response = await this.tryStudentLogin(credentials);\n      if (response) return response;\n    }\n\n    // If all attempts failed\n    console.error('❌ UnifiedAuth - All login attempts failed');\n    throw new Error('Invalid credentials. Please check your email/student number and password.');\n  }\n\n  /**\n   * Logout from a specific role\n   */\n  public async logout(role: UserRole): Promise<void> {\n    try {\n      console.log(`🚪 UnifiedAuth - Logging out ${role}`);\n      \n      if (role === 'admin') {\n        await AdminAuthService.logout();\n      } else {\n        await StudentAuthService.logout();\n      }\n      \n      console.log(`✅ UnifiedAuth - ${role} logout successful`);\n    } catch (error) {\n      console.error(`❌ UnifiedAuth - ${role} logout failed:`, error);\n      // Clear local storage even if server logout fails\n      unifiedStorage.clearAuthData(role);\n    }\n  }\n\n  /**\n   * Logout from all roles\n   */\n  public async logoutAll(): Promise<void> {\n    console.log('🚪 UnifiedAuth - Logging out from all roles');\n    \n    const availableRoles = unifiedStorage.getAvailableRoles();\n    \n    for (const role of availableRoles) {\n      try {\n        await this.logout(role);\n      } catch (error) {\n        console.error(`❌ UnifiedAuth - Failed to logout ${role}:`, error);\n      }\n    }\n    \n    // Ensure all local data is cleared\n    unifiedStorage.clearAllAuthData();\n    console.log('✅ UnifiedAuth - Logged out from all roles');\n  }\n\n  /**\n   * Check authentication status for a specific role\n   */\n  public isAuthenticated(role: UserRole): boolean {\n    return unifiedStorage.isAuthenticated(role);\n  }\n\n  /**\n   * Get user data for a specific role\n   */\n  public getUser(role: UserRole): User | null {\n    return unifiedStorage.getUserData(role);\n  }\n\n  /**\n   * Get all available authenticated roles\n   */\n  public getAvailableRoles(): UserRole[] {\n    return unifiedStorage.getAvailableRoles();\n  }\n\n  /**\n   * Get the most recently used role\n   */\n  public getCurrentRole(): UserRole | null {\n    return unifiedStorage.getMostRecentRole();\n  }\n\n  /**\n   * Switch to a different authenticated role\n   */\n  public async switchRole(targetRole: UserRole): Promise<User> {\n    console.log(`🔄 UnifiedAuth - Switching to ${targetRole} role`);\n    \n    if (!this.isAuthenticated(targetRole)) {\n      throw new Error(`Cannot switch to ${targetRole}: not authenticated for this role`);\n    }\n\n    const user = this.getUser(targetRole);\n    if (!user) {\n      throw new Error(`Cannot switch to ${targetRole}: user data not found`);\n    }\n\n    console.log(`✅ UnifiedAuth - Switched to ${targetRole} role:`, user.email);\n    return user;\n  }\n\n  /**\n   * Admin registration (delegated to AdminAuthService)\n   */\n  public async registerAdmin(data: AdminRegistrationData): Promise<RegistrationResponse> {\n    return AdminAuthService.registerAdmin(data);\n  }\n\n  /**\n   * Verify OTP for admin registration (delegated to AdminAuthService)\n   */\n  public async verifyOtp(data: OtpVerificationData): Promise<void> {\n    return AdminAuthService.verifyOtp(data);\n  }\n\n  /**\n   * Resend OTP for admin registration (delegated to AdminAuthService)\n   */\n  public async resendOtp(email: string): Promise<void> {\n    return AdminAuthService.resendOtp(email);\n  }\n\n  /**\n   * Get debug information\n   */\n  public getDebugInfo(): Record<string, any> {\n    return {\n      storage: unifiedStorage.getDebugInfo(),\n      availableRoles: this.getAvailableRoles(),\n      currentRole: this.getCurrentRole(),\n    };\n  }\n}\n\n// Export singleton instance\nexport const unifiedAuthService = UnifiedAuthService.getInstance();\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAkB,yBAAyB;AAUlE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,CAAC;EAGtBC,WAAWA,CAAA,EAAG,CAAC;EAEvB,OAAcC,WAAWA,CAAA,EAAuB;IAC9C,IAAI,CAACF,kBAAkB,CAACG,QAAQ,EAAE;MAChCH,kBAAkB,CAACG,QAAQ,GAAG,IAAIH,kBAAkB,CAAC,CAAC;IACxD;IACA,OAAOA,kBAAkB,CAACG,QAAQ;EACpC;;EAEA;AACF;AACA;AACA;EACUC,cAAcA,CAACC,UAAkB,EAAgC;IACvE,MAAMC,OAAO,GAAGD,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC;IACxC,MAAMC,eAAe,GAAG,WAAW,CAACC,IAAI,CAACJ,UAAU,CAACK,IAAI,CAAC,CAAC,CAAC;IAE3D,IAAIJ,OAAO,IAAI,CAACE,eAAe,EAAE;MAC/B,OAAO,MAAM,CAAC,CAAC;IACjB,CAAC,MAAM,IAAIA,eAAe,IAAI,CAACF,OAAO,EAAE;MACtC,OAAO,SAAS,CAAC,CAAC;IACpB,CAAC,MAAM,IAAIA,OAAO,EAAE;MAClB,OAAO,MAAM,CAAC,CAAC;IACjB;IAEA,OAAO,MAAM,CAAC,CAAC;EACjB;;EAEA;AACF;AACA;EACE,MAAcK,aAAaA,CAACC,WAAoC,EAAgC;IAC9F,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEF,WAAW,CAACP,UAAU,CAAC;MAEnF,MAAMU,gBAAgB,GAAG;QACvBC,KAAK,EAAEJ,WAAW,CAACP,UAAU;QAC7BY,QAAQ,EAAEL,WAAW,CAACK,QAAQ;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMtB,gBAAgB,CAACuB,KAAK,CAACL,gBAAgB,CAAC;MAC/DF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,OAAOK,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBR,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEO,KAAK,CAACC,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAcC,eAAeA,CAACX,WAAoC,EAAgC;IAChG,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,WAAW,CAACP,UAAU,CAAC;MAErF,MAAMmB,kBAAkB,GAAG;QACzBR,KAAK,EAAEJ,WAAW,CAACP,UAAU;QAAE;QAC/BY,QAAQ,EAAEL,WAAW,CAACK,QAAQ;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMrB,kBAAkB,CAACsB,KAAK,CAACI,kBAAkB,CAAC;MACnEX,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,OAAOK,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBR,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEO,KAAK,CAACC,OAAO,CAAC;MACnE,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAaF,KAAKA,CAACR,WAAoC,EAAyB;IAC9EC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAE9D,MAAMW,YAAY,GAAG,IAAI,CAACrB,cAAc,CAACQ,WAAW,CAACP,UAAU,CAAC;IAChEQ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEW,YAAY,CAAC;IAEjE,IAAIN,QAA6B,GAAG,IAAI;IACxC,IAAIO,SAAuB,GAAG,IAAI;;IAElC;IACA,IAAId,WAAW,CAACM,QAAQ,EAAE;MACxB,IAAIN,WAAW,CAACM,QAAQ,KAAK,OAAO,EAAE;QACpCC,QAAQ,GAAG,MAAM,IAAI,CAACR,aAAa,CAACC,WAAW,CAAC;MAClD,CAAC,MAAM;QACLO,QAAQ,GAAG,MAAM,IAAI,CAACI,eAAe,CAACX,WAAW,CAAC;MACpD;MAEA,IAAIO,QAAQ,EAAE;QACZ,OAAOA,QAAQ;MACjB;IACF;;IAEA;IACA,IAAIM,YAAY,KAAK,SAAS,EAAE;MAC9BN,QAAQ,GAAG,MAAM,IAAI,CAACI,eAAe,CAACX,WAAW,CAAC;MAClD,IAAIO,QAAQ,EAAE,OAAOA,QAAQ;;MAE7B;MACAA,QAAQ,GAAG,MAAM,IAAI,CAACR,aAAa,CAACC,WAAW,CAAC;MAChD,IAAIO,QAAQ,EAAE,OAAOA,QAAQ;IAC/B,CAAC,MAAM;MACL;MACAA,QAAQ,GAAG,MAAM,IAAI,CAACR,aAAa,CAACC,WAAW,CAAC;MAChD,IAAIO,QAAQ,EAAE,OAAOA,QAAQ;;MAE7B;MACAA,QAAQ,GAAG,MAAM,IAAI,CAACI,eAAe,CAACX,WAAW,CAAC;MAClD,IAAIO,QAAQ,EAAE,OAAOA,QAAQ;IAC/B;;IAEA;IACAN,OAAO,CAACQ,KAAK,CAAC,2CAA2C,CAAC;IAC1D,MAAM,IAAIM,KAAK,CAAC,2EAA2E,CAAC;EAC9F;;EAEA;AACF;AACA;EACE,MAAaC,MAAMA,CAACC,IAAc,EAAiB;IACjD,IAAI;MACFhB,OAAO,CAACC,GAAG,CAAC,gCAAgCe,IAAI,EAAE,CAAC;MAEnD,IAAIA,IAAI,KAAK,OAAO,EAAE;QACpB,MAAMhC,gBAAgB,CAAC+B,MAAM,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,MAAM9B,kBAAkB,CAAC8B,MAAM,CAAC,CAAC;MACnC;MAEAf,OAAO,CAACC,GAAG,CAAC,mBAAmBe,IAAI,oBAAoB,CAAC;IAC1D,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,mBAAmBQ,IAAI,iBAAiB,EAAER,KAAK,CAAC;MAC9D;MACAtB,cAAc,CAAC+B,aAAa,CAACD,IAAI,CAAC;IACpC;EACF;;EAEA;AACF;AACA;EACE,MAAaE,SAASA,CAAA,EAAkB;IACtClB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D,MAAMkB,cAAc,GAAGjC,cAAc,CAACkC,iBAAiB,CAAC,CAAC;IAEzD,KAAK,MAAMJ,IAAI,IAAIG,cAAc,EAAE;MACjC,IAAI;QACF,MAAM,IAAI,CAACJ,MAAM,CAACC,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOR,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,oCAAoCQ,IAAI,GAAG,EAAER,KAAK,CAAC;MACnE;IACF;;IAEA;IACAtB,cAAc,CAACmC,gBAAgB,CAAC,CAAC;IACjCrB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;;EAEA;AACF;AACA;EACSqB,eAAeA,CAACN,IAAc,EAAW;IAC9C,OAAO9B,cAAc,CAACoC,eAAe,CAACN,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;EACSO,OAAOA,CAACP,IAAc,EAAe;IAC1C,OAAO9B,cAAc,CAACsC,WAAW,CAACR,IAAI,CAAC;EACzC;;EAEA;AACF;AACA;EACSI,iBAAiBA,CAAA,EAAe;IACrC,OAAOlC,cAAc,CAACkC,iBAAiB,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;EACSK,cAAcA,CAAA,EAAoB;IACvC,OAAOvC,cAAc,CAACwC,iBAAiB,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;EACE,MAAaC,UAAUA,CAACC,UAAoB,EAAiB;IAC3D5B,OAAO,CAACC,GAAG,CAAC,iCAAiC2B,UAAU,OAAO,CAAC;IAE/D,IAAI,CAAC,IAAI,CAACN,eAAe,CAACM,UAAU,CAAC,EAAE;MACrC,MAAM,IAAId,KAAK,CAAC,oBAAoBc,UAAU,mCAAmC,CAAC;IACpF;IAEA,MAAMC,IAAI,GAAG,IAAI,CAACN,OAAO,CAACK,UAAU,CAAC;IACrC,IAAI,CAACC,IAAI,EAAE;MACT,MAAM,IAAIf,KAAK,CAAC,oBAAoBc,UAAU,uBAAuB,CAAC;IACxE;IAEA5B,OAAO,CAACC,GAAG,CAAC,+BAA+B2B,UAAU,QAAQ,EAAEC,IAAI,CAAC1B,KAAK,CAAC;IAC1E,OAAO0B,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAaC,aAAaA,CAACC,IAA2B,EAAiC;IACrF,OAAO/C,gBAAgB,CAAC8C,aAAa,CAACC,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;EACE,MAAaC,SAASA,CAACD,IAAyB,EAAiB;IAC/D,OAAO/C,gBAAgB,CAACgD,SAAS,CAACD,IAAI,CAAC;EACzC;;EAEA;AACF;AACA;EACE,MAAaE,SAASA,CAAC9B,KAAa,EAAiB;IACnD,OAAOnB,gBAAgB,CAACiD,SAAS,CAAC9B,KAAK,CAAC;EAC1C;;EAEA;AACF;AACA;EACS+B,YAAYA,CAAA,EAAwB;IACzC,OAAO;MACLC,OAAO,EAAEjD,cAAc,CAACgD,YAAY,CAAC,CAAC;MACtCf,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACxCgB,WAAW,EAAE,IAAI,CAACX,cAAc,CAAC;IACnC,CAAC;EACH;AACF;;AAEA;AAAAY,mBAAA,GArPalD,kBAAkB;AAAlBA,kBAAkB,CACdG,QAAQ;AAqPzB,OAAO,MAAMgD,kBAAkB,GAAGnD,kBAAkB,CAACE,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}