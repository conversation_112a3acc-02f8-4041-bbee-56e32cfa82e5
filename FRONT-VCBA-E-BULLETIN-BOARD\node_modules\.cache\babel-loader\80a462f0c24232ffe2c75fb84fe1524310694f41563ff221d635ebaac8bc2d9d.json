{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M9 10h6\",\n  key: \"9gxzsh\"\n}], [\"path\", {\n  d: \"M12 13V7\",\n  key: \"h0r20n\"\n}], [\"path\", {\n  d: \"M9 17h6\",\n  key: \"r8uit2\"\n}]];\nconst FileDiff = createLucideIcon(\"file-diff\", __iconNode);\nexport { __iconNode, FileDiff as default };\n//# sourceMappingURL=file-diff.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}