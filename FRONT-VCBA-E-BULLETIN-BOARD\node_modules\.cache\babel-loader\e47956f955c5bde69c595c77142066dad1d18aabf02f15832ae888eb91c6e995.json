{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}]];\nconst MessageSquare = createLucideIcon(\"message-square\", __iconNode);\nexport { __iconNode, MessageSquare as default };\n//# sourceMappingURL=message-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}