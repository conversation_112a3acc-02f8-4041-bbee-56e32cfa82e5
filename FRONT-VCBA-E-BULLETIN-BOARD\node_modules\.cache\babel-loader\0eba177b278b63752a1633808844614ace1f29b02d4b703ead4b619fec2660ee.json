{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18h10\",\n  key: \"1y5s8o\"\n}], [\"path\", {\n  d: \"m17 21 3-3-3-3\",\n  key: \"1ammt0\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"76md6a\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"v9paqo\"\n}]];\nconst DecimalsArrowRight = createLucideIcon(\"decimals-arrow-right\", __iconNode);\nexport { __iconNode, DecimalsArrowRight as default };\n//# sourceMappingURL=decimals-arrow-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}