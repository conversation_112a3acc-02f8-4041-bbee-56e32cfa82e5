{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 8h.01\",\n  key: \"1r9ogq\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}], [\"path\", {\n  d: \"M16 12h.01\",\n  key: \"1l6xoz\"\n}], [\"path\", {\n  d: \"M18 8h.01\",\n  key: \"emo2bl\"\n}], [\"path\", {\n  d: \"M6 8h.01\",\n  key: \"x9i8wu\"\n}], [\"path\", {\n  d: \"M7 16h10\",\n  key: \"wp8him\"\n}], [\"path\", {\n  d: \"M8 12h.01\",\n  key: \"czm47f\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}]];\nconst Keyboard = createLucideIcon(\"keyboard\", __iconNode);\nexport { __iconNode, Keyboard as default };\n//# sourceMappingURL=keyboard.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}