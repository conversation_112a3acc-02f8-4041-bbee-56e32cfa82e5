{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6\",\n  key: \"3zrzxg\"\n}], [\"path\", {\n  d: \"M2 12a9 9 0 0 1 8 8\",\n  key: \"g6cvee\"\n}], [\"path\", {\n  d: \"M2 16a5 5 0 0 1 4 4\",\n  key: \"1y1dii\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"2.01\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"xu2jvo\"\n}]];\nconst Cast = createLucideIcon(\"cast\", __iconNode);\nexport { __iconNode, Cast as default };\n//# sourceMappingURL=cast.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}