{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 22H4a2 2 0 0 1-2-2V6\",\n  key: \"pblm9e\"\n}], [\"path\", {\n  d: \"m22 13-1.296-1.296a2.41 2.41 0 0 0-3.408 0L11 18\",\n  key: \"nf6bnh\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"1822b1\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"16\",\n  x: \"6\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"12espp\"\n}]];\nconst Images = createLucideIcon(\"images\", __iconNode);\nexport { __iconNode, Images as default };\n//# sourceMappingURL=images.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}