{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"m14.305 7.53.923-.382\",\n  key: \"1mlnsw\"\n}], [\"path\", {\n  d: \"m15.228 4.852-.923-.383\",\n  key: \"82mpwg\"\n}], [\"path\", {\n  d: \"m16.852 3.228-.383-.924\",\n  key: \"ln4sir\"\n}], [\"path\", {\n  d: \"m16.852 8.772-.383.923\",\n  key: \"1dejw0\"\n}], [\"path\", {\n  d: \"m19.148 3.228.383-.924\",\n  key: \"192kgf\"\n}], [\"path\", {\n  d: \"m19.53 9.696-.382-.924\",\n  key: \"fiavlr\"\n}], [\"path\", {\n  d: \"m20.772 4.852.924-.383\",\n  key: \"1j8mgp\"\n}], [\"path\", {\n  d: \"m20.772 7.148.924.383\",\n  key: \"zix9be\"\n}], [\"path\", {\n  d: \"M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"1tnzv8\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}]];\nconst MonitorCog = createLucideIcon(\"monitor-cog\", __iconNode);\nexport { __iconNode, MonitorCog as default };\n//# sourceMappingURL=monitor-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}