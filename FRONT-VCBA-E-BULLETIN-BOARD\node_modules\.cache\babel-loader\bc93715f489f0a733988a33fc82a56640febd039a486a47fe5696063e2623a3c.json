{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"13\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1fdu0f\"\n}], [\"path\", {\n  d: \"m9 22 3-3 3 3\",\n  key: \"17z65a\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"13\",\n  x: \"14\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"1squn4\"\n}]];\nconst BetweenVerticalEnd = createLucideIcon(\"between-vertical-end\", __iconNode);\nexport { __iconNode, BetweenVerticalEnd as default };\n//# sourceMappingURL=between-vertical-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}