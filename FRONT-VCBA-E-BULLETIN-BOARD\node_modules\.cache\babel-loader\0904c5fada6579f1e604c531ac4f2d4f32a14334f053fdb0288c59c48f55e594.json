{"ast": null, "code": "import { httpClient } from './api.service';\nclass HolidayService {\n  constructor() {\n    this.baseUrl = '/api/holidays';\n  }\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidays(year, category, countryCode) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    if (category) params.append('category', category);\n    if (countryCode) params.append('country_code', countryCode);\n    const response = await httpClient.get(`${this.baseUrl}?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get Philippine holidays for a specific year\n   */\n  async getPhilippineHolidays(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/philippine?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get international holidays for a specific year\n   */\n  async getInternationalHolidays(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/international?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get religious holidays for a specific year\n   */\n  async getReligiousHolidays(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/religious?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get all holidays from API sources (not database)\n   */\n  async getAllHolidaysFromAPI(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/api-source?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/stats?${params.toString()}`);\n    return response.data.stats;\n  }\n\n  /**\n   * Preview holidays before syncing (admin only)\n   */\n  async previewHolidays(year) {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    const response = await httpClient.get(`${this.baseUrl}/preview?${params.toString()}`);\n    return response.data.preview;\n  }\n\n  /**\n   * Sync holidays to database (admin only)\n   */\n  async syncHolidays(year, force = false) {\n    const response = await httpClient.post(`${this.baseUrl}/sync`, {\n      year: year || new Date().getFullYear(),\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Sync holidays for multiple years (admin only)\n   */\n  async syncMultipleYears(years, force = false) {\n    const response = await httpClient.post(`${this.baseUrl}/sync-multiple`, {\n      years,\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year (admin only)\n   */\n  async deleteAutoGeneratedHolidays(year) {\n    const response = await httpClient.delete(`${this.baseUrl}/auto-generated/${year}`);\n    return response.data.deletedCount;\n  }\n\n  /**\n   * Check if a date is a holiday\n   */\n  async isHoliday(date) {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.some(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error checking if date is holiday:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holidays for a specific date\n   */\n  async getHolidaysForDate(date) {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.filter(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error getting holidays for date:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get upcoming holidays (next 30 days)\n   */\n  async getUpcomingHolidays(days = 30) {\n    try {\n      const today = new Date();\n      const endDate = new Date();\n      endDate.setDate(today.getDate() + days);\n      const currentYear = today.getFullYear();\n      const endYear = endDate.getFullYear();\n      let holidays = [];\n\n      // Get holidays for current year\n      holidays = await this.getHolidays(currentYear);\n\n      // If the end date is in the next year, also get holidays for next year\n      if (endYear > currentYear) {\n        const nextYearHolidays = await this.getHolidays(endYear);\n        holidays = [...holidays, ...nextYearHolidays];\n      }\n\n      // Filter holidays within the date range\n      const todayStr = today.toISOString().split('T')[0];\n      const endDateStr = endDate.toISOString().split('T')[0];\n      return holidays.filter(holiday => holiday.event_date >= todayStr && holiday.event_date <= endDateStr).sort((a, b) => a.event_date.localeCompare(b.event_date));\n    } catch (error) {\n      console.error('Error getting upcoming holidays:', error);\n      return [];\n    }\n  }\n}\nexport const holidayService = new HolidayService();", "map": {"version": 3, "names": ["httpClient", "HolidayService", "constructor", "baseUrl", "getHolidays", "year", "category", "countryCode", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "holidays", "getPhilippineHolidays", "getInternationalHolidays", "getReligiousHolidays", "getAllHolidaysFromAPI", "getHolidayStats", "stats", "previewHolidays", "preview", "syncHolidays", "force", "post", "Date", "getFullYear", "results", "syncMultipleYears", "years", "deleteAutoGeneratedHolidays", "delete", "deletedCount", "isHoliday", "date", "some", "holiday", "event_date", "error", "console", "getHolidaysForDate", "filter", "getUpcomingHolidays", "days", "today", "endDate", "setDate", "getDate", "currentYear", "endYear", "nextYearHolidays", "todayStr", "toISOString", "split", "endDateStr", "sort", "a", "b", "localeCompare", "holidayService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/holidayService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { ApiResponse } from '../types';\n\nexport interface Holiday {\n  calendar_id: number;\n  title: string;\n  description: string;\n  event_date: string;\n  category_id: number;\n  category_name: string;\n  category_color: string;\n  holiday_type: 'local' | 'international' | 'school';\n  country_code: string;\n  is_auto_generated: boolean;\n  api_source: string;\n  local_name: string;\n  holiday_types: string;\n  is_global: boolean;\n  is_fixed: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface HolidayStats {\n  total: number;\n  byCategory: Record<string, number>;\n  byCountry: Record<string, number>;\n  byType: Record<string, number>;\n  autoGenerated: number;\n  manual: number;\n}\n\nexport interface SyncResults {\n  created: number;\n  updated: number;\n  skipped: number;\n  errors: Array<{ holiday: string; error: string }>;\n}\n\nexport interface HolidayPreview {\n  new: Holiday[];\n  existing: Holiday[];\n  toUpdate: Holiday[];\n}\n\nclass HolidayService {\n  private baseUrl = '/api/holidays';\n\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidays(year?: number, category?: string, countryCode?: string): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n    if (category) params.append('category', category);\n    if (countryCode) params.append('country_code', countryCode);\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get Philippine holidays for a specific year\n   */\n  async getPhilippineHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/philippine?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get international holidays for a specific year\n   */\n  async getInternationalHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/international?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get religious holidays for a specific year\n   */\n  async getReligiousHolidays(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/religious?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get all holidays from API sources (not database)\n   */\n  async getAllHolidaysFromAPI(year?: number): Promise<Holiday[]> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/api-source?${params.toString()}`);\n    return response.data.holidays;\n  }\n\n  /**\n   * Get holiday statistics\n   */\n  async getHolidayStats(year?: number): Promise<HolidayStats> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ stats: HolidayStats }>(`${this.baseUrl}/stats?${params.toString()}`);\n    return response.data.stats;\n  }\n\n  /**\n   * Preview holidays before syncing (admin only)\n   */\n  async previewHolidays(year?: number): Promise<HolidayPreview> {\n    const params = new URLSearchParams();\n    if (year) params.append('year', year.toString());\n\n    const response = await httpClient.get<{ preview: HolidayPreview }>(`${this.baseUrl}/preview?${params.toString()}`);\n    return response.data.preview;\n  }\n\n  /**\n   * Sync holidays to database (admin only)\n   */\n  async syncHolidays(year?: number, force = false): Promise<SyncResults> {\n    const response = await httpClient.post<{ results: SyncResults }>(`${this.baseUrl}/sync`, {\n      year: year || new Date().getFullYear(),\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Sync holidays for multiple years (admin only)\n   */\n  async syncMultipleYears(years: number[], force = false): Promise<Record<number, SyncResults>> {\n    const response = await httpClient.post<{ results: Record<number, SyncResults> }>(`${this.baseUrl}/sync-multiple`, {\n      years,\n      force\n    });\n    return response.data.results;\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year (admin only)\n   */\n  async deleteAutoGeneratedHolidays(year: number): Promise<number> {\n    const response = await httpClient.delete<{ deletedCount: number }>(`${this.baseUrl}/auto-generated/${year}`);\n    return response.data.deletedCount;\n  }\n\n  /**\n   * Check if a date is a holiday\n   */\n  async isHoliday(date: string): Promise<boolean> {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.some(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error checking if date is holiday:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get holidays for a specific date\n   */\n  async getHolidaysForDate(date: string): Promise<Holiday[]> {\n    try {\n      const year = new Date(date).getFullYear();\n      const holidays = await this.getHolidays(year);\n      return holidays.filter(holiday => holiday.event_date === date);\n    } catch (error) {\n      console.error('Error getting holidays for date:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get upcoming holidays (next 30 days)\n   */\n  async getUpcomingHolidays(days = 30): Promise<Holiday[]> {\n    try {\n      const today = new Date();\n      const endDate = new Date();\n      endDate.setDate(today.getDate() + days);\n\n      const currentYear = today.getFullYear();\n      const endYear = endDate.getFullYear();\n\n      let holidays: Holiday[] = [];\n\n      // Get holidays for current year\n      holidays = await this.getHolidays(currentYear);\n\n      // If the end date is in the next year, also get holidays for next year\n      if (endYear > currentYear) {\n        const nextYearHolidays = await this.getHolidays(endYear);\n        holidays = [...holidays, ...nextYearHolidays];\n      }\n\n      // Filter holidays within the date range\n      const todayStr = today.toISOString().split('T')[0];\n      const endDateStr = endDate.toISOString().split('T')[0];\n\n      return holidays.filter(holiday => \n        holiday.event_date >= todayStr && holiday.event_date <= endDateStr\n      ).sort((a, b) => a.event_date.localeCompare(b.event_date));\n    } catch (error) {\n      console.error('Error getting upcoming holidays:', error);\n      return [];\n    }\n  }\n}\n\nexport const holidayService = new HolidayService();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AA6C1C,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,eAAe;EAAA;EAEjC;AACF;AACA;EACE,MAAMC,WAAWA,CAACC,IAAa,EAAEC,QAAiB,EAAEC,WAAoB,EAAsB;IAC5F,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAChD,IAAIL,QAAQ,EAAEE,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;IACjD,IAAIC,WAAW,EAAEC,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEH,WAAW,CAAC;IAE3D,MAAMK,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,IAAIK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACtG,OAAOC,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B;;EAEA;AACF;AACA;EACE,MAAMC,qBAAqBA,CAACX,IAAa,EAAsB;IAC7D,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,eAAeK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACjH,OAAOC,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B;;EAEA;AACF;AACA;EACE,MAAME,wBAAwBA,CAACZ,IAAa,EAAsB;IAChE,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,kBAAkBK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACpH,OAAOC,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B;;EAEA;AACF;AACA;EACE,MAAMG,oBAAoBA,CAACb,IAAa,EAAsB;IAC5D,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,cAAcK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChH,OAAOC,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B;;EAEA;AACF;AACA;EACE,MAAMI,qBAAqBA,CAACd,IAAa,EAAsB;IAC7D,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,eAAeK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IACjH,OAAOC,QAAQ,CAACE,IAAI,CAACC,QAAQ;EAC/B;;EAEA;AACF;AACA;EACE,MAAMK,eAAeA,CAACf,IAAa,EAAyB;IAC1D,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA0B,GAAG,IAAI,CAACV,OAAO,UAAUK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC5G,OAAOC,QAAQ,CAACE,IAAI,CAACO,KAAK;EAC5B;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAACjB,IAAa,EAA2B;IAC5D,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIJ,IAAI,EAAEG,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAMC,QAAQ,GAAG,MAAMZ,UAAU,CAACa,GAAG,CAA8B,GAAG,IAAI,CAACV,OAAO,YAAYK,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;IAClH,OAAOC,QAAQ,CAACE,IAAI,CAACS,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMC,YAAYA,CAACnB,IAAa,EAAEoB,KAAK,GAAG,KAAK,EAAwB;IACrE,MAAMb,QAAQ,GAAG,MAAMZ,UAAU,CAAC0B,IAAI,CAA2B,GAAG,IAAI,CAACvB,OAAO,OAAO,EAAE;MACvFE,IAAI,EAAEA,IAAI,IAAI,IAAIsB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACtCH;IACF,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI,CAACe,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAACC,KAAe,EAAEN,KAAK,GAAG,KAAK,EAAwC;IAC5F,MAAMb,QAAQ,GAAG,MAAMZ,UAAU,CAAC0B,IAAI,CAA2C,GAAG,IAAI,CAACvB,OAAO,gBAAgB,EAAE;MAChH4B,KAAK;MACLN;IACF,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI,CAACe,OAAO;EAC9B;;EAEA;AACF;AACA;EACE,MAAMG,2BAA2BA,CAAC3B,IAAY,EAAmB;IAC/D,MAAMO,QAAQ,GAAG,MAAMZ,UAAU,CAACiC,MAAM,CAA2B,GAAG,IAAI,CAAC9B,OAAO,mBAAmBE,IAAI,EAAE,CAAC;IAC5G,OAAOO,QAAQ,CAACE,IAAI,CAACoB,YAAY;EACnC;;EAEA;AACF;AACA;EACE,MAAMC,SAASA,CAACC,IAAY,EAAoB;IAC9C,IAAI;MACF,MAAM/B,IAAI,GAAG,IAAIsB,IAAI,CAACS,IAAI,CAAC,CAACR,WAAW,CAAC,CAAC;MACzC,MAAMb,QAAQ,GAAG,MAAM,IAAI,CAACX,WAAW,CAACC,IAAI,CAAC;MAC7C,OAAOU,QAAQ,CAACsB,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,UAAU,KAAKH,IAAI,CAAC;IAC9D,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAME,kBAAkBA,CAACN,IAAY,EAAsB;IACzD,IAAI;MACF,MAAM/B,IAAI,GAAG,IAAIsB,IAAI,CAACS,IAAI,CAAC,CAACR,WAAW,CAAC,CAAC;MACzC,MAAMb,QAAQ,GAAG,MAAM,IAAI,CAACX,WAAW,CAACC,IAAI,CAAC;MAC7C,OAAOU,QAAQ,CAAC4B,MAAM,CAACL,OAAO,IAAIA,OAAO,CAACC,UAAU,KAAKH,IAAI,CAAC;IAChE,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMI,mBAAmBA,CAACC,IAAI,GAAG,EAAE,EAAsB;IACvD,IAAI;MACF,MAAMC,KAAK,GAAG,IAAInB,IAAI,CAAC,CAAC;MACxB,MAAMoB,OAAO,GAAG,IAAIpB,IAAI,CAAC,CAAC;MAC1BoB,OAAO,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAAC;MAEvC,MAAMK,WAAW,GAAGJ,KAAK,CAAClB,WAAW,CAAC,CAAC;MACvC,MAAMuB,OAAO,GAAGJ,OAAO,CAACnB,WAAW,CAAC,CAAC;MAErC,IAAIb,QAAmB,GAAG,EAAE;;MAE5B;MACAA,QAAQ,GAAG,MAAM,IAAI,CAACX,WAAW,CAAC8C,WAAW,CAAC;;MAE9C;MACA,IAAIC,OAAO,GAAGD,WAAW,EAAE;QACzB,MAAME,gBAAgB,GAAG,MAAM,IAAI,CAAChD,WAAW,CAAC+C,OAAO,CAAC;QACxDpC,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAGqC,gBAAgB,CAAC;MAC/C;;MAEA;MACA,MAAMC,QAAQ,GAAGP,KAAK,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD,MAAMC,UAAU,GAAGT,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEtD,OAAOxC,QAAQ,CAAC4B,MAAM,CAACL,OAAO,IAC5BA,OAAO,CAACC,UAAU,IAAIc,QAAQ,IAAIf,OAAO,CAACC,UAAU,IAAIiB,UAC1D,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,UAAU,CAACqB,aAAa,CAACD,CAAC,CAACpB,UAAU,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,EAAE;IACX;EACF;AACF;AAEA,OAAO,MAAMqB,cAAc,GAAG,IAAI5D,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}