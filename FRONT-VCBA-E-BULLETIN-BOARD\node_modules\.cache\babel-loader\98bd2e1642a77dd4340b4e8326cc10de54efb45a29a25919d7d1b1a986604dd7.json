{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{API_BASE_URL,AUTH_TOKEN_KEY,ADMIN_AUTH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY}from'../config/constants';// API configuration\nconst API_CONFIG={baseURL:API_BASE_URL,timeout:10000,headers:{'Content-Type':'application/json'}};// Custom error class for API errors\nexport class ApiError extends Error{constructor(message,status,data){super(message);this.status=void 0;this.data=void 0;this.name='ApiError';this.status=status;this.data=data;}}// Base token manager interface\n// Generic token manager (legacy - for backward compatibility)\nexport const tokenManager={getToken(){// First try the general auth token\nlet token=localStorage.getItem(AUTH_TOKEN_KEY);// If not found, prioritize admin token over student token\n// This ensures admin sessions take precedence when both tokens exist\nif(!token){token=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);}// If still not found, try student-specific token as fallback\nif(!token){token=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);}return token;},setToken(token){localStorage.setItem(AUTH_TOKEN_KEY,token);},removeToken(){localStorage.removeItem(AUTH_TOKEN_KEY);},getAuthHeaders(){const token=this.getToken();return token?{Authorization:\"Bearer \".concat(token)}:{};}};// Admin-specific token manager\nexport const adminTokenManager={getToken(){const token=localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);console.log('🔑 Admin Token Manager - Getting token:',token?\"\".concat(token.substring(0,20),\"...\"):'null');return token;},setToken(token){console.log('🔑 Admin Token Manager - Setting token:',\"\".concat(token.substring(0,20),\"...\"));localStorage.setItem(ADMIN_AUTH_TOKEN_KEY,token);},removeToken(){console.log('🔑 Admin Token Manager - Removing token');localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);},getAuthHeaders(){const token=this.getToken();const headers=token?{Authorization:\"Bearer \".concat(token)}:{};console.log('🔑 Admin Token Manager - Auth headers:',token?'Bearer token present':'No token');return headers;}};// Student-specific token manager\nexport const studentTokenManager={getToken(){const token=localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);console.log('🎓 Student Token Manager - Getting token:',token?\"\".concat(token.substring(0,20),\"...\"):'null');return token;},setToken(token){console.log('🎓 Student Token Manager - Setting token:',\"\".concat(token.substring(0,20),\"...\"));localStorage.setItem(STUDENT_AUTH_TOKEN_KEY,token);},removeToken(){console.log('🎓 Student Token Manager - Removing token');localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);},getAuthHeaders(){const token=this.getToken();const headers=token?{Authorization:\"Bearer \".concat(token)}:{};console.log('🎓 Student Token Manager - Auth headers:',token?'Bearer token present':'No token');return headers;}};// HTTP client class\nclass HttpClient{constructor(baseURL){let defaultHeaders=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};let customTokenManager=arguments.length>2?arguments[2]:undefined;this.baseURL=void 0;this.defaultHeaders=void 0;this.tokenManager=void 0;this.baseURL=baseURL;this.defaultHeaders=defaultHeaders;this.tokenManager=customTokenManager||tokenManager;// Use custom or default token manager\n}async request(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};let includeAuth=arguments.length>2&&arguments[2]!==undefined?arguments[2]:true;const url=\"\".concat(this.baseURL).concat(endpoint);// Build headers properly handling different header types\nconst headers=_objectSpread({},this.defaultHeaders);// Handle options.headers which could be Headers object or plain object\nif(options.headers){if(options.headers instanceof Headers){// Convert Headers object to plain object\noptions.headers.forEach((value,key)=>{headers[key]=value;});}else{// Plain object - spread it\nObject.assign(headers,options.headers);}}// Only add auth headers if requested\nif(includeAuth){Object.assign(headers,this.tokenManager.getAuthHeaders());}const config=_objectSpread(_objectSpread({},options),{},{credentials:'include',// Include cookies for refresh token\nheaders});try{const response=await fetch(url,config);// Handle different content types\nlet data;const contentType=response.headers.get('content-type');if(contentType&&contentType.includes('application/json')){data=await response.json();}else{data=await response.text();}// Handle HTTP errors\nif(!response.ok){var _data,_data$error,_data2;const errorMessage=((_data=data)===null||_data===void 0?void 0:(_data$error=_data.error)===null||_data$error===void 0?void 0:_data$error.message)||((_data2=data)===null||_data2===void 0?void 0:_data2.message)||\"HTTP \".concat(response.status);throw new ApiError(errorMessage,response.status,data);}return data;}catch(error){// Handle network errors\nif(error instanceof ApiError){throw error;}// Handle specific network connection errors\nif(error instanceof TypeError){if(error.message.includes('fetch')||error.message.includes('Failed to fetch')){throw new ApiError(\"Unable to connect to server at \".concat(this.baseURL,\". Please ensure the backend server is running.\"),0,{originalError:error.message,endpoint});}}// Handle other network errors\nif(error&&typeof error==='object'){const errorObj=error;if(errorObj.name==='NetworkError'||errorObj.message&&typeof errorObj.message==='string'&&errorObj.message.includes('ERR_CONNECTION_REFUSED')){throw new ApiError(\"Connection refused to \".concat(this.baseURL,\". Please check if the server is running on the correct port.\"),0,{originalError:errorObj.message||String(error),endpoint});}}throw new ApiError('An unexpected error occurred.',0,{originalError:String(error),endpoint});}}async get(endpoint,params){const url=params?\"\".concat(endpoint,\"?\").concat(new URLSearchParams(params)):endpoint;return this.request(url,{method:'GET'});}// Public GET method without authentication\nasync getPublic(endpoint,params){const url=params?\"\".concat(endpoint,\"?\").concat(new URLSearchParams(params)):endpoint;return this.request(url,{method:'GET'},false);}async post(endpoint,data){return this.request(endpoint,{method:'POST',body:data?JSON.stringify(data):undefined});}async put(endpoint,data){return this.request(endpoint,{method:'PUT',body:data?JSON.stringify(data):undefined});}async patch(endpoint,data){return this.request(endpoint,{method:'PATCH',body:data?JSON.stringify(data):undefined});}async delete(endpoint){return this.request(endpoint,{method:'DELETE'});}}// Create and export HTTP client instances\nexport const httpClient=new HttpClient(API_CONFIG.baseURL,API_CONFIG.headers);// Default/legacy client\nexport const adminHttpClient=new HttpClient(API_CONFIG.baseURL,API_CONFIG.headers,adminTokenManager);export const studentHttpClient=new HttpClient(API_CONFIG.baseURL,API_CONFIG.headers,studentTokenManager);// Setup response interceptor for a specific client\nconst setupClientInterceptor=(client,clientTokenManager,onUnauthorized)=>{const originalRequest=client['request'].bind(client);client['request']=async function(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{return await originalRequest(endpoint,options);}catch(error){if(error instanceof ApiError){// Handle unauthorized access\nif(error.status===401&&onUnauthorized){clientTokenManager.removeToken();onUnauthorized();}// DISABLED TOKEN REFRESH - Just log the error and continue\nif(error.status===401){console.warn('API Service - 401 error detected but token refresh DISABLED:',endpoint);// Don't attempt refresh, just let the error propagate\n}}throw error;}};};// Setup response interceptor for all clients\nexport const setupResponseInterceptor=onUnauthorized=>{setupClientInterceptor(httpClient,tokenManager,onUnauthorized);setupClientInterceptor(adminHttpClient,adminTokenManager,onUnauthorized);setupClientInterceptor(studentHttpClient,studentTokenManager,onUnauthorized);};export default httpClient;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}