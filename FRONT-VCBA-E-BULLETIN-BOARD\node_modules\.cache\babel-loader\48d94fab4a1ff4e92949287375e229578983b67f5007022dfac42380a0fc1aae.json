{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"9lu3g6\"\n}]];\nconst RectangleHorizontal = createLucideIcon(\"rectangle-horizontal\", __iconNode);\nexport { __iconNode, RectangleHorizontal as default };\n//# sourceMappingURL=rectangle-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}