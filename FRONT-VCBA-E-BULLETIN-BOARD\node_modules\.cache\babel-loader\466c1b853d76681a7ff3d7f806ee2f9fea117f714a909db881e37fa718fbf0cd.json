{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 8V2h6\",\n  key: \"hiwtdz\"\n}], [\"path\", {\n  d: \"m2 2 10 10\",\n  key: \"1oh8rs\"\n}], [\"path\", {\n  d: \"M12 2A10 10 0 1 1 2 12\",\n  key: \"rrk4fa\"\n}]];\nconst CircleArrowOutUpLeft = createLucideIcon(\"circle-arrow-out-up-left\", __iconNode);\nexport { __iconNode, CircleArrowOutUpLeft as default };\n//# sourceMappingURL=circle-arrow-out-up-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}