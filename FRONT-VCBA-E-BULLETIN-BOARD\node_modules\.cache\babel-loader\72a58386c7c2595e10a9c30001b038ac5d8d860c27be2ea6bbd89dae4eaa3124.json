{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"4\",\n  y2: \"20\",\n  key: \"fy8qot\"\n}], [\"polygon\", {\n  points: \"10,4 20,12 10,20\",\n  key: \"1mc1pf\"\n}]];\nconst StepForward = createLucideIcon(\"step-forward\", __iconNode);\nexport { __iconNode, StepForward as default };\n//# sourceMappingURL=step-forward.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}