{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6V2H8\",\n  key: \"1155em\"\n}], [\"path\", {\n  d: \"m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z\",\n  key: \"w2lp3e\"\n}], [\"path\", {\n  d: \"M2 12h2\",\n  key: \"1t8f8n\"\n}], [\"path\", {\n  d: \"M9 11v2\",\n  key: \"1ueba0\"\n}], [\"path\", {\n  d: \"M15 11v2\",\n  key: \"i11awn\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}]];\nconst BotMessageSquare = createLucideIcon(\"bot-message-square\", __iconNode);\nexport { __iconNode, BotMessageSquare as default };\n//# sourceMappingURL=bot-message-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}