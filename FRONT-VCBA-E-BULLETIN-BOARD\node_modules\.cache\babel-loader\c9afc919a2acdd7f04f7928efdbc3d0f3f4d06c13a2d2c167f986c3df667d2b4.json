{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useCallback,useEffect}from'react';import{calendarService}from'../services/calendarService';export const useCalendarImageUpload=_ref=>{let{calendarId,onSuccess,onError}=_ref;const[existingImages,setExistingImages]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[pendingDeletes,setPendingDeletes]=useState([]);// Clear existing images when calendarId becomes null (for new events)\nuseEffect(()=>{if(!calendarId){// console.log('🧹 useCalendarImageUpload - Clearing existing images for new event');\nsetExistingImages([]);setPendingDeletes([]);setError(null);}},[calendarId]);// Clear error\nconst clearError=useCallback(()=>{setError(null);},[]);// Clear pending deletes\nconst clearPendingDeletes=useCallback(()=>{setPendingDeletes([]);},[]);// Clear all image-related state (for new events)\nconst clearAllImageState=useCallback(()=>{// console.log('🧹 useCalendarImageUpload - Clearing all image state');\nsetExistingImages([]);setPendingDeletes([]);setError(null);setLoading(false);},[]);// Fetch existing images for the calendar event\nconst refreshImages=useCallback(async()=>{if(!calendarId){setExistingImages([]);return;}try{setLoading(true);setError(null);// console.log(`📸 Fetching images for calendar event ${calendarId}`);\nconst response=await calendarService.getEventAttachments(calendarId);if(response.success&&response.data){const images=response.data.attachments||[];// console.log(`✅ Found ${images.length} images for calendar event ${calendarId}`);\n// Add file_url for display using the same approach as announcements\nconst imagesWithUrls=images.map(img=>{// Use the same getImageUrl function that announcements use\nconst{getImageUrl}=require('../config/constants');const file_url=getImageUrl(img.file_path);return _objectSpread(_objectSpread({},img),{},{file_url});});setExistingImages(imagesWithUrls);}else{throw new Error(response.message||'Failed to fetch calendar images');}}catch(err){console.error('❌ Error fetching calendar images:',err);const errorMessage=err.message||'Failed to fetch calendar images';setError(errorMessage);// Call onError if it exists, but don't include it in dependencies to prevent infinite loop\nif(onError){onError(errorMessage);}}finally{setLoading(false);}},[calendarId]);// Remove onError from dependencies to prevent infinite loop\n// Upload new images\nconst uploadImages=useCallback(async files=>{if(!calendarId){throw new Error('Calendar ID is required for image upload');}if(!files||files.length===0){throw new Error('No files selected for upload');}try{setLoading(true);setError(null);console.log(\"\\uD83D\\uDCE4 Uploading \".concat(files.length,\" images for calendar event \").concat(calendarId));const formData=new FormData();files.forEach(file=>{formData.append('images',file);});const response=await calendarService.uploadEventAttachments(calendarId,formData);if(response.success){console.log(\"\\u2705 Successfully uploaded \".concat(files.length,\" images\"));if(onSuccess){onSuccess(\"Successfully uploaded \".concat(files.length,\" image(s)\"));}await refreshImages();// Refresh to show new images\n}else{throw new Error(response.message||'Failed to upload images');}}catch(err){console.error('❌ Error uploading calendar images:',err);const errorMessage=err.message||'Failed to upload images';setError(errorMessage);if(onError){onError(errorMessage);}throw err;}finally{setLoading(false);}},[calendarId,refreshImages]);// Remove onSuccess and onError from dependencies\n// Delete image\nconst deleteImage=useCallback(async attachmentId=>{try{setLoading(true);setError(null);console.log(\"\\uD83D\\uDDD1\\uFE0F Deleting calendar image \".concat(attachmentId));const response=await calendarService.deleteEventAttachment(attachmentId);if(response.success){console.log(\"\\u2705 Successfully deleted calendar image \".concat(attachmentId));if(onSuccess){onSuccess('Image deleted successfully');}await refreshImages();// Refresh to remove deleted image\n}else{throw new Error(response.message||'Failed to delete image');}}catch(err){console.error('❌ Error deleting calendar image:',err);const errorMessage=err.message||'Failed to delete image';setError(errorMessage);if(onError){onError(errorMessage);}throw err;}finally{setLoading(false);}},[refreshImages]);// Remove onSuccess and onError from dependencies\n// Set primary image\nconst setPrimaryImage=useCallback(async attachmentId=>{if(!calendarId){throw new Error('Calendar ID is required');}try{setLoading(true);setError(null);console.log(\"\\u2B50 Setting calendar image \".concat(attachmentId,\" as primary\"));const response=await calendarService.setPrimaryAttachment(calendarId,attachmentId);if(response.success){console.log(\"\\u2705 Successfully set calendar image \".concat(attachmentId,\" as primary\"));if(onSuccess){onSuccess('Primary image updated successfully');}await refreshImages();// Refresh to show updated primary status\n}else{throw new Error(response.message||'Failed to set primary image');}}catch(err){console.error('❌ Error setting primary calendar image:',err);const errorMessage=err.message||'Failed to set primary image';setError(errorMessage);if(onError){onError(errorMessage);}throw err;}finally{setLoading(false);}},[calendarId,refreshImages]);// Remove onSuccess and onError from dependencies\n// Mark image for deletion (pending)\nconst markForDeletion=useCallback(attachmentId=>{console.log('🏷️ Marking calendar image for deletion:',attachmentId);setPendingDeletes(prev=>{if(!prev.includes(attachmentId)){const newPending=[...prev,attachmentId];console.log('📋 Pending deletes updated:',newPending);return newPending;}return prev;});},[]);// Unmark image for deletion\nconst unmarkForDeletion=useCallback(attachmentId=>{console.log('🔄 Unmarking calendar image for deletion:',attachmentId);setPendingDeletes(prev=>{const newPending=prev.filter(id=>id!==attachmentId);console.log('📋 Updated pending deletes:',newPending);return newPending;});},[]);// Apply pending deletions\nconst applyPendingDeletes=useCallback(async()=>{if(pendingDeletes.length===0)return;try{console.log(\"\\uD83D\\uDDD1\\uFE0F Applying \".concat(pendingDeletes.length,\" pending calendar image deletions\"));// Delete images directly without calling deleteImage to avoid dependency issues\nfor(const attachmentId of pendingDeletes){try{setLoading(true);console.log(\"\\uD83D\\uDDD1\\uFE0F Deleting calendar image \".concat(attachmentId));const response=await calendarService.deleteEventAttachment(attachmentId);if(response.success){console.log(\"\\u2705 Successfully deleted calendar image \".concat(attachmentId));}else{throw new Error(response.message||'Failed to delete image');}}catch(err){console.error('❌ Error deleting calendar image:',err);throw err;}}setPendingDeletes([]);// Call refreshImages directly without including it in dependencies\nrefreshImages();console.log('✅ All pending calendar image deletions applied');}catch(err){console.error('❌ Error applying pending calendar image deletions:',err);throw err;}finally{setLoading(false);}},[pendingDeletes]);// Remove refreshImages from dependencies to prevent infinite loop\n// Fetch images when calendarId changes\nuseEffect(()=>{if(calendarId){refreshImages();}},[calendarId]);// Remove refreshImages from dependency array to prevent infinite loop\nreturn{existingImages,loading,error,uploadImages,deleteImage,setPrimaryImage,refreshImages,clearError,pendingDeletes,markForDeletion,unmarkForDeletion,applyPendingDeletes,clearPendingDeletes,clearAllImageState};};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}