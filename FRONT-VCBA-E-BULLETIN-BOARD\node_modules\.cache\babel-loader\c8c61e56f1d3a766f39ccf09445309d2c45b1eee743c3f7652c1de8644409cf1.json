{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z\",\n  key: \"4pj2yx\"\n}], [\"path\", {\n  d: \"M20 3v4\",\n  key: \"1olli1\"\n}], [\"path\", {\n  d: \"M22 5h-4\",\n  key: \"1gvqau\"\n}], [\"path\", {\n  d: \"M4 17v2\",\n  key: \"vumght\"\n}], [\"path\", {\n  d: \"M5 18H3\",\n  key: \"zchphs\"\n}]];\nconst Sparkles = createLucideIcon(\"sparkles\", __iconNode);\nexport { __iconNode, Sparkles as default };\n//# sourceMappingURL=sparkles.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}