{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"19\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"17f5cg\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"1gwv83\"\n}], [\"path\", {\n  d: \"M6.48 3.66a10 10 0 0 1 13.86 13.86\",\n  key: \"xr8kdq\"\n}], [\"path\", {\n  d: \"m6.41 6.41 11.18 11.18\",\n  key: \"uhpjw7\"\n}], [\"path\", {\n  d: \"M3.66 6.48a10 10 0 0 0 13.86 13.86\",\n  key: \"cldpwv\"\n}]];\nconst Diameter = createLucideIcon(\"diameter\", __iconNode);\nexport { __iconNode, Diameter as default };\n//# sourceMappingURL=diameter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}