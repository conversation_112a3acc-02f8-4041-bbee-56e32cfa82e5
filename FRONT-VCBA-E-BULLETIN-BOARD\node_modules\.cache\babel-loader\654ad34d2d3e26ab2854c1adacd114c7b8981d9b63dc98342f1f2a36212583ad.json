{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 12H3\",\n  key: \"8awo09\"\n}], [\"path\", {\n  d: \"m11 18 6-6-6-6\",\n  key: \"8c2y43\"\n}], [\"path\", {\n  d: \"M21 5v14\",\n  key: \"nzette\"\n}]];\nconst ArrowRightToLine = createLucideIcon(\"arrow-right-to-line\", __iconNode);\nexport { __iconNode, ArrowRightToLine as default };\n//# sourceMappingURL=arrow-right-to-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}