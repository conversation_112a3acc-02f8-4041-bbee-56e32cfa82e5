{"ast": null, "code": "import { httpClient } from './api.service';\n\n// Holiday types\n\n/**\n * Holiday Service for managing public holidays\n */\nclass HolidayService {\n  constructor() {\n    this.baseUrl = '/api/holidays';\n  }\n  /**\n   * Sync holidays for a specific year\n   */\n  async syncHolidays(year, forceUpdate = false) {\n    return httpClient.post(`${this.baseUrl}/sync`, {\n      year,\n      force_update: forceUpdate\n    });\n  }\n\n  /**\n   * Get holiday sync status\n   */\n  async getSyncStatus() {\n    try {\n      return await httpClient.get(`${this.baseUrl}/sync-status`);\n    } catch (error) {\n      console.error('Failed to get sync status:', error);\n      // Return a fallback response if authentication fails\n      return {\n        success: false,\n        message: 'Authentication required to access sync status',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidaysByYear(year) {\n    try {\n      return await httpClient.get(`${this.baseUrl}/${year}`);\n    } catch (error) {\n      console.error(`Failed to get holidays for year ${year}:`, error);\n      // Return a fallback response if authentication fails\n      return {\n        success: false,\n        message: 'Authentication required to access holiday data',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year\n   */\n  async deleteHolidaysByYear(year) {\n    return httpClient.delete(`${this.baseUrl}/${year}`);\n  }\n\n  /**\n   * Test holiday API connectivity\n   */\n  async testAPI() {\n    return httpClient.getPublic(`${this.baseUrl}/test-api`);\n  }\n\n  /**\n   * Get holiday configuration\n   */\n  async getConfig() {\n    return httpClient.getPublic(`${this.baseUrl}/config`);\n  }\n\n  /**\n   * Get current year holidays\n   */\n  async getCurrentYearHolidays() {\n    const currentYear = new Date().getFullYear();\n    return this.getHolidaysByYear(currentYear);\n  }\n\n  /**\n   * Get next year holidays\n   */\n  async getNextYearHolidays() {\n    const nextYear = new Date().getFullYear() + 1;\n    return this.getHolidaysByYear(nextYear);\n  }\n\n  /**\n   * Sync current and next year holidays\n   */\n  async syncCurrentAndNextYear(forceUpdate = false) {\n    const currentYear = new Date().getFullYear();\n    const nextYear = currentYear + 1;\n    const [currentYearResult, nextYearResult] = await Promise.all([this.syncHolidays(currentYear, forceUpdate), this.syncHolidays(nextYear, forceUpdate)]);\n    return {\n      currentYear: currentYearResult,\n      nextYear: nextYearResult\n    };\n  }\n\n  /**\n   * Check if holiday sync is needed\n   */\n  async isSyncNeeded() {\n    try {\n      const status = await this.getSyncStatus();\n      if (!status.success || !status.data) return true;\n      const currentYear = new Date().getFullYear();\n      const currentYearData = status.data.currentYear;\n\n      // Check if current year has no holidays\n      if (currentYearData.total_holidays === 0) {\n        return true;\n      }\n\n      // Check if last sync was more than 6 months ago\n      if (currentYearData.last_sync) {\n        const lastSync = new Date(currentYearData.last_sync);\n        const sixMonthsAgo = new Date();\n        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);\n        if (lastSync < sixMonthsAgo) {\n          return true;\n        }\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to check sync status:', error);\n      return true; // Assume sync is needed if we can't check\n    }\n  }\n}\nexport const holidayService = new HolidayService();\nexport default holidayService;", "map": {"version": 3, "names": ["httpClient", "HolidayService", "constructor", "baseUrl", "syncHolidays", "year", "forceUpdate", "post", "force_update", "getSyncStatus", "get", "error", "console", "success", "message", "data", "undefined", "getHolidaysByYear", "deleteHolidaysByYear", "delete", "testAPI", "getPublic", "getConfig", "getCurrentYearHolidays", "currentYear", "Date", "getFullYear", "getNextYearHolidays", "nextYear", "syncCurrentAndNextYear", "currentYearResult", "nextYearResult", "Promise", "all", "isSyncNeeded", "status", "currentYearData", "total_holidays", "last_sync", "lastSync", "sixMonthsAgo", "setMonth", "getMonth", "holidayService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/holidayService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport type { ApiResponse } from '../types';\n\n// Holiday types\nexport interface Holiday {\n  calendar_id: number;\n  title: string;\n  description: string;\n  event_date: string;\n  end_date: string;\n  is_holiday: boolean;\n  holiday_type: 'local' | 'international' | 'school';\n  country_code: string;\n  is_auto_generated: boolean;\n  api_source: string;\n  local_name: string;\n  is_global: boolean;\n  is_fixed: boolean;\n  is_active: boolean;\n  is_published: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface HolidaySyncResult {\n  year: number;\n  totalFetched: number;\n  totalFiltered: number;\n  totalCreated: number;\n  totalUpdated: number;\n  totalSkipped: number;\n  errors: string[];\n  holidays: Array<{\n    name: string;\n    date: string;\n    action: 'created' | 'updated' | 'skipped';\n    type: 'international' | 'philippine';\n  }>;\n}\n\nexport interface HolidaySyncStatus {\n  currentYear: {\n    year: number;\n    total_holidays: number;\n    international_count: number;\n    local_count: number;\n    last_sync: string | null;\n  };\n  nextYear: {\n    year: number;\n    total_holidays: number;\n    international_count: number;\n    local_count: number;\n    last_sync: string | null;\n  };\n  apiStatus: string;\n}\n\nexport interface HolidayConfig {\n  supportedCountries: Array<{\n    code: string;\n    name: string;\n    type: string;\n  }>;\n  globalHolidays: string[];\n  philippineHolidays: string[];\n  apiSource: string;\n  syncSettings: {\n    autoSync: boolean;\n    syncYearsAhead: number;\n    lastSync: string | null;\n  };\n}\n\n/**\n * Holiday Service for managing public holidays\n */\nclass HolidayService {\n  private baseUrl = '/api/holidays';\n\n  /**\n   * Sync holidays for a specific year\n   */\n  async syncHolidays(year: number, forceUpdate = false): Promise<ApiResponse<HolidaySyncResult>> {\n    return httpClient.post(`${this.baseUrl}/sync`, {\n      year,\n      force_update: forceUpdate\n    });\n  }\n\n  /**\n   * Get holiday sync status\n   */\n  async getSyncStatus(): Promise<ApiResponse<HolidaySyncStatus>> {\n    try {\n      return await httpClient.get(`${this.baseUrl}/sync-status`);\n    } catch (error) {\n      console.error('Failed to get sync status:', error);\n      // Return a fallback response if authentication fails\n      return {\n        success: false,\n        message: 'Authentication required to access sync status',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * Get holidays for a specific year\n   */\n  async getHolidaysByYear(year: number): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {\n    try {\n      return await httpClient.get(`${this.baseUrl}/${year}`);\n    } catch (error) {\n      console.error(`Failed to get holidays for year ${year}:`, error);\n      // Return a fallback response if authentication fails\n      return {\n        success: false,\n        message: 'Authentication required to access holiday data',\n        data: undefined\n      };\n    }\n  }\n\n  /**\n   * Delete auto-generated holidays for a specific year\n   */\n  async deleteHolidaysByYear(year: number): Promise<ApiResponse<{ year: number; deletedCount: number }>> {\n    return httpClient.delete(`${this.baseUrl}/${year}`);\n  }\n\n  /**\n   * Test holiday API connectivity\n   */\n  async testAPI(): Promise<ApiResponse<{\n    apiStatus: string;\n    year: number;\n    philippineHolidays: { total: number; filtered: number };\n    internationalHolidays: { total: number; filtered: number };\n    totalRelevantHolidays: number;\n  }>> {\n    return httpClient.getPublic(`${this.baseUrl}/test-api`);\n  }\n\n  /**\n   * Get holiday configuration\n   */\n  async getConfig(): Promise<ApiResponse<HolidayConfig>> {\n    return httpClient.getPublic(`${this.baseUrl}/config`);\n  }\n\n  /**\n   * Get current year holidays\n   */\n  async getCurrentYearHolidays(): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {\n    const currentYear = new Date().getFullYear();\n    return this.getHolidaysByYear(currentYear);\n  }\n\n  /**\n   * Get next year holidays\n   */\n  async getNextYearHolidays(): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {\n    const nextYear = new Date().getFullYear() + 1;\n    return this.getHolidaysByYear(nextYear);\n  }\n\n  /**\n   * Sync current and next year holidays\n   */\n  async syncCurrentAndNextYear(forceUpdate = false): Promise<{\n    currentYear: ApiResponse<HolidaySyncResult>;\n    nextYear: ApiResponse<HolidaySyncResult>;\n  }> {\n    const currentYear = new Date().getFullYear();\n    const nextYear = currentYear + 1;\n\n    const [currentYearResult, nextYearResult] = await Promise.all([\n      this.syncHolidays(currentYear, forceUpdate),\n      this.syncHolidays(nextYear, forceUpdate)\n    ]);\n\n    return {\n      currentYear: currentYearResult,\n      nextYear: nextYearResult\n    };\n  }\n\n  /**\n   * Check if holiday sync is needed\n   */\n  async isSyncNeeded(): Promise<boolean> {\n    try {\n      const status = await this.getSyncStatus();\n      if (!status.success || !status.data) return true;\n\n      const currentYear = new Date().getFullYear();\n      const currentYearData = status.data.currentYear;\n\n      // Check if current year has no holidays\n      if (currentYearData.total_holidays === 0) {\n        return true;\n      }\n\n      // Check if last sync was more than 6 months ago\n      if (currentYearData.last_sync) {\n        const lastSync = new Date(currentYearData.last_sync);\n        const sixMonthsAgo = new Date();\n        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);\n\n        if (lastSync < sixMonthsAgo) {\n          return true;\n        }\n      }\n\n      return false;\n    } catch (error) {\n      console.error('Failed to check sync status:', error);\n      return true; // Assume sync is needed if we can't check\n    }\n  }\n}\n\nexport const holidayService = new HolidayService();\nexport default holidayService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;;AAG1C;;AAuEA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,eAAe;EAAA;EAEjC;AACF;AACA;EACE,MAAMC,YAAYA,CAACC,IAAY,EAAEC,WAAW,GAAG,KAAK,EAA2C;IAC7F,OAAON,UAAU,CAACO,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,OAAO,EAAE;MAC7CE,IAAI;MACJG,YAAY,EAAEF;IAChB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMG,aAAaA,CAAA,EAA4C;IAC7D,IAAI;MACF,OAAO,MAAMT,UAAU,CAACU,GAAG,CAAC,GAAG,IAAI,CAACP,OAAO,cAAc,CAAC;IAC5D,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLE,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,+CAA+C;QACxDC,IAAI,EAAEC;MACR,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAACZ,IAAY,EAA8E;IAChH,IAAI;MACF,OAAO,MAAML,UAAU,CAACU,GAAG,CAAC,GAAG,IAAI,CAACP,OAAO,IAAIE,IAAI,EAAE,CAAC;IACxD,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCN,IAAI,GAAG,EAAEM,KAAK,CAAC;MAChE;MACA,OAAO;QACLE,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,gDAAgD;QACzDC,IAAI,EAAEC;MACR,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAME,oBAAoBA,CAACb,IAAY,EAAgE;IACrG,OAAOL,UAAU,CAACmB,MAAM,CAAC,GAAG,IAAI,CAAChB,OAAO,IAAIE,IAAI,EAAE,CAAC;EACrD;;EAEA;AACF;AACA;EACE,MAAMe,OAAOA,CAAA,EAMT;IACF,OAAOpB,UAAU,CAACqB,SAAS,CAAC,GAAG,IAAI,CAAClB,OAAO,WAAW,CAAC;EACzD;;EAEA;AACF;AACA;EACE,MAAMmB,SAASA,CAAA,EAAwC;IACrD,OAAOtB,UAAU,CAACqB,SAAS,CAAC,GAAG,IAAI,CAAClB,OAAO,SAAS,CAAC;EACvD;;EAEA;AACF;AACA;EACE,MAAMoB,sBAAsBA,CAAA,EAA+E;IACzG,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,OAAO,IAAI,CAACT,iBAAiB,CAACO,WAAW,CAAC;EAC5C;;EAEA;AACF;AACA;EACE,MAAMG,mBAAmBA,CAAA,EAA+E;IACtG,MAAMC,QAAQ,GAAG,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC;IAC7C,OAAO,IAAI,CAACT,iBAAiB,CAACW,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACE,MAAMC,sBAAsBA,CAACvB,WAAW,GAAG,KAAK,EAG7C;IACD,MAAMkB,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,MAAME,QAAQ,GAAGJ,WAAW,GAAG,CAAC;IAEhC,MAAM,CAACM,iBAAiB,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5D,IAAI,CAAC7B,YAAY,CAACoB,WAAW,EAAElB,WAAW,CAAC,EAC3C,IAAI,CAACF,YAAY,CAACwB,QAAQ,EAAEtB,WAAW,CAAC,CACzC,CAAC;IAEF,OAAO;MACLkB,WAAW,EAAEM,iBAAiB;MAC9BF,QAAQ,EAAEG;IACZ,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMG,YAAYA,CAAA,EAAqB;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAAC1B,aAAa,CAAC,CAAC;MACzC,IAAI,CAAC0B,MAAM,CAACtB,OAAO,IAAI,CAACsB,MAAM,CAACpB,IAAI,EAAE,OAAO,IAAI;MAEhD,MAAMS,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5C,MAAMU,eAAe,GAAGD,MAAM,CAACpB,IAAI,CAACS,WAAW;;MAE/C;MACA,IAAIY,eAAe,CAACC,cAAc,KAAK,CAAC,EAAE;QACxC,OAAO,IAAI;MACb;;MAEA;MACA,IAAID,eAAe,CAACE,SAAS,EAAE;QAC7B,MAAMC,QAAQ,GAAG,IAAId,IAAI,CAACW,eAAe,CAACE,SAAS,CAAC;QACpD,MAAME,YAAY,GAAG,IAAIf,IAAI,CAAC,CAAC;QAC/Be,YAAY,CAACC,QAAQ,CAACD,YAAY,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAElD,IAAIH,QAAQ,GAAGC,YAAY,EAAE;UAC3B,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI,CAAC,CAAC;IACf;EACF;AACF;AAEA,OAAO,MAAMgC,cAAc,GAAG,IAAI1C,cAAc,CAAC,CAAC;AAClD,eAAe0C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}