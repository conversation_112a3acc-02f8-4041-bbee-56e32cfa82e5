{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z\",\n  key: \"1fy3hk\"\n}]];\nconst Bookmark = createLucideIcon(\"bookmark\", __iconNode);\nexport { __iconNode, Bookmark as default };\n//# sourceMappingURL=bookmark.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}