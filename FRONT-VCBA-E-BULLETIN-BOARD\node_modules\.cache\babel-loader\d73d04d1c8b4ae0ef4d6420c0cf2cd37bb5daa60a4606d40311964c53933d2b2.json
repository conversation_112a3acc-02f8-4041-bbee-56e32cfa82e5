{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.54 15H17a2 2 0 0 0-2 2v4.54\",\n  key: \"1djwo0\"\n}], [\"path\", {\n  d: \"M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17\",\n  key: \"1tzkfa\"\n}], [\"path\", {\n  d: \"M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05\",\n  key: \"14pb5j\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst Earth = createLucideIcon(\"earth\", __iconNode);\nexport { __iconNode, Earth as default };\n//# sourceMappingURL=earth.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}