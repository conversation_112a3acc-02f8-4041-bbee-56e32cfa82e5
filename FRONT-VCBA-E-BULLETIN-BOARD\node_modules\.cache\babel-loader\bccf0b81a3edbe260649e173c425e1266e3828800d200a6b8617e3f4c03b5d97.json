{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 19.5 12 21l-7-7c-1.5-1.45-3-3.2-3-5.5A5.5 5.5 0 0 1 7.5 3c1.76 0 3 .5 4.5 2 1.5-1.5 2.74-2 4.5-2a5.5 5.5 0 0 1 5.402 6.5\",\n  key: \"vd0vy5\"\n}], [\"path\", {\n  d: \"M15 15h6\",\n  key: \"1u4692\"\n}], [\"path\", {\n  d: \"M18 12v6\",\n  key: \"1houu1\"\n}]];\nconst HeartPlus = createLucideIcon(\"heart-plus\", __iconNode);\nexport { __iconNode, HeartPlus as default };\n//# sourceMappingURL=heart-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}