{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\ToastContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport Toast from '../components/common/Toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToastContext = /*#__PURE__*/createContext(undefined);\nexport const useToast = () => {\n  _s();\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n_s(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ToastProvider = ({\n  children,\n  maxToasts = 5\n}) => {\n  _s2();\n  const [toasts, setToasts] = useState([]);\n  const generateId = useCallback(() => {\n    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }, []);\n  const showToast = useCallback(toast => {\n    var _toast$duration;\n    const id = generateId();\n    const newToast = {\n      ...toast,\n      id,\n      duration: (_toast$duration = toast.duration) !== null && _toast$duration !== void 0 ? _toast$duration : 4000\n    };\n    setToasts(prev => {\n      const updated = [newToast, ...prev];\n      // Limit the number of toasts\n      return updated.slice(0, maxToasts);\n    });\n  }, [generateId, maxToasts]);\n  const showSuccess = useCallback((title, message, duration) => {\n    showToast({\n      type: 'success',\n      title,\n      message,\n      duration\n    });\n  }, [showToast]);\n  const showError = useCallback((title, message, duration) => {\n    showToast({\n      type: 'error',\n      title,\n      message,\n      duration\n    });\n  }, [showToast]);\n  const showInfo = useCallback((title, message, duration) => {\n    showToast({\n      type: 'info',\n      title,\n      message,\n      duration\n    });\n  }, [showToast]);\n  const showWarning = useCallback((title, message, duration) => {\n    showToast({\n      type: 'warning',\n      title,\n      message,\n      duration\n    });\n  }, [showToast]);\n  const removeToast = useCallback(id => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n  const clearAllToasts = useCallback(() => {\n    setToasts([]);\n  }, []);\n  const contextValue = {\n    showToast,\n    showSuccess,\n    showError,\n    showInfo,\n    showWarning,\n    removeToast,\n    clearAllToasts\n  };\n  return /*#__PURE__*/_jsxDEV(ToastContext.Provider, {\n    value: contextValue,\n    children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '1rem',\n        right: '1rem',\n        zIndex: 9999,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '0.5rem',\n        pointerEvents: 'none'\n      },\n      children: toasts.map((toast, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          pointerEvents: 'auto',\n          transform: `translateY(${index * 10}px)`,\n          transition: 'transform 0.3s ease'\n        },\n        children: /*#__PURE__*/_jsxDEV(Toast, {\n          id: toast.id,\n          type: toast.type,\n          title: toast.title,\n          message: toast.message,\n          duration: toast.duration,\n          onClose: removeToast\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this)\n      }, toast.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s2(ToastProvider, \"TEBeA6c5QOr4EsOcA5jdVG99WoI=\");\n_c = ToastProvider;\nvar _c;\n$RefreshReg$(_c, \"ToastProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "Toast", "jsxDEV", "_jsxDEV", "ToastContext", "undefined", "useToast", "_s", "context", "Error", "ToastProvider", "children", "maxToasts", "_s2", "toasts", "setToasts", "generateId", "Date", "now", "Math", "random", "toString", "substr", "showToast", "toast", "_toast$duration", "id", "newToast", "duration", "prev", "updated", "slice", "showSuccess", "title", "message", "type", "showError", "showInfo", "showWarning", "removeToast", "filter", "clearAllToasts", "contextValue", "Provider", "value", "style", "position", "top", "right", "zIndex", "display", "flexDirection", "gap", "pointerEvents", "map", "index", "transform", "transition", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/ToastContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';\nimport Toast, { ToastType, ToastProps } from '../components/common/Toast';\n\ninterface ToastData {\n  id: string;\n  type: ToastType;\n  title: string;\n  message?: string;\n  duration?: number;\n}\n\ninterface ToastContextType {\n  showToast: (toast: Omit<ToastData, 'id'>) => void;\n  showSuccess: (title: string, message?: string, duration?: number) => void;\n  showError: (title: string, message?: string, duration?: number) => void;\n  showInfo: (title: string, message?: string, duration?: number) => void;\n  showWarning: (title: string, message?: string, duration?: number) => void;\n  removeToast: (id: string) => void;\n  clearAllToasts: () => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport const useToast = (): ToastContextType => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\ninterface ToastProviderProps {\n  children: ReactNode;\n  maxToasts?: number;\n}\n\nexport const ToastProvider: React.FC<ToastProviderProps> = ({ \n  children, \n  maxToasts = 5 \n}) => {\n  const [toasts, setToasts] = useState<ToastData[]>([]);\n\n  const generateId = useCallback(() => {\n    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n  }, []);\n\n  const showToast = useCallback((toast: Omit<ToastData, 'id'>) => {\n    const id = generateId();\n    const newToast: ToastData = {\n      ...toast,\n      id,\n      duration: toast.duration ?? 4000\n    };\n\n    setToasts(prev => {\n      const updated = [newToast, ...prev];\n      // Limit the number of toasts\n      return updated.slice(0, maxToasts);\n    });\n  }, [generateId, maxToasts]);\n\n  const showSuccess = useCallback((title: string, message?: string, duration?: number) => {\n    showToast({ type: 'success', title, message, duration });\n  }, [showToast]);\n\n  const showError = useCallback((title: string, message?: string, duration?: number) => {\n    showToast({ type: 'error', title, message, duration });\n  }, [showToast]);\n\n  const showInfo = useCallback((title: string, message?: string, duration?: number) => {\n    showToast({ type: 'info', title, message, duration });\n  }, [showToast]);\n\n  const showWarning = useCallback((title: string, message?: string, duration?: number) => {\n    showToast({ type: 'warning', title, message, duration });\n  }, [showToast]);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n\n  const clearAllToasts = useCallback(() => {\n    setToasts([]);\n  }, []);\n\n  const contextValue: ToastContextType = {\n    showToast,\n    showSuccess,\n    showError,\n    showInfo,\n    showWarning,\n    removeToast,\n    clearAllToasts\n  };\n\n  return (\n    <ToastContext.Provider value={contextValue}>\n      {children}\n      \n      {/* Toast Container */}\n      <div style={{\n        position: 'fixed',\n        top: '1rem',\n        right: '1rem',\n        zIndex: 9999,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '0.5rem',\n        pointerEvents: 'none'\n      }}>\n        {toasts.map((toast, index) => (\n          <div\n            key={toast.id}\n            style={{\n              pointerEvents: 'auto',\n              transform: `translateY(${index * 10}px)`,\n              transition: 'transform 0.3s ease'\n            }}\n          >\n            <Toast\n              id={toast.id}\n              type={toast.type}\n              title={toast.title}\n              message={toast.message}\n              duration={toast.duration}\n              onClose={removeToast}\n            />\n          </div>\n        ))}\n      </div>\n    </ToastContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAmB,OAAO;AAC1F,OAAOC,KAAK,MAAiC,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoB1E,MAAMC,YAAY,gBAAGP,aAAa,CAA+BQ,SAAS,CAAC;AAE3E,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAwB;EAAAC,EAAA;EAC9C,MAAMC,OAAO,GAAGV,UAAU,CAACM,YAAY,CAAC;EACxC,IAAI,CAACI,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAarB,OAAO,MAAMI,aAA2C,GAAGA,CAAC;EAC1DC,QAAQ;EACRC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAc,EAAE,CAAC;EAErD,MAAMiB,UAAU,GAAGhB,WAAW,CAAC,MAAM;IACnC,OAAO,SAASiB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACzE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,SAAS,GAAGvB,WAAW,CAAEwB,KAA4B,IAAK;IAAA,IAAAC,eAAA;IAC9D,MAAMC,EAAE,GAAGV,UAAU,CAAC,CAAC;IACvB,MAAMW,QAAmB,GAAG;MAC1B,GAAGH,KAAK;MACRE,EAAE;MACFE,QAAQ,GAAAH,eAAA,GAAED,KAAK,CAACI,QAAQ,cAAAH,eAAA,cAAAA,eAAA,GAAI;IAC9B,CAAC;IAEDV,SAAS,CAACc,IAAI,IAAI;MAChB,MAAMC,OAAO,GAAG,CAACH,QAAQ,EAAE,GAAGE,IAAI,CAAC;MACnC;MACA,OAAOC,OAAO,CAACC,KAAK,CAAC,CAAC,EAAEnB,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACI,UAAU,EAAEJ,SAAS,CAAC,CAAC;EAE3B,MAAMoB,WAAW,GAAGhC,WAAW,CAAC,CAACiC,KAAa,EAAEC,OAAgB,EAAEN,QAAiB,KAAK;IACtFL,SAAS,CAAC;MAAEY,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC,OAAO;MAAEN;IAAS,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,MAAMa,SAAS,GAAGpC,WAAW,CAAC,CAACiC,KAAa,EAAEC,OAAgB,EAAEN,QAAiB,KAAK;IACpFL,SAAS,CAAC;MAAEY,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC,OAAO;MAAEN;IAAS,CAAC,CAAC;EACxD,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,MAAMc,QAAQ,GAAGrC,WAAW,CAAC,CAACiC,KAAa,EAAEC,OAAgB,EAAEN,QAAiB,KAAK;IACnFL,SAAS,CAAC;MAAEY,IAAI,EAAE,MAAM;MAAEF,KAAK;MAAEC,OAAO;MAAEN;IAAS,CAAC,CAAC;EACvD,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,MAAMe,WAAW,GAAGtC,WAAW,CAAC,CAACiC,KAAa,EAAEC,OAAgB,EAAEN,QAAiB,KAAK;IACtFL,SAAS,CAAC;MAAEY,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC,OAAO;MAAEN;IAAS,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,MAAMgB,WAAW,GAAGvC,WAAW,CAAE0B,EAAU,IAAK;IAC9CX,SAAS,CAACc,IAAI,IAAIA,IAAI,CAACW,MAAM,CAAChB,KAAK,IAAIA,KAAK,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,cAAc,GAAGzC,WAAW,CAAC,MAAM;IACvCe,SAAS,CAAC,EAAE,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,YAA8B,GAAG;IACrCnB,SAAS;IACTS,WAAW;IACXI,SAAS;IACTC,QAAQ;IACRC,WAAW;IACXC,WAAW;IACXE;EACF,CAAC;EAED,oBACEtC,OAAA,CAACC,YAAY,CAACuC,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAA/B,QAAA,GACxCA,QAAQ,eAGTR,OAAA;MAAK0C,KAAK,EAAE;QACVC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,QAAQ;QACbC,aAAa,EAAE;MACjB,CAAE;MAAA1C,QAAA,EACCG,MAAM,CAACwC,GAAG,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,kBACvBpD,OAAA;QAEE0C,KAAK,EAAE;UACLQ,aAAa,EAAE,MAAM;UACrBG,SAAS,EAAE,cAAcD,KAAK,GAAG,EAAE,KAAK;UACxCE,UAAU,EAAE;QACd,CAAE;QAAA9C,QAAA,eAEFR,OAAA,CAACF,KAAK;UACJyB,EAAE,EAAEF,KAAK,CAACE,EAAG;UACbS,IAAI,EAAEX,KAAK,CAACW,IAAK;UACjBF,KAAK,EAAET,KAAK,CAACS,KAAM;UACnBC,OAAO,EAAEV,KAAK,CAACU,OAAQ;UACvBN,QAAQ,EAAEJ,KAAK,CAACI,QAAS;UACzB8B,OAAO,EAAEnB;QAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC,GAdGtC,KAAK,CAACE,EAAE;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE5B,CAAC;AAACjD,GAAA,CAhGWH,aAA2C;AAAAqD,EAAA,GAA3CrD,aAA2C;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}