{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\modals\\\\AnnouncementModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { useHierarchicalCategories } from '../../../hooks/useAnnouncements';\nimport { useMultipleImageUpload } from '../../../hooks/useMultipleImageUpload';\nimport MultipleImageUpload from '../MultipleImageUpload';\nimport CascadingCategoryDropdown from '../../common/CascadingCategoryDropdown';\nimport { createFormData, validateFormFields, announcementValidationRules } from '../../../utils/formUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnnouncementModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  announcement,\n  loading = false\n}) => {\n  _s();\n  const {\n    categories,\n    loading: categoriesLoading,\n    error: categoriesError\n  } = useHierarchicalCategories(); // Use public service (categories should be public)\n  const [formData, setFormData] = useState({\n    title: '',\n    content: '',\n    category_id: '',\n    subcategory_id: '',\n    status: 'draft',\n    is_pinned: false,\n    is_alert: false,\n    allow_comments: true,\n    allow_sharing: true,\n    scheduled_publish_at: ''\n  });\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [errors, setErrors] = useState({});\n  const [successMessage, setSuccessMessage] = useState(null);\n\n  // Debug categories loading\n  useEffect(() => {\n    console.log('🔍 AnnouncementModal - Categories state:', {\n      categories: (categories === null || categories === void 0 ? void 0 : categories.length) || 0,\n      categoriesLoading,\n      categoriesError,\n      categoriesData: categories\n    });\n  }, [categories, categoriesLoading, categoriesError]);\n\n  // Memoized validation using utility function\n  const validationErrors = useMemo(() => {\n    return validateFormFields(formData, {\n      ...announcementValidationRules,\n      custom: {\n        ...announcementValidationRules.custom,\n        scheduled_publish_at: value => announcementValidationRules.custom.scheduled_publish_at(value, formData)\n      }\n    });\n  }, [formData]);\n\n  // Multiple image upload hook\n  const {\n    existingImages,\n    loading: imageLoading,\n    error: imageError,\n    uploadImages,\n    setPrimaryImage,\n    refreshImages,\n    clearError: clearImageError,\n    // New pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  } = useMultipleImageUpload({\n    announcementId: announcement === null || announcement === void 0 ? void 0 : announcement.announcement_id,\n    onSuccess: message => setSuccessMessage(message),\n    onError: error => console.error('Image upload error:', error)\n  });\n\n  // Comprehensive reset function for new announcements\n  const resetModalForNewAnnouncement = useCallback(() => {\n    console.log('🧹 Resetting modal for new announcement');\n\n    // Reset form data to initial state\n    setFormData({\n      title: '',\n      content: '',\n      category_id: '',\n      subcategory_id: '',\n      status: 'draft',\n      is_pinned: false,\n      is_alert: false,\n      allow_comments: true,\n      allow_sharing: true,\n      scheduled_publish_at: ''\n    });\n\n    // Clear all images and image-related state\n    setSelectedImages([]);\n\n    // Clear all errors and messages\n    setErrors({});\n    setSuccessMessage(null);\n\n    // Clear image upload related state\n    clearImageError();\n    clearPendingDeletes();\n    clearAllImageState(); // This will clear existing images from previous announcements\n\n    console.log('✅ Modal reset complete - ready for new announcement');\n  }, [clearImageError, clearPendingDeletes, clearAllImageState]);\n\n  // Enhanced close handler that ensures everything is cleared\n  const handleClose = useCallback(() => {\n    console.log('🚪 Closing modal - clearing all data');\n\n    // Reset everything when closing\n    resetModalForNewAnnouncement();\n\n    // Call the parent's onClose\n    onClose();\n  }, [onClose, resetModalForNewAnnouncement]);\n\n  // Initialize form data when announcement changes\n  useEffect(() => {\n    if (announcement) {\n      var _announcement$categor, _announcement$subcate;\n      console.log('📝 Loading announcement for editing:', announcement.announcement_id);\n      console.log('📝 Raw boolean values from database:', {\n        is_pinned: announcement.is_pinned,\n        is_alert: announcement.is_alert,\n        allow_comments: announcement.allow_comments,\n        allow_sharing: announcement.allow_sharing\n      });\n      const convertedBooleans = {\n        is_pinned: Boolean(announcement.is_pinned),\n        is_alert: Boolean(announcement.is_alert),\n        allow_comments: Boolean(announcement.allow_comments),\n        allow_sharing: Boolean(announcement.allow_sharing)\n      };\n      console.log('📝 Converted boolean values for form:', convertedBooleans);\n      setFormData({\n        title: announcement.title || '',\n        content: announcement.content || '',\n        category_id: ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) || '',\n        subcategory_id: ((_announcement$subcate = announcement.subcategory_id) === null || _announcement$subcate === void 0 ? void 0 : _announcement$subcate.toString()) || '',\n        status: announcement.status || 'draft',\n        is_pinned: convertedBooleans.is_pinned,\n        is_alert: convertedBooleans.is_alert,\n        allow_comments: convertedBooleans.allow_comments,\n        allow_sharing: convertedBooleans.allow_sharing,\n        scheduled_publish_at: announcement.scheduled_publish_at || ''\n      });\n      // Clear selected images for editing\n      setSelectedImages([]);\n\n      // Load existing images for editing\n      if (announcement.announcement_id) {\n        refreshImages();\n      }\n    } else if (isOpen) {\n      // Modal is open for creating new announcement\n      console.log('🆕 Modal opened for new announcement - resetting all data');\n      resetModalForNewAnnouncement();\n    }\n\n    // Always clear errors and messages when modal state changes\n    setErrors({});\n    setSuccessMessage(null);\n    clearImageError();\n    clearPendingDeletes(); // Clear any pending deletions when modal opens/closes\n  }, [announcement === null || announcement === void 0 ? void 0 : announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement]);\n\n  // Handle Escape key to close modal and clear data\n  useEffect(() => {\n    const handleEscapeKey = event => {\n      if (event.key === 'Escape' && isOpen) {\n        handleClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      return () => document.removeEventListener('keydown', handleEscapeKey);\n    }\n  }, [isOpen, handleClose]);\n\n  // Optimized validation using memoized errors\n  const validateForm = useCallback(() => {\n    const newErrors = {\n      ...validationErrors\n    };\n\n    // Add additional validation rules\n    if (formData.title.length > 255) {\n      newErrors.title = 'Title must be less than 255 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  }, [validationErrors, formData.title]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      // Create completion callback for additional operations\n      const onComplete = async () => {\n        // If we're editing and have images, upload them separately\n        if (announcement && selectedImages.length > 0) {\n          try {\n            await uploadImages(selectedImages);\n            setSelectedImages([]); // Clear selected images after upload\n          } catch (uploadError) {\n            console.error('Error uploading additional images:', uploadError);\n            // Don't throw here as the main announcement was saved successfully\n          }\n        }\n\n        // Refresh images to show updates immediately\n        if (announcement !== null && announcement !== void 0 && announcement.announcement_id) {\n          await refreshImages();\n        }\n\n        // Clear pending deletes after successful update\n        clearPendingDeletes();\n      };\n\n      // Debug logging for form submission\n      console.log('🧪 Form submission data:', {\n        category_id: formData.category_id,\n        subcategory_id: formData.subcategory_id,\n        title: formData.title,\n        status: formData.status,\n        is_pinned: formData.is_pinned,\n        is_alert: formData.is_alert,\n        allow_comments: formData.allow_comments,\n        allow_sharing: formData.allow_sharing\n      });\n\n      // Determine if we need to send FormData (for files) or JSON (for text-only)\n      const hasFiles = selectedImages.length > 0;\n      let dataToSubmit;\n      if (hasFiles) {\n        // Create FormData for file uploads\n        dataToSubmit = createFormData(formData, selectedImages);\n        console.log('📤 Submitting with FormData (has files):', {\n          isEditing: !!announcement,\n          formData,\n          selectedImagesCount: selectedImages.length\n        });\n        console.log('📋 FormData entries being sent:');\n        const formDataEntries = [];\n        dataToSubmit.forEach((value, key) => {\n          formDataEntries.push(`  ${key}: ${value} (${typeof value})`);\n        });\n        console.log(formDataEntries.join('\\n'));\n      } else {\n        var _formData$title, _formData$content;\n        // Send as JSON for text-only updates\n        dataToSubmit = {\n          title: (_formData$title = formData.title) === null || _formData$title === void 0 ? void 0 : _formData$title.trim(),\n          content: (_formData$content = formData.content) === null || _formData$content === void 0 ? void 0 : _formData$content.trim(),\n          category_id: parseInt(formData.category_id),\n          subcategory_id: formData.subcategory_id ? parseInt(formData.subcategory_id) : null,\n          status: formData.status,\n          is_pinned: formData.is_pinned,\n          is_alert: formData.is_alert,\n          allow_comments: formData.allow_comments,\n          allow_sharing: formData.allow_sharing,\n          scheduled_publish_at: formData.status === 'scheduled' && formData.scheduled_publish_at ? formData.scheduled_publish_at : null\n        };\n        console.log('📤 Submitting with JSON (no files):', {\n          isEditing: !!announcement,\n          dataToSubmit\n        });\n      }\n\n      // Call onSave with completion callback - parent will handle modal closing and success message\n      await onSave(dataToSubmit, pendingDeletes.length > 0 ? applyPendingDeletes : undefined, onComplete);\n\n      // Parent component will handle:\n      // 1. Executing onComplete (image uploads)\n      // 2. Refreshing announcements list\n      // 3. Closing modal\n      // 4. Showing success message\n    } catch (error) {\n      console.error('Error saving announcement:', error);\n      setErrors({\n        submit: 'Failed to save announcement. Please try again.'\n      });\n    }\n  };\n\n  // Handle category selection\n  const handleCategoryChange = categoryId => {\n    console.log('🧪 AnnouncementModal - Category changed:', categoryId);\n    console.log('🧪 AnnouncementModal - Available categories:', categories === null || categories === void 0 ? void 0 : categories.map(cat => ({\n      id: cat.category_id,\n      name: cat.name\n    })));\n    setFormData(prev => ({\n      ...prev,\n      category_id: (categoryId === null || categoryId === void 0 ? void 0 : categoryId.toString()) || '',\n      subcategory_id: '' // Clear subcategory when category changes\n    }));\n\n    // Clear category error\n    if (errors.category_id) {\n      setErrors(prev => ({\n        ...prev,\n        category_id: ''\n      }));\n    }\n  };\n\n  // Handle subcategory selection\n  const handleSubcategoryChange = subcategoryId => {\n    console.log('🧪 Subcategory changed:', subcategoryId);\n    setFormData(prev => ({\n      ...prev,\n      subcategory_id: (subcategoryId === null || subcategoryId === void 0 ? void 0 : subcategoryId.toString()) || ''\n    }));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    if (type === 'checkbox') {\n      const checked = e.target.checked;\n      setFormData(prev => ({\n        ...prev,\n        [name]: checked\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        width: '100%',\n        maxWidth: '600px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '1.5rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: 0\n          },\n          children: announcement ? 'Edit Announcement' : 'Create New Announcement'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          style: {\n            background: 'none',\n            border: 'none',\n            fontSize: '1.5rem',\n            cursor: 'pointer',\n            color: '#6b7280',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Title *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"title\",\n            value: formData.title,\n            onChange: handleInputChange,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: `1px solid ${errors.title ? '#ef4444' : '#d1d5db'}`,\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease'\n            },\n            placeholder: \"Enter announcement title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ef4444',\n              fontSize: '0.75rem',\n              marginTop: '0.25rem'\n            },\n            children: errors.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Content *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"content\",\n            value: formData.content,\n            onChange: handleInputChange,\n            rows: 6,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: `1px solid ${errors.content ? '#ef4444' : '#d1d5db'}`,\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease',\n              resize: 'vertical'\n            },\n            placeholder: \"Enter announcement content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), errors.content && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ef4444',\n              fontSize: '0.75rem',\n              marginTop: '0.25rem'\n            },\n            children: errors.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Category *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), categoriesError ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.75rem',\n                backgroundColor: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '0.375rem',\n                color: '#dc2626',\n                fontSize: '0.875rem'\n              },\n              children: [\"Error loading categories: \", categoriesError, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => window.location.reload(),\n                style: {\n                  marginTop: '0.5rem',\n                  padding: '0.25rem 0.5rem',\n                  backgroundColor: '#dc2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.25rem',\n                  fontSize: '0.75rem',\n                  cursor: 'pointer'\n                },\n                children: \"Reload Page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this) : categoriesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.75rem',\n                backgroundColor: '#f0f9ff',\n                border: '1px solid #bae6fd',\n                borderRadius: '0.375rem',\n                color: '#0369a1',\n                fontSize: '0.875rem'\n              },\n              children: \"Loading categories...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this) : !categories || categories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.75rem',\n                backgroundColor: '#fffbeb',\n                border: '1px solid #fed7aa',\n                borderRadius: '0.375rem',\n                color: '#ea580c',\n                fontSize: '0.875rem'\n              },\n              children: \"No categories available. Please contact administrator.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(CascadingCategoryDropdown, {\n              categories: categories === null || categories === void 0 ? void 0 : categories.filter(category =>\n              // Hide holiday categories from announcement creation/editing\n              !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)),\n              selectedCategoryId: formData.category_id ? parseInt(formData.category_id) : undefined,\n              selectedSubcategoryId: formData.subcategory_id ? parseInt(formData.subcategory_id) : undefined,\n              onCategoryChange: handleCategoryChange,\n              onSubcategoryChange: handleSubcategoryChange,\n              placeholder: \"Select Category\",\n              required: true,\n              error: errors.category_id,\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"status\",\n              value: formData.status,\n              onChange: handleInputChange,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                backgroundColor: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"draft\",\n                children: \"Draft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"published\",\n                children: \"Published\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"scheduled\",\n                children: \"Scheduled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem',\n            backgroundColor: '#f0fdf4',\n            border: '1px solid #bbf7d0',\n            borderRadius: '8px',\n            color: '#166534',\n            fontSize: '0.875rem',\n            marginBottom: '1rem'\n          },\n          children: [\"\\u2713 \", successMessage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem',\n            padding: '16px',\n            backgroundColor: '#F8FAFC',\n            borderRadius: '8px',\n            border: '1px solid #E2E8F0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                fontSize: '1rem',\n                fontWeight: '600',\n                color: '#374151',\n                margin: 0\n              },\n              children: \"Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), pendingDeletes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem 0.75rem',\n                backgroundColor: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '6px',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), pendingDeletes.length, \" image\", pendingDeletes.length > 1 ? 's' : '', \" will be deleted\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MultipleImageUpload, {\n            onImagesChange: setSelectedImages,\n            existingImages: existingImages,\n            onSetPrimary: setPrimaryImage,\n            maxImages: 10,\n            disabled: imageLoading,\n            pendingDeletes: pendingDeletes,\n            onMarkForDeletion: markForDeletion,\n            onUnmarkForDeletion: unmarkForDeletion\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc2626',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: imageError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this), formData.status === 'scheduled' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Scheduled Publish Date *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            name: \"scheduled_publish_at\",\n            value: formData.scheduled_publish_at,\n            onChange: handleInputChange,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: `1px solid ${errors.scheduled_publish_at ? '#ef4444' : '#d1d5db'}`,\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 15\n          }, this), errors.scheduled_publish_at && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#ef4444',\n              fontSize: '0.75rem',\n              marginTop: '0.25rem'\n            },\n            children: errors.scheduled_publish_at\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_pinned\",\n                checked: formData.is_pinned,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), \"Pin this announcement\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_alert\",\n                checked: formData.is_alert,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), \"Mark as alert\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"allow_comments\",\n                checked: formData.allow_comments,\n                onChange: handleInputChange,\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this), \"Allow comments\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleClose,\n            style: {\n              padding: '0.75rem 1.5rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontWeight: '600',\n              fontSize: '0.875rem'\n            },\n            children: loading ? 'Saving...' : announcement ? 'Update' : 'Create'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s(AnnouncementModal, \"E4xS4BVD8HUREuy2KHrBESdgHBw=\", false, function () {\n  return [useHierarchicalCategories, useMultipleImageUpload];\n});\n_c = AnnouncementModal;\nexport default AnnouncementModal;\nvar _c;\n$RefreshReg$(_c, \"AnnouncementModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useHierarchicalCategories", "useMultipleImageUpload", "MultipleImageUpload", "CascadingCategoryDropdown", "createFormData", "validate<PERSON><PERSON><PERSON><PERSON>s", "announcementValidationRules", "jsxDEV", "_jsxDEV", "AnnouncementModal", "isOpen", "onClose", "onSave", "announcement", "loading", "_s", "categories", "categoriesLoading", "error", "categoriesError", "formData", "setFormData", "title", "content", "category_id", "subcategory_id", "status", "is_pinned", "is_alert", "allow_comments", "allow_sharing", "scheduled_publish_at", "selectedImages", "setSelectedImages", "errors", "setErrors", "successMessage", "setSuccessMessage", "console", "log", "length", "categoriesData", "validationErrors", "custom", "value", "existingImages", "imageLoading", "imageError", "uploadImages", "setPrimaryImage", "refreshImages", "clearError", "clearImageError", "pendingDeletes", "markForDeletion", "unmarkForDeletion", "applyPendingDeletes", "clearPendingDeletes", "clearAllImageState", "announcementId", "announcement_id", "onSuccess", "message", "onError", "resetModalForNewAnnouncement", "handleClose", "_announcement$categor", "_announcement$subcate", "convertedBooleans", "Boolean", "toString", "handleEscapeKey", "event", "key", "document", "addEventListener", "removeEventListener", "validateForm", "newErrors", "Object", "keys", "handleSubmit", "e", "preventDefault", "onComplete", "uploadError", "hasFiles", "dataToSubmit", "isEditing", "selectedImagesCount", "formDataEntries", "for<PERSON>ach", "push", "join", "_formData$title", "_formData$content", "trim", "parseInt", "undefined", "submit", "handleCategoryChange", "categoryId", "map", "cat", "id", "name", "prev", "handleSubcategoryChange", "subcategoryId", "handleInputChange", "type", "target", "checked", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "alignItems", "justifyContent", "zIndex", "padding", "children", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "marginBottom", "fontSize", "fontWeight", "color", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "background", "border", "cursor", "onSubmit", "onChange", "outline", "transition", "placeholder", "marginTop", "rows", "resize", "gridTemplateColumns", "gap", "window", "location", "reload", "filter", "category", "includes", "selectedCategoryId", "selectedSubcategoryId", "onCategoryChange", "onSubcategoryChange", "required", "disabled", "onImagesChange", "onSetPrimary", "maxImages", "onMarkForDeletion", "onUnmarkForDeletion", "marginRight", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/modals/AnnouncementModal.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { useHierarchicalCategories } from '../../../hooks/useAnnouncements';\nimport { useMultipleImageUpload } from '../../../hooks/useMultipleImageUpload';\nimport MultipleImageUpload from '../MultipleImageUpload';\nimport CascadingCategoryDropdown from '../../common/CascadingCategoryDropdown';\nimport { createFormData, validateFormFields, announcementValidationRules } from '../../../utils/formUtils';\nimport type { Announcement, CreateAnnouncementData, UpdateAnnouncementData } from '../../../types/announcement.types';\n\ninterface AnnouncementModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (data: CreateAnnouncementData | UpdateAnnouncementData | FormData, applyPendingDeletes?: () => Promise<void>, onComplete?: () => Promise<void>) => Promise<void>;\n  announcement?: Announcement | null;\n  loading?: boolean;\n}\n\nconst AnnouncementModal: React.FC<AnnouncementModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  announcement,\n  loading = false\n}) => {\n  const { categories, loading: categoriesLoading, error: categoriesError } = useHierarchicalCategories(); // Use public service (categories should be public)\n  const [formData, setFormData] = useState({\n    title: '',\n    content: '',\n    category_id: '',\n    subcategory_id: '',\n    status: 'draft' as 'draft' | 'published' | 'scheduled' | 'archived',\n    is_pinned: false,\n    is_alert: false,\n    allow_comments: true,\n    allow_sharing: true,\n    scheduled_publish_at: ''\n  });\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n\n  // Debug categories loading\n  useEffect(() => {\n    console.log('🔍 AnnouncementModal - Categories state:', {\n      categories: categories?.length || 0,\n      categoriesLoading,\n      categoriesError,\n      categoriesData: categories\n    });\n  }, [categories, categoriesLoading, categoriesError]);\n\n  // Memoized validation using utility function\n  const validationErrors = useMemo(() => {\n    return validateFormFields(formData, {\n      ...announcementValidationRules,\n      custom: {\n        ...announcementValidationRules.custom,\n        scheduled_publish_at: (value: any) =>\n          announcementValidationRules.custom.scheduled_publish_at(value, formData)\n      }\n    });\n  }, [formData]);\n\n  // Multiple image upload hook\n  const {\n    existingImages,\n    loading: imageLoading,\n    error: imageError,\n    uploadImages,\n\n\n    setPrimaryImage,\n    refreshImages,\n    clearError: clearImageError,\n    // New pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  } = useMultipleImageUpload({\n    announcementId: announcement?.announcement_id,\n    onSuccess: (message) => setSuccessMessage(message),\n    onError: (error) => console.error('Image upload error:', error)\n  });\n\n  // Comprehensive reset function for new announcements\n  const resetModalForNewAnnouncement = useCallback(() => {\n    console.log('🧹 Resetting modal for new announcement');\n\n    // Reset form data to initial state\n    setFormData({\n      title: '',\n      content: '',\n      category_id: '',\n      subcategory_id: '',\n      status: 'draft',\n      is_pinned: false,\n      is_alert: false,\n      allow_comments: true,\n      allow_sharing: true,\n      scheduled_publish_at: ''\n    });\n\n    // Clear all images and image-related state\n    setSelectedImages([]);\n\n    // Clear all errors and messages\n    setErrors({});\n    setSuccessMessage(null);\n\n    // Clear image upload related state\n    clearImageError();\n    clearPendingDeletes();\n    clearAllImageState(); // This will clear existing images from previous announcements\n\n    console.log('✅ Modal reset complete - ready for new announcement');\n  }, [clearImageError, clearPendingDeletes, clearAllImageState]);\n\n  // Enhanced close handler that ensures everything is cleared\n  const handleClose = useCallback(() => {\n    console.log('🚪 Closing modal - clearing all data');\n\n    // Reset everything when closing\n    resetModalForNewAnnouncement();\n\n    // Call the parent's onClose\n    onClose();\n  }, [onClose, resetModalForNewAnnouncement]);\n\n  // Initialize form data when announcement changes\n  useEffect(() => {\n    if (announcement) {\n      console.log('📝 Loading announcement for editing:', announcement.announcement_id);\n      console.log('📝 Raw boolean values from database:', {\n        is_pinned: announcement.is_pinned,\n        is_alert: announcement.is_alert,\n        allow_comments: announcement.allow_comments,\n        allow_sharing: announcement.allow_sharing\n      });\n\n      const convertedBooleans = {\n        is_pinned: Boolean(announcement.is_pinned),\n        is_alert: Boolean(announcement.is_alert),\n        allow_comments: Boolean(announcement.allow_comments),\n        allow_sharing: Boolean(announcement.allow_sharing)\n      };\n\n      console.log('📝 Converted boolean values for form:', convertedBooleans);\n\n      setFormData({\n        title: announcement.title || '',\n        content: announcement.content || '',\n        category_id: announcement.category_id?.toString() || '',\n        subcategory_id: announcement.subcategory_id?.toString() || '',\n        status: announcement.status || 'draft',\n        is_pinned: convertedBooleans.is_pinned,\n        is_alert: convertedBooleans.is_alert,\n        allow_comments: convertedBooleans.allow_comments,\n        allow_sharing: convertedBooleans.allow_sharing,\n        scheduled_publish_at: announcement.scheduled_publish_at || ''\n      });\n      // Clear selected images for editing\n      setSelectedImages([]);\n\n      // Load existing images for editing\n      if (announcement.announcement_id) {\n        refreshImages();\n      }\n    } else if (isOpen) {\n      // Modal is open for creating new announcement\n      console.log('🆕 Modal opened for new announcement - resetting all data');\n      resetModalForNewAnnouncement();\n    }\n\n    // Always clear errors and messages when modal state changes\n    setErrors({});\n    setSuccessMessage(null);\n    clearImageError();\n    clearPendingDeletes(); // Clear any pending deletions when modal opens/closes\n  }, [announcement?.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement]);\n\n  // Handle Escape key to close modal and clear data\n  useEffect(() => {\n    const handleEscapeKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        handleClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      return () => document.removeEventListener('keydown', handleEscapeKey);\n    }\n  }, [isOpen, handleClose]);\n\n  // Optimized validation using memoized errors\n  const validateForm = useCallback((): boolean => {\n    const newErrors = { ...validationErrors };\n\n    // Add additional validation rules\n    if (formData.title.length > 255) {\n      newErrors.title = 'Title must be less than 255 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  }, [validationErrors, formData.title]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      // Create completion callback for additional operations\n      const onComplete = async () => {\n        // If we're editing and have images, upload them separately\n        if (announcement && selectedImages.length > 0) {\n          try {\n            await uploadImages(selectedImages);\n            setSelectedImages([]); // Clear selected images after upload\n          } catch (uploadError) {\n            console.error('Error uploading additional images:', uploadError);\n            // Don't throw here as the main announcement was saved successfully\n          }\n        }\n\n        // Refresh images to show updates immediately\n        if (announcement?.announcement_id) {\n          await refreshImages();\n        }\n\n        // Clear pending deletes after successful update\n        clearPendingDeletes();\n      };\n\n      // Debug logging for form submission\n      console.log('🧪 Form submission data:', {\n        category_id: formData.category_id,\n        subcategory_id: formData.subcategory_id,\n        title: formData.title,\n        status: formData.status,\n        is_pinned: formData.is_pinned,\n        is_alert: formData.is_alert,\n        allow_comments: formData.allow_comments,\n        allow_sharing: formData.allow_sharing\n      });\n\n      // Determine if we need to send FormData (for files) or JSON (for text-only)\n      const hasFiles = selectedImages.length > 0;\n\n      let dataToSubmit: FormData | object;\n\n      if (hasFiles) {\n        // Create FormData for file uploads\n        dataToSubmit = createFormData(formData, selectedImages);\n        console.log('📤 Submitting with FormData (has files):', {\n          isEditing: !!announcement,\n          formData,\n          selectedImagesCount: selectedImages.length\n        });\n\n        console.log('📋 FormData entries being sent:');\n        const formDataEntries: string[] = [];\n        (dataToSubmit as FormData).forEach((value, key) => {\n          formDataEntries.push(`  ${key}: ${value} (${typeof value})`);\n        });\n        console.log(formDataEntries.join('\\n'));\n      } else {\n        // Send as JSON for text-only updates\n        dataToSubmit = {\n          title: formData.title?.trim(),\n          content: formData.content?.trim(),\n          category_id: parseInt(formData.category_id),\n          subcategory_id: formData.subcategory_id ? parseInt(formData.subcategory_id) : null,\n          status: formData.status,\n          is_pinned: formData.is_pinned,\n          is_alert: formData.is_alert,\n          allow_comments: formData.allow_comments,\n          allow_sharing: formData.allow_sharing,\n          scheduled_publish_at: formData.status === 'scheduled' && formData.scheduled_publish_at ? formData.scheduled_publish_at : null\n        };\n\n        console.log('📤 Submitting with JSON (no files):', {\n          isEditing: !!announcement,\n          dataToSubmit\n        });\n      }\n\n      // Call onSave with completion callback - parent will handle modal closing and success message\n      await onSave(\n        dataToSubmit,\n        pendingDeletes.length > 0 ? applyPendingDeletes : undefined,\n        onComplete\n      );\n\n      // Parent component will handle:\n      // 1. Executing onComplete (image uploads)\n      // 2. Refreshing announcements list\n      // 3. Closing modal\n      // 4. Showing success message\n    } catch (error) {\n      console.error('Error saving announcement:', error);\n      setErrors({ submit: 'Failed to save announcement. Please try again.' });\n    }\n  };\n\n  // Handle category selection\n  const handleCategoryChange = (categoryId: number | null) => {\n    console.log('🧪 AnnouncementModal - Category changed:', categoryId);\n    console.log('🧪 AnnouncementModal - Available categories:', categories?.map(cat => ({ id: cat.category_id, name: cat.name })));\n    setFormData(prev => ({\n      ...prev,\n      category_id: categoryId?.toString() || '',\n      subcategory_id: '' // Clear subcategory when category changes\n    }));\n\n    // Clear category error\n    if (errors.category_id) {\n      setErrors(prev => ({ ...prev, category_id: '' }));\n    }\n  };\n\n  // Handle subcategory selection\n  const handleSubcategoryChange = (subcategoryId: number | null) => {\n    console.log('🧪 Subcategory changed:', subcategoryId);\n    setFormData(prev => ({\n      ...prev,\n      subcategory_id: subcategoryId?.toString() || ''\n    }));\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n\n    if (type === 'checkbox') {\n      const checked = (e.target as HTMLInputElement).checked;\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    }}>\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        width: '100%',\n        maxWidth: '600px',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      }}>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        }}>\n          <h2 style={{\n            fontSize: '1.5rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: 0\n          }}>\n            {announcement ? 'Edit Announcement' : 'Create New Announcement'}\n          </h2>\n          <button\n            onClick={handleClose}\n            style={{\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280',\n              padding: '0.25rem'\n            }}\n          >\n            ×\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit}>\n          {/* Title */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.title ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease'\n              }}\n              placeholder=\"Enter announcement title\"\n            />\n            {errors.title && (\n              <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                {errors.title}\n              </p>\n            )}\n          </div>\n\n          {/* Content */}\n          <div style={{ marginBottom: '1rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              Content *\n            </label>\n            <textarea\n              name=\"content\"\n              value={formData.content}\n              onChange={handleInputChange}\n              rows={6}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.content ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease',\n                resize: 'vertical'\n              }}\n              placeholder=\"Enter announcement content\"\n            />\n            {errors.content && (\n              <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                {errors.content}\n              </p>\n            )}\n          </div>\n\n          {/* Category and Status Row */}\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1rem'\n          }}>\n            {/* Category */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Category *\n              </label>\n              {categoriesError ? (\n                <div style={{\n                  padding: '0.75rem',\n                  backgroundColor: '#fef2f2',\n                  border: '1px solid #fecaca',\n                  borderRadius: '0.375rem',\n                  color: '#dc2626',\n                  fontSize: '0.875rem'\n                }}>\n                  Error loading categories: {categoriesError}\n                  <br />\n                  <button\n                    type=\"button\"\n                    onClick={() => window.location.reload()}\n                    style={{\n                      marginTop: '0.5rem',\n                      padding: '0.25rem 0.5rem',\n                      backgroundColor: '#dc2626',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '0.25rem',\n                      fontSize: '0.75rem',\n                      cursor: 'pointer'\n                    }}\n                  >\n                    Reload Page\n                  </button>\n                </div>\n              ) : categoriesLoading ? (\n                <div style={{\n                  padding: '0.75rem',\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #bae6fd',\n                  borderRadius: '0.375rem',\n                  color: '#0369a1',\n                  fontSize: '0.875rem'\n                }}>\n                  Loading categories...\n                </div>\n              ) : !categories || categories.length === 0 ? (\n                <div style={{\n                  padding: '0.75rem',\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fed7aa',\n                  borderRadius: '0.375rem',\n                  color: '#ea580c',\n                  fontSize: '0.875rem'\n                }}>\n                  No categories available. Please contact administrator.\n                </div>\n              ) : (\n                <CascadingCategoryDropdown\n                  categories={categories?.filter(category =>\n                    // Hide holiday categories from announcement creation/editing\n                    !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                  )}\n                  selectedCategoryId={formData.category_id ? parseInt(formData.category_id) : undefined}\n                  selectedSubcategoryId={formData.subcategory_id ? parseInt(formData.subcategory_id) : undefined}\n                  onCategoryChange={handleCategoryChange}\n                  onSubcategoryChange={handleSubcategoryChange}\n                  placeholder=\"Select Category\"\n                  required={true}\n                  error={errors.category_id}\n                  disabled={loading}\n                />\n              )}\n            </div>\n\n            {/* Status */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Status\n              </label>\n              <select\n                name=\"status\"\n                value={formData.status}\n                onChange={handleInputChange}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  backgroundColor: 'white'\n                }}\n              >\n                <option value=\"draft\">Draft</option>\n                <option value=\"published\">Published</option>\n                <option value=\"scheduled\">Scheduled</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Success Message */}\n          {successMessage && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '0.75rem',\n              backgroundColor: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              fontSize: '0.875rem',\n              marginBottom: '1rem'\n            }}>\n              ✓ {successMessage}\n            </div>\n          )}\n\n\n\n          {/* Image Upload */}\n          <div style={{\n            marginBottom: '1.5rem',\n            padding: '16px',\n            backgroundColor: '#F8FAFC',\n            borderRadius: '8px',\n            border: '1px solid #E2E8F0'\n          }}>\n            <div style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            }}>\n              <h4 style={{\n                fontSize: '1rem',\n                fontWeight: '600',\n                color: '#374151',\n                margin: 0\n              }}>\n                Images\n              </h4>\n              {pendingDeletes.length > 0 && (\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 0.75rem',\n                  backgroundColor: '#fef2f2',\n                  border: '1px solid #fecaca',\n                  borderRadius: '6px',\n                  color: '#dc2626',\n                  fontSize: '0.875rem',\n                  fontWeight: '500'\n                }}>\n                  <span>⚠️</span>\n                  {pendingDeletes.length} image{pendingDeletes.length > 1 ? 's' : ''} will be deleted\n                </div>\n              )}\n            </div>\n            <MultipleImageUpload\n              onImagesChange={setSelectedImages}\n              existingImages={existingImages}\n              onSetPrimary={setPrimaryImage}\n              maxImages={10}\n              disabled={imageLoading}\n              pendingDeletes={pendingDeletes}\n              onMarkForDeletion={markForDeletion}\n              onUnmarkForDeletion={unmarkForDeletion}\n            />\n            {imageError && (\n              <div style={{\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                marginTop: '0.5rem'\n              }}>\n                {imageError}\n              </div>\n            )}\n          </div>\n\n          {/* Scheduled Publish Date (only show if status is scheduled) */}\n          {formData.status === 'scheduled' && (\n            <div style={{ marginBottom: '1rem' }}>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Scheduled Publish Date *\n              </label>\n              <input\n                type=\"datetime-local\"\n                name=\"scheduled_publish_at\"\n                value={formData.scheduled_publish_at}\n                onChange={handleInputChange}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.scheduled_publish_at ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  fontSize: '0.875rem',\n                  outline: 'none'\n                }}\n              />\n              {errors.scheduled_publish_at && (\n                <p style={{ color: '#ef4444', fontSize: '0.75rem', marginTop: '0.25rem' }}>\n                  {errors.scheduled_publish_at}\n                </p>\n              )}\n            </div>\n          )}\n\n          {/* Options */}\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1rem',\n            marginBottom: '1.5rem'\n          }}>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"is_pinned\"\n                  checked={formData.is_pinned}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Pin this announcement\n              </label>\n            </div>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"is_alert\"\n                  checked={formData.is_alert}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Mark as alert\n              </label>\n            </div>\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"allow_comments\"\n                  checked={formData.allow_comments}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Allow comments\n              </label>\n            </div>\n            {/* I will comment \"Allow sharing\" for the mean time and I will uncomment this in the future */}\n            {/* <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                fontSize: '0.875rem',\n                color: '#374151',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  name=\"allow_sharing\"\n                  checked={formData.allow_sharing}\n                  onChange={handleInputChange}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Allow sharing\n              </label>\n            </div> */}\n          </div>\n\n          {/* Action Buttons */}\n          <div style={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem'\n          }}>\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              style={{\n                padding: '0.75rem 1.5rem',\n                backgroundColor: '#f3f4f6',\n                color: '#374151',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                fontWeight: '500',\n                fontSize: '0.875rem'\n              }}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontWeight: '600',\n                fontSize: '0.875rem'\n              }}\n            >\n              {loading ? 'Saving...' : (announcement ? 'Update' : 'Create')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AnnouncementModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,yBAAyB,MAAM,wCAAwC;AAC9E,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,2BAA2B,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW3G,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,MAAM;EACNC,OAAO;EACPC,MAAM;EACNC,YAAY;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,UAAU;IAAEF,OAAO,EAAEG,iBAAiB;IAAEC,KAAK,EAAEC;EAAgB,CAAC,GAAGnB,yBAAyB,CAAC,CAAC,CAAC,CAAC;EACxG,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,MAAM,EAAE,OAA2D;IACnEC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAgB,IAAI,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACdyC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDvB,UAAU,EAAE,CAAAA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,MAAM,KAAI,CAAC;MACnCvB,iBAAiB;MACjBE,eAAe;MACfsB,cAAc,EAAEzB;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,UAAU,EAAEC,iBAAiB,EAAEE,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAMuB,gBAAgB,GAAG3C,OAAO,CAAC,MAAM;IACrC,OAAOM,kBAAkB,CAACe,QAAQ,EAAE;MAClC,GAAGd,2BAA2B;MAC9BqC,MAAM,EAAE;QACN,GAAGrC,2BAA2B,CAACqC,MAAM;QACrCZ,oBAAoB,EAAGa,KAAU,IAC/BtC,2BAA2B,CAACqC,MAAM,CAACZ,oBAAoB,CAACa,KAAK,EAAExB,QAAQ;MAC3E;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM;IACJyB,cAAc;IACd/B,OAAO,EAAEgC,YAAY;IACrB5B,KAAK,EAAE6B,UAAU;IACjBC,YAAY;IAGZC,eAAe;IACfC,aAAa;IACbC,UAAU,EAAEC,eAAe;IAC3B;IACAC,cAAc;IACdC,eAAe;IACfC,iBAAiB;IACjBC,mBAAmB;IACnBC,mBAAmB;IACnB;IACAC;EACF,CAAC,GAAGzD,sBAAsB,CAAC;IACzB0D,cAAc,EAAE9C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE+C,eAAe;IAC7CC,SAAS,EAAGC,OAAO,IAAKzB,iBAAiB,CAACyB,OAAO,CAAC;IAClDC,OAAO,EAAG7C,KAAK,IAAKoB,OAAO,CAACpB,KAAK,CAAC,qBAAqB,EAAEA,KAAK;EAChE,CAAC,CAAC;;EAEF;EACA,MAAM8C,4BAA4B,GAAGlE,WAAW,CAAC,MAAM;IACrDwC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;IAEtD;IACAlB,WAAW,CAAC;MACVC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,OAAO;MACfC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,IAAI;MACnBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;;IAEF;IACAE,iBAAiB,CAAC,EAAE,CAAC;;IAErB;IACAE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACAe,eAAe,CAAC,CAAC;IACjBK,mBAAmB,CAAC,CAAC;IACrBC,kBAAkB,CAAC,CAAC,CAAC,CAAC;;IAEtBpB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EACpE,CAAC,EAAE,CAACa,eAAe,EAAEK,mBAAmB,EAAEC,kBAAkB,CAAC,CAAC;;EAE9D;EACA,MAAMO,WAAW,GAAGnE,WAAW,CAAC,MAAM;IACpCwC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAEnD;IACAyB,4BAA4B,CAAC,CAAC;;IAE9B;IACArD,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACA,OAAO,EAAEqD,4BAA4B,CAAC,CAAC;;EAE3C;EACAnE,SAAS,CAAC,MAAM;IACd,IAAIgB,YAAY,EAAE;MAAA,IAAAqD,qBAAA,EAAAC,qBAAA;MAChB7B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE1B,YAAY,CAAC+C,eAAe,CAAC;MACjFtB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClDZ,SAAS,EAAEd,YAAY,CAACc,SAAS;QACjCC,QAAQ,EAAEf,YAAY,CAACe,QAAQ;QAC/BC,cAAc,EAAEhB,YAAY,CAACgB,cAAc;QAC3CC,aAAa,EAAEjB,YAAY,CAACiB;MAC9B,CAAC,CAAC;MAEF,MAAMsC,iBAAiB,GAAG;QACxBzC,SAAS,EAAE0C,OAAO,CAACxD,YAAY,CAACc,SAAS,CAAC;QAC1CC,QAAQ,EAAEyC,OAAO,CAACxD,YAAY,CAACe,QAAQ,CAAC;QACxCC,cAAc,EAAEwC,OAAO,CAACxD,YAAY,CAACgB,cAAc,CAAC;QACpDC,aAAa,EAAEuC,OAAO,CAACxD,YAAY,CAACiB,aAAa;MACnD,CAAC;MAEDQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE6B,iBAAiB,CAAC;MAEvE/C,WAAW,CAAC;QACVC,KAAK,EAAET,YAAY,CAACS,KAAK,IAAI,EAAE;QAC/BC,OAAO,EAAEV,YAAY,CAACU,OAAO,IAAI,EAAE;QACnCC,WAAW,EAAE,EAAA0C,qBAAA,GAAArD,YAAY,CAACW,WAAW,cAAA0C,qBAAA,uBAAxBA,qBAAA,CAA0BI,QAAQ,CAAC,CAAC,KAAI,EAAE;QACvD7C,cAAc,EAAE,EAAA0C,qBAAA,GAAAtD,YAAY,CAACY,cAAc,cAAA0C,qBAAA,uBAA3BA,qBAAA,CAA6BG,QAAQ,CAAC,CAAC,KAAI,EAAE;QAC7D5C,MAAM,EAAEb,YAAY,CAACa,MAAM,IAAI,OAAO;QACtCC,SAAS,EAAEyC,iBAAiB,CAACzC,SAAS;QACtCC,QAAQ,EAAEwC,iBAAiB,CAACxC,QAAQ;QACpCC,cAAc,EAAEuC,iBAAiB,CAACvC,cAAc;QAChDC,aAAa,EAAEsC,iBAAiB,CAACtC,aAAa;QAC9CC,oBAAoB,EAAElB,YAAY,CAACkB,oBAAoB,IAAI;MAC7D,CAAC,CAAC;MACF;MACAE,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,IAAIpB,YAAY,CAAC+C,eAAe,EAAE;QAChCV,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,MAAM,IAAIxC,MAAM,EAAE;MACjB;MACA4B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEyB,4BAA4B,CAAC,CAAC;IAChC;;IAEA;IACA7B,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;IACvBe,eAAe,CAAC,CAAC;IACjBK,mBAAmB,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC5C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE+C,eAAe,EAAElD,MAAM,EAAE0C,eAAe,EAAEK,mBAAmB,EAAEO,4BAA4B,CAAC,CAAC;;EAE/G;EACAnE,SAAS,CAAC,MAAM;IACd,MAAM0E,eAAe,GAAIC,KAAoB,IAAK;MAChD,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAI/D,MAAM,EAAE;QACpCuD,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAED,IAAIvD,MAAM,EAAE;MACVgE,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,eAAe,CAAC;MACrD,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEL,eAAe,CAAC;IACvE;EACF,CAAC,EAAE,CAAC7D,MAAM,EAAEuD,WAAW,CAAC,CAAC;;EAEzB;EACA,MAAMY,YAAY,GAAG/E,WAAW,CAAC,MAAe;IAC9C,MAAMgF,SAAS,GAAG;MAAE,GAAGpC;IAAiB,CAAC;;IAEzC;IACA,IAAItB,QAAQ,CAACE,KAAK,CAACkB,MAAM,GAAG,GAAG,EAAE;MAC/BsC,SAAS,CAACxD,KAAK,GAAG,wCAAwC;IAC5D;IAEAa,SAAS,CAAC2C,SAAS,CAAC;IACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACtC,MAAM,KAAK,CAAC;EAC5C,CAAC,EAAE,CAACE,gBAAgB,EAAEtB,QAAQ,CAACE,KAAK,CAAC,CAAC;EAEtC,MAAM2D,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF;MACA,MAAMO,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B;QACA,IAAIvE,YAAY,IAAImB,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC7C,IAAI;YACF,MAAMQ,YAAY,CAAChB,cAAc,CAAC;YAClCC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,OAAOoD,WAAW,EAAE;YACpB/C,OAAO,CAACpB,KAAK,CAAC,oCAAoC,EAAEmE,WAAW,CAAC;YAChE;UACF;QACF;;QAEA;QACA,IAAIxE,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE+C,eAAe,EAAE;UACjC,MAAMV,aAAa,CAAC,CAAC;QACvB;;QAEA;QACAO,mBAAmB,CAAC,CAAC;MACvB,CAAC;;MAED;MACAnB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;QACtCf,WAAW,EAAEJ,QAAQ,CAACI,WAAW;QACjCC,cAAc,EAAEL,QAAQ,CAACK,cAAc;QACvCH,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBI,MAAM,EAAEN,QAAQ,CAACM,MAAM;QACvBC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;QAC3BC,cAAc,EAAET,QAAQ,CAACS,cAAc;QACvCC,aAAa,EAAEV,QAAQ,CAACU;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMwD,QAAQ,GAAGtD,cAAc,CAACQ,MAAM,GAAG,CAAC;MAE1C,IAAI+C,YAA+B;MAEnC,IAAID,QAAQ,EAAE;QACZ;QACAC,YAAY,GAAGnF,cAAc,CAACgB,QAAQ,EAAEY,cAAc,CAAC;QACvDM,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;UACtDiD,SAAS,EAAE,CAAC,CAAC3E,YAAY;UACzBO,QAAQ;UACRqE,mBAAmB,EAAEzD,cAAc,CAACQ;QACtC,CAAC,CAAC;QAEFF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,MAAMmD,eAAyB,GAAG,EAAE;QACnCH,YAAY,CAAcI,OAAO,CAAC,CAAC/C,KAAK,EAAE6B,GAAG,KAAK;UACjDiB,eAAe,CAACE,IAAI,CAAC,KAAKnB,GAAG,KAAK7B,KAAK,KAAK,OAAOA,KAAK,GAAG,CAAC;QAC9D,CAAC,CAAC;QACFN,OAAO,CAACC,GAAG,CAACmD,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;MACzC,CAAC,MAAM;QAAA,IAAAC,eAAA,EAAAC,iBAAA;QACL;QACAR,YAAY,GAAG;UACbjE,KAAK,GAAAwE,eAAA,GAAE1E,QAAQ,CAACE,KAAK,cAAAwE,eAAA,uBAAdA,eAAA,CAAgBE,IAAI,CAAC,CAAC;UAC7BzE,OAAO,GAAAwE,iBAAA,GAAE3E,QAAQ,CAACG,OAAO,cAAAwE,iBAAA,uBAAhBA,iBAAA,CAAkBC,IAAI,CAAC,CAAC;UACjCxE,WAAW,EAAEyE,QAAQ,CAAC7E,QAAQ,CAACI,WAAW,CAAC;UAC3CC,cAAc,EAAEL,QAAQ,CAACK,cAAc,GAAGwE,QAAQ,CAAC7E,QAAQ,CAACK,cAAc,CAAC,GAAG,IAAI;UAClFC,MAAM,EAAEN,QAAQ,CAACM,MAAM;UACvBC,SAAS,EAAEP,QAAQ,CAACO,SAAS;UAC7BC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;UAC3BC,cAAc,EAAET,QAAQ,CAACS,cAAc;UACvCC,aAAa,EAAEV,QAAQ,CAACU,aAAa;UACrCC,oBAAoB,EAAEX,QAAQ,CAACM,MAAM,KAAK,WAAW,IAAIN,QAAQ,CAACW,oBAAoB,GAAGX,QAAQ,CAACW,oBAAoB,GAAG;QAC3H,CAAC;QAEDO,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDiD,SAAS,EAAE,CAAC,CAAC3E,YAAY;UACzB0E;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM3E,MAAM,CACV2E,YAAY,EACZlC,cAAc,CAACb,MAAM,GAAG,CAAC,GAAGgB,mBAAmB,GAAG0C,SAAS,EAC3Dd,UACF,CAAC;;MAED;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDiB,SAAS,CAAC;QAAEgE,MAAM,EAAE;MAAiD,CAAC,CAAC;IACzE;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,UAAyB,IAAK;IAC1D/D,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8D,UAAU,CAAC;IACnE/D,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEvB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEsF,GAAG,CAACC,GAAG,KAAK;MAAEC,EAAE,EAAED,GAAG,CAAC/E,WAAW;MAAEiF,IAAI,EAAEF,GAAG,CAACE;IAAK,CAAC,CAAC,CAAC,CAAC;IAC9HpF,WAAW,CAACqF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPlF,WAAW,EAAE,CAAA6E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE/B,QAAQ,CAAC,CAAC,KAAI,EAAE;MACzC7C,cAAc,EAAE,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIS,MAAM,CAACV,WAAW,EAAE;MACtBW,SAAS,CAACuE,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElF,WAAW,EAAE;MAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMmF,uBAAuB,GAAIC,aAA4B,IAAK;IAChEtE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqE,aAAa,CAAC;IACrDvF,WAAW,CAACqF,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjF,cAAc,EAAE,CAAAmF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEtC,QAAQ,CAAC,CAAC,KAAI;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuC,iBAAiB,GAAI3B,CAAgF,IAAK;IAC9G,MAAM;MAAEuB,IAAI;MAAE7D,KAAK;MAAEkE;IAAK,CAAC,GAAG5B,CAAC,CAAC6B,MAAM;IAEtC,IAAID,IAAI,KAAK,UAAU,EAAE;MACvB,MAAME,OAAO,GAAI9B,CAAC,CAAC6B,MAAM,CAAsBC,OAAO;MACtD3F,WAAW,CAACqF,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAGO;MAAQ,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM;MACL3F,WAAW,CAACqF,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAG7D;MAAM,CAAC,CAAC,CAAC;IACnD;;IAEA;IACA,IAAIV,MAAM,CAACuE,IAAI,CAAC,EAAE;MAChBtE,SAAS,CAACuE,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,IAAI,CAAC/F,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKyG,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,oBAAoB;MACrCC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACArH,OAAA;MAAKyG,KAAK,EAAE;QACVM,eAAe,EAAE,OAAO;QACxBO,YAAY,EAAE,MAAM;QACpBF,OAAO,EAAE,MAAM;QACfG,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBACArH,OAAA;QAAKyG,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBW,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACArH,OAAA;UAAIyG,KAAK,EAAE;YACToB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE;UACV,CAAE;UAAAX,QAAA,EACChH,YAAY,GAAG,mBAAmB,GAAG;QAAyB;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACLpI,OAAA;UACEqI,OAAO,EAAE5E,WAAY;UACrBgD,KAAK,EAAE;YACL6B,UAAU,EAAE,MAAM;YAClBC,MAAM,EAAE,MAAM;YACdV,QAAQ,EAAE,QAAQ;YAClBW,MAAM,EAAE,SAAS;YACjBT,KAAK,EAAE,SAAS;YAChBX,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpI,OAAA;QAAMyI,QAAQ,EAAEhE,YAAa;QAAA4C,QAAA,gBAE3BrH,OAAA;UAAKyG,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrH,OAAA;YAAOyG,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRpI,OAAA;YACEsG,IAAI,EAAC,MAAM;YACXL,IAAI,EAAC,OAAO;YACZ7D,KAAK,EAAExB,QAAQ,CAACE,KAAM;YACtB4H,QAAQ,EAAErC,iBAAkB;YAC5BI,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBmB,MAAM,EAAE,aAAa7G,MAAM,CAACZ,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;cAC3DwG,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAC;UAA0B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,EACD1G,MAAM,CAACZ,KAAK,iBACXd,OAAA;YAAGyG,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEiB,SAAS,EAAE;YAAU,CAAE;YAAAzB,QAAA,EACvE3F,MAAM,CAACZ;UAAK;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpI,OAAA;UAAKyG,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrH,OAAA;YAAOyG,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRpI,OAAA;YACEiG,IAAI,EAAC,SAAS;YACd7D,KAAK,EAAExB,QAAQ,CAACG,OAAQ;YACxB2H,QAAQ,EAAErC,iBAAkB;YAC5B0C,IAAI,EAAE,CAAE;YACRtC,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBmB,MAAM,EAAE,aAAa7G,MAAM,CAACX,OAAO,GAAG,SAAS,GAAG,SAAS,EAAE;cAC7DuG,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,wBAAwB;cACpCI,MAAM,EAAE;YACV,CAAE;YACFH,WAAW,EAAC;UAA4B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACD1G,MAAM,CAACX,OAAO,iBACbf,OAAA;YAAGyG,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEiB,SAAS,EAAE;YAAU,CAAE;YAAAzB,QAAA,EACvE3F,MAAM,CAACX;UAAO;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpI,OAAA;UAAKyG,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfiC,mBAAmB,EAAE,SAAS;YAC9BC,GAAG,EAAE,MAAM;YACXtB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBAEArH,OAAA;YAAAqH,QAAA,gBACErH,OAAA;cAAOyG,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBa,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBH,YAAY,EAAE;cAChB,CAAE;cAAAP,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPzH,eAAe,gBACdX,OAAA;cAAKyG,KAAK,EAAE;gBACVW,OAAO,EAAE,SAAS;gBAClBL,eAAe,EAAE,SAAS;gBAC1BwB,MAAM,EAAE,mBAAmB;gBAC3BjB,YAAY,EAAE,UAAU;gBACxBS,KAAK,EAAE,SAAS;gBAChBF,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,GAAC,4BACyB,EAAC1G,eAAe,eAC1CX,OAAA;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpI,OAAA;gBACEsG,IAAI,EAAC,QAAQ;gBACb+B,OAAO,EAAEA,CAAA,KAAMc,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;gBACxC5C,KAAK,EAAE;kBACLqC,SAAS,EAAE,QAAQ;kBACnB1B,OAAO,EAAE,gBAAgB;kBACzBL,eAAe,EAAE,SAAS;kBAC1BgB,KAAK,EAAE,OAAO;kBACdQ,MAAM,EAAE,MAAM;kBACdjB,YAAY,EAAE,SAAS;kBACvBO,QAAQ,EAAE,SAAS;kBACnBW,MAAM,EAAE;gBACV,CAAE;gBAAAnB,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,GACJ3H,iBAAiB,gBACnBT,OAAA;cAAKyG,KAAK,EAAE;gBACVW,OAAO,EAAE,SAAS;gBAClBL,eAAe,EAAE,SAAS;gBAC1BwB,MAAM,EAAE,mBAAmB;gBAC3BjB,YAAY,EAAE,UAAU;gBACxBS,KAAK,EAAE,SAAS;gBAChBF,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACJ,CAAC5H,UAAU,IAAIA,UAAU,CAACwB,MAAM,KAAK,CAAC,gBACxChC,OAAA;cAAKyG,KAAK,EAAE;gBACVW,OAAO,EAAE,SAAS;gBAClBL,eAAe,EAAE,SAAS;gBAC1BwB,MAAM,EAAE,mBAAmB;gBAC3BjB,YAAY,EAAE,UAAU;gBACxBS,KAAK,EAAE,SAAS;gBAChBF,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAENpI,OAAA,CAACL,yBAAyB;cACxBa,UAAU,EAAEA,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8I,MAAM,CAACC,QAAQ;cACrC;cACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAACC,QAAQ,CAACD,QAAQ,CAACtD,IAAI,CACjG,CAAE;cACFwD,kBAAkB,EAAE7I,QAAQ,CAACI,WAAW,GAAGyE,QAAQ,CAAC7E,QAAQ,CAACI,WAAW,CAAC,GAAG0E,SAAU;cACtFgE,qBAAqB,EAAE9I,QAAQ,CAACK,cAAc,GAAGwE,QAAQ,CAAC7E,QAAQ,CAACK,cAAc,CAAC,GAAGyE,SAAU;cAC/FiE,gBAAgB,EAAE/D,oBAAqB;cACvCgE,mBAAmB,EAAEzD,uBAAwB;cAC7C0C,WAAW,EAAC,iBAAiB;cAC7BgB,QAAQ,EAAE,IAAK;cACfnJ,KAAK,EAAEgB,MAAM,CAACV,WAAY;cAC1B8I,QAAQ,EAAExJ;YAAQ;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNpI,OAAA;YAAAqH,QAAA,gBACErH,OAAA;cAAOyG,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBa,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBH,YAAY,EAAE;cAChB,CAAE;cAAAP,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpI,OAAA;cACEiG,IAAI,EAAC,QAAQ;cACb7D,KAAK,EAAExB,QAAQ,CAACM,MAAO;cACvBwH,QAAQ,EAAErC,iBAAkB;cAC5BI,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBmB,MAAM,EAAE,mBAAmB;gBAC3BjB,YAAY,EAAE,KAAK;gBACnBO,QAAQ,EAAE,UAAU;gBACpBc,OAAO,EAAE,MAAM;gBACf5B,eAAe,EAAE;cACnB,CAAE;cAAAM,QAAA,gBAEFrH,OAAA;gBAAQoC,KAAK,EAAC,OAAO;gBAAAiF,QAAA,EAAC;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCpI,OAAA;gBAAQoC,KAAK,EAAC,WAAW;gBAAAiF,QAAA,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CpI,OAAA;gBAAQoC,KAAK,EAAC,WAAW;gBAAAiF,QAAA,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxG,cAAc,iBACb5B,OAAA;UAAKyG,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBiC,GAAG,EAAE,QAAQ;YACb9B,OAAO,EAAE,SAAS;YAClBL,eAAe,EAAE,SAAS;YAC1BwB,MAAM,EAAE,mBAAmB;YAC3BjB,YAAY,EAAE,KAAK;YACnBS,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE,UAAU;YACpBD,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,GAAC,SACC,EAACzF,cAAc;QAAA;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,eAKDpI,OAAA;UAAKyG,KAAK,EAAE;YACVmB,YAAY,EAAE,QAAQ;YACtBR,OAAO,EAAE,MAAM;YACfL,eAAe,EAAE,SAAS;YAC1BO,YAAY,EAAE,KAAK;YACnBiB,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBACArH,OAAA;YAAKyG,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,QAAQ;cACpBW,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,gBACArH,OAAA;cAAIyG,KAAK,EAAE;gBACToB,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBC,MAAM,EAAE;cACV,CAAE;cAAAX,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJvF,cAAc,CAACb,MAAM,GAAG,CAAC,iBACxBhC,OAAA;cAAKyG,KAAK,EAAE;gBACVO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBiC,GAAG,EAAE,QAAQ;gBACb9B,OAAO,EAAE,gBAAgB;gBACzBL,eAAe,EAAE,SAAS;gBAC1BwB,MAAM,EAAE,mBAAmB;gBAC3BjB,YAAY,EAAE,KAAK;gBACnBS,KAAK,EAAE,SAAS;gBAChBF,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,gBACArH,OAAA;gBAAAqH,QAAA,EAAM;cAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACdvF,cAAc,CAACb,MAAM,EAAC,QAAM,EAACa,cAAc,CAACb,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,kBACrE;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNpI,OAAA,CAACN,mBAAmB;YAClBqK,cAAc,EAAEtI,iBAAkB;YAClCY,cAAc,EAAEA,cAAe;YAC/B2H,YAAY,EAAEvH,eAAgB;YAC9BwH,SAAS,EAAE,EAAG;YACdH,QAAQ,EAAExH,YAAa;YACvBO,cAAc,EAAEA,cAAe;YAC/BqH,iBAAiB,EAAEpH,eAAgB;YACnCqH,mBAAmB,EAAEpH;UAAkB;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACD7F,UAAU,iBACTvC,OAAA;YAAKyG,KAAK,EAAE;cACVsB,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,UAAU;cACpBiB,SAAS,EAAE;YACb,CAAE;YAAAzB,QAAA,EACC9E;UAAU;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLxH,QAAQ,CAACM,MAAM,KAAK,WAAW,iBAC9BlB,OAAA;UAAKyG,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrH,OAAA;YAAOyG,KAAK,EAAE;cACZO,OAAO,EAAE,OAAO;cAChBa,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBH,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRpI,OAAA;YACEsG,IAAI,EAAC,gBAAgB;YACrBL,IAAI,EAAC,sBAAsB;YAC3B7D,KAAK,EAAExB,QAAQ,CAACW,oBAAqB;YACrCmH,QAAQ,EAAErC,iBAAkB;YAC5BI,KAAK,EAAE;cACLc,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBmB,MAAM,EAAE,aAAa7G,MAAM,CAACH,oBAAoB,GAAG,SAAS,GAAG,SAAS,EAAE;cAC1E+F,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE,UAAU;cACpBc,OAAO,EAAE;YACX;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD1G,MAAM,CAACH,oBAAoB,iBAC1BvB,OAAA;YAAGyG,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEiB,SAAS,EAAE;YAAU,CAAE;YAAAzB,QAAA,EACvE3F,MAAM,CAACH;UAAoB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDpI,OAAA;UAAKyG,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfiC,mBAAmB,EAAE,SAAS;YAC9BC,GAAG,EAAE,MAAM;YACXtB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACArH,OAAA;YAAAqH,QAAA,eACErH,OAAA;cAAOyG,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBS,MAAM,EAAE;cACV,CAAE;cAAAnB,QAAA,gBACArH,OAAA;gBACEsG,IAAI,EAAC,UAAU;gBACfL,IAAI,EAAC,WAAW;gBAChBO,OAAO,EAAE5F,QAAQ,CAACO,SAAU;gBAC5BuH,QAAQ,EAAErC,iBAAkB;gBAC5BI,KAAK,EAAE;kBAAE2D,WAAW,EAAE;gBAAS;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,yBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNpI,OAAA;YAAAqH,QAAA,eACErH,OAAA;cAAOyG,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBS,MAAM,EAAE;cACV,CAAE;cAAAnB,QAAA,gBACArH,OAAA;gBACEsG,IAAI,EAAC,UAAU;gBACfL,IAAI,EAAC,UAAU;gBACfO,OAAO,EAAE5F,QAAQ,CAACQ,QAAS;gBAC3BsH,QAAQ,EAAErC,iBAAkB;gBAC5BI,KAAK,EAAE;kBAAE2D,WAAW,EAAE;gBAAS;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,iBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNpI,OAAA;YAAAqH,QAAA,eACErH,OAAA;cAAOyG,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBY,QAAQ,EAAE,UAAU;gBACpBE,KAAK,EAAE,SAAS;gBAChBS,MAAM,EAAE;cACV,CAAE;cAAAnB,QAAA,gBACArH,OAAA;gBACEsG,IAAI,EAAC,UAAU;gBACfL,IAAI,EAAC,gBAAgB;gBACrBO,OAAO,EAAE5F,QAAQ,CAACS,cAAe;gBACjCqH,QAAQ,EAAErC,iBAAkB;gBAC5BI,KAAK,EAAE;kBAAE2D,WAAW,EAAE;gBAAS;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,kBAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBH,CAAC,eAGNpI,OAAA;UAAKyG,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,UAAU;YAC1BgC,GAAG,EAAE;UACP,CAAE;UAAA7B,QAAA,gBACArH,OAAA;YACEsG,IAAI,EAAC,QAAQ;YACb+B,OAAO,EAAE5E,WAAY;YACrBgD,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBL,eAAe,EAAE,SAAS;cAC1BgB,KAAK,EAAE,SAAS;cAChBQ,MAAM,EAAE,MAAM;cACdjB,YAAY,EAAE,KAAK;cACnBkB,MAAM,EAAE,SAAS;cACjBV,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpI,OAAA;YACEsG,IAAI,EAAC,QAAQ;YACbwD,QAAQ,EAAExJ,OAAQ;YAClBmG,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBkB,UAAU,EAAEhI,OAAO,GAAG,SAAS,GAAG,mDAAmD;cACrFyH,KAAK,EAAE,OAAO;cACdQ,MAAM,EAAE,MAAM;cACdjB,YAAY,EAAE,KAAK;cACnBkB,MAAM,EAAElI,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3CwH,UAAU,EAAE,KAAK;cACjBD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EAED/G,OAAO,GAAG,WAAW,GAAID,YAAY,GAAG,QAAQ,GAAG;UAAS;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7H,EAAA,CAnzBIN,iBAAmD;EAAA,QAOoBT,yBAAyB,EA0DhGC,sBAAsB;AAAA;AAAA4K,EAAA,GAjEtBpK,iBAAmD;AAqzBzD,eAAeA,iBAAiB;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}