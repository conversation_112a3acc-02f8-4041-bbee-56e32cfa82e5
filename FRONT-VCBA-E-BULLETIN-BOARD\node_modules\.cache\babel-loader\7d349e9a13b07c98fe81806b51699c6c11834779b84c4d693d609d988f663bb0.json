{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 2-5 5-5-5\",\n  key: \"16satq\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"15\",\n  x: \"2\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1e6viu\"\n}]];\nconst Tv = createLucideIcon(\"tv\", __iconNode);\nexport { __iconNode, Tv as default };\n//# sourceMappingURL=tv.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}