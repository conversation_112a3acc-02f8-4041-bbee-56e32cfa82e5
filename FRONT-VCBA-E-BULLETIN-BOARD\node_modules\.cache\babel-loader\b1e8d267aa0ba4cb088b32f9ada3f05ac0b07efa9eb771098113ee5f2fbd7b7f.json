{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"5\",\n  x2: \"19\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"1nwqeh\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"19\",\n  y1: \"15\",\n  y2: \"15\",\n  key: \"g8yjpy\"\n}]];\nconst Equal = createLucideIcon(\"equal\", __iconNode);\nexport { __iconNode, Equal as default };\n//# sourceMappingURL=equal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}