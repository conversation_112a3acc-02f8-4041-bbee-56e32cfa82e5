{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3h3a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1h-3\",\n  key: \"1kt8lf\"\n}], [\"path\", {\n  d: \"M8 21H5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h3\",\n  key: \"gduv9\"\n}]];\nconst Brackets = createLucideIcon(\"brackets\", __iconNode);\nexport { __iconNode, Brackets as default };\n//# sourceMappingURL=brackets.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}