{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 5v14\",\n  key: \"1nt18q\"\n}], [\"path\", {\n  d: \"M8 5v14\",\n  key: \"1ybrkv\"\n}], [\"path\", {\n  d: \"M12 5v14\",\n  key: \"s699le\"\n}], [\"path\", {\n  d: \"M17 5v14\",\n  key: \"ycjyhj\"\n}], [\"path\", {\n  d: \"M21 5v14\",\n  key: \"nzette\"\n}]];\nconst Barcode = createLucideIcon(\"barcode\", __iconNode);\nexport { __iconNode, Barcode as default };\n//# sourceMappingURL=barcode.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}