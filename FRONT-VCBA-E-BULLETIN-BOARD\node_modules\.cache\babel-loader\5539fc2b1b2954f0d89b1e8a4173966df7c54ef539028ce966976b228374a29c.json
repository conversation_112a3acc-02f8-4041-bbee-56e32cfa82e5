{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M12 9v6\",\n  key: \"199k2o\"\n}], [\"path\", {\n  d: \"M16 15v6\",\n  key: \"8rj2es\"\n}], [\"path\", {\n  d: \"M16 3v6\",\n  key: \"1j6rpj\"\n}], [\"path\", {\n  d: \"M3 15h18\",\n  key: \"5xshup\"\n}], [\"path\", {\n  d: \"M3 9h18\",\n  key: \"1pudct\"\n}], [\"path\", {\n  d: \"M8 15v6\",\n  key: \"1stoo3\"\n}], [\"path\", {\n  d: \"M8 3v6\",\n  key: \"vlvjmk\"\n}]];\nconst BrickWall = createLucideIcon(\"brick-wall\", __iconNode);\nexport { __iconNode, BrickWall as default };\n//# sourceMappingURL=brick-wall.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}