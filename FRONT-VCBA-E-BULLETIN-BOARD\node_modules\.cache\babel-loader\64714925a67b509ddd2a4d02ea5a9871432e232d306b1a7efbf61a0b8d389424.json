{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m6 17 5-5-5-5\",\n  key: \"xnjwq\"\n}], [\"path\", {\n  d: \"m13 17 5-5-5-5\",\n  key: \"17xmmf\"\n}]];\nconst ChevronsRight = createLucideIcon(\"chevrons-right\", __iconNode);\nexport { __iconNode, ChevronsRight as default };\n//# sourceMappingURL=chevrons-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}