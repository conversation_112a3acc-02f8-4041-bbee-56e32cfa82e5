{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import reportWebVitals from'./reportWebVitals';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}