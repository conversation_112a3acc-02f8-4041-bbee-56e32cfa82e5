{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v12\",\n  key: \"1vza4d\"\n}], [\"path\", {\n  d: \"M17.196 9 6.804 15\",\n  key: \"1ah31z\"\n}], [\"path\", {\n  d: \"m6.804 9 10.392 6\",\n  key: \"1b6pxd\"\n}]];\nconst Asterisk = createLucideIcon(\"asterisk\", __iconNode);\nexport { __iconNode, Asterisk as default };\n//# sourceMappingURL=asterisk.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}