{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 15V3\",\n  key: \"m9g1x1\"\n}], [\"path\", {\n  d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n  key: \"ih7n3h\"\n}], [\"path\", {\n  d: \"m7 10 5 5 5-5\",\n  key: \"brsn70\"\n}]];\nconst Download = createLucideIcon(\"download\", __iconNode);\nexport { __iconNode, Download as default };\n//# sourceMappingURL=download.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}