{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v2\",\n  key: \"7u0qdc\"\n}], [\"path\", {\n  d: \"M14 2v4\",\n  key: \"qmzblu\"\n}], [\"path\", {\n  d: \"M17 2a1 1 0 0 1 1 1v9H6V3a1 1 0 0 1 1-1z\",\n  key: \"ycvu00\"\n}], [\"path\", {\n  d: \"M6 12a1 1 0 0 0-1 1v1a2 2 0 0 0 2 2h2a1 1 0 0 1 1 1v2.9a2 2 0 1 0 4 0V17a1 1 0 0 1 1-1h2a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1\",\n  key: \"iw4wnp\"\n}]];\nconst PaintbrushVertical = createLucideIcon(\"paintbrush-vertical\", __iconNode);\nexport { __iconNode, PaintbrushVertical as default };\n//# sourceMappingURL=paintbrush-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}