{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Edit3, Trash2 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef(null);\n  const dropdownRef = useRef(null);\n  const avatarRef = useRef(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && avatarRef.current && !avatarRef.current.contains(event.target)) {\n        setShowDropdown(false);\n      }\n    };\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n    console.log('📤 Profile Picture Upload - Starting upload process...', {\n      fileName: pendingFile.name,\n      fileSize: pendingFile.size,\n      fileType: pendingFile.type\n    });\n    try {\n      await onUpload(pendingFile);\n      console.log('✅ Profile Picture Upload - Successfully uploaded profile picture');\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      console.error('❌ Profile Picture Upload - Failed to upload:', err);\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = e => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    var _fileInputRef$current;\n    setShowDropdown(false);\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = async () => {\n    setShowDropdown(false);\n    console.log('🗑️ Profile Picture Remove - User clicked remove photo');\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n    try {\n      console.log('🗑️ Profile Picture Remove - Starting removal process...');\n      await onRemove();\n      console.log('✅ Profile Picture Remove - Successfully removed profile picture');\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      console.error('❌ Profile Picture Remove - Failed to remove:', err);\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: avatarRef,\n        style: {\n          position: 'relative',\n          width: `${size}px`,\n          height: `${size}px`,\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n          transition: 'all 0.3s ease',\n          cursor: 'pointer',\n          boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n          transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: `${size * 0.3}px`,\n            transition: 'filter 0.3s ease',\n            filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(34, 197, 94, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.3s ease',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            size: size * 0.2,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.9)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${size * 0.2}px`,\n              height: `${size * 0.2}px`,\n              border: '3px solid #e8f5e8',\n              borderTop: '3px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: handleCameraClick,\n        style: {\n          position: 'absolute',\n          bottom: '8px',\n          right: '8px',\n          width: `${size * 0.25}px`,\n          height: `${size * 0.25}px`,\n          background: '#ffffff',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n          border: '2px solid #ffffff',\n          transition: 'all 0.2s ease',\n          transform: isHovered ? 'scale(1.1)' : 'scale(1)',\n          zIndex: 10\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#f3f4f6';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = '#ffffff';\n        },\n        children: /*#__PURE__*/_jsxDEV(Camera, {\n          size: size * 0.12,\n          color: \"#374151\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), showDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: dropdownRef,\n        style: {\n          position: 'absolute',\n          top: `${size + 10}px`,\n          right: '0',\n          background: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n          border: '1px solid #e8f5e8',\n          minWidth: '180px',\n          zIndex: 1000,\n          overflow: 'hidden',\n          animation: 'dropdownFadeIn 0.2s ease-out'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleChangePhoto,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '12px 16px',\n            border: 'none',\n            background: 'transparent',\n            textAlign: 'left',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#374151',\n            transition: 'background-color 0.2s ease',\n            opacity: isLoading ? 0.6 : 1\n          },\n          onMouseEnter: e => {\n            if (!isLoading) e.currentTarget.style.background = '#f9fafb';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.background = 'transparent';\n          },\n          children: [/*#__PURE__*/_jsxDEV(Edit3, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this), \"Change Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRemovePhoto,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '12px 16px',\n            border: 'none',\n            background: 'transparent',\n            textAlign: 'left',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#dc2626',\n            transition: 'background-color 0.2s ease',\n            opacity: isLoading ? 0.6 : 1,\n            borderTop: '1px solid #f3f4f6'\n          },\n          onMouseEnter: e => {\n            if (!isLoading) e.currentTarget.style.background = '#fef2f2';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.background = 'transparent';\n          },\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), \"Remove Photo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), hasPendingChanges && showActions && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        display: 'flex',\n        gap: '0.75rem',\n        animation: 'fadeIn 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSave,\n        disabled: isLoading,\n        style: {\n          background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          padding: '0.75rem 1.5rem',\n          fontWeight: '600',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          opacity: isLoading ? 0.6 : 1,\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '14px',\n          transition: 'all 0.2s ease',\n          boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n        },\n        onMouseEnter: e => {\n          if (!isLoading) {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n          }\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'translateY(0)';\n          e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this), \"Save Changes\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCancel,\n        disabled: isLoading,\n        style: {\n          background: 'transparent',\n          border: '2px solid #e8f5e8',\n          borderRadius: '8px',\n          padding: '0.75rem 1.5rem',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          color: '#6b7280',\n          opacity: isLoading ? 0.6 : 1,\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '14px',\n          fontWeight: '600',\n          transition: 'all 0.2s ease'\n        },\n        onMouseEnter: e => {\n          if (!isLoading) {\n            e.currentTarget.style.borderColor = '#d1d5db';\n            e.currentTarget.style.background = '#f9fafb';\n          }\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.borderColor = '#e8f5e8';\n          e.currentTarget.style.background = 'transparent';\n        },\n        children: [/*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this), \"Cancel\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n      onChange: handleInputChange,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '1rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '12px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem',\n        animation: 'slideDown 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 20,\n        style: {\n          marginTop: '1px',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          },\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            lineHeight: '1.4'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '1rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '12px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.75rem',\n        animation: 'slideDown 0.3s ease-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 20,\n        style: {\n          marginTop: '1px',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          },\n          children: \"Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            lineHeight: '1.4'\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        @keyframes fadeIn {\n          0% { opacity: 0; }\n          100% { opacity: 1; }\n        }\n\n        @keyframes slideUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes slideDown {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        @keyframes dropdownFadeIn {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"qCz3Y8HHscP+zcY9DtnLUxcMExg=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "Edit3", "Trash2", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "size", "showActions", "_s", "preview", "setPreview", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showDropdown", "setShowDropdown", "pendingFile", "setPendingFile", "isHovered", "setIsHovered", "fileInputRef", "dropdownRef", "avatar<PERSON><PERSON>", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "result", "onerror", "readAsDataURL", "handleSave", "console", "log", "fileName", "name", "fileSize", "fileType", "setTimeout", "err", "message", "handleCancel", "handleInputChange", "_e$target$files", "files", "value", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleCameraClick", "stopPropagation", "handleChangePhoto", "_fileInputRef$current", "click", "handleRemovePhoto", "displayImage", "hasImage", "Boolean", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "style", "position", "display", "ref", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "boxShadow", "transform", "onDragOver", "onDragLeave", "onDrop", "onMouseEnter", "onMouseLeave", "src", "alt", "onLoad", "onError", "objectFit", "filter", "_jsxFileName", "lineNumber", "columnNumber", "background", "alignItems", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "onClick", "zIndex", "currentTarget", "min<PERSON><PERSON><PERSON>", "disabled", "padding", "textAlign", "gap", "marginTop", "borderColor", "accept", "onChange", "flexShrink", "marginBottom", "lineHeight", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Edit3, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n  size?: number; // Size in pixels for the circular avatar\n  showActions?: boolean; // Whether to show action buttons\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = '',\n  size = 120,\n  showActions = true\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showDropdown, setShowDropdown] = useState(false);\n\n  const [pendingFile, setPendingFile] = useState<File | null>(null);\n  const [isHovered, setIsHovered] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const avatarRef = useRef<HTMLDivElement>(null);\n\n  // Click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&\n          avatarRef.current && !avatarRef.current.contains(event.target as Node)) {\n        setShowDropdown(false);\n      }\n    };\n\n    if (showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => document.removeEventListener('mousedown', handleClickOutside);\n    }\n  }, [showDropdown]);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (now just sets preview and pending file)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n    setShowDropdown(false);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setPendingFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (upload the pending file)\n  const handleSave = useCallback(async () => {\n    if (!pendingFile) return;\n\n    console.log('📤 Profile Picture Upload - Starting upload process...', {\n      fileName: pendingFile.name,\n      fileSize: pendingFile.size,\n      fileType: pendingFile.type\n    });\n\n    try {\n      await onUpload(pendingFile);\n      console.log('✅ Profile Picture Upload - Successfully uploaded profile picture');\n      setSuccess('Profile picture updated successfully!');\n      setPendingFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      console.error('❌ Profile Picture Upload - Failed to upload:', err);\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n      setPendingFile(null);\n    }\n  }, [pendingFile, onUpload]);\n\n  // Handle cancel (discard preview and pending file)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setPendingFile(null);\n    setError(null);\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle camera icon click\n  const handleCameraClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setShowDropdown(!showDropdown);\n  };\n\n  // Handle change photo\n  const handleChangePhoto = () => {\n    setShowDropdown(false);\n    fileInputRef.current?.click();\n  };\n\n  // Handle remove photo\n  const handleRemovePhoto = async () => {\n    setShowDropdown(false);\n    console.log('🗑️ Profile Picture Remove - User clicked remove photo');\n\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setPendingFile(null);\n\n    try {\n      console.log('🗑️ Profile Picture Remove - Starting removal process...');\n      await onRemove();\n      console.log('✅ Profile Picture Remove - Successfully removed profile picture');\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      console.error('❌ Profile Picture Remove - Failed to remove:', err);\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n  const hasPendingChanges = Boolean(pendingFile);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage,\n    hasPendingChanges\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      {/* Facebook-style Circular Avatar with Camera Overlay */}\n      <div style={{ position: 'relative', display: 'inline-block' }}>\n        <div\n          ref={avatarRef}\n          style={{\n            position: 'relative',\n            width: `${size}px`,\n            height: `${size}px`,\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '4px solid #22c55e' : hasPendingChanges ? '4px solid #facc15' : '4px solid #e8f5e8',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            boxShadow: isHovered ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',\n            transform: isHovered ? 'scale(1.02)' : 'scale(1)'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: `${size * 0.3}px`,\n                transition: 'filter 0.3s ease',\n                filter: isHovered ? 'brightness(0.9)' : 'brightness(1)'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Drag Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(34, 197, 94, 0.8)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.3s ease',\n              borderRadius: '50%'\n            }}\n          >\n            <Upload size={size * 0.2} color=\"white\" />\n          </div>\n\n          {/* Loading Overlay */}\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.9)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: '50%'\n              }}\n            >\n              <div\n                style={{\n                  width: `${size * 0.2}px`,\n                  height: `${size * 0.2}px`,\n                  border: '3px solid #e8f5e8',\n                  borderTop: '3px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Facebook-style Camera Icon Overlay (Bottom-right) */}\n        <div\n          onClick={handleCameraClick}\n          style={{\n            position: 'absolute',\n            bottom: '8px',\n            right: '8px',\n            width: `${size * 0.25}px`,\n            height: `${size * 0.25}px`,\n            background: '#ffffff',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n            border: '2px solid #ffffff',\n            transition: 'all 0.2s ease',\n            transform: isHovered ? 'scale(1.1)' : 'scale(1)',\n            zIndex: 10\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#f3f4f6';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = '#ffffff';\n          }}\n        >\n          <Camera size={size * 0.12} color=\"#374151\" />\n        </div>\n\n        {/* Dropdown Menu */}\n        {showDropdown && (\n          <div\n            ref={dropdownRef}\n            style={{\n              position: 'absolute',\n              top: `${size + 10}px`,\n              right: '0',\n              background: 'white',\n              borderRadius: '12px',\n              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n              border: '1px solid #e8f5e8',\n              minWidth: '180px',\n              zIndex: 1000,\n              overflow: 'hidden',\n              animation: 'dropdownFadeIn 0.2s ease-out'\n            }}\n          >\n            <button\n              onClick={handleChangePhoto}\n              disabled={isLoading}\n              style={{\n                width: '100%',\n                padding: '12px 16px',\n                border: 'none',\n                background: 'transparent',\n                textAlign: 'left',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '12px',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: '#374151',\n                transition: 'background-color 0.2s ease',\n                opacity: isLoading ? 0.6 : 1\n              }}\n              onMouseEnter={(e) => {\n                if (!isLoading) e.currentTarget.style.background = '#f9fafb';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.background = 'transparent';\n              }}\n            >\n              <Edit3 size={16} />\n              Change Photo\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemovePhoto}\n                disabled={isLoading}\n                style={{\n                  width: '100%',\n                  padding: '12px 16px',\n                  border: 'none',\n                  background: 'transparent',\n                  textAlign: 'left',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  color: '#dc2626',\n                  transition: 'background-color 0.2s ease',\n                  opacity: isLoading ? 0.6 : 1,\n                  borderTop: '1px solid #f3f4f6'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isLoading) e.currentTarget.style.background = '#fef2f2';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = 'transparent';\n                }}\n              >\n                <Trash2 size={16} />\n                Remove Photo\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Save/Cancel Buttons (only show when there are pending changes) */}\n      {hasPendingChanges && showActions && (\n        <div style={{\n          marginTop: '1rem',\n          display: 'flex',\n          gap: '0.75rem',\n          animation: 'fadeIn 0.3s ease-out'\n        }}>\n          <button\n            onClick={handleSave}\n            disabled={isLoading}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '14px',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n            }}\n            onMouseEnter={(e) => {\n              if (!isLoading) {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              }\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n            }}\n          >\n            <CheckCircle size={16} />\n            Save Changes\n          </button>\n\n          <button\n            onClick={handleCancel}\n            disabled={isLoading}\n            style={{\n              background: 'transparent',\n              border: '2px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '14px',\n              fontWeight: '600',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              if (!isLoading) {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.background = '#f9fafb';\n              }\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.borderColor = '#e8f5e8';\n              e.currentTarget.style.background = 'transparent';\n            }}\n          >\n            <X size={16} />\n            Cancel\n          </button>\n        </div>\n      )}\n\n      {/* File Input */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n        onChange={handleInputChange}\n        style={{ display: 'none' }}\n      />\n\n\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '12px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'flex-start',\n          gap: '0.75rem',\n          animation: 'slideDown 0.3s ease-out'\n        }}>\n          <AlertCircle size={20} style={{ marginTop: '1px', flexShrink: 0 }} />\n          <div>\n            <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>Error</div>\n            <div style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>{error}</div>\n          </div>\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '12px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'flex-start',\n          gap: '0.75rem',\n          animation: 'slideDown 0.3s ease-out'\n        }}>\n          <CheckCircle size={20} style={{ marginTop: '1px', flexShrink: 0 }} />\n          <div>\n            <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>Success</div>\n            <div style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>{success}</div>\n          </div>\n        </div>\n      )}\n\n      {/* CSS Animations */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        @keyframes fadeIn {\n          0% { opacity: 0; }\n          100% { opacity: 1; }\n        }\n\n        @keyframes slideUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes slideDown {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        @keyframes dropdownFadeIn {\n          0% {\n            opacity: 0;\n            transform: translateY(-10px) scale(0.95);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa1F,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,GAAG;EACVC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMqC,YAAY,GAAGpC,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMqC,WAAW,GAAGrC,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMsC,SAAS,GAAGtC,MAAM,CAAiB,IAAI,CAAC;;EAE9C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMqC,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,IAC1EL,SAAS,CAACG,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QAC1EZ,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC5E;EACF,CAAC,EAAE,CAACT,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMiB,YAAY,GAAG9C,WAAW,CAAE+C,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAAC7B,IAAI,GAAG8B,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,gBAAgB,GAAGpD,WAAW,CAAE+C,IAAU,IAAK;IACnDrB,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,KAAK,CAAC;IAEtB,MAAMuB,eAAe,GAAGP,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIM,eAAe,EAAE;MACnB3B,QAAQ,CAAC2B,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBpC,UAAU,EAAAoC,SAAA,GAACD,CAAC,CAACf,MAAM,cAAAgB,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACtC3B,cAAc,CAACe,IAAI,CAAC;IACtB,CAAC;IACDO,MAAM,CAACM,OAAO,GAAG,MAAM;MACrBlC,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACD4B,MAAM,CAACO,aAAa,CAACd,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgB,UAAU,GAAG9D,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC+B,WAAW,EAAE;IAElBgC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;MACpEC,QAAQ,EAAElC,WAAW,CAACmC,IAAI;MAC1BC,QAAQ,EAAEpC,WAAW,CAACb,IAAI;MAC1BkD,QAAQ,EAAErC,WAAW,CAACoB;IACxB,CAAC,CAAC;IAEF,IAAI;MACF,MAAMrC,QAAQ,CAACiB,WAAW,CAAC;MAC3BgC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/EpC,UAAU,CAAC,uCAAuC,CAAC;MACnDI,cAAc,CAAC,IAAI,CAAC;MACpBqC,UAAU,CAAC,MAAMzC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAO0C,GAAQ,EAAE;MACjBP,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAE6C,GAAG,CAAC;MAClE5C,QAAQ,CAAC4C,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3DjD,UAAU,CAAC,IAAI,CAAC;MAChBU,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACD,WAAW,EAAEjB,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAM0D,YAAY,GAAGxE,WAAW,CAAC,MAAM;IACrCsB,UAAU,CAAC,IAAI,CAAC;IAChBU,cAAc,CAAC,IAAI,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,iBAAiB,GAAIhB,CAAsC,IAAK;IAAA,IAAAiB,eAAA;IACpE,MAAM3B,IAAI,IAAA2B,eAAA,GAAGjB,CAAC,CAACf,MAAM,CAACiC,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAI3B,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;IACA;IACAU,CAAC,CAACf,MAAM,CAACkC,KAAK,GAAG,EAAE;EACrB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIpB,CAAkB,IAAK;IAC7CA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClBtD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMuD,eAAe,GAAItB,CAAkB,IAAK;IAC9CA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClBtD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMwD,UAAU,GAAIvB,CAAkB,IAAK;IACzCA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClBtD,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMuB,IAAI,GAAGU,CAAC,CAACwB,YAAY,CAACN,KAAK,CAAC,CAAC,CAAC;IACpC,IAAI5B,IAAI,EAAE;MACRK,gBAAgB,CAACL,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAIzB,CAAmB,IAAK;IACjDA,CAAC,CAAC0B,eAAe,CAAC,CAAC;IACnBrD,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA,MAAMuD,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC9BvD,eAAe,CAAC,KAAK,CAAC;IACtB,CAAAuD,qBAAA,GAAAlD,YAAY,CAACK,OAAO,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCzD,eAAe,CAAC,KAAK,CAAC;IACtBiC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErEtC,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBN,UAAU,CAAC,IAAI,CAAC;IAChBU,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF+B,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,MAAMjD,QAAQ,CAAC,CAAC;MAChBgD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9EpC,UAAU,CAAC,uCAAuC,CAAC;MACnDyC,UAAU,CAAC,MAAMzC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAO0C,GAAQ,EAAE;MACjBP,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAE6C,GAAG,CAAC;MAClE5C,QAAQ,CAAC4C,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMiB,YAAY,GAAGnE,OAAO,IAAIT,cAAc;EAC9C,MAAM6E,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;EACtC,MAAMG,iBAAiB,GAAGD,OAAO,CAAC3D,WAAW,CAAC;;EAE9C;EACAgC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9CpD,cAAc;IACdS,OAAO;IACPmE,YAAY;IACZC,QAAQ;IACRE;EACF,CAAC,CAAC;EAEF,oBACEjF,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAA2E,QAAA,gBAEpDlF,OAAA;MAAKmF,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAe,CAAE;MAAAH,QAAA,gBAC5DlF,OAAA;QACEsF,GAAG,EAAE3D,SAAU;QACfwD,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBG,KAAK,EAAE,GAAG/E,IAAI,IAAI;UAClBgF,MAAM,EAAE,GAAGhF,IAAI,IAAI;UACnBiF,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE9E,UAAU,GAAG,mBAAmB,GAAGoE,iBAAiB,GAAG,mBAAmB,GAAG,mBAAmB;UACxGW,UAAU,EAAE,eAAe;UAC3BC,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAEvE,SAAS,GAAG,gCAAgC,GAAG,+BAA+B;UACzFwE,SAAS,EAAExE,SAAS,GAAG,aAAa,GAAG;QACzC,CAAE;QACFyE,UAAU,EAAE7B,cAAe;QAC3B8B,WAAW,EAAE5B,eAAgB;QAC7B6B,MAAM,EAAE5B,UAAW;QACnB6B,YAAY,EAAEA,CAAA,KAAM3E,YAAY,CAAC,IAAI,CAAE;QACvC4E,YAAY,EAAEA,CAAA,KAAM5E,YAAY,CAAC,KAAK,CAAE;QAAA0D,QAAA,GAEvCH,QAAQ,gBACP/E,OAAA;UACEqG,GAAG,EAAEvB,YAAa;UAClBwB,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMlD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEwB,YAAY,CAAE;UACxE0B,OAAO,EAAGzD,CAAC,IAAKM,OAAO,CAACtC,KAAK,CAAC,yBAAyB,EAAE+D,YAAY,EAAE/B,CAAC,CAAE;UAC1EoC,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE,OAAO;YAClBb,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAEnF,SAAS,GAAG,iBAAiB,GAAG;UAC1C;QAAE;UAAAgC,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF7G,OAAA;UACEmF,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdsB,UAAU,EAAE,mDAAmD;YAC/DzB,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,GAAG3G,IAAI,GAAG,GAAG,IAAI;YAC3BoF,UAAU,EAAE,kBAAkB;YAC9Bc,MAAM,EAAEnF,SAAS,GAAG,iBAAiB,GAAG;UAC1C,CAAE;UAAA2D,QAAA,EAED/E;QAAY;UAAAoD,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD7G,OAAA;UACEmF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBgC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,wBAAwB;YACpCzB,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAE3G,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3B+E,UAAU,EAAE,mBAAmB;YAC/BH,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFlF,OAAA,CAACR,MAAM;YAACgB,IAAI,EAAEA,IAAI,GAAG,GAAI;YAACyG,KAAK,EAAC;UAAO;YAAA1D,QAAA,EAAAoD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAtD,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EAGLvG,SAAS,iBACRN,OAAA;UACEmF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBgC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTT,UAAU,EAAE,0BAA0B;YACtCzB,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBvB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFlF,OAAA;YACEmF,KAAK,EAAE;cACLI,KAAK,EAAE,GAAG/E,IAAI,GAAG,GAAG,IAAI;cACxBgF,MAAM,EAAE,GAAGhF,IAAI,GAAG,GAAG,IAAI;cACzBmF,MAAM,EAAE,mBAAmB;cAC3B8B,SAAS,EAAE,mBAAmB;cAC9BhC,YAAY,EAAE,KAAK;cACnBiC,SAAS,EAAE;YACb;UAAE;YAAAnE,QAAA,EAAAoD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAtD,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA;QACE2H,OAAO,EAAEnD,iBAAkB;QAC3BW,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBmC,MAAM,EAAE,KAAK;UACbD,KAAK,EAAE,KAAK;UACZ/B,KAAK,EAAE,GAAG/E,IAAI,GAAG,IAAI,IAAI;UACzBgF,MAAM,EAAE,GAAGhF,IAAI,GAAG,IAAI,IAAI;UAC1BsG,UAAU,EAAE,SAAS;UACrBrB,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBnB,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAE,+BAA+B;UAC1CH,MAAM,EAAE,mBAAmB;UAC3BC,UAAU,EAAE,eAAe;UAC3BG,SAAS,EAAExE,SAAS,GAAG,YAAY,GAAG,UAAU;UAChDqG,MAAM,EAAE;QACV,CAAE;QACFzB,YAAY,EAAGpD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,SAAS;QAC9C,CAAE;QACFV,YAAY,EAAGrD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,SAAS;QAC9C,CAAE;QAAA5B,QAAA,eAEFlF,OAAA,CAACN,MAAM;UAACc,IAAI,EAAEA,IAAI,GAAG,IAAK;UAACyG,KAAK,EAAC;QAAS;UAAA1D,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EAGL1F,YAAY,iBACXnB,OAAA;QACEsF,GAAG,EAAE5D,WAAY;QACjByD,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBgC,GAAG,EAAE,GAAG5G,IAAI,GAAG,EAAE,IAAI;UACrB8G,KAAK,EAAE,GAAG;UACVR,UAAU,EAAE,OAAO;UACnBrB,YAAY,EAAE,MAAM;UACpBK,SAAS,EAAE,iCAAiC;UAC5CH,MAAM,EAAE,mBAAmB;UAC3BmC,QAAQ,EAAE,OAAO;UACjBF,MAAM,EAAE,IAAI;UACZlC,QAAQ,EAAE,QAAQ;UAClBgC,SAAS,EAAE;QACb,CAAE;QAAAxC,QAAA,gBAEFlF,OAAA;UACE2H,OAAO,EAAEjD,iBAAkB;UAC3BqD,QAAQ,EAAEzH,SAAU;UACpB6E,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbyC,OAAO,EAAE,WAAW;YACpBrC,MAAM,EAAE,MAAM;YACdmB,UAAU,EAAE,aAAa;YACzBmB,SAAS,EAAE,MAAM;YACjBpC,MAAM,EAAEvF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC7C+E,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBmB,GAAG,EAAE,MAAM;YACXf,QAAQ,EAAE,MAAM;YAChBD,UAAU,EAAE,KAAK;YACjBD,KAAK,EAAE,SAAS;YAChBrB,UAAU,EAAE,4BAA4B;YACxC4B,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG;UAC7B,CAAE;UACF6F,YAAY,EAAGpD,CAAC,IAAK;YACnB,IAAI,CAACzC,SAAS,EAAEyC,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,SAAS;UAC9D,CAAE;UACFV,YAAY,EAAGrD,CAAC,IAAK;YACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,aAAa;UAClD,CAAE;UAAA5B,QAAA,gBAEFlF,OAAA,CAACH,KAAK;YAACW,IAAI,EAAE;UAAG;YAAA+C,QAAA,EAAAoD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErB;QAAA;UAAAtD,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER9B,QAAQ,iBACP/E,OAAA;UACE2H,OAAO,EAAE9C,iBAAkB;UAC3BkD,QAAQ,EAAEzH,SAAU;UACpB6E,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbyC,OAAO,EAAE,WAAW;YACpBrC,MAAM,EAAE,MAAM;YACdmB,UAAU,EAAE,aAAa;YACzBmB,SAAS,EAAE,MAAM;YACjBpC,MAAM,EAAEvF,SAAS,GAAG,aAAa,GAAG,SAAS;YAC7C+E,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBmB,GAAG,EAAE,MAAM;YACXf,QAAQ,EAAE,MAAM;YAChBD,UAAU,EAAE,KAAK;YACjBD,KAAK,EAAE,SAAS;YAChBrB,UAAU,EAAE,4BAA4B;YACxC4B,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;YAC5BmH,SAAS,EAAE;UACb,CAAE;UACFtB,YAAY,EAAGpD,CAAC,IAAK;YACnB,IAAI,CAACzC,SAAS,EAAEyC,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,SAAS;UAC9D,CAAE;UACFV,YAAY,EAAGrD,CAAC,IAAK;YACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,aAAa;UAClD,CAAE;UAAA5B,QAAA,gBAEFlF,OAAA,CAACF,MAAM;YAACU,IAAI,EAAE;UAAG;YAAA+C,QAAA,EAAAoD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtB;QAAA;UAAAtD,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAtD,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL5B,iBAAiB,IAAIxE,WAAW,iBAC/BT,OAAA;MAAKmF,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjB9C,OAAO,EAAE,MAAM;QACf6C,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAxC,QAAA,gBACAlF,OAAA;QACE2H,OAAO,EAAEvE,UAAW;QACpB2E,QAAQ,EAAEzH,SAAU;QACpB6E,KAAK,EAAE;UACL2B,UAAU,EAAE,mDAAmD;UAC/DG,KAAK,EAAE,OAAO;UACdtB,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnBuC,OAAO,EAAE,gBAAgB;UACzBd,UAAU,EAAE,KAAK;UACjBrB,MAAM,EAAEvF,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7CkH,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;UAC5B+E,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBmB,GAAG,EAAE,QAAQ;UACbf,QAAQ,EAAE,MAAM;UAChBvB,UAAU,EAAE,eAAe;UAC3BE,SAAS,EAAE;QACb,CAAE;QACFK,YAAY,EAAGpD,CAAC,IAAK;UACnB,IAAI,CAACzC,SAAS,EAAE;YACdyC,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACY,SAAS,GAAG,kBAAkB;YACpDhD,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACW,SAAS,GAAG,mCAAmC;UACvE;QACF,CAAE;QACFM,YAAY,EAAGrD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACY,SAAS,GAAG,eAAe;UACjDhD,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACW,SAAS,GAAG,kCAAkC;QACtE,CAAE;QAAAZ,QAAA,gBAEFlF,OAAA,CAACJ,WAAW;UAACY,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3B;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET7G,OAAA;QACE2H,OAAO,EAAE7D,YAAa;QACtBiE,QAAQ,EAAEzH,SAAU;QACpB6E,KAAK,EAAE;UACL2B,UAAU,EAAE,aAAa;UACzBnB,MAAM,EAAE,mBAAmB;UAC3BF,YAAY,EAAE,KAAK;UACnBuC,OAAO,EAAE,gBAAgB;UACzBnC,MAAM,EAAEvF,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7C2G,KAAK,EAAE,SAAS;UAChBO,OAAO,EAAElH,SAAS,GAAG,GAAG,GAAG,CAAC;UAC5B+E,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBmB,GAAG,EAAE,QAAQ;UACbf,QAAQ,EAAE,MAAM;UAChBD,UAAU,EAAE,KAAK;UACjBtB,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGpD,CAAC,IAAK;UACnB,IAAI,CAACzC,SAAS,EAAE;YACdyC,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACiD,WAAW,GAAG,SAAS;YAC7CrF,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,SAAS;UAC9C;QACF,CAAE;QACFV,YAAY,EAAGrD,CAAC,IAAK;UACnBA,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAACiD,WAAW,GAAG,SAAS;UAC7CrF,CAAC,CAAC8E,aAAa,CAAC1C,KAAK,CAAC2B,UAAU,GAAG,aAAa;QAClD,CAAE;QAAA5B,QAAA,gBAEFlF,OAAA,CAACP,CAAC;UAACe,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,UAEjB;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAtD,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGD7G,OAAA;MACEsF,GAAG,EAAE7D,YAAa;MAClBgB,IAAI,EAAC,MAAM;MACX4F,MAAM,EAAC,2CAA2C;MAClDC,QAAQ,EAAEvE,iBAAkB;MAC5BoB,KAAK,EAAE;QAAEE,OAAO,EAAE;MAAO;IAAE;MAAA9B,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,EAKD9F,KAAK,iBACJf,OAAA;MAAKmF,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBH,OAAO,EAAE,MAAM;QACflB,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,MAAM;QACpBwB,KAAK,EAAE,SAAS;QAChB5B,OAAO,EAAE,MAAM;QACf0B,UAAU,EAAE,YAAY;QACxBmB,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAxC,QAAA,gBACAlF,OAAA,CAACL,WAAW;QAACa,IAAI,EAAE,EAAG;QAAC2E,KAAK,EAAE;UAAEgD,SAAS,EAAE,KAAK;UAAEI,UAAU,EAAE;QAAE;MAAE;QAAAhF,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrE7G,OAAA;QAAAkF,QAAA,gBACElF,OAAA;UAAKmF,KAAK,EAAE;YAAE+B,UAAU,EAAE,KAAK;YAAEsB,YAAY,EAAE;UAAU,CAAE;UAAAtD,QAAA,EAAC;QAAK;UAAA3B,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvE7G,OAAA;UAAKmF,KAAK,EAAE;YAAEgC,QAAQ,EAAE,UAAU;YAAEsB,UAAU,EAAE;UAAM,CAAE;UAAAvD,QAAA,EAAEnE;QAAK;UAAAwC,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAtD,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5F,OAAO,iBACNjB,OAAA;MAAKmF,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBH,OAAO,EAAE,MAAM;QACflB,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,MAAM;QACpBwB,KAAK,EAAE,SAAS;QAChB5B,OAAO,EAAE,MAAM;QACf0B,UAAU,EAAE,YAAY;QACxBmB,GAAG,EAAE,SAAS;QACdR,SAAS,EAAE;MACb,CAAE;MAAAxC,QAAA,gBACAlF,OAAA,CAACJ,WAAW;QAACY,IAAI,EAAE,EAAG;QAAC2E,KAAK,EAAE;UAAEgD,SAAS,EAAE,KAAK;UAAEI,UAAU,EAAE;QAAE;MAAE;QAAAhF,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrE7G,OAAA;QAAAkF,QAAA,gBACElF,OAAA;UAAKmF,KAAK,EAAE;YAAE+B,UAAU,EAAE,KAAK;YAAEsB,YAAY,EAAE;UAAU,CAAE;UAAAtD,QAAA,EAAC;QAAO;UAAA3B,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzE7G,OAAA;UAAKmF,KAAK,EAAE;YAAEgC,QAAQ,EAAE,UAAU;YAAEsB,UAAU,EAAE;UAAM,CAAE;UAAAvD,QAAA,EAAEjE;QAAO;UAAAsC,QAAA,EAAAoD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAtD,QAAA,EAAAoD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAtD,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7G,OAAA;MAAAkF,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA3B,QAAA,EAAAoD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAtD,QAAA,EAAAoD,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnG,EAAA,CA5kBIT,oBAAyD;AAAAyI,EAAA,GAAzDzI,oBAAyD;AA8kB/D,eAAeA,oBAAoB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}