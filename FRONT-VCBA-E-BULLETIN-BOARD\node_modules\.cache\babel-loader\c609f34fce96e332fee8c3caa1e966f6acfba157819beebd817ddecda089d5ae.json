{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"16\",\n  cy: \"4\",\n  r: \"1\",\n  key: \"1grugj\"\n}], [\"path\", {\n  d: \"m18 19 1-7-6 1\",\n  key: \"r0i19z\"\n}], [\"path\", {\n  d: \"m5 8 3-3 5.5 3-2.36 3.5\",\n  key: \"9ptxx2\"\n}], [\"path\", {\n  d: \"M4.24 14.5a5 5 0 0 0 6.88 6\",\n  key: \"10kmtu\"\n}], [\"path\", {\n  d: \"M13.76 17.5a5 5 0 0 0-6.88-6\",\n  key: \"2qq6rc\"\n}]];\nconst Accessibility = createLucideIcon(\"accessibility\", __iconNode);\nexport { __iconNode, Accessibility as default };\n//# sourceMappingURL=accessibility.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}