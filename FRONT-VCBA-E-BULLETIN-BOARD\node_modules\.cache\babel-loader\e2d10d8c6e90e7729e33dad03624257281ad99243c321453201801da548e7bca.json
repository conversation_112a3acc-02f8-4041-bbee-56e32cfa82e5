{"ast": null, "code": "import { httpClient } from './api.service';\nimport { API_BASE_URL } from '../config/constants';\nclass CalendarReactionService {\n  /**\n   * Get current user authentication context\n   */\n  getCurrentUserAuth() {\n    // Check for student authentication first\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    if (studentToken && studentUser) {\n      return {\n        useStudentAuth: true,\n        token: studentToken,\n        userType: 'student'\n      };\n    }\n\n    // Fallback to admin authentication\n    return {\n      useStudentAuth: false,\n      token: null,\n      userType: 'admin'\n    };\n  }\n\n  // Like a calendar event\n  async likeEvent(eventId) {\n    try {\n      const {\n        useStudentAuth,\n        token\n      } = this.getCurrentUserAuth();\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify({})\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar like failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || {\n            added: false\n          }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.post(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || {\n            added: false\n          }\n        };\n      }\n    } catch (error) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId) {\n    try {\n      const {\n        useStudentAuth,\n        token\n      } = this.getCurrentUserAuth();\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar unlike failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || {\n            removed: false\n          }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.delete(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || {\n            removed: false\n          }\n        };\n      }\n    } catch (error) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["httpClient", "API_BASE_URL", "CalendarReactionService", "getCurrentUserAuth", "studentToken", "localStorage", "getItem", "studentUser", "useStudentAuth", "token", "userType", "likeEvent", "eventId", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "errorData", "json", "console", "error", "Error", "status", "result", "success", "message", "data", "added", "post", "unlikeEvent", "removed", "delete", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { httpClient } from './api.service';\nimport { ApiResponse } from '../types/common.types';\nimport { API_BASE_URL } from '../config/constants';\n\nexport interface CalendarReactionData {\n  added?: boolean;\n  removed?: boolean;\n}\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: CalendarReactionData;\n}\n\nclass CalendarReactionService {\n  /**\n   * Get current user authentication context\n   */\n  private getCurrentUserAuth() {\n    // Check for student authentication first\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n\n    if (studentToken && studentUser) {\n      return {\n        useStudentAuth: true,\n        token: studentToken,\n        userType: 'student'\n      };\n    }\n\n    // Fallback to admin authentication\n    return {\n      useStudentAuth: false,\n      token: null,\n      userType: 'admin'\n    };\n  }\n\n  // Like a calendar event\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const { useStudentAuth, token } = this.getCurrentUserAuth();\n\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify({})\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar like failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || { added: false }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.post<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || { added: false }\n        };\n      }\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const { useStudentAuth, token } = this.getCurrentUserAuth();\n\n      if (useStudentAuth && token) {\n        // Use student authentication\n        const response = await fetch(`${API_BASE_URL}/api/calendar/${eventId}/like`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error('Student calendar unlike failed:', errorData);\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: result.success,\n          message: result.message,\n          data: result.data || { removed: false }\n        };\n      } else {\n        // Use admin authentication (httpClient)\n        const response = await httpClient.delete<CalendarReactionData>(`/api/calendar/${eventId}/like`);\n        return {\n          success: response.success,\n          message: response.message,\n          data: response.data || { removed: false }\n        };\n      }\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,YAAY,QAAQ,qBAAqB;AAalD,MAAMC,uBAAuB,CAAC;EAC5B;AACF;AACA;EACUC,kBAAkBA,CAAA,EAAG;IAC3B;IACA,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,YAAY,IAAIG,WAAW,EAAE;MAC/B,OAAO;QACLC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAEL,YAAY;QACnBM,QAAQ,EAAE;MACZ,CAAC;IACH;;IAEA;IACA,OAAO;MACLF,cAAc,EAAE,KAAK;MACrBC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;EACA,MAAMC,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACF,MAAM;QAAEJ,cAAc;QAAEC;MAAM,CAAC,GAAG,IAAI,CAACN,kBAAkB,CAAC,CAAC;MAE3D,IAAIK,cAAc,IAAIC,KAAK,EAAE;QAC3B;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGb,YAAY,iBAAiBW,OAAO,OAAO,EAAE;UAC3EG,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUP,KAAK;UAClC,CAAC;UACDQ,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;UACvCC,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEH,SAAS,CAAC;UACzD,MAAM,IAAII,KAAK,CAAC,uBAAuBZ,QAAQ,CAACa,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMd,QAAQ,CAACS,IAAI,CAAC,CAAC;QACpC,OAAO;UACLM,OAAO,EAAED,MAAM,CAACC,OAAO;UACvBC,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI;YAAEC,KAAK,EAAE;UAAM;QACtC,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMlB,QAAQ,GAAG,MAAMb,UAAU,CAACgC,IAAI,CAAuB,iBAAiBpB,OAAO,OAAO,CAAC;QAC7F,OAAO;UACLgB,OAAO,EAAEf,QAAQ,CAACe,OAAO;UACzBC,OAAO,EAAEhB,QAAQ,CAACgB,OAAO;UACzBC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI,IAAI;YAAEC,KAAK,EAAE;UAAM;QACxC,CAAC;MACH;IACF,CAAC,CAAC,OAAOP,KAAU,EAAE;MACnBD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACK,OAAO,IAAI,+BAA+B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMI,WAAWA,CAACrB,OAAe,EAAqC;IACpE,IAAI;MACF,MAAM;QAAEJ,cAAc;QAAEC;MAAM,CAAC,GAAG,IAAI,CAACN,kBAAkB,CAAC,CAAC;MAE3D,IAAIK,cAAc,IAAIC,KAAK,EAAE;QAC3B;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGb,YAAY,iBAAiBW,OAAO,OAAO,EAAE;UAC3EG,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUP,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACI,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;UACvCC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEH,SAAS,CAAC;UAC3D,MAAM,IAAII,KAAK,CAAC,uBAAuBZ,QAAQ,CAACa,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMd,QAAQ,CAACS,IAAI,CAAC,CAAC;QACpC,OAAO;UACLM,OAAO,EAAED,MAAM,CAACC,OAAO;UACvBC,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAI;YAAEI,OAAO,EAAE;UAAM;QACxC,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMrB,QAAQ,GAAG,MAAMb,UAAU,CAACmC,MAAM,CAAuB,iBAAiBvB,OAAO,OAAO,CAAC;QAC/F,OAAO;UACLgB,OAAO,EAAEf,QAAQ,CAACe,OAAO;UACzBC,OAAO,EAAEhB,QAAQ,CAACgB,OAAO;UACzBC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI,IAAI;YAAEI,OAAO,EAAE;UAAM;QAC1C,CAAC;MACH;IACF,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnBD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACK,OAAO,IAAI,iCAAiC,CAAC;IACrE;EACF;;EAEA;EACA,MAAMO,UAAUA,CAACxB,OAAe,EAAEyB,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACJ,WAAW,CAACrB,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAM0B,uBAAuB,GAAG,IAAIpC,uBAAuB,CAAC,CAAC;AACpE,eAAeoC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}