{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 18H6a2 2 0 0 1-2-2V7a2 2 0 0 0-2-2\",\n  key: \"4irg2o\"\n}], [\"path\", {\n  d: \"M17 14V4a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v10\",\n  key: \"14fcyx\"\n}], [\"rect\", {\n  width: \"13\",\n  height: \"8\",\n  x: \"8\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"o6oiis\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"t9985n\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"e5v82j\"\n}]];\nconst BaggageClaim = createLucideIcon(\"baggage-claim\", __iconNode);\nexport { __iconNode, BaggageClaim as default };\n//# sourceMappingURL=baggage-claim.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}