{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2c-1.35 1.5-2.092 3-2.5 4.5L14 8\",\n  key: \"1bivrr\"\n}], [\"path\", {\n  d: \"m17 6-2.891-2.891\",\n  key: \"xu6p2f\"\n}], [\"path\", {\n  d: \"M2 15c3.333-3 6.667-3 10-3\",\n  key: \"nxix30\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"m20 9 .891.891\",\n  key: \"3xwk7g\"\n}], [\"path\", {\n  d: \"M22 9c-1.5 1.35-3 2.092-4.5 2.5l-1-1\",\n  key: \"18cutr\"\n}], [\"path\", {\n  d: \"M3.109 14.109 4 15\",\n  key: \"q76aoh\"\n}], [\"path\", {\n  d: \"m6.5 12.5 1 1\",\n  key: \"cs35ky\"\n}], [\"path\", {\n  d: \"m7 18 2.891 2.891\",\n  key: \"1sisit\"\n}], [\"path\", {\n  d: \"M9 22c1.35-1.5 2.092-3 2.5-4.5L10 16\",\n  key: \"rlvei3\"\n}]];\nconst DnaOff = createLucideIcon(\"dna-off\", __iconNode);\nexport { __iconNode, DnaOff as default };\n//# sourceMappingURL=dna-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}