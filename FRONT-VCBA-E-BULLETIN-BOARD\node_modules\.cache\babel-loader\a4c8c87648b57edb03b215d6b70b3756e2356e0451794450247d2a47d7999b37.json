{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport{API_ENDPOINTS,STUDENT_USER_DATA_KEY,STUDENT_REFRESH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY,API_BASE_URL}from'../config/constants';// Student-specific token manager\nclass StudentTokenManager{getToken(){return localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);}setToken(token){localStorage.setItem(STUDENT_AUTH_TOKEN_KEY,token);}removeToken(){localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);}getAuthHeaders(){const token=this.getToken();return token?{Authorization:\"Bearer \".concat(token)}:{};}}const studentTokenManager=new StudentTokenManager();export class StudentAuthService{/**\n   * Custom request method with student token\n   */static async request(method,endpoint,data){const token=studentTokenManager.getToken();const headers={'Content-Type':'application/json'};if(token){headers['Authorization']=\"Bearer \".concat(token);}const config={method,headers,credentials:'include'};if(data&&(method==='POST'||method==='PUT'||method==='PATCH')){config.body=JSON.stringify(data);}const response=await fetch(\"\".concat(API_BASE_URL).concat(endpoint),config);const responseData=await response.json();if(!response.ok){var _responseData$error;// Handle error response with proper message\nconst errorMessage=(responseData===null||responseData===void 0?void 0:responseData.message)||(responseData===null||responseData===void 0?void 0:(_responseData$error=responseData.error)===null||_responseData$error===void 0?void 0:_responseData$error.message)||\"HTTP \".concat(response.status);throw new Error(errorMessage);}return responseData;}/**\n   * Get current authenticated student user\n   */static async getCurrentUser(){try{const token=studentTokenManager.getToken();if(!token){return null;}const response=await this.request('GET',API_ENDPOINTS.AUTH.PROFILE);if(response&&response.user){return response.user;}return null;}catch(error){console.error('Failed to get current student user:',error);return null;}}/**\n   * Login student user\n   */static async login(credentials){try{const response=await this.request('POST',API_ENDPOINTS.AUTH.LOGIN,_objectSpread(_objectSpread({},credentials),{},{userType:'student'}));if(response&&response.success&&response.data){// Transform raw database user data to frontend format\nconst rawUser=response.data.user;const transformedUser={id:rawUser.admin_id||rawUser.student_id,email:rawUser.email,role:rawUser.admin_id?'admin':'student',firstName:rawUser.first_name,lastName:rawUser.last_name,middleName:rawUser.middle_name,suffix:rawUser.suffix,phoneNumber:rawUser.phone_number||rawUser.phone,department:rawUser.department,position:rawUser.position,grade_level:rawUser.grade_level,studentNumber:rawUser.student_number,isActive:Boolean(rawUser.is_active),lastLogin:rawUser.last_login,createdAt:rawUser.account_created_at||rawUser.created_at,updatedAt:rawUser.account_updated_at||rawUser.updated_at};// Verify the user is actually a student\nif(transformedUser.role!=='student'){throw new Error('Access denied: Student account required');}// Store tokens and transformed user data in student-specific keys\nstudentTokenManager.setToken(response.data.accessToken);if(response.data.refreshToken){localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY,response.data.refreshToken);}localStorage.setItem(STUDENT_USER_DATA_KEY,JSON.stringify(transformedUser));return{success:true,message:response.message||'Student login successful',data:_objectSpread(_objectSpread({},response.data),{},{user:transformedUser})};}throw new Error((response===null||response===void 0?void 0:response.message)||'Student login failed');}catch(error){console.error('StudentAuthService.login error:',error);throw new Error(error.message||'Student login failed');}}/**\n   * Logout student user\n   */static async logout(){try{console.log('🚪 StudentAuthService - Calling server logout endpoint');await this.request('POST',API_ENDPOINTS.AUTH.LOGOUT);console.log('✅ StudentAuthService - Server logout successful');}catch(error){console.warn('⚠️ StudentAuthService - Server logout failed, continuing with local logout:',error);}finally{console.log('🧹 StudentAuthService - Clearing student local storage');this.clearLocalStorage();}}/**\n   * Clear student local storage and tokens\n   */static clearLocalStorage(){console.log('🧹 StudentAuthService - Clearing student authentication data');studentTokenManager.removeToken();localStorage.removeItem(STUDENT_USER_DATA_KEY);localStorage.removeItem(STUDENT_REFRESH_TOKEN_KEY);console.log('✅ StudentAuthService - Student authentication data cleared');}/**\n   * Check if student is authenticated\n   */static isAuthenticated(){const token=studentTokenManager.getToken();const userData=this.getStoredUser();return!!(token&&userData&&userData.role==='student');}/**\n   * Get stored student user data\n   */static getStoredUser(){try{const userData=localStorage.getItem(STUDENT_USER_DATA_KEY);const user=userData?JSON.parse(userData):null;return user&&user.role==='student'?user:null;}catch(error){console.error('Error parsing stored student user data:',error);return null;}}/**\n   * Get student user role\n   */static getUserRole(){const user=this.getStoredUser();return(user===null||user===void 0?void 0:user.role)==='student'?'student':null;}/**\n   * Refresh student access token\n   */static async refreshToken(){try{const refreshToken=localStorage.getItem(STUDENT_REFRESH_TOKEN_KEY);if(!refreshToken){throw new Error('No refresh token available');}const response=await this.request('POST',API_ENDPOINTS.AUTH.REFRESH,{refreshToken});if(response&&response.success&&response.data){// Update stored tokens\nstudentTokenManager.setToken(response.data.accessToken);if(response.data.refreshToken){localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY,response.data.refreshToken);}return{success:true,message:response.message,data:response.data};}throw new Error((response===null||response===void 0?void 0:response.message)||'Token refresh failed');}catch(error){console.error('StudentAuthService.refreshToken error:',error);throw new Error(error.message||'Token refresh failed');}}/**\n   * Get student profile\n   */static async getProfile(){try{var _response$error;const response=await this.request('GET',API_ENDPOINTS.AUTH.PROFILE);if(response&&response.user){// Update stored user data\nlocalStorage.setItem(STUDENT_USER_DATA_KEY,JSON.stringify(response.user));return response.user;}throw new Error(((_response$error=response.error)===null||_response$error===void 0?void 0:_response$error.message)||'Failed to get profile');}catch(error){throw new Error(error.message||'Failed to get profile');}}}export default StudentAuthService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}