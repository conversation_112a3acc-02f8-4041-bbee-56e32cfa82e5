{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"8\",\n  y: \"8\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"yj20xf\"\n}], [\"path\", {\n  d: \"M4 10a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2\",\n  key: \"1ltk23\"\n}], [\"path\", {\n  d: \"M14 20a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2\",\n  key: \"1q24h9\"\n}]];\nconst BringToFront = createLucideIcon(\"bring-to-front\", __iconNode);\nexport { __iconNode, BringToFront as default };\n//# sourceMappingURL=bring-to-front.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}