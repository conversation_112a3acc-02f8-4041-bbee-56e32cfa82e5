{"ast": null, "code": "import React,{useState,useRef,useCallback,useEffect}from'react';import{Upload,X,Star,Image as ImageIcon,AlertCircle}from'lucide-react';import{getImageUrl}from'../../config/constants';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Custom hook for CORS-safe image loading\nconst useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}// Fetch image as blob to bypass CORS restrictions\nconst response=await fetch(fullUrl,{method:'GET',headers:{'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);setImageUrl(objectUrl);}catch(err){console.error('❌ Failed to load image via CORS-safe method:',err);setError(err.message||'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup function to revoke object URL\nreturn()=>{if(imageUrl){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// Component for displaying individual calendar images with CORS-safe loading\nconst CalendarImageDisplay=_ref=>{let{image,isMarkedForDeletion,onToggleDeletion,onSetPrimary}=_ref;const{imageUrl,loading,error}=useImageLoader(image.file_path);return/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',aspectRatio:'1',borderRadius:'8px',overflow:'hidden',border:image.is_primary?'2px solid #22c55e':'1px solid #e5e7eb',opacity:isMarkedForDeletion?0.5:1,filter:isMarkedForDeletion?'grayscale(100%)':'none',backgroundColor:'#f9fafb'},children:[loading&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',inset:0,display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',backgroundColor:'#f3f4f6',color:'#6b7280',fontSize:'0.75rem',textAlign:'center',padding:'8px'},children:[/*#__PURE__*/_jsx(ImageIcon,{size:24}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'4px'},children:\"Loading...\"})]}),(error||loading)&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',inset:0,display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b',fontSize:'0.75rem',textAlign:'center',padding:'8px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'1.5rem',marginBottom:'4px'},children:\"\\u23F3\"}),/*#__PURE__*/_jsxs(\"div\",{style:{wordBreak:'break-all'},children:[\"Image loading...\",/*#__PURE__*/_jsx(\"br\",{}),image.file_name]})]}),imageUrl&&!loading&&!error&&/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:image.file_name,style:{width:'100%',height:'100%',objectFit:'cover'}}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'4px',right:'4px',display:'flex',gap:'4px'},children:[!image.is_primary&&!isMarkedForDeletion&&onSetPrimary&&/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.preventDefault();e.stopPropagation();onSetPrimary(image.attachment_id);},style:{backgroundColor:'rgba(34, 197, 94, 0.7)',color:'white',border:'none',borderRadius:'4px',padding:'4px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:\"Set as primary\",children:/*#__PURE__*/_jsx(Star,{size:12})}),/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.preventDefault();e.stopPropagation();onToggleDeletion(image.attachment_id);},style:{backgroundColor:isMarkedForDeletion?'rgba(34, 197, 94, 0.7)':'rgba(239, 68, 68, 0.7)',color:'white',border:'none',borderRadius:'4px',padding:'4px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:isMarkedForDeletion?'Undo delete':'Mark for deletion',children:/*#__PURE__*/_jsx(X,{size:12})})]}),isMarkedForDeletion&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',inset:0,backgroundColor:'rgba(239, 68, 68, 0.1)',display:'flex',alignItems:'center',justifyContent:'center',color:'#dc2626',fontSize:'0.875rem',fontWeight:'500'},children:\"Will be deleted\"})]});};const CalendarImageUpload=_ref2=>{let{onImagesChange,existingImages=[],onSetPrimary,maxImages=10,maxFileSize=5*1024*1024,// 5MB\nacceptedTypes=['image/jpeg','image/png','image/gif','image/webp'],className='',disabled=false,pendingDeletes=[],onMarkForDeletion,onUnmarkForDeletion}=_ref2;const[images,setImages]=useState([]);const[dragActive,setDragActive]=useState(false);const[error,setError]=useState(null);const fileInputRef=useRef(null);const dragCounter=useRef(0);// Validate file\nconst validateFile=useCallback(file=>{if(!acceptedTypes.includes(file.type)){return\"Invalid file type. Accepted types: \".concat(acceptedTypes.join(', '));}if(file.size>maxFileSize){return\"File size too large. Maximum size: \".concat((maxFileSize/(1024*1024)).toFixed(1),\"MB\");}return null;},[acceptedTypes,maxFileSize]);// Process files\nconst processFiles=useCallback(fileList=>{const files=Array.from(fileList);const totalImages=images.length+existingImages.length;if(totalImages+files.length>maxImages){setError(\"Maximum \".concat(maxImages,\" images allowed. You can add \").concat(maxImages-totalImages,\" more.\"));return;}const validFiles=[];const errors=[];files.forEach(file=>{const validationError=validateFile(file);if(validationError){errors.push(\"\".concat(file.name,\": \").concat(validationError));}else{validFiles.push(file);}});if(errors.length>0){setError(errors.join('\\n'));return;}setError(null);const newImages=validFiles.map(file=>({id:\"\".concat(Date.now(),\"-\").concat(Math.random()),file,preview:URL.createObjectURL(file)}));setImages(prev=>{const updated=[...prev,...newImages];onImagesChange(updated.map(img=>img.file));return updated;});},[images.length,existingImages.length,maxImages,validateFile,onImagesChange]);// Handle file input change\nconst handleFileChange=useCallback(e=>{if(e.target.files&&e.target.files.length>0){processFiles(e.target.files);}},[processFiles]);// Handle drag events\nconst handleDragEnter=useCallback(e=>{e.preventDefault();e.stopPropagation();dragCounter.current++;if(e.dataTransfer.items&&e.dataTransfer.items.length>0){setDragActive(true);}},[]);const handleDragLeave=useCallback(e=>{e.preventDefault();e.stopPropagation();dragCounter.current--;if(dragCounter.current===0){setDragActive(false);}},[]);const handleDragOver=useCallback(e=>{e.preventDefault();e.stopPropagation();},[]);const handleDrop=useCallback(e=>{e.preventDefault();e.stopPropagation();setDragActive(false);dragCounter.current=0;if(e.dataTransfer.files&&e.dataTransfer.files.length>0){processFiles(e.dataTransfer.files);}},[processFiles]);// Remove new image\nconst removeImage=useCallback(imageId=>{setImages(prev=>{const updated=prev.filter(img=>img.id!==imageId);// Clean up object URL\nconst removedImage=prev.find(img=>img.id===imageId);if(removedImage){URL.revokeObjectURL(removedImage.preview);}onImagesChange(updated.map(img=>img.file));return updated;});},[onImagesChange]);// Toggle existing image for deletion\nconst toggleExistingImageDeletion=useCallback(attachmentId=>{const isMarkedForDeletion=pendingDeletes.includes(attachmentId);if(isMarkedForDeletion){console.log('🔄 Unmarking image for deletion:',attachmentId);onUnmarkForDeletion===null||onUnmarkForDeletion===void 0?void 0:onUnmarkForDeletion(attachmentId);}else{console.log('🏷️ Marking image for deletion:',attachmentId);onMarkForDeletion===null||onMarkForDeletion===void 0?void 0:onMarkForDeletion(attachmentId);}},[pendingDeletes,onMarkForDeletion,onUnmarkForDeletion]);// Set primary image\nconst setPrimaryImage=useCallback(attachmentId=>{if(onSetPrimary){onSetPrimary(attachmentId);}},[onSetPrimary]);// Clear error after 5 seconds\nReact.useEffect(()=>{if(error){const timer=setTimeout(()=>setError(null),5000);return()=>clearTimeout(timer);}},[error]);// Clean up object URLs on unmount\nReact.useEffect(()=>{return()=>{images.forEach(img=>URL.revokeObjectURL(img.preview));};},[]);const totalImages=images.length+existingImages.length;const canAddMore=totalImages<maxImages&&!disabled;return/*#__PURE__*/_jsxs(\"div\",{className:\"calendar-image-upload \".concat(className),children:[canAddMore&&/*#__PURE__*/_jsxs(\"div\",{className:\"upload-area \".concat(dragActive?'drag-active':'',\" \").concat(disabled?'disabled':''),onDragEnter:handleDragEnter,onDragLeave:handleDragLeave,onDragOver:handleDragOver,onDrop:handleDrop,onClick:()=>{var _fileInputRef$current;return!disabled&&((_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click());},style:{border:'2px dashed #d1d5db',borderRadius:'8px',padding:'2rem',textAlign:'center',cursor:disabled?'not-allowed':'pointer',backgroundColor:dragActive?'#f3f4f6':disabled?'#f9fafb':'white',borderColor:dragActive?'#22c55e':error?'#ef4444':'#d1d5db',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",multiple:true,accept:acceptedTypes.join(','),onChange:handleFileChange,style:{display:'none'},disabled:disabled}),/*#__PURE__*/_jsx(Upload,{size:48,style:{color:dragActive?'#22c55e':disabled?'#9ca3af':'#6b7280',marginBottom:'1rem'}}),/*#__PURE__*/_jsx(\"p\",{style:{color:disabled?'#9ca3af':'#374151',fontSize:'1rem',fontWeight:'500',marginBottom:'0.5rem'},children:dragActive?'Drop images here':'Click to upload or drag and drop'}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:[\"PNG, JPG, GIF, WebP up to \",(maxFileSize/(1024*1024)).toFixed(1),\"MB\",/*#__PURE__*/_jsx(\"br\",{}),totalImages,\"/\",maxImages,\" images\"]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'6px',padding:'0.75rem',marginBottom:'1rem',display:'flex',alignItems:'flex-start',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:16,style:{color:'#ef4444',marginTop:'0.125rem',flexShrink:0}}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc2626',fontSize:'0.875rem',whiteSpace:'pre-line'},children:error})]}),(existingImages.length>0||images.length>0)&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fill, minmax(120px, 1fr))',gap:'1rem'},children:[existingImages.map(image=>{const isMarkedForDeletion=pendingDeletes.includes(image.attachment_id);return/*#__PURE__*/_jsx(CalendarImageDisplay,{image:image,isMarkedForDeletion:isMarkedForDeletion,onToggleDeletion:toggleExistingImageDeletion,onSetPrimary:onSetPrimary},\"existing-\".concat(image.attachment_id));}),images.map(image=>/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',aspectRatio:'1',borderRadius:'8px',overflow:'hidden',border:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(\"img\",{src:image.preview,alt:\"Preview\",style:{width:'100%',height:'100%',objectFit:'cover'}}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>removeImage(image.id),style:{position:'absolute',top:'4px',right:'4px',backgroundColor:'rgba(239, 68, 68, 0.7)',color:'white',border:'none',borderRadius:'4px',padding:'4px',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center'},title:\"Remove image\",children:/*#__PURE__*/_jsx(X,{size:12})})]},image.id))]}),existingImages.length===0&&images.length===0&&!canAddMore&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(ImageIcon,{size:48,style:{marginBottom:'1rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"p\",{children:\"No images uploaded\"})]})]});};export default CalendarImageUpload;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}