{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 11 2-2-2-2\",\n  key: \"1lz0vl\"\n}], [\"path\", {\n  d: \"M11 13h4\",\n  key: \"1p7l4v\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}]];\nconst SquareTerminal = createLucideIcon(\"square-terminal\", __iconNode);\nexport { __iconNode, SquareTerminal as default };\n//# sourceMappingURL=square-terminal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}