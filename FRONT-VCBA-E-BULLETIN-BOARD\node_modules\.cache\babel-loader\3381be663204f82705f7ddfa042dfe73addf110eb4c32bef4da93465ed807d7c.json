{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21 21-4.34-4.34\",\n  key: \"14j7rj\"\n}], [\"circle\", {\n  cx: \"11\",\n  cy: \"11\",\n  r: \"8\",\n  key: \"4ej97u\"\n}]];\nconst Search = createLucideIcon(\"search\", __iconNode);\nexport { __iconNode, Search as default };\n//# sourceMappingURL=search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}