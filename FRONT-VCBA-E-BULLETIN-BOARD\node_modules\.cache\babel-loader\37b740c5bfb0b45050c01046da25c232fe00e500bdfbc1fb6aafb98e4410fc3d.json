{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 7h-3a2 2 0 0 1-2-2V2\",\n  key: \"x099mo\"\n}], [\"path\", {\n  d: \"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z\",\n  key: \"18t6ie\"\n}], [\"path\", {\n  d: \"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8\",\n  key: \"1nja0z\"\n}]];\nconst Files = createLucideIcon(\"files\", __iconNode);\nexport { __iconNode, Files as default };\n//# sourceMappingURL=files.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}