{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 5H9\",\n  key: \"1tp3ed\"\n}], [\"path\", {\n  d: \"M15 9v3h4l-7 7-7-7h4V9z\",\n  key: \"ncdc4b\"\n}]];\nconst ArrowBigDownDash = createLucideIcon(\"arrow-big-down-dash\", __iconNode);\nexport { __iconNode, ArrowBigDownDash as default };\n//# sourceMappingURL=arrow-big-down-dash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}