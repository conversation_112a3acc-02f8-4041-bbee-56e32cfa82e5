{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12.5 8 15l2 2.5\",\n  key: \"1tg20x\"\n}], [\"path\", {\n  d: \"m14 12.5 2 2.5-2 2.5\",\n  key: \"yinavb\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z\",\n  key: \"1mlx9k\"\n}]];\nconst FileCode = createLucideIcon(\"file-code\", __iconNode);\nexport { __iconNode, FileCode as default };\n//# sourceMappingURL=file-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}