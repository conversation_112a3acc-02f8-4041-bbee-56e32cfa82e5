{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13.5 6.5-3.148-3.148a1.205 1.205 0 0 0-1.704 0L6.352 5.648a1.205 1.205 0 0 0 0 1.704L9.5 10.5\",\n  key: \"dzhfyz\"\n}], [\"path\", {\n  d: \"M16.5 7.5 19 5\",\n  key: \"1ltcjm\"\n}], [\"path\", {\n  d: \"m17.5 10.5 3.148 3.148a1.205 1.205 0 0 1 0 1.704l-2.296 2.296a1.205 1.205 0 0 1-1.704 0L13.5 14.5\",\n  key: \"nfoymv\"\n}], [\"path\", {\n  d: \"M9 21a6 6 0 0 0-6-6\",\n  key: \"1iajcf\"\n}], [\"path\", {\n  d: \"M9.352 10.648a1.205 1.205 0 0 0 0 1.704l2.296 2.296a1.205 1.205 0 0 0 1.704 0l4.296-4.296a1.205 1.205 0 0 0 0-1.704l-2.296-2.296a1.205 1.205 0 0 0-1.704 0z\",\n  key: \"nv9zqy\"\n}]];\nconst Satellite = createLucideIcon(\"satellite\", __iconNode);\nexport { __iconNode, Satellite as default };\n//# sourceMappingURL=satellite.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}