{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v.5\",\n  key: \"1dkoa9\"\n}], [\"path\", {\n  d: \"M12 10v4h4\",\n  key: \"1czhmt\"\n}], [\"path\", {\n  d: \"m12 14 1.535-1.605a5 5 0 0 1 8 1.5\",\n  key: \"lvuxfi\"\n}], [\"path\", {\n  d: \"M22 22v-4h-4\",\n  key: \"1ewp4q\"\n}], [\"path\", {\n  d: \"m22 18-1.535 1.605a5 5 0 0 1-8-1.5\",\n  key: \"14ync0\"\n}]];\nconst FolderSync = createLucideIcon(\"folder-sync\", __iconNode);\nexport { __iconNode, FolderSync as default };\n//# sourceMappingURL=folder-sync.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}