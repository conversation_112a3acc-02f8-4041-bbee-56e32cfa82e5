{"ast": null, "code": "/**\r\n * Form utility functions for consistent form handling across the application\r\n */\n\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images'\n  } = options;\n  const formDataToSubmit = new FormData();\n\n  // Validate required fields before processing\n  const requiredFields = ['title', 'content', 'category_id'];\n  const missingFields = requiredFields.filter(field => {\n    const value = formData[field];\n    return !value || typeof value === 'string' && !value.trim();\n  });\n  if (missingFields.length > 0) {\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n  }\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    if (key === 'category_id' && typeof value === 'string') {\n      // Send as integer for proper validation - this should never be empty due to validation above\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue) && parsedValue > 0) {\n          formDataToSubmit.append(key, parsedValue.toString());\n        } else {\n          throw new Error(`Invalid category_id: ${value}`);\n        }\n      } else {\n        throw new Error('Category is required');\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and send as integer\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue)) {\n          formDataToSubmit.append(key, parsedValue.toString());\n        }\n      }\n      // Skip empty subcategory_id - it's optional\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      // Send boolean values as proper boolean strings for express-validator\n      formDataToSubmit.append(key, value.toString());\n    } else if (value !== null && value !== undefined) {\n      formDataToSubmit.append(key, value.toString());\n    }\n    // Skip null/undefined values\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n  return formDataToSubmit;\n};\n\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\r\n * Common validation rules for announcements\r\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "formDataToSubmit", "FormData", "requiredFields", "missingFields", "filter", "field", "value", "trim", "length", "Error", "join", "Object", "entries", "for<PERSON>ach", "key", "parsedValue", "parseInt", "isNaN", "append", "toString", "status", "undefined", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\r\n * Form utility functions for consistent form handling across the application\r\n */\r\n\r\nexport interface FormField {\r\n  [key: string]: string | number | boolean | null | undefined;\r\n}\r\n\r\n/**\r\n * Creates FormData from form fields and files\r\n * @param formData - Object containing form field values\r\n * @param files - Array of files to append\r\n * @param options - Configuration options\r\n */\r\nexport const createFormData = (\r\n  formData: FormField,\r\n  files: File[] = [],\r\n  options: {\r\n    skipScheduledDate?: boolean;\r\n    fileFieldName?: string;\r\n  } = {}\r\n): FormData => {\r\n  const { skipScheduledDate = true, fileFieldName = 'images' } = options;\r\n  const formDataToSubmit = new FormData();\r\n\r\n  // Validate required fields before processing\r\n  const requiredFields = ['title', 'content', 'category_id'];\r\n  const missingFields = requiredFields.filter(field => {\r\n    const value = formData[field];\r\n    return !value || (typeof value === 'string' && !value.trim());\r\n  });\r\n\r\n  if (missingFields.length > 0) {\r\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\r\n  }\r\n\r\n  // Add all form fields\r\n  Object.entries(formData).forEach(([key, value]) => {\r\n    if (key === 'category_id' && typeof value === 'string') {\r\n      // Send as integer for proper validation - this should never be empty due to validation above\r\n      if (value.trim() !== '') {\r\n        const parsedValue = parseInt(value);\r\n        if (!isNaN(parsedValue) && parsedValue > 0) {\r\n          formDataToSubmit.append(key, parsedValue.toString());\r\n        } else {\r\n          throw new Error(`Invalid category_id: ${value}`);\r\n        }\r\n      } else {\r\n        throw new Error('Category is required');\r\n      }\r\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\r\n      // Only append subcategory_id if it's not empty and send as integer\r\n      if (value.trim() !== '') {\r\n        const parsedValue = parseInt(value);\r\n        if (!isNaN(parsedValue)) {\r\n          formDataToSubmit.append(key, parsedValue.toString());\r\n        }\r\n      }\r\n      // Skip empty subcategory_id - it's optional\r\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\r\n      // Skip scheduled_publish_at if not scheduling\r\n      return;\r\n    } else if (typeof value === 'boolean') {\r\n      // Send boolean values as proper boolean strings for express-validator\r\n      formDataToSubmit.append(key, value.toString());\r\n    } else if (value !== null && value !== undefined) {\r\n      formDataToSubmit.append(key, value.toString());\r\n    }\r\n    // Skip null/undefined values\r\n  });\r\n\r\n  // Add files\r\n  if (files.length > 0) {\r\n    files.forEach((file) => {\r\n      formDataToSubmit.append(fileFieldName, file);\r\n    });\r\n  }\r\n\r\n  return formDataToSubmit;\r\n};\r\n\r\n/**\r\n * Validates common form fields\r\n * @param formData - Form data to validate\r\n * @param rules - Validation rules\r\n */\r\nexport const validateFormFields = (\r\n  formData: FormField,\r\n  rules: {\r\n    required?: string[];\r\n    maxLength?: { [key: string]: number };\r\n    custom?: { [key: string]: (value: any) => string | null };\r\n  } = {}\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n  const { required = [], maxLength = {}, custom = {} } = rules;\r\n\r\n  // Check required fields\r\n  required.forEach(field => {\r\n    const value = formData[field];\r\n    if (!value || (typeof value === 'string' && !value.trim())) {\r\n      errors[field] = `${field.replace('_', ' ')} is required`;\r\n    }\r\n  });\r\n\r\n  // Check max length\r\n  Object.entries(maxLength).forEach(([field, max]) => {\r\n    const value = formData[field];\r\n    if (typeof value === 'string' && value.length > max) {\r\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\r\n    }\r\n  });\r\n\r\n  // Apply custom validation\r\n  Object.entries(custom).forEach(([field, validator]) => {\r\n    const value = formData[field];\r\n    const error = validator(value);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n/**\r\n * Common validation rules for announcements\r\n */\r\nexport const announcementValidationRules = {\r\n  required: ['title', 'content', 'category_id'],\r\n  maxLength: { title: 255 },\r\n  custom: {\r\n    scheduled_publish_at: (value: any, formData?: FormField) => {\r\n      if (formData?.status === 'scheduled' && !value) {\r\n        return 'Scheduled publish date is required for scheduled announcements';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Formats file size for display\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Validates file type and size\r\n * @param file - File to validate\r\n * @param options - Validation options\r\n */\r\nexport const validateFile = (\r\n  file: File,\r\n  options: {\r\n    maxSize?: number;\r\n    allowedTypes?: string[];\r\n  } = {}\r\n): string | null => {\r\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\r\n\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  if (file.size > maxSize) {\r\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAGC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG;EAAS,CAAC,GAAGF,OAAO;EACtE,MAAMG,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACA,MAAMC,cAAc,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC1D,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI;IACnD,MAAMC,KAAK,GAAGX,QAAQ,CAACU,KAAK,CAAC;IAC7B,OAAO,CAACC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAE;EAC/D,CAAC,CAAC;EAEF,IAAIJ,aAAa,CAACK,MAAM,GAAG,CAAC,EAAE;IAC5B,MAAM,IAAIC,KAAK,CAAC,4BAA4BN,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzE;;EAEA;EACAC,MAAM,CAACC,OAAO,CAACjB,QAAQ,CAAC,CAACkB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAER,KAAK,CAAC,KAAK;IACjD,IAAIQ,GAAG,KAAK,aAAa,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;MACtD;MACA,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMQ,WAAW,GAAGC,QAAQ,CAACV,KAAK,CAAC;QACnC,IAAI,CAACW,KAAK,CAACF,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;UAC1Cf,gBAAgB,CAACkB,MAAM,CAACJ,GAAG,EAAEC,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QACtD,CAAC,MAAM;UACL,MAAM,IAAIV,KAAK,CAAC,wBAAwBH,KAAK,EAAE,CAAC;QAClD;MACF,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,MAAM,IAAIK,GAAG,KAAK,gBAAgB,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMQ,WAAW,GAAGC,QAAQ,CAACV,KAAK,CAAC;QACnC,IAAI,CAACW,KAAK,CAACF,WAAW,CAAC,EAAE;UACvBf,gBAAgB,CAACkB,MAAM,CAACJ,GAAG,EAAEC,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QACtD;MACF;MACA;IACF,CAAC,MAAM,IAAIL,GAAG,KAAK,sBAAsB,IAAIhB,iBAAiB,IAAIH,QAAQ,CAACyB,MAAM,KAAK,WAAW,EAAE;MACjG;MACA;IACF,CAAC,MAAM,IAAI,OAAOd,KAAK,KAAK,SAAS,EAAE;MACrC;MACAN,gBAAgB,CAACkB,MAAM,CAACJ,GAAG,EAAER,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC,MAAM,IAAIb,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKe,SAAS,EAAE;MAChDrB,gBAAgB,CAACkB,MAAM,CAACJ,GAAG,EAAER,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;IAChD;IACA;EACF,CAAC,CAAC;;EAEF;EACA,IAAIvB,KAAK,CAACY,MAAM,GAAG,CAAC,EAAE;IACpBZ,KAAK,CAACiB,OAAO,CAAES,IAAI,IAAK;MACtBtB,gBAAgB,CAACkB,MAAM,CAACnB,aAAa,EAAEuB,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAOtB,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuB,kBAAkB,GAAGA,CAChC5B,QAAmB,EACnB6B,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACb,OAAO,CAACR,KAAK,IAAI;IACxB,MAAMC,KAAK,GAAGX,QAAQ,CAACU,KAAK,CAAC;IAC7B,IAAI,CAACC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAE,EAAE;MAC1DkB,MAAM,CAACpB,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACwB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACAlB,MAAM,CAACC,OAAO,CAACe,SAAS,CAAC,CAACd,OAAO,CAAC,CAAC,CAACR,KAAK,EAAEyB,GAAG,CAAC,KAAK;IAClD,MAAMxB,KAAK,GAAGX,QAAQ,CAACU,KAAK,CAAC;IAC7B,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,MAAM,GAAGsB,GAAG,EAAE;MACnDL,MAAM,CAACpB,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACwB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACAnB,MAAM,CAACC,OAAO,CAACgB,MAAM,CAAC,CAACf,OAAO,CAAC,CAAC,CAACR,KAAK,EAAE0B,SAAS,CAAC,KAAK;IACrD,MAAMzB,KAAK,GAAGX,QAAQ,CAACU,KAAK,CAAC;IAC7B,MAAM2B,KAAK,GAAGD,SAAS,CAACzB,KAAK,CAAC;IAC9B,IAAI0B,KAAK,EAAE;MACTP,MAAM,CAACpB,KAAK,CAAC,GAAG2B,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOP,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMQ,2BAA2B,GAAG;EACzCP,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEO,KAAK,EAAE;EAAI,CAAC;EACzBN,MAAM,EAAE;IACNO,oBAAoB,EAAEA,CAAC7B,KAAU,EAAEX,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyB,MAAM,MAAK,WAAW,IAAI,CAACd,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8B,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACR,KAAK,CAAC,GAAGM,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,CAAC;EAEnD,OAAOO,UAAU,CAAC,CAACT,KAAK,GAAGM,IAAI,CAACI,GAAG,CAACR,CAAC,EAAEG,CAAC,CAAC,EAAEM,OAAO,CAACR,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,YAAY,GAAGA,CAC1B3B,IAAU,EACVzB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEqD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGtD,OAAO;EAEpH,IAAI,CAACsD,YAAY,CAACC,QAAQ,CAAC9B,IAAI,CAAC+B,IAAI,CAAC,EAAE;IACrC,OAAO,aAAa/B,IAAI,CAAC+B,IAAI,qCAAqCF,YAAY,CAACzC,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIY,IAAI,CAACgC,IAAI,GAAGJ,OAAO,EAAE;IACvB,OAAO,aAAad,cAAc,CAACd,IAAI,CAACgC,IAAI,CAAC,oCAAoClB,cAAc,CAACc,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}