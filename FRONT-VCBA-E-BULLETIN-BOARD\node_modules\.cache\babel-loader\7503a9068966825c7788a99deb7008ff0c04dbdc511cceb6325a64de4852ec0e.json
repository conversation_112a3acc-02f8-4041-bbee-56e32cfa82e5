{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 6h8\",\n  key: \"zvc2xc\"\n}], [\"path\", {\n  d: \"M12 16h6\",\n  key: \"yi5mkt\"\n}], [\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M8 11h7\",\n  key: \"wz2hg0\"\n}]];\nconst ChartGantt = createLucideIcon(\"chart-gantt\", __iconNode);\nexport { __iconNode, ChartGantt as default };\n//# sourceMappingURL=chart-gantt.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}