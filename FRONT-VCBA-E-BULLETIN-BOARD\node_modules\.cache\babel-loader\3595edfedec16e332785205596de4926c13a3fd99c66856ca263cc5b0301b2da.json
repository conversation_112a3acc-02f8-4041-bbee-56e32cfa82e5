{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n  key: \"yt0hxn\"\n}]];\nconst Hexagon = createLucideIcon(\"hexagon\", __iconNode);\nexport { __iconNode, Hexagon as default };\n//# sourceMappingURL=hexagon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}