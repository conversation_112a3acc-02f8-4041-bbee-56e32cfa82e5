{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 3 2 5v15c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5Z\",\n  key: \"1n2rgs\"\n}], [\"path\", {\n  d: \"M6 8h4\",\n  key: \"utf9t1\"\n}], [\"path\", {\n  d: \"M6 18h4\",\n  key: \"12yh4b\"\n}], [\"path\", {\n  d: \"m12 3-2 2v15c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5Z\",\n  key: \"3ha7mj\"\n}], [\"path\", {\n  d: \"M14 8h4\",\n  key: \"1r8wg2\"\n}], [\"path\", {\n  d: \"M14 18h4\",\n  key: \"1t3kbu\"\n}], [\"path\", {\n  d: \"m20 3-2 2v15c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5Z\",\n  key: \"dfd4e2\"\n}]];\nconst Fen<PERSON> = createLucideIcon(\"fence\", __iconNode);\nexport { __iconNode, Fence as default };\n//# sourceMappingURL=fence.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}