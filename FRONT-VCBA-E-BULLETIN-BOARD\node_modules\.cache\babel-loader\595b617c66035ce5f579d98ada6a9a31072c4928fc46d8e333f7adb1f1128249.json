{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9.5 13.866a4 4 0 0 1 5 .01\",\n  key: \"1wy54i\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}], [\"path\", {\n  d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n  key: \"1d0kgt\"\n}], [\"path\", {\n  d: \"M7 10.754a8 8 0 0 1 10 0\",\n  key: \"exoy2g\"\n}]];\nconst HouseWifi = createLucideIcon(\"house-wifi\", __iconNode);\nexport { __iconNode, HouseWifi as default };\n//# sourceMappingURL=house-wifi.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}