{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 2 2 22\",\n  key: \"y4kqgn\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst CircleSlash2 = createLucideIcon(\"circle-slash-2\", __iconNode);\nexport { __iconNode, CircleSlash2 as default };\n//# sourceMappingURL=circle-slash-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}