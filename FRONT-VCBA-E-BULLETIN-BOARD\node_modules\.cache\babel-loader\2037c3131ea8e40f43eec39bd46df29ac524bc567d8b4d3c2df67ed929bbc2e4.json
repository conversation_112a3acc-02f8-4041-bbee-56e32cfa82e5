{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 15 4-8 4 8\",\n  key: \"1vwr6u\"\n}], [\"path\", {\n  d: \"M4 13h6\",\n  key: \"1r9ots\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1kchzo\"\n}], [\"path\", {\n  d: \"M21 9v6\",\n  key: \"anns31\"\n}]];\nconst CaseSensitive = createLucideIcon(\"case-sensitive\", __iconNode);\nexport { __iconNode, CaseSensitive as default };\n//# sourceMappingURL=case-sensitive.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}