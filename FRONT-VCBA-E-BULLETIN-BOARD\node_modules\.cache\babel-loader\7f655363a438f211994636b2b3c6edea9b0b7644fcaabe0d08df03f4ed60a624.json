{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 14h.01\",\n  key: \"7oqj8z\"\n}], [\"path\", {\n  d: \"M7 7h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14\",\n  key: \"u1rqew\"\n}]];\nconst WalletMinimal = createLucideIcon(\"wallet-minimal\", __iconNode);\nexport { __iconNode, WalletMinimal as default };\n//# sourceMappingURL=wallet-minimal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}