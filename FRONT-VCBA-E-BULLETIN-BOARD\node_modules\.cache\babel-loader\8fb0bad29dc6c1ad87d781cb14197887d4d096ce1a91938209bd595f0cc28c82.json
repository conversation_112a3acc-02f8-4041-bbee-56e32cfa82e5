{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8.35 2.69A10 10 0 0 1 21.3 15.65\",\n  key: \"1pfsoa\"\n}], [\"path\", {\n  d: \"M19.08 19.08A10 10 0 1 1 4.92 4.92\",\n  key: \"1ablyi\"\n}]];\nconst CircleOff = createLucideIcon(\"circle-off\", __iconNode);\nexport { __iconNode, CircleOff as default };\n//# sourceMappingURL=circle-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}