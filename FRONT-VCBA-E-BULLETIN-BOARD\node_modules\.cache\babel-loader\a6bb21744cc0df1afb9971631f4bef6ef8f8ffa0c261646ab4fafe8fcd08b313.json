{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.354 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14v6a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341l1.218-1.348\",\n  key: \"8mvsmf\"\n}], [\"path\", {\n  d: \"M16 6h6\",\n  key: \"1dogtp\"\n}], [\"path\", {\n  d: \"M19 3v6\",\n  key: \"1ytpjt\"\n}]];\nconst FunnelPlus = createLucideIcon(\"funnel-plus\", __iconNode);\nexport { __iconNode, FunnelPlus as default };\n//# sourceMappingURL=funnel-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}