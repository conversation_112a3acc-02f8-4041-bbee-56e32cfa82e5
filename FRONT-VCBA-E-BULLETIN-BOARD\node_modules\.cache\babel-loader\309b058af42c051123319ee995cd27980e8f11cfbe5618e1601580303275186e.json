{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport{API_ENDPOINTS,ADMIN_USER_DATA_KEY,ADMIN_REFRESH_TOKEN_KEY,ADMIN_AUTH_TOKEN_KEY,API_BASE_URL}from'../config/constants';// Admin-specific token manager\nclass AdminTokenManager{getToken(){return localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);}setToken(token){localStorage.setItem(ADMIN_AUTH_TOKEN_KEY,token);}removeToken(){localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);}getAuthHeaders(){const token=this.getToken();return token?{Authorization:\"Bearer \".concat(token)}:{};}}const adminTokenManager=new AdminTokenManager();export class AdminAuthService{/**\n   * Custom request method with admin token\n   */static async request(method,endpoint,data){let retryCount=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;const token=adminTokenManager.getToken();const headers={};// Only set Content-Type for non-FormData requests\nif(!(data instanceof FormData)){headers['Content-Type']='application/json';}if(token){headers['Authorization']=\"Bearer \".concat(token);}const config={method,headers,credentials:'include'};if(data&&(method==='POST'||method==='PUT'||method==='PATCH')){// Handle FormData differently - don't stringify it\nconfig.body=data instanceof FormData?data:JSON.stringify(data);}const url=\"\".concat(API_BASE_URL).concat(endpoint);try{console.log(\"\\uD83D\\uDD04 Admin API Request: \".concat(method,\" \").concat(url));const response=await fetch(url,config);if(!response.ok){var _responseData$error;if(response.status===401){// Token expired or invalid - clear auth and redirect\nadminTokenManager.removeToken();throw new Error('Authentication required');}const responseData=await response.json().catch(()=>({message:'Request failed'}));const errorMessage=(responseData===null||responseData===void 0?void 0:responseData.message)||(responseData===null||responseData===void 0?void 0:(_responseData$error=responseData.error)===null||_responseData$error===void 0?void 0:_responseData$error.message)||\"HTTP \".concat(response.status);throw new Error(errorMessage);}const responseData=await response.json();console.log(\"\\u2705 Admin API Success: \".concat(method,\" \").concat(endpoint));return responseData;}catch(error){console.error(\"\\u274C Admin API request failed: \".concat(method,\" \").concat(endpoint),error);// Retry logic for network errors\nif(retryCount<2&&(error instanceof TypeError||error instanceof Error&&error.message.includes('Failed to fetch'))){console.log(\"\\uD83D\\uDD04 Retrying request (attempt \".concat(retryCount+1,\"/3)...\"));await new Promise(resolve=>setTimeout(resolve,1000*(retryCount+1)));// Exponential backoff\nreturn this.request(method,endpoint,data,retryCount+1);}// Enhanced error information for connection issues\nif(error instanceof TypeError&&error.message.includes('Failed to fetch')){throw new Error(\"\\u274C Network connection failed. Please check:\\n1. Backend server is running on \".concat(API_BASE_URL,\"\\n2. CORS is properly configured\\n3. No firewall blocking the connection\"));}throw error;}}/**\n   * Get current authenticated admin user\n   */static async getCurrentUser(){try{const token=adminTokenManager.getToken();if(!token){return null;}const response=await this.request('GET',API_ENDPOINTS.AUTH.PROFILE);if(response&&response.user){return response.user;}return null;}catch(error){console.error('Failed to get current admin user:',error);return null;}}/**\n   * Login admin user\n   */static async login(credentials){try{const response=await this.request('POST',API_ENDPOINTS.AUTH.LOGIN,_objectSpread(_objectSpread({},credentials),{},{userType:'admin'}));if(response&&response.success&&response.data){// Transform raw database user data to frontend format\nconst rawUser=response.data.user;const transformedUser={id:rawUser.admin_id||rawUser.student_id,email:rawUser.email,role:rawUser.admin_id?'admin':'student',firstName:rawUser.first_name,lastName:rawUser.last_name,middleName:rawUser.middle_name,suffix:rawUser.suffix,phoneNumber:rawUser.phone_number||rawUser.phone,department:rawUser.department,position:rawUser.position,grade_level:rawUser.grade_level,studentNumber:rawUser.student_number,profilePicture:rawUser.profile_picture,isActive:Boolean(rawUser.is_active),lastLogin:rawUser.last_login,createdAt:rawUser.account_created_at||rawUser.created_at,updatedAt:rawUser.account_updated_at||rawUser.updated_at};// Verify the user is actually an admin\nif(transformedUser.role!=='admin'){throw new Error('Access denied: Admin privileges required');}// Store tokens and transformed user data in admin-specific keys\nadminTokenManager.setToken(response.data.accessToken);if(response.data.refreshToken){localStorage.setItem(ADMIN_REFRESH_TOKEN_KEY,response.data.refreshToken);}localStorage.setItem(ADMIN_USER_DATA_KEY,JSON.stringify(transformedUser));return{success:true,message:response.message||'Admin login successful',data:_objectSpread(_objectSpread({},response.data),{},{user:transformedUser})};}throw new Error((response===null||response===void 0?void 0:response.message)||'Admin login failed');}catch(error){console.error('AdminAuthService.login error:',error);throw new Error(error.message||'Admin login failed');}}/**\n   * Register admin user\n   */static async registerAdmin(data){try{const response=await this.request('POST',API_ENDPOINTS.AUTH.ADMIN_REGISTER,data);if(response&&response.success){return{success:true,message:response.message||'Admin registration initiated successfully',data:response.data};}throw new Error((response===null||response===void 0?void 0:response.message)||'Admin registration failed');}catch(error){console.error('AdminAuthService.registerAdmin error:',error);throw new Error(error.message||'Admin registration failed');}}/**\n   * Verify OTP for admin registration\n   */static async verifyOtp(data){try{const response=await this.request('POST',API_ENDPOINTS.AUTH.VERIFY_OTP,data);if(response&&response.success){return{success:true,message:response.message||'OTP verification successful',data:response.data};}throw new Error((response===null||response===void 0?void 0:response.message)||'OTP verification failed');}catch(error){console.error('AdminAuthService.verifyOtp error:',error);throw new Error(error.message||'OTP verification failed');}}/**\n   * Resend OTP for admin registration\n   */static async resendOtp(email){try{const response=await this.request('POST',API_ENDPOINTS.AUTH.RESEND_OTP,{email});if(response&&response.success){return response;}throw new Error((response===null||response===void 0?void 0:response.message)||'Failed to resend OTP');}catch(error){console.error('AdminAuthService.resendOtp error:',error);throw new Error(error.message||'Failed to resend OTP');}}/**\n   * Logout admin user\n   */static async logout(){try{console.log('🚪 AdminAuthService - Calling server logout endpoint');await this.request('POST',API_ENDPOINTS.AUTH.LOGOUT);console.log('✅ AdminAuthService - Server logout successful');}catch(error){console.warn('⚠️ AdminAuthService - Server logout failed, continuing with local logout:',error);}finally{console.log('🧹 AdminAuthService - Clearing admin local storage');this.clearLocalStorage();}}/**\n   * Clear admin local storage and tokens\n   */static clearLocalStorage(){console.log('🧹 AdminAuthService - Clearing admin authentication data');adminTokenManager.removeToken();localStorage.removeItem(ADMIN_USER_DATA_KEY);localStorage.removeItem(ADMIN_REFRESH_TOKEN_KEY);console.log('✅ AdminAuthService - Admin authentication data cleared');}/**\n   * Check if admin is authenticated\n   */static isAuthenticated(){const token=adminTokenManager.getToken();const userData=this.getStoredUser();return!!(token&&userData&&userData.role==='admin');}/**\n   * Get stored admin user data\n   */static getStoredUser(){try{const userData=localStorage.getItem(ADMIN_USER_DATA_KEY);const user=userData?JSON.parse(userData):null;return user&&user.role==='admin'?user:null;}catch(error){console.error('Error parsing stored admin user data:',error);return null;}}/**\n   * Get admin user role\n   */static getUserRole(){const user=this.getStoredUser();return(user===null||user===void 0?void 0:user.role)==='admin'?'admin':null;}/**\n   * HTTP GET method with admin authentication\n   */static async get(endpoint){return this.request('GET',endpoint);}/**\n   * HTTP POST method with admin authentication\n   */static async post(endpoint,data){return this.request('POST',endpoint,data);}/**\n   * HTTP PUT method with admin authentication\n   */static async put(endpoint,data){return this.request('PUT',endpoint,data);}/**\n   * HTTP DELETE method with admin authentication\n   */static async delete(endpoint){return this.request('DELETE',endpoint);}/**\n   * Upload profile picture\n   */static async uploadProfilePicture(file){console.log('📤 AdminAuthService - Uploading profile picture:',file.name);try{const formData=new FormData();formData.append('profilePicture',file);// Get the token for authorization\nconst token=adminTokenManager.getToken();if(!token){throw new Error('No authentication token found');}// Make direct fetch request with proper headers for file upload\nconst response=await fetch(\"\".concat(API_BASE_URL,\"/api/admin/profile/picture\"),{method:'POST',headers:{'Authorization':\"Bearer \".concat(token)// Don't set Content-Type for FormData - browser will set it with boundary\n},body:formData});const result=await response.json();if(!response.ok||!result.success){var _result$error;throw new Error(((_result$error=result.error)===null||_result$error===void 0?void 0:_result$error.message)||'Failed to upload profile picture');}console.log('✅ AdminAuthService - Profile picture uploaded successfully');// Transform the admin data\nconst rawAdmin=result.data.admin;const transformedAdmin={id:rawAdmin.admin_id,email:rawAdmin.email,role:'admin',firstName:rawAdmin.first_name,lastName:rawAdmin.last_name,middleName:rawAdmin.middle_name,suffix:rawAdmin.suffix,phoneNumber:rawAdmin.phone_number,department:rawAdmin.department,position:rawAdmin.position,grade_level:rawAdmin.grade_level,profilePicture:rawAdmin.profile_picture,isActive:Boolean(rawAdmin.is_active),lastLogin:rawAdmin.last_login,createdAt:rawAdmin.account_created_at,updatedAt:rawAdmin.account_updated_at};// Update stored user data\nlocalStorage.setItem(ADMIN_USER_DATA_KEY,JSON.stringify(transformedAdmin));return{admin:transformedAdmin,profilePicture:result.data.profilePicture};}catch(error){console.error('❌ AdminAuthService - Profile picture upload failed:',error);throw error;}}/**\n   * Remove profile picture\n   */static async removeProfilePicture(){console.log('🗑️ AdminAuthService - Removing profile picture');const response=await this.request('DELETE','/api/admin/profile/picture');if(!response.success){var _response$error;throw new Error(((_response$error=response.error)===null||_response$error===void 0?void 0:_response$error.message)||'Failed to remove profile picture');}console.log('✅ AdminAuthService - Profile picture removed successfully');// Transform the admin data\nconst rawAdmin=response.data.admin;const transformedAdmin={id:rawAdmin.admin_id,email:rawAdmin.email,role:'admin',firstName:rawAdmin.first_name,lastName:rawAdmin.last_name,middleName:rawAdmin.middle_name,suffix:rawAdmin.suffix,phoneNumber:rawAdmin.phone_number,department:rawAdmin.department,position:rawAdmin.position,grade_level:rawAdmin.grade_level,profilePicture:rawAdmin.profile_picture,isActive:Boolean(rawAdmin.is_active),lastLogin:rawAdmin.last_login,createdAt:rawAdmin.account_created_at,updatedAt:rawAdmin.account_updated_at};// Update stored user data\nlocalStorage.setItem(ADMIN_USER_DATA_KEY,JSON.stringify(transformedAdmin));return transformedAdmin;}}export default AdminAuthService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}