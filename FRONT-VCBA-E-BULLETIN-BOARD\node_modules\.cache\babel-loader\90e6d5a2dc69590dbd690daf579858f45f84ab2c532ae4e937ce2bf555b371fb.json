{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3v2.107\",\n  key: \"gq8xun\"\n}], [\"path\", {\n  d: \"M17 9c1 3 2.5 3.5 3.5 4.5A5 5 0 0 1 22 17a5 5 0 0 1-10 0c0-.3 0-.6.1-.9a2 2 0 1 0 3.3-2C13 11.5 16 9 17 9\",\n  key: \"1l2pih\"\n}], [\"path\", {\n  d: \"M21 8.274V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.938\",\n  key: \"jrnqjp\"\n}], [\"path\", {\n  d: \"M3 15h5.253\",\n  key: \"xqg7rb\"\n}], [\"path\", {\n  d: \"M3 9h8.228\",\n  key: \"1ppb70\"\n}], [\"path\", {\n  d: \"M8 15v6\",\n  key: \"1stoo3\"\n}], [\"path\", {\n  d: \"M8 3v6\",\n  key: \"vlvjmk\"\n}]];\nconst BrickWallFire = createLucideIcon(\"brick-wall-fire\", __iconNode);\nexport { __iconNode, BrickWallFire as default };\n//# sourceMappingURL=brick-wall-fire.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}