{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 6 7 17l-5-5\",\n  key: \"116fxf\"\n}], [\"path\", {\n  d: \"m22 10-7.5 7.5L13 16\",\n  key: \"ke71qq\"\n}]];\nconst CheckCheck = createLucideIcon(\"check-check\", __iconNode);\nexport { __iconNode, CheckCheck as default };\n//# sourceMappingURL=check-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}