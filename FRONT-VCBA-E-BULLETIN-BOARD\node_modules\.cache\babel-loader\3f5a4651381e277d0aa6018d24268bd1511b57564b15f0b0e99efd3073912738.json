{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5v14\",\n  key: \"s699le\"\n}], [\"path\", {\n  d: \"m19 12-7 7-7-7\",\n  key: \"1idqje\"\n}]];\nconst ArrowDown = createLucideIcon(\"arrow-down\", __iconNode);\nexport { __iconNode, ArrowDown as default };\n//# sourceMappingURL=arrow-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}