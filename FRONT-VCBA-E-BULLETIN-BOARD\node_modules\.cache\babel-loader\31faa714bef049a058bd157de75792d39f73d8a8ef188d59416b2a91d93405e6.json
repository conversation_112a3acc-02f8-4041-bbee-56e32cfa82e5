{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"19qu7m\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"20\",\n  x: \"14\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"24v0nk\"\n}]];\nconst StretchVertical = createLucideIcon(\"stretch-vertical\", __iconNode);\nexport { __iconNode, StretchVertical as default };\n//# sourceMappingURL=stretch-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}