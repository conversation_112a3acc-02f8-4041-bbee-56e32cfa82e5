{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41L13.7 2.71a2.41 2.41 0 0 0-3.41 0Z\",\n  key: \"1tpxz2\"\n}], [\"path\", {\n  d: \"M9.2 9.2h.01\",\n  key: \"1b7bvt\"\n}], [\"path\", {\n  d: \"m14.5 9.5-5 5\",\n  key: \"17q4r4\"\n}], [\"path\", {\n  d: \"M14.7 14.8h.01\",\n  key: \"17nsh4\"\n}]];\nconst DiamondPercent = createLucideIcon(\"diamond-percent\", __iconNode);\nexport { __iconNode, DiamondPercent as default };\n//# sourceMappingURL=diamond-percent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}