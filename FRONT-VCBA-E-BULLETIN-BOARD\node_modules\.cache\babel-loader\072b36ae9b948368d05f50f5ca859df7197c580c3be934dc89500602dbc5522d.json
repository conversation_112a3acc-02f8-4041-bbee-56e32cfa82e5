{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"m19 9-5 5-4-4-3 3\",\n  key: \"2osh9i\"\n}]];\nconst ChartLine = createLucideIcon(\"chart-line\", __iconNode);\nexport { __iconNode, ChartLine as default };\n//# sourceMappingURL=chart-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}