{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{useCategories,useAnnouncements}from'../../hooks/useAnnouncements';import{useNotificationTarget}from'../../hooks/useNotificationNavigation';import{useAdminAuth}from'../../contexts/AdminAuthContext';import AdminCommentSection from'../../components/admin/AdminCommentSection';import NotificationBell from'../../components/admin/NotificationBell';import FacebookImageGallery from'../../components/common/FacebookImageGallery';import ImageLightbox from'../../components/common/ImageLightbox';import{getImageUrl,API_BASE_URL}from'../../config/constants';import'../../styles/notificationHighlight.css';import{Newspaper,Search,Pin,Calendar,MessageSquare,Heart,Users,LayoutDashboard,BookOpen,PartyPopper,AlertTriangle,Clock,Trophy,Briefcase,GraduationCap,Flag,Coffee,Plane,ChevronDown,User,LogOut}from'lucide-react';// Custom hook for CORS-safe image loading\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}console.log('🔄 Fetching image via CORS-safe method:',fullUrl);// Fetch image as blob to bypass CORS restrictions\nconst response=await fetch(fullUrl,{method:'GET',headers:{'Authorization':\"Bearer \".concat(localStorage.getItem('adminToken')),'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);setImageUrl(objectUrl);console.log('✅ Image loaded successfully via fetch');}catch(err){console.error('❌ Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// CORS-safe image component\nconst ImageDisplay=_ref=>{let{imagePath,alt,style,className,onLoad,onMouseEnter,onMouseLeave}=_ref;const{imageUrl,loading,error}=useImageLoader(imagePath);if(loading){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Loading...\"})]})});}if(error||!imageUrl){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b',border:'2px dashed #cbd5e1'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:\"Image unavailable\"}),error&&/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',marginTop:'0.25rem',color:'#9ca3af'},children:error})]})});}return/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:alt,style:style,className:className,onLoad:e=>{console.log('✅ Image rendered successfully via CORS-safe method');onLoad===null||onLoad===void 0?void 0:onLoad(e);},onMouseEnter:onMouseEnter,onMouseLeave:onMouseLeave});};// Image Gallery Component\nconst ImageGallery=_ref2=>{let{images,altPrefix,onImageClick}=_ref2;if(!images||images.length===0)return null;const visibleImages=images.slice(0,4);const remainingCount=Math.max(0,images.length-4);const getContainerStyle=(index,total)=>{const baseStyle={position:'relative',overflow:'hidden',borderRadius:'12px',cursor:onImageClick?'pointer':'default'};if(total===1){return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'300px'});}else if(total===2){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'250px'});}else if(total===3){if(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'250px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'120px'});}}else{if(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'250px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'33.33%',height:'120px'});}}};const getImageStyle=(index,total)=>{return{width:'100%',height:'100%',objectFit:'cover',transition:'transform 0.3s ease'};};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'4px',width:'100%',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(0,visibleImages.length),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:visibleImages[0].file_path,alt:\"\".concat(altPrefix,\" - Image 1\"),style:getImageStyle(0,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(0)})]}),visibleImages.length>1&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:visibleImages.length===2?'row':'column',gap:'4px',width:visibleImages.length===2?'50%':'50%'},children:visibleImages.slice(1).map((image,idx)=>{const actualIndex=idx+1;const isLast=actualIndex===visibleImages.length-1&&remainingCount>0;return/*#__PURE__*/_jsxs(\"div\",{style:_objectSpread(_objectSpread({},getContainerStyle(actualIndex,visibleImages.length)),{},{position:'relative'}),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:image.file_path,alt:\"\".concat(altPrefix,\" - Image \").concat(actualIndex+1),style:getImageStyle(actualIndex,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),isLast&&remainingCount>0&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'1.5rem',fontWeight:'600'},children:[\"+\",remainingCount]}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(actualIndex)})]},actualIndex);})})]});};// Main AdminNewsfeed Component\nconst AdminNewsfeed=()=>{const navigate=useNavigate();// Open lightbox function for announcements\nconst openLightbox=(images,initialIndex)=>{const imageUrls=images.map(img=>getImageUrl(img.file_path)).filter(Boolean);setLightboxImages(imageUrls);setLightboxInitialIndex(initialIndex);setLightboxOpen(true);};// Open lightbox function for image URLs (calendar events)\nconst openLightboxWithUrls=(imageUrls,initialIndex)=>{setLightboxImages(imageUrls);setLightboxInitialIndex(initialIndex);setLightboxOpen(true);};// Category styling function\nconst getCategoryStyle=categoryName=>{const styles={'ACADEMIC':{background:'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',icon:BookOpen},'GENERAL':{background:'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',icon:Users},'EVENTS':{background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',icon:PartyPopper},'EMERGENCY':{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',icon:AlertTriangle},'SPORTS':{background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',icon:Trophy},'DEADLINES':{background:'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',icon:Clock}};return styles[categoryName]||styles['GENERAL'];};// Holiday type styling function\nconst getHolidayTypeStyle=holidayTypeName=>{const styles={'National Holiday':{background:'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',icon:Flag},'School Event':{background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',icon:GraduationCap},'Academic Break':{background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',icon:Coffee},'Sports Event':{background:'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',icon:Trophy},'Field Trip':{background:'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',icon:Plane},'Meeting':{background:'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',icon:Briefcase}};return styles[holidayTypeName]||styles['School Event'];};// Filter states\nconst[filterCategory,setFilterCategory]=useState('');const[filterGradeLevel,setFilterGradeLevel]=useState('');const[searchTerm,setSearchTerm]=useState('');// UI states\nconst[showComments,setShowComments]=useState(null);const[selectedPinnedPost,setSelectedPinnedPost]=useState(null);const[showUserDropdown,setShowUserDropdown]=useState(false);// Lightbox states\nconst[lightboxOpen,setLightboxOpen]=useState(false);const[lightboxImages,setLightboxImages]=useState([]);const[lightboxInitialIndex,setLightboxInitialIndex]=useState(0);// Data states\nconst[pinnedAnnouncements,setPinnedAnnouncements]=useState([]);const[calendarEvents,setCalendarEvents]=useState([]);const[calendarLoading,setCalendarLoading]=useState(false);const[calendarError,setCalendarError]=useState();const[recentStudents,setRecentStudents]=useState([]);const[studentLoading,setStudentLoading]=useState(false);const{categories}=useCategories();// Get admin user context\nconst{user:adminUser,logout}=useAdminAuth();// Use the announcements hook for proper state management (admin service)\nconst{announcements,loading,error,likeAnnouncement,unlikeAnnouncement,refresh:refreshAnnouncements}=useAnnouncements({status:'published',page:1,limit:50,sort_by:'created_at',sort_order:'DESC'},true);// true for admin service\n// Handle notification-triggered navigation\nconst{isFromNotification,notificationId,scrollTarget}=useNotificationTarget();// Update pinned announcements when announcements change\nuseEffect(()=>{const pinned=announcements.filter(ann=>ann.is_pinned===1);setPinnedAnnouncements(pinned);},[announcements]);// Fetch calendar events\nconst fetchCalendarEvents=async()=>{try{setCalendarLoading(true);setCalendarError(undefined);const response=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar?limit=50&sort_by=event_date&sort_order=ASC\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem('adminToken')),'Content-Type':'application/json'}});const data=await response.json();if(data.success&&data.data){const eventsData=data.data.events||data.data||[];// Fetch images for each event\nconst eventsWithImages=await Promise.all(eventsData.map(async event=>{try{const imageResponse=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar/\").concat(event.calendar_id,\"/images\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem('adminToken')),'Content-Type':'application/json'}});const imageData=await imageResponse.json();if(imageData.success&&imageData.data){event.images=imageData.data.attachments||[];}else{event.images=[];}}catch(imgErr){console.warn(\"Failed to fetch images for event \".concat(event.calendar_id,\":\"),imgErr);event.images=[];}return event;}));setCalendarEvents(eventsWithImages);}else{setCalendarError('Failed to load calendar events');}}catch(err){console.error('Error fetching calendar events:',err);setCalendarError(err.message||'Failed to load calendar events');}finally{setCalendarLoading(false);}};// Fetch recent student registrations\nconst fetchRecentStudents=async()=>{try{setStudentLoading(true);const response=await fetch(\"\".concat(API_BASE_URL,\"/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem('adminToken')),'Content-Type':'application/json'}});const data=await response.json();if(data.success&&data.data){setRecentStudents(data.data.students||[]);}}catch(err){console.error('Error fetching recent students:',err);}finally{setStudentLoading(false);}};// Initial data fetch\nuseEffect(()=>{fetchCalendarEvents();// fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n},[]);// Handle like/unlike functionality (admin perspective)\nconst handleLikeToggle=async announcement=>{try{console.log('[DEBUG] Admin toggling reaction for announcement:',announcement.announcement_id);console.log('[DEBUG] Current user_reaction:',announcement.user_reaction);console.log('[DEBUG] Admin user context:',{id:adminUser===null||adminUser===void 0?void 0:adminUser.id,role:'admin'});if(announcement.user_reaction){// Unlike the announcement\nconsole.log('[DEBUG] Admin removing reaction...');await unlikeAnnouncement(announcement.announcement_id);}else{// Like the announcement\nconsole.log('[DEBUG] Admin adding reaction...');await likeAnnouncement(announcement.announcement_id,1);}console.log('[SUCCESS] Admin reaction toggled successfully');}catch(error){console.error('[ERROR] Admin error toggling like:',error);}};// Handle logout\nconst handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);// Force redirect even if logout fails\nwindow.location.href='/admin/login';}};// Close dropdown when clicking outside\nuseEffect(()=>{const handleClickOutside=event=>{if(showUserDropdown){const target=event.target;if(!target.closest('[data-dropdown=\"user-dropdown\"]')){setShowUserDropdown(false);}}};document.addEventListener('mousedown',handleClickOutside);return()=>document.removeEventListener('mousedown',handleClickOutside);},[showUserDropdown]);// Filter announcements\nconst filteredAnnouncements=announcements.filter(announcement=>{var _announcement$categor,_announcement$grade_l;const matchesSearch=!searchTerm||announcement.title.toLowerCase().includes(searchTerm.toLowerCase())||announcement.content.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=!filterCategory||((_announcement$categor=announcement.category_id)===null||_announcement$categor===void 0?void 0:_announcement$categor.toString())===filterCategory;const matchesGradeLevel=!filterGradeLevel||((_announcement$grade_l=announcement.grade_level)===null||_announcement$grade_l===void 0?void 0:_announcement$grade_l.toString())===filterGradeLevel;return matchesSearch&&matchesCategory&&matchesGradeLevel;});// Filter calendar events with date-based filtering\nconst filteredCalendarEvents=calendarEvents.filter(event=>{const matchesSearch=!searchTerm||event.title.toLowerCase().includes(searchTerm.toLowerCase())||event.description&&event.description.toLowerCase().includes(searchTerm.toLowerCase());// Only show events that are published and on or after today's date\nconst today=new Date();today.setHours(0,0,0,0);// Reset time to start of day\nconst eventDate=new Date(event.event_date);eventDate.setHours(0,0,0,0);// Reset time to start of day\nconst isEventDateValid=eventDate>=today;const isPublished=event.is_published===1;return matchesSearch&&isEventDateValid&&isPublished;});// Combine and sort all content by date (most recent first)\nconst displayAnnouncements=filteredAnnouncements;const displayEvents=filteredCalendarEvents;// Create combined content array for better chronological display\nconst combinedContent=[...displayAnnouncements.map(item=>_objectSpread(_objectSpread({},item),{},{type:'announcement',sortDate:new Date(item.created_at)})),...displayEvents.map(item=>_objectSpread(_objectSpread({},item),{},{type:'event',sortDate:new Date(item.event_date)}))].sort((a,b)=>b.sortDate.getTime()-a.sortDate.getTime());return/*#__PURE__*/_jsxs(\"div\",{style:{minHeight:'100vh',background:'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',position:'relative'},children:[/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundImage:\"\\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\\n        \",pointerEvents:'none'}}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsx(\"header\",{style:{background:'white',borderBottom:'1px solid #e5e7eb',position:'sticky',top:0,zIndex:100,boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0 2rem',height:'72px',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem',minWidth:'300px'},children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",style:{width:'48px',height:'48px',objectFit:'contain'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{margin:0,fontSize:'1.25rem',fontWeight:'600',color:'#111827',lineHeight:'1.2'},children:\"VCBA E-Bulletin Board\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#6b7280',lineHeight:'1.2'},children:\"Admin Newsfeed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,maxWidth:'500px',margin:'0 2rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(Search,{size:20,style:{position:'absolute',left:'1rem',top:'50%',transform:'translateY(-50%)',color:'#9ca3af'}}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search post\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{width:'100%',height:'44px',padding:'0 1rem 0 3rem',border:'1px solid #d1d5db',borderRadius:'12px',background:'#f9fafb',color:'#374151',fontSize:'0.875rem',outline:'none',transition:'all 0.2s ease'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.background='white';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.background='#f9fafb';e.currentTarget.style.boxShadow='none';}})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',minWidth:'400px',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem',background:'#f9fafb',borderRadius:'12px',border:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsxs(\"select\",{value:filterCategory,onChange:e=>setFilterCategory(e.target.value),style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'white',color:'#374151',fontSize:'0.875rem',outline:'none',cursor:'pointer',minWidth:'110px'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.category_id.toString(),children:category.name},category.category_id))]}),/*#__PURE__*/_jsxs(\"select\",{value:filterGradeLevel,onChange:e=>setFilterGradeLevel(e.target.value),style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'white',color:'#374151',fontSize:'0.875rem',outline:'none',cursor:'pointer',minWidth:'100px'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Grades\"}),/*#__PURE__*/_jsx(\"option\",{value:\"11\",children:\"Grade 11\"}),/*#__PURE__*/_jsx(\"option\",{value:\"12\",children:\"Grade 12\"})]}),(searchTerm||filterCategory||filterGradeLevel)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setSearchTerm('');setFilterCategory('');setFilterGradeLevel('');},style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'#ef4444',color:'white',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#dc2626';},onMouseLeave:e=>{e.currentTarget.style.background='#ef4444';},children:\"Clear\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(NotificationBell,{}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},\"data-dropdown\":\"user-dropdown\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowUserDropdown(!showUserDropdown),style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.75rem 1rem',background:'white',border:'1px solid #d1d5db',borderRadius:'12px',color:'#374151',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)'},onMouseEnter:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 2px 8px rgba(34, 197, 94, 0.1)';},onMouseLeave:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='0 1px 3px rgba(0, 0, 0, 0.1)';},children:[/*#__PURE__*/_jsx(User,{size:16}),/*#__PURE__*/_jsx(\"span\",{children:(adminUser===null||adminUser===void 0?void 0:adminUser.firstName)||'Admin'}),/*#__PURE__*/_jsx(ChevronDown,{size:14,style:{transform:showUserDropdown?'rotate(180deg)':'rotate(0deg)',transition:'transform 0.2s ease'}})]}),showUserDropdown&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'100%',right:0,marginTop:'0.5rem',background:'white',border:'1px solid #e5e7eb',borderRadius:'12px',boxShadow:'0 10px 25px rgba(0, 0, 0, 0.15)',minWidth:'200px',zIndex:1000,overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.75rem 1rem',borderBottom:'1px solid #f3f4f6',background:'#f9fafb'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#111827'},children:[adminUser===null||adminUser===void 0?void 0:adminUser.firstName,\" \",adminUser===null||adminUser===void 0?void 0:adminUser.lastName]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280'},children:adminUser===null||adminUser===void 0?void 0:adminUser.email})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.5rem 0'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{navigate('/admin/dashboard');setShowUserDropdown(false);},style:{width:'100%',display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem 1rem',background:'transparent',border:'none',color:'#374151',fontSize:'0.875rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(LayoutDashboard,{size:16}),\"Dashboard\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{handleLogout();setShowUserDropdown(false);},style:{width:'100%',display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem 1rem',background:'transparent',border:'none',color:'#ef4444',fontSize:'0.875rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#fef2f2';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(LogOut,{size:16}),\"Logout\"]})]})]})]})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem',display:'flex',gap:'2rem',alignItems:'flex-start'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'320px',flexShrink:0},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',border:'1px solid #e5e7eb',overflow:'hidden',position:'sticky',top:'100px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem 1.5rem 1rem',borderBottom:'1px solid #f3f4f6'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(Pin,{size:20,style:{color:'#22c55e'}}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.125rem',fontWeight:'600',color:'#111827'},children:\"Pinned Posts\"})]}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#6b7280'},children:\"Important announcements and updates\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem'},children:pinnedAnnouncements.length>0?/*#__PURE__*/_jsxs(_Fragment,{children:[pinnedAnnouncements.slice(0,3).map((announcement,index)=>{// Handle alert announcements with special styling\nconst isAlert=announcement.is_alert;const categoryName=(announcement.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);return/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',background:isAlert?'#fef2f2':'#f8fafc',borderRadius:'12px',border:isAlert?'1px solid #fecaca':'1px solid #e2e8f0',marginBottom:'1rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background=isAlert?'#fee2e2':'#f1f5f9';e.currentTarget.style.borderColor=isAlert?'#ef4444':'#22c55e';},onMouseLeave:e=>{e.currentTarget.style.background=isAlert?'#fef2f2':'#f8fafc';e.currentTarget.style.borderColor=isAlert?'#fecaca':'#e2e8f0';},onClick:()=>setSelectedPinnedPost(announcement),children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'8px',height:'8px',background:isAlert?'#ef4444':categoryStyle.background.includes('#ef4444')?'#ef4444':'#22c55e',borderRadius:'50%',marginTop:'0.5rem',flexShrink:0}}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 0.5rem 0',fontSize:'0.875rem',fontWeight:'600',color:'#111827',lineHeight:'1.4'},children:announcement.title}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 0.5rem 0',fontSize:'0.8rem',color:'#6b7280',lineHeight:'1.4'},children:announcement.content.length>80?\"\".concat(announcement.content.substring(0,80),\"...\"):announcement.content}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.75rem',color:'#9ca3af'},children:[/*#__PURE__*/_jsx(Calendar,{size:12}),/*#__PURE__*/_jsx(\"span\",{children:new Date(announcement.created_at).toLocaleDateString()})]})]})]})},announcement.announcement_id);}),pinnedAnnouncements.length>3&&/*#__PURE__*/_jsxs(\"button\",{style:{width:'100%',padding:'0.75rem',border:'1px solid #e5e7eb',borderRadius:'8px',background:'white',color:'#22c55e',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#f0fdf4';e.currentTarget.style.borderColor='#22c55e';},onMouseLeave:e=>{e.currentTarget.style.background='white';e.currentTarget.style.borderColor='#e5e7eb';},children:[\"View All \",pinnedAnnouncements.length,\" Pinned Posts\"]})]}):/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem 1rem',textAlign:'center',color:'#6b7280'},children:[/*#__PURE__*/_jsx(Pin,{size:24,style:{marginBottom:'0.5rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem'},children:\"No pinned posts available\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[(loading||calendarLoading)&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',minHeight:'400px'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'3rem',height:'3rem',border:'4px solid rgba(34, 197, 94, 0.2)',borderTop:'4px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',fontSize:'1rem',fontWeight:'500'},children:\"Loading content...\"})]})}),(error||calendarError)&&!loading&&!calendarLoading&&/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem',background:'rgba(239, 68, 68, 0.1)',border:'1px solid rgba(239, 68, 68, 0.2)',borderRadius:'16px',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'4rem',height:'4rem',background:'rgba(239, 68, 68, 0.1)',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',margin:'0 auto 1rem'},children:/*#__PURE__*/_jsx(MessageSquare,{size:24,color:\"#ef4444\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#ef4444',margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'600'},children:\"Error Loading Content\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:'0 0 1.5rem 0',fontSize:'1rem'},children:error||calendarError}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{refreshAnnouncements();fetchCalendarEvents();},style:{background:'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',color:'white',border:'none',borderRadius:'12px',padding:'0.75rem 1.5rem',fontSize:'0.875rem',fontWeight:'600',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:\"Try Again\"})]}),!loading&&!calendarLoading&&!error&&!calendarError&&displayAnnouncements.length===0&&displayEvents.length===0&&/*#__PURE__*/_jsxs(\"div\",{style:{padding:'4rem 2rem',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'5rem',height:'5rem',background:'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',margin:'0 auto 2rem'},children:/*#__PURE__*/_jsx(Newspaper,{size:32,color:\"white\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#374151',margin:'0 0 1rem 0',fontSize:'1.5rem',fontWeight:'600'},children:\"No Content Available\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:'0 0 2rem 0',fontSize:'1rem',lineHeight:'1.6',maxWidth:'500px',marginLeft:'auto',marginRight:'auto'},children:searchTerm||filterCategory||filterGradeLevel?'No content matches your current filters. Try adjusting your search criteria.':'There are no published announcements or events at the moment. Check back later for updates.'}),(searchTerm||filterCategory||filterGradeLevel)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setSearchTerm('');setFilterCategory('');setFilterGradeLevel('');},style:{background:'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',color:'white',border:'none',borderRadius:'12px',padding:'0.75rem 1.5rem',fontSize:'0.875rem',fontWeight:'600',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:\"Clear Filters\"})]}),!studentLoading&&recentStudents.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'1.25rem',fontWeight:'600',color:'#2d5016',margin:'0 0 1rem 0',display:'flex',alignItems:'center',gap:'0.5rem'},children:\"\\uD83D\\uDC65 Recent Student Registrations\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gap:'0.75rem'},children:recentStudents.slice(0,3).map(student=>{var _student$profile,_student$profile2,_student$profile3;return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',padding:'0.75rem',backgroundColor:'#f8fdf8',borderRadius:'8px',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'500',color:'#2d5016',fontSize:'0.875rem'},children:[(_student$profile=student.profile)===null||_student$profile===void 0?void 0:_student$profile.first_name,\" \",(_student$profile2=student.profile)===null||_student$profile2===void 0?void 0:_student$profile2.last_name]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280'},children:[\"Grade \",(_student$profile3=student.profile)===null||_student$profile3===void 0?void 0:_student$profile3.grade_level,\" \\u2022 \",student.student_number]})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280'},children:new Date(student.created_at).toLocaleDateString()})]},student.student_id);})})]}),!loading&&!calendarLoading&&(displayAnnouncements.length>0||displayEvents.length>0)&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[displayEvents.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:displayEvents.map(event=>/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(Calendar,{size:24,color:\"white\"})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.5rem'},children:[(()=>{const holidayTypeName=event.holiday_type_name||'School Event';const holidayStyle=getHolidayTypeStyle(holidayTypeName);const IconComponent=holidayStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:holidayStyle.background,color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),holidayTypeName]});})(),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',color:'#6b7280',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:14}),new Date(event.event_date).toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'})]})]}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'700',color:'#1f2937',lineHeight:'1.3'},children:event.title})]})]}),(()=>{// Get event images if they exist\nconst eventImageUrls=[];if(event.images&&event.images.length>0){event.images.forEach(img=>{if(img.file_path){// Convert file_path to full URL\nconst imageUrl=getImageUrl(img.file_path);if(imageUrl){eventImageUrls.push(imageUrl);}}});}return eventImageUrls.length>0?/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:eventImageUrls.filter(Boolean),altPrefix:event.title,maxVisible:4,onImageClick:index=>{const filteredImages=eventImageUrls.filter(Boolean);openLightboxWithUrls(filteredImages,index);}})}):null;})(),event.description&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#4b5563',fontSize:'0.95rem',lineHeight:'1.6',marginBottom:'1rem'},children:event.description}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem',padding:'1rem',background:'rgba(59, 130, 246, 0.05)',borderRadius:'12px',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(Calendar,{size:16}),/*#__PURE__*/_jsx(\"span\",{children:event.end_date&&event.end_date!==event.event_date?\"\".concat(new Date(event.event_date).toLocaleDateString(),\" - \").concat(new Date(event.end_date).toLocaleDateString()):new Date(event.event_date).toLocaleDateString()})]}),event.holiday_type_name&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',color:'#6b7280'},children:/*#__PURE__*/_jsx(\"span\",{style:{padding:'0.25rem 0.5rem',background:'rgba(59, 130, 246, 0.1)',borderRadius:'6px',fontSize:'0.75rem',fontWeight:'500'},children:event.holiday_type_name})})]})]},\"event-\".concat(event.calendar_id)))}),displayAnnouncements.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:displayAnnouncements.map(announcement=>/*#__PURE__*/_jsxs(\"div\",{id:\"announcement-\".concat(announcement.announcement_id),className:isFromNotification&&scrollTarget===\"announcement-\".concat(announcement.announcement_id)?'notification-highlight announcement':'',style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:announcement.is_pinned?'2px solid rgba(250, 204, 21, 0.3)':'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:announcement.is_pinned?'0 4px 20px rgba(250, 204, 21, 0.15)':'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease',position:'relative'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow=announcement.is_pinned?'0 8px 30px rgba(250, 204, 21, 0.25)':'0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow=announcement.is_pinned?'0 4px 20px rgba(250, 204, 21, 0.15)':'0 4px 20px rgba(0, 0, 0, 0.08)';},children:[announcement.is_pinned&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'-8px',right:'1rem',background:'linear-gradient(135deg, #facc15 0%, #eab308 100%)',color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem',boxShadow:'0 2px 8px rgba(250, 204, 21, 0.3)'},children:[/*#__PURE__*/_jsx(Pin,{size:12}),\"Pinned\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'1rem',marginBottom:'1rem'},children:[(()=>{if(announcement.is_alert){return/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(AlertTriangle,{size:24,color:\"white\"})});}else{const categoryName=(announcement.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:categoryStyle.background,borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(IconComponent,{size:24,color:\"white\"})});}})(),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.5rem',flexWrap:'wrap'},children:[(()=>{if(announcement.is_alert){return/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:12,color:\"white\"}),\"Alert\"]});}else{const categoryName=(announcement.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:categoryStyle.background,color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),categoryName]});}})(),announcement.grade_level&&/*#__PURE__*/_jsxs(\"span\",{style:{background:'rgba(59, 130, 246, 0.1)',color:'#3b82f6',fontSize:'0.75rem',fontWeight:'500',padding:'0.25rem 0.75rem',borderRadius:'20px'},children:[\"Grade \",announcement.grade_level]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:new Date(announcement.created_at).toLocaleDateString('en-US',{weekday:'short',year:'numeric',month:'short',day:'numeric'})})]}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'700',color:'#1f2937',lineHeight:'1.3'},children:announcement.title})]})]}),announcement.attachments&&announcement.attachments.length>0&&/*#__PURE__*/_jsx(ImageGallery,{images:announcement.attachments,altPrefix:announcement.title,onImageClick:index=>{openLightbox(announcement.attachments||[],index);}}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#4b5563',fontSize:'0.95rem',lineHeight:'1.6',marginBottom:'1rem'},children:announcement.content}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',padding:'1rem',background:'rgba(0, 0, 0, 0.02)',borderRadius:'12px',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleLikeToggle(announcement),style:{display:'flex',alignItems:'center',gap:'0.5rem',background:'none',border:'none',color:announcement.user_reaction?'#ef4444':'#6b7280',cursor:'pointer',padding:'0.5rem',borderRadius:'8px',transition:'all 0.2s ease',fontSize:'0.875rem',fontWeight:'500'},onMouseEnter:e=>{e.currentTarget.style.background='rgba(0, 0, 0, 0.05)';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:[/*#__PURE__*/_jsx(Heart,{size:18,fill:announcement.user_reaction?'#ef4444':'none'}),/*#__PURE__*/_jsx(\"span\",{children:announcement.reaction_count||0})]}),announcement.allow_comments&&/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowComments(showComments===announcement.announcement_id?null:announcement.announcement_id),style:{display:'flex',alignItems:'center',gap:'0.5rem',background:'none',border:'none',color:showComments===announcement.announcement_id?'#22c55e':'#6b7280',cursor:'pointer',padding:'0.5rem',borderRadius:'8px',transition:'all 0.2s ease',fontSize:'0.875rem',fontWeight:'500'},onMouseEnter:e=>{e.currentTarget.style.background='rgba(0, 0, 0, 0.05)';e.currentTarget.style.color='#22c55e';},onMouseLeave:e=>{e.currentTarget.style.background='none';e.currentTarget.style.color=showComments===announcement.announcement_id?'#22c55e':'#6b7280';},children:[/*#__PURE__*/_jsx(MessageSquare,{size:18}),/*#__PURE__*/_jsx(\"span\",{children:announcement.comment_count||0})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.75rem',color:'#6b7280'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Users,{size:14}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Posted by \",announcement.posted_by_name||announcement.author_name||'Admin']})]}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.25rem 0.5rem',background:announcement.status==='published'?'rgba(34, 197, 94, 0.1)':'rgba(107, 114, 128, 0.1)',color:announcement.status==='published'?'#22c55e':'#6b7280',borderRadius:'6px',fontWeight:'500'},children:announcement.status})]})]}),showComments===announcement.announcement_id&&announcement.allow_comments&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem',paddingTop:'1rem',borderTop:'1px solid rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsx(AdminCommentSection,{announcementId:announcement.announcement_id,allowComments:announcement.allow_comments,currentUserId:adminUser===null||adminUser===void 0?void 0:adminUser.id,currentUserType:\"admin\"})})]},\"announcement-\".concat(announcement.announcement_id)))})]})]})]})]}),selectedPinnedPost&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'2rem'},onClick:()=>setSelectedPinnedPost(null),children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',maxWidth:'600px',width:'100%',maxHeight:'80vh',overflow:'auto',boxShadow:'0 20px 60px rgba(0, 0, 0, 0.3)'},onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',borderBottom:'1px solid #e5e7eb',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(Pin,{size:20,style:{color:'#22c55e'}}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.25rem',fontWeight:'600',color:'#111827'},children:\"Pinned Post\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedPinnedPost(null),style:{background:'none',border:'none',fontSize:'1.5rem',color:'#6b7280',cursor:'pointer',padding:'0.25rem',borderRadius:'4px',transition:'color 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.color='#374151';},onMouseLeave:e=>{e.currentTarget.style.color='#6b7280';},children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'1rem'},children:[(()=>{if(selectedPinnedPost.is_alert){return/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:12,color:\"white\"}),\"Alert\"]});}else{const categoryName=(selectedPinnedPost.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:categoryStyle.background,color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),categoryName]});}})(),/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #facc15 0%, #eab308 100%)',color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Pin,{size:12}),\"PINNED\"]})]}),/*#__PURE__*/_jsx(\"h2\",{style:{margin:'0 0 1rem 0',fontSize:'1.5rem',fontWeight:'700',color:'#111827',lineHeight:'1.3'},children:selectedPinnedPost.title}),selectedPinnedPost.attachments&&selectedPinnedPost.attachments.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(ImageGallery,{images:selectedPinnedPost.attachments,altPrefix:selectedPinnedPost.title,onImageClick:index=>{openLightbox(selectedPinnedPost.attachments,index);}})}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#4b5563',fontSize:'1rem',lineHeight:'1.6',marginBottom:'1.5rem'},children:selectedPinnedPost.content}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:16}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Published: \",new Date(selectedPinnedPost.created_at).toLocaleDateString()]})]}),selectedPinnedPost.author_name&&/*#__PURE__*/_jsxs(\"div\",{children:[\"By: \",selectedPinnedPost.author_name]})]})]})]})}),/*#__PURE__*/_jsx(ImageLightbox,{images:lightboxImages,initialIndex:lightboxInitialIndex,isOpen:lightboxOpen,onClose:()=>setLightboxOpen(false),altPrefix:\"Announcement Image\"})]});};export default AdminNewsfeed;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}