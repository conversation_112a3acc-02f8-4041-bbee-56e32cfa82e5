{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 17V5a2 2 0 0 0-2-2H4\",\n  key: \"zz82l3\"\n}], [\"path\", {\n  d: \"M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3\",\n  key: \"1ph1d7\"\n}]];\nconst Scroll = createLucideIcon(\"scroll\", __iconNode);\nexport { __iconNode, Scroll as default };\n//# sourceMappingURL=scroll.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}