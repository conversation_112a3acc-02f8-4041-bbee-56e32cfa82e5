{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 10 5 5-5 5\",\n  key: \"qqa56n\"\n}], [\"path\", {\n  d: \"M4 4v7a4 4 0 0 0 4 4h12\",\n  key: \"z08zvw\"\n}]];\nconst CornerDownRight = createLucideIcon(\"corner-down-right\", __iconNode);\nexport { __iconNode, CornerDownRight as default };\n//# sourceMappingURL=corner-down-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}