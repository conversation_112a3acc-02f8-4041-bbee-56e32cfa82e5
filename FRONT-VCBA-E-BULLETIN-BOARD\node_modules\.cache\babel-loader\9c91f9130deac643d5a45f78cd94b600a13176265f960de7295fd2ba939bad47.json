{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 7 7 17\",\n  key: \"15tmo1\"\n}], [\"path\", {\n  d: \"M17 17H7V7\",\n  key: \"1org7z\"\n}]];\nconst ArrowDownLeft = createLucideIcon(\"arrow-down-left\", __iconNode);\nexport { __iconNode, ArrowDownLeft as default };\n//# sourceMappingURL=arrow-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}