{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"path\", {\n  d: \"M14.8 7.5a1.84 1.84 0 0 0-2.6 0l-.2.3-.3-.3a1.84 1.84 0 1 0-2.4 2.8L12 13l2.7-2.7c.9-.9.8-2.1.1-2.8\",\n  key: \"1blaws\"\n}]];\nconst MessageSquareHeart = createLucideIcon(\"message-square-heart\", __iconNode);\nexport { __iconNode, MessageSquareHeart as default };\n//# sourceMappingURL=message-square-heart.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}