{"ast": null, "code": "/**\n * Form utility functions for consistent form handling across the application\n *//**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */export const createFormData=function(formData){let files=arguments.length>1&&arguments[1]!==undefined?arguments[1]:[];let options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};const{skipScheduledDate=true,fileFieldName='images'}=options;const formDataToSubmit=new FormData();// Add all form fields with proper validation and transformation\nObject.entries(formData).forEach(_ref=>{let[key,value]=_ref;if(key==='category_id'&&typeof value==='string'){// category_id is required and must be a valid number\nconst categoryId=parseInt(value);if(!isNaN(categoryId)&&categoryId>0){formDataToSubmit.append(key,categoryId.toString());}}else if(key==='subcategory_id'&&typeof value==='string'){// subcategory_id is optional - only append if it's a valid number\nif(value.trim()!==''){const subcategoryId=parseInt(value);if(!isNaN(subcategoryId)&&subcategoryId>0){formDataToSubmit.append(key,subcategoryId.toString());}}// If empty, don't append anything (backend will handle as null)\n}else if(key==='scheduled_publish_at'){// Only include scheduled_publish_at if status is 'scheduled' and value is not empty\nif(formData.status==='scheduled'&&value&&typeof value==='string'&&value.trim()!==''){formDataToSubmit.append(key,value.trim());}// For non-scheduled announcements, don't include this field\n}else if(typeof value==='boolean'){// Boolean fields - send as string representation of boolean for FormData\nformDataToSubmit.append(key,value.toString());}else if(value!==null&&value!==undefined&&value!==''){// Regular fields - only append if they have actual content\nformDataToSubmit.append(key,value.toString().trim());}});// Add files\nif(files.length>0){files.forEach(file=>{formDataToSubmit.append(fileFieldName,file);});}return formDataToSubmit;};/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */export const validateFormFields=function(formData){let rules=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const errors={};const{required=[],maxLength={},custom={}}=rules;// Check required fields\nrequired.forEach(field=>{const value=formData[field];if(!value||typeof value==='string'&&!value.trim()){errors[field]=\"\".concat(field.replace('_',' '),\" is required\");}});// Check max length\nObject.entries(maxLength).forEach(_ref2=>{let[field,max]=_ref2;const value=formData[field];if(typeof value==='string'&&value.length>max){errors[field]=\"\".concat(field.replace('_',' '),\" must be less than \").concat(max,\" characters\");}});// Apply custom validation\nObject.entries(custom).forEach(_ref3=>{let[field,validator]=_ref3;const value=formData[field];const error=validator(value);if(error){errors[field]=error;}});return errors;};/**\n * Common validation rules for announcements\n */export const announcementValidationRules={required:['title','content','category_id'],maxLength:{title:255},custom:{scheduled_publish_at:(value,formData)=>{if((formData===null||formData===void 0?void 0:formData.status)==='scheduled'&&!value){return'Scheduled publish date is required for scheduled announcements';}return null;}}};/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */export const formatFileSize=function(bytes){let decimals=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;if(bytes===0)return'0 Bytes';const k=1024;const dm=decimals<0?0:decimals;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(dm))+' '+sizes[i];};/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */export const validateFile=function(file){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const{maxSize=5*1024*1024,allowedTypes=['image/jpeg','image/png','image/gif','image/webp']}=options;if(!allowedTypes.includes(file.type)){return\"File type \".concat(file.type,\" is not supported. Allowed types: \").concat(allowedTypes.join(', '));}if(file.size>maxSize){return\"File size \".concat(formatFileSize(file.size),\" exceeds maximum allowed size of \").concat(formatFileSize(maxSize));}return null;};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}