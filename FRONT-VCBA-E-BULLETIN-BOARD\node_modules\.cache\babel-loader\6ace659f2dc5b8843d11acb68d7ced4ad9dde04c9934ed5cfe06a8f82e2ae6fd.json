{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.6 3.6A2 2 0 0 1 5 3h14a2 2 0 0 1 2 2v14a2 2 0 0 1-.59 1.41\",\n  key: \"9l1ft6\"\n}], [\"path\", {\n  d: \"M3 8.7V19a2 2 0 0 0 2 2h10.3\",\n  key: \"17knke\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M13 13a3 3 0 1 0 0-6H9v2\",\n  key: \"uoagbd\"\n}], [\"path\", {\n  d: \"M9 17v-2.3\",\n  key: \"1jxgo2\"\n}]];\nconst SquareParkingOff = createLucideIcon(\"square-parking-off\", __iconNode);\nexport { __iconNode, SquareParkingOff as default };\n//# sourceMappingURL=square-parking-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}