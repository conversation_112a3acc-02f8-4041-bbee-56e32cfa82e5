{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\WelcomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ChevronRight, Users, BookOpen, Calendar, Award, ArrowRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [scrollY, setScrollY] = useState(0);\n  useEffect(() => {\n    setIsLoaded(true);\n    const handleScroll = () => {\n      setScrollY(window.scrollY);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(BookOpen, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: \"Academic Excellence\",\n    description: \"Access to quality education and comprehensive learning resources\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Users, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: \"Vibrant Community\",\n    description: \"Join a diverse community of students, faculty, and staff\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    title: \"Campus Events\",\n    description: \"Stay updated with the latest announcements and campus activities\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Award, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    title: \"Achievement Recognition\",\n    description: \"Celebrate academic and extracurricular accomplishments\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-background\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-overlay\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/villamor-image/villamor-collge-BG-landscape.jpg\",\n          alt: \"Villamor College Campus\",\n          className: \"hero-image\",\n          style: {\n            transform: `translateY(${scrollY * 0.5}px)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-inner\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `logo-section ${isLoaded ? 'animate-fade-in' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/vcba1.png\",\n                alt: \"Villamor College of Business and Arts\",\n                className: \"college-logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `main-content ${isLoaded ? 'animate-fade-in' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"hero-title\",\n                children: [\"Welcome to\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"title-highlight\",\n                  children: \"Villamor College\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this), \"of Business and Arts\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hero-subtitle\",\n                children: \"Your gateway to academic excellence and personal growth. Stay connected with the latest announcements, events, and opportunities that shape your educational journey.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cta-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleStudentLogin,\n                  className: \"cta-button\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Student Portal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"cta-subtitle\",\n                  children: \"Access your personalized dashboard, announcements, and campus updates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"features-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Why Choose Villamor College?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Discover what makes our institution a leader in business and arts education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-grid\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `feature-card ${isLoaded ? 'animate-fade-in' : ''}`,\n            style: {\n              animationDelay: `${index * 0.1}s`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"final-cta-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"final-cta-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Ready to Get Started?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Join thousands of students who trust Villamor College for their educational journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleStudentLogin,\n            className: \"secondary-cta-button\",\n            children: [\"Access Student Portal\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .welcome-page {\n          min-height: 100vh;\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n        }\n\n        /* Hero Section */\n        .hero-section {\n          position: relative;\n          min-height: 100vh;\n          display: flex;\n          align-items: center;\n          overflow: hidden;\n        }\n\n        .hero-background {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1;\n        }\n\n        .hero-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          object-position: center;\n        }\n\n        .hero-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(\n            135deg,\n            rgba(34, 197, 94, 0.9) 0%,\n            rgba(21, 128, 61, 0.8) 50%,\n            rgba(20, 83, 45, 0.9) 100%\n          );\n          z-index: 2;\n        }\n\n        .hero-content {\n          position: relative;\n          z-index: 3;\n          width: 100%;\n          padding: 2rem 0;\n        }\n\n        .container {\n          max-width: 1280px;\n          margin: 0 auto;\n          padding: 0 1rem;\n        }\n\n        .hero-inner {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          gap: 3rem;\n        }\n\n        /* Logo Section */\n        .logo-section {\n          opacity: 0;\n          transform: translateY(30px);\n          transition: all 0.8s ease-out;\n        }\n\n        .logo-section.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .college-logo {\n          height: 120px;\n          width: auto;\n          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\n        }\n\n        /* Main Content */\n        .main-content {\n          max-width: 800px;\n          opacity: 0;\n          transform: translateY(30px);\n          transition: all 0.8s ease-out 0.2s;\n        }\n\n        .main-content.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .hero-title {\n          font-size: 3.5rem;\n          font-weight: 700;\n          color: white;\n          margin-bottom: 1.5rem;\n          line-height: 1.1;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n\n        .title-highlight {\n          display: block;\n          color: #fbbf24;\n          font-size: 4rem;\n          margin: 0.5rem 0;\n        }\n\n        .hero-subtitle {\n          font-size: 1.25rem;\n          color: rgba(255, 255, 255, 0.9);\n          margin-bottom: 3rem;\n          line-height: 1.6;\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n        }\n\n        /* CTA Section */\n        .cta-section {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .cta-button {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\n          color: #1f2937;\n          font-size: 1.125rem;\n          font-weight: 600;\n          padding: 1rem 2rem;\n          border: none;\n          border-radius: 50px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        .cta-button:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(251, 191, 36, 0.6);\n          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n        }\n\n        .cta-subtitle {\n          color: rgba(255, 255, 255, 0.8);\n          font-size: 0.95rem;\n          text-align: center;\n          max-width: 400px;\n        }\n\n        /* Features Section */\n        .features-section {\n          padding: 6rem 0;\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);\n          position: relative;\n          overflow: hidden;\n        }\n\n        .features-section::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background:\n            radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);\n          pointer-events: none;\n        }\n\n        .features-header {\n          text-align: center;\n          margin-bottom: 4rem;\n        }\n\n        .features-header h2 {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .features-header p {\n          font-size: 1.125rem;\n          color: #6b7280;\n          max-width: 600px;\n          margin: 0 auto;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n          gap: 2rem;\n          margin-top: 3rem;\n        }\n\n        .feature-card {\n          background: rgba(255, 255, 255, 0.95);\n          backdrop-filter: blur(10px);\n          padding: 2rem;\n          border-radius: 20px;\n          text-align: center;\n          box-shadow:\n            0 8px 32px rgba(0, 0, 0, 0.1),\n            0 1px 0 rgba(255, 255, 255, 0.5) inset;\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n          opacity: 0;\n          transform: translateY(30px);\n          position: relative;\n          overflow: hidden;\n        }\n\n        .feature-card::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(\n            90deg,\n            transparent,\n            rgba(255, 255, 255, 0.2),\n            transparent\n          );\n          transition: left 0.5s ease;\n        }\n\n        .feature-card:hover::before {\n          left: 100%;\n        }\n\n        .feature-card.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .feature-card:hover {\n          transform: translateY(-8px) scale(1.02);\n          box-shadow:\n            0 20px 40px rgba(0, 0, 0, 0.15),\n            0 1px 0 rgba(255, 255, 255, 0.6) inset;\n          border-color: rgba(34, 197, 94, 0.3);\n        }\n\n        .feature-icon {\n          width: 60px;\n          height: 60px;\n          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n          border-radius: 16px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 1.5rem;\n          color: white;\n        }\n\n        .feature-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .feature-description {\n          color: #6b7280;\n          line-height: 1.6;\n        }\n\n        /* Final CTA Section */\n        .final-cta-section {\n          padding: 4rem 0;\n          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n        }\n\n        .final-cta-content {\n          text-align: center;\n          color: white;\n        }\n\n        .final-cta-content h2 {\n          font-size: 2.25rem;\n          font-weight: 700;\n          margin-bottom: 1rem;\n          color: white;\n        }\n\n        .final-cta-content p {\n          font-size: 1.125rem;\n          color: rgba(255, 255, 255, 0.8);\n          margin-bottom: 2rem;\n          max-width: 500px;\n          margin-left: auto;\n          margin-right: auto;\n        }\n\n        .secondary-cta-button {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: transparent;\n          color: #22c55e;\n          font-size: 1.125rem;\n          font-weight: 600;\n          padding: 1rem 2rem;\n          border: 2px solid #22c55e;\n          border-radius: 50px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .secondary-cta-button:hover {\n          background: #22c55e;\n          color: white;\n          transform: translateY(-2px);\n        }\n\n        /* Responsive Design */\n        @media (max-width: 1024px) {\n          .hero-title {\n            font-size: 3rem;\n          }\n\n          .title-highlight {\n            font-size: 3.5rem;\n          }\n\n          .features-grid {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n\n        @media (max-width: 768px) {\n          .container {\n            padding: 0 1rem;\n          }\n\n          .hero-inner {\n            gap: 2rem;\n          }\n\n          .hero-title {\n            font-size: 2.5rem;\n          }\n\n          .title-highlight {\n            font-size: 3rem;\n          }\n\n          .hero-subtitle {\n            font-size: 1.125rem;\n            margin-bottom: 2rem;\n          }\n\n          .college-logo {\n            height: 80px;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n            gap: 1.5rem;\n          }\n\n          .features-header h2 {\n            font-size: 2rem;\n          }\n\n          .final-cta-content h2 {\n            font-size: 1.875rem;\n          }\n\n          .features-section {\n            padding: 4rem 0;\n          }\n\n          .final-cta-section {\n            padding: 3rem 0;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .hero-section {\n            min-height: 100vh;\n            padding: 1rem 0;\n          }\n\n          .hero-inner {\n            gap: 1.5rem;\n          }\n\n          .hero-title {\n            font-size: 2rem;\n            line-height: 1.2;\n          }\n\n          .title-highlight {\n            font-size: 2.5rem;\n            margin: 0.25rem 0;\n          }\n\n          .hero-subtitle {\n            font-size: 1rem;\n            margin-bottom: 1.5rem;\n          }\n\n          .cta-button {\n            font-size: 1rem;\n            padding: 0.875rem 1.5rem;\n            width: 100%;\n            max-width: 280px;\n          }\n\n          .college-logo {\n            height: 60px;\n          }\n\n          .feature-card {\n            padding: 1.5rem;\n          }\n\n          .features-header {\n            margin-bottom: 2rem;\n          }\n\n          .features-header h2 {\n            font-size: 1.75rem;\n          }\n\n          .final-cta-content h2 {\n            font-size: 1.5rem;\n          }\n\n          .secondary-cta-button {\n            width: 100%;\n            max-width: 280px;\n            justify-content: center;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .hero-title {\n            font-size: 1.75rem;\n          }\n\n          .title-highlight {\n            font-size: 2.25rem;\n          }\n\n          .hero-subtitle {\n            font-size: 0.95rem;\n          }\n\n          .college-logo {\n            height: 50px;\n          }\n\n          .cta-button {\n            font-size: 0.95rem;\n            padding: 0.75rem 1.25rem;\n          }\n        }\n\n        /* Enhanced animations and interactions */\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n\n        .college-logo {\n          animation: float 6s ease-in-out infinite;\n        }\n\n        @keyframes pulse {\n          0%, 100% { box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4); }\n          50% { box-shadow: 0 4px 25px rgba(251, 191, 36, 0.6); }\n        }\n\n        .cta-button {\n          animation: pulse 2s ease-in-out infinite;\n        }\n\n        .cta-button:hover {\n          animation: none;\n        }\n\n        /* Improved accessibility */\n        @media (prefers-reduced-motion: reduce) {\n          .college-logo,\n          .cta-button,\n          .feature-card,\n          .main-content,\n          .logo-section {\n            animation: none !important;\n            transition: none !important;\n          }\n        }\n\n        /* High contrast mode support */\n        @media (prefers-contrast: high) {\n          .hero-overlay {\n            background: rgba(0, 0, 0, 0.8);\n          }\n\n          .cta-button {\n            border: 2px solid #000;\n          }\n\n          .feature-card {\n            border: 2px solid #000;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomePage, \"3rD14B5XrL0Lc5/KIRos40T7e80=\", false, function () {\n  return [useNavigate];\n});\n_c = WelcomePage;\nexport default WelcomePage;\nvar _c;\n$RefreshReg$(_c, \"WelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "ChevronRight", "Users", "BookOpen", "Calendar", "Award", "ArrowRight", "jsxDEV", "_jsxDEV", "WelcomePage", "_s", "navigate", "isLoaded", "setIsLoaded", "scrollY", "setScrollY", "handleScroll", "window", "addEventListener", "removeEventListener", "handleStudentLogin", "features", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "className", "children", "src", "alt", "style", "transform", "onClick", "map", "feature", "index", "animationDelay", "jsx", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/WelcomePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { ChevronRight, Users, BookOpen, Calendar, Award, ArrowRight } from 'lucide-react';\n\nconst WelcomePage: React.FC = () => {\n  const navigate = useNavigate();\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [scrollY, setScrollY] = useState(0);\n\n  useEffect(() => {\n    setIsLoaded(true);\n\n    const handleScroll = () => {\n      setScrollY(window.scrollY);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleStudentLogin = () => {\n    navigate('/student/login');\n  };\n\n  const features = [\n    {\n      icon: <BookOpen size={24} />,\n      title: \"Academic Excellence\",\n      description: \"Access to quality education and comprehensive learning resources\"\n    },\n    {\n      icon: <Users size={24} />,\n      title: \"Vibrant Community\",\n      description: \"Join a diverse community of students, faculty, and staff\"\n    },\n    {\n      icon: <Calendar size={24} />,\n      title: \"Campus Events\",\n      description: \"Stay updated with the latest announcements and campus activities\"\n    },\n    {\n      icon: <Award size={24} />,\n      title: \"Achievement Recognition\",\n      description: \"Celebrate academic and extracurricular accomplishments\"\n    }\n  ];\n\n  return (\n    <div className=\"welcome-page\">\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-background\">\n          <div className=\"hero-overlay\"></div>\n          <img\n            src=\"/villamor-image/villamor-collge-BG-landscape.jpg\"\n            alt=\"Villamor College Campus\"\n            className=\"hero-image\"\n            style={{\n              transform: `translateY(${scrollY * 0.5}px)`,\n            }}\n          />\n        </div>\n        \n        <div className=\"hero-content\">\n          <div className=\"container\">\n            <div className=\"hero-inner\">\n              {/* Logo Section */}\n              <div className={`logo-section ${isLoaded ? 'animate-fade-in' : ''}`}>\n                <img \n                  src=\"/logo/vcba1.png\" \n                  alt=\"Villamor College of Business and Arts\" \n                  className=\"college-logo\"\n                />\n              </div>\n\n              {/* Main Content */}\n              <div className={`main-content ${isLoaded ? 'animate-fade-in' : ''}`}>\n                <h1 className=\"hero-title\">\n                  Welcome to\n                  <span className=\"title-highlight\">Villamor College</span>\n                  of Business and Arts\n                </h1>\n                \n                <p className=\"hero-subtitle\">\n                  Your gateway to academic excellence and personal growth. \n                  Stay connected with the latest announcements, events, and opportunities \n                  that shape your educational journey.\n                </p>\n\n                {/* CTA Button */}\n                <div className=\"cta-section\">\n                  <button \n                    onClick={handleStudentLogin}\n                    className=\"cta-button\"\n                  >\n                    <span>Student Portal</span>\n                    <ArrowRight size={20} />\n                  </button>\n                  \n                  <p className=\"cta-subtitle\">\n                    Access your personalized dashboard, announcements, and campus updates\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"features-section\">\n        <div className=\"container\">\n          <div className=\"features-header\">\n            <h2>Why Choose Villamor College?</h2>\n            <p>Discover what makes our institution a leader in business and arts education</p>\n          </div>\n          \n          <div className=\"features-grid\">\n            {features.map((feature, index) => (\n              <div \n                key={index} \n                className={`feature-card ${isLoaded ? 'animate-fade-in' : ''}`}\n                style={{ animationDelay: `${index * 0.1}s` }}\n              >\n                <div className=\"feature-icon\">\n                  {feature.icon}\n                </div>\n                <h3 className=\"feature-title\">{feature.title}</h3>\n                <p className=\"feature-description\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action Section */}\n      <section className=\"final-cta-section\">\n        <div className=\"container\">\n          <div className=\"final-cta-content\">\n            <h2>Ready to Get Started?</h2>\n            <p>Join thousands of students who trust Villamor College for their educational journey</p>\n            <button \n              onClick={handleStudentLogin}\n              className=\"secondary-cta-button\"\n            >\n              Access Student Portal\n              <ChevronRight size={20} />\n            </button>\n          </div>\n        </div>\n      </section>\n\n      <style jsx>{`\n        .welcome-page {\n          min-height: 100vh;\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n        }\n\n        /* Hero Section */\n        .hero-section {\n          position: relative;\n          min-height: 100vh;\n          display: flex;\n          align-items: center;\n          overflow: hidden;\n        }\n\n        .hero-background {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1;\n        }\n\n        .hero-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          object-position: center;\n        }\n\n        .hero-overlay {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(\n            135deg,\n            rgba(34, 197, 94, 0.9) 0%,\n            rgba(21, 128, 61, 0.8) 50%,\n            rgba(20, 83, 45, 0.9) 100%\n          );\n          z-index: 2;\n        }\n\n        .hero-content {\n          position: relative;\n          z-index: 3;\n          width: 100%;\n          padding: 2rem 0;\n        }\n\n        .container {\n          max-width: 1280px;\n          margin: 0 auto;\n          padding: 0 1rem;\n        }\n\n        .hero-inner {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          gap: 3rem;\n        }\n\n        /* Logo Section */\n        .logo-section {\n          opacity: 0;\n          transform: translateY(30px);\n          transition: all 0.8s ease-out;\n        }\n\n        .logo-section.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .college-logo {\n          height: 120px;\n          width: auto;\n          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\n        }\n\n        /* Main Content */\n        .main-content {\n          max-width: 800px;\n          opacity: 0;\n          transform: translateY(30px);\n          transition: all 0.8s ease-out 0.2s;\n        }\n\n        .main-content.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .hero-title {\n          font-size: 3.5rem;\n          font-weight: 700;\n          color: white;\n          margin-bottom: 1.5rem;\n          line-height: 1.1;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n\n        .title-highlight {\n          display: block;\n          color: #fbbf24;\n          font-size: 4rem;\n          margin: 0.5rem 0;\n        }\n\n        .hero-subtitle {\n          font-size: 1.25rem;\n          color: rgba(255, 255, 255, 0.9);\n          margin-bottom: 3rem;\n          line-height: 1.6;\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n        }\n\n        /* CTA Section */\n        .cta-section {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .cta-button {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\n          color: #1f2937;\n          font-size: 1.125rem;\n          font-weight: 600;\n          padding: 1rem 2rem;\n          border: none;\n          border-radius: 50px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        .cta-button:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(251, 191, 36, 0.6);\n          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n        }\n\n        .cta-subtitle {\n          color: rgba(255, 255, 255, 0.8);\n          font-size: 0.95rem;\n          text-align: center;\n          max-width: 400px;\n        }\n\n        /* Features Section */\n        .features-section {\n          padding: 6rem 0;\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);\n          position: relative;\n          overflow: hidden;\n        }\n\n        .features-section::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background:\n            radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);\n          pointer-events: none;\n        }\n\n        .features-header {\n          text-align: center;\n          margin-bottom: 4rem;\n        }\n\n        .features-header h2 {\n          font-size: 2.5rem;\n          font-weight: 700;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .features-header p {\n          font-size: 1.125rem;\n          color: #6b7280;\n          max-width: 600px;\n          margin: 0 auto;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n          gap: 2rem;\n          margin-top: 3rem;\n        }\n\n        .feature-card {\n          background: rgba(255, 255, 255, 0.95);\n          backdrop-filter: blur(10px);\n          padding: 2rem;\n          border-radius: 20px;\n          text-align: center;\n          box-shadow:\n            0 8px 32px rgba(0, 0, 0, 0.1),\n            0 1px 0 rgba(255, 255, 255, 0.5) inset;\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n          opacity: 0;\n          transform: translateY(30px);\n          position: relative;\n          overflow: hidden;\n        }\n\n        .feature-card::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(\n            90deg,\n            transparent,\n            rgba(255, 255, 255, 0.2),\n            transparent\n          );\n          transition: left 0.5s ease;\n        }\n\n        .feature-card:hover::before {\n          left: 100%;\n        }\n\n        .feature-card.animate-fade-in {\n          opacity: 1;\n          transform: translateY(0);\n        }\n\n        .feature-card:hover {\n          transform: translateY(-8px) scale(1.02);\n          box-shadow:\n            0 20px 40px rgba(0, 0, 0, 0.15),\n            0 1px 0 rgba(255, 255, 255, 0.6) inset;\n          border-color: rgba(34, 197, 94, 0.3);\n        }\n\n        .feature-icon {\n          width: 60px;\n          height: 60px;\n          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n          border-radius: 16px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 auto 1.5rem;\n          color: white;\n        }\n\n        .feature-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .feature-description {\n          color: #6b7280;\n          line-height: 1.6;\n        }\n\n        /* Final CTA Section */\n        .final-cta-section {\n          padding: 4rem 0;\n          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n        }\n\n        .final-cta-content {\n          text-align: center;\n          color: white;\n        }\n\n        .final-cta-content h2 {\n          font-size: 2.25rem;\n          font-weight: 700;\n          margin-bottom: 1rem;\n          color: white;\n        }\n\n        .final-cta-content p {\n          font-size: 1.125rem;\n          color: rgba(255, 255, 255, 0.8);\n          margin-bottom: 2rem;\n          max-width: 500px;\n          margin-left: auto;\n          margin-right: auto;\n        }\n\n        .secondary-cta-button {\n          display: inline-flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: transparent;\n          color: #22c55e;\n          font-size: 1.125rem;\n          font-weight: 600;\n          padding: 1rem 2rem;\n          border: 2px solid #22c55e;\n          border-radius: 50px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .secondary-cta-button:hover {\n          background: #22c55e;\n          color: white;\n          transform: translateY(-2px);\n        }\n\n        /* Responsive Design */\n        @media (max-width: 1024px) {\n          .hero-title {\n            font-size: 3rem;\n          }\n\n          .title-highlight {\n            font-size: 3.5rem;\n          }\n\n          .features-grid {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n\n        @media (max-width: 768px) {\n          .container {\n            padding: 0 1rem;\n          }\n\n          .hero-inner {\n            gap: 2rem;\n          }\n\n          .hero-title {\n            font-size: 2.5rem;\n          }\n\n          .title-highlight {\n            font-size: 3rem;\n          }\n\n          .hero-subtitle {\n            font-size: 1.125rem;\n            margin-bottom: 2rem;\n          }\n\n          .college-logo {\n            height: 80px;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n            gap: 1.5rem;\n          }\n\n          .features-header h2 {\n            font-size: 2rem;\n          }\n\n          .final-cta-content h2 {\n            font-size: 1.875rem;\n          }\n\n          .features-section {\n            padding: 4rem 0;\n          }\n\n          .final-cta-section {\n            padding: 3rem 0;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .hero-section {\n            min-height: 100vh;\n            padding: 1rem 0;\n          }\n\n          .hero-inner {\n            gap: 1.5rem;\n          }\n\n          .hero-title {\n            font-size: 2rem;\n            line-height: 1.2;\n          }\n\n          .title-highlight {\n            font-size: 2.5rem;\n            margin: 0.25rem 0;\n          }\n\n          .hero-subtitle {\n            font-size: 1rem;\n            margin-bottom: 1.5rem;\n          }\n\n          .cta-button {\n            font-size: 1rem;\n            padding: 0.875rem 1.5rem;\n            width: 100%;\n            max-width: 280px;\n          }\n\n          .college-logo {\n            height: 60px;\n          }\n\n          .feature-card {\n            padding: 1.5rem;\n          }\n\n          .features-header {\n            margin-bottom: 2rem;\n          }\n\n          .features-header h2 {\n            font-size: 1.75rem;\n          }\n\n          .final-cta-content h2 {\n            font-size: 1.5rem;\n          }\n\n          .secondary-cta-button {\n            width: 100%;\n            max-width: 280px;\n            justify-content: center;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .hero-title {\n            font-size: 1.75rem;\n          }\n\n          .title-highlight {\n            font-size: 2.25rem;\n          }\n\n          .hero-subtitle {\n            font-size: 0.95rem;\n          }\n\n          .college-logo {\n            height: 50px;\n          }\n\n          .cta-button {\n            font-size: 0.95rem;\n            padding: 0.75rem 1.25rem;\n          }\n        }\n\n        /* Enhanced animations and interactions */\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n\n        .college-logo {\n          animation: float 6s ease-in-out infinite;\n        }\n\n        @keyframes pulse {\n          0%, 100% { box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4); }\n          50% { box-shadow: 0 4px 25px rgba(251, 191, 36, 0.6); }\n        }\n\n        .cta-button {\n          animation: pulse 2s ease-in-out infinite;\n        }\n\n        .cta-button:hover {\n          animation: none;\n        }\n\n        /* Improved accessibility */\n        @media (prefers-reduced-motion: reduce) {\n          .college-logo,\n          .cta-button,\n          .feature-card,\n          .main-content,\n          .logo-section {\n            animation: none !important;\n            transition: none !important;\n          }\n        }\n\n        /* High contrast mode support */\n        @media (prefers-contrast: high) {\n          .hero-overlay {\n            background: rgba(0, 0, 0, 0.8);\n          }\n\n          .cta-button {\n            border: 2px solid #000;\n          }\n\n          .feature-card {\n            border: 2px solid #000;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default WelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAEzCC,SAAS,CAAC,MAAM;IACdc,WAAW,CAAC,IAAI,CAAC;IAEjB,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzBD,UAAU,CAACE,MAAM,CAACH,OAAO,CAAC;IAC5B,CAAC;IAEDG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,MAAMU,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEd,OAAA,CAACL,QAAQ;MAACoB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEd,OAAA,CAACN,KAAK;MAACqB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEd,OAAA,CAACJ,QAAQ;MAACmB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEd,OAAA,CAACH,KAAK;MAACkB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACErB,OAAA;IAAKsB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3BvB,OAAA;MAASsB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC/BvB,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAKsB,SAAS,EAAC;QAAc;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCnB,OAAA;UACEwB,GAAG,EAAC,kDAAkD;UACtDC,GAAG,EAAC,yBAAyB;UAC7BH,SAAS,EAAC,YAAY;UACtBI,KAAK,EAAE;YACLC,SAAS,EAAE,cAAcrB,OAAO,GAAG,GAAG;UACxC;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BvB,OAAA;UAAKsB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBvB,OAAA;YAAKsB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzBvB,OAAA;cAAKsB,SAAS,EAAE,gBAAgBlB,QAAQ,GAAG,iBAAiB,GAAG,EAAE,EAAG;cAAAmB,QAAA,eAClEvB,OAAA;gBACEwB,GAAG,EAAC,iBAAiB;gBACrBC,GAAG,EAAC,uCAAuC;gBAC3CH,SAAS,EAAC;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnB,OAAA;cAAKsB,SAAS,EAAE,gBAAgBlB,QAAQ,GAAG,iBAAiB,GAAG,EAAE,EAAG;cAAAmB,QAAA,gBAClEvB,OAAA;gBAAIsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,YAEzB,eAAAvB,OAAA;kBAAMsB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,wBAE3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELnB,OAAA;gBAAGsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAI7B;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAGJnB,OAAA;gBAAKsB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvB,OAAA;kBACE4B,OAAO,EAAEhB,kBAAmB;kBAC5BU,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAEtBvB,OAAA;oBAAAuB,QAAA,EAAM;kBAAc;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3BnB,OAAA,CAACF,UAAU;oBAACiB,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAETnB,OAAA;kBAAGsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAE5B;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASsB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCvB,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvB,OAAA;UAAKsB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvB,OAAA;YAAAuB,QAAA,EAAI;UAA4B;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCnB,OAAA;YAAAuB,QAAA,EAAG;UAA2E;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eAENnB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BV,QAAQ,CAACgB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B/B,OAAA;YAEEsB,SAAS,EAAE,gBAAgBlB,QAAQ,GAAG,iBAAiB,GAAG,EAAE,EAAG;YAC/DsB,KAAK,EAAE;cAAEM,cAAc,EAAE,GAAGD,KAAK,GAAG,GAAG;YAAI,CAAE;YAAAR,QAAA,gBAE7CvB,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BO,OAAO,CAAChB;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNnB,OAAA;cAAIsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEO,OAAO,CAACV;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDnB,OAAA;cAAGsB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEO,OAAO,CAACT;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GARvDY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASsB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACpCvB,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBvB,OAAA;UAAKsB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvB,OAAA;YAAAuB,QAAA,EAAI;UAAqB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BnB,OAAA;YAAAuB,QAAA,EAAG;UAAmF;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1FnB,OAAA;YACE4B,OAAO,EAAEhB,kBAAmB;YAC5BU,SAAS,EAAC,sBAAsB;YAAAC,QAAA,GACjC,uBAEC,eAAAvB,OAAA,CAACP,YAAY;cAACsB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVnB,OAAA;MAAOiC,GAAG;MAAAV,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjB,EAAA,CAnqBID,WAAqB;EAAA,QACRT,WAAW;AAAA;AAAA0C,EAAA,GADxBjC,WAAqB;AAqqB3B,eAAeA,WAAW;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}