{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2\",\n  key: \"1vk7w2\"\n}], [\"path\", {\n  d: \"M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6\",\n  key: \"1jink5\"\n}], [\"path\", {\n  d: \"m5 11-3 3\",\n  key: \"1dgrs4\"\n}], [\"path\", {\n  d: \"m5 17-3-3h10\",\n  key: \"1mvvaf\"\n}]];\nconst FileOutput = createLucideIcon(\"file-output\", __iconNode);\nexport { __iconNode, FileOutput as default };\n//# sourceMappingURL=file-output.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}