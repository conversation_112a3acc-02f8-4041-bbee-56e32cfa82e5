{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 21a8 8 0 0 1 11.873-7\",\n  key: \"74fkxq\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"path\", {\n  d: \"m17 17 5 5\",\n  key: \"p7ous7\"\n}], [\"path\", {\n  d: \"m22 17-5 5\",\n  key: \"gqnmv0\"\n}]];\nconst UserRoundX = createLucideIcon(\"user-round-x\", __iconNode);\nexport { __iconNode, UserRoundX as default };\n//# sourceMappingURL=user-round-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}