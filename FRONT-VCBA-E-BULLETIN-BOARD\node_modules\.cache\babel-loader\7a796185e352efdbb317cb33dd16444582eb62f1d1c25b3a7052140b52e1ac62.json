{"ast": null, "code": "/**\n * Professional Notification Navigation Service\n * Handles context-aware redirection based on notification metadata\n * Following Google/OpenAI software engineering practices\n */\n\nclass NotificationNavigationService {\n  constructor() {\n    this.navigate = null;\n    this.currentUserRole = null;\n  }\n  /**\n   * Initialize the navigation service with React Router navigate function\n   */\n  initialize(navigate, userRole) {\n    this.navigate = navigate;\n    this.currentUserRole = userRole;\n  }\n\n  /**\n   * Handle notification click with context-aware redirection\n   */\n  async handleNotificationClick(notification, options = {}) {\n    if (!this.navigate || !this.currentUserRole) {\n      console.error('NotificationNavigationService not initialized');\n      return false;\n    }\n    try {\n      // Parse context metadata\n      const context = this.parseNotificationContext(notification);\n\n      // Mark as read if requested\n      if (options.markAsRead !== false) {\n        await this.markNotificationAsRead(notification.notification_id);\n      }\n\n      // Determine target route\n      const targetRoute = this.buildTargetRoute(context, this.currentUserRole);\n      if (!targetRoute) {\n        console.warn('Unable to determine target route for notification:', notification);\n        return this.handleFallback(options.fallbackRoute);\n      }\n\n      // Navigate to target with state for highlighting and scrolling\n      const navigationState = {\n        fromNotification: true,\n        notificationId: notification.notification_id,\n        highlightTarget: options.highlightTarget !== false,\n        scrollTo: context.scroll_to,\n        scrollBehavior: options.scrollBehavior || 'smooth'\n      };\n      this.navigate(targetRoute, {\n        state: navigationState\n      });\n      console.log('🎯 Navigated to:', targetRoute, 'with state:', navigationState);\n      return true;\n    } catch (error) {\n      console.error('Error handling notification click:', error);\n      return this.handleFallback(options.fallbackRoute);\n    }\n  }\n\n  /**\n   * Parse notification context metadata\n   */\n  parseNotificationContext(notification) {\n    try {\n      // Handle both string and object context_metadata\n      let contextData;\n      if (typeof notification.context_metadata === 'string') {\n        contextData = JSON.parse(notification.context_metadata);\n      } else if (typeof notification.context_metadata === 'object' && notification.context_metadata !== null) {\n        contextData = notification.context_metadata;\n      } else {\n        // Fallback: infer context from notification type and related IDs\n        contextData = this.inferContextFromNotification(notification);\n      }\n      return contextData;\n    } catch (error) {\n      console.warn('Failed to parse notification context, using fallback:', error);\n      return this.inferContextFromNotification(notification);\n    }\n  }\n\n  /**\n   * Infer context from notification data when metadata is not available\n   */\n  inferContextFromNotification(notification) {\n    const {\n      type_name,\n      related_announcement_id,\n      related_comment_id\n    } = notification;\n\n    // Comment-related notifications\n    if (type_name && ['comment_reply', 'comment_reaction', 'comment_flagged'].includes(type_name)) {\n      return {\n        type: 'comment',\n        target_id: related_comment_id || null,\n        announcement_id: related_announcement_id,\n        scroll_to: related_comment_id ? `comment-${related_comment_id}` : undefined\n      };\n    }\n\n    // Announcement-related notifications\n    if (type_name && ['new_announcement', 'alert_announcement', 'announcement_reaction', 'pinned_post'].includes(type_name)) {\n      return {\n        type: 'announcement',\n        target_id: related_announcement_id || null,\n        announcement_id: related_announcement_id,\n        scroll_to: related_announcement_id ? `announcement-${related_announcement_id}` : undefined\n      };\n    }\n\n    // Calendar-related notifications\n    if (type_name === 'calendar_event') {\n      return {\n        type: 'calendar',\n        target_id: related_announcement_id || null,\n        // Calendar events might use announcement_id field\n        scroll_to: related_announcement_id ? `event-${related_announcement_id}` : undefined\n      };\n    }\n\n    // General fallback\n    return {\n      type: 'general',\n      target_id: null\n    };\n  }\n\n  /**\n   * Build target route based on context and user role\n   */\n  buildTargetRoute(context, userRole) {\n    const baseRoute = userRole === 'admin' ? '/admin' : '/student';\n    switch (context.type) {\n      case 'comment':\n        if (context.announcement_id) {\n          // Navigate to newsfeed with announcement focus\n          return `${baseRoute}/newsfeed?focus=announcement&id=${context.announcement_id}&comment=${context.target_id}`;\n        }\n        return `${baseRoute}/newsfeed`;\n      case 'announcement':\n        if (context.target_id) {\n          // Navigate to newsfeed with announcement focus\n          return `${baseRoute}/newsfeed?focus=announcement&id=${context.target_id}`;\n        }\n        return `${baseRoute}/newsfeed`;\n      case 'calendar':\n        if (context.target_id) {\n          // Navigate to calendar with event focus\n          return `${baseRoute}/calendar?focus=event&id=${context.target_id}`;\n        }\n        return `${baseRoute}/calendar`;\n      case 'general':\n      default:\n        // Navigate to dashboard or newsfeed as fallback\n        return `${baseRoute}/newsfeed`;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markNotificationAsRead(notificationId) {\n    try {\n      // Import notification service dynamically to avoid circular dependencies\n      const {\n        studentNotificationService,\n        adminNotificationService\n      } = await import('./notificationService');\n      const service = this.currentUserRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAsRead(notificationId);\n      console.log('✅ Marked notification as read:', notificationId);\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      // Don't throw - this shouldn't prevent navigation\n    }\n  }\n\n  /**\n   * Handle fallback navigation when target route cannot be determined\n   */\n  handleFallback(fallbackRoute) {\n    if (!this.navigate || !this.currentUserRole) return false;\n    const defaultFallback = this.currentUserRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n    const targetRoute = fallbackRoute || defaultFallback;\n    this.navigate(targetRoute);\n    console.log('🔄 Fallback navigation to:', targetRoute);\n    return true;\n  }\n\n  /**\n   * Validate if target content still exists (for error handling)\n   */\n  async validateTarget(context) {\n    try {\n      // This would typically make API calls to verify content exists\n      // For now, we'll implement basic validation\n\n      if (context.type === 'announcement' && context.target_id) {\n        // Could check if announcement still exists and is accessible\n        return true;\n      }\n      if (context.type === 'comment' && context.target_id) {\n        // Could check if comment still exists and is not deleted\n        return true;\n      }\n      if (context.type === 'calendar' && context.target_id) {\n        // Could check if calendar event still exists\n        return true;\n      }\n      return true;\n    } catch (error) {\n      console.error('Error validating target:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get user-friendly error message for invalid targets\n   */\n  getTargetErrorMessage(context) {\n    switch (context.type) {\n      case 'announcement':\n        return 'This announcement is no longer available or has been removed.';\n      case 'comment':\n        return 'This comment is no longer available or has been removed.';\n      case 'calendar':\n        return 'This calendar event is no longer available or has been removed.';\n      default:\n        return 'The content you\\'re looking for is no longer available.';\n    }\n  }\n\n  /**\n   * Generate deep link URL for sharing notifications\n   */\n  generateDeepLink(notification, baseUrl = window.location.origin) {\n    const context = this.parseNotificationContext(notification);\n    const userRole = this.currentUserRole || 'student';\n    const targetRoute = this.buildTargetRoute(context, userRole);\n    if (targetRoute) {\n      return `${baseUrl}${targetRoute}&notification=${notification.notification_id}`;\n    }\n    return `${baseUrl}/${userRole}/dashboard`;\n  }\n\n  /**\n   * Handle browser back navigation after notification click\n   */\n  handleBackNavigation() {\n    // This could be enhanced to provide smart back navigation\n    // For now, we'll let the browser handle it naturally\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      var _this$navigate;\n      const fallbackRoute = this.currentUserRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n      (_this$navigate = this.navigate) === null || _this$navigate === void 0 ? void 0 : _this$navigate.call(this, fallbackRoute);\n    }\n  }\n}\n\n// Export singleton instance\nexport const notificationNavigationService = new NotificationNavigationService();\nexport default notificationNavigationService;", "map": {"version": 3, "names": ["NotificationNavigationService", "constructor", "navigate", "currentUserRole", "initialize", "userRole", "handleNotificationClick", "notification", "options", "console", "error", "context", "parseNotificationContext", "mark<PERSON><PERSON><PERSON>", "markNotificationAsRead", "notification_id", "targetRoute", "buildTargetRoute", "warn", "handleFallback", "fallback<PERSON><PERSON><PERSON>", "navigationState", "fromNotification", "notificationId", "highlightTarget", "scrollTo", "scroll_to", "scroll<PERSON>eh<PERSON>or", "state", "log", "contextData", "context_metadata", "JSON", "parse", "inferContextFromNotification", "type_name", "related_announcement_id", "related_comment_id", "includes", "type", "target_id", "announcement_id", "undefined", "baseRoute", "studentNotificationService", "adminNotificationService", "service", "defaultFallback", "validate<PERSON><PERSON><PERSON>", "getTargetErrorMessage", "generateDeepLink", "baseUrl", "window", "location", "origin", "handleBackNavigation", "history", "length", "back", "_this$navigate", "call", "notificationNavigationService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/notificationNavigationService.ts"], "sourcesContent": ["import { NavigateFunction } from 'react-router-dom';\nimport { Notification } from './notificationService';\n\n/**\n * Professional Notification Navigation Service\n * Handles context-aware redirection based on notification metadata\n * Following Google/OpenAI software engineering practices\n */\n\nexport interface NotificationContext {\n  type: 'comment' | 'announcement' | 'calendar' | 'general';\n  target_id: number | null;\n  announcement_id?: number;\n  parent_comment_id?: number;\n  event_date?: string;\n  scroll_to?: string;\n}\n\nexport interface NavigationOptions {\n  markAsRead?: boolean;\n  highlightTarget?: boolean;\n  scrollBehavior?: ScrollBehavior;\n  fallbackRoute?: string;\n}\n\nclass NotificationNavigationService {\n  private navigate: NavigateFunction | null = null;\n  private currentUserRole: 'admin' | 'student' | null = null;\n\n  /**\n   * Initialize the navigation service with React Router navigate function\n   */\n  initialize(navigate: NavigateFunction, userRole: 'admin' | 'student') {\n    this.navigate = navigate;\n    this.currentUserRole = userRole;\n  }\n\n  /**\n   * Handle notification click with context-aware redirection\n   */\n  async handleNotificationClick(\n    notification: Notification,\n    options: NavigationOptions = {}\n  ): Promise<boolean> {\n    if (!this.navigate || !this.currentUserRole) {\n      console.error('NotificationNavigationService not initialized');\n      return false;\n    }\n\n    try {\n      // Parse context metadata\n      const context = this.parseNotificationContext(notification);\n      \n      // Mark as read if requested\n      if (options.markAsRead !== false) {\n        await this.markNotificationAsRead(notification.notification_id);\n      }\n\n      // Determine target route\n      const targetRoute = this.buildTargetRoute(context, this.currentUserRole);\n      \n      if (!targetRoute) {\n        console.warn('Unable to determine target route for notification:', notification);\n        return this.handleFallback(options.fallbackRoute);\n      }\n\n      // Navigate to target with state for highlighting and scrolling\n      const navigationState = {\n        fromNotification: true,\n        notificationId: notification.notification_id,\n        highlightTarget: options.highlightTarget !== false,\n        scrollTo: context.scroll_to,\n        scrollBehavior: (options.scrollBehavior || 'smooth') as ScrollBehavior\n      };\n\n      this.navigate(targetRoute, { state: navigationState });\n      \n      console.log('🎯 Navigated to:', targetRoute, 'with state:', navigationState);\n      return true;\n\n    } catch (error) {\n      console.error('Error handling notification click:', error);\n      return this.handleFallback(options.fallbackRoute);\n    }\n  }\n\n  /**\n   * Parse notification context metadata\n   */\n  private parseNotificationContext(notification: Notification): NotificationContext {\n    try {\n      // Handle both string and object context_metadata\n      let contextData: NotificationContext;\n      \n      if (typeof notification.context_metadata === 'string') {\n        contextData = JSON.parse(notification.context_metadata);\n      } else if (typeof notification.context_metadata === 'object' && notification.context_metadata !== null) {\n        contextData = notification.context_metadata as NotificationContext;\n      } else {\n        // Fallback: infer context from notification type and related IDs\n        contextData = this.inferContextFromNotification(notification);\n      }\n\n      return contextData;\n    } catch (error) {\n      console.warn('Failed to parse notification context, using fallback:', error);\n      return this.inferContextFromNotification(notification);\n    }\n  }\n\n  /**\n   * Infer context from notification data when metadata is not available\n   */\n  private inferContextFromNotification(notification: Notification): NotificationContext {\n    const { type_name, related_announcement_id, related_comment_id } = notification;\n\n    // Comment-related notifications\n    if (type_name && ['comment_reply', 'comment_reaction', 'comment_flagged'].includes(type_name)) {\n      return {\n        type: 'comment',\n        target_id: related_comment_id || null,\n        announcement_id: related_announcement_id,\n        scroll_to: related_comment_id ? `comment-${related_comment_id}` : undefined\n      };\n    }\n\n    // Announcement-related notifications\n    if (type_name && ['new_announcement', 'alert_announcement', 'announcement_reaction', 'pinned_post'].includes(type_name)) {\n      return {\n        type: 'announcement',\n        target_id: related_announcement_id || null,\n        announcement_id: related_announcement_id,\n        scroll_to: related_announcement_id ? `announcement-${related_announcement_id}` : undefined\n      };\n    }\n\n    // Calendar-related notifications\n    if (type_name === 'calendar_event') {\n      return {\n        type: 'calendar',\n        target_id: related_announcement_id || null, // Calendar events might use announcement_id field\n        scroll_to: related_announcement_id ? `event-${related_announcement_id}` : undefined\n      };\n    }\n\n    // General fallback\n    return {\n      type: 'general',\n      target_id: null\n    };\n  }\n\n  /**\n   * Build target route based on context and user role\n   */\n  private buildTargetRoute(context: NotificationContext, userRole: 'admin' | 'student'): string | null {\n    const baseRoute = userRole === 'admin' ? '/admin' : '/student';\n\n    switch (context.type) {\n      case 'comment':\n        if (context.announcement_id) {\n          // Navigate to newsfeed with announcement focus\n          return `${baseRoute}/newsfeed?focus=announcement&id=${context.announcement_id}&comment=${context.target_id}`;\n        }\n        return `${baseRoute}/newsfeed`;\n\n      case 'announcement':\n        if (context.target_id) {\n          // Navigate to newsfeed with announcement focus\n          return `${baseRoute}/newsfeed?focus=announcement&id=${context.target_id}`;\n        }\n        return `${baseRoute}/newsfeed`;\n\n      case 'calendar':\n        if (context.target_id) {\n          // Navigate to calendar with event focus\n          return `${baseRoute}/calendar?focus=event&id=${context.target_id}`;\n        }\n        return `${baseRoute}/calendar`;\n\n      case 'general':\n      default:\n        // Navigate to dashboard or newsfeed as fallback\n        return `${baseRoute}/newsfeed`;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  private async markNotificationAsRead(notificationId: number): Promise<void> {\n    try {\n      // Import notification service dynamically to avoid circular dependencies\n      const { studentNotificationService, adminNotificationService } = await import('./notificationService');\n      \n      const service = this.currentUserRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAsRead(notificationId);\n      \n      console.log('✅ Marked notification as read:', notificationId);\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      // Don't throw - this shouldn't prevent navigation\n    }\n  }\n\n  /**\n   * Handle fallback navigation when target route cannot be determined\n   */\n  private handleFallback(fallbackRoute?: string): boolean {\n    if (!this.navigate || !this.currentUserRole) return false;\n\n    const defaultFallback = this.currentUserRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n    const targetRoute = fallbackRoute || defaultFallback;\n\n    this.navigate(targetRoute);\n    console.log('🔄 Fallback navigation to:', targetRoute);\n    return true;\n  }\n\n  /**\n   * Validate if target content still exists (for error handling)\n   */\n  async validateTarget(context: NotificationContext): Promise<boolean> {\n    try {\n      // This would typically make API calls to verify content exists\n      // For now, we'll implement basic validation\n      \n      if (context.type === 'announcement' && context.target_id) {\n        // Could check if announcement still exists and is accessible\n        return true;\n      }\n\n      if (context.type === 'comment' && context.target_id) {\n        // Could check if comment still exists and is not deleted\n        return true;\n      }\n\n      if (context.type === 'calendar' && context.target_id) {\n        // Could check if calendar event still exists\n        return true;\n      }\n\n      return true;\n    } catch (error) {\n      console.error('Error validating target:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Get user-friendly error message for invalid targets\n   */\n  getTargetErrorMessage(context: NotificationContext): string {\n    switch (context.type) {\n      case 'announcement':\n        return 'This announcement is no longer available or has been removed.';\n      case 'comment':\n        return 'This comment is no longer available or has been removed.';\n      case 'calendar':\n        return 'This calendar event is no longer available or has been removed.';\n      default:\n        return 'The content you\\'re looking for is no longer available.';\n    }\n  }\n\n  /**\n   * Generate deep link URL for sharing notifications\n   */\n  generateDeepLink(notification: Notification, baseUrl: string = window.location.origin): string {\n    const context = this.parseNotificationContext(notification);\n    const userRole = this.currentUserRole || 'student';\n    const targetRoute = this.buildTargetRoute(context, userRole);\n    \n    if (targetRoute) {\n      return `${baseUrl}${targetRoute}&notification=${notification.notification_id}`;\n    }\n    \n    return `${baseUrl}/${userRole}/dashboard`;\n  }\n\n  /**\n   * Handle browser back navigation after notification click\n   */\n  handleBackNavigation(): void {\n    // This could be enhanced to provide smart back navigation\n    // For now, we'll let the browser handle it naturally\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      const fallbackRoute = this.currentUserRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n      this.navigate?.(fallbackRoute);\n    }\n  }\n}\n\n// Export singleton instance\nexport const notificationNavigationService = new NotificationNavigationService();\nexport default notificationNavigationService;\n"], "mappings": "AAGA;AACA;AACA;AACA;AACA;;AAkBA,MAAMA,6BAA6B,CAAC;EAAAC,YAAA;IAAA,KAC1BC,QAAQ,GAA4B,IAAI;IAAA,KACxCC,eAAe,GAA+B,IAAI;EAAA;EAE1D;AACF;AACA;EACEC,UAAUA,CAACF,QAA0B,EAAEG,QAA6B,EAAE;IACpE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,eAAe,GAAGE,QAAQ;EACjC;;EAEA;AACF;AACA;EACE,MAAMC,uBAAuBA,CAC3BC,YAA0B,EAC1BC,OAA0B,GAAG,CAAC,CAAC,EACb;IAClB,IAAI,CAAC,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC3CM,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;MAC9D,OAAO,KAAK;IACd;IAEA,IAAI;MACF;MACA,MAAMC,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACL,YAAY,CAAC;;MAE3D;MACA,IAAIC,OAAO,CAACK,UAAU,KAAK,KAAK,EAAE;QAChC,MAAM,IAAI,CAACC,sBAAsB,CAACP,YAAY,CAACQ,eAAe,CAAC;MACjE;;MAEA;MACA,MAAMC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACN,OAAO,EAAE,IAAI,CAACR,eAAe,CAAC;MAExE,IAAI,CAACa,WAAW,EAAE;QAChBP,OAAO,CAACS,IAAI,CAAC,oDAAoD,EAAEX,YAAY,CAAC;QAChF,OAAO,IAAI,CAACY,cAAc,CAACX,OAAO,CAACY,aAAa,CAAC;MACnD;;MAEA;MACA,MAAMC,eAAe,GAAG;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAEhB,YAAY,CAACQ,eAAe;QAC5CS,eAAe,EAAEhB,OAAO,CAACgB,eAAe,KAAK,KAAK;QAClDC,QAAQ,EAAEd,OAAO,CAACe,SAAS;QAC3BC,cAAc,EAAGnB,OAAO,CAACmB,cAAc,IAAI;MAC7C,CAAC;MAED,IAAI,CAACzB,QAAQ,CAACc,WAAW,EAAE;QAAEY,KAAK,EAAEP;MAAgB,CAAC,CAAC;MAEtDZ,OAAO,CAACoB,GAAG,CAAC,kBAAkB,EAAEb,WAAW,EAAE,aAAa,EAAEK,eAAe,CAAC;MAC5E,OAAO,IAAI;IAEb,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,IAAI,CAACS,cAAc,CAACX,OAAO,CAACY,aAAa,CAAC;IACnD;EACF;;EAEA;AACF;AACA;EACUR,wBAAwBA,CAACL,YAA0B,EAAuB;IAChF,IAAI;MACF;MACA,IAAIuB,WAAgC;MAEpC,IAAI,OAAOvB,YAAY,CAACwB,gBAAgB,KAAK,QAAQ,EAAE;QACrDD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAAC1B,YAAY,CAACwB,gBAAgB,CAAC;MACzD,CAAC,MAAM,IAAI,OAAOxB,YAAY,CAACwB,gBAAgB,KAAK,QAAQ,IAAIxB,YAAY,CAACwB,gBAAgB,KAAK,IAAI,EAAE;QACtGD,WAAW,GAAGvB,YAAY,CAACwB,gBAAuC;MACpE,CAAC,MAAM;QACL;QACAD,WAAW,GAAG,IAAI,CAACI,4BAA4B,CAAC3B,YAAY,CAAC;MAC/D;MAEA,OAAOuB,WAAW;IACpB,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACS,IAAI,CAAC,uDAAuD,EAAER,KAAK,CAAC;MAC5E,OAAO,IAAI,CAACwB,4BAA4B,CAAC3B,YAAY,CAAC;IACxD;EACF;;EAEA;AACF;AACA;EACU2B,4BAA4BA,CAAC3B,YAA0B,EAAuB;IACpF,MAAM;MAAE4B,SAAS;MAAEC,uBAAuB;MAAEC;IAAmB,CAAC,GAAG9B,YAAY;;IAE/E;IACA,IAAI4B,SAAS,IAAI,CAAC,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAACG,QAAQ,CAACH,SAAS,CAAC,EAAE;MAC7F,OAAO;QACLI,IAAI,EAAE,SAAS;QACfC,SAAS,EAAEH,kBAAkB,IAAI,IAAI;QACrCI,eAAe,EAAEL,uBAAuB;QACxCV,SAAS,EAAEW,kBAAkB,GAAG,WAAWA,kBAAkB,EAAE,GAAGK;MACpE,CAAC;IACH;;IAEA;IACA,IAAIP,SAAS,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,aAAa,CAAC,CAACG,QAAQ,CAACH,SAAS,CAAC,EAAE;MACvH,OAAO;QACLI,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAEJ,uBAAuB,IAAI,IAAI;QAC1CK,eAAe,EAAEL,uBAAuB;QACxCV,SAAS,EAAEU,uBAAuB,GAAG,gBAAgBA,uBAAuB,EAAE,GAAGM;MACnF,CAAC;IACH;;IAEA;IACA,IAAIP,SAAS,KAAK,gBAAgB,EAAE;MAClC,OAAO;QACLI,IAAI,EAAE,UAAU;QAChBC,SAAS,EAAEJ,uBAAuB,IAAI,IAAI;QAAE;QAC5CV,SAAS,EAAEU,uBAAuB,GAAG,SAASA,uBAAuB,EAAE,GAAGM;MAC5E,CAAC;IACH;;IAEA;IACA,OAAO;MACLH,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE;IACb,CAAC;EACH;;EAEA;AACF;AACA;EACUvB,gBAAgBA,CAACN,OAA4B,EAAEN,QAA6B,EAAiB;IACnG,MAAMsC,SAAS,GAAGtC,QAAQ,KAAK,OAAO,GAAG,QAAQ,GAAG,UAAU;IAE9D,QAAQM,OAAO,CAAC4B,IAAI;MAClB,KAAK,SAAS;QACZ,IAAI5B,OAAO,CAAC8B,eAAe,EAAE;UAC3B;UACA,OAAO,GAAGE,SAAS,mCAAmChC,OAAO,CAAC8B,eAAe,YAAY9B,OAAO,CAAC6B,SAAS,EAAE;QAC9G;QACA,OAAO,GAAGG,SAAS,WAAW;MAEhC,KAAK,cAAc;QACjB,IAAIhC,OAAO,CAAC6B,SAAS,EAAE;UACrB;UACA,OAAO,GAAGG,SAAS,mCAAmChC,OAAO,CAAC6B,SAAS,EAAE;QAC3E;QACA,OAAO,GAAGG,SAAS,WAAW;MAEhC,KAAK,UAAU;QACb,IAAIhC,OAAO,CAAC6B,SAAS,EAAE;UACrB;UACA,OAAO,GAAGG,SAAS,4BAA4BhC,OAAO,CAAC6B,SAAS,EAAE;QACpE;QACA,OAAO,GAAGG,SAAS,WAAW;MAEhC,KAAK,SAAS;MACd;QACE;QACA,OAAO,GAAGA,SAAS,WAAW;IAClC;EACF;;EAEA;AACF;AACA;EACE,MAAc7B,sBAAsBA,CAACS,cAAsB,EAAiB;IAC1E,IAAI;MACF;MACA,MAAM;QAAEqB,0BAA0B;QAAEC;MAAyB,CAAC,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC;MAEtG,MAAMC,OAAO,GAAG,IAAI,CAAC3C,eAAe,KAAK,OAAO,GAAG0C,wBAAwB,GAAGD,0BAA0B;MACxG,MAAME,OAAO,CAACjC,UAAU,CAACU,cAAc,CAAC;MAExCd,OAAO,CAACoB,GAAG,CAAC,gCAAgC,EAAEN,cAAc,CAAC;IAC/D,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D;IACF;EACF;;EAEA;AACF;AACA;EACUS,cAAcA,CAACC,aAAsB,EAAW;IACtD,IAAI,CAAC,IAAI,CAAClB,QAAQ,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,OAAO,KAAK;IAEzD,MAAM4C,eAAe,GAAG,IAAI,CAAC5C,eAAe,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;IACpG,MAAMa,WAAW,GAAGI,aAAa,IAAI2B,eAAe;IAEpD,IAAI,CAAC7C,QAAQ,CAACc,WAAW,CAAC;IAC1BP,OAAO,CAACoB,GAAG,CAAC,4BAA4B,EAAEb,WAAW,CAAC;IACtD,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMgC,cAAcA,CAACrC,OAA4B,EAAoB;IACnE,IAAI;MACF;MACA;;MAEA,IAAIA,OAAO,CAAC4B,IAAI,KAAK,cAAc,IAAI5B,OAAO,CAAC6B,SAAS,EAAE;QACxD;QACA,OAAO,IAAI;MACb;MAEA,IAAI7B,OAAO,CAAC4B,IAAI,KAAK,SAAS,IAAI5B,OAAO,CAAC6B,SAAS,EAAE;QACnD;QACA,OAAO,IAAI;MACb;MAEA,IAAI7B,OAAO,CAAC4B,IAAI,KAAK,UAAU,IAAI5B,OAAO,CAAC6B,SAAS,EAAE;QACpD;QACA,OAAO,IAAI;MACb;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACEuC,qBAAqBA,CAACtC,OAA4B,EAAU;IAC1D,QAAQA,OAAO,CAAC4B,IAAI;MAClB,KAAK,cAAc;QACjB,OAAO,+DAA+D;MACxE,KAAK,SAAS;QACZ,OAAO,0DAA0D;MACnE,KAAK,UAAU;QACb,OAAO,iEAAiE;MAC1E;QACE,OAAO,yDAAyD;IACpE;EACF;;EAEA;AACF;AACA;EACEW,gBAAgBA,CAAC3C,YAA0B,EAAE4C,OAAe,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAU;IAC7F,MAAM3C,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACL,YAAY,CAAC;IAC3D,MAAMF,QAAQ,GAAG,IAAI,CAACF,eAAe,IAAI,SAAS;IAClD,MAAMa,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACN,OAAO,EAAEN,QAAQ,CAAC;IAE5D,IAAIW,WAAW,EAAE;MACf,OAAO,GAAGmC,OAAO,GAAGnC,WAAW,iBAAiBT,YAAY,CAACQ,eAAe,EAAE;IAChF;IAEA,OAAO,GAAGoC,OAAO,IAAI9C,QAAQ,YAAY;EAC3C;;EAEA;AACF;AACA;EACEkD,oBAAoBA,CAAA,EAAS;IAC3B;IACA;IACA,IAAIH,MAAM,CAACI,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7BL,MAAM,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;IACvB,CAAC,MAAM;MAAA,IAAAC,cAAA;MACL,MAAMvC,aAAa,GAAG,IAAI,CAACjB,eAAe,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;MAClG,CAAAwD,cAAA,OAAI,CAACzD,QAAQ,cAAAyD,cAAA,uBAAbA,cAAA,CAAAC,IAAA,KAAI,EAAYxC,aAAa,CAAC;IAChC;EACF;AACF;;AAEA;AACA,OAAO,MAAMyC,6BAA6B,GAAG,IAAI7D,6BAA6B,CAAC,CAAC;AAChF,eAAe6D,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}