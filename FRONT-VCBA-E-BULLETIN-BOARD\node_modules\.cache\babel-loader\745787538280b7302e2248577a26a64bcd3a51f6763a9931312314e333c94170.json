{"ast": null, "code": "import{httpClient,adminHttpClient,studentHttpClient}from'./api.service';class CalendarReactionService{// No need to store client in constructor - get it dynamically each time\n/**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */getAuthenticatedClient(){const studentToken=localStorage.getItem('studentToken');const studentUser=localStorage.getItem('studentUser');const adminToken=localStorage.getItem('adminToken');const adminUser=localStorage.getItem('adminUser');console.log('🔍 CalendarReactionService - Auth detection:',{hasStudentToken:!!studentToken,hasStudentUser:!!studentUser,hasAdminToken:!!adminToken,hasAdminUser:!!adminUser,studentTokenPrefix:studentToken?studentToken.substring(0,10)+'...':null,adminTokenPrefix:adminToken?adminToken.substring(0,10)+'...':null});// Prefer student authentication if available\nif(studentToken&&studentUser){console.log('🎓 CalendarReactionService - Using STUDENT authentication');return studentHttpClient;}else if(adminToken&&adminUser){console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');return adminHttpClient;}else{console.log('🔧 CalendarReactionService - Using DEFAULT authentication');return httpClient;}}// Like a calendar event (following announcement pattern)\nasync likeEvent(eventId){try{const client=this.getAuthenticatedClient();console.log('❤️ CalendarReactionService - Liking calendar event:',{eventId,clientType:client===adminHttpClient?'ADMIN':client===studentHttpClient?'STUDENT':'DEFAULT'});const response=await client.post(\"/api/calendar/\".concat(eventId,\"/like\"),{});return{success:response.success,message:response.message,data:response.data||{added:false}};}catch(error){console.error('Error liking calendar event:',error);throw new Error(error.message||'Failed to like calendar event');}}// Unlike a calendar event (following announcement pattern)\nasync unlikeEvent(eventId){try{const client=this.getAuthenticatedClient();console.log('💔 CalendarReactionService - Unliking calendar event:',{eventId,clientType:client===adminHttpClient?'ADMIN':client===studentHttpClient?'STUDENT':'DEFAULT'});const response=await client.delete(\"/api/calendar/\".concat(eventId,\"/like\"));return{success:response.success,message:response.message,data:response.data||{removed:false}};}catch(error){console.error('Error unliking calendar event:',error);throw new Error(error.message||'Failed to unlike calendar event');}}// Toggle like/unlike for a calendar event\nasync toggleLike(eventId,currentlyLiked){if(currentlyLiked){return this.unlikeEvent(eventId);}else{return this.likeEvent(eventId);}}}export const calendarReactionService=new CalendarReactionService();export default calendarReactionService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}