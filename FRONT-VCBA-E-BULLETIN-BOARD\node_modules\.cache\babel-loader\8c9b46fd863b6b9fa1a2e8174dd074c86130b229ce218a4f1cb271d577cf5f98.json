{"ast": null, "code": "// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, STUDENT_USER_DATA_KEY, STUDENT_REFRESH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\n// Student-specific token manager\nclass StudentTokenManager {\n  getToken() {\n    return localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n  }\n  setToken(token) {\n    localStorage.setItem(STUDENT_AUTH_TOKEN_KEY, token);\n  }\n  removeToken() {\n    localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);\n  }\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n  }\n}\nconst studentTokenManager = new StudentTokenManager();\nexport class StudentAuthService {\n  /**\n   * Custom request method with student token\n   */\n  static async request(method, endpoint, data) {\n    const token = studentTokenManager.getToken();\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n    const config = {\n      method,\n      headers,\n      credentials: 'include'\n    };\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      config.body = JSON.stringify(data);\n    }\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);\n    const responseData = await response.json();\n    if (!response.ok) {\n      var _responseData$error;\n      // Handle error response with proper message\n      const errorMessage = (responseData === null || responseData === void 0 ? void 0 : responseData.message) || (responseData === null || responseData === void 0 ? void 0 : (_responseData$error = responseData.error) === null || _responseData$error === void 0 ? void 0 : _responseData$error.message) || `HTTP ${response.status}`;\n      throw new Error(errorMessage);\n    }\n    return responseData;\n  }\n\n  /**\n   * Get current authenticated student user\n   */\n  static async getCurrentUser() {\n    try {\n      const token = studentTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n      const response = await this.request('GET', API_ENDPOINTS.AUTH.PROFILE);\n      if (response && response.user) {\n        return response.user;\n      }\n      return null;\n    } catch (error) {\n      console.error('Failed to get current student user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login student user\n   */\n  static async login(credentials) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, {\n        ...credentials,\n        userType: 'student'\n      });\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user;\n        const transformedUser = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at\n        };\n\n        // Verify the user is actually a student\n        if (transformedUser.role !== 'student') {\n          throw new Error('Access denied: Student account required');\n        }\n\n        // Store tokens and transformed user data in student-specific keys\n        studentTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(STUDENT_USER_DATA_KEY, JSON.stringify(transformedUser));\n        return {\n          success: true,\n          message: response.message || 'Student login successful',\n          data: {\n            ...response.data,\n            user: transformedUser\n          }\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Student login failed');\n    } catch (error) {\n      console.error('StudentAuthService.login error:', error);\n      throw new Error(error.message || 'Student login failed');\n    }\n  }\n\n  /**\n   * Logout student user\n   */\n  static async logout() {\n    try {\n      console.log('🚪 StudentAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ StudentAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ StudentAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 StudentAuthService - Clearing student local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear student local storage and tokens\n   */\n  static clearLocalStorage() {\n    console.log('🧹 StudentAuthService - Clearing student authentication data');\n    studentTokenManager.removeToken();\n    localStorage.removeItem(STUDENT_USER_DATA_KEY);\n    localStorage.removeItem(STUDENT_REFRESH_TOKEN_KEY);\n    console.log('✅ StudentAuthService - Student authentication data cleared');\n  }\n\n  /**\n   * Check if student is authenticated\n   */\n  static isAuthenticated() {\n    const token = studentTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'student');\n  }\n\n  /**\n   * Get stored student user data\n   */\n  static getStoredUser() {\n    try {\n      const userData = localStorage.getItem(STUDENT_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'student' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored student user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get student user role\n   */\n  static getUserRole() {\n    const user = this.getStoredUser();\n    return (user === null || user === void 0 ? void 0 : user.role) === 'student' ? 'student' : null;\n  }\n\n  /**\n   * Refresh student access token\n   */\n  static async refreshToken() {\n    try {\n      const refreshToken = localStorage.getItem(STUDENT_REFRESH_TOKEN_KEY);\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.REFRESH, {\n        refreshToken\n      });\n      if (response && response.success && response.data) {\n        // Update stored tokens\n        studentTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        return {\n          success: true,\n          message: response.message,\n          data: response.data\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Token refresh failed');\n    } catch (error) {\n      console.error('StudentAuthService.refreshToken error:', error);\n      throw new Error(error.message || 'Token refresh failed');\n    }\n  }\n\n  /**\n   * Get student profile\n   */\n  static async getProfile() {\n    try {\n      var _response$error;\n      const response = await this.request('GET', API_ENDPOINTS.AUTH.PROFILE);\n      if (response && response.user) {\n        // Update stored user data\n        localStorage.setItem(STUDENT_USER_DATA_KEY, JSON.stringify(response.user));\n        return response.user;\n      }\n      throw new Error(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Failed to get profile');\n    } catch (error) {\n      throw new Error(error.message || 'Failed to get profile');\n    }\n  }\n\n  /**\n   * HTTP GET method with student authentication\n   */\n  static async get(endpoint) {\n    return this.request('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with student authentication\n   */\n  static async post(endpoint, data) {\n    return this.request('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with student authentication\n   */\n  static async put(endpoint, data) {\n    return this.request('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with student authentication\n   */\n  static async delete(endpoint) {\n    return this.request('DELETE', endpoint);\n  }\n}\nexport default StudentAuthService;", "map": {"version": 3, "names": ["API_ENDPOINTS", "STUDENT_USER_DATA_KEY", "STUDENT_REFRESH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "API_BASE_URL", "StudentTokenManager", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "removeToken", "removeItem", "getAuthHeaders", "Authorization", "studentTokenManager", "StudentAuthService", "request", "method", "endpoint", "data", "headers", "config", "credentials", "body", "JSON", "stringify", "response", "fetch", "responseData", "json", "ok", "_responseData$error", "errorMessage", "message", "error", "status", "Error", "getCurrentUser", "AUTH", "PROFILE", "user", "console", "login", "LOGIN", "userType", "success", "rawUser", "transformedUser", "id", "admin_id", "student_id", "email", "role", "firstName", "first_name", "lastName", "last_name", "middleName", "middle_name", "suffix", "phoneNumber", "phone_number", "phone", "department", "position", "grade_level", "studentNumber", "student_number", "isActive", "Boolean", "is_active", "lastLogin", "last_login", "createdAt", "account_created_at", "created_at", "updatedAt", "account_updated_at", "updated_at", "accessToken", "refreshToken", "logout", "log", "LOGOUT", "warn", "clearLocalStorage", "isAuthenticated", "userData", "getStoredUser", "parse", "getUserRole", "REFRESH", "getProfile", "_response$error", "get", "post", "put", "delete"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/student-auth.service.ts"], "sourcesContent": ["// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, STUDENT_USER_DATA_KEY, STUDENT_REFRESH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\nimport {\n  LoginCredentials,\n  AuthResponse,\n  User,\n  ApiResponse,\n} from '../types';\n\n// Student-specific token manager\nclass StudentTokenManager {\n  getToken(): string | null {\n    return localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n  }\n\n  setToken(token: string): void {\n    localStorage.setItem(STUDENT_AUTH_TOKEN_KEY, token);\n  }\n\n  removeToken(): void {\n    localStorage.removeItem(STUDENT_AUTH_TOKEN_KEY);\n  }\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    return token ? { Authorization: `Bearer ${token}` } : {};\n  }\n}\n\nconst studentTokenManager = new StudentTokenManager();\n\nexport class StudentAuthService {\n  /**\n   * Custom request method with student token\n   */\n  private static async request<T>(\n    method: string,\n    endpoint: string,\n    data?: any\n  ): Promise<any> {\n    const token = studentTokenManager.getToken();\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n    };\n\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    const config: RequestInit = {\n      method,\n      headers,\n      credentials: 'include',\n    };\n\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      config.body = JSON.stringify(data);\n    }\n\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);\n    const responseData = await response.json();\n\n    if (!response.ok) {\n      // Handle error response with proper message\n      const errorMessage = responseData?.message || responseData?.error?.message || `HTTP ${response.status}`;\n      throw new Error(errorMessage);\n    }\n\n    return responseData;\n  }\n\n  /**\n   * Get current authenticated student user\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = studentTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n\n      const response = await this.request<{ user: User }>('GET', API_ENDPOINTS.AUTH.PROFILE);\n\n      if (response && response.user) {\n        return response.user;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Failed to get current student user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login student user\n   */\n  static async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, { ...credentials, userType: 'student' });\n\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user as any;\n        const transformedUser: User = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at,\n        };\n\n        // Verify the user is actually a student\n        if (transformedUser.role !== 'student') {\n          throw new Error('Access denied: Student account required');\n        }\n\n        // Store tokens and transformed user data in student-specific keys\n        studentTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(STUDENT_USER_DATA_KEY, JSON.stringify(transformedUser));\n\n        return {\n          success: true,\n          message: response.message || 'Student login successful',\n          data: {\n            ...response.data,\n            user: transformedUser,\n          },\n        };\n      }\n\n      throw new Error(response?.message || 'Student login failed');\n    } catch (error: any) {\n      console.error('StudentAuthService.login error:', error);\n      throw new Error(error.message || 'Student login failed');\n    }\n  }\n\n  /**\n   * Logout student user\n   */\n  static async logout(): Promise<void> {\n    try {\n      console.log('🚪 StudentAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ StudentAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ StudentAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 StudentAuthService - Clearing student local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear student local storage and tokens\n   */\n  static clearLocalStorage(): void {\n    console.log('🧹 StudentAuthService - Clearing student authentication data');\n    studentTokenManager.removeToken();\n    localStorage.removeItem(STUDENT_USER_DATA_KEY);\n    localStorage.removeItem(STUDENT_REFRESH_TOKEN_KEY);\n    console.log('✅ StudentAuthService - Student authentication data cleared');\n  }\n\n  /**\n   * Check if student is authenticated\n   */\n  static isAuthenticated(): boolean {\n    const token = studentTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'student');\n  }\n\n  /**\n   * Get stored student user data\n   */\n  static getStoredUser(): User | null {\n    try {\n      const userData = localStorage.getItem(STUDENT_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'student' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored student user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get student user role\n   */\n  static getUserRole(): 'student' | null {\n    const user = this.getStoredUser();\n    return user?.role === 'student' ? 'student' : null;\n  }\n\n  /**\n   * Refresh student access token\n   */\n  static async refreshToken(): Promise<AuthResponse> {\n    try {\n      const refreshToken = localStorage.getItem(STUDENT_REFRESH_TOKEN_KEY);\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.REFRESH, { refreshToken });\n\n      if (response && response.success && response.data) {\n        // Update stored tokens\n        studentTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(STUDENT_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n\n        return {\n          success: true,\n          message: response.message,\n          data: response.data,\n        };\n      }\n\n      throw new Error(response?.message || 'Token refresh failed');\n    } catch (error: any) {\n      console.error('StudentAuthService.refreshToken error:', error);\n      throw new Error(error.message || 'Token refresh failed');\n    }\n  }\n\n  /**\n   * Get student profile\n   */\n  static async getProfile(): Promise<User> {\n    try {\n      const response = await this.request<{ user: User }>('GET', API_ENDPOINTS.AUTH.PROFILE);\n\n      if (response && response.user) {\n        // Update stored user data\n        localStorage.setItem(STUDENT_USER_DATA_KEY, JSON.stringify(response.user));\n        return response.user;\n      }\n\n      throw new Error(response.error?.message || 'Failed to get profile');\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to get profile');\n    }\n  }\n\n  /**\n   * HTTP GET method with student authentication\n   */\n  static async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with student authentication\n   */\n  static async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with student authentication\n   */\n  static async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with student authentication\n   */\n  static async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('DELETE', endpoint);\n  }\n}\n\nexport default StudentAuthService;\n"], "mappings": "AAAA;AACA,SAASA,aAAa,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,sBAAsB,EAAEC,YAAY,QAAQ,qBAAqB;AAQ3I;AACA,MAAMC,mBAAmB,CAAC;EACxBC,QAAQA,CAAA,EAAkB;IACxB,OAAOC,YAAY,CAACC,OAAO,CAACL,sBAAsB,CAAC;EACrD;EAEAM,QAAQA,CAACC,KAAa,EAAQ;IAC5BH,YAAY,CAACI,OAAO,CAACR,sBAAsB,EAAEO,KAAK,CAAC;EACrD;EAEAE,WAAWA,CAAA,EAAS;IAClBL,YAAY,CAACM,UAAU,CAACV,sBAAsB,CAAC;EACjD;EAEAW,cAAcA,CAAA,EAA2B;IACvC,MAAMJ,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;IAC7B,OAAOI,KAAK,GAAG;MAAEK,aAAa,EAAE,UAAUL,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;AACF;AAEA,MAAMM,mBAAmB,GAAG,IAAIX,mBAAmB,CAAC,CAAC;AAErD,OAAO,MAAMY,kBAAkB,CAAC;EAC9B;AACF;AACA;EACE,aAAqBC,OAAOA,CAC1BC,MAAc,EACdC,QAAgB,EAChBC,IAAU,EACI;IACd,MAAMX,KAAK,GAAGM,mBAAmB,CAACV,QAAQ,CAAC,CAAC;IAC5C,MAAMgB,OAA+B,GAAG;MACtC,cAAc,EAAE;IAClB,CAAC;IAED,IAAIZ,KAAK,EAAE;MACTY,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUZ,KAAK,EAAE;IAC9C;IAEA,MAAMa,MAAmB,GAAG;MAC1BJ,MAAM;MACNG,OAAO;MACPE,WAAW,EAAE;IACf,CAAC;IAED,IAAIH,IAAI,KAAKF,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,OAAO,CAAC,EAAE;MACzEI,MAAM,CAACE,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC;IACpC;IAEA,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGzB,YAAY,GAAGgB,QAAQ,EAAE,EAAEG,MAAM,CAAC;IAClE,MAAMO,YAAY,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAE1C,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;MAAA,IAAAC,mBAAA;MAChB;MACA,MAAMC,YAAY,GAAG,CAAAJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEK,OAAO,MAAIL,YAAY,aAAZA,YAAY,wBAAAG,mBAAA,GAAZH,YAAY,CAAEM,KAAK,cAAAH,mBAAA,uBAAnBA,mBAAA,CAAqBE,OAAO,KAAI,QAAQP,QAAQ,CAACS,MAAM,EAAE;MACvG,MAAM,IAAIC,KAAK,CAACJ,YAAY,CAAC;IAC/B;IAEA,OAAOJ,YAAY;EACrB;;EAEA;AACF;AACA;EACE,aAAaS,cAAcA,CAAA,EAAyB;IAClD,IAAI;MACF,MAAM7B,KAAK,GAAGM,mBAAmB,CAACV,QAAQ,CAAC,CAAC;MAC5C,IAAI,CAACI,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MAEA,MAAMkB,QAAQ,GAAG,MAAM,IAAI,CAACV,OAAO,CAAiB,KAAK,EAAElB,aAAa,CAACwC,IAAI,CAACC,OAAO,CAAC;MAEtF,IAAIb,QAAQ,IAAIA,QAAQ,CAACc,IAAI,EAAE;QAC7B,OAAOd,QAAQ,CAACc,IAAI;MACtB;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAON,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAaQ,KAAKA,CAACpB,WAA6B,EAAyB;IACvE,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM,IAAI,CAACV,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACwC,IAAI,CAACK,KAAK,EAAE;QAAE,GAAGrB,WAAW;QAAEsB,QAAQ,EAAE;MAAU,CAAC,CAAC;MAE9G,IAAIlB,QAAQ,IAAIA,QAAQ,CAACmB,OAAO,IAAInB,QAAQ,CAACP,IAAI,EAAE;QACjD;QACA,MAAM2B,OAAO,GAAGpB,QAAQ,CAACP,IAAI,CAACqB,IAAW;QACzC,MAAMO,eAAqB,GAAG;UAC5BC,EAAE,EAAEF,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACI,UAAU;UAC1CC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBC,IAAI,EAAEN,OAAO,CAACG,QAAQ,GAAG,OAAO,GAAG,SAAS;UAC5CI,SAAS,EAAEP,OAAO,CAACQ,UAAU;UAC7BC,QAAQ,EAAET,OAAO,CAACU,SAAS;UAC3BC,UAAU,EAAEX,OAAO,CAACY,WAAW;UAC/BC,MAAM,EAAEb,OAAO,CAACa,MAAM;UACtBC,WAAW,EAAEd,OAAO,CAACe,YAAY,IAAIf,OAAO,CAACgB,KAAK;UAClDC,UAAU,EAAEjB,OAAO,CAACiB,UAAU;UAC9BC,QAAQ,EAAElB,OAAO,CAACkB,QAAQ;UAC1BC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;UAChCC,aAAa,EAAEpB,OAAO,CAACqB,cAAc;UACrCC,QAAQ,EAAEC,OAAO,CAACvB,OAAO,CAACwB,SAAS,CAAC;UACpCC,SAAS,EAAEzB,OAAO,CAAC0B,UAAU;UAC7BC,SAAS,EAAE3B,OAAO,CAAC4B,kBAAkB,IAAI5B,OAAO,CAAC6B,UAAU;UAC3DC,SAAS,EAAE9B,OAAO,CAAC+B,kBAAkB,IAAI/B,OAAO,CAACgC;QACnD,CAAC;;QAED;QACA,IAAI/B,eAAe,CAACK,IAAI,KAAK,SAAS,EAAE;UACtC,MAAM,IAAIhB,KAAK,CAAC,yCAAyC,CAAC;QAC5D;;QAEA;QACAtB,mBAAmB,CAACP,QAAQ,CAACmB,QAAQ,CAACP,IAAI,CAAC4D,WAAW,CAAC;QACvD,IAAIrD,QAAQ,CAACP,IAAI,CAAC6D,YAAY,EAAE;UAC9B3E,YAAY,CAACI,OAAO,CAACT,yBAAyB,EAAE0B,QAAQ,CAACP,IAAI,CAAC6D,YAAY,CAAC;QAC7E;QACA3E,YAAY,CAACI,OAAO,CAACV,qBAAqB,EAAEyB,IAAI,CAACC,SAAS,CAACsB,eAAe,CAAC,CAAC;QAE5E,OAAO;UACLF,OAAO,EAAE,IAAI;UACbZ,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAI,0BAA0B;UACvDd,IAAI,EAAE;YACJ,GAAGO,QAAQ,CAACP,IAAI;YAChBqB,IAAI,EAAEO;UACR;QACF,CAAC;MACH;MAEA,MAAM,IAAIX,KAAK,CAAC,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,OAAO,KAAI,sBAAsB,CAAC;IAC9D,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBO,OAAO,CAACP,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAM,IAAIE,KAAK,CAACF,KAAK,CAACD,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAagD,MAAMA,CAAA,EAAkB;IACnC,IAAI;MACFxC,OAAO,CAACyC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAM,IAAI,CAAClE,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACwC,IAAI,CAAC6C,MAAM,CAAC;MACrD1C,OAAO,CAACyC,GAAG,CAAC,iDAAiD,CAAC;IAChE,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdO,OAAO,CAAC2C,IAAI,CAAC,6EAA6E,EAAElD,KAAK,CAAC;IACpG,CAAC,SAAS;MACRO,OAAO,CAACyC,GAAG,CAAC,wDAAwD,CAAC;MACrE,IAAI,CAACG,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACE,OAAOA,iBAAiBA,CAAA,EAAS;IAC/B5C,OAAO,CAACyC,GAAG,CAAC,8DAA8D,CAAC;IAC3EpE,mBAAmB,CAACJ,WAAW,CAAC,CAAC;IACjCL,YAAY,CAACM,UAAU,CAACZ,qBAAqB,CAAC;IAC9CM,YAAY,CAACM,UAAU,CAACX,yBAAyB,CAAC;IAClDyC,OAAO,CAACyC,GAAG,CAAC,4DAA4D,CAAC;EAC3E;;EAEA;AACF;AACA;EACE,OAAOI,eAAeA,CAAA,EAAY;IAChC,MAAM9E,KAAK,GAAGM,mBAAmB,CAACV,QAAQ,CAAC,CAAC;IAC5C,MAAMmF,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,OAAO,CAAC,EAAEhF,KAAK,IAAI+E,QAAQ,IAAIA,QAAQ,CAACnC,IAAI,KAAK,SAAS,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,OAAOoC,aAAaA,CAAA,EAAgB;IAClC,IAAI;MACF,MAAMD,QAAQ,GAAGlF,YAAY,CAACC,OAAO,CAACP,qBAAqB,CAAC;MAC5D,MAAMyC,IAAI,GAAG+C,QAAQ,GAAG/D,IAAI,CAACiE,KAAK,CAACF,QAAQ,CAAC,GAAG,IAAI;MACnD,OAAO/C,IAAI,IAAIA,IAAI,CAACY,IAAI,KAAK,SAAS,GAAGZ,IAAI,GAAG,IAAI;IACtD,CAAC,CAAC,OAAON,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,OAAOwD,WAAWA,CAAA,EAAqB;IACrC,MAAMlD,IAAI,GAAG,IAAI,CAACgD,aAAa,CAAC,CAAC;IACjC,OAAO,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI,MAAK,SAAS,GAAG,SAAS,GAAG,IAAI;EACpD;;EAEA;AACF;AACA;EACE,aAAa4B,YAAYA,CAAA,EAA0B;IACjD,IAAI;MACF,MAAMA,YAAY,GAAG3E,YAAY,CAACC,OAAO,CAACN,yBAAyB,CAAC;MACpE,IAAI,CAACgF,YAAY,EAAE;QACjB,MAAM,IAAI5C,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MAEA,MAAMV,QAAQ,GAAG,MAAM,IAAI,CAACV,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACwC,IAAI,CAACqD,OAAO,EAAE;QAAEX;MAAa,CAAC,CAAC;MAEzF,IAAItD,QAAQ,IAAIA,QAAQ,CAACmB,OAAO,IAAInB,QAAQ,CAACP,IAAI,EAAE;QACjD;QACAL,mBAAmB,CAACP,QAAQ,CAACmB,QAAQ,CAACP,IAAI,CAAC4D,WAAW,CAAC;QACvD,IAAIrD,QAAQ,CAACP,IAAI,CAAC6D,YAAY,EAAE;UAC9B3E,YAAY,CAACI,OAAO,CAACT,yBAAyB,EAAE0B,QAAQ,CAACP,IAAI,CAAC6D,YAAY,CAAC;QAC7E;QAEA,OAAO;UACLnC,OAAO,EAAE,IAAI;UACbZ,OAAO,EAAEP,QAAQ,CAACO,OAAO;UACzBd,IAAI,EAAEO,QAAQ,CAACP;QACjB,CAAC;MACH;MAEA,MAAM,IAAIiB,KAAK,CAAC,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,OAAO,KAAI,sBAAsB,CAAC;IAC9D,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBO,OAAO,CAACP,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAM,IAAIE,KAAK,CAACF,KAAK,CAACD,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAa2D,UAAUA,CAAA,EAAkB;IACvC,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMnE,QAAQ,GAAG,MAAM,IAAI,CAACV,OAAO,CAAiB,KAAK,EAAElB,aAAa,CAACwC,IAAI,CAACC,OAAO,CAAC;MAEtF,IAAIb,QAAQ,IAAIA,QAAQ,CAACc,IAAI,EAAE;QAC7B;QACAnC,YAAY,CAACI,OAAO,CAACV,qBAAqB,EAAEyB,IAAI,CAACC,SAAS,CAACC,QAAQ,CAACc,IAAI,CAAC,CAAC;QAC1E,OAAOd,QAAQ,CAACc,IAAI;MACtB;MAEA,MAAM,IAAIJ,KAAK,CAAC,EAAAyD,eAAA,GAAAnE,QAAQ,CAACQ,KAAK,cAAA2D,eAAA,uBAAdA,eAAA,CAAgB5D,OAAO,KAAI,uBAAuB,CAAC;IACrE,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,MAAM,IAAIE,KAAK,CAACF,KAAK,CAACD,OAAO,IAAI,uBAAuB,CAAC;IAC3D;EACF;;EAEA;AACF;AACA;EACE,aAAa6D,GAAGA,CAAI5E,QAAgB,EAA2B;IAC7D,OAAO,IAAI,CAACF,OAAO,CAAiB,KAAK,EAAEE,QAAQ,CAAC;EACtD;;EAEA;AACF;AACA;EACE,aAAa6E,IAAIA,CAAI7E,QAAgB,EAAEC,IAAU,EAA2B;IAC1E,OAAO,IAAI,CAACH,OAAO,CAAiB,MAAM,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,aAAa6E,GAAGA,CAAI9E,QAAgB,EAAEC,IAAU,EAA2B;IACzE,OAAO,IAAI,CAACH,OAAO,CAAiB,KAAK,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC5D;;EAEA;AACF;AACA;EACE,aAAa8E,MAAMA,CAAI/E,QAAgB,EAA2B;IAChE,OAAO,IAAI,CAACF,OAAO,CAAiB,QAAQ,EAAEE,QAAQ,CAAC;EACzD;AACF;AAEA,eAAeH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}