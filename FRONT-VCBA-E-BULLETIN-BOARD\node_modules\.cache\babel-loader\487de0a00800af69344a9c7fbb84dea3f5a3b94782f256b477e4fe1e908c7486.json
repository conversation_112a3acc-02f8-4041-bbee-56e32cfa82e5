{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12H3\",\n  key: \"1a2rj7\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M10 6H3\",\n  key: \"lf8lx7\"\n}], [\"path\", {\n  d: \"M21 18V8a2 2 0 0 0-2-2h-5\",\n  key: \"1hghli\"\n}], [\"path\", {\n  d: \"m16 8-2-2 2-2\",\n  key: \"160uvd\"\n}]];\nconst ListStart = createLucideIcon(\"list-start\", __iconNode);\nexport { __iconNode, ListStart as default };\n//# sourceMappingURL=list-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}