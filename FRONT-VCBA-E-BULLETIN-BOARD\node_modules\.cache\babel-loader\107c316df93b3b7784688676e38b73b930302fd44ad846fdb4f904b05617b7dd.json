{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 15h-6v4l-7-7 7-7v4h6v6z\",\n  key: \"lbrdak\"\n}]];\nconst ArrowBigLeft = createLucideIcon(\"arrow-big-left\", __iconNode);\nexport { __iconNode, ArrowBigLeft as default };\n//# sourceMappingURL=arrow-big-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}