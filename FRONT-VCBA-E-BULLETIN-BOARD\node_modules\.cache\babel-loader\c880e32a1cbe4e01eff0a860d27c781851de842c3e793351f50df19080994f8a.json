{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Archive.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Archive as ArchiveIcon, FileText, Calendar, Users } from 'lucide-react';\nimport { archiveService } from '../../services/archiveService';\nimport ArchivedAnnouncements from '../../components/admin/archive/ArchivedAnnouncements';\nimport ArchivedCalendarEvents from '../../components/admin/archive/ArchivedCalendarEvents';\nimport ArchivedStudents from '../../components/admin/archive/ArchivedStudents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Archive = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('announcements');\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadStatistics();\n  }, []);\n  const loadStatistics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await archiveService.getArchiveStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      } else {\n        setError('Failed to load archive statistics');\n      }\n    } catch (error) {\n      console.error('Error loading archive statistics:', error);\n      setError(error.message || 'Failed to load archive statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const tabs = [{\n    id: 'announcements',\n    label: 'Announcements',\n    icon: FileText,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.announcements) || 0,\n    color: '#3b82f6'\n  }, {\n    id: 'calendar',\n    label: 'Calendar Events',\n    icon: Calendar,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.calendar_events) || 0,\n    color: '#10b981'\n  }, {\n    id: 'students',\n    label: 'Students',\n    icon: Users,\n    count: (statistics === null || statistics === void 0 ? void 0 : statistics.students) || 0,\n    color: '#f59e0b'\n  }];\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'announcements':\n        return /*#__PURE__*/_jsxDEV(ArchivedAnnouncements, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n      case 'calendar':\n        return /*#__PURE__*/_jsxDEV(ArchivedCalendarEvents, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      case 'students':\n        return /*#__PURE__*/_jsxDEV(ArchivedStudents, {\n          onRestoreSuccess: loadStatistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px',\n        color: '#6b7280'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), \"Loading archive data...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(ArchiveIcon, {\n          size: 24,\n          color: \"white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: 0,\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Archive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"View and manage archived records\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1.5rem',\n        color: '#dc2626'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), statistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '12px',\n          padding: '1.5rem',\n          border: '1px solid #e5e7eb',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',\n          borderColor: activeTab === tab.id ? tab.color : '#e5e7eb'\n        },\n        onClick: () => setActiveTab(tab.id),\n        onMouseEnter: e => {\n          if (activeTab !== tab.id) {\n            e.currentTarget.style.transform = 'translateY(-1px)';\n            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n          }\n        },\n        onMouseLeave: e => {\n          if (activeTab !== tab.id) {\n            e.currentTarget.style.transform = 'none';\n            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              background: `${tab.color}20`,\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(tab.icon, {\n              size: 18,\n              color: tab.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#6b7280'\n            },\n            children: tab.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: tab.color\n          },\n          children: tab.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',\n          borderRadius: '12px',\n          padding: '1.5rem',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              background: 'rgba(255, 255, 255, 0.2)',\n              borderRadius: '8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ArchiveIcon, {\n              size: 18,\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              opacity: 0.9\n            },\n            children: \"Total Archived\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700'\n          },\n          children: statistics.total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '2px solid #e5e7eb',\n        marginBottom: '2rem'\n      },\n      children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setActiveTab(tab.id),\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '1rem 1.5rem',\n          background: 'none',\n          border: 'none',\n          borderBottom: `3px solid ${activeTab === tab.id ? tab.color : 'transparent'}`,\n          color: activeTab === tab.id ? tab.color : '#6b7280',\n          fontWeight: activeTab === tab.id ? '600' : '500',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this), tab.label, tab.count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: activeTab === tab.id ? tab.color : '#e5e7eb',\n            color: activeTab === tab.id ? 'white' : '#6b7280',\n            fontSize: '0.75rem',\n            fontWeight: '600',\n            padding: '0.25rem 0.5rem',\n            borderRadius: '12px',\n            minWidth: '20px',\n            textAlign: 'center'\n          },\n          children: tab.count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: renderTabContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(Archive, \"8sA8jgfn0X6j3CyM0AqroqTfv40=\");\n_c = Archive;\nexport default Archive;\nvar _c;\n$RefreshReg$(_c, \"Archive\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Archive", "ArchiveIcon", "FileText", "Calendar", "Users", "archiveService", "ArchivedAnnouncements", "ArchivedCalendarEvents", "ArchivedStudents", "jsxDEV", "_jsxDEV", "_s", "activeTab", "setActiveTab", "statistics", "setStatistics", "loading", "setLoading", "error", "setError", "loadStatistics", "response", "getArchiveStatistics", "success", "data", "console", "message", "tabs", "id", "label", "icon", "count", "announcements", "color", "calendar_events", "students", "renderTabContent", "onRestoreSuccess", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "minHeight", "children", "textAlign", "width", "height", "border", "borderTop", "borderRadius", "animation", "margin", "padding", "gap", "marginBottom", "background", "size", "fontSize", "fontWeight", "gridTemplateColumns", "map", "tab", "boxShadow", "cursor", "transition", "transform", "borderColor", "onClick", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "opacity", "total", "borderBottom", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Archive.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Archive as ArchiveIcon, FileText, Calendar, Users, RotateCcw, Trash2, Search, Filter } from 'lucide-react';\nimport { archiveService, ArchiveStatistics } from '../../services/archiveService';\nimport ArchivedAnnouncements from '../../components/admin/archive/ArchivedAnnouncements';\nimport ArchivedCalendarEvents from '../../components/admin/archive/ArchivedCalendarEvents';\nimport ArchivedStudents from '../../components/admin/archive/ArchivedStudents';\n\ntype ArchiveTab = 'announcements' | 'calendar' | 'students';\n\nconst Archive: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<ArchiveTab>('announcements');\n  const [statistics, setStatistics] = useState<ArchiveStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadStatistics();\n  }, []);\n\n  const loadStatistics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await archiveService.getArchiveStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      } else {\n        setError('Failed to load archive statistics');\n      }\n    } catch (error: any) {\n      console.error('Error loading archive statistics:', error);\n      setError(error.message || 'Failed to load archive statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    {\n      id: 'announcements' as ArchiveTab,\n      label: 'Announcements',\n      icon: FileText,\n      count: statistics?.announcements || 0,\n      color: '#3b82f6'\n    },\n    {\n      id: 'calendar' as ArchiveTab,\n      label: 'Calendar Events',\n      icon: Calendar,\n      count: statistics?.calendar_events || 0,\n      color: '#10b981'\n    },\n    {\n      id: 'students' as ArchiveTab,\n      label: 'Students',\n      icon: Users,\n      count: statistics?.students || 0,\n      color: '#f59e0b'\n    }\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'announcements':\n        return <ArchivedAnnouncements onRestoreSuccess={loadStatistics} />;\n      case 'calendar':\n        return <ArchivedCalendarEvents onRestoreSuccess={loadStatistics} />;\n      case 'students':\n        return <ArchivedStudents onRestoreSuccess={loadStatistics} />;\n      default:\n        return null;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px',\n        color: '#6b7280'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{\n            width: '40px',\n            height: '40px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }} />\n          Loading archive data...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      }}>\n        <div style={{\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}>\n          <ArchiveIcon size={24} color=\"white\" />\n        </div>\n        <div>\n          <h1 style={{\n            margin: 0,\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Archive\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            View and manage archived records\n          </p>\n        </div>\n      </div>\n\n      {error && (\n        <div style={{\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          padding: '1rem',\n          marginBottom: '1.5rem',\n          color: '#dc2626'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      {statistics && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem',\n          marginBottom: '2rem'\n        }}>\n          {tabs.map((tab) => (\n            <div\n              key={tab.id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                padding: '1.5rem',\n                border: '1px solid #e5e7eb',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',\n                borderColor: activeTab === tab.id ? tab.color : '#e5e7eb'\n              }}\n              onClick={() => setActiveTab(tab.id)}\n              onMouseEnter={(e) => {\n                if (activeTab !== tab.id) {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (activeTab !== tab.id) {\n                  e.currentTarget.style.transform = 'none';\n                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                }\n              }}\n            >\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '0.5rem'\n              }}>\n                <div style={{\n                  width: '32px',\n                  height: '32px',\n                  background: `${tab.color}20`,\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <tab.icon size={18} color={tab.color} />\n                </div>\n                <span style={{\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  color: '#6b7280'\n                }}>\n                  {tab.label}\n                </span>\n              </div>\n              <div style={{\n                fontSize: '2rem',\n                fontWeight: '700',\n                color: tab.color\n              }}>\n                {tab.count}\n              </div>\n            </div>\n          ))}\n          \n          {/* Total Card */}\n          <div style={{\n            background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            color: 'white'\n          }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '0.5rem'\n            }}>\n              <div style={{\n                width: '32px',\n                height: '32px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                borderRadius: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <ArchiveIcon size={18} color=\"white\" />\n              </div>\n              <span style={{\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                opacity: 0.9\n              }}>\n                Total Archived\n              </span>\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700'\n            }}>\n              {statistics.total}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Tab Navigation */}\n      <div style={{\n        display: 'flex',\n        borderBottom: '2px solid #e5e7eb',\n        marginBottom: '2rem'\n      }}>\n        {tabs.map((tab) => (\n          <button\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '1rem 1.5rem',\n              background: 'none',\n              border: 'none',\n              borderBottom: `3px solid ${activeTab === tab.id ? tab.color : 'transparent'}`,\n              color: activeTab === tab.id ? tab.color : '#6b7280',\n              fontWeight: activeTab === tab.id ? '600' : '500',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n          >\n            <tab.icon size={18} />\n            {tab.label}\n            {tab.count > 0 && (\n              <span style={{\n                background: activeTab === tab.id ? tab.color : '#e5e7eb',\n                color: activeTab === tab.id ? 'white' : '#6b7280',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '12px',\n                minWidth: '20px',\n                textAlign: 'center'\n              }}>\n                {tab.count}\n              </span>\n            )}\n          </button>\n        ))}\n      </div>\n\n      {/* Tab Content */}\n      <div>\n        {renderTabContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default Archive;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,IAAIC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,QAA2C,cAAc;AACnH,SAASC,cAAc,QAA2B,+BAA+B;AACjF,OAAOC,qBAAqB,MAAM,sDAAsD;AACxF,OAAOC,sBAAsB,MAAM,uDAAuD;AAC1F,OAAOC,gBAAgB,MAAM,iDAAiD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI/E,MAAMV,OAAiB,GAAGA,CAAA,KAAM;EAAAW,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAa,eAAe,CAAC;EACvE,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAA2B,IAAI,CAAC;EAC5E,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,QAAQ,GAAG,MAAMhB,cAAc,CAACiB,oBAAoB,CAAC,CAAC;MAC5D,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBR,aAAa,CAACM,QAAQ,CAACG,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLL,QAAQ,CAAC,mCAAmC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBO,OAAO,CAACP,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAACD,KAAK,CAACQ,OAAO,IAAI,mCAAmC,CAAC;IAChE,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,eAA6B;IACjCC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE5B,QAAQ;IACd6B,KAAK,EAAE,CAAAjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,aAAa,KAAI,CAAC;IACrCC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,EAAE,EAAE,UAAwB;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE3B,QAAQ;IACd4B,KAAK,EAAE,CAAAjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoB,eAAe,KAAI,CAAC;IACvCD,KAAK,EAAE;EACT,CAAC,EACD;IACEL,EAAE,EAAE,UAAwB;IAC5BC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE1B,KAAK;IACX2B,KAAK,EAAE,CAAAjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqB,QAAQ,KAAI,CAAC;IAChCF,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQxB,SAAS;MACf,KAAK,eAAe;QAClB,oBAAOF,OAAA,CAACJ,qBAAqB;UAAC+B,gBAAgB,EAAEjB;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpE,KAAK,UAAU;QACb,oBAAO/B,OAAA,CAACH,sBAAsB;UAAC8B,gBAAgB,EAAEjB;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrE,KAAK,UAAU;QACb,oBAAO/B,OAAA,CAACF,gBAAgB;UAAC6B,gBAAgB,EAAEjB;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKgC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,OAAO;QAClBb,KAAK,EAAE;MACT,CAAE;MAAAc,QAAA,eACArC,OAAA;QAAKgC,KAAK,EAAE;UAAEM,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCrC,OAAA;UAAKgC,KAAK,EAAE;YACVO,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE,mBAAmB;YAC9BC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE;UACV;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAEP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/B,OAAA;IAAKgC,KAAK,EAAE;MAAEc,OAAO,EAAE;IAAO,CAAE;IAAAT,QAAA,gBAE9BrC,OAAA;MAAKgC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBY,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,gBACArC,OAAA;QAAKgC,KAAK,EAAE;UACVO,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdS,UAAU,EAAE,mDAAmD;UAC/DN,YAAY,EAAE,MAAM;UACpBV,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE;QAClB,CAAE;QAAAG,QAAA,eACArC,OAAA,CAACT,WAAW;UAAC2D,IAAI,EAAE,EAAG;UAAC3B,KAAK,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACN/B,OAAA;QAAAqC,QAAA,gBACErC,OAAA;UAAIgC,KAAK,EAAE;YACTa,MAAM,EAAE,CAAC;YACTM,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjB7B,KAAK,EAAE;UACT,CAAE;UAAAc,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/B,OAAA;UAAGgC,KAAK,EAAE;YACRa,MAAM,EAAE,CAAC;YACTtB,KAAK,EAAE,SAAS;YAChB4B,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELvB,KAAK,iBACJR,OAAA;MAAKgC,KAAK,EAAE;QACViB,UAAU,EAAE,SAAS;QACrBR,MAAM,EAAE,mBAAmB;QAC3BE,YAAY,EAAE,KAAK;QACnBG,OAAO,EAAE,MAAM;QACfE,YAAY,EAAE,QAAQ;QACtBzB,KAAK,EAAE;MACT,CAAE;MAAAc,QAAA,EACC7B;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA3B,UAAU,iBACTJ,OAAA;MAAKgC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfoB,mBAAmB,EAAE,sCAAsC;QAC3DN,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,GACCpB,IAAI,CAACqC,GAAG,CAAEC,GAAG,iBACZvD,OAAA;QAEEgC,KAAK,EAAE;UACLiB,UAAU,EAAE,OAAO;UACnBN,YAAY,EAAE,MAAM;UACpBG,OAAO,EAAE,QAAQ;UACjBL,MAAM,EAAE,mBAAmB;UAC3Be,SAAS,EAAE,8BAA8B;UACzCC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3BC,SAAS,EAAEzD,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAG,kBAAkB,GAAG,MAAM;UAC7D0C,WAAW,EAAE1D,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAGqC,GAAG,CAAChC,KAAK,GAAG;QAClD,CAAE;QACFsC,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAACoD,GAAG,CAACrC,EAAE,CAAE;QACpC4C,YAAY,EAAGC,CAAC,IAAK;UACnB,IAAI7D,SAAS,KAAKqD,GAAG,CAACrC,EAAE,EAAE;YACxB6C,CAAC,CAACC,aAAa,CAAChC,KAAK,CAAC2B,SAAS,GAAG,kBAAkB;YACpDI,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACwB,SAAS,GAAG,gCAAgC;UACpE;QACF,CAAE;QACFS,YAAY,EAAGF,CAAC,IAAK;UACnB,IAAI7D,SAAS,KAAKqD,GAAG,CAACrC,EAAE,EAAE;YACxB6C,CAAC,CAACC,aAAa,CAAChC,KAAK,CAAC2B,SAAS,GAAG,MAAM;YACxCI,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACwB,SAAS,GAAG,8BAA8B;UAClE;QACF,CAAE;QAAAnB,QAAA,gBAEFrC,OAAA;UAAKgC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBY,GAAG,EAAE,SAAS;YACdC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBACArC,OAAA;YAAKgC,KAAK,EAAE;cACVO,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdS,UAAU,EAAE,GAAGM,GAAG,CAAChC,KAAK,IAAI;cAC5BoB,YAAY,EAAE,KAAK;cACnBV,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACArC,OAAA,CAACuD,GAAG,CAACnC,IAAI;cAAC8B,IAAI,EAAE,EAAG;cAAC3B,KAAK,EAAEgC,GAAG,CAAChC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN/B,OAAA;YAAMgC,KAAK,EAAE;cACXmB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjB7B,KAAK,EAAE;YACT,CAAE;YAAAc,QAAA,EACCkB,GAAG,CAACpC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/B,OAAA;UAAKgC,KAAK,EAAE;YACVmB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjB7B,KAAK,EAAEgC,GAAG,CAAChC;UACb,CAAE;UAAAc,QAAA,EACCkB,GAAG,CAAClC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA,GAzDDwB,GAAG,CAACrC,EAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0DR,CACN,CAAC,eAGF/B,OAAA;QAAKgC,KAAK,EAAE;UACViB,UAAU,EAAE,mDAAmD;UAC/DN,YAAY,EAAE,MAAM;UACpBG,OAAO,EAAE,QAAQ;UACjBvB,KAAK,EAAE;QACT,CAAE;QAAAc,QAAA,gBACArC,OAAA;UAAKgC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBY,GAAG,EAAE,SAAS;YACdC,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBACArC,OAAA;YAAKgC,KAAK,EAAE;cACVO,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdS,UAAU,EAAE,0BAA0B;cACtCN,YAAY,EAAE,KAAK;cACnBV,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACArC,OAAA,CAACT,WAAW;cAAC2D,IAAI,EAAE,EAAG;cAAC3B,KAAK,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACN/B,OAAA;YAAMgC,KAAK,EAAE;cACXmB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBc,OAAO,EAAE;YACX,CAAE;YAAA7B,QAAA,EAAC;UAEH;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN/B,OAAA;UAAKgC,KAAK,EAAE;YACVmB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EACCjC,UAAU,CAAC+D;QAAK;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD/B,OAAA;MAAKgC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfmC,YAAY,EAAE,mBAAmB;QACjCpB,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,EACCpB,IAAI,CAACqC,GAAG,CAAEC,GAAG,iBACZvD,OAAA;QAEE6D,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAACoD,GAAG,CAACrC,EAAE,CAAE;QACpCc,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBY,GAAG,EAAE,QAAQ;UACbD,OAAO,EAAE,aAAa;UACtBG,UAAU,EAAE,MAAM;UAClBR,MAAM,EAAE,MAAM;UACd2B,YAAY,EAAE,aAAalE,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAGqC,GAAG,CAAChC,KAAK,GAAG,aAAa,EAAE;UAC7EA,KAAK,EAAErB,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAGqC,GAAG,CAAChC,KAAK,GAAG,SAAS;UACnD6B,UAAU,EAAElD,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAG,KAAK,GAAG,KAAK;UAChDuC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE;QACd,CAAE;QAAArB,QAAA,gBAEFrC,OAAA,CAACuD,GAAG,CAACnC,IAAI;UAAC8B,IAAI,EAAE;QAAG;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrBwB,GAAG,CAACpC,KAAK,EACToC,GAAG,CAAClC,KAAK,GAAG,CAAC,iBACZrB,OAAA;UAAMgC,KAAK,EAAE;YACXiB,UAAU,EAAE/C,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAGqC,GAAG,CAAChC,KAAK,GAAG,SAAS;YACxDA,KAAK,EAAErB,SAAS,KAAKqD,GAAG,CAACrC,EAAE,GAAG,OAAO,GAAG,SAAS;YACjDiC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,KAAK;YACjBN,OAAO,EAAE,gBAAgB;YACzBH,YAAY,EAAE,MAAM;YACpB0B,QAAQ,EAAE,MAAM;YAChB/B,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EACCkB,GAAG,CAAClC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACP;MAAA,GA/BIwB,GAAG,CAACrC,EAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCL,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/B,OAAA;MAAAqC,QAAA,EACGX,gBAAgB,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAjTIX,OAAiB;AAAAgF,EAAA,GAAjBhF,OAAiB;AAmTvB,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}