{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"3\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"j77dae\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"15\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"bq30hj\"\n}], [\"path\", {\n  d: \"M3 2v20\",\n  key: \"1d2pfg\"\n}], [\"path\", {\n  d: \"M21 2v20\",\n  key: \"p059bm\"\n}]];\nconst AlignHorizontalSpaceBetween = createLucideIcon(\"align-horizontal-space-between\", __iconNode);\nexport { __iconNode, AlignHorizontalSpaceBetween as default };\n//# sourceMappingURL=align-horizontal-space-between.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}