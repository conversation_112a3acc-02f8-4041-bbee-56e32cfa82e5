{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 5H6a2 2 0 0 0-2 2v3\",\n  key: \"l96uqu\"\n}], [\"path\", {\n  d: \"m9 8 3-3-3-3\",\n  key: \"1gzgc3\"\n}], [\"path\", {\n  d: \"M4 14v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2\",\n  key: \"1w2k5h\"\n}]];\nconst RotateCwSquare = createLucideIcon(\"rotate-cw-square\", __iconNode);\nexport { __iconNode, RotateCwSquare as default };\n//# sourceMappingURL=rotate-cw-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}