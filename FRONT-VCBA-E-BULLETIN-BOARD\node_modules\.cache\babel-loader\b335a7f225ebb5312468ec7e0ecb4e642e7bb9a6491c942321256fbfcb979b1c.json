{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 17a10 10 0 0 0-20 0\",\n  key: \"ozegv\"\n}], [\"path\", {\n  d: \"M6 17a6 6 0 0 1 12 0\",\n  key: \"5giftw\"\n}], [\"path\", {\n  d: \"M10 17a2 2 0 0 1 4 0\",\n  key: \"gnsikk\"\n}]];\nconst Rainbow = createLucideIcon(\"rainbow\", __iconNode);\nexport { __iconNode, Rainbow as default };\n//# sourceMappingURL=rainbow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}