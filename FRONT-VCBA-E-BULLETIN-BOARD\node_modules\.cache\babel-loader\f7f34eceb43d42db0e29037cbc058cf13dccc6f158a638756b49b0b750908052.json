{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 9V5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4\",\n  key: \"vvzvr1\"\n}], [\"path\", {\n  d: \"M8 8v1\",\n  key: \"xcqmfk\"\n}], [\"path\", {\n  d: \"M12 8v1\",\n  key: \"1rj8u4\"\n}], [\"path\", {\n  d: \"M16 8v1\",\n  key: \"1q12zr\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"igpb89\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"fa4a8s\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"14c3ya\"\n}]];\nconst BoomBox = createLucideIcon(\"boom-box\", __iconNode);\nexport { __iconNode, BoomBox as default };\n//# sourceMappingURL=boom-box.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}