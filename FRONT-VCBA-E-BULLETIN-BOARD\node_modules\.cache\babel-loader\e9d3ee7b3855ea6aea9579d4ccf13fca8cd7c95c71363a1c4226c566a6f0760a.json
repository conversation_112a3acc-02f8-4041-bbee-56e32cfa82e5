{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 19-7-7 7-7\",\n  key: \"1l729n\"\n}], [\"path\", {\n  d: \"M19 12H5\",\n  key: \"x3x0zl\"\n}]];\nconst ArrowLeft = createLucideIcon(\"arrow-left\", __iconNode);\nexport { __iconNode, ArrowLeft as default };\n//# sourceMappingURL=arrow-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}