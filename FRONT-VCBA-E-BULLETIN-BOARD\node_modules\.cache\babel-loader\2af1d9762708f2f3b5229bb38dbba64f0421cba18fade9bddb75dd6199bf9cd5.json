{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 15 4-8 4 8\",\n  key: \"1vwr6u\"\n}], [\"path\", {\n  d: \"M4 13h6\",\n  key: \"1r9ots\"\n}], [\"path\", {\n  d: \"M15 11h4.5a2 2 0 0 1 0 4H15V7h4a2 2 0 0 1 0 4\",\n  key: \"1sqfas\"\n}]];\nconst CaseUpper = createLucideIcon(\"case-upper\", __iconNode);\nexport { __iconNode, CaseUpper as default };\n//# sourceMappingURL=case-upper.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}