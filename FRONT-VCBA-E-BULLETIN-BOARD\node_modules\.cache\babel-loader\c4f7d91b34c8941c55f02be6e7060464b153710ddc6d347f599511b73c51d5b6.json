{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 11h1a3 3 0 0 1 0 6h-1\",\n  key: \"1yp76v\"\n}], [\"path\", {\n  d: \"M9 12v6\",\n  key: \"1u1cab\"\n}], [\"path\", {\n  d: \"M13 12v6\",\n  key: \"1sugkk\"\n}], [\"path\", {\n  d: \"M14 7.5c-1 0-1.44.5-3 .5s-2-.5-3-.5-1.72.5-2.5.5a2.5 2.5 0 0 1 0-5c.78 0 1.57.5 2.5.5S9.44 2 11 2s2 1.5 3 1.5 1.72-.5 2.5-.5a2.5 2.5 0 0 1 0 5c-.78 0-1.5-.5-2.5-.5Z\",\n  key: \"1510fo\"\n}], [\"path\", {\n  d: \"M5 8v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V8\",\n  key: \"19jb7n\"\n}]];\nconst Beer = createLucideIcon(\"beer\", __iconNode);\nexport { __iconNode, Beer as default };\n//# sourceMappingURL=beer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}