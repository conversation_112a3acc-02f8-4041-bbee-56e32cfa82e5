{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m18 8 4 4-4 4\",\n  key: \"1ak13k\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"m6 8-4 4 4 4\",\n  key: \"15zrgr\"\n}]];\nconst MoveHorizontal = createLucideIcon(\"move-horizontal\", __iconNode);\nexport { __iconNode, MoveHorizontal as default };\n//# sourceMappingURL=move-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}