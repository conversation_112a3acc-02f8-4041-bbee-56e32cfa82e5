{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 20v-7a4 4 0 0 0-4-4H4\",\n  key: \"1nkjon\"\n}], [\"path\", {\n  d: \"M9 14 4 9l5-5\",\n  key: \"102s5s\"\n}]];\nconst CornerUpLeft = createLucideIcon(\"corner-up-left\", __iconNode);\nexport { __iconNode, CornerUpLeft as default };\n//# sourceMappingURL=corner-up-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}