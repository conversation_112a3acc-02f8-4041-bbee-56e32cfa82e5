{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Admin Protected Route Component\nconst AdminProtectedRoute = ({\n  children,\n  requiredRole,\n  redirectTo\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAdminAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: \"Checking authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ AdminProtectedRoute - User not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo || '/admin/login',\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ AdminProtectedRoute - Role mismatch:', {\n      userRole: user.role,\n      requiredRole\n    });\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ AdminProtectedRoute - Access granted');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Student Protected Route Component\n_s(AdminProtectedRoute, \"Q/Wy693qavFlaSd3QuyBDPnKMXk=\", false, function () {\n  return [useAdminAuth, useLocation];\n});\n_c = AdminProtectedRoute;\nconst StudentProtectedRoute = ({\n  children,\n  requiredRole,\n  redirectTo\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useStudentAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: \"Checking authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ StudentProtectedRoute - User not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo || '/student/login',\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ StudentProtectedRoute - Role mismatch:', {\n      userRole: user.role,\n      requiredRole\n    });\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ StudentProtectedRoute - Access granted');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Main Protected Route Component that routes to appropriate sub-component\n_s2(StudentProtectedRoute, \"n6jH2ir7ODVkWJS6EpLdBRTW75U=\", false, function () {\n  return [useStudentAuth, useLocation];\n});\n_c2 = StudentProtectedRoute;\nconst ProtectedRoute = props => {\n  _s3();\n  const location = useLocation();\n\n  // Determine which protected route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 12\n    }, this);\n  } else if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(StudentProtectedRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Fallback - redirect to appropriate login\n  const loginPath = location.pathname.startsWith('/student') ? '/student/login' : '/admin/login';\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: loginPath,\n    state: {\n      from: location\n    },\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 10\n  }, this);\n};\n_s3(ProtectedRoute, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c3 = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AdminProtectedRoute\");\n$RefreshReg$(_c2, \"StudentProtectedRoute\");\n$RefreshReg$(_c3, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdminAuth", "useStudentAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminProtectedRoute", "children", "requiredRole", "redirectTo", "_s", "isAuthenticated", "user", "isLoading", "location", "style", "display", "alignItems", "justifyContent", "minHeight", "flexDirection", "gap", "background", "width", "height", "border", "borderTop", "borderRadius", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "console", "log", "to", "state", "from", "replace", "role", "userRole", "_c", "StudentProtectedRoute", "_s2", "_c2", "ProtectedRoute", "props", "_s3", "pathname", "startsWith", "loginPath", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { UserRole } from '../../types';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: UserRole;\n  redirectTo?: string;\n}\n\n// Admin Protected Route Component\nconst AdminProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  redirectTo,\n}) => {\n  const { isAuthenticated, user, isLoading } = useAdminAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }}></div>\n        <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n          Checking authentication...\n        </p>\n      </div>\n    );\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ AdminProtectedRoute - User not authenticated, redirecting to login');\n    return <Navigate to={redirectTo || '/admin/login'} state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ AdminProtectedRoute - Role mismatch:', { userRole: user.role, requiredRole });\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  console.log('✅ AdminProtectedRoute - Access granted');\n  return <>{children}</>;\n};\n\n// Student Protected Route Component\nconst StudentProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  redirectTo,\n}) => {\n  const { isAuthenticated, user, isLoading } = useStudentAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '100vh',\n        flexDirection: 'column',\n        gap: '1rem',\n        background: '#f9fafb'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #e5e7eb',\n          borderTop: '4px solid #22c55e',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }}></div>\n        <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n          Checking authentication...\n        </p>\n      </div>\n    );\n  }\n\n  // Check if user is authenticated\n  if (!isAuthenticated || !user) {\n    console.log('❌ StudentProtectedRoute - User not authenticated, redirecting to login');\n    return <Navigate to={redirectTo || '/student/login'} state={{ from: location }} replace />;\n  }\n\n  // Check role-based access if required role is specified\n  if (requiredRole && user.role !== requiredRole) {\n    console.log('❌ StudentProtectedRoute - Role mismatch:', { userRole: user.role, requiredRole });\n    return <Navigate to=\"/student/dashboard\" replace />;\n  }\n\n  console.log('✅ StudentProtectedRoute - Access granted');\n  return <>{children}</>;\n};\n\n// Main Protected Route Component that routes to appropriate sub-component\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = (props) => {\n  const location = useLocation();\n\n  // Determine which protected route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return <AdminProtectedRoute {...props} />;\n  } else if (location.pathname.startsWith('/student')) {\n    return <StudentProtectedRoute {...props} />;\n  }\n\n  // Fallback - redirect to appropriate login\n  const loginPath = location.pathname.startsWith('/student') ? '/student/login' : '/admin/login';\n  return <Navigate to={loginPath} state={{ from: location }} replace />;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAExD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQnE;AACA,MAAMC,mBAAkD,GAAGA,CAAC;EAC1DC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGb,YAAY,CAAC,CAAC;EAC3D,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIc,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKY,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBACAJ,OAAA;QAAKY,KAAK,EAAE;UACVQ,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACT7B,OAAA;QAAGY,KAAK,EAAE;UAAEkB,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAA3B,QAAA,EAAC;MAEtD;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7BuB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;IACnF,oBAAOjC,OAAA,CAACL,QAAQ;MAACuC,EAAE,EAAE5B,UAAU,IAAI,cAAe;MAAC6B,KAAK,EAAE;QAAEC,IAAI,EAAEzB;MAAS,CAAE;MAAC0B,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1F;;EAEA;EACA,IAAIxB,YAAY,IAAII,IAAI,CAAC6B,IAAI,KAAKjC,YAAY,EAAE;IAC9C2B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MAAEM,QAAQ,EAAE9B,IAAI,CAAC6B,IAAI;MAAEjC;IAAa,CAAC,CAAC;IAC5F,oBAAOL,OAAA,CAACL,QAAQ;MAACuC,EAAE,EAAC,kBAAkB;MAACG,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;EAEAG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACrD,oBAAOjC,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAG,EAAA,CAnDMJ,mBAAkD;EAAA,QAKTN,YAAY,EACxCD,WAAW;AAAA;AAAA4C,EAAA,GANxBrC,mBAAkD;AAoDxD,MAAMsC,qBAAoD,GAAGA,CAAC;EAC5DrC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAoC,GAAA;EACJ,MAAM;IAAElC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC7D,MAAMa,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIc,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKY,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,MAAM;QACXC,UAAU,EAAE;MACd,CAAE;MAAAf,QAAA,gBACAJ,OAAA;QAAKY,KAAK,EAAE;UACVQ,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACT7B,OAAA;QAAGY,KAAK,EAAE;UAAEkB,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAW,CAAE;QAAA3B,QAAA,EAAC;MAEtD;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7BuB,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;IACrF,oBAAOjC,OAAA,CAACL,QAAQ;MAACuC,EAAE,EAAE5B,UAAU,IAAI,gBAAiB;MAAC6B,KAAK,EAAE;QAAEC,IAAI,EAAEzB;MAAS,CAAE;MAAC0B,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5F;;EAEA;EACA,IAAIxB,YAAY,IAAII,IAAI,CAAC6B,IAAI,KAAKjC,YAAY,EAAE;IAC9C2B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MAAEM,QAAQ,EAAE9B,IAAI,CAAC6B,IAAI;MAAEjC;IAAa,CAAC,CAAC;IAC9F,oBAAOL,OAAA,CAACL,QAAQ;MAACuC,EAAE,EAAC,oBAAoB;MAACG,OAAO;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEAG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACvD,oBAAOjC,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAsC,GAAA,CAnDMD,qBAAoD;EAAA,QAKX3C,cAAc,EAC1CF,WAAW;AAAA;AAAA+C,GAAA,GANxBF,qBAAoD;AAoD1D,MAAMG,cAA6C,GAAIC,KAAK,IAAK;EAAAC,GAAA;EAC/D,MAAMnC,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIe,QAAQ,CAACoC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;IAC1C,oBAAOhD,OAAA,CAACG,mBAAmB;MAAA,GAAK0C;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC3C,CAAC,MAAM,IAAIlB,QAAQ,CAACoC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACnD,oBAAOhD,OAAA,CAACyC,qBAAqB;MAAA,GAAKI;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;;EAEA;EACA,MAAMoB,SAAS,GAAGtC,QAAQ,CAACoC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,GAAG,gBAAgB,GAAG,cAAc;EAC9F,oBAAOhD,OAAA,CAACL,QAAQ;IAACuC,EAAE,EAAEe,SAAU;IAACd,KAAK,EAAE;MAAEC,IAAI,EAAEzB;IAAS,CAAE;IAAC0B,OAAO;EAAA;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACvE,CAAC;AAACiB,GAAA,CAbIF,cAA6C;EAAA,QAChChD,WAAW;AAAA;AAAAsD,GAAA,GADxBN,cAA6C;AAenD,eAAeA,cAAc;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}