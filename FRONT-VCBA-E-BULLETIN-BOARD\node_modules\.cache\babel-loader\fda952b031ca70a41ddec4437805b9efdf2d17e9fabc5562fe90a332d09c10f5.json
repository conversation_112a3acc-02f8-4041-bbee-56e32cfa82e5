{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M9 15h6\",\n  key: \"cctwl0\"\n}], [\"path\", {\n  d: \"M12 18v-6\",\n  key: \"17g6i2\"\n}]];\nconst FilePlus = createLucideIcon(\"file-plus\", __iconNode);\nexport { __iconNode, FilePlus as default };\n//# sourceMappingURL=file-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}