{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 12A10 10 0 1 1 12 2\",\n  key: \"1fm58d\"\n}], [\"path\", {\n  d: \"M22 2 12 12\",\n  key: \"yg2myt\"\n}], [\"path\", {\n  d: \"M16 2h6v6\",\n  key: \"zan5cs\"\n}]];\nconst CircleArrowOutUpRight = createLucideIcon(\"circle-arrow-out-up-right\", __iconNode);\nexport { __iconNode, CircleArrowOutUpRight as default };\n//# sourceMappingURL=circle-arrow-out-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}