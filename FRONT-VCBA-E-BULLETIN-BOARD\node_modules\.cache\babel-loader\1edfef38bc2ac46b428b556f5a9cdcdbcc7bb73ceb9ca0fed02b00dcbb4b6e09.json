{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"path\", {\n  d: \"M17 13v-3h4\",\n  key: \"1nvgqp\"\n}], [\"path\", {\n  d: \"M17 17.7c.4.2.8.3 1.3.3 1.5 0 2.7-1.1 2.7-2.5S19.8 13 18.3 13H17\",\n  key: \"2nebdn\"\n}]];\nconst Heading5 = createLucideIcon(\"heading-5\", __iconNode);\nexport { __iconNode, Heading5 as default };\n//# sourceMappingURL=heading-5.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}