{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 11.5V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1.4\",\n  key: \"edstyy\"\n}], [\"path\", {\n  d: \"M14 10V8a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2\",\n  key: \"19wdwo\"\n}], [\"path\", {\n  d: \"M10 9.9V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v5\",\n  key: \"1lugqo\"\n}], [\"path\", {\n  d: \"M6 14a2 2 0 0 0-2-2a2 2 0 0 0-2 2\",\n  key: \"1hbeus\"\n}], [\"path\", {\n  d: \"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-4a8 8 0 0 1-8-8 2 2 0 1 1 4 0\",\n  key: \"1etffm\"\n}]];\nconst Grab = createLucideIcon(\"grab\", __iconNode);\nexport { __iconNode, Grab as default };\n//# sourceMappingURL=grab.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}