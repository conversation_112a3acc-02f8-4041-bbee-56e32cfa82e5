{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"20\",\n  y2: \"10\",\n  key: \"1xfpm4\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"20\",\n  y2: \"4\",\n  key: \"be30l9\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"20\",\n  y2: \"14\",\n  key: \"1r4le6\"\n}]];\nconst ChartNoAxesColumn = createLucideIcon(\"chart-no-axes-column\", __iconNode);\nexport { __iconNode, ChartNoAxesColumn as default };\n//# sourceMappingURL=chart-no-axes-column.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}