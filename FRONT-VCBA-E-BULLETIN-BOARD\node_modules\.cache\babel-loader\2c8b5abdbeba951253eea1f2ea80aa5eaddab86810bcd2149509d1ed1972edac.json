{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 15v-3.014\",\n  key: \"aw6ppf\"\n}], [\"path\", {\n  d: \"M16 15v-3.014\",\n  key: \"9e0vc7\"\n}], [\"path\", {\n  d: \"M20 6H4\",\n  key: \"1lfz86\"\n}], [\"path\", {\n  d: \"M20 8V4\",\n  key: \"1l2g47\"\n}], [\"path\", {\n  d: \"M4 8V4\",\n  key: \"sppxzt\"\n}], [\"path\", {\n  d: \"M8 15v-3.014\",\n  key: \"when08\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"12\",\n  width: \"18\",\n  height: \"7\",\n  rx: \"1\",\n  key: \"1ucwdz\"\n}]];\nconst RulerDimensionLine = createLucideIcon(\"ruler-dimension-line\", __iconNode);\nexport { __iconNode, RulerDimensionLine as default };\n//# sourceMappingURL=ruler-dimension-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}