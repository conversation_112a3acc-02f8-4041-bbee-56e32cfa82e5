{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 9-8.414 8.414A2 2 0 0 0 3 18.828v1.344a2 2 0 0 1-.586 1.414A2 2 0 0 1 3.828 21h1.344a2 2 0 0 0 1.414-.586L15 12\",\n  key: \"1y3wsu\"\n}], [\"path\", {\n  d: \"m18 9 .4.4a1 1 0 1 1-3 3l-3.8-3.8a1 1 0 1 1 3-3l.4.4 3.4-3.4a1 1 0 1 1 3 3z\",\n  key: \"110lr1\"\n}], [\"path\", {\n  d: \"m2 22 .414-.414\",\n  key: \"jhxm08\"\n}]];\nconst Pipette = createLucideIcon(\"pipette\", __iconNode);\nexport { __iconNode, Pipette as default };\n//# sourceMappingURL=pipette.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}