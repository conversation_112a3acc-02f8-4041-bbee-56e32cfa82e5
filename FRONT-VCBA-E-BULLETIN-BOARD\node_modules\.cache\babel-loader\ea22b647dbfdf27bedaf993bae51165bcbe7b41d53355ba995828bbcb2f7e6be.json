{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 14v2.2l1.6 1\",\n  key: \"fo4ql5\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5\",\n  key: \"1osxxc\"\n}], [\"path\", {\n  d: \"M3 10h5\",\n  key: \"r794hk\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"qoo3c4\"\n}]];\nconst CalendarClock = createLucideIcon(\"calendar-clock\", __iconNode);\nexport { __iconNode, CalendarClock as default };\n//# sourceMappingURL=calendar-clock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}