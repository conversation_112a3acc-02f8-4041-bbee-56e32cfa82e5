{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20V10\",\n  key: \"g8npz5\"\n}], [\"path\", {\n  d: \"M18 20v-4\",\n  key: \"8uic4z\"\n}], [\"path\", {\n  d: \"M6 20V4\",\n  key: \"1w1bmo\"\n}]];\nconst ChartNoAxesColumnDecreasing = createLucideIcon(\"chart-no-axes-column-decreasing\", __iconNode);\nexport { __iconNode, ChartNoAxesColumnDecreasing as default };\n//# sourceMappingURL=chart-no-axes-column-decreasing.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}