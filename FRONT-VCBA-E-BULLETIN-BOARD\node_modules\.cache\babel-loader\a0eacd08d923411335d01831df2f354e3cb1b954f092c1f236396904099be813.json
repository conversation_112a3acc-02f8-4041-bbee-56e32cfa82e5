{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v1\",\n  key: \"jmtmu2\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"2\",\n  y: \"13\",\n  rx: \"1\",\n  key: \"10y5wo\"\n}], [\"path\", {\n  d: \"M8 13v-2a2 2 0 1 0-4 0v2\",\n  key: \"1pdxzg\"\n}]];\nconst FileLock2 = createLucideIcon(\"file-lock-2\", __iconNode);\nexport { __iconNode, FileLock2 as default };\n//# sourceMappingURL=file-lock-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}