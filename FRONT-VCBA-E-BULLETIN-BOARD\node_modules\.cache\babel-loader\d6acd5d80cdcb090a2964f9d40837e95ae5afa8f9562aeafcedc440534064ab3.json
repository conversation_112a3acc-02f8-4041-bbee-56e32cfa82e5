{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M17 20v2\",\n  key: \"1rnc9c\"\n}], [\"path\", {\n  d: \"M17 2v2\",\n  key: \"11trls\"\n}], [\"path\", {\n  d: \"M2 12h2\",\n  key: \"1t8f8n\"\n}], [\"path\", {\n  d: \"M2 17h2\",\n  key: \"7oei6x\"\n}], [\"path\", {\n  d: \"M2 7h2\",\n  key: \"asdhe0\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}], [\"path\", {\n  d: \"M20 17h2\",\n  key: \"1fpfkl\"\n}], [\"path\", {\n  d: \"M20 7h2\",\n  key: \"1o8tra\"\n}], [\"path\", {\n  d: \"M7 20v2\",\n  key: \"4gnj0m\"\n}], [\"path\", {\n  d: \"M7 2v2\",\n  key: \"1i4yhu\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"4\",\n  width: \"16\",\n  height: \"16\",\n  rx: \"2\",\n  key: \"1vbyd7\"\n}], [\"rect\", {\n  x: \"8\",\n  y: \"8\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"z9xiuo\"\n}]];\nconst Cpu = createLucideIcon(\"cpu\", __iconNode);\nexport { __iconNode, Cpu as default };\n//# sourceMappingURL=cpu.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}