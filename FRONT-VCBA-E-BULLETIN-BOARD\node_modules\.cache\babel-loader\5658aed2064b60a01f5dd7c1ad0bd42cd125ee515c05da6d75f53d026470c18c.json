{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m4.93 4.93 4.24 4.24\",\n  key: \"1ymg45\"\n}], [\"path\", {\n  d: \"m14.83 9.17 4.24-4.24\",\n  key: \"1cb5xl\"\n}], [\"path\", {\n  d: \"m14.83 14.83 4.24 4.24\",\n  key: \"q42g0n\"\n}], [\"path\", {\n  d: \"m9.17 14.83-4.24 4.24\",\n  key: \"bqpfvv\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}]];\nconst LifeBuoy = createLucideIcon(\"life-buoy\", __iconNode);\nexport { __iconNode, LifeBuoy as default };\n//# sourceMappingURL=life-buoy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}