{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useAnnouncements,useCategories}from'../../hooks/useAnnouncements';import{useToast}from'../../contexts/ToastContext';import{useAdminAuth}from'../../contexts/AdminAuthContext';import AnnouncementModal from'../../components/admin/modals/AnnouncementModal';import AnnouncementViewDialog from'../../components/admin/modals/AnnouncementViewDialog';import FacebookImageGallery from'../../components/common/FacebookImageGallery';import{CheckCircle,Clock,Calendar,X,MessageSquare,Search,RefreshCw,Pin,AlertTriangle,User,Eye,Heart,MessageCircle,Edit,Send,Trash2}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PostManagement=()=>{const{user}=useAdminAuth();const[searchTerm,setSearchTerm]=useState('');const[debouncedSearchTerm,setDebouncedSearchTerm]=useState('');const[selectedCategoryId,setSelectedCategoryId]=useState(undefined);const[selectedStatus,setSelectedStatus]=useState('');const[showModal,setShowModal]=useState(false);const[editingAnnouncement,setEditingAnnouncement]=useState(null);const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');// Toast notifications\nconst{showSuccess,showError}=useToast();// View dialog state\nconst[isViewDialogOpen,setIsViewDialogOpen]=useState(false);const[viewingAnnouncement,setViewingAnnouncement]=useState(null);// Initialize filters with grade level filtering for grade-specific admins\nconst[filters,setFilters]=useState(()=>{const baseFilters={page:1,limit:10,sort_by:'created_at',sort_order:'DESC'};// Add grade level filter for grade-specific admins\nif(user!==null&&user!==void 0&&user.grade_level){baseFilters.grade_level=user.grade_level;console.log('🎯 PostManagement - Initial grade level filter set:',{userGradeLevel:user.grade_level,userEmail:user.email,userRole:user.role});}else{console.log('🌐 PostManagement - No grade level filter (system admin):',{userGradeLevel:user===null||user===void 0?void 0:user.grade_level,userEmail:user===null||user===void 0?void 0:user.email,userRole:user===null||user===void 0?void 0:user.role});}return baseFilters;});const{announcements,loading,error,pagination,refresh,createAnnouncement,updateAnnouncement,deleteAnnouncement,publishAnnouncement,unpublishAnnouncement,setFilters:updateFilters}=useAnnouncements(filters,true);// Use admin service\nconst{categories}=useCategories();// Handle category selection\nconst handleCategoryChange=categoryId=>{setSelectedCategoryId(categoryId||undefined);};// Clear all filters\nconst clearFilters=()=>{setSearchTerm('');setDebouncedSearchTerm('');setSelectedCategoryId(undefined);setSelectedStatus('');};// Get current filter summary\nconst getFilterSummary=()=>{const filters=[];if(debouncedSearchTerm){filters.push(\"Search: \\\"\".concat(debouncedSearchTerm,\"\\\"\"));}if(selectedCategoryId){const category=categories.find(cat=>cat.category_id===selectedCategoryId);if(category){filters.push(\"Category: \".concat(category.name));}}if(selectedStatus){filters.push(\"Status: \".concat(selectedStatus.charAt(0).toUpperCase()+selectedStatus.slice(1)));}return filters;};// Debounced search - update debounced term after delay\nuseEffect(()=>{const timeoutId=setTimeout(()=>{setDebouncedSearchTerm(searchTerm);},500);return()=>clearTimeout(timeoutId);},[searchTerm]);// Update filters when debounced search term or other filter values change\nuseEffect(()=>{const newFilters=_objectSpread(_objectSpread({},filters),{},{page:1,// Reset to first page when filtering\nsearch:debouncedSearchTerm||undefined,category_id:selectedCategoryId,status:selectedStatus||undefined});// Ensure grade level filter is preserved for grade-specific admins\nif(user!==null&&user!==void 0&&user.grade_level){newFilters.grade_level=user.grade_level;}console.log('🔍 PostManagement - Updating filters:',{userGradeLevel:user===null||user===void 0?void 0:user.grade_level,appliedGradeLevel:newFilters.grade_level,allFilters:newFilters});updateFilters(newFilters);},[debouncedSearchTerm,selectedCategoryId,selectedStatus,user===null||user===void 0?void 0:user.grade_level]);// Clear messages after 5 seconds\nuseEffect(()=>{if(successMessage||errorMessage){const timer=setTimeout(()=>{setSuccessMessage('');setErrorMessage('');},5000);return()=>clearTimeout(timer);}},[successMessage,errorMessage]);const handleCreateAnnouncement=()=>{console.log('🆕 Creating new announcement - clearing all data');// Ensure editing announcement is completely cleared\nsetEditingAnnouncement(null);// Clear any existing success/error messages\nsetSuccessMessage('');setErrorMessage('');// Small delay to ensure state is cleared before opening modal\nsetTimeout(()=>{setShowModal(true);console.log('✅ Modal opened for new announcement creation');},10);};const handleEditAnnouncement=announcement=>{console.log('✏️ Editing announcement:',{id:announcement.announcement_id,title:announcement.title,is_pinned:announcement.is_pinned,is_alert:announcement.is_alert,allow_comments:announcement.allow_comments,allow_sharing:announcement.allow_sharing});setEditingAnnouncement(announcement);setShowModal(true);};const handleDeleteAnnouncement=async announcementId=>{const announcement=announcements.find(a=>a.announcement_id===announcementId);const confirmMessage=\"Are you sure you want to delete \\\"\".concat(announcement===null||announcement===void 0?void 0:announcement.title,\"\\\"? This action can be undone from the admin panel.\");if(window.confirm(confirmMessage)){try{await deleteAnnouncement(announcementId);showSuccess('Announcement Deleted',\"\\\"\".concat(announcement===null||announcement===void 0?void 0:announcement.title,\"\\\" has been moved to trash successfully.\"),3000);}catch(error){const errorMsg=error.message||'Failed to delete announcement';setErrorMessage(errorMsg);showError('Delete Failed',errorMsg,4000);}}};const handlePublishAnnouncement=async announcementId=>{try{await publishAnnouncement(announcementId);const announcement=announcements.find(a=>a.announcement_id===announcementId);showSuccess('Announcement Published',\"\\\"\".concat(announcement===null||announcement===void 0?void 0:announcement.title,\"\\\" is now live and visible to all users.\"),3000);}catch(error){const errorMsg=error.message||'Failed to publish announcement';setErrorMessage(errorMsg);showError('Publish Failed',errorMsg,4000);}};const handleUnpublishAnnouncement=async announcementId=>{try{await unpublishAnnouncement(announcementId);const announcement=announcements.find(a=>a.announcement_id===announcementId);showSuccess('Announcement Unpublished',\"\\\"\".concat(announcement===null||announcement===void 0?void 0:announcement.title,\"\\\" has been moved back to draft status.\"),3000);}catch(error){const errorMsg=error.message||'Failed to unpublish announcement';setErrorMessage(errorMsg);showError('Unpublish Failed',errorMsg,4000);}};const handleViewAnnouncement=announcementId=>{const announcement=announcements.find(a=>a.announcement_id===announcementId);if(announcement){setViewingAnnouncement(announcement);setIsViewDialogOpen(true);}};const handleCloseViewDialog=()=>{setIsViewDialogOpen(false);setViewingAnnouncement(null);};const handleSaveAnnouncement=async(data,applyPendingDeletes,onComplete)=>{try{const isEditing=!!editingAnnouncement;if(isEditing){await updateAnnouncement(editingAnnouncement.announcement_id,data);// Apply pending image deletions AFTER successful update\nif(applyPendingDeletes){console.log('🗑️ Applying pending image deletions after successful update');await applyPendingDeletes();}}else{// For new announcements, automatically set grade level for grade-specific admins\nlet announcementData=data;if(user!==null&&user!==void 0&&user.grade_level&&!(data instanceof FormData)){// If admin has a specific grade level and data is not FormData, set grade_level\nannouncementData=_objectSpread(_objectSpread({},data),{},{grade_level:user.grade_level});}else if(user!==null&&user!==void 0&&user.grade_level&&data instanceof FormData){// If data is FormData, append grade_level\ndata.append('grade_level',user.grade_level.toString());}await createAnnouncement(announcementData);}// Execute completion callback (image uploads, etc.) BEFORE refreshing\nif(onComplete){await onComplete();}// Explicitly refresh the list to ensure new/updated announcements appear immediately\nconsole.log('🔄 Refreshing announcements list after save...');await refresh();console.log('✅ Announcements list refreshed');// Clear any existing error messages\nsetErrorMessage('');// Close modal first\nsetShowModal(false);setEditingAnnouncement(null);// Then show success message after a brief delay to ensure modal is closed\nsetTimeout(()=>{if(isEditing){alert('✅ Announcement updated successfully! Changes are now visible.');}else{alert('✅ Announcement created successfully!');}},100);}catch(error){const errorMsg=error.message||'Failed to save announcement';setErrorMessage(errorMsg);showError('Save Failed',errorMsg,5000);throw error;// Re-throw to let modal handle it\n}finally{// Always clean up modal state\nsetShowModal(false);setEditingAnnouncement(null);}};const handleCloseModal=()=>{setShowModal(false);setEditingAnnouncement(null);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'});};// Get category color\nconst getCategoryColor=announcement=>{// Find the category color from the categories list\nif(announcement.category_id&&categories&&categories.length>0){const category=categories.find(cat=>cat.category_id===announcement.category_id);if(category&&category.color_code){return category.color_code;}}// Fallback to announcement's category_color if available\nif(announcement.category_color){return announcement.category_color;}// Default fallback color\nreturn'#22c55e';};const getStatusBadge=status=>{const statusConfig={published:{color:'bg-green-100 text-green-800',icon:CheckCircle},draft:{color:'bg-yellow-100 text-yellow-800',icon:Clock},scheduled:{color:'bg-blue-100 text-blue-800',icon:Calendar},archived:{color:'bg-gray-100 text-gray-800',icon:X}};const config=statusConfig[status]||statusConfig.draft;const IconComponent=config.icon;return/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(config.color),children:[/*#__PURE__*/_jsx(IconComponent,{size:12,className:\"mr-1\"}),status.charAt(0).toUpperCase()+status.slice(1)]});};const handlePageChange=page=>{updateFilters(_objectSpread(_objectSpread({},filters),{},{page}));};const handleItemsPerPageChange=limit=>{updateFilters(_objectSpread(_objectSpread({},filters),{},{limit,page:1}));// Reset to first page when changing items per page\n};// Enhanced pagination component - Always visible\nconst EnhancedPagination=()=>{// Always show pagination if we have pagination data, even for single page\nif(!pagination)return null;const getPageNumbers=()=>{const pages=[];const maxVisiblePages=5;const currentPage=pagination.page;const totalPages=Math.max(pagination.totalPages,1);// Ensure at least 1 page\nif(totalPages<=maxVisiblePages){for(let i=1;i<=totalPages;i++){pages.push(i);}}else{if(currentPage<=3){pages.push(1,2,3,4,'...',totalPages);}else if(currentPage>=totalPages-2){pages.push(1,'...',totalPages-3,totalPages-2,totalPages-1,totalPages);}else{pages.push(1,'...',currentPage-1,currentPage,currentPage+1,'...',totalPages);}}return pages;};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginTop:'2rem',marginBottom:'2rem',padding:'1.5rem',backgroundColor:'white',borderRadius:'12px',border:'1px solid #e5e7eb',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.05)',flexWrap:'wrap',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:\"Show:\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.limit,onChange:e=>handleItemsPerPageChange(Number(e.target.value)),style:{padding:'0.25rem 0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',fontSize:'0.875rem',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:10,children:\"10\"}),/*#__PURE__*/_jsx(\"option\",{value:20,children:\"20\"}),/*#__PURE__*/_jsx(\"option\",{value:50,children:\"50\"}),/*#__PURE__*/_jsx(\"option\",{value:100,children:\"100\"})]}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:\"per page\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280'},children:[\"Showing \",(pagination.page-1)*filters.limit+1,\"-\",Math.min(pagination.page*filters.limit,pagination.total),\" of \",pagination.total,\" posts\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.25rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(1),disabled:pagination.page===1,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:pagination.page===1?'#f3f4f6':'white',color:pagination.page===1?'#9ca3af':'#374151',cursor:pagination.page===1?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"First\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(pagination.page-1),disabled:!pagination.hasPrev,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:!pagination.hasPrev?'#f3f4f6':'white',color:!pagination.hasPrev?'#9ca3af':'#374151',cursor:!pagination.hasPrev?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Previous\"}),getPageNumbers().map((page,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>typeof page==='number'&&handlePageChange(page),disabled:page==='...',style:{padding:'0.5rem 0.75rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:page===pagination.page?'#3b82f6':page==='...'?'transparent':'white',color:page===pagination.page?'white':page==='...'?'#9ca3af':'#374151',cursor:page==='...'?'default':'pointer',fontSize:'0.875rem',fontWeight:page===pagination.page?'600':'400'},children:page},index)),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(pagination.page+1),disabled:!pagination.hasNext,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:!pagination.hasNext?'#f3f4f6':'white',color:!pagination.hasNext?'#9ca3af':'#374151',cursor:!pagination.hasNext?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Next\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(pagination.totalPages),disabled:pagination.page===pagination.totalPages,style:{padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'4px',backgroundColor:pagination.page===pagination.totalPages?'#f3f4f6':'white',color:pagination.page===pagination.totalPages?'#9ca3af':'#374151',cursor:pagination.page===pagination.totalPages?'not-allowed':'pointer',fontSize:'0.875rem'},children:\"Last\"})]})]});};if(loading&&(!announcements||announcements.length===0)){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{style:{maxWidth:'1200px',margin:'0 auto'},children:[successMessage&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',backgroundColor:'#f0fdf4',border:'1px solid #bbf7d0',color:'#166534',borderRadius:'8px'},children:successMessage}),errorMessage&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',color:'#dc2626',borderRadius:'8px'},children:errorMessage}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontSize:'2rem',fontWeight:'700',color:'#2d5016',margin:'0 0 0.5rem 0'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(MessageSquare,{size:20,color:\"#1e40af\"}),\"Post Management\"]})}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'1.1rem'},children:[\"Create, edit, and manage announcement posts with advanced filtering\",(user===null||user===void 0?void 0:user.grade_level)&&/*#__PURE__*/_jsxs(\"span\",{style:{display:'inline-block',marginLeft:'0.5rem',padding:'0.25rem 0.5rem',backgroundColor:'#dbeafe',color:'#1e40af',borderRadius:'0.375rem',fontSize:'0.875rem',fontWeight:'500'},children:[\"Grade \",user.grade_level,\" Only\"]})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCreateAnnouncement,style:{display:'inline-flex',alignItems:'center',padding:'0.75rem 1.5rem',background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'600',fontSize:'0.95rem',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';},onMouseOut:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:\"+ Create Announcement\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',padding:'1.5rem',marginBottom:'1.5rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Search\"}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(Search,{size:16,color:\"#9ca3af\",style:{position:'absolute',left:'0.75rem',top:'50%',transform:'translateY(-50%)',pointerEvents:'none'}}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search announcements...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{width:'100%',padding:'0.75rem 0.75rem 0.75rem 2.5rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease, box-shadow 0.2s ease'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';}})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Category Filter\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCategoryId||'',onChange:e=>handleCategoryChange(e.target.value?parseInt(e.target.value):null),style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white',transition:'border-color 0.2s ease, box-shadow 0.2s ease'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),categories&&categories.length>0?categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.category_id,children:category.name},category.category_id)):/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"Loading categories...\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedStatus,onChange:e=>setSelectedStatus(e.target.value),style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white',transition:'border-color 0.2s ease, box-shadow 0.2s ease'},onFocus:e=>{e.currentTarget.style.borderColor='#22c55e';e.currentTarget.style.boxShadow='0 0 0 3px rgba(34, 197, 94, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"published\",children:\"Published\"}),/*#__PURE__*/_jsx(\"option\",{value:\"draft\",children:\"Draft\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'end',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:refresh,style:{flex:1,padding:'0.75rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'background-color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.backgroundColor='#e5e7eb',onMouseOut:e=>e.currentTarget.style.backgroundColor='#f3f4f6',children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',justifyContent:'center'},children:[/*#__PURE__*/_jsx(RefreshCw,{size:16}),\"Refresh\"]})}),/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,style:{flex:1,padding:'0.75rem',backgroundColor:'#fef3c7',color:'#92400e',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem',transition:'background-color 0.2s ease'},onMouseOver:e=>e.currentTarget.style.backgroundColor='#fde68a',onMouseOut:e=>e.currentTarget.style.backgroundColor='#fef3c7',children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',justifyContent:'center'},children:[/*#__PURE__*/_jsx(X,{size:16}),\"Clear\"]})})]})]})}),getFilterSummary().length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f0f9ff',border:'1px solid #bae6fd',borderRadius:'12px',padding:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(Search,{size:16,style:{color:'#0369a1'}}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#0369a1'},children:\"Active Filters:\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexWrap:'wrap',gap:'0.5rem'},children:getFilterSummary().map((filter,index)=>/*#__PURE__*/_jsx(\"span\",{style:{background:'#dbeafe',color:'#1e40af',padding:'0.25rem 0.75rem',borderRadius:'20px',fontSize:'0.75rem',fontWeight:'500'},children:filter},index))})]}),/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:loading?/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',padding:'3rem 0'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'2rem',height:'2rem',border:'2px solid #e5e7eb',borderTop:'2px solid #22c55e',borderRadius:'50%',animation:'spin 1s linear infinite'}})}):announcements.length===0?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'3rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'3rem',color:'#9ca3af',marginBottom:'1rem'},children:/*#__PURE__*/_jsx(MessageSquare,{size:20,color:\"#1e40af\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0.5rem 0',fontSize:'0.875rem',fontWeight:'500',color:'#111827'},children:\"No announcements\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0.25rem 0 1.5rem 0',fontSize:'0.875rem',color:'#6b7280'},children:\"Get started by creating a new announcement.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCreateAnnouncement,style:{display:'inline-flex',alignItems:'center',padding:'0.75rem 1.5rem',background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'600',fontSize:'0.95rem',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.transform='translateY(-1px)';e.currentTarget.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)';},onMouseOut:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},children:\"+ Create Announcement\"})]}):/*#__PURE__*/_jsx(\"div\",{style:{padding:'1rem',display:'flex',flexDirection:'column',gap:'1rem'},children:announcements&&announcements.length>0?announcements.map(announcement=>/*#__PURE__*/_jsx(\"div\",{style:{padding:'1.5rem',background:'#ffffff',border:'1px solid #e5e7eb',borderRadius:'12px',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.04)',transition:'all 0.2s ease'},onMouseOver:e=>{e.currentTarget.style.backgroundColor='#f9fafb';e.currentTarget.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.08)';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.backgroundColor='#ffffff';e.currentTarget.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.04)';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'start',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'1.125rem',fontWeight:'600',color:'#111827',margin:0,overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap'},children:announcement.title}),announcement.category_name&&/*#__PURE__*/_jsx(\"span\",{style:{background:getCategoryColor(announcement),color:'white',padding:'0.25rem 0.5rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600'},children:announcement.category_name}),announcement.is_pinned&&/*#__PURE__*/_jsx(Pin,{size:16,color:\"#eab308\"}),announcement.is_alert&&/*#__PURE__*/_jsx(AlertTriangle,{size:16,color:\"#ef4444\"}),getStatusBadge(announcement.status)]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.75rem',lineHeight:'1.5',display:'-webkit-box',WebkitLineClamp:2,WebkitBoxOrient:'vertical',overflow:'hidden'},children:announcement.content}),(announcement.attachments&&announcement.attachments.length>0||announcement.images&&announcement.images.length>0||announcement.image_url||announcement.image_path)&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.75rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:// Use attachments/images array if available, otherwise fallback to single image\nannouncement.attachments&&announcement.attachments.length>0?announcement.attachments.map(att=>att.file_path):announcement.images&&announcement.images.length>0?announcement.images.map(img=>img.file_path):[announcement.image_url||announcement.image_path].filter(Boolean),altPrefix:announcement.title,maxVisible:3})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280',flexWrap:'wrap'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(User,{size:14,style:{marginRight:'0.25rem'}}),announcement.author_name||'Unknown']}),/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Calendar,{size:14,style:{marginRight:'0.25rem'}}),formatDate(announcement.created_at)]}),/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Heart,{size:14,style:{marginRight:'0.25rem'}}),announcement.reaction_count||0,\" reactions\"]}),/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:14,style:{marginRight:'0.25rem'}}),announcement.comment_count||0,\" comments\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginLeft:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleViewAnnouncement(announcement.announcement_id),style:{padding:'0.5rem',color:'#ffffff',background:'#6b7280',border:'none',cursor:'pointer',borderRadius:'6px',transition:'all 0.2s ease'},title:\"View\",onMouseOver:e=>{e.currentTarget.style.background='#4b5563';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.background='#6b7280';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsx(Eye,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditAnnouncement(announcement),style:{padding:'0.5rem',color:'#ffffff',background:'#3b82f6',border:'none',cursor:'pointer',borderRadius:'6px',transition:'all 0.2s ease'},title:\"Edit\",onMouseOver:e=>{e.currentTarget.style.background='#2563eb';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.background='#3b82f6';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsx(Edit,{size:16})}),announcement.status==='draft'&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePublishAnnouncement(announcement.announcement_id),style:{padding:'0.5rem',color:'#ffffff',background:'#22c55e',border:'none',cursor:'pointer',borderRadius:'6px',transition:'all 0.2s ease'},title:\"Publish\",onMouseOver:e=>{e.currentTarget.style.background='#16a34a';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.background='#22c55e';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsx(Send,{size:16})}),announcement.status==='published'&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleUnpublishAnnouncement(announcement.announcement_id),style:{padding:'0.5rem',color:'#ffffff',background:'#eab308',border:'none',cursor:'pointer',borderRadius:'6px',transition:'all 0.2s ease'},title:\"Unpublish\",onMouseOver:e=>{e.currentTarget.style.background='#ca8a04';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.background='#eab308';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsx(Clock,{size:16})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteAnnouncement(announcement.announcement_id),style:{padding:'0.5rem',color:'#ffffff',background:'#ef4444',border:'none',cursor:'pointer',borderRadius:'6px',transition:'all 0.2s ease'},title:\"Delete\",onMouseOver:e=>{e.currentTarget.style.background='#dc2626';e.currentTarget.style.transform='translateY(-1px)';},onMouseOut:e=>{e.currentTarget.style.background='#ef4444';e.currentTarget.style.transform='translateY(0)';},children:/*#__PURE__*/_jsx(Trash2,{size:16})})]})]})},announcement.announcement_id)):/*#__PURE__*/_jsx(\"div\",{style:{padding:'2rem',textAlign:'center',color:'#6b7280'},children:loading?'Loading announcements...':'No announcements found.'})})}),/*#__PURE__*/_jsx(EnhancedPagination,{}),/*#__PURE__*/_jsx(AnnouncementModal,{isOpen:showModal,onClose:handleCloseModal,onSave:handleSaveAnnouncement,announcement:editingAnnouncement,loading:loading}),/*#__PURE__*/_jsx(AnnouncementViewDialog,{isOpen:isViewDialogOpen,onClose:handleCloseViewDialog,announcement:viewingAnnouncement})]});};export default PostManagement;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}