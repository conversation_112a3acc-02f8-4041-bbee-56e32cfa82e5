{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user,\n    checkAuthStatus\n  } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  // Password change state\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [passwordErrors, setPasswordErrors] = useState([]);\n  const [passwordSuccess, setPasswordSuccess] = useState(null);\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile Settings',\n    icon: User\n  }, {\n    key: 'system',\n    label: 'System Settings',\n    icon: SettingsIcon\n  }, {\n    key: 'security',\n    label: 'Security',\n    icon: Lock\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }];\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async file => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  // Password validation\n  const validatePassword = password => {\n    const errors = [];\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n    if (!/(?=.*[a-z])/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/(?=.*[A-Z])/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/(?=.*\\d)/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!/(?=.*[@$!%*?&])/.test(password)) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n    return errors;\n  };\n\n  // Handle password input change\n  const handlePasswordChange = (field, value) => {\n    setPasswordData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = field => {\n    setShowPasswords(prev => ({\n      ...prev,\n      [field]: !prev[field]\n    }));\n  };\n\n  // Handle password change submission\n  const handlePasswordSubmit = async e => {\n    e.preventDefault();\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n\n    // Validation\n    const errors = [];\n    if (!passwordData.currentPassword) {\n      errors.push('Current password is required');\n    }\n    if (!passwordData.newPassword) {\n      errors.push('New password is required');\n    } else {\n      const passwordValidationErrors = validatePassword(passwordData.newPassword);\n      errors.push(...passwordValidationErrors);\n    }\n    if (!passwordData.confirmPassword) {\n      errors.push('Password confirmation is required');\n    } else if (passwordData.newPassword !== passwordData.confirmPassword) {\n      errors.push('New password and confirmation do not match');\n    }\n    if (passwordData.currentPassword === passwordData.newPassword) {\n      errors.push('New password must be different from current password');\n    }\n    if (errors.length > 0) {\n      setPasswordErrors(errors);\n      return;\n    }\n    setIsChangingPassword(true);\n    try {\n      // Call API to change password\n      await AdminAuthService.changePassword({\n        currentPassword: passwordData.currentPassword,\n        newPassword: passwordData.newPassword\n      });\n      setPasswordSuccess('Password changed successfully!');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n\n      // Clear success message after 5 seconds\n      setTimeout(() => setPasswordSuccess(null), 5000);\n    } catch (error) {\n      setPasswordErrors([error.message || 'Failed to change password']);\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-layout\",\n          style: {\n            display: 'flex',\n            gap: '2rem',\n            alignItems: 'flex-start',\n            marginBottom: '2rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n              currentPicture: user !== null && user !== void 0 && user.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined,\n              userInitials: `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`,\n              onUpload: handleProfilePictureUpload,\n              onRemove: handleProfilePictureRemove,\n              isLoading: isUploadingPicture,\n              size: 140\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-details\",\n            style: {\n              flex: 1,\n              minWidth: '300px',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem',\n              paddingTop: '0.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#111827',\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  marginBottom: '0.5rem'\n                },\n                children: [`${(user === null || user === void 0 ? void 0 : user.firstName) || ''} ${(user === null || user === void 0 ? void 0 : user.lastName) || ''}`, (user === null || user === void 0 ? void 0 : user.suffix) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: '400',\n                    color: '#6b7280'\n                  },\n                  children: [\" \", user.suffix]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 34\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (user === null || user === void 0 ? void 0 : user.email) || 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#2d5016',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '2rem',\n            background: '#f9fafb',\n            borderRadius: '12px',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Lock, {\n              size: 48,\n              color: \"#6b7280\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#374151',\n              fontSize: '1.125rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Password Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280',\n              marginBottom: '1.5rem',\n              lineHeight: '1.5'\n            },\n            children: \"Password change functionality will be available soon. For now, please contact your system administrator if you need to update your password.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            disabled: true,\n            style: {\n              background: '#e8f5e8',\n              color: '#9ca3af',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'not-allowed',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              margin: '0 auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 13\n            }, this), \"Change Password (Coming Soon)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 5\n    }, this);\n  };\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"General Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Maintenance Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Enable maintenance mode to restrict access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Auto-approve Posts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Automatically approve new posts without review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.25rem'\n              },\n              children: \"Email Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: \"Send email notifications for important events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              position: 'relative',\n              display: 'inline-block',\n              width: '60px',\n              height: '34px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              defaultChecked: true,\n              style: {\n                opacity: 0,\n                width: 0,\n                height: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        },\n        children: \"System Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"System Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Last Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"June 28, 2025\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Database Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), \"Connected\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.25rem'\n            },\n            children: \"Server Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '600',\n              color: '#22c55e'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#22c55e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), \"Online\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Lock, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Security Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Security settings panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this);\n      case 'notifications':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Bell, {\n              size: 48,\n              color: \"#2d5016\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              marginBottom: '0.5rem'\n            },\n            children: \"Notification Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: \"Notification preferences panel coming soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-container\",\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"tab-button\",\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.3s ease',\n            transform: activeTab === tab.key ? 'translateY(-1px)' : 'translateY(0)',\n            boxShadow: activeTab === tab.key ? '0 4px 15px rgba(34, 197, 94, 0.3)' : 'none'\n          },\n          onMouseEnter: e => {\n            if (activeTab !== tab.key) {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.background = '#f9fafb';\n              e.currentTarget.style.transform = 'translateY(-1px)';\n            }\n          },\n          onMouseLeave: e => {\n            if (activeTab !== tab.key) {\n              e.currentTarget.style.borderColor = '#e8f5e8';\n              e.currentTarget.style.background = 'transparent';\n              e.currentTarget.style.transform = 'translateY(0)';\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"poP+7hqjoo+a2gF4wXhe6xsoQw4=\", false, function () {\n  return [useAdminAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useAdminAuth", "User", "Settings", "SettingsIcon", "Lock", "Bell", "CheckCircle", "ProfilePictureUpload", "AdminAuthService", "jsxDEV", "_jsxDEV", "_s", "user", "checkAuthStatus", "activeTab", "setActiveTab", "isUploadingPicture", "setIsUploadingPicture", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "confirm", "passwordErrors", "setPasswordErrors", "passwordSuccess", "setPasswordSuccess", "isChangingPassword", "setIsChangingPassword", "tabs", "key", "label", "icon", "handleProfilePictureUpload", "file", "console", "log", "result", "uploadProfilePicture", "error", "Error", "message", "handleProfilePictureRemove", "removeProfilePicture", "validatePassword", "password", "errors", "length", "push", "test", "handlePasswordChange", "field", "value", "prev", "togglePasswordVisibility", "handlePasswordSubmit", "e", "preventDefault", "passwordValidationErrors", "changePassword", "setTimeout", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "className", "background", "borderRadius", "padding", "boxShadow", "border", "alignItems", "marginBottom", "flexWrap", "flexShrink", "currentPicture", "profilePicture", "undefined", "userInitials", "firstName", "char<PERSON>t", "lastName", "onUpload", "onRemove", "isLoading", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "min<PERSON><PERSON><PERSON>", "paddingTop", "color", "fontSize", "fontWeight", "suffix", "email", "margin", "textAlign", "lineHeight", "disabled", "cursor", "renderSystemSettings", "justifyContent", "position", "width", "height", "type", "opacity", "top", "left", "right", "bottom", "transition", "defaultChecked", "gridTemplateColumns", "renderContent", "map", "tab", "onClick", "transform", "onMouseEnter", "currentTarget", "borderColor", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle, Eye, EyeOff, AlertCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\n\nconst Settings: React.FC = () => {\n  const { user, checkAuthStatus } = useAdminAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'system' | 'security' | 'notifications'>('profile');\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  // Password change state\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);\n  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile Settings', icon: User },\n    { key: 'system', label: 'System Settings', icon: SettingsIcon },\n    { key: 'security', label: 'Security', icon: Lock },\n    { key: 'notifications', label: 'Notifications', icon: Bell }\n  ];\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async (file: File) => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error: any) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  // Password validation\n  const validatePassword = (password: string): string[] => {\n    const errors: string[] = [];\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n    if (!/(?=.*[a-z])/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/(?=.*[A-Z])/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/(?=.*\\d)/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!/(?=.*[@$!%*?&])/.test(password)) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n    return errors;\n  };\n\n  // Handle password input change\n  const handlePasswordChange = (field: keyof typeof passwordData, value: string) => {\n    setPasswordData(prev => ({ ...prev, [field]: value }));\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {\n    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));\n  };\n\n  // Handle password change submission\n  const handlePasswordSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n\n    // Validation\n    const errors: string[] = [];\n\n    if (!passwordData.currentPassword) {\n      errors.push('Current password is required');\n    }\n\n    if (!passwordData.newPassword) {\n      errors.push('New password is required');\n    } else {\n      const passwordValidationErrors = validatePassword(passwordData.newPassword);\n      errors.push(...passwordValidationErrors);\n    }\n\n    if (!passwordData.confirmPassword) {\n      errors.push('Password confirmation is required');\n    } else if (passwordData.newPassword !== passwordData.confirmPassword) {\n      errors.push('New password and confirmation do not match');\n    }\n\n    if (passwordData.currentPassword === passwordData.newPassword) {\n      errors.push('New password must be different from current password');\n    }\n\n    if (errors.length > 0) {\n      setPasswordErrors(errors);\n      return;\n    }\n\n    setIsChangingPassword(true);\n    try {\n      // Call API to change password\n      await AdminAuthService.changePassword({\n        currentPassword: passwordData.currentPassword,\n        newPassword: passwordData.newPassword\n      });\n\n      setPasswordSuccess('Password changed successfully!');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n\n      // Clear success message after 5 seconds\n      setTimeout(() => setPasswordSuccess(null), 5000);\n    } catch (error: any) {\n      setPasswordErrors([error.message || 'Failed to change password']);\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Information Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n\n        {/* Horizontal Layout: Profile Picture + Profile Details */}\n        <div className=\"profile-layout\" style={{\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start',\n          marginBottom: '2rem',\n          flexWrap: 'wrap'\n        }}>\n          {/* Profile Picture (Left Side) */}\n          <div style={{ flexShrink: 0 }}>\n            <ProfilePictureUpload\n              currentPicture={user?.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined}\n              userInitials={`${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`}\n              onUpload={handleProfilePictureUpload}\n              onRemove={handleProfilePictureRemove}\n              isLoading={isUploadingPicture}\n              size={140}\n            />\n          </div>\n\n          {/* Profile Details (Right Side) */}\n          <div className=\"profile-details\" style={{\n            flex: 1,\n            minWidth: '300px',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem',\n            paddingTop: '0.5rem'\n          }}>\n            {/* Name */}\n            <div style={{ marginBottom: '1.5rem' }}>\n              <div style={{\n                color: '#111827',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                marginBottom: '0.5rem'\n              }}>\n                {`${user?.firstName || ''} ${user?.lastName || ''}`}\n                {user?.suffix && <span style={{ fontWeight: '400', color: '#6b7280' }}> {user.suffix}</span>}\n              </div>\n              <div style={{\n                color: '#6b7280',\n                fontSize: '1rem',\n                fontWeight: '500'\n              }}>\n                  <span>{user?.email || 'Not provided'}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Password Change Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Security Settings\n        </h3>\n\n        <div style={{\n          padding: '2rem',\n          background: '#f9fafb',\n          borderRadius: '12px',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        }}>\n          <div style={{ marginBottom: '1rem' }}>\n            <Lock size={48} color=\"#6b7280\" />\n          </div>\n          <h4 style={{\n            color: '#374151',\n            fontSize: '1.125rem',\n            fontWeight: '600',\n            marginBottom: '0.5rem'\n          }}>\n            Password Management\n          </h4>\n          <p style={{\n            color: '#6b7280',\n            marginBottom: '1.5rem',\n            lineHeight: '1.5'\n          }}>\n            Password change functionality will be available soon. For now, please contact your system administrator if you need to update your password.\n          </p>\n          <button\n            disabled\n            style={{\n              background: '#e8f5e8',\n              color: '#9ca3af',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: 'not-allowed',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              margin: '0 auto'\n            }}\n          >\n            <Lock size={16} />\n            Change Password (Coming Soon)\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* General Settings */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          General Settings\n        </h3>\n        \n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Maintenance Mode\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Enable maintenance mode to restrict access\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#ccc',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Auto-approve Posts\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Automatically approve new posts without review\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n          \n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <div>\n              <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n                Email Notifications\n              </div>\n              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                Send email notifications for important events\n              </div>\n            </div>\n            <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n              <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n              <span style={{\n                position: 'absolute',\n                cursor: 'pointer',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: '#22c55e',\n                transition: '0.4s',\n                borderRadius: '34px'\n              }} />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* System Information */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          System Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              System Version\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              v1.0.0\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Last Updated\n            </div>\n            <div style={{ fontWeight: '600', color: '#374151' }}>\n              June 28, 2025\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Database Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Connected\n              </span>\n            </div>\n          </div>\n          \n          <div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '0.25rem' }}>\n              Server Status\n            </div>\n            <div style={{ fontWeight: '600', color: '#22c55e' }}>\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                <CheckCircle size={16} color=\"#22c55e\" />\n                Online\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'system':\n        return renderSystemSettings();\n      case 'security':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Lock size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Security Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Security settings panel coming soon\n            </p>\n          </div>\n        );\n      case 'notifications':\n        return (\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '4rem 2rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{ marginBottom: '1rem' }}>\n              <Bell size={48} color=\"#2d5016\" />\n            </div>\n            <h3 style={{ color: '#2d5016', fontSize: '1.5rem', fontWeight: '600', marginBottom: '0.5rem' }}>\n              Notification Settings\n            </h3>\n            <p style={{ color: '#6b7280' }}>\n              Notification preferences panel coming soon\n            </p>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div>\n      {/* CSS for responsive design and animations */}\n      <style>{`\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `}</style>\n\n      {/* Tabs */}\n      <div className=\"fade-in\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div className=\"tab-container\" style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              className=\"tab-button\"\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key\n                  ? 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e8f5e8',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.3s ease',\n                transform: activeTab === tab.key ? 'translateY(-1px)' : 'translateY(0)',\n                boxShadow: activeTab === tab.key ? '0 4px 15px rgba(34, 197, 94, 0.3)' : 'none'\n              }}\n              onMouseEnter={(e) => {\n                if (activeTab !== tab.key) {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (activeTab !== tab.key) {\n                  e.currentTarget.style.borderColor = '#e8f5e8';\n                  e.currentTarget.style.background = 'transparent';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                }\n              }}\n            >\n              <tab.icon size={16} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"fade-in\">\n        {renderContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,IAAI,EAAEC,QAAQ,IAAIC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,QAAkC,cAAc;AAChH,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMR,QAAkB,GAAGA,CAAA,KAAM;EAAAS,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGb,YAAY,CAAC,CAAC;EAChD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAsD,SAAS,CAAC;EAC1G,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC;IAC/CqB,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC;IACjD0B,OAAO,EAAE,KAAK;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACiC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMmC,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEpC;EAAK,CAAC,EACzD;IAAEkC,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAElC;EAAa,CAAC,EAC/D;IAAEgC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEjC;EAAK,CAAC,EAClD;IAAE+B,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAEhC;EAAK,CAAC,CAC7D;;EAED;EACA,MAAMiC,0BAA0B,GAAG,MAAOC,IAAU,IAAK;IACvDtB,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACFuB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,MAAMC,MAAM,GAAG,MAAMlC,gBAAgB,CAACmC,oBAAoB,CAACJ,IAAI,CAAC;MAChEC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,MAAM,CAAC;;MAEnD;MACAF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAM5B,eAAe,CAAC,CAAC;MACvB2B,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE7B,IAAI,CAAC;IACrE,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBJ,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACR7B,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAM8B,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C9B,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMT,gBAAgB,CAACwC,oBAAoB,CAAC,CAAC;MAC7C;MACA,MAAMnC,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO+B,KAAU,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACR7B,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMgC,gBAAgB,GAAIC,QAAgB,IAAe;IACvD,MAAMC,MAAgB,GAAG,EAAE;IAC3B,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvBD,MAAM,CAACE,IAAI,CAAC,6CAA6C,CAAC;IAC5D;IACA,IAAI,CAAC,aAAa,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACjCC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAAC,aAAa,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACjCC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAAC,UAAU,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MAC9BC,MAAM,CAACE,IAAI,CAAC,2CAA2C,CAAC;IAC1D;IACA,IAAI,CAAC,iBAAiB,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACrCC,MAAM,CAACE,IAAI,CAAC,gEAAgE,CAAC;IAC/E;IACA,OAAOF,MAAM;EACf,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAACC,KAAgC,EAAEC,KAAa,KAAK;IAChFtC,eAAe,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IACtD5B,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM4B,wBAAwB,GAAIH,KAAiC,IAAK;IACtEhC,gBAAgB,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAG,CAACE,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAG,MAAOC,CAAkB,IAAK;IACzDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjC,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMoB,MAAgB,GAAG,EAAE;IAE3B,IAAI,CAACjC,YAAY,CAACE,eAAe,EAAE;MACjC+B,MAAM,CAACE,IAAI,CAAC,8BAA8B,CAAC;IAC7C;IAEA,IAAI,CAACnC,YAAY,CAACG,WAAW,EAAE;MAC7B8B,MAAM,CAACE,IAAI,CAAC,0BAA0B,CAAC;IACzC,CAAC,MAAM;MACL,MAAMU,wBAAwB,GAAGd,gBAAgB,CAAC/B,YAAY,CAACG,WAAW,CAAC;MAC3E8B,MAAM,CAACE,IAAI,CAAC,GAAGU,wBAAwB,CAAC;IAC1C;IAEA,IAAI,CAAC7C,YAAY,CAACI,eAAe,EAAE;MACjC6B,MAAM,CAACE,IAAI,CAAC,mCAAmC,CAAC;IAClD,CAAC,MAAM,IAAInC,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MACpE6B,MAAM,CAACE,IAAI,CAAC,4CAA4C,CAAC;IAC3D;IAEA,IAAInC,YAAY,CAACE,eAAe,KAAKF,YAAY,CAACG,WAAW,EAAE;MAC7D8B,MAAM,CAACE,IAAI,CAAC,sDAAsD,CAAC;IACrE;IAEA,IAAIF,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACrBvB,iBAAiB,CAACsB,MAAM,CAAC;MACzB;IACF;IAEAlB,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF;MACA,MAAMzB,gBAAgB,CAACwD,cAAc,CAAC;QACpC5C,eAAe,EAAEF,YAAY,CAACE,eAAe;QAC7CC,WAAW,EAAEH,YAAY,CAACG;MAC5B,CAAC,CAAC;MAEFU,kBAAkB,CAAC,gCAAgC,CAAC;MACpDZ,eAAe,CAAC;QACdC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA2C,UAAU,CAAC,MAAMlC,kBAAkB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOa,KAAU,EAAE;MACnBf,iBAAiB,CAAC,CAACe,KAAK,CAACE,OAAO,IAAI,2BAA2B,CAAC,CAAC;IACnE,CAAC,SAAS;MACRb,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMiC,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5B1D,OAAA;MAAK2D,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpE/D,OAAA;QAAKgE,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,eAGA/D,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAACL,KAAK,EAAE;YACrCC,OAAO,EAAE,MAAM;YACfE,GAAG,EAAE,MAAM;YACXQ,UAAU,EAAE,YAAY;YACxBC,YAAY,EAAE,MAAM;YACpBC,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,gBAEA/D,OAAA;YAAK2D,KAAK,EAAE;cAAEc,UAAU,EAAE;YAAE,CAAE;YAAAV,QAAA,eAC5B/D,OAAA,CAACH,oBAAoB;cACnB6E,cAAc,EAAExE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyE,cAAc,GAAG,wBAAwBzE,IAAI,CAACyE,cAAc,EAAE,GAAGC,SAAU;cACjGC,YAAY,EAAE,GAAG,CAAA3E,IAAI,aAAJA,IAAI,wBAAAuD,eAAA,GAAJvD,IAAI,CAAE4E,SAAS,cAAArB,eAAA,uBAAfA,eAAA,CAAiBsB,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA7E,IAAI,aAAJA,IAAI,wBAAAwD,cAAA,GAAJxD,IAAI,CAAE8E,QAAQ,cAAAtB,cAAA,uBAAdA,cAAA,CAAgBqB,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAG;cACtFE,QAAQ,EAAErD,0BAA2B;cACrCsD,QAAQ,EAAE7C,0BAA2B;cACrC8C,SAAS,EAAE7E,kBAAmB;cAC9B8E,IAAI,EAAE;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxF,OAAA;YAAKgE,SAAS,EAAC,iBAAiB;YAACL,KAAK,EAAE;cACtC8B,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjB9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,GAAG,EAAE,QAAQ;cACb6B,UAAU,EAAE;YACd,CAAE;YAAA5B,QAAA,eAEA/D,OAAA;cAAK2D,KAAK,EAAE;gBAAEY,YAAY,EAAE;cAAS,CAAE;cAAAR,QAAA,gBACrC/D,OAAA;gBAAK2D,KAAK,EAAE;kBACViC,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE,KAAK;kBACjBvB,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,GACC,GAAG,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,SAAS,KAAI,EAAE,IAAI,CAAA5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8E,QAAQ,KAAI,EAAE,EAAE,EAClD,CAAA9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,MAAM,kBAAI/F,OAAA;kBAAM2D,KAAK,EAAE;oBAAEmC,UAAU,EAAE,KAAK;oBAAEF,KAAK,EAAE;kBAAU,CAAE;kBAAA7B,QAAA,GAAC,GAAC,EAAC7D,IAAI,CAAC6F,MAAM;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,eACNxF,OAAA;gBAAK2D,KAAK,EAAE;kBACViC,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,eACE/D,OAAA;kBAAA+D,QAAA,EAAO,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAK,KAAI;gBAAc;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA;QAAKgE,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,gBACA/D,OAAA;UAAI2D,KAAK,EAAE;YACTsC,MAAM,EAAE,cAAc;YACtBL,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAA/B,QAAA,EAAC;QAEH;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELxF,OAAA;UAAK2D,KAAK,EAAE;YACVQ,OAAO,EAAE,MAAM;YACfF,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpBG,MAAM,EAAE,mBAAmB;YAC3B6B,SAAS,EAAE;UACb,CAAE;UAAAnC,QAAA,gBACA/D,OAAA;YAAK2D,KAAK,EAAE;cAAEY,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,eACnC/D,OAAA,CAACN,IAAI;cAAC0F,IAAI,EAAE,EAAG;cAACQ,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNxF,OAAA;YAAI2D,KAAK,EAAE;cACTiC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBvB,YAAY,EAAE;YAChB,CAAE;YAAAR,QAAA,EAAC;UAEH;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAG2D,KAAK,EAAE;cACRiC,KAAK,EAAE,SAAS;cAChBrB,YAAY,EAAE,QAAQ;cACtB4B,UAAU,EAAE;YACd,CAAE;YAAApC,QAAA,EAAC;UAEH;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJxF,OAAA;YACEoG,QAAQ;YACRzC,KAAK,EAAE;cACLM,UAAU,EAAE,SAAS;cACrB2B,KAAK,EAAE,SAAS;cAChBvB,MAAM,EAAE,MAAM;cACdH,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,gBAAgB;cACzB2B,UAAU,EAAE,KAAK;cACjBO,MAAM,EAAE,aAAa;cACrBzC,OAAO,EAAE,MAAM;cACfU,UAAU,EAAE,QAAQ;cACpBR,GAAG,EAAE,QAAQ;cACbmC,MAAM,EAAE;YACV,CAAE;YAAAlC,QAAA,gBAEF/D,OAAA,CAACN,IAAI;cAAC0F,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iCAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAMc,oBAAoB,GAAGA,CAAA,kBAC3BtG,OAAA;IAAK2D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEpE/D,OAAA;MAAK2D,KAAK,EAAE;QACVM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,gBACA/D,OAAA;QAAI2D,KAAK,EAAE;UACTsC,MAAM,EAAE,cAAc;UACtBL,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAA/B,QAAA,EAAC;MAEH;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELxF,OAAA;QAAK2D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACtE/D,OAAA;UAAK2D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE2C,cAAc,EAAE,eAAe;YAAEjC,UAAU,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrF/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAK2D,KAAK,EAAE;gBAAEmC,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAErB,YAAY,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAE9E;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxF,OAAA;cAAK2D,KAAK,EAAE;gBAAEiC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAA9B,QAAA,EAAC;YAExD;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxF,OAAA;YAAO2D,KAAK,EAAE;cAAE6C,QAAQ,EAAE,UAAU;cAAE5C,OAAO,EAAE,cAAc;cAAE6C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAA3C,QAAA,gBAC7F/D,OAAA;cAAO2G,IAAI,EAAC,UAAU;cAAChD,KAAK,EAAE;gBAAEiD,OAAO,EAAE,CAAC;gBAAEH,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrExF,OAAA;cAAM2D,KAAK,EAAE;gBACX6C,QAAQ,EAAE,UAAU;gBACpBH,MAAM,EAAE,SAAS;gBACjBQ,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACT/C,UAAU,EAAE,MAAM;gBAClBgD,UAAU,EAAE,MAAM;gBAClB/C,YAAY,EAAE;cAChB;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENxF,OAAA;UAAK2D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE2C,cAAc,EAAE,eAAe;YAAEjC,UAAU,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrF/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAK2D,KAAK,EAAE;gBAAEmC,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAErB,YAAY,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAE9E;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxF,OAAA;cAAK2D,KAAK,EAAE;gBAAEiC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAA9B,QAAA,EAAC;YAExD;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxF,OAAA;YAAO2D,KAAK,EAAE;cAAE6C,QAAQ,EAAE,UAAU;cAAE5C,OAAO,EAAE,cAAc;cAAE6C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAA3C,QAAA,gBAC7F/D,OAAA;cAAO2G,IAAI,EAAC,UAAU;cAAChD,KAAK,EAAE;gBAAEiD,OAAO,EAAE,CAAC;gBAAEH,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrExF,OAAA;cAAM2D,KAAK,EAAE;gBACX6C,QAAQ,EAAE,UAAU;gBACpBH,MAAM,EAAE,SAAS;gBACjBQ,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACT/C,UAAU,EAAE,SAAS;gBACrBgD,UAAU,EAAE,MAAM;gBAClB/C,YAAY,EAAE;cAChB;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENxF,OAAA;UAAK2D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE2C,cAAc,EAAE,eAAe;YAAEjC,UAAU,EAAE;UAAS,CAAE;UAAAP,QAAA,gBACrF/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA;cAAK2D,KAAK,EAAE;gBAAEmC,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,SAAS;gBAAErB,YAAY,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAE9E;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxF,OAAA;cAAK2D,KAAK,EAAE;gBAAEiC,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAA9B,QAAA,EAAC;YAExD;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxF,OAAA;YAAO2D,KAAK,EAAE;cAAE6C,QAAQ,EAAE,UAAU;cAAE5C,OAAO,EAAE,cAAc;cAAE6C,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAA3C,QAAA,gBAC7F/D,OAAA;cAAO2G,IAAI,EAAC,UAAU;cAACO,cAAc;cAACvD,KAAK,EAAE;gBAAEiD,OAAO,EAAE,CAAC;gBAAEH,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFxF,OAAA;cAAM2D,KAAK,EAAE;gBACX6C,QAAQ,EAAE,UAAU;gBACpBH,MAAM,EAAE,SAAS;gBACjBQ,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACT/C,UAAU,EAAE,SAAS;gBACrBgD,UAAU,EAAE,MAAM;gBAClB/C,YAAY,EAAE;cAChB;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAK2D,KAAK,EAAE;QACVM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,gBACA/D,OAAA;QAAI2D,KAAK,EAAE;UACTsC,MAAM,EAAE,cAAc;UACtBL,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE;QACd,CAAE;QAAA/B,QAAA,EAAC;MAEH;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELxF,OAAA;QAAK2D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEuD,mBAAmB,EAAE,SAAS;UAAErD,GAAG,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAC7E/D,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAK2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEtB,YAAY,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEjF;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxF,OAAA;YAAK2D,KAAK,EAAE;cAAEmC,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,EAAC;UAErD;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAK2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEtB,YAAY,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEjF;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxF,OAAA;YAAK2D,KAAK,EAAE;cAAEmC,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,EAAC;UAErD;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAK2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEtB,YAAY,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEjF;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxF,OAAA;YAAK2D,KAAK,EAAE;cAAEmC,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,eAClD/D,OAAA;cAAM2D,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEU,UAAU,EAAE,QAAQ;gBAAER,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE/D,OAAA,CAACJ,WAAW;gBAACwF,IAAI,EAAE,EAAG;gBAACQ,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAK2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEtB,YAAY,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAC;UAEjF;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxF,OAAA;YAAK2D,KAAK,EAAE;cAAEmC,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,eAClD/D,OAAA;cAAM2D,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEU,UAAU,EAAE,QAAQ;gBAAER,GAAG,EAAE;cAAU,CAAE;cAAAC,QAAA,gBACrE/D,OAAA,CAACJ,WAAW;gBAACwF,IAAI,EAAE,EAAG;gBAACQ,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQhH,SAAS;MACf,KAAK,SAAS;QACZ,OAAOoD,qBAAqB,CAAC,CAAC;MAChC,KAAK,QAAQ;QACX,OAAO8C,oBAAoB,CAAC,CAAC;MAC/B,KAAK,UAAU;QACb,oBACEtG,OAAA;UAAK2D,KAAK,EAAE;YACVM,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3B6B,SAAS,EAAE;UACb,CAAE;UAAAnC,QAAA,gBACA/D,OAAA;YAAK2D,KAAK,EAAE;cAAEY,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,eACnC/D,OAAA,CAACN,IAAI;cAAC0F,IAAI,EAAE,EAAG;cAACQ,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNxF,OAAA;YAAI2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEvB,YAAY,EAAE;YAAS,CAAE;YAAAR,QAAA,EAAC;UAEhG;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAG2D,KAAK,EAAE;cAAEiC,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,EAAC;UAEhC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV,KAAK,eAAe;QAClB,oBACExF,OAAA;UAAK2D,KAAK,EAAE;YACVM,UAAU,EAAE,OAAO;YACnBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,WAAW;YACpBC,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE,mBAAmB;YAC3B6B,SAAS,EAAE;UACb,CAAE;UAAAnC,QAAA,gBACA/D,OAAA;YAAK2D,KAAK,EAAE;cAAEY,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,eACnC/D,OAAA,CAACL,IAAI;cAACyF,IAAI,EAAE,EAAG;cAACQ,KAAK,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACNxF,OAAA;YAAI2D,KAAK,EAAE;cAAEiC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEvB,YAAY,EAAE;YAAS,CAAE;YAAAR,QAAA,EAAC;UAEhG;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAG2D,KAAK,EAAE;cAAEiC,KAAK,EAAE;YAAU,CAAE;YAAA7B,QAAA,EAAC;UAEhC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACExF,OAAA;IAAA+D,QAAA,gBAEE/D,OAAA;MAAA+D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGVxF,OAAA;MAAKgE,SAAS,EAAC,SAAS;MAACL,KAAK,EAAE;QAC9BM,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBI,YAAY,EAAE,MAAM;QACpBH,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAN,QAAA,eACA/D,OAAA;QAAKgE,SAAS,EAAC,eAAe;QAACL,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EACtFvC,IAAI,CAAC6F,GAAG,CAACC,GAAG,iBACXtH,OAAA;UAEEgE,SAAS,EAAC,YAAY;UACtBuD,OAAO,EAAEA,CAAA,KAAMlH,YAAY,CAACiH,GAAG,CAAC7F,GAAU,CAAE;UAC5CkC,KAAK,EAAE;YACLM,UAAU,EAAE7D,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjBmE,KAAK,EAAExF,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD4C,MAAM,EAAEjE,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5DyC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzBkC,MAAM,EAAE,SAAS;YACjBP,UAAU,EAAE,KAAK;YACjBlC,OAAO,EAAE,MAAM;YACfU,UAAU,EAAE,QAAQ;YACpBR,GAAG,EAAE,QAAQ;YACbmD,UAAU,EAAE,eAAe;YAC3BO,SAAS,EAAEpH,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,GAAG,kBAAkB,GAAG,eAAe;YACvE2C,SAAS,EAAEhE,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,GAAG,mCAAmC,GAAG;UAC3E,CAAE;UACFgG,YAAY,EAAGtE,CAAC,IAAK;YACnB,IAAI/C,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,EAAE;cACzB0B,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAACgE,WAAW,GAAG,SAAS;cAC7CxE,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAACM,UAAU,GAAG,SAAS;cAC5Cd,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAAC6D,SAAS,GAAG,kBAAkB;YACtD;UACF,CAAE;UACFI,YAAY,EAAGzE,CAAC,IAAK;YACnB,IAAI/C,SAAS,KAAKkH,GAAG,CAAC7F,GAAG,EAAE;cACzB0B,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAACgE,WAAW,GAAG,SAAS;cAC7CxE,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAACM,UAAU,GAAG,aAAa;cAChDd,CAAC,CAACuE,aAAa,CAAC/D,KAAK,CAAC6D,SAAS,GAAG,eAAe;YACnD;UACF,CAAE;UAAAzD,QAAA,gBAEF/D,OAAA,CAACsH,GAAG,CAAC3F,IAAI;YAACyD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrB8B,GAAG,CAAC5F,KAAK;QAAA,GApCL4F,GAAG,CAAC7F,GAAG;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqCN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKgE,SAAS,EAAC,SAAS;MAAAD,QAAA,EACrBqD,aAAa,CAAC;IAAC;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvF,EAAA,CAhnBIT,QAAkB;EAAA,QACYF,YAAY;AAAA;AAAAuI,EAAA,GAD1CrI,QAAkB;AAknBxB,eAAeA,QAAQ;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}