{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 13v5\",\n  key: \"igwfh0\"\n}], [\"path\", {\n  d: \"M17 11.47V8\",\n  key: \"16yw0g\"\n}], [\"path\", {\n  d: \"M17 11h1a3 3 0 0 1 2.745 4.211\",\n  key: \"1xbt65\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M5 8v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-3\",\n  key: \"c55o3e\"\n}], [\"path\", {\n  d: \"M7.536 7.535C6.766 7.649 6.154 8 5.5 8a2.5 2.5 0 0 1-1.768-4.268\",\n  key: \"1ydug7\"\n}], [\"path\", {\n  d: \"M8.727 3.204C9.306 2.767 9.885 2 11 2c1.56 0 2 1.5 3 1.5s1.72-.5 2.5-.5a1 1 0 1 1 0 5c-.78 0-1.5-.5-2.5-.5a3.149 3.149 0 0 0-.842.12\",\n  key: \"q81o7q\"\n}], [\"path\", {\n  d: \"M9 14.6V18\",\n  key: \"20ek98\"\n}]];\nconst BeerOff = createLucideIcon(\"beer-off\", __iconNode);\nexport { __iconNode, BeerOff as default };\n//# sourceMappingURL=beer-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}