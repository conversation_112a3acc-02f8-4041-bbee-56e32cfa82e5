{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 3h6a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-6\",\n  key: \"14mv1t\"\n}], [\"path\", {\n  d: \"m3 3 9 9\",\n  key: \"rks13r\"\n}], [\"path\", {\n  d: \"M3 9V3h6\",\n  key: \"ira0h2\"\n}]];\nconst SquareArrowOutUpLeft = createLucideIcon(\"square-arrow-out-up-left\", __iconNode);\nexport { __iconNode, SquareArrowOutUpLeft as default };\n//# sourceMappingURL=square-arrow-out-up-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}