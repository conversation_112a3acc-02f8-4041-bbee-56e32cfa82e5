{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M10 9H8\",\n  key: \"b1mrlr\"\n}], [\"path\", {\n  d: \"M16 13H8\",\n  key: \"t4e002\"\n}], [\"path\", {\n  d: \"M16 17H8\",\n  key: \"z1uh3a\"\n}]];\nconst FileText = createLucideIcon(\"file-text\", __iconNode);\nexport { __iconNode, FileText as default };\n//# sourceMappingURL=file-text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}