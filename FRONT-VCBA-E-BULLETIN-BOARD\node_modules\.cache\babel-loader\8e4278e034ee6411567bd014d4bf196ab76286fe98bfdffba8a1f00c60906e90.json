{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2C8 2 4 8 4 14a8 8 0 0 0 16 0c0-6-4-12-8-12\",\n  key: \"1le142\"\n}]];\nconst Egg = createLucideIcon(\"egg\", __iconNode);\nexport { __iconNode, Egg as default };\n//# sourceMappingURL=egg.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}