{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useMemo } from 'react';\nimport { useAdminAuth } from '../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../contexts/StudentAuthContext';\nimport { detectUserContext } from '../utils/authUtils';\n/**\n * Hook that provides unified authentication context\n * Automatically detects user role and provides appropriate auth context\n */\nexport const useUnifiedAuth = forceRole => {\n  _s();\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Get both auth contexts\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Detect user context\n  const userContext = useMemo(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return {\n        ...context,\n        role: forceRole\n      };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Determine which auth context to use\n  const currentAuth = useMemo(() => {\n    if (userContext.role === 'admin') {\n      return {\n        user: adminAuth.user,\n        logout: adminAuth.logout,\n        userType: 'admin'\n      };\n    } else if (userContext.role === 'student') {\n      return {\n        user: studentAuth.user,\n        logout: studentAuth.logout,\n        userType: 'student'\n      };\n    }\n    return null;\n  }, [userContext.role, adminAuth, studentAuth]);\n\n  // Handle loading states\n  useEffect(() => {\n    const adminLoading = adminAuth.isLoading;\n    const studentLoading = studentAuth.isLoading;\n\n    // If we know the role, only wait for that specific auth context\n    if (userContext.role === 'admin') {\n      setIsLoading(adminLoading);\n    } else if (userContext.role === 'student') {\n      setIsLoading(studentLoading);\n    } else {\n      // If role is unknown, wait for both to finish loading\n      setIsLoading(adminLoading || studentLoading);\n    }\n  }, [adminAuth.isLoading, studentAuth.isLoading, userContext.role]);\n\n  // Handle error states\n  useEffect(() => {\n    if (userContext.role === 'admin' && adminAuth.error) {\n      setError(adminAuth.error);\n    } else if (userContext.role === 'student' && studentAuth.error) {\n      setError(studentAuth.error);\n    } else {\n      setError(null);\n    }\n  }, [adminAuth.error, studentAuth.error, userContext.role]);\n  return {\n    userContext,\n    currentAuth,\n    isLoading,\n    error\n  };\n};\n_s(useUnifiedAuth, \"wOeo6/FWnadVIL8MaZulDhXGUBM=\", false, function () {\n  return [useAdminAuth, useStudentAuth];\n});\nexport default useUnifiedAuth;", "map": {"version": 3, "names": ["useState", "useEffect", "useMemo", "useAdminAuth", "useStudentAuth", "detectUserContext", "useUnifiedAuth", "forceRole", "_s", "isLoading", "setIsLoading", "error", "setError", "adminAuth", "studentAuth", "userContext", "context", "role", "currentAuth", "user", "logout", "userType", "adminLoading", "studentLoading"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useUnifiedAuth.ts"], "sourcesContent": ["import { useState, useEffect, useMemo } from 'react';\nimport { useAdminAuth } from '../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../contexts/StudentAuthContext';\nimport { detectUserContext, UserContext } from '../utils/authUtils';\n\ninterface UnifiedAuthReturn {\n  userContext: UserContext;\n  currentAuth: {\n    user: any;\n    logout: () => Promise<void>;\n    userType: 'admin' | 'student';\n  } | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\n/**\n * Hook that provides unified authentication context\n * Automatically detects user role and provides appropriate auth context\n */\nexport const useUnifiedAuth = (forceRole?: 'admin' | 'student'): UnifiedAuthReturn => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Get both auth contexts\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Detect user context\n  const userContext = useMemo<UserContext>(() => {\n    if (forceRole) {\n      const context = detectUserContext();\n      return { ...context, role: forceRole };\n    }\n    return detectUserContext();\n  }, [forceRole]);\n\n  // Determine which auth context to use\n  const currentAuth = useMemo(() => {\n    if (userContext.role === 'admin') {\n      return {\n        user: adminAuth.user,\n        logout: adminAuth.logout,\n        userType: 'admin' as const\n      };\n    } else if (userContext.role === 'student') {\n      return {\n        user: studentAuth.user,\n        logout: studentAuth.logout,\n        userType: 'student' as const\n      };\n    }\n    return null;\n  }, [userContext.role, adminAuth, studentAuth]);\n\n  // Handle loading states\n  useEffect(() => {\n    const adminLoading = adminAuth.isLoading;\n    const studentLoading = studentAuth.isLoading;\n    \n    // If we know the role, only wait for that specific auth context\n    if (userContext.role === 'admin') {\n      setIsLoading(adminLoading);\n    } else if (userContext.role === 'student') {\n      setIsLoading(studentLoading);\n    } else {\n      // If role is unknown, wait for both to finish loading\n      setIsLoading(adminLoading || studentLoading);\n    }\n  }, [adminAuth.isLoading, studentAuth.isLoading, userContext.role]);\n\n  // Handle error states\n  useEffect(() => {\n    if (userContext.role === 'admin' && adminAuth.error) {\n      setError(adminAuth.error);\n    } else if (userContext.role === 'student' && studentAuth.error) {\n      setError(studentAuth.error);\n    } else {\n      setError(null);\n    }\n  }, [adminAuth.error, studentAuth.error, userContext.role]);\n\n  return {\n    userContext,\n    currentAuth,\n    isLoading,\n    error\n  };\n};\n\nexport default useUnifiedAuth;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AACpD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,iBAAiB,QAAqB,oBAAoB;AAanE;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAIC,SAA+B,IAAwB;EAAAC,EAAA;EACpF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMa,SAAS,GAAGV,YAAY,CAAC,CAAC;EAChC,MAAMW,WAAW,GAAGV,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMW,WAAW,GAAGb,OAAO,CAAc,MAAM;IAC7C,IAAIK,SAAS,EAAE;MACb,MAAMS,OAAO,GAAGX,iBAAiB,CAAC,CAAC;MACnC,OAAO;QAAE,GAAGW,OAAO;QAAEC,IAAI,EAAEV;MAAU,CAAC;IACxC;IACA,OAAOF,iBAAiB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACE,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMW,WAAW,GAAGhB,OAAO,CAAC,MAAM;IAChC,IAAIa,WAAW,CAACE,IAAI,KAAK,OAAO,EAAE;MAChC,OAAO;QACLE,IAAI,EAAEN,SAAS,CAACM,IAAI;QACpBC,MAAM,EAAEP,SAAS,CAACO,MAAM;QACxBC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM,IAAIN,WAAW,CAACE,IAAI,KAAK,SAAS,EAAE;MACzC,OAAO;QACLE,IAAI,EAAEL,WAAW,CAACK,IAAI;QACtBC,MAAM,EAAEN,WAAW,CAACM,MAAM;QAC1BC,QAAQ,EAAE;MACZ,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACN,WAAW,CAACE,IAAI,EAAEJ,SAAS,EAAEC,WAAW,CAAC,CAAC;;EAE9C;EACAb,SAAS,CAAC,MAAM;IACd,MAAMqB,YAAY,GAAGT,SAAS,CAACJ,SAAS;IACxC,MAAMc,cAAc,GAAGT,WAAW,CAACL,SAAS;;IAE5C;IACA,IAAIM,WAAW,CAACE,IAAI,KAAK,OAAO,EAAE;MAChCP,YAAY,CAACY,YAAY,CAAC;IAC5B,CAAC,MAAM,IAAIP,WAAW,CAACE,IAAI,KAAK,SAAS,EAAE;MACzCP,YAAY,CAACa,cAAc,CAAC;IAC9B,CAAC,MAAM;MACL;MACAb,YAAY,CAACY,YAAY,IAAIC,cAAc,CAAC;IAC9C;EACF,CAAC,EAAE,CAACV,SAAS,CAACJ,SAAS,EAAEK,WAAW,CAACL,SAAS,EAAEM,WAAW,CAACE,IAAI,CAAC,CAAC;;EAElE;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIc,WAAW,CAACE,IAAI,KAAK,OAAO,IAAIJ,SAAS,CAACF,KAAK,EAAE;MACnDC,QAAQ,CAACC,SAAS,CAACF,KAAK,CAAC;IAC3B,CAAC,MAAM,IAAII,WAAW,CAACE,IAAI,KAAK,SAAS,IAAIH,WAAW,CAACH,KAAK,EAAE;MAC9DC,QAAQ,CAACE,WAAW,CAACH,KAAK,CAAC;IAC7B,CAAC,MAAM;MACLC,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,CAACC,SAAS,CAACF,KAAK,EAAEG,WAAW,CAACH,KAAK,EAAEI,WAAW,CAACE,IAAI,CAAC,CAAC;EAE1D,OAAO;IACLF,WAAW;IACXG,WAAW;IACXT,SAAS;IACTE;EACF,CAAC;AACH,CAAC;AAACH,EAAA,CApEWF,cAAc;EAAA,QAKPH,YAAY,EACVC,cAAc;AAAA;AAgEpC,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}