{"version": 3, "file": "Scope.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint-scope/Scope.ts"], "names": [], "mappings": "AAgBA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC5D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE3C,KAAK,SAAS,GACV,OAAO,GACP,OAAO,GACP,OAAO,GACP,KAAK,GACL,UAAU,GACV,0BAA0B,GAC1B,QAAQ,GACR,QAAQ,GACR,QAAQ,GACR,MAAM,GACN,KAAK,GACL,MAAM,GACN,gBAAgB,CAAC;AAErB,UAAU,KAAK;IACb,IAAI,EAAE,SAAS,CAAC;IAChB,QAAQ,EAAE,OAAO,CAAC;IAClB,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IACpB,WAAW,EAAE,KAAK,EAAE,CAAC;IACrB,aAAa,EAAE,KAAK,CAAC;IACrB,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;IACrB,SAAS,EAAE,QAAQ,EAAE,CAAC;IACtB,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC3B,UAAU,EAAE,SAAS,EAAE,CAAC;IACxB,OAAO,EAAE,SAAS,EAAE,CAAC;IACrB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,uBAAuB,EAAE,OAAO,CAAC;IACjC,MAAM,EAAE,SAAS,EAAE,CAAC;IAEpB,uBAAuB,CAAC,YAAY,EAAE,YAAY,GAAG,OAAO,CAAC;IAC7D,gCAAgC,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO,CAAC;IACpD,gBAAgB,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IACjC,iBAAiB,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IAClC,gBAAgB,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IACjC,OAAO,CAAC,YAAY,EAAE,YAAY,GAAG,KAAK,CAAC;IAC3C,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC;IACnE,SAAS,CAAC,GAAG,EAAE,SAAS,GAAG,OAAO,CAAC;IACnC,sBAAsB,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IACvC,4BAA4B,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACvE,eAAe,CACb,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,EAC1B,SAAS,EAAE,QAAQ,EAAE,EACrB,IAAI,EAAE,QAAQ,CAAC,UAAU,EACzB,GAAG,EAAE,UAAU,GACd,IAAI,CAAC;IAER,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,IAAI,CAAC;IAErD,aAAa,CACX,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,MAAM,CAAC,EAAE,aAAa,EACtB,SAAS,CAAC,EAAE,QAAQ,CAAC,IAAI,EACzB,mBAAmB,CAAC,EAAE,GAAG,EACzB,OAAO,CAAC,EAAE,GAAG,EACb,IAAI,CAAC,EAAE,GAAG,GACT,IAAI,CAAC;IAER,YAAY,IAAI,IAAI,CAAC;IACrB,YAAY,IAAI,IAAI,CAAC;IACrB,UAAU,IAAI,OAAO,CAAC;IACtB;;;;;OAKG;IACH,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC;IAEzC;;;;OAIG;IACH,QAAQ,IAAI,OAAO,CAAC;IAEpB;;;;OAIG;IACH,uBAAuB,IAAI,OAAO,CAAC;IAEnC;;;;OAIG;IACH,kBAAkB,IAAI,OAAO,CAAC;IAE9B,UAAU,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC;CAChC;AACD,UAAU,gBAAgB;IACxB,KACE,YAAY,EAAE,YAAY,EAC1B,IAAI,EAAE,SAAS,EACf,UAAU,EAAE,KAAK,GAAG,IAAI,EACxB,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,EAC3B,kBAAkB,EAAE,OAAO,GAC1B,KAAK,CAAC;CACV;AACD,QAAA,MAAM,KAAK,kBAAkC,CAAC;AAE9C,UAAU,mCAAmC,CAAC,CAAC;IAC7C,KACE,YAAY,EAAE,YAAY,EAC1B,UAAU,EAAE,KAAK,EACjB,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,GAC1B,CAAC,CAAC;CACN;AAED,UAAU,WAAY,SAAQ,KAAK;CAAG;AACtC,QAAA,MAAM,WAAW,yCACI,YAAY,SAAS,SAAS,IAAI,GAAG,IAAI,KAAG,WAAW,CAC3E,CAAC;AAEF,UAAU,WAAY,SAAQ,KAAK;CAAG;AACtC,QAAA,MAAM,WAAW,qEACiC,CAAC;AAEnD,UAAU,2BAA4B,SAAQ,KAAK;CAAG;AACtD,QAAA,MAAM,2BAA2B,qFAEmC,CAAC;AAErE,UAAU,UAAW,SAAQ,KAAK;CAAG;AACrC,QAAA,MAAM,UAAU,oEACiC,CAAC;AAElD,UAAU,SAAU,SAAQ,KAAK;CAAG;AACpC,QAAA,MAAM,SAAS,mEACiC,CAAC;AAEjD,UAAU,UAAW,SAAQ,KAAK;CAAG;AACrC,QAAA,MAAM,UAAU,oEACiC,CAAC;AAElD,UAAU,WAAY,SAAQ,KAAK;CAAG;AACtC,QAAA,MAAM,WAAW,qEACiC,CAAC;AAEnD,UAAU,aAAc,SAAQ,KAAK;CAAG;AACxC,QAAA,MAAM,aAAa,yCAED,YAAY,cACd,KAAK,SACV,SAAS,IAAI,GAAG,IAAI,sBACP,OAAO,KAC1B,aAAa,CACjB,CAAC;AAEF,UAAU,QAAS,SAAQ,KAAK;CAAG;AACnC,QAAA,MAAM,QAAQ,kEACiC,CAAC;AAEhD,UAAU,UAAW,SAAQ,KAAK;CAAG;AACrC,QAAA,MAAM,UAAU,oEACiC,CAAC;AAElD,OAAO,EACL,SAAS,EACT,KAAK,EACL,WAAW,EACX,WAAW,EACX,2BAA2B,EAC3B,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,aAAa,EACb,QAAQ,EACR,UAAU,GACX,CAAC"}