{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 7.75a.75.75 0 0 1 1.142-.638l3.664 2.249a.75.75 0 0 1 0 1.278l-3.664 2.25a.75.75 0 0 1-1.142-.64z\",\n  key: \"1pctta\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"3\",\n  width: \"20\",\n  height: \"14\",\n  rx: \"2\",\n  key: \"x3v2xh\"\n}]];\nconst MonitorPlay = createLucideIcon(\"monitor-play\", __iconNode);\nexport { __iconNode, MonitorPlay as default };\n//# sourceMappingURL=monitor-play.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}