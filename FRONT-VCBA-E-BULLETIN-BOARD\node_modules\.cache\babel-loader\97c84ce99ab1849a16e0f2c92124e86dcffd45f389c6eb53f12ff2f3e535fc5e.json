{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13V7\",\n  key: \"h0r20n\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"k3hazp\"\n}], [\"path\", {\n  d: \"m9 10 3-3 3 3\",\n  key: \"11gsxs\"\n}]];\nconst BookUp = createLucideIcon(\"book-up\", __iconNode);\nexport { __iconNode, BookUp as default };\n//# sourceMappingURL=book-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}