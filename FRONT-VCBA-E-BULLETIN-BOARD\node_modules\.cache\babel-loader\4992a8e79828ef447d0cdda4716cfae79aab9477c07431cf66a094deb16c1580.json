{"ast": null, "code": "import { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY } from '../config/constants';\n/**\n * Detects the current user's role and authentication status\n * by checking available tokens and user data in localStorage\n */\nexport const detectUserContext = () => {\n  const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n  const adminUserData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n  const studentUserData = localStorage.getItem(STUDENT_USER_DATA_KEY);\n\n  // Check current path to help determine context\n  const currentPath = window.location.pathname;\n  const isAdminPath = currentPath.includes('/admin');\n  const isStudentPath = currentPath.includes('/student');\n\n  // Priority 1: If we're on an admin path and have admin auth\n  if (isAdminPath && adminToken && adminUserData) {\n    try {\n      const user = JSON.parse(adminUserData);\n      if (user.role === 'admin') {\n        return {\n          role: 'admin',\n          user,\n          token: adminToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse admin user data:', error);\n    }\n  }\n\n  // Priority 2: If we're on a student path and have student auth\n  if (isStudentPath && studentToken && studentUserData) {\n    try {\n      const user = JSON.parse(studentUserData);\n      if (user.role === 'student') {\n        return {\n          role: 'student',\n          user,\n          token: studentToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse student user data:', error);\n    }\n  }\n\n  // Priority 3: Check admin auth regardless of path\n  if (adminToken && adminUserData) {\n    try {\n      const user = JSON.parse(adminUserData);\n      if (user.role === 'admin') {\n        return {\n          role: 'admin',\n          user,\n          token: adminToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse admin user data:', error);\n    }\n  }\n\n  // Priority 4: Check student auth regardless of path\n  if (studentToken && studentUserData) {\n    try {\n      const user = JSON.parse(studentUserData);\n      if (user.role === 'student') {\n        return {\n          role: 'student',\n          user,\n          token: studentToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse student user data:', error);\n    }\n  }\n\n  // No valid authentication found\n  return {\n    role: null,\n    user: null,\n    token: null,\n    isAuthenticated: false\n  };\n};\n\n/**\n * Gets the appropriate authentication context hook based on detected role\n */\nexport const getAuthContextForRole = role => {\n  if (role === 'admin') {\n    return 'admin';\n  } else if (role === 'student') {\n    return 'student';\n  }\n  return null;\n};\n\n/**\n * Determines if the current user has admin privileges\n */\nexport const isAdminUser = () => {\n  const context = detectUserContext();\n  return context.role === 'admin' && context.isAuthenticated;\n};\n\n/**\n * Determines if the current user has student privileges\n */\nexport const isStudentUser = () => {\n  const context = detectUserContext();\n  return context.role === 'student' && context.isAuthenticated;\n};\n\n/**\n * Gets the current user's role as a string\n */\nexport const getCurrentUserRole = () => {\n  const context = detectUserContext();\n  return context.role;\n};", "map": {"version": 3, "names": ["ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "ADMIN_USER_DATA_KEY", "STUDENT_USER_DATA_KEY", "detectUserContext", "adminToken", "localStorage", "getItem", "studentToken", "adminUserData", "studentUserData", "currentPath", "window", "location", "pathname", "isAdminPath", "includes", "isStudentPath", "user", "JSON", "parse", "role", "token", "isAuthenticated", "error", "console", "warn", "getAuthContextForRole", "isAdminUser", "context", "isStudentUser", "getCurrentUserRole"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/authUtils.ts"], "sourcesContent": ["import { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY } from '../config/constants';\n\nexport interface UserContext {\n  role: 'admin' | 'student' | null;\n  user: any;\n  token: string | null;\n  isAuthenticated: boolean;\n}\n\n/**\n * Detects the current user's role and authentication status\n * by checking available tokens and user data in localStorage\n */\nexport const detectUserContext = (): UserContext => {\n  const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n  const adminUserData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n  const studentUserData = localStorage.getItem(STUDENT_USER_DATA_KEY);\n\n  // Check current path to help determine context\n  const currentPath = window.location.pathname;\n  const isAdminPath = currentPath.includes('/admin');\n  const isStudentPath = currentPath.includes('/student');\n\n  // Priority 1: If we're on an admin path and have admin auth\n  if (isAdminPath && adminToken && adminUserData) {\n    try {\n      const user = JSON.parse(adminUserData);\n      if (user.role === 'admin') {\n        return {\n          role: 'admin',\n          user,\n          token: adminToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse admin user data:', error);\n    }\n  }\n\n  // Priority 2: If we're on a student path and have student auth\n  if (isStudentPath && studentToken && studentUserData) {\n    try {\n      const user = JSON.parse(studentUserData);\n      if (user.role === 'student') {\n        return {\n          role: 'student',\n          user,\n          token: studentToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse student user data:', error);\n    }\n  }\n\n  // Priority 3: Check admin auth regardless of path\n  if (adminToken && adminUserData) {\n    try {\n      const user = JSON.parse(adminUserData);\n      if (user.role === 'admin') {\n        return {\n          role: 'admin',\n          user,\n          token: adminToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse admin user data:', error);\n    }\n  }\n\n  // Priority 4: Check student auth regardless of path\n  if (studentToken && studentUserData) {\n    try {\n      const user = JSON.parse(studentUserData);\n      if (user.role === 'student') {\n        return {\n          role: 'student',\n          user,\n          token: studentToken,\n          isAuthenticated: true\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to parse student user data:', error);\n    }\n  }\n\n  // No valid authentication found\n  return {\n    role: null,\n    user: null,\n    token: null,\n    isAuthenticated: false\n  };\n};\n\n/**\n * Gets the appropriate authentication context hook based on detected role\n */\nexport const getAuthContextForRole = (role: 'admin' | 'student' | null) => {\n  if (role === 'admin') {\n    return 'admin';\n  } else if (role === 'student') {\n    return 'student';\n  }\n  return null;\n};\n\n/**\n * Determines if the current user has admin privileges\n */\nexport const isAdminUser = (): boolean => {\n  const context = detectUserContext();\n  return context.role === 'admin' && context.isAuthenticated;\n};\n\n/**\n * Determines if the current user has student privileges\n */\nexport const isStudentUser = (): boolean => {\n  const context = detectUserContext();\n  return context.role === 'student' && context.isAuthenticated;\n};\n\n/**\n * Gets the current user's role as a string\n */\nexport const getCurrentUserRole = (): 'admin' | 'student' | null => {\n  const context = detectUserContext();\n  return context.role;\n};\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,qBAAqB;AAS9H;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAmB;EAClD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACP,oBAAoB,CAAC;EAC7D,MAAMQ,YAAY,GAAGF,YAAY,CAACC,OAAO,CAACN,sBAAsB,CAAC;EACjE,MAAMQ,aAAa,GAAGH,YAAY,CAACC,OAAO,CAACL,mBAAmB,CAAC;EAC/D,MAAMQ,eAAe,GAAGJ,YAAY,CAACC,OAAO,CAACJ,qBAAqB,CAAC;;EAEnE;EACA,MAAMQ,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAC5C,MAAMC,WAAW,GAAGJ,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAMC,aAAa,GAAGN,WAAW,CAACK,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,IAAID,WAAW,IAAIV,UAAU,IAAII,aAAa,EAAE;IAC9C,IAAI;MACF,MAAMS,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACX,aAAa,CAAC;MACtC,IAAIS,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;QACzB,OAAO;UACLA,IAAI,EAAE,OAAO;UACbH,IAAI;UACJI,KAAK,EAAEjB,UAAU;UACjBkB,eAAe,EAAE;QACnB,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IACzD;EACF;;EAEA;EACA,IAAIP,aAAa,IAAIT,YAAY,IAAIE,eAAe,EAAE;IACpD,IAAI;MACF,MAAMQ,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACV,eAAe,CAAC;MACxC,IAAIQ,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;QAC3B,OAAO;UACLA,IAAI,EAAE,SAAS;UACfH,IAAI;UACJI,KAAK,EAAEd,YAAY;UACnBe,eAAe,EAAE;QACnB,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;IAC3D;EACF;;EAEA;EACA,IAAInB,UAAU,IAAII,aAAa,EAAE;IAC/B,IAAI;MACF,MAAMS,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACX,aAAa,CAAC;MACtC,IAAIS,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;QACzB,OAAO;UACLA,IAAI,EAAE,OAAO;UACbH,IAAI;UACJI,KAAK,EAAEjB,UAAU;UACjBkB,eAAe,EAAE;QACnB,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAEF,KAAK,CAAC;IACzD;EACF;;EAEA;EACA,IAAIhB,YAAY,IAAIE,eAAe,EAAE;IACnC,IAAI;MACF,MAAMQ,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACV,eAAe,CAAC;MACxC,IAAIQ,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;QAC3B,OAAO;UACLA,IAAI,EAAE,SAAS;UACfH,IAAI;UACJI,KAAK,EAAEd,YAAY;UACnBe,eAAe,EAAE;QACnB,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;IAC3D;EACF;;EAEA;EACA,OAAO;IACLH,IAAI,EAAE,IAAI;IACVH,IAAI,EAAE,IAAI;IACVI,KAAK,EAAE,IAAI;IACXC,eAAe,EAAE;EACnB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,qBAAqB,GAAIN,IAAgC,IAAK;EACzE,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,OAAO;EAChB,CAAC,MAAM,IAAIA,IAAI,KAAK,SAAS,EAAE;IAC7B,OAAO,SAAS;EAClB;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,WAAW,GAAGA,CAAA,KAAe;EACxC,MAAMC,OAAO,GAAGzB,iBAAiB,CAAC,CAAC;EACnC,OAAOyB,OAAO,CAACR,IAAI,KAAK,OAAO,IAAIQ,OAAO,CAACN,eAAe;AAC5D,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,aAAa,GAAGA,CAAA,KAAe;EAC1C,MAAMD,OAAO,GAAGzB,iBAAiB,CAAC,CAAC;EACnC,OAAOyB,OAAO,CAACR,IAAI,KAAK,SAAS,IAAIQ,OAAO,CAACN,eAAe;AAC9D,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMQ,kBAAkB,GAAGA,CAAA,KAAkC;EAClE,MAAMF,OAAO,GAAGzB,iBAAiB,CAAC,CAAC;EACnC,OAAOyB,OAAO,CAACR,IAAI;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}