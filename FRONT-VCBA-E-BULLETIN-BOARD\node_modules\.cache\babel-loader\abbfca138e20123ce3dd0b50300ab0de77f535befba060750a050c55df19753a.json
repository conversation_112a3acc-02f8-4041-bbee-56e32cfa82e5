{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"13\",\n  height: \"7\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"11xb64\"\n}], [\"path\", {\n  d: \"m22 15-3-3 3-3\",\n  key: \"26chmm\"\n}], [\"rect\", {\n  width: \"13\",\n  height: \"7\",\n  x: \"3\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"k6ky7n\"\n}]];\nconst BetweenHorizontalEnd = createLucideIcon(\"between-horizontal-end\", __iconNode);\nexport { __iconNode, BetweenHorizontalEnd as default };\n//# sourceMappingURL=between-horizontal-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}