{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M18 6V5\",\n  key: \"1oao2s\"\n}], [\"path\", {\n  d: \"M18 11v-1\",\n  key: \"11c8tz\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"9\",\n  y2: \"21\",\n  key: \"rroup\"\n}]];\nconst GitPullRequestDraft = createLucideIcon(\"git-pull-request-draft\", __iconNode);\nexport { __iconNode, GitPullRequestDraft as default };\n//# sourceMappingURL=git-pull-request-draft.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}