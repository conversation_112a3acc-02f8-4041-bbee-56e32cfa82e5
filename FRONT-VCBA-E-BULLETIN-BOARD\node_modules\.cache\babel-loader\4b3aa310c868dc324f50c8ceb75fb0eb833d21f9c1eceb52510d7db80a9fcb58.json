{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M7 11h8\",\n  key: \"1feolt\"\n}], [\"path\", {\n  d: \"M7 16h3\",\n  key: \"ur6vzw\"\n}], [\"path\", {\n  d: \"M7 6h12\",\n  key: \"sz5b0d\"\n}]];\nconst ChartBarDecreasing = createLucideIcon(\"chart-bar-decreasing\", __iconNode);\nexport { __iconNode, ChartBarDecreasing as default };\n//# sourceMappingURL=chart-bar-decreasing.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}