{"ast": null, "code": "import React from'react';import{useNavigate}from'react-router-dom';import{MessageSquare,Users,BarChart3,Rss}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminDashboard=()=>{const navigate=useNavigate();return/*#__PURE__*/_jsx(\"div\",{style:{maxWidth:'1200px',margin:'0 auto'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(300px, 1fr))',gap:'1.5rem',marginBottom:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{onClick:()=>navigate('/admin/posts'),style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'left',transition:'transform 0.2s ease, box-shadow 0.2s ease',cursor:'pointer'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-4px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',borderRadius:'16px',display:'flex',alignItems:'center',justifyContent:'center',marginBottom:'1rem'},children:/*#__PURE__*/_jsx(MessageSquare,{size:24,color:\"white\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#1e40af',margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'600'},children:\"Post Management\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'0.95rem',lineHeight:'1.5'},children:\"Create, edit, and manage bulletin board announcements for students and faculty\"})]}),/*#__PURE__*/_jsxs(\"div\",{onClick:()=>navigate('/admin/student-management'),style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #fef3c7',textAlign:'left',transition:'transform 0.2s ease, box-shadow 0.2s ease',cursor:'pointer'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-4px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #facc15 0%, #eab308 100%)',borderRadius:'16px',display:'flex',alignItems:'center',justifyContent:'center',marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Users,{size:24,color:\"white\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#92400e',margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'600'},children:\"Student Management\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'0.95rem',lineHeight:'1.5'},children:\"Create and manage student accounts, profiles, and academic information\"})]}),/*#__PURE__*/_jsxs(\"div\",{onClick:()=>navigate('/admin/newsfeed'),style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'left',transition:'transform 0.2s ease, box-shadow 0.2s ease',cursor:'pointer'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-4px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',borderRadius:'16px',display:'flex',alignItems:'center',justifyContent:'center',marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Rss,{size:24,color:\"white\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#d97706',margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'600'},children:\"Admin Newsfeed\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'0.95rem',lineHeight:'1.5'},children:\"Monitor announcements, events, and community engagement\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e5e7eb',textAlign:'left',transition:'transform 0.2s ease, box-shadow 0.2s ease',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',borderRadius:'16px',display:'flex',alignItems:'center',justifyContent:'center',marginBottom:'1rem'},children:/*#__PURE__*/_jsx(BarChart3,{size:24,color:\"white\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#374151',margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'600'},children:\"Analytics & Reports\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:0,fontSize:'0.95rem',lineHeight:'1.5'},children:\"View engagement metrics, generate reports, and track system usage\"})]})]})});};export default AdminDashboard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}