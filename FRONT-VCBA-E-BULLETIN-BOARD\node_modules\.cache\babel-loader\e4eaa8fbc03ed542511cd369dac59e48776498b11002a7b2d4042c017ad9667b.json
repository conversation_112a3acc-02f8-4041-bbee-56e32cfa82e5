{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useState,useCallback}from'react';import Toast from'../components/common/Toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ToastContext=/*#__PURE__*/createContext(undefined);export const useToast=()=>{const context=useContext(ToastContext);if(!context){throw new Error('useToast must be used within a ToastProvider');}return context;};export const ToastProvider=_ref=>{let{children,maxToasts=5}=_ref;const[toasts,setToasts]=useState([]);const generateId=useCallback(()=>{return\"toast-\".concat(Date.now(),\"-\").concat(Math.random().toString(36).substr(2,9));},[]);const showToast=useCallback(toast=>{var _toast$duration;const id=generateId();const newToast=_objectSpread(_objectSpread({},toast),{},{id,duration:(_toast$duration=toast.duration)!==null&&_toast$duration!==void 0?_toast$duration:4000});setToasts(prev=>{const updated=[newToast,...prev];// Limit the number of toasts\nreturn updated.slice(0,maxToasts);});},[generateId,maxToasts]);const showSuccess=useCallback((title,message,duration)=>{showToast({type:'success',title,message,duration});},[showToast]);const showError=useCallback((title,message,duration)=>{showToast({type:'error',title,message,duration});},[showToast]);const showInfo=useCallback((title,message,duration)=>{showToast({type:'info',title,message,duration});},[showToast]);const showWarning=useCallback((title,message,duration)=>{showToast({type:'warning',title,message,duration});},[showToast]);const removeToast=useCallback(id=>{setToasts(prev=>prev.filter(toast=>toast.id!==id));},[]);const clearAllToasts=useCallback(()=>{setToasts([]);},[]);const contextValue={showToast,showSuccess,showError,showInfo,showWarning,removeToast,clearAllToasts};return/*#__PURE__*/_jsxs(ToastContext.Provider,{value:contextValue,children:[children,/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:'1rem',right:'1rem',zIndex:9999,display:'flex',flexDirection:'column',gap:'0.5rem',pointerEvents:'none'},children:toasts.map((toast,index)=>/*#__PURE__*/_jsx(\"div\",{style:{pointerEvents:'auto',transform:\"translateY(\".concat(index*10,\"px)\"),transition:'transform 0.3s ease'},children:/*#__PURE__*/_jsx(Toast,{id:toast.id,type:toast.type,title:toast.title,message:toast.message,duration:toast.duration,onClose:removeToast})},toast.id))})]});};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}