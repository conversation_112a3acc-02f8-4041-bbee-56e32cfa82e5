{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m19 3 1 1\",\n  key: \"ze14oc\"\n}], [\"path\", {\n  d: \"m20 2-4.5 4.5\",\n  key: \"1sppr8\"\n}], [\"path\", {\n  d: \"M20 7.898V21a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"1xzogz\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2h7.844\",\n  key: \"vtdg6h\"\n}], [\"circle\", {\n  cx: \"14\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"u49eql\"\n}]];\nconst BookKey = createLucideIcon(\"book-key\", __iconNode);\nexport { __iconNode, Book<PERSON><PERSON> as default };\n//# sourceMappingURL=book-key.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}