{"name": "@sinclair/typebox", "version": "0.34.38", "description": "Json Schema Type Builder with Static Type Resolution for TypeScript", "keywords": ["typescript", "json-schema", "validate", "typecheck"], "author": "sinclairzx81", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sinclairzx81/typebox"}, "scripts": {"test": "echo test"}, "types": "./build/cjs/index.d.ts", "main": "./build/cjs/index.js", "module": "./build/esm/index.mjs", "esm.sh": {"bundle": false}, "sideEffects": ["./build/esm/type/registry/format.mjs", "./build/esm/type/registry/type.mjs", "./build/esm/type/system/policy.mjs", "./build/cjs/type/registry/format.js", "./build/cjs/type/registry/type.js", "./build/cjs/type/system/policy.js"], "exports": {".": {"require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}, "import": {"types": "./build/esm/index.d.mts", "default": "./build/esm/index.mjs"}}, "./compiler": {"require": {"types": "./build/cjs/compiler/index.d.ts", "default": "./build/cjs/compiler/index.js"}, "import": {"types": "./build/esm/compiler/index.d.mts", "default": "./build/esm/compiler/index.mjs"}}, "./errors": {"require": {"types": "./build/cjs/errors/index.d.ts", "default": "./build/cjs/errors/index.js"}, "import": {"types": "./build/esm/errors/index.d.mts", "default": "./build/esm/errors/index.mjs"}}, "./parser": {"require": {"types": "./build/cjs/parser/index.d.ts", "default": "./build/cjs/parser/index.js"}, "import": {"types": "./build/esm/parser/index.d.mts", "default": "./build/esm/parser/index.mjs"}}, "./syntax": {"require": {"types": "./build/cjs/syntax/index.d.ts", "default": "./build/cjs/syntax/index.js"}, "import": {"types": "./build/esm/syntax/index.d.mts", "default": "./build/esm/syntax/index.mjs"}}, "./system": {"require": {"types": "./build/cjs/system/index.d.ts", "default": "./build/cjs/system/index.js"}, "import": {"types": "./build/esm/system/index.d.mts", "default": "./build/esm/system/index.mjs"}}, "./type": {"require": {"types": "./build/cjs/type/index.d.ts", "default": "./build/cjs/type/index.js"}, "import": {"types": "./build/esm/type/index.d.mts", "default": "./build/esm/type/index.mjs"}}, "./value": {"require": {"types": "./build/cjs/value/index.d.ts", "default": "./build/cjs/value/index.js"}, "import": {"types": "./build/esm/value/index.d.mts", "default": "./build/esm/value/index.mjs"}}}}