{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0\",\n  key: \"1r0f0z\"\n}], [\"path\", {\n  d: \"M9 10h6\",\n  key: \"9gxzsh\"\n}]];\nconst MapPinMinusInside = createLucideIcon(\"map-pin-minus-inside\", __iconNode);\nexport { __iconNode, MapPinMinusInside as default };\n//# sourceMappingURL=map-pin-minus-inside.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}