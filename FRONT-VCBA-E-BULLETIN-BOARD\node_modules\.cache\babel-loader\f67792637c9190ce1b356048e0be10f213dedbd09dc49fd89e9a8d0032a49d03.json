{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 13H8a1 1 0 0 0-1 1v7\",\n  key: \"h8g396\"\n}], [\"path\", {\n  d: \"M14 8h1\",\n  key: \"1lfen6\"\n}], [\"path\", {\n  d: \"M17 21v-4\",\n  key: \"1yknxs\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20.41 20.41A2 2 0 0 1 19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 .59-1.41\",\n  key: \"1t4vdl\"\n}], [\"path\", {\n  d: \"M29.5 11.5s5 5 4 5\",\n  key: \"zzn4i6\"\n}], [\"path\", {\n  d: \"M9 3h6.2a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V15\",\n  key: \"24cby9\"\n}]];\nconst SaveOff = createLucideIcon(\"save-off\", __iconNode);\nexport { __iconNode, SaveOff as default };\n//# sourceMappingURL=save-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}