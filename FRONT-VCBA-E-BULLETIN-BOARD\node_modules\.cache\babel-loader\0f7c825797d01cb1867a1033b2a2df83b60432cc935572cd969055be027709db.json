{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 4v16H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z\",\n  key: \"1m5n7q\"\n}], [\"circle\", {\n  cx: \"14\",\n  cy: \"12\",\n  r: \"8\",\n  key: \"1pag6k\"\n}]];\nconst RectangleCircle = createLucideIcon(\"rectangle-circle\", __iconNode);\nexport { __iconNode, RectangleCircle as default };\n//# sourceMappingURL=rectangle-circle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}