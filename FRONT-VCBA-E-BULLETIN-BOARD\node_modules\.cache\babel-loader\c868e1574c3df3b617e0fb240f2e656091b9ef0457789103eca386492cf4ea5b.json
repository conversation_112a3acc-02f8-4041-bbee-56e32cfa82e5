{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 3H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-3\",\n  key: \"i8wdob\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"m22 3-5 5\",\n  key: \"12jva0\"\n}], [\"path\", {\n  d: \"m17 3 5 5\",\n  key: \"k36vhe\"\n}]];\nconst ScreenShareOff = createLucideIcon(\"screen-share-off\", __iconNode);\nexport { __iconNode, ScreenShareOff as default };\n//# sourceMappingURL=screen-share-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}