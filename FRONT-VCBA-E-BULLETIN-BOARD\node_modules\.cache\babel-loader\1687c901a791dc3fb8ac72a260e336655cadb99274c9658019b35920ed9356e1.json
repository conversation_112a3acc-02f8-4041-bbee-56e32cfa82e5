{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"10\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"e45bow\"\n}], [\"path\", {\n  d: \"M10.3 15H7a4 4 0 0 0-4 4v2\",\n  key: \"3bnktk\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"18b49y\"\n}], [\"path\", {\n  d: \"m21 21-1.9-1.9\",\n  key: \"1g2n9r\"\n}]];\nconst UserSearch = createLucideIcon(\"user-search\", __iconNode);\nexport { __iconNode, UserSearch as default };\n//# sourceMappingURL=user-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}