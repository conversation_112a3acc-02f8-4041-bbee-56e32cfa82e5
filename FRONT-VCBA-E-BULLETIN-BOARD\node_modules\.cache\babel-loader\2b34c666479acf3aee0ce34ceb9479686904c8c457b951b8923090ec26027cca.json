{"ast": null, "code": "// Central export for common components\nexport { default as ProtectedRoute } from './ProtectedRoute';\nexport { default as PublicRoute } from './PublicRoute';\nexport { default as FacebookImageGallery } from './FacebookImageGallery';", "map": {"version": 3, "names": ["default", "ProtectedRoute", "PublicRoute", "FacebookImageGallery"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/index.ts"], "sourcesContent": ["// Central export for common components\nexport { default as ProtectedRoute } from './ProtectedRoute';\nexport { default as PublicRoute } from './PublicRoute';\nexport { default as FacebookImageGallery } from './FacebookImageGallery';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,SAASD,OAAO,IAAIE,WAAW,QAAQ,eAAe;AACtD,SAASF,OAAO,IAAIG,oBAAoB,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}