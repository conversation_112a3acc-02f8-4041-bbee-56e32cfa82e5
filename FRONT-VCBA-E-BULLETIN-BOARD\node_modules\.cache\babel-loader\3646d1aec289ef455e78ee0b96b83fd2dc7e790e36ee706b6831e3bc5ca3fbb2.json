{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.662 21H5a2 2 0 0 1-2-2v-9a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v2.475\",\n  key: \"uubd2h\"\n}], [\"path\", {\n  d: \"M14.959 12.717A1 1 0 0 0 14 12h-4a1 1 0 0 0-1 1v8\",\n  key: \"p7f341\"\n}], [\"path\", {\n  d: \"M15 18h6\",\n  key: \"3b3c90\"\n}], [\"path\", {\n  d: \"M18 15v6\",\n  key: \"9wciyi\"\n}]];\nconst HousePlus = createLucideIcon(\"house-plus\", __iconNode);\nexport { __iconNode, HousePlus as default };\n//# sourceMappingURL=house-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}