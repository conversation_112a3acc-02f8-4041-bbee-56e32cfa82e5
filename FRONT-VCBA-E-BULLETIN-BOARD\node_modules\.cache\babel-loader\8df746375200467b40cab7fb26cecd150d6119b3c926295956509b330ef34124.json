{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 5H19a2 2 0 0 1 2 2v8.5\",\n  key: \"jqtk4d\"\n}], [\"path\", {\n  d: \"M17 11h-.5\",\n  key: \"1961ue\"\n}], [\"path\", {\n  d: \"M19 19H5a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2\",\n  key: \"1keqsi\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M7 11h4\",\n  key: \"1o1z6v\"\n}], [\"path\", {\n  d: \"M7 15h2.5\",\n  key: \"1ina1g\"\n}]];\nconst CaptionsOff = createLucideIcon(\"captions-off\", __iconNode);\nexport { __iconNode, CaptionsOff as default };\n//# sourceMappingURL=captions-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}