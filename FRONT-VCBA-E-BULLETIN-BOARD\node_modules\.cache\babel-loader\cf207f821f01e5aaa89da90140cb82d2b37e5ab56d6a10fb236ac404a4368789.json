{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"m12 10 3-3\",\n  key: \"1mc12w\"\n}], [\"path\", {\n  d: \"m9 7 3 3v7.5\",\n  key: \"39i0xv\"\n}], [\"path\", {\n  d: \"M9 11h6\",\n  key: \"1fldmi\"\n}], [\"path\", {\n  d: \"M9 15h6\",\n  key: \"cctwl0\"\n}]];\nconst ReceiptJapaneseYen = createLucideIcon(\"receipt-japanese-yen\", __iconNode);\nexport { __iconNode, ReceiptJapaneseYen as default };\n//# sourceMappingURL=receipt-japanese-yen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}