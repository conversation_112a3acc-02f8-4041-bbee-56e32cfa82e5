{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"9\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1vctgf\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"hp0tcf\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"fkjjf6\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1tmaij\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"5\",\n  r: \"1\",\n  key: \"19l28e\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"f4zoj3\"\n}]];\nconst GripVertical = createLucideIcon(\"grip-vertical\", __iconNode);\nexport { __iconNode, GripVertical as default };\n//# sourceMappingURL=grip-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}