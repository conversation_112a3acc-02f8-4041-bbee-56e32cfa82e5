{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z\",\n  key: \"1fr9dc\"\n}], [\"path\", {\n  d: \"M8 10v4\",\n  key: \"tgpxqk\"\n}], [\"path\", {\n  d: \"M12 10v2\",\n  key: \"hh53o1\"\n}], [\"path\", {\n  d: \"M16 10v6\",\n  key: \"1d6xys\"\n}]];\nconst FolderKanban = createLucideIcon(\"folder-kanban\", __iconNode);\nexport { __iconNode, Folder<PERSON>anban as default };\n//# sourceMappingURL=folder-kanban.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}