{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 14h1v4\",\n  key: \"fy54vd\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"4\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"12vinp\"\n}]];\nconst Calendar1 = createLucideIcon(\"calendar-1\", __iconNode);\nexport { __iconNode, Calendar1 as default };\n//# sourceMappingURL=calendar-1.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}