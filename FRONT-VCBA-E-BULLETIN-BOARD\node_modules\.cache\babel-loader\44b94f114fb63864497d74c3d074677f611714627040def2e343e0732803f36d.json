{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 21h10\",\n  key: \"1b0cd5\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}]];\nconst TvMinimal = createLucideIcon(\"tv-minimal\", __iconNode);\nexport { __iconNode, TvMinimal as default };\n//# sourceMappingURL=tv-minimal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}