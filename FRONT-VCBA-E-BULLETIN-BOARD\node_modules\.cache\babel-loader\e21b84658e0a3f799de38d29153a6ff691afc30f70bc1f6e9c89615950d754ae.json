{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22v-5\",\n  key: \"sfixh4\"\n}], [\"path\", {\n  d: \"M14 19v-2\",\n  key: \"pdve8j\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M18 20v-3\",\n  key: \"uox2gk\"\n}], [\"path\", {\n  d: \"M2 13h20\",\n  key: \"5evz65\"\n}], [\"path\", {\n  d: \"M20 13V7l-5-5H6a2 2 0 0 0-2 2v9\",\n  key: \"1rnpe2\"\n}], [\"path\", {\n  d: \"M6 20v-3\",\n  key: \"c6pdcb\"\n}]];\nconst Shredder = createLucideIcon(\"shredder\", __iconNode);\nexport { __iconNode, Shredder as default };\n//# sourceMappingURL=shredder.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}