{"ast": null, "code": "import React,{useState}from'react';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{User,Settings as SettingsIcon,Lock,Bell,CheckCircle}from'lucide-react';import ProfilePictureUpload from'../../components/admin/ProfilePictureUpload';import{AdminAuthService}from'../../services/admin-auth.service';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Settings=()=>{const{user,checkAuthStatus}=useAdminAuth();const[activeTab,setActiveTab]=useState('profile');const[isUploadingPicture,setIsUploadingPicture]=useState(false);const tabs=[{key:'profile',label:'Profile Settings',icon:User},{key:'system',label:'System Settings',icon:SettingsIcon},{key:'security',label:'Security',icon:Lock},{key:'notifications',label:'Notifications',icon:Bell}];// Profile picture handlers\nconst handleProfilePictureUpload=async file=>{setIsUploadingPicture(true);try{await AdminAuthService.uploadProfilePicture(file);// Refresh user data to get updated profile picture\nawait checkAuthStatus();}catch(error){throw new Error(error.message||'Failed to upload profile picture');}finally{setIsUploadingPicture(false);}};const handleProfilePictureRemove=async()=>{setIsUploadingPicture(true);try{await AdminAuthService.removeProfilePicture();// Refresh user data to remove profile picture\nawait checkAuthStatus();}catch(error){throw new Error(error.message||'Failed to remove profile picture');}finally{setIsUploadingPicture(false);}};const renderProfileSettings=()=>{var _user$firstName,_user$lastName;return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"Profile Picture\"}),/*#__PURE__*/_jsx(ProfilePictureUpload,{currentPicture:user===null||user===void 0?void 0:user.profilePicture,userInitials:\"\".concat((user===null||user===void 0?void 0:(_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0))||'').concat((user===null||user===void 0?void 0:(_user$lastName=user.lastName)===null||_user$lastName===void 0?void 0:_user$lastName.charAt(0))||''),onUpload:handleProfilePictureUpload,onRemove:handleProfilePictureRemove,isLoading:isUploadingPicture})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"Personal Information\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"First Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.firstName,style:{width:'100%',padding:'0.75rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Last Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.lastName,style:{width:'100%',padding:'0.75rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{style:{gridColumn:'1 / -1'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",defaultValue:user===null||user===void 0?void 0:user.email,style:{width:'100%',padding:'0.75rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Department\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.department,style:{width:'100%',padding:'0.75rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Position\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.position,style:{width:'100%',padding:'0.75rem',border:'1px solid #e8f5e8',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'2rem',display:'flex',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{style:{background:'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 2rem',fontWeight:'600',cursor:'pointer'},children:\"Save Changes\"}),/*#__PURE__*/_jsx(\"button\",{style:{background:'none',border:'1px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 2rem',cursor:'pointer',color:'#6b7280'},children:\"Cancel\"})]})]})]});};const renderSystemSettings=()=>/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"General Settings\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Maintenance Mode\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Enable maintenance mode to restrict access\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#ccc',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Auto-approve Posts\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Automatically approve new posts without review\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#22c55e',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Email Notifications\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Send email notifications for important events\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#22c55e',transition:'0.4s',borderRadius:'34px'}})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#2d5016',fontSize:'1.25rem',fontWeight:'600'},children:\"System Information\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"System Version\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#374151'},children:\"v1.0.0\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Last Updated\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#374151'},children:\"June 28, 2025\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Database Status\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#22c55e'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16,color:\"#22c55e\"}),\"Connected\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem',marginBottom:'0.25rem'},children:\"Server Status\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'600',color:'#22c55e'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(CheckCircle,{size:16,color:\"#22c55e\"}),\"Online\"]})})]})]})]})]});const renderContent=()=>{switch(activeTab){case'profile':return renderProfileSettings();case'system':return renderSystemSettings();case'security':return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'4rem 2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Lock,{size:48,color:\"#2d5016\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#2d5016',fontSize:'1.5rem',fontWeight:'600',marginBottom:'0.5rem'},children:\"Security Settings\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280'},children:\"Security settings panel coming soon\"})]});case'notifications':return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'4rem 2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Bell,{size:48,color:\"#2d5016\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#2d5016',fontSize:'1.5rem',fontWeight:'600',marginBottom:'0.5rem'},children:\"Notification Settings\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280'},children:\"Notification preferences panel coming soon\"})]});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',padding:'1.5rem',marginBottom:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e8f5e8'},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',gap:'1rem',flexWrap:'wrap'},children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.key),style:{background:activeTab===tab.key?'linear-gradient(135deg, #22c55e 0%, #facc15 100%)':'transparent',color:activeTab===tab.key?'white':'#6b7280',border:activeTab===tab.key?'none':'1px solid #e8f5e8',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:'pointer',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.5rem',transition:'all 0.2s ease'},children:[/*#__PURE__*/_jsx(tab.icon,{size:16}),tab.label]},tab.key))})}),renderContent()]});};export default Settings;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}