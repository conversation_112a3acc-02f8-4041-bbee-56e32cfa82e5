{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 18h-5\",\n  key: \"95g1m2\"\n}], [\"path\", {\n  d: \"M18 14h-8\",\n  key: \"sponae\"\n}], [\"path\", {\n  d: \"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2\",\n  key: \"39pd36\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"10\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"aywv1n\"\n}]];\nconst Newspaper = createLucideIcon(\"newspaper\", __iconNode);\nexport { __iconNode, Newspaper as default };\n//# sourceMappingURL=newspaper.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}