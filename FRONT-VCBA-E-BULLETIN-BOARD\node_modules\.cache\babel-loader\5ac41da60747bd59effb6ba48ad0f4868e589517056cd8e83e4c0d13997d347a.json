{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 20a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2\",\n  key: \"1m57jg\"\n}], [\"path\", {\n  d: \"M8 18V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v14\",\n  key: \"1l99gc\"\n}], [\"path\", {\n  d: \"M10 20h4\",\n  key: \"ni2waw\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"1vifvg\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"20\",\n  r: \"2\",\n  key: \"ckkr5m\"\n}]];\nconst Luggage = createLucideIcon(\"luggage\", __iconNode);\nexport { __iconNode, Luggage as default };\n//# sourceMappingURL=luggage.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}