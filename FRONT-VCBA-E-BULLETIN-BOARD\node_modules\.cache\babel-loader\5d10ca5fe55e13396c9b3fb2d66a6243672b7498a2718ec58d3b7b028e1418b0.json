{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 3 4 7l4 4\",\n  key: \"9rb6wj\"\n}], [\"path\", {\n  d: \"M4 7h16\",\n  key: \"6tx8e3\"\n}], [\"path\", {\n  d: \"m16 21 4-4-4-4\",\n  key: \"siv7j2\"\n}], [\"path\", {\n  d: \"M20 17H4\",\n  key: \"h6l3hr\"\n}]];\nconst ArrowLeftRight = createLucideIcon(\"arrow-left-right\", __iconNode);\nexport { __iconNode, ArrowLeftRight as default };\n//# sourceMappingURL=arrow-left-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}