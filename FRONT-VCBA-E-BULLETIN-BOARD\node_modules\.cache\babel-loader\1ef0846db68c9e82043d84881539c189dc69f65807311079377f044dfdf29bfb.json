{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 17 5-5-5-5\",\n  key: \"nf172w\"\n}], [\"path\", {\n  d: \"M4 18v-2a4 4 0 0 1 4-4h12\",\n  key: \"jmiej9\"\n}]];\nconst Forward = createLucideIcon(\"forward\", __iconNode);\nexport { __iconNode, Forward as default };\n//# sourceMappingURL=forward.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}