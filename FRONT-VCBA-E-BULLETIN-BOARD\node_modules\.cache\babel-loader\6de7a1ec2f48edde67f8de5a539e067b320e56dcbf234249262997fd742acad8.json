{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"12\",\n  height: \"12\",\n  x: \"2\",\n  y: \"10\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"6agr2n\"\n}], [\"path\", {\n  d: \"m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6\",\n  key: \"1o487t\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"M10 14h.01\",\n  key: \"ssrbsk\"\n}], [\"path\", {\n  d: \"M15 6h.01\",\n  key: \"cblpky\"\n}], [\"path\", {\n  d: \"M18 9h.01\",\n  key: \"2061c0\"\n}]];\nconst Dices = createLucideIcon(\"dices\", __iconNode);\nexport { __iconNode, Dices as default };\n//# sourceMappingURL=dices.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}