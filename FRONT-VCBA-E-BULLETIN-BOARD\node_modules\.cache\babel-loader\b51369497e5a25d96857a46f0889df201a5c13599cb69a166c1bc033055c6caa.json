{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate,useLocation}from'react-router-dom';import{useAdminAuth}from'../../../contexts/AdminAuthContext';import{VALIDATION_RULES}from'../../../config/constants';import'./AdminLogin.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLogin=()=>{const navigate=useNavigate();const location=useLocation();const{login,error,isLoading,clearError}=useAdminAuth();const[formData,setFormData]=useState({email:'',password:'',remember:false});const[formErrors,setFormErrors]=useState({});const[showPassword,setShowPassword]=useState(false);const validateForm=()=>{const errors={};if(!formData.email.trim()){errors.email='Email is required';}else if(!VALIDATION_RULES.EMAIL.PATTERN.test(formData.email)){errors.email='Please enter a valid email address';}if(!formData.password){errors.password='Password is required';}setFormErrors(errors);return Object.keys(errors).length===0;};const handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));if(formErrors[name]){setFormErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:undefined}));}if(error){clearError();}};const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{var _location$state,_location$state$from;await login({email:formData.email,password:formData.password,userType:'admin'});const from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||'/admin/dashboard';navigate(from,{replace:true});}catch(err){console.error('Login failed:',err);}};const togglePasswordVisibility=()=>{setShowPassword(!showPassword);};const handleForceLogout=()=>{console.log('🚪 AdminLogin - Force logout clicked');// Clear all possible authentication data\nlocalStorage.clear();sessionStorage.clear();// Clear cookies by setting them to expire\ndocument.cookie.split(\";\").forEach(function(c){document.cookie=c.replace(/^ +/,\"\").replace(/=.*/,\"=;expires=\"+new Date().toUTCString()+\";path=/\");});// Reload the page to ensure clean state\nwindow.location.reload();};return/*#__PURE__*/_jsx(\"div\",{className:\"admin-login\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__form-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__form-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__form-header\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",className:\"admin-login__form-logo\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"admin-login__form-title\",children:\"Villamor College of Business and Arts, Inc.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"admin-login__form-subtitle\",children:\"Administrator Portal\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__error\",children:[/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})}),error]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"admin-login__form\",noValidate:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",className:\"admin-login__label\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,placeholder:\"Enter your email address\",className:\"admin-login__input \".concat(formErrors.email?'error':''),disabled:isLoading,autoComplete:\"email\",required:true}),formErrors.email&&/*#__PURE__*/_jsx(\"span\",{className:\"admin-login__error-text\",children:formErrors.email})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"admin-login__label\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:showPassword?'text':'password',id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,placeholder:\"Enter your password\",className:\"admin-login__input \".concat(formErrors.password?'error':''),disabled:isLoading,autoComplete:\"current-password\",required:true}),formErrors.password&&/*#__PURE__*/_jsx(\"span\",{className:\"admin-login__error-text\",children:formErrors.password})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"admin-login__submit-btn\",disabled:isLoading,children:isLoading?'Signing in...':'Sign In'})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__info-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__info-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__school-info\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/ebb1.png\",alt:\"E-Bulletin Board Logo\",className:\"admin-login__school-logo\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"admin-login__school-name\",children:\"VCBA E-BULLETIN BOARD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"admin-login__school-description\",children:\"Villamor College of Business and Arts, Inc.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/megaphone.png\",alt:\"Categorized Contents\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Categorized Contents\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Organized announcements by departments, clubs, events, and more\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/message.png\",alt:\"Centralized Platform\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Centralized Platform\"}),/*#__PURE__*/_jsx(\"p\",{children:\"All school announcements in one place \\u2014 accessible anytime, anywhere\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"admin-login__feature-icon\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/icons/heart.png\",alt:\"User-Friendly Environment\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login__feature-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"User-Friendly Environment\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Simple design with smooth navigation and accessibility support\"})]})]})]})]})})})]})});};export default AdminLogin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}