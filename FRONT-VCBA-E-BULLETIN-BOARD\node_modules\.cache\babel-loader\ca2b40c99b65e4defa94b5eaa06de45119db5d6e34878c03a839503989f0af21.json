{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"16\",\n  key: \"10p56q\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"16\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1jonct\"\n}]];\nconst BadgePlus = createLucideIcon(\"badge-plus\", __iconNode);\nexport { __iconNode, BadgePlus as default };\n//# sourceMappingURL=badge-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}