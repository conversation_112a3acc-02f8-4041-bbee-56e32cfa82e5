{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1lh9wr\"\n}], [\"path\", {\n  d: \"M6 9v12\",\n  key: \"1sc30k\"\n}], [\"path\", {\n  d: \"m21 3-6 6\",\n  key: \"16nqsk\"\n}], [\"path\", {\n  d: \"m21 9-6-6\",\n  key: \"9j17rh\"\n}], [\"path\", {\n  d: \"M18 11.5V15\",\n  key: \"65xf6f\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}]];\nconst GitPullRequestClosed = createLucideIcon(\"git-pull-request-closed\", __iconNode);\nexport { __iconNode, GitPullRequestClosed as default };\n//# sourceMappingURL=git-pull-request-closed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}