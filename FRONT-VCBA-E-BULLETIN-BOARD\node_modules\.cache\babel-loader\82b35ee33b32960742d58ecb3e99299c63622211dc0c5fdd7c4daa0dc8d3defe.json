{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}], [\"path\", {\n  d: \"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662\",\n  key: \"154egf\"\n}]];\nconst CircleUser = createLucideIcon(\"circle-user\", __iconNode);\nexport { __iconNode, CircleUser as default };\n//# sourceMappingURL=circle-user.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}