{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 7V5a1 1 0 0 0-1-1H6.5a.5.5 0 0 0-.4.8l4.5 6a2 2 0 0 1 0 2.4l-4.5 6a.5.5 0 0 0 .4.8H17a1 1 0 0 0 1-1v-2\",\n  key: \"wuwx1p\"\n}]];\nconst Sigma = createLucideIcon(\"sigma\", __iconNode);\nexport { __iconNode, Sigma as default };\n//# sourceMappingURL=sigma.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}