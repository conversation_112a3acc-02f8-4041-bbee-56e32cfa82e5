{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\layout\\\\AdminLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport AdminSidebar from './AdminSidebar';\nimport AdminHeader from './AdminHeader';\nimport '../admin.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: sidebarOpen,\n      onToggle: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        marginLeft: sidebarOpen ? '280px' : '80px',\n        transition: 'margin-left 0.3s ease',\n        minHeight: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AdminHeader, {\n        onToggleSidebar: toggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        style: {\n          flex: 1,\n          padding: '2rem',\n          background: 'transparent',\n          overflow: 'auto'\n        },\n        children: children || /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 24\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLayout, \"fX/xBG9dqPCXDXt8dvZeCgFoxdw=\");\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AdminLayout", "children", "_s", "sidebarOpen", "setSidebarOpen", "toggleSidebar", "style", "display", "minHeight", "background", "isOpen", "onToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "flexDirection", "marginLeft", "transition", "onToggleSidebar", "padding", "overflow", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/layout/AdminLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport AdminSidebar from './AdminSidebar';\nimport AdminHeader from './AdminHeader';\nimport '../admin.css';\n\ninterface AdminLayoutProps {\n  children?: React.ReactNode;\n}\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  return (\n    <div style={{ \n      display: 'flex', \n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n    }}>\n      {/* Sidebar */}\n      <AdminSidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />\n      \n      {/* Main Content Area */}\n      <div style={{\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        marginLeft: sidebarOpen ? '280px' : '80px',\n        transition: 'margin-left 0.3s ease',\n        minHeight: '100vh'\n      }}>\n        {/* Header */}\n        <AdminHeader onToggleSidebar={toggleSidebar} />\n        \n        {/* Page Content */}\n        <main style={{\n          flex: 1,\n          padding: '2rem',\n          background: 'transparent',\n          overflow: 'auto'\n        }}>\n          {children || <Outlet />}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtB,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEJ,OAAA;IAAKO,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE;IACd,CAAE;IAAAR,QAAA,gBAEAF,OAAA,CAACH,YAAY;MAACc,MAAM,EAAEP,WAAY;MAACQ,QAAQ,EAAEN;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG9DhB,OAAA;MAAKO,KAAK,EAAE;QACVU,IAAI,EAAE,CAAC;QACPT,OAAO,EAAE,MAAM;QACfU,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAEf,WAAW,GAAG,OAAO,GAAG,MAAM;QAC1CgB,UAAU,EAAE,uBAAuB;QACnCX,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,gBAEAF,OAAA,CAACF,WAAW;QAACuB,eAAe,EAAEf;MAAc;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG/ChB,OAAA;QAAMO,KAAK,EAAE;UACXU,IAAI,EAAE,CAAC;UACPK,OAAO,EAAE,MAAM;UACfZ,UAAU,EAAE,aAAa;UACzBa,QAAQ,EAAE;QACZ,CAAE;QAAArB,QAAA,EACCA,QAAQ,iBAAIF,OAAA,CAACJ,MAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CAxCIF,WAAuC;AAAAuB,EAAA,GAAvCvB,WAAuC;AA0C7C,eAAeA,WAAW;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}