{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8\",\n  key: \"10dyio\"\n}], [\"path\", {\n  d: \"M10 19v-3.96 3.15\",\n  key: \"1irgej\"\n}], [\"path\", {\n  d: \"M7 19h5\",\n  key: \"qswx4l\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"16\",\n  y: \"12\",\n  rx: \"2\",\n  key: \"1egngj\"\n}]];\nconst MonitorSmartphone = createLucideIcon(\"monitor-smartphone\", __iconNode);\nexport { __iconNode, MonitorSmartphone as default };\n//# sourceMappingURL=monitor-smartphone.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}