{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11.9 12.1 4.514-4.514\",\n  key: \"109xqo\"\n}], [\"path\", {\n  d: \"M20.1 2.3a1 1 0 0 0-1.4 0l-1.114 1.114A2 2 0 0 0 17 4.828v1.344a2 2 0 0 1-.586 1.414A2 2 0 0 1 17.828 7h1.344a2 2 0 0 0 1.414-.586L21.7 5.3a1 1 0 0 0 0-1.4z\",\n  key: \"txyc8t\"\n}], [\"path\", {\n  d: \"m6 16 2 2\",\n  key: \"16qmzd\"\n}], [\"path\", {\n  d: \"M8.23 9.85A3 3 0 0 1 11 8a5 5 0 0 1 5 5 3 3 0 0 1-1.85 2.77l-.92.38A2 2 0 0 0 12 18a4 4 0 0 1-4 4 6 6 0 0 1-6-6 4 4 0 0 1 4-4 2 2 0 0 0 1.85-1.23z\",\n  key: \"1de1vg\"\n}]];\nconst Guitar = createLucideIcon(\"guitar\", __iconNode);\nexport { __iconNode, Guitar as default };\n//# sourceMappingURL=guitar.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}