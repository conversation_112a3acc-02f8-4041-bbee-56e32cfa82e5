{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 9 3-3 3 3\",\n  key: \"1ltn5i\"\n}], [\"path\", {\n  d: \"M13 18H7a2 2 0 0 1-2-2V6\",\n  key: \"1r6tfw\"\n}], [\"path\", {\n  d: \"m22 15-3 3-3-3\",\n  key: \"4rnwn2\"\n}], [\"path\", {\n  d: \"M11 6h6a2 2 0 0 1 2 2v10\",\n  key: \"2f72bc\"\n}]];\nconst Repeat2 = createLucideIcon(\"repeat-2\", __iconNode);\nexport { __iconNode, Repeat2 as default };\n//# sourceMappingURL=repeat-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}