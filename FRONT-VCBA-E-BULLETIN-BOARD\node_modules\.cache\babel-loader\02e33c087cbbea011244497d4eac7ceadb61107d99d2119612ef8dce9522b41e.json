{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\",\n  key: \"pzmjnu\"\n}], [\"path\", {\n  d: \"M21.21 15.89A10 10 0 1 1 8 2.83\",\n  key: \"k2fpak\"\n}]];\nconst ChartPie = createLucideIcon(\"chart-pie\", __iconNode);\nexport { __iconNode, ChartPie as default };\n//# sourceMappingURL=chart-pie.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}