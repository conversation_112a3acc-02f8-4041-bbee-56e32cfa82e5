{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 11 4.08 10.35a1 1 0 0 0 1.84 0L17 11\",\n  key: \"1v6356\"\n}], [\"path\", {\n  d: \"M17 7A5 5 0 0 0 7 7\",\n  key: \"151p3v\"\n}], [\"path\", {\n  d: \"M17 7a2 2 0 0 1 0 4H7a2 2 0 0 1 0-4\",\n  key: \"1sdaij\"\n}]];\nconst IceCreamCone = createLucideIcon(\"ice-cream-cone\", __iconNode);\nexport { __iconNode, IceCreamCone as default };\n//# sourceMappingURL=ice-cream-cone.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}