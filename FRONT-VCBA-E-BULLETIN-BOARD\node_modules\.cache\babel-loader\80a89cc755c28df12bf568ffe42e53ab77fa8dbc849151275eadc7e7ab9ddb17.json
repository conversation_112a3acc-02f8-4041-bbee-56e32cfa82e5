{"ast": null, "code": "import { adminHttpClient } from './api.service';\nimport { API_ENDPOINTS } from '../config/constants';\nclass ArchiveService {\n  // Get archived announcements\n  async getArchivedAnnouncements(filters = {}, pagination = {}) {\n    const params = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n    return adminHttpClient.get(API_ENDPOINTS.ARCHIVE.ANNOUNCEMENTS, params);\n  }\n\n  // Get archived calendar events\n  async getArchivedCalendarEvents(filters = {}, pagination = {}) {\n    const params = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n    return adminHttpClient.get(API_ENDPOINTS.ARCHIVE.CALENDAR_EVENTS, params);\n  }\n\n  // Get archived students\n  async getArchivedStudents(filters = {}, pagination = {}) {\n    const params = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n    return adminHttpClient.get(API_ENDPOINTS.ARCHIVE.STUDENTS, params);\n  }\n\n  // Get archive statistics\n  async getArchiveStatistics() {\n    return adminHttpClient.get(API_ENDPOINTS.ARCHIVE.STATISTICS);\n  }\n\n  // Restore archived announcement\n  async restoreAnnouncement(announcementId) {\n    return adminHttpClient.put(API_ENDPOINTS.ARCHIVE.RESTORE_ANNOUNCEMENT(announcementId.toString()));\n  }\n\n  // Restore archived calendar event\n  async restoreCalendarEvent(eventId) {\n    return adminHttpClient.put(API_ENDPOINTS.ARCHIVE.RESTORE_CALENDAR_EVENT(eventId.toString()));\n  }\n\n  // Restore archived student\n  async restoreStudent(studentId) {\n    return adminHttpClient.put(API_ENDPOINTS.ARCHIVE.RESTORE_STUDENT(studentId.toString()));\n  }\n\n  // Permanently delete announcement\n  async permanentlyDeleteAnnouncement(announcementId) {\n    return adminHttpClient.delete(API_ENDPOINTS.ARCHIVE.PERMANENT_DELETE_ANNOUNCEMENT(announcementId.toString()));\n  }\n}\nexport const archiveService = new ArchiveService();\nexport default archiveService;", "map": {"version": 3, "names": ["adminHttpClient", "API_ENDPOINTS", "ArchiveService", "getArchivedAnnouncements", "filters", "pagination", "params", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "get", "ARCHIVE", "ANNOUNCEMENTS", "getArchivedCalendarEvents", "CALENDAR_EVENTS", "getArchivedStudents", "STUDENTS", "getArchiveStatistics", "STATISTICS", "restoreAnnouncement", "announcementId", "put", "RESTORE_ANNOUNCEMENT", "toString", "restoreCalendarEvent", "eventId", "RESTORE_CALENDAR_EVENT", "restoreStudent", "studentId", "RESTORE_STUDENT", "permanentlyDeleteAnnouncement", "delete", "PERMANENT_DELETE_ANNOUNCEMENT", "archiveService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/archiveService.ts"], "sourcesContent": ["import { adminHttpClient } from './api.service';\nimport { API_ENDPOINTS } from '../config/constants';\nimport { ApiResponse } from '../types';\n\nexport interface ArchiveFilters {\n  search?: string;\n  category_id?: number;\n  subcategory_id?: number;\n  posted_by?: number;\n  created_by?: number;\n  grade_level?: number;\n  section?: string;\n  start_date?: string;\n  end_date?: string;\n}\n\nexport interface ArchivePagination {\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface ArchiveResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nexport interface ArchivedAnnouncement {\n  announcement_id: number;\n  title: string;\n  content: string;\n  image_path?: string;\n  category_id: number;\n  subcategory_id?: number;\n  posted_by: number;\n  grade_level?: number;\n  status: string;\n  is_pinned: boolean;\n  is_alert: boolean;\n  allow_comments: boolean;\n  allow_sharing: boolean;\n  scheduled_publish_at?: string;\n  published_at?: string;\n  archived_at?: string;\n  deleted_at: string;\n  view_count: number;\n  created_at: string;\n  updated_at: string;\n  category_name: string;\n  category_color: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  author_name: string;\n}\n\nexport interface ArchivedCalendarEvent {\n  calendar_id: number;\n  title: string;\n  description?: string;\n  event_date: string;\n  end_date?: string;\n  event_time?: string;\n  end_time?: string;\n  location?: string;\n  category_id: number;\n  subcategory_id?: number;\n  created_by: number;\n  is_recurring: boolean;\n  recurrence_pattern?: string;\n  is_active: boolean;\n  is_published: boolean;\n  allow_comments: boolean;\n  is_alert: boolean;\n  deleted_at: string;\n  created_at: string;\n  updated_at: string;\n  category_name: string;\n  category_color: string;\n  subcategory_name?: string;\n  subcategory_color?: string;\n  created_by_name: string;\n}\n\nexport interface ArchivedStudent {\n  student_id: number;\n  email: string;\n  student_number: string;\n  is_active: boolean;\n  last_login?: string;\n  created_by: number;\n  created_by_name: string;\n  created_at: string;\n  updated_at: string;\n  profile?: {\n    profile_id: number;\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number: string;\n    grade_level: number;\n    section: string;\n    parent_guardian_name?: string;\n    parent_guardian_phone?: string;\n    address?: string;\n    profile_picture?: string;\n    created_at: string;\n    updated_at: string;\n  };\n}\n\nexport interface ArchiveStatistics {\n  announcements: number;\n  calendar_events: number;\n  students: number;\n  total: number;\n}\n\nclass ArchiveService {\n  // Get archived announcements\n  async getArchivedAnnouncements(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedAnnouncement>>> {\n    const params: Record<string, any> = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n\n    return adminHttpClient.get<ArchiveResponse<ArchivedAnnouncement>>(API_ENDPOINTS.ARCHIVE.ANNOUNCEMENTS, params);\n  }\n\n  // Get archived calendar events\n  async getArchivedCalendarEvents(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedCalendarEvent>>> {\n    const params: Record<string, any> = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n\n    return adminHttpClient.get<ArchiveResponse<ArchivedCalendarEvent>>(API_ENDPOINTS.ARCHIVE.CALENDAR_EVENTS, params);\n  }\n\n  // Get archived students\n  async getArchivedStudents(\n    filters: ArchiveFilters = {},\n    pagination: ArchivePagination = {}\n  ): Promise<ApiResponse<ArchiveResponse<ArchivedStudent>>> {\n    const params: Record<string, any> = {};\n\n    // Add filters\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value;\n      }\n    });\n\n    // Add pagination\n    Object.entries(pagination).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params[key] = value;\n      }\n    });\n\n    return adminHttpClient.get<ArchiveResponse<ArchivedStudent>>(API_ENDPOINTS.ARCHIVE.STUDENTS, params);\n  }\n\n  // Get archive statistics\n  async getArchiveStatistics(): Promise<ApiResponse<ArchiveStatistics>> {\n    return adminHttpClient.get<ArchiveStatistics>(API_ENDPOINTS.ARCHIVE.STATISTICS);\n  }\n\n  // Restore archived announcement\n  async restoreAnnouncement(announcementId: number): Promise<ApiResponse<{ announcement: ArchivedAnnouncement }>> {\n    return adminHttpClient.put<{ announcement: ArchivedAnnouncement }>(API_ENDPOINTS.ARCHIVE.RESTORE_ANNOUNCEMENT(announcementId.toString()));\n  }\n\n  // Restore archived calendar event\n  async restoreCalendarEvent(eventId: number): Promise<ApiResponse<void>> {\n    return adminHttpClient.put<void>(API_ENDPOINTS.ARCHIVE.RESTORE_CALENDAR_EVENT(eventId.toString()));\n  }\n\n  // Restore archived student\n  async restoreStudent(studentId: number): Promise<ApiResponse<{ student: ArchivedStudent }>> {\n    return adminHttpClient.put<{ student: ArchivedStudent }>(API_ENDPOINTS.ARCHIVE.RESTORE_STUDENT(studentId.toString()));\n  }\n\n  // Permanently delete announcement\n  async permanentlyDeleteAnnouncement(announcementId: number): Promise<ApiResponse<void>> {\n    return adminHttpClient.delete<void>(API_ENDPOINTS.ARCHIVE.PERMANENT_DELETE_ANNOUNCEMENT(announcementId.toString()));\n  }\n}\n\nexport const archiveService = new ArchiveService();\nexport default archiveService;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,eAAe;AAC/C,SAASC,aAAa,QAAQ,qBAAqB;AA8HnD,MAAMC,cAAc,CAAC;EACnB;EACA,MAAMC,wBAAwBA,CAC5BC,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EAC2B;IAC7D,MAAMC,MAA2B,GAAG,CAAC,CAAC;;IAEtC;IACAC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;;IAEF;IACAJ,MAAM,CAACC,OAAO,CAACH,UAAU,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;IAEF,OAAOX,eAAe,CAACa,GAAG,CAAwCZ,aAAa,CAACa,OAAO,CAACC,aAAa,EAAET,MAAM,CAAC;EAChH;;EAEA;EACA,MAAMU,yBAAyBA,CAC7BZ,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EAC4B;IAC9D,MAAMC,MAA2B,GAAG,CAAC,CAAC;;IAEtC;IACAC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;;IAEF;IACAJ,MAAM,CAACC,OAAO,CAACH,UAAU,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;IAEF,OAAOX,eAAe,CAACa,GAAG,CAAyCZ,aAAa,CAACa,OAAO,CAACG,eAAe,EAAEX,MAAM,CAAC;EACnH;;EAEA;EACA,MAAMY,mBAAmBA,CACvBd,OAAuB,GAAG,CAAC,CAAC,EAC5BC,UAA6B,GAAG,CAAC,CAAC,EACsB;IACxD,MAAMC,MAA2B,GAAG,CAAC,CAAC;;IAEtC;IACAC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;;IAEF;IACAJ,MAAM,CAACC,OAAO,CAACH,UAAU,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCL,MAAM,CAACI,GAAG,CAAC,GAAGC,KAAK;MACrB;IACF,CAAC,CAAC;IAEF,OAAOX,eAAe,CAACa,GAAG,CAAmCZ,aAAa,CAACa,OAAO,CAACK,QAAQ,EAAEb,MAAM,CAAC;EACtG;;EAEA;EACA,MAAMc,oBAAoBA,CAAA,EAA4C;IACpE,OAAOpB,eAAe,CAACa,GAAG,CAAoBZ,aAAa,CAACa,OAAO,CAACO,UAAU,CAAC;EACjF;;EAEA;EACA,MAAMC,mBAAmBA,CAACC,cAAsB,EAAgE;IAC9G,OAAOvB,eAAe,CAACwB,GAAG,CAAyCvB,aAAa,CAACa,OAAO,CAACW,oBAAoB,CAACF,cAAc,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC3I;;EAEA;EACA,MAAMC,oBAAoBA,CAACC,OAAe,EAA8B;IACtE,OAAO5B,eAAe,CAACwB,GAAG,CAAOvB,aAAa,CAACa,OAAO,CAACe,sBAAsB,CAACD,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpG;;EAEA;EACA,MAAMI,cAAcA,CAACC,SAAiB,EAAsD;IAC1F,OAAO/B,eAAe,CAACwB,GAAG,CAA+BvB,aAAa,CAACa,OAAO,CAACkB,eAAe,CAACD,SAAS,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvH;;EAEA;EACA,MAAMO,6BAA6BA,CAACV,cAAsB,EAA8B;IACtF,OAAOvB,eAAe,CAACkC,MAAM,CAAOjC,aAAa,CAACa,OAAO,CAACqB,6BAA6B,CAACZ,cAAc,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrH;AACF;AAEA,OAAO,MAAMU,cAAc,GAAG,IAAIlC,cAAc,CAAC,CAAC;AAClD,eAAekC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}