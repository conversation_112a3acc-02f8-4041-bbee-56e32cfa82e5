{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h9\",\n  key: \"t2du7b\"\n}], [\"path\", {\n  d: \"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z\",\n  key: \"1ykcvy\"\n}], [\"path\", {\n  d: \"m15 5 3 3\",\n  key: \"1w25hb\"\n}]];\nconst PencilLine = createLucideIcon(\"pencil-line\", __iconNode);\nexport { __iconNode, PencilLine as default };\n//# sourceMappingURL=pencil-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}