{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551\",\n  key: \"1miecu\"\n}]];\nconst Paperclip = createLucideIcon(\"paperclip\", __iconNode);\nexport { __iconNode, Paperclip as default };\n//# sourceMappingURL=paperclip.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}