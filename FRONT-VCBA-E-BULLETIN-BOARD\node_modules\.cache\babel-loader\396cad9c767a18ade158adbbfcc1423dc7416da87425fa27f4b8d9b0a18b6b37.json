{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 16V4a2 2 0 0 1 2-2h11\",\n  key: \"spzkk5\"\n}], [\"path\", {\n  d: \"M22 18H11a2 2 0 1 0 0 4h10.5a.5.5 0 0 0 .5-.5v-15a.5.5 0 0 0-.5-.5H11a2 2 0 0 0-2 2v12\",\n  key: \"1wz07i\"\n}], [\"path\", {\n  d: \"M5 14H4a2 2 0 1 0 0 4h1\",\n  key: \"16gqf9\"\n}]];\nconst BookCopy = createLucideIcon(\"book-copy\", __iconNode);\nexport { __iconNode, BookCopy as default };\n//# sourceMappingURL=book-copy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}