{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 8 2 2-2 2 2 2-2 2\",\n  key: \"sv1b1\"\n}], [\"path\", {\n  d: \"m22 8-2 2 2 2-2 2 2 2\",\n  key: \"101i4y\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"14\",\n  x: \"8\",\n  y: \"5\",\n  rx: \"1\",\n  key: \"1oyrl4\"\n}]];\nconst Vibrate = createLucideIcon(\"vibrate\", __iconNode);\nexport { __iconNode, Vibrate as default };\n//# sourceMappingURL=vibrate.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}