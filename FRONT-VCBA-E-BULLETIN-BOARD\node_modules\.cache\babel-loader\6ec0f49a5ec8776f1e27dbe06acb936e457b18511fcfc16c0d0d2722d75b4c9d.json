{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M8 15h8\",\n  key: \"45n4r\"\n}], [\"path\", {\n  d: \"M8 9h2\",\n  key: \"1g203m\"\n}], [\"path\", {\n  d: \"M14 9h2\",\n  key: \"116p9w\"\n}]];\nconst Annoyed = createLucideIcon(\"annoyed\", __iconNode);\nexport { __iconNode, Annoyed as default };\n//# sourceMappingURL=annoyed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}