{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 15V9\",\n  key: \"1lckn7\"\n}], [\"path\", {\n  d: \"M14 15V9\",\n  key: \"1muqhk\"\n}], [\"path\", {\n  d: \"M2.586 16.726A2 2 0 0 1 2 15.312V8.688a2 2 0 0 1 .586-1.414l4.688-4.688A2 2 0 0 1 8.688 2h6.624a2 2 0 0 1 1.414.586l4.688 4.688A2 2 0 0 1 22 8.688v6.624a2 2 0 0 1-.586 1.414l-4.688 4.688a2 2 0 0 1-1.414.586H8.688a2 2 0 0 1-1.414-.586z\",\n  key: \"2d38gg\"\n}]];\nconst OctagonPause = createLucideIcon(\"octagon-pause\", __iconNode);\nexport { __iconNode, OctagonPause as default };\n//# sourceMappingURL=octagon-pause.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}