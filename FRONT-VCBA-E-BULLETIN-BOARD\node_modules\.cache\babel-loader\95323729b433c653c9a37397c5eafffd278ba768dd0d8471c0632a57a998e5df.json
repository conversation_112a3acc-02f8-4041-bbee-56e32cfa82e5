{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 15a6.5 6.5 0 0 1 7 0 6.5 6.5 0 0 0 7 0\",\n  key: \"yrdkhy\"\n}], [\"path\", {\n  d: \"M5 9a6.5 6.5 0 0 1 7 0 6.5 6.5 0 0 0 7 0\",\n  key: \"gzkvyz\"\n}]];\nconst EqualApproximately = createLucideIcon(\"equal-approximately\", __iconNode);\nexport { __iconNode, EqualApproximately as default };\n//# sourceMappingURL=equal-approximately.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}