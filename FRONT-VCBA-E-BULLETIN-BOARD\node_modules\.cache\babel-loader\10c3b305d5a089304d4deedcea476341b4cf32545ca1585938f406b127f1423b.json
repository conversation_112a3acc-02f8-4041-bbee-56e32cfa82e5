{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 7.601-5.994 8.19a1 1 0 0 0 .1 1.298l.817.818a1 1 0 0 0 1.314.087L15.09 12\",\n  key: \"80a601\"\n}], [\"path\", {\n  d: \"M16.5 21.174C15.5 20.5 14.372 20 13 20c-2.058 0-3.928 2.356-6 2-2.072-.356-2.775-3.369-1.5-4.5\",\n  key: \"j0ngtp\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"7\",\n  r: \"5\",\n  key: \"d08jfb\"\n}]];\nconst MicVocal = createLucideIcon(\"mic-vocal\", __iconNode);\nexport { __iconNode, MicVocal as default };\n//# sourceMappingURL=mic-vocal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}