{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 5v11\",\n  key: \"mdvv1e\"\n}], [\"path\", {\n  d: \"M12 5v6\",\n  key: \"14ar3b\"\n}], [\"path\", {\n  d: \"M18 5v14\",\n  key: \"7ji314\"\n}]];\nconst Kanban = createLucideIcon(\"kanban\", __iconNode);\nexport { __iconNode, Kanban as default };\n//# sourceMappingURL=kanban.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}