{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 10v4h4\",\n  key: \"172dkj\"\n}], [\"path\", {\n  d: \"m11 14 1.535-1.605a5 5 0 0 1 8 1.5\",\n  key: \"vu0qm5\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"m21 18-1.535 1.605a5 5 0 0 1-8-1.5\",\n  key: \"1qgeyt\"\n}], [\"path\", {\n  d: \"M21 22v-4h-4\",\n  key: \"hrummi\"\n}], [\"path\", {\n  d: \"M21 8.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h4.3\",\n  key: \"mctw84\"\n}], [\"path\", {\n  d: \"M3 10h4\",\n  key: \"1el30a\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}]];\nconst CalendarSync = createLucideIcon(\"calendar-sync\", __iconNode);\nexport { __iconNode, CalendarSync as default };\n//# sourceMappingURL=calendar-sync.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}