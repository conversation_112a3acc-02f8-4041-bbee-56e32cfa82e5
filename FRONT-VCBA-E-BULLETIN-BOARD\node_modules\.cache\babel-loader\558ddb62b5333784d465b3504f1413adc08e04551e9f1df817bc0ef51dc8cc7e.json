{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4.5 3h15\",\n  key: \"c7n0jr\"\n}], [\"path\", {\n  d: \"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3\",\n  key: \"m1uhx7\"\n}], [\"path\", {\n  d: \"M6 14h12\",\n  key: \"4cwo0f\"\n}]];\nconst Beaker = createLucideIcon(\"beaker\", __iconNode);\nexport { __iconNode, Beaker as default };\n//# sourceMappingURL=beaker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}