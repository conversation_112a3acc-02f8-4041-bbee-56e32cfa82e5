{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.75 7.09a3 3 0 0 1 2.16 2.16\",\n  key: \"1d4wjd\"\n}], [\"path\", {\n  d: \"M17.072 17.072c-1.634 2.17-3.527 3.912-4.471 4.727a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 1.432-4.568\",\n  key: \"12yil7\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8.475 2.818A8 8 0 0 1 20 10c0 1.183-.31 2.377-.81 3.533\",\n  key: \"lhrkcz\"\n}], [\"path\", {\n  d: \"M9.13 9.13a3 3 0 0 0 3.74 3.74\",\n  key: \"13wojd\"\n}]];\nconst MapPinOff = createLucideIcon(\"map-pin-off\", __iconNode);\nexport { __iconNode, MapPinOff as default };\n//# sourceMappingURL=map-pin-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}