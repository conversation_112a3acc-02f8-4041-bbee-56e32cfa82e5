{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"ynyp8z\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"10\",\n  y2: \"10\",\n  key: \"1b3vmo\"\n}]];\nconst CreditCard = createLucideIcon(\"credit-card\", __iconNode);\nexport { __iconNode, CreditCard as default };\n//# sourceMappingURL=credit-card.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}