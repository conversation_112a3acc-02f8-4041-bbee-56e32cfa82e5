# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.8](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.7...v1.0.8) - 2025-03-14

### Commits

- [actions] split out node 10-20, and 20+ [`14cb785`](https://github.com/es-shims/Array.prototype.reduce/commit/14cb78573734e36e5f15402c01eb0f9256d86a30)
- [Dev <PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `has-strict-mode`, `tape` [`1d3d206`](https://github.com/es-shims/Array.prototype.reduce/commit/1d3d206245d643e57addd79fe9c398fc2b759a1e)
- [Deps] update `call-bind`, `es-abstract`, `es-object-atoms`, `is-string` [`18f0dcc`](https://github.com/es-shims/Array.prototype.reduce/commit/18f0dcc6ddebf1fcb3f911c0098e66996b765c22)
- [Refactor] use `call-bound` directly [`f64c7db`](https://github.com/es-shims/Array.prototype.reduce/commit/f64c7dbbbd1c6aa64a31cb1bdb013d431bdec24a)
- [Tests] replace `aud` with `npm audit` [`c79219f`](https://github.com/es-shims/Array.prototype.reduce/commit/c79219f77ef470bc3027a11c29cccf612295b77d)
- [Dev Deps] add missing peer dep [`baae754`](https://github.com/es-shims/Array.prototype.reduce/commit/baae754ac63450953d5a2d7520b380c3f0f12649)

## [v1.0.7](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.6...v1.0.7) - 2024-03-20

### Commits

- [Deps] update `call-bind`, `define-properties`, `es-abstract` [`4c68200`](https://github.com/es-shims/Array.prototype.reduce/commit/4c68200a2ea24a919dfd3bd89c01953c34f85050)
- [actions] use reusable workflows [`f755af3`](https://github.com/es-shims/Array.prototype.reduce/commit/f755af3f6932130328b74c722bd4ff3db179eb7a)
- [Refactor] use `es-object-atoms` where possible [`d64f55f`](https://github.com/es-shims/Array.prototype.reduce/commit/d64f55f43784d01193858ab66b47cf7acbce6ee1)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`2da232a`](https://github.com/es-shims/Array.prototype.reduce/commit/2da232a7dadedc545c5ebb89052f399f4e734748)
- [Robustness] use `es-errors` [`020b529`](https://github.com/es-shims/Array.prototype.reduce/commit/020b5296e26f15065ca0ac08ce565f47835e32c4)

## [v1.0.6](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.5...v1.0.6) - 2023-08-29

### Commits

- [Deps] update `define-properties`, `es-abstract` [`5afbf91`](https://github.com/es-shims/Array.prototype.reduce/commit/5afbf91e47c7be69e30237870d3bfbde32f092c3)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`abc82a9`](https://github.com/es-shims/Array.prototype.reduce/commit/abc82a9b428d695aba48b856221d1eb2c198d87b)

## [v1.0.5](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.4...v1.0.5) - 2022-11-03

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`a2333c6`](https://github.com/es-shims/Array.prototype.reduce/commit/a2333c6370c478238b72d01d2b53b423ebb321c5)
- [Deps] update `es-abstract` [`86a838f`](https://github.com/es-shims/Array.prototype.reduce/commit/86a838f1faebb4fc6d2f7269fa186535121fd270)
- [actions] update rebase action to use reusable workflow [`4710394`](https://github.com/es-shims/Array.prototype.reduce/commit/4710394340a9cfe26758971559fd4b7b523827c5)
- [Dev Deps] update `aud`, `tape` [`e2bcb57`](https://github.com/es-shims/Array.prototype.reduce/commit/e2bcb57270484bbfdbb25d8d9d9a880061cf8e84)
- [Deps] update `define-properties`, `es-abstract` [`4c693aa`](https://github.com/es-shims/Array.prototype.reduce/commit/4c693aa96a66c374489afc2c0b2e8c0f433a0efe)
- [Deps] update `es-abstract` [`ca5557c`](https://github.com/es-shims/Array.prototype.reduce/commit/ca5557ce5a7b8b71a079687383e58f21c566bc6d)
- [Dev Deps] update `functions-have-names` [`dcb2d35`](https://github.com/es-shims/Array.prototype.reduce/commit/dcb2d3577b1a489d5b69e35b2e878dc4a3a242e0)

## [v1.0.4](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.3...v1.0.4) - 2022-04-11

### Commits

- [actions] reuse common workflows [`42fe1e2`](https://github.com/es-shims/Array.prototype.reduce/commit/42fe1e2f8182227dc46c8a4af82fee0ee949144b)
- [Fix] properly handle a large negative length [`b24d3c4`](https://github.com/es-shims/Array.prototype.reduce/commit/b24d3c47b1ddbd2296000c4483ad6c987127df90)
- [Fix] properly handle only-holes array [`823a890`](https://github.com/es-shims/Array.prototype.reduce/commit/823a89004496524c8915fc436b4c6279031db7fa)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`235ae27`](https://github.com/es-shims/Array.prototype.reduce/commit/235ae27d0561122eb771095a2f791bf024b12985)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`7f720c6`](https://github.com/es-shims/Array.prototype.reduce/commit/7f720c66825ce7f5d1e8c21efc051d834fc9da00)
- [actions] update codecov uploader [`9d6eff0`](https://github.com/es-shims/Array.prototype.reduce/commit/9d6eff0e66c6d61db869e2fb7eb6c1f764f8dfd8)
- [Deps] update `es-abstract` [`c14d421`](https://github.com/es-shims/Array.prototype.reduce/commit/c14d421406593cc4208af26787b830c83a014bb7)
- [Deps] update `es-abstract` [`5288168`](https://github.com/es-shims/Array.prototype.reduce/commit/528816886ba56b5a0dabffa389708c1ecde09a18)

## [v1.0.3](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.2...v1.0.3) - 2021-10-01

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`bbd7913`](https://github.com/es-shims/Array.prototype.reduce/commit/bbd791365c6aaaf9bf65ea408fa24145dae37e96)
- [Deps] update `es-abstract` [`6f05f53`](https://github.com/es-shims/Array.prototype.reduce/commit/6f05f530335550a8b05d1a7e8b9ff9f9a1f36fa2)
- [readme] remove travis badge [`fc5c01c`](https://github.com/es-shims/Array.prototype.reduce/commit/fc5c01cb4168a7478b64f07d79dec2cd3d72cb30)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `tape` [`7eb4251`](https://github.com/es-shims/Array.prototype.reduce/commit/7eb4251db41fc6350fcc8b4e45fe0161a8d43c4b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `functions-have-names`, `has-strict-mode`, `tape` [`b6d02ba`](https://github.com/es-shims/Array.prototype.reduce/commit/b6d02ba6579bef59150af4d37d867a8e3e6d74f9)
- [actions] update workflows [`403b142`](https://github.com/es-shims/Array.prototype.reduce/commit/403b1423722ebcf236d8b0f34d9e8cd4f3820221)
- [readme] add github actions/codecov badges [`e070a24`](https://github.com/es-shims/Array.prototype.reduce/commit/e070a2455b14ac47ef133aca8119e7cbd114f2b8)
- [actions] update workflows [`9657169`](https://github.com/es-shims/Array.prototype.reduce/commit/96571694e39f29a70d4ab222715096b21dd0359f)
- [Dep] move `is-string` to prod deps [`1eb89d6`](https://github.com/es-shims/Array.prototype.reduce/commit/1eb89d620d4493988824aade117f3f66c42a82c7)
- [Dev Deps] update `eslint`, `tape` [`70049c6`](https://github.com/es-shims/Array.prototype.reduce/commit/70049c65f794931d586b8cbe07b722048f457211)
- [Deps] update `call-bind`, `es-abstract` [`e49aea9`](https://github.com/es-shims/Array.prototype.reduce/commit/e49aea9752701d79fbc8606a172fb7dbc4d179cc)
- [meta] use `prepublishOnly` script for npm 7+ [`e2bbb8b`](https://github.com/es-shims/Array.prototype.reduce/commit/e2bbb8b440389573b900b0c084b5149fb8a487e7)
- [Tests] increase coverage [`4b9b02c`](https://github.com/es-shims/Array.prototype.reduce/commit/4b9b02ca6d5ebca08c68442ec410252ca7403fac)
- [Deps] update `es-abstract` [`e349e01`](https://github.com/es-shims/Array.prototype.reduce/commit/e349e0133314f6d2443ca7cc6451f0403fa147c0)

## [v1.0.2](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.1...v1.0.2) - 2020-11-19

### Commits

- [Tests] migrate tests to Github Actions [`397cded`](https://github.com/es-shims/Array.prototype.reduce/commit/397cded4df1918965948f178060a9842d91f11ed)
- [meta] do not publish github action workflow files [`7ac0e80`](https://github.com/es-shims/Array.prototype.reduce/commit/7ac0e80845bf9dcf97d36ebe0fea1f11f4faf0ff)
- [Deps] update `es-abstract`; use `call-bind` where applicable; remove `function-bind` [`3470ed1`](https://github.com/es-shims/Array.prototype.reduce/commit/3470ed1ed6474c92dcc547929ab101322de9ee12)
- [Tests] run `nyc` on all tests [`6efa838`](https://github.com/es-shims/Array.prototype.reduce/commit/6efa838535903d2260b46bcc73a7e0c8c8c22e18)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`eae319e`](https://github.com/es-shims/Array.prototype.reduce/commit/eae319ef283bad89ab807e73c931eb714ce06279)
- [actions] add "Allow Edits" workflow [`87ed4f8`](https://github.com/es-shims/Array.prototype.reduce/commit/87ed4f86ea68a88bd6e4b72c5ee6cccf2ac6ce02)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`9bfea52`](https://github.com/es-shims/Array.prototype.reduce/commit/9bfea523756dbd8dc9308916145f8d3a2fe46103)
- [Dev Deps] update `@ljharb/eslint-config`, `tape`, `has-strict-mode`, `functions-have-names` [`fdac028`](https://github.com/es-shims/Array.prototype.reduce/commit/fdac028b1e46873180cfae102d409d148fb3e6f7)
- [Dev Deps] update `auto-changelog`, `tape` [`f595f6a`](https://github.com/es-shims/Array.prototype.reduce/commit/f595f6a31eabeef2d92895855c818507fe6e0219)
- [Dev Deps] update `auto-changelog`; add `aud` [`9d3d84c`](https://github.com/es-shims/Array.prototype.reduce/commit/9d3d84c030f0350f5e98bd320ead76392b252240)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`2c05596`](https://github.com/es-shims/Array.prototype.reduce/commit/2c055961d27ebcde19f5dde83e4e14eab945b924)
- [Tests] only audit prod deps [`89c3610`](https://github.com/es-shims/Array.prototype.reduce/commit/89c36102d528b1b17a56cf7ef0395cec83196e6f)
- [Deps] update `es-abstract` [`65bdd68`](https://github.com/es-shims/Array.prototype.reduce/commit/65bdd68d942a2d82e9c5548c25944b2bebb9a52d)
- [Deps] update `es-abstract` [`ad19dd9`](https://github.com/es-shims/Array.prototype.reduce/commit/ad19dd9a1008a05381bab87b79444cc93b28ac3b)
- [Dev Deps] update `eslint` [`ce28412`](https://github.com/es-shims/Array.prototype.reduce/commit/ce28412065431e3dc21010e6c7c981739676161e)

## [v1.0.1](https://github.com/es-shims/Array.prototype.reduce/compare/v1.0.0...v1.0.1) - 2019-12-20

### Commits

- [Tests] use shared travis-ci configs [`cc10c0e`](https://github.com/es-shims/Array.prototype.reduce/commit/cc10c0e89f27b1f1985ad927a98eb8f61f548645)
- [Refactor] use split-up `es-abstract` (77% bundle size decrease) [`1d34e2b`](https://github.com/es-shims/Array.prototype.reduce/commit/1d34e2b786eef0a699d15c2ade3afbbcd79b697a)
- [meta] update `auto-changelog` [`0d4c1a7`](https://github.com/es-shims/Array.prototype.reduce/commit/0d4c1a77ceb359ea57c6f4721bd2ac27029837f7)
- [actions] add automatic rebasing / merge commit blocking [`3c5a9aa`](https://github.com/es-shims/Array.prototype.reduce/commit/3c5a9aa612533f9288f092c48559100f0c7c0d51)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is-string`, `tape` [`37b79b1`](https://github.com/es-shims/Array.prototype.reduce/commit/37b79b12e55fa1751a3d50e17822d85039c7d51d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `functions-have-names` [`f5c5499`](https://github.com/es-shims/Array.prototype.reduce/commit/f5c5499e6c41fd318b7e7149b240bdbb36e3542d)
- [Deps] update `es-abstract` [`bd187af`](https://github.com/es-shims/Array.prototype.reduce/commit/bd187af654f13a6f2a761e6919a35aa8e21ba0fb)
- [meta] add `funding` field [`8992db6`](https://github.com/es-shims/Array.prototype.reduce/commit/8992db65687dd6a0cb2a84feafe0405e564a4c1a)
- [Deps] update `es-abstract` [`eda5044`](https://github.com/es-shims/Array.prototype.reduce/commit/eda50449d54ab75b01754b61b72d51d1ca9a4dc7)

## v1.0.0 - 2019-09-30

### Commits

- Tests [`49c8a44`](https://github.com/es-shims/Array.prototype.reduce/commit/49c8a449b43cb6cc2784ff77cf10a1fee819e14f)
- Implementation [`877b95a`](https://github.com/es-shims/Array.prototype.reduce/commit/877b95a04f941d18dbc6081e026a49e14950a151)
- Initial commit [`06b97be`](https://github.com/es-shims/Array.prototype.reduce/commit/06b97bed9f8b2da62133d65449b6dc86bf0db0ee)
- readme [`1049385`](https://github.com/es-shims/Array.prototype.reduce/commit/1049385c90a912bd5196f5ecf2efb6e2ee942c0b)
- npm init [`57807e8`](https://github.com/es-shims/Array.prototype.reduce/commit/57807e8507a732cc0539e5f8154571b6952e30a4)
- [meta] add `auto-changelog`, `safe-publish-latest` [`74a5ff3`](https://github.com/es-shims/Array.prototype.reduce/commit/74a5ff39b29cd01ae6b25f32d2629bb53fef8380)
- Only apps should have lockfiles [`50da8f2`](https://github.com/es-shims/Array.prototype.reduce/commit/50da8f2127c825bdebbac921f38237fd495f5b65)
