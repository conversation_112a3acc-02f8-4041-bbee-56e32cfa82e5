{"ast": null, "code": "import{httpClient}from'./api.service';class HolidayService{constructor(){this.baseUrl='/api/holidays';}/**\n   * Get holidays for a specific year\n   */async getHolidays(year,category,countryCode){var _response$data;const params=new URLSearchParams();if(year)params.append('year',year.toString());if(category)params.append('category',category);if(countryCode)params.append('country_code',countryCode);const response=await httpClient.get(\"\".concat(this.baseUrl,\"?\").concat(params.toString()));return((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.holidays)||[];}/**\n   * Get Philippine holidays for a specific year\n   */async getPhilippineHolidays(year){var _response$data2;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/philippine?\").concat(params.toString()));return((_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.holidays)||[];}/**\n   * Get international holidays for a specific year\n   */async getInternationalHolidays(year){var _response$data3;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/international?\").concat(params.toString()));return((_response$data3=response.data)===null||_response$data3===void 0?void 0:_response$data3.holidays)||[];}/**\n   * Get religious holidays for a specific year\n   */async getReligiousHolidays(year){var _response$data4;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/religious?\").concat(params.toString()));return((_response$data4=response.data)===null||_response$data4===void 0?void 0:_response$data4.holidays)||[];}/**\n   * Get all holidays from API sources (not database)\n   */async getAllHolidaysFromAPI(year){var _response$data5;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/api-source?\").concat(params.toString()));return((_response$data5=response.data)===null||_response$data5===void 0?void 0:_response$data5.holidays)||[];}/**\n   * Get holiday statistics\n   */async getHolidayStats(year){var _response$data6;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/stats?\").concat(params.toString()));if(!((_response$data6=response.data)!==null&&_response$data6!==void 0&&_response$data6.stats)){throw new Error('Failed to get holiday statistics');}return response.data.stats;}/**\n   * Preview holidays before syncing (admin only)\n   */async previewHolidays(year){var _response$data7;const params=new URLSearchParams();if(year)params.append('year',year.toString());const response=await httpClient.get(\"\".concat(this.baseUrl,\"/preview?\").concat(params.toString()));if(!((_response$data7=response.data)!==null&&_response$data7!==void 0&&_response$data7.preview)){throw new Error('Failed to get holiday preview');}return response.data.preview;}/**\n   * Sync holidays to database (admin only)\n   */async syncHolidays(year){var _response$data8;let force=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const response=await httpClient.post(\"\".concat(this.baseUrl,\"/sync\"),{year:year||new Date().getFullYear(),force});if(!((_response$data8=response.data)!==null&&_response$data8!==void 0&&_response$data8.results)){throw new Error('Failed to sync holidays');}return response.data.results;}/**\n   * Sync holidays for multiple years (admin only)\n   */async syncMultipleYears(years){var _response$data9;let force=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const response=await httpClient.post(\"\".concat(this.baseUrl,\"/sync-multiple\"),{years,force});if(!((_response$data9=response.data)!==null&&_response$data9!==void 0&&_response$data9.results)){throw new Error('Failed to sync holidays for multiple years');}return response.data.results;}/**\n   * Delete auto-generated holidays for a specific year (admin only)\n   */async deleteAutoGeneratedHolidays(year){var _response$data0;const response=await httpClient.delete(\"\".concat(this.baseUrl,\"/auto-generated/\").concat(year));if(((_response$data0=response.data)===null||_response$data0===void 0?void 0:_response$data0.deletedCount)===undefined){throw new Error('Failed to delete auto-generated holidays');}return response.data.deletedCount;}/**\n   * Check if a date is a holiday\n   */async isHoliday(date){try{const year=new Date(date).getFullYear();const holidays=await this.getHolidays(year);return holidays.some(holiday=>holiday.event_date===date);}catch(error){console.error('Error checking if date is holiday:',error);return false;}}/**\n   * Get holidays for a specific date\n   */async getHolidaysForDate(date){try{const year=new Date(date).getFullYear();const holidays=await this.getHolidays(year);return holidays.filter(holiday=>holiday.event_date===date);}catch(error){console.error('Error getting holidays for date:',error);return[];}}/**\n   * Get upcoming holidays (next 30 days)\n   */async getUpcomingHolidays(){let days=arguments.length>0&&arguments[0]!==undefined?arguments[0]:30;try{const today=new Date();const endDate=new Date();endDate.setDate(today.getDate()+days);const currentYear=today.getFullYear();const endYear=endDate.getFullYear();let holidays=[];// Get holidays for current year\nholidays=await this.getHolidays(currentYear);// If the end date is in the next year, also get holidays for next year\nif(endYear>currentYear){const nextYearHolidays=await this.getHolidays(endYear);holidays=[...holidays,...nextYearHolidays];}// Filter holidays within the date range\nconst todayStr=today.toISOString().split('T')[0];const endDateStr=endDate.toISOString().split('T')[0];return holidays.filter(holiday=>holiday.event_date>=todayStr&&holiday.event_date<=endDateStr).sort((a,b)=>a.event_date.localeCompare(b.event_date));}catch(error){console.error('Error getting upcoming holidays:',error);return[];}}}export const holidayService=new HolidayService();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}