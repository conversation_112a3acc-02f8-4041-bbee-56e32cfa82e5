{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 10 3-3 3 3\",\n  key: \"11gsxs\"\n}], [\"path\", {\n  d: \"M12 13V7\",\n  key: \"h0r20n\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}]];\nconst MonitorUp = createLucideIcon(\"monitor-up\", __iconNode);\nexport { __iconNode, MonitorUp as default };\n//# sourceMappingURL=monitor-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}