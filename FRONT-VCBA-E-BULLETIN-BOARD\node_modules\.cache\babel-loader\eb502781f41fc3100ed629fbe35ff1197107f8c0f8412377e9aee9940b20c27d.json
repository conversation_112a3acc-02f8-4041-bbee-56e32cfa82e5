{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 15 6 6\",\n  key: \"1s409w\"\n}], [\"path\", {\n  d: \"m15 9 6-6\",\n  key: \"ko1vev\"\n}], [\"path\", {\n  d: \"M21 16v5h-5\",\n  key: \"1ck2sf\"\n}], [\"path\", {\n  d: \"M21 8V3h-5\",\n  key: \"1qoq8a\"\n}], [\"path\", {\n  d: \"M3 16v5h5\",\n  key: \"1t08am\"\n}], [\"path\", {\n  d: \"m3 21 6-6\",\n  key: \"wwnumi\"\n}], [\"path\", {\n  d: \"M3 8V3h5\",\n  key: \"1ln10m\"\n}], [\"path\", {\n  d: \"M9 9 3 3\",\n  key: \"v551iv\"\n}]];\nconst Expand = createLucideIcon(\"expand\", __iconNode);\nexport { __iconNode, Expand as default };\n//# sourceMappingURL=expand.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}