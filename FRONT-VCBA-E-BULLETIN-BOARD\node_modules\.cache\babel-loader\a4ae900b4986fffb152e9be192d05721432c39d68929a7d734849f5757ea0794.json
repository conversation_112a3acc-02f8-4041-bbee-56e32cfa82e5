{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 18v-6a5 5 0 1 1 10 0v6\",\n  key: \"pcx96s\"\n}], [\"path\", {\n  d: \"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z\",\n  key: \"1b4s83\"\n}], [\"path\", {\n  d: \"M21 12h1\",\n  key: \"jtio3y\"\n}], [\"path\", {\n  d: \"M18.5 4.5 18 5\",\n  key: \"g5sp9y\"\n}], [\"path\", {\n  d: \"M2 12h1\",\n  key: \"1uaihz\"\n}], [\"path\", {\n  d: \"M12 2v1\",\n  key: \"11qlp1\"\n}], [\"path\", {\n  d: \"m4.929 4.929.707.707\",\n  key: \"1i51kw\"\n}], [\"path\", {\n  d: \"M12 12v6\",\n  key: \"3ahymv\"\n}]];\nconst Siren = createLucideIcon(\"siren\", __iconNode);\nexport { __iconNode, Siren as default };\n//# sourceMappingURL=siren.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}