{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"M10 16v4a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-4\",\n  key: \"11f1s0\"\n}], [\"path\", {\n  d: \"M10 8V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v4\",\n  key: \"t14dx9\"\n}], [\"path\", {\n  d: \"M20 16v1a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-1\",\n  key: \"1w07xs\"\n}], [\"path\", {\n  d: \"M14 8V7c0-1.1.9-2 2-2h2a2 2 0 0 1 2 2v1\",\n  key: \"1apec2\"\n}]];\nconst AlignCenterHorizontal = createLucideIcon(\"align-center-horizontal\", __iconNode);\nexport { __iconNode, AlignCenterHorizontal as default };\n//# sourceMappingURL=align-center-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}