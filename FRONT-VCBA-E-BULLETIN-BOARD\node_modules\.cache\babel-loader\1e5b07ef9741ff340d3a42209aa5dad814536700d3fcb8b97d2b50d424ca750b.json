{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12.1\",\n  cy: \"12.1\",\n  r: \"1\",\n  key: \"18d7e5\"\n}]];\nconst Dot = createLucideIcon(\"dot\", __iconNode);\nexport { __iconNode, Dot as default };\n//# sourceMappingURL=dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}