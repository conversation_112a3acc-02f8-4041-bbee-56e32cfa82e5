{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3h18\",\n  key: \"o7r712\"\n}], [\"path\", {\n  d: \"M20 7H8\",\n  key: \"gd2fo2\"\n}], [\"path\", {\n  d: \"M20 11H8\",\n  key: \"1ynp89\"\n}], [\"path\", {\n  d: \"M10 19h10\",\n  key: \"19hjk5\"\n}], [\"path\", {\n  d: \"M8 15h12\",\n  key: \"1yqzne\"\n}], [\"path\", {\n  d: \"M4 3v14\",\n  key: \"fggqzn\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"p3m9r0\"\n}]];\nconst Blinds = createLucideIcon(\"blinds\", __iconNode);\nexport { __iconNode, Blinds as default };\n//# sourceMappingURL=blinds.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}