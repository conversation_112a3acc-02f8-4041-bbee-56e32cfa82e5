{"ast": null, "code": "export { default } from './ErrorBoundary';\nexport { default as ErrorBoundary } from './ErrorBoundary';", "map": {"version": 3, "names": ["default", "Error<PERSON>ou<PERSON><PERSON>"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/ErrorBoundary/index.ts"], "sourcesContent": ["export { default } from './ErrorBoundary';\nexport { default as ErrorBoundary } from './ErrorBoundary';\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}