{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"20\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"1estib\"\n}], [\"path\", {\n  d: \"M17 14v7\",\n  key: \"7m2elx\"\n}], [\"path\", {\n  d: \"M7 14v7\",\n  key: \"1cm7wv\"\n}], [\"path\", {\n  d: \"M17 3v3\",\n  key: \"1v4jwn\"\n}], [\"path\", {\n  d: \"M7 3v3\",\n  key: \"7o6guu\"\n}], [\"path\", {\n  d: \"M10 14 2.3 6.3\",\n  key: \"1023jk\"\n}], [\"path\", {\n  d: \"m14 6 7.7 7.7\",\n  key: \"1s8pl2\"\n}], [\"path\", {\n  d: \"m8 6 8 8\",\n  key: \"hl96qh\"\n}]];\nconst Construction = createLucideIcon(\"construction\", __iconNode);\nexport { __iconNode, Construction as default };\n//# sourceMappingURL=construction.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}