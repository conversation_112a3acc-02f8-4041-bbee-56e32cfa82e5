{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 2h10\",\n  key: \"nczekb\"\n}], [\"path\", {\n  d: \"M5 6h14\",\n  key: \"u2x4p\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"12\",\n  x: \"3\",\n  y: \"10\",\n  rx: \"2\",\n  key: \"l0tzu3\"\n}]];\nconst GalleryVerticalEnd = createLucideIcon(\"gallery-vertical-end\", __iconNode);\nexport { __iconNode, GalleryVerticalEnd as default };\n//# sourceMappingURL=gallery-vertical-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}