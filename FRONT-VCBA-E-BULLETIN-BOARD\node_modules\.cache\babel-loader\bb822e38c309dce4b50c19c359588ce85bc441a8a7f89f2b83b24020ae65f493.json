{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16H4a2 2 0 1 1 0-4h16a2 2 0 1 1 0 4h-4.25\",\n  key: \"5dloqd\"\n}], [\"path\", {\n  d: \"M5 12a2 2 0 0 1-2-2 9 7 0 0 1 18 0 2 2 0 0 1-2 2\",\n  key: \"1vl3my\"\n}], [\"path\", {\n  d: \"M5 16a2 2 0 0 0-2 2 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 2 2 0 0 0-2-2q0 0 0 0\",\n  key: \"1us75o\"\n}], [\"path\", {\n  d: \"m6.67 12 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2\",\n  key: \"qqzweh\"\n}]];\nconst Hamburger = createLucideIcon(\"hamburger\", __iconNode);\nexport { __iconNode, Hamburger as default };\n//# sourceMappingURL=hamburger.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}