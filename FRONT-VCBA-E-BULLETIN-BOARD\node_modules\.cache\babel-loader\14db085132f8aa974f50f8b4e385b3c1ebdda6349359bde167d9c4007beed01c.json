{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.531 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14v6a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341l.427-.473\",\n  key: \"ol2ft2\"\n}], [\"path\", {\n  d: \"m16.5 3.5 5 5\",\n  key: \"15e6fa\"\n}], [\"path\", {\n  d: \"m21.5 3.5-5 5\",\n  key: \"m0lwru\"\n}]];\nconst FunnelX = createLucideIcon(\"funnel-x\", __iconNode);\nexport { __iconNode, FunnelX as default };\n//# sourceMappingURL=funnel-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}