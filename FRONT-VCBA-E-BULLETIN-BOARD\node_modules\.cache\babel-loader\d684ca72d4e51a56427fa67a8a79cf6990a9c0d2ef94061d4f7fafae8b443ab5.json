{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2\",\n  key: \"5owen\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"17\",\n  r: \"2\",\n  key: \"u2ysq9\"\n}], [\"path\", {\n  d: \"M9 17h6\",\n  key: \"r8uit2\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"2\",\n  key: \"axvx0g\"\n}]];\nconst Car = createLucideIcon(\"car\", __iconNode);\nexport { __iconNode, Car as default };\n//# sourceMappingURL=car.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}