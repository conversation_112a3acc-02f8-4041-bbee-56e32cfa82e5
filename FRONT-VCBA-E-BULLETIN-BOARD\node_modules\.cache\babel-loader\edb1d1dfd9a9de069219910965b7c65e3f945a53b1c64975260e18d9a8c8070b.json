{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, AlertCircle, Loader } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentImageUrl,\n  onImageSelect,\n  onUpload,\n  onRemove,\n  maxSize = 5 * 1024 * 1024,\n  // 5MB default\n  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  loading = false,\n  userInitials = 'U',\n  gradientColors = ['#22c55e', '#facc15']\n}) => {\n  _s();\n  const [preview, setPreview] = useState(currentImageUrl || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const fileInputRef = useRef(null);\n\n  // Validate file\n  const validateFile = useCallback(file => {\n    if (file.size > maxSize) {\n      return `File size must be less than ${(maxSize / (1024 * 1024)).toFixed(1)}MB`;\n    }\n    if (!acceptedFormats.includes(file.type)) {\n      return `File type not supported. Accepted formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n    return null;\n  }, [maxSize, acceptedFormats]);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setSelectedFile(file);\n      onImageSelect(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile, onImageSelect]);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag events\n  const handleDragIn = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(true);\n  };\n  const handleDragOut = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n  };\n  const handleDrag = e => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n    if (disabled) return;\n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = () => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    onImageSelect(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    if (onRemove) {\n      onRemove();\n    }\n  };\n\n  // Handle upload\n  const handleUpload = () => {\n    if (onUpload) {\n      onUpload();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100px',\n            height: '100px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: '3px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            background: preview ? `url(${preview}) center/cover` : `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem',\n            cursor: disabled ? 'default' : 'pointer',\n            transition: 'all 0.2s ease',\n            opacity: loading ? 0.7 : 1\n          },\n          onClick: () => {\n            var _fileInputRef$current;\n            return !disabled && !loading && ((_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click());\n          },\n          onDragEnter: handleDragIn,\n          onDragLeave: handleDragOut,\n          onDragOver: handleDrag,\n          onDrop: handleDrop,\n          children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              inset: 0,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'rgba(0, 0, 0, 0.5)',\n              borderRadius: '50%'\n            },\n            children: /*#__PURE__*/_jsxDEV(Loader, {\n              size: 24,\n              style: {\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), !preview && !loading && userInitials, isDragging && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              inset: 0,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'rgba(34, 197, 94, 0.8)',\n              borderRadius: '50%'\n            },\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), preview && !loading && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRemove,\n          disabled: disabled,\n          style: {\n            position: 'absolute',\n            top: '-5px',\n            right: '-5px',\n            width: '24px',\n            height: '24px',\n            borderRadius: '50%',\n            background: '#ef4444',\n            color: 'white',\n            border: 'none',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '12px'\n          },\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: disabled || loading,\n            style: {\n              background: `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: disabled || loading ? 'not-allowed' : 'pointer',\n              marginRight: '1rem',\n              opacity: disabled || loading ? 0.6 : 1\n            },\n            children: selectedFile ? 'Change Photo' : 'Upload New Photo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), selectedFile && onUpload && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUpload,\n            disabled: disabled || loading,\n            style: {\n              background: '#10b981',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: disabled || loading ? 'not-allowed' : 'pointer',\n              marginRight: '1rem',\n              opacity: disabled || loading ? 0.6 : 1\n            },\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), (preview || currentImageUrl) && onRemove && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: disabled || loading,\n            style: {\n              background: 'none',\n              border: '1px solid #e5e7eb',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: disabled || loading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: disabled || loading ? 0.6 : 1\n            },\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"PNG, JPG, GIF, WebP up to \", (maxSize / (1024 * 1024)).toFixed(1), \"MB\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#10b981',\n              fontWeight: '500'\n            },\n            children: [\"Selected: \", selectedFile.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        fontSize: '14px',\n        color: '#DC2626',\n        background: '#FEF2F2',\n        padding: '0.75rem',\n        borderRadius: '6px',\n        border: '1px solid #FECACA'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16,\n        style: {\n          marginRight: '8px',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      accept: acceptedFormats.join(','),\n      onChange: handleInputChange,\n      style: {\n        display: 'none'\n      },\n      disabled: disabled || loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"qkCiroaFYgKh8VBbueMU4y1ta8E=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "AlertCircle", "Loader", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentImageUrl", "onImageSelect", "onUpload", "onRemove", "maxSize", "acceptedFormats", "className", "disabled", "loading", "userInitials", "gradientColors", "_s", "preview", "setPreview", "isDragging", "setIsDragging", "error", "setError", "selectedFile", "setSelectedFile", "fileInputRef", "validateFile", "file", "size", "toFixed", "includes", "type", "map", "f", "split", "toUpperCase", "join", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "handleInputChange", "_e$target$files", "files", "handleDragIn", "preventDefault", "stopPropagation", "handleDragOut", "handleDrag", "handleDrop", "dataTransfer", "length", "handleRemove", "current", "value", "handleUpload", "children", "style", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "justifyContent", "background", "color", "fontWeight", "fontSize", "cursor", "transition", "opacity", "onClick", "_fileInputRef$current", "click", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "inset", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "right", "marginBottom", "_fileInputRef$current2", "padding", "marginRight", "name", "marginTop", "flexShrink", "ref", "accept", "onChange", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, User, AlertCircle, Loader } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentImageUrl?: string;\n  onImageSelect: (file: File | null) => void;\n  onUpload?: () => void;\n  onRemove?: () => void;\n  maxSize?: number; // in bytes\n  acceptedFormats?: string[];\n  className?: string;\n  disabled?: boolean;\n  loading?: boolean;\n  userInitials?: string;\n  gradientColors?: [string, string]; // For fallback gradient\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentImageUrl,\n  onImageSelect,\n  onUpload,\n  onRemove,\n  maxSize = 5 * 1024 * 1024, // 5MB default\n  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  loading = false,\n  userInitials = 'U',\n  gradientColors = ['#22c55e', '#facc15']\n}) => {\n  const [preview, setPreview] = useState<string | null>(currentImageUrl || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Validate file\n  const validateFile = useCallback((file: File): string | null => {\n    if (file.size > maxSize) {\n      return `File size must be less than ${(maxSize / (1024 * 1024)).toFixed(1)}MB`;\n    }\n\n    if (!acceptedFormats.includes(file.type)) {\n      return `File type not supported. Accepted formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n\n    return null;\n  }, [maxSize, acceptedFormats]);\n\n  // Handle file selection\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setSelectedFile(file);\n      onImageSelect(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile, onImageSelect]);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag events\n  const handleDragIn = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(true);\n  };\n\n  const handleDragOut = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n  };\n\n  const handleDrag = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n\n    if (disabled) return;\n\n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = () => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    onImageSelect(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    if (onRemove) {\n      onRemove();\n    }\n  };\n\n  // Handle upload\n  const handleUpload = () => {\n    if (onUpload) {\n      onUpload();\n    }\n  };\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div style={{ position: 'relative' }}>\n          <div\n            style={{\n              width: '100px',\n              height: '100px',\n              borderRadius: '50%',\n              overflow: 'hidden',\n              border: '3px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: preview \n                ? `url(${preview}) center/cover` \n                : `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,\n              color: 'white',\n              fontWeight: '700',\n              fontSize: '2rem',\n              cursor: disabled ? 'default' : 'pointer',\n              transition: 'all 0.2s ease',\n              opacity: loading ? 0.7 : 1\n            }}\n            onClick={() => !disabled && !loading && fileInputRef.current?.click()}\n            onDragEnter={handleDragIn}\n            onDragLeave={handleDragOut}\n            onDragOver={handleDrag}\n            onDrop={handleDrop}\n          >\n            {loading && (\n              <div style={{\n                position: 'absolute',\n                inset: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'rgba(0, 0, 0, 0.5)',\n                borderRadius: '50%'\n              }}>\n                <Loader size={24} style={{ animation: 'spin 1s linear infinite' }} />\n              </div>\n            )}\n            {!preview && !loading && userInitials}\n            {isDragging && (\n              <div style={{\n                position: 'absolute',\n                inset: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'rgba(34, 197, 94, 0.8)',\n                borderRadius: '50%'\n              }}>\n                <Upload size={24} />\n              </div>\n            )}\n          </div>\n\n          {/* Remove button */}\n          {preview && !loading && (\n            <button\n              onClick={handleRemove}\n              disabled={disabled}\n              style={{\n                position: 'absolute',\n                top: '-5px',\n                right: '-5px',\n                width: '24px',\n                height: '24px',\n                borderRadius: '50%',\n                background: '#ef4444',\n                color: 'white',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px'\n              }}\n            >\n              <X size={14} />\n            </button>\n          )}\n        </div>\n\n        {/* Upload Controls */}\n        <div>\n          <div style={{ marginBottom: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={disabled || loading}\n              style={{\n                background: `linear-gradient(135deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: disabled || loading ? 'not-allowed' : 'pointer',\n                marginRight: '1rem',\n                opacity: disabled || loading ? 0.6 : 1\n              }}\n            >\n              {selectedFile ? 'Change Photo' : 'Upload New Photo'}\n            </button>\n\n            {selectedFile && onUpload && (\n              <button\n                onClick={handleUpload}\n                disabled={disabled || loading}\n                style={{\n                  background: '#10b981',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontWeight: '600',\n                  cursor: disabled || loading ? 'not-allowed' : 'pointer',\n                  marginRight: '1rem',\n                  opacity: disabled || loading ? 0.6 : 1\n                }}\n              >\n                Save\n              </button>\n            )}\n\n            {(preview || currentImageUrl) && onRemove && (\n              <button\n                onClick={handleRemove}\n                disabled={disabled || loading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e5e7eb',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: disabled || loading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: disabled || loading ? 0.6 : 1\n                }}\n              >\n                Remove\n              </button>\n            )}\n          </div>\n\n          <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n            <p>PNG, JPG, GIF, WebP up to {(maxSize / (1024 * 1024)).toFixed(1)}MB</p>\n            {selectedFile && (\n              <p style={{ color: '#10b981', fontWeight: '500' }}>\n                Selected: {selectedFile.name}\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Error message */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          fontSize: '14px',\n          color: '#DC2626',\n          background: '#FEF2F2',\n          padding: '0.75rem',\n          borderRadius: '6px',\n          border: '1px solid #FECACA'\n        }}>\n          <AlertCircle size={16} style={{ marginRight: '8px', flexShrink: 0 }} />\n          {error}\n        </div>\n      )}\n\n      {/* Hidden file input */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept={acceptedFormats.join(',')}\n        onChange={handleInputChange}\n        style={{ display: 'none' }}\n        disabled={disabled || loading}\n      />\n\n      {/* CSS for spin animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAQC,WAAW,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBpE,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,eAAe;EACfC,aAAa;EACbC,QAAQ;EACRC,QAAQ;EACRC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EAC3BC,eAAe,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACxEC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,YAAY,GAAG,GAAG;EAClBC,cAAc,GAAG,CAAC,SAAS,EAAE,SAAS;AACxC,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAgBU,eAAe,IAAI,IAAI,CAAC;EAC9E,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAG7B,WAAW,CAAE8B,IAAU,IAAoB;IAC9D,IAAIA,IAAI,CAACC,IAAI,GAAGnB,OAAO,EAAE;MACvB,OAAO,+BAA+B,CAACA,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEoB,OAAO,CAAC,CAAC,CAAC,IAAI;IAChF;IAEA,IAAI,CAACnB,eAAe,CAACoB,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACxC,OAAO,8CAA8CrB,eAAe,CAACsB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IAC3H;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAAC3B,OAAO,EAAEC,eAAe,CAAC,CAAC;;EAE9B;EACA,MAAM2B,gBAAgB,GAAGxC,WAAW,CAAE8B,IAAU,IAAK;IACnDL,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMgB,eAAe,GAAGZ,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIW,eAAe,EAAE;MACnBhB,QAAQ,CAACgB,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBzB,UAAU,EAAAyB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;MACtCrB,eAAe,CAACG,IAAI,CAAC;MACrBrB,aAAa,CAACqB,IAAI,CAAC;IACrB,CAAC;IACDY,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBxB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDiB,MAAM,CAACQ,aAAa,CAACpB,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,EAAEpB,aAAa,CAAC,CAAC;;EAEjC;EACA,MAAM0C,iBAAiB,GAAIN,CAAsC,IAAK;IAAA,IAAAO,eAAA;IACpE,MAAMtB,IAAI,IAAAsB,eAAA,GAAGP,CAAC,CAACE,MAAM,CAACM,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAItB,IAAI,EAAE;MACRU,gBAAgB,CAACV,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMwB,YAAY,GAAIT,CAAkB,IAAK;IAC3CA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBV,CAAC,CAACW,eAAe,CAAC,CAAC;IACnBjC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMkC,aAAa,GAAIZ,CAAkB,IAAK;IAC5CA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBV,CAAC,CAACW,eAAe,CAAC,CAAC;IACnBjC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMmC,UAAU,GAAIb,CAAkB,IAAK;IACzCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBV,CAAC,CAACW,eAAe,CAAC,CAAC;EACrB,CAAC;EAED,MAAMG,UAAU,GAAId,CAAkB,IAAK;IACzCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBV,CAAC,CAACW,eAAe,CAAC,CAAC;IACnBjC,aAAa,CAAC,KAAK,CAAC;IAEpB,IAAIR,QAAQ,EAAE;IAEd,MAAMsC,KAAK,GAAGR,CAAC,CAACe,YAAY,CAACP,KAAK;IAClC,IAAIA,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC7BrB,gBAAgB,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBzC,UAAU,CAAC,IAAI,CAAC;IAChBM,eAAe,CAAC,IAAI,CAAC;IACrBF,QAAQ,CAAC,IAAI,CAAC;IACdhB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAImB,YAAY,CAACmC,OAAO,EAAE;MACxBnC,YAAY,CAACmC,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;IACA,IAAIrD,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;;EAED;EACA,MAAMsD,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvD,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKQ,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAoD,QAAA,gBACpD5D,OAAA;MAAK6D,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAEjE5D,OAAA;QAAK6D,KAAK,EAAE;UAAEI,QAAQ,EAAE;QAAW,CAAE;QAAAL,QAAA,gBACnC5D,OAAA;UACE6D,KAAK,EAAE;YACLK,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfC,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,QAAQ;YAClBC,MAAM,EAAE,mBAAmB;YAC3BR,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBQ,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAE1D,OAAO,GACf,OAAOA,OAAO,gBAAgB,GAC9B,2BAA2BF,cAAc,CAAC,CAAC,CAAC,QAAQA,cAAc,CAAC,CAAC,CAAC,QAAQ;YACjF6D,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAEnE,QAAQ,GAAG,SAAS,GAAG,SAAS;YACxCoE,UAAU,EAAE,eAAe;YAC3BC,OAAO,EAAEpE,OAAO,GAAG,GAAG,GAAG;UAC3B,CAAE;UACFqE,OAAO,EAAEA,CAAA;YAAA,IAAAC,qBAAA;YAAA,OAAM,CAACvE,QAAQ,IAAI,CAACC,OAAO,MAAAsE,qBAAA,GAAI1D,YAAY,CAACmC,OAAO,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;UAAA,CAAC;UACtEC,WAAW,EAAElC,YAAa;UAC1BmC,WAAW,EAAEhC,aAAc;UAC3BiC,UAAU,EAAEhC,UAAW;UACvBiC,MAAM,EAAEhC,UAAW;UAAAO,QAAA,GAElBlD,OAAO,iBACNV,OAAA;YAAK6D,KAAK,EAAE;cACVI,QAAQ,EAAE,UAAU;cACpBqB,KAAK,EAAE,CAAC;cACRxB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBQ,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE,oBAAoB;cAChCJ,YAAY,EAAE;YAChB,CAAE;YAAAR,QAAA,eACA5D,OAAA,CAACF,MAAM;cAAC2B,IAAI,EAAE,EAAG;cAACoC,KAAK,EAAE;gBAAE0B,SAAS,EAAE;cAA0B;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CACN,EACA,CAAC7E,OAAO,IAAI,CAACJ,OAAO,IAAIC,YAAY,EACpCK,UAAU,iBACThB,OAAA;YAAK6D,KAAK,EAAE;cACVI,QAAQ,EAAE,UAAU;cACpBqB,KAAK,EAAE,CAAC;cACRxB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBQ,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE,wBAAwB;cACpCJ,YAAY,EAAE;YAChB,CAAE;YAAAR,QAAA,eACA5D,OAAA,CAACL,MAAM;cAAC8B,IAAI,EAAE;YAAG;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL7E,OAAO,IAAI,CAACJ,OAAO,iBAClBV,OAAA;UACE+E,OAAO,EAAEvB,YAAa;UACtB/C,QAAQ,EAAEA,QAAS;UACnBoD,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB2B,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACb3B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBI,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,OAAO;YACdH,MAAM,EAAE,MAAM;YACdM,MAAM,EAAE,SAAS;YACjBd,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBQ,cAAc,EAAE,QAAQ;YACxBI,QAAQ,EAAE;UACZ,CAAE;UAAAf,QAAA,eAEF5D,OAAA,CAACJ,CAAC;YAAC6B,IAAI,EAAE;UAAG;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3F,OAAA;QAAA4D,QAAA,gBACE5D,OAAA;UAAK6D,KAAK,EAAE;YAAEiC,YAAY,EAAE;UAAO,CAAE;UAAAlC,QAAA,gBACnC5D,OAAA;YACE+E,OAAO,EAAEA,CAAA;cAAA,IAAAgB,sBAAA;cAAA,QAAAA,sBAAA,GAAMzE,YAAY,CAACmC,OAAO,cAAAsC,sBAAA,uBAApBA,sBAAA,CAAsBd,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CxE,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;YAC9BmD,KAAK,EAAE;cACLW,UAAU,EAAE,2BAA2B5D,cAAc,CAAC,CAAC,CAAC,QAAQA,cAAc,CAAC,CAAC,CAAC,QAAQ;cACzF6D,KAAK,EAAE,OAAO;cACdH,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnB4B,OAAO,EAAE,gBAAgB;cACzBtB,UAAU,EAAE,KAAK;cACjBE,MAAM,EAAEnE,QAAQ,IAAIC,OAAO,GAAG,aAAa,GAAG,SAAS;cACvDuF,WAAW,EAAE,MAAM;cACnBnB,OAAO,EAAErE,QAAQ,IAAIC,OAAO,GAAG,GAAG,GAAG;YACvC,CAAE;YAAAkD,QAAA,EAEDxC,YAAY,GAAG,cAAc,GAAG;UAAkB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EAERvE,YAAY,IAAIhB,QAAQ,iBACvBJ,OAAA;YACE+E,OAAO,EAAEpB,YAAa;YACtBlD,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;YAC9BmD,KAAK,EAAE;cACLW,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdH,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnB4B,OAAO,EAAE,gBAAgB;cACzBtB,UAAU,EAAE,KAAK;cACjBE,MAAM,EAAEnE,QAAQ,IAAIC,OAAO,GAAG,aAAa,GAAG,SAAS;cACvDuF,WAAW,EAAE,MAAM;cACnBnB,OAAO,EAAErE,QAAQ,IAAIC,OAAO,GAAG,GAAG,GAAG;YACvC,CAAE;YAAAkD,QAAA,EACH;UAED;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EAEA,CAAC7E,OAAO,IAAIZ,eAAe,KAAKG,QAAQ,iBACvCL,OAAA;YACE+E,OAAO,EAAEvB,YAAa;YACtB/C,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;YAC9BmD,KAAK,EAAE;cACLW,UAAU,EAAE,MAAM;cAClBF,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnB4B,OAAO,EAAE,gBAAgB;cACzBpB,MAAM,EAAEnE,QAAQ,IAAIC,OAAO,GAAG,aAAa,GAAG,SAAS;cACvD+D,KAAK,EAAE,SAAS;cAChBK,OAAO,EAAErE,QAAQ,IAAIC,OAAO,GAAG,GAAG,GAAG;YACvC,CAAE;YAAAkD,QAAA,EACH;UAED;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN3F,OAAA;UAAK6D,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEF,KAAK,EAAE;UAAU,CAAE;UAAAb,QAAA,gBACrD5D,OAAA;YAAA4D,QAAA,GAAG,4BAA0B,EAAC,CAACtD,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEoB,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EACxEvE,YAAY,iBACXpB,OAAA;YAAG6D,KAAK,EAAE;cAAEY,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAd,QAAA,GAAC,YACvC,EAACxC,YAAY,CAAC8E,IAAI;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzE,KAAK,iBACJlB,OAAA;MAAK6D,KAAK,EAAE;QACVsC,SAAS,EAAE,MAAM;QACjBrC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBY,QAAQ,EAAE,MAAM;QAChBF,KAAK,EAAE,SAAS;QAChBD,UAAU,EAAE,SAAS;QACrBwB,OAAO,EAAE,SAAS;QAClB5B,YAAY,EAAE,KAAK;QACnBE,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,gBACA5D,OAAA,CAACH,WAAW;QAAC4B,IAAI,EAAE,EAAG;QAACoC,KAAK,EAAE;UAAEoC,WAAW,EAAE,KAAK;UAAEG,UAAU,EAAE;QAAE;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtEzE,KAAK;IAAA;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3F,OAAA;MACEqG,GAAG,EAAE/E,YAAa;MAClBM,IAAI,EAAC,MAAM;MACX0E,MAAM,EAAE/F,eAAe,CAAC0B,IAAI,CAAC,GAAG,CAAE;MAClCsE,QAAQ,EAAE1D,iBAAkB;MAC5BgB,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAC3BrD,QAAQ,EAAEA,QAAQ,IAAIC;IAAQ;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAGF3F,OAAA;MAAA4D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAtTIZ,oBAAyD;AAAAuG,EAAA,GAAzDvG,oBAAyD;AAwT/D,eAAeA,oBAAoB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}