{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\",\n  key: \"1kt360\"\n}], [\"path\", {\n  d: \"M12 10v6\",\n  key: \"1bos4e\"\n}], [\"path\", {\n  d: \"m15 13-3 3-3-3\",\n  key: \"6j2sf0\"\n}]];\nconst FolderDown = createLucideIcon(\"folder-down\", __iconNode);\nexport { __iconNode, FolderDown as default };\n//# sourceMappingURL=folder-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}