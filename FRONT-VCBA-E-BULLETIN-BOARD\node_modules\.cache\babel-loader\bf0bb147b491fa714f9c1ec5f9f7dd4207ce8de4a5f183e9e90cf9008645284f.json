{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 20h4.5a.5.5 0 0 0 .5-.5v-.282a.52.52 0 0 0-.247-.437 8 8 0 1 1 8.494-.001.52.52 0 0 0-.247.438v.282a.5.5 0 0 0 .5.5H21\",\n  key: \"1x94xo\"\n}]];\nconst Omega = createLucideIcon(\"omega\", __iconNode);\nexport { __iconNode, Omega as default };\n//# sourceMappingURL=omega.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}