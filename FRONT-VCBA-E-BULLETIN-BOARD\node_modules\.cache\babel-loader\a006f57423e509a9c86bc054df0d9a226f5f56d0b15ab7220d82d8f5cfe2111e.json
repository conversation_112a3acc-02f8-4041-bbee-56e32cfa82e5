{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M10 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1\",\n  key: \"1ss3eq\"\n}], [\"path\", {\n  d: \"M17 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1\",\n  key: \"1od56t\"\n}]];\nconst CreativeCommons = createLucideIcon(\"creative-commons\", __iconNode);\nexport { __iconNode, CreativeCommons as default };\n//# sourceMappingURL=creative-commons.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}