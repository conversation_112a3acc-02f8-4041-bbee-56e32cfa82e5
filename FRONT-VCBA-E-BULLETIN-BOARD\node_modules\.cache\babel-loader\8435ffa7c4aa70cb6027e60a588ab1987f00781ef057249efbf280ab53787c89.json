{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 4L9 15\",\n  key: \"1qkx8z\"\n}], [\"path\", {\n  d: \"M21 19L3 19\",\n  key: \"100sma\"\n}], [\"path\", {\n  d: \"M9 15L4 10\",\n  key: \"9zxff7\"\n}]];\nconst CheckLine = createLucideIcon(\"check-line\", __iconNode);\nexport { __iconNode, CheckLine as default };\n//# sourceMappingURL=check-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}