{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z\",\n  key: \"1b4qmf\"\n}], [\"path\", {\n  d: \"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2\",\n  key: \"i71pzd\"\n}], [\"path\", {\n  d: \"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2\",\n  key: \"10jefs\"\n}], [\"path\", {\n  d: \"M10 6h4\",\n  key: \"1itunk\"\n}], [\"path\", {\n  d: \"M10 10h4\",\n  key: \"tcdvrf\"\n}], [\"path\", {\n  d: \"M10 14h4\",\n  key: \"kelpxr\"\n}], [\"path\", {\n  d: \"M10 18h4\",\n  key: \"1ulq68\"\n}]];\nconst Building2 = createLucideIcon(\"building-2\", __iconNode);\nexport { __iconNode, Building2 as default };\n//# sourceMappingURL=building-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}