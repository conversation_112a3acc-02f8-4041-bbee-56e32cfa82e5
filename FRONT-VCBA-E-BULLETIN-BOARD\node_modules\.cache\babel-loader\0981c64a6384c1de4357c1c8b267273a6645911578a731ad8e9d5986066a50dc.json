{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8\",\n  key: \"1p45f6\"\n}], [\"path\", {\n  d: \"M21 3v5h-5\",\n  key: \"1q7to0\"\n}]];\nconst RotateCw = createLucideIcon(\"rotate-cw\", __iconNode);\nexport { __iconNode, RotateCw as default };\n//# sourceMappingURL=rotate-cw.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}