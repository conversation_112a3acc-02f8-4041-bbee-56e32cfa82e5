{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z\",\n  key: \"p7xjir\"\n}]];\nconst Cloud = createLucideIcon(\"cloud\", __iconNode);\nexport { __iconNode, Cloud as default };\n//# sourceMappingURL=cloud.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}