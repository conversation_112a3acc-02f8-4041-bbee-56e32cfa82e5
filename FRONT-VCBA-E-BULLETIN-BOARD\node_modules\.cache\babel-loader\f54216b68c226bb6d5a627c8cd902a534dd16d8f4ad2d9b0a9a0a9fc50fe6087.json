{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m20 17-5-5 5-5\",\n  key: \"30x0n2\"\n}], [\"path\", {\n  d: \"m4 17 5-5-5-5\",\n  key: \"16spf4\"\n}]];\nconst ChevronsRightLeft = createLucideIcon(\"chevrons-right-left\", __iconNode);\nexport { __iconNode, ChevronsRightLeft as default };\n//# sourceMappingURL=chevrons-right-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}