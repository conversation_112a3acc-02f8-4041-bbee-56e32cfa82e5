{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 9h6V5l7 7-7 7v-4H6V9z\",\n  key: \"7fvt9c\"\n}]];\nconst ArrowBigRight = createLucideIcon(\"arrow-big-right\", __iconNode);\nexport { __iconNode, ArrowBigRight as default };\n//# sourceMappingURL=arrow-big-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}