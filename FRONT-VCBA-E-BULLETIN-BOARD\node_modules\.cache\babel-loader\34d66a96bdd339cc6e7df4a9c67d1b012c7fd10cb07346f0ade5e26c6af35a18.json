{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\PublicRoute.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Admin Public Route Component\nconst AdminPublicRoute = ({\n  children,\n  restricted = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAdminAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Student Public Route Component\n_s(AdminPublicRoute, \"zeGHgmRC+QMCcA/aIWqLB+h7J9Q=\", false, function () {\n  return [useAdminAuth];\n});\n_c = AdminPublicRoute;\nconst StudentPublicRoute = ({\n  children,\n  restricted = false\n}) => {\n  _s2();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useStudentAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Unified Public Route Component\n_s2(StudentPublicRoute, \"qQO1MO9AEmjDbNk78wOdubWscbE=\", false, function () {\n  return [useStudentAuth];\n});\n_c2 = StudentPublicRoute;\nconst UnifiedPublicRoute = ({\n  children,\n  restricted = false\n}) => {\n  _s3();\n  const {\n    isAuthenticated,\n    isLoading,\n    currentRole\n  } = useUnifiedAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If route is restricted and user is authenticated, redirect to appropriate dashboard\n  if (restricted && isAuthenticated && currentRole) {\n    const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/newsfeed';\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: dashboardPath,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n\n// Main Public Route Component\n_s3(UnifiedPublicRoute, \"f6gftIGV4sQRN5xBAirGwarhOpI=\", false, function () {\n  return [useUnifiedAuth];\n});\n_c3 = UnifiedPublicRoute;\nconst PublicRoute = props => {\n  _s4();\n  const location = useLocation();\n\n  // Use unified auth if explicitly requested\n  if (props.useUnified) {\n    return /*#__PURE__*/_jsxDEV(UnifiedPublicRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Determine which public route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return /*#__PURE__*/_jsxDEV(AdminPublicRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 12\n    }, this);\n  } else if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(StudentPublicRoute, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 12\n    }, this);\n  }\n\n  // For other routes, just render children\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: props.children\n  }, void 0, false);\n};\n_s4(PublicRoute, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c4 = PublicRoute;\nexport default PublicRoute;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"AdminPublicRoute\");\n$RefreshReg$(_c2, \"StudentPublicRoute\");\n$RefreshReg$(_c3, \"UnifiedPublicRoute\");\n$RefreshReg$(_c4, \"PublicRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdminAuth", "useStudentAuth", "useUnifiedAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminPublicRoute", "children", "restricted", "_s", "isAuthenticated", "user", "isLoading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "StudentPublicRoute", "_s2", "_c2", "UnifiedPublicRoute", "_s3", "currentRole", "dashboardPath", "_c3", "PublicRoute", "props", "_s4", "location", "useUnified", "pathname", "startsWith", "_c4", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/PublicRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { useUnifiedAuth } from '../../contexts/UnifiedAuthContext';\n\ninterface PublicRouteProps {\n  children: React.ReactNode;\n  restricted?: boolean; // If true, authenticated users will be redirected\n  useUnified?: boolean; // Flag to use unified auth context\n}\n\n// Admin Public Route Component\nconst AdminPublicRoute: React.FC<PublicRouteProps> = ({\n  children,\n  restricted = false,\n}) => {\n  const { isAuthenticated, user, isLoading } = useAdminAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Student Public Route Component\nconst StudentPublicRoute: React.FC<PublicRouteProps> = ({\n  children,\n  restricted = false,\n}) => {\n  const { isAuthenticated, user, isLoading } = useStudentAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // If route is restricted and user is authenticated, redirect to dashboard\n  if (restricted && isAuthenticated && user) {\n    return <Navigate to=\"/student/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Unified Public Route Component\nconst UnifiedPublicRoute: React.FC<PublicRouteProps> = ({\n  children,\n  restricted = false,\n}) => {\n  const { isAuthenticated, isLoading, currentRole } = useUnifiedAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // If route is restricted and user is authenticated, redirect to appropriate dashboard\n  if (restricted && isAuthenticated && currentRole) {\n    const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/newsfeed';\n    return <Navigate to={dashboardPath} replace />;\n  }\n\n  return <>{children}</>;\n};\n\n// Main Public Route Component\nconst PublicRoute: React.FC<PublicRouteProps> = (props) => {\n  const location = useLocation();\n\n  // Use unified auth if explicitly requested\n  if (props.useUnified) {\n    return <UnifiedPublicRoute {...props} />;\n  }\n\n  // Determine which public route component to use based on path\n  if (location.pathname.startsWith('/admin')) {\n    return <AdminPublicRoute {...props} />;\n  } else if (location.pathname.startsWith('/student')) {\n    return <StudentPublicRoute {...props} />;\n  }\n\n  // For other routes, just render children\n  return <>{props.children}</>;\n};\n\nexport default PublicRoute;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQnE;AACA,MAAMC,gBAA4C,GAAGA,CAAC;EACpDC,QAAQ;EACRC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGb,YAAY,CAAC,CAAC;;EAE3D;EACA,IAAIa,SAAS,EAAE;IACb,oBACET,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA;QAAKU,SAAS,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAIT,UAAU,IAAIE,eAAe,IAAIC,IAAI,EAAE;IACzC,oBAAOR,OAAA,CAACN,QAAQ;MAACqB,EAAE,EAAC,kBAAkB;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAE,EAAA,CAvBMH,gBAA4C;EAAA,QAIHP,YAAY;AAAA;AAAAqB,EAAA,GAJrDd,gBAA4C;AAwBlD,MAAMe,kBAA8C,GAAGA,CAAC;EACtDd,QAAQ;EACRC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAc,GAAA;EACJ,MAAM;IAAEZ,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGZ,cAAc,CAAC,CAAC;;EAE7D;EACA,IAAIY,SAAS,EAAE;IACb,oBACET,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA;QAAKU,SAAS,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAIT,UAAU,IAAIE,eAAe,IAAIC,IAAI,EAAE;IACzC,oBAAOR,OAAA,CAACN,QAAQ;MAACqB,EAAE,EAAC,oBAAoB;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAe,GAAA,CAvBMD,kBAA8C;EAAA,QAILrB,cAAc;AAAA;AAAAuB,GAAA,GAJvDF,kBAA8C;AAwBpD,MAAMG,kBAA8C,GAAGA,CAAC;EACtDjB,QAAQ;EACRC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAiB,GAAA;EACJ,MAAM;IAAEf,eAAe;IAAEE,SAAS;IAAEc;EAAY,CAAC,GAAGzB,cAAc,CAAC,CAAC;;EAEpE;EACA,IAAIW,SAAS,EAAE;IACb,oBACET,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA;QAAKU,SAAS,EAAC;MAAiE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;;EAEA;EACA,IAAIT,UAAU,IAAIE,eAAe,IAAIgB,WAAW,EAAE;IAChD,MAAMC,aAAa,GAAGD,WAAW,KAAK,OAAO,GAAG,kBAAkB,GAAG,mBAAmB;IACxF,oBAAOvB,OAAA,CAACN,QAAQ;MAACqB,EAAE,EAAES,aAAc;MAACR,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAChD;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;;AAED;AAAAkB,GAAA,CAxBMD,kBAA8C;EAAA,QAIEvB,cAAc;AAAA;AAAA2B,GAAA,GAJ9DJ,kBAA8C;AAyBpD,MAAMK,WAAuC,GAAIC,KAAK,IAAK;EAAAC,GAAA;EACzD,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIgC,KAAK,CAACG,UAAU,EAAE;IACpB,oBAAO9B,OAAA,CAACqB,kBAAkB;MAAA,GAAKM;IAAK;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC1C;;EAEA;EACA,IAAIe,QAAQ,CAACE,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;IAC1C,oBAAOhC,OAAA,CAACG,gBAAgB;MAAA,GAAKwB;IAAK;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACxC,CAAC,MAAM,IAAIe,QAAQ,CAACE,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACnD,oBAAOhC,OAAA,CAACkB,kBAAkB;MAAA,GAAKS;IAAK;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC1C;;EAEA;EACA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGuB,KAAK,CAACvB;EAAQ,gBAAG,CAAC;AAC9B,CAAC;AAACwB,GAAA,CAjBIF,WAAuC;EAAA,QAC1B/B,WAAW;AAAA;AAAAsC,GAAA,GADxBP,WAAuC;AAmB7C,eAAeA,WAAW;AAAC,IAAAT,EAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}