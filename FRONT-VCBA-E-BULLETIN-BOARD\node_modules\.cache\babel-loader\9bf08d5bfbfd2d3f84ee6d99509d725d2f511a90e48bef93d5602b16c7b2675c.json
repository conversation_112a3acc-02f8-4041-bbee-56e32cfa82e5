{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useRef,useEffect}from'react';import{ChevronDown,ChevronRight}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CascadingCategoryDropdown=_ref=>{var _categories$find,_categories$find$subc;let{categories,selectedCategoryId,selectedSubcategoryId,onCategoryChange,onSubcategoryChange,placeholder='Select Category',disabled=false,error,required=false,style,className}=_ref;const[isOpen,setIsOpen]=useState(false);const[hoveredCategory,setHoveredCategory]=useState(null);const[submenuPosition,setSubmenuPosition]=useState(null);const dropdownRef=useRef(null);const submenuRef=useRef(null);const hoverTimeoutRef=useRef(null);// Debug categories\nuseEffect(()=>{console.log('🔍 CascadingCategoryDropdown - Categories received:',{count:(categories===null||categories===void 0?void 0:categories.length)||0,categories:categories===null||categories===void 0?void 0:categories.map(cat=>{var _cat$subcategories;return{id:cat.category_id,name:cat.name,hasSubcategories:((_cat$subcategories=cat.subcategories)===null||_cat$subcategories===void 0?void 0:_cat$subcategories.length)||0};})});},[categories]);// Close dropdown when clicking outside\nuseEffect(()=>{const handleClickOutside=event=>{if(dropdownRef.current&&!dropdownRef.current.contains(event.target)){setIsOpen(false);setHoveredCategory(null);setSubmenuPosition(null);// Clear any pending timeout\nif(hoverTimeoutRef.current){clearTimeout(hoverTimeoutRef.current);hoverTimeoutRef.current=null;}}};document.addEventListener('mousedown',handleClickOutside);return()=>{document.removeEventListener('mousedown',handleClickOutside);// Cleanup timeout on unmount\nif(hoverTimeoutRef.current){clearTimeout(hoverTimeoutRef.current);}};},[]);// Get display text for selected items\nconst getDisplayText=()=>{if(!selectedCategoryId)return placeholder;const category=categories.find(cat=>cat.category_id===selectedCategoryId);if(!category)return placeholder;if(selectedSubcategoryId&&category.subcategories){const subcategory=category.subcategories.find(sub=>sub.subcategory_id===selectedSubcategoryId);if(subcategory){return\"\".concat(category.name,\" > \").concat(subcategory.name);}}return category.name;};const handleCategorySelect=categoryId=>{console.log('🎯 CascadingCategoryDropdown - Category selected:',categoryId);onCategoryChange(categoryId);onSubcategoryChange(null);// Clear subcategory when category changes\nsetIsOpen(false);setHoveredCategory(null);};const handleSubcategorySelect=subcategoryId=>{onSubcategoryChange(subcategoryId);setIsOpen(false);setHoveredCategory(null);};const handleCategoryHover=(categoryId,event)=>{// Clear any existing timeout\nif(hoverTimeoutRef.current){clearTimeout(hoverTimeoutRef.current);hoverTimeoutRef.current=null;}const category=categories.find(cat=>cat.category_id===categoryId);if(category&&category.subcategories&&category.subcategories.length>0){var _dropdownRef$current;console.log('🎯 Hovering category with subcategories:',category.name,category.subcategories.length);setHoveredCategory(categoryId);// Calculate submenu position\nconst rect=event.currentTarget.getBoundingClientRect();const dropdownRect=(_dropdownRef$current=dropdownRef.current)===null||_dropdownRef$current===void 0?void 0:_dropdownRef$current.getBoundingClientRect();if(dropdownRect){const viewportWidth=window.innerWidth;const submenuWidth=200;// Approximate submenu width\nlet leftPosition=dropdownRect.width+4;// Add 4px gap between main menu and submenu\n// Check if submenu would go off-screen and adjust if needed\nif(dropdownRect.left+leftPosition+submenuWidth>viewportWidth){leftPosition=-submenuWidth-4;// Position to the left instead\n}setSubmenuPosition({top:Math.max(0,rect.top-dropdownRect.top),// Ensure it doesn't go above dropdown\nleft:leftPosition});}}else{console.log('🎯 Hovering category without subcategories:',(category===null||category===void 0?void 0:category.name)||'Unknown');setHoveredCategory(null);setSubmenuPosition(null);}};const handleCategoryLeave=()=>{console.log('🚪 Leaving category, setting timeout...');// Clear any existing timeout\nif(hoverTimeoutRef.current){clearTimeout(hoverTimeoutRef.current);}// Set a longer delay to allow moving to submenu\nhoverTimeoutRef.current=setTimeout(()=>{console.log('⏰ Timeout triggered, closing submenu');setHoveredCategory(null);setSubmenuPosition(null);},300);// Increased delay to 300ms\n};const handleSubmenuEnter=()=>{console.log('🎯 Entering submenu, clearing timeout');// Clear timeout when entering submenu\nif(hoverTimeoutRef.current){clearTimeout(hoverTimeoutRef.current);hoverTimeoutRef.current=null;}};const handleSubmenuLeave=()=>{console.log('🚪 Leaving submenu, closing immediately');// Immediately close submenu when leaving it\nsetHoveredCategory(null);setSubmenuPosition(null);};// Styles\nconst dropdownStyle=_objectSpread({position:'relative',display:'inline-block',width:'100%'},style);const triggerStyle={width:'100%',padding:'0.75rem 1rem',border:error?'2px solid #ef4444':'1px solid #d1d5db',borderRadius:'8px',backgroundColor:disabled?'#f9fafb':'white',cursor:disabled?'not-allowed':'pointer',display:'flex',justifyContent:'space-between',alignItems:'center',fontSize:'0.875rem',color:selectedCategoryId?'#374151':'#9ca3af',outline:'none',transition:'all 0.2s ease',boxShadow:isOpen?'0 0 0 3px rgba(59, 130, 246, 0.1)':'none'};const menuStyle={position:'absolute',top:'100%',left:0,right:0,backgroundColor:'white',border:'1px solid #e5e7eb',borderRadius:'8px',boxShadow:'0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',zIndex:1000,marginTop:'4px',overflow:'hidden'// Remove scrollbar completely\n};const menuItemStyle={padding:'0.875rem 1rem',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'space-between',fontSize:'0.875rem',color:'#374151',borderBottom:'1px solid #f3f4f6',transition:'background-color 0.15s ease',position:'relative'};const submenuStyle={position:'absolute',top:(submenuPosition===null||submenuPosition===void 0?void 0:submenuPosition.top)||0,left:(submenuPosition===null||submenuPosition===void 0?void 0:submenuPosition.left)||0,backgroundColor:'white',border:'1px solid #e5e7eb',borderRadius:'8px',boxShadow:'0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',zIndex:1001,minWidth:'200px',overflow:'hidden'// Remove scrollbar from submenu as well\n};const submenuItemStyle={padding:'0.75rem 1rem',cursor:'pointer',fontSize:'0.875rem',color:'#374151',borderBottom:'1px solid #f3f4f6',transition:'background-color 0.15s ease'};return/*#__PURE__*/_jsxs(\"div\",{ref:dropdownRef,style:dropdownStyle,className:className,children:[/*#__PURE__*/_jsxs(\"div\",{style:triggerStyle,onClick:()=>!disabled&&setIsOpen(!isOpen),onKeyDown:e=>{if(e.key==='Enter'||e.key===' '){e.preventDefault();!disabled&&setIsOpen(!isOpen);}},tabIndex:disabled?-1:0,role:\"button\",\"aria-expanded\":isOpen,\"aria-haspopup\":\"listbox\",\"aria-required\":required,children:[/*#__PURE__*/_jsx(\"span\",{children:getDisplayText()}),/*#__PURE__*/_jsx(ChevronDown,{size:16,style:{transform:isOpen?'rotate(180deg)':'rotate(0deg)',transition:'transform 0.2s ease'}})]}),isOpen&&/*#__PURE__*/_jsx(\"div\",{style:menuStyle,role:\"listbox\",children:categories.map(category=>{const hasSubcategories=category.subcategories&&category.subcategories.length>0;const isSelected=selectedCategoryId===category.category_id;return/*#__PURE__*/_jsxs(\"div\",{style:_objectSpread(_objectSpread({},menuItemStyle),{},{backgroundColor:isSelected?'#eff6ff':hoveredCategory===category.category_id?'#f9fafb':'transparent'}),onClick:()=>handleCategorySelect(category.category_id),onMouseEnter:e=>handleCategoryHover(category.category_id,e),onMouseLeave:handleCategoryLeave,role:\"option\",\"aria-selected\":isSelected,children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:category.color_code,marginRight:'0.75rem'}}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]}),hasSubcategories&&/*#__PURE__*/_jsx(ChevronRight,{size:14,style:{color:'#9ca3af'}})]},category.category_id);})}),isOpen&&hoveredCategory&&submenuPosition&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:submenuPosition.top,left:submenuPosition.left>0?menuStyle.right||0:submenuPosition.left+200,width:'8px',height:'40px',zIndex:1000,backgroundColor:'transparent'},onMouseEnter:handleSubmenuEnter}),/*#__PURE__*/_jsx(\"div\",{ref:submenuRef,style:submenuStyle,onMouseEnter:handleSubmenuEnter,onMouseLeave:handleSubmenuLeave,children:(_categories$find=categories.find(cat=>cat.category_id===hoveredCategory))===null||_categories$find===void 0?void 0:(_categories$find$subc=_categories$find.subcategories)===null||_categories$find$subc===void 0?void 0:_categories$find$subc.map(subcategory=>{const isSelected=selectedSubcategoryId===subcategory.subcategory_id;return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},submenuItemStyle),{},{backgroundColor:isSelected?'#eff6ff':'transparent'}),onClick:()=>handleSubcategorySelect(subcategory.subcategory_id),onMouseEnter:e=>{e.currentTarget.style.backgroundColor=isSelected?'#eff6ff':'#f9fafb';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor=isSelected?'#eff6ff':'transparent';},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:subcategory.color_code,marginRight:'0.75rem'}}),/*#__PURE__*/_jsx(\"span\",{children:subcategory.name})]})},subcategory.subcategory_id);})})]}),error&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'0.25rem',fontSize:'0.75rem',color:'#ef4444'},children:error})]});};export default CascadingCategoryDropdown;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}