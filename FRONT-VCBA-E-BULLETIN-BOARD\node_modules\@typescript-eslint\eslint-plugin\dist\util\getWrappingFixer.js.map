{"version": 3, "file": "getWrappingFixer.js", "sourceRoot": "", "sources": ["../../src/util/getWrappingFixer.ts"], "names": [], "mappings": ";;;AACA,oDAAoE;AAsBpE;;;GAGG;AACH,SAAgB,gBAAgB,CAC9B,MAA2B;IAE3B,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;IAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAEtE,OAAO,CAAC,KAAK,EAAoB,EAAE;QACjC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YAC5C,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAEzC,0CAA0C;YAC1C,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE;gBACtC,8EAA8E;gBAC9E,uGAAuG;gBACvG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;aACpB;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAE/B,0CAA0C;QAC1C,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,iHAAiH;YACjH,yDAAyD;YACzD,IAAI,CAAC,gBAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;gBAC/C,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;aACpB;SACF;QAED,uCAAuC;QACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,wBAAwB,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;YACrE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;SACnB;QAED,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;AACJ,CAAC;AAvCD,4CAuCC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAAC,SAAwB;IAC7D,OAAO,CACL,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;QACzC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC5C,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;QACjD,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAClD,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAClD,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAChD,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;QAC/C,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,CAC3D,CAAC;AACJ,CAAC;AAXD,wDAWC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,IAAmB;IACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;IAE5B,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;QAC9C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAChD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;QACpD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAC9C;QACA,OAAO,IAAI,CAAC;KACb;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,MAAM,KAAK,IAAI,EACtB;QACA,OAAO,IAAI,CAAC;KACb;IAED,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC5C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;QAC/C,MAAM,CAAC,MAAM,KAAK,IAAI,EACtB;QACA,OAAO,IAAI,CAAC;KACb;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QACvD,MAAM,CAAC,GAAG,KAAK,IAAI,EACnB;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;GAGG;AACH,SAAS,wBAAwB,CAC/B,IAAmB,EACnB,UAA+B;IAE/B,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;QAE5B,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;YACtD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAO,CAAC;YAC7B,IACE,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACrC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAC5C;gBACA,+CAA+C;gBAC/C,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;gBACzD,IACE,cAAc,GAAG,CAAC;oBAClB,UAAU,CAAC,YAAY,CAAC,iBAAiB,CAAE,CAAC,KAAK,KAAK,GAAG,EACzD;oBACA,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,GAAG,MAAM,CAAC;KACf;AACH,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,IAAmB;IACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;IAE5B,MAAM;IACN,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;QACnD,OAAO,IAAI,CAAC;KACb;IAED,QAAQ;IACR,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC9C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAChD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB,CAAC;QACtD,IAAI,KAAK,MAAM,CAAC,IAAI,EACpB;QACA,OAAO,IAAI,CAAC;KACb;IAED,YAAY;IACZ,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;QACpD,IAAI,KAAK,MAAM,CAAC,IAAI,EACpB;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO;IACP,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,IAAI,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE;QAC3E,OAAO,IAAI,CAAC;KACb;IAED,OAAO;IACP,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QACvD,IAAI,KAAK,MAAM,CAAC,GAAG,EACnB;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}