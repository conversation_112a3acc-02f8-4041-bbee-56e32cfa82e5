{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n  key: \"hod4my\"\n}], [\"path\", {\n  d: \"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n  key: \"w4yl2u\"\n}], [\"path\", {\n  d: \"M3 5a2 2 0 0 0 2 2h3\",\n  key: \"f2jnh7\"\n}], [\"path\", {\n  d: \"M3 3v13a2 2 0 0 0 2 2h3\",\n  key: \"k8epm1\"\n}]];\nconst FolderTree = createLucideIcon(\"folder-tree\", __iconNode);\nexport { __iconNode, FolderTree as default };\n//# sourceMappingURL=folder-tree.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}