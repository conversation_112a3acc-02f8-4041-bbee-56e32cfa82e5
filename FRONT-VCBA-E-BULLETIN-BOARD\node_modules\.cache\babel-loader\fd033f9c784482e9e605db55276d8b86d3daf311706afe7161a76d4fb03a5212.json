{"ast": null, "code": "import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { API_ENDPOINTS, STUDENT_AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY, API_BASE_URL } from '../config/constants';\n\n// Types for comments\n\nclass CommentService {\n  // HTTP client instance\n\n  constructor(customHttpClient) {\n    this.client = void 0;\n    this.client = customHttpClient || httpClient; // Use custom client or default\n  }\n  // Determine which authentication method to use based on current user context\n  getCurrentUserAuth(preferredUserType) {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const adminUser = localStorage.getItem(ADMIN_USER_DATA_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    const studentUser = localStorage.getItem(STUDENT_USER_DATA_KEY);\n\n    // If a preferred user type is specified, use that context first\n    if (preferredUserType === 'admin' && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (preferred)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n    if (preferredUserType === 'student' && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (preferred)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // If no preference specified, determine based on current page context\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n    if (isAdminPage && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (admin page context)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n    if (isStudentPage && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (student page context)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Fallback: Use student authentication if available (prioritize student over admin)\n    if (studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (fallback)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Then try admin authentication\n    if (adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (fallback)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    // Last resort: check tokens without user data validation (prioritize student)\n    if (studentToken) {\n      console.log('🔑 CommentService - Using student token (no user data)');\n      return {\n        useStudentAuth: true,\n        token: studentToken,\n        userType: 'student'\n      };\n    }\n    if (adminToken) {\n      console.log('🔑 CommentService - Using admin token (no user data)');\n      return {\n        useStudentAuth: false,\n        token: adminToken,\n        userType: 'admin'\n      };\n    }\n\n    // No authentication available\n    console.log('🔑 CommentService - No authentication available');\n    return {\n      useStudentAuth: false,\n      token: null,\n      userType: null\n    };\n  }\n\n  // Get comments for an announcement\n  async getComments(filters) {\n    const params = this.buildQueryParams(filters);\n    return httpClient.get(API_ENDPOINTS.COMMENTS.BASE, params);\n  }\n\n  // Get comments by announcement ID\n  async getCommentsByAnnouncement(announcementId, options) {\n    const filters = {\n      announcement_id: announcementId,\n      page: (options === null || options === void 0 ? void 0 : options.page) || 1,\n      limit: (options === null || options === void 0 ? void 0 : options.limit) || 20,\n      sort_by: (options === null || options === void 0 ? void 0 : options.sort_by) || 'created_at',\n      sort_order: (options === null || options === void 0 ? void 0 : options.sort_order) || 'ASC'\n    };\n    return this.getComments(filters);\n  }\n\n  // Get comments by calendar event ID\n  async getCommentsByCalendar(calendarId, options) {\n    const filters = {\n      calendar_id: calendarId,\n      page: (options === null || options === void 0 ? void 0 : options.page) || 1,\n      limit: (options === null || options === void 0 ? void 0 : options.limit) || 20,\n      sort_by: (options === null || options === void 0 ? void 0 : options.sort_by) || 'created_at',\n      sort_order: (options === null || options === void 0 ? void 0 : options.sort_order) || 'ASC'\n    };\n    return this.getComments(filters);\n  }\n\n  // Get single comment by ID\n  async getCommentById(id) {\n    return httpClient.get(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));\n  }\n\n  // Create new comment with optional user type preference\n  async createComment(data, preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n\n    // Log authentication context for debugging\n    console.log('CommentService.createComment - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      tokenPrefix: token ? token.substring(0, 10) + '...' : null\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.COMMENTS.BASE}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Comment created successfully',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to create comment');\n      }\n    }\n\n    // Use httpClient for admin authentication or general fallback\n    return httpClient.post(API_ENDPOINTS.COMMENTS.BASE, data);\n  }\n\n  // Update comment\n  async updateComment(id, data) {\n    return httpClient.put(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()), data);\n  }\n\n  // Delete comment\n  async deleteComment(id) {\n    return httpClient.delete(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));\n  }\n\n  // Add reaction to comment\n  async addReaction(id, reactionId) {\n    console.log('❤️ CommentService - Adding reaction:', {\n      commentId: id,\n      reactionId,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.post(API_ENDPOINTS.COMMENTS.LIKE(id.toString()), {\n      reaction_id: reactionId\n    });\n  }\n\n  // Remove reaction from comment\n  async removeReaction(id) {\n    console.log('💔 CommentService - Removing reaction:', {\n      commentId: id,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.delete(API_ENDPOINTS.COMMENTS.LIKE(id.toString()));\n  }\n\n  // Flag comment\n  async flagComment(id, reason) {\n    return httpClient.post(API_ENDPOINTS.COMMENTS.FLAG(id.toString()), {\n      reason\n    });\n  }\n\n  // Get flagged comments (admin only)\n  async getFlaggedComments(filters) {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return httpClient.get(API_ENDPOINTS.COMMENTS.FLAGGED, params);\n  }\n\n  // Approve flagged comment (admin only)\n  async approveComment(id) {\n    return httpClient.post(API_ENDPOINTS.COMMENTS.APPROVE(id.toString()));\n  }\n\n  // Reject flagged comment (admin only)\n  async rejectComment(id) {\n    return httpClient.post(API_ENDPOINTS.COMMENTS.REJECT(id.toString()));\n  }\n\n  // Get comment reaction statistics\n  async getReactionStats(id) {\n    const endpoint = id ? API_ENDPOINTS.COMMENTS.REACTIONS(id.toString()) : API_ENDPOINTS.COMMENTS.BASE + '/reactions';\n    return httpClient.get(endpoint);\n  }\n\n  // Helper method to build query parameters\n  buildQueryParams(filters) {\n    const params = {};\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    return params;\n  }\n\n  // Create reply to a comment\n  async createReply(parentCommentId, data, preferredUserType) {\n    return this.createComment({\n      ...data,\n      parent_comment_id: parentCommentId\n    }, preferredUserType);\n  }\n\n  // Get replies for a comment\n  async getReplies(parentCommentId) {\n    var _response$data;\n    // This would typically be included in the parent comment response\n    // but can be implemented as a separate endpoint if needed\n    const response = await this.getCommentById(parentCommentId);\n    return ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.comment.replies) || [];\n  }\n\n  // Get comment count for an announcement\n  async getCommentCount(announcementId) {\n    var _response$data2;\n    const response = await this.getCommentsByAnnouncement(announcementId, {\n      limit: 1\n    });\n    return ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.pagination.total) || 0;\n  }\n\n  // Get recent comments for admin dashboard\n  async getRecentComments(limit = 10) {\n    return this.getComments({\n      page: 1,\n      limit,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Search comments\n  async searchComments(query, announcementId) {\n    const filters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    };\n    if (announcementId) {\n      filters.announcement_id = announcementId;\n    }\n\n    // Note: Search functionality would need to be implemented in the backend\n    // For now, this returns all comments with the filters\n    return this.getComments(filters);\n  }\n\n  // Get user's comments\n  async getUserComments(userId, userType) {\n    // This would require additional backend endpoint or filtering\n    // For now, return all comments (would need backend implementation)\n    return this.getComments({\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Bulk operations for admin\n  async bulkApproveComments(commentIds) {\n    // This would require a bulk endpoint in the backend\n    // For now, approve each comment individually\n    const promises = commentIds.map(id => this.approveComment(id));\n    await Promise.all(promises);\n    return {\n      success: true,\n      message: 'Comments approved successfully',\n      data: undefined\n    };\n  }\n  async bulkRejectComments(commentIds) {\n    // This would require a bulk endpoint in the backend\n    // For now, reject each comment individually\n    const promises = commentIds.map(id => this.rejectComment(id));\n    await Promise.all(promises);\n    return {\n      success: true,\n      message: 'Comments rejected successfully',\n      data: undefined\n    };\n  }\n\n  // Get comment statistics\n  async getCommentStatistics() {\n    // This would require a statistics endpoint in the backend\n    // For now, return mock data\n    return {\n      total: 0,\n      flagged: 0,\n      today: 0,\n      thisWeek: 0\n    };\n  }\n}\n\n// Role-specific comment service classes\nclass AdminCommentService extends CommentService {\n  constructor() {\n    super(adminHttpClient);\n  }\n  async createComment(data) {\n    console.log('🔧 AdminCommentService - Creating comment as admin');\n    return super.createComment(data, 'admin');\n  }\n  async getCommentsByAnnouncement(announcementId, options) {\n    console.log('🔧 AdminCommentService - Getting comments as admin');\n    return super.getCommentsByAnnouncement(announcementId, options);\n  }\n  async getCommentsByCalendar(calendarId, options) {\n    console.log('🔧 AdminCommentService - Getting calendar comments as admin');\n    return super.getCommentsByCalendar(calendarId, options);\n  }\n  async addReaction(id, reactionId) {\n    console.log('🔧 AdminCommentService - Adding reaction as admin');\n    return super.addReaction(id, reactionId);\n  }\n  async removeReaction(id) {\n    console.log('🔧 AdminCommentService - Removing reaction as admin');\n    return super.removeReaction(id);\n  }\n  async createReply(parentCommentId, data) {\n    console.log('🔧 AdminCommentService - Creating reply as admin');\n    return super.createReply(parentCommentId, data, 'admin');\n  }\n}\nclass StudentCommentService extends CommentService {\n  constructor() {\n    super(studentHttpClient);\n  }\n  async createComment(data) {\n    console.log('🔧 StudentCommentService - Creating comment as student');\n    return super.createComment(data, 'student');\n  }\n  async getCommentsByAnnouncement(announcementId, options) {\n    console.log('🔧 StudentCommentService - Getting comments as student');\n    return super.getCommentsByAnnouncement(announcementId, options);\n  }\n  async getCommentsByCalendar(calendarId, options) {\n    console.log('🔧 StudentCommentService - Getting calendar comments as student');\n    return super.getCommentsByCalendar(calendarId, options);\n  }\n  async addReaction(id, reactionId) {\n    console.log('🔧 StudentCommentService - Adding reaction as student');\n    return super.addReaction(id, reactionId);\n  }\n  async removeReaction(id) {\n    console.log('🔧 StudentCommentService - Removing reaction as student');\n    return super.removeReaction(id);\n  }\n  async createReply(parentCommentId, data) {\n    console.log('🔧 StudentCommentService - Creating reply as student');\n    return super.createReply(parentCommentId, data, 'student');\n  }\n}\n\n// Export service instances\nexport const commentService = new CommentService(); // Default/legacy service\n\n// Role-specific comment services with proper token management and user type enforcement\nexport const adminCommentServiceWithToken = new AdminCommentService();\nexport const studentCommentServiceWithToken = new StudentCommentService();\nexport default commentService;", "map": {"version": 3, "names": ["httpClient", "adminHttpClient", "studentHttpClient", "API_ENDPOINTS", "STUDENT_AUTH_TOKEN_KEY", "ADMIN_AUTH_TOKEN_KEY", "ADMIN_USER_DATA_KEY", "STUDENT_USER_DATA_KEY", "API_BASE_URL", "CommentService", "constructor", "customHttpClient", "client", "getCurrentUserAuth", "preferredUserType", "adminToken", "localStorage", "getItem", "adminUser", "studentToken", "studentUser", "userData", "JSON", "parse", "role", "console", "log", "useStudentAuth", "token", "userType", "e", "warn", "currentPath", "window", "location", "pathname", "isAdminPage", "includes", "isStudentPage", "getComments", "filters", "params", "buildQueryParams", "get", "COMMENTS", "BASE", "getCommentsByAnnouncement", "announcementId", "options", "announcement_id", "page", "limit", "sort_by", "sort_order", "getCommentsByCalendar", "calendarId", "calendar_id", "getCommentById", "id", "BY_ID", "toString", "createComment", "data", "hasToken", "tokenPrefix", "substring", "response", "fetch", "method", "headers", "body", "stringify", "ok", "Error", "status", "result", "json", "success", "message", "error", "post", "updateComment", "put", "deleteComment", "delete", "addReaction", "reactionId", "commentId", "clientType", "LIKE", "reaction_id", "removeReaction", "flagComment", "reason", "FLAG", "getFlaggedComments", "undefined", "FLAGGED", "approveComment", "APPROVE", "rejectComment", "REJECT", "getReactionStats", "endpoint", "REACTIONS", "Object", "entries", "for<PERSON>ach", "key", "value", "createReply", "parentCommentId", "parent_comment_id", "getReplies", "_response$data", "comment", "replies", "getCommentCount", "_response$data2", "pagination", "total", "getRecentComments", "searchComments", "query", "getUserComments", "userId", "bulkApproveComments", "commentIds", "promises", "map", "Promise", "all", "bulkRejectComments", "getCommentStatistics", "flagged", "today", "thisWeek", "AdminCommentService", "StudentCommentService", "commentService", "adminCommentServiceWithToken", "studentCommentServiceWithToken"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/commentService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { API_ENDPOINTS, STUDENT_AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY, API_BASE_URL } from '../config/constants';\nimport { ApiResponse } from '../types';\nimport { ReactionSummary, ReactionType, UserReaction } from './announcementService';\n\n// Types for comments\nexport interface Comment {\n  comment_id: number;\n  announcement_id?: number;\n  calendar_id?: number;\n  parent_comment_id?: number;\n  user_type: 'admin' | 'student';\n  user_id: number;\n  comment_text: string;\n  is_anonymous: boolean;\n  is_flagged: boolean;\n  flagged_by?: number;\n  flagged_reason?: string;\n  flagged_at?: string;\n  is_deleted: boolean;\n  deleted_at?: string;\n  created_at: string;\n  updated_at: string;\n  author_name?: string;\n  author_picture?: string;\n  reaction_count: number;\n  user_reaction?: UserReaction;\n  reactions?: ReactionSummary[];\n  replies?: Comment[];\n}\n\nexport interface CreateCommentData {\n  announcement_id?: number;\n  calendar_id?: number;\n  parent_comment_id?: number;\n  comment_text: string;\n  is_anonymous?: boolean;\n}\n\nexport interface UpdateCommentData {\n  comment_text: string;\n}\n\nexport interface CommentFilters {\n  announcement_id?: number;\n  calendar_id?: number;\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface FlaggedCommentFilters {\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface PaginatedCommentsResponse {\n  comments: Comment[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nclass CommentService {\n  private client: typeof httpClient; // HTTP client instance\n\n  constructor(customHttpClient?: typeof httpClient) {\n    this.client = customHttpClient || httpClient; // Use custom client or default\n  }\n  // Determine which authentication method to use based on current user context\n  private getCurrentUserAuth(preferredUserType?: 'admin' | 'student'): { useStudentAuth: boolean; token: string | null; userType: 'admin' | 'student' | null } {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const adminUser = localStorage.getItem(ADMIN_USER_DATA_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    const studentUser = localStorage.getItem(STUDENT_USER_DATA_KEY);\n\n    // If a preferred user type is specified, use that context first\n    if (preferredUserType === 'admin' && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (preferred)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    if (preferredUserType === 'student' && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (preferred)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // If no preference specified, determine based on current page context\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n\n    if (isAdminPage && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (admin page context)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    if (isStudentPage && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (student page context)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Fallback: Use student authentication if available (prioritize student over admin)\n    if (studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 CommentService - Using student authentication (fallback)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Then try admin authentication\n    if (adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 CommentService - Using admin authentication (fallback)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    // Last resort: check tokens without user data validation (prioritize student)\n    if (studentToken) {\n      console.log('🔑 CommentService - Using student token (no user data)');\n      return { useStudentAuth: true, token: studentToken, userType: 'student' };\n    }\n\n    if (adminToken) {\n      console.log('🔑 CommentService - Using admin token (no user data)');\n      return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n    }\n\n    // No authentication available\n    console.log('🔑 CommentService - No authentication available');\n    return { useStudentAuth: false, token: null, userType: null };\n  }\n\n  // Get comments for an announcement\n  async getComments(filters: CommentFilters): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    const params = this.buildQueryParams(filters);\n    return httpClient.get<PaginatedCommentsResponse>(API_ENDPOINTS.COMMENTS.BASE, params);\n  }\n\n  // Get comments by announcement ID\n  async getCommentsByAnnouncement(\n    announcementId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    const filters: CommentFilters = {\n      announcement_id: announcementId,\n      page: options?.page || 1,\n      limit: options?.limit || 20,\n      sort_by: options?.sort_by || 'created_at',\n      sort_order: options?.sort_order || 'ASC'\n    };\n    return this.getComments(filters);\n  }\n\n  // Get comments by calendar event ID\n  async getCommentsByCalendar(\n    calendarId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    const filters: CommentFilters = {\n      calendar_id: calendarId,\n      page: options?.page || 1,\n      limit: options?.limit || 20,\n      sort_by: options?.sort_by || 'created_at',\n      sort_order: options?.sort_order || 'ASC'\n    };\n    return this.getComments(filters);\n  }\n\n  // Get single comment by ID\n  async getCommentById(id: number): Promise<ApiResponse<{ comment: Comment }>> {\n    return httpClient.get<{ comment: Comment }>(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));\n  }\n\n  // Create new comment with optional user type preference\n  async createComment(data: CreateCommentData, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<{ comment: Comment }>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    // Log authentication context for debugging\n    console.log('CommentService.createComment - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      tokenPrefix: token ? token.substring(0, 10) + '...' : null\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.COMMENTS.BASE}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify(data)\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Comment created successfully',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to create comment');\n      }\n    }\n\n    // Use httpClient for admin authentication or general fallback\n    return httpClient.post<{ comment: Comment }>(API_ENDPOINTS.COMMENTS.BASE, data);\n  }\n\n  // Update comment\n  async updateComment(id: number, data: UpdateCommentData): Promise<ApiResponse<{ comment: Comment }>> {\n    return httpClient.put<{ comment: Comment }>(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()), data);\n  }\n\n  // Delete comment\n  async deleteComment(id: number): Promise<ApiResponse<void>> {\n    return httpClient.delete<void>(API_ENDPOINTS.COMMENTS.BY_ID(id.toString()));\n  }\n\n  // Add reaction to comment\n  async addReaction(id: number, reactionId: number): Promise<ApiResponse<void>> {\n    console.log('❤️ CommentService - Adding reaction:', {\n      commentId: id,\n      reactionId,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.post(API_ENDPOINTS.COMMENTS.LIKE(id.toString()), { reaction_id: reactionId });\n  }\n\n  // Remove reaction from comment\n  async removeReaction(id: number): Promise<ApiResponse<{ removed: boolean }>> {\n    console.log('💔 CommentService - Removing reaction:', {\n      commentId: id,\n      clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n    });\n    return this.client.delete(API_ENDPOINTS.COMMENTS.LIKE(id.toString()));\n  }\n\n  // Flag comment\n  async flagComment(id: number, reason?: string): Promise<ApiResponse<void>> {\n    return httpClient.post<void>(API_ENDPOINTS.COMMENTS.FLAG(id.toString()), { reason });\n  }\n\n  // Get flagged comments (admin only)\n  async getFlaggedComments(filters?: FlaggedCommentFilters): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n    return httpClient.get<PaginatedCommentsResponse>(API_ENDPOINTS.COMMENTS.FLAGGED, params);\n  }\n\n  // Approve flagged comment (admin only)\n  async approveComment(id: number): Promise<ApiResponse<void>> {\n    return httpClient.post<void>(API_ENDPOINTS.COMMENTS.APPROVE(id.toString()));\n  }\n\n  // Reject flagged comment (admin only)\n  async rejectComment(id: number): Promise<ApiResponse<void>> {\n    return httpClient.post<void>(API_ENDPOINTS.COMMENTS.REJECT(id.toString()));\n  }\n\n  // Get comment reaction statistics\n  async getReactionStats(id?: number): Promise<ApiResponse<{ stats: ReactionSummary[] }>> {\n    const endpoint = id \n      ? API_ENDPOINTS.COMMENTS.REACTIONS(id.toString())\n      : API_ENDPOINTS.COMMENTS.BASE + '/reactions';\n    return httpClient.get<{ stats: ReactionSummary[] }>(endpoint);\n  }\n\n  // Helper method to build query parameters\n  private buildQueryParams(filters: CommentFilters | FlaggedCommentFilters): Record<string, string> {\n    const params: Record<string, string> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    \n    return params;\n  }\n\n  // Create reply to a comment\n  async createReply(parentCommentId: number, data: Omit<CreateCommentData, 'parent_comment_id'>, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<{ comment: Comment }>> {\n    return this.createComment({\n      ...data,\n      parent_comment_id: parentCommentId\n    }, preferredUserType);\n  }\n\n  // Get replies for a comment\n  async getReplies(parentCommentId: number): Promise<Comment[]> {\n    // This would typically be included in the parent comment response\n    // but can be implemented as a separate endpoint if needed\n    const response = await this.getCommentById(parentCommentId);\n    return response.data?.comment.replies || [];\n  }\n\n  // Get comment count for an announcement\n  async getCommentCount(announcementId: number): Promise<number> {\n    const response = await this.getCommentsByAnnouncement(announcementId, { limit: 1 });\n    return response.data?.pagination.total || 0;\n  }\n\n  // Get recent comments for admin dashboard\n  async getRecentComments(limit: number = 10): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    return this.getComments({\n      page: 1,\n      limit,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Search comments\n  async searchComments(query: string, announcementId?: number): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    const filters: CommentFilters = {\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    };\n\n    if (announcementId) {\n      filters.announcement_id = announcementId;\n    }\n\n    // Note: Search functionality would need to be implemented in the backend\n    // For now, this returns all comments with the filters\n    return this.getComments(filters);\n  }\n\n  // Get user's comments\n  async getUserComments(userId: number, userType: 'admin' | 'student'): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    // This would require additional backend endpoint or filtering\n    // For now, return all comments (would need backend implementation)\n    return this.getComments({\n      page: 1,\n      limit: 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Bulk operations for admin\n  async bulkApproveComments(commentIds: number[]): Promise<ApiResponse<void>> {\n    // This would require a bulk endpoint in the backend\n    // For now, approve each comment individually\n    const promises = commentIds.map(id => this.approveComment(id));\n    await Promise.all(promises);\n    return { success: true, message: 'Comments approved successfully', data: undefined };\n  }\n\n  async bulkRejectComments(commentIds: number[]): Promise<ApiResponse<void>> {\n    // This would require a bulk endpoint in the backend\n    // For now, reject each comment individually\n    const promises = commentIds.map(id => this.rejectComment(id));\n    await Promise.all(promises);\n    return { success: true, message: 'Comments rejected successfully', data: undefined };\n  }\n\n  // Get comment statistics\n  async getCommentStatistics(): Promise<{\n    total: number;\n    flagged: number;\n    today: number;\n    thisWeek: number;\n  }> {\n    // This would require a statistics endpoint in the backend\n    // For now, return mock data\n    return {\n      total: 0,\n      flagged: 0,\n      today: 0,\n      thisWeek: 0\n    };\n  }\n}\n\n// Role-specific comment service classes\nclass AdminCommentService extends CommentService {\n  constructor() {\n    super(adminHttpClient);\n  }\n\n  async createComment(data: CreateCommentData): Promise<ApiResponse<{ comment: Comment }>> {\n    console.log('🔧 AdminCommentService - Creating comment as admin');\n    return super.createComment(data, 'admin');\n  }\n\n  async getCommentsByAnnouncement(\n    announcementId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    console.log('🔧 AdminCommentService - Getting comments as admin');\n    return super.getCommentsByAnnouncement(announcementId, options);\n  }\n\n  async getCommentsByCalendar(\n    calendarId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    console.log('🔧 AdminCommentService - Getting calendar comments as admin');\n    return super.getCommentsByCalendar(calendarId, options);\n  }\n\n  async addReaction(id: number, reactionId: number): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminCommentService - Adding reaction as admin');\n    return super.addReaction(id, reactionId);\n  }\n\n  async removeReaction(id: number): Promise<ApiResponse<{ removed: boolean }>> {\n    console.log('🔧 AdminCommentService - Removing reaction as admin');\n    return super.removeReaction(id);\n  }\n\n  async createReply(parentCommentId: number, data: Omit<CreateCommentData, 'parent_comment_id'>): Promise<ApiResponse<{ comment: Comment }>> {\n    console.log('🔧 AdminCommentService - Creating reply as admin');\n    return super.createReply(parentCommentId, data, 'admin');\n  }\n}\n\nclass StudentCommentService extends CommentService {\n  constructor() {\n    super(studentHttpClient);\n  }\n\n  async createComment(data: CreateCommentData): Promise<ApiResponse<{ comment: Comment }>> {\n    console.log('🔧 StudentCommentService - Creating comment as student');\n    return super.createComment(data, 'student');\n  }\n\n  async getCommentsByAnnouncement(\n    announcementId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    console.log('🔧 StudentCommentService - Getting comments as student');\n    return super.getCommentsByAnnouncement(announcementId, options);\n  }\n\n  async getCommentsByCalendar(\n    calendarId: number,\n    options?: { page?: number; limit?: number; sort_by?: string; sort_order?: 'ASC' | 'DESC' }\n  ): Promise<ApiResponse<PaginatedCommentsResponse>> {\n    console.log('🔧 StudentCommentService - Getting calendar comments as student');\n    return super.getCommentsByCalendar(calendarId, options);\n  }\n\n  async addReaction(id: number, reactionId: number): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentCommentService - Adding reaction as student');\n    return super.addReaction(id, reactionId);\n  }\n\n  async removeReaction(id: number): Promise<ApiResponse<{ removed: boolean }>> {\n    console.log('🔧 StudentCommentService - Removing reaction as student');\n    return super.removeReaction(id);\n  }\n\n  async createReply(parentCommentId: number, data: Omit<CreateCommentData, 'parent_comment_id'>): Promise<ApiResponse<{ comment: Comment }>> {\n    console.log('🔧 StudentCommentService - Creating reply as student');\n    return super.createReply(parentCommentId, data, 'student');\n  }\n}\n\n// Export service instances\nexport const commentService = new CommentService(); // Default/legacy service\n\n// Role-specific comment services with proper token management and user type enforcement\nexport const adminCommentServiceWithToken = new AdminCommentService();\nexport const studentCommentServiceWithToken = new StudentCommentService();\n\nexport default commentService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAC9E,SAASC,aAAa,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,qBAAqB;;AAI3J;;AAkEA,MAAMC,cAAc,CAAC;EACgB;;EAEnCC,WAAWA,CAACC,gBAAoC,EAAE;IAAA,KAF1CC,MAAM;IAGZ,IAAI,CAACA,MAAM,GAAGD,gBAAgB,IAAIX,UAAU,CAAC,CAAC;EAChD;EACA;EACQa,kBAAkBA,CAACC,iBAAuC,EAA2F;IAC3J,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACZ,oBAAoB,CAAC;IAC7D,MAAMa,SAAS,GAAGF,YAAY,CAACC,OAAO,CAACX,mBAAmB,CAAC;IAC3D,MAAMa,YAAY,GAAGH,YAAY,CAACC,OAAO,CAACb,sBAAsB,CAAC;IACjE,MAAMgB,WAAW,GAAGJ,YAAY,CAACC,OAAO,CAACV,qBAAqB,CAAC;;IAE/D;IACA,IAAIO,iBAAiB,KAAK,OAAO,IAAIC,UAAU,IAAIG,SAAS,EAAE;MAC5D,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QACtC,IAAIG,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;UACzE,OAAO;YAAEC,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAEb,UAAU;YAAEc,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;IAEA,IAAIjB,iBAAiB,KAAK,SAAS,IAAIK,YAAY,IAAIC,WAAW,EAAE;MAClE,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,CAAC;QACxC,IAAIC,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/BC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3E,OAAO;YAAEC,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAET,YAAY;YAAEU,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,WAAW,GAAGJ,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC;IAClD,MAAMC,aAAa,GAAGN,WAAW,CAACK,QAAQ,CAAC,UAAU,CAAC;IAEtD,IAAID,WAAW,IAAIrB,UAAU,IAAIG,SAAS,EAAE;MAC1C,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QACtC,IAAIG,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;UAClF,OAAO;YAAEC,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAEb,UAAU;YAAEc,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;IAEA,IAAIO,aAAa,IAAInB,YAAY,IAAIC,WAAW,EAAE;MAChD,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,CAAC;QACxC,IAAIC,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/BC,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;UACtF,OAAO;YAAEC,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAET,YAAY;YAAEU,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,IAAIZ,YAAY,IAAIC,WAAW,EAAE;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,CAAC;QACxC,IAAIC,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/BC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;UAC1E,OAAO;YAAEC,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAET,YAAY;YAAEU,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,IAAIhB,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QACtC,IAAIG,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;UACxE,OAAO;YAAEC,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAEb,UAAU;YAAEc,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVL,OAAO,CAACM,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;;IAEA;IACA,IAAIZ,YAAY,EAAE;MAChBM,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,OAAO;QAAEC,cAAc,EAAE,IAAI;QAAEC,KAAK,EAAET,YAAY;QAAEU,QAAQ,EAAE;MAAU,CAAC;IAC3E;IAEA,IAAId,UAAU,EAAE;MACdU,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,OAAO;QAAEC,cAAc,EAAE,KAAK;QAAEC,KAAK,EAAEb,UAAU;QAAEc,QAAQ,EAAE;MAAQ,CAAC;IACxE;;IAEA;IACAJ,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAO;MAAEC,cAAc,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC;EAC/D;;EAEA;EACA,MAAMU,WAAWA,CAACC,OAAuB,EAAmD;IAC1F,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACF,OAAO,CAAC;IAC7C,OAAOxC,UAAU,CAAC2C,GAAG,CAA4BxC,aAAa,CAACyC,QAAQ,CAACC,IAAI,EAAEJ,MAAM,CAAC;EACvF;;EAEA;EACA,MAAMK,yBAAyBA,CAC7BC,cAAsB,EACtBC,OAA0F,EACzC;IACjD,MAAMR,OAAuB,GAAG;MAC9BS,eAAe,EAAEF,cAAc;MAC/BG,IAAI,EAAE,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,IAAI,KAAI,CAAC;MACxBC,KAAK,EAAE,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,KAAK,KAAI,EAAE;MAC3BC,OAAO,EAAE,CAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,KAAI,YAAY;MACzCC,UAAU,EAAE,CAAAL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,UAAU,KAAI;IACrC,CAAC;IACD,OAAO,IAAI,CAACd,WAAW,CAACC,OAAO,CAAC;EAClC;;EAEA;EACA,MAAMc,qBAAqBA,CACzBC,UAAkB,EAClBP,OAA0F,EACzC;IACjD,MAAMR,OAAuB,GAAG;MAC9BgB,WAAW,EAAED,UAAU;MACvBL,IAAI,EAAE,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,IAAI,KAAI,CAAC;MACxBC,KAAK,EAAE,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,KAAK,KAAI,EAAE;MAC3BC,OAAO,EAAE,CAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,KAAI,YAAY;MACzCC,UAAU,EAAE,CAAAL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,UAAU,KAAI;IACrC,CAAC;IACD,OAAO,IAAI,CAACd,WAAW,CAACC,OAAO,CAAC;EAClC;;EAEA;EACA,MAAMiB,cAAcA,CAACC,EAAU,EAA8C;IAC3E,OAAO1D,UAAU,CAAC2C,GAAG,CAAuBxC,aAAa,CAACyC,QAAQ,CAACe,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1F;;EAEA;EACA,MAAMC,aAAaA,CAACC,IAAuB,EAAEhD,iBAAuC,EAA8C;IAChI,MAAM;MAAEa,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAAChB,kBAAkB,CAACC,iBAAiB,CAAC;;IAEtF;IACAW,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DZ,iBAAiB;MACjBa,cAAc;MACdoC,QAAQ,EAAE,CAAC,CAACnC,KAAK;MACjBC,QAAQ;MACRmC,WAAW,EAAEpC,KAAK,GAAGA,KAAK,CAACqC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;IACxD,CAAC,CAAC;;IAEF;IACA,IAAItC,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMsC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3D,YAAY,GAAGL,aAAa,CAACyC,QAAQ,CAACC,IAAI,EAAE,EAAE;UAC5EuB,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUzC,KAAK;UAClC,CAAC;UACD0C,IAAI,EAAEhD,IAAI,CAACiD,SAAS,CAACT,IAAI;QAC3B,CAAC,CAAC;QAEF,IAAI,CAACI,QAAQ,CAACM,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBP,QAAQ,CAACQ,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,8BAA8B;UACzDhB,IAAI,EAAEa,MAAM,CAACb;QACf,CAAC;MACH,CAAC,CAAC,OAAOiB,KAAU,EAAE;QACnB,MAAM,IAAIN,KAAK,CAACM,KAAK,CAACD,OAAO,IAAI,0BAA0B,CAAC;MAC9D;IACF;;IAEA;IACA,OAAO9E,UAAU,CAACgF,IAAI,CAAuB7E,aAAa,CAACyC,QAAQ,CAACC,IAAI,EAAEiB,IAAI,CAAC;EACjF;;EAEA;EACA,MAAMmB,aAAaA,CAACvB,EAAU,EAAEI,IAAuB,EAA8C;IACnG,OAAO9D,UAAU,CAACkF,GAAG,CAAuB/E,aAAa,CAACyC,QAAQ,CAACe,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAEE,IAAI,CAAC;EAChG;;EAEA;EACA,MAAMqB,aAAaA,CAACzB,EAAU,EAA8B;IAC1D,OAAO1D,UAAU,CAACoF,MAAM,CAAOjF,aAAa,CAACyC,QAAQ,CAACe,KAAK,CAACD,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7E;;EAEA;EACA,MAAMyB,WAAWA,CAAC3B,EAAU,EAAE4B,UAAkB,EAA8B;IAC5E7D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;MAClD6D,SAAS,EAAE7B,EAAE;MACb4B,UAAU;MACVE,UAAU,EAAE,IAAI,CAAC5E,MAAM,KAAKX,eAAe,GAAG,OAAO,GAAG,IAAI,CAACW,MAAM,KAAKV,iBAAiB,GAAG,SAAS,GAAG;IAC1G,CAAC,CAAC;IACF,OAAO,IAAI,CAACU,MAAM,CAACoE,IAAI,CAAC7E,aAAa,CAACyC,QAAQ,CAAC6C,IAAI,CAAC/B,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAE;MAAE8B,WAAW,EAAEJ;IAAW,CAAC,CAAC;EAClG;;EAEA;EACA,MAAMK,cAAcA,CAACjC,EAAU,EAA8C;IAC3EjC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MACpD6D,SAAS,EAAE7B,EAAE;MACb8B,UAAU,EAAE,IAAI,CAAC5E,MAAM,KAAKX,eAAe,GAAG,OAAO,GAAG,IAAI,CAACW,MAAM,KAAKV,iBAAiB,GAAG,SAAS,GAAG;IAC1G,CAAC,CAAC;IACF,OAAO,IAAI,CAACU,MAAM,CAACwE,MAAM,CAACjF,aAAa,CAACyC,QAAQ,CAAC6C,IAAI,CAAC/B,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvE;;EAEA;EACA,MAAMgC,WAAWA,CAAClC,EAAU,EAAEmC,MAAe,EAA8B;IACzE,OAAO7F,UAAU,CAACgF,IAAI,CAAO7E,aAAa,CAACyC,QAAQ,CAACkD,IAAI,CAACpC,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAE;MAAEiC;IAAO,CAAC,CAAC;EACtF;;EAEA;EACA,MAAME,kBAAkBA,CAACvD,OAA+B,EAAmD;IACzG,MAAMC,MAAM,GAAGD,OAAO,GAAG,IAAI,CAACE,gBAAgB,CAACF,OAAO,CAAC,GAAGwD,SAAS;IACnE,OAAOhG,UAAU,CAAC2C,GAAG,CAA4BxC,aAAa,CAACyC,QAAQ,CAACqD,OAAO,EAAExD,MAAM,CAAC;EAC1F;;EAEA;EACA,MAAMyD,cAAcA,CAACxC,EAAU,EAA8B;IAC3D,OAAO1D,UAAU,CAACgF,IAAI,CAAO7E,aAAa,CAACyC,QAAQ,CAACuD,OAAO,CAACzC,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7E;;EAEA;EACA,MAAMwC,aAAaA,CAAC1C,EAAU,EAA8B;IAC1D,OAAO1D,UAAU,CAACgF,IAAI,CAAO7E,aAAa,CAACyC,QAAQ,CAACyD,MAAM,CAAC3C,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5E;;EAEA;EACA,MAAM0C,gBAAgBA,CAAC5C,EAAW,EAAsD;IACtF,MAAM6C,QAAQ,GAAG7C,EAAE,GACfvD,aAAa,CAACyC,QAAQ,CAAC4D,SAAS,CAAC9C,EAAE,CAACE,QAAQ,CAAC,CAAC,CAAC,GAC/CzD,aAAa,CAACyC,QAAQ,CAACC,IAAI,GAAG,YAAY;IAC9C,OAAO7C,UAAU,CAAC2C,GAAG,CAA+B4D,QAAQ,CAAC;EAC/D;;EAEA;EACQ7D,gBAAgBA,CAACF,OAA+C,EAA0B;IAChG,MAAMC,MAA8B,GAAG,CAAC,CAAC;IAEzCgE,MAAM,CAACC,OAAO,CAAClE,OAAO,CAAC,CAACmE,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKb,SAAS,IAAIa,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDpE,MAAM,CAACmE,GAAG,CAAC,GAAGC,KAAK,CAACjD,QAAQ,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IAEF,OAAOnB,MAAM;EACf;;EAEA;EACA,MAAMqE,WAAWA,CAACC,eAAuB,EAAEjD,IAAkD,EAAEhD,iBAAuC,EAA8C;IAClL,OAAO,IAAI,CAAC+C,aAAa,CAAC;MACxB,GAAGC,IAAI;MACPkD,iBAAiB,EAAED;IACrB,CAAC,EAAEjG,iBAAiB,CAAC;EACvB;;EAEA;EACA,MAAMmG,UAAUA,CAACF,eAAuB,EAAsB;IAAA,IAAAG,cAAA;IAC5D;IACA;IACA,MAAMhD,QAAQ,GAAG,MAAM,IAAI,CAACT,cAAc,CAACsD,eAAe,CAAC;IAC3D,OAAO,EAAAG,cAAA,GAAAhD,QAAQ,CAACJ,IAAI,cAAAoD,cAAA,uBAAbA,cAAA,CAAeC,OAAO,CAACC,OAAO,KAAI,EAAE;EAC7C;;EAEA;EACA,MAAMC,eAAeA,CAACtE,cAAsB,EAAmB;IAAA,IAAAuE,eAAA;IAC7D,MAAMpD,QAAQ,GAAG,MAAM,IAAI,CAACpB,yBAAyB,CAACC,cAAc,EAAE;MAAEI,KAAK,EAAE;IAAE,CAAC,CAAC;IACnF,OAAO,EAAAmE,eAAA,GAAApD,QAAQ,CAACJ,IAAI,cAAAwD,eAAA,uBAAbA,eAAA,CAAeC,UAAU,CAACC,KAAK,KAAI,CAAC;EAC7C;;EAEA;EACA,MAAMC,iBAAiBA,CAACtE,KAAa,GAAG,EAAE,EAAmD;IAC3F,OAAO,IAAI,CAACZ,WAAW,CAAC;MACtBW,IAAI,EAAE,CAAC;MACPC,KAAK;MACLC,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMqE,cAAcA,CAACC,KAAa,EAAE5E,cAAuB,EAAmD;IAC5G,MAAMP,OAAuB,GAAG;MAC9BU,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC;IAED,IAAIN,cAAc,EAAE;MAClBP,OAAO,CAACS,eAAe,GAAGF,cAAc;IAC1C;;IAEA;IACA;IACA,OAAO,IAAI,CAACR,WAAW,CAACC,OAAO,CAAC;EAClC;;EAEA;EACA,MAAMoF,eAAeA,CAACC,MAAc,EAAEhG,QAA6B,EAAmD;IACpH;IACA;IACA,OAAO,IAAI,CAACU,WAAW,CAAC;MACtBW,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMyE,mBAAmBA,CAACC,UAAoB,EAA8B;IAC1E;IACA;IACA,MAAMC,QAAQ,GAAGD,UAAU,CAACE,GAAG,CAACvE,EAAE,IAAI,IAAI,CAACwC,cAAc,CAACxC,EAAE,CAAC,CAAC;IAC9D,MAAMwE,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC3B,OAAO;MAAEnD,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,gCAAgC;MAAEhB,IAAI,EAAEkC;IAAU,CAAC;EACtF;EAEA,MAAMoC,kBAAkBA,CAACL,UAAoB,EAA8B;IACzE;IACA;IACA,MAAMC,QAAQ,GAAGD,UAAU,CAACE,GAAG,CAACvE,EAAE,IAAI,IAAI,CAAC0C,aAAa,CAAC1C,EAAE,CAAC,CAAC;IAC7D,MAAMwE,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC3B,OAAO;MAAEnD,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,gCAAgC;MAAEhB,IAAI,EAAEkC;IAAU,CAAC;EACtF;;EAEA;EACA,MAAMqC,oBAAoBA,CAAA,EAKvB;IACD;IACA;IACA,OAAO;MACLb,KAAK,EAAE,CAAC;MACRc,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ,CAAC;EACH;AACF;;AAEA;AACA,MAAMC,mBAAmB,SAAShI,cAAc,CAAC;EAC/CC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACT,eAAe,CAAC;EACxB;EAEA,MAAM4D,aAAaA,CAACC,IAAuB,EAA8C;IACvFrC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjE,OAAO,KAAK,CAACmC,aAAa,CAACC,IAAI,EAAE,OAAO,CAAC;EAC3C;EAEA,MAAMhB,yBAAyBA,CAC7BC,cAAsB,EACtBC,OAA0F,EACzC;IACjDvB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjE,OAAO,KAAK,CAACoB,yBAAyB,CAACC,cAAc,EAAEC,OAAO,CAAC;EACjE;EAEA,MAAMM,qBAAqBA,CACzBC,UAAkB,EAClBP,OAA0F,EACzC;IACjDvB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAC1E,OAAO,KAAK,CAAC4B,qBAAqB,CAACC,UAAU,EAAEP,OAAO,CAAC;EACzD;EAEA,MAAMqC,WAAWA,CAAC3B,EAAU,EAAE4B,UAAkB,EAA8B;IAC5E7D,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChE,OAAO,KAAK,CAAC2D,WAAW,CAAC3B,EAAE,EAAE4B,UAAU,CAAC;EAC1C;EAEA,MAAMK,cAAcA,CAACjC,EAAU,EAA8C;IAC3EjC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClE,OAAO,KAAK,CAACiE,cAAc,CAACjC,EAAE,CAAC;EACjC;EAEA,MAAMoD,WAAWA,CAACC,eAAuB,EAAEjD,IAAkD,EAA8C;IACzIrC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/D,OAAO,KAAK,CAACoF,WAAW,CAACC,eAAe,EAAEjD,IAAI,EAAE,OAAO,CAAC;EAC1D;AACF;AAEA,MAAM4E,qBAAqB,SAASjI,cAAc,CAAC;EACjDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAACR,iBAAiB,CAAC;EAC1B;EAEA,MAAM2D,aAAaA,CAACC,IAAuB,EAA8C;IACvFrC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE,OAAO,KAAK,CAACmC,aAAa,CAACC,IAAI,EAAE,SAAS,CAAC;EAC7C;EAEA,MAAMhB,yBAAyBA,CAC7BC,cAAsB,EACtBC,OAA0F,EACzC;IACjDvB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE,OAAO,KAAK,CAACoB,yBAAyB,CAACC,cAAc,EAAEC,OAAO,CAAC;EACjE;EAEA,MAAMM,qBAAqBA,CACzBC,UAAkB,EAClBP,OAA0F,EACzC;IACjDvB,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;IAC9E,OAAO,KAAK,CAAC4B,qBAAqB,CAACC,UAAU,EAAEP,OAAO,CAAC;EACzD;EAEA,MAAMqC,WAAWA,CAAC3B,EAAU,EAAE4B,UAAkB,EAA8B;IAC5E7D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,OAAO,KAAK,CAAC2D,WAAW,CAAC3B,EAAE,EAAE4B,UAAU,CAAC;EAC1C;EAEA,MAAMK,cAAcA,CAACjC,EAAU,EAA8C;IAC3EjC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IACtE,OAAO,KAAK,CAACiE,cAAc,CAACjC,EAAE,CAAC;EACjC;EAEA,MAAMoD,WAAWA,CAACC,eAAuB,EAAEjD,IAAkD,EAA8C;IACzIrC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnE,OAAO,KAAK,CAACoF,WAAW,CAACC,eAAe,EAAEjD,IAAI,EAAE,SAAS,CAAC;EAC5D;AACF;;AAEA;AACA,OAAO,MAAM6E,cAAc,GAAG,IAAIlI,cAAc,CAAC,CAAC,CAAC,CAAC;;AAEpD;AACA,OAAO,MAAMmI,4BAA4B,GAAG,IAAIH,mBAAmB,CAAC,CAAC;AACrE,OAAO,MAAMI,8BAA8B,GAAG,IAAIH,qBAAqB,CAAC,CAAC;AAEzE,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}