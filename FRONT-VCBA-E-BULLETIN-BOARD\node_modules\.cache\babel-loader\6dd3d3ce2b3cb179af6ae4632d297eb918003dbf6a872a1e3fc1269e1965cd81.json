{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 19.772-.383.924\",\n  key: \"r7sl7d\"\n}], [\"path\", {\n  d: \"m13.148 14.228.383-.923\",\n  key: \"1d5zpm\"\n}], [\"path\", {\n  d: \"M13.148 19.772a3 3 0 1 0-2.296-5.544l-.383-.923\",\n  key: \"1ydik7\"\n}], [\"path\", {\n  d: \"m13.53 20.696-.382-.924a3 3 0 1 1-2.296-5.544\",\n  key: \"1m1vsf\"\n}], [\"path\", {\n  d: \"m14.772 15.852.923-.383\",\n  key: \"660p6e\"\n}], [\"path\", {\n  d: \"m14.772 18.148.923.383\",\n  key: \"hrcpis\"\n}], [\"path\", {\n  d: \"M4.2 15.1a7 7 0 1 1 9.93-9.858A7 7 0 0 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2\",\n  key: \"j2q98n\"\n}], [\"path\", {\n  d: \"m9.228 15.852-.923-.383\",\n  key: \"1p9ong\"\n}], [\"path\", {\n  d: \"m9.228 18.148-.923.383\",\n  key: \"6558rz\"\n}]];\nconst CloudCog = createLucideIcon(\"cloud-cog\", __iconNode);\nexport { __iconNode, CloudCog as default };\n//# sourceMappingURL=cloud-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}