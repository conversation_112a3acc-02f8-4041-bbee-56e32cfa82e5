{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"737tya\"\n}], [\"path\", {\n  d: \"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22\",\n  key: \"wt3hpn\"\n}]];\nconst FileImage = createLucideIcon(\"file-image\", __iconNode);\nexport { __iconNode, FileImage as default };\n//# sourceMappingURL=file-image.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}