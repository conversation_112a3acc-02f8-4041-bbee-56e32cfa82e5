{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"10\",\n  r: \"2\",\n  key: \"1xl4ub\"\n}], [\"path\", {\n  d: \"M8 12h8\",\n  key: \"1wcyev\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"10\",\n  r: \"2\",\n  key: \"r14t7q\"\n}], [\"path\", {\n  d: \"m6 20 .7-2.9A1.4 1.4 0 0 1 8.1 16h7.8a1.4 1.4 0 0 1 1.4 1l.7 3\",\n  key: \"l01ucn\"\n}]];\nconst CassetteTape = createLucideIcon(\"cassette-tape\", __iconNode);\nexport { __iconNode, CassetteTape as default };\n//# sourceMappingURL=cassette-tape.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}