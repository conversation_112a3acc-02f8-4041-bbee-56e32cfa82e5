{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18h4\",\n  key: \"1ulq68\"\n}], [\"path\", {\n  d: \"M11 6H3\",\n  key: \"1u26ik\"\n}], [\"path\", {\n  d: \"M15 6h6\",\n  key: \"1jlkvy\"\n}], [\"path\", {\n  d: \"M18 9V3\",\n  key: \"xwwp7m\"\n}], [\"path\", {\n  d: \"M7 12h8\",\n  key: \"7a1bxv\"\n}]];\nconst ListFilterPlus = createLucideIcon(\"list-filter-plus\", __iconNode);\nexport { __iconNode, ListFilterPlus as default };\n//# sourceMappingURL=list-filter-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}