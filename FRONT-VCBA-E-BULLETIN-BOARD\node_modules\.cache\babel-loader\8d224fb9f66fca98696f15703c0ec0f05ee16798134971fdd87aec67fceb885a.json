{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\layout\\\\StudentHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { useStudentAuth } from '../../../contexts/StudentAuthContext';\nimport StudentNotificationBell from '../NotificationBell';\nimport { Newspaper, Settings, GraduationCap, Menu, LogOut } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentHeader = ({\n  onToggleSidebar\n}) => {\n  _s();\n  var _user$firstName, _user$lastName;\n  const {\n    user,\n    logout\n  } = useStudentAuth();\n  const location = useLocation();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getPageInfo = () => {\n    const path = location.pathname;\n    switch (path) {\n      case '/student':\n      case '/student/newsfeed':\n        return {\n          title: 'Newsfeed',\n          subtitle: 'Latest Updates',\n          icon: Newspaper,\n          description: 'Stay informed with the latest announcements and news'\n        };\n      case '/student/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'Profile & Preferences',\n          icon: Settings,\n          description: 'Manage your profile and account preferences'\n        };\n      default:\n        return {\n          title: 'Student Portal',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: GraduationCap,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n  const pageInfo = getPageInfo();\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    style: {\n      background: 'white',\n      borderBottom: '1px solid #e0f2fe',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 9998\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onToggleSidebar,\n        style: {\n          background: 'none',\n          border: 'none',\n          padding: '0.5rem',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transition: 'background-color 0.2s ease',\n          fontSize: '1.25rem'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#f3f4f6';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'none';\n        },\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          size: 20,\n          color: \"#2d5016\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(pageInfo.icon, {\n            size: 24,\n            color: \"#2d5016\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1e40af',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: pageInfo.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            },\n            children: pageInfo.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          },\n          children: pageInfo.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          },\n          children: getCurrentTime()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          zIndex: 10000\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowUserMenu(!showUserMenu),\n          style: {\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '12px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            transition: 'background-color 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.background = '#f3f4f6';\n          },\n          onMouseLeave: e => {\n            if (!showUserMenu) {\n              e.currentTarget.style.background = 'none';\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem'\n            },\n            children: [user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0), user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#1e40af',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              },\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            },\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '100%',\n            right: 0,\n            marginTop: '0.5rem',\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n            border: '1px solid #e0f2fe',\n            minWidth: '200px',\n            zIndex: 9999\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#1e40af',\n                fontWeight: '600',\n                marginBottom: '0.25rem'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                marginBottom: '1rem'\n              },\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              style: {\n                border: 'none',\n                borderTop: '1px solid #e0f2fe',\n                margin: '1rem 0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowUserMenu(false);\n                handleLogout();\n              },\n              style: {\n                width: '100%',\n                background: 'none',\n                border: 'none',\n                padding: '0.75rem',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                textAlign: 'left',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                transition: 'background-color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.background = '#fef2f2';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.background = 'none';\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 999\n      },\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentHeader, \"rqDmkxMhxLz0249hNPKn+zs9800=\", false, function () {\n  return [useStudentAuth, useLocation];\n});\n_c = StudentHeader;\nexport default StudentHeader;\nvar _c;\n$RefreshReg$(_c, \"StudentHeader\");", "map": {"version": 3, "names": ["React", "useState", "useLocation", "useStudentAuth", "StudentNotificationBell", "Newspaper", "Settings", "GraduationCap", "<PERSON><PERSON>", "LogOut", "jsxDEV", "_jsxDEV", "StudentHeader", "onToggleSidebar", "_s", "_user$firstName", "_user$lastName", "user", "logout", "location", "showUserMenu", "setShowUserMenu", "handleLogout", "error", "console", "getCurrentTime", "Date", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "getPageInfo", "path", "pathname", "title", "subtitle", "icon", "description", "pageInfo", "style", "background", "borderBottom", "padding", "display", "alignItems", "justifyContent", "boxShadow", "position", "top", "zIndex", "children", "gap", "onClick", "border", "borderRadius", "cursor", "transition", "fontSize", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "margin", "fontWeight", "width", "height", "firstName", "char<PERSON>t", "lastName", "textAlign", "lineHeight", "transform", "right", "marginTop", "min<PERSON><PERSON><PERSON>", "email", "borderTop", "left", "bottom", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/layout/StudentHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { useStudentAuth } from '../../../contexts/StudentAuthContext';\nimport StudentNotificationBell from '../NotificationBell';\nimport { Newspaper, Settings, GraduationCap, Menu, LogOut } from 'lucide-react';\n\ninterface StudentHeaderProps {\n  onToggleSidebar: () => void;\n}\n\nconst StudentHeader: React.FC<StudentHeaderProps> = ({ onToggleSidebar }) => {\n  const { user, logout } = useStudentAuth();\n  const location = useLocation();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getPageInfo = () => {\n    const path = location.pathname;\n\n    switch (path) {\n      case '/student':\n      case '/student/newsfeed':\n        return {\n          title: 'Newsfeed',\n          subtitle: 'Latest Updates',\n          icon: Newspaper,\n          description: 'Stay informed with the latest announcements and news'\n        };\n      case '/student/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'Profile & Preferences',\n          icon: Settings,\n          description: 'Manage your profile and account preferences'\n        };\n      default:\n        return {\n          title: 'Student Portal',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: GraduationCap,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n\n  const pageInfo = getPageInfo();\n\n  return (\n    <header style={{\n      background: 'white',\n      borderBottom: '1px solid #e0f2fe',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 9998\n    }}>\n      {/* Left Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Sidebar Toggle */}\n        <button\n          onClick={onToggleSidebar}\n          style={{\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transition: 'background-color 0.2s ease',\n            fontSize: '1.25rem'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#f3f4f6';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'none';\n          }}\n        >\n          <Menu size={20} color=\"#2d5016\" />\n        </button>\n\n        {/* Page Title */}\n        <div>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.25rem' }}>\n            <pageInfo.icon size={24} color=\"#2d5016\" />\n            <h1 style={{\n              margin: 0,\n              color: '#1e40af',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            }}>\n              {pageInfo.title}\n            </h1>\n            <span style={{\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            }}>\n              {pageInfo.subtitle}\n            </span>\n          </div>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          }}>\n            {pageInfo.description}\n          </p>\n          <p style={{\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          }}>\n            {getCurrentTime()}\n          </p>\n        </div>\n      </div>\n\n      {/* Right Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Notifications */}\n        <StudentNotificationBell />\n\n        {/* User Profile */}\n        <div style={{ position: 'relative', zIndex: 10000 }}>\n          <button\n            onClick={() => setShowUserMenu(!showUserMenu)}\n            style={{\n              background: 'none',\n              border: 'none',\n              padding: '0.5rem',\n              borderRadius: '12px',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              transition: 'background-color 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = '#f3f4f6';\n            }}\n            onMouseLeave={(e) => {\n              if (!showUserMenu) {\n                e.currentTarget.style.background = 'none';\n              }\n            }}\n          >\n            {/* Avatar */}\n            <div style={{\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem'\n            }}>\n              {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}\n            </div>\n\n            {/* User Info */}\n            <div style={{ textAlign: 'left' }}>\n              <div style={{\n                color: '#1e40af',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              }}>\n                {user?.firstName} {user?.lastName}\n              </div>\n              <div style={{\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              }}>\n                Student\n              </div>\n            </div>\n\n            {/* Dropdown Arrow */}\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            }}>\n              ▼\n            </span>\n          </button>\n\n          {/* User Dropdown Menu */}\n          {showUserMenu && (\n            <div style={{\n              position: 'absolute',\n              top: '100%',\n              right: 0,\n              marginTop: '0.5rem',\n              background: 'white',\n              borderRadius: '12px',\n              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n              border: '1px solid #e0f2fe',\n              minWidth: '200px',\n              zIndex: 9999\n            }}>\n              <div style={{ padding: '1rem' }}>\n                <div style={{\n                  color: '#1e40af',\n                  fontWeight: '600',\n                  marginBottom: '0.25rem'\n                }}>\n                  {user?.firstName} {user?.lastName}\n                </div>\n                <div style={{\n                  color: '#6b7280',\n                  fontSize: '0.875rem',\n                  marginBottom: '1rem'\n                }}>\n                  {user?.email}\n                </div>\n                \n                <hr style={{\n                  border: 'none',\n                  borderTop: '1px solid #e0f2fe',\n                  margin: '1rem 0'\n                }} />\n\n                <button\n                  onClick={() => {\n                    setShowUserMenu(false);\n                    handleLogout();\n                  }}\n                  style={{\n                    width: '100%',\n                    background: 'none',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    textAlign: 'left',\n                    color: '#dc2626',\n                    fontSize: '0.875rem',\n                    transition: 'background-color 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.background = '#fef2f2';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.background = 'none';\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <LogOut size={16} color=\"#ef4444\" />\n                    Logout\n                  </span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 999\n          }}\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default StudentHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,QAAQ,sCAAsC;AACrE,OAAOC,uBAAuB,MAAM,qBAAqB;AACzD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhF,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,cAAA;EAC3E,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGf,cAAc,CAAC,CAAC;EACzC,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMqB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMJ,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,IAAI,GAAGhB,QAAQ,CAACiB,QAAQ;IAE9B,QAAQD,IAAI;MACV,KAAK,UAAU;MACf,KAAK,mBAAmB;QACtB,OAAO;UACLE,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,gBAAgB;UAC1BC,IAAI,EAAElC,SAAS;UACfmC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,mBAAmB;QACtB,OAAO;UACLH,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,uBAAuB;UACjCC,IAAI,EAAEjC,QAAQ;UACdkC,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACLH,KAAK,EAAE,gBAAgB;UACvBC,QAAQ,EAAE,uBAAuB;UACjCC,IAAI,EAAEhC,aAAa;UACnBiC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACEvB,OAAA;IAAQ+B,KAAK,EAAE;MACbC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,mBAAmB;MACjCC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,SAAS,EAAE,gCAAgC;MAC3CC,QAAQ,EAAE,QAAQ;MAClBC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBAEA1C,OAAA;MAAK+B,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE1C,OAAA;QACE4C,OAAO,EAAE1C,eAAgB;QACzB6B,KAAK,EAAE;UACLC,UAAU,EAAE,MAAM;UAClBa,MAAM,EAAE,MAAM;UACdX,OAAO,EAAE,QAAQ;UACjBY,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBZ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBW,UAAU,EAAE,4BAA4B;UACxCC,QAAQ,EAAE;QACZ,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;QAC9C,CAAE;QACFqB,YAAY,EAAGF,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;QAC3C,CAAE;QAAAU,QAAA,eAEF1C,OAAA,CAACH,IAAI;UAACyD,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGT3D,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAK+B,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE,SAAS;YAAEiB,YAAY,EAAE;UAAU,CAAE;UAAAlB,QAAA,gBAC7F1C,OAAA,CAAC8B,QAAQ,CAACF,IAAI;YAAC0B,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C3D,OAAA;YAAI+B,KAAK,EAAE;cACT8B,MAAM,EAAE,CAAC;cACTN,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,QAAQ;cAClBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACJ;UAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACL3D,OAAA;YAAM+B,KAAK,EAAE;cACXC,UAAU,EAAE,mDAAmD;cAC/DuB,KAAK,EAAE,OAAO;cACdrB,OAAO,EAAE,iBAAiB;cAC1BY,YAAY,EAAE,MAAM;cACpBG,QAAQ,EAAE,SAAS;cACnBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACH;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3D,OAAA;UAAG+B,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE,UAAU;YACpBW,YAAY,EAAE;UAChB,CAAE;UAAAlB,QAAA,EACCZ,QAAQ,CAACD;QAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJ3D,OAAA;UAAG+B,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE;UACZ,CAAE;UAAAP,QAAA,EACC5B,cAAc,CAAC;QAAC;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAK+B,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE1C,OAAA,CAACP,uBAAuB;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3B3D,OAAA;QAAK+B,KAAK,EAAE;UAAEQ,QAAQ,EAAE,UAAU;UAAEE,MAAM,EAAE;QAAM,CAAE;QAAAC,QAAA,gBAClD1C,OAAA;UACE4C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CsB,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBa,MAAM,EAAE,MAAM;YACdX,OAAO,EAAE,QAAQ;YACjBY,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,SAAS;YACjBZ,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBO,GAAG,EAAE,SAAS;YACdK,UAAU,EAAE;UACd,CAAE;UACFE,YAAY,EAAGC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;UAC9C,CAAE;UACFqB,YAAY,EAAGF,CAAC,IAAK;YACnB,IAAI,CAAC1C,YAAY,EAAE;cACjB0C,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;YAC3C;UACF,CAAE;UAAAU,QAAA,gBAGF1C,OAAA;YAAK+B,KAAK,EAAE;cACVgC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,KAAK;cACnBd,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBkB,KAAK,EAAE,OAAO;cACdO,UAAU,EAAE,KAAK;cACjBb,QAAQ,EAAE;YACZ,CAAE;YAAAP,QAAA,GACCpC,IAAI,aAAJA,IAAI,wBAAAF,eAAA,GAAJE,IAAI,CAAE2D,SAAS,cAAA7D,eAAA,uBAAfA,eAAA,CAAiB8D,MAAM,CAAC,CAAC,CAAC,EAAE5D,IAAI,aAAJA,IAAI,wBAAAD,cAAA,GAAJC,IAAI,CAAE6D,QAAQ,cAAA9D,cAAA,uBAAdA,cAAA,CAAgB6D,MAAM,CAAC,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGN3D,OAAA;YAAK+B,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBAChC1C,OAAA;cAAK+B,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBO,UAAU,EAAE,KAAK;gBACjBb,QAAQ,EAAE,QAAQ;gBAClBoB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,GACCpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,SAAS,EAAC,GAAC,EAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN3D,OAAA;cAAK+B,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,SAAS;gBACnBoB,UAAU,EAAE;cACd,CAAE;cAAA3B,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3D,OAAA;YAAM+B,KAAK,EAAE;cACXwB,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,SAAS;cACnBqB,SAAS,EAAE7D,YAAY,GAAG,gBAAgB,GAAG,cAAc;cAC3DuC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGRlD,YAAY,iBACXT,OAAA;UAAK+B,KAAK,EAAE;YACVQ,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,MAAM;YACX+B,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,QAAQ;YACnBxC,UAAU,EAAE,OAAO;YACnBc,YAAY,EAAE,MAAM;YACpBR,SAAS,EAAE,iCAAiC;YAC5CO,MAAM,EAAE,mBAAmB;YAC3B4B,QAAQ,EAAE,OAAO;YACjBhC,MAAM,EAAE;UACV,CAAE;UAAAC,QAAA,eACA1C,OAAA;YAAK+B,KAAK,EAAE;cAAEG,OAAO,EAAE;YAAO,CAAE;YAAAQ,QAAA,gBAC9B1C,OAAA;cAAK+B,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBO,UAAU,EAAE,KAAK;gBACjBF,YAAY,EAAE;cAChB,CAAE;cAAAlB,QAAA,GACCpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,SAAS,EAAC,GAAC,EAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN3D,OAAA;cAAK+B,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBW,YAAY,EAAE;cAChB,CAAE;cAAAlB,QAAA,EACCpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN3D,OAAA;cAAI+B,KAAK,EAAE;gBACTc,MAAM,EAAE,MAAM;gBACd8B,SAAS,EAAE,mBAAmB;gBAC9Bd,MAAM,EAAE;cACV;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEL3D,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM;gBACblC,eAAe,CAAC,KAAK,CAAC;gBACtBC,YAAY,CAAC,CAAC;cAChB,CAAE;cACFoB,KAAK,EAAE;gBACLgC,KAAK,EAAE,MAAM;gBACb/B,UAAU,EAAE,MAAM;gBAClBa,MAAM,EAAE,MAAM;gBACdX,OAAO,EAAE,SAAS;gBAClBY,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBqB,SAAS,EAAE,MAAM;gBACjBb,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBD,UAAU,EAAE;cACd,CAAE;cACFE,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;cAC9C,CAAE;cACFqB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;cAC3C,CAAE;cAAAU,QAAA,eAEF1C,OAAA;gBAAM+B,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,GAAG,EAAE;gBAAS,CAAE;gBAAAD,QAAA,gBACpE1C,OAAA,CAACF,MAAM;kBAACwD,IAAI,EAAE,EAAG;kBAACC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlD,YAAY,iBACXT,OAAA;MACE+B,KAAK,EAAE;QACLQ,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNoC,IAAI,EAAE,CAAC;QACPL,KAAK,EAAE,CAAC;QACRM,MAAM,EAAE,CAAC;QACTpC,MAAM,EAAE;MACV,CAAE;MACFG,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,KAAK;IAAE;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACxD,EAAA,CA1SIF,aAA2C;EAAA,QACtBT,cAAc,EACtBD,WAAW;AAAA;AAAAuF,EAAA,GAFxB7E,aAA2C;AA4SjD,eAAeA,aAAa;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}