{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 7 10 10-5 5V2l5 5L7 17\",\n  key: \"1q5490\"\n}]];\nconst Bluetooth = createLucideIcon(\"bluetooth\", __iconNode);\nexport { __iconNode, Bluetooth as default };\n//# sourceMappingURL=bluetooth.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}