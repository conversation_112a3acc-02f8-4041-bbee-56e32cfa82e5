{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 14h10\",\n  key: \"1w8e9d\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v1.344\",\n  key: \"1e62lh\"\n}], [\"path\", {\n  d: \"m17 18 4-4-4-4\",\n  key: \"z2g111\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 1.793-1.113\",\n  key: \"bjbb7m\"\n}], [\"rect\", {\n  x: \"8\",\n  y: \"2\",\n  width: \"8\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"ublpy\"\n}]];\nconst ClipboardPaste = createLucideIcon(\"clipboard-paste\", __iconNode);\nexport { __iconNode, ClipboardPaste as default };\n//# sourceMappingURL=clipboard-paste.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}