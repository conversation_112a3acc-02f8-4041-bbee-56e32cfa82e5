{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v12\",\n  key: \"1x0j5s\"\n}], [\"path\", {\n  d: \"m17 8-5-5-5 5\",\n  key: \"7q97r8\"\n}], [\"path\", {\n  d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n  key: \"ih7n3h\"\n}]];\nconst Upload = createLucideIcon(\"upload\", __iconNode);\nexport { __iconNode, Upload as default };\n//# sourceMappingURL=upload.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}