{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.28 9.05a5.5 5.5 0 1 0-10.56 0A5.5 5.5 0 1 0 12 17.66a5.5 5.5 0 1 0 5.28-8.6Z\",\n  key: \"27yuqz\"\n}], [\"path\", {\n  d: \"M12 17.66L12 22\",\n  key: \"ogfahf\"\n}]];\nconst Club = createLucideIcon(\"club\", __iconNode);\nexport { __iconNode, Club as default };\n//# sourceMappingURL=club.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}