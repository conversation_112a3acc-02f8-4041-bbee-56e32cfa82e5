{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16v5\",\n  key: \"zza2cw\"\n}], [\"path\", {\n  d: \"M16 14v7\",\n  key: \"1g90b9\"\n}], [\"path\", {\n  d: \"M20 10v11\",\n  key: \"1iqoj0\"\n}], [\"path\", {\n  d: \"m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15\",\n  key: \"1fw8x9\"\n}], [\"path\", {\n  d: \"M4 18v3\",\n  key: \"1yp0dc\"\n}], [\"path\", {\n  d: \"M8 14v7\",\n  key: \"n3cwzv\"\n}]];\nconst ChartNoAxesCombined = createLucideIcon(\"chart-no-axes-combined\", __iconNode);\nexport { __iconNode, ChartNoAxesCombined as default };\n//# sourceMappingURL=chart-no-axes-combined.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}