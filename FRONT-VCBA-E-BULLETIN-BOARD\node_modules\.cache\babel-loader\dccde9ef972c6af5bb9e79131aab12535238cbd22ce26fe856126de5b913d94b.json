{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17\",\n  key: \"geh8rc\"\n}], [\"path\", {\n  d: \"m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9\",\n  key: \"1fto5m\"\n}], [\"path\", {\n  d: \"m2 16 6 6\",\n  key: \"1pfhp9\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"9\",\n  r: \"2.9\",\n  key: \"1n0dlu\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"151irh\"\n}]];\nconst HandCoins = createLucideIcon(\"hand-coins\", __iconNode);\nexport { __iconNode, HandCoins as default };\n//# sourceMappingURL=hand-coins.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}