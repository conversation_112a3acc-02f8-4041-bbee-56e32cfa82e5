{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12h.01\",\n  key: \"1kxr2c\"\n}], [\"path\", {\n  d: \"M18 9V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14\",\n  key: \"1bnhmg\"\n}], [\"path\", {\n  d: \"M2 20h8\",\n  key: \"10ntw1\"\n}], [\"path\", {\n  d: \"M20 17v-2a2 2 0 1 0-4 0v2\",\n  key: \"pwaxnr\"\n}], [\"rect\", {\n  x: \"14\",\n  y: \"17\",\n  width: \"8\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"15pjcy\"\n}]];\nconst DoorClosedLocked = createLucideIcon(\"door-closed-locked\", __iconNode);\nexport { __iconNode, DoorClosedLocked as default };\n//# sourceMappingURL=door-closed-locked.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}