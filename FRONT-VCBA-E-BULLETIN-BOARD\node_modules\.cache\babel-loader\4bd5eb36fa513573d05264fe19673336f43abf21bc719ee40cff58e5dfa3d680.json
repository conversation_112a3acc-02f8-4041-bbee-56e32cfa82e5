{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978\",\n  key: \"1n3hpd\"\n}], [\"path\", {\n  d: \"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978\",\n  key: \"rfe1zi\"\n}], [\"path\", {\n  d: \"M18 9h1.5a1 1 0 0 0 0-5H18\",\n  key: \"7xy6bh\"\n}], [\"path\", {\n  d: \"M4 22h16\",\n  key: \"57wxv0\"\n}], [\"path\", {\n  d: \"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z\",\n  key: \"1mhfuq\"\n}], [\"path\", {\n  d: \"M6 9H4.5a1 1 0 0 1 0-5H6\",\n  key: \"tex48p\"\n}]];\nconst Trophy = createLucideIcon(\"trophy\", __iconNode);\nexport { __iconNode, Trophy as default };\n//# sourceMappingURL=trophy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}