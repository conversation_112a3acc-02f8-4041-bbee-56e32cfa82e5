{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12v6\",\n  key: \"3ahymv\"\n}], [\"path\", {\n  d: \"M4.077 10.615A1 1 0 0 0 5 12h14a1 1 0 0 0 .923-1.385l-3.077-7.384A2 2 0 0 0 15 2H9a2 2 0 0 0-1.846 1.23Z\",\n  key: \"1l7kg2\"\n}], [\"path\", {\n  d: \"M8 20a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1z\",\n  key: \"1mmzpi\"\n}]];\nconst Lamp = createLucideIcon(\"lamp\", __iconNode);\nexport { __iconNode, Lamp as default };\n//# sourceMappingURL=lamp.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}