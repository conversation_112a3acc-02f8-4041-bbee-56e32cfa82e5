{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\NewsFeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart, Users, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = (imagePath, userRole) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath, userRole);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  userRole,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = () => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(),\n        userRole: userRole,\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(),\n            userRole: userRole,\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n\n// Props interface for the unified NewsFeed component\n_c2 = ImageGallery;\n// Main unified NewsFeed Component\nconst NewsFeed = ({\n  userRole\n}) => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine current user role and context\n  const currentRole = userRole || (adminAuth.isAuthenticated ? 'admin' : studentAuth.isAuthenticated ? 'student' : null);\n  const currentUser = currentRole === 'admin' ? adminAuth.user : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth.logout : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const {\n    categories\n  } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#ef4444',\n            marginBottom: '1rem'\n          },\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            marginBottom: '1.5rem'\n          },\n          children: \"Please log in to access the newsfeed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          style: {\n            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            padding: '0.75rem 1.5rem',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const authToken = currentRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${authToken}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n  }, []);\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: !event.user_has_reacted,\n          reaction_count: event.user_has_reacted ? (event.reaction_count || 1) - 1 : (event.reaction_count || 0) + 1\n        } : e));\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await currentLogout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' + String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' + String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                },\n                children: currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search post\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 19\n                }, this), categories.filter(category =>\n                // Hide holiday categories from dropdown\n                !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 67\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                \"data-dropdown\": \"user-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowUserDropdown(!showUserDropdown),\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    padding: '0.75rem 1rem',\n                    background: 'white',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(User, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentRole === 'admin' ? 'Admin' : 'Student')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1019,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                    size: 14,\n                    style: {\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 19\n                }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '100%',\n                    right: 0,\n                    marginTop: '0.5rem',\n                    background: 'white',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '12px',\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                    minWidth: '200px',\n                    zIndex: 1000,\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem 1rem',\n                      borderBottom: '1px solid #f3f4f6',\n                      background: '#f9fafb'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827'\n                      },\n                      children: [currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      },\n                      children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1053,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.5rem 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                        navigate(dashboardPath);\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#374151',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#f3f4f6';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1088,\n                        columnNumber: 27\n                      }, this), \"Dashboard\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        handleLogout();\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#ef4444',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#fef2f2';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1117,\n                        columnNumber: 27\n                      }, this), \"Logout\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1092,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map(announcement => {\n                  // Handle alert announcements with special styling\n                  const isAlert = announcement.is_alert;\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: isAlert ? '#fef2f2' : '#f8fafc',\n                      borderRadius: '12px',\n                      border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                      e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                      e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: isAlert ? '#ef4444' : categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1218,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1227,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1236,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1253,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1254,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1246,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1226,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1213,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1263,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1307,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                refreshAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1340,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1431,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1446,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1407,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1529,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1519,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.category_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1558,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1545,\n                          columnNumber: 33\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1571,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1564,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1533,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1581,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1532,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1513,\n                  columnNumber: 23\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = eventImageUrls.filter(Boolean);\n                        openLightboxWithUrls(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1612,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1611,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1627,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1653,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1654,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1647,\n                    columnNumber: 25\n                  }, this), event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.category_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1669,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1663,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1638,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCalendarLikeToggle(event),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: event.user_has_reacted ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1715,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.reaction_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1719,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1692,\n                    columnNumber: 25\n                  }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1751,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.comment_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1752,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1724,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1683,\n                  columnNumber: 23\n                }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1765,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1772,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1759,\n                  columnNumber: 25\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1492,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                id: `announcement-${announcement.announcement_id}`,\n                className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1838,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1823,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1863,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1853,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1882,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1872,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1912,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1899,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1935,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1922,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1943,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1955,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1889,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1968,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1888,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1844,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  userRole: currentRole,\n                  onImageClick: index => {\n                    openLightbox(announcement.attachments || [], index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1982,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1993,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2041,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2045,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2018,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2077,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2078,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2050,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2012,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2098,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || announcement.author_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2099,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2093,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2102,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2086,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2003,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2124,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2131,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2118,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1790,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              if (selectedPinnedPost.is_alert) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2248,\n                    columnNumber: 25\n                  }, this), \"Alert\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2235,\n                  columnNumber: 23\n                }, this);\n              } else {\n                const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                const categoryStyle = getCategoryStyle(categoryName);\n                const IconComponent = categoryStyle.icon;\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: categoryStyle.background,\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2271,\n                    columnNumber: 25\n                  }, this), categoryName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2258,\n                  columnNumber: 23\n                }, this);\n              }\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2289,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2294,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              userRole: currentRole,\n              onImageClick: index => {\n                openLightbox(selectedPinnedPost.attachments, index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2307,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2306,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2336,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2345,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2167,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2152,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      isOpen: lightboxOpen,\n      onClose: () => setLightboxOpen(false),\n      altPrefix: \"Announcement Image\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2356,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 762,\n    columnNumber: 5\n  }, this);\n};\n_s3(NewsFeed, \"ZhLzYUh6hZd93n0XmO1QTWCXlXg=\", false, function () {\n  return [useNavigate, useAdminAuth, useStudentAuth, useCategories, useAnnouncements, useNotificationTarget];\n});\n_c3 = NewsFeed;\nexport default NewsFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"NewsFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "calendarReactionService", "useCategories", "useAnnouncements", "useNotificationTarget", "useAdminAuth", "useStudentAuth", "AdminCommentSection", "CommentSection", "NotificationBell", "StudentNotificationBell", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "userRole", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "authToken", "localStorage", "getItem", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "position", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "transition", "gap", "file_path", "currentTarget", "transform", "top", "left", "right", "bottom", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "_c2", "NewsFeed", "_s3", "navigate", "adminAuth", "studentAuth", "currentRole", "isAuthenticated", "currentUser", "user", "currentLogout", "logout", "categories", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "isFromNotification", "scrollTarget", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "minHeight", "background", "padding", "max<PERSON><PERSON><PERSON>", "openLightbox", "initialIndex", "imageUrls", "img", "filter", "Boolean", "openLightboxWithUrls", "getCategoryStyle", "categoryName", "styles", "icon", "getHolidayTypeStyle", "holidayTypeName", "pinned", "ann", "is_pinned", "fetchCalendarEvents", "undefined", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "handleCalendarLikeToggle", "user_has_reacted", "toggleLike", "prevEvents", "reaction_count", "handleLogout", "redirectPath", "href", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "event_date", "eventStartDateString", "eventEndDateString", "end_date", "endDate", "isEventActive", "isActive", "is_active", "displayAnnouncements", "displayEvents", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "size", "type", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "firstName", "lastName", "email", "dashboardPath", "flexShrink", "<PERSON><PERSON><PERSON><PERSON>", "is_alert", "category_name", "toUpperCase", "categoryStyle", "substring", "created_at", "toLocaleDateString", "opacity", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "push", "maxVisible", "filteredImages", "paddingTop", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "flexWrap", "posted_by_name", "author_name", "announcementId", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/NewsFeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Edit,\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nconst useImageLoader = (imagePath: string | null, userRole?: 'admin' | 'student') => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin'\n          ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n          : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  userRole?: 'admin' | 'student';\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath, userRole);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  userRole?: 'admin' | 'student';\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, userRole, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle()}\n          userRole={userRole}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle()}\n                  userRole={userRole}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Props interface for the unified NewsFeed component\ninterface NewsFeedProps {\n  userRole?: 'admin' | 'student'; // Optional prop to override role detection\n}\n\n// Main unified NewsFeed Component\nconst NewsFeed: React.FC<NewsFeedProps> = ({ userRole }) => {\n  const navigate = useNavigate();\n\n  // Get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useAdminAuth();\n  const studentAuth = useStudentAuth();\n\n  // Determine current user role and context\n  const currentRole = userRole ||\n    (adminAuth.isAuthenticated ? 'admin' :\n     studentAuth.isAuthenticated ? 'student' : null);\n\n  const currentUser = currentRole === 'admin' ? adminAuth.user : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth.logout : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const { categories } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, scrollTarget } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      }}>\n        <div style={{\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        }}>\n          <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>Authentication Required</h2>\n          <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>\n            Please log in to access the newsfeed.\n          </p>\n          <button\n            onClick={() => navigate('/')}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '12px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const authToken = currentRole === 'admin'\n        ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n        : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${authToken}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n  }, []);\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      const response = await calendarReactionService.toggleLike(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: !event.user_has_reacted,\n                  reaction_count: event.user_has_reacted\n                    ? (event.reaction_count || 1) - 1\n                    : (event.reaction_count || 0) + 1\n                }\n              : e\n          )\n        );\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await currentLogout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' +\n      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n      String(today.getDate()).padStart(2, '0');\n\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' +\n      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +\n      String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' +\n        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +\n        String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = (event as any).is_active !== 0;\n\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                  {currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'}\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories\n                    .filter(category =>\n                      // Hide holiday categories from dropdown\n                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                    )\n                    .map(category => (\n                      <option key={category.category_id} value={category.category_id.toString()}>\n                        {category.name}\n                      </option>\n                    ))\n                  }\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell - Role-aware */}\n                {currentRole === 'admin' ? <NotificationBell /> : <StudentNotificationBell />}\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#22c55e';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    <User size={16} />\n                    <span>{currentUser?.firstName || (currentRole === 'admin' ? 'Admin' : 'Student')}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#111827'\n                        }}>\n                          {currentUser?.firstName} {currentUser?.lastName}\n                        </div>\n                        <div style={{\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          {currentUser?.email}\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        <button\n                          onClick={() => {\n                            const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                            navigate(dashboardPath);\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#374151',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f3f4f6';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LayoutDashboard size={16} />\n                          Dashboard\n                        </button>\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement) => {\n                      // Handle alert announcements with special styling\n                      const isAlert = announcement.is_alert;\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: isAlert ? '#fef2f2' : '#f8fafc',\n                            borderRadius: '12px',\n                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  refreshAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) - Commented out for now */}\n          {/* Future feature: Recent student registrations for admin dashboard */}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.category_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightboxWithUrls(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.category_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.category_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Interaction Section */}\n                      <div style={{\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      }}>\n                        {/* Like Button */}\n                        <button\n                          onClick={() => handleCalendarLikeToggle(event)}\n                          style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            background: 'none',\n                            border: 'none',\n                            color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                            cursor: 'pointer',\n                            padding: '0.5rem',\n                            borderRadius: '8px',\n                            transition: 'all 0.2s ease',\n                            fontSize: '0.875rem',\n                            fontWeight: '500'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'none';\n                          }}\n                        >\n                          <Heart\n                            size={18}\n                            fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                          />\n                          <span>{(event as any).reaction_count || 0}</span>\n                        </button>\n\n                        {/* Comments Button */}\n                        {(event as any).allow_comments && (\n                          <button\n                            onClick={() => setShowCalendarComments(\n                              showCalendarComments === event.calendar_id ? null : event.calendar_id\n                            )}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                            }}\n                          >\n                            <MessageSquare size={18} />\n                            <span>{(event as any).comment_count || 0}</span>\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Comments Section */}\n                      {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          userRole={currentRole}\n                          onImageClick={(index) => {\n                            openLightbox(announcement.attachments || [], index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {(announcement as any).posted_by_name || announcement.author_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    userRole={currentRole}\n                    onImageClick={(index) => {\n                      openLightbox(selectedPinnedPost.attachments, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Announcement Image\"\n      />\n    </div>\n  );\n};\n\nexport default NewsFeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,uBAAuB,MAAM,6BAA6B;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAG3C,SAASC,WAAW,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAChH,OAAO,wCAAwC;AAC/C,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EAELC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAwB,EAAEC,QAA8B,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,SAAS,EAAE;MACdI,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGxC,WAAW,CAAC8B,SAAS,CAAC;QACtC,IAAI,CAACU,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,SAAS,GAAGb,QAAQ,KAAK,OAAO,GAClCc,YAAY,CAACC,OAAO,CAAC5C,oBAAoB,CAAC,GAC1C2C,YAAY,CAACC,OAAO,CAAC3C,sBAAsB,CAAC;;QAEhD;QACA,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAACR,OAAO,EAAE;UACpCS,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,SAAS,EAAE;YACtC,QAAQ,EAAEO,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAId,KAAK,CAAC,QAAQM,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CxB,WAAW,CAACyB,SAAS,CAAC;QAEtBjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOmB,GAAG,EAAE;QACZpB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEyB,GAAG,CAAC;QAC3CxB,QAAQ,CAACwB,GAAG,YAAYrB,KAAK,GAAGqB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACR3B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAChC,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEG,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CArEMH,cAAc;AAiFpB,MAAMqC,YAAyC,GAAGA,CAAC;EACjDpC,SAAS;EACTqC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTtC,QAAQ;EACRuC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAExC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGR,cAAc,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAExE,IAAII,OAAO,EAAE;IACX,oBACET,OAAA;MAAK0C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBrD,OAAA;QAAK0C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCrD,OAAA;UAAK0C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE5D,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjD,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEP,OAAA;MAAK0C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBrD,OAAA;QAAK0C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCrD,OAAA;UAAK0C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrE5D,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/EjD,KAAK,iBACJX,OAAA;UAAK0C,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzE1C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5D,OAAA;IACEgE,GAAG,EAAEzD,QAAS;IACdkC,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACbjD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE2B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CAtEMP,YAAyC;EAAA,QAURrC,cAAc;AAAA;AAAA+D,EAAA,GAV/C1B,YAAyC;AA8E/C,MAAM2B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEhE,QAAQ;EAAEiE;AAAa,CAAC,KAAK;EACnG,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEd,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIR,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAA2B;IAC/C,OAAO;MACLF,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACEzF,OAAA;IAAK0C,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf0C,GAAG,EAAE,KAAK;MACVL,KAAK,EAAE,MAAM;MACb9B,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEArD,OAAA;MAAK0C,KAAK,EAAEmC,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAAlB,QAAA,gBACrDrD,OAAA,CAACwC,YAAY;QACXpC,SAAS,EAAEoE,aAAa,CAAC,CAAC,CAAC,CAACmB,SAAU;QACtClD,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAE6C,aAAa,CAAC,CAAE;QACvBlF,QAAQ,EAAEA,QAAS;QACnBwC,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;QACjD,CAAE;QACF/C,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDU,YAAY,iBACXtE,OAAA;QACE0C,KAAK,EAAE;UACLuC,QAAQ,EAAE,UAAU;UACpBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAAC;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLY,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBvE,OAAA;MAAK0C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE3B,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DmB,GAAG,EAAE,KAAK;QACVL,KAAK,EAAEb,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAlB,QAAA,EACCmB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK/B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE1E,OAAA;UAEE0C,KAAK,EAAE;YACL,GAAGmC,iBAAiB,CAAC0B,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAC;YACvDU,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBAEFrD,OAAA,CAACwC,YAAY;YACXpC,SAAS,EAAEiG,KAAK,CAACV,SAAU;YAC3BlD,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAE6C,aAAa,CAAC,CAAE;YACvBlF,QAAQ,EAAEA,QAAS;YACnBwC,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;YACjD,CAAE;YACF/C,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD4C,MAAM,IAAI9B,cAAc,GAAG,CAAC,iBAC3B1E,OAAA;YAAK0C,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT9C,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdI,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,GACA,EAACqB,cAAc;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAU,YAAY,iBACXtE,OAAA;YACE0C,KAAK,EAAE;cACLuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACiC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAnDI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA6C,GAAA,GAzJMtC,YAAyC;AA8J/C;AACA,MAAMuC,QAAiC,GAAGA,CAAC;EAAErG;AAAS,CAAC,KAAK;EAAAsG,GAAA;EAC1D,MAAMC,QAAQ,GAAGnJ,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoJ,SAAS,GAAG/I,YAAY,CAAC,CAAC;EAChC,MAAMgJ,WAAW,GAAG/I,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAMgJ,WAAW,GAAG1G,QAAQ,KACzBwG,SAAS,CAACG,eAAe,GAAG,OAAO,GACnCF,WAAW,CAACE,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC;EAElD,MAAMC,WAAW,GAAGF,WAAW,KAAK,OAAO,GAAGF,SAAS,CAACK,IAAI,GAAGJ,WAAW,CAACI,IAAI;EAC/E,MAAMC,aAAa,GAAGJ,WAAW,KAAK,OAAO,GAAGF,SAAS,CAACO,MAAM,GAAGN,WAAW,CAACM,MAAM;;EAErF;EACA,MAAM;IAAEC;EAAW,CAAC,GAAG1J,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IACJ2J,aAAa;IACb7G,OAAO;IACPE,KAAK;IACL4G,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAG9J,gBAAgB,CAAC;IACnBkE,MAAM,EAAE,WAAW;IACnB6F,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAEf,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAM;IAAEgB,kBAAkB;IAAEC;EAAa,CAAC,GAAGnK,qBAAqB,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACoK,cAAc,EAAEC,iBAAiB,CAAC,GAAG3K,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC4K,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7K,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC8K,UAAU,EAAEC,aAAa,CAAC,GAAG/K,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACgL,YAAY,EAAEC,eAAe,CAAC,GAAGjL,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACkL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnL,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACoL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrL,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAACsL,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvL,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACwL,YAAY,EAAEC,eAAe,CAAC,GAAGzL,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0L,cAAc,EAAEC,iBAAiB,CAAC,GAAG3L,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC4L,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7L,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC8L,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/L,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACgM,cAAc,EAAEC,iBAAiB,CAAC,GAAGjM,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACkM,eAAe,EAAEC,kBAAkB,CAAC,GAAGnM,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoM,aAAa,EAAEC,gBAAgB,CAAC,GAAGrM,QAAQ,CAAqB,CAAC;EACxE;;EAEA;EACA,IAAI,CAACwJ,WAAW,EAAE;IAChB,oBACE/G,OAAA;MAAK0C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpB4G,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE;MACd,CAAE;MAAAzG,QAAA,eACArD,OAAA;QAAK0C,KAAK,EAAE;UACVoH,UAAU,EAAE,OAAO;UACnBC,OAAO,EAAE,MAAM;UACf5E,YAAY,EAAE,MAAM;UACpBtB,MAAM,EAAE,mBAAmB;UAC3BP,SAAS,EAAE,QAAQ;UACnB0G,QAAQ,EAAE;QACZ,CAAE;QAAA3G,QAAA,gBACArD,OAAA;UAAI0C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF5D,OAAA;UAAG0C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ5D,OAAA;UACEkG,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAAC,GAAG,CAAE;UAC7BlE,KAAK,EAAE;YACLoH,UAAU,EAAE,mDAAmD;YAC/D1G,KAAK,EAAE,OAAO;YACdS,MAAM,EAAE,MAAM;YACdsB,YAAY,EAAE,MAAM;YACpB4E,OAAO,EAAE,gBAAgB;YACzBvG,QAAQ,EAAE,UAAU;YACpBM,UAAU,EAAE,KAAK;YACjBsB,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMqG,YAAY,GAAGA,CAAC7F,MAAgC,EAAE8F,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAG/F,MAAM,CAACgC,GAAG,CAACgE,GAAG,IAAI9L,WAAW,CAAC8L,GAAG,CAACzE,SAAS,CAAC,CAAC,CAAC0E,MAAM,CAACC,OAAO,CAAa;IAC3FpB,iBAAiB,CAACiB,SAAS,CAAC;IAC5Bf,uBAAuB,CAACc,YAAY,CAAC;IACrClB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAGA,CAACJ,SAAmB,EAAED,YAAoB,KAAK;IAC1EhB,iBAAiB,CAACiB,SAAS,CAAC;IAC5Bf,uBAAuB,CAACc,YAAY,CAAC;IACrClB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVZ,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEzL;MACR,CAAC;MACD,SAAS,EAAE;QACT4K,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAE3L;MACR,CAAC;MACD,QAAQ,EAAE;QACR8K,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAExL;MACR,CAAC;MACD,WAAW,EAAE;QACX2K,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEvL;MACR,CAAC;MACD,QAAQ,EAAE;QACR0K,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAErL;MACR,CAAC;MACD,WAAW,EAAE;QACXwK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEtL;MACR;IACF,CAAC;IAED,OAAOqL,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMH,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBZ,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAElL;MACR,CAAC;MACD,cAAc,EAAE;QACdqK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEnL;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBsK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEjL;MACR,CAAC;MACD,cAAc,EAAE;QACdoK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAErL;MACR,CAAC;MACD,YAAY,EAAE;QACZwK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEhL;MACR,CAAC;MACD,SAAS,EAAE;QACTmK,UAAU,EAAE,mDAAmD;QAC/Da,IAAI,EAAEpL;MACR;IACF,CAAC;IAED,OAAOmL,MAAM,CAACG,eAAe,CAAwB,IAAIH,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACAlN,SAAS,CAAC,MAAM;IACd,MAAMsN,MAAM,GAAGxD,aAAa,CAAC+C,MAAM,CAAEU,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtE1B,sBAAsB,CAACwB,MAAM,CAAC;EAChC,CAAC,EAAE,CAACxD,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM2D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFvB,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACsB,SAAS,CAAC;MAE3B,MAAMhK,SAAS,GAAG6F,WAAW,KAAK,OAAO,GACrC5F,YAAY,CAACC,OAAO,CAAC5C,oBAAoB,CAAC,GAC1C2C,YAAY,CAACC,OAAO,CAAC3C,sBAAsB,CAAC;MAEhD,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,0DAA0D,EAAE;QACtGiD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUN,SAAS,EAAE;UACtC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMiK,IAAI,GAAG,MAAM9J,QAAQ,CAAC+J,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAAClF,GAAG,CAAC,MAAOuF,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMtK,KAAK,CAAC,GAAG/C,YAAY,iBAAiBoN,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FrK,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUN,SAAS,EAAE;gBACtC,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAM4K,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACvH,MAAM,GAAG0H,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACvH,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO4H,MAAM,EAAE;YACfhL,OAAO,CAACiL,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACvH,MAAM,GAAG,EAAE;UACnB;UACA,OAAOuH,KAAK;QACd,CAAC,CACH,CAAC;QAEDnC,iBAAiB,CAACgC,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACL5B,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOxH,GAAQ,EAAE;MACjBpB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEyB,GAAG,CAAC;MACrDwH,gBAAgB,CAACxH,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRqH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;;EAEA;EACAlM,SAAS,CAAC,MAAM;IACdyN,mBAAmB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiB,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACFnL,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,sCAAsC,EAAEoF,YAAY,CAACC,eAAe,CAAC;MACvGpL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkL,YAAY,CAACE,aAAa,CAAC;MACzErL,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,gBAAgB,EAAE;QAAEuF,EAAE,EAAErF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAE;QAAEC,IAAI,EAAExF;MAAY,CAAC,CAAC;MAE/F,IAAIoF,YAAY,CAACE,aAAa,EAAE;QAC9B;QACArL,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,uBAAuB,CAAC;QAC1D,MAAMS,kBAAkB,CAAC2E,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACApL,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,qBAAqB,CAAC;QACxD,MAAMQ,gBAAgB,CAAC4E,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEApL,OAAO,CAACC,GAAG,CAAC,aAAa8F,WAAW,gCAAgC,CAAC;IACvE,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,WAAWoG,WAAW,uBAAuB,EAAEpG,KAAK,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM6L,wBAAwB,GAAG,MAAOb,KAAU,IAAK;IACrD,IAAI;MACF3K,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,wCAAwC,EAAE4E,KAAK,CAACE,WAAW,CAAC;MAC9F7K,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0K,KAAK,CAACc,gBAAgB,CAAC;MACxEzL,OAAO,CAACC,GAAG,CAAC,WAAW8F,WAAW,gBAAgB,EAAE;QAAEuF,EAAE,EAAErF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAE;QAAEC,IAAI,EAAExF;MAAY,CAAC,CAAC;MAE/F,MAAM1F,QAAQ,GAAG,MAAM3D,uBAAuB,CAACgP,UAAU,CAACf,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACc,gBAAgB,IAAI,KAAK,CAAC;MAE7G,IAAIpL,QAAQ,CAACgK,OAAO,EAAE;QACpB;QACA7B,iBAAiB,CAACmD,UAAU,IAC1BA,UAAU,CAACvG,GAAG,CAACnC,CAAC,IACdA,CAAC,CAAC4H,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAG5H,CAAC;UACJwI,gBAAgB,EAAE,CAACd,KAAK,CAACc,gBAAgB;UACzCG,cAAc,EAAEjB,KAAK,CAACc,gBAAgB,GAClC,CAACd,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI,CAAC,GAC/B,CAACjB,KAAK,CAACiB,cAAc,IAAI,CAAC,IAAI;QACpC,CAAC,GACD3I,CACN,CACF,CAAC;QACDjD,OAAO,CAACC,GAAG,CAAC,aAAa8F,WAAW,+CAA+C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,WAAWoG,WAAW,gCAAgC,EAAEpG,KAAK,CAAC;IAC9E;EACF,CAAC;;EAED;EACA,MAAMkM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM1F,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOxG,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACA,MAAMmM,YAAY,GAAG/F,WAAW,KAAK,OAAO,GAAG,cAAc,GAAG,gBAAgB;MAChFtF,MAAM,CAACC,QAAQ,CAACqL,IAAI,GAAGD,YAAY;IACrC;EACF,CAAC;;EAED;EACAtP,SAAS,CAAC,MAAM;IACd,MAAMwP,kBAAkB,GAAIrB,KAAiB,IAAK;MAChD,IAAI9C,gBAAgB,EAAE;QACpB,MAAMoE,MAAM,GAAGtB,KAAK,CAACsB,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtDpE,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAEDqE,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACnE,gBAAgB,CAAC,CAAC;;EAItB;EACA,MAAMyE,qBAAqB,GAAGhG,aAAa,CAAC+C,MAAM,CAAC8B,YAAY,IAAI;IAAA,IAAAoB,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAACpF,UAAU,IAC/B8D,YAAY,CAACuB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC,IACnExB,YAAY,CAAC0B,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAAC7F,cAAc,IACrC,EAAAsF,qBAAA,GAAApB,YAAY,CAAC4B,WAAW,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BS,QAAQ,CAAC,CAAC,MAAK/F,cAAc;IAEzD,MAAMgG,iBAAiB,GAAG,CAAC9F,gBAAgB,IACzC,EAAAqF,qBAAA,GAAArB,YAAY,CAAC+B,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAK7F,gBAAgB;IAE3D,OAAOsF,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAG5E,cAAc,CAACc,MAAM,CAACsB,KAAK,IAAI;IAC5D,MAAM8B,aAAa,GAAG,CAACpF,UAAU,IAC/BsD,KAAK,CAAC+B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAC,IAC3DhC,KAAK,CAACyC,WAAW,IAAIzC,KAAK,CAACyC,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvF,UAAU,CAACsF,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACJ,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1C,MAAME,cAAc,GAAG,IAAIP,IAAI,CAAC3C,KAAK,CAACmD,UAAU,CAAC;IACjD,MAAMC,oBAAoB,GAAGF,cAAc,CAACL,WAAW,CAAC,CAAC,GAAG,GAAG,GAC7DC,MAAM,CAACI,cAAc,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAC5DF,MAAM,CAACI,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMK,kBAAkB,GAAGrD,KAAK,CAACsD,QAAQ,GAAG,CAAC,MAAM;MACjD,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAAC3C,KAAK,CAACsD,QAAQ,CAAC;MACxC,OAAOC,OAAO,CAACV,WAAW,CAAC,CAAC,GAAG,GAAG,GAChCC,MAAM,CAACS,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACrDF,MAAM,CAACS,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9C,CAAC,EAAE,CAAC,GAAGI,oBAAoB;;IAE3B;IACA,MAAMI,aAAa,GAAGZ,eAAe,IAAIQ,oBAAoB,IAAIR,eAAe,IAAIS,kBAAkB;;IAEtG;IACA,MAAMI,QAAQ,GAAIzD,KAAK,CAAS0D,SAAS,KAAK,CAAC;IAE/C,OAAO5B,aAAa,IAAI0B,aAAa,IAAIC,QAAQ;EACnD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGhC,qBAAqB;EAClD,MAAMiC,aAAa,GAAGpB,sBAAsB;;EAI5C;EACA;EACA;EACA;EACA;;EAEA,oBACEnO,OAAA;IAAK0C,KAAK,EAAE;MACVmH,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/D7E,QAAQ,EAAE;IACZ,CAAE;IAAA5B,QAAA,gBAEArD,OAAA;MAAK0C,KAAK,EAAE;QACVuC,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTuJ,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAhM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL5D,OAAA;MAAK0C,KAAK,EAAE;QAAEuC,QAAQ,EAAE,UAAU;QAAEyK,MAAM,EAAE;MAAE,CAAE;MAAArM,QAAA,gBAE9CrD,OAAA;QAAQ0C,KAAK,EAAE;UACboH,UAAU,EAAE,OAAO;UACnB6F,YAAY,EAAE,mBAAmB;UACjC1K,QAAQ,EAAE,QAAQ;UAClBa,GAAG,EAAE,CAAC;UACN4J,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAAvM,QAAA,eACArD,OAAA;UAAK0C,KAAK,EAAE;YACVqH,OAAO,EAAE,QAAQ;YACjBzE,MAAM,EAAE,MAAM;YACdtC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBAEArD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,QAAQ;cACbmK,QAAQ,EAAE;YACZ,CAAE;YAAAxM,QAAA,gBACArD,OAAA;cACEgE,GAAG,EAAC,iBAAiB;cACrBvB,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACL2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5D,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAI0C,KAAK,EAAE;kBACToN,MAAM,EAAE,CAAC;kBACTtM,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChB2M,UAAU,EAAE;gBACd,CAAE;gBAAA1M,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRoN,MAAM,EAAE,CAAC;kBACTtM,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE,SAAS;kBAChB2M,UAAU,EAAE;gBACd,CAAE;gBAAA1M,QAAA,EACC0D,WAAW,KAAK,OAAO,GAAG,gBAAgB,GAAG;cAAkB;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAK0C,KAAK,EAAE;cACVsN,IAAI,EAAE,CAAC;cACPhG,QAAQ,EAAE,OAAO;cACjB8F,MAAM,EAAE;YACV,CAAE;YAAAzM,QAAA,eACArD,OAAA;cAAK0C,KAAK,EAAE;gBAAEuC,QAAQ,EAAE;cAAW,CAAE;cAAA5B,QAAA,gBACnCrD,OAAA,CAACrB,MAAM;gBACLsR,IAAI,EAAE,EAAG;gBACTvN,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBc,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVD,SAAS,EAAE,kBAAkB;kBAC7BzC,KAAK,EAAE;gBACT;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5D,OAAA;gBACEkQ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,aAAa;gBACzBC,KAAK,EAAE/H,UAAW;gBAClBgI,QAAQ,EAAGpM,CAAC,IAAKqE,aAAa,CAACrE,CAAC,CAACgJ,MAAM,CAACmD,KAAK,CAAE;gBAC/C1N,KAAK,EAAE;kBACL2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdyE,OAAO,EAAE,eAAe;kBACxBlG,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,MAAM;kBACpB2E,UAAU,EAAE,SAAS;kBACrB1G,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB8M,OAAO,EAAE,MAAM;kBACf7K,UAAU,EAAE;gBACd,CAAE;gBACF8K,OAAO,EAAGtM,CAAC,IAAK;kBACdA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;kBAC7CvM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,OAAO;kBAC1C7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFa,MAAM,EAAGxM,CAAC,IAAK;kBACbA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;kBAC7CvM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;kBAC5C7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAAnM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXmK,QAAQ,EAAE,OAAO;cACjB3M,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAGArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbqE,OAAO,EAAE,QAAQ;gBACjBD,UAAU,EAAE,SAAS;gBACrB3E,YAAY,EAAE,MAAM;gBACpBtB,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACArD,OAAA;gBACEoQ,KAAK,EAAEnI,cAAe;gBACtBoI,QAAQ,EAAGpM,CAAC,IAAKiE,iBAAiB,CAACjE,CAAC,CAACgJ,MAAM,CAACmD,KAAK,CAAE;gBACnD1N,KAAK,EAAE;kBACLqH,OAAO,EAAE,gBAAgB;kBACzBlG,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB2E,UAAU,EAAE,OAAO;kBACnB1G,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB8M,OAAO,EAAE,MAAM;kBACflL,MAAM,EAAE,SAAS;kBACjByK,QAAQ,EAAE;gBACZ,CAAE;gBAAAxM,QAAA,gBAEFrD,OAAA;kBAAQoQ,KAAK,EAAC,EAAE;kBAAA/M,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCyD,UAAU,CACRgD,MAAM,CAACqG,QAAQ;gBACd;gBACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC9C,QAAQ,CAAC8C,QAAQ,CAACC,IAAI,CACjG,CAAC,CACAvK,GAAG,CAACsK,QAAQ,iBACX1Q,OAAA;kBAAmCoQ,KAAK,EAAEM,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAA3K,QAAA,EACvEqN,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC3C,WAAW;kBAAAtK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eAET5D,OAAA;gBACEoQ,KAAK,EAAEjI,gBAAiB;gBACxBkI,QAAQ,EAAGpM,CAAC,IAAKmE,mBAAmB,CAACnE,CAAC,CAACgJ,MAAM,CAACmD,KAAK,CAAE;gBACrD1N,KAAK,EAAE;kBACLqH,OAAO,EAAE,gBAAgB;kBACzBlG,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB2E,UAAU,EAAE,OAAO;kBACnB1G,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB8M,OAAO,EAAE,MAAM;kBACflL,MAAM,EAAE,SAAS;kBACjByK,QAAQ,EAAE;gBACZ,CAAE;gBAAAxM,QAAA,gBAEFrD,OAAA;kBAAQoQ,KAAK,EAAC,EAAE;kBAAA/M,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC5D,OAAA;kBAAQoQ,KAAK,EAAC,IAAI;kBAAA/M,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC5D,OAAA;kBAAQoQ,KAAK,EAAC,IAAI;kBAAA/M,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAACyE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDnI,OAAA;gBACEkG,OAAO,EAAEA,CAAA,KAAM;kBACboC,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACF1F,KAAK,EAAE;kBACLqH,OAAO,EAAE,gBAAgB;kBACzBlG,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnB2E,UAAU,EAAE,SAAS;kBACrB1G,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBsB,MAAM,EAAE,SAAS;kBACjBK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACFhH,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAAzG,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5D,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,GAEC0D,WAAW,KAAK,OAAO,gBAAG/G,OAAA,CAAC9B,gBAAgB;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5D,OAAA,CAAC7B,uBAAuB;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG7E5D,OAAA;gBAAK0C,KAAK,EAAE;kBAAEuC,QAAQ,EAAE;gBAAW,CAAE;gBAAC,iBAAc,eAAe;gBAAA5B,QAAA,gBACjErD,OAAA;kBACEkG,OAAO,EAAEA,CAAA,KAAM4C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDnG,KAAK,EAAE;oBACLM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbqE,OAAO,EAAE,cAAc;oBACvBD,UAAU,EAAE,OAAO;oBACnBjG,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB/B,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE,eAAe;oBAC3BmK,SAAS,EAAE;kBACb,CAAE;kBACF/M,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;oBAC7CvM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,kCAAkC;kBACtE,CAAE;kBACF9M,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;oBAC7CvM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,8BAA8B;kBAClE,CAAE;kBAAAvM,QAAA,gBAEFrD,OAAA,CAACH,IAAI;oBAACoQ,IAAI,EAAE;kBAAG;oBAAAxM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClB5D,OAAA;oBAAAqD,QAAA,EAAO,CAAA4D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2J,SAAS,MAAK7J,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;kBAAC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5D,OAAA,CAACJ,WAAW;oBAACqQ,IAAI,EAAE,EAAG;oBAACvN,KAAK,EAAE;sBAC5BmD,SAAS,EAAEgD,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;sBAC/DpD,UAAU,EAAE;oBACd;kBAAE;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGRiF,gBAAgB,iBACf7I,OAAA;kBAAK0C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,CAAC;oBACRjC,SAAS,EAAE,QAAQ;oBACnB+F,UAAU,EAAE,OAAO;oBACnBjG,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpByK,SAAS,EAAE,iCAAiC;oBAC5CC,QAAQ,EAAE,OAAO;oBACjBH,MAAM,EAAE,IAAI;oBACZxK,QAAQ,EAAE;kBACZ,CAAE;kBAAA7B,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVqH,OAAO,EAAE,cAAc;sBACvB4F,YAAY,EAAE,mBAAmB;sBACjC7F,UAAU,EAAE;oBACd,CAAE;oBAAAzG,QAAA,gBACArD,OAAA;sBAAK0C,KAAK,EAAE;wBACVc,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE;sBACT,CAAE;sBAAAC,QAAA,GACC4D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2J,SAAS,EAAC,GAAC,EAAC3J,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4J,QAAQ;oBAAA;sBAAApN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACN5D,OAAA;sBAAK0C,KAAK,EAAE;wBACVc,QAAQ,EAAE,SAAS;wBACnBJ,KAAK,EAAE;sBACT,CAAE;sBAAAC,QAAA,EACC4D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6J;oBAAK;sBAAArN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEqH,OAAO,EAAE;oBAAW,CAAE;oBAAA1G,QAAA,gBAClCrD,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM6K,aAAa,GAAGhK,WAAW,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;wBACzFH,QAAQ,CAACmK,aAAa,CAAC;wBACvBjI,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFpG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdqE,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzBjG,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACFhH,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAzG,QAAA,gBAEFrD,OAAA,CAACf,eAAe;wBAACgR,IAAI,EAAE;sBAAG;wBAAAxM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAE/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAET5D,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAM;wBACb2G,YAAY,CAAC,CAAC;wBACd/D,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFpG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdqE,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzBjG,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACFhH,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAzG,QAAA,gBAEFrD,OAAA,CAACF,MAAM;wBAACmQ,IAAI,EAAE;sBAAG;wBAAAxM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT5D,OAAA;QAAK0C,KAAK,EAAE;UACVqH,OAAO,EAAE,MAAM;UACf/G,OAAO,EAAE,MAAM;UACf0C,GAAG,EAAE,MAAM;UACXzC,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEArD,OAAA;UAAK0C,KAAK,EAAE;YACV2C,KAAK,EAAE,OAAO;YACd2L,UAAU,EAAE;UACd,CAAE;UAAA3N,QAAA,eACArD,OAAA;YAAK0C,KAAK,EAAE;cACVoH,UAAU,EAAE,OAAO;cACnB3E,YAAY,EAAE,MAAM;cACpBtB,MAAM,EAAE,mBAAmB;cAC3BqB,QAAQ,EAAE,QAAQ;cAClBD,QAAQ,EAAE,QAAQ;cAClBa,GAAG,EAAE;YACP,CAAE;YAAAzC,QAAA,gBAEArD,OAAA;cAAK0C,KAAK,EAAE;gBACVqH,OAAO,EAAE,oBAAoB;gBAC7B4F,YAAY,EAAE;cAChB,CAAE;cAAAtM,QAAA,gBACArD,OAAA;gBAAK0C,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE,SAAS;kBACdnC,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,gBACArD,OAAA,CAACpB,GAAG;kBAACqR,IAAI,EAAE,EAAG;kBAACvN,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C5D,OAAA;kBAAI0C,KAAK,EAAE;oBACToN,MAAM,EAAE,CAAC;oBACTtM,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRoN,MAAM,EAAE,CAAC;kBACTtM,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN5D,OAAA;cAAK0C,KAAK,EAAE;gBAAEqH,OAAO,EAAE;cAAO,CAAE;cAAA1G,QAAA,EAC7BgG,mBAAmB,CAAC9E,MAAM,GAAG,CAAC,gBAC7BvE,OAAA,CAAAE,SAAA;gBAAAmD,QAAA,GACGgG,mBAAmB,CAAC5E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAE+F,YAAY,IAAK;kBACrD;kBACA,MAAM8E,OAAO,GAAG9E,YAAY,CAAC+E,QAAQ;kBACrC,MAAMzG,YAAY,GAAG,CAAC0B,YAAY,CAACgF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAG7G,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACEzK,OAAA;oBAEE0C,KAAK,EAAE;sBACLqH,OAAO,EAAE,MAAM;sBACfD,UAAU,EAAEmH,OAAO,GAAG,SAAS,GAAG,SAAS;sBAC3C9L,YAAY,EAAE,MAAM;sBACpBtB,MAAM,EAAEoN,OAAO,GAAG,mBAAmB,GAAG,mBAAmB;sBAC3D1N,YAAY,EAAE,MAAM;sBACpB6B,MAAM,EAAE,SAAS;sBACjBK,UAAU,EAAE;oBACd,CAAE;oBACF5C,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAGmH,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClEhN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAGS,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACFnO,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAGmH,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClEhN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAGS,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACF/K,OAAO,EAAEA,CAAA,KAAM0C,qBAAqB,CAACuD,YAAY,CAAE;oBAAA9I,QAAA,eAEnDrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACArD,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbwE,UAAU,EAAEmH,OAAO,GAAG,SAAS,GAAII,aAAa,CAACvH,UAAU,CAAC8D,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAU;0BACxGzI,YAAY,EAAE,KAAK;0BACnBpB,SAAS,EAAE,QAAQ;0BACnBiN,UAAU,EAAE;wBACd;sBAAE;wBAAAvN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL5D,OAAA;wBAAK0C,KAAK,EAAE;0BAAEsN,IAAI,EAAE;wBAAE,CAAE;wBAAA3M,QAAA,gBACtBrD,OAAA;0BAAI0C,KAAK,EAAE;4BACToN,MAAM,EAAE,cAAc;4BACtBtM,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE,SAAS;4BAChB2M,UAAU,EAAE;0BACd,CAAE;0BAAA1M,QAAA,EACC8I,YAAY,CAACuB;wBAAK;0BAAAjK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL5D,OAAA;0BAAG0C,KAAK,EAAE;4BACRoN,MAAM,EAAE,cAAc;4BACtBtM,QAAQ,EAAE,QAAQ;4BAClBJ,KAAK,EAAE,SAAS;4BAChB2M,UAAU,EAAE;0BACd,CAAE;0BAAA1M,QAAA,EACC8I,YAAY,CAAC0B,OAAO,CAACtJ,MAAM,GAAG,EAAE,GAC7B,GAAG4H,YAAY,CAAC0B,OAAO,CAACyD,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7CnF,YAAY,CAAC0B;wBAAO;0BAAApK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ5D,OAAA;0BAAK0C,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE,QAAQ;4BACblC,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;4BAACoR,IAAI,EAAE;0BAAG;4BAAAxM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB5D,OAAA;4BAAAqD,QAAA,EAAO,IAAIiL,IAAI,CAACnC,YAAY,CAACoF,UAAU,CAAC,CAACC,kBAAkB,CAAC;0BAAC;4BAAA/N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhEDuI,YAAY,CAACC,eAAe;oBAAA3I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAEDyF,mBAAmB,CAAC9E,MAAM,GAAG,CAAC,iBAC7BvE,OAAA;kBAAQ0C,KAAK,EAAE;oBACb2C,KAAK,EAAE,MAAM;oBACb0E,OAAO,EAAE,SAAS;oBAClBlG,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,KAAK;oBACnB2E,UAAU,EAAE,OAAO;oBACnB1G,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE;kBACd,CAAE;kBACF5C,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,SAAS;oBAC5C7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACF1N,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,OAAO;oBAC1C7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC8N,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAAnN,QAAA,GAAC,WACQ,EAACgG,mBAAmB,CAAC9E,MAAM,EAAC,eACvC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH5D,OAAA;gBAAK0C,KAAK,EAAE;kBACVqH,OAAO,EAAE,WAAW;kBACpBzG,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,gBACArD,OAAA,CAACpB,GAAG;kBAACqR,IAAI,EAAE,EAAG;kBAACvN,KAAK,EAAE;oBAAEa,YAAY,EAAE,QAAQ;oBAAEkO,OAAO,EAAE;kBAAI;gBAAE;kBAAAhO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE5D,OAAA;kBAAG0C,KAAK,EAAE;oBAAEoN,MAAM,EAAE,CAAC;oBAAEtM,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,EAAC;gBAE/C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAK0C,KAAK,EAAE;YAAEsN,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAAxM,QAAA,GAEpC,CAAC5C,OAAO,IAAIgJ,eAAe,kBAC1BzJ,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpB4G,SAAS,EAAE;YACb,CAAE;YAAAxG,QAAA,eACArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBlD,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA;gBAAK0C,KAAK,EAAE;kBACV2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,MAAM,EAAE,kCAAkC;kBAC1C6N,SAAS,EAAE,mBAAmB;kBAC9BvM,YAAY,EAAE,KAAK;kBACnBwM,SAAS,EAAE;gBACb;cAAE;gBAAAlO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT5D,OAAA;gBAAG0C,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,MAAM;kBAChBM,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAACjD,KAAK,IAAIgJ,aAAa,KAAK,CAAClJ,OAAO,IAAI,CAACgJ,eAAe,iBACvDzJ,OAAA;YAAK0C,KAAK,EAAE;cACVqH,OAAO,EAAE,MAAM;cACfD,UAAU,EAAE,wBAAwB;cACpCjG,MAAM,EAAE,kCAAkC;cAC1CsB,YAAY,EAAE,MAAM;cACpB7B,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwE,UAAU,EAAE,wBAAwB;gBACpC3E,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB4M,MAAM,EAAE;cACV,CAAE;cAAAzM,QAAA,eACArD,OAAA,CAAClB,aAAa;gBAACmR,IAAI,EAAE,EAAG;gBAAC7M,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN5D,OAAA;cAAI0C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB0M,MAAM,EAAE,cAAc;gBACtBtM,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAG0C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB0M,MAAM,EAAE,cAAc;gBACtBtM,QAAQ,EAAE;cACZ,CAAE;cAAAH,QAAA,EACC1C,KAAK,IAAIgJ;YAAa;cAAAlG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ5D,OAAA;cACEkG,OAAO,EAAEA,CAAA,KAAM;gBACbwB,oBAAoB,CAAC,CAAC;gBACtBuD,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFvI,KAAK,EAAE;gBACLoH,UAAU,EAAE,mDAAmD;gBAC/D1G,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB4E,OAAO,EAAE,gBAAgB;gBACzBvG,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACF9M,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAvM,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAACnD,OAAO,IAAI,CAACgJ,eAAe,IAAI,CAAC9I,KAAK,IAAI,CAACgJ,aAAa,IACxD2F,oBAAoB,CAAC/K,MAAM,KAAK,CAAC,IAAIgL,aAAa,CAAChL,MAAM,KAAK,CAAC,iBAC9DvE,OAAA;YAAK0C,KAAK,EAAE;cACVqH,OAAO,EAAE,WAAW;cACpBzG,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwE,UAAU,EAAE,mDAAmD;gBAC/D3E,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB4M,MAAM,EAAE;cACV,CAAE;cAAAzM,QAAA,eACArD,OAAA,CAACtB,SAAS;gBAACuR,IAAI,EAAE,EAAG;gBAAC7M,KAAK,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN5D,OAAA;cAAI0C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB0M,MAAM,EAAE,YAAY;gBACpBtM,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAG0C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB0M,MAAM,EAAE,YAAY;gBACpBtM,QAAQ,EAAE,MAAM;gBAChBuM,UAAU,EAAE,KAAK;gBACjB/F,QAAQ,EAAE,OAAO;gBACjB4H,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAxO,QAAA,EACCgF,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAACyE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDnI,OAAA;cACEkG,OAAO,EAAEA,CAAA,KAAM;gBACboC,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACF1F,KAAK,EAAE;gBACLoH,UAAU,EAAE,mDAAmD;gBAC/D1G,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB4E,OAAO,EAAE,gBAAgB;gBACzBvG,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACF9M,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAvM,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAMA,CAACnD,OAAO,IAAI,CAACgJ,eAAe,KAAK6F,oBAAoB,CAAC/K,MAAM,GAAG,CAAC,IAAIgL,aAAa,CAAChL,MAAM,GAAG,CAAC,CAAC,iBAC5FvE,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAECkM,aAAa,CAAChL,MAAM,GAAG,CAAC,iBACvBvE,OAAA,CAAAE,SAAA;cAAAmD,QAAA,EACGkM,aAAa,CAACnJ,GAAG,CAACuF,KAAK,iBACtB3L,OAAA;gBAEE0C,KAAK,EAAE;kBACLoH,UAAU,EAAE,2BAA2B;kBACvC3E,YAAY,EAAE,MAAM;kBACpB4E,OAAO,EAAE,QAAQ;kBACjBlG,MAAM,EAAE,8BAA8B;kBACtCiO,cAAc,EAAE,YAAY;kBAC5BlC,SAAS,EAAE,gCAAgC;kBAC3CnK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACF9M,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAAvM,QAAA,gBAGFrD,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACV2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdwE,UAAU,EAAE,mDAAmD;sBAC/D3E,YAAY,EAAE,MAAM;sBACpBnC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxB8N,UAAU,EAAE;oBACd,CAAE;oBAAA3N,QAAA,eACArD,OAAA,CAACnB,QAAQ;sBAACoR,IAAI,EAAE,EAAG;sBAAC7M,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEsN,IAAI,EAAE;oBAAE,CAAE;oBAAA3M,QAAA,gBACtBrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMwH,eAAe,GAAGc,KAAK,CAACwF,aAAa,IAAI,cAAc;wBAC7D,MAAMY,YAAY,GAAGnH,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAMmH,aAAa,GAAGD,YAAY,CAACpH,IAAI;wBAEvC,oBACE3K,OAAA;0BAAM0C,KAAK,EAAE;4BACXoH,UAAU,EAAEiI,YAAY,CAACjI,UAAU;4BACnC1G,KAAK,EAAE,OAAO;4BACdI,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjBiG,OAAO,EAAE,iBAAiB;4BAC1B5E,YAAY,EAAE,MAAM;4BACpB8M,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtBlP,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACArD,OAAA,CAACgS,aAAa;4BAAC/B,IAAI,EAAE,EAAG;4BAAC7M,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxCiH,eAAe;wBAAA;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,eAEJ5D,OAAA;wBAAK0C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,SAAS;0BACdtC,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,gBACArD,OAAA,CAACnB,QAAQ;0BAACoR,IAAI,EAAE;wBAAG;0BAAAxM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAI0K,IAAI,CAAC3C,KAAK,CAACmD,UAAU,CAAC,CAAC0C,kBAAkB,CAAC,OAAO,EAAE;0BACtDW,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAA7O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN5D,OAAA;sBAAI0C,KAAK,EAAE;wBACToN,MAAM,EAAE,cAAc;wBACtBtM,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB2M,UAAU,EAAE;sBACd,CAAE;sBAAA1M,QAAA,EACCsI,KAAK,CAAC+B;oBAAK;sBAAAjK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAAC,MAAM;kBACN;kBACA,MAAM2O,cAAwB,GAAG,EAAE;kBAEnC,IAAK5G,KAAK,CAASvH,MAAM,IAAKuH,KAAK,CAASvH,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DoH,KAAK,CAASvH,MAAM,CAACoO,OAAO,CAAEpI,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACzE,SAAS,EAAE;wBACjB;wBACA,MAAMpF,QAAQ,GAAGjC,WAAW,CAAC8L,GAAG,CAACzE,SAAS,CAAC;wBAC3C,IAAIpF,QAAQ,EAAE;0BACZgS,cAAc,CAACE,IAAI,CAAClS,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAOgS,cAAc,CAAChO,MAAM,GAAG,CAAC,gBAC9BvE,OAAA;oBAAK0C,KAAK,EAAE;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAAF,QAAA,eACnCrD,OAAA,CAAC5B,oBAAoB;sBACnBgG,MAAM,EAAEmO,cAAc,CAAClI,MAAM,CAACC,OAAO,CAAc;sBACnDjG,SAAS,EAAEsH,KAAK,CAAC+B,KAAM;sBACvBgF,UAAU,EAAE,CAAE;sBACdpO,YAAY,EAAGQ,KAAK,IAAK;wBACvB,MAAM6N,cAAc,GAAGJ,cAAc,CAAClI,MAAM,CAACC,OAAO,CAAa;wBACjEC,oBAAoB,CAACoI,cAAc,EAAE7N,KAAK,CAAC;sBAC7C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,EAGH+H,KAAK,CAACyC,WAAW,iBAChBpO,OAAA;kBAAK0C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnBuM,UAAU,EAAE,KAAK;oBACjBxM,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCsI,KAAK,CAACyC;gBAAW;kBAAA3K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbqE,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,0BAA0B;oBACtC3E,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE;kBACZ,CAAE;kBAAAH,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;sBAACoR,IAAI,EAAE;oBAAG;sBAAAxM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB5D,OAAA;sBAAAqD,QAAA,EACGsI,KAAK,CAACsD,QAAQ,IAAItD,KAAK,CAACsD,QAAQ,KAAKtD,KAAK,CAACmD,UAAU,GAClD,GAAG,IAAIR,IAAI,CAAC3C,KAAK,CAACmD,UAAU,CAAC,CAAC0C,kBAAkB,CAAC,CAAC,MAAM,IAAIlD,IAAI,CAAC3C,KAAK,CAACsD,QAAQ,CAAC,CAACuC,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAIlD,IAAI,CAAC3C,KAAK,CAACmD,UAAU,CAAC,CAAC0C,kBAAkB,CAAC;oBAAC;sBAAA/N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAEL+H,KAAK,CAACwF,aAAa,iBAClBnR,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,eACArD,OAAA;sBAAM0C,KAAK,EAAE;wBACXqH,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAE,yBAAyB;wBACrC3E,YAAY,EAAE,KAAK;wBACnB3B,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCsI,KAAK,CAACwF;oBAAa;sBAAA1N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGN5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB6O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE,8BAA8B;oBACzC1O,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBAEArD,OAAA;oBACEkG,OAAO,EAAEA,CAAA,KAAMsG,wBAAwB,CAACb,KAAK,CAAE;oBAC/CjJ,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACboE,UAAU,EAAE,MAAM;sBAClBjG,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAGuI,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG,SAAS;sBAC9DrH,MAAM,EAAE,SAAS;sBACjB2E,OAAO,EAAE,QAAQ;sBACjB5E,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,qBAAqB;oBAC1D,CAAE;oBACFhH,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAAzG,QAAA,gBAEFrD,OAAA,CAACjB,KAAK;sBACJkR,IAAI,EAAE,EAAG;sBACT4C,IAAI,EAAGlH,KAAK,CAASc,gBAAgB,GAAG,SAAS,GAAG;oBAAO;sBAAAhJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACF5D,OAAA;sBAAAqD,QAAA,EAAQsI,KAAK,CAASiB,cAAc,IAAI;oBAAC;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,EAGP+H,KAAK,CAASmH,cAAc,iBAC5B9S,OAAA;oBACEkG,OAAO,EAAEA,CAAA,KAAMwC,uBAAuB,CACpCD,oBAAoB,KAAKkD,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;oBACFnJ,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACboE,UAAU,EAAE,MAAM;sBAClBjG,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAEqF,oBAAoB,KAAKkD,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;sBACzEzG,MAAM,EAAE,SAAS;sBACjB2E,OAAO,EAAE,QAAQ;sBACjB5E,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,qBAAqB;sBACxD7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGqF,oBAAoB,KAAKkD,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBACF/I,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,MAAM;sBACzC7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGqF,oBAAoB,KAAKkD,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBAAAxI,QAAA,gBAEFrD,OAAA,CAAClB,aAAa;sBAACmR,IAAI,EAAE;oBAAG;sBAAAxM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B5D,OAAA;sBAAAqD,QAAA,EAAQsI,KAAK,CAASoH,aAAa,IAAI;oBAAC;sBAAAtP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGL6E,oBAAoB,KAAKkD,KAAK,CAACE,WAAW,IAAKF,KAAK,CAASmH,cAAc,iBAC1E9S,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB6O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE;kBACb,CAAE;kBAAArO,QAAA,EACC0D,WAAW,KAAK,OAAO,gBACtB/G,OAAA,CAAChC,mBAAmB;oBAClBgV,UAAU,EAAErH,KAAK,CAACE,WAAY;oBAC9BoH,aAAa,EAAGtH,KAAK,CAASmH,cAAe;oBAC7CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAG;oBAC/B6G,eAAe,EAAC;kBAAO;oBAAA1P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF5D,OAAA,CAAC/B,cAAc;oBACb+U,UAAU,EAAErH,KAAK,CAACE,WAAY;oBAC9BoH,aAAa,EAAGtH,KAAK,CAASmH,cAAe;oBAC7CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAG;oBAC/B6G,eAAe,EAAC;kBAAS;oBAAA1P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GA/RI,SAAS+H,KAAK,CAACE,WAAW,EAAE;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgS9B,CACN;YAAC,gBACF,CACH,EAGA0L,oBAAoB,CAAC/K,MAAM,GAAG,CAAC,iBAC9BvE,OAAA,CAAAE,SAAA;cAAAmD,QAAA,EACGiM,oBAAoB,CAAClJ,GAAG,CAAC+F,YAAY,iBACpCnM,OAAA;gBAEEsM,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;gBACnDzJ,SAAS,EAAEoF,kBAAkB,IAAIC,YAAY,KAAK,gBAAgBmE,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;gBAC9I1J,KAAK,EAAE;kBACLoH,UAAU,EAAE,2BAA2B;kBACvC3E,YAAY,EAAE,MAAM;kBACpB4E,OAAO,EAAE,QAAQ;kBACjBlG,MAAM,EAAEsI,YAAY,CAACnB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClC8G,cAAc,EAAE,YAAY;kBAC5BlC,SAAS,EAAEzD,YAAY,CAACnB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpCvF,UAAU,EAAE,2CAA2C;kBACvDR,QAAQ,EAAE;gBACZ,CAAE;gBACFpC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAGzD,YAAY,CAACnB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFlI,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,SAAS,GAAGzD,YAAY,CAACnB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAA3H,QAAA,GAGD8I,YAAY,CAACnB,SAAS,iBACrBhL,OAAA;kBAAK0C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACb8D,UAAU,EAAE,mDAAmD;oBAC/D1G,KAAK,EAAE,OAAO;oBACd2G,OAAO,EAAE,iBAAiB;oBAC1B5E,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBd,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,SAAS;oBACdkK,SAAS,EAAE;kBACb,CAAE;kBAAAvM,QAAA,gBACArD,OAAA,CAACpB,GAAG;oBAACqR,IAAI,EAAE;kBAAG;oBAAAxM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,GACC,CAAC,MAAM;oBACN,IAAI8I,YAAY,CAAC+E,QAAQ,EAAE;sBACzB,oBACElR,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdwE,UAAU,EAAE,mDAAmD;0BAC/D3E,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxB8N,UAAU,EAAE;wBACd,CAAE;wBAAA3N,QAAA,eACArD,OAAA,CAACZ,aAAa;0BAAC6Q,IAAI,EAAE,EAAG;0BAAC7M,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAM6G,YAAY,GAAG,CAAC0B,YAAY,CAACgF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAG7G,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAMuH,aAAa,GAAGX,aAAa,CAAC1G,IAAI;sBAExC,oBACE3K,OAAA;wBAAK0C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdwE,UAAU,EAAEuH,aAAa,CAACvH,UAAU;0BACpC3E,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxB8N,UAAU,EAAE;wBACd,CAAE;wBAAA3N,QAAA,eACArD,OAAA,CAACgS,aAAa;0BAAC/B,IAAI,EAAE,EAAG;0BAAC7M,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ5D,OAAA;oBAAK0C,KAAK,EAAE;sBAAEsN,IAAI,EAAE;oBAAE,CAAE;oBAAA3M,QAAA,gBACtBrD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE,QAAQ;wBACtB6P,QAAQ,EAAE;sBACZ,CAAE;sBAAA/P,QAAA,GACC,CAAC,MAAM;wBACN,IAAI8I,YAAY,CAAC+E,QAAQ,EAAE;0BACzB,oBACElR,OAAA;4BAAM0C,KAAK,EAAE;8BACXoH,UAAU,EAAE,mDAAmD;8BAC/D1G,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBiG,OAAO,EAAE,iBAAiB;8BAC1B5E,YAAY,EAAE,MAAM;8BACpB8M,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBlP,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACArD,OAAA,CAACZ,aAAa;8BAAC6Q,IAAI,EAAE,EAAG;8BAAC7M,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAM6G,YAAY,GAAG,CAAC0B,YAAY,CAACgF,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAG7G,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMuH,aAAa,GAAGX,aAAa,CAAC1G,IAAI;0BAExC,oBACE3K,OAAA;4BAAM0C,KAAK,EAAE;8BACXoH,UAAU,EAAEuH,aAAa,CAACvH,UAAU;8BACpC1G,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBiG,OAAO,EAAE,iBAAiB;8BAC1B5E,YAAY,EAAE,MAAM;8BACpB8M,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBlP,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACArD,OAAA,CAACgS,aAAa;8BAAC/B,IAAI,EAAE,EAAG;8BAAC7M,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxC6G,YAAY;0BAAA;4BAAAhH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEHuI,YAAY,CAAC+B,WAAW,iBACvBlO,OAAA;wBAAM0C,KAAK,EAAE;0BACXoH,UAAU,EAAE,yBAAyB;0BACrC1G,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,SAAS;0BACnBM,UAAU,EAAE,KAAK;0BACjBiG,OAAO,EAAE,iBAAiB;0BAC1B5E,YAAY,EAAE;wBAChB,CAAE;wBAAA9B,QAAA,GAAC,QACK,EAAC8I,YAAY,CAAC+B,WAAW;sBAAA;wBAAAzK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,eAED5D,OAAA;wBAAK0C,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAIiL,IAAI,CAACnC,YAAY,CAACoF,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BAC7DW,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA7O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN5D,OAAA;sBAAI0C,KAAK,EAAE;wBACToN,MAAM,EAAE,cAAc;wBACtBtM,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB2M,UAAU,EAAE;sBACd,CAAE;sBAAA1M,QAAA,EACC8I,YAAY,CAACuB;oBAAK;sBAAAjK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLuI,YAAY,CAACJ,WAAW,IAAII,YAAY,CAACJ,WAAW,CAACxH,MAAM,GAAG,CAAC,iBAC9DvE,OAAA,CAACmE,YAAY;kBACXC,MAAM,EAAE+H,YAAY,CAACJ,WAAY;kBACjC1H,SAAS,EAAE8H,YAAY,CAACuB,KAAM;kBAC9BrN,QAAQ,EAAE0G,WAAY;kBACtBzC,YAAY,EAAGQ,KAAK,IAAK;oBACvBmF,YAAY,CAACkC,YAAY,CAACJ,WAAW,IAAI,EAAE,EAAEjH,KAAK,CAAC;kBACrD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnBuM,UAAU,EAAE,KAAK;oBACjBxM,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACC8I,YAAY,CAAC0B;gBAAO;kBAAApK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGN5D,OAAA;kBAAK0C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B6G,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,qBAAqB;oBACjC3E,YAAY,EAAE,MAAM;oBACpB5B,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACArD,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEArD,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAMgG,gBAAgB,CAACC,YAAY,CAAE;sBAC9CzJ,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACboE,UAAU,EAAE,MAAM;wBAClBjG,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE+I,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDjH,MAAM,EAAE,SAAS;wBACjB2E,OAAO,EAAE,QAAQ;wBACjB5E,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACFhH,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAAzG,QAAA,gBAEFrD,OAAA,CAACjB,KAAK;wBACJkR,IAAI,EAAE,EAAG;wBACT4C,IAAI,EAAE1G,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAA5I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF5D,OAAA;wBAAAqD,QAAA,EAAO8I,YAAY,CAACS,cAAc,IAAI;sBAAC;wBAAAnJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGRuI,YAAY,CAAC2G,cAAc,iBAC1B9S,OAAA;sBACEkG,OAAO,EAAEA,CAAA,KAAMsC,eAAe,CAC5BD,YAAY,KAAK4D,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;sBACF1J,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACboE,UAAU,EAAE,MAAM;wBAClBjG,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEmF,YAAY,KAAK4D,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5EhH,MAAM,EAAE,SAAS;wBACjB2E,OAAO,EAAE,QAAQ;wBACjB5E,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,qBAAqB;wBACxD7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFN,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoH,UAAU,GAAG,MAAM;wBACzC7F,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGmF,YAAY,KAAK4D,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAA/I,QAAA,gBAEFrD,OAAA,CAAClB,aAAa;wBAACmR,IAAI,EAAE;sBAAG;wBAAAxM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B5D,OAAA;wBAAAqD,QAAA,EAAO8I,YAAY,CAAC4G,aAAa,IAAI;sBAAC;wBAAAtP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGE,CAAC,eAGN5D,OAAA;oBAAK0C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,MAAM;sBACXlC,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACArD,OAAA;sBAAK0C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACArD,OAAA,CAAChB,KAAK;wBAACiR,IAAI,EAAE;sBAAG;wBAAAxM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB5D,OAAA;wBAAAqD,QAAA,GAAM,YAAU,EAAE8I,YAAY,CAASkH,cAAc,IAAIlH,YAAY,CAACmH,WAAW,IAAI,OAAO;sBAAA;wBAAA7P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjG,CAAC,eAEN5D,OAAA;sBAAK0C,KAAK,EAAE;wBACVqH,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAEqC,YAAY,CAACrK,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BsB,KAAK,EAAE+I,YAAY,CAACrK,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClEqD,YAAY,EAAE,KAAK;wBACnBrB,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACC8I,YAAY,CAACrK;oBAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL2E,YAAY,KAAK4D,YAAY,CAACC,eAAe,IAAID,YAAY,CAAC2G,cAAc,iBAC3E9S,OAAA;kBAAK0C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB6O,UAAU,EAAE,MAAM;oBAClBlB,SAAS,EAAE;kBACb,CAAE;kBAAArO,QAAA,EACC0D,WAAW,KAAK,OAAO,gBACtB/G,OAAA,CAAChC,mBAAmB;oBAClBuV,cAAc,EAAEpH,YAAY,CAACC,eAAgB;oBAC7C6G,aAAa,EAAE9G,YAAY,CAAC2G,cAAe;oBAC3CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAG;oBAC/B6G,eAAe,EAAC;kBAAO;oBAAA1P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF5D,OAAA,CAAC/B,cAAc;oBACbsV,cAAc,EAAEpH,YAAY,CAACC,eAAgB;oBAC7C6G,aAAa,EAAE9G,YAAY,CAAC2G,cAAe;oBAC3CI,aAAa,EAAEjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,EAAG;oBAC/B6G,eAAe,EAAC;kBAAS;oBAAA1P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GA5VI,gBAAgBuI,YAAY,CAACC,eAAe,EAAE;gBAAA3I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6VhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL+E,kBAAkB,iBACjB3I,OAAA;MAAK0C,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBwM,MAAM,EAAE,IAAI;QACZ3F,OAAO,EAAE;MACX,CAAE;MACF7D,OAAO,EAAEA,CAAA,KAAM0C,qBAAqB,CAAC,IAAI,CAAE;MAAAvF,QAAA,eAEzCrD,OAAA;QAAK0C,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpB6E,QAAQ,EAAE,OAAO;UACjB3E,KAAK,EAAE,MAAM;UACbmO,SAAS,EAAE,MAAM;UACjBtO,QAAQ,EAAE,MAAM;UAChB0K,SAAS,EAAE;QACb,CAAE;QACF1J,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACwP,eAAe,CAAC,CAAE;QAAApQ,QAAA,gBAGlCrD,OAAA;UAAK0C,KAAK,EAAE;YACVqH,OAAO,EAAE,QAAQ;YACjB4F,YAAY,EAAE,mBAAmB;YACjC3M,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACArD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACArD,OAAA,CAACpB,GAAG;cAACqR,IAAI,EAAE,EAAG;cAACvN,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5D,OAAA;cAAI0C,KAAK,EAAE;gBACToN,MAAM,EAAE,CAAC;gBACTtM,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN5D,OAAA;YACEkG,OAAO,EAAEA,CAAA,KAAM0C,qBAAqB,CAAC,IAAI,CAAE;YAC3ClG,KAAK,EAAE;cACLoH,UAAU,EAAE,MAAM;cAClBjG,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClBJ,KAAK,EAAE,SAAS;cAChBgC,MAAM,EAAE,SAAS;cACjB2E,OAAO,EAAE,SAAS;cAClB5E,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5D,OAAA;UAAK0C,KAAK,EAAE;YAAEqH,OAAO,EAAE;UAAS,CAAE;UAAA1G,QAAA,gBAChCrD,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,SAAS;cACdnC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,GACC,CAAC,MAAM;cACN,IAAIsF,kBAAkB,CAACuI,QAAQ,EAAE;gBAC/B,oBACElR,OAAA;kBAAM0C,KAAK,EAAE;oBACXoH,UAAU,EAAE,mDAAmD;oBAC/D1G,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBiG,OAAO,EAAE,iBAAiB;oBAC1B5E,YAAY,EAAE,MAAM;oBACpB8M,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtBlP,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACArD,OAAA,CAACZ,aAAa;oBAAC6Q,IAAI,EAAE,EAAG;oBAAC7M,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAEX,CAAC,MAAM;gBACL,MAAM6G,YAAY,GAAG,CAAC9B,kBAAkB,CAACwI,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;gBAClF,MAAMC,aAAa,GAAG7G,gBAAgB,CAACC,YAAY,CAAC;gBACpD,MAAMuH,aAAa,GAAGX,aAAa,CAAC1G,IAAI;gBAExC,oBACE3K,OAAA;kBAAM0C,KAAK,EAAE;oBACXoH,UAAU,EAAEuH,aAAa,CAACvH,UAAU;oBACpC1G,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBiG,OAAO,EAAE,iBAAiB;oBAC1B5E,YAAY,EAAE,MAAM;oBACpB8M,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtBlP,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACArD,OAAA,CAACgS,aAAa;oBAAC/B,IAAI,EAAE,EAAG;oBAAC7M,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxC6G,YAAY;gBAAA;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAEX;YACF,CAAC,EAAE,CAAC,eAEJ5D,OAAA;cAAM0C,KAAK,EAAE;gBACXoH,UAAU,EAAE,mDAAmD;gBAC/D1G,KAAK,EAAE,OAAO;gBACd2G,OAAO,EAAE,iBAAiB;gBAC1B5E,YAAY,EAAE,MAAM;gBACpB3B,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBd,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA,CAACpB,GAAG;gBAACqR,IAAI,EAAE;cAAG;gBAAAxM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN5D,OAAA;YAAI0C,KAAK,EAAE;cACToN,MAAM,EAAE,YAAY;cACpBtM,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE,SAAS;cAChB2M,UAAU,EAAE;YACd,CAAE;YAAA1M,QAAA,EACCsF,kBAAkB,CAAC+E;UAAK;YAAAjK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EAGJ+E,kBAAkB,CAACoD,WAAW,IAAIpD,kBAAkB,CAACoD,WAAW,CAACxH,MAAM,GAAG,CAAC,iBAC1EvE,OAAA;YAAK0C,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,eACrCrD,OAAA,CAACmE,YAAY;cACXC,MAAM,EAAEuE,kBAAkB,CAACoD,WAAY;cACvC1H,SAAS,EAAEsE,kBAAkB,CAAC+E,KAAM;cACpCrN,QAAQ,EAAE0G,WAAY;cACtBzC,YAAY,EAAGQ,KAAK,IAAK;gBACvBmF,YAAY,CAACtB,kBAAkB,CAACoD,WAAW,EAAEjH,KAAK,CAAC;cACrD;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED5D,OAAA;YAAK0C,KAAK,EAAE;cACVU,KAAK,EAAE,SAAS;cAChBI,QAAQ,EAAE,MAAM;cAChBuM,UAAU,EAAE,KAAK;cACjBxM,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACCsF,kBAAkB,CAACkF;UAAO;YAAApK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEN5D,OAAA;YAAK0C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXlC,QAAQ,EAAE,UAAU;cACpBJ,KAAK,EAAE,SAAS;cAChBwP,UAAU,EAAE,MAAM;cAClBlB,SAAS,EAAE;YACb,CAAE;YAAArO,QAAA,gBACArD,OAAA;cAAK0C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACArD,OAAA,CAACnB,QAAQ;gBAACoR,IAAI,EAAE;cAAG;gBAAAxM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB5D,OAAA;gBAAAqD,QAAA,GAAM,aAAW,EAAC,IAAIiL,IAAI,CAAC3F,kBAAkB,CAAC4I,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA/N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACL+E,kBAAkB,CAAC2K,WAAW,iBAC7BtT,OAAA;cAAAqD,QAAA,GAAK,MACC,EAACsF,kBAAkB,CAAC2K,WAAW;YAAA;cAAA7P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5D,OAAA,CAAC3B,aAAa;MACZ+F,MAAM,EAAE6E,cAAe;MACvBiB,YAAY,EAAEf,oBAAqB;MACnCuK,MAAM,EAAE3K,YAAa;MACrB4K,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,KAAK,CAAE;MACtC3E,SAAS,EAAC;IAAoB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC+C,GAAA,CAl9DID,QAAiC;EAAA,QACpBjJ,WAAW,EAGVK,YAAY,EACVC,cAAc,EAWXJ,aAAa,EAUhCC,gBAAgB,EASyBC,qBAAqB;AAAA;AAAA+V,GAAA,GAnC9DlN,QAAiC;AAo9DvC,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAAuC,GAAA,EAAAmN,GAAA;AAAAC,YAAA,CAAA3P,EAAA;AAAA2P,YAAA,CAAApN,GAAA;AAAAoN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}