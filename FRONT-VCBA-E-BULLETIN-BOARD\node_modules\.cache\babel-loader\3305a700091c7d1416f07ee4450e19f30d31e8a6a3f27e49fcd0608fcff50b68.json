{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M7 7v10\",\n  key: \"d5nglc\"\n}], [\"path\", {\n  d: \"M11 7v10\",\n  key: \"pptsnr\"\n}], [\"path\", {\n  d: \"m15 7 2 10\",\n  key: \"1m7qm5\"\n}]];\nconst SquareLibrary = createLucideIcon(\"square-library\", __iconNode);\nexport { __iconNode, SquareLibrary as default };\n//# sourceMappingURL=square-library.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}