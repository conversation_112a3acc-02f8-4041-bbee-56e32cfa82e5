{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l-4-2\",\n  key: \"cedpoo\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst Clock10 = createLucideIcon(\"clock-10\", __iconNode);\nexport { __iconNode, Clock10 as default };\n//# sourceMappingURL=clock-10.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}