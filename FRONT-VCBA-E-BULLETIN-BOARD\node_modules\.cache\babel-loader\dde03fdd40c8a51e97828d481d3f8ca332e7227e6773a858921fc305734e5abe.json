{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22v-6\",\n  key: \"6o8u61\"\n}], [\"path\", {\n  d: \"M12 8V2\",\n  key: \"1wkif3\"\n}], [\"path\", {\n  d: \"M4 12H2\",\n  key: \"rhcxmi\"\n}], [\"path\", {\n  d: \"M10 12H8\",\n  key: \"s88cx1\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}], [\"path\", {\n  d: \"m15 19-3-3-3 3\",\n  key: \"e37ymu\"\n}], [\"path\", {\n  d: \"m15 5-3 3-3-3\",\n  key: \"19d6lf\"\n}]];\nconst FoldVertical = createLucideIcon(\"fold-vertical\", __iconNode);\nexport { __iconNode, FoldVertical as default };\n//# sourceMappingURL=fold-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}