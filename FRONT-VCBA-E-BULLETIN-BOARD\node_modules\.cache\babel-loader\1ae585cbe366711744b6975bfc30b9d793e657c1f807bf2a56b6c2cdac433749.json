{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M11 9h4a2 2 0 0 0 2-2V3\",\n  key: \"1ve2rv\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"M7 21v-4a2 2 0 0 1 2-2h4\",\n  key: \"1fwkro\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"3i40o0\"\n}]];\nconst CircuitBoard = createLucideIcon(\"circuit-board\", __iconNode);\nexport { __iconNode, CircuitBoard as default };\n//# sourceMappingURL=circuit-board.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}