{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 17h1.227a2 2 0 0 0 1.345-.52L18 12\",\n  key: \"16muxl\"\n}], [\"path\", {\n  d: \"m12 13.5 3.75.5\",\n  key: \"1i9qhk\"\n}], [\"path\", {\n  d: \"m4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8\",\n  key: \"12lg5p\"\n}], [\"path\", {\n  d: \"M6 10V8\",\n  key: \"1y41hn\"\n}], [\"path\", {\n  d: \"M6 14v1\",\n  key: \"cao2tf\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"8\",\n  width: \"20\",\n  height: \"13\",\n  rx: \"2\",\n  key: \"p3bz5l\"\n}]];\nconst TicketsPlane = createLucideIcon(\"tickets-plane\", __iconNode);\nexport { __iconNode, TicketsPlane as default };\n//# sourceMappingURL=tickets-plane.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}