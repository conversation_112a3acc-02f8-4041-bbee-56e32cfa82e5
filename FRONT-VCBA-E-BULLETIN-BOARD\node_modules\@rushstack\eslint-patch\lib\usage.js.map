{"version": 3, "file": "usage.js", "sourceRoot": "", "sources": ["../src/usage.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,MAAM,IAAI,KAAK,CACb,0EAA0E;IACxE,wCAAwC,CAC3C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nthrow new Error(\n  'The @rushstack/eslint-patch package does not have a default entry point.' +\n    ' See README.md for usage instructions.'\n);\n\nexport {};\n"]}