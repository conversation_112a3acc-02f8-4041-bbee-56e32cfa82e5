{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const fileInputRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '120px',\n          height: '120px',\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '3px dashed #22c55e' : hasImage ? '3px solid #e8f5e8' : '3px dashed #d1d5db',\n          transition: 'all 0.3s ease',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          transform: isDragOver ? 'scale(1.05)' : 'scale(1)',\n          boxShadow: isDragOver ? '0 8px 25px rgba(34, 197, 94, 0.15)' : hasImage ? '0 4px 12px rgba(0, 0, 0, 0.1)' : '0 2px 8px rgba(0, 0, 0, 0.05)',\n          background: isDragOver ? '#f0fdf4' : hasImage ? 'transparent' : '#f9fafb'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return !isLoading && ((_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click());\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        role: \"button\",\n        tabIndex: 0,\n        \"aria-label\": hasImage ? \"Change profile picture\" : \"Upload profile picture\",\n        onKeyDown: e => {\n          if (e.key === 'Enter' || e.key === ' ') {\n            var _fileInputRef$current2;\n            e.preventDefault();\n            !isLoading && ((_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click());\n          }\n        },\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: isDragOver ? 'rgba(34, 197, 94, 0.8)' : isHovered && !isLoading ? 'rgba(0, 0, 0, 0.6)' : 'transparent',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : isHovered && !isLoading ? 1 : 0,\n            transition: 'all 0.3s ease',\n            color: 'white'\n          },\n          children: isDragOver ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 24,\n              style: {\n                marginBottom: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.75rem',\n                fontWeight: '500'\n              },\n              children: \"Drop here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Camera, {\n              size: 20,\n              style: {\n                marginBottom: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.7rem',\n                fontWeight: '500'\n              },\n              children: hasImage ? 'Change' : 'Upload'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              border: '2px solid #e8f5e8',\n              borderTop: '2px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current3;\n              return (_fileInputRef$current3 = fileInputRef.current) === null || _fileInputRef$current3 === void 0 ? void 0 : _fileInputRef$current3.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: isLoading ? 'linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%)' : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '12px',\n              padding: '0.875rem 1.75rem',\n              fontWeight: '600',\n              fontSize: '0.875rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.7 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              transition: 'all 0.2s ease',\n              boxShadow: isLoading ? 'none' : '0 2px 8px rgba(34, 197, 94, 0.2)',\n              minWidth: '140px',\n              justifyContent: 'center'\n            },\n            onMouseEnter: e => {\n              if (!isLoading) {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              }\n            },\n            onMouseLeave: e => {\n              if (!isLoading) {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n              }\n            },\n            children: [isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid rgba(255, 255, 255, 0.3)',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), isLoading ? 'Uploading...' : hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"4rMTCBYoUV0cMPGHqLCNDbTEfz8=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "_s", "preview", "setPreview", "isDragOver", "setIsDragOver", "isHovered", "setIsHovered", "error", "setError", "success", "setSuccess", "fileInputRef", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "size", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "setTimeout", "err", "message", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemove", "displayImage", "hasImage", "Boolean", "console", "log", "children", "style", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "transform", "boxShadow", "background", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "current", "click", "onMouseEnter", "onMouseLeave", "role", "tabIndex", "onKeyDown", "key", "_fileInputRef$current2", "src", "alt", "onLoad", "onError", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "flexDirection", "opacity", "marginBottom", "borderTop", "animation", "_fileInputRef$current3", "disabled", "padding", "min<PERSON><PERSON><PERSON>", "currentTarget", "ref", "accept", "onChange", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(async (file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n\n    try {\n      await onUpload(file);\n      setSuccess('Profile picture updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n      setPreview(null);\n    }\n  }, [validateFile, onUpload]);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove\n  const handleRemove = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image\n  const displayImage = preview || currentPicture;\n  const hasImage = Boolean(displayImage);\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasImage\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div\n          style={{\n            position: 'relative',\n            width: '120px',\n            height: '120px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '3px dashed #22c55e' : hasImage ? '3px solid #e8f5e8' : '3px dashed #d1d5db',\n            transition: 'all 0.3s ease',\n            cursor: isLoading ? 'not-allowed' : 'pointer',\n            transform: isDragOver ? 'scale(1.05)' : 'scale(1)',\n            boxShadow: isDragOver ? '0 8px 25px rgba(34, 197, 94, 0.15)' : hasImage ? '0 4px 12px rgba(0, 0, 0, 0.1)' : '0 2px 8px rgba(0, 0, 0, 0.05)',\n            background: isDragOver ? '#f0fdf4' : hasImage ? 'transparent' : '#f9fafb'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => !isLoading && fileInputRef.current?.click()}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          role=\"button\"\n          tabIndex={0}\n          aria-label={hasImage ? \"Change profile picture\" : \"Upload profile picture\"}\n          onKeyDown={(e) => {\n            if (e.key === 'Enter' || e.key === ' ') {\n              e.preventDefault();\n              !isLoading && fileInputRef.current?.click();\n            }\n          }}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: '2rem'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Interactive Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: isDragOver\n                ? 'rgba(34, 197, 94, 0.8)'\n                : isHovered && !isLoading\n                  ? 'rgba(0, 0, 0, 0.6)'\n                  : 'transparent',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : isHovered && !isLoading ? 1 : 0,\n              transition: 'all 0.3s ease',\n              color: 'white'\n            }}\n          >\n            {isDragOver ? (\n              <>\n                <Upload size={24} style={{ marginBottom: '4px' }} />\n                <span style={{ fontSize: '0.75rem', fontWeight: '500' }}>Drop here</span>\n              </>\n            ) : (\n              <>\n                <Camera size={20} style={{ marginBottom: '4px' }} />\n                <span style={{ fontSize: '0.7rem', fontWeight: '500' }}>\n                  {hasImage ? 'Change' : 'Upload'}\n                </span>\n              </>\n            )}\n          </div>\n\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  border: '2px solid #e8f5e8',\n                  borderTop: '2px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: isLoading\n                  ? 'linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%)'\n                  : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.875rem 1.75rem',\n                fontWeight: '600',\n                fontSize: '0.875rem',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.7 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease',\n                boxShadow: isLoading ? 'none' : '0 2px 8px rgba(34, 197, 94, 0.2)',\n                minWidth: '140px',\n                justifyContent: 'center'\n              }}\n              onMouseEnter={(e) => {\n                if (!isLoading) {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isLoading) {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }\n              }}\n            >\n              {isLoading ? (\n                <div\n                  style={{\n                    width: '16px',\n                    height: '16px',\n                    border: '2px solid rgba(255, 255, 255, 0.3)',\n                    borderTop: '2px solid white',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }}\n                />\n              ) : (\n                <Upload size={16} />\n              )}\n              {isLoading ? 'Uploading...' : hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAW3E,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAG7B,WAAW,CAAE8B,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,gBAAgB,GAAGpC,WAAW,CAAC,MAAO8B,IAAU,IAAK;IACzDL,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMU,eAAe,GAAGR,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIO,eAAe,EAAE;MACnBZ,QAAQ,CAACY,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBvB,UAAU,EAAAuB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;IACxC,CAAC;IACDN,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBpB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDa,MAAM,CAACQ,aAAa,CAAChB,IAAI,CAAC;IAE1B,IAAI;MACF,MAAMjB,QAAQ,CAACiB,IAAI,CAAC;MACpBH,UAAU,CAAC,uCAAuC,CAAC;MACnDoB,UAAU,CAAC,MAAMpB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MACjBvB,QAAQ,CAACuB,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;MAC3D9B,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACU,YAAY,EAAEhB,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAMqC,iBAAiB,GAAIT,CAAsC,IAAK;IAAA,IAAAU,eAAA;IACpE,MAAMrB,IAAI,IAAAqB,eAAA,GAAGV,CAAC,CAACE,MAAM,CAACS,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIrB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIZ,CAAkB,IAAK;IAC7CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMkC,eAAe,GAAId,CAAkB,IAAK;IAC9CA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMmC,UAAU,GAAIf,CAAkB,IAAK;IACzCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMS,IAAI,GAAGW,CAAC,CAACgB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAItB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BjC,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBR,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAML,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDoB,UAAU,CAAC,MAAMpB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MACjBvB,QAAQ,CAACuB,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMU,YAAY,GAAGzC,OAAO,IAAIP,cAAc;EAC9C,MAAMiD,QAAQ,GAAGC,OAAO,CAACF,YAAY,CAAC;;EAEtC;EACAG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9CpD,cAAc;IACdO,OAAO;IACPyC,YAAY;IACZC;EACF,CAAC,CAAC;EAEF,oBACErD,OAAA;IAAKS,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAgD,QAAA,gBACpDzD,OAAA;MAAK0D,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAEjEzD,OAAA;QACE0D,KAAK,EAAE;UACLI,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEtD,UAAU,GAAG,oBAAoB,GAAGwC,QAAQ,GAAG,mBAAmB,GAAG,oBAAoB;UACjGe,UAAU,EAAE,eAAe;UAC3BC,MAAM,EAAE7D,SAAS,GAAG,aAAa,GAAG,SAAS;UAC7C8D,SAAS,EAAEzD,UAAU,GAAG,aAAa,GAAG,UAAU;UAClD0D,SAAS,EAAE1D,UAAU,GAAG,oCAAoC,GAAGwC,QAAQ,GAAG,+BAA+B,GAAG,+BAA+B;UAC3ImB,UAAU,EAAE3D,UAAU,GAAG,SAAS,GAAGwC,QAAQ,GAAG,aAAa,GAAG;QAClE,CAAE;QACFoB,UAAU,EAAE3B,cAAe;QAC3B4B,WAAW,EAAE1B,eAAgB;QAC7B2B,MAAM,EAAE1B,UAAW;QACnB2B,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,OAAM,CAACrE,SAAS,MAAAqE,qBAAA,GAAIxD,YAAY,CAACyD,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;QAAA,CAAC;QAC3DC,YAAY,EAAEA,CAAA,KAAMhE,YAAY,CAAC,IAAI,CAAE;QACvCiE,YAAY,EAAEA,CAAA,KAAMjE,YAAY,CAAC,KAAK,CAAE;QACxCkE,IAAI,EAAC,QAAQ;QACbC,QAAQ,EAAE,CAAE;QACZ,cAAY9B,QAAQ,GAAG,wBAAwB,GAAG,wBAAyB;QAC3E+B,SAAS,EAAGlD,CAAC,IAAK;UAChB,IAAIA,CAAC,CAACmD,GAAG,KAAK,OAAO,IAAInD,CAAC,CAACmD,GAAG,KAAK,GAAG,EAAE;YAAA,IAAAC,sBAAA;YACtCpD,CAAC,CAACa,cAAc,CAAC,CAAC;YAClB,CAACvC,SAAS,MAAA8E,sBAAA,GAAIjE,YAAY,CAACyD,OAAO,cAAAQ,sBAAA,uBAApBA,sBAAA,CAAsBP,KAAK,CAAC,CAAC;UAC7C;QACF,CAAE;QAAAtB,QAAA,GAEDJ,QAAQ,gBACPrD,OAAA;UACEuF,GAAG,EAAEnC,YAAa;UAClBoC,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMlC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,YAAY,CAAE;UACxEsC,OAAO,EAAGxD,CAAC,IAAKqB,OAAO,CAACtC,KAAK,CAAC,yBAAyB,EAAEmC,YAAY,EAAElB,CAAC,CAAE;UAC1EwB,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACd2B,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF/F,OAAA;UACE0D,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdQ,UAAU,EAAE,mDAAmD;YAC/Db,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBoC,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAA1C,QAAA,EAEDpD;QAAY;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD/F,OAAA;UACE0D,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpBsC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACT/B,UAAU,EAAE3D,UAAU,GAClB,wBAAwB,GACxBE,SAAS,IAAI,CAACP,SAAS,GACrB,oBAAoB,GACpB,aAAa;YACnBmD,OAAO,EAAE,MAAM;YACf6C,aAAa,EAAE,QAAQ;YACvB5C,UAAU,EAAE,QAAQ;YACpBoC,cAAc,EAAE,QAAQ;YACxBS,OAAO,EAAE5F,UAAU,GAAG,CAAC,GAAGE,SAAS,IAAI,CAACP,SAAS,GAAG,CAAC,GAAG,CAAC;YACzD4D,UAAU,EAAE,eAAe;YAC3B6B,KAAK,EAAE;UACT,CAAE;UAAAxC,QAAA,EAED5C,UAAU,gBACTb,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACEzD,OAAA,CAACN,MAAM;cAACkC,IAAI,EAAE,EAAG;cAAC8B,KAAK,EAAE;gBAAEgD,YAAY,EAAE;cAAM;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/F,OAAA;cAAM0D,KAAK,EAAE;gBAAEyC,QAAQ,EAAE,SAAS;gBAAED,UAAU,EAAE;cAAM,CAAE;cAAAzC,QAAA,EAAC;YAAS;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACzE,CAAC,gBAEH/F,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACEzD,OAAA,CAACJ,MAAM;cAACgC,IAAI,EAAE,EAAG;cAAC8B,KAAK,EAAE;gBAAEgD,YAAY,EAAE;cAAM;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/F,OAAA;cAAM0D,KAAK,EAAE;gBAAEyC,QAAQ,EAAE,QAAQ;gBAAED,UAAU,EAAE;cAAM,CAAE;cAAAzC,QAAA,EACpDJ,QAAQ,GAAG,QAAQ,GAAG;YAAQ;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELvF,SAAS,iBACRR,OAAA;UACE0D,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpBsC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACT/B,UAAU,EAAE,0BAA0B;YACtCb,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBoC,cAAc,EAAE;UAClB,CAAE;UAAAvC,QAAA,eAEFzD,OAAA;YACE0D,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,MAAM,EAAE,mBAAmB;cAC3BwC,SAAS,EAAE,mBAAmB;cAC9B1C,YAAY,EAAE,KAAK;cACnB2C,SAAS,EAAE;YACb;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/F,OAAA;QAAK0D,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE6C,aAAa,EAAE,QAAQ;UAAE3C,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACpEzD,OAAA;UAAK0D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAC3CzD,OAAA;YACE4E,OAAO,EAAEA,CAAA;cAAA,IAAAiC,sBAAA;cAAA,QAAAA,sBAAA,GAAMxF,YAAY,CAACyD,OAAO,cAAA+B,sBAAA,uBAApBA,sBAAA,CAAsB9B,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7C+B,QAAQ,EAAEtG,SAAU;YACpBkD,KAAK,EAAE;cACLc,UAAU,EAAEhE,SAAS,GACjB,mDAAmD,GACnD,mDAAmD;cACvDyF,KAAK,EAAE,OAAO;cACd9B,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,MAAM;cACpB8C,OAAO,EAAE,kBAAkB;cAC3Bb,UAAU,EAAE,KAAK;cACjBC,QAAQ,EAAE,UAAU;cACpB9B,MAAM,EAAE7D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CiG,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BmD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,QAAQ;cACbO,UAAU,EAAE,eAAe;cAC3BG,SAAS,EAAE/D,SAAS,GAAG,MAAM,GAAG,kCAAkC;cAClEwG,QAAQ,EAAE,OAAO;cACjBhB,cAAc,EAAE;YAClB,CAAE;YACFhB,YAAY,EAAG9C,CAAC,IAAK;cACnB,IAAI,CAAC1B,SAAS,EAAE;gBACd0B,CAAC,CAAC+E,aAAa,CAACvD,KAAK,CAACY,SAAS,GAAG,kBAAkB;gBACpDpC,CAAC,CAAC+E,aAAa,CAACvD,KAAK,CAACa,SAAS,GAAG,mCAAmC;cACvE;YACF,CAAE;YACFU,YAAY,EAAG/C,CAAC,IAAK;cACnB,IAAI,CAAC1B,SAAS,EAAE;gBACd0B,CAAC,CAAC+E,aAAa,CAACvD,KAAK,CAACY,SAAS,GAAG,eAAe;gBACjDpC,CAAC,CAAC+E,aAAa,CAACvD,KAAK,CAACa,SAAS,GAAG,kCAAkC;cACtE;YACF,CAAE;YAAAd,QAAA,GAEDjD,SAAS,gBACRR,OAAA;cACE0D,KAAK,EAAE;gBACLK,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdG,MAAM,EAAE,oCAAoC;gBAC5CwC,SAAS,EAAE,iBAAiB;gBAC5B1C,YAAY,EAAE,KAAK;gBACnB2C,SAAS,EAAE;cACb;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEF/F,OAAA,CAACN,MAAM;cAACkC,IAAI,EAAE;YAAG;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACpB,EACAvF,SAAS,GAAG,cAAc,GAAG6C,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,EAER1C,QAAQ,iBACPrD,OAAA;YACE4E,OAAO,EAAEzB,YAAa;YACtB2D,QAAQ,EAAEtG,SAAU;YACpBkD,KAAK,EAAE;cACLc,UAAU,EAAE,MAAM;cAClBL,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnB8C,OAAO,EAAE,gBAAgB;cACzB1C,MAAM,EAAE7D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CyF,KAAK,EAAE,SAAS;cAChBQ,OAAO,EAAEjG,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5BmD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEFzD,OAAA,CAACL,CAAC;cAACiC,IAAI,EAAE;YAAG;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/F,OAAA;UACEkH,GAAG,EAAE7F,YAAa;UAClBM,IAAI,EAAC,MAAM;UACXwF,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAEzE,iBAAkB;UAC5Be,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF/F,OAAA;UAAG0D,KAAK,EAAE;YACRyC,QAAQ,EAAE,UAAU;YACpBF,KAAK,EAAE,SAAS;YAChBoB,MAAM,EAAE;UACV,CAAE;UAAA5D,QAAA,EAAC;QAEH;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9E,KAAK,iBACJjB,OAAA;MAAK0D,KAAK,EAAE;QACV4D,SAAS,EAAE,MAAM;QACjBP,OAAO,EAAE,SAAS;QAClBvC,UAAU,EAAE,SAAS;QACrBL,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBgC,KAAK,EAAE,SAAS;QAChBtC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAzD,OAAA,CAACH,WAAW;QAAC+B,IAAI,EAAE;MAAG;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB9E,KAAK;IAAA;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5E,OAAO,iBACNnB,OAAA;MAAK0D,KAAK,EAAE;QACV4D,SAAS,EAAE,MAAM;QACjBP,OAAO,EAAE,SAAS;QAClBvC,UAAU,EAAE,SAAS;QACrBL,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBgC,KAAK,EAAE,SAAS;QAChBtC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACAzD,OAAA,CAACF,WAAW;QAAC8B,IAAI,EAAE;MAAG;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB5E,OAAO;IAAA;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD/F,OAAA;MAAAyD,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACrF,EAAA,CAtYIP,oBAAyD;AAAAoH,EAAA,GAAzDpH,oBAAyD;AAwY/D,eAAeA,oBAAoB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}