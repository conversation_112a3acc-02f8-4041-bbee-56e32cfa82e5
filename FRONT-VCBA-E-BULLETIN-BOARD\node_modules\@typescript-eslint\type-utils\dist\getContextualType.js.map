{"version": 3, "file": "getContextualType.js", "sourceRoot": "", "sources": ["../src/getContextualType.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAUiB;AACjB,+CAAiC;AAEjC;;;;GAIG;AACH,SAAgB,iBAAiB,CAC/B,OAAuB,EACvB,IAAmB;IAEnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO;KACR;IAED,IAAI,IAAA,0BAAgB,EAAC,MAAM,CAAC,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,EAAE;QACvD,IAAI,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;YAC9B,2CAA2C;YAC3C,OAAO;SACR;KACF;SAAM,IACL,IAAA,+BAAqB,EAAC,MAAM,CAAC;QAC7B,IAAA,+BAAqB,EAAC,MAAM,CAAC;QAC7B,IAAA,gCAAsB,EAAC,MAAM,CAAC,EAC9B;QACA,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KAC3E;SAAM,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;KAC1C;SAAM,IAAI,IAAA,8BAAoB,EAAC,MAAM,CAAC,IAAI,IAAA,sBAAY,EAAC,IAAI,CAAC,EAAE;QAC7D,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACxC;SAAM,IACL,IAAA,4BAAkB,EAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;QACvD,MAAM,CAAC,KAAK,KAAK,IAAI,EACrB;QACA,uBAAuB;QACvB,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC/C;SAAM,IACL,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,QAAQ,CACjE,MAAM,CAAC,IAAI,CACZ,EACD;QACA,oEAAoE;QACpE,OAAO;KACR;IACD,2CAA2C;IAE3C,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC;AA1CD,8CA0CC"}