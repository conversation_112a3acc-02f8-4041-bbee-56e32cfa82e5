{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 4v16\",\n  key: \"8vvj80\"\n}], [\"path\", {\n  d: \"M17 4v16\",\n  key: \"7dpous\"\n}], [\"path\", {\n  d: \"M19 4H9.5a4.5 4.5 0 0 0 0 9H13\",\n  key: \"sh4n9v\"\n}]];\nconst Pilcrow = createLucideIcon(\"pilcrow\", __iconNode);\nexport { __iconNode, Pilcrow as default };\n//# sourceMappingURL=pilcrow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}