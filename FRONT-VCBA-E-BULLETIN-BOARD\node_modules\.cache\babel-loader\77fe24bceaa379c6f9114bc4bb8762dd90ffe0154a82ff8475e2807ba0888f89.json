{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 4.1 12 6\",\n  key: \"ita8i4\"\n}], [\"path\", {\n  d: \"m5.1 8-2.9-.8\",\n  key: \"1go3kf\"\n}], [\"path\", {\n  d: \"m6 12-1.9 2\",\n  key: \"mnht97\"\n}], [\"path\", {\n  d: \"M7.2 2.2 8 5.1\",\n  key: \"1cfko1\"\n}], [\"path\", {\n  d: \"M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z\",\n  key: \"s0h3yz\"\n}]];\nconst MousePointerClick = createLucideIcon(\"mouse-pointer-click\", __iconNode);\nexport { __iconNode, MousePointerClick as default };\n//# sourceMappingURL=mouse-pointer-click.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}