{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 6 4 14\",\n  key: \"ji33uf\"\n}], [\"path\", {\n  d: \"M12 6v14\",\n  key: \"1n7gus\"\n}], [\"path\", {\n  d: \"M8 8v12\",\n  key: \"1gg7y9\"\n}], [\"path\", {\n  d: \"M4 4v16\",\n  key: \"6qkkli\"\n}]];\nconst Library = createLucideIcon(\"library\", __iconNode);\nexport { __iconNode, Library as default };\n//# sourceMappingURL=library.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}