{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * Comment Depth Management Utilities\n * \n * This module provides utilities for managing comment threading depth\n * to prevent infinite reply loops, following industry best practices.\n * \n * Depth Levels:\n * - Level 0: Original comment (top-level)\n * - Level 1: Reply to original comment\n * - Level 2: Reply to reply (maximum allowed depth)\n * - Level 3+: Should be flattened or redirected\n */// Configuration constants\nexport const COMMENT_DEPTH_CONFIG={MAX_DEPTH:2,// Maximum allowed depth (0-based)\nMAX_VISUAL_DEPTH:3,// Maximum visual indentation depth\nFLATTEN_THRESHOLD:3,// Depth at which to start flattening\nINDENT_SIZE:20,// Pixels per depth level\nMAX_INDENT:60// Maximum indentation in pixels\n};/**\n * Calculate the depth of a comment in the thread hierarchy\n */export function calculateCommentDepth(comment,allComments){let depth=0;let currentComment=comment;// Traverse up the parent chain to calculate depth\nwhile(currentComment.parent_comment_id){depth++;const parentComment=allComments.find(c=>c.comment_id===currentComment.parent_comment_id);if(!parentComment)break;currentComment=parentComment;// Safety check to prevent infinite loops\nif(depth>10){console.warn('Comment depth calculation exceeded safety limit');break;}}return depth;}/**\n * Check if a comment can have replies based on depth limits\n */export function canCommentHaveReplies(depth){return depth<COMMENT_DEPTH_CONFIG.MAX_DEPTH;}/**\n * Check if a comment should show the reply button\n */export function shouldShowReplyButton(depth){return canCommentHaveReplies(depth);}/**\n * Check if a comment thread should be flattened\n */export function shouldFlattenThread(depth){return depth>=COMMENT_DEPTH_CONFIG.FLATTEN_THRESHOLD;}/**\n * Calculate visual indentation for a comment based on depth\n */export function calculateIndentation(depth){const visualDepth=Math.min(depth,COMMENT_DEPTH_CONFIG.MAX_VISUAL_DEPTH);const indentation=visualDepth*COMMENT_DEPTH_CONFIG.INDENT_SIZE;return Math.min(indentation,COMMENT_DEPTH_CONFIG.MAX_INDENT);}/**\n * Get the appropriate parent comment ID for a new reply\n * If depth limit is reached, redirect to the root comment\n */export function getReplyParentId(targetComment,allComments){const currentDepth=calculateCommentDepth(targetComment,allComments);if(currentDepth<COMMENT_DEPTH_CONFIG.MAX_DEPTH){// Normal reply - use the target comment as parent\nreturn targetComment.comment_id;}else{// Depth limit reached - find the root comment of this thread\nlet rootComment=targetComment;while(rootComment.parent_comment_id){const parentComment=allComments.find(c=>c.comment_id===rootComment.parent_comment_id);if(!parentComment)break;rootComment=parentComment;}return rootComment.comment_id;}}/**\n * Build a hierarchical comment tree with depth limits\n */export function buildCommentTree(comments){const commentMap=new Map();const rootComments=[];// Create a map of all comments\ncomments.forEach(comment=>{commentMap.set(comment.comment_id,_objectSpread(_objectSpread({},comment),{},{replies:[]}));});// Build the tree structure\ncomments.forEach(comment=>{const commentWithReplies=commentMap.get(comment.comment_id);if(comment.parent_comment_id){const parent=commentMap.get(comment.parent_comment_id);if(parent){const parentDepth=calculateCommentDepth(parent,comments);const currentDepth=parentDepth+1;// Only add as reply if within depth limits\nif(currentDepth<=COMMENT_DEPTH_CONFIG.MAX_DEPTH){parent.replies=parent.replies||[];parent.replies.push(commentWithReplies);}else{// If depth limit exceeded, add as root comment\nrootComments.push(commentWithReplies);}}else{// Parent not found, add as root comment\nrootComments.push(commentWithReplies);}}else{// Top-level comment\nrootComments.push(commentWithReplies);}});return rootComments;}/**\n * Get a user-friendly message for depth limit reached\n */export function getDepthLimitMessage(depth){if(depth>=COMMENT_DEPTH_CONFIG.MAX_DEPTH){return\"Reply depth limit reached. Your reply will be added as a new comment in this thread.\";}return\"\";}/**\n * Get thread continuation message\n */export function getThreadContinuationMessage(commentCount){if(commentCount>0){return\"Continue this thread (\".concat(commentCount,\" more \").concat(commentCount===1?'reply':'replies',\")\");}return\"Continue this thread\";}/**\n * Validate comment depth before creation (for backend validation)\n */export function validateCommentDepth(parentCommentId,allComments){if(!parentCommentId){// Top-level comment is always valid\nreturn{isValid:true};}const parentComment=allComments.find(c=>c.comment_id===parentCommentId);if(!parentComment){return{isValid:false,message:\"Parent comment not found\"};}const parentDepth=calculateCommentDepth(parentComment,allComments);const newCommentDepth=parentDepth+1;if(newCommentDepth>COMMENT_DEPTH_CONFIG.MAX_DEPTH){const suggestedParentId=getReplyParentId(parentComment,allComments);return{isValid:false,message:\"Comment depth limit (\".concat(COMMENT_DEPTH_CONFIG.MAX_DEPTH+1,\" levels) exceeded. Reply will be added to thread root.\"),suggestedParentId};}return{isValid:true};}/**\n * Get CSS classes for comment depth styling\n */export function getCommentDepthClasses(depth){const classes=[\"comment-depth-\".concat(depth)];if(depth===0){classes.push('comment-root');}else if(depth===1){classes.push('comment-reply');}else if(depth>=2){classes.push('comment-deep-reply');}if(shouldFlattenThread(depth)){classes.push('comment-flattened');}return classes;}/**\n * Format depth information for debugging\n */export function formatDepthInfo(comment,allComments){const depth=calculateCommentDepth(comment,allComments);const canReply=canCommentHaveReplies(depth);const shouldFlatten=shouldFlattenThread(depth);return\"Depth: \".concat(depth,\", CanReply: \").concat(canReply,\", ShouldFlatten: \").concat(shouldFlatten);}export default{COMMENT_DEPTH_CONFIG,calculateCommentDepth,canCommentHaveReplies,shouldShowReplyButton,shouldFlattenThread,calculateIndentation,getReplyParentId,buildCommentTree,getDepthLimitMessage,getThreadContinuationMessage,validateCommentDepth,getCommentDepthClasses,formatDepthInfo};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}