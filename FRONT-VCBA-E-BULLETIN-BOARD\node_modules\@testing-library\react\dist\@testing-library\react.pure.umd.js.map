{"version": 3, "file": "react.pure.umd.js", "sources": ["../../src/act-compat.js", "../../src/fire-event.js", "../../src/config.js", "../../src/pure.js"], "sourcesContent": ["import * as React from 'react'\nimport * as DeprecatedReactTestUtils from 'react-dom/test-utils'\n\nconst reactAct =\n  typeof React.act === 'function' ? React.act : DeprecatedReactTestUtils.act\n\nfunction getGlobalThis() {\n  /* istanbul ignore else */\n  if (typeof globalThis !== 'undefined') {\n    return globalThis\n  }\n  /* istanbul ignore next */\n  if (typeof self !== 'undefined') {\n    return self\n  }\n  /* istanbul ignore next */\n  if (typeof window !== 'undefined') {\n    return window\n  }\n  /* istanbul ignore next */\n  if (typeof global !== 'undefined') {\n    return global\n  }\n  /* istanbul ignore next */\n  throw new Error('unable to locate global object')\n}\n\nfunction setIsReactActEnvironment(isReactActEnvironment) {\n  getGlobalThis().IS_REACT_ACT_ENVIRONMENT = isReactActEnvironment\n}\n\nfunction getIsReactActEnvironment() {\n  return getGlobalThis().IS_REACT_ACT_ENVIRONMENT\n}\n\nfunction withGlobalActEnvironment(actImplementation) {\n  return callback => {\n    const previousActEnvironment = getIsReactActEnvironment()\n    setIsReactActEnvironment(true)\n    try {\n      // The return value of `act` is always a thenable.\n      let callbackNeedsToBeAwaited = false\n      const actResult = actImplementation(() => {\n        const result = callback()\n        if (\n          result !== null &&\n          typeof result === 'object' &&\n          typeof result.then === 'function'\n        ) {\n          callbackNeedsToBeAwaited = true\n        }\n        return result\n      })\n      if (callbackNeedsToBeAwaited) {\n        const thenable = actResult\n        return {\n          then: (resolve, reject) => {\n            thenable.then(\n              returnValue => {\n                setIsReactActEnvironment(previousActEnvironment)\n                resolve(returnValue)\n              },\n              error => {\n                setIsReactActEnvironment(previousActEnvironment)\n                reject(error)\n              },\n            )\n          },\n        }\n      } else {\n        setIsReactActEnvironment(previousActEnvironment)\n        return actResult\n      }\n    } catch (error) {\n      // Can't be a `finally {}` block since we don't know if we have to immediately restore IS_REACT_ACT_ENVIRONMENT\n      // or if we have to await the callback first.\n      setIsReactActEnvironment(previousActEnvironment)\n      throw error\n    }\n  }\n}\n\nconst act = withGlobalActEnvironment(reactAct)\n\nexport default act\nexport {\n  setIsReactActEnvironment as setReactActEnvironment,\n  getIsReactActEnvironment,\n}\n\n/* eslint no-console:0 */\n", "import {fireEvent as dtlFireEvent} from '@testing-library/dom'\n\n// react-testing-library's version of fireEvent will call\n// dom-testing-library's version of fireEvent. The reason\n// we make this distinction however is because we have\n// a few extra events that work a bit differently\nconst fireEvent = (...args) => dtlFireEvent(...args)\n\nObject.keys(dtlFireEvent).forEach(key => {\n  fireEvent[key] = (...args) => dtlFireEvent[key](...args)\n})\n\n// React event system tracks native mouseOver/mouseOut events for\n// running onMouseEnter/onMouseLeave handlers\n// @link https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/events/EnterLeaveEventPlugin.js#L24-L31\nconst mouseEnter = fireEvent.mouseEnter\nconst mouseLeave = fireEvent.mouseLeave\nfireEvent.mouseEnter = (...args) => {\n  mouseEnter(...args)\n  return fireEvent.mouseOver(...args)\n}\nfireEvent.mouseLeave = (...args) => {\n  mouseLeave(...args)\n  return fireEvent.mouseOut(...args)\n}\n\nconst pointerEnter = fireEvent.pointerEnter\nconst pointerLeave = fireEvent.pointerLeave\nfireEvent.pointerEnter = (...args) => {\n  pointerEnter(...args)\n  return fireEvent.pointerOver(...args)\n}\nfireEvent.pointerLeave = (...args) => {\n  pointerLeave(...args)\n  return fireEvent.pointerOut(...args)\n}\n\nconst select = fireEvent.select\nfireEvent.select = (node, init) => {\n  select(node, init)\n  // React tracks this event only on focused inputs\n  node.focus()\n\n  // React creates this event when one of the following native events happens\n  // - contextMenu\n  // - mouseUp\n  // - dragEnd\n  // - keyUp\n  // - keyDown\n  // so we can use any here\n  // @link https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/events/SelectEventPlugin.js#L203-L224\n  fireEvent.keyUp(node, init)\n}\n\n// React event system tracks native focusout/focusin events for\n// running blur/focus handlers\n// @link https://github.com/facebook/react/pull/19186\nconst blur = fireEvent.blur\nconst focus = fireEvent.focus\nfireEvent.blur = (...args) => {\n  fireEvent.focusOut(...args)\n  return blur(...args)\n}\nfireEvent.focus = (...args) => {\n  fireEvent.focusIn(...args)\n  return focus(...args)\n}\n\nexport {fireEvent}\n", "import {\n  getConfig as getConfigDTL,\n  configure as configureDTL,\n} from '@testing-library/dom'\n\nlet configForRTL = {\n  reactStrictMode: false,\n}\n\nfunction getConfig() {\n  return {\n    ...getConfigDTL(),\n    ...configForRTL,\n  }\n}\n\nfunction configure(newConfig) {\n  if (typeof newConfig === 'function') {\n    // Pass the existing config out to the provided function\n    // and accept a delta in return\n    newConfig = newConfig(getConfig())\n  }\n\n  const {reactStrictMode, ...configForDTL} = newConfig\n\n  configureDTL(configForDTL)\n\n  configForRTL = {\n    ...configForRTL,\n    reactStrictMode,\n  }\n}\n\nexport {getConfig, configure}\n", "import * as React from 'react'\nimport ReactDOM from 'react-dom'\nimport * as ReactDOMClient from 'react-dom/client'\nimport {\n  getQueriesForElement,\n  prettyDOM,\n  configure as configureDTL,\n} from '@testing-library/dom'\nimport act, {\n  getIsReactActEnvironment,\n  setReactActEnvironment,\n} from './act-compat'\nimport {fireEvent} from './fire-event'\nimport {getConfig, configure} from './config'\n\nfunction jestFakeTimersAreEnabled() {\n  /* istanbul ignore else */\n  if (typeof jest !== 'undefined' && jest !== null) {\n    return (\n      // legacy timers\n      setTimeout._isMockFunction === true || // modern timers\n      // eslint-disable-next-line prefer-object-has-own -- No Object.hasOwn in all target environments we support.\n      Object.prototype.hasOwnProperty.call(setTimeout, 'clock')\n    )\n  } // istanbul ignore next\n\n  return false\n}\n\nconfigureDTL({\n  unstable_advanceTimersWrapper: cb => {\n    return act(cb)\n  },\n  // We just want to run `waitFor` without IS_REACT_ACT_ENVIRONMENT\n  // But that's not necessarily how `asyncWrapper` is used since it's a public method.\n  // Let's just hope nobody else is using it.\n  asyncWrapper: async cb => {\n    const previousActEnvironment = getIsReactActEnvironment()\n    setReactActEnvironment(false)\n    try {\n      const result = await cb()\n      // Drain microtask queue.\n      // Otherwise we'll restore the previous act() environment, before we resolve the `waitFor` call.\n      // The caller would have no chance to wrap the in-flight Promises in `act()`\n      await new Promise(resolve => {\n        setTimeout(() => {\n          resolve()\n        }, 0)\n\n        if (jestFakeTimersAreEnabled()) {\n          jest.advanceTimersByTime(0)\n        }\n      })\n\n      return result\n    } finally {\n      setReactActEnvironment(previousActEnvironment)\n    }\n  },\n  eventWrapper: cb => {\n    let result\n    act(() => {\n      result = cb()\n    })\n    return result\n  },\n})\n\n// Ideally we'd just use a WeakMap where containers are keys and roots are values.\n// We use two variables so that we can bail out in constant time when we render with a new container (most common use case)\n/**\n * @type {Set<import('react-dom').Container>}\n */\nconst mountedContainers = new Set()\n/**\n * @type Array<{container: import('react-dom').Container, root: ReturnType<typeof createConcurrentRoot>}>\n */\nconst mountedRootEntries = []\n\nfunction strictModeIfNeeded(innerElement, reactStrictMode) {\n  return reactStrictMode ?? getConfig().reactStrictMode\n    ? React.createElement(React.StrictMode, null, innerElement)\n    : innerElement\n}\n\nfunction wrapUiIfNeeded(innerElement, wrapperComponent) {\n  return wrapperComponent\n    ? React.createElement(wrapperComponent, null, innerElement)\n    : innerElement\n}\n\nfunction createConcurrentRoot(\n  container,\n  {\n    hydrate,\n    onCaughtError,\n    onRecoverableError,\n    ui,\n    wrapper: WrapperComponent,\n    reactStrictMode,\n  },\n) {\n  let root\n  if (hydrate) {\n    act(() => {\n      root = ReactDOMClient.hydrateRoot(\n        container,\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        {onCaughtError, onRecoverableError},\n      )\n    })\n  } else {\n    root = ReactDOMClient.createRoot(container, {\n      onCaughtError,\n      onRecoverableError,\n    })\n  }\n\n  return {\n    hydrate() {\n      /* istanbul ignore if */\n      if (!hydrate) {\n        throw new Error(\n          'Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.',\n        )\n      }\n      // Nothing to do since hydration happens when creating the root object.\n    },\n    render(element) {\n      root.render(element)\n    },\n    unmount() {\n      root.unmount()\n    },\n  }\n}\n\nfunction createLegacyRoot(container) {\n  return {\n    hydrate(element) {\n      ReactDOM.hydrate(element, container)\n    },\n    render(element) {\n      ReactDOM.render(element, container)\n    },\n    unmount() {\n      ReactDOM.unmountComponentAtNode(container)\n    },\n  }\n}\n\nfunction renderRoot(\n  ui,\n  {\n    baseElement,\n    container,\n    hydrate,\n    queries,\n    root,\n    wrapper: WrapperComponent,\n    reactStrictMode,\n  },\n) {\n  act(() => {\n    if (hydrate) {\n      root.hydrate(\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        container,\n      )\n    } else {\n      root.render(\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        container,\n      )\n    }\n  })\n\n  return {\n    container,\n    baseElement,\n    debug: (el = baseElement, maxLength, options) =>\n      Array.isArray(el)\n        ? // eslint-disable-next-line no-console\n          el.forEach(e => console.log(prettyDOM(e, maxLength, options)))\n        : // eslint-disable-next-line no-console,\n          console.log(prettyDOM(el, maxLength, options)),\n    unmount: () => {\n      act(() => {\n        root.unmount()\n      })\n    },\n    rerender: rerenderUi => {\n      renderRoot(rerenderUi, {\n        container,\n        baseElement,\n        root,\n        wrapper: WrapperComponent,\n        reactStrictMode,\n      })\n      // Intentionally do not return anything to avoid unnecessarily complicating the API.\n      // folks can use all the same utilities we return in the first place that are bound to the container\n    },\n    asFragment: () => {\n      /* istanbul ignore else (old jsdom limitation) */\n      if (typeof document.createRange === 'function') {\n        return document\n          .createRange()\n          .createContextualFragment(container.innerHTML)\n      } else {\n        const template = document.createElement('template')\n        template.innerHTML = container.innerHTML\n        return template.content\n      }\n    },\n    ...getQueriesForElement(baseElement, queries),\n  }\n}\n\nfunction render(\n  ui,\n  {\n    container,\n    baseElement = container,\n    legacyRoot = false,\n    onCaughtError,\n    onUncaughtError,\n    onRecoverableError,\n    queries,\n    hydrate = false,\n    wrapper,\n    reactStrictMode,\n  } = {},\n) {\n  if (onUncaughtError !== undefined) {\n    throw new Error(\n      'onUncaughtError is not supported. The `render` call will already throw on uncaught errors.',\n    )\n  }\n  if (legacyRoot && typeof ReactDOM.render !== 'function') {\n    const error = new Error(\n      '`legacyRoot: true` is not supported in this version of React. ' +\n        'If your app runs React 19 or later, you should remove this flag. ' +\n        'If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.',\n    )\n    Error.captureStackTrace(error, render)\n    throw error\n  }\n\n  if (!baseElement) {\n    // default to document.body instead of documentElement to avoid output of potentially-large\n    // head elements (such as JSS style blocks) in debug output\n    baseElement = document.body\n  }\n  if (!container) {\n    container = baseElement.appendChild(document.createElement('div'))\n  }\n\n  let root\n  // eslint-disable-next-line no-negated-condition -- we want to map the evolution of this over time. The root is created first. Only later is it re-used so we don't want to read the case that happens later first.\n  if (!mountedContainers.has(container)) {\n    const createRootImpl = legacyRoot ? createLegacyRoot : createConcurrentRoot\n    root = createRootImpl(container, {\n      hydrate,\n      onCaughtError,\n      onRecoverableError,\n      ui,\n      wrapper,\n      reactStrictMode,\n    })\n\n    mountedRootEntries.push({container, root})\n    // we'll add it to the mounted containers regardless of whether it's actually\n    // added to document.body so the cleanup method works regardless of whether\n    // they're passing us a custom container or not.\n    mountedContainers.add(container)\n  } else {\n    mountedRootEntries.forEach(rootEntry => {\n      // Else is unreachable since `mountedContainers` has the `container`.\n      // Only reachable if one would accidentally add the container to `mountedContainers` but not the root to `mountedRootEntries`\n      /* istanbul ignore else */\n      if (rootEntry.container === container) {\n        root = rootEntry.root\n      }\n    })\n  }\n\n  return renderRoot(ui, {\n    container,\n    baseElement,\n    queries,\n    hydrate,\n    wrapper,\n    root,\n    reactStrictMode,\n  })\n}\n\nfunction cleanup() {\n  mountedRootEntries.forEach(({root, container}) => {\n    act(() => {\n      root.unmount()\n    })\n    if (container.parentNode === document.body) {\n      document.body.removeChild(container)\n    }\n  })\n  mountedRootEntries.length = 0\n  mountedContainers.clear()\n}\n\nfunction renderHook(renderCallback, options = {}) {\n  const {initialProps, ...renderOptions} = options\n\n  if (renderOptions.legacyRoot && typeof ReactDOM.render !== 'function') {\n    const error = new Error(\n      '`legacyRoot: true` is not supported in this version of React. ' +\n        'If your app runs React 19 or later, you should remove this flag. ' +\n        'If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.',\n    )\n    Error.captureStackTrace(error, renderHook)\n    throw error\n  }\n\n  const result = React.createRef()\n\n  function TestComponent({renderCallbackProps}) {\n    const pendingResult = renderCallback(renderCallbackProps)\n\n    React.useEffect(() => {\n      result.current = pendingResult\n    })\n\n    return null\n  }\n\n  const {rerender: baseRerender, unmount} = render(\n    <TestComponent renderCallbackProps={initialProps} />,\n    renderOptions,\n  )\n\n  function rerender(rerenderCallbackProps) {\n    return baseRerender(\n      <TestComponent renderCallbackProps={rerenderCallbackProps} />,\n    )\n  }\n\n  return {result, rerender, unmount}\n}\n\n// just re-export everything from dom-testing-library\nexport * from '@testing-library/dom'\nexport {render, renderHook, cleanup, act, fireEvent, getConfig, configure}\n\n/* eslint func-name-matching:0 */\n"], "names": ["reactAct", "React", "act", "DeprecatedReactTestUtils", "getGlobalThis", "globalThis", "self", "window", "global", "Error", "setIsReactActEnvironment", "isReactActEnvironment", "IS_REACT_ACT_ENVIRONMENT", "getIsReactActEnvironment", "withGlobalActEnvironment", "actImplementation", "callback", "previousActEnvironment", "callbackNeedsToBeAwaited", "actResult", "result", "then", "thenable", "resolve", "reject", "returnValue", "error", "fireEvent", "dtlFireEvent", "arguments", "Object", "keys", "for<PERSON>ach", "key", "mouseEnter", "mouseLeave", "mouseOver", "mouseOut", "pointerEnter", "pointerLeave", "pointerOver", "pointerOut", "select", "node", "init", "focus", "keyUp", "blur", "focusOut", "focusIn", "configForRTL", "reactStrictMode", "getConfig", "getConfigDTL", "configure", "newConfig", "configForDTL", "configureDTL", "jestFakeTimersAreEnabled", "jest", "setTimeout", "_isMockFunction", "prototype", "hasOwnProperty", "call", "unstable_advanceTimersWrapper", "cb", "asyncWrapper", "setReactActEnvironment", "Promise", "advanceTimersByTime", "eventWrapper", "mountedContainers", "Set", "mountedRootEntries", "strictModeIfNeeded", "innerElement", "createElement", "StrictMode", "wrapUiIfNeeded", "wrapperComponent", "createConcurrentRoot", "container", "_ref", "hydrate", "onCaughtError", "onRecoverableError", "ui", "wrapper", "WrapperComponent", "root", "ReactDOMClient", "hydrateRoot", "createRoot", "render", "element", "unmount", "createLegacyRoot", "ReactDOM", "unmountComponentAtNode", "renderRoot", "_ref2", "baseElement", "queries", "debug", "el", "max<PERSON><PERSON><PERSON>", "options", "Array", "isArray", "e", "console", "log", "prettyDOM", "rerender", "rerenderUi", "asFragment", "document", "createRange", "createContextualFragment", "innerHTML", "template", "content", "getQueriesForElement", "_temp", "legacyRoot", "onUncaughtError", "undefined", "captureStackTrace", "body", "append<PERSON><PERSON><PERSON>", "has", "createRootImpl", "push", "add", "rootEntry", "cleanup", "_ref3", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "length", "clear", "renderHook", "renderCallback", "initialProps", "renderOptions", "createRef", "TestComponent", "_ref4", "renderCallbackProps", "pendingResult", "useEffect", "current", "baseRerender", "rerenderCallbackProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGA,MAAMA,QAAQ,GACZ,OAAOC,gBAAK,CAACC,GAAG,KAAK,UAAU,GAAGD,gBAAK,CAACC,GAAG,GAAGC,mCAAwB,CAACD,GAAG,CAAA;EAE5E,SAASE,aAAaA,GAAG;EACvB;EACA,EAAA,IAAI,OAAOC,UAAU,KAAK,WAAW,EAAE;EACrC,IAAA,OAAOA,UAAU,CAAA;EACnB,GAAA;EACA;EACA,EAAA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC/B,IAAA,OAAOA,IAAI,CAAA;EACb,GAAA;EACA;EACA,EAAA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjC,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;EACA;EACA,EAAA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjC,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;EACA;EACA,EAAA,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAA;EACnD,CAAA;EAEA,SAASC,wBAAwBA,CAACC,qBAAqB,EAAE;EACvDP,EAAAA,aAAa,EAAE,CAACQ,wBAAwB,GAAGD,qBAAqB,CAAA;EAClE,CAAA;EAEA,SAASE,wBAAwBA,GAAG;EAClC,EAAA,OAAOT,aAAa,EAAE,CAACQ,wBAAwB,CAAA;EACjD,CAAA;EAEA,SAASE,wBAAwBA,CAACC,iBAAiB,EAAE;EACnD,EAAA,OAAOC,QAAQ,IAAI;EACjB,IAAA,MAAMC,sBAAsB,GAAGJ,wBAAwB,EAAE,CAAA;MACzDH,wBAAwB,CAAC,IAAI,CAAC,CAAA;MAC9B,IAAI;EACF;QACA,IAAIQ,wBAAwB,GAAG,KAAK,CAAA;EACpC,MAAA,MAAMC,SAAS,GAAGJ,iBAAiB,CAAC,MAAM;EACxC,QAAA,MAAMK,MAAM,GAAGJ,QAAQ,EAAE,CAAA;EACzB,QAAA,IACEI,MAAM,KAAK,IAAI,IACf,OAAOA,MAAM,KAAK,QAAQ,IAC1B,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EACjC;EACAH,UAAAA,wBAAwB,GAAG,IAAI,CAAA;EACjC,SAAA;EACA,QAAA,OAAOE,MAAM,CAAA;EACf,OAAC,CAAC,CAAA;EACF,MAAA,IAAIF,wBAAwB,EAAE;UAC5B,MAAMI,QAAQ,GAAGH,SAAS,CAAA;UAC1B,OAAO;EACLE,UAAAA,IAAI,EAAEA,CAACE,OAAO,EAAEC,MAAM,KAAK;EACzBF,YAAAA,QAAQ,CAACD,IAAI,CACXI,WAAW,IAAI;gBACbf,wBAAwB,CAACO,sBAAsB,CAAC,CAAA;gBAChDM,OAAO,CAACE,WAAW,CAAC,CAAA;eACrB,EACDC,KAAK,IAAI;gBACPhB,wBAAwB,CAACO,sBAAsB,CAAC,CAAA;gBAChDO,MAAM,CAACE,KAAK,CAAC,CAAA;EACf,aACF,CAAC,CAAA;EACH,WAAA;WACD,CAAA;EACH,OAAC,MAAM;UACLhB,wBAAwB,CAACO,sBAAsB,CAAC,CAAA;EAChD,QAAA,OAAOE,SAAS,CAAA;EAClB,OAAA;OACD,CAAC,OAAOO,KAAK,EAAE;EACd;EACA;QACAhB,wBAAwB,CAACO,sBAAsB,CAAC,CAAA;EAChD,MAAA,MAAMS,KAAK,CAAA;EACb,KAAA;KACD,CAAA;EACH,CAAA;AAEA,QAAMxB,GAAG,GAAGY,wBAAwB,CAACd,QAAQ,EAAC;;EAQ9C;;ECxFA;EACA;EACA;EACA;AACM2B,QAAAA,SAAS,GAAG,YAAA;EAAA,EAAA,OAAaC,aAAY,CAAC,GAAAC,SAAO,CAAC,CAAA;EAAA,EAAA;EAEpDC,MAAM,CAACC,IAAI,CAACH,aAAY,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACvCN,SAAS,CAACM,GAAG,CAAC,GAAG,YAAA;EAAA,IAAA,OAAaL,aAAY,CAACK,GAAG,CAAC,CAAC,GAAAJ,SAAO,CAAC,CAAA;EAAA,GAAA,CAAA;EAC1D,CAAC,CAAC,CAAA;;EAEF;EACA;EACA;EACA,MAAMK,UAAU,GAAGP,SAAS,CAACO,UAAU,CAAA;EACvC,MAAMC,UAAU,GAAGR,SAAS,CAACQ,UAAU,CAAA;EACvCR,SAAS,CAACO,UAAU,GAAG,YAAa;IAClCA,UAAU,CAAC,GAAAL,SAAO,CAAC,CAAA;EACnB,EAAA,OAAOF,SAAS,CAACS,SAAS,CAAC,GAAAP,SAAO,CAAC,CAAA;EACrC,CAAC,CAAA;EACDF,SAAS,CAACQ,UAAU,GAAG,YAAa;IAClCA,UAAU,CAAC,GAAAN,SAAO,CAAC,CAAA;EACnB,EAAA,OAAOF,SAAS,CAACU,QAAQ,CAAC,GAAAR,SAAO,CAAC,CAAA;EACpC,CAAC,CAAA;EAED,MAAMS,YAAY,GAAGX,SAAS,CAACW,YAAY,CAAA;EAC3C,MAAMC,YAAY,GAAGZ,SAAS,CAACY,YAAY,CAAA;EAC3CZ,SAAS,CAACW,YAAY,GAAG,YAAa;IACpCA,YAAY,CAAC,GAAAT,SAAO,CAAC,CAAA;EACrB,EAAA,OAAOF,SAAS,CAACa,WAAW,CAAC,GAAAX,SAAO,CAAC,CAAA;EACvC,CAAC,CAAA;EACDF,SAAS,CAACY,YAAY,GAAG,YAAa;IACpCA,YAAY,CAAC,GAAAV,SAAO,CAAC,CAAA;EACrB,EAAA,OAAOF,SAAS,CAACc,UAAU,CAAC,GAAAZ,SAAO,CAAC,CAAA;EACtC,CAAC,CAAA;EAED,MAAMa,MAAM,GAAGf,SAAS,CAACe,MAAM,CAAA;EAC/Bf,SAAS,CAACe,MAAM,GAAG,CAACC,IAAI,EAAEC,IAAI,KAAK;EACjCF,EAAAA,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,CAAA;EAClB;IACAD,IAAI,CAACE,KAAK,EAAE,CAAA;;EAEZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAlB,EAAAA,SAAS,CAACmB,KAAK,CAACH,IAAI,EAAEC,IAAI,CAAC,CAAA;EAC7B,CAAC,CAAA;;EAED;EACA;EACA;EACA,MAAMG,IAAI,GAAGpB,SAAS,CAACoB,IAAI,CAAA;EAC3B,MAAMF,KAAK,GAAGlB,SAAS,CAACkB,KAAK,CAAA;EAC7BlB,SAAS,CAACoB,IAAI,GAAG,YAAa;EAC5BpB,EAAAA,SAAS,CAACqB,QAAQ,CAAC,GAAAnB,SAAO,CAAC,CAAA;EAC3B,EAAA,OAAOkB,IAAI,CAAC,GAAAlB,SAAO,CAAC,CAAA;EACtB,CAAC,CAAA;EACDF,SAAS,CAACkB,KAAK,GAAG,YAAa;EAC7BlB,EAAAA,SAAS,CAACsB,OAAO,CAAC,GAAApB,SAAO,CAAC,CAAA;EAC1B,EAAA,OAAOgB,KAAK,CAAC,GAAAhB,SAAO,CAAC,CAAA;EACvB,CAAC;;EC7DD,IAAIqB,YAAY,GAAG;EACjBC,EAAAA,eAAe,EAAE,KAAA;EACnB,CAAC,CAAA;EAED,SAASC,SAASA,GAAG;IACnB,OAAO;MACL,GAAGC,aAAY,EAAE;MACjB,GAAGH,YAAAA;KACJ,CAAA;EACH,CAAA;EAEA,SAASI,SAASA,CAACC,SAAS,EAAE;EAC5B,EAAA,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;EACnC;EACA;EACAA,IAAAA,SAAS,GAAGA,SAAS,CAACH,SAAS,EAAE,CAAC,CAAA;EACpC,GAAA;IAEA,MAAM;MAACD,eAAe;MAAE,GAAGK,YAAAA;EAAY,GAAC,GAAGD,SAAS,CAAA;IAEpDE,aAAY,CAACD,YAAY,CAAC,CAAA;EAE1BN,EAAAA,YAAY,GAAG;EACb,IAAA,GAAGA,YAAY;EACfC,IAAAA,eAAAA;KACD,CAAA;EACH;;EChBA,SAASO,wBAAwBA,GAAG;EAClC;IACA,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,EAAE;EAChD,IAAA;EACE;QACAC,UAAU,CAACC,eAAe,KAAK,IAAI;EAAI;EACvC;QACA/B,MAAM,CAACgC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,UAAU,EAAE,OAAO,CAAA;EAAC,MAAA;EAE7D,GAAC;;EAED,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;AAEAH,eAAY,CAAC;IACXQ,6BAA6B,EAAEC,EAAE,IAAI;MACnC,OAAOhE,GAAG,CAACgE,EAAE,CAAC,CAAA;KACf;EACD;EACA;EACA;IACAC,YAAY,EAAE,MAAMD,EAAE,IAAI;EACxB,IAAA,MAAMjD,sBAAsB,GAAGJ,wBAAwB,EAAE,CAAA;MACzDuD,wBAAsB,CAAC,KAAK,CAAC,CAAA;MAC7B,IAAI;EACF,MAAA,MAAMhD,MAAM,GAAG,MAAM8C,EAAE,EAAE,CAAA;EACzB;EACA;EACA;EACA,MAAA,MAAM,IAAIG,OAAO,CAAC9C,OAAO,IAAI;EAC3BqC,QAAAA,UAAU,CAAC,MAAM;EACfrC,UAAAA,OAAO,EAAE,CAAA;WACV,EAAE,CAAC,CAAC,CAAA;UAEL,IAAImC,wBAAwB,EAAE,EAAE;EAC9BC,UAAAA,IAAI,CAACW,mBAAmB,CAAC,CAAC,CAAC,CAAA;EAC7B,SAAA;EACF,OAAC,CAAC,CAAA;EAEF,MAAA,OAAOlD,MAAM,CAAA;EACf,KAAC,SAAS;QACRgD,wBAAsB,CAACnD,sBAAsB,CAAC,CAAA;EAChD,KAAA;KACD;IACDsD,YAAY,EAAEL,EAAE,IAAI;EAClB,IAAA,IAAI9C,MAAM,CAAA;EACVlB,IAAAA,GAAG,CAAC,MAAM;QACRkB,MAAM,GAAG8C,EAAE,EAAE,CAAA;EACf,KAAC,CAAC,CAAA;EACF,IAAA,OAAO9C,MAAM,CAAA;EACf,GAAA;EACF,CAAC,CAAC,CAAA;;EAEF;EACA;EACA;EACA;EACA;EACA,MAAMoD,iBAAiB,GAAG,IAAIC,GAAG,EAAE,CAAA;EACnC;EACA;EACA;EACA,MAAMC,kBAAkB,GAAG,EAAE,CAAA;EAE7B,SAASC,kBAAkBA,CAACC,YAAY,EAAEzB,eAAe,EAAE;IACzD,OAAOA,eAAe,IAAIC,SAAS,EAAE,CAACD,eAAe,gBACjDlD,gBAAK,CAAC4E,aAAa,CAAC5E,gBAAK,CAAC6E,UAAU,EAAE,IAAI,EAAEF,YAAY,CAAC,GACzDA,YAAY,CAAA;EAClB,CAAA;EAEA,SAASG,cAAcA,CAACH,YAAY,EAAEI,gBAAgB,EAAE;EACtD,EAAA,OAAOA,gBAAgB,gBACnB/E,gBAAK,CAAC4E,aAAa,CAACG,gBAAgB,EAAE,IAAI,EAAEJ,YAAY,CAAC,GACzDA,YAAY,CAAA;EAClB,CAAA;EAEA,SAASK,oBAAoBA,CAC3BC,SAAS,EAAAC,IAAA,EAST;IAAA,IARA;MACEC,OAAO;MACPC,aAAa;MACbC,kBAAkB;MAClBC,EAAE;EACFC,IAAAA,OAAO,EAAEC,gBAAgB;EACzBtC,IAAAA,eAAAA;EACF,GAAC,GAAAgC,IAAA,CAAA;EAED,EAAA,IAAIO,IAAI,CAAA;EACR,EAAA,IAAIN,OAAO,EAAE;EACXlF,IAAAA,GAAG,CAAC,MAAM;EACRwF,MAAAA,IAAI,GAAGC,yBAAc,CAACC,WAAW,CAC/BV,SAAS,EACTP,kBAAkB,CAChBI,cAAc,CAACQ,EAAE,EAAEE,gBAAgB,CAAC,EACpCtC,eACF,CAAC,EACD;UAACkC,aAAa;EAAEC,QAAAA,kBAAAA;EAAkB,OACpC,CAAC,CAAA;EACH,KAAC,CAAC,CAAA;EACJ,GAAC,MAAM;EACLI,IAAAA,IAAI,GAAGC,yBAAc,CAACE,UAAU,CAACX,SAAS,EAAE;QAC1CG,aAAa;EACbC,MAAAA,kBAAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAA;IAEA,OAAO;EACLF,IAAAA,OAAOA,GAAG;EACR;QACA,IAAI,CAACA,OAAO,EAAE;EACZ,QAAA,MAAM,IAAI3E,KAAK,CACb,yFACF,CAAC,CAAA;EACH,OAAA;EACA;OACD;MACDqF,MAAMA,CAACC,OAAO,EAAE;EACdL,MAAAA,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,CAAA;OACrB;EACDC,IAAAA,OAAOA,GAAG;QACRN,IAAI,CAACM,OAAO,EAAE,CAAA;EAChB,KAAA;KACD,CAAA;EACH,CAAA;EAEA,SAASC,gBAAgBA,CAACf,SAAS,EAAE;IACnC,OAAO;MACLE,OAAOA,CAACW,OAAO,EAAE;EACfG,MAAAA,4BAAQ,CAACd,OAAO,CAACW,OAAO,EAAEb,SAAS,CAAC,CAAA;OACrC;MACDY,MAAMA,CAACC,OAAO,EAAE;EACdG,MAAAA,4BAAQ,CAACJ,MAAM,CAACC,OAAO,EAAEb,SAAS,CAAC,CAAA;OACpC;EACDc,IAAAA,OAAOA,GAAG;EACRE,MAAAA,4BAAQ,CAACC,sBAAsB,CAACjB,SAAS,CAAC,CAAA;EAC5C,KAAA;KACD,CAAA;EACH,CAAA;EAEA,SAASkB,UAAUA,CACjBb,EAAE,EAAAc,KAAA,EAUF;IAAA,IATA;MACEC,WAAW;MACXpB,SAAS;MACTE,OAAO;MACPmB,OAAO;MACPb,IAAI;EACJF,IAAAA,OAAO,EAAEC,gBAAgB;EACzBtC,IAAAA,eAAAA;EACF,GAAC,GAAAkD,KAAA,CAAA;EAEDnG,EAAAA,GAAG,CAAC,MAAM;EACR,IAAA,IAAIkF,OAAO,EAAE;EACXM,MAAAA,IAAI,CAACN,OAAO,CACVT,kBAAkB,CAChBI,cAAc,CAACQ,EAAE,EAAEE,gBAAgB,CAAC,EACpCtC,eACF,CAAC,EACD+B,SACF,CAAC,CAAA;EACH,KAAC,MAAM;EACLQ,MAAAA,IAAI,CAACI,MAAM,CACTnB,kBAAkB,CAChBI,cAAc,CAACQ,EAAE,EAAEE,gBAAgB,CAAC,EACpCtC,eACF,CAAC,EACD+B,SACF,CAAC,CAAA;EACH,KAAA;EACF,GAAC,CAAC,CAAA;IAEF,OAAO;MACLA,SAAS;MACToB,WAAW;EACXE,IAAAA,KAAK,EAAE,UAACC,EAAE,EAAgBC,SAAS,EAAEC,OAAO,EAAA;EAAA,MAAA,IAApCF,EAAE,KAAA,KAAA,CAAA,EAAA;EAAFA,QAAAA,EAAE,GAAGH,WAAW,CAAA;EAAA,OAAA;EAAA,MAAA,OACtBM,KAAK,CAACC,OAAO,CAACJ,EAAE,CAAC;EACb;EACAA,MAAAA,EAAE,CAACzE,OAAO,CAAC8E,CAAC,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAS,CAACH,CAAC,EAAEJ,SAAS,EAAEC,OAAO,CAAC,CAAC,CAAC;EAC9D;QACAI,OAAO,CAACC,GAAG,CAACC,aAAS,CAACR,EAAE,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAAC,CAAA;EAAA,KAAA;MACpDX,OAAO,EAAEA,MAAM;EACb9F,MAAAA,GAAG,CAAC,MAAM;UACRwF,IAAI,CAACM,OAAO,EAAE,CAAA;EAChB,OAAC,CAAC,CAAA;OACH;MACDkB,QAAQ,EAAEC,UAAU,IAAI;QACtBf,UAAU,CAACe,UAAU,EAAE;UACrBjC,SAAS;UACToB,WAAW;UACXZ,IAAI;EACJF,QAAAA,OAAO,EAAEC,gBAAgB;EACzBtC,QAAAA,eAAAA;EACF,OAAC,CAAC,CAAA;EACF;EACA;OACD;MACDiE,UAAU,EAAEA,MAAM;EAChB;EACA,MAAA,IAAI,OAAOC,QAAQ,CAACC,WAAW,KAAK,UAAU,EAAE;UAC9C,OAAOD,QAAQ,CACZC,WAAW,EAAE,CACbC,wBAAwB,CAACrC,SAAS,CAACsC,SAAS,CAAC,CAAA;EAClD,OAAC,MAAM;EACL,QAAA,MAAMC,QAAQ,GAAGJ,QAAQ,CAACxC,aAAa,CAAC,UAAU,CAAC,CAAA;EACnD4C,QAAAA,QAAQ,CAACD,SAAS,GAAGtC,SAAS,CAACsC,SAAS,CAAA;UACxC,OAAOC,QAAQ,CAACC,OAAO,CAAA;EACzB,OAAA;OACD;EACD,IAAA,GAAGC,wBAAoB,CAACrB,WAAW,EAAEC,OAAO,CAAA;KAC7C,CAAA;EACH,CAAA;EAEA,SAAST,MAAMA,CACbP,EAAE,EAAAqC,KAAA,EAaF;IAAA,IAZA;MACE1C,SAAS;EACToB,IAAAA,WAAW,GAAGpB,SAAS;EACvB2C,IAAAA,UAAU,GAAG,KAAK;MAClBxC,aAAa;MACbyC,eAAe;MACfxC,kBAAkB;MAClBiB,OAAO;EACPnB,IAAAA,OAAO,GAAG,KAAK;MACfI,OAAO;EACPrC,IAAAA,eAAAA;EACF,GAAC,GAAAyE,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;IAEN,IAAIE,eAAe,KAAKC,SAAS,EAAE;EACjC,IAAA,MAAM,IAAItH,KAAK,CACb,4FACF,CAAC,CAAA;EACH,GAAA;IACA,IAAIoH,UAAU,IAAI,OAAO3B,4BAAQ,CAACJ,MAAM,KAAK,UAAU,EAAE;MACvD,MAAMpE,KAAK,GAAG,IAAIjB,KAAK,CACrB,gEAAgE,GAC9D,mEAAmE,GACnE,gIACJ,CAAC,CAAA;EACDA,IAAAA,KAAK,CAACuH,iBAAiB,CAACtG,KAAK,EAAEoE,MAAM,CAAC,CAAA;EACtC,IAAA,MAAMpE,KAAK,CAAA;EACb,GAAA;IAEA,IAAI,CAAC4E,WAAW,EAAE;EAChB;EACA;MACAA,WAAW,GAAGe,QAAQ,CAACY,IAAI,CAAA;EAC7B,GAAA;IACA,IAAI,CAAC/C,SAAS,EAAE;MACdA,SAAS,GAAGoB,WAAW,CAAC4B,WAAW,CAACb,QAAQ,CAACxC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAA;EACpE,GAAA;EAEA,EAAA,IAAIa,IAAI,CAAA;EACR;EACA,EAAA,IAAI,CAAClB,iBAAiB,CAAC2D,GAAG,CAACjD,SAAS,CAAC,EAAE;EACrC,IAAA,MAAMkD,cAAc,GAAGP,UAAU,GAAG5B,gBAAgB,GAAGhB,oBAAoB,CAAA;EAC3ES,IAAAA,IAAI,GAAG0C,cAAc,CAAClD,SAAS,EAAE;QAC/BE,OAAO;QACPC,aAAa;QACbC,kBAAkB;QAClBC,EAAE;QACFC,OAAO;EACPrC,MAAAA,eAAAA;EACF,KAAC,CAAC,CAAA;MAEFuB,kBAAkB,CAAC2D,IAAI,CAAC;QAACnD,SAAS;EAAEQ,MAAAA,IAAAA;EAAI,KAAC,CAAC,CAAA;EAC1C;EACA;EACA;EACAlB,IAAAA,iBAAiB,CAAC8D,GAAG,CAACpD,SAAS,CAAC,CAAA;EAClC,GAAC,MAAM;EACLR,IAAAA,kBAAkB,CAAC1C,OAAO,CAACuG,SAAS,IAAI;EACtC;EACA;EACA;EACA,MAAA,IAAIA,SAAS,CAACrD,SAAS,KAAKA,SAAS,EAAE;UACrCQ,IAAI,GAAG6C,SAAS,CAAC7C,IAAI,CAAA;EACvB,OAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAA;IAEA,OAAOU,UAAU,CAACb,EAAE,EAAE;MACpBL,SAAS;MACToB,WAAW;MACXC,OAAO;MACPnB,OAAO;MACPI,OAAO;MACPE,IAAI;EACJvC,IAAAA,eAAAA;EACF,GAAC,CAAC,CAAA;EACJ,CAAA;EAEA,SAASqF,OAAOA,GAAG;EACjB9D,EAAAA,kBAAkB,CAAC1C,OAAO,CAACyG,KAAA,IAAuB;MAAA,IAAtB;QAAC/C,IAAI;EAAER,MAAAA,SAAAA;EAAS,KAAC,GAAAuD,KAAA,CAAA;EAC3CvI,IAAAA,GAAG,CAAC,MAAM;QACRwF,IAAI,CAACM,OAAO,EAAE,CAAA;EAChB,KAAC,CAAC,CAAA;EACF,IAAA,IAAId,SAAS,CAACwD,UAAU,KAAKrB,QAAQ,CAACY,IAAI,EAAE;EAC1CZ,MAAAA,QAAQ,CAACY,IAAI,CAACU,WAAW,CAACzD,SAAS,CAAC,CAAA;EACtC,KAAA;EACF,GAAC,CAAC,CAAA;IACFR,kBAAkB,CAACkE,MAAM,GAAG,CAAC,CAAA;IAC7BpE,iBAAiB,CAACqE,KAAK,EAAE,CAAA;EAC3B,CAAA;EAEA,SAASC,UAAUA,CAACC,cAAc,EAAEpC,OAAO,EAAO;EAAA,EAAA,IAAdA,OAAO,KAAA,KAAA,CAAA,EAAA;MAAPA,OAAO,GAAG,EAAE,CAAA;EAAA,GAAA;IAC9C,MAAM;MAACqC,YAAY;MAAE,GAAGC,aAAAA;EAAa,GAAC,GAAGtC,OAAO,CAAA;IAEhD,IAAIsC,aAAa,CAACpB,UAAU,IAAI,OAAO3B,4BAAQ,CAACJ,MAAM,KAAK,UAAU,EAAE;MACrE,MAAMpE,KAAK,GAAG,IAAIjB,KAAK,CACrB,gEAAgE,GAC9D,mEAAmE,GACnE,gIACJ,CAAC,CAAA;EACDA,IAAAA,KAAK,CAACuH,iBAAiB,CAACtG,KAAK,EAAEoH,UAAU,CAAC,CAAA;EAC1C,IAAA,MAAMpH,KAAK,CAAA;EACb,GAAA;EAEA,EAAA,MAAMN,MAAM,gBAAGnB,gBAAK,CAACiJ,SAAS,EAAE,CAAA;IAEhC,SAASC,aAAaA,CAAAC,KAAA,EAAwB;MAAA,IAAvB;EAACC,MAAAA,mBAAAA;EAAmB,KAAC,GAAAD,KAAA,CAAA;EAC1C,IAAA,MAAME,aAAa,GAAGP,cAAc,CAACM,mBAAmB,CAAC,CAAA;MAEzDpJ,gBAAK,CAACsJ,SAAS,CAAC,MAAM;QACpBnI,MAAM,CAACoI,OAAO,GAAGF,aAAa,CAAA;EAChC,KAAC,CAAC,CAAA;EAEF,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAM;EAACpC,IAAAA,QAAQ,EAAEuC,YAAY;EAAEzD,IAAAA,OAAAA;EAAO,GAAC,GAAGF,MAAM,cAC9C7F,gBAAA,CAAA4E,aAAA,CAACsE,aAAa,EAAA;EAACE,IAAAA,mBAAmB,EAAEL,YAAAA;KAAe,CAAC,EACpDC,aACF,CAAC,CAAA;IAED,SAAS/B,QAAQA,CAACwC,qBAAqB,EAAE;EACvC,IAAA,OAAOD,YAAY,cACjBxJ,gBAAA,CAAA4E,aAAA,CAACsE,aAAa,EAAA;EAACE,MAAAA,mBAAmB,EAAEK,qBAAAA;EAAsB,KAAE,CAC9D,CAAC,CAAA;EACH,GAAA;IAEA,OAAO;MAACtI,MAAM;MAAE8F,QAAQ;EAAElB,IAAAA,OAAAA;KAAQ,CAAA;EACpC,CAAA;;EAMA;;;;;;;;;;;;;;;;;;;;;;"}