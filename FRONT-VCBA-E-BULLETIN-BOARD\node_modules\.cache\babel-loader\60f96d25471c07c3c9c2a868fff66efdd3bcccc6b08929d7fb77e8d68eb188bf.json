{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"9\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dyftd\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"21\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"oup4p8\"\n}]];\nconst GitCommitHorizontal = createLucideIcon(\"git-commit-horizontal\", __iconNode);\nexport { __iconNode, GitCommitHorizontal as default };\n//# sourceMappingURL=git-commit-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}