import React, { useState, useEffect } from 'react';
import { Archive as ArchiveIcon, FileText, Calendar, Users, RotateCcw, Trash2, Search, Filter } from 'lucide-react';
import { archiveService, ArchiveStatistics } from '../../services/archiveService';
import ArchivedAnnouncements from '../../components/admin/archive/ArchivedAnnouncements';
import ArchivedCalendarEvents from '../../components/admin/archive/ArchivedCalendarEvents';
import ArchivedStudents from '../../components/admin/archive/ArchivedStudents';

type ArchiveTab = 'announcements' | 'calendar' | 'students';

const Archive: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ArchiveTab>('announcements');
  const [statistics, setStatistics] = useState<ArchiveStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await archiveService.getArchiveStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        setError('Failed to load archive statistics');
        // Set default statistics to prevent UI errors
        setStatistics({
          announcements: 0,
          calendar_events: 0,
          students: 0,
          total: 0
        });
      }
    } catch (error: any) {
      console.error('Error loading archive statistics:', error);
      console.error('Error details:', error.response?.data || error);
      setError(error.message || 'Failed to load archive statistics');
      // Set default statistics to prevent UI errors
      setStatistics({
        announcements: 0,
        calendar_events: 0,
        students: 0,
        total: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    {
      id: 'announcements' as ArchiveTab,
      label: 'Announcements',
      icon: FileText,
      count: statistics?.announcements || 0,
      color: '#3b82f6'
    },
    {
      id: 'calendar' as ArchiveTab,
      label: 'Calendar Events',
      icon: Calendar,
      count: statistics?.calendar_events || 0,
      color: '#10b981'
    },
    {
      id: 'students' as ArchiveTab,
      label: 'Students',
      icon: Users,
      count: statistics?.students || 0,
      color: '#f59e0b'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'announcements':
        return <ArchivedAnnouncements onRestoreSuccess={loadStatistics} />;
      case 'calendar':
        return <ArchivedCalendarEvents onRestoreSuccess={loadStatistics} />;
      case 'students':
        return <ArchivedStudents onRestoreSuccess={loadStatistics} />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px',
        color: '#6b7280'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '3px solid #e5e7eb',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }} />
          Loading archive data...
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          width: '48px',
          height: '48px',
          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
          borderRadius: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <ArchiveIcon size={24} color="white" />
        </div>
        <div>
          <h1 style={{
            margin: 0,
            fontSize: '2rem',
            fontWeight: '700',
            color: '#1f2937'
          }}>
            Archive
          </h1>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '1rem'
          }}>
            View and manage archived records
          </p>
        </div>
      </div>

      {error && (
        <div style={{
          background: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          padding: '1rem',
          marginBottom: '1.5rem',
          color: '#dc2626'
        }}>
          {error}
        </div>
      )}

      {/* Statistics Cards */}
      {statistics && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          {tabs.map((tab) => (
            <div
              key={tab.id}
              style={{
                background: 'white',
                borderRadius: '12px',
                padding: '1.5rem',
                border: '1px solid #e5e7eb',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',
                borderColor: activeTab === tab.id ? tab.color : '#e5e7eb'
              }}
              onClick={() => setActiveTab(tab.id)}
              onMouseEnter={(e) => {
                if (activeTab !== tab.id) {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab.id) {
                  e.currentTarget.style.transform = 'none';
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                }
              }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                marginBottom: '0.5rem'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  background: `${tab.color}20`,
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <tab.icon size={18} color={tab.color} />
                </div>
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#6b7280'
                }}>
                  {tab.label}
                </span>
              </div>
              <div style={{
                fontSize: '2rem',
                fontWeight: '700',
                color: tab.color
              }}>
                {tab.count}
              </div>
            </div>
          ))}
          
          {/* Total Card */}
          <div style={{
            background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',
            borderRadius: '12px',
            padding: '1.5rem',
            color: 'white'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem',
              marginBottom: '0.5rem'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <ArchiveIcon size={18} color="white" />
              </div>
              <span style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                opacity: 0.9
              }}>
                Total Archived
              </span>
            </div>
            <div style={{
              fontSize: '2rem',
              fontWeight: '700'
            }}>
              {statistics.total}
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        borderBottom: '2px solid #e5e7eb',
        marginBottom: '2rem'
      }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '1rem 1.5rem',
              background: 'none',
              border: 'none',
              borderBottom: `3px solid ${activeTab === tab.id ? tab.color : 'transparent'}`,
              color: activeTab === tab.id ? tab.color : '#6b7280',
              fontWeight: activeTab === tab.id ? '600' : '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            <tab.icon size={18} />
            {tab.label}
            {tab.count > 0 && (
              <span style={{
                background: activeTab === tab.id ? tab.color : '#e5e7eb',
                color: activeTab === tab.id ? 'white' : '#6b7280',
                fontSize: '0.75rem',
                fontWeight: '600',
                padding: '0.25rem 0.5rem',
                borderRadius: '12px',
                minWidth: '20px',
                textAlign: 'center'
              }}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div>
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Archive;
