{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"12\",\n  rx: \"2\",\n  key: \"4l4tp2\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"ypihtt\"\n}], [\"path\", {\n  d: \"M2 22h20\",\n  key: \"272qi7\"\n}]];\nconst AlignVerticalJustifyEnd = createLucideIcon(\"align-vertical-justify-end\", __iconNode);\nexport { __iconNode, AlignVerticalJustifyEnd as default };\n//# sourceMappingURL=align-vertical-justify-end.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}