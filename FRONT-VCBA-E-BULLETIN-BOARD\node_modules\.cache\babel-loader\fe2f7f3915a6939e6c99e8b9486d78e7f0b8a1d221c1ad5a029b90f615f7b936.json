{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"3\",\n  y: \"5\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"1defrl\"\n}], [\"path\", {\n  d: \"m3 17 2 2 4-4\",\n  key: \"1jhpwq\"\n}], [\"path\", {\n  d: \"M13 6h8\",\n  key: \"15sg57\"\n}], [\"path\", {\n  d: \"M13 12h8\",\n  key: \"h98zly\"\n}], [\"path\", {\n  d: \"M13 18h8\",\n  key: \"oe0vm4\"\n}]];\nconst ListTodo = createLucideIcon(\"list-todo\", __iconNode);\nexport { __iconNode, ListTodo as default };\n//# sourceMappingURL=list-todo.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}