{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  _s();\n  const [preview, setPreview] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback(file => {\n    setError(null);\n    setSuccess(null);\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to upload profile picture');\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard preview)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragOver(false);\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove confirmation\n  const handleRemoveConfirm = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n    setShowRemoveConfirm(false);\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasCurrentImage = Boolean(currentPicture);\n  const hasPreview = Boolean(preview);\n  const hasChanges = hasPreview && selectedFile;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasCurrentImage,\n    hasPreview,\n    hasChanges\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '100px',\n          height: '100px',\n          borderRadius: '50%',\n          overflow: 'hidden',\n          border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n          transition: 'border-color 0.2s ease',\n          cursor: 'pointer'\n        },\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n        },\n        children: [hasImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: displayImage,\n          alt: \"Profile\",\n          onLoad: () => console.log('✅ Image loaded successfully:', displayImage),\n          onError: e => console.error('❌ Image failed to load:', displayImage, e),\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: 'white',\n            fontWeight: '700',\n            fontSize: '2rem'\n          },\n          children: userInitials\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: isDragOver ? 1 : 0,\n            transition: 'opacity 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(Camera, {\n            size: 24,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(255, 255, 255, 0.8)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              border: '2px solid #e8f5e8',\n              borderTop: '2px solid #22c55e',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current2;\n              return (_fileInputRef$current2 = fileInputRef.current) === null || _fileInputRef$current2 === void 0 ? void 0 : _fileInputRef$current2.click();\n            },\n            disabled: isLoading,\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontWeight: '600',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), hasImage ? 'Change Photo' : 'Upload Photo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), hasImage && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRemove,\n            disabled: isLoading,\n            style: {\n              background: 'none',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              cursor: isLoading ? 'not-allowed' : 'pointer',\n              color: '#6b7280',\n              opacity: isLoading ? 0.6 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/jpeg,image/jpg,image/png,image/webp\",\n          onChange: handleInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          },\n          children: \"Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '1rem',\n        padding: '0.75rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#16a34a',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"8Tu3RyHS4UDLHKJowR6hJhNAexY=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "Camera", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ProfilePictureUpload", "currentPicture", "userInitials", "onUpload", "onRemove", "isLoading", "className", "_s", "preview", "setPreview", "selectedFile", "setSelectedFile", "isDragOver", "setIsDragOver", "error", "setError", "success", "setSuccess", "showRemoveConfirm", "setShowRemoveConfirm", "fileInputRef", "validateFile", "file", "maxSize", "allowedTypes", "includes", "type", "size", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "onerror", "readAsDataURL", "handleSave", "setTimeout", "err", "message", "handleCancel", "current", "value", "handleInputChange", "_e$target$files", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleRemoveConfirm", "displayImage", "hasCurrentImage", "Boolean", "hasPreview", "has<PERSON><PERSON><PERSON>", "console", "log", "children", "style", "display", "alignItems", "gap", "position", "width", "height", "borderRadius", "overflow", "border", "transition", "cursor", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "click", "hasImage", "src", "alt", "onLoad", "onError", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "justifyContent", "color", "fontWeight", "fontSize", "top", "left", "right", "bottom", "opacity", "borderTop", "animation", "flexDirection", "_fileInputRef$current2", "disabled", "padding", "handleRemove", "ref", "accept", "onChange", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, Camera, AlertCircle, CheckCircle, Save, RotateCcw, Trash2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentPicture?: string;\n  userInitials?: string;\n  onUpload: (file: File) => Promise<void>;\n  onRemove: () => Promise<void>;\n  isLoading?: boolean;\n  className?: string;\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentPicture,\n  userInitials = 'U',\n  onUpload,\n  onRemove,\n  isLoading = false,\n  className = ''\n}) => {\n  const [preview, setPreview] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // File validation\n  const validateFile = useCallback((file: File): string | null => {\n    const maxSize = 2 * 1024 * 1024; // 2MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Please select a valid image file (JPEG, PNG, or WebP)';\n    }\n\n    if (file.size > maxSize) {\n      return 'File size must be less than 2MB';\n    }\n\n    return null;\n  }, []);\n\n  // Handle file selection (preview only, don't upload yet)\n  const handleFileSelect = useCallback((file: File) => {\n    setError(null);\n    setSuccess(null);\n\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n      setSelectedFile(file);\n    };\n    reader.onerror = () => {\n      setError('Failed to read file');\n    };\n    reader.readAsDataURL(file);\n  }, [validateFile]);\n\n  // Handle save (actually upload the file)\n  const handleSave = useCallback(async () => {\n    if (!selectedFile) return;\n\n    try {\n      await onUpload(selectedFile);\n      setSuccess('Profile picture updated successfully!');\n      setSelectedFile(null);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to upload profile picture');\n    }\n  }, [selectedFile, onUpload]);\n\n  // Handle cancel (discard preview)\n  const handleCancel = useCallback(() => {\n    setPreview(null);\n    setSelectedFile(null);\n    setError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // Handle file input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  // Handle remove confirmation\n  const handleRemoveConfirm = async () => {\n    setError(null);\n    setSuccess(null);\n    setPreview(null);\n    setSelectedFile(null);\n    setShowRemoveConfirm(false);\n\n    try {\n      await onRemove();\n      setSuccess('Profile picture removed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err: any) {\n      setError(err.message || 'Failed to remove profile picture');\n    }\n  };\n\n  // Get display image and states\n  const displayImage = preview || currentPicture;\n  const hasCurrentImage = Boolean(currentPicture);\n  const hasPreview = Boolean(preview);\n  const hasChanges = hasPreview && selectedFile;\n\n  // Debug logging\n  console.log('🔍 ProfilePictureUpload - Props:', {\n    currentPicture,\n    preview,\n    displayImage,\n    hasCurrentImage,\n    hasPreview,\n    hasChanges\n  });\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n        {/* Profile Picture Display */}\n        <div\n          style={{\n            position: 'relative',\n            width: '100px',\n            height: '100px',\n            borderRadius: '50%',\n            overflow: 'hidden',\n            border: isDragOver ? '3px dashed #22c55e' : '3px solid #e8f5e8',\n            transition: 'border-color 0.2s ease',\n            cursor: 'pointer'\n          }}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n        >\n          {hasImage ? (\n            <img\n              src={displayImage}\n              alt=\"Profile\"\n              onLoad={() => console.log('✅ Image loaded successfully:', displayImage)}\n              onError={(e) => console.error('❌ Image failed to load:', displayImage, e)}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover'\n              }}\n            />\n          ) : (\n            <div\n              style={{\n                width: '100%',\n                height: '100%',\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: '700',\n                fontSize: '2rem'\n              }}\n            >\n              {userInitials}\n            </div>\n          )}\n\n          {/* Upload Overlay */}\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'rgba(0, 0, 0, 0.5)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              opacity: isDragOver ? 1 : 0,\n              transition: 'opacity 0.2s ease'\n            }}\n          >\n            <Camera size={24} color=\"white\" />\n          </div>\n\n          {isLoading && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'rgba(255, 255, 255, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            >\n              <div\n                style={{\n                  width: '24px',\n                  height: '24px',\n                  border: '2px solid #e8f5e8',\n                  borderTop: '2px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isLoading}\n              style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                fontWeight: '600',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                opacity: isLoading ? 0.6 : 1,\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {hasImage ? 'Change Photo' : 'Upload Photo'}\n            </button>\n\n            {hasImage && (\n              <button\n                onClick={handleRemove}\n                disabled={isLoading}\n                style={{\n                  background: 'none',\n                  border: '1px solid #e8f5e8',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  color: '#6b7280',\n                  opacity: isLoading ? 0.6 : 1,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                <X size={16} />\n                Remove\n              </button>\n            )}\n          </div>\n\n          {/* File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n            onChange={handleInputChange}\n            style={{ display: 'none' }}\n          />\n\n          {/* Help Text */}\n          <p style={{\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            margin: 0\n          }}>\n            Drag and drop or click to upload. Max 2MB. JPEG, PNG, WebP only.\n          </p>\n        </div>\n      </div>\n\n      {/* Messages */}\n      {error && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.75rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#16a34a',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* CSS for spinner animation */}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAiC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpG,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,cAAc;EACdC,YAAY,GAAG,GAAG;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAG7B,WAAW,CAAE8B,IAAU,IAAoB;IAC9D,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAE3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,uDAAuD;IAChE;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO,iCAAiC;IAC1C;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,gBAAgB,GAAGpC,WAAW,CAAE8B,IAAU,IAAK;IACnDP,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMY,eAAe,GAAGR,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIO,eAAe,EAAE;MACnBd,QAAQ,CAACc,eAAe,CAAC;MACzB;IACF;;IAEA;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBzB,UAAU,EAAAyB,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;MACtCzB,eAAe,CAACW,IAAI,CAAC;IACvB,CAAC;IACDQ,MAAM,CAACO,OAAO,GAAG,MAAM;MACrBtB,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC;IACDe,MAAM,CAACQ,aAAa,CAAChB,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkB,UAAU,GAAG/C,WAAW,CAAC,YAAY;IACzC,IAAI,CAACkB,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMP,QAAQ,CAACO,YAAY,CAAC;MAC5BO,UAAU,CAAC,uCAAuC,CAAC;MACnDN,eAAe,CAAC,IAAI,CAAC;MACrB6B,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChC,YAAY,EAAEP,QAAQ,CAAC,CAAC;;EAE5B;EACA,MAAMwC,YAAY,GAAGnD,WAAW,CAAC,MAAM;IACrCiB,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAIK,YAAY,CAACwB,OAAO,EAAE;MACxBxB,YAAY,CAACwB,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAIb,CAAsC,IAAK;IAAA,IAAAc,eAAA;IACpE,MAAMzB,IAAI,IAAAyB,eAAA,GAAGd,CAAC,CAACE,MAAM,CAACa,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIzB,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAIhB,CAAkB,IAAK;IAC7CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMsC,eAAe,GAAIlB,CAAkB,IAAK;IAC9CA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuC,UAAU,GAAInB,CAAkB,IAAK;IACzCA,CAAC,CAACiB,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMS,IAAI,GAAGW,CAAC,CAACoB,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC;IACpC,IAAI1B,IAAI,EAAE;MACRM,gBAAgB,CAACN,IAAI,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCvC,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAChBR,UAAU,CAAC,IAAI,CAAC;IAChBE,eAAe,CAAC,IAAI,CAAC;IACrBQ,oBAAoB,CAAC,KAAK,CAAC;IAE3B,IAAI;MACF,MAAMf,QAAQ,CAAC,CAAC;MAChBa,UAAU,CAAC,uCAAuC,CAAC;MACnDuB,UAAU,CAAC,MAAMvB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOwB,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMa,YAAY,GAAG/C,OAAO,IAAIP,cAAc;EAC9C,MAAMuD,eAAe,GAAGC,OAAO,CAACxD,cAAc,CAAC;EAC/C,MAAMyD,UAAU,GAAGD,OAAO,CAACjD,OAAO,CAAC;EACnC,MAAMmD,UAAU,GAAGD,UAAU,IAAIhD,YAAY;;EAE7C;EACAkD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;IAC9C5D,cAAc;IACdO,OAAO;IACP+C,YAAY;IACZC,eAAe;IACfE,UAAU;IACVC;EACF,CAAC,CAAC;EAEF,oBACE5D,OAAA;IAAKO,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAwD,QAAA,gBACpD/D,OAAA;MAAKgE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAEjE/D,OAAA;QACEgE,KAAK,EAAE;UACLI,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE5D,UAAU,GAAG,oBAAoB,GAAG,mBAAmB;UAC/D6D,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAE1B,cAAe;QAC3B2B,WAAW,EAAEzB,eAAgB;QAC7B0B,MAAM,EAAEzB,UAAW;QACnB0B,OAAO,EAAEA,CAAA;UAAA,IAAAC,qBAAA;UAAA,QAAAA,qBAAA,GAAM3D,YAAY,CAACwB,OAAO,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;QAAA,CAAC;QAAAlB,QAAA,GAE5CmB,QAAQ,gBACPlF,OAAA;UACEmF,GAAG,EAAE3B,YAAa;UAClB4B,GAAG,EAAC,SAAS;UACbC,MAAM,EAAEA,CAAA,KAAMxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,YAAY,CAAE;UACxE8B,OAAO,EAAGpD,CAAC,IAAK2B,OAAO,CAAC9C,KAAK,CAAC,yBAAyB,EAAEyC,YAAY,EAAEtB,CAAC,CAAE;UAC1E8B,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiB,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF3F,OAAA;UACEgE,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdsB,UAAU,EAAE,mDAAmD;YAC/D3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBC,KAAK,EAAE,OAAO;YACdC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAjC,QAAA,EAED5D;QAAY;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,eAGD3F,OAAA;UACEgE,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,oBAAoB;YAChC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE,QAAQ;YACxBQ,OAAO,EAAExF,UAAU,GAAG,CAAC,GAAG,CAAC;YAC3B6D,UAAU,EAAE;UACd,CAAE;UAAAX,QAAA,eAEF/D,OAAA,CAACJ,MAAM;YAACgC,IAAI,EAAE,EAAG;YAACkE,KAAK,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EAELrF,SAAS,iBACRN,OAAA;UACEgE,KAAK,EAAE;YACLI,QAAQ,EAAE,UAAU;YACpB6B,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTR,UAAU,EAAE,0BAA0B;YACtC3B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,cAAc,EAAE;UAClB,CAAE;UAAA9B,QAAA,eAEF/D,OAAA;YACEgE,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,MAAM,EAAE,mBAAmB;cAC3B6B,SAAS,EAAE,mBAAmB;cAC9B/B,YAAY,EAAE,KAAK;cACnBgC,SAAS,EAAE;YACb;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3F,OAAA;QAAKgE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEuC,aAAa,EAAE,QAAQ;UAAErC,GAAG,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACpE/D,OAAA;UAAKgE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAC3C/D,OAAA;YACE+E,OAAO,EAAEA,CAAA;cAAA,IAAA0B,sBAAA;cAAA,QAAAA,sBAAA,GAAMpF,YAAY,CAACwB,OAAO,cAAA4D,sBAAA,uBAApBA,sBAAA,CAAsBxB,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CyB,QAAQ,EAAEpG,SAAU;YACpB0D,KAAK,EAAE;cACL4B,UAAU,EAAE,mDAAmD;cAC/DE,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBZ,UAAU,EAAE,KAAK;cACjBpB,MAAM,EAAErE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C+F,OAAO,EAAE/F,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEF/D,OAAA,CAACN,MAAM;cAACkC,IAAI,EAAE;YAAG;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBT,QAAQ,GAAG,cAAc,GAAG,cAAc;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,EAERT,QAAQ,iBACPlF,OAAA;YACE+E,OAAO,EAAE6B,YAAa;YACtBF,QAAQ,EAAEpG,SAAU;YACpB0D,KAAK,EAAE;cACL4B,UAAU,EAAE,MAAM;cAClBnB,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBoC,OAAO,EAAE,gBAAgB;cACzBhC,MAAM,EAAErE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CwF,KAAK,EAAE,SAAS;cAChBO,OAAO,EAAE/F,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B2D,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAJ,QAAA,gBAEF/D,OAAA,CAACL,CAAC;cAACiC,IAAI,EAAE;YAAG;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3F,OAAA;UACE6G,GAAG,EAAExF,YAAa;UAClBM,IAAI,EAAC,MAAM;UACXmF,MAAM,EAAC,2CAA2C;UAClDC,QAAQ,EAAEhE,iBAAkB;UAC5BiB,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF3F,OAAA;UAAGgE,KAAK,EAAE;YACRgC,QAAQ,EAAE,UAAU;YACpBF,KAAK,EAAE,SAAS;YAChBkB,MAAM,EAAE;UACV,CAAE;UAAAjD,QAAA,EAAC;QAEH;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5E,KAAK,iBACJf,OAAA;MAAKgE,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBN,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACA/D,OAAA,CAACH,WAAW;QAAC+B,IAAI,EAAE;MAAG;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB5E,KAAK;IAAA;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA1E,OAAO,iBACNjB,OAAA;MAAKgE,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjBN,OAAO,EAAE,SAAS;QAClBf,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBuB,KAAK,EAAE,SAAS;QAChB7B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAJ,QAAA,gBACA/D,OAAA,CAACF,WAAW;QAAC8B,IAAI,EAAE;MAAG;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB1E,OAAO;IAAA;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD3F,OAAA;MAAA+D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnF,EAAA,CA7VIP,oBAAyD;AAAAiH,EAAA,GAAzDjH,oBAAyD;AA+V/D,eAAeA,oBAAoB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}