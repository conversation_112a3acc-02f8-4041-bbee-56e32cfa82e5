{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ProfilePictureUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, User, AlertCircle, Loader2 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfilePictureUpload = ({\n  currentImageUrl,\n  onImageSelect,\n  onImageRemove,\n  maxSize = 5 * 1024 * 1024,\n  // 5MB default\n  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  loading = false,\n  error = null,\n  size = 'medium'\n}) => {\n  _s();\n  const [preview, setPreview] = useState(currentImageUrl || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [localError, setLocalError] = useState(null);\n  const fileInputRef = useRef(null);\n  const dragCounter = useRef(0);\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: '80px',\n      avatar: '80px',\n      fontSize: '1.5rem'\n    },\n    medium: {\n      container: '120px',\n      avatar: '120px',\n      fontSize: '2rem'\n    },\n    large: {\n      container: '160px',\n      avatar: '160px',\n      fontSize: '2.5rem'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Validate file\n  const validateFile = useCallback(file => {\n    if (!acceptedFormats.includes(file.type)) {\n      return `Invalid file format. Accepted formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n    if (file.size > maxSize) {\n      const maxSizeMB = Math.round(maxSize / (1024 * 1024));\n      return `File size too large. Maximum size: ${maxSizeMB}MB`;\n    }\n    return null;\n  }, [acceptedFormats, maxSize]);\n\n  // Handle file selection\n  const handleFileSelect = useCallback(file => {\n    const validationError = validateFile(file);\n    if (validationError) {\n      setLocalError(validationError);\n      return;\n    }\n    setLocalError(null);\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      var _e$target;\n      setPreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n    };\n    reader.readAsDataURL(file);\n\n    // Call parent handler\n    onImageSelect(file);\n  }, [validateFile, onImageSelect]);\n\n  // Handle file input change\n  const handleFileChange = useCallback(e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  }, [handleFileSelect]);\n\n  // Handle drag events\n  const handleDragEnter = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setIsDragging(true);\n    }\n  }, []);\n  const handleDragLeave = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setIsDragging(false);\n    }\n  }, []);\n  const handleDragOver = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n  const handleDrop = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n    dragCounter.current = 0;\n    if (disabled || loading) return;\n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  }, [disabled, loading, handleFileSelect]);\n\n  // Handle remove image\n  const handleRemoveImage = useCallback(() => {\n    setPreview(null);\n    setLocalError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    onImageRemove === null || onImageRemove === void 0 ? void 0 : onImageRemove();\n  }, [onImageRemove]);\n\n  // Handle click to upload\n  const handleUploadClick = useCallback(() => {\n    var _fileInputRef$current;\n    if (disabled || loading) return;\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  }, [disabled, loading]);\n  const displayError = error || localError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `profile-picture-upload ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: config.container,\n          height: config.container,\n          borderRadius: '50%',\n          border: isDragging ? '3px dashed #3b82f6' : '2px solid #e5e7eb',\n          backgroundColor: isDragging ? '#eff6ff' : '#f9fafb',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: disabled || loading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.2s ease',\n          overflow: 'hidden'\n        },\n        onClick: handleUploadClick,\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: loading ? /*#__PURE__*/_jsxDEV(Loader2, {\n          size: 32,\n          className: \"animate-spin text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this) : preview ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: preview,\n            alt: \"Profile preview\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover',\n              borderRadius: '50%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), !disabled && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleRemoveImage();\n            },\n            style: {\n              position: 'absolute',\n              top: '8px',\n              right: '8px',\n              background: '#ef4444',\n              color: 'white',\n              border: 'none',\n              borderRadius: '50%',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              fontSize: '12px',\n              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(X, {\n              size: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            color: '#6b7280'\n          },\n          children: [isDragging ? /*#__PURE__*/_jsxDEV(Upload, {\n            size: 32,\n            color: \"#3b82f6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(User, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              marginTop: '0.5rem'\n            },\n            children: isDragging ? 'Drop image here' : 'Click or drag to upload'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Maximum file size: \", Math.round(maxSize / (1024 * 1024)), \"MB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Supported formats: \", acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), displayError && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          color: '#ef4444',\n          fontSize: '0.875rem',\n          backgroundColor: '#fef2f2',\n          padding: '0.75rem',\n          borderRadius: '8px',\n          border: '1px solid #fecaca'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), displayError]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: fileInputRef,\n        type: \"file\",\n        accept: acceptedFormats.join(','),\n        onChange: handleFileChange,\n        style: {\n          display: 'none'\n        },\n        disabled: disabled || loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePictureUpload, \"m7EdNt3YCjISZz5lJFOq8RCJOU0=\");\n_c = ProfilePictureUpload;\nexport default ProfilePictureUpload;\nvar _c;\n$RefreshReg$(_c, \"ProfilePictureUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "Upload", "X", "User", "AlertCircle", "Loader2", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfilePictureUpload", "currentImageUrl", "onImageSelect", "onImageRemove", "maxSize", "acceptedFormats", "className", "disabled", "loading", "error", "size", "_s", "preview", "setPreview", "isDragging", "setIsDragging", "localError", "setLocalError", "fileInputRef", "dragCounter", "sizeConfig", "small", "container", "avatar", "fontSize", "medium", "large", "config", "validateFile", "file", "includes", "type", "map", "f", "split", "toUpperCase", "join", "maxSizeMB", "Math", "round", "handleFileSelect", "validationError", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "readAsDataURL", "handleFileChange", "_e$target$files", "files", "handleDragEnter", "preventDefault", "stopPropagation", "current", "dataTransfer", "items", "length", "handleDragLeave", "handleDragOver", "handleDrop", "handleRemoveImage", "value", "handleUploadClick", "_fileInputRef$current", "click", "displayError", "children", "style", "display", "flexDirection", "alignItems", "gap", "position", "width", "height", "borderRadius", "border", "backgroundColor", "justifyContent", "cursor", "transition", "overflow", "onClick", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "objectFit", "top", "right", "background", "color", "boxShadow", "textAlign", "marginTop", "padding", "ref", "accept", "onChange", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ProfilePictureUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { Upload, X, User, AlertCircle, Loader2 } from 'lucide-react';\n\ninterface ProfilePictureUploadProps {\n  currentImageUrl?: string | null;\n  onImageSelect: (file: File) => void;\n  onImageRemove?: () => void;\n  maxSize?: number; // in bytes\n  acceptedFormats?: string[];\n  className?: string;\n  disabled?: boolean;\n  loading?: boolean;\n  error?: string | null;\n  size?: 'small' | 'medium' | 'large';\n}\n\nconst ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({\n  currentImageUrl,\n  onImageSelect,\n  onImageRemove,\n  maxSize = 5 * 1024 * 1024, // 5MB default\n  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  loading = false,\n  error = null,\n  size = 'medium'\n}) => {\n  const [preview, setPreview] = useState<string | null>(currentImageUrl || null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [localError, setLocalError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dragCounter = useRef(0);\n\n  // Size configurations\n  const sizeConfig = {\n    small: { container: '80px', avatar: '80px', fontSize: '1.5rem' },\n    medium: { container: '120px', avatar: '120px', fontSize: '2rem' },\n    large: { container: '160px', avatar: '160px', fontSize: '2.5rem' }\n  };\n\n  const config = sizeConfig[size];\n\n  // Validate file\n  const validateFile = useCallback((file: File): string | null => {\n    if (!acceptedFormats.includes(file.type)) {\n      return `Invalid file format. Accepted formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`;\n    }\n\n    if (file.size > maxSize) {\n      const maxSizeMB = Math.round(maxSize / (1024 * 1024));\n      return `File size too large. Maximum size: ${maxSizeMB}MB`;\n    }\n\n    return null;\n  }, [acceptedFormats, maxSize]);\n\n  // Handle file selection\n  const handleFileSelect = useCallback((file: File) => {\n    const validationError = validateFile(file);\n    if (validationError) {\n      setLocalError(validationError);\n      return;\n    }\n\n    setLocalError(null);\n    \n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string);\n    };\n    reader.readAsDataURL(file);\n\n    // Call parent handler\n    onImageSelect(file);\n  }, [validateFile, onImageSelect]);\n\n  // Handle file input change\n  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  }, [handleFileSelect]);\n\n  // Handle drag events\n  const handleDragEnter = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setIsDragging(true);\n    }\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setIsDragging(false);\n    }\n  }, []);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragging(false);\n    dragCounter.current = 0;\n\n    if (disabled || loading) return;\n\n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  }, [disabled, loading, handleFileSelect]);\n\n  // Handle remove image\n  const handleRemoveImage = useCallback(() => {\n    setPreview(null);\n    setLocalError(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    onImageRemove?.();\n  }, [onImageRemove]);\n\n  // Handle click to upload\n  const handleUploadClick = useCallback(() => {\n    if (disabled || loading) return;\n    fileInputRef.current?.click();\n  }, [disabled, loading]);\n\n  const displayError = error || localError;\n\n  return (\n    <div className={`profile-picture-upload ${className}`}>\n      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>\n        {/* Profile Picture Container */}\n        <div\n          style={{\n            position: 'relative',\n            width: config.container,\n            height: config.container,\n            borderRadius: '50%',\n            border: isDragging ? '3px dashed #3b82f6' : '2px solid #e5e7eb',\n            backgroundColor: isDragging ? '#eff6ff' : '#f9fafb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: disabled || loading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.2s ease',\n            overflow: 'hidden'\n          }}\n          onClick={handleUploadClick}\n          onDragEnter={handleDragEnter}\n          onDragLeave={handleDragLeave}\n          onDragOver={handleDragOver}\n          onDrop={handleDrop}\n        >\n          {loading ? (\n            <Loader2 size={32} className=\"animate-spin text-blue-500\" />\n          ) : preview ? (\n            <>\n              <img\n                src={preview}\n                alt=\"Profile preview\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover',\n                  borderRadius: '50%'\n                }}\n              />\n              {!disabled && (\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleRemoveImage();\n                  }}\n                  style={{\n                    position: 'absolute',\n                    top: '8px',\n                    right: '8px',\n                    background: '#ef4444',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '50%',\n                    width: '24px',\n                    height: '24px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    cursor: 'pointer',\n                    fontSize: '12px',\n                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n                  }}\n                >\n                  <X size={14} />\n                </button>\n              )}\n            </>\n          ) : (\n            <div style={{ textAlign: 'center', color: '#6b7280' }}>\n              {isDragging ? (\n                <Upload size={32} color=\"#3b82f6\" />\n              ) : (\n                <User size={32} />\n              )}\n              <div style={{ fontSize: '0.75rem', marginTop: '0.5rem' }}>\n                {isDragging ? 'Drop image here' : 'Click or drag to upload'}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Upload Instructions */}\n        <div style={{ textAlign: 'center', fontSize: '0.875rem', color: '#6b7280' }}>\n          <div>Maximum file size: {Math.round(maxSize / (1024 * 1024))}MB</div>\n          <div>Supported formats: {acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}</div>\n        </div>\n\n        {/* Error Message */}\n        {displayError && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            color: '#ef4444',\n            fontSize: '0.875rem',\n            backgroundColor: '#fef2f2',\n            padding: '0.75rem',\n            borderRadius: '8px',\n            border: '1px solid #fecaca'\n          }}>\n            <AlertCircle size={16} />\n            {displayError}\n          </div>\n        )}\n\n        {/* Hidden File Input */}\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept={acceptedFormats.join(',')}\n          onChange={handleFileChange}\n          style={{ display: 'none' }}\n          disabled={disabled || loading}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ProfilePictureUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SAASC,MAAM,EAAEC,CAAC,EAAEC,IAAI,EAAEC,WAAW,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAerE,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,eAAe;EACfC,aAAa;EACbC,aAAa;EACbC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EAC3BC,eAAe,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACxEC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG,IAAI;EACZC,IAAI,GAAG;AACT,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAgBa,eAAe,IAAI,IAAI,CAAC;EAC9E,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM8B,YAAY,GAAG7B,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAM8B,WAAW,GAAG9B,MAAM,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAM+B,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,MAAM,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAS,CAAC;IAChEC,MAAM,EAAE;MAAEH,SAAS,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAO,CAAC;IACjEE,KAAK,EAAE;MAAEJ,SAAS,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS;EACnE,CAAC;EAED,MAAMG,MAAM,GAAGP,UAAU,CAACV,IAAI,CAAC;;EAE/B;EACA,MAAMkB,YAAY,GAAGtC,WAAW,CAAEuC,IAAU,IAAoB;IAC9D,IAAI,CAACxB,eAAe,CAACyB,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACxC,OAAO,0CAA0C1B,eAAe,CAAC2B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IACvH;IAEA,IAAIP,IAAI,CAACnB,IAAI,GAAGN,OAAO,EAAE;MACvB,MAAMiC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACnC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;MACrD,OAAO,sCAAsCiC,SAAS,IAAI;IAC5D;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAAChC,eAAe,EAAED,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMoC,gBAAgB,GAAGlD,WAAW,CAAEuC,IAAU,IAAK;IACnD,MAAMY,eAAe,GAAGb,YAAY,CAACC,IAAI,CAAC;IAC1C,IAAIY,eAAe,EAAE;MACnBxB,aAAa,CAACwB,eAAe,CAAC;MAC9B;IACF;IAEAxB,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMyB,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MAAA,IAAAC,SAAA;MACrBjC,UAAU,EAAAiC,SAAA,GAACD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB,CAAC;IACxC,CAAC;IACDN,MAAM,CAACO,aAAa,CAACpB,IAAI,CAAC;;IAE1B;IACA3B,aAAa,CAAC2B,IAAI,CAAC;EACrB,CAAC,EAAE,CAACD,YAAY,EAAE1B,aAAa,CAAC,CAAC;;EAEjC;EACA,MAAMgD,gBAAgB,GAAG5D,WAAW,CAAEuD,CAAsC,IAAK;IAAA,IAAAM,eAAA;IAC/E,MAAMtB,IAAI,IAAAsB,eAAA,GAAGN,CAAC,CAACE,MAAM,CAACK,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAItB,IAAI,EAAE;MACRW,gBAAgB,CAACX,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,CAACW,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMa,eAAe,GAAG/D,WAAW,CAAEuD,CAAkB,IAAK;IAC1DA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBT,CAAC,CAACU,eAAe,CAAC,CAAC;IACnBpC,WAAW,CAACqC,OAAO,EAAE;IACrB,IAAIX,CAAC,CAACY,YAAY,CAACC,KAAK,IAAIb,CAAC,CAACY,YAAY,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3D5C,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,eAAe,GAAGtE,WAAW,CAAEuD,CAAkB,IAAK;IAC1DA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBT,CAAC,CAACU,eAAe,CAAC,CAAC;IACnBpC,WAAW,CAACqC,OAAO,EAAE;IACrB,IAAIrC,WAAW,CAACqC,OAAO,KAAK,CAAC,EAAE;MAC7BzC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8C,cAAc,GAAGvE,WAAW,CAAEuD,CAAkB,IAAK;IACzDA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBT,CAAC,CAACU,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,UAAU,GAAGxE,WAAW,CAAEuD,CAAkB,IAAK;IACrDA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBT,CAAC,CAACU,eAAe,CAAC,CAAC;IACnBxC,aAAa,CAAC,KAAK,CAAC;IACpBI,WAAW,CAACqC,OAAO,GAAG,CAAC;IAEvB,IAAIjD,QAAQ,IAAIC,OAAO,EAAE;IAEzB,MAAM4C,KAAK,GAAGP,CAAC,CAACY,YAAY,CAACL,KAAK;IAClC,IAAIA,KAAK,IAAIA,KAAK,CAACO,MAAM,GAAG,CAAC,EAAE;MAC7BnB,gBAAgB,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC7C,QAAQ,EAAEC,OAAO,EAAEgC,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAMuB,iBAAiB,GAAGzE,WAAW,CAAC,MAAM;IAC1CuB,UAAU,CAAC,IAAI,CAAC;IAChBI,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,YAAY,CAACsC,OAAO,EAAE;MACxBtC,YAAY,CAACsC,OAAO,CAACQ,KAAK,GAAG,EAAE;IACjC;IACA7D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;EACnB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8D,iBAAiB,GAAG3E,WAAW,CAAC,MAAM;IAAA,IAAA4E,qBAAA;IAC1C,IAAI3D,QAAQ,IAAIC,OAAO,EAAE;IACzB,CAAA0D,qBAAA,GAAAhD,YAAY,CAACsC,OAAO,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;EAC/B,CAAC,EAAE,CAAC5D,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvB,MAAM4D,YAAY,GAAG3D,KAAK,IAAIO,UAAU;EAExC,oBACEnB,OAAA;IAAKS,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAA+D,QAAA,eACpDxE,OAAA;MAAKyE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAE1FxE,OAAA;QACEyE,KAAK,EAAE;UACLK,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAEjD,MAAM,CAACL,SAAS;UACvBuD,MAAM,EAAElD,MAAM,CAACL,SAAS;UACxBwD,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAEjE,UAAU,GAAG,oBAAoB,GAAG,mBAAmB;UAC/DkE,eAAe,EAAElE,UAAU,GAAG,SAAS,GAAG,SAAS;UACnDyD,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBQ,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE3E,QAAQ,IAAIC,OAAO,GAAG,aAAa,GAAG,SAAS;UACvD2E,UAAU,EAAE,eAAe;UAC3BC,QAAQ,EAAE;QACZ,CAAE;QACFC,OAAO,EAAEpB,iBAAkB;QAC3BqB,WAAW,EAAEjC,eAAgB;QAC7BkC,WAAW,EAAE3B,eAAgB;QAC7B4B,UAAU,EAAE3B,cAAe;QAC3B4B,MAAM,EAAE3B,UAAW;QAAAO,QAAA,EAElB7D,OAAO,gBACNX,OAAA,CAACF,OAAO;UAACe,IAAI,EAAE,EAAG;UAACJ,SAAS,EAAC;QAA4B;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC1DjF,OAAO,gBACTf,OAAA,CAAAE,SAAA;UAAAsE,QAAA,gBACExE,OAAA;YACEiG,GAAG,EAAElF,OAAQ;YACbmF,GAAG,EAAC,iBAAiB;YACrBzB,KAAK,EAAE;cACLM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdmB,SAAS,EAAE,OAAO;cAClBlB,YAAY,EAAE;YAChB;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD,CAACtF,QAAQ,iBACRV,OAAA;YACEwF,OAAO,EAAGxC,CAAC,IAAK;cACdA,CAAC,CAACU,eAAe,CAAC,CAAC;cACnBQ,iBAAiB,CAAC,CAAC;YACrB,CAAE;YACFO,KAAK,EAAE;cACLK,QAAQ,EAAE,UAAU;cACpBsB,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE,KAAK;cACZC,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,KAAK;cACnBF,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdN,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBQ,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjB1D,QAAQ,EAAE,MAAM;cAChB6E,SAAS,EAAE;YACb,CAAE;YAAAhC,QAAA,eAEFxE,OAAA,CAACL,CAAC;cAACkB,IAAI,EAAE;YAAG;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACT;QAAA,eACD,CAAC,gBAEHhG,OAAA;UAAKyE,KAAK,EAAE;YAAEgC,SAAS,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAU,CAAE;UAAA/B,QAAA,GACnDvD,UAAU,gBACTjB,OAAA,CAACN,MAAM;YAACmB,IAAI,EAAE,EAAG;YAAC0F,KAAK,EAAC;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpChG,OAAA,CAACJ,IAAI;YAACiB,IAAI,EAAE;UAAG;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAClB,eACDhG,OAAA;YAAKyE,KAAK,EAAE;cAAE9C,QAAQ,EAAE,SAAS;cAAE+E,SAAS,EAAE;YAAS,CAAE;YAAAlC,QAAA,EACtDvD,UAAU,GAAG,iBAAiB,GAAG;UAAyB;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhG,OAAA;QAAKyE,KAAK,EAAE;UAAEgC,SAAS,EAAE,QAAQ;UAAE9E,QAAQ,EAAE,UAAU;UAAE4E,KAAK,EAAE;QAAU,CAAE;QAAA/B,QAAA,gBAC1ExE,OAAA;UAAAwE,QAAA,GAAK,qBAAmB,EAAC/B,IAAI,CAACC,KAAK,CAACnC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,EAAC,IAAE;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrEhG,OAAA;UAAAwE,QAAA,GAAK,qBAAmB,EAAChE,eAAe,CAAC2B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,EAGLzB,YAAY,iBACXvE,OAAA;QAAKyE,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,QAAQ;UACb0B,KAAK,EAAE,SAAS;UAChB5E,QAAQ,EAAE,UAAU;UACpBwD,eAAe,EAAE,SAAS;UAC1BwB,OAAO,EAAE,SAAS;UAClB1B,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAV,QAAA,gBACAxE,OAAA,CAACH,WAAW;UAACgB,IAAI,EAAE;QAAG;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxBzB,YAAY;MAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAGDhG,OAAA;QACE4G,GAAG,EAAEvF,YAAa;QAClBa,IAAI,EAAC,MAAM;QACX2E,MAAM,EAAErG,eAAe,CAAC+B,IAAI,CAAC,GAAG,CAAE;QAClCuE,QAAQ,EAAEzD,gBAAiB;QAC3BoB,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAC3BhE,QAAQ,EAAEA,QAAQ,IAAIC;MAAQ;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAnPIX,oBAAyD;AAAA4G,EAAA,GAAzD5G,oBAAyD;AAqP/D,eAAeA,oBAAoB;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}