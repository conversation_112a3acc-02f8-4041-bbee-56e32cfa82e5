{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 16 1.5 1.5\",\n  key: \"11lckj\"\n}], [\"path\", {\n  d: \"m14 8-1.5-1.5\",\n  key: \"1ohn8i\"\n}], [\"path\", {\n  d: \"M15 2c-1.798 1.998-2.518 3.995-2.807 5.993\",\n  key: \"80uv8i\"\n}], [\"path\", {\n  d: \"m16.5 10.5 1 1\",\n  key: \"696xn5\"\n}], [\"path\", {\n  d: \"m17 6-2.891-2.891\",\n  key: \"xu6p2f\"\n}], [\"path\", {\n  d: \"M2 15c6.667-6 13.333 0 20-6\",\n  key: \"1pyr53\"\n}], [\"path\", {\n  d: \"m20 9 .891.891\",\n  key: \"3xwk7g\"\n}], [\"path\", {\n  d: \"M3.109 14.109 4 15\",\n  key: \"q76aoh\"\n}], [\"path\", {\n  d: \"m6.5 12.5 1 1\",\n  key: \"cs35ky\"\n}], [\"path\", {\n  d: \"m7 18 2.891 2.891\",\n  key: \"1sisit\"\n}], [\"path\", {\n  d: \"M9 22c1.798-1.998 2.518-3.995 2.807-5.993\",\n  key: \"q3hbxp\"\n}]];\nconst Dna = createLucideIcon(\"dna\", __iconNode);\nexport { __iconNode, Dna as default };\n//# sourceMappingURL=dna.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}