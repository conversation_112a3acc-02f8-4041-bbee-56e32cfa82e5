{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"path\", {\n  d: \"M9 17c2 0 2.8-1 2.8-2.8V10c0-2 1-3.3 3.2-3\",\n  key: \"m1af9g\"\n}], [\"path\", {\n  d: \"M9 11.2h5.7\",\n  key: \"3zgcl2\"\n}]];\nconst SquareFunction = createLucideIcon(\"square-function\", __iconNode);\nexport { __iconNode, SquareFunction as default };\n//# sourceMappingURL=square-function.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}