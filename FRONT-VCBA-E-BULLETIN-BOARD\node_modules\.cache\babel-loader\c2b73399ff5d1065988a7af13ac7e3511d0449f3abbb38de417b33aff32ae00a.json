{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"7\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"f1a2em\"\n}], [\"rect\", {\n  width: \"9\",\n  height: \"7\",\n  x: \"3\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"jqznyg\"\n}], [\"rect\", {\n  width: \"5\",\n  height: \"7\",\n  x: \"16\",\n  y: \"14\",\n  rx: \"1\",\n  key: \"q5h2i8\"\n}]];\nconst LayoutTemplate = createLucideIcon(\"layout-template\", __iconNode);\nexport { __iconNode, LayoutTemplate as default };\n//# sourceMappingURL=layout-template.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}