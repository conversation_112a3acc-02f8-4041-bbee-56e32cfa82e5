{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 13v6h-6\",\n  key: \"1hxl6d\"\n}], [\"path\", {\n  d: \"M5 11V5h6\",\n  key: \"12e2xe\"\n}], [\"path\", {\n  d: \"m5 5 14 14\",\n  key: \"11anup\"\n}]];\nconst MoveDiagonal2 = createLucideIcon(\"move-diagonal-2\", __iconNode);\nexport { __iconNode, MoveDiagonal2 as default };\n//# sourceMappingURL=move-diagonal-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}