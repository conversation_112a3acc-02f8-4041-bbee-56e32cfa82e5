{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.827 16.379a6.082 6.082 0 0 1-8.618-7.002l5.412 1.45a6.082 6.082 0 0 1 7.002-8.618l-1.45 5.412a6.082 6.082 0 0 1 8.618 7.002l-5.412-1.45a6.082 6.082 0 0 1-7.002 8.618l1.45-5.412Z\",\n  key: \"484a7f\"\n}], [\"path\", {\n  d: \"M12 12v.01\",\n  key: \"u5ubse\"\n}]];\nconst Fan = createLucideIcon(\"fan\", __iconNode);\nexport { __iconNode, Fan as default };\n//# sourceMappingURL=fan.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}