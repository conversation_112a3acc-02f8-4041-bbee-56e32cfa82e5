{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z\",\n  key: \"1fy3hk\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"9\",\n  y1: \"10\",\n  y2: \"10\",\n  key: \"1gty7f\"\n}]];\nconst BookmarkMinus = createLucideIcon(\"bookmark-minus\", __iconNode);\nexport { __iconNode, BookmarkMinus as default };\n//# sourceMappingURL=bookmark-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}