"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionTypeScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class FunctionTypeScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>anager, ScopeType_1.ScopeType.functionType, upperScope, block, false);
    }
}
exports.FunctionTypeScope = FunctionTypeScope;
//# sourceMappingURL=FunctionTypeScope.js.map