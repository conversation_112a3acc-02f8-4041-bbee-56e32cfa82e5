{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"16\",\n  rx: \"2\",\n  key: \"1i8z2d\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"ypihtt\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}]];\nconst AlignVerticalJustifyCenter = createLucideIcon(\"align-vertical-justify-center\", __iconNode);\nexport { __iconNode, AlignVerticalJustifyCenter as default };\n//# sourceMappingURL=align-vertical-justify-center.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}