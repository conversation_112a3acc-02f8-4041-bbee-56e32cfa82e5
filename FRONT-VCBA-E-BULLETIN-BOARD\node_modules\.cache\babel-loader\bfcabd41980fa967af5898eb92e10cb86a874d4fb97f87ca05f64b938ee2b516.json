{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 14v4\",\n  key: \"1thi36\"\n}], [\"path\", {\n  d: \"M14.172 2a2 2 0 0 1 1.414.586l3.828 3.828A2 2 0 0 1 20 7.828V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z\",\n  key: \"1o66bk\"\n}], [\"path\", {\n  d: \"M8 14h8\",\n  key: \"1fgep2\"\n}], [\"rect\", {\n  x: \"8\",\n  y: \"10\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"1aonk6\"\n}]];\nconst CardSim = createLucideIcon(\"card-sim\", __iconNode);\nexport { __iconNode, CardSim as default };\n//# sourceMappingURL=card-sim.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}