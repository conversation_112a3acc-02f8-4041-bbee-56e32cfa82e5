{"ast": null, "code": "import { httpClient } from './api.service';\nimport { API_ENDPOINTS, STUDENT_AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY, API_BASE_URL } from '../config/constants';\n\n// Types for notifications\n\nclass NotificationService {\n  // Role-based authentication context detection (similar to comment service)\n  getCurrentUserAuth(preferredUserType) {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    const adminUser = localStorage.getItem(ADMIN_USER_DATA_KEY);\n    const studentUser = localStorage.getItem(STUDENT_USER_DATA_KEY);\n    console.log('🔍 NotificationService - Detecting user authentication context:', {\n      preferredUserType,\n      hasAdminToken: !!adminToken,\n      hasStudentToken: !!studentToken,\n      hasAdminUser: !!adminUser,\n      hasStudentUser: !!studentUser,\n      currentPath: window.location.pathname\n    });\n\n    // If a preferred user type is specified, use that context first\n    if (preferredUserType === 'admin' && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (preferred)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n    if (preferredUserType === 'student' && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (preferred)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // If no preference specified, determine based on current page context\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n    if (isAdminPage && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (admin page context)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n    if (isStudentPage && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (student page context)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Fallback: Use student authentication if available (prioritize student over admin)\n    if (studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (fallback)');\n          return {\n            useStudentAuth: true,\n            token: studentToken,\n            userType: 'student'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Then try admin authentication\n    if (adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (fallback)');\n          return {\n            useStudentAuth: false,\n            token: adminToken,\n            userType: 'admin'\n          };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    // Last resort: check tokens without user data validation (prioritize student)\n    if (studentToken) {\n      console.log('🔑 NotificationService - Using student token (no user data)');\n      return {\n        useStudentAuth: true,\n        token: studentToken,\n        userType: 'student'\n      };\n    }\n    if (adminToken) {\n      console.log('🔑 NotificationService - Using admin token (no user data)');\n      return {\n        useStudentAuth: false,\n        token: adminToken,\n        userType: 'admin'\n      };\n    }\n\n    // No authentication available\n    console.warn('⚠️ NotificationService - No authentication context available');\n    throw new Error('No authentication context available');\n  }\n\n  // Get user notifications with role-based authentication\n  async getNotifications(filters, preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n\n    // Log authentication context for debugging\n    console.log('NotificationService.getNotifications - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      tokenPrefix: token ? token.substring(0, 10) + '...' : null\n    });\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const url = params ? `${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}?${new URLSearchParams(params)}` : `${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notifications retrieved successfully',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to get notifications');\n      }\n    }\n\n    // Use httpClient for admin authentication or general fallback\n    return httpClient.get(API_ENDPOINTS.NOTIFICATIONS.BASE, params);\n  }\n\n  // Get unread notification count with role-based authentication\n  async getUnreadCount(preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n    console.log('NotificationService.getUnreadCount - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Unread count retrieved successfully',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to get unread count');\n      }\n    }\n    return httpClient.get(API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT);\n  }\n\n  // Mark notification as read with role-based authentication\n  async markAsRead(id, preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n    console.log('NotificationService.markAsRead - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      notificationId: id\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString())}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notification marked as read',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to mark notification as read');\n      }\n    }\n    return httpClient.put(API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString()));\n  }\n\n  // Mark all notifications as read with role-based authentication\n  async markAllAsRead(preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n    console.log('NotificationService.markAllAsRead - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'All notifications marked as read',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to mark all notifications as read');\n      }\n    }\n    return httpClient.put(API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ);\n  }\n\n  // Delete notification with role-based authentication\n  async deleteNotification(id, preferredUserType) {\n    const {\n      useStudentAuth,\n      token,\n      userType\n    } = this.getCurrentUserAuth(preferredUserType);\n    console.log('NotificationService.deleteNotification - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      notificationId: id\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}/${id}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notification deleted successfully',\n          data: result.data\n        };\n      } catch (error) {\n        throw new Error(error.message || 'Failed to delete notification');\n      }\n    }\n    return httpClient.delete(API_ENDPOINTS.NOTIFICATIONS.BASE + `/${id}`);\n  }\n\n  // Helper method to build query parameters\n  buildQueryParams(filters) {\n    const params = {};\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    return params;\n  }\n\n  // Get unread notifications with role-based authentication\n  async getUnreadNotifications(limit, preferredUserType) {\n    return this.getNotifications({\n      is_read: false,\n      limit: limit || 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    }, preferredUserType);\n  }\n\n  // Get recent notifications with role-based authentication\n  async getRecentNotifications(limit = 10, preferredUserType) {\n    return this.getNotifications({\n      limit,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    }, preferredUserType);\n  }\n\n  // Get notifications by type with role-based authentication\n  async getNotificationsByType(typeId, filters, preferredUserType) {\n    return this.getNotifications({\n      notification_type_id: typeId,\n      ...filters\n    }, preferredUserType);\n  }\n\n  // Get announcement-related notifications\n  async getAnnouncementNotifications(announcementId) {\n    // This would require backend support for filtering by related_announcement_id\n    // For now, return all notifications\n    return this.getNotifications({\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Get comment-related notifications\n  async getCommentNotifications(commentId) {\n    // This would require backend support for filtering by related_comment_id\n    // For now, return all notifications\n    return this.getNotifications({\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Bulk mark notifications as read with role-based authentication\n  async bulkMarkAsRead(notificationIds, preferredUserType) {\n    // This would require a bulk endpoint in the backend\n    // For now, mark each notification individually\n    const promises = notificationIds.map(id => this.markAsRead(id, preferredUserType));\n    await Promise.all(promises);\n    return {\n      success: true,\n      message: 'Notifications marked as read',\n      data: undefined\n    };\n  }\n\n  // Bulk delete notifications with role-based authentication\n  async bulkDeleteNotifications(notificationIds, preferredUserType) {\n    // This would require a bulk endpoint in the backend\n    // For now, delete each notification individually\n    const promises = notificationIds.map(id => this.deleteNotification(id, preferredUserType));\n    await Promise.all(promises);\n    return {\n      success: true,\n      message: 'Notifications deleted',\n      data: undefined\n    };\n  }\n\n  // Get notification statistics with role-based authentication\n  async getNotificationStatistics(preferredUserType) {\n    try {\n      var _allResponse$data, _unreadResponse$data;\n      const [unreadResponse, allResponse] = await Promise.all([this.getUnreadCount(preferredUserType), this.getNotifications({\n        limit: 1\n      }, preferredUserType)]);\n      return {\n        total: ((_allResponse$data = allResponse.data) === null || _allResponse$data === void 0 ? void 0 : _allResponse$data.pagination.total) || 0,\n        unread: ((_unreadResponse$data = unreadResponse.data) === null || _unreadResponse$data === void 0 ? void 0 : _unreadResponse$data.unreadCount) || 0,\n        today: 0,\n        // Would need backend support\n        thisWeek: 0 // Would need backend support\n      };\n    } catch (error) {\n      return {\n        total: 0,\n        unread: 0,\n        today: 0,\n        thisWeek: 0\n      };\n    }\n  }\n\n  // Check for new notifications (polling) with role-based authentication\n  async checkForNewNotifications(lastCheckTime, preferredUserType) {\n    try {\n      var _response$data;\n      const response = await this.getUnreadNotifications(10, preferredUserType);\n      const notifications = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.notifications) || [];\n      let newNotifications = notifications;\n      if (lastCheckTime) {\n        newNotifications = notifications.filter(notification => new Date(notification.created_at) > new Date(lastCheckTime));\n      }\n      return {\n        hasNew: newNotifications.length > 0,\n        count: newNotifications.length,\n        notifications: newNotifications\n      };\n    } catch (error) {\n      return {\n        hasNew: false,\n        count: 0,\n        notifications: []\n      };\n    }\n  }\n\n  // Format notification for display\n  formatNotification(notification) {\n    return {\n      title: notification.title,\n      message: notification.message,\n      timeAgo: this.getTimeAgo(notification.created_at),\n      type: this.getNotificationType(notification.notification_type_id)\n    };\n  }\n\n  // Get time ago string\n  getTimeAgo(dateString) {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n      return 'Just now';\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 604800) {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n\n  // Get notification type name\n  getNotificationType(typeId) {\n    const types = {\n      1: 'announcement',\n      2: 'alert',\n      3: 'comment',\n      4: 'reaction',\n      5: 'system',\n      6: 'reminder'\n    };\n    return types[typeId] || 'notification';\n  }\n\n  // Subscribe to real-time notifications (WebSocket)\n  subscribeToNotifications(callback) {\n    // This would implement WebSocket connection for real-time notifications\n    // For now, return a dummy unsubscribe function\n    console.log('Notification subscription would be implemented here');\n    return () => {\n      console.log('Unsubscribing from notifications');\n    };\n  }\n}\n\n// Role-specific notification service classes\nclass AdminNotificationService extends NotificationService {\n  constructor() {\n    super();\n  }\n  async getNotifications(filters) {\n    console.log('🔧 AdminNotificationService - Getting notifications as admin');\n    return super.getNotifications(filters, 'admin');\n  }\n  async getUnreadCount() {\n    console.log('🔧 AdminNotificationService - Getting unread count as admin');\n    return super.getUnreadCount('admin');\n  }\n  async markAsRead(id) {\n    console.log('🔧 AdminNotificationService - Marking notification as read as admin');\n    return super.markAsRead(id, 'admin');\n  }\n  async markAllAsRead() {\n    console.log('🔧 AdminNotificationService - Marking all notifications as read as admin');\n    return super.markAllAsRead('admin');\n  }\n  async deleteNotification(id) {\n    console.log('🔧 AdminNotificationService - Deleting notification as admin');\n    return super.deleteNotification(id, 'admin');\n  }\n  async getUnreadNotifications(limit) {\n    console.log('🔧 AdminNotificationService - Getting unread notifications as admin');\n    return super.getUnreadNotifications(limit, 'admin');\n  }\n  async getRecentNotifications(limit = 10) {\n    console.log('🔧 AdminNotificationService - Getting recent notifications as admin');\n    return super.getRecentNotifications(limit, 'admin');\n  }\n  async getNotificationsByType(typeId, filters) {\n    console.log('🔧 AdminNotificationService - Getting notifications by type as admin');\n    return super.getNotificationsByType(typeId, filters, 'admin');\n  }\n  async getNotificationStatistics() {\n    console.log('🔧 AdminNotificationService - Getting notification statistics as admin');\n    return super.getNotificationStatistics('admin');\n  }\n  async checkForNewNotifications(lastCheckTime) {\n    console.log('🔧 AdminNotificationService - Checking for new notifications as admin');\n    return super.checkForNewNotifications(lastCheckTime, 'admin');\n  }\n  async bulkMarkAsRead(notificationIds) {\n    console.log('🔧 AdminNotificationService - Bulk marking notifications as read as admin');\n    return super.bulkMarkAsRead(notificationIds, 'admin');\n  }\n  async bulkDeleteNotifications(notificationIds) {\n    console.log('🔧 AdminNotificationService - Bulk deleting notifications as admin');\n    return super.bulkDeleteNotifications(notificationIds, 'admin');\n  }\n}\nclass StudentNotificationService extends NotificationService {\n  constructor() {\n    super();\n  }\n  async getNotifications(filters) {\n    console.log('🔧 StudentNotificationService - Getting notifications as student');\n    return super.getNotifications(filters, 'student');\n  }\n  async getUnreadCount() {\n    console.log('🔧 StudentNotificationService - Getting unread count as student');\n    return super.getUnreadCount('student');\n  }\n  async markAsRead(id) {\n    console.log('🔧 StudentNotificationService - Marking notification as read as student');\n    return super.markAsRead(id, 'student');\n  }\n  async markAllAsRead() {\n    console.log('🔧 StudentNotificationService - Marking all notifications as read as student');\n    return super.markAllAsRead('student');\n  }\n  async deleteNotification(id) {\n    console.log('🔧 StudentNotificationService - Deleting notification as student');\n    return super.deleteNotification(id, 'student');\n  }\n  async getUnreadNotifications(limit) {\n    console.log('🔧 StudentNotificationService - Getting unread notifications as student');\n    return super.getUnreadNotifications(limit, 'student');\n  }\n  async getRecentNotifications(limit = 10) {\n    console.log('🔧 StudentNotificationService - Getting recent notifications as student');\n    return super.getRecentNotifications(limit, 'student');\n  }\n  async getNotificationsByType(typeId, filters) {\n    console.log('🔧 StudentNotificationService - Getting notifications by type as student');\n    return super.getNotificationsByType(typeId, filters, 'student');\n  }\n  async getNotificationStatistics() {\n    console.log('🔧 StudentNotificationService - Getting notification statistics as student');\n    return super.getNotificationStatistics('student');\n  }\n  async checkForNewNotifications(lastCheckTime) {\n    console.log('🔧 StudentNotificationService - Checking for new notifications as student');\n    return super.checkForNewNotifications(lastCheckTime, 'student');\n  }\n  async bulkMarkAsRead(notificationIds) {\n    console.log('🔧 StudentNotificationService - Bulk marking notifications as read as student');\n    return super.bulkMarkAsRead(notificationIds, 'student');\n  }\n  async bulkDeleteNotifications(notificationIds) {\n    console.log('🔧 StudentNotificationService - Bulk deleting notifications as student');\n    return super.bulkDeleteNotifications(notificationIds, 'student');\n  }\n}\nexport const notificationService = new NotificationService();\nexport const adminNotificationService = new AdminNotificationService();\nexport const studentNotificationService = new StudentNotificationService();\nexport default notificationService;", "map": {"version": 3, "names": ["httpClient", "API_ENDPOINTS", "STUDENT_AUTH_TOKEN_KEY", "ADMIN_AUTH_TOKEN_KEY", "ADMIN_USER_DATA_KEY", "STUDENT_USER_DATA_KEY", "API_BASE_URL", "NotificationService", "getCurrentUserAuth", "preferredUserType", "adminToken", "localStorage", "getItem", "studentToken", "adminUser", "studentUser", "console", "log", "hasAdminToken", "hasStudentToken", "hasAdminUser", "hasStudentUser", "currentPath", "window", "location", "pathname", "userData", "JSON", "parse", "role", "useStudentAuth", "token", "userType", "e", "warn", "isAdminPage", "includes", "isStudentPage", "Error", "getNotifications", "filters", "hasToken", "tokenPrefix", "substring", "params", "buildQueryParams", "undefined", "url", "NOTIFICATIONS", "BASE", "URLSearchParams", "response", "fetch", "method", "headers", "ok", "status", "result", "json", "success", "message", "data", "error", "get", "getUnreadCount", "UNREAD_COUNT", "mark<PERSON><PERSON><PERSON>", "id", "notificationId", "MARK_READ", "toString", "put", "markAllAsRead", "MARK_ALL_READ", "deleteNotification", "delete", "Object", "entries", "for<PERSON>ach", "key", "value", "getUnreadNotifications", "limit", "is_read", "sort_by", "sort_order", "getRecentNotifications", "getNotificationsByType", "typeId", "notification_type_id", "getAnnouncementNotifications", "announcementId", "getCommentNotifications", "commentId", "bulkMarkAsRead", "notificationIds", "promises", "map", "Promise", "all", "bulkDeleteNotifications", "getNotificationStatistics", "_allResponse$data", "_unreadResponse$data", "unreadResponse", "allResponse", "total", "pagination", "unread", "unreadCount", "today", "thisWeek", "checkForNewNotifications", "lastCheckTime", "_response$data", "notifications", "newNotifications", "filter", "notification", "Date", "created_at", "hasNew", "length", "count", "formatNotification", "title", "timeAgo", "getTimeAgo", "type", "getNotificationType", "dateString", "date", "now", "diffInSeconds", "Math", "floor", "getTime", "minutes", "hours", "days", "toLocaleDateString", "types", "subscribeToNotifications", "callback", "AdminNotificationService", "constructor", "StudentNotificationService", "notificationService", "adminNotificationService", "studentNotificationService"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/notificationService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nimport { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS, STUDENT_AUTH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, ADMIN_USER_DATA_KEY, STUDENT_USER_DATA_KEY, API_BASE_URL } from '../config/constants';\nimport { ApiResponse } from '../types';\n\n// Types for notifications\nexport interface Notification {\n  notification_id: number;\n  recipient_type: 'admin' | 'student';\n  recipient_id: number;\n  notification_type_id: number;\n  title: string;\n  message: string;\n  related_announcement_id?: number;\n  related_comment_id?: number;\n  is_read: boolean;\n  read_at?: string;\n  created_at: string;\n  // Enhanced metadata for navigation\n  type_name?: string;\n  context_metadata?: string | {\n    type: 'comment' | 'announcement' | 'calendar' | 'general';\n    target_id: number | null;\n    announcement_id?: number;\n    parent_comment_id?: number;\n    event_date?: string;\n    scroll_to?: string;\n  };\n}\n\nexport interface NotificationFilters {\n  page?: number;\n  limit?: number;\n  is_read?: boolean;\n  notification_type_id?: number;\n  start_date?: string;\n  end_date?: string;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface PaginatedNotificationsResponse {\n  notifications: Notification[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\nclass NotificationService {\n  // Role-based authentication context detection (similar to comment service)\n  private getCurrentUserAuth(preferredUserType?: 'admin' | 'student'): {\n    useStudentAuth: boolean;\n    token: string | null;\n    userType: 'admin' | 'student';\n  } {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n    const adminUser = localStorage.getItem(ADMIN_USER_DATA_KEY);\n    const studentUser = localStorage.getItem(STUDENT_USER_DATA_KEY);\n\n    console.log('🔍 NotificationService - Detecting user authentication context:', {\n      preferredUserType,\n      hasAdminToken: !!adminToken,\n      hasStudentToken: !!studentToken,\n      hasAdminUser: !!adminUser,\n      hasStudentUser: !!studentUser,\n      currentPath: window.location.pathname\n    });\n\n    // If a preferred user type is specified, use that context first\n    if (preferredUserType === 'admin' && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (preferred)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    if (preferredUserType === 'student' && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (preferred)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // If no preference specified, determine based on current page context\n    const currentPath = window.location.pathname;\n    const isAdminPage = currentPath.includes('/admin');\n    const isStudentPage = currentPath.includes('/student');\n\n    if (isAdminPage && adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (admin page context)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    if (isStudentPage && studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (student page context)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Fallback: Use student authentication if available (prioritize student over admin)\n    if (studentToken && studentUser) {\n      try {\n        const userData = JSON.parse(studentUser);\n        if (userData.role === 'student') {\n          console.log('🔑 NotificationService - Using student authentication (fallback)');\n          return { useStudentAuth: true, token: studentToken, userType: 'student' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse student user data');\n      }\n    }\n\n    // Then try admin authentication\n    if (adminToken && adminUser) {\n      try {\n        const userData = JSON.parse(adminUser);\n        if (userData.role === 'admin') {\n          console.log('🔑 NotificationService - Using admin authentication (fallback)');\n          return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n        }\n      } catch (e) {\n        console.warn('Failed to parse admin user data');\n      }\n    }\n\n    // Last resort: check tokens without user data validation (prioritize student)\n    if (studentToken) {\n      console.log('🔑 NotificationService - Using student token (no user data)');\n      return { useStudentAuth: true, token: studentToken, userType: 'student' };\n    }\n\n    if (adminToken) {\n      console.log('🔑 NotificationService - Using admin token (no user data)');\n      return { useStudentAuth: false, token: adminToken, userType: 'admin' };\n    }\n\n    // No authentication available\n    console.warn('⚠️ NotificationService - No authentication context available');\n    throw new Error('No authentication context available');\n  }\n\n  // Get user notifications with role-based authentication\n  async getNotifications(filters?: NotificationFilters, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    // Log authentication context for debugging\n    console.log('NotificationService.getNotifications - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      tokenPrefix: token ? token.substring(0, 10) + '...' : null\n    });\n\n    const params = filters ? this.buildQueryParams(filters) : undefined;\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const url = params ? `${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}?${new URLSearchParams(params)}` : `${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notifications retrieved successfully',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to get notifications');\n      }\n    }\n\n    // Use httpClient for admin authentication or general fallback\n    return httpClient.get<PaginatedNotificationsResponse>(API_ENDPOINTS.NOTIFICATIONS.BASE, params);\n  }\n\n  // Get unread notification count with role-based authentication\n  async getUnreadCount(preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<{ unreadCount: number }>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    console.log('NotificationService.getUnreadCount - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Unread count retrieved successfully',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to get unread count');\n      }\n    }\n\n    return httpClient.get<{ unreadCount: number }>(API_ENDPOINTS.NOTIFICATIONS.UNREAD_COUNT);\n  }\n\n  // Mark notification as read with role-based authentication\n  async markAsRead(id: number, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<void>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    console.log('NotificationService.markAsRead - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      notificationId: id\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString())}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notification marked as read',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to mark notification as read');\n      }\n    }\n\n    return httpClient.put<void>(API_ENDPOINTS.NOTIFICATIONS.MARK_READ(id.toString()));\n  }\n\n  // Mark all notifications as read with role-based authentication\n  async markAllAsRead(preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<void>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    console.log('NotificationService.markAllAsRead - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'All notifications marked as read',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to mark all notifications as read');\n      }\n    }\n\n    return httpClient.put<void>(API_ENDPOINTS.NOTIFICATIONS.MARK_ALL_READ);\n  }\n\n  // Delete notification with role-based authentication\n  async deleteNotification(id: number, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<void>> {\n    const { useStudentAuth, token, userType } = this.getCurrentUserAuth(preferredUserType);\n\n    console.log('NotificationService.deleteNotification - Auth context:', {\n      preferredUserType,\n      useStudentAuth,\n      hasToken: !!token,\n      userType,\n      notificationId: id\n    });\n\n    // Use specific authentication if we have a token\n    if (useStudentAuth && token) {\n      try {\n        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.NOTIFICATIONS.BASE}/${id}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        return {\n          success: true,\n          message: result.message || 'Notification deleted successfully',\n          data: result.data\n        };\n      } catch (error: any) {\n        throw new Error(error.message || 'Failed to delete notification');\n      }\n    }\n\n    return httpClient.delete<void>(API_ENDPOINTS.NOTIFICATIONS.BASE + `/${id}`);\n  }\n\n  // Helper method to build query parameters\n  private buildQueryParams(filters: NotificationFilters): Record<string, string> {\n    const params: Record<string, string> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params[key] = value.toString();\n      }\n    });\n    \n    return params;\n  }\n\n  // Get unread notifications with role-based authentication\n  async getUnreadNotifications(limit?: number, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    return this.getNotifications({\n      is_read: false,\n      limit: limit || 20,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    }, preferredUserType);\n  }\n\n  // Get recent notifications with role-based authentication\n  async getRecentNotifications(limit: number = 10, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    return this.getNotifications({\n      limit,\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    }, preferredUserType);\n  }\n\n  // Get notifications by type with role-based authentication\n  async getNotificationsByType(typeId: number, filters?: Partial<NotificationFilters>, preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    return this.getNotifications({\n      notification_type_id: typeId,\n      ...filters\n    }, preferredUserType);\n  }\n\n  // Get announcement-related notifications\n  async getAnnouncementNotifications(announcementId: number): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    // This would require backend support for filtering by related_announcement_id\n    // For now, return all notifications\n    return this.getNotifications({\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Get comment-related notifications\n  async getCommentNotifications(commentId: number): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    // This would require backend support for filtering by related_comment_id\n    // For now, return all notifications\n    return this.getNotifications({\n      sort_by: 'created_at',\n      sort_order: 'DESC'\n    });\n  }\n\n  // Bulk mark notifications as read with role-based authentication\n  async bulkMarkAsRead(notificationIds: number[], preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<void>> {\n    // This would require a bulk endpoint in the backend\n    // For now, mark each notification individually\n    const promises = notificationIds.map(id => this.markAsRead(id, preferredUserType));\n    await Promise.all(promises);\n    return { success: true, message: 'Notifications marked as read', data: undefined };\n  }\n\n  // Bulk delete notifications with role-based authentication\n  async bulkDeleteNotifications(notificationIds: number[], preferredUserType?: 'admin' | 'student'): Promise<ApiResponse<void>> {\n    // This would require a bulk endpoint in the backend\n    // For now, delete each notification individually\n    const promises = notificationIds.map(id => this.deleteNotification(id, preferredUserType));\n    await Promise.all(promises);\n    return { success: true, message: 'Notifications deleted', data: undefined };\n  }\n\n  // Get notification statistics with role-based authentication\n  async getNotificationStatistics(preferredUserType?: 'admin' | 'student'): Promise<{\n    total: number;\n    unread: number;\n    today: number;\n    thisWeek: number;\n  }> {\n    try {\n      const [unreadResponse, allResponse] = await Promise.all([\n        this.getUnreadCount(preferredUserType),\n        this.getNotifications({ limit: 1 }, preferredUserType)\n      ]);\n\n      return {\n        total: allResponse.data?.pagination.total || 0,\n        unread: unreadResponse.data?.unreadCount || 0,\n        today: 0, // Would need backend support\n        thisWeek: 0 // Would need backend support\n      };\n    } catch (error) {\n      return {\n        total: 0,\n        unread: 0,\n        today: 0,\n        thisWeek: 0\n      };\n    }\n  }\n\n  // Check for new notifications (polling) with role-based authentication\n  async checkForNewNotifications(lastCheckTime?: string, preferredUserType?: 'admin' | 'student'): Promise<{\n    hasNew: boolean;\n    count: number;\n    notifications: Notification[];\n  }> {\n    try {\n      const response = await this.getUnreadNotifications(10, preferredUserType);\n      const notifications = response.data?.notifications || [];\n\n      let newNotifications = notifications;\n      if (lastCheckTime) {\n        newNotifications = notifications.filter(\n          notification => new Date(notification.created_at) > new Date(lastCheckTime)\n        );\n      }\n\n      return {\n        hasNew: newNotifications.length > 0,\n        count: newNotifications.length,\n        notifications: newNotifications\n      };\n    } catch (error) {\n      return {\n        hasNew: false,\n        count: 0,\n        notifications: []\n      };\n    }\n  }\n\n  // Format notification for display\n  formatNotification(notification: Notification): {\n    title: string;\n    message: string;\n    timeAgo: string;\n    type: string;\n  } {\n    return {\n      title: notification.title,\n      message: notification.message,\n      timeAgo: this.getTimeAgo(notification.created_at),\n      type: this.getNotificationType(notification.notification_type_id)\n    };\n  }\n\n  // Get time ago string\n  private getTimeAgo(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) {\n      return 'Just now';\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 604800) {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n\n  // Get notification type name\n  private getNotificationType(typeId: number): string {\n    const types: Record<number, string> = {\n      1: 'announcement',\n      2: 'alert',\n      3: 'comment',\n      4: 'reaction',\n      5: 'system',\n      6: 'reminder'\n    };\n    return types[typeId] || 'notification';\n  }\n\n  // Subscribe to real-time notifications (WebSocket)\n  subscribeToNotifications(callback: (notification: Notification) => void): () => void {\n    // This would implement WebSocket connection for real-time notifications\n    // For now, return a dummy unsubscribe function\n    console.log('Notification subscription would be implemented here');\n    \n    return () => {\n      console.log('Unsubscribing from notifications');\n    };\n  }\n}\n\n// Role-specific notification service classes\nclass AdminNotificationService extends NotificationService {\n  constructor() {\n    super();\n  }\n\n  async getNotifications(filters?: NotificationFilters): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 AdminNotificationService - Getting notifications as admin');\n    return super.getNotifications(filters, 'admin');\n  }\n\n  async getUnreadCount(): Promise<ApiResponse<{ unreadCount: number }>> {\n    console.log('🔧 AdminNotificationService - Getting unread count as admin');\n    return super.getUnreadCount('admin');\n  }\n\n  async markAsRead(id: number): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminNotificationService - Marking notification as read as admin');\n    return super.markAsRead(id, 'admin');\n  }\n\n  async markAllAsRead(): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminNotificationService - Marking all notifications as read as admin');\n    return super.markAllAsRead('admin');\n  }\n\n  async deleteNotification(id: number): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminNotificationService - Deleting notification as admin');\n    return super.deleteNotification(id, 'admin');\n  }\n\n  async getUnreadNotifications(limit?: number): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 AdminNotificationService - Getting unread notifications as admin');\n    return super.getUnreadNotifications(limit, 'admin');\n  }\n\n  async getRecentNotifications(limit: number = 10): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 AdminNotificationService - Getting recent notifications as admin');\n    return super.getRecentNotifications(limit, 'admin');\n  }\n\n  async getNotificationsByType(typeId: number, filters?: Partial<NotificationFilters>): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 AdminNotificationService - Getting notifications by type as admin');\n    return super.getNotificationsByType(typeId, filters, 'admin');\n  }\n\n  async getNotificationStatistics(): Promise<{ total: number; unread: number; today: number; thisWeek: number; }> {\n    console.log('🔧 AdminNotificationService - Getting notification statistics as admin');\n    return super.getNotificationStatistics('admin');\n  }\n\n  async checkForNewNotifications(lastCheckTime?: string): Promise<{ hasNew: boolean; count: number; notifications: Notification[]; }> {\n    console.log('🔧 AdminNotificationService - Checking for new notifications as admin');\n    return super.checkForNewNotifications(lastCheckTime, 'admin');\n  }\n\n  async bulkMarkAsRead(notificationIds: number[]): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminNotificationService - Bulk marking notifications as read as admin');\n    return super.bulkMarkAsRead(notificationIds, 'admin');\n  }\n\n  async bulkDeleteNotifications(notificationIds: number[]): Promise<ApiResponse<void>> {\n    console.log('🔧 AdminNotificationService - Bulk deleting notifications as admin');\n    return super.bulkDeleteNotifications(notificationIds, 'admin');\n  }\n}\n\nclass StudentNotificationService extends NotificationService {\n  constructor() {\n    super();\n  }\n\n  async getNotifications(filters?: NotificationFilters): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 StudentNotificationService - Getting notifications as student');\n    return super.getNotifications(filters, 'student');\n  }\n\n  async getUnreadCount(): Promise<ApiResponse<{ unreadCount: number }>> {\n    console.log('🔧 StudentNotificationService - Getting unread count as student');\n    return super.getUnreadCount('student');\n  }\n\n  async markAsRead(id: number): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentNotificationService - Marking notification as read as student');\n    return super.markAsRead(id, 'student');\n  }\n\n  async markAllAsRead(): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentNotificationService - Marking all notifications as read as student');\n    return super.markAllAsRead('student');\n  }\n\n  async deleteNotification(id: number): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentNotificationService - Deleting notification as student');\n    return super.deleteNotification(id, 'student');\n  }\n\n  async getUnreadNotifications(limit?: number): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 StudentNotificationService - Getting unread notifications as student');\n    return super.getUnreadNotifications(limit, 'student');\n  }\n\n  async getRecentNotifications(limit: number = 10): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 StudentNotificationService - Getting recent notifications as student');\n    return super.getRecentNotifications(limit, 'student');\n  }\n\n  async getNotificationsByType(typeId: number, filters?: Partial<NotificationFilters>): Promise<ApiResponse<PaginatedNotificationsResponse>> {\n    console.log('🔧 StudentNotificationService - Getting notifications by type as student');\n    return super.getNotificationsByType(typeId, filters, 'student');\n  }\n\n  async getNotificationStatistics(): Promise<{ total: number; unread: number; today: number; thisWeek: number; }> {\n    console.log('🔧 StudentNotificationService - Getting notification statistics as student');\n    return super.getNotificationStatistics('student');\n  }\n\n  async checkForNewNotifications(lastCheckTime?: string): Promise<{ hasNew: boolean; count: number; notifications: Notification[]; }> {\n    console.log('🔧 StudentNotificationService - Checking for new notifications as student');\n    return super.checkForNewNotifications(lastCheckTime, 'student');\n  }\n\n  async bulkMarkAsRead(notificationIds: number[]): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentNotificationService - Bulk marking notifications as read as student');\n    return super.bulkMarkAsRead(notificationIds, 'student');\n  }\n\n  async bulkDeleteNotifications(notificationIds: number[]): Promise<ApiResponse<void>> {\n    console.log('🔧 StudentNotificationService - Bulk deleting notifications as student');\n    return super.bulkDeleteNotifications(notificationIds, 'student');\n  }\n}\n\nexport const notificationService = new NotificationService();\nexport const adminNotificationService = new AdminNotificationService();\nexport const studentNotificationService = new StudentNotificationService();\nexport default notificationService;\n"], "mappings": "AAAA,SAASA,UAAU,QAA4C,eAAe;AAE9E,SAASC,aAAa,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,qBAAqB;;AAG3J;;AAgDA,MAAMC,mBAAmB,CAAC;EACxB;EACQC,kBAAkBA,CAACC,iBAAuC,EAIhE;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACT,oBAAoB,CAAC;IAC7D,MAAMU,YAAY,GAAGF,YAAY,CAACC,OAAO,CAACV,sBAAsB,CAAC;IACjE,MAAMY,SAAS,GAAGH,YAAY,CAACC,OAAO,CAACR,mBAAmB,CAAC;IAC3D,MAAMW,WAAW,GAAGJ,YAAY,CAACC,OAAO,CAACP,qBAAqB,CAAC;IAE/DW,OAAO,CAACC,GAAG,CAAC,iEAAiE,EAAE;MAC7ER,iBAAiB;MACjBS,aAAa,EAAE,CAAC,CAACR,UAAU;MAC3BS,eAAe,EAAE,CAAC,CAACN,YAAY;MAC/BO,YAAY,EAAE,CAAC,CAACN,SAAS;MACzBO,cAAc,EAAE,CAAC,CAACN,WAAW;MAC7BO,WAAW,EAAEC,MAAM,CAACC,QAAQ,CAACC;IAC/B,CAAC,CAAC;;IAEF;IACA,IAAIhB,iBAAiB,KAAK,OAAO,IAAIC,UAAU,IAAII,SAAS,EAAE;MAC5D,IAAI;QACF,MAAMY,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACd,SAAS,CAAC;QACtC,IAAIY,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7Bb,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;UAC9E,OAAO;YAAEa,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAErB,UAAU;YAAEsB,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;IAEA,IAAIzB,iBAAiB,KAAK,SAAS,IAAII,YAAY,IAAIE,WAAW,EAAE;MAClE,IAAI;QACF,MAAMW,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACb,WAAW,CAAC;QACxC,IAAIW,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/Bb,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;UAChF,OAAO;YAAEa,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAElB,YAAY;YAAEmB,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,MAAMZ,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMU,WAAW,GAAGb,WAAW,CAACc,QAAQ,CAAC,QAAQ,CAAC;IAClD,MAAMC,aAAa,GAAGf,WAAW,CAACc,QAAQ,CAAC,UAAU,CAAC;IAEtD,IAAID,WAAW,IAAIzB,UAAU,IAAII,SAAS,EAAE;MAC1C,IAAI;QACF,MAAMY,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACd,SAAS,CAAC;QACtC,IAAIY,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7Bb,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;UACvF,OAAO;YAAEa,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAErB,UAAU;YAAEsB,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;IAEA,IAAIG,aAAa,IAAIxB,YAAY,IAAIE,WAAW,EAAE;MAChD,IAAI;QACF,MAAMW,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACb,WAAW,CAAC;QACxC,IAAIW,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/Bb,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;UAC3F,OAAO;YAAEa,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAElB,YAAY;YAAEmB,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,IAAIrB,YAAY,IAAIE,WAAW,EAAE;MAC/B,IAAI;QACF,MAAMW,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACb,WAAW,CAAC;QACxC,IAAIW,QAAQ,CAACG,IAAI,KAAK,SAAS,EAAE;UAC/Bb,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;UAC/E,OAAO;YAAEa,cAAc,EAAE,IAAI;YAAEC,KAAK,EAAElB,YAAY;YAAEmB,QAAQ,EAAE;UAAU,CAAC;QAC3E;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,mCAAmC,CAAC;MACnD;IACF;;IAEA;IACA,IAAIxB,UAAU,IAAII,SAAS,EAAE;MAC3B,IAAI;QACF,MAAMY,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACd,SAAS,CAAC;QACtC,IAAIY,QAAQ,CAACG,IAAI,KAAK,OAAO,EAAE;UAC7Bb,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E,OAAO;YAAEa,cAAc,EAAE,KAAK;YAAEC,KAAK,EAAErB,UAAU;YAAEsB,QAAQ,EAAE;UAAQ,CAAC;QACxE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVjB,OAAO,CAACkB,IAAI,CAAC,iCAAiC,CAAC;MACjD;IACF;;IAEA;IACA,IAAIrB,YAAY,EAAE;MAChBG,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E,OAAO;QAAEa,cAAc,EAAE,IAAI;QAAEC,KAAK,EAAElB,YAAY;QAAEmB,QAAQ,EAAE;MAAU,CAAC;IAC3E;IAEA,IAAItB,UAAU,EAAE;MACdM,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAO;QAAEa,cAAc,EAAE,KAAK;QAAEC,KAAK,EAAErB,UAAU;QAAEsB,QAAQ,EAAE;MAAQ,CAAC;IACxE;;IAEA;IACAhB,OAAO,CAACkB,IAAI,CAAC,8DAA8D,CAAC;IAC5E,MAAM,IAAII,KAAK,CAAC,qCAAqC,CAAC;EACxD;;EAEA;EACA,MAAMC,gBAAgBA,CAACC,OAA6B,EAAE/B,iBAAuC,EAAwD;IACnJ,MAAM;MAAEqB,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACxB,kBAAkB,CAACC,iBAAiB,CAAC;;IAEtF;IACAO,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE;MAClER,iBAAiB;MACjBqB,cAAc;MACdW,QAAQ,EAAE,CAAC,CAACV,KAAK;MACjBC,QAAQ;MACRU,WAAW,EAAEX,KAAK,GAAGA,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;IACxD,CAAC,CAAC;IAEF,MAAMC,MAAM,GAAGJ,OAAO,GAAG,IAAI,CAACK,gBAAgB,CAACL,OAAO,CAAC,GAAGM,SAAS;;IAEnE;IACA,IAAIhB,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMgB,GAAG,GAAGH,MAAM,GAAG,GAAGtC,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACC,IAAI,IAAI,IAAIC,eAAe,CAACN,MAAM,CAAC,EAAE,GAAG,GAAGtC,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACC,IAAI,EAAE;QAC/J,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAE;UAChCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUvB,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CAAC,uBAAuBa,QAAQ,CAACK,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,sCAAsC;UACjEC,IAAI,EAAEJ,MAAM,CAACI;QACf,CAAC;MACH,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,MAAM,IAAIxB,KAAK,CAACwB,KAAK,CAACF,OAAO,IAAI,6BAA6B,CAAC;MACjE;IACF;;IAEA;IACA,OAAO5D,UAAU,CAAC+D,GAAG,CAAiC9D,aAAa,CAAC+C,aAAa,CAACC,IAAI,EAAEL,MAAM,CAAC;EACjG;;EAEA;EACA,MAAMoB,cAAcA,CAACvD,iBAAuC,EAAiD;IAC3G,MAAM;MAAEqB,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACxB,kBAAkB,CAACC,iBAAiB,CAAC;IAEtFO,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;MAChER,iBAAiB;MACjBqB,cAAc;MACdW,QAAQ,EAAE,CAAC,CAACV,KAAK;MACjBC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIF,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9C,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACiB,YAAY,EAAE,EAAE;UACzFZ,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUvB,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CAAC,uBAAuBa,QAAQ,CAACK,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,qCAAqC;UAChEC,IAAI,EAAEJ,MAAM,CAACI;QACf,CAAC;MACH,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,MAAM,IAAIxB,KAAK,CAACwB,KAAK,CAACF,OAAO,IAAI,4BAA4B,CAAC;MAChE;IACF;IAEA,OAAO5D,UAAU,CAAC+D,GAAG,CAA0B9D,aAAa,CAAC+C,aAAa,CAACiB,YAAY,CAAC;EAC1F;;EAEA;EACA,MAAMC,UAAUA,CAACC,EAAU,EAAE1D,iBAAuC,EAA8B;IAChG,MAAM;MAAEqB,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACxB,kBAAkB,CAACC,iBAAiB,CAAC;IAEtFO,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;MAC5DR,iBAAiB;MACjBqB,cAAc;MACdW,QAAQ,EAAE,CAAC,CAACV,KAAK;MACjBC,QAAQ;MACRoC,cAAc,EAAED;IAClB,CAAC,CAAC;;IAEF;IACA,IAAIrC,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9C,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACqB,SAAS,CAACF,EAAE,CAACG,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE;UACrGjB,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUvB,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CAAC,uBAAuBa,QAAQ,CAACK,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,6BAA6B;UACxDC,IAAI,EAAEJ,MAAM,CAACI;QACf,CAAC;MACH,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,MAAM,IAAIxB,KAAK,CAACwB,KAAK,CAACF,OAAO,IAAI,qCAAqC,CAAC;MACzE;IACF;IAEA,OAAO5D,UAAU,CAACuE,GAAG,CAAOtE,aAAa,CAAC+C,aAAa,CAACqB,SAAS,CAACF,EAAE,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACnF;;EAEA;EACA,MAAME,aAAaA,CAAC/D,iBAAuC,EAA8B;IACvF,MAAM;MAAEqB,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACxB,kBAAkB,CAACC,iBAAiB,CAAC;IAEtFO,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;MAC/DR,iBAAiB;MACjBqB,cAAc;MACdW,QAAQ,EAAE,CAAC,CAACV,KAAK;MACjBC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIF,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9C,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACyB,aAAa,EAAE,EAAE;UAC1FpB,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUvB,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CAAC,uBAAuBa,QAAQ,CAACK,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,kCAAkC;UAC7DC,IAAI,EAAEJ,MAAM,CAACI;QACf,CAAC;MACH,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,MAAM,IAAIxB,KAAK,CAACwB,KAAK,CAACF,OAAO,IAAI,0CAA0C,CAAC;MAC9E;IACF;IAEA,OAAO5D,UAAU,CAACuE,GAAG,CAAOtE,aAAa,CAAC+C,aAAa,CAACyB,aAAa,CAAC;EACxE;;EAEA;EACA,MAAMC,kBAAkBA,CAACP,EAAU,EAAE1D,iBAAuC,EAA8B;IACxG,MAAM;MAAEqB,cAAc;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACxB,kBAAkB,CAACC,iBAAiB,CAAC;IAEtFO,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;MACpER,iBAAiB;MACjBqB,cAAc;MACdW,QAAQ,EAAE,CAAC,CAACV,KAAK;MACjBC,QAAQ;MACRoC,cAAc,EAAED;IAClB,CAAC,CAAC;;IAEF;IACA,IAAIrC,cAAc,IAAIC,KAAK,EAAE;MAC3B,IAAI;QACF,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9C,YAAY,GAAGL,aAAa,CAAC+C,aAAa,CAACC,IAAI,IAAIkB,EAAE,EAAE,EAAE;UACvFd,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUvB,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI,CAACoB,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CAAC,uBAAuBa,QAAQ,CAACK,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACpC,OAAO;UACLC,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEH,MAAM,CAACG,OAAO,IAAI,mCAAmC;UAC9DC,IAAI,EAAEJ,MAAM,CAACI;QACf,CAAC;MACH,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,MAAM,IAAIxB,KAAK,CAACwB,KAAK,CAACF,OAAO,IAAI,+BAA+B,CAAC;MACnE;IACF;IAEA,OAAO5D,UAAU,CAAC2E,MAAM,CAAO1E,aAAa,CAAC+C,aAAa,CAACC,IAAI,GAAG,IAAIkB,EAAE,EAAE,CAAC;EAC7E;;EAEA;EACQtB,gBAAgBA,CAACL,OAA4B,EAA0B;IAC7E,MAAMI,MAA8B,GAAG,CAAC,CAAC;IAEzCgC,MAAM,CAACC,OAAO,CAACrC,OAAO,CAAC,CAACsC,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKlC,SAAS,IAAIkC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDpC,MAAM,CAACmC,GAAG,CAAC,GAAGC,KAAK,CAACV,QAAQ,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IAEF,OAAO1B,MAAM;EACf;;EAEA;EACA,MAAMqC,sBAAsBA,CAACC,KAAc,EAAEzE,iBAAuC,EAAwD;IAC1I,OAAO,IAAI,CAAC8B,gBAAgB,CAAC;MAC3B4C,OAAO,EAAE,KAAK;MACdD,KAAK,EAAEA,KAAK,IAAI,EAAE;MAClBE,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,EAAE5E,iBAAiB,CAAC;EACvB;;EAEA;EACA,MAAM6E,sBAAsBA,CAACJ,KAAa,GAAG,EAAE,EAAEzE,iBAAuC,EAAwD;IAC9I,OAAO,IAAI,CAAC8B,gBAAgB,CAAC;MAC3B2C,KAAK;MACLE,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,EAAE5E,iBAAiB,CAAC;EACvB;;EAEA;EACA,MAAM8E,sBAAsBA,CAACC,MAAc,EAAEhD,OAAsC,EAAE/B,iBAAuC,EAAwD;IAClL,OAAO,IAAI,CAAC8B,gBAAgB,CAAC;MAC3BkD,oBAAoB,EAAED,MAAM;MAC5B,GAAGhD;IACL,CAAC,EAAE/B,iBAAiB,CAAC;EACvB;;EAEA;EACA,MAAMiF,4BAA4BA,CAACC,cAAsB,EAAwD;IAC/G;IACA;IACA,OAAO,IAAI,CAACpD,gBAAgB,CAAC;MAC3B6C,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,uBAAuBA,CAACC,SAAiB,EAAwD;IACrG;IACA;IACA,OAAO,IAAI,CAACtD,gBAAgB,CAAC;MAC3B6C,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMS,cAAcA,CAACC,eAAyB,EAAEtF,iBAAuC,EAA8B;IACnH;IACA;IACA,MAAMuF,QAAQ,GAAGD,eAAe,CAACE,GAAG,CAAC9B,EAAE,IAAI,IAAI,CAACD,UAAU,CAACC,EAAE,EAAE1D,iBAAiB,CAAC,CAAC;IAClF,MAAMyF,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC3B,OAAO;MAAErC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,8BAA8B;MAAEC,IAAI,EAAEf;IAAU,CAAC;EACpF;;EAEA;EACA,MAAMsD,uBAAuBA,CAACL,eAAyB,EAAEtF,iBAAuC,EAA8B;IAC5H;IACA;IACA,MAAMuF,QAAQ,GAAGD,eAAe,CAACE,GAAG,CAAC9B,EAAE,IAAI,IAAI,CAACO,kBAAkB,CAACP,EAAE,EAAE1D,iBAAiB,CAAC,CAAC;IAC1F,MAAMyF,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC3B,OAAO;MAAErC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,uBAAuB;MAAEC,IAAI,EAAEf;IAAU,CAAC;EAC7E;;EAEA;EACA,MAAMuD,yBAAyBA,CAAC5F,iBAAuC,EAKpE;IACD,IAAI;MAAA,IAAA6F,iBAAA,EAAAC,oBAAA;MACF,MAAM,CAACC,cAAc,EAAEC,WAAW,CAAC,GAAG,MAAMP,OAAO,CAACC,GAAG,CAAC,CACtD,IAAI,CAACnC,cAAc,CAACvD,iBAAiB,CAAC,EACtC,IAAI,CAAC8B,gBAAgB,CAAC;QAAE2C,KAAK,EAAE;MAAE,CAAC,EAAEzE,iBAAiB,CAAC,CACvD,CAAC;MAEF,OAAO;QACLiG,KAAK,EAAE,EAAAJ,iBAAA,GAAAG,WAAW,CAAC5C,IAAI,cAAAyC,iBAAA,uBAAhBA,iBAAA,CAAkBK,UAAU,CAACD,KAAK,KAAI,CAAC;QAC9CE,MAAM,EAAE,EAAAL,oBAAA,GAAAC,cAAc,CAAC3C,IAAI,cAAA0C,oBAAA,uBAAnBA,oBAAA,CAAqBM,WAAW,KAAI,CAAC;QAC7CC,KAAK,EAAE,CAAC;QAAE;QACVC,QAAQ,EAAE,CAAC,CAAC;MACd,CAAC;IACH,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACd,OAAO;QACL4C,KAAK,EAAE,CAAC;QACRE,MAAM,EAAE,CAAC;QACTE,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAE;MACZ,CAAC;IACH;EACF;;EAEA;EACA,MAAMC,wBAAwBA,CAACC,aAAsB,EAAExG,iBAAuC,EAI3F;IACD,IAAI;MAAA,IAAAyG,cAAA;MACF,MAAM/D,QAAQ,GAAG,MAAM,IAAI,CAAC8B,sBAAsB,CAAC,EAAE,EAAExE,iBAAiB,CAAC;MACzE,MAAM0G,aAAa,GAAG,EAAAD,cAAA,GAAA/D,QAAQ,CAACU,IAAI,cAAAqD,cAAA,uBAAbA,cAAA,CAAeC,aAAa,KAAI,EAAE;MAExD,IAAIC,gBAAgB,GAAGD,aAAa;MACpC,IAAIF,aAAa,EAAE;QACjBG,gBAAgB,GAAGD,aAAa,CAACE,MAAM,CACrCC,YAAY,IAAI,IAAIC,IAAI,CAACD,YAAY,CAACE,UAAU,CAAC,GAAG,IAAID,IAAI,CAACN,aAAa,CAC5E,CAAC;MACH;MAEA,OAAO;QACLQ,MAAM,EAAEL,gBAAgB,CAACM,MAAM,GAAG,CAAC;QACnCC,KAAK,EAAEP,gBAAgB,CAACM,MAAM;QAC9BP,aAAa,EAAEC;MACjB,CAAC;IACH,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACd,OAAO;QACL2D,MAAM,EAAE,KAAK;QACbE,KAAK,EAAE,CAAC;QACRR,aAAa,EAAE;MACjB,CAAC;IACH;EACF;;EAEA;EACAS,kBAAkBA,CAACN,YAA0B,EAK3C;IACA,OAAO;MACLO,KAAK,EAAEP,YAAY,CAACO,KAAK;MACzBjE,OAAO,EAAE0D,YAAY,CAAC1D,OAAO;MAC7BkE,OAAO,EAAE,IAAI,CAACC,UAAU,CAACT,YAAY,CAACE,UAAU,CAAC;MACjDQ,IAAI,EAAE,IAAI,CAACC,mBAAmB,CAACX,YAAY,CAAC7B,oBAAoB;IAClE,CAAC;EACH;;EAEA;EACQsC,UAAUA,CAACG,UAAkB,EAAU;IAC7C,MAAMC,IAAI,GAAG,IAAIZ,IAAI,CAACW,UAAU,CAAC;IACjC,MAAME,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;IACtB,MAAMc,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGL,IAAI,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,UAAU;IACnB,CAAC,MAAM,IAAIA,aAAa,GAAG,IAAI,EAAE;MAC/B,MAAMI,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;MAC9C,OAAO,GAAGI,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACzD,CAAC,MAAM,IAAIJ,aAAa,GAAG,KAAK,EAAE;MAChC,MAAMK,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC;MAC9C,OAAO,GAAGK,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACnD,CAAC,MAAM,IAAIL,aAAa,GAAG,MAAM,EAAE;MACjC,MAAMM,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC;MAC9C,OAAO,GAAGM,IAAI,OAAOA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IAChD,CAAC,MAAM;MACL,OAAOR,IAAI,CAACS,kBAAkB,CAAC,CAAC;IAClC;EACF;;EAEA;EACQX,mBAAmBA,CAACzC,MAAc,EAAU;IAClD,MAAMqD,KAA6B,GAAG;MACpC,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,UAAU;MACb,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE;IACL,CAAC;IACD,OAAOA,KAAK,CAACrD,MAAM,CAAC,IAAI,cAAc;EACxC;;EAEA;EACAsD,wBAAwBA,CAACC,QAA8C,EAAc;IACnF;IACA;IACA/H,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjD,CAAC;EACH;AACF;;AAEA;AACA,MAAM+H,wBAAwB,SAASzI,mBAAmB,CAAC;EACzD0I,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;EACT;EAEA,MAAM1G,gBAAgBA,CAACC,OAA6B,EAAwD;IAC1GxB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E,OAAO,KAAK,CAACsB,gBAAgB,CAACC,OAAO,EAAE,OAAO,CAAC;EACjD;EAEA,MAAMwB,cAAcA,CAAA,EAAkD;IACpEhD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAC1E,OAAO,KAAK,CAAC+C,cAAc,CAAC,OAAO,CAAC;EACtC;EAEA,MAAME,UAAUA,CAACC,EAAU,EAA8B;IACvDnD,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;IAClF,OAAO,KAAK,CAACiD,UAAU,CAACC,EAAE,EAAE,OAAO,CAAC;EACtC;EAEA,MAAMK,aAAaA,CAAA,EAA+B;IAChDxD,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;IACvF,OAAO,KAAK,CAACuD,aAAa,CAAC,OAAO,CAAC;EACrC;EAEA,MAAME,kBAAkBA,CAACP,EAAU,EAA8B;IAC/DnD,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E,OAAO,KAAK,CAACyD,kBAAkB,CAACP,EAAE,EAAE,OAAO,CAAC;EAC9C;EAEA,MAAMc,sBAAsBA,CAACC,KAAc,EAAwD;IACjGlE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;IAClF,OAAO,KAAK,CAACgE,sBAAsB,CAACC,KAAK,EAAE,OAAO,CAAC;EACrD;EAEA,MAAMI,sBAAsBA,CAACJ,KAAa,GAAG,EAAE,EAAwD;IACrGlE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;IAClF,OAAO,KAAK,CAACqE,sBAAsB,CAACJ,KAAK,EAAE,OAAO,CAAC;EACrD;EAEA,MAAMK,sBAAsBA,CAACC,MAAc,EAAEhD,OAAsC,EAAwD;IACzIxB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;IACnF,OAAO,KAAK,CAACsE,sBAAsB,CAACC,MAAM,EAAEhD,OAAO,EAAE,OAAO,CAAC;EAC/D;EAEA,MAAM6D,yBAAyBA,CAAA,EAAiF;IAC9GrF,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;IACrF,OAAO,KAAK,CAACoF,yBAAyB,CAAC,OAAO,CAAC;EACjD;EAEA,MAAMW,wBAAwBA,CAACC,aAAsB,EAA+E;IAClIjG,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;IACpF,OAAO,KAAK,CAAC+F,wBAAwB,CAACC,aAAa,EAAE,OAAO,CAAC;EAC/D;EAEA,MAAMnB,cAAcA,CAACC,eAAyB,EAA8B;IAC1E/E,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;IACxF,OAAO,KAAK,CAAC6E,cAAc,CAACC,eAAe,EAAE,OAAO,CAAC;EACvD;EAEA,MAAMK,uBAAuBA,CAACL,eAAyB,EAA8B;IACnF/E,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;IACjF,OAAO,KAAK,CAACmF,uBAAuB,CAACL,eAAe,EAAE,OAAO,CAAC;EAChE;AACF;AAEA,MAAMmD,0BAA0B,SAAS3I,mBAAmB,CAAC;EAC3D0I,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;EACT;EAEA,MAAM1G,gBAAgBA,CAACC,OAA6B,EAAwD;IAC1GxB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;IAC/E,OAAO,KAAK,CAACsB,gBAAgB,CAACC,OAAO,EAAE,SAAS,CAAC;EACnD;EAEA,MAAMwB,cAAcA,CAAA,EAAkD;IACpEhD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;IAC9E,OAAO,KAAK,CAAC+C,cAAc,CAAC,SAAS,CAAC;EACxC;EAEA,MAAME,UAAUA,CAACC,EAAU,EAA8B;IACvDnD,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;IACtF,OAAO,KAAK,CAACiD,UAAU,CAACC,EAAE,EAAE,SAAS,CAAC;EACxC;EAEA,MAAMK,aAAaA,CAAA,EAA+B;IAChDxD,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;IAC3F,OAAO,KAAK,CAACuD,aAAa,CAAC,SAAS,CAAC;EACvC;EAEA,MAAME,kBAAkBA,CAACP,EAAU,EAA8B;IAC/DnD,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;IAC/E,OAAO,KAAK,CAACyD,kBAAkB,CAACP,EAAE,EAAE,SAAS,CAAC;EAChD;EAEA,MAAMc,sBAAsBA,CAACC,KAAc,EAAwD;IACjGlE,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;IACtF,OAAO,KAAK,CAACgE,sBAAsB,CAACC,KAAK,EAAE,SAAS,CAAC;EACvD;EAEA,MAAMI,sBAAsBA,CAACJ,KAAa,GAAG,EAAE,EAAwD;IACrGlE,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;IACtF,OAAO,KAAK,CAACqE,sBAAsB,CAACJ,KAAK,EAAE,SAAS,CAAC;EACvD;EAEA,MAAMK,sBAAsBA,CAACC,MAAc,EAAEhD,OAAsC,EAAwD;IACzIxB,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;IACvF,OAAO,KAAK,CAACsE,sBAAsB,CAACC,MAAM,EAAEhD,OAAO,EAAE,SAAS,CAAC;EACjE;EAEA,MAAM6D,yBAAyBA,CAAA,EAAiF;IAC9GrF,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IACzF,OAAO,KAAK,CAACoF,yBAAyB,CAAC,SAAS,CAAC;EACnD;EAEA,MAAMW,wBAAwBA,CAACC,aAAsB,EAA+E;IAClIjG,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;IACxF,OAAO,KAAK,CAAC+F,wBAAwB,CAACC,aAAa,EAAE,SAAS,CAAC;EACjE;EAEA,MAAMnB,cAAcA,CAACC,eAAyB,EAA8B;IAC1E/E,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;IAC5F,OAAO,KAAK,CAAC6E,cAAc,CAACC,eAAe,EAAE,SAAS,CAAC;EACzD;EAEA,MAAMK,uBAAuBA,CAACL,eAAyB,EAA8B;IACnF/E,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;IACrF,OAAO,KAAK,CAACmF,uBAAuB,CAACL,eAAe,EAAE,SAAS,CAAC;EAClE;AACF;AAEA,OAAO,MAAMoD,mBAAmB,GAAG,IAAI5I,mBAAmB,CAAC,CAAC;AAC5D,OAAO,MAAM6I,wBAAwB,GAAG,IAAIJ,wBAAwB,CAAC,CAAC;AACtE,OAAO,MAAMK,0BAA0B,GAAG,IAAIH,0BAA0B,CAAC,CAAC;AAC1E,eAAeC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}