{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 18 6-6-6-6\",\n  key: \"lwmzdw\"\n}], [\"path\", {\n  d: \"M17 6v12\",\n  key: \"1o0aio\"\n}]];\nconst ChevronLast = createLucideIcon(\"chevron-last\", __iconNode);\nexport { __iconNode, ChevronLast as default };\n//# sourceMappingURL=chevron-last.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}