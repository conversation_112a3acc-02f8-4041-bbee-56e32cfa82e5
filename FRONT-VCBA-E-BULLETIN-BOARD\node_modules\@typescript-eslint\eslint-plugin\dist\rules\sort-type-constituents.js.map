{"version": 3, "file": "sort-type-constituents.js", "sourceRoot": "", "sources": ["../../src/rules/sort-type-constituents.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,kCAAoE;AAEpE,IAAK,KAaJ;AAbD,WAAK,KAAK;IACR,oCAA2B,CAAA;IAC3B,8BAAqB,CAAA;IACrB,0BAAiB,CAAA;IACjB,sCAA6B,CAAA;IAC7B,4BAAmB,CAAA;IACnB,4BAAmB,CAAA;IACnB,4BAAmB,CAAA;IACnB,wBAAe,CAAA;IACf,0BAAiB,CAAA;IACjB,8BAAqB,CAAA;IACrB,wBAAe,CAAA;IACf,wBAAe,CAAA;AACjB,CAAC,EAbI,KAAK,KAAL,KAAK,QAaT;AAED,SAAS,QAAQ,CAAC,IAAuB;IACvC,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,sBAAc,CAAC,iBAAiB;YACnC,OAAO,KAAK,CAAC,WAAW,CAAC;QAE3B,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,KAAK,CAAC,QAAQ,CAAC;QAExB,KAAK,sBAAc,CAAC,YAAY;YAC9B,OAAO,KAAK,CAAC,MAAM,CAAC;QAEtB,KAAK,sBAAc,CAAC,kBAAkB;YACpC,OAAO,KAAK,CAAC,YAAY,CAAC;QAE5B,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,kBAAkB;YACpC,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,aAAa;YAC/B,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,aAAa;YAC/B,OAAO,KAAK,CAAC,MAAM,CAAC;QAEtB,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,QAAQ,CAAC;QAExB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,iEAAiE;QACjE,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe;YACjC,0BAA0B;YAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KACnD;AACH,CAAC;AAWD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,+EAA+E;YACjF,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,SAAS,EAAE,4CAA4C;YACvD,cAAc,EAAE,qDAAqD;YACrE,UAAU,EAAE,mDAAmD;SAChE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,kBAAkB,EAAE;wBAClB,WAAW,EAAE,sCAAsC;wBACnD,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EAAE,+BAA+B;wBAC5C,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EAAE,yBAAyB;wBACtC,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;4BACd,IAAI,EAAE,IAAA,mBAAY,EAAC,KAAK,CAAC;yBAC1B;qBACF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,kBAAkB,EAAE,IAAI;YACxB,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE;gBACV,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,YAAY;gBAClB,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,OAAO;aACd;SACF;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;QAC/D,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACvC,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,SAAS,YAAY,CACnB,IAAwD;;YAExD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;gBACxC,MAAM,KAAK,GAAG,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,mCAAI,CAAC,CAAC,CAAC;gBACxD,OAAO;oBACL,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK;oBACrD,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;iBAC/B,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE;oBACvB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;iBAC1B;gBAED,OAAO,CACL,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;oBAChC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzC,MAAM,KAAK,GACT,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,MAAM;oBACzC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBAC3C,OAAO,KAAK,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBAChD,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;oBACjD,IAAI,SAAS,GAAe,WAAW,CAAC;oBACxC,MAAM,IAAI,GAAG;wBACX,IAAI,EAAE,EAAE;wBACR,IAAI,EACF,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;4BAC7C,CAAC,CAAC,cAAc;4BAChB,CAAC,CAAC,OAAO;qBACd,CAAC;oBACF,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,sBAAsB,EAAE;wBAC/D,SAAS,GAAG,gBAAgB,CAAC;wBAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;qBACjC;oBAED,MAAM,GAAG,GAA+B,KAAK,CAAC,EAAE;wBAC9C,MAAM,MAAM,GAAG,aAAa;6BACzB,GAAG,CAAC,CAAC,CAAC,EAAE,CACP,IAAA,kCAA2B,EAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;4BAC3C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gCAC9C,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC;4BAC3C,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG;4BACf,CAAC,CAAC,CAAC,CAAC,IAAI,CACX;6BACA,IAAI,CACH,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAChE,CAAC;wBAEJ,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACzC,CAAC,CAAC;oBACF,OAAO,OAAO,CAAC,MAAM,iBACnB,IAAI;wBACJ,SAAS;wBACT,IAAI,IAGD,CAAC,WAAW;wBACb,CAAC,CAAC;4BACE,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,YAAY;oCACvB,GAAG;iCACJ;6BACF;yBACF;wBACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EACZ,CAAC;iBACJ;aACF;QACH,CAAC;QAED,uCACK,CAAC,kBAAkB,IAAI;YACxB,kBAAkB,CAAC,IAAI;gBACrB,YAAY,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;SACF,CAAC,GACC,CAAC,WAAW,IAAI;YACjB,WAAW,CAAC,IAAI;gBACd,YAAY,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;SACF,CAAC,EACF;IACJ,CAAC;CACF,CAAC,CAAC"}