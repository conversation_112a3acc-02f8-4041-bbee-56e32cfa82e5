{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 19a4 4 0 0 1-2.24-7.32A3.5 3.5 0 0 1 9 6.03V6a3 3 0 1 1 6 0v.04a3.5 3.5 0 0 1 3.24 5.65A4 4 0 0 1 16 19Z\",\n  key: \"oadzkq\"\n}], [\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}]];\nconst TreeDeciduous = createLucideIcon(\"tree-deciduous\", __iconNode);\nexport { __iconNode, TreeDeciduous as default };\n//# sourceMappingURL=tree-deciduous.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}