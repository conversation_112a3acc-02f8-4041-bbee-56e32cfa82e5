{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3\",\n  key: \"1i73f7\"\n}], [\"path\", {\n  d: \"M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3\",\n  key: \"saxlbk\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"M12 14v2\",\n  key: \"8jcxud\"\n}], [\"path\", {\n  d: \"M12 8v2\",\n  key: \"1woqiv\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}]];\nconst FlipHorizontal = createLucideIcon(\"flip-horizontal\", __iconNode);\nexport { __iconNode, FlipHorizontal as default };\n//# sourceMappingURL=flip-horizontal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}