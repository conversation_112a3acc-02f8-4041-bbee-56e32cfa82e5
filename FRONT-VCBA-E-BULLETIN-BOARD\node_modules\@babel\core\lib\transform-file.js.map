{"version": 3, "names": ["_gensync", "data", "require", "_index", "_index2", "fs", "transformFileRunner", "gens<PERSON>", "filename", "opts", "options", "Object", "assign", "config", "loadConfig", "code", "readFile", "run", "transformFile", "args", "errback", "transformFileSync", "sync", "transformFileAsync", "async"], "sources": ["../src/transform-file.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config/index.ts\";\nimport type { InputOptions, ResolvedConfig } from \"./config/index.ts\";\nimport { run } from \"./transformation/index.ts\";\nimport type { FileResult, FileResultCallback } from \"./transformation/index.ts\";\nimport * as fs from \"./gensync-utils/fs.ts\";\n\ntype transformFileBrowserType = typeof import(\"./transform-file-browser\");\ntype transformFileType = typeof import(\"./transform-file\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of transform-file-browser, since this file may be replaced at bundle time with\n// transform-file-browser.\n({}) as any as transformFileBrowserType as transformFileType;\n\nconst transformFileRunner = gensync(function* (\n  filename: string,\n  opts?: InputOptions,\n): Handler<FileResult | null> {\n  const options = { ...opts, filename };\n\n  const config: ResolvedConfig | null = yield* loadConfig(options);\n  if (config === null) return null;\n\n  const code = yield* fs.readFile(filename, \"utf8\");\n  return yield* run(config, code);\n});\n\n// @ts-expect-error TS doesn't detect that this signature is compatible\nexport function transformFile(\n  filename: string,\n  callback: FileResultCallback,\n): void;\nexport function transformFile(\n  filename: string,\n  opts: InputOptions | undefined | null,\n  callback: FileResultCallback,\n): void;\nexport function transformFile(\n  ...args: Parameters<typeof transformFileRunner.errback>\n) {\n  transformFileRunner.errback(...args);\n}\n\nexport function transformFileSync(\n  ...args: Parameters<typeof transformFileRunner.sync>\n) {\n  return transformFileRunner.sync(...args);\n}\nexport function transformFileAsync(\n  ...args: Parameters<typeof transformFileRunner.async>\n) {\n  return transformFileRunner.async(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAAG,EAAA,GAAAH,OAAA;AAQA,CAAC,CAAC,CAAC;AAEH,MAAMI,mBAAmB,GAAGC,SAAMA,CAAC,CAAC,WAClCC,QAAgB,EAChBC,IAAmB,EACS;EAC5B,MAAMC,OAAO,GAAAC,MAAA,CAAAC,MAAA,KAAQH,IAAI;IAAED;EAAQ,EAAE;EAErC,MAAMK,MAA6B,GAAG,OAAO,IAAAC,cAAU,EAACJ,OAAO,CAAC;EAChE,IAAIG,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EAEhC,MAAME,IAAI,GAAG,OAAOV,EAAE,CAACW,QAAQ,CAACR,QAAQ,EAAE,MAAM,CAAC;EACjD,OAAO,OAAO,IAAAS,WAAG,EAACJ,MAAM,EAAEE,IAAI,CAAC;AACjC,CAAC,CAAC;AAYK,SAASG,aAAaA,CAC3B,GAAGC,IAAoD,EACvD;EACAb,mBAAmB,CAACc,OAAO,CAAC,GAAGD,IAAI,CAAC;AACtC;AAEO,SAASE,iBAAiBA,CAC/B,GAAGF,IAAiD,EACpD;EACA,OAAOb,mBAAmB,CAACgB,IAAI,CAAC,GAAGH,IAAI,CAAC;AAC1C;AACO,SAASI,kBAAkBA,CAChC,GAAGJ,IAAkD,EACrD;EACA,OAAOb,mBAAmB,CAACkB,KAAK,CAAC,GAAGL,IAAI,CAAC;AAC3C;AAAC", "ignoreList": []}