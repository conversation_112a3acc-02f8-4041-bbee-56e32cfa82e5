{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z\",\n  key: \"nt11vn\"\n}], [\"path\", {\n  d: \"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18\",\n  key: \"15qc1e\"\n}], [\"path\", {\n  d: \"m2.3 2.3 7.286 7.286\",\n  key: \"1wuzzi\"\n}], [\"circle\", {\n  cx: \"11\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"xmgehs\"\n}]];\nconst PenTool = createLucideIcon(\"pen-tool\", __iconNode);\nexport { __iconNode, PenTool as default };\n//# sourceMappingURL=pen-tool.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}