{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"m8 18 4-4\",\n  key: \"12zab0\"\n}], [\"path\", {\n  d: \"M8 10v8h8\",\n  key: \"tlaukw\"\n}]];\nconst FileAxis3d = createLucideIcon(\"file-axis-3d\", __iconNode);\nexport { __iconNode, FileAxis3d as default };\n//# sourceMappingURL=file-axis-3d.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}