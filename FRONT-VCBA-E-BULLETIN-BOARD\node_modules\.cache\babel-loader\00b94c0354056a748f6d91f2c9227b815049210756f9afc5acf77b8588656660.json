{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Link,useNavigate}from'react-router-dom';import{AdminAuthService}from'../../../services/admin-auth.service';import'./AdminRegister.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminRegister=()=>{const navigate=useNavigate();const[currentStep,setCurrentStep]=useState('registration');const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[formData,setFormData]=useState({firstName:'',lastName:'',middleName:'',suffix:'',email:'',phoneNumber:'',department:'',position:'',gradeLevel:'',password:'',confirmPassword:''});const[otpData,setOtpData]=useState({email:'',otp:''});const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));setError('');};const handleOtpChange=e=>{const{value}=e.target;const cleanValue=value.replace(/\\D/g,'').slice(0,6);setOtpData(prev=>_objectSpread(_objectSpread({},prev),{},{otp:cleanValue}));setError('');};const validateForm=()=>{if(!formData.firstName.trim()){setError('First name is required');return false;}if(!formData.lastName.trim()){setError('Last name is required');return false;}if(!formData.email.trim()){setError('Email is required');return false;}if(!formData.email.includes('@')){setError('Please enter a valid email address');return false;}if(!formData.department.trim()){setError('Department is required');return false;}if(!formData.position.trim()){setError('Position is required');return false;}if(!formData.password){setError('Password is required');return false;}if(formData.password.length<8){setError('Password must be at least 8 characters long');return false;}// Check for uppercase, lowercase, and number\nconst passwordRegex=/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/;if(!passwordRegex.test(formData.password)){setError('Password must contain at least one uppercase letter, one lowercase letter, and one number');return false;}if(formData.password!==formData.confirmPassword){setError('Passwords do not match');return false;}return true;};const handleRegistrationSubmit=async e=>{e.preventDefault();console.log('Form submit started');if(!validateForm()){console.log('Validation failed');return;}setLoading(true);setError('');try{console.log('Calling AdminAuthService with form data:',formData);// Transform form data to match AdminRegistrationData interface\nconst registrationData={email:formData.email,password:formData.password,firstName:formData.firstName,lastName:formData.lastName,middleName:formData.middleName||undefined,suffix:formData.suffix||undefined,phoneNumber:formData.phoneNumber||undefined,department:formData.department||undefined,position:formData.position||undefined,gradeLevel:formData.gradeLevel&&formData.gradeLevel.trim()!==''?formData.gradeLevel.trim():undefined};console.log('Transformed registration data:',registrationData);console.log('Grade level being sent:',registrationData.gradeLevel,'Type:',typeof registrationData.gradeLevel);const response=await AdminAuthService.registerAdmin(registrationData);console.log('API Response:',response);if(response.success){console.log('SUCCESS - Moving to OTP step');setOtpData(prev=>_objectSpread(_objectSpread({},prev),{},{email:formData.email}));setCurrentStep('otp');setSuccess('Registration initiated! Please check your email for the OTP.');}else{console.log('API Error:',response.message);setError(response.message||'Registration failed. Please try again.');}}catch(err){console.error('Network Error:',err);setError('Network error. Please check your connection and try again.');}finally{setLoading(false);}};const handleOtpSubmit=async e=>{e.preventDefault();if(!otpData.otp||otpData.otp.length!==6){setError('Please enter a valid 6-digit OTP');return;}setLoading(true);setError('');try{console.log('Submitting OTP:',otpData);const response=await AdminAuthService.verifyOtp(otpData);console.log('OTP Response:',response);if(response.success){setCurrentStep('success');setSuccess('Account created successfully! Redirecting to login...');setTimeout(()=>{navigate('/admin/login');},3000);}else{setError(response.message||'OTP verification failed. Please try again.');}}catch(err){console.error('OTP Error:',err);setError('Network error. Please check your connection and try again.');}finally{setLoading(false);}};const handleResendOtp=async()=>{setLoading(true);setError('');try{const response=await AdminAuthService.resendOtp(otpData.email);if(response.success){setSuccess('OTP resent successfully! Please check your email.');}else{setError(response.message||'Failed to resend OTP. Please try again.');}}catch(err){console.error('Resend Error:',err);setError('Network error. Please check your connection and try again.');}finally{setLoading(false);}};console.log('CURRENT STEP:',currentStep);console.log('OTP EMAIL:',otpData.email);return/*#__PURE__*/_jsx(\"div\",{className:\"admin-register\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"register-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"register-header\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",className:\"logo\"}),/*#__PURE__*/_jsx(\"h1\",{children:\"Create Admin Account\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Fill in the details to register as an administrator\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u26A0\\uFE0F \",error]})}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"success-message\",children:/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u2705 \",success]})}),currentStep==='registration'&&/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleRegistrationSubmit,className:\"register-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Personal Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"firstName\",children:\"First Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"firstName\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"lastName\",children:\"Last Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"lastName\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"middleName\",children:\"Middle Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"middleName\",name:\"middleName\",value:formData.middleName,onChange:handleInputChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"suffix\",children:\"Suffix\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"suffix\",name:\"suffix\",value:formData.suffix,onChange:handleInputChange})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Contact Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email Address *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"phoneNumber\",children:\"Phone Number\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",id:\"phoneNumber\",name:\"phoneNumber\",value:formData.phoneNumber,onChange:handleInputChange,onInput:e=>{// Allow only numbers\ne.currentTarget.value=e.currentTarget.value.replace(/[^0-9]/g,'');},placeholder:\"09123456789\",maxLength:11})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Professional Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"department\",children:\"Department *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"department\",name:\"department\",value:formData.department,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"position\",children:\"Position *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"position\",name:\"position\",value:formData.position,onChange:handleInputChange,required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"gradeLevel\",children:\"Assigned Grade Level\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"gradeLevel\",name:\"gradeLevel\",value:formData.gradeLevel,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'1rem',outline:'none',transition:'border-color 0.2s',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"System Admin (All Grades)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"11\",children:\"Grade 11\"}),/*#__PURE__*/_jsx(\"option\",{value:\"12\",children:\"Grade 12\"})]}),/*#__PURE__*/_jsx(\"small\",{style:{color:'#6b7280',fontSize:'0.875rem',marginTop:'0.25rem',display:'block'},children:\"Leave empty for system admin access to all grades, or select a specific grade to restrict access.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Security\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,required:true}),/*#__PURE__*/_jsx(\"small\",{className:\"password-hint\",children:\"Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, and one number.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"confirmPassword\",children:\"Confirm Password *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"confirmPassword\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleInputChange,required:true})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-btn\",disabled:loading,children:loading?'Creating Account...':'Create Account'})]}),currentStep==='otp'&&/*#__PURE__*/_jsxs(\"div\",{className:\"otp-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"OTP Verification\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"We've sent a 6-digit code to \",/*#__PURE__*/_jsx(\"strong\",{children:otpData.email})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleOtpSubmit,className:\"otp-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"otp\",children:\"Enter OTP\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"otp\",value:otpData.otp,onChange:handleOtpChange,placeholder:\"000000\",maxLength:6,className:\"otp-input\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-btn\",disabled:loading,children:loading?'Verifying...':'Verify OTP'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"otp-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleResendOtp,className:\"resend-btn\",disabled:loading,children:\"Resend OTP\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setCurrentStep('registration'),className:\"back-btn\",children:\"Back to Registration\"})]})]}),currentStep==='success'&&/*#__PURE__*/_jsxs(\"div\",{className:\"success-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"success-icon\",children:\"\\u2705\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Account Created Successfully!\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Your admin account has been created. You will be redirected to the login page shortly.\"}),/*#__PURE__*/_jsx(Link,{to:\"/admin/login\",className:\"login-link\",children:\"Go to Login\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"register-footer\",children:/*#__PURE__*/_jsxs(\"p\",{children:[\"Already have an account? \",/*#__PURE__*/_jsx(Link,{to:\"/admin/login\",children:\"Sign in here\"})]})})]})});};export default AdminRegister;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}