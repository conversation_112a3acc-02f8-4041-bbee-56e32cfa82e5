{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"path\", {\n  d: \"M3 12h18\",\n  key: \"1i2n21\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"h1oib\"\n}]];\nconst Grid2x2 = createLucideIcon(\"grid-2x2\", __iconNode);\nexport { __iconNode, Grid2x2 as default };\n//# sourceMappingURL=grid-2x2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}