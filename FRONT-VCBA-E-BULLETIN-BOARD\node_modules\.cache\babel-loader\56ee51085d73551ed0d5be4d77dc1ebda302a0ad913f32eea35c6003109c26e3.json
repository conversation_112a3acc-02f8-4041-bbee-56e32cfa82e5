{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\AdminAuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\n// Admin Auth context interface\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AdminAuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\nexport const AdminAuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 AdminAuth - Checking authentication status');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n\n      // First check local storage for authentication data\n      const storedUser = AdminAuthService.getStoredUser();\n      const hasToken = AdminAuthService.isAuthenticated();\n\n      // console.log('🔍 AdminAuth - Local auth data:', {\n      //   hasUser: !!storedUser,\n      //   hasToken: !!hasToken,\n      //   userRole: storedUser?.role,\n      //   userEmail: storedUser?.email\n      // });\n\n      // If we have local data and it's an admin user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'admin') {\n        console.log('✅ AdminAuth - Admin user authenticated from local storage:', storedUser.email);\n        dispatch({\n          type: 'AUTH_SUCCESS',\n          payload: storedUser\n        });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ AdminAuth - No admin user found in local storage or wrong role');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    } catch (error) {\n      console.error('❌ AdminAuth - Auth check failed:', error.message);\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n\n      // Use AdminAuthService for login\n      const response = await AdminAuthService.login(credentials);\n\n      // Verify the user is actually an admin\n      if (response.data.user.role !== 'admin') {\n        throw new Error('Access denied: Admin privileges required');\n      }\n      console.log('✅ AdminAuth - Admin login successful:', response.data.user.email);\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: response.data.user\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Admin login failed. Please try again.';\n      console.error('❌ AdminAuth - Login failed:', errorMessage);\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      console.log('🚪 AdminAuth - Starting admin logout process');\n      await AdminAuthService.logout();\n      console.log('✅ AdminAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ AdminAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 AdminAuth - Clearing admin state');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n\n      // Redirect to admin login\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Register function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await AdminAuthService.registerAdmin(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      return response;\n    } catch (error) {\n      const errorMessage = error.message || 'Admin registration failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await AdminAuthService.verifyOtp(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async email => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await AdminAuthService.resendOtp(email);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 AdminAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 AdminAuth - Unauthorized request detected, logging out admin');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AdminAuth - Component mounted, checking admin auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    userType: 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AdminAuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use admin auth context\n_s(AdminAuthProvider, \"az7Z8nchPpIKydmJ9mbFqt7e/WQ=\");\n_c = AdminAuthProvider;\nexport const useAdminAuth = () => {\n  _s2();\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n};\n_s2(useAdminAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AdminAuthContext;\nvar _c;\n$RefreshReg$(_c, \"AdminAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "useEffect", "AdminAuthService", "jsxDEV", "_jsxDEV", "initialState", "user", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AdminAuthContext", "undefined", "AdminAuth<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "clearError", "checkAuthStatus", "console", "log", "storedUser", "getStoredUser", "hasToken", "role", "email", "message", "login", "credentials", "response", "data", "Error", "errorMessage", "logout", "window", "location", "href", "register", "registerAdmin", "verifyOtp", "resendOtp", "value", "userType", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAdminAuth", "_s2", "context", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/AdminAuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport {\n  AuthState,\n  LoginCredentials,\n  AdminRegistrationData,\n  RegistrationResponse,\n  OtpVerificationData\n} from '../types/auth.types';\n\n// Admin Auth context interface\ninterface AdminAuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n  register: (data: AdminRegistrationData) => Promise<RegistrationResponse>;\n  verifyOtp: (data: OtpVerificationData) => Promise<void>;\n  resendOtp: (email: string) => Promise<void>;\n  clearError: () => void;\n  checkAuthStatus: () => Promise<void>;\n  userType: 'admin';\n}\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: any }\n  | { type: 'AUTH_ERROR'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'CLEAR_ERROR' };\n\n// Reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return { ...state, isLoading: true, error: null };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'CLEAR_ERROR':\n      return { ...state, error: null };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);\n\n// Provider component\nexport const AdminAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 AdminAuth - Checking authentication status');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      // First check local storage for authentication data\n      const storedUser = AdminAuthService.getStoredUser();\n      const hasToken = AdminAuthService.isAuthenticated();\n\n      // console.log('🔍 AdminAuth - Local auth data:', {\n      //   hasUser: !!storedUser,\n      //   hasToken: !!hasToken,\n      //   userRole: storedUser?.role,\n      //   userEmail: storedUser?.email\n      // });\n\n      // If we have local data and it's an admin user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'admin') {\n        console.log('✅ AdminAuth - Admin user authenticated from local storage:', storedUser.email);\n        dispatch({ type: 'AUTH_SUCCESS', payload: storedUser });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ AdminAuth - No admin user found in local storage or wrong role');\n      dispatch({ type: 'AUTH_LOGOUT' });\n\n    } catch (error: any) {\n      console.error('❌ AdminAuth - Auth check failed:', error.message);\n      dispatch({ type: 'AUTH_LOGOUT' });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      // Use AdminAuthService for login\n      const response = await AdminAuthService.login(credentials);\n      \n      // Verify the user is actually an admin\n      if (response.data.user.role !== 'admin') {\n        throw new Error('Access denied: Admin privileges required');\n      }\n\n      console.log('✅ AdminAuth - Admin login successful:', response.data.user.email);\n      dispatch({ type: 'AUTH_SUCCESS', payload: response.data.user });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Admin login failed. Please try again.';\n      console.error('❌ AdminAuth - Login failed:', errorMessage);\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      console.log('🚪 AdminAuth - Starting admin logout process');\n      await AdminAuthService.logout();\n      console.log('✅ AdminAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ AdminAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 AdminAuth - Clearing admin state');\n      dispatch({ type: 'AUTH_LOGOUT' });\n      \n      // Redirect to admin login\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Register function\n  const register = async (data: AdminRegistrationData): Promise<RegistrationResponse> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await AdminAuthService.registerAdmin(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n      return response;\n    } catch (error: any) {\n      const errorMessage = error.message || 'Admin registration failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async (data: OtpVerificationData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await AdminAuthService.verifyOtp(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async (email: string): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await AdminAuthService.resendOtp(email);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 AdminAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 AdminAuth - Unauthorized request detected, logging out admin');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AdminAuth - Component mounted, checking admin auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value: AdminAuthContextType = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    userType: 'admin',\n  };\n\n  return (\n    <AdminAuthContext.Provider value={value}>\n      {children}\n    </AdminAuthContext.Provider>\n  );\n};\n\n// Hook to use admin auth context\nexport const useAdminAuth = (): AdminAuthContextType => {\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n};\n\nexport default AdminAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC5F,SAASC,gBAAgB,QAAQ,gCAAgC;;AASjE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;;AASA;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QAAE,GAAGF,KAAK;QAAEH,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC;IACnD,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAEM,MAAM,CAACE,OAAO;QACpBP,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,YAAY;MACf,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGE,KAAK;QAAEH,SAAS,EAAEI,MAAM,CAACE;MAAQ,CAAC;IAChD,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGH,KAAK;QAAEF,KAAK,EAAE;MAAK,CAAC;IAClC;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,gBAAgB,gBAAGlB,aAAa,CAAmCmB,SAAS,CAAC;;AAEnF;AACA,OAAO,MAAMC,iBAA0D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC1F,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGrB,UAAU,CAACW,WAAW,EAAEL,YAAY,CAAC;;EAE/D;EACA,MAAMgB,UAAU,GAAGrB,WAAW,CAAC,MAAM;IACnCoB,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,eAAe,GAAGtB,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFuB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DJ,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;;MAEhD;MACA,MAAMW,UAAU,GAAGvB,gBAAgB,CAACwB,aAAa,CAAC,CAAC;MACnD,MAAMC,QAAQ,GAAGzB,gBAAgB,CAACK,eAAe,CAAC,CAAC;;MAEnD;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAIkB,UAAU,IAAIE,QAAQ,IAAIF,UAAU,CAACG,IAAI,KAAK,OAAO,EAAE;QACzDL,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEC,UAAU,CAACI,KAAK,CAAC;QAC3FT,QAAQ,CAAC;UAAEP,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAEW;QAAW,CAAC,CAAC;QACvD;MACF;;MAEA;MACAF,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/EJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IAEnC,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBc,OAAO,CAACd,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAACqB,OAAO,CAAC;MAChEV,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkB,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFZ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;;MAEhC;MACA,MAAMoB,QAAQ,GAAG,MAAM/B,gBAAgB,CAAC6B,KAAK,CAACC,WAAW,CAAC;;MAE1D;MACA,IAAIC,QAAQ,CAACC,IAAI,CAAC5B,IAAI,CAACsB,IAAI,KAAK,OAAO,EAAE;QACvC,MAAM,IAAIO,KAAK,CAAC,0CAA0C,CAAC;MAC7D;MAEAZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAES,QAAQ,CAACC,IAAI,CAAC5B,IAAI,CAACuB,KAAK,CAAC;MAC9ET,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEmB,QAAQ,CAACC,IAAI,CAAC5B;MAAK,CAAC,CAAC;IACjE,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnB,MAAM2B,YAAY,GAAG3B,KAAK,CAACqB,OAAO,IAAI,uCAAuC;MAC7EP,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAE2B,YAAY,CAAC;MAC1DhB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACvD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM4B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,MAAMtB,gBAAgB,CAACmC,MAAM,CAAC,CAAC;MAC/Bd,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRc,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;;MAEjC;MACAyB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;IACvC;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOP,IAA2B,IAAoC;IACrF,IAAI;MACFd,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMoB,QAAQ,GAAG,MAAM/B,gBAAgB,CAACwC,aAAa,CAACR,IAAI,CAAC;MAE3Dd,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjD,OAAOmB,QAAQ;IACjB,CAAC,CAAC,OAAOxB,KAAU,EAAE;MACnB,MAAM2B,YAAY,GAAG3B,KAAK,CAACqB,OAAO,IAAI,8CAA8C;MACpFV,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACvD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMkC,SAAS,GAAG,MAAOT,IAAyB,IAAoB;IACpE,IAAI;MACFd,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMX,gBAAgB,CAACyC,SAAS,CAACT,IAAI,CAAC;MAEtCd,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM2B,YAAY,GAAG3B,KAAK,CAACqB,OAAO,IAAI,4CAA4C;MAClFV,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACvD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAOf,KAAa,IAAoB;IACxD,IAAI;MACFT,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMX,gBAAgB,CAAC0C,SAAS,CAACf,KAAK,CAAC;MAEvCT,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM2B,YAAY,GAAG3B,KAAK,CAACqB,OAAO,IAAI,yCAAyC;MAC/EV,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACvD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACAR,SAAS,CAAC,MAAM;IACdsB,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;IACxF;IACA;IACA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACdsB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3EF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMuB,KAA2B,GAAG;IAClC,GAAGlC,KAAK;IACRoB,KAAK;IACLM,MAAM;IACNI,QAAQ;IACRE,SAAS;IACTC,SAAS;IACTvB,UAAU;IACVC,eAAe;IACfwB,QAAQ,EAAE;EACZ,CAAC;EAED,oBACE1C,OAAA,CAACW,gBAAgB,CAACgC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EACrCA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;;AAED;AAAAhC,EAAA,CAnKaF,iBAA0D;AAAAmC,EAAA,GAA1DnC,iBAA0D;AAoKvE,OAAO,MAAMoC,YAAY,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EACtD,MAAMC,OAAO,GAAGzD,UAAU,CAACiB,gBAAgB,CAAC;EAC5C,IAAIwC,OAAO,KAAKvC,SAAS,EAAE;IACzB,MAAM,IAAImB,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EACA,OAAOoB,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAQzB,eAAetC,gBAAgB;AAAC,IAAAqC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}