{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M16 22h2a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3\",\n  key: \"37hlfg\"\n}], [\"path\", {\n  d: \"M8 14v2.2l1.6 1\",\n  key: \"6m4bie\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"10v15b\"\n}]];\nconst FileClock = createLucideIcon(\"file-clock\", __iconNode);\nexport { __iconNode, FileClock as default };\n//# sourceMappingURL=file-clock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}