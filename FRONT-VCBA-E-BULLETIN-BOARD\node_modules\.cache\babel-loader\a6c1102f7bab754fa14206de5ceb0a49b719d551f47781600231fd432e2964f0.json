{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 17h.01\",\n  key: \"nbq80n\"\n}], [\"path\", {\n  d: \"M10 7v6\",\n  key: \"nne03l\"\n}], [\"path\", {\n  d: \"M14 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2\",\n  key: \"1m83kb\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M6 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2\",\n  key: \"h8lgfh\"\n}]];\nconst BatteryWarning = createLucideIcon(\"battery-warning\", __iconNode);\nexport { __iconNode, BatteryWarning as default };\n//# sourceMappingURL=battery-warning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}