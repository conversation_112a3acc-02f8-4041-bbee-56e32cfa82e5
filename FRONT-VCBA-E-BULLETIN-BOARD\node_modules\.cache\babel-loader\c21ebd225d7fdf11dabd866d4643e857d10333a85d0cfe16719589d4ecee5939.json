{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 14 5-5-5-5\",\n  key: \"12vg1m\"\n}], [\"path\", {\n  d: \"M20 9H9.5A5.5 5.5 0 0 0 4 14.5A5.5 5.5 0 0 0 9.5 20H13\",\n  key: \"6uklza\"\n}]];\nconst Redo2 = createLucideIcon(\"redo-2\", __iconNode);\nexport { __iconNode, Redo2 as default };\n//# sourceMappingURL=redo-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}