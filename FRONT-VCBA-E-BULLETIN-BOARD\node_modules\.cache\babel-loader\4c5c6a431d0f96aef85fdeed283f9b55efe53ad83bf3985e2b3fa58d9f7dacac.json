{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 17H7A5 5 0 0 1 7 7\",\n  key: \"10o201\"\n}], [\"path\", {\n  d: \"M15 7h2a5 5 0 0 1 4 8\",\n  key: \"1d3206\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"12\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"rvw6j4\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst Link2Off = createLucideIcon(\"link-2-off\", __iconNode);\nexport { __iconNode, Link2Off as default };\n//# sourceMappingURL=link-2-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}