{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M14.83 14.83a4 4 0 1 1 0-5.66\",\n  key: \"1i56pz\"\n}]];\nconst Copyright = createLucideIcon(\"copyright\", __iconNode);\nexport { __iconNode, Copyright as default };\n//# sourceMappingURL=copyright.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}