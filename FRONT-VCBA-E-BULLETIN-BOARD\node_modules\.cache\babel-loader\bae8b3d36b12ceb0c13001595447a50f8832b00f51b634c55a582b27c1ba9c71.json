{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"path\", {\n  d: \"M17 10v3a1 1 0 0 0 1 1h3\",\n  key: \"tj5zdr\"\n}], [\"path\", {\n  d: \"M21 10v8\",\n  key: \"1kdml4\"\n}], [\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}]];\nconst Heading4 = createLucideIcon(\"heading-4\", __iconNode);\nexport { __iconNode, Heading4 as default };\n//# sourceMappingURL=heading-4.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}