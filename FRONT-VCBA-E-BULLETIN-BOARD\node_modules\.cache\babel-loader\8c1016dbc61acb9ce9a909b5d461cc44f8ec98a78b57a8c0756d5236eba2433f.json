{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { announcementService } from '../services';\nimport { adminAnnouncementServiceWithToken, studentAnnouncementServiceWithToken } from '../services/announcementService';\nimport { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\n// Hook for managing announcements\nexport const useAnnouncements = (initialFilters, useAdminService = false) => {\n  _s();\n  const [announcements, setAnnouncements] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [pagination, setPagination] = useState({\n    page: 1,\n    totalPages: 0,\n    total: 0,\n    hasNext: false,\n    hasPrev: false\n  });\n  const [filters, setFilters] = useState(initialFilters || {\n    page: 1,\n    limit: 20,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Track current user context to detect changes\n  const currentUserContextRef = useRef('');\n\n  // Choose the appropriate service based on the parameter - use role-specific services with proper tokens\n  const service = useAdminService ? adminAnnouncementServiceWithToken : studentAnnouncementServiceWithToken;\n\n  // Function to get current user context identifier\n  const getCurrentUserContext = useCallback(() => {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n    // Create a unique identifier for the current user context\n    // Priority: if both tokens exist, determine which one is currently active\n    // by checking which service we're using\n    if (useAdminService && adminToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (!useAdminService && studentToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    } else if (adminToken && !studentToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (studentToken && !adminToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    }\n    return 'anonymous';\n  }, [useAdminService]);\n\n  // Function to clear cache when user context changes\n  const clearCacheIfUserChanged = useCallback(() => {\n    const currentContext = getCurrentUserContext();\n    if (currentUserContextRef.current && currentUserContextRef.current !== currentContext) {\n      console.log('🔄 User context changed, clearing announcement cache', {\n        previous: currentUserContextRef.current,\n        current: currentContext\n      });\n      // Clear all cached data when user context changes\n      setAnnouncements([]);\n      setPagination({\n        page: 1,\n        totalPages: 0,\n        total: 0,\n        hasNext: false,\n        hasPrev: false\n      });\n      setError(undefined);\n    }\n    currentUserContextRef.current = currentContext;\n  }, [getCurrentUserContext]);\n  const fetchAnnouncements = useCallback(async () => {\n    try {\n      // Clear cache if user context changed\n      clearCacheIfUserChanged();\n      setLoading(true);\n      setError(undefined);\n      console.log('🔍 Fetching announcements with context:', {\n        useAdminService,\n        currentContext: getCurrentUserContext(),\n        filters\n      });\n      const response = await service.getAnnouncements(filters);\n      if (response.success && response.data) {\n        var _response$data$announ;\n        console.log('✅ Announcements fetched successfully:', {\n          count: ((_response$data$announ = response.data.announcements) === null || _response$data$announ === void 0 ? void 0 : _response$data$announ.length) || 0,\n          userContext: getCurrentUserContext()\n        });\n        setAnnouncements(response.data.announcements || []);\n        setPagination(response.data.pagination);\n      } else {\n        setError(response.message || 'Failed to fetch announcements');\n      }\n    } catch (err) {\n      console.error('❌ Error fetching announcements:', err);\n      setError(err.message || 'An error occurred while fetching announcements');\n    } finally {\n      setLoading(false);\n    }\n  }, [JSON.stringify(filters), useAdminService, clearCacheIfUserChanged, getCurrentUserContext, service]);\n  const refresh = useCallback(async () => {\n    await fetchAnnouncements();\n  }, [fetchAnnouncements]);\n  const createAnnouncement = useCallback(async data => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      // Type assertion based on service type - admin service supports FormData\n      const response = useAdminService ? await service.createAnnouncement(data) : await service.createAnnouncement(data);\n      if (response.success) {\n        // Refresh the list to get the new announcement\n        await fetchAnnouncements();\n      } else {\n        throw new Error(response.message || 'gago Failed to create announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while creating announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchAnnouncements, service, useAdminService]);\n  const updateAnnouncement = useCallback(async (id, data) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      // Type assertion based on service type - admin service supports FormData\n      const response = useAdminService ? await service.updateAnnouncement(id, data) : await service.updateAnnouncement(id, data);\n      if (response.success && response.data) {\n        // Update the announcement in the local state\n        setAnnouncements(prev => prev.map(announcement => {\n          var _response$data;\n          return announcement.announcement_id === id ? {\n            ...announcement,\n            ...((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.announcement)\n          } : announcement;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to update announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while updating announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service, useAdminService]);\n  const deleteAnnouncement = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.deleteAnnouncement(id);\n      if (response.success) {\n        // Remove the announcement from local state\n        setAnnouncements(prev => prev.filter(announcement => announcement.announcement_id !== id));\n      } else {\n        throw new Error(response.message || 'Failed to delete announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while deleting announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n  const publishAnnouncement = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.publishAnnouncement(id);\n      if (response.success && response.data) {\n        // Update the announcement status in local state\n        setAnnouncements(prev => prev.map(announcement => {\n          var _response$data2;\n          return announcement.announcement_id === id ? {\n            ...announcement,\n            ...((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.announcement)\n          } : announcement;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to publish announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while publishing announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n  const unpublishAnnouncement = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await service.unpublishAnnouncement(id);\n      if (response.success && response.data) {\n        // Update the announcement status in local state\n        setAnnouncements(prev => prev.map(announcement => {\n          var _response$data3;\n          return announcement.announcement_id === id ? {\n            ...announcement,\n            ...((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.announcement)\n          } : announcement;\n        }));\n      } else {\n        throw new Error(response.message || 'Failed to unpublish announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while unpublishing announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n  const likeAnnouncement = useCallback(async (id, reactionId = 1) => {\n    try {\n      setError(undefined);\n      const response = await service.addReaction(id, reactionId);\n      if (response.success) {\n        // Update the announcement reaction in local state\n        setAnnouncements(prev => prev.map(announcement => announcement.announcement_id === id ? {\n          ...announcement,\n          reaction_count: (announcement.reaction_count || 0) + (announcement.user_reaction ? 0 : 1),\n          user_reaction: {\n            reaction_id: reactionId,\n            reaction_name: 'like',\n            reaction_emoji: '❤️'\n          }\n        } : announcement));\n      } else {\n        throw new Error(response.message || 'Failed to like announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while liking announcement');\n      throw err;\n    }\n  }, [service]);\n  const unlikeAnnouncement = useCallback(async id => {\n    try {\n      setError(undefined);\n      const response = await service.removeReaction(id);\n      if (response.success) {\n        // Update the announcement reaction in local state\n        setAnnouncements(prev => prev.map(announcement => announcement.announcement_id === id ? {\n          ...announcement,\n          reaction_count: Math.max((announcement.reaction_count || 0) - (announcement.user_reaction ? 1 : 0), 0),\n          user_reaction: undefined\n        } : announcement));\n      } else {\n        throw new Error(response.message || 'Failed to unlike announcement');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while unliking announcement');\n      throw err;\n    }\n  }, [service]);\n\n  // Update filters and refetch\n  const updateFilters = useCallback(newFilters => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters\n    }));\n  }, []);\n\n  // Force refresh when service type changes (admin vs student)\n  useEffect(() => {\n    console.log('🔄 Service type changed, forcing refresh:', {\n      useAdminService\n    });\n    clearCacheIfUserChanged();\n    fetchAnnouncements();\n  }, [useAdminService, clearCacheIfUserChanged]);\n  useEffect(() => {\n    fetchAnnouncements();\n  }, [fetchAnnouncements]);\n  return {\n    announcements,\n    loading,\n    error,\n    pagination,\n    filters,\n    setFilters: updateFilters,\n    refresh,\n    createAnnouncement,\n    updateAnnouncement,\n    deleteAnnouncement,\n    publishAnnouncement,\n    unpublishAnnouncement,\n    likeAnnouncement,\n    unlikeAnnouncement\n  };\n};\n\n// Hook for managing categories\n_s(useAnnouncements, \"9qyghDczvtauuMkfUQQIQm9HCBQ=\");\nexport const useCategories = () => {\n  _s2();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await announcementService.getCategories();\n      if (response.success && response.data) {\n        setCategories(response.data.categories);\n      } else {\n        setError(response.message || 'Failed to fetch categories');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Hook for managing hierarchical categories (categories with subcategories)\n_s2(useCategories, \"3XxIpEdDGVmVVv8EaSr/Mpt+rFo=\");\nexport const useHierarchicalCategories = () => {\n  _s3();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      console.log('🔧 Fetching categories with subcategories from API');\n\n      // Use the working hierarchical endpoint\n      const categoriesResponse = await announcementService.getCategoriesWithSubcategories();\n      if (categoriesResponse.success && categoriesResponse.data) {\n        const categories = categoriesResponse.data.categories;\n\n        // Use real subcategories from API\n        setCategories(categories);\n        console.log('✅ Categories loaded with real subcategories from API:', {\n          count: categories.length,\n          categoriesWithSubcategories: categories.filter(cat => cat.subcategories && cat.subcategories.length > 0).length,\n          categories: categories.map(cat => {\n            var _cat$subcategories;\n            return {\n              id: cat.category_id,\n              name: cat.name,\n              subcategoriesCount: ((_cat$subcategories = cat.subcategories) === null || _cat$subcategories === void 0 ? void 0 : _cat$subcategories.length) || 0\n            };\n          })\n        });\n      } else {\n        setError('Failed to fetch categories');\n      }\n    } catch (err) {\n      console.error('Error fetching categories:', err);\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Hook for managing reaction types\n_s3(useHierarchicalCategories, \"3XxIpEdDGVmVVv8EaSr/Mpt+rFo=\");\nexport const useReactionTypes = () => {\n  _s4();\n  const [reactionTypes, setReactionTypes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const fetchReactionTypes = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await announcementService.getReactionTypes();\n      if (response.success && response.data) {\n        setReactionTypes(response.data.reactionTypes);\n      } else {\n        setError(response.message || 'Failed to fetch reaction types');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching reaction types');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  const refresh = useCallback(async () => {\n    await fetchReactionTypes();\n  }, [fetchReactionTypes]);\n  useEffect(() => {\n    fetchReactionTypes();\n  }, [fetchReactionTypes]);\n  return {\n    reactionTypes,\n    loading,\n    error,\n    refresh\n  };\n};\n_s4(useReactionTypes, \"MidrpPPVMbdgfINIjpgrTQsqqBc=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "announcementService", "adminAnnouncementServiceWithToken", "studentAnnouncementServiceWithToken", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "useAnnouncements", "initialFilters", "useAdminService", "_s", "announcements", "setAnnouncements", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "page", "totalPages", "total", "hasNext", "has<PERSON>rev", "filters", "setFilters", "limit", "sort_by", "sort_order", "currentUserContextRef", "service", "getCurrentUserContext", "adminToken", "localStorage", "getItem", "studentToken", "substring", "clearCacheIfUserChanged", "currentContext", "current", "console", "log", "previous", "undefined", "fetchAnnouncements", "response", "getAnnouncements", "success", "data", "_response$data$announ", "count", "length", "userContext", "message", "err", "JSON", "stringify", "refresh", "createAnnouncement", "Error", "updateAnnouncement", "id", "prev", "map", "announcement", "_response$data", "announcement_id", "deleteAnnouncement", "filter", "publishAnnouncement", "_response$data2", "unpublishAnnouncement", "_response$data3", "likeAnnouncement", "reactionId", "addReaction", "reaction_count", "user_reaction", "reaction_id", "reaction_name", "reaction_emoji", "unlikeAnnouncement", "removeReaction", "Math", "max", "updateFilters", "newFilters", "useCategories", "_s2", "categories", "setCategories", "fetchCategories", "getCategories", "useHierarchicalCategories", "_s3", "categoriesResponse", "getCategoriesWithSubcategories", "categoriesWithSubcategories", "cat", "subcategories", "_cat$subcategories", "category_id", "name", "subcategoriesCount", "useReactionTypes", "_s4", "reactionTypes", "setReactionTypes", "fetchReactionTypes", "getReactionTypes"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useAnnouncements.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport { announcementService, adminAnnouncementService } from '../services';\nimport {\n  adminAnnouncementServiceWithToken,\n  studentAnnouncementServiceWithToken\n} from '../services/announcementService';\nimport { ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../config/constants';\nimport type { Subcategory } from '../services/announcementService';\nimport type {\n  Announcement,\n  CreateAnnouncementData,\n  UpdateAnnouncementData,\n  AnnouncementFilters,\n  UseAnnouncementsReturn,\n  Category,\n  ReactionType,\n  UseCategoriesReturn,\n  UseHierarchicalCategoriesReturn,\n  UseReactionTypesReturn\n} from '../types/announcement.types';\n\n// Hook for managing announcements\nexport const useAnnouncements = (initialFilters?: AnnouncementFilters, useAdminService: boolean = false): UseAnnouncementsReturn => {\n  const [announcements, setAnnouncements] = useState<Announcement[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [pagination, setPagination] = useState({\n    page: 1,\n    totalPages: 0,\n    total: 0,\n    hasNext: false,\n    hasPrev: false\n  });\n  const [filters, setFilters] = useState<AnnouncementFilters>(initialFilters || {\n    page: 1,\n    limit: 20,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  });\n\n  // Track current user context to detect changes\n  const currentUserContextRef = useRef<string>('');\n\n  // Choose the appropriate service based on the parameter - use role-specific services with proper tokens\n  const service = useAdminService ? adminAnnouncementServiceWithToken : studentAnnouncementServiceWithToken;\n\n  // Function to get current user context identifier\n  const getCurrentUserContext = useCallback(() => {\n    const adminToken = localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n    const studentToken = localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n    // Create a unique identifier for the current user context\n    // Priority: if both tokens exist, determine which one is currently active\n    // by checking which service we're using\n    if (useAdminService && adminToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (!useAdminService && studentToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    } else if (adminToken && !studentToken) {\n      return `admin:${adminToken.substring(0, 10)}`;\n    } else if (studentToken && !adminToken) {\n      return `student:${studentToken.substring(0, 10)}`;\n    }\n    return 'anonymous';\n  }, [useAdminService]);\n\n  // Function to clear cache when user context changes\n  const clearCacheIfUserChanged = useCallback(() => {\n    const currentContext = getCurrentUserContext();\n    if (currentUserContextRef.current && currentUserContextRef.current !== currentContext) {\n      console.log('🔄 User context changed, clearing announcement cache', {\n        previous: currentUserContextRef.current,\n        current: currentContext\n      });\n      // Clear all cached data when user context changes\n      setAnnouncements([]);\n      setPagination({\n        page: 1,\n        totalPages: 0,\n        total: 0,\n        hasNext: false,\n        hasPrev: false\n      });\n      setError(undefined);\n    }\n    currentUserContextRef.current = currentContext;\n  }, [getCurrentUserContext]);\n\n  const fetchAnnouncements = useCallback(async () => {\n    try {\n      // Clear cache if user context changed\n      clearCacheIfUserChanged();\n\n      setLoading(true);\n      setError(undefined);\n\n      console.log('🔍 Fetching announcements with context:', {\n        useAdminService,\n        currentContext: getCurrentUserContext(),\n        filters\n      });\n\n      const response = await service.getAnnouncements(filters);\n\n      if (response.success && response.data) {\n        console.log('✅ Announcements fetched successfully:', {\n          count: response.data.announcements?.length || 0,\n          userContext: getCurrentUserContext()\n        });\n        setAnnouncements(response.data.announcements || []);\n        setPagination(response.data.pagination);\n      } else {\n        setError(response.message || 'Failed to fetch announcements');\n      }\n    } catch (err: any) {\n      console.error('❌ Error fetching announcements:', err);\n      setError(err.message || 'An error occurred while fetching announcements');\n    } finally {\n      setLoading(false);\n    }\n  }, [JSON.stringify(filters), useAdminService, clearCacheIfUserChanged, getCurrentUserContext, service]);\n\n  const refresh = useCallback(async () => {\n    await fetchAnnouncements();\n  }, [fetchAnnouncements]);\n\n  const createAnnouncement = useCallback(async (data: CreateAnnouncementData | FormData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      // Type assertion based on service type - admin service supports FormData\n      const response = useAdminService\n        ? await (service as any).createAnnouncement(data)\n        : await service.createAnnouncement(data as CreateAnnouncementData);\n\n      if (response.success) {\n        // Refresh the list to get the new announcement\n        await fetchAnnouncements();\n      } else {\n        throw new Error(response.message || 'gago Failed to create announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchAnnouncements, service, useAdminService]);\n\n  const updateAnnouncement = useCallback(async (id: number, data: UpdateAnnouncementData | FormData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      // Type assertion based on service type - admin service supports FormData\n      const response = useAdminService\n        ? await (service as any).updateAnnouncement(id, data)\n        : await service.updateAnnouncement(id, data as UpdateAnnouncementData);\n\n      if (response.success && response.data) {\n        // Update the announcement in the local state\n        setAnnouncements(prev =>\n          prev.map(announcement =>\n            announcement.announcement_id === id\n              ? { ...announcement, ...response.data?.announcement }\n              : announcement\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to update announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while updating announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service, useAdminService]);\n\n  const deleteAnnouncement = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await service.deleteAnnouncement(id);\n      \n      if (response.success) {\n        // Remove the announcement from local state\n        setAnnouncements(prev => \n          prev.filter(announcement => announcement.announcement_id !== id)\n        );\n      } else {\n        throw new Error(response.message || 'Failed to delete announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while deleting announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n\n  const publishAnnouncement = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await service.publishAnnouncement(id);\n      \n      if (response.success && response.data) {\n        // Update the announcement status in local state\n        setAnnouncements(prev =>\n          prev.map(announcement =>\n            announcement.announcement_id === id\n              ? { ...announcement, ...response.data?.announcement }\n              : announcement\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to publish announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while publishing announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n\n  const unpublishAnnouncement = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await service.unpublishAnnouncement(id);\n\n      if (response.success && response.data) {\n        // Update the announcement status in local state\n        setAnnouncements(prev =>\n          prev.map(announcement =>\n            announcement.announcement_id === id\n              ? { ...announcement, ...response.data?.announcement }\n              : announcement\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to unpublish announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while unpublishing announcement');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [service]);\n\n  const likeAnnouncement = useCallback(async (id: number, reactionId: number = 1) => {\n    try {\n      setError(undefined);\n\n      const response = await service.addReaction(id, reactionId);\n\n      if (response.success) {\n        // Update the announcement reaction in local state\n        setAnnouncements(prev =>\n          prev.map(announcement =>\n            announcement.announcement_id === id\n              ? {\n                  ...announcement,\n                  reaction_count: (announcement.reaction_count || 0) + (announcement.user_reaction ? 0 : 1),\n                  user_reaction: { reaction_id: reactionId, reaction_name: 'like', reaction_emoji: '❤️' }\n                }\n              : announcement\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to like announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while liking announcement');\n      throw err;\n    }\n  }, [service]);\n\n  const unlikeAnnouncement = useCallback(async (id: number) => {\n    try {\n      setError(undefined);\n\n      const response = await service.removeReaction(id);\n\n      if (response.success) {\n        // Update the announcement reaction in local state\n        setAnnouncements(prev =>\n          prev.map(announcement =>\n            announcement.announcement_id === id\n              ? {\n                  ...announcement,\n                  reaction_count: Math.max((announcement.reaction_count || 0) - (announcement.user_reaction ? 1 : 0), 0),\n                  user_reaction: undefined\n                }\n              : announcement\n          )\n        );\n      } else {\n        throw new Error(response.message || 'Failed to unlike announcement');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while unliking announcement');\n      throw err;\n    }\n  }, [service]);\n\n  // Update filters and refetch\n  const updateFilters = useCallback((newFilters: AnnouncementFilters) => {\n    setFilters(prev => ({ ...prev, ...newFilters }));\n  }, []);\n\n  // Force refresh when service type changes (admin vs student)\n  useEffect(() => {\n    console.log('🔄 Service type changed, forcing refresh:', { useAdminService });\n    clearCacheIfUserChanged();\n    fetchAnnouncements();\n  }, [useAdminService, clearCacheIfUserChanged]);\n\n  useEffect(() => {\n    fetchAnnouncements();\n  }, [fetchAnnouncements]);\n\n  return {\n    announcements,\n    loading,\n    error,\n    pagination,\n    filters,\n    setFilters: updateFilters,\n    refresh,\n    createAnnouncement,\n    updateAnnouncement,\n    deleteAnnouncement,\n    publishAnnouncement,\n    unpublishAnnouncement,\n    likeAnnouncement,\n    unlikeAnnouncement\n  };\n};\n\n// Hook for managing categories\nexport const useCategories = (): UseCategoriesReturn => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await announcementService.getCategories();\n\n      if (response.success && response.data) {\n        setCategories(response.data.categories);\n      } else {\n        setError(response.message || 'Failed to fetch categories');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n\n\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Hook for managing hierarchical categories (categories with subcategories)\nexport const useHierarchicalCategories = (): UseHierarchicalCategoriesReturn => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      console.log('🔧 Fetching categories with subcategories from API');\n\n      // Use the working hierarchical endpoint\n      const categoriesResponse = await announcementService.getCategoriesWithSubcategories();\n\n      if (categoriesResponse.success && categoriesResponse.data) {\n        const categories = categoriesResponse.data.categories;\n\n        // Use real subcategories from API\n        setCategories(categories);\n        console.log('✅ Categories loaded with real subcategories from API:', {\n          count: categories.length,\n          categoriesWithSubcategories: categories.filter(cat => cat.subcategories && cat.subcategories.length > 0).length,\n          categories: categories.map(cat => ({\n            id: cat.category_id,\n            name: cat.name,\n            subcategoriesCount: cat.subcategories?.length || 0\n          }))\n        });\n\n      } else {\n        setError('Failed to fetch categories');\n      }\n    } catch (err: any) {\n      console.error('Error fetching categories:', err);\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Hook for managing reaction types\nexport const useReactionTypes = (): UseReactionTypesReturn => {\n  const [reactionTypes, setReactionTypes] = useState<ReactionType[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const fetchReactionTypes = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await announcementService.getReactionTypes();\n      \n      if (response.success && response.data) {\n        setReactionTypes(response.data.reactionTypes);\n      } else {\n        setError(response.message || 'Failed to fetch reaction types');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching reaction types');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const refresh = useCallback(async () => {\n    await fetchReactionTypes();\n  }, [fetchReactionTypes]);\n\n  useEffect(() => {\n    fetchReactionTypes();\n  }, [fetchReactionTypes]);\n\n  return {\n    reactionTypes,\n    loading,\n    error,\n    refresh\n  };\n};\n"], "mappings": ";;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAChE,SAASC,mBAAmB,QAAkC,aAAa;AAC3E,SACEC,iCAAiC,EACjCC,mCAAmC,QAC9B,iCAAiC;AACxC,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,qBAAqB;AAelF;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,cAAoC,EAAEC,eAAwB,GAAG,KAAK,KAA6B;EAAAC,EAAA;EAClI,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC;IAC3CqB,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,CAAC;IACbC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAsBU,cAAc,IAAI;IAC5EW,IAAI,EAAE,CAAC;IACPO,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,qBAAqB,GAAG5B,MAAM,CAAS,EAAE,CAAC;;EAEhD;EACA,MAAM6B,OAAO,GAAGrB,eAAe,GAAGN,iCAAiC,GAAGC,mCAAmC;;EAEzG;EACA,MAAM2B,qBAAqB,GAAG/B,WAAW,CAAC,MAAM;IAC9C,MAAMgC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC7B,oBAAoB,CAAC;IAC7D,MAAM8B,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC5B,sBAAsB,CAAC;;IAEjE;IACA;IACA;IACA,IAAIG,eAAe,IAAIuB,UAAU,EAAE;MACjC,OAAO,SAASA,UAAU,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC/C,CAAC,MAAM,IAAI,CAAC3B,eAAe,IAAI0B,YAAY,EAAE;MAC3C,OAAO,WAAWA,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IACnD,CAAC,MAAM,IAAIJ,UAAU,IAAI,CAACG,YAAY,EAAE;MACtC,OAAO,SAASH,UAAU,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC/C,CAAC,MAAM,IAAID,YAAY,IAAI,CAACH,UAAU,EAAE;MACtC,OAAO,WAAWG,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IACnD;IACA,OAAO,WAAW;EACpB,CAAC,EAAE,CAAC3B,eAAe,CAAC,CAAC;;EAErB;EACA,MAAM4B,uBAAuB,GAAGrC,WAAW,CAAC,MAAM;IAChD,MAAMsC,cAAc,GAAGP,qBAAqB,CAAC,CAAC;IAC9C,IAAIF,qBAAqB,CAACU,OAAO,IAAIV,qBAAqB,CAACU,OAAO,KAAKD,cAAc,EAAE;MACrFE,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE;QAClEC,QAAQ,EAAEb,qBAAqB,CAACU,OAAO;QACvCA,OAAO,EAAED;MACX,CAAC,CAAC;MACF;MACA1B,gBAAgB,CAAC,EAAE,CAAC;MACpBM,aAAa,CAAC;QACZC,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;MACFP,QAAQ,CAAC2B,SAAS,CAAC;IACrB;IACAd,qBAAqB,CAACU,OAAO,GAAGD,cAAc;EAChD,CAAC,EAAE,CAACP,qBAAqB,CAAC,CAAC;EAE3B,MAAMa,kBAAkB,GAAG5C,WAAW,CAAC,YAAY;IACjD,IAAI;MACF;MACAqC,uBAAuB,CAAC,CAAC;MAEzBvB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnBH,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrDhC,eAAe;QACf6B,cAAc,EAAEP,qBAAqB,CAAC,CAAC;QACvCP;MACF,CAAC,CAAC;MAEF,MAAMqB,QAAQ,GAAG,MAAMf,OAAO,CAACgB,gBAAgB,CAACtB,OAAO,CAAC;MAExD,IAAIqB,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QAAA,IAAAC,qBAAA;QACrCT,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UACnDS,KAAK,EAAE,EAAAD,qBAAA,GAAAJ,QAAQ,CAACG,IAAI,CAACrC,aAAa,cAAAsC,qBAAA,uBAA3BA,qBAAA,CAA6BE,MAAM,KAAI,CAAC;UAC/CC,WAAW,EAAErB,qBAAqB,CAAC;QACrC,CAAC,CAAC;QACFnB,gBAAgB,CAACiC,QAAQ,CAACG,IAAI,CAACrC,aAAa,IAAI,EAAE,CAAC;QACnDO,aAAa,CAAC2B,QAAQ,CAACG,IAAI,CAAC/B,UAAU,CAAC;MACzC,CAAC,MAAM;QACLD,QAAQ,CAAC6B,QAAQ,CAACQ,OAAO,IAAI,+BAA+B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBd,OAAO,CAACzB,KAAK,CAAC,iCAAiC,EAAEuC,GAAG,CAAC;MACrDtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,gDAAgD,CAAC;IAC3E,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACyC,IAAI,CAACC,SAAS,CAAChC,OAAO,CAAC,EAAEf,eAAe,EAAE4B,uBAAuB,EAAEN,qBAAqB,EAAED,OAAO,CAAC,CAAC;EAEvG,MAAM2B,OAAO,GAAGzD,WAAW,CAAC,YAAY;IACtC,MAAM4C,kBAAkB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMc,kBAAkB,GAAG1D,WAAW,CAAC,MAAOgD,IAAuC,IAAK;IACxF,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;;MAEnB;MACA,MAAME,QAAQ,GAAGpC,eAAe,GAC5B,MAAOqB,OAAO,CAAS4B,kBAAkB,CAACV,IAAI,CAAC,GAC/C,MAAMlB,OAAO,CAAC4B,kBAAkB,CAACV,IAA8B,CAAC;MAEpE,IAAIH,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMH,kBAAkB,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM,IAAIe,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,oCAAoC,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,+CAA+C,CAAC;MACxE,MAAMC,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAC8B,kBAAkB,EAAEd,OAAO,EAAErB,eAAe,CAAC,CAAC;EAElD,MAAMmD,kBAAkB,GAAG5D,WAAW,CAAC,OAAO6D,EAAU,EAAEb,IAAuC,KAAK;IACpG,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;;MAEnB;MACA,MAAME,QAAQ,GAAGpC,eAAe,GAC5B,MAAOqB,OAAO,CAAS8B,kBAAkB,CAACC,EAAE,EAAEb,IAAI,CAAC,GACnD,MAAMlB,OAAO,CAAC8B,kBAAkB,CAACC,EAAE,EAAEb,IAA8B,CAAC;MAExE,IAAIH,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC;QACApC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,YAAY;UAAA,IAAAC,cAAA;UAAA,OACnBD,YAAY,CAACE,eAAe,KAAKL,EAAE,GAC/B;YAAE,GAAGG,YAAY;YAAE,KAAAC,cAAA,GAAGpB,QAAQ,CAACG,IAAI,cAAAiB,cAAA,uBAAbA,cAAA,CAAeD,YAAY;UAAC,CAAC,GACnDA,YAAY;QAAA,CAClB,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,+BAA+B,CAAC;MACtE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,+CAA+C,CAAC;MACxE,MAAMC,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACgB,OAAO,EAAErB,eAAe,CAAC,CAAC;EAE9B,MAAM0D,kBAAkB,GAAGnE,WAAW,CAAC,MAAO6D,EAAU,IAAK;IAC3D,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACqC,kBAAkB,CAACN,EAAE,CAAC;MAErD,IAAIhB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAnC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACM,MAAM,CAACJ,YAAY,IAAIA,YAAY,CAACE,eAAe,KAAKL,EAAE,CACjE,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,+BAA+B,CAAC;MACtE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,+CAA+C,CAAC;MACxE,MAAMC,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;EAEb,MAAMuC,mBAAmB,GAAGrE,WAAW,CAAC,MAAO6D,EAAU,IAAK;IAC5D,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACuC,mBAAmB,CAACR,EAAE,CAAC;MAEtD,IAAIhB,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC;QACApC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,YAAY;UAAA,IAAAM,eAAA;UAAA,OACnBN,YAAY,CAACE,eAAe,KAAKL,EAAE,GAC/B;YAAE,GAAGG,YAAY;YAAE,KAAAM,eAAA,GAAGzB,QAAQ,CAACG,IAAI,cAAAsB,eAAA,uBAAbA,eAAA,CAAeN,YAAY;UAAC,CAAC,GACnDA,YAAY;QAAA,CAClB,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,gCAAgC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,iDAAiD,CAAC;MAC1E,MAAMC,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;EAEb,MAAMyC,qBAAqB,GAAGvE,WAAW,CAAC,MAAO6D,EAAU,IAAK;IAC9D,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACyC,qBAAqB,CAACV,EAAE,CAAC;MAExD,IAAIhB,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC;QACApC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,YAAY;UAAA,IAAAQ,eAAA;UAAA,OACnBR,YAAY,CAACE,eAAe,KAAKL,EAAE,GAC/B;YAAE,GAAGG,YAAY;YAAE,KAAAQ,eAAA,GAAG3B,QAAQ,CAACG,IAAI,cAAAwB,eAAA,uBAAbA,eAAA,CAAeR,YAAY;UAAC,CAAC,GACnDA,YAAY;QAAA,CAClB,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,kCAAkC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,mDAAmD,CAAC;MAC5E,MAAMC,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;EAEb,MAAM2C,gBAAgB,GAAGzE,WAAW,CAAC,OAAO6D,EAAU,EAAEa,UAAkB,GAAG,CAAC,KAAK;IACjF,IAAI;MACF1D,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAAC6C,WAAW,CAACd,EAAE,EAAEa,UAAU,CAAC;MAE1D,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAnC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,YAAY,IACnBA,YAAY,CAACE,eAAe,KAAKL,EAAE,GAC/B;UACE,GAAGG,YAAY;UACfY,cAAc,EAAE,CAACZ,YAAY,CAACY,cAAc,IAAI,CAAC,KAAKZ,YAAY,CAACa,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;UACzFA,aAAa,EAAE;YAAEC,WAAW,EAAEJ,UAAU;YAAEK,aAAa,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAK;QACxF,CAAC,GACDhB,YACN,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,6BAA6B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,6CAA6C,CAAC;MACtE,MAAMC,GAAG;IACX;EACF,CAAC,EAAE,CAACxB,OAAO,CAAC,CAAC;EAEb,MAAMmD,kBAAkB,GAAGjF,WAAW,CAAC,MAAO6D,EAAU,IAAK;IAC3D,IAAI;MACF7C,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACoD,cAAc,CAACrB,EAAE,CAAC;MAEjD,IAAIhB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAnC,gBAAgB,CAACkD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,YAAY,IACnBA,YAAY,CAACE,eAAe,KAAKL,EAAE,GAC/B;UACE,GAAGG,YAAY;UACfY,cAAc,EAAEO,IAAI,CAACC,GAAG,CAAC,CAACpB,YAAY,CAACY,cAAc,IAAI,CAAC,KAAKZ,YAAY,CAACa,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;UACtGA,aAAa,EAAElC;QACjB,CAAC,GACDqB,YACN,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAACd,QAAQ,CAACQ,OAAO,IAAI,+BAA+B,CAAC;MACtE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,+CAA+C,CAAC;MACxE,MAAMC,GAAG;IACX;EACF,CAAC,EAAE,CAACxB,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMuD,aAAa,GAAGrF,WAAW,CAAEsF,UAA+B,IAAK;IACrE7D,UAAU,CAACqC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGwB;IAAW,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvF,SAAS,CAAC,MAAM;IACdyC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MAAEhC;IAAgB,CAAC,CAAC;IAC7E4B,uBAAuB,CAAC,CAAC;IACzBO,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACnC,eAAe,EAAE4B,uBAAuB,CAAC,CAAC;EAE9CtC,SAAS,CAAC,MAAM;IACd6C,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACLjC,aAAa;IACbE,OAAO;IACPE,KAAK;IACLE,UAAU;IACVO,OAAO;IACPC,UAAU,EAAE4D,aAAa;IACzB5B,OAAO;IACPC,kBAAkB;IAClBE,kBAAkB;IAClBO,kBAAkB;IAClBE,mBAAmB;IACnBE,qBAAqB;IACrBE,gBAAgB;IAChBQ;EACF,CAAC;AACH,CAAC;;AAED;AAAAvE,EAAA,CArUaH,gBAAgB;AAsU7B,OAAO,MAAMgF,aAAa,GAAGA,CAAA,KAA2B;EAAAC,GAAA;EACtD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAqB,CAAC;EAExD,MAAM6F,eAAe,GAAG3F,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFc,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAM3C,mBAAmB,CAAC0F,aAAa,CAAC,CAAC;MAE1D,IAAI/C,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC0C,aAAa,CAAC7C,QAAQ,CAACG,IAAI,CAACyC,UAAU,CAAC;MACzC,CAAC,MAAM;QACLzE,QAAQ,CAAC6B,QAAQ,CAACQ,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,6CAA6C,CAAC;IACxE,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAIN,MAAM2C,OAAO,GAAGzD,WAAW,CAAC,YAAY;IACtC,MAAM2F,eAAe,CAAC,CAAC;EACzB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB5F,SAAS,CAAC,MAAM;IACd4F,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,OAAO;IACLF,UAAU;IACV5E,OAAO;IACPE,KAAK;IACL0C;EACF,CAAC;AACH,CAAC;;AAED;AAAA+B,GAAA,CA1CaD,aAAa;AA2C1B,OAAO,MAAMM,yBAAyB,GAAGA,CAAA,KAAuC;EAAAC,GAAA;EAC9E,MAAM,CAACL,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAqB,CAAC;EAExD,MAAM6F,eAAe,GAAG3F,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFc,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnBH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;MAEjE;MACA,MAAMsD,kBAAkB,GAAG,MAAM7F,mBAAmB,CAAC8F,8BAA8B,CAAC,CAAC;MAErF,IAAID,kBAAkB,CAAChD,OAAO,IAAIgD,kBAAkB,CAAC/C,IAAI,EAAE;QACzD,MAAMyC,UAAU,GAAGM,kBAAkB,CAAC/C,IAAI,CAACyC,UAAU;;QAErD;QACAC,aAAa,CAACD,UAAU,CAAC;QACzBjD,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;UACnES,KAAK,EAAEuC,UAAU,CAACtC,MAAM;UACxB8C,2BAA2B,EAAER,UAAU,CAACrB,MAAM,CAAC8B,GAAG,IAAIA,GAAG,CAACC,aAAa,IAAID,GAAG,CAACC,aAAa,CAAChD,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;UAC/GsC,UAAU,EAAEA,UAAU,CAAC1B,GAAG,CAACmC,GAAG;YAAA,IAAAE,kBAAA;YAAA,OAAK;cACjCvC,EAAE,EAAEqC,GAAG,CAACG,WAAW;cACnBC,IAAI,EAAEJ,GAAG,CAACI,IAAI;cACdC,kBAAkB,EAAE,EAAAH,kBAAA,GAAAF,GAAG,CAACC,aAAa,cAAAC,kBAAA,uBAAjBA,kBAAA,CAAmBjD,MAAM,KAAI;YACnD,CAAC;UAAA,CAAC;QACJ,CAAC,CAAC;MAEJ,CAAC,MAAM;QACLnC,QAAQ,CAAC,4BAA4B,CAAC;MACxC;IACF,CAAC,CAAC,OAAOsC,GAAQ,EAAE;MACjBd,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEuC,GAAG,CAAC;MAChDtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,6CAA6C,CAAC;IACxE,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2C,OAAO,GAAGzD,WAAW,CAAC,YAAY;IACtC,MAAM2F,eAAe,CAAC,CAAC;EACzB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB5F,SAAS,CAAC,MAAM;IACd4F,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,OAAO;IACLF,UAAU;IACV5E,OAAO;IACPE,KAAK;IACL0C;EACF,CAAC;AACH,CAAC;;AAED;AAAAqC,GAAA,CAzDaD,yBAAyB;AA0DtC,OAAO,MAAMW,gBAAgB,GAAGA,CAAA,KAA8B;EAAAC,GAAA;EAC5D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAqB,CAAC;EAExD,MAAM8G,kBAAkB,GAAG5G,WAAW,CAAC,YAAY;IACjD,IAAI;MACFc,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC2B,SAAS,CAAC;MAEnB,MAAME,QAAQ,GAAG,MAAM3C,mBAAmB,CAAC2G,gBAAgB,CAAC,CAAC;MAE7D,IAAIhE,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC2D,gBAAgB,CAAC9D,QAAQ,CAACG,IAAI,CAAC0D,aAAa,CAAC;MAC/C,CAAC,MAAM;QACL1F,QAAQ,CAAC6B,QAAQ,CAACQ,OAAO,IAAI,gCAAgC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACD,OAAO,IAAI,iDAAiD,CAAC;IAC5E,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2C,OAAO,GAAGzD,WAAW,CAAC,YAAY;IACtC,MAAM4G,kBAAkB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB7G,SAAS,CAAC,MAAM;IACd6G,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,OAAO;IACLF,aAAa;IACb7F,OAAO;IACPE,KAAK;IACL0C;EACF,CAAC;AACH,CAAC;AAACgD,GAAA,CAtCWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}