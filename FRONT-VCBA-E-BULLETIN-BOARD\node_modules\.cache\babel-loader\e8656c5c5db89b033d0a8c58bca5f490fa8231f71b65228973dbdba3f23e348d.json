{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\auth\\\\AdminLogin\\\\AdminLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport { VALIDATION_RULES } from '../../../config/constants';\nimport './AdminLogin.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLogin = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    error,\n    isLoading,\n    clearError\n  } = useAdminAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    remember: false\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const validateForm = () => {\n    const errors = {};\n    if (!formData.email.trim()) {\n      errors.email = 'Email is required';\n    } else if (!VALIDATION_RULES.EMAIL.PATTERN.test(formData.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n    if (error) {\n      clearError();\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      var _location$state, _location$state$from;\n      await login({\n        email: formData.email,\n        password: formData.password,\n        userType: 'admin'\n      });\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/admin/dashboard';\n      navigate(from, {\n        replace: true\n      });\n    } catch (err) {\n      console.error('Login failed:', err);\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const handleForceLogout = () => {\n    console.log('🚪 AdminLogin - Force logout clicked');\n    // Clear all possible authentication data\n    localStorage.clear();\n    sessionStorage.clear();\n\n    // Clear cookies by setting them to expire\n    document.cookie.split(\";\").forEach(function (c) {\n      document.cookie = c.replace(/^ +/, \"\").replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\");\n    });\n\n    // Reload the page to ensure clean state\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-login__form-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-login__form-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-login__form-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              className: \"admin-login__form-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"admin-login__form-title\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"admin-login__form-subtitle\",\n              children: \"Administrator Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-login__error\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"admin-login__form\",\n            noValidate: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"admin-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"admin-login__label\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                placeholder: \"Enter your email address\",\n                className: `admin-login__input ${formErrors.email ? 'error' : ''}`,\n                disabled: isLoading,\n                autoComplete: \"email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), formErrors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"admin-login__error-text\",\n                children: formErrors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"admin-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"admin-login__label\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                placeholder: \"Enter your password\",\n                className: `admin-login__input ${formErrors.password ? 'error' : ''}`,\n                disabled: isLoading,\n                autoComplete: \"current-password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), formErrors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"admin-login__error-text\",\n                children: formErrors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"admin-login__submit-btn\",\n              disabled: isLoading,\n              children: isLoading ? 'Signing in...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-login__info-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-login__info-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-login__school-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/ebb1.png\",\n              alt: \"E-Bulletin Board Logo\",\n              className: \"admin-login__school-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"admin-login__school-name\",\n              children: \"VCBA E-BULLETIN BOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"admin-login__school-description\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"admin-login__features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/megaphone.png\",\n                    alt: \"Categorized Contents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Categorized Contents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Organized announcements by departments, clubs, events, and more\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/message.png\",\n                    alt: \"Centralized Platform\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Centralized Platform\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"All school announcements in one place \\u2014 accessible anytime, anywhere\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/heart.png\",\n                    alt: \"User-Friendly Environment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"User-Friendly Environment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Simple design with smooth navigation and accessibility support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLogin, \"YIkHr34/LNef4yO7KEKXiIyC2E4=\", false, function () {\n  return [useNavigate, useLocation, useAdminAuth];\n});\n_c = AdminLogin;\nexport default AdminLogin;\nvar _c;\n$RefreshReg$(_c, \"AdminLogin\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "useAdminAuth", "VALIDATION_RULES", "jsxDEV", "_jsxDEV", "AdminLogin", "_s", "navigate", "location", "login", "error", "isLoading", "clearError", "formData", "setFormData", "email", "password", "remember", "formErrors", "setFormErrors", "showPassword", "setShowPassword", "validateForm", "errors", "trim", "EMAIL", "PATTERN", "test", "Object", "keys", "length", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "undefined", "handleSubmit", "preventDefault", "_location$state", "_location$state$from", "userType", "from", "state", "pathname", "replace", "err", "console", "togglePasswordVisibility", "handleForceLogout", "log", "localStorage", "clear", "sessionStorage", "document", "cookie", "split", "for<PERSON>ach", "c", "Date", "toUTCString", "window", "reload", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onSubmit", "noValidate", "htmlFor", "id", "onChange", "placeholder", "disabled", "autoComplete", "required", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/auth/AdminLogin/AdminLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport { VALIDATION_RULES } from '../../../config/constants';\nimport './AdminLogin.css';\n\ninterface FormData {\n  email: string;\n  password: string;\n  remember: boolean;\n}\n\ninterface FormErrors {\n  email?: string;\n  password?: string;\n}\n\nconst AdminLogin: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, error, isLoading, clearError } = useAdminAuth();\n\n  const [formData, setFormData] = useState<FormData>({\n    email: '',\n    password: '',\n    remember: false,\n  });\n\n  const [formErrors, setFormErrors] = useState<FormErrors>({});\n  const [showPassword, setShowPassword] = useState(false);\n\n  const validateForm = (): boolean => {\n    const errors: FormErrors = {};\n\n    if (!formData.email.trim()) {\n      errors.email = 'Email is required';\n    } else if (!VALIDATION_RULES.EMAIL.PATTERN.test(formData.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n\n    if (formErrors[name as keyof FormErrors]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined,\n      }));\n    }\n\n    if (error) {\n      clearError();\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await login({\n        email: formData.email,\n        password: formData.password,\n        userType: 'admin',\n      });\n\n      const from = (location.state as any)?.from?.pathname || '/admin/dashboard';\n      navigate(from, { replace: true });\n    } catch (err) {\n      console.error('Login failed:', err);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const handleForceLogout = () => {\n    console.log('🚪 AdminLogin - Force logout clicked');\n    // Clear all possible authentication data\n    localStorage.clear();\n    sessionStorage.clear();\n\n    // Clear cookies by setting them to expire\n    document.cookie.split(\";\").forEach(function(c) {\n      document.cookie = c.replace(/^ +/, \"\").replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\");\n    });\n\n    // Reload the page to ensure clean state\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"admin-login\">\n      <div className=\"admin-login__container\">\n        {/* Left Panel - Login Form */}\n        <div className=\"admin-login__form-section\">\n          <div className=\"admin-login__form-container\">\n            {/* Login Form Header */}\n            <div className=\"admin-login__form-header\">\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                className=\"admin-login__form-logo\"\n              />\n              <h1 className=\"admin-login__form-title\">Villamor College of Business and Arts, Inc.</h1>\n              <p className=\"admin-login__form-subtitle\">Administrator Portal</p>\n            </div>\n\n            {/* Error Display */}\n            {error && (\n              <div className=\"admin-login__error\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n                {error}\n              </div>\n            )}\n\n            {/* Login Form */}\n            <form onSubmit={handleSubmit} className=\"admin-login__form\" noValidate>\n              <div className=\"admin-login__form-group\">\n                <label htmlFor=\"email\" className=\"admin-login__label\">Email Address</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  placeholder=\"Enter your email address\"\n                  className={`admin-login__input ${formErrors.email ? 'error' : ''}`}\n                  disabled={isLoading}\n                  autoComplete=\"email\"\n                  required\n                />\n                {formErrors.email && <span className=\"admin-login__error-text\">{formErrors.email}</span>}\n              </div>\n\n              <div className=\"admin-login__form-group\">\n                <label htmlFor=\"password\" className=\"admin-login__label\">Password</label>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  placeholder=\"Enter your password\"\n                  className={`admin-login__input ${formErrors.password ? 'error' : ''}`}\n                  disabled={isLoading}\n                  autoComplete=\"current-password\"\n                  required\n                />\n                {formErrors.password && <span className=\"admin-login__error-text\">{formErrors.password}</span>}\n              </div>\n\n              {/* <div className=\"admin-login__remember\">\n                <input\n                  type=\"checkbox\"\n                  id=\"remember\"\n                  name=\"remember\"\n                checked={formData.remember}\n                onChange={handleInputChange}\n                className=\"admin-login__checkbox\"\n              />\n                <label htmlFor=\"remember\" className=\"admin-login__remember-label\">Remember me</label>\n              </div> */}\n\n              <button\n                type=\"submit\"\n                className=\"admin-login__submit-btn\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </button>\n            </form>\n\n            {/* Sign Up Link */}\n            {/* <div className=\"admin-login__signup-link\">\n              Don't have an account? <Link to=\"/admin/register\">Sign Up</Link>\n            </div> */}\n\n            {/* Force Logout Button */}\n            {/* <div style={{ marginTop: '1rem', textAlign: 'center' }}>\n              <button\n                type=\"button\"\n                onClick={handleForceLogout}\n                style={{\n                  background: 'none',\n                  border: '1px solid #dc2626',\n                  color: '#dc2626',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = '#dc2626';\n                  e.currentTarget.style.color = 'white';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = 'none';\n                  e.currentTarget.style.color = '#dc2626';\n                }}\n              >\n                🚪 Clear All Data & Logout\n              </button>\n            </div> */}\n          </div>\n        </div>\n\n        {/* Right Panel - Information Section */}\n        <div className=\"admin-login__info-section\">\n          <div className=\"admin-login__info-content\">\n\n            {/* School Information */}\n            <div className=\"admin-login__school-info\">\n              <img\n                src=\"/logo/ebb1.png\"\n                alt=\"E-Bulletin Board Logo\"\n                className=\"admin-login__school-logo\"\n              />\n              <h3 className=\"admin-login__school-name\">\n                VCBA E-BULLETIN BOARD\n              </h3>\n              <p className=\"admin-login__school-description\">\n                Villamor College of Business and Arts, Inc.\n              </p>\n\n              {/* Features */}\n              <div className=\"admin-login__features\">\n                <div className=\"admin-login__feature\">\n                  <div className=\"admin-login__feature-icon\">\n                    <img src=\"/icons/megaphone.png\" alt=\"Categorized Contents\" />\n                  </div>\n                  <div className=\"admin-login__feature-content\">\n                    <h4>Categorized Contents</h4>\n                    <p>Organized announcements by departments, clubs, events, and more</p>\n                  </div>\n                </div>\n\n                <div className=\"admin-login__feature\">\n                  <div className=\"admin-login__feature-icon\">\n                    <img src=\"/icons/message.png\" alt=\"Centralized Platform\" />\n                  </div>\n                  <div className=\"admin-login__feature-content\">\n                    <h4>Centralized Platform</h4>\n                    <p>All school announcements in one place — accessible anytime, anywhere</p>\n                  </div>\n                </div>\n\n                <div className=\"admin-login__feature\">\n                  <div className=\"admin-login__feature-icon\">\n                    <img src=\"/icons/heart.png\" alt=\"User-Friendly Environment\" />\n                  </div>\n                  <div className=\"admin-login__feature-content\">\n                    <h4>User-Friendly Environment</h4>\n                    <p>Simple design with smooth navigation and accessibility support</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n    </div>\n  );\n};\n\nexport default AdminLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAeC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa1B,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,KAAK;IAAEC,KAAK;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGX,YAAY,CAAC,CAAC;EAE9D,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAW;IACjDiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAa,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMwB,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,MAAkB,GAAG,CAAC,CAAC;IAE7B,IAAI,CAACV,QAAQ,CAACE,KAAK,CAACS,IAAI,CAAC,CAAC,EAAE;MAC1BD,MAAM,CAACR,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAACb,gBAAgB,CAACuB,KAAK,CAACC,OAAO,CAACC,IAAI,CAACd,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/DQ,MAAM,CAACR,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBO,MAAM,CAACP,QAAQ,GAAG,sBAAsB;IAC1C;IAEAG,aAAa,CAACI,MAAM,CAAC;IACrB,OAAOK,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAE/CvB,WAAW,CAACwB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;IAEH,IAAIhB,UAAU,CAACe,IAAI,CAAqB,EAAE;MACxCd,aAAa,CAACmB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACL,IAAI,GAAGM;MACV,CAAC,CAAC,CAAC;IACL;IAEA,IAAI7B,KAAK,EAAE;MACTE,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAM4B,YAAY,GAAG,MAAOR,CAAkB,IAAK;IACjDA,CAAC,CAACS,cAAc,CAAC,CAAC;IAElB,IAAI,CAACnB,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACF,MAAMlC,KAAK,CAAC;QACVM,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3B4B,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,EAAAH,eAAA,GAAClC,QAAQ,CAACsC,KAAK,cAAAJ,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBG,IAAI,cAAAF,oBAAA,uBAA7BA,oBAAA,CAA+BI,QAAQ,KAAI,kBAAkB;MAC1ExC,QAAQ,CAACsC,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACxC,KAAK,CAAC,eAAe,EAAEuC,GAAG,CAAC;IACrC;EACF,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrC9B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMgC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,OAAO,CAACG,GAAG,CAAC,sCAAsC,CAAC;IACnD;IACAC,YAAY,CAACC,KAAK,CAAC,CAAC;IACpBC,cAAc,CAACD,KAAK,CAAC,CAAC;;IAEtB;IACAE,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAASC,CAAC,EAAE;MAC7CJ,QAAQ,CAACC,MAAM,GAAGG,CAAC,CAACb,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,YAAY,GAAG,IAAIc,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,SAAS,CAAC;IAC5G,CAAC,CAAC;;IAEF;IACAC,MAAM,CAACxD,QAAQ,CAACyD,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACE7D,OAAA;IAAK8D,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1B/D,OAAA;MAAK8D,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC/D,OAAA;QAAK8D,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC/D,OAAA;UAAK8D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1C/D,OAAA;YAAK8D,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC/D,OAAA;cACEgE,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,WAAW;cACfH,SAAS,EAAC;YAAwB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACFrE,OAAA;cAAI8D,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFrE,OAAA;cAAG8D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EAGL/D,KAAK,iBACJN,OAAA;YAAK8D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/D,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAV,QAAA,eACzD/D,OAAA;gBAAM0E,CAAC,EAAC,uIAAuI;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO,CAAC,EACL/D,KAAK;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDrE,OAAA;YAAM+E,QAAQ,EAAE3C,YAAa;YAAC0B,SAAS,EAAC,mBAAmB;YAACkB,UAAU;YAAAjB,QAAA,gBACpE/D,OAAA;cAAK8D,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/D,OAAA;gBAAOiF,OAAO,EAAC,OAAO;gBAACnB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ErE,OAAA;gBACE+B,IAAI,EAAC,OAAO;gBACZmD,EAAE,EAAC,OAAO;gBACVrD,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAErB,QAAQ,CAACE,KAAM;gBACtBwE,QAAQ,EAAExD,iBAAkB;gBAC5ByD,WAAW,EAAC,0BAA0B;gBACtCtB,SAAS,EAAE,sBAAsBhD,UAAU,CAACH,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;gBACnE0E,QAAQ,EAAE9E,SAAU;gBACpB+E,YAAY,EAAC,OAAO;gBACpBC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDvD,UAAU,CAACH,KAAK,iBAAIX,OAAA;gBAAM8D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEjD,UAAU,CAACH;cAAK;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENrE,OAAA;cAAK8D,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/D,OAAA;gBAAOiF,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzErE,OAAA;gBACE+B,IAAI,EAAEf,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCkE,EAAE,EAAC,UAAU;gBACbrD,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAErB,QAAQ,CAACG,QAAS;gBACzBuE,QAAQ,EAAExD,iBAAkB;gBAC5ByD,WAAW,EAAC,qBAAqB;gBACjCtB,SAAS,EAAE,sBAAsBhD,UAAU,CAACF,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;gBACtEyE,QAAQ,EAAE9E,SAAU;gBACpB+E,YAAY,EAAC,kBAAkB;gBAC/BC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDvD,UAAU,CAACF,QAAQ,iBAAIZ,OAAA;gBAAM8D,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEjD,UAAU,CAACF;cAAQ;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eAcNrE,OAAA;cACE+B,IAAI,EAAC,QAAQ;cACb+B,SAAS,EAAC,yBAAyB;cACnCuB,QAAQ,EAAE9E,SAAU;cAAAwD,QAAA,EAEnBxD,SAAS,GAAG,eAAe,GAAG;YAAS;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAK8D,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC/D,OAAA;UAAK8D,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eAGxC/D,OAAA;YAAK8D,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC/D,OAAA;cACEgE,GAAG,EAAC,gBAAgB;cACpBC,GAAG,EAAC,uBAAuB;cAC3BH,SAAS,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFrE,OAAA;cAAI8D,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrE,OAAA;cAAG8D,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAE/C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJrE,OAAA;cAAK8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC/D,OAAA;gBAAK8D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC/D,OAAA;kBAAK8D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxC/D,OAAA;oBAAKgE,GAAG,EAAC,sBAAsB;oBAACC,GAAG,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNrE,OAAA;kBAAK8D,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C/D,OAAA;oBAAA+D,QAAA,EAAI;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7BrE,OAAA;oBAAA+D,QAAA,EAAG;kBAA+D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrE,OAAA;gBAAK8D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC/D,OAAA;kBAAK8D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxC/D,OAAA;oBAAKgE,GAAG,EAAC,oBAAoB;oBAACC,GAAG,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNrE,OAAA;kBAAK8D,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C/D,OAAA;oBAAA+D,QAAA,EAAI;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7BrE,OAAA;oBAAA+D,QAAA,EAAG;kBAAoE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrE,OAAA;gBAAK8D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC/D,OAAA;kBAAK8D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxC/D,OAAA;oBAAKgE,GAAG,EAAC,kBAAkB;oBAACC,GAAG,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNrE,OAAA;kBAAK8D,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C/D,OAAA;oBAAA+D,QAAA,EAAI;kBAAyB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClCrE,OAAA;oBAAA+D,QAAA,EAAG;kBAA8D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV,CAAC;AAACnE,EAAA,CA3QID,UAAoB;EAAA,QACPN,WAAW,EACXC,WAAW,EACoBC,YAAY;AAAA;AAAA2F,EAAA,GAHxDvF,UAAoB;AA6Q1B,eAAeA,UAAU;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}