{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13v8\",\n  key: \"1l5pq0\"\n}], [\"path\", {\n  d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\",\n  key: \"1pljnt\"\n}], [\"path\", {\n  d: \"m8 17 4-4 4 4\",\n  key: \"1quai1\"\n}]];\nconst CloudUpload = createLucideIcon(\"cloud-upload\", __iconNode);\nexport { __iconNode, CloudUpload as default };\n//# sourceMappingURL=cloud-upload.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}