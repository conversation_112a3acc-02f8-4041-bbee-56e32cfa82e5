{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6.87 6.87a8 8 0 1 0 11.26 11.26\",\n  key: \"3on8tj\"\n}], [\"path\", {\n  d: \"M19.9 14.25a8 8 0 0 0-9.15-9.15\",\n  key: \"15ghsc\"\n}], [\"path\", {\n  d: \"m22 6-3-3\",\n  key: \"1opdir\"\n}], [\"path\", {\n  d: \"M6.26 18.67 4 21\",\n  key: \"yzmioq\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M4 4 2 6\",\n  key: \"1ycko6\"\n}]];\nconst AlarmClockOff = createLucideIcon(\"alarm-clock-off\", __iconNode);\nexport { __iconNode, AlarmClockOff as default };\n//# sourceMappingURL=alarm-clock-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}