{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12H3\",\n  key: \"18klou\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M12 18H3\",\n  key: \"11ftsu\"\n}], [\"path\", {\n  d: \"m16 12 5 3-5 3v-6Z\",\n  key: \"zpskkp\"\n}]];\nconst ListVideo = createLucideIcon(\"list-video\", __iconNode);\nexport { __iconNode, ListVideo as default };\n//# sourceMappingURL=list-video.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}