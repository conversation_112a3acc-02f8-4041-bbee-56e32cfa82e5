{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"m16 8-8 8\",\n  key: \"166keh\"\n}], [\"path\", {\n  d: \"M16 16H8V8\",\n  key: \"1w2ppm\"\n}]];\nconst SquareArrowDownLeft = createLucideIcon(\"square-arrow-down-left\", __iconNode);\nexport { __iconNode, SquareArrowDownLeft as default };\n//# sourceMappingURL=square-arrow-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}