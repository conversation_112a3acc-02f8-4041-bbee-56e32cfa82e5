{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M5 9v12\",\n  key: \"ih889a\"\n}], [\"path\", {\n  d: \"m15 9-3-3 3-3\",\n  key: \"1lwv8l\"\n}], [\"path\", {\n  d: \"M12 6h5a2 2 0 0 1 2 2v3\",\n  key: \"1rbwk6\"\n}], [\"path\", {\n  d: \"M19 15v6\",\n  key: \"10aioa\"\n}], [\"path\", {\n  d: \"M22 18h-6\",\n  key: \"1d5gi5\"\n}]];\nconst GitPullRequestCreateArrow = createLucideIcon(\"git-pull-request-create-arrow\", __iconNode);\nexport { __iconNode, GitPullRequestCreateArrow as default };\n//# sourceMappingURL=git-pull-request-create-arrow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}