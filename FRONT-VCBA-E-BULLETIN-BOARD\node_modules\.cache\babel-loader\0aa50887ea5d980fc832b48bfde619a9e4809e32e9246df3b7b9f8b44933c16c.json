{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5\",\n  key: \"laymnq\"\n}], [\"path\", {\n  d: \"M8.5 8.5v.01\",\n  key: \"ue8clq\"\n}], [\"path\", {\n  d: \"M16 15.5v.01\",\n  key: \"14dtrp\"\n}], [\"path\", {\n  d: \"M12 12v.01\",\n  key: \"u5ubse\"\n}], [\"path\", {\n  d: \"M11 17v.01\",\n  key: \"1hyl5a\"\n}], [\"path\", {\n  d: \"M7 14v.01\",\n  key: \"uct60s\"\n}]];\nconst Cookie = createLucideIcon(\"cookie\", __iconNode);\nexport { __iconNode, Cookie as default };\n//# sourceMappingURL=cookie.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}