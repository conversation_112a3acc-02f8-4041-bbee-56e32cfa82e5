{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 21V10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v11\",\n  key: \"pb2vm6\"\n}], [\"path\", {\n  d: \"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 1.132-1.803l7.95-3.974a2 2 0 0 1 1.837 0l7.948 3.974A2 2 0 0 1 22 8z\",\n  key: \"doq5xv\"\n}], [\"path\", {\n  d: \"M6 13h12\",\n  key: \"yf64js\"\n}], [\"path\", {\n  d: \"M6 17h12\",\n  key: \"1jwigz\"\n}]];\nconst Warehouse = createLucideIcon(\"warehouse\", __iconNode);\nexport { __iconNode, Warehouse as default };\n//# sourceMappingURL=warehouse.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}