{"name": "@rollup/pluginutils", "version": "3.1.0", "publishConfig": {"access": "public"}, "description": "A set of utility functions commonly used by Rollup plugins", "license": "MIT", "repository": "rollup/plugins", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/pluginutils#readme", "bugs": {"url": "https://github.com/rollup/plugins/issues"}, "main": "./dist/cjs/index.js", "engines": {"node": ">= 8.0.0"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm run test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm run build && pnpm run lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm run test -- --verbose", "lint": "pnpm run lint:js && pnpm run lint:docs && pnpm run lint:package", "lint:docs": "prettier --single-quote --write README.md", "lint:js": "eslint --fix --cache src test types --ext .js,.ts", "lint:package": "prettier --write package.json --plugin=prettier-plugin-package", "prebuild": "del-cli dist", "prepare": "pnpm run build", "prepublishOnly": "pnpm run lint && pnpm run build", "pretest": "pnpm run build -- --sourcemap", "test": "ava"}, "files": ["dist", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "utils"], "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}, "dependencies": {"@types/estree": "0.0.39", "estree-walker": "^1.0.1", "picomatch": "^2.2.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-typescript": "^3.0.0", "@types/jest": "^24.9.0", "@types/node": "^12.12.25", "@types/picomatch": "^2.2.1", "typescript": "^3.7.5"}, "ava": {"compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"], "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "exports": {"require": "./dist/cjs/index.js", "import": "./dist/es/index.js"}, "module": "./dist/es/index.js", "nyc": {"extension": [".js", ".ts"]}, "type": "commonjs", "types": "types/index.d.ts"}