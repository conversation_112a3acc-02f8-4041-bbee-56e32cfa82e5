{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 3h6v6\",\n  key: \"1q9fwt\"\n}], [\"path\", {\n  d: \"M10 14 21 3\",\n  key: \"gplh6r\"\n}], [\"path\", {\n  d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\",\n  key: \"a6xqqp\"\n}]];\nconst ExternalLink = createLucideIcon(\"external-link\", __iconNode);\nexport { __iconNode, ExternalLink as default };\n//# sourceMappingURL=external-link.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}