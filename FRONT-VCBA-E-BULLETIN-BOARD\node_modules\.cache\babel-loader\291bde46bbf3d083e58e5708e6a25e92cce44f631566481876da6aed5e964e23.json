{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M8 14s1.5 2 4 2 4-2 4-2\",\n  key: \"1y1vjs\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"9.01\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"yxxnd0\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"15.01\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"1p4y9e\"\n}]];\nconst Smile = createLucideIcon(\"smile\", __iconNode);\nexport { __iconNode, Smile as default };\n//# sourceMappingURL=smile.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}