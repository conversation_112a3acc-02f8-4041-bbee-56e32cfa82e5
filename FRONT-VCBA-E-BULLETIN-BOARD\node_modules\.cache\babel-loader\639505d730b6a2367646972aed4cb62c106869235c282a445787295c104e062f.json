{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.5 7-5 5\",\n  key: \"dy991v\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"k3hazp\"\n}], [\"path\", {\n  d: \"m9.5 7 5 5\",\n  key: \"s45iea\"\n}]];\nconst BookX = createLucideIcon(\"book-x\", __iconNode);\nexport { __iconNode, BookX as default };\n//# sourceMappingURL=book-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}