{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3\",\n  key: \"11bfej\"\n}]];\nconst Command = createLucideIcon(\"command\", __iconNode);\nexport { __iconNode, Command as default };\n//# sourceMappingURL=command.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}