{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"3\",\n  y2: \"15\",\n  key: \"17qcm7\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"fqmcym\"\n}], [\"path\", {\n  d: \"M18 9a9 9 0 0 1-9 9\",\n  key: \"n2h4wq\"\n}]];\nconst GitBranch = createLucideIcon(\"git-branch\", __iconNode);\nexport { __iconNode, GitBranch as default };\n//# sourceMappingURL=git-branch.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}