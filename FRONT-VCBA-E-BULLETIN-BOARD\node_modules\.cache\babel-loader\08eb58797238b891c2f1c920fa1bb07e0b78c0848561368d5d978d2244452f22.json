{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.6 2.7a10 10 0 1 0 5.7 5.7\",\n  key: \"1e0p6d\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}], [\"path\", {\n  d: \"M13.4 10.6 19 5\",\n  key: \"1kr7tw\"\n}]];\nconst CircleGauge = createLucideIcon(\"circle-gauge\", __iconNode);\nexport { __iconNode, CircleGauge as default };\n//# sourceMappingURL=circle-gauge.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}