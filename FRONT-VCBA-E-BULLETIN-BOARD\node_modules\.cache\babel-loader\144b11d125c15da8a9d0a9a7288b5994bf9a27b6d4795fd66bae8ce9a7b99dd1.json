{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"path\", {\n  d: \"M12 7v2\",\n  key: \"stiyo7\"\n}], [\"path\", {\n  d: \"M12 13h.01\",\n  key: \"y0uutt\"\n}]];\nconst MessageSquareWarning = createLucideIcon(\"message-square-warning\", __iconNode);\nexport { __iconNode, MessageSquareWarning as default };\n//# sourceMappingURL=message-square-warning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}