{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n  key: \"1pf5j1\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"6\",\n  x: \"2\",\n  y: \"12\",\n  rx: \"1\",\n  key: \"1a6c1e\"\n}], [\"path\", {\n  d: \"m10 15.5 4 2.5v-6l-4 2.5\",\n  key: \"t7cp39\"\n}]];\nconst FileVideo2 = createLucideIcon(\"file-video-2\", __iconNode);\nexport { __iconNode, FileVideo2 as default };\n//# sourceMappingURL=file-video-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}