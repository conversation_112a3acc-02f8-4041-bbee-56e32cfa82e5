{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3h6l6 18h6\",\n  key: \"ph9rgk\"\n}], [\"path\", {\n  d: \"M14 3h7\",\n  key: \"16f0ms\"\n}]];\nconst Option = createLucideIcon(\"option\", __iconNode);\nexport { __iconNode, Option as default };\n//# sourceMappingURL=option.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}