{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7.2 14.8a2 2 0 0 1 2 2\",\n  key: \"1tw9gg\"\n}], [\"circle\", {\n  cx: \"18.5\",\n  cy: \"8.5\",\n  r: \"3.5\",\n  key: \"1wadoa\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"16.5\",\n  r: \"5.5\",\n  key: \"6mdt3g\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"4.5\",\n  r: \"2.5\",\n  key: \"637s54\"\n}]];\nconst Bubbles = createLucideIcon(\"bubbles\", __iconNode);\nexport { __iconNode, Bubbles as default };\n//# sourceMappingURL=bubbles.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}