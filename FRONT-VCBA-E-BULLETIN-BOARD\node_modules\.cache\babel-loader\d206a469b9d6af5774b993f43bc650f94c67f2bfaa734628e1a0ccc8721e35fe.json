{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m18 16 4-4-4-4\",\n  key: \"1inbqp\"\n}], [\"path\", {\n  d: \"m6 8-4 4 4 4\",\n  key: \"15zrgr\"\n}], [\"path\", {\n  d: \"m14.5 4-5 16\",\n  key: \"e7oirm\"\n}]];\nconst CodeXml = createLucideIcon(\"code-xml\", __iconNode);\nexport { __iconNode, CodeXml as default };\n//# sourceMappingURL=code-xml.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}