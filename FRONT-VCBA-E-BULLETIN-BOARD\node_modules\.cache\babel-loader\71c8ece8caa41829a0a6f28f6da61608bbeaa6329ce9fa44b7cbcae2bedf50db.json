{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12h.01\",\n  key: \"1kxr2c\"\n}], [\"path\", {\n  d: \"M18 20V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14\",\n  key: \"36qu9e\"\n}], [\"path\", {\n  d: \"M2 20h20\",\n  key: \"owomy5\"\n}]];\nconst DoorClosed = createLucideIcon(\"door-closed\", __iconNode);\nexport { __iconNode, DoorClosed as default };\n//# sourceMappingURL=door-closed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}