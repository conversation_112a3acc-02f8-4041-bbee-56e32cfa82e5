version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: zaira-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${DB_NAME:-db_ebulletin_system}
      MYSQL_USER: ${DB_USER:-zaira_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-userpassword}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./ebulletin_system.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - zaira-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis (optional, for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: zaira-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - zaira-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # Node.js Backend
  backend:
    build: .
    container_name: zaira-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${PORT:-3000}
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-db_ebulletin_system}
      DB_USER: ${DB_USER:-zaira_user}
      DB_PASSWORD: ${DB_PASSWORD:-userpassword}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      SESSION_SECRET: ${SESSION_SECRET}
    ports:
      - "${PORT:-3000}:3000"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - zaira-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      timeout: 5s
      retries: 5
      start_period: 30s

  # Nginx (optional, for reverse proxy and static files)
  nginx:
    image: nginx:alpine
    container_name: zaira-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - zaira-network
    depends_on:
      - backend

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  zaira-network:
    driver: bridge
