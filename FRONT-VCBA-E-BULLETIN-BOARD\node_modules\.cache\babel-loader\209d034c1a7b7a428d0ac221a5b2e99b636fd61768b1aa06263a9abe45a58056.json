{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 9V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h7\",\n  key: \"x1c07l\"\n}], [\"path\", {\n  d: \"m8 16 3-3-3-3\",\n  key: \"rlqrt1\"\n}]];\nconst FolderSymlink = createLucideIcon(\"folder-symlink\", __iconNode);\nexport { __iconNode, FolderSymlink as default };\n//# sourceMappingURL=folder-symlink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}