{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 18-.722-3.25\",\n  key: \"1j64jw\"\n}], [\"path\", {\n  d: \"M2 8a10.645 10.645 0 0 0 20 0\",\n  key: \"1e7gxb\"\n}], [\"path\", {\n  d: \"m20 15-1.726-2.05\",\n  key: \"1cnuld\"\n}], [\"path\", {\n  d: \"m4 15 1.726-2.05\",\n  key: \"1dsqqd\"\n}], [\"path\", {\n  d: \"m9 18 .722-3.25\",\n  key: \"ypw2yx\"\n}]];\nconst EyeClosed = createLucideIcon(\"eye-closed\", __iconNode);\nexport { __iconNode, EyeClosed as default };\n//# sourceMappingURL=eye-closed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}