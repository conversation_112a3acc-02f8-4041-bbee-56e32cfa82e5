{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 12H3a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h14\",\n  key: \"1mb5g1\"\n}], [\"path\", {\n  d: \"M18 8c0-2.5-2-2.5-2-5\",\n  key: \"1il607\"\n}], [\"path\", {\n  d: \"M21 16a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1\",\n  key: \"1yl5r7\"\n}], [\"path\", {\n  d: \"M22 8c0-2.5-2-2.5-2-5\",\n  key: \"1gah44\"\n}], [\"path\", {\n  d: \"M7 12v4\",\n  key: \"jqww69\"\n}]];\nconst Cigarette = createLucideIcon(\"cigarette\", __iconNode);\nexport { __iconNode, Cigarette as default };\n//# sourceMappingURL=cigarette.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}