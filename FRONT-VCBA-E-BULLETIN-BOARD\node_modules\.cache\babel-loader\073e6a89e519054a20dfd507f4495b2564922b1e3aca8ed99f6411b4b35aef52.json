{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8\",\n  key: \"15492f\"\n}], [\"path\", {\n  d: \"m16 16 6-6\",\n  key: \"vzrcl6\"\n}], [\"path\", {\n  d: \"m8 8 6-6\",\n  key: \"18bi4p\"\n}], [\"path\", {\n  d: \"m9 7 8 8\",\n  key: \"5jnvq1\"\n}], [\"path\", {\n  d: \"m21 11-8-8\",\n  key: \"z4y7zo\"\n}]];\nconst Gavel = createLucideIcon(\"gavel\", __iconNode);\nexport { __iconNode, Gavel as default };\n//# sourceMappingURL=gavel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}