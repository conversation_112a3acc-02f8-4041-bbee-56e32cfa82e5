{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}]];\nconst Disc2 = createLucideIcon(\"disc-2\", __iconNode);\nexport { __iconNode, Disc2 as default };\n//# sourceMappingURL=disc-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}