{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 12h13a1 1 0 0 1 1 1 5 5 0 0 1-5 5h-.598a.5.5 0 0 0-.424.765l1.544 2.47a.5.5 0 0 1-.424.765H5.402a.5.5 0 0 1-.424-.765L7 18\",\n  key: \"kc4kqr\"\n}], [\"path\", {\n  d: \"M8 18a5 5 0 0 1-5-5V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8\",\n  key: \"1tqs57\"\n}]];\nconst Toilet = createLucideIcon(\"toilet\", __iconNode);\nexport { __iconNode, Toilet as default };\n//# sourceMappingURL=toilet.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}