{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\archive\\\\ArchivedAnnouncements.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Search, RotateCcw, Trash2, Calendar, User, Tag, AlertTriangle } from 'lucide-react';\nimport { archiveService } from '../../../services/archiveService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ArchivedAnnouncements = ({\n  onRestoreSuccess\n}) => {\n  _s();\n  const [announcements, setAnnouncements] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [total, setTotal] = useState(0);\n  const [restoring, setRestoring] = useState(null);\n  const [deleting, setDeleting] = useState(null);\n  const limit = 10;\n  useEffect(() => {\n    loadAnnouncements();\n  }, [currentPage, searchTerm]);\n  const loadAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const filters = {};\n      if (searchTerm.trim()) {\n        filters.search = searchTerm.trim();\n      }\n      const pagination = {\n        page: currentPage,\n        limit,\n        sort_by: 'deleted_at',\n        sort_order: 'DESC'\n      };\n      const response = await archiveService.getArchivedAnnouncements(filters, pagination);\n      if (response.success && response.data && response.data.data) {\n        var _response$data$pagina, _response$data$pagina2;\n        setAnnouncements(response.data.data);\n        setTotalPages(((_response$data$pagina = response.data.pagination) === null || _response$data$pagina === void 0 ? void 0 : _response$data$pagina.totalPages) || 1);\n        setTotal(((_response$data$pagina2 = response.data.pagination) === null || _response$data$pagina2 === void 0 ? void 0 : _response$data$pagina2.total) || 0);\n      } else {\n        setError('Failed to load archived announcements');\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('Error loading archived announcements:', error);\n      console.error('Error details:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error);\n      setError(error.message || 'Failed to load archived announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRestore = async announcementId => {\n    if (!window.confirm('Are you sure you want to restore this announcement?')) {\n      return;\n    }\n    try {\n      setRestoring(announcementId);\n      const response = await archiveService.restoreAnnouncement(announcementId);\n      if (response.success) {\n        alert('Announcement restored successfully!');\n        await loadAnnouncements();\n        onRestoreSuccess === null || onRestoreSuccess === void 0 ? void 0 : onRestoreSuccess();\n      } else {\n        alert('Failed to restore announcement');\n      }\n    } catch (error) {\n      console.error('Error restoring announcement:', error);\n      alert(error.message || 'Failed to restore announcement');\n    } finally {\n      setRestoring(null);\n    }\n  };\n  const handlePermanentDelete = async announcementId => {\n    if (!window.confirm('Are you sure you want to PERMANENTLY delete this announcement? This action cannot be undone!')) {\n      return;\n    }\n    try {\n      setDeleting(announcementId);\n      const response = await archiveService.permanentlyDeleteAnnouncement(announcementId);\n      if (response.success) {\n        alert('Announcement permanently deleted!');\n        await loadAnnouncements();\n        onRestoreSuccess === null || onRestoreSuccess === void 0 ? void 0 : onRestoreSuccess();\n      } else {\n        alert('Failed to delete announcement');\n      }\n    } catch (error) {\n      console.error('Error deleting announcement:', error);\n      alert(error.message || 'Failed to delete announcement');\n    } finally {\n      setDeleting(null);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const truncateContent = (content, maxLength = 150) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength) + '...';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '300px',\n        color: '#6b7280'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '32px',\n            height: '32px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), \"Loading archived announcements...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '1.5rem',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          flex: 1,\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          size: 20,\n          style: {\n            position: 'absolute',\n            left: '12px',\n            top: '50%',\n            transform: 'translateY(-50%)',\n            color: '#6b7280'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search archived announcements...\",\n          value: searchTerm,\n          onChange: e => {\n            setSearchTerm(e.target.value);\n            setCurrentPage(1);\n          },\n          style: {\n            width: '100%',\n            padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            outline: 'none',\n            transition: 'border-color 0.2s ease'\n          },\n          onFocus: e => e.target.style.borderColor = '#3b82f6',\n          onBlur: e => e.target.style.borderColor = '#d1d5db'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem',\n          fontWeight: '500'\n        },\n        children: [total, \" archived announcement\", total !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1.5rem',\n        color: '#dc2626'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this), !announcements || announcements.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '3rem',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '64px',\n          height: '64px',\n          background: '#f3f4f6',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          margin: '0 auto 1rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(Search, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.125rem',\n          fontWeight: '600'\n        },\n        children: \"No archived announcements found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '0.875rem'\n        },\n        children: searchTerm ? 'Try adjusting your search terms' : 'No announcements have been archived yet'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1rem'\n      },\n      children: announcements && announcements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '12px',\n          border: '1px solid #e5e7eb',\n          padding: '1.5rem',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          transition: 'all 0.2s ease'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1f2937'\n                },\n                children: announcement.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this), announcement.is_alert && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem',\n                  background: '#fef3c7',\n                  color: '#d97706',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '6px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 25\n                }, this), \"Alert\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 23\n              }, this), announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#dbeafe',\n                  color: '#1d4ed8',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '6px',\n                  fontSize: '0.75rem',\n                  fontWeight: '500'\n                },\n                children: \"Pinned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 1rem',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                lineHeight: '1.5'\n              },\n              children: truncateContent(announcement.content)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: '1rem',\n                fontSize: '0.75rem',\n                color: '#6b7280'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: announcement.category_color,\n                    color: 'white',\n                    padding: '0.125rem 0.375rem',\n                    borderRadius: '4px',\n                    fontSize: '0.6875rem',\n                    fontWeight: '500'\n                  },\n                  children: announcement.category_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this), announcement.author_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), \"Deleted: \", formatDate(announcement.deleted_at)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              marginLeft: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleRestore(announcement.announcement_id),\n              disabled: restoring === announcement.announcement_id,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem 1rem',\n                background: '#10b981',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: restoring === announcement.announcement_id ? 'not-allowed' : 'pointer',\n                opacity: restoring === announcement.announcement_id ? 0.6 : 1,\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                if (restoring !== announcement.announcement_id) {\n                  e.currentTarget.style.background = '#059669';\n                }\n              },\n              onMouseLeave: e => {\n                if (restoring !== announcement.announcement_id) {\n                  e.currentTarget.style.background = '#10b981';\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this), restoring === announcement.announcement_id ? 'Restoring...' : 'Restore']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePermanentDelete(announcement.announcement_id),\n              disabled: deleting === announcement.announcement_id,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem 1rem',\n                background: '#dc2626',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: deleting === announcement.announcement_id ? 'not-allowed' : 'pointer',\n                opacity: deleting === announcement.announcement_id ? 0.6 : 1,\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                if (deleting !== announcement.announcement_id) {\n                  e.currentTarget.style.background = '#b91c1c';\n                }\n              },\n              onMouseLeave: e => {\n                if (deleting !== announcement.announcement_id) {\n                  e.currentTarget.style.background = '#dc2626';\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), deleting === announcement.announcement_id ? 'Deleting...' : 'Delete']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 15\n        }, this)\n      }, announcement.announcement_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        gap: '0.5rem',\n        marginTop: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(currentPage - 1),\n        disabled: currentPage === 1,\n        style: {\n          padding: '0.5rem 1rem',\n          background: currentPage === 1 ? '#f3f4f6' : '#3b82f6',\n          color: currentPage === 1 ? '#9ca3af' : 'white',\n          border: 'none',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n        },\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0.5rem 1rem',\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: [\"Page \", currentPage, \" of \", totalPages]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(currentPage + 1),\n        disabled: currentPage === totalPages,\n        style: {\n          padding: '0.5rem 1rem',\n          background: currentPage === totalPages ? '#f3f4f6' : '#3b82f6',\n          color: currentPage === totalPages ? '#9ca3af' : 'white',\n          border: 'none',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n        },\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(ArchivedAnnouncements, \"tAoWvTXGWy0aUNU+zYz95J/EEmI=\");\n_c = ArchivedAnnouncements;\nexport default ArchivedAnnouncements;\nvar _c;\n$RefreshReg$(_c, \"ArchivedAnnouncements\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Search", "RotateCcw", "Trash2", "Calendar", "User", "Tag", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archiveService", "jsxDEV", "_jsxDEV", "ArchivedAnnouncements", "onRestoreSuccess", "_s", "announcements", "setAnnouncements", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "total", "setTotal", "restoring", "setRestoring", "deleting", "setDeleting", "limit", "loadAnnouncements", "filters", "trim", "search", "pagination", "page", "sort_by", "sort_order", "response", "getArchivedAnnouncements", "success", "data", "_response$data$pagina", "_response$data$pagina2", "_error$response", "console", "message", "handleRestore", "announcementId", "window", "confirm", "restoreAnnouncement", "alert", "handlePermanentDelete", "permanentlyDeleteAnnouncement", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "style", "display", "justifyContent", "alignItems", "minHeight", "color", "children", "textAlign", "width", "height", "border", "borderTop", "borderRadius", "animation", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "marginBottom", "position", "flex", "max<PERSON><PERSON><PERSON>", "size", "left", "top", "transform", "type", "placeholder", "value", "onChange", "e", "target", "padding", "fontSize", "outline", "transition", "onFocus", "borderColor", "onBlur", "fontWeight", "background", "flexDirection", "map", "announcement", "boxShadow", "onMouseEnter", "currentTarget", "onMouseLeave", "title", "is_alert", "is_pinned", "lineHeight", "flexWrap", "category_color", "category_name", "author_name", "deleted_at", "marginLeft", "onClick", "announcement_id", "disabled", "cursor", "opacity", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/archive/ArchivedAnnouncements.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Search, RotateCcw, Trash2, Calendar, User, Tag, AlertTriangle } from 'lucide-react';\nimport { archiveService, ArchivedAnnouncement, ArchiveFilters, ArchivePagination } from '../../../services/archiveService';\n\ninterface ArchivedAnnouncementsProps {\n  onRestoreSuccess?: () => void;\n}\n\nconst ArchivedAnnouncements: React.FC<ArchivedAnnouncementsProps> = ({ onRestoreSuccess }) => {\n  const [announcements, setAnnouncements] = useState<ArchivedAnnouncement[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [total, setTotal] = useState(0);\n  const [restoring, setRestoring] = useState<number | null>(null);\n  const [deleting, setDeleting] = useState<number | null>(null);\n\n  const limit = 10;\n\n  useEffect(() => {\n    loadAnnouncements();\n  }, [currentPage, searchTerm]);\n\n  const loadAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const filters: ArchiveFilters = {};\n      if (searchTerm.trim()) {\n        filters.search = searchTerm.trim();\n      }\n\n      const pagination: ArchivePagination = {\n        page: currentPage,\n        limit,\n        sort_by: 'deleted_at',\n        sort_order: 'DESC'\n      };\n\n      const response = await archiveService.getArchivedAnnouncements(filters, pagination);\n\n      if (response.success && response.data && response.data.data) {\n        setAnnouncements(response.data.data);\n        setTotalPages(response.data.pagination?.totalPages || 1);\n        setTotal(response.data.pagination?.total || 0);\n      } else {\n        setError('Failed to load archived announcements');\n      }\n    } catch (error: any) {\n      console.error('Error loading archived announcements:', error);\n      console.error('Error details:', error.response?.data || error);\n      setError(error.message || 'Failed to load archived announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRestore = async (announcementId: number) => {\n    if (!window.confirm('Are you sure you want to restore this announcement?')) {\n      return;\n    }\n\n    try {\n      setRestoring(announcementId);\n      const response = await archiveService.restoreAnnouncement(announcementId);\n      \n      if (response.success) {\n        alert('Announcement restored successfully!');\n        await loadAnnouncements();\n        onRestoreSuccess?.();\n      } else {\n        alert('Failed to restore announcement');\n      }\n    } catch (error: any) {\n      console.error('Error restoring announcement:', error);\n      alert(error.message || 'Failed to restore announcement');\n    } finally {\n      setRestoring(null);\n    }\n  };\n\n  const handlePermanentDelete = async (announcementId: number) => {\n    if (!window.confirm('Are you sure you want to PERMANENTLY delete this announcement? This action cannot be undone!')) {\n      return;\n    }\n\n    try {\n      setDeleting(announcementId);\n      const response = await archiveService.permanentlyDeleteAnnouncement(announcementId);\n      \n      if (response.success) {\n        alert('Announcement permanently deleted!');\n        await loadAnnouncements();\n        onRestoreSuccess?.();\n      } else {\n        alert('Failed to delete announcement');\n      }\n    } catch (error: any) {\n      console.error('Error deleting announcement:', error);\n      alert(error.message || 'Failed to delete announcement');\n    } finally {\n      setDeleting(null);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const truncateContent = (content: string, maxLength: number = 150) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength) + '...';\n  };\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '300px',\n        color: '#6b7280'\n      }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{\n            width: '32px',\n            height: '32px',\n            border: '3px solid #e5e7eb',\n            borderTop: '3px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            margin: '0 auto 1rem'\n          }} />\n          Loading archived announcements...\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Search and Filters */}\n      <div style={{\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '1.5rem',\n        alignItems: 'center'\n      }}>\n        <div style={{ position: 'relative', flex: 1, maxWidth: '400px' }}>\n          <Search\n            size={20}\n            style={{\n              position: 'absolute',\n              left: '12px',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#6b7280'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Search archived announcements...\"\n            value={searchTerm}\n            onChange={(e) => {\n              setSearchTerm(e.target.value);\n              setCurrentPage(1);\n            }}\n            style={{\n              width: '100%',\n              padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              transition: 'border-color 0.2s ease'\n            }}\n            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}\n            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}\n          />\n        </div>\n        \n        <div style={{\n          color: '#6b7280',\n          fontSize: '0.875rem',\n          fontWeight: '500'\n        }}>\n          {total} archived announcement{total !== 1 ? 's' : ''}\n        </div>\n      </div>\n\n      {error && (\n        <div style={{\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          padding: '1rem',\n          marginBottom: '1.5rem',\n          color: '#dc2626'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {/* Announcements List */}\n      {!announcements || announcements.length === 0 ? (\n        <div style={{\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        }}>\n          <div style={{\n            width: '64px',\n            height: '64px',\n            background: '#f3f4f6',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            margin: '0 auto 1rem'\n          }}>\n            <Search size={24} />\n          </div>\n          <h3 style={{ margin: '0 0 0.5rem', fontSize: '1.125rem', fontWeight: '600' }}>\n            No archived announcements found\n          </h3>\n          <p style={{ margin: 0, fontSize: '0.875rem' }}>\n            {searchTerm ? 'Try adjusting your search terms' : 'No announcements have been archived yet'}\n          </p>\n        </div>\n      ) : (\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          {announcements && announcements.map((announcement) => (\n            <div\n              key={announcement.announcement_id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb',\n                padding: '1.5rem',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n              }}\n            >\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ flex: 1 }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  }}>\n                    <h3 style={{\n                      margin: 0,\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {announcement.title}\n                    </h3>\n                    {announcement.is_alert && (\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        background: '#fef3c7',\n                        color: '#d97706',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      }}>\n                        <AlertTriangle size={12} />\n                        Alert\n                      </div>\n                    )}\n                    {announcement.is_pinned && (\n                      <div style={{\n                        background: '#dbeafe',\n                        color: '#1d4ed8',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      }}>\n                        Pinned\n                      </div>\n                    )}\n                  </div>\n                  \n                  <p style={{\n                    margin: '0 0 1rem',\n                    color: '#6b7280',\n                    fontSize: '0.875rem',\n                    lineHeight: '1.5'\n                  }}>\n                    {truncateContent(announcement.content)}\n                  </p>\n\n                  <div style={{\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: '1rem',\n                    fontSize: '0.75rem',\n                    color: '#6b7280'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                      <Tag size={12} />\n                      <span style={{\n                        background: announcement.category_color,\n                        color: 'white',\n                        padding: '0.125rem 0.375rem',\n                        borderRadius: '4px',\n                        fontSize: '0.6875rem',\n                        fontWeight: '500'\n                      }}>\n                        {announcement.category_name}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                      <User size={12} />\n                      {announcement.author_name}\n                    </div>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                      <Calendar size={12} />\n                      Deleted: {formatDate(announcement.deleted_at)}\n                    </div>\n                  </div>\n                </div>\n\n                <div style={{\n                  display: 'flex',\n                  gap: '0.5rem',\n                  marginLeft: '1rem'\n                }}>\n                  <button\n                    onClick={() => handleRestore(announcement.announcement_id)}\n                    disabled={restoring === announcement.announcement_id}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.5rem 1rem',\n                      background: '#10b981',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '6px',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: restoring === announcement.announcement_id ? 'not-allowed' : 'pointer',\n                      opacity: restoring === announcement.announcement_id ? 0.6 : 1,\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (restoring !== announcement.announcement_id) {\n                        e.currentTarget.style.background = '#059669';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (restoring !== announcement.announcement_id) {\n                        e.currentTarget.style.background = '#10b981';\n                      }\n                    }}\n                  >\n                    <RotateCcw size={14} />\n                    {restoring === announcement.announcement_id ? 'Restoring...' : 'Restore'}\n                  </button>\n\n                  <button\n                    onClick={() => handlePermanentDelete(announcement.announcement_id)}\n                    disabled={deleting === announcement.announcement_id}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.5rem 1rem',\n                      background: '#dc2626',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '6px',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: deleting === announcement.announcement_id ? 'not-allowed' : 'pointer',\n                      opacity: deleting === announcement.announcement_id ? 0.6 : 1,\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (deleting !== announcement.announcement_id) {\n                        e.currentTarget.style.background = '#b91c1c';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (deleting !== announcement.announcement_id) {\n                        e.currentTarget.style.background = '#dc2626';\n                      }\n                    }}\n                  >\n                    <Trash2 size={14} />\n                    {deleting === announcement.announcement_id ? 'Deleting...' : 'Delete'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          gap: '0.5rem',\n          marginTop: '2rem'\n        }}>\n          <button\n            onClick={() => setCurrentPage(currentPage - 1)}\n            disabled={currentPage === 1}\n            style={{\n              padding: '0.5rem 1rem',\n              background: currentPage === 1 ? '#f3f4f6' : '#3b82f6',\n              color: currentPage === 1 ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Previous\n          </button>\n          \n          <span style={{\n            padding: '0.5rem 1rem',\n            color: '#6b7280',\n            fontSize: '0.875rem'\n          }}>\n            Page {currentPage} of {totalPages}\n          </span>\n          \n          <button\n            onClick={() => setCurrentPage(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            style={{\n              padding: '0.5rem 1rem',\n              background: currentPage === totalPages ? '#f3f4f6' : '#3b82f6',\n              color: currentPage === totalPages ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ArchivedAnnouncements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,aAAa,QAAQ,cAAc;AAC5F,SAASC,cAAc,QAAiE,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3H,MAAMC,qBAA2D,GAAGA,CAAC;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAyB,EAAE,CAAC;EAC9E,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EAE7D,MAAMiC,KAAK,GAAG,EAAE;EAEhBhC,SAAS,CAAC,MAAM;IACdiC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACX,WAAW,EAAEF,UAAU,CAAC,CAAC;EAE7B,MAAMa,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMe,OAAuB,GAAG,CAAC,CAAC;MAClC,IAAId,UAAU,CAACe,IAAI,CAAC,CAAC,EAAE;QACrBD,OAAO,CAACE,MAAM,GAAGhB,UAAU,CAACe,IAAI,CAAC,CAAC;MACpC;MAEA,MAAME,UAA6B,GAAG;QACpCC,IAAI,EAAEhB,WAAW;QACjBU,KAAK;QACLO,OAAO,EAAE,YAAY;QACrBC,UAAU,EAAE;MACd,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMjC,cAAc,CAACkC,wBAAwB,CAACR,OAAO,EAAEG,UAAU,CAAC;MAEnF,IAAII,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACA,IAAI,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC3D/B,gBAAgB,CAAC0B,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;QACpCnB,aAAa,CAAC,EAAAoB,qBAAA,GAAAJ,QAAQ,CAACG,IAAI,CAACP,UAAU,cAAAQ,qBAAA,uBAAxBA,qBAAA,CAA0BrB,UAAU,KAAI,CAAC,CAAC;QACxDG,QAAQ,CAAC,EAAAmB,sBAAA,GAAAL,QAAQ,CAACG,IAAI,CAACP,UAAU,cAAAS,sBAAA,uBAAxBA,sBAAA,CAA0BpB,KAAK,KAAI,CAAC,CAAC;MAChD,CAAC,MAAM;QACLP,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MAAA,IAAA6B,eAAA;MACnBC,OAAO,CAAC9B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D8B,OAAO,CAAC9B,KAAK,CAAC,gBAAgB,EAAE,EAAA6B,eAAA,GAAA7B,KAAK,CAACuB,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBH,IAAI,KAAI1B,KAAK,CAAC;MAC9DC,QAAQ,CAACD,KAAK,CAAC+B,OAAO,IAAI,uCAAuC,CAAC;IACpE,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,aAAa,GAAG,MAAOC,cAAsB,IAAK;IACtD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAC1E;IACF;IAEA,IAAI;MACFxB,YAAY,CAACsB,cAAc,CAAC;MAC5B,MAAMV,QAAQ,GAAG,MAAMjC,cAAc,CAAC8C,mBAAmB,CAACH,cAAc,CAAC;MAEzE,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBY,KAAK,CAAC,qCAAqC,CAAC;QAC5C,MAAMtB,iBAAiB,CAAC,CAAC;QACzBrB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,CAAC;MACtB,CAAC,MAAM;QACL2C,KAAK,CAAC,gCAAgC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOrC,KAAU,EAAE;MACnB8B,OAAO,CAAC9B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDqC,KAAK,CAACrC,KAAK,CAAC+B,OAAO,IAAI,gCAAgC,CAAC;IAC1D,CAAC,SAAS;MACRpB,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;EAED,MAAM2B,qBAAqB,GAAG,MAAOL,cAAsB,IAAK;IAC9D,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8FAA8F,CAAC,EAAE;MACnH;IACF;IAEA,IAAI;MACFtB,WAAW,CAACoB,cAAc,CAAC;MAC3B,MAAMV,QAAQ,GAAG,MAAMjC,cAAc,CAACiD,6BAA6B,CAACN,cAAc,CAAC;MAEnF,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBY,KAAK,CAAC,mCAAmC,CAAC;QAC1C,MAAMtB,iBAAiB,CAAC,CAAC;QACzBrB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,CAAC;MACtB,CAAC,MAAM;QACL2C,KAAK,CAAC,+BAA+B,CAAC;MACxC;IACF,CAAC,CAAC,OAAOrC,KAAU,EAAE;MACnB8B,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDqC,KAAK,CAACrC,KAAK,CAAC+B,OAAO,IAAI,+BAA+B,CAAC;IACzD,CAAC,SAAS;MACRlB,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAChD,CAAC;EAED,IAAIrD,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK8D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,eACApE,OAAA;QAAK8D,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCpE,OAAA;UAAK8D,KAAK,EAAE;YACVQ,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE,mBAAmB;YAC9BC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE;UACV;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qCAEP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAAoE,QAAA,gBAEEpE,OAAA;MAAK8D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfkB,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE,QAAQ;QACtBjB,UAAU,EAAE;MACd,CAAE;MAAAG,QAAA,gBACApE,OAAA;QAAK8D,KAAK,EAAE;UAAEqB,QAAQ,EAAE,UAAU;UAAEC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAjB,QAAA,gBAC/DpE,OAAA,CAACT,MAAM;UACL+F,IAAI,EAAE,EAAG;UACTxB,KAAK,EAAE;YACLqB,QAAQ,EAAE,UAAU;YACpBI,IAAI,EAAE,MAAM;YACZC,GAAG,EAAE,KAAK;YACVC,SAAS,EAAE,kBAAkB;YAC7BtB,KAAK,EAAE;UACT;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFhF,OAAA;UACE0F,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,kCAAkC;UAC9CC,KAAK,EAAElF,UAAW;UAClBmF,QAAQ,EAAGC,CAAC,IAAK;YACfnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;YAC7B/E,cAAc,CAAC,CAAC,CAAC;UACnB,CAAE;UACFiD,KAAK,EAAE;YACLQ,KAAK,EAAE,MAAM;YACb0B,OAAO,EAAE,gCAAgC;YACzCxB,MAAM,EAAE,mBAAmB;YAC3BE,YAAY,EAAE,KAAK;YACnBuB,QAAQ,EAAE,UAAU;YACpBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGN,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACuC,WAAW,GAAG,SAAU;UACvDC,MAAM,EAAGR,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACuC,WAAW,GAAG;QAAU;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhF,OAAA;QAAK8D,KAAK,EAAE;UACVK,KAAK,EAAE,SAAS;UAChB8B,QAAQ,EAAE,UAAU;UACpBM,UAAU,EAAE;QACd,CAAE;QAAAnC,QAAA,GACCpD,KAAK,EAAC,wBAAsB,EAACA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxE,KAAK,iBACJR,OAAA;MAAK8D,KAAK,EAAE;QACV0C,UAAU,EAAE,SAAS;QACrBhC,MAAM,EAAE,mBAAmB;QAC3BE,YAAY,EAAE,KAAK;QACnBsB,OAAO,EAAE,MAAM;QACfd,YAAY,EAAE,QAAQ;QACtBf,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,EACC5D;IAAK;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAAC5E,aAAa,IAAIA,aAAa,CAACwD,MAAM,KAAK,CAAC,gBAC3C5D,OAAA;MAAK8D,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnB2B,OAAO,EAAE,MAAM;QACf7B,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACApE,OAAA;QAAK8D,KAAK,EAAE;UACVQ,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdiC,UAAU,EAAE,SAAS;UACrB9B,YAAY,EAAE,KAAK;UACnBX,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBY,MAAM,EAAE;QACV,CAAE;QAAAR,QAAA,eACApE,OAAA,CAACT,MAAM;UAAC+F,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACNhF,OAAA;QAAI8D,KAAK,EAAE;UAAEc,MAAM,EAAE,YAAY;UAAEqB,QAAQ,EAAE,UAAU;UAAEM,UAAU,EAAE;QAAM,CAAE;QAAAnC,QAAA,EAAC;MAE9E;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLhF,OAAA;QAAG8D,KAAK,EAAE;UAAEc,MAAM,EAAE,CAAC;UAAEqB,QAAQ,EAAE;QAAW,CAAE;QAAA7B,QAAA,EAC3C1D,UAAU,GAAG,iCAAiC,GAAG;MAAyC;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAENhF,OAAA;MAAK8D,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAE0C,aAAa,EAAE,QAAQ;QAAExB,GAAG,EAAE;MAAO,CAAE;MAAAb,QAAA,EACnEhE,aAAa,IAAIA,aAAa,CAACsG,GAAG,CAAEC,YAAY,iBAC/C3G,OAAA;QAEE8D,KAAK,EAAE;UACL0C,UAAU,EAAE,OAAO;UACnB9B,YAAY,EAAE,MAAM;UACpBF,MAAM,EAAE,mBAAmB;UAC3BwB,OAAO,EAAE,QAAQ;UACjBY,SAAS,EAAE,8BAA8B;UACzCT,UAAU,EAAE;QACd,CAAE;QACFU,YAAY,EAAGf,CAAC,IAAK;UACnBA,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC8C,SAAS,GAAG,gCAAgC;QACpE,CAAE;QACFG,YAAY,EAAGjB,CAAC,IAAK;UACnBA,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC8C,SAAS,GAAG,8BAA8B;QAClE,CAAE;QAAAxC,QAAA,eAEFpE,OAAA;UAAK8D,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,YAAY;YACxBiB,YAAY,EAAE;UAChB,CAAE;UAAAd,QAAA,gBACApE,OAAA;YAAK8D,KAAK,EAAE;cAAEsB,IAAI,EAAE;YAAE,CAAE;YAAAhB,QAAA,gBACtBpE,OAAA;cAAK8D,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBgB,GAAG,EAAE,QAAQ;gBACbC,YAAY,EAAE;cAChB,CAAE;cAAAd,QAAA,gBACApE,OAAA;gBAAI8D,KAAK,EAAE;kBACTc,MAAM,EAAE,CAAC;kBACTqB,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBpC,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EACCuC,YAAY,CAACK;cAAK;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACJ2B,YAAY,CAACM,QAAQ,iBACpBjH,OAAA;gBAAK8D,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBgB,GAAG,EAAE,SAAS;kBACduB,UAAU,EAAE,SAAS;kBACrBrC,KAAK,EAAE,SAAS;kBAChB6B,OAAO,EAAE,gBAAgB;kBACzBtB,YAAY,EAAE,KAAK;kBACnBuB,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE;gBACd,CAAE;gBAAAnC,QAAA,gBACApE,OAAA,CAACH,aAAa;kBAACyF,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACA2B,YAAY,CAACO,SAAS,iBACrBlH,OAAA;gBAAK8D,KAAK,EAAE;kBACV0C,UAAU,EAAE,SAAS;kBACrBrC,KAAK,EAAE,SAAS;kBAChB6B,OAAO,EAAE,gBAAgB;kBACzBtB,YAAY,EAAE,KAAK;kBACnBuB,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE;gBACd,CAAE;gBAAAnC,QAAA,EAAC;cAEH;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENhF,OAAA;cAAG8D,KAAK,EAAE;gBACRc,MAAM,EAAE,UAAU;gBAClBT,KAAK,EAAE,SAAS;gBAChB8B,QAAQ,EAAE,UAAU;gBACpBkB,UAAU,EAAE;cACd,CAAE;cAAA/C,QAAA,EACCX,eAAe,CAACkD,YAAY,CAACjD,OAAO;YAAC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eAEJhF,OAAA;cAAK8D,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfqD,QAAQ,EAAE,MAAM;gBAChBnC,GAAG,EAAE,MAAM;gBACXgB,QAAQ,EAAE,SAAS;gBACnB9B,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,gBACApE,OAAA;gBAAK8D,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEgB,GAAG,EAAE;gBAAU,CAAE;gBAAAb,QAAA,gBACpEpE,OAAA,CAACJ,GAAG;kBAAC0F,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjBhF,OAAA;kBAAM8D,KAAK,EAAE;oBACX0C,UAAU,EAAEG,YAAY,CAACU,cAAc;oBACvClD,KAAK,EAAE,OAAO;oBACd6B,OAAO,EAAE,mBAAmB;oBAC5BtB,YAAY,EAAE,KAAK;oBACnBuB,QAAQ,EAAE,WAAW;oBACrBM,UAAU,EAAE;kBACd,CAAE;kBAAAnC,QAAA,EACCuC,YAAY,CAACW;gBAAa;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhF,OAAA;gBAAK8D,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEgB,GAAG,EAAE;gBAAU,CAAE;gBAAAb,QAAA,gBACpEpE,OAAA,CAACL,IAAI;kBAAC2F,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACjB2B,YAAY,CAACY,WAAW;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNhF,OAAA;gBAAK8D,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEgB,GAAG,EAAE;gBAAU,CAAE;gBAAAb,QAAA,gBACpEpE,OAAA,CAACN,QAAQ;kBAAC4F,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aACb,EAAChC,UAAU,CAAC2D,YAAY,CAACa,UAAU,CAAC;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK8D,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfkB,GAAG,EAAE,QAAQ;cACbwC,UAAU,EAAE;YACd,CAAE;YAAArD,QAAA,gBACApE,OAAA;cACE0H,OAAO,EAAEA,CAAA,KAAMlF,aAAa,CAACmE,YAAY,CAACgB,eAAe,CAAE;cAC3DC,QAAQ,EAAE1G,SAAS,KAAKyF,YAAY,CAACgB,eAAgB;cACrD7D,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBgB,GAAG,EAAE,QAAQ;gBACbe,OAAO,EAAE,aAAa;gBACtBQ,UAAU,EAAE,SAAS;gBACrBrC,KAAK,EAAE,OAAO;gBACdK,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnBuB,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE3G,SAAS,KAAKyF,YAAY,CAACgB,eAAe,GAAG,aAAa,GAAG,SAAS;gBAC9EG,OAAO,EAAE5G,SAAS,KAAKyF,YAAY,CAACgB,eAAe,GAAG,GAAG,GAAG,CAAC;gBAC7DxB,UAAU,EAAE;cACd,CAAE;cACFU,YAAY,EAAGf,CAAC,IAAK;gBACnB,IAAI5E,SAAS,KAAKyF,YAAY,CAACgB,eAAe,EAAE;kBAC9C7B,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC0C,UAAU,GAAG,SAAS;gBAC9C;cACF,CAAE;cACFO,YAAY,EAAGjB,CAAC,IAAK;gBACnB,IAAI5E,SAAS,KAAKyF,YAAY,CAACgB,eAAe,EAAE;kBAC9C7B,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC0C,UAAU,GAAG,SAAS;gBAC9C;cACF,CAAE;cAAApC,QAAA,gBAEFpE,OAAA,CAACR,SAAS;gBAAC8F,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACtB9D,SAAS,KAAKyF,YAAY,CAACgB,eAAe,GAAG,cAAc,GAAG,SAAS;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAEThF,OAAA;cACE0H,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC6D,YAAY,CAACgB,eAAe,CAAE;cACnEC,QAAQ,EAAExG,QAAQ,KAAKuF,YAAY,CAACgB,eAAgB;cACpD7D,KAAK,EAAE;gBACLC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBgB,GAAG,EAAE,QAAQ;gBACbe,OAAO,EAAE,aAAa;gBACtBQ,UAAU,EAAE,SAAS;gBACrBrC,KAAK,EAAE,OAAO;gBACdK,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnBuB,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAEzG,QAAQ,KAAKuF,YAAY,CAACgB,eAAe,GAAG,aAAa,GAAG,SAAS;gBAC7EG,OAAO,EAAE1G,QAAQ,KAAKuF,YAAY,CAACgB,eAAe,GAAG,GAAG,GAAG,CAAC;gBAC5DxB,UAAU,EAAE;cACd,CAAE;cACFU,YAAY,EAAGf,CAAC,IAAK;gBACnB,IAAI1E,QAAQ,KAAKuF,YAAY,CAACgB,eAAe,EAAE;kBAC7C7B,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC0C,UAAU,GAAG,SAAS;gBAC9C;cACF,CAAE;cACFO,YAAY,EAAGjB,CAAC,IAAK;gBACnB,IAAI1E,QAAQ,KAAKuF,YAAY,CAACgB,eAAe,EAAE;kBAC7C7B,CAAC,CAACgB,aAAa,CAAChD,KAAK,CAAC0C,UAAU,GAAG,SAAS;gBAC9C;cACF,CAAE;cAAApC,QAAA,gBAEFpE,OAAA,CAACP,MAAM;gBAAC6F,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnB5D,QAAQ,KAAKuF,YAAY,CAACgB,eAAe,GAAG,aAAa,GAAG,QAAQ;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAlLD2B,YAAY,CAACgB,eAAe;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmL9B,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAlE,UAAU,GAAG,CAAC,iBACbd,OAAA;MAAK8D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBgB,GAAG,EAAE,QAAQ;QACb8C,SAAS,EAAE;MACb,CAAE;MAAA3D,QAAA,gBACApE,OAAA;QACE0H,OAAO,EAAEA,CAAA,KAAM7G,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;QAC/CgH,QAAQ,EAAEhH,WAAW,KAAK,CAAE;QAC5BkD,KAAK,EAAE;UACLkC,OAAO,EAAE,aAAa;UACtBQ,UAAU,EAAE5F,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;UACrDuD,KAAK,EAAEvD,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;UAC9C4D,MAAM,EAAE,MAAM;UACdE,YAAY,EAAE,KAAK;UACnBuB,QAAQ,EAAE,UAAU;UACpB4B,MAAM,EAAEjH,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;QAC9C,CAAE;QAAAwD,QAAA,EACH;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEThF,OAAA;QAAM8D,KAAK,EAAE;UACXkC,OAAO,EAAE,aAAa;UACtB7B,KAAK,EAAE,SAAS;UAChB8B,QAAQ,EAAE;QACZ,CAAE;QAAA7B,QAAA,GAAC,OACI,EAACxD,WAAW,EAAC,MAAI,EAACE,UAAU;MAAA;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEPhF,OAAA;QACE0H,OAAO,EAAEA,CAAA,KAAM7G,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;QAC/CgH,QAAQ,EAAEhH,WAAW,KAAKE,UAAW;QACrCgD,KAAK,EAAE;UACLkC,OAAO,EAAE,aAAa;UACtBQ,UAAU,EAAE5F,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,SAAS;UAC9DqD,KAAK,EAAEvD,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,OAAO;UACvD0D,MAAM,EAAE,MAAM;UACdE,YAAY,EAAE,KAAK;UACnBuB,QAAQ,EAAE,UAAU;UACpB4B,MAAM,EAAEjH,WAAW,KAAKE,UAAU,GAAG,aAAa,GAAG;QACvD,CAAE;QAAAsD,QAAA,EACH;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7E,EAAA,CAtdIF,qBAA2D;AAAA+H,EAAA,GAA3D/H,qBAA2D;AAwdjE,eAAeA,qBAAqB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}