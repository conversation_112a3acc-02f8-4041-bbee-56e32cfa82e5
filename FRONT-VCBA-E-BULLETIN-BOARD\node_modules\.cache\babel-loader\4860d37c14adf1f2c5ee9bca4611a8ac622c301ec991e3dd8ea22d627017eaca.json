{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 21s-4-3-4-9 4-9 4-9\",\n  key: \"uto9ud\"\n}], [\"path\", {\n  d: \"M16 3s4 3 4 9-4 9-4 9\",\n  key: \"4w2vsq\"\n}]];\nconst Parentheses = createLucideIcon(\"parentheses\", __iconNode);\nexport { __iconNode, Parentheses as default };\n//# sourceMappingURL=parentheses.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}