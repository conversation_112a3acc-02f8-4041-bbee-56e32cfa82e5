{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"m16.2 7.8 2.9-2.9\",\n  key: \"r700ao\"\n}], [\"path\", {\n  d: \"M18 12h4\",\n  key: \"wj9ykh\"\n}], [\"path\", {\n  d: \"m16.2 16.2 2.9 2.9\",\n  key: \"1bxg5t\"\n}], [\"path\", {\n  d: \"M12 18v4\",\n  key: \"jadmvz\"\n}], [\"path\", {\n  d: \"m4.9 19.1 2.9-2.9\",\n  key: \"bwix9q\"\n}], [\"path\", {\n  d: \"M2 12h4\",\n  key: \"j09sii\"\n}], [\"path\", {\n  d: \"m4.9 4.9 2.9 2.9\",\n  key: \"giyufr\"\n}]];\nconst Loader = createLucideIcon(\"loader\", __iconNode);\nexport { __iconNode, Loader as default };\n//# sourceMappingURL=loader.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}