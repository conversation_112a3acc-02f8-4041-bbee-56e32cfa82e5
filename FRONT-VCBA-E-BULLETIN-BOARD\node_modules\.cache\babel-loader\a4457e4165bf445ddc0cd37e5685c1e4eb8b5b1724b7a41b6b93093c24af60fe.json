{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M7.5 3v18\",\n  key: \"w0wo6v\"\n}], [\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"path\", {\n  d: \"M16.5 3v18\",\n  key: \"10tjh1\"\n}]];\nconst Columns4 = createLucideIcon(\"columns-4\", __iconNode);\nexport { __iconNode, Columns4 as default };\n//# sourceMappingURL=columns-4.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}