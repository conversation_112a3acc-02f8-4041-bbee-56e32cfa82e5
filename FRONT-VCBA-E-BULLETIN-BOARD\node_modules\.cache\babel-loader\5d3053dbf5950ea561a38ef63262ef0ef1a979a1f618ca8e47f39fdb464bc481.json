{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v8\",\n  key: \"1q4o3n\"\n}], [\"path\", {\n  d: \"m16 6-4 4-4-4\",\n  key: \"6wukr\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"w68u3i\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"M10 18h.01\",\n  key: \"h775k\"\n}]];\nconst HardDriveDownload = createLucideIcon(\"hard-drive-download\", __iconNode);\nexport { __iconNode, HardDriveDownload as default };\n//# sourceMappingURL=hard-drive-download.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}