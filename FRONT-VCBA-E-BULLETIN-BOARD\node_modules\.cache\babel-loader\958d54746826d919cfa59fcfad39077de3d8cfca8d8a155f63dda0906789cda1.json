{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"9\",\n  height: \"6\",\n  x: \"6\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"lpm2y7\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"6\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"rdj6ps\"\n}], [\"path\", {\n  d: \"M2 2v20\",\n  key: \"1ivd8o\"\n}]];\nconst AlignStartVertical = createLucideIcon(\"align-start-vertical\", __iconNode);\nexport { __iconNode, AlignStartVertical as default };\n//# sourceMappingURL=align-start-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}