{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n  key: \"13zald\"\n}]];\nconst LoaderCircle = createLucideIcon(\"loader-circle\", __iconNode);\nexport { __iconNode, LoaderCircle as default };\n//# sourceMappingURL=loader-circle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}