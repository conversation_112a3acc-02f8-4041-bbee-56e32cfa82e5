{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 6v6\",\n  key: \"18i7km\"\n}], [\"path\", {\n  d: \"M15 6v6\",\n  key: \"1sg6z9\"\n}], [\"path\", {\n  d: \"M2 12h19.6\",\n  key: \"de5uta\"\n}], [\"path\", {\n  d: \"M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3\",\n  key: \"1wwztk\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}], [\"path\", {\n  d: \"M9 18h5\",\n  key: \"lrx6i\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1v4tcr\"\n}]];\nconst Bus = createLucideIcon(\"bus\", __iconNode);\nexport { __iconNode, Bus as default };\n//# sourceMappingURL=bus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}