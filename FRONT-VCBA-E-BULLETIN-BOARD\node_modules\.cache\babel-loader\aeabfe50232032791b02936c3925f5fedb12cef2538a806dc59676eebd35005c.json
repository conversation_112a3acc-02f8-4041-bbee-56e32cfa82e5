{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{calendarReactionService}from'../../services/calendarReactionService';import{useCategories,useAnnouncements}from'../../hooks/useAnnouncements';import{useNotificationTarget}from'../../hooks/useNotificationNavigation';import{useUnifiedAuth}from'../../hooks/useUnifiedAuth';import UnifiedCommentSection from'./UnifiedCommentSection';import NotificationBell from'../admin/NotificationBell';import StudentNotificationBell from'../student/NotificationBell';import FacebookImageGallery from'./FacebookImageGallery';import ImageLightbox from'./ImageLightbox';import{getImageUrl,API_BASE_URL,ADMIN_AUTH_TOKEN_KEY,STUDENT_AUTH_TOKEN_KEY}from'../../config/constants';import'../../styles/notificationHighlight.css';import{Newspaper,Search,Pin,Calendar,MessageSquare,Heart,Edit,Users,AlertTriangle,ChevronDown,User,LogOut}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const UnifiedNewsfeed=_ref=>{var _currentAuth$user3,_currentAuth$user4;let{forceRole}=_ref;const navigate=useNavigate();// Use unified auth hook\nconst{userContext,currentAuth,isLoading:authLoading,error:authError}=useUnifiedAuth(forceRole);// Redirect if not authenticated\nuseEffect(()=>{if(!userContext.isAuthenticated||!currentAuth){const loginPath=userContext.role==='student'?'/student/login':'/admin/login';navigate(loginPath);return;}},[userContext.isAuthenticated,currentAuth,navigate,userContext.role]);// UI States\nconst[searchTerm,setSearchTerm]=useState('');const[selectedCategory,setSelectedCategory]=useState('all');const[showFilters,setShowFilters]=useState(false);const[lightboxOpen,setLightboxOpen]=useState(false);const[lightboxImages,setLightboxImages]=useState([]);const[lightboxInitialIndex,setLightboxInitialIndex]=useState(0);// Data states\nconst[pinnedAnnouncements,setPinnedAnnouncements]=useState([]);const[calendarEvents,setCalendarEvents]=useState([]);const[calendarLoading,setCalendarLoading]=useState(false);const[calendarError,setCalendarError]=useState();const[recentStudents,setRecentStudents]=useState([]);const[studentLoading,setStudentLoading]=useState(false);const{categories}=useCategories();// Use the announcements hook with role-appropriate service\nconst{announcements,loading,error,likeAnnouncement,unlikeAnnouncement,refresh:refreshAnnouncements}=useAnnouncements({status:'published',page:1,limit:50,sort_by:'created_at',sort_order:'DESC'},userContext.role==='admin');// true for admin service, false for student\n// Handle notification-triggered navigation\nconst{isFromNotification,notificationId,scrollTarget}=useNotificationTarget();// Update pinned announcements when announcements change\nuseEffect(()=>{const pinned=announcements.filter(ann=>ann.is_pinned===1);setPinnedAnnouncements(pinned);},[announcements]);// Fetch calendar events\nconst fetchCalendarEvents=async()=>{try{setCalendarLoading(true);setCalendarError(undefined);const tokenKey=userContext.role==='admin'?ADMIN_AUTH_TOKEN_KEY:STUDENT_AUTH_TOKEN_KEY;const response=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar/events\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem(tokenKey)),'Content-Type':'application/json'}});const data=await response.json();if(data.success&&data.data){const eventsData=data.data.events||data.data||[];// Fetch images for each event\nconst eventsWithImages=await Promise.all(eventsData.map(async event=>{try{const imageResponse=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar/\").concat(event.calendar_id,\"/images\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem(tokenKey)),'Content-Type':'application/json'}});const imageData=await imageResponse.json();if(imageData.success&&imageData.data){event.images=imageData.data.attachments||[];}else{event.images=[];}}catch(imgErr){console.warn(\"Failed to fetch images for event \".concat(event.calendar_id,\":\"),imgErr);event.images=[];}return event;}));setCalendarEvents(eventsWithImages);}}catch(err){console.error('Error fetching calendar events:',err);setCalendarError(err.message||'Failed to fetch calendar events');}finally{setCalendarLoading(false);}};// Fetch recent students (admin only)\nconst fetchRecentStudents=async()=>{if(userContext.role!=='admin')return;try{setStudentLoading(true);const response=await fetch(\"\".concat(API_BASE_URL,\"/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC\"),{headers:{'Authorization':\"Bearer \".concat(localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)),'Content-Type':'application/json'}});const data=await response.json();if(data.success&&data.data){setRecentStudents(data.data.students||[]);}}catch(err){console.error('Error fetching recent students:',err);}finally{setStudentLoading(false);}};// Initial data fetch\nuseEffect(()=>{fetchCalendarEvents();if(userContext.role==='admin'){// fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n}},[userContext.role]);// Handle like/unlike functionality\nconst handleLikeToggle=async announcement=>{try{var _currentAuth$user;console.log(\"[DEBUG] \".concat(userContext.role,\" toggling reaction for announcement:\"),announcement.announcement_id);console.log('[DEBUG] Current user_reaction:',announcement.user_reaction);console.log(\"[DEBUG] \".concat(userContext.role,\" user context:\"),{id:currentAuth===null||currentAuth===void 0?void 0:(_currentAuth$user=currentAuth.user)===null||_currentAuth$user===void 0?void 0:_currentAuth$user.id,role:userContext.role});if(announcement.user_reaction){// Unlike the announcement\nconsole.log(\"[DEBUG] \".concat(userContext.role,\" removing reaction...\"));await unlikeAnnouncement(announcement.announcement_id);}else{// Like the announcement\nconsole.log(\"[DEBUG] \".concat(userContext.role,\" adding reaction...\"));await likeAnnouncement(announcement.announcement_id,1);}console.log(\"[SUCCESS] \".concat(userContext.role,\" reaction toggled successfully\"));}catch(error){console.error(\"[ERROR] \".concat(userContext.role,\" error toggling like:\"),error);}};// Handle calendar event like/unlike functionality\nconst handleCalendarLikeToggle=async event=>{try{var _currentAuth$user2;console.log(\"[DEBUG] \".concat(userContext.role,\" toggling reaction for calendar event:\"),event.calendar_id);console.log('[DEBUG] Current user_has_reacted:',event.user_has_reacted);console.log(\"[DEBUG] \".concat(userContext.role,\" user context:\"),{id:currentAuth===null||currentAuth===void 0?void 0:(_currentAuth$user2=currentAuth.user)===null||_currentAuth$user2===void 0?void 0:_currentAuth$user2.id,role:userContext.role});const response=await calendarReactionService.toggleLike(event.calendar_id,event.user_has_reacted||false);if(response.success){// Update the local state\nsetCalendarEvents(prevEvents=>prevEvents.map(e=>e.calendar_id===event.calendar_id?_objectSpread(_objectSpread({},e),{},{user_has_reacted:!e.user_has_reacted,reaction_count:e.user_has_reacted?(e.reaction_count||1)-1:(e.reaction_count||0)+1}):e));console.log(\"[SUCCESS] \".concat(userContext.role,\" calendar reaction toggled successfully\"));}}catch(error){console.error(\"[ERROR] \".concat(userContext.role,\" error toggling calendar like:\"),error);}};// Open lightbox function for announcements\nconst openLightbox=(images,initialIndex)=>{const imageUrls=images.map(img=>getImageUrl(img.file_path)).filter(Boolean);setLightboxImages(imageUrls);setLightboxInitialIndex(initialIndex);setLightboxOpen(true);};// Open lightbox function for image URLs (calendar events)\nconst openLightboxWithUrls=(imageUrls,initialIndex)=>{setLightboxImages(imageUrls);setLightboxInitialIndex(initialIndex);setLightboxOpen(true);};// Don't render if not authenticated\nif(!userContext.isAuthenticated||!currentAuth){return null;}return/*#__PURE__*/_jsxs(\"div\",{style:{minHeight:'100vh',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',padding:'2rem',fontFamily:'Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'2rem',background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',backdropFilter:'blur(10px)',boxShadow:'0 8px 32px rgba(0, 0, 0, 0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Newspaper,{size:24,color:\"white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{margin:0,fontSize:'1.875rem',fontWeight:'700',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',backgroundClip:'text'},children:userContext.role==='admin'?'Admin Newsfeed':'Student Newsfeed'}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,color:'#6b7280',fontSize:'0.875rem'},children:userContext.role==='admin'?'Monitor and manage announcements & events':'Stay updated with latest announcements & events'})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[userContext.role==='admin'?/*#__PURE__*/_jsx(NotificationBell,{}):/*#__PURE__*/_jsx(StudentNotificationBell,{}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.5rem 1rem',background:'rgba(103, 126, 234, 0.1)',borderRadius:'12px',border:'1px solid rgba(103, 126, 234, 0.2)'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'32px',height:'32px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'8px',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(User,{size:16,color:\"white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#374151'},children:[(_currentAuth$user3=currentAuth.user)===null||_currentAuth$user3===void 0?void 0:_currentAuth$user3.first_name,\" \",(_currentAuth$user4=currentAuth.user)===null||_currentAuth$user4===void 0?void 0:_currentAuth$user4.last_name]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280',textTransform:'capitalize'},children:userContext.role})]}),/*#__PURE__*/_jsx(\"button\",{onClick:currentAuth.logout,style:{background:'none',border:'none',cursor:'pointer',padding:'0.25rem',borderRadius:'6px',display:'flex',alignItems:'center',justifyContent:'center',color:'#6b7280',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='rgba(239, 68, 68, 0.1)';e.currentTarget.style.color='#ef4444';},onMouseLeave:e=>{e.currentTarget.style.background='none';e.currentTarget.style.color='#6b7280';},children:/*#__PURE__*/_jsx(LogOut,{size:16})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',marginBottom:'2rem',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative',marginBottom:showFilters?'1.5rem':'0'},children:[/*#__PURE__*/_jsx(Search,{size:20,style:{position:'absolute',left:'1rem',top:'50%',transform:'translateY(-50%)',color:'#9ca3af'}}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search announcements and events...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{width:'100%',padding:'0.875rem 1rem 0.875rem 3rem',border:'2px solid #e5e7eb',borderRadius:'12px',fontSize:'1rem',outline:'none',transition:'all 0.2s ease',background:'white'},onFocus:e=>{e.currentTarget.style.borderColor='#667eea';e.currentTarget.style.boxShadow='0 0 0 3px rgba(103, 126, 234, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#e5e7eb';e.currentTarget.style.boxShadow='none';}})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowFilters(!showFilters),style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem 1rem',background:showFilters?'#667eea':'transparent',color:showFilters?'white':'#667eea',border:\"2px solid #667eea\",borderRadius:'8px',cursor:'pointer',fontSize:'0.875rem',fontWeight:'500',transition:'all 0.2s ease'},children:[\"Filters\",/*#__PURE__*/_jsx(ChevronDown,{size:16,style:{transform:showFilters?'rotate(180deg)':'rotate(0deg)',transition:'transform 0.2s ease'}})]})}),showFilters&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1.5rem',padding:'1.5rem',background:'#f9fafb',borderRadius:'12px',border:'1px solid #e5e7eb'},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'600',color:'#374151',marginBottom:'0.5rem'},children:\"Category\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),style:{width:'100%',padding:'0.5rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',background:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.category_id.toString(),children:category.category_name},category.category_id))]})]})})})]}),(loading||calendarLoading||authLoading)&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',padding:'3rem',background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',marginBottom:'2rem'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'40px',height:'40px',border:'4px solid #e5e7eb',borderTop:'4px solid #667eea',borderRadius:'50%',animation:'spin 1s linear infinite'}})}),(error||calendarError||authError)&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(239, 68, 68, 0.1)',border:'1px solid rgba(239, 68, 68, 0.2)',borderRadius:'12px',padding:'1rem',marginBottom:'2rem',color:'#dc2626'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:20}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600'},children:\"Error\"})]}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0.5rem 0 0 0',fontSize:'0.875rem'},children:error||calendarError||authError})]}),!loading&&!calendarLoading&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:userContext.role==='admin'?'1fr 300px':'1fr',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[pinnedAnnouncements.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'2px solid #fbbf24',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(251, 191, 36, 0.2)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(Pin,{size:20,color:\"#f59e0b\"}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.125rem',fontWeight:'600',color:'#f59e0b'},children:\"Pinned Announcements\"})]}),pinnedAnnouncements.map(announcement=>{var _announcement$content,_announcement$content2;return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem',background:'#fffbeb',borderRadius:'12px',border:'1px solid #fde68a',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 0.5rem 0',fontSize:'1rem',fontWeight:'600',color:'#92400e'},children:announcement.title}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#a16207',lineHeight:'1.5'},children:[(_announcement$content=announcement.content)===null||_announcement$content===void 0?void 0:_announcement$content.substring(0,150),((_announcement$content2=announcement.content)===null||_announcement$content2===void 0?void 0:_announcement$content2.length)>150&&'...']})]},\"pinned-\".concat(announcement.announcement_id));})]}),announcements.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:announcements.filter(ann=>ann.is_pinned!==1)// Exclude pinned announcements\n.filter(ann=>{var _ann$category_id;const matchesSearch=!searchTerm||ann.title.toLowerCase().includes(searchTerm.toLowerCase())||ann.content.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=selectedCategory==='all'||((_ann$category_id=ann.category_id)===null||_ann$category_id===void 0?void 0:_ann$category_id.toString())===selectedCategory;return matchesSearch&&matchesCategory;}).map(announcement=>{var _currentAuth$user5;return/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(Newspaper,{size:24,color:\"white\"})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'700',color:'#111827',lineHeight:'1.3'},children:announcement.title}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCE2 Announcement\"}),announcement.category_name&&/*#__PURE__*/_jsx(\"span\",{style:{background:'#e0e7ff',color:'#3730a3',padding:'0.25rem 0.5rem',borderRadius:'6px',fontSize:'0.75rem',fontWeight:'500'},children:announcement.category_name})]})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',background:'#f9fafb',borderRadius:'12px',border:'1px solid #e5e7eb'},children:/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#374151',lineHeight:'1.6',whiteSpace:'pre-wrap'},children:announcement.content})}),announcement.attachments&&announcement.attachments.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:announcement.attachments,altPrefix:\"Announcement: \".concat(announcement.title),onImageClick:index=>openLightbox(announcement.attachments,index)})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleLikeToggle(announcement),style:{display:'flex',alignItems:'center',gap:'0.5rem',background:'none',border:'none',cursor:'pointer',padding:'0.5rem',borderRadius:'8px',color:announcement.user_reaction?'#ef4444':'#6b7280',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='rgba(239, 68, 68, 0.1)';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:[/*#__PURE__*/_jsx(Heart,{size:18,fill:announcement.user_reaction?'#ef4444':'none'}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:announcement.total_reactions||0})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',color:'#6b7280'},children:[/*#__PURE__*/_jsx(MessageSquare,{size:18}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:announcement.total_comments||0})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#9ca3af'},children:new Date(announcement.created_at).toLocaleDateString()})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem'},children:/*#__PURE__*/_jsx(UnifiedCommentSection,{announcementId:announcement.announcement_id,currentUserId:currentAuth===null||currentAuth===void 0?void 0:(_currentAuth$user5=currentAuth.user)===null||_currentAuth$user5===void 0?void 0:_currentAuth$user5.id,currentUserType:userContext.role})})]},\"announcement-\".concat(announcement.announcement_id));})}),calendarEvents.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:calendarEvents.filter(event=>{const matchesSearch=!searchTerm||event.title.toLowerCase().includes(searchTerm.toLowerCase())||event.description&&event.description.toLowerCase().includes(searchTerm.toLowerCase());// Show events that are currently active (between start and end date)\nconst today=new Date();const todayDateString=today.getFullYear()+'-'+String(today.getMonth()+1).padStart(2,'0')+'-'+String(today.getDate()).padStart(2,'0');const eventStartDate=event.event_date;const eventEndDate=event.end_date||event.event_date;const isActive=todayDateString>=eventStartDate&&todayDateString<=eventEndDate;return matchesSearch&&isActive;}).map(event=>{var _currentAuth$user6;return/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'48px',height:'48px',background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(Calendar,{size:24,color:\"white\"})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.5rem 0',fontSize:'1.25rem',fontWeight:'700',color:'#111827',lineHeight:'1.3'},children:event.title}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\uD83D\\uDCC5 \",new Date(event.event_date).toLocaleDateString()]}),event.end_date&&event.end_date!==event.event_date&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u2192 \",new Date(event.end_date).toLocaleDateString()]})]})]})]}),event.description&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem',padding:'1rem',background:'#f9fafb',borderRadius:'12px',border:'1px solid #e5e7eb'},children:/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#374151',lineHeight:'1.6'},children:event.description})}),event.images&&event.images.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:event.images.map(img=>getImageUrl(img.file_path)),altPrefix:\"Event: \".concat(event.title),onImageClick:index=>{const imageUrls=event.images.map(img=>getImageUrl(img.file_path));openLightboxWithUrls(imageUrls,index);}})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem'},children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleCalendarLikeToggle(event),style:{display:'flex',alignItems:'center',gap:'0.5rem',background:'none',border:'none',cursor:'pointer',padding:'0.5rem',borderRadius:'8px',color:event.user_has_reacted?'#ef4444':'#6b7280',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='rgba(239, 68, 68, 0.1)';},onMouseLeave:e=>{e.currentTarget.style.background='none';},children:[/*#__PURE__*/_jsx(Heart,{size:18,fill:event.user_has_reacted?'#ef4444':'none'}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:event.reaction_count||0})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#9ca3af'},children:[\"Event \\u2022 \",new Date(event.created_at).toLocaleDateString()]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem'},children:/*#__PURE__*/_jsx(UnifiedCommentSection,{calendarId:event.calendar_id,currentUserId:currentAuth===null||currentAuth===void 0?void 0:(_currentAuth$user6=currentAuth.user)===null||_currentAuth$user6===void 0?void 0:_currentAuth$user6.id,currentUserType:userContext.role})})]},\"event-\".concat(event.calendar_id));})})]}),userContext.role==='admin'&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1rem 0',fontSize:'1.125rem',fontWeight:'600',color:'#111827'},children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'0.75rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/admin/posts'),style:{display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontSize:'0.875rem',fontWeight:'500',transition:'transform 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';},children:[/*#__PURE__*/_jsx(Edit,{size:16}),\"Create Post\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/admin/calendar'),style:{display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem',background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontSize:'0.875rem',fontWeight:'500',transition:'transform 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';},children:[/*#__PURE__*/_jsx(Calendar,{size:16}),\"Manage Calendar\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/admin/student-management'),style:{display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem',background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',color:'white',border:'none',borderRadius:'8px',cursor:'pointer',fontSize:'0.875rem',fontWeight:'500',transition:'transform 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';},children:[/*#__PURE__*/_jsx(Users,{size:16}),\"Manage Students\"]})]})]})})]}),lightboxOpen&&/*#__PURE__*/_jsx(ImageLightbox,{images:lightboxImages,initialIndex:lightboxInitialIndex,onClose:()=>setLightboxOpen(false)})]});};export default UnifiedNewsfeed;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}