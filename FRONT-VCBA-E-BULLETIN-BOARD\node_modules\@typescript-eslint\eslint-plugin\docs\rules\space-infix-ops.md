---
description: 'Require spacing around infix operators.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/space-infix-ops** for documentation.

This rule extends the base [`eslint/space-infix-ops`](https://eslint.org/docs/rules/space-infix-ops) rule.
It adds support for enum members.

```ts
enum MyEnum {
  KEY = 'value',
}
```
