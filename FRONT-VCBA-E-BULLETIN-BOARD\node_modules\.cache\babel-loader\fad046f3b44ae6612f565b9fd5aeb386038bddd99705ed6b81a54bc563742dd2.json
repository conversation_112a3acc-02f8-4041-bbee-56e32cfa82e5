{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18.2 12.27 20 6H4l1.8 6.27a1 1 0 0 0 .95.73h10.5a1 1 0 0 0 .96-.73Z\",\n  key: \"1pledb\"\n}], [\"path\", {\n  d: \"M8 13v9\",\n  key: \"hmv0ci\"\n}], [\"path\", {\n  d: \"M16 22v-9\",\n  key: \"ylnf1u\"\n}], [\"path\", {\n  d: \"m9 6 1 7\",\n  key: \"dpdgam\"\n}], [\"path\", {\n  d: \"m15 6-1 7\",\n  key: \"ls7zgu\"\n}], [\"path\", {\n  d: \"M12 6V2\",\n  key: \"1pj48d\"\n}], [\"path\", {\n  d: \"M13 2h-2\",\n  key: \"mj6ths\"\n}]];\nconst TowerControl = createLucideIcon(\"tower-control\", __iconNode);\nexport { __iconNode, TowerControl as default };\n//# sourceMappingURL=tower-control.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}