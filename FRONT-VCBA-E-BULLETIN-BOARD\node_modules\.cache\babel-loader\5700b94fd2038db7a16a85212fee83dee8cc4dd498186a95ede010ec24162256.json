{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{useHierarchicalCategories}from'../../../hooks/useAnnouncements';import{useCalendarImageUpload}from'../../../hooks/useCalendarImageUpload';import CalendarImageUpload from'../CalendarImageUpload';import CascadingCategoryDropdown from'../../common/CascadingCategoryDropdown';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CalendarEventModal=_ref=>{let{isOpen,onClose,onSave,event,selectedDate,loading=false}=_ref;const{categories,loading:categoriesLoading,error:categoriesError}=useHierarchicalCategories();// Use public service (categories should be public)\nconst[formData,setFormData]=useState({title:'',description:'',event_date:'',end_date:'',category_id:'',subcategory_id:'',is_recurring:false,recurrence_pattern:'',is_active:true,is_published:false,allow_comments:true,is_alert:false});const[errors,setErrors]=useState({});const[selectedImages,setSelectedImages]=useState([]);const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');// Check if the current event is a holiday\nconst isHoliday=['Philippine Holidays','International Holidays','Religious Holidays'].includes((event===null||event===void 0?void 0:event.category_name)||'')||(event===null||event===void 0?void 0:event.is_holiday);// Debug categories loading\nuseEffect(()=>{console.log('🔍 CalendarEventModal - Categories state:',{categories:(categories===null||categories===void 0?void 0:categories.length)||0,categoriesLoading,categoriesError,categoriesData:categories});},[categories,categoriesLoading,categoriesError]);// Image upload hook\nconst{existingImages,loading:imageLoading,error:imageError,uploadImages,setPrimaryImage,refreshImages,clearError:clearImageError,// Pending operations\npendingDeletes,markForDeletion,unmarkForDeletion,applyPendingDeletes,clearPendingDeletes,// Clear all image state\nclearAllImageState}=useCalendarImageUpload({calendarId:event===null||event===void 0?void 0:event.calendar_id,onSuccess:message=>setSuccessMessage(message),onError:error=>setErrorMessage(error)});// Initialize form data when event or selectedDate changes\nuseEffect(()=>{if(event){var _event$category_id,_event$subcategory_id;// Helper function to extract date part without timezone issues\nconst extractDatePart=dateString=>{if(!dateString)return'';// If it's already in YYYY-MM-DD format, return as-is\nif(dateString.match(/^\\d{4}-\\d{2}-\\d{2}$/)){return dateString;}// Otherwise, extract the date part from ISO string\nreturn dateString.split('T')[0];};setFormData({title:event.title||'',description:event.description||'',event_date:extractDatePart(event.event_date),end_date:event.end_date?extractDatePart(event.end_date):'',// Use category_id/subcategory_id if available, otherwise leave empty for manual selection\ncategory_id:((_event$category_id=event.category_id)===null||_event$category_id===void 0?void 0:_event$category_id.toString())||'',subcategory_id:((_event$subcategory_id=event.subcategory_id)===null||_event$subcategory_id===void 0?void 0:_event$subcategory_id.toString())||'',is_recurring:event.is_recurring||false,recurrence_pattern:event.recurrence_pattern||'',is_active:event.is_active!==false,is_published:event.is_published||false,allow_comments:Boolean(event.allow_comments),is_alert:event.is_alert||false});}else{// Format selected date properly to avoid timezone issues\nconst formatLocalDate=date=>{const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day);};const dateString=selectedDate?formatLocalDate(selectedDate):'';setFormData({title:'',description:'',event_date:dateString,end_date:'',category_id:'',subcategory_id:'',is_recurring:false,recurrence_pattern:'',is_active:true,is_published:false,allow_comments:true,is_alert:false});// Clear image state for new events\nclearAllImageState();setSelectedImages([]);}setErrors({});setSuccessMessage('');setErrorMessage('');clearImageError();},[event,selectedDate,clearImageError,clearAllImageState]);// Separate effect to handle modal open/close state\nuseEffect(()=>{if(!isOpen){// Clear pending deletes when modal closes\nclearPendingDeletes();}},[isOpen,clearPendingDeletes]);// Enhanced close handler that ensures everything is cleared\nconst handleClose=useCallback(()=>{console.log('🚪 Closing calendar modal - clearing all data');// Clear pending deletes before closing\nclearPendingDeletes();// Clear other state\nsetErrors({});setSuccessMessage('');setErrorMessage('');clearImageError();// Call parent's onClose\nonClose();},[clearPendingDeletes,clearImageError,onClose]);// Handle Escape key to close modal and clear data\nuseEffect(()=>{const handleEscapeKey=event=>{if(event.key==='Escape'&&isOpen){handleClose();}};if(isOpen){document.addEventListener('keydown',handleEscapeKey);return()=>document.removeEventListener('keydown',handleEscapeKey);}},[isOpen,handleClose]);// Clear messages after 5 seconds\nuseEffect(()=>{if(successMessage||errorMessage){const timer=setTimeout(()=>{setSuccessMessage('');setErrorMessage('');},5000);return()=>clearTimeout(timer);}},[successMessage,errorMessage]);const validateForm=()=>{const newErrors={};if(!formData.title.trim()){newErrors.title='Title is required';}else if(formData.title.length>255){newErrors.title='Title must be less than 255 characters';}if(!formData.event_date){newErrors.event_date='Event date is required';}if(!formData.category_id){newErrors.category_id='Category is required';}if(formData.end_date&&formData.event_date&&formData.end_date<formData.event_date){newErrors.end_date='End date cannot be before start date';}if(formData.is_recurring&&!formData.recurrence_pattern){newErrors.recurrence_pattern='Recurrence pattern is required for recurring events';}setErrors(newErrors);return Object.keys(newErrors).length===0;};const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}try{// Format dates to ensure they're sent as local dates without timezone conversion\nconst formatDateForSubmission=dateString=>{if(!dateString)return undefined;// Simply return the date string as-is if it's already in YYYY-MM-DD format\n// This prevents any timezone conversion issues\nreturn dateString;};// Build submit data with only the fields that should be updated\nconst submitData={};// Always include basic fields that are being edited\nif(formData.title!==undefined)submitData.title=formData.title;if(formData.description!==undefined)submitData.description=formData.description;if(formData.event_date!==undefined)submitData.event_date=formatDateForSubmission(formData.event_date);if(formData.category_id!==undefined)submitData.category_id=parseInt(formData.category_id);// Only include subcategory_id if it has a valid value\nif(formData.subcategory_id&&formData.subcategory_id!==''&&formData.subcategory_id!=='0'){submitData.subcategory_id=parseInt(formData.subcategory_id);}// Only include end_date if it has a value\nif(formData.end_date&&formData.end_date!==''){submitData.end_date=formatDateForSubmission(formData.end_date);}// Include boolean fields (but skip recurring fields for holidays as they shouldn't change)\nif(!isHoliday&&formData.is_recurring!==undefined)submitData.is_recurring=Boolean(formData.is_recurring);if(formData.is_active!==undefined)submitData.is_active=Boolean(formData.is_active);if(formData.is_published!==undefined)submitData.is_published=Boolean(formData.is_published);if(formData.allow_comments!==undefined)submitData.allow_comments=Boolean(formData.allow_comments);if(formData.is_alert!==undefined)submitData.is_alert=Boolean(formData.is_alert);// Only include recurrence_pattern for non-holidays (holidays should keep their yearly pattern)\nif(!isHoliday&&formData.is_recurring&&formData.recurrence_pattern){submitData.recurrence_pattern=formData.recurrence_pattern;}// Create completion callback for additional operations\nconst onComplete=async()=>{// If we're editing and have images, upload them separately\nif(event&&selectedImages.length>0){try{await uploadImages(selectedImages);setSelectedImages([]);// Clear selected images after upload\n}catch(uploadError){console.error('Error uploading additional images:',uploadError);// Don't throw here as the main event was saved successfully\n}}// Refresh images to show updates immediately\nif(event!==null&&event!==void 0&&event.calendar_id){await refreshImages();}// Clear pending deletes after successful update\nclearPendingDeletes();};console.log('🚀 Submitting calendar event:',submitData);// Debug log\nconsole.log('📋 Data types check:',{title:typeof submitData.title,description:typeof submitData.description,event_date:typeof submitData.event_date,category_id:typeof submitData.category_id,subcategory_id:typeof submitData.subcategory_id,is_recurring:typeof submitData.is_recurring,recurrence_pattern:typeof submitData.recurrence_pattern,is_active:typeof submitData.is_active,is_published:typeof submitData.is_published,allow_comments:typeof submitData.allow_comments,is_alert:typeof submitData.is_alert});console.log('📋 Pending deletes before save:',pendingDeletes);await onSave(submitData,pendingDeletes.length>0?applyPendingDeletes:undefined,onComplete);handleClose();}catch(error){var _response,_response2,_response3;console.error('❌ Error saving event:',error);console.error('❌ Error details:',{message:error instanceof Error?error.message:'Unknown error',response:error===null||error===void 0?void 0:(_response=error.response)===null||_response===void 0?void 0:_response.data,status:error===null||error===void 0?void 0:(_response2=error.response)===null||_response2===void 0?void 0:_response2.status,statusText:error===null||error===void 0?void 0:(_response3=error.response)===null||_response3===void 0?void 0:_response3.statusText});}};// Handle category selection\nconst handleCategoryChange=categoryId=>{console.log('🧪 CalendarEventModal - Category changed:',categoryId);console.log('🧪 CalendarEventModal - Available categories:',categories===null||categories===void 0?void 0:categories.map(cat=>({id:cat.category_id,name:cat.name})));setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{category_id:(categoryId===null||categoryId===void 0?void 0:categoryId.toString())||'',subcategory_id:''// Clear subcategory when category changes\n}));// Clear category error\nif(errors.category_id){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{category_id:''}));}};// Handle subcategory selection\nconst handleSubcategoryChange=subcategoryId=>{console.log('🧪 CalendarEventModal - Subcategory changed:',subcategoryId);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{subcategory_id:(subcategoryId===null||subcategoryId===void 0?void 0:subcategoryId.toString())||''}));};const handleInputChange=e=>{const{name,value,type}=e.target;if(type==='checkbox'){const checked=e.target.checked;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:checked}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));}// Clear error when user starts typing\nif(errors[name]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'1rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',padding:'2rem',width:'100%',maxWidth:'500px',maxHeight:'90vh',overflow:'auto',boxShadow:'0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"h2\",{style:{fontSize:'1.5rem',fontWeight:'700',color:'#2d5016',margin:0},children:[isHoliday?'Holiday Details':event?'Edit Event':'Create New Event',isHoliday&&/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'0.5rem',padding:'0.25rem 0.5rem',backgroundColor:'#dbeafe',color:'#1e40af',fontSize:'0.75rem',fontWeight:'500',borderRadius:'4px',border:'1px solid #bfdbfe'},children:\"Holiday\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleClose,style:{background:'none',border:'none',fontSize:'1.5rem',cursor:'pointer',color:'#6b7280',padding:'0.25rem'},children:\"\\xD7\"})]}),isHoliday?/*#__PURE__*/// Holiday View - Show title (read-only), editable description, and editable image\n_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"h3\",{style:{fontSize:'1.25rem',fontWeight:'600',color:'#2d5016',margin:'0 0 0.5rem 0',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{style:{width:'12px',height:'12px',borderRadius:'50%',backgroundColor:(event===null||event===void 0?void 0:event.category_color)||'#22c55e'}}),event===null||event===void 0?void 0:event.title]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(\"span\",{children:event===null||event===void 0?void 0:event.category_name}),(event===null||event===void 0?void 0:event.event_date)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsx(\"span\",{children:new Date(event.event_date).toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'})})]}),(event===null||event===void 0?void 0:event.is_recurring)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsxs(\"span\",{style:{backgroundColor:'#22c55e',color:'white',padding:'0.125rem 0.375rem',borderRadius:'4px',fontSize:'0.75rem',fontWeight:'500'},children:[\"Recurring \",event.recurrence_pattern]})]}),(event===null||event===void 0?void 0:event.is_recurring_instance)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:'#3b82f6',color:'white',padding:'0.125rem 0.375rem',borderRadius:'4px',fontSize:'0.75rem',fontWeight:'500'},children:\"Recurring Instance\"})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280',marginTop:'0.5rem',fontStyle:'italic'},children:\"Holiday name and date cannot be changed. You can edit the description and add images.\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:[\"Description\",/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.75rem',color:'#6b7280',fontWeight:'400',marginLeft:'0.5rem'},children:\"(You can add local context or additional information)\"})]}),/*#__PURE__*/_jsx(\"textarea\",{name:\"description\",value:formData.description,onChange:handleInputChange,rows:4,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease',resize:'vertical'},placeholder:\"Add description, local context, or additional information about this holiday...\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:[\"Event Images\",/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.75rem',color:'#6b7280',fontWeight:'400',marginLeft:'0.5rem'},children:\"(Add images to make this holiday more engaging)\"})]}),pendingDeletes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem 0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'6px',marginBottom:'0.75rem',color:'#dc2626',fontSize:'0.875rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A0\\uFE0F\"}),pendingDeletes.length,\" image\",pendingDeletes.length>1?'s':'',\" will be deleted\"]}),/*#__PURE__*/_jsx(CalendarImageUpload,{onImagesChange:setSelectedImages,existingImages:existingImages,onSetPrimary:setPrimaryImage,maxImages:10,onMarkForDeletion:markForDeletion,onUnmarkForDeletion:unmarkForDeletion,pendingDeletes:pendingDeletes,disabled:loading})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',paddingTop:'1rem',borderTop:'1px solid #e5e7eb',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleClose,style:{padding:'0.75rem 1.5rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem'},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,style:{padding:'0.75rem 1.5rem',background:loading?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:loading?'not-allowed':'pointer',fontWeight:'600',fontSize:'0.875rem'},children:loading?'Saving...':'Save Changes'})]})]}):/*#__PURE__*/// Regular Event Form\n_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Title *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"title\",value:formData.title,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.title?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease'},placeholder:\"Enter event title\"}),errors.title&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.title})]}),((event===null||event===void 0?void 0:event.is_recurring)||(event===null||event===void 0?void 0:event.is_recurring_instance))&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem',padding:'0.75rem',backgroundColor:'#f0f9ff',border:'1px solid #0ea5e9',borderRadius:'8px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem'},children:/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#0369a1'},children:\"\\uD83D\\uDCC5 Recurring Event Information\"})}),(event===null||event===void 0?void 0:event.is_recurring)&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#0369a1',marginBottom:'0.25rem'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Pattern:\"}),\" This event repeats \",event.recurrence_pattern]}),(event===null||event===void 0?void 0:event.is_recurring_instance)&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.75rem',color:'#0369a1',marginBottom:'0.25rem'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Instance:\"}),\" This is a recurring instance of event #\",event.original_event_id]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#0369a1',fontStyle:'italic'},children:event!==null&&event!==void 0&&event.is_recurring?'Changes to this event will affect all future occurrences.':'Changes to this instance will only affect this specific occurrence.'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"description\",value:formData.description,onChange:handleInputChange,rows:3,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'8px',fontSize:'0.875rem',outline:'none',transition:'border-color 0.2s ease',resize:'vertical'},placeholder:\"Enter event description (optional)\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Start Date *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",name:\"event_date\",value:formData.event_date,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.event_date?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none'}}),errors.event_date&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.event_date})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"End Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",name:\"end_date\",value:formData.end_date,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.end_date?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none'}}),errors.end_date&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.end_date})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',fontSize:'0.875rem',fontWeight:'500',color:'#374151',marginBottom:'0.5rem'},children:\"Category *\"}),categoriesError?/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'0.375rem',color:'#dc2626',fontSize:'0.875rem'},children:[\"Error loading categories: \",categoriesError,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>window.location.reload(),style:{marginTop:'0.5rem',padding:'0.25rem 0.5rem',backgroundColor:'#dc2626',color:'white',border:'none',borderRadius:'0.25rem',fontSize:'0.75rem',cursor:'pointer'},children:\"Reload Page\"})]}):categoriesLoading?/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'#f0f9ff',border:'1px solid #bae6fd',borderRadius:'0.375rem',color:'#0369a1',fontSize:'0.875rem'},children:\"Loading categories...\"}):!categories||categories.length===0?/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fffbeb',border:'1px solid #fed7aa',borderRadius:'0.375rem',color:'#ea580c',fontSize:'0.875rem'},children:\"No categories available. Please contact administrator.\"}):/*#__PURE__*/_jsx(CascadingCategoryDropdown,{categories:categories===null||categories===void 0?void 0:categories.filter(category=>// Hide holiday categories from event creation/editing\n!['Philippine Holidays','International Holidays','Religious Holidays'].includes(category.name)),selectedCategoryId:formData.category_id?parseInt(formData.category_id):undefined,selectedSubcategoryId:formData.subcategory_id?parseInt(formData.subcategory_id):undefined,onCategoryChange:handleCategoryChange,onSubcategoryChange:handleSubcategoryChange,placeholder:\"Select Category\",required:true,error:errors.category_id,disabled:loading})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_recurring\",checked:formData.is_recurring,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Recurring Event\"]}),formData.is_recurring&&/*#__PURE__*/_jsxs(\"select\",{name:\"recurrence_pattern\",value:formData.recurrence_pattern,onChange:handleInputChange,style:{width:'100%',padding:'0.75rem',border:\"1px solid \".concat(errors.recurrence_pattern?'#ef4444':'#d1d5db'),borderRadius:'8px',fontSize:'0.875rem',outline:'none',backgroundColor:'white'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Recurrence Pattern\"}),/*#__PURE__*/_jsx(\"option\",{value:\"weekly\",children:\"Weekly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"monthly\",children:\"Monthly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"yearly\",children:\"Yearly\"})]}),errors.recurrence_pattern&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#ef4444',fontSize:'0.75rem',marginTop:'0.25rem'},children:errors.recurrence_pattern})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1rem',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_active\",checked:formData.is_active,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Active Event\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"allow_comments\",checked:formData.allow_comments,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Allow comments\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',fontSize:'0.875rem',color:'#374151',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"is_alert\",checked:formData.is_alert,onChange:handleInputChange,style:{marginRight:'0.5rem'}}),\"Mark as alert\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(\"h4\",{style:{fontSize:'0.875rem',fontWeight:'500',color:'#374151',margin:0},children:\"Event Images\"}),pendingDeletes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem 0.75rem',marginTop:'0.5rem',backgroundColor:'#fef2f2',borderRadius:'6px',border:'1px solid #fecaca',color:'#dc2626',fontSize:'0.875rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A0\\uFE0F\"}),pendingDeletes.length,\" image\",pendingDeletes.length>1?'s':'',\" will be deleted\"]})]}),/*#__PURE__*/_jsx(CalendarImageUpload,{onImagesChange:setSelectedImages,existingImages:existingImages,onSetPrimary:setPrimaryImage,maxImages:10,disabled:imageLoading,pendingDeletes:pendingDeletes,onMarkForDeletion:markForDeletion,onUnmarkForDeletion:unmarkForDeletion}),imageError&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#dc2626',fontSize:'0.875rem',marginTop:'0.5rem'},children:imageError})]}),successMessage&&/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:'#f0fdf4',border:'1px solid #bbf7d0',borderRadius:'6px',padding:'0.75rem',marginBottom:'1rem',color:'#15803d',fontSize:'0.875rem'},children:successMessage}),errorMessage&&/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:'#fef2f2',border:'1px solid #fecaca',borderRadius:'6px',padding:'0.75rem',marginBottom:'1rem',color:'#dc2626',fontSize:'0.875rem'},children:errorMessage}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'flex-end',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleClose,style:{padding:'0.75rem 1.5rem',backgroundColor:'#f3f4f6',color:'#374151',border:'none',borderRadius:'8px',cursor:'pointer',fontWeight:'500',fontSize:'0.875rem'},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,style:{padding:'0.75rem 1.5rem',background:loading?'#9ca3af':'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',color:'white',border:'none',borderRadius:'8px',cursor:loading?'not-allowed':'pointer',fontWeight:'600',fontSize:'0.875rem'},children:loading?'Saving...':event?'Update':'Create'})]})]})]})});};export default CalendarEventModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}