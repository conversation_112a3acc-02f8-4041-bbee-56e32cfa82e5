{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M2 14h20\",\n  key: \"myj16y\"\n}], [\"path\", {\n  d: \"M12 20v-6\",\n  key: \"1rm09r\"\n}]];\nconst Touchpad = createLucideIcon(\"touchpad\", __iconNode);\nexport { __iconNode, Touchpad as default };\n//# sourceMappingURL=touchpad.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}