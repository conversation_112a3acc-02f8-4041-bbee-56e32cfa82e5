{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{httpClient}from'./api.service';import{AdminAuthService}from'./admin-auth.service';import{API_ENDPOINTS}from'../config/constants';// Types for calendar\nclass CalendarService{// Helper method to determine which HTTP client to use based on user type\nasync makeRequest(method,endpoint,data,params){// Check if user is admin (has admin token) - check both admin-specific and general auth\nconst isAdminAuth=AdminAuthService.isAuthenticated();const hasGeneralToken=localStorage.getItem('vcba_auth_token');const generalUserData=localStorage.getItem('vcba_user_data');// Check if general auth user is admin\nlet isGeneralAdmin=false;if(hasGeneralToken&&generalUserData){try{const userData=JSON.parse(generalUserData);isGeneralAdmin=userData.role==='admin';}catch(e){// Ignore parsing errors\n}}const isAdmin=isAdminAuth||isGeneralAdmin;if(isAdmin){// For admin requests, prefer AdminAuthService if available, otherwise use httpClient with admin token\nif(isAdminAuth){// Use AdminAuthService for admin requests\nconst url=params?\"\".concat(endpoint,\"?\").concat(new URLSearchParams(params)):endpoint;switch(method){case'GET':return AdminAuthService.request('GET',url);case'POST':return AdminAuthService.request('POST',endpoint,data);case'PUT':return AdminAuthService.request('PUT',endpoint,data);case'DELETE':return AdminAuthService.request('DELETE',endpoint);default:throw new Error(\"Unsupported method: \".concat(method));}}else{// Use regular httpClient with general admin token\nconst urlWithParams=params?\"\".concat(endpoint,\"?\").concat(new URLSearchParams(params)):endpoint;switch(method){case'GET':return httpClient.get(urlWithParams);case'POST':return httpClient.post(endpoint,data);case'PUT':return httpClient.put(endpoint,data);case'DELETE':return httpClient.delete(endpoint);default:throw new Error(\"Unsupported method: \".concat(method));}}}else{// Use regular httpClient for student/public requests\nconst urlWithParams=params?\"\".concat(endpoint,\"?\").concat(new URLSearchParams(params)):endpoint;switch(method){case'GET':return httpClient.get(urlWithParams);case'POST':return httpClient.post(endpoint,data);case'PUT':return httpClient.put(endpoint,data);case'DELETE':return httpClient.delete(endpoint);default:throw new Error(\"Unsupported method: \".concat(method));}}}// Get calendar events with filters and pagination\nasync getEvents(filters){const params=filters?this.buildQueryParams(filters):undefined;return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.BASE,undefined,params);}// Get single event by ID\nasync getEventById(id){return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));}// Create new event\nasync createEvent(data){return this.makeRequest('POST',API_ENDPOINTS.CALENDAR.BASE,data);}// Update event\nasync updateEvent(id,data){return this.makeRequest('PUT',API_ENDPOINTS.CALENDAR.BY_ID(id.toString()),data);}// Delete event\nasync deleteEvent(id){return this.makeRequest('DELETE',API_ENDPOINTS.CALENDAR.BY_ID(id.toString()));}// Get calendar view (month/year)\nasync getCalendarView(year,month){const params={year:year.toString()};if(month){params.month=month.toString();}return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.VIEW,undefined,params);}// Get current month events\nasync getCurrentMonthEvents(){return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.CURRENT_MONTH);}// Get upcoming events\nasync getUpcomingEvents(limit){const params=limit?{limit:limit.toString()}:undefined;return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.UPCOMING,undefined,params);}// Get events by date\nasync getEventsByDate(date){return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.BY_DATE(date));}// Get events by date range\nasync getEventsByDateRange(startDate,endDate){const params={start_date:startDate,end_date:endDate};return this.makeRequest('GET',API_ENDPOINTS.CALENDAR.DATE_RANGE,undefined,params);}// Helper method to build query parameters\nbuildQueryParams(filters){const params={};Object.entries(filters).forEach(_ref=>{let[key,value]=_ref;if(value!==undefined&&value!==null&&value!==''){params[key]=value.toString();}});return params;}// Get events for a specific month\nasync getMonthEvents(year,month){return this.getCalendarView(year,month);}// Get events for a specific year\nasync getYearEvents(year){return this.getCalendarView(year);}// Get today's events\nasync getTodayEvents(){// Format today's date manually to avoid timezone issues\nconst today=new Date();const year=today.getFullYear();const month=String(today.getMonth()+1).padStart(2,'0');const day=String(today.getDate()).padStart(2,'0');const todayStr=\"\".concat(year,\"-\").concat(month,\"-\").concat(day);return this.getEventsByDate(todayStr);}// Get this week's events\nasync getWeekEvents(startDate){const start=startDate||this.getWeekStart();const end=this.getWeekEnd(start);return this.getEventsByDateRange(start,end);}// Get events for admin dashboard\nasync getAdminEvents(filters){const defaultFilters=_objectSpread({page:1,limit:50,sort_by:'event_date',sort_order:'ASC'},filters);return this.getEvents(defaultFilters);}// Get active events only\nasync getActiveEvents(filters){return this.getEvents(_objectSpread({is_active:true},filters));}// Search events\nasync searchEvents(query,filters){return this.getEvents(_objectSpread({search:query,is_active:true},filters));}// Get recurring events\nasync getRecurringEvents(){return this.getEvents({is_recurring:true,is_active:true,sort_by:'event_date',sort_order:'ASC'});}// Get events by category\nasync getEventsByCategory(categoryId){return this.getEvents({category_id:categoryId,is_active:true,sort_by:'event_date',sort_order:'ASC'});}// Utility methods for date calculations\ngetWeekStart(date){const d=date?new Date(date):new Date();const day=d.getDay();const diff=d.getDate()-day;const weekStart=new Date(d.setDate(diff));return weekStart.toISOString().split('T')[0];}getWeekEnd(startDate){const start=new Date(startDate);const end=new Date(start);end.setDate(start.getDate()+6);return end.toISOString().split('T')[0];}// Get month boundaries\ngetMonthBoundaries(year,month){const start=new Date(year,month-1,1);const end=new Date(year,month,0);return{start:start.toISOString().split('T')[0],end:end.toISOString().split('T')[0]};}// Format date for display\nformatDate(date){return new Date(date).toLocaleDateString();}// Check if date is today\nisToday(date){const today=new Date().toISOString().split('T')[0];return date===today;}// Check if date is in the past\nisPast(date){const today=new Date().toISOString().split('T')[0];return date<today;}// Check if date is in the future\nisFuture(date){const today=new Date().toISOString().split('T')[0];return date>today;}// Get categories with subcategories for calendar events\nasync getCategoriesWithSubcategories(){return this.makeRequest('GET',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/categories/with-subcategories\"));}// Calendar attachment methods\nasync getEventAttachments(eventId){return this.makeRequest('GET',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/attachments\"));}async uploadEventAttachments(eventId,formData){return this.makeRequest('POST',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/attachments\"),formData);}async deleteEventAttachment(attachmentId){return this.makeRequest('DELETE',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/attachments/\").concat(attachmentId));}async setPrimaryAttachment(eventId,attachmentId){return this.makeRequest('PUT',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/attachments/\").concat(attachmentId,\"/primary\"));}// Event management methods\nasync publishEvent(eventId){return this.makeRequest('PUT',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/publish\"));}async unpublishEvent(eventId){return this.makeRequest('PUT',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/unpublish\"));}async softDeleteEvent(eventId){return this.makeRequest('PUT',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/soft-delete\"));}async restoreEvent(eventId){return this.makeRequest('PUT',\"\".concat(API_ENDPOINTS.CALENDAR.BASE,\"/\").concat(eventId,\"/restore\"));}}export const calendarService=new CalendarService();export default calendarService;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}