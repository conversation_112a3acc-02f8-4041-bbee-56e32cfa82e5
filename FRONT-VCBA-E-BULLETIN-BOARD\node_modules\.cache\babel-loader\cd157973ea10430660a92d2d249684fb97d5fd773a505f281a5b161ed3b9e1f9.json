{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 9v6\",\n  key: \"158jrl\"\n}], [\"path\", {\n  d: \"M9 9h3V5l7 7-7 7v-4H9V9z\",\n  key: \"1sg2xn\"\n}]];\nconst ArrowBigRightDash = createLucideIcon(\"arrow-big-right-dash\", __iconNode);\nexport { __iconNode, ArrowBigRightDash as default };\n//# sourceMappingURL=arrow-big-right-dash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}