{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\HolidayManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { RefreshCw, Globe, AlertCircle, CheckCircle, Loader, X } from 'lucide-react';\nimport { holidayService } from '../../services/holidayService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HolidayManagement = ({\n  onClose\n}) => {\n  _s();\n  const [holidays, setHolidays] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [syncing, setSyncing] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const [holidaysResponse, statsResponse] = await Promise.all([holidayService.getHolidays({\n        year: new Date().getFullYear()\n      }), holidayService.getHolidayStats()]);\n      if (holidaysResponse.success && holidaysResponse.data) {\n        setHolidays(holidaysResponse.data.holidays);\n      }\n      if (statsResponse.success && statsResponse.data) {\n        setStats(statsResponse.data);\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to load holiday data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSyncHolidays = async () => {\n    setSyncing(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      const response = await holidayService.syncMultipleCountries({\n        countries: ['PH', 'US', 'GB', 'CA'],\n        year: new Date().getFullYear()\n      });\n      if (response.success && response.data) {\n        const {\n          results\n        } = response.data;\n        const totalCreated = results.reduce((sum, r) => sum + r.stats.created, 0);\n        const totalUpdated = results.reduce((sum, r) => sum + r.stats.updated, 0);\n        setSuccess(`Successfully synced holidays! Created: ${totalCreated}, Updated: ${totalUpdated}`);\n        loadData(); // Refresh data\n      } else {\n        setError(response.message || 'Failed to sync holidays');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to sync holidays');\n    } finally {\n      setSyncing(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '4rem',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Loader, {\n        size: 24,\n        style: {\n          marginRight: '0.5rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), \"Loading holiday data...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Globe, {\n        size: 32,\n        color: \"#2d5016\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          fontSize: '2rem',\n          fontWeight: '700',\n          color: '#2d5016',\n          margin: 0\n        },\n        children: \"Holiday Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1.5rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        color: '#dc2626'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setError(null),\n        style: {\n          marginLeft: 'auto',\n          background: 'none',\n          border: 'none',\n          cursor: 'pointer',\n          color: '#dc2626'\n        },\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1.5rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        color: '#16a34a'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setSuccess(null),\n        style: {\n          marginLeft: 'auto',\n          background: 'none',\n          border: 'none',\n          cursor: 'pointer',\n          color: '#16a34a'\n        },\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this), stats && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '1.5rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.5rem',\n            textTransform: 'uppercase',\n            letterSpacing: '0.05em'\n          },\n          children: \"Total Holidays\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2d5016'\n          },\n          children: stats.total\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '1.5rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.5rem',\n            textTransform: 'uppercase',\n            letterSpacing: '0.05em'\n          },\n          children: \"Philippine Holidays\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#dc2626'\n          },\n          children: stats.by_type.local\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '1.5rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.5rem',\n            textTransform: 'uppercase',\n            letterSpacing: '0.05em'\n          },\n          children: \"Global Holidays\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2563eb'\n          },\n          children: stats.by_type.international\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '1.5rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.5rem',\n            textTransform: 'uppercase',\n            letterSpacing: '0.05em'\n          },\n          children: \"Auto-Generated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#16a34a'\n          },\n          children: stats.by_source.auto_generated\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.25rem',\n          fontWeight: '600',\n          color: '#2d5016',\n          marginBottom: '1rem'\n        },\n        children: \"Global Holiday Sync\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          marginBottom: '1.5rem',\n          lineHeight: '1.6'\n        },\n        children: \"Sync globally recognized holidays and Philippine holidays from external APIs. This will update your calendar with the latest holiday information while filtering out country-specific regional holidays.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSyncHolidays,\n        disabled: syncing,\n        style: {\n          background: syncing ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          padding: '0.75rem 2rem',\n          fontSize: '1rem',\n          fontWeight: '600',\n          cursor: syncing ? 'not-allowed' : 'pointer',\n          display: 'inline-flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          transition: 'all 0.2s ease'\n        },\n        children: [syncing ? /*#__PURE__*/_jsxDEV(Loader, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 22\n        }, this) : /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 45\n        }, this), syncing ? 'Syncing Holidays...' : 'Sync Global Holidays']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(HolidayManagement, \"9RAhgu41CmO6ezTsSbSkuDCF9IE=\");\n_c = HolidayManagement;\nexport default HolidayManagement;\nvar _c;\n$RefreshReg$(_c, \"HolidayManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RefreshCw", "Globe", "AlertCircle", "CheckCircle", "Loader", "X", "holidayService", "jsxDEV", "_jsxDEV", "HolidayManagement", "onClose", "_s", "holidays", "setHolidays", "stats", "setStats", "loading", "setLoading", "syncing", "setSyncing", "error", "setError", "success", "setSuccess", "loadData", "holidaysResponse", "statsResponse", "Promise", "all", "getHolidays", "year", "Date", "getFullYear", "getHolidayStats", "data", "err", "message", "handleSyncHolidays", "response", "syncMultipleCountries", "countries", "results", "totalCreated", "reduce", "sum", "r", "created", "totalUpdated", "updated", "style", "display", "alignItems", "justifyContent", "padding", "color", "children", "size", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "marginBottom", "fontSize", "fontWeight", "margin", "background", "border", "borderRadius", "onClick", "marginLeft", "cursor", "gridTemplateColumns", "boxShadow", "textAlign", "textTransform", "letterSpacing", "total", "by_type", "local", "international", "by_source", "auto_generated", "lineHeight", "disabled", "transition", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/HolidayManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  RefreshCw, \n  Globe,\n  AlertCircle,\n  CheckCircle,\n  Loader,\n  X\n} from 'lucide-react';\nimport { holidayService, type Holiday, type HolidayStats } from '../../services/holidayService';\n\ninterface HolidayManagementProps {\n  onClose?: () => void;\n}\n\nconst HolidayManagement: React.FC<HolidayManagementProps> = ({ onClose }) => {\n  const [holidays, setHolidays] = useState<Holiday[]>([]);\n  const [stats, setStats] = useState<HolidayStats | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [syncing, setSyncing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const [holidaysResponse, statsResponse] = await Promise.all([\n        holidayService.getHolidays({ year: new Date().getFullYear() }),\n        holidayService.getHolidayStats()\n      ]);\n\n      if (holidaysResponse.success && holidaysResponse.data) {\n        setHolidays(holidaysResponse.data.holidays);\n      }\n\n      if (statsResponse.success && statsResponse.data) {\n        setStats(statsResponse.data);\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to load holiday data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSyncHolidays = async () => {\n    setSyncing(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const response = await holidayService.syncMultipleCountries({\n        countries: ['PH', 'US', 'GB', 'CA'],\n        year: new Date().getFullYear()\n      });\n\n      if (response.success && response.data) {\n        const { results } = response.data;\n        const totalCreated = results.reduce((sum: number, r: any) => sum + r.stats.created, 0);\n        const totalUpdated = results.reduce((sum: number, r: any) => sum + r.stats.updated, 0);\n        setSuccess(`Successfully synced holidays! Created: ${totalCreated}, Updated: ${totalUpdated}`);\n        loadData(); // Refresh data\n      } else {\n        setError(response.message || 'Failed to sync holidays');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to sync holidays');\n    } finally {\n      setSyncing(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '4rem',\n        color: '#6b7280'\n      }}>\n        <Loader size={24} style={{ marginRight: '0.5rem' }} />\n        Loading holiday data...\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '2rem'\n      }}>\n        <Globe size={32} color=\"#2d5016\" />\n        <h1 style={{\n          fontSize: '2rem',\n          fontWeight: '700',\n          color: '#2d5016',\n          margin: 0\n        }}>\n          Holiday Management\n        </h1>\n      </div>\n\n      {/* Error Alert */}\n      {error && (\n        <div style={{\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          padding: '1rem',\n          marginBottom: '1.5rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          color: '#dc2626'\n        }}>\n          <AlertCircle size={20} />\n          <span>{error}</span>\n          <button\n            onClick={() => setError(null)}\n            style={{\n              marginLeft: 'auto',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: '#dc2626'\n            }}\n          >\n            <X size={16} />\n          </button>\n        </div>\n      )}\n\n      {/* Success Alert */}\n      {success && (\n        <div style={{\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          padding: '1rem',\n          marginBottom: '1.5rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          color: '#16a34a'\n        }}>\n          <CheckCircle size={20} />\n          <span>{success}</span>\n          <button\n            onClick={() => setSuccess(null)}\n            style={{\n              marginLeft: 'auto',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              color: '#16a34a'\n            }}\n          >\n            <X size={16} />\n          </button>\n        </div>\n      )}\n\n      {/* Statistics Cards */}\n      {stats && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.5rem',\n              textTransform: 'uppercase',\n              letterSpacing: '0.05em'\n            }}>\n              Total Holidays\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700',\n              color: '#2d5016'\n            }}>\n              {stats.total}\n            </div>\n          </div>\n\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.5rem',\n              textTransform: 'uppercase',\n              letterSpacing: '0.05em'\n            }}>\n              Philippine Holidays\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700',\n              color: '#dc2626'\n            }}>\n              {stats.by_type.local}\n            </div>\n          </div>\n\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.5rem',\n              textTransform: 'uppercase',\n              letterSpacing: '0.05em'\n            }}>\n              Global Holidays\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700',\n              color: '#2563eb'\n            }}>\n              {stats.by_type.international}\n            </div>\n          </div>\n\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '1.5rem',\n            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n            border: '1px solid #e8f5e8',\n            textAlign: 'center'\n          }}>\n            <div style={{\n              color: '#6b7280',\n              fontSize: '0.875rem',\n              marginBottom: '0.5rem',\n              textTransform: 'uppercase',\n              letterSpacing: '0.05em'\n            }}>\n              Auto-Generated\n            </div>\n            <div style={{\n              fontSize: '2rem',\n              fontWeight: '700',\n              color: '#16a34a'\n            }}>\n              {stats.by_source.auto_generated}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Sync Button */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        textAlign: 'center'\n      }}>\n        <h3 style={{\n          fontSize: '1.25rem',\n          fontWeight: '600',\n          color: '#2d5016',\n          marginBottom: '1rem'\n        }}>\n          Global Holiday Sync\n        </h3>\n        <p style={{\n          color: '#6b7280',\n          marginBottom: '1.5rem',\n          lineHeight: '1.6'\n        }}>\n          Sync globally recognized holidays and Philippine holidays from external APIs. \n          This will update your calendar with the latest holiday information while filtering out country-specific regional holidays.\n        </p>\n        <button\n          onClick={handleSyncHolidays}\n          disabled={syncing}\n          style={{\n            background: syncing ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            fontSize: '1rem',\n            fontWeight: '600',\n            cursor: syncing ? 'not-allowed' : 'pointer',\n            display: 'inline-flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.2s ease'\n          }}\n        >\n          {syncing ? <Loader size={20} /> : <RefreshCw size={20} />}\n          {syncing ? 'Syncing Holidays...' : 'Sync Global Holidays'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default HolidayManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,KAAK,EACLC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,CAAC,QACI,cAAc;AACrB,SAASC,cAAc,QAAyC,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhG,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAsB,IAAI,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACdyB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BP,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM,CAACI,gBAAgB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DtB,cAAc,CAACuB,WAAW,CAAC;QAAEC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,CAAC,EAC9D1B,cAAc,CAAC2B,eAAe,CAAC,CAAC,CACjC,CAAC;MAEF,IAAIR,gBAAgB,CAACH,OAAO,IAAIG,gBAAgB,CAACS,IAAI,EAAE;QACrDrB,WAAW,CAACY,gBAAgB,CAACS,IAAI,CAACtB,QAAQ,CAAC;MAC7C;MAEA,IAAIc,aAAa,CAACJ,OAAO,IAAII,aAAa,CAACQ,IAAI,EAAE;QAC/CnB,QAAQ,CAACW,aAAa,CAACQ,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,6BAA6B,CAAC;IACxD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrClB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMhC,cAAc,CAACiC,qBAAqB,CAAC;QAC1DC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACnCV,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAC/B,CAAC,CAAC;MAEF,IAAIM,QAAQ,CAAChB,OAAO,IAAIgB,QAAQ,CAACJ,IAAI,EAAE;QACrC,MAAM;UAAEO;QAAQ,CAAC,GAAGH,QAAQ,CAACJ,IAAI;QACjC,MAAMQ,YAAY,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,GAAW,EAAEC,CAAM,KAAKD,GAAG,GAAGC,CAAC,CAAC/B,KAAK,CAACgC,OAAO,EAAE,CAAC,CAAC;QACtF,MAAMC,YAAY,GAAGN,OAAO,CAACE,MAAM,CAAC,CAACC,GAAW,EAAEC,CAAM,KAAKD,GAAG,GAAGC,CAAC,CAAC/B,KAAK,CAACkC,OAAO,EAAE,CAAC,CAAC;QACtFzB,UAAU,CAAC,0CAA0CmB,YAAY,cAAcK,YAAY,EAAE,CAAC;QAC9FvB,QAAQ,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACLH,QAAQ,CAACiB,QAAQ,CAACF,OAAO,IAAI,yBAAyB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOD,GAAQ,EAAE;MACjBd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,yBAAyB,CAAC;IACpD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACER,OAAA;MAAKyC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,OAAO,EAAE,MAAM;QACfC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA/C,OAAA,CAACJ,MAAM;QAACoD,IAAI,EAAE,EAAG;QAACP,KAAK,EAAE;UAAEQ,WAAW,EAAE;QAAS;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,2BAExD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKyC,KAAK,EAAE;MAAEI,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBAE9B/C,OAAA;MAAKyC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBW,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,gBACA/C,OAAA,CAACP,KAAK;QAACuD,IAAI,EAAE,EAAG;QAACF,KAAK,EAAC;MAAS;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCrD,OAAA;QAAIyC,KAAK,EAAE;UACTe,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE,SAAS;UAChBY,MAAM,EAAE;QACV,CAAE;QAAAX,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGLzC,KAAK,iBACJZ,OAAA;MAAKyC,KAAK,EAAE;QACVkB,UAAU,EAAE,SAAS;QACrBC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBhB,OAAO,EAAE,MAAM;QACfU,YAAY,EAAE,QAAQ;QACtBb,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBW,GAAG,EAAE,QAAQ;QACbR,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA/C,OAAA,CAACN,WAAW;QAACsD,IAAI,EAAE;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzBrD,OAAA;QAAA+C,QAAA,EAAOnC;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpBrD,OAAA;QACE8D,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,IAAI,CAAE;QAC9B4B,KAAK,EAAE;UACLsB,UAAU,EAAE,MAAM;UAClBJ,UAAU,EAAE,MAAM;UAClBC,MAAM,EAAE,MAAM;UACdI,MAAM,EAAE,SAAS;UACjBlB,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,eAEF/C,OAAA,CAACH,CAAC;UAACmD,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAGAvC,OAAO,iBACNd,OAAA;MAAKyC,KAAK,EAAE;QACVkB,UAAU,EAAE,SAAS;QACrBC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBhB,OAAO,EAAE,MAAM;QACfU,YAAY,EAAE,QAAQ;QACtBb,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBW,GAAG,EAAE,QAAQ;QACbR,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA/C,OAAA,CAACL,WAAW;QAACqD,IAAI,EAAE;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzBrD,OAAA;QAAA+C,QAAA,EAAOjC;MAAO;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACtBrD,OAAA;QACE8D,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAAC,IAAI,CAAE;QAChC0B,KAAK,EAAE;UACLsB,UAAU,EAAE,MAAM;UAClBJ,UAAU,EAAE,MAAM;UAClBC,MAAM,EAAE,MAAM;UACdI,MAAM,EAAE,SAAS;UACjBlB,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,eAEF/C,OAAA,CAACH,CAAC;UAACmD,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAGA/C,KAAK,iBACJN,OAAA;MAAKyC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfuB,mBAAmB,EAAE,sCAAsC;QAC3DX,GAAG,EAAE,QAAQ;QACbC,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,gBACA/C,OAAA;QAAKyC,KAAK,EAAE;UACVkB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,QAAQ;UACjBqB,SAAS,EAAE,gCAAgC;UAC3CN,MAAM,EAAE,mBAAmB;UAC3BO,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACA/C,OAAA;UAAKyC,KAAK,EAAE;YACVK,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,UAAU;YACpBD,YAAY,EAAE,QAAQ;YACtBa,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrD,OAAA;UAAKyC,KAAK,EAAE;YACVe,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCzC,KAAK,CAACgE;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QAAKyC,KAAK,EAAE;UACVkB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,QAAQ;UACjBqB,SAAS,EAAE,gCAAgC;UAC3CN,MAAM,EAAE,mBAAmB;UAC3BO,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACA/C,OAAA;UAAKyC,KAAK,EAAE;YACVK,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,UAAU;YACpBD,YAAY,EAAE,QAAQ;YACtBa,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrD,OAAA;UAAKyC,KAAK,EAAE;YACVe,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCzC,KAAK,CAACiE,OAAO,CAACC;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QAAKyC,KAAK,EAAE;UACVkB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,QAAQ;UACjBqB,SAAS,EAAE,gCAAgC;UAC3CN,MAAM,EAAE,mBAAmB;UAC3BO,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACA/C,OAAA;UAAKyC,KAAK,EAAE;YACVK,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,UAAU;YACpBD,YAAY,EAAE,QAAQ;YACtBa,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrD,OAAA;UAAKyC,KAAK,EAAE;YACVe,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCzC,KAAK,CAACiE,OAAO,CAACE;QAAa;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QAAKyC,KAAK,EAAE;UACVkB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,QAAQ;UACjBqB,SAAS,EAAE,gCAAgC;UAC3CN,MAAM,EAAE,mBAAmB;UAC3BO,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACA/C,OAAA;UAAKyC,KAAK,EAAE;YACVK,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE,UAAU;YACpBD,YAAY,EAAE,QAAQ;YACtBa,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrD,OAAA;UAAKyC,KAAK,EAAE;YACVe,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCzC,KAAK,CAACoE,SAAS,CAACC;QAAc;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrD,OAAA;MAAKyC,KAAK,EAAE;QACVkB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBhB,OAAO,EAAE,MAAM;QACfqB,SAAS,EAAE,gCAAgC;QAC3CN,MAAM,EAAE,mBAAmB;QAC3BO,SAAS,EAAE;MACb,CAAE;MAAApB,QAAA,gBACA/C,OAAA;QAAIyC,KAAK,EAAE;UACTe,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE,SAAS;UAChBS,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrD,OAAA;QAAGyC,KAAK,EAAE;UACRK,KAAK,EAAE,SAAS;UAChBS,YAAY,EAAE,QAAQ;UACtBqB,UAAU,EAAE;QACd,CAAE;QAAA7B,QAAA,EAAC;MAGH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrD,OAAA;QACE8D,OAAO,EAAEjC,kBAAmB;QAC5BgD,QAAQ,EAAEnE,OAAQ;QAClB+B,KAAK,EAAE;UACLkB,UAAU,EAAEjD,OAAO,GAAG,SAAS,GAAG,mDAAmD;UACrFoC,KAAK,EAAE,OAAO;UACdc,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE,cAAc;UACvBW,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBO,MAAM,EAAEtD,OAAO,GAAG,aAAa,GAAG,SAAS;UAC3CgC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,QAAQ;UACpBW,GAAG,EAAE,QAAQ;UACbwB,UAAU,EAAE;QACd,CAAE;QAAA/B,QAAA,GAEDrC,OAAO,gBAAGV,OAAA,CAACJ,MAAM;UAACoD,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACR,SAAS;UAACwD,IAAI,EAAE;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxD3C,OAAO,GAAG,qBAAqB,GAAG,sBAAsB;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAlUIF,iBAAmD;AAAA8E,EAAA,GAAnD9E,iBAAmD;AAoUzD,eAAeA,iBAAiB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}