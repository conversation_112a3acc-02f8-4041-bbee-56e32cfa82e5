{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 17V7h10\",\n  key: \"11bw93\"\n}], [\"path\", {\n  d: \"M17 17 7 7\",\n  key: \"2786uv\"\n}]];\nconst ArrowUpLeft = createLucideIcon(\"arrow-up-left\", __iconNode);\nexport { __iconNode, ArrowUpLeft as default };\n//# sourceMappingURL=arrow-up-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}