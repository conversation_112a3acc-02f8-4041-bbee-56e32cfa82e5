{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 2v4\",\n  key: \"1xt6in\"\n}], [\"path\", {\n  d: \"M14 2H7a2 2 0 0 0-2 2\",\n  key: \"e6xig3\"\n}], [\"path\", {\n  d: \"M19.29 14.76A6.67 6.67 0 0 1 17 11a6.6 6.6 0 0 1-2.29 3.76c-1.15.92-1.71 2.04-1.71 3.19 0 2.22 1.8 4.05 4 4.05s4-1.83 4-4.05c0-1.16-.57-2.26-1.71-3.19\",\n  key: \"adq7uc\"\n}], [\"path\", {\n  d: \"M9.607 21H6a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h7V7a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3\",\n  key: \"t9hm96\"\n}]];\nconst SoapDispenserDroplet = createLucideIcon(\"soap-dispenser-droplet\", __iconNode);\nexport { __iconNode, SoapDispenserDroplet as default };\n//# sourceMappingURL=soap-dispenser-droplet.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}