{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 7h10v10\",\n  key: \"1tivn9\"\n}], [\"path\", {\n  d: \"M7 17 17 7\",\n  key: \"1vkiza\"\n}]];\nconst ArrowUpRight = createLucideIcon(\"arrow-up-right\", __iconNode);\nexport { __iconNode, ArrowUpRight as default };\n//# sourceMappingURL=arrow-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}