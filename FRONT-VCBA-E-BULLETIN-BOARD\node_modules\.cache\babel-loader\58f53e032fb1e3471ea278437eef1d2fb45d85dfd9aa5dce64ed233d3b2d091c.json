{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 12H3\",\n  key: \"51ecnj\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M18 9v6\",\n  key: \"1twb98\"\n}], [\"path\", {\n  d: \"M21 12h-6\",\n  key: \"bt1uis\"\n}]];\nconst ListPlus = createLucideIcon(\"list-plus\", __iconNode);\nexport { __iconNode, ListPlus as default };\n//# sourceMappingURL=list-plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}