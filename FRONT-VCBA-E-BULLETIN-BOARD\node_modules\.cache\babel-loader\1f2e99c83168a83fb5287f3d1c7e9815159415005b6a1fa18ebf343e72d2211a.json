{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m16 10-4 4-4-4\",\n  key: \"894hmk\"\n}]];\nconst CircleChevronDown = createLucideIcon(\"circle-chevron-down\", __iconNode);\nexport { __iconNode, CircleChevronDown as default };\n//# sourceMappingURL=circle-chevron-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}