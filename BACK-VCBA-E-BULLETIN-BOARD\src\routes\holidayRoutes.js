const express = require('express');
const { body, query, param } = require('express-validator');
const { authenticate, optionalAuth } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const HolidayController = require('../controllers/HolidayController');

const router = express.Router();

// Validation middleware
const yearValidation = [
  query('year')
    .optional()
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be between 2020 and 2030')
];

const categoryValidation = [
  query('category')
    .optional()
    .isIn(['philippine', 'international', 'religious'])
    .withMessage('Category must be one of: philippine, international, religious')
];

const countryCodeValidation = [
  query('country_code')
    .optional()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country code must be 2 characters')
];

const syncValidation = [
  body('year')
    .optional()
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be between 2020 and 2030'),
  body('force')
    .optional()
    .isBoolean()
    .withMessage('Force must be a boolean')
];

const multiYearSyncValidation = [
  body('years')
    .isArray({ min: 1, max: 5 })
    .withMessage('Years must be an array with 1-5 elements'),
  body('years.*')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Each year must be between 2020 and 2030'),
  body('force')
    .optional()
    .isBoolean()
    .withMessage('Force must be a boolean')
];

const yearParamValidation = [
  param('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be between 2020 and 2030')
];

// Public routes (with optional authentication for user-specific data)
router.get(
  '/',
  optionalAuth,
  yearValidation,
  categoryValidation,
  countryCodeValidation,
  validateRequest,
  HolidayController.getHolidays
);

router.get(
  '/philippine',
  optionalAuth,
  yearValidation,
  validateRequest,
  HolidayController.getPhilippineHolidays
);

router.get(
  '/international',
  optionalAuth,
  yearValidation,
  validateRequest,
  HolidayController.getInternationalHolidays
);

router.get(
  '/religious',
  optionalAuth,
  yearValidation,
  validateRequest,
  HolidayController.getReligiousHolidays
);

router.get(
  '/api-source',
  optionalAuth,
  yearValidation,
  validateRequest,
  HolidayController.getAllHolidaysFromAPI
);

router.get(
  '/stats',
  optionalAuth,
  yearValidation,
  validateRequest,
  HolidayController.getHolidayStats
);

// Protected routes (require authentication)
router.use(authenticate);

// Admin-only routes for holiday management
router.get(
  '/preview',
  yearValidation,
  validateRequest,
  HolidayController.previewHolidays
);

router.post(
  '/sync',
  syncValidation,
  validateRequest,
  HolidayController.syncHolidays
);

router.post(
  '/sync-multiple',
  multiYearSyncValidation,
  validateRequest,
  HolidayController.syncMultipleYears
);

router.delete(
  '/auto-generated/:year',
  yearParamValidation,
  validateRequest,
  HolidayController.deleteAutoGeneratedHolidays
);

module.exports = router;
