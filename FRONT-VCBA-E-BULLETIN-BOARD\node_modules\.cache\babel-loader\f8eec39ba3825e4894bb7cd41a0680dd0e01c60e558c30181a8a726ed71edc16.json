{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M 22 14 L 22 10\",\n  key: \"nqc4tb\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"16\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"13zb55\"\n}]];\nconst Battery = createLucideIcon(\"battery\", __iconNode);\nexport { __iconNode, Battery as default };\n//# sourceMappingURL=battery.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}