{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 2v2\",\n  key: \"1539x4\"\n}], [\"path\", {\n  d: \"M5 2v2\",\n  key: \"1yf1q8\"\n}], [\"path\", {\n  d: \"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1\",\n  key: \"rb5t3r\"\n}], [\"path\", {\n  d: \"M8 15a6 6 0 0 0 12 0v-3\",\n  key: \"x18d4x\"\n}], [\"circle\", {\n  cx: \"20\",\n  cy: \"10\",\n  r: \"2\",\n  key: \"ts1r5v\"\n}]];\nconst Stethoscope = createLucideIcon(\"stethoscope\", __iconNode);\nexport { __iconNode, Stethoscope as default };\n//# sourceMappingURL=stethoscope.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}