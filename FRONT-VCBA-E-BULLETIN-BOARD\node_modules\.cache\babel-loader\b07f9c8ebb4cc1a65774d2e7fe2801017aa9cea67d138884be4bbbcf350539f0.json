{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 17V9\",\n  key: \"1fwyjl\"\n}], [\"path\", {\n  d: \"M18 17v-3\",\n  key: \"1sqioe\"\n}], [\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"path\", {\n  d: \"M8 17V5\",\n  key: \"1wzmnc\"\n}]];\nconst ChartColumnDecreasing = createLucideIcon(\"chart-column-decreasing\", __iconNode);\nexport { __iconNode, ChartColumnDecreasing as default };\n//# sourceMappingURL=chart-column-decreasing.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}