{"name": "builtin-modules", "version": "3.3.0", "description": "List of the Node.js builtin modules", "license": "MIT", "repository": "sindresorhus/builtin-modules", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd", "make": "node make.js"}, "files": ["index.js", "index.d.ts", "static.js", "static.d.ts", "builtin-modules.json"], "keywords": ["builtin", "built-in", "builtins", "node", "modules", "core", "bundled", "list", "array", "names"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}