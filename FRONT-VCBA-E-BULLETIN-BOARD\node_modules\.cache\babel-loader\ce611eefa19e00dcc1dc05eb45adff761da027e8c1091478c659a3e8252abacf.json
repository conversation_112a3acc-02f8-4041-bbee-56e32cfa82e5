{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 17a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-2Z\",\n  key: \"jg2n2t\"\n}], [\"path\", {\n  d: \"M6 15v-2\",\n  key: \"gd6mvg\"\n}], [\"path\", {\n  d: \"M12 15V9\",\n  key: \"8c7uyn\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1gm2ql\"\n}]];\nconst Joystick = createLucideIcon(\"joystick\", __iconNode);\nexport { __iconNode, Joystick as default };\n//# sourceMappingURL=joystick.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}