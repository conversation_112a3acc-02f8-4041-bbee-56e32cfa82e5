{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9\",\n  key: \"4ay0iu\"\n}], [\"path\", {\n  d: \"M20 3v4\",\n  key: \"1olli1\"\n}], [\"path\", {\n  d: \"M22 5h-4\",\n  key: \"1gvqau\"\n}]];\nconst MoonStar = createLucideIcon(\"moon-star\", __iconNode);\nexport { __iconNode, MoonStar as default };\n//# sourceMappingURL=moon-star.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}