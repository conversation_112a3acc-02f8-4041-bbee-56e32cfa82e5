{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 14a8 8 0 0 1-8 8\",\n  key: \"56vcr3\"\n}], [\"path\", {\n  d: \"M18 11v-1a2 2 0 0 0-2-2a2 2 0 0 0-2 2\",\n  key: \"1agjmk\"\n}], [\"path\", {\n  d: \"M14 10V9a2 2 0 0 0-2-2a2 2 0 0 0-2 2v1\",\n  key: \"wdbh2u\"\n}], [\"path\", {\n  d: \"M10 9.5V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v10\",\n  key: \"1ibuk9\"\n}], [\"path\", {\n  d: \"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15\",\n  key: \"g6ys72\"\n}]];\nconst Pointer = createLucideIcon(\"pointer\", __iconNode);\nexport { __iconNode, Pointer as default };\n//# sourceMappingURL=pointer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}