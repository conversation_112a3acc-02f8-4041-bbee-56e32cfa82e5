{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback, useEffect } from 'react';\nimport { adminAnnouncementService } from '../services/announcementService';\nexport const useMultipleImageUpload = ({\n  announcementId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [existingImages, setExistingImages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pendingDeletes, setPendingDeletes] = useState([]);\n\n  // Clear existing images when announcementId becomes null (for new announcements)\n  useEffect(() => {\n    if (!announcementId) {\n      console.log('🧹 useMultipleImageUpload - Clearing existing images for new announcement');\n      setExistingImages([]);\n      setPendingDeletes([]);\n      setError(null);\n    }\n  }, [announcementId]);\n\n  // Use the imported service instance\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Clear pending deletes\n  const clearPendingDeletes = useCallback(() => {\n    setPendingDeletes([]);\n  }, []);\n\n  // Clear all image-related state (for new announcements)\n  const clearAllImageState = useCallback(() => {\n    console.log('🧹 useMultipleImageUpload - Clearing all image state');\n    setExistingImages([]);\n    setPendingDeletes([]);\n    setError(null);\n    setLoading(false);\n  }, []);\n\n  // Mark image for deletion (don't delete immediately)\n  const markForDeletion = useCallback(attachmentId => {\n    console.log('🔴 Marking image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      if (!prev.includes(attachmentId)) {\n        const newPending = [...prev, attachmentId];\n        console.log('🔴 Updated pending deletes:', newPending);\n        return newPending;\n      }\n      return prev;\n    });\n  }, []);\n\n  // Unmark image for deletion\n  const unmarkForDeletion = useCallback(attachmentId => {\n    console.log('🟢 Unmarking image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      const newPending = prev.filter(id => id !== attachmentId);\n      console.log('🟢 Updated pending deletes:', newPending);\n      return newPending;\n    });\n  }, []);\n\n  // Handle API errors\n  const handleError = useCallback((err, defaultMessage) => {\n    var _err$response, _err$response$data;\n    const errorMessage = (err === null || err === void 0 ? void 0 : (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || (err === null || err === void 0 ? void 0 : err.message) || defaultMessage;\n    setError(errorMessage);\n    onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n  }, [onError]);\n\n  // Refresh images from server\n  const refreshImages = useCallback(async () => {\n    if (!announcementId) return;\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      const response = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.images) {\n        setExistingImages(response.data.images);\n      } else {\n        setExistingImages([]);\n      }\n    } catch (err) {\n      handleError(err, 'Failed to load images');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, handleError]);\n\n  // Upload multiple images\n  const uploadImages = useCallback(async files => {\n    if (!announcementId || files.length === 0) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('images', file);\n      });\n      const response = await adminAnnouncementService.addAnnouncementImages(announcementId, formData);\n      if (response.success) {\n        var _response$data2;\n        // Directly update state with new images instead of making another API call\n        if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && _response$data2.images) {\n          setExistingImages(response.data.images);\n        } else {\n          // Fallback to refresh if response doesn't include images\n          await refreshImages();\n        }\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Successfully uploaded ${files.length} image${files.length > 1 ? 's' : ''}`);\n      } else {\n        throw new Error(response.message || 'Upload failed');\n      }\n    } catch (err) {\n      handleError(err, 'Failed to upload images');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError, refreshImages]);\n\n  // Apply pending deletes (called when user clicks Update)\n  const applyPendingDeletes = useCallback(async () => {\n    if (!announcementId || pendingDeletes.length === 0) {\n      return;\n    }\n    const deletesToApply = [...pendingDeletes]; // Capture current state\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Batch delete all pending images\n      const deletePromises = deletesToApply.map(attachmentId => adminAnnouncementService.deleteAnnouncementImage(announcementId, attachmentId));\n      const results = await Promise.allSettled(deletePromises);\n\n      // Check for any failures\n      const failures = results.filter(result => result.status === 'rejected');\n      if (failures.length > 0) {\n        throw new Error(`Failed to delete ${failures.length} image(s)`);\n      }\n\n      // Update state optimistically - remove deleted images from existingImages\n      setExistingImages(prev => prev.filter(img => !deletesToApply.includes(img.attachment_id)));\n\n      // Clear pending deletes\n      setPendingDeletes([]);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Successfully deleted ${deletesToApply.length} image(s)`);\n    } catch (err) {\n      console.error('❌ Error applying pending deletes:', err);\n      handleError(err, 'Failed to delete images');\n      // Refresh images to ensure consistency\n      await refreshImages();\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, pendingDeletes, onSuccess, handleError, refreshImages]);\n\n  // Delete an image (immediate deletion - kept for backward compatibility)\n  const deleteImage = useCallback(async attachmentId => {\n    if (!announcementId) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await adminAnnouncementService.deleteAnnouncementImage(announcementId, attachmentId);\n      if (response.success) {\n        // Remove from local state immediately for better UX\n        setExistingImages(prev => prev.filter(img => img.attachment_id !== attachmentId));\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Image deleted successfully');\n      } else {\n        throw new Error(response.message || 'Delete failed');\n      }\n    } catch (err) {\n      var _refreshResponse$data;\n      handleError(err, 'Failed to delete image');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && (_refreshResponse$data = refreshResponse.data) !== null && _refreshResponse$data !== void 0 && _refreshResponse$data.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n\n  // Update image display order\n  const updateImageOrder = useCallback(async imageOrder => {\n    if (!announcementId) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await adminAnnouncementService.updateImageOrder(announcementId, imageOrder);\n      if (response.success) {\n        // Update local state to reflect new order\n        setExistingImages(prev => {\n          const updated = [...prev];\n          imageOrder.forEach(({\n            attachment_id,\n            display_order\n          }) => {\n            const index = updated.findIndex(img => img.attachment_id === attachment_id);\n            if (index !== -1) {\n              updated[index] = {\n                ...updated[index],\n                display_order\n              };\n            }\n          });\n          return updated.sort((a, b) => a.display_order - b.display_order);\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Image order updated successfully');\n      } else {\n        throw new Error(response.message || 'Update failed');\n      }\n    } catch (err) {\n      var _refreshResponse$data2;\n      handleError(err, 'Failed to update image order');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && (_refreshResponse$data2 = refreshResponse.data) !== null && _refreshResponse$data2 !== void 0 && _refreshResponse$data2.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback(async attachmentId => {\n    if (!announcementId) return;\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await adminAnnouncementService.setPrimaryImage(announcementId, attachmentId);\n      if (response.success) {\n        // Update local state to reflect new primary image\n        setExistingImages(prev => prev.map(img => ({\n          ...img,\n          is_primary: img.attachment_id === attachmentId\n        })));\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Primary image updated successfully');\n      } else {\n        throw new Error(response.message || 'Update failed');\n      }\n    } catch (err) {\n      var _refreshResponse$data3;\n      handleError(err, 'Failed to set primary image');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && (_refreshResponse$data3 = refreshResponse.data) !== null && _refreshResponse$data3 !== void 0 && _refreshResponse$data3.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n  return {\n    existingImages,\n    loading,\n    error,\n    uploadImages,\n    deleteImage,\n    updateImageOrder,\n    setPrimaryImage,\n    refreshImages,\n    clearError,\n    // New pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  };\n};\n_s(useMultipleImageUpload, \"zDvwljJp17RtorAoOwFjmu5kDNE=\");", "map": {"version": 3, "names": ["useState", "useCallback", "useEffect", "adminAnnouncementService", "useMultipleImageUpload", "announcementId", "onSuccess", "onError", "_s", "existingImages", "setExistingImages", "loading", "setLoading", "error", "setError", "pendingDeletes", "setPendingDeletes", "console", "log", "clearError", "clearPendingDeletes", "clearAllImageState", "markForDeletion", "attachmentId", "prev", "includes", "newPending", "unmarkForDeletion", "filter", "id", "handleError", "err", "defaultMessage", "_err$response", "_err$response$data", "errorMessage", "response", "data", "message", "refreshImages", "_response$data", "getAnnouncementImages", "success", "images", "uploadImages", "files", "length", "formData", "FormData", "for<PERSON>ach", "file", "append", "addAnnouncementImages", "_response$data2", "Error", "applyPendingDeletes", "deletesToApply", "deletePromises", "map", "deleteAnnouncementImage", "results", "Promise", "allSettled", "failures", "result", "status", "img", "attachment_id", "deleteImage", "_refreshResponse$data", "refreshResponse", "updateImageOrder", "imageOrder", "updated", "display_order", "index", "findIndex", "sort", "a", "b", "_refreshResponse$data2", "setPrimaryImage", "is_primary", "_refreshResponse$data3"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useMultipleImageUpload.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { adminAnnouncementService } from '../services/announcementService';\n\ninterface ExistingImage {\n  attachment_id: number;\n  file_name: string;\n  file_path: string;\n  file_url: string;\n  display_order: number;\n  is_primary: boolean;\n}\n\ninterface UseMultipleImageUploadProps {\n  announcementId?: number;\n  onSuccess?: (message: string) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UseMultipleImageUploadReturn {\n  existingImages: ExistingImage[];\n  loading: boolean;\n  error: string | null;\n  uploadImages: (files: File[]) => Promise<void>;\n  deleteImage: (attachmentId: number) => Promise<void>;\n  updateImageOrder: (imageOrder: { attachment_id: number; display_order: number }[]) => Promise<void>;\n  setPrimaryImage: (attachmentId: number) => Promise<void>;\n  refreshImages: () => Promise<void>;\n  clearError: () => void;\n  // New methods for pending operations\n  pendingDeletes: number[];\n  markForDeletion: (attachmentId: number) => void;\n  unmarkForDeletion: (attachmentId: number) => void;\n  applyPendingDeletes: () => Promise<void>;\n  clearPendingDeletes: () => void;\n  // Clear all image state for new announcements\n  clearAllImageState: () => void;\n}\n\nexport const useMultipleImageUpload = ({\n  announcementId,\n  onSuccess,\n  onError\n}: UseMultipleImageUploadProps): UseMultipleImageUploadReturn => {\n  const [existingImages, setExistingImages] = useState<ExistingImage[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [pendingDeletes, setPendingDeletes] = useState<number[]>([]);\n\n  // Clear existing images when announcementId becomes null (for new announcements)\n  useEffect(() => {\n    if (!announcementId) {\n      console.log('🧹 useMultipleImageUpload - Clearing existing images for new announcement');\n      setExistingImages([]);\n      setPendingDeletes([]);\n      setError(null);\n    }\n  }, [announcementId]);\n\n  // Use the imported service instance\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Clear pending deletes\n  const clearPendingDeletes = useCallback(() => {\n    setPendingDeletes([]);\n  }, []);\n\n  // Clear all image-related state (for new announcements)\n  const clearAllImageState = useCallback(() => {\n    console.log('🧹 useMultipleImageUpload - Clearing all image state');\n    setExistingImages([]);\n    setPendingDeletes([]);\n    setError(null);\n    setLoading(false);\n  }, []);\n\n  // Mark image for deletion (don't delete immediately)\n  const markForDeletion = useCallback((attachmentId: number) => {\n    console.log('🔴 Marking image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      if (!prev.includes(attachmentId)) {\n        const newPending = [...prev, attachmentId];\n        console.log('🔴 Updated pending deletes:', newPending);\n        return newPending;\n      }\n      return prev;\n    });\n  }, []);\n\n  // Unmark image for deletion\n  const unmarkForDeletion = useCallback((attachmentId: number) => {\n    console.log('🟢 Unmarking image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      const newPending = prev.filter(id => id !== attachmentId);\n      console.log('🟢 Updated pending deletes:', newPending);\n      return newPending;\n    });\n  }, []);\n\n  // Handle API errors\n  const handleError = useCallback((err: any, defaultMessage: string) => {\n    const errorMessage = err?.response?.data?.message || err?.message || defaultMessage;\n    setError(errorMessage);\n    onError?.(errorMessage);\n  }, [onError]);\n\n  // Refresh images from server\n  const refreshImages = useCallback(async () => {\n    if (!announcementId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      \n      if (response.success && response.data?.images) {\n        setExistingImages(response.data.images);\n      } else {\n        setExistingImages([]);\n      }\n    } catch (err) {\n      handleError(err, 'Failed to load images');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, handleError]);\n\n  // Upload multiple images\n  const uploadImages = useCallback(async (files: File[]) => {\n    if (!announcementId || files.length === 0) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const formData = new FormData();\n      files.forEach((file) => {\n        formData.append('images', file);\n      });\n\n      const response = await adminAnnouncementService.addAnnouncementImages(announcementId, formData);\n\n      if (response.success) {\n        // Directly update state with new images instead of making another API call\n        if (response.data?.images) {\n          setExistingImages(response.data.images);\n        } else {\n          // Fallback to refresh if response doesn't include images\n          await refreshImages();\n        }\n        onSuccess?.(`Successfully uploaded ${files.length} image${files.length > 1 ? 's' : ''}`);\n      } else {\n        throw new Error(response.message || 'Upload failed');\n      }\n    } catch (err) {\n      handleError(err, 'Failed to upload images');\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError, refreshImages]);\n\n  // Apply pending deletes (called when user clicks Update)\n  const applyPendingDeletes = useCallback(async () => {\n    if (!announcementId || pendingDeletes.length === 0) {\n      return;\n    }\n\n    const deletesToApply = [...pendingDeletes]; // Capture current state\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Batch delete all pending images\n      const deletePromises = deletesToApply.map(attachmentId =>\n        adminAnnouncementService.deleteAnnouncementImage(announcementId, attachmentId)\n      );\n\n      const results = await Promise.allSettled(deletePromises);\n\n      // Check for any failures\n      const failures = results.filter(result => result.status === 'rejected');\n      if (failures.length > 0) {\n        throw new Error(`Failed to delete ${failures.length} image(s)`);\n      }\n\n      // Update state optimistically - remove deleted images from existingImages\n      setExistingImages(prev =>\n        prev.filter(img => !deletesToApply.includes(img.attachment_id))\n      );\n\n      // Clear pending deletes\n      setPendingDeletes([]);\n\n      onSuccess?.(`Successfully deleted ${deletesToApply.length} image(s)`);\n    } catch (err) {\n      console.error('❌ Error applying pending deletes:', err);\n      handleError(err, 'Failed to delete images');\n      // Refresh images to ensure consistency\n      await refreshImages();\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, pendingDeletes, onSuccess, handleError, refreshImages]);\n\n  // Delete an image (immediate deletion - kept for backward compatibility)\n  const deleteImage = useCallback(async (attachmentId: number) => {\n    if (!announcementId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await adminAnnouncementService.deleteAnnouncementImage(announcementId, attachmentId);\n\n      if (response.success) {\n        // Remove from local state immediately for better UX\n        setExistingImages(prev => prev.filter(img => img.attachment_id !== attachmentId));\n        onSuccess?.('Image deleted successfully');\n      } else {\n        throw new Error(response.message || 'Delete failed');\n      }\n    } catch (err) {\n      handleError(err, 'Failed to delete image');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && refreshResponse.data?.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n\n  // Update image display order\n  const updateImageOrder = useCallback(async (imageOrder: { attachment_id: number; display_order: number }[]) => {\n    if (!announcementId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await adminAnnouncementService.updateImageOrder(announcementId, imageOrder);\n      \n      if (response.success) {\n        // Update local state to reflect new order\n        setExistingImages(prev => {\n          const updated = [...prev];\n          imageOrder.forEach(({ attachment_id, display_order }) => {\n            const index = updated.findIndex(img => img.attachment_id === attachment_id);\n            if (index !== -1) {\n              updated[index] = { ...updated[index], display_order };\n            }\n          });\n          return updated.sort((a, b) => a.display_order - b.display_order);\n        });\n        onSuccess?.('Image order updated successfully');\n      } else {\n        throw new Error(response.message || 'Update failed');\n      }\n    } catch (err) {\n      handleError(err, 'Failed to update image order');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && refreshResponse.data?.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback(async (attachmentId: number) => {\n    if (!announcementId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await adminAnnouncementService.setPrimaryImage(announcementId, attachmentId);\n      \n      if (response.success) {\n        // Update local state to reflect new primary image\n        setExistingImages(prev => prev.map(img => ({\n          ...img,\n          is_primary: img.attachment_id === attachmentId\n        })));\n        onSuccess?.('Primary image updated successfully');\n      } else {\n        throw new Error(response.message || 'Update failed');\n      }\n    } catch (err) {\n      handleError(err, 'Failed to set primary image');\n      // Refresh images to ensure consistency\n      const refreshResponse = await adminAnnouncementService.getAnnouncementImages(announcementId);\n      if (refreshResponse.success && refreshResponse.data?.images) {\n        setExistingImages(refreshResponse.data.images);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [announcementId, onSuccess, handleError]);\n\n  return {\n    existingImages,\n    loading,\n    error,\n    uploadImages,\n    deleteImage,\n    updateImageOrder,\n    setPrimaryImage,\n    refreshImages,\n    clearError,\n    // New pending operations\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    // Clear all image state\n    clearAllImageState\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,wBAAwB,QAAQ,iCAAiC;AAqC1E,OAAO,MAAMC,sBAAsB,GAAGA,CAAC;EACrCC,cAAc;EACdC,SAAS;EACTC;AAC2B,CAAC,KAAmC;EAAAC,EAAA;EAC/D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;;EAElE;EACAE,SAAS,CAAC,MAAM;IACd,IAAI,CAACG,cAAc,EAAE;MACnBY,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;MACxFR,iBAAiB,CAAC,EAAE,CAAC;MACrBM,iBAAiB,CAAC,EAAE,CAAC;MACrBF,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,CAACT,cAAc,CAAC,CAAC;;EAEpB;;EAEA;EACA,MAAMc,UAAU,GAAGlB,WAAW,CAAC,MAAM;IACnCa,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,mBAAmB,GAAGnB,WAAW,CAAC,MAAM;IAC5Ce,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,kBAAkB,GAAGpB,WAAW,CAAC,MAAM;IAC3CgB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnER,iBAAiB,CAAC,EAAE,CAAC;IACrBM,iBAAiB,CAAC,EAAE,CAAC;IACrBF,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,eAAe,GAAGrB,WAAW,CAAEsB,YAAoB,IAAK;IAC5DN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,YAAY,CAAC;IAC3DP,iBAAiB,CAACQ,IAAI,IAAI;MACxB,IAAI,CAACA,IAAI,CAACC,QAAQ,CAACF,YAAY,CAAC,EAAE;QAChC,MAAMG,UAAU,GAAG,CAAC,GAAGF,IAAI,EAAED,YAAY,CAAC;QAC1CN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEQ,UAAU,CAAC;QACtD,OAAOA,UAAU;MACnB;MACA,OAAOF,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,iBAAiB,GAAG1B,WAAW,CAAEsB,YAAoB,IAAK;IAC9DN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,YAAY,CAAC;IAC7DP,iBAAiB,CAACQ,IAAI,IAAI;MACxB,MAAME,UAAU,GAAGF,IAAI,CAACI,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,YAAY,CAAC;MACzDN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEQ,UAAU,CAAC;MACtD,OAAOA,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,WAAW,GAAG7B,WAAW,CAAC,CAAC8B,GAAQ,EAAEC,cAAsB,KAAK;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACpE,MAAMC,YAAY,GAAG,CAAAJ,GAAG,aAAHA,GAAG,wBAAAE,aAAA,GAAHF,GAAG,CAAEK,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAbD,aAAA,CAAeI,IAAI,cAAAH,kBAAA,uBAAnBA,kBAAA,CAAqBI,OAAO,MAAIP,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEO,OAAO,KAAIN,cAAc;IACnFlB,QAAQ,CAACqB,YAAY,CAAC;IACtB5B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG4B,YAAY,CAAC;EACzB,CAAC,EAAE,CAAC5B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMgC,aAAa,GAAGtC,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACI,cAAc,EAAE;IAErB,IAAI;MAAA,IAAAmC,cAAA;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsB,QAAQ,GAAG,MAAMjC,wBAAwB,CAACsC,qBAAqB,CAACpC,cAAc,CAAC;MAErF,IAAI+B,QAAQ,CAACM,OAAO,KAAAF,cAAA,GAAIJ,QAAQ,CAACC,IAAI,cAAAG,cAAA,eAAbA,cAAA,CAAeG,MAAM,EAAE;QAC7CjC,iBAAiB,CAAC0B,QAAQ,CAACC,IAAI,CAACM,MAAM,CAAC;MACzC,CAAC,MAAM;QACLjC,iBAAiB,CAAC,EAAE,CAAC;MACvB;IACF,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZD,WAAW,CAACC,GAAG,EAAE,uBAAuB,CAAC;IAC3C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEyB,WAAW,CAAC,CAAC;;EAEjC;EACA,MAAMc,YAAY,GAAG3C,WAAW,CAAC,MAAO4C,KAAa,IAAK;IACxD,IAAI,CAACxC,cAAc,IAAIwC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IAE3C,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMiC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BH,KAAK,CAACI,OAAO,CAAEC,IAAI,IAAK;QACtBH,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;MACjC,CAAC,CAAC;MAEF,MAAMd,QAAQ,GAAG,MAAMjC,wBAAwB,CAACiD,qBAAqB,CAAC/C,cAAc,EAAE0C,QAAQ,CAAC;MAE/F,IAAIX,QAAQ,CAACM,OAAO,EAAE;QAAA,IAAAW,eAAA;QACpB;QACA,KAAAA,eAAA,GAAIjB,QAAQ,CAACC,IAAI,cAAAgB,eAAA,eAAbA,eAAA,CAAeV,MAAM,EAAE;UACzBjC,iBAAiB,CAAC0B,QAAQ,CAACC,IAAI,CAACM,MAAM,CAAC;QACzC,CAAC,MAAM;UACL;UACA,MAAMJ,aAAa,CAAC,CAAC;QACvB;QACAjC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,yBAAyBuC,KAAK,CAACC,MAAM,SAASD,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;MAC1F,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAAClB,QAAQ,CAACE,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZD,WAAW,CAACC,GAAG,EAAE,yBAAyB,CAAC;IAC7C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEC,SAAS,EAAEwB,WAAW,EAAES,aAAa,CAAC,CAAC;;EAE3D;EACA,MAAMgB,mBAAmB,GAAGtD,WAAW,CAAC,YAAY;IAClD,IAAI,CAACI,cAAc,IAAIU,cAAc,CAAC+B,MAAM,KAAK,CAAC,EAAE;MAClD;IACF;IAEA,MAAMU,cAAc,GAAG,CAAC,GAAGzC,cAAc,CAAC,CAAC,CAAC;;IAE5C,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM2C,cAAc,GAAGD,cAAc,CAACE,GAAG,CAACnC,YAAY,IACpDpB,wBAAwB,CAACwD,uBAAuB,CAACtD,cAAc,EAAEkB,YAAY,CAC/E,CAAC;MAED,MAAMqC,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAACL,cAAc,CAAC;;MAExD;MACA,MAAMM,QAAQ,GAAGH,OAAO,CAAChC,MAAM,CAACoC,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,CAAC;MACvE,IAAIF,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAE;QACvB,MAAM,IAAIQ,KAAK,CAAC,oBAAoBS,QAAQ,CAACjB,MAAM,WAAW,CAAC;MACjE;;MAEA;MACApC,iBAAiB,CAACc,IAAI,IACpBA,IAAI,CAACI,MAAM,CAACsC,GAAG,IAAI,CAACV,cAAc,CAAC/B,QAAQ,CAACyC,GAAG,CAACC,aAAa,CAAC,CAChE,CAAC;;MAED;MACAnD,iBAAiB,CAAC,EAAE,CAAC;MAErBV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,wBAAwBkD,cAAc,CAACV,MAAM,WAAW,CAAC;IACvE,CAAC,CAAC,OAAOf,GAAG,EAAE;MACZd,OAAO,CAACJ,KAAK,CAAC,mCAAmC,EAAEkB,GAAG,CAAC;MACvDD,WAAW,CAACC,GAAG,EAAE,yBAAyB,CAAC;MAC3C;MACA,MAAMQ,aAAa,CAAC,CAAC;IACvB,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEU,cAAc,EAAET,SAAS,EAAEwB,WAAW,EAAES,aAAa,CAAC,CAAC;;EAE3E;EACA,MAAM6B,WAAW,GAAGnE,WAAW,CAAC,MAAOsB,YAAoB,IAAK;IAC9D,IAAI,CAAClB,cAAc,EAAE;IAErB,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsB,QAAQ,GAAG,MAAMjC,wBAAwB,CAACwD,uBAAuB,CAACtD,cAAc,EAAEkB,YAAY,CAAC;MAErG,IAAIa,QAAQ,CAACM,OAAO,EAAE;QACpB;QACAhC,iBAAiB,CAACc,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACsC,GAAG,IAAIA,GAAG,CAACC,aAAa,KAAK5C,YAAY,CAAC,CAAC;QACjFjB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,4BAA4B,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM,IAAIgD,KAAK,CAAClB,QAAQ,CAACE,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOP,GAAG,EAAE;MAAA,IAAAsC,qBAAA;MACZvC,WAAW,CAACC,GAAG,EAAE,wBAAwB,CAAC;MAC1C;MACA,MAAMuC,eAAe,GAAG,MAAMnE,wBAAwB,CAACsC,qBAAqB,CAACpC,cAAc,CAAC;MAC5F,IAAIiE,eAAe,CAAC5B,OAAO,KAAA2B,qBAAA,GAAIC,eAAe,CAACjC,IAAI,cAAAgC,qBAAA,eAApBA,qBAAA,CAAsB1B,MAAM,EAAE;QAC3DjC,iBAAiB,CAAC4D,eAAe,CAACjC,IAAI,CAACM,MAAM,CAAC;MAChD;IACF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEC,SAAS,EAAEwB,WAAW,CAAC,CAAC;;EAE5C;EACA,MAAMyC,gBAAgB,GAAGtE,WAAW,CAAC,MAAOuE,UAA8D,IAAK;IAC7G,IAAI,CAACnE,cAAc,EAAE;IAErB,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsB,QAAQ,GAAG,MAAMjC,wBAAwB,CAACoE,gBAAgB,CAAClE,cAAc,EAAEmE,UAAU,CAAC;MAE5F,IAAIpC,QAAQ,CAACM,OAAO,EAAE;QACpB;QACAhC,iBAAiB,CAACc,IAAI,IAAI;UACxB,MAAMiD,OAAO,GAAG,CAAC,GAAGjD,IAAI,CAAC;UACzBgD,UAAU,CAACvB,OAAO,CAAC,CAAC;YAAEkB,aAAa;YAAEO;UAAc,CAAC,KAAK;YACvD,MAAMC,KAAK,GAAGF,OAAO,CAACG,SAAS,CAACV,GAAG,IAAIA,GAAG,CAACC,aAAa,KAAKA,aAAa,CAAC;YAC3E,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;cAChBF,OAAO,CAACE,KAAK,CAAC,GAAG;gBAAE,GAAGF,OAAO,CAACE,KAAK,CAAC;gBAAED;cAAc,CAAC;YACvD;UACF,CAAC,CAAC;UACF,OAAOD,OAAO,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,aAAa,GAAGK,CAAC,CAACL,aAAa,CAAC;QAClE,CAAC,CAAC;QACFpE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,kCAAkC,CAAC;MACjD,CAAC,MAAM;QACL,MAAM,IAAIgD,KAAK,CAAClB,QAAQ,CAACE,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOP,GAAG,EAAE;MAAA,IAAAiD,sBAAA;MACZlD,WAAW,CAACC,GAAG,EAAE,8BAA8B,CAAC;MAChD;MACA,MAAMuC,eAAe,GAAG,MAAMnE,wBAAwB,CAACsC,qBAAqB,CAACpC,cAAc,CAAC;MAC5F,IAAIiE,eAAe,CAAC5B,OAAO,KAAAsC,sBAAA,GAAIV,eAAe,CAACjC,IAAI,cAAA2C,sBAAA,eAApBA,sBAAA,CAAsBrC,MAAM,EAAE;QAC3DjC,iBAAiB,CAAC4D,eAAe,CAACjC,IAAI,CAACM,MAAM,CAAC;MAChD;IACF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEC,SAAS,EAAEwB,WAAW,CAAC,CAAC;;EAE5C;EACA,MAAMmD,eAAe,GAAGhF,WAAW,CAAC,MAAOsB,YAAoB,IAAK;IAClE,IAAI,CAAClB,cAAc,EAAE;IAErB,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsB,QAAQ,GAAG,MAAMjC,wBAAwB,CAAC8E,eAAe,CAAC5E,cAAc,EAAEkB,YAAY,CAAC;MAE7F,IAAIa,QAAQ,CAACM,OAAO,EAAE;QACpB;QACAhC,iBAAiB,CAACc,IAAI,IAAIA,IAAI,CAACkC,GAAG,CAACQ,GAAG,KAAK;UACzC,GAAGA,GAAG;UACNgB,UAAU,EAAEhB,GAAG,CAACC,aAAa,KAAK5C;QACpC,CAAC,CAAC,CAAC,CAAC;QACJjB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,oCAAoC,CAAC;MACnD,CAAC,MAAM;QACL,MAAM,IAAIgD,KAAK,CAAClB,QAAQ,CAACE,OAAO,IAAI,eAAe,CAAC;MACtD;IACF,CAAC,CAAC,OAAOP,GAAG,EAAE;MAAA,IAAAoD,sBAAA;MACZrD,WAAW,CAACC,GAAG,EAAE,6BAA6B,CAAC;MAC/C;MACA,MAAMuC,eAAe,GAAG,MAAMnE,wBAAwB,CAACsC,qBAAqB,CAACpC,cAAc,CAAC;MAC5F,IAAIiE,eAAe,CAAC5B,OAAO,KAAAyC,sBAAA,GAAIb,eAAe,CAACjC,IAAI,cAAA8C,sBAAA,eAApBA,sBAAA,CAAsBxC,MAAM,EAAE;QAC3DjC,iBAAiB,CAAC4D,eAAe,CAACjC,IAAI,CAACM,MAAM,CAAC;MAChD;IACF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEC,SAAS,EAAEwB,WAAW,CAAC,CAAC;EAE5C,OAAO;IACLrB,cAAc;IACdE,OAAO;IACPE,KAAK;IACL+B,YAAY;IACZwB,WAAW;IACXG,gBAAgB;IAChBU,eAAe;IACf1C,aAAa;IACbpB,UAAU;IACV;IACAJ,cAAc;IACdO,eAAe;IACfK,iBAAiB;IACjB4B,mBAAmB;IACnBnC,mBAAmB;IACnB;IACAC;EACF,CAAC;AACH,CAAC;AAACb,EAAA,CAjSWJ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}