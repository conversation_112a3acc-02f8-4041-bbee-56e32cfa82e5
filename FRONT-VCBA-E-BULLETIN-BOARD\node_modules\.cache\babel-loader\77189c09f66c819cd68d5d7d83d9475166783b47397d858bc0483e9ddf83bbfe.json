{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z\",\n  key: \"1pdavp\"\n}], [\"path\", {\n  d: \"M20.054 15.987H3.946\",\n  key: \"14rxg9\"\n}]];\nconst Laptop = createLucideIcon(\"laptop\", __iconNode);\nexport { __iconNode, Laptop as default };\n//# sourceMappingURL=laptop.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}