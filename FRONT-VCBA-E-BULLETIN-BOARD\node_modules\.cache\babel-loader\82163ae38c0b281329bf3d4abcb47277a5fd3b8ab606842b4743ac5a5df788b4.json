{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l2-4\",\n  key: \"miptyd\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst Clock1 = createLucideIcon(\"clock-1\", __iconNode);\nexport { __iconNode, Clock1 as default };\n//# sourceMappingURL=clock-1.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}