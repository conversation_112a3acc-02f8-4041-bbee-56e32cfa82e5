{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10 7 7\",\n  key: \"zp14k7\"\n}], [\"path\", {\n  d: \"m10 14-3 3\",\n  key: \"1jrpxk\"\n}], [\"path\", {\n  d: \"m14 10 3-3\",\n  key: \"7tigam\"\n}], [\"path\", {\n  d: \"m14 14 3 3\",\n  key: \"vm23p3\"\n}], [\"path\", {\n  d: \"M14.205 4.139a4 4 0 1 1 5.439 5.863\",\n  key: \"1tm5p2\"\n}], [\"path\", {\n  d: \"M19.637 14a4 4 0 1 1-5.432 5.868\",\n  key: \"16egi2\"\n}], [\"path\", {\n  d: \"M4.367 10a4 4 0 1 1 5.438-5.862\",\n  key: \"1wta6a\"\n}], [\"path\", {\n  d: \"M9.795 19.862a4 4 0 1 1-5.429-5.873\",\n  key: \"q39hpv\"\n}], [\"rect\", {\n  x: \"10\",\n  y: \"8\",\n  width: \"4\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"phrjt1\"\n}]];\nconst Drone = createLucideIcon(\"drone\", __iconNode);\nexport { __iconNode, Drone as default };\n//# sourceMappingURL=drone.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}