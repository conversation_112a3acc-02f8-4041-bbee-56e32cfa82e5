{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 8L22 12L18 16\",\n  key: \"1r0oui\"\n}], [\"path\", {\n  d: \"M2 12H22\",\n  key: \"1m8cig\"\n}]];\nconst MoveRight = createLucideIcon(\"move-right\", __iconNode);\nexport { __iconNode, MoveRight as default };\n//# sourceMappingURL=move-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}