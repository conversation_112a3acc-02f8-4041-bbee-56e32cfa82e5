{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m20 13.7-2.1-2.1a2 2 0 0 0-2.8 0L9.7 17\",\n  key: \"q6ojf0\"\n}], [\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20\",\n  key: \"k3hazp\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"2qkj4p\"\n}]];\nconst BookImage = createLucideIcon(\"book-image\", __iconNode);\nexport { __iconNode, BookImage as default };\n//# sourceMappingURL=book-image.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}