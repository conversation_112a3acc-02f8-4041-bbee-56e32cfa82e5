// Generated by dts-bundle v0.7.3

declare module "@eslint-community/regexpp" {
  import * as AST from "@eslint-community/regexpp/ast";
  import { RegExpParser } from "@eslint-community/regexpp/parser";
  import { RegExpValidator } from "@eslint-community/regexpp/validator";
  import { RegExpVisitor } from "@eslint-community/regexpp/visitor";
  export { RegExpSyntaxError } from "@eslint-community/regexpp/regexp-syntax-error";
  export { AST, RegExpParser, RegExpValidator };
  /**
   * Parse a given regular expression literal then make AST object.
   * @param source The source code to parse.
   * @param options The options to parse.
   * @returns The AST of the regular expression.
   */
  export function parseRegExpLiteral(
    source: RegExp | string,
    options?: RegExpParser.Options
  ): AST.RegExpLiteral;
  /**
   * Validate a given regular expression literal.
   * @param source The source code to validate.
   * @param options The options to validate.
   */
  export function validateRegExpLiteral(
    source: string,
    options?: RegExpValidator.Options
  ): void;
  export function visitRegExpAST(
    node: AST.Node,
    handlers: RegExpVisitor.Handlers
  ): void;
}

declare module "@eslint-community/regexpp/ast" {
  /**
   * The type which includes all nodes.
   */
  export type Node = BranchNode | LeafNode;
  /**
   * The type which includes all branch nodes.
   */
  export type BranchNode =
    | Alternative
    | CapturingGroup
    | CharacterClass
    | CharacterClassRange
    | ClassIntersection
    | ClassStringDisjunction
    | ClassSubtraction
    | ExpressionCharacterClass
    | Group
    | LookaroundAssertion
    | Modifiers
    | Pattern
    | Quantifier
    | RegExpLiteral
    | StringAlternative;
  /**
   * The type which includes all leaf nodes.
   */
  export type LeafNode =
    | Backreference
    | BoundaryAssertion
    | Character
    | CharacterSet
    | Flags
    | ModifierFlags;
  /**
   * The type which includes all atom nodes.
   */
  export type Element = Assertion | QuantifiableElement | Quantifier;
  /**
   * The type which includes all atom nodes that Quantifier node can have as children.
   */
  export type QuantifiableElement =
    | Backreference
    | CapturingGroup
    | Character
    | CharacterClass
    | CharacterSet
    | ExpressionCharacterClass
    | Group
    | LookaheadAssertion;
  /**
   * The type which includes all character class atom nodes.
   */
  export type CharacterClassElement =
    | ClassRangesCharacterClassElement
    | UnicodeSetsCharacterClassElement;
  export type ClassRangesCharacterClassElement =
    | Character
    | CharacterClassRange
    | CharacterUnicodePropertyCharacterSet
    | EscapeCharacterSet;
  export type UnicodeSetsCharacterClassElement =
    | Character
    | CharacterClassRange
    | ClassStringDisjunction
    | EscapeCharacterSet
    | ExpressionCharacterClass
    | UnicodePropertyCharacterSet
    | UnicodeSetsCharacterClass;
  /**
   * The type which defines common properties for all node types.
   */
  export interface NodeBase {
    /** The node type. */
    type: Node["type"];
    /** The parent node. */
    parent: Node["parent"];
    /** The 0-based index that this node starts. */
    start: number;
    /** The 0-based index that this node ends. */
    end: number;
    /** The raw text of this node. */
    raw: string;
  }
  /**
   * The root node.
   */
  export interface RegExpLiteral extends NodeBase {
    type: "RegExpLiteral";
    parent: null;
    pattern: Pattern;
    flags: Flags;
  }
  /**
   * The pattern.
   */
  export interface Pattern extends NodeBase {
    type: "Pattern";
    parent: RegExpLiteral | null;
    alternatives: Alternative[];
  }
  /**
   * The alternative.
   * E.g. `a|b`
   */
  export interface Alternative extends NodeBase {
    type: "Alternative";
    parent: CapturingGroup | Group | LookaroundAssertion | Pattern;
    elements: Element[];
  }
  /**
   * The uncapturing group.
   * E.g. `(?:ab)`
   */
  export interface Group extends NodeBase {
    type: "Group";
    parent: Alternative | Quantifier;
    modifiers: Modifiers | null;
    alternatives: Alternative[];
  }
  /**
   * The capturing group.
   * E.g. `(ab)`, `(?<name>ab)`
   */
  export interface CapturingGroup extends NodeBase {
    type: "CapturingGroup";
    parent: Alternative | Quantifier;
    name: string | null;
    alternatives: Alternative[];
    references: Backreference[];
  }
  /**
   * The lookaround assertion.
   */
  export type LookaroundAssertion = LookaheadAssertion | LookbehindAssertion;
  /**
   * The lookahead assertion.
   * E.g. `(?=ab)`, `(?!ab)`
   */
  export interface LookaheadAssertion extends NodeBase {
    type: "Assertion";
    parent: Alternative | Quantifier;
    kind: "lookahead";
    negate: boolean;
    alternatives: Alternative[];
  }
  /**
   * The lookbehind assertion.
   * E.g. `(?<=ab)`, `(?<!ab)`
   */
  export interface LookbehindAssertion extends NodeBase {
    type: "Assertion";
    parent: Alternative;
    kind: "lookbehind";
    negate: boolean;
    alternatives: Alternative[];
  }
  /**
   * The quantifier.
   * E.g. `a?`, `a*`, `a+`, `a{1,2}`, `a??`, `a*?`, `a+?`, `a{1,2}?`
   */
  export interface Quantifier extends NodeBase {
    type: "Quantifier";
    parent: Alternative;
    min: number;
    max: number;
    greedy: boolean;
    element: QuantifiableElement;
  }
  /**
   * The character class.
   * E.g. `[ab]`, `[^ab]`
   */
  export type CharacterClass =
    | ClassRangesCharacterClass
    | UnicodeSetsCharacterClass;
  interface BaseCharacterClass extends NodeBase {
    type: "CharacterClass";
    parent:
      | Alternative
      | ClassIntersection
      | ClassSubtraction
      | Quantifier
      | UnicodeSetsCharacterClass;
    unicodeSets: boolean;
    negate: boolean;
    elements: CharacterClassElement[];
  }
  /**
   * The character class used in legacy (neither `u` nor `v` flag) and Unicode mode (`u` flag).
   *
   * This character class is guaranteed to **not** contain strings.
   *
   * In Unicode sets mode (`v` flag), {@link UnicodeSetsCharacterClass} is used.
   */
  export interface ClassRangesCharacterClass extends BaseCharacterClass {
    parent: Alternative | Quantifier;
    unicodeSets: false;
    elements: ClassRangesCharacterClassElement[];
  }
  /**
   * The character class used in Unicode sets mode (`v` flag).
   *
   * This character class may contain strings.
   */
  export interface UnicodeSetsCharacterClass extends BaseCharacterClass {
    parent:
      | Alternative
      | ClassIntersection
      | ClassSubtraction
      | Quantifier
      | UnicodeSetsCharacterClass;
    unicodeSets: true;
    elements: UnicodeSetsCharacterClassElement[];
  }
  /**
   * The character class.
   * E.g. `[a-b]`
   */
  export interface CharacterClassRange extends NodeBase {
    type: "CharacterClassRange";
    parent: CharacterClass;
    min: Character;
    max: Character;
  }
  /**
   * The assertion.
   */
  export type Assertion = BoundaryAssertion | LookaroundAssertion;
  /**
   * The boundary assertion.
   */
  export type BoundaryAssertion = EdgeAssertion | WordBoundaryAssertion;
  /**
   * The edge boundary assertion.
   * E.g. `^`, `$`
   */
  export interface EdgeAssertion extends NodeBase {
    type: "Assertion";
    parent: Alternative | Quantifier;
    kind: "end" | "start";
  }
  /**
   * The word bondary assertion.
   * E.g. `\b`, `\B`
   */
  export interface WordBoundaryAssertion extends NodeBase {
    type: "Assertion";
    parent: Alternative | Quantifier;
    kind: "word";
    negate: boolean;
  }
  /**
   * The character set.
   */
  export type CharacterSet =
    | AnyCharacterSet
    | EscapeCharacterSet
    | UnicodePropertyCharacterSet;
  /**
   * The dot.
   * E.g. `.`
   */
  export interface AnyCharacterSet extends NodeBase {
    type: "CharacterSet";
    parent: Alternative | Quantifier;
    kind: "any";
  }
  /**
   * The character class escape.
   * E.g. `\d`, `\s`, `\w`, `\D`, `\S`, `\W`
   */
  export interface EscapeCharacterSet extends NodeBase {
    type: "CharacterSet";
    parent:
      | Alternative
      | CharacterClass
      | ClassIntersection
      | ClassSubtraction
      | Quantifier;
    kind: "digit" | "space" | "word";
    negate: boolean;
  }
  /**
   * The unicode property escape.
   * E.g. `\p{ASCII}`, `\P{ASCII}`, `\p{Script=Hiragana}`
   */
  export type UnicodePropertyCharacterSet =
    | CharacterUnicodePropertyCharacterSet
    | StringsUnicodePropertyCharacterSet;
  interface BaseUnicodePropertyCharacterSet extends NodeBase {
    type: "CharacterSet";
    parent:
      | Alternative
      | CharacterClass
      | ClassIntersection
      | ClassSubtraction
      | Quantifier;
    kind: "property";
    strings: boolean;
    key: string;
    value: string | null;
    negate: boolean;
  }
  export interface CharacterUnicodePropertyCharacterSet
    extends BaseUnicodePropertyCharacterSet {
    strings: false;
    value: string | null;
    negate: boolean;
  }
  /** StringsUnicodePropertyCharacterSet is Unicode property escape with property of strings. */
  export interface StringsUnicodePropertyCharacterSet
    extends BaseUnicodePropertyCharacterSet {
    parent:
      | Alternative
      | ClassIntersection
      | ClassSubtraction
      | Quantifier
      | UnicodeSetsCharacterClass;
    strings: true;
    value: null;
    negate: false;
  }
  /**
   * The expression character class.
   * E.g. `[a--b]`, `[a&&b]`,`[^a--b]`, `[^a&&b]`
   */
  export interface ExpressionCharacterClass extends NodeBase {
    type: "ExpressionCharacterClass";
    parent:
      | Alternative
      | ClassIntersection
      | ClassSubtraction
      | Quantifier
      | UnicodeSetsCharacterClass;
    negate: boolean;
    expression: ClassIntersection | ClassSubtraction;
  }
  export type ClassSetOperand =
    | Character
    | ClassStringDisjunction
    | EscapeCharacterSet
    | ExpressionCharacterClass
    | UnicodePropertyCharacterSet
    | UnicodeSetsCharacterClass;
  /**
   * The character class intersection.
   * E.g. `a&&b`
   */
  export interface ClassIntersection extends NodeBase {
    type: "ClassIntersection";
    parent: ClassIntersection | ExpressionCharacterClass;
    left: ClassIntersection | ClassSetOperand;
    right: ClassSetOperand;
  }
  /**
   * The character class subtraction.
   * E.g. `a--b`
   */
  export interface ClassSubtraction extends NodeBase {
    type: "ClassSubtraction";
    parent: ClassSubtraction | ExpressionCharacterClass;
    left: ClassSetOperand | ClassSubtraction;
    right: ClassSetOperand;
  }
  /**
   * The character class string disjunction.
   * E.g. `\q{a|b}`
   */
  export interface ClassStringDisjunction extends NodeBase {
    type: "ClassStringDisjunction";
    parent: ClassIntersection | ClassSubtraction | UnicodeSetsCharacterClass;
    alternatives: StringAlternative[];
  }
  /** StringAlternative is only used for `\q{alt}`({@link ClassStringDisjunction}). */
  export interface StringAlternative extends NodeBase {
    type: "StringAlternative";
    parent: ClassStringDisjunction;
    elements: Character[];
  }
  /**
   * The character.
   * This includes escape sequences which mean a character.
   * E.g. `a`, `あ`, `✿`, `\x65`, `\u0065`, `\u{65}`, `\/`
   */
  export interface Character extends NodeBase {
    type: "Character";
    parent:
      | Alternative
      | CharacterClass
      | CharacterClassRange
      | ClassIntersection
      | ClassSubtraction
      | Quantifier
      | StringAlternative;
    value: number;
  }
  /**
   * The backreference.
   * E.g. `\1`, `\k<name>`
   */
  export type Backreference = AmbiguousBackreference | UnambiguousBackreference;
  interface BaseBackreference extends NodeBase {
    type: "Backreference";
    parent: Alternative | Quantifier;
    ref: number | string;
    ambiguous: boolean;
    resolved: CapturingGroup | CapturingGroup[];
  }
  export interface AmbiguousBackreference extends BaseBackreference {
    ref: string;
    ambiguous: true;
    resolved: CapturingGroup[];
  }
  export interface UnambiguousBackreference extends BaseBackreference {
    ambiguous: false;
    resolved: CapturingGroup;
  }
  /**
   * The modifiers.
   */
  export interface Modifiers extends NodeBase {
    type: "Modifiers";
    parent: Group;
    /**
     * The add modifier flags.
     */
    add: ModifierFlags;
    /**
     * The remove modifier flags.
     *
     * `null` means no remove modifier flags. e.g. `(?ims:x)`
     * The reason for `null` is that there is no position where the remove modifier flags appears. Must be behind the minus mark.
     */
    remove: ModifierFlags | null;
  }
  /**
   * The modifier flags.
   */
  export interface ModifierFlags extends NodeBase {
    type: "ModifierFlags";
    parent: Modifiers;
    dotAll: boolean;
    ignoreCase: boolean;
    multiline: boolean;
  }
  /**
   * The flags.
   */
  export interface Flags extends NodeBase {
    type: "Flags";
    parent: RegExpLiteral | null;
    dotAll: boolean;
    global: boolean;
    hasIndices: boolean;
    ignoreCase: boolean;
    multiline: boolean;
    sticky: boolean;
    unicode: boolean;
    unicodeSets: boolean;
  }
  export {};
}

declare module "@eslint-community/regexpp/parser" {
  import type {
    Flags,
    RegExpLiteral,
    Pattern,
  } from "@eslint-community/regexpp/ast";
  import type { EcmaVersion } from "@eslint-community/regexpp/ecma-versions";
  export namespace RegExpParser {
    /**
     * The options for RegExpParser construction.
     */
    interface Options {
      /**
       * The flag to disable Annex B syntax. Default is `false`.
       */
      strict?: boolean;
      /**
       * ECMAScript version. Default is `2025`.
       * - `2015` added `u` and `y` flags.
       * - `2018` added `s` flag, Named Capturing Group, Lookbehind Assertion,
       *   and Unicode Property Escape.
       * - `2019`, `2020`, and `2021` added more valid Unicode Property Escapes.
       * - `2022` added `d` flag.
       * - `2023` added more valid Unicode Property Escapes.
       * - `2024` added `v` flag.
       * - `2025` added duplicate named capturing groups, modifiers.
       */
      ecmaVersion?: EcmaVersion;
    }
  }
  export class RegExpParser {
    /**
     * Initialize this parser.
     * @param options The options of parser.
     */
    constructor(options?: RegExpParser.Options);
    /**
     * Parse a regular expression literal. E.g. "/abc/g"
     * @param source The source code to parse.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @returns The AST of the given regular expression.
     */
    parseLiteral(source: string, start?: number, end?: number): RegExpLiteral;
    /**
     * Parse a regular expression flags. E.g. "gim"
     * @param source The source code to parse.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @returns The AST of the given flags.
     */
    parseFlags(source: string, start?: number, end?: number): Flags;
    /**
     * Parse a regular expression pattern. E.g. "abc"
     * @param source The source code to parse.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @param flags The flags.
     * @returns The AST of the given pattern.
     */
    parsePattern(
      source: string,
      start?: number,
      end?: number,
      flags?: {
        unicode?: boolean;
        unicodeSets?: boolean;
      }
    ): Pattern;
    /**
     * @deprecated Backward compatibility
     * Use object `flags` instead of boolean `uFlag`.
     *
     * @param source The source code to parse.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @param uFlag The flag to set unicode mode.
     * @returns The AST of the given pattern.
     */
    parsePattern(
      source: string,
      start?: number,
      end?: number,
      uFlag?: boolean
    ): Pattern;
  }
}

declare module "@eslint-community/regexpp/validator" {
  import type { EcmaVersion } from "@eslint-community/regexpp/ecma-versions";
  export type RegExpValidatorSourceContext = {
    readonly source: string;
    readonly start: number;
    readonly end: number;
    readonly kind: "flags" | "literal" | "pattern";
  };
  export namespace RegExpValidator {
    /**
     * The options for RegExpValidator construction.
     */
    interface Options {
      /**
       * The flag to disable Annex B syntax. Default is `false`.
       */
      strict?: boolean;
      /**
       * ECMAScript version. Default is `2025`.
       * - `2015` added `u` and `y` flags.
       * - `2018` added `s` flag, Named Capturing Group, Lookbehind Assertion,
       *   and Unicode Property Escape.
       * - `2019`, `2020`, and `2021` added more valid Unicode Property Escapes.
       * - `2022` added `d` flag.
       * - `2023` added more valid Unicode Property Escapes.
       * - `2024` added `v` flag.
       * - `2025` added duplicate named capturing groups, modifiers.
       */
      ecmaVersion?: EcmaVersion;
      /**
       * A function that is called when the validator entered a RegExp literal.
       * @param start The 0-based index of the first character.
       */
      onLiteralEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left a RegExp literal.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onLiteralLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator found flags.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param flags.global `g` flag.
       * @param flags.ignoreCase `i` flag.
       * @param flags.multiline `m` flag.
       * @param flags.unicode `u` flag.
       * @param flags.sticky `y` flag.
       * @param flags.dotAll `s` flag.
       * @param flags.hasIndices `d` flag.
       * @param flags.unicodeSets `v` flag.
       */
      onRegExpFlags?: (
        start: number,
        end: number,
        flags: {
          global: boolean;
          ignoreCase: boolean;
          multiline: boolean;
          unicode: boolean;
          sticky: boolean;
          dotAll: boolean;
          hasIndices: boolean;
          unicodeSets: boolean;
        }
      ) => void;
      /**
       * A function that is called when the validator found flags.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param global `g` flag.
       * @param ignoreCase `i` flag.
       * @param multiline `m` flag.
       * @param unicode `u` flag.
       * @param sticky `y` flag.
       * @param dotAll `s` flag.
       * @param hasIndices `d` flag.
       *
       * @deprecated Use `onRegExpFlags` instead.
       */
      onFlags?: (
        start: number,
        end: number,
        global: boolean,
        ignoreCase: boolean,
        multiline: boolean,
        unicode: boolean,
        sticky: boolean,
        dotAll: boolean,
        hasIndices: boolean
      ) => void;
      /**
       * A function that is called when the validator entered a pattern.
       * @param start The 0-based index of the first character.
       */
      onPatternEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left a pattern.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onPatternLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator entered a disjunction.
       * @param start The 0-based index of the first character.
       */
      onDisjunctionEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left a disjunction.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onDisjunctionLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator entered an alternative.
       * @param start The 0-based index of the first character.
       * @param index The 0-based index of alternatives in a disjunction.
       */
      onAlternativeEnter?: (start: number, index: number) => void;
      /**
       * A function that is called when the validator left an alternative.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param index The 0-based index of alternatives in a disjunction.
       */
      onAlternativeLeave?: (start: number, end: number, index: number) => void;
      /**
       * A function that is called when the validator entered an uncapturing group.
       * @param start The 0-based index of the first character.
       */
      onGroupEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left an uncapturing group.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onGroupLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator entered a modifiers.
       * @param start The 0-based index of the first character.
       */
      onModifiersEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left a modifiers.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onModifiersLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator found an add modifiers.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param flags flags.
       * @param flags.ignoreCase `i` flag.
       * @param flags.multiline `m` flag.
       * @param flags.dotAll `s` flag.
       */
      onAddModifiers?: (
        start: number,
        end: number,
        flags: {
          ignoreCase: boolean;
          multiline: boolean;
          dotAll: boolean;
        }
      ) => void;
      /**
       * A function that is called when the validator found a remove modifiers.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param flags flags.
       * @param flags.ignoreCase `i` flag.
       * @param flags.multiline `m` flag.
       * @param flags.dotAll `s` flag.
       */
      onRemoveModifiers?: (
        start: number,
        end: number,
        flags: {
          ignoreCase: boolean;
          multiline: boolean;
          dotAll: boolean;
        }
      ) => void;
      /**
       * A function that is called when the validator entered a capturing group.
       * @param start The 0-based index of the first character.
       * @param name The group name.
       */
      onCapturingGroupEnter?: (start: number, name: string | null) => void;
      /**
       * A function that is called when the validator left a capturing group.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param name The group name.
       */
      onCapturingGroupLeave?: (
        start: number,
        end: number,
        name: string | null
      ) => void;
      /**
       * A function that is called when the validator found a quantifier.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param min The minimum number of repeating.
       * @param max The maximum number of repeating.
       * @param greedy The flag to choose the longest matching.
       */
      onQuantifier?: (
        start: number,
        end: number,
        min: number,
        max: number,
        greedy: boolean
      ) => void;
      /**
       * A function that is called when the validator entered a lookahead/lookbehind assertion.
       * @param start The 0-based index of the first character.
       * @param kind The kind of the assertion.
       * @param negate The flag which represents that the assertion is negative.
       */
      onLookaroundAssertionEnter?: (
        start: number,
        kind: "lookahead" | "lookbehind",
        negate: boolean
      ) => void;
      /**
       * A function that is called when the validator left a lookahead/lookbehind assertion.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the assertion.
       * @param negate The flag which represents that the assertion is negative.
       */
      onLookaroundAssertionLeave?: (
        start: number,
        end: number,
        kind: "lookahead" | "lookbehind",
        negate: boolean
      ) => void;
      /**
       * A function that is called when the validator found an edge boundary assertion.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the assertion.
       */
      onEdgeAssertion?: (
        start: number,
        end: number,
        kind: "end" | "start"
      ) => void;
      /**
       * A function that is called when the validator found a word boundary assertion.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the assertion.
       * @param negate The flag which represents that the assertion is negative.
       */
      onWordBoundaryAssertion?: (
        start: number,
        end: number,
        kind: "word",
        negate: boolean
      ) => void;
      /**
       * A function that is called when the validator found a dot.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the character set.
       */
      onAnyCharacterSet?: (start: number, end: number, kind: "any") => void;
      /**
       * A function that is called when the validator found a character set escape.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the character set.
       * @param negate The flag which represents that the character set is negative.
       */
      onEscapeCharacterSet?: (
        start: number,
        end: number,
        kind: "digit" | "space" | "word",
        negate: boolean
      ) => void;
      /**
       * A function that is called when the validator found a Unicode proerty escape.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param kind The kind of the character set.
       * @param key The property name.
       * @param value The property value.
       * @param negate The flag which represents that the character set is negative.
       * @param strings If true, the given property is property of strings.
       */
      onUnicodePropertyCharacterSet?: (
        start: number,
        end: number,
        kind: "property",
        key: string,
        value: string | null,
        negate: boolean,
        strings: boolean
      ) => void;
      /**
       * A function that is called when the validator found a character.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param value The code point of the character.
       */
      onCharacter?: (start: number, end: number, value: number) => void;
      /**
       * A function that is called when the validator found a backreference.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param ref The key of the referred capturing group.
       */
      onBackreference?: (
        start: number,
        end: number,
        ref: number | string
      ) => void;
      /**
       * A function that is called when the validator entered a character class.
       * @param start The 0-based index of the first character.
       * @param negate The flag which represents that the character class is negative.
       * @param unicodeSets `true` if unicodeSets mode.
       */
      onCharacterClassEnter?: (
        start: number,
        negate: boolean,
        unicodeSets: boolean
      ) => void;
      /**
       * A function that is called when the validator left a character class.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param negate The flag which represents that the character class is negative.
       */
      onCharacterClassLeave?: (
        start: number,
        end: number,
        negate: boolean
      ) => void;
      /**
       * A function that is called when the validator found a character class range.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param min The minimum code point of the range.
       * @param max The maximum code point of the range.
       */
      onCharacterClassRange?: (
        start: number,
        end: number,
        min: number,
        max: number
      ) => void;
      /**
       * A function that is called when the validator found a class intersection.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onClassIntersection?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator found a class subtraction.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onClassSubtraction?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator entered a class string disjunction.
       * @param start The 0-based index of the first character.
       */
      onClassStringDisjunctionEnter?: (start: number) => void;
      /**
       * A function that is called when the validator left a class string disjunction.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       */
      onClassStringDisjunctionLeave?: (start: number, end: number) => void;
      /**
       * A function that is called when the validator entered a string alternative.
       * @param start The 0-based index of the first character.
       * @param index The 0-based index of alternatives in a disjunction.
       */
      onStringAlternativeEnter?: (start: number, index: number) => void;
      /**
       * A function that is called when the validator left a string alternative.
       * @param start The 0-based index of the first character.
       * @param end The next 0-based index of the last character.
       * @param index The 0-based index of alternatives in a disjunction.
       */
      onStringAlternativeLeave?: (
        start: number,
        end: number,
        index: number
      ) => void;
    }
  }
  /**
   * The regular expression validator.
   */
  export class RegExpValidator {
    /**
     * Initialize this validator.
     * @param options The options of validator.
     */
    constructor(options?: RegExpValidator.Options);
    /**
     * Validate a regular expression literal. E.g. "/abc/g"
     * @param source The source code to validate.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     */
    validateLiteral(source: string, start?: number, end?: number): void;
    /**
     * Validate a regular expression flags. E.g. "gim"
     * @param source The source code to validate.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     */
    validateFlags(source: string, start?: number, end?: number): void;
    /**
     * Validate a regular expression pattern. E.g. "abc"
     * @param source The source code to validate.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @param flags The flags.
     */
    validatePattern(
      source: string,
      start?: number,
      end?: number,
      flags?: {
        unicode?: boolean;
        unicodeSets?: boolean;
      }
    ): void;
    /**
     * @deprecated Backward compatibility
     * Use object `flags` instead of boolean `uFlag`.
     * @param source The source code to validate.
     * @param start The start index in the source code.
     * @param end The end index in the source code.
     * @param uFlag The flag to set unicode mode.
     */
    validatePattern(
      source: string,
      start?: number,
      end?: number,
      uFlag?: boolean
    ): void;
  }
}

declare module "@eslint-community/regexpp/visitor" {
  import type {
    Alternative,
    Assertion,
    Backreference,
    CapturingGroup,
    Character,
    CharacterClass,
    CharacterClassRange,
    CharacterSet,
    ClassIntersection,
    ClassStringDisjunction,
    ClassSubtraction,
    ExpressionCharacterClass,
    Flags,
    Group,
    ModifierFlags,
    Modifiers,
    Node,
    Pattern,
    Quantifier,
    RegExpLiteral,
    StringAlternative,
  } from "@eslint-community/regexpp/ast";
  /**
   * The visitor to walk on AST.
   */
  export class RegExpVisitor {
    /**
     * Initialize this visitor.
     * @param handlers Callbacks for each node.
     */
    constructor(handlers: RegExpVisitor.Handlers);
    /**
     * Visit a given node and descendant nodes.
     * @param node The root node to visit tree.
     */
    visit(node: Node): void;
  }
  export namespace RegExpVisitor {
    interface Handlers {
      onAlternativeEnter?: (node: Alternative) => void;
      onAlternativeLeave?: (node: Alternative) => void;
      onAssertionEnter?: (node: Assertion) => void;
      onAssertionLeave?: (node: Assertion) => void;
      onBackreferenceEnter?: (node: Backreference) => void;
      onBackreferenceLeave?: (node: Backreference) => void;
      onCapturingGroupEnter?: (node: CapturingGroup) => void;
      onCapturingGroupLeave?: (node: CapturingGroup) => void;
      onCharacterEnter?: (node: Character) => void;
      onCharacterLeave?: (node: Character) => void;
      onCharacterClassEnter?: (node: CharacterClass) => void;
      onCharacterClassLeave?: (node: CharacterClass) => void;
      onCharacterClassRangeEnter?: (node: CharacterClassRange) => void;
      onCharacterClassRangeLeave?: (node: CharacterClassRange) => void;
      onCharacterSetEnter?: (node: CharacterSet) => void;
      onCharacterSetLeave?: (node: CharacterSet) => void;
      onClassIntersectionEnter?: (node: ClassIntersection) => void;
      onClassIntersectionLeave?: (node: ClassIntersection) => void;
      onClassStringDisjunctionEnter?: (node: ClassStringDisjunction) => void;
      onClassStringDisjunctionLeave?: (node: ClassStringDisjunction) => void;
      onClassSubtractionEnter?: (node: ClassSubtraction) => void;
      onClassSubtractionLeave?: (node: ClassSubtraction) => void;
      onExpressionCharacterClassEnter?: (
        node: ExpressionCharacterClass
      ) => void;
      onExpressionCharacterClassLeave?: (
        node: ExpressionCharacterClass
      ) => void;
      onFlagsEnter?: (node: Flags) => void;
      onFlagsLeave?: (node: Flags) => void;
      onGroupEnter?: (node: Group) => void;
      onGroupLeave?: (node: Group) => void;
      onModifierFlagsEnter?: (node: ModifierFlags) => void;
      onModifierFlagsLeave?: (node: ModifierFlags) => void;
      onModifiersEnter?: (node: Modifiers) => void;
      onModifiersLeave?: (node: Modifiers) => void;
      onPatternEnter?: (node: Pattern) => void;
      onPatternLeave?: (node: Pattern) => void;
      onQuantifierEnter?: (node: Quantifier) => void;
      onQuantifierLeave?: (node: Quantifier) => void;
      onRegExpLiteralEnter?: (node: RegExpLiteral) => void;
      onRegExpLiteralLeave?: (node: RegExpLiteral) => void;
      onStringAlternativeEnter?: (node: StringAlternative) => void;
      onStringAlternativeLeave?: (node: StringAlternative) => void;
    }
  }
}

declare module "@eslint-community/regexpp/regexp-syntax-error" {
  import type { RegExpValidatorSourceContext } from "@eslint-community/regexpp/validator";
  export class RegExpSyntaxError extends SyntaxError {
    index: number;
    constructor(message: string, index: number);
  }
  export function newRegExpSyntaxError(
    srcCtx: RegExpValidatorSourceContext,
    flags: {
      unicode: boolean;
      unicodeSets: boolean;
    },
    index: number,
    message: string
  ): RegExpSyntaxError;
}

declare module "@eslint-community/regexpp/ecma-versions" {
  export type EcmaVersion =
    | 5
    | 2015
    | 2016
    | 2017
    | 2018
    | 2019
    | 2020
    | 2021
    | 2022
    | 2023
    | 2024
    | 2025;
  export const latestEcmaVersion = 2025;
}
