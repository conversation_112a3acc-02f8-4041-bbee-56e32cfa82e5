{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z\",\n  key: \"9m4mmf\"\n}], [\"path\", {\n  d: \"m2.5 21.5 1.4-1.4\",\n  key: \"17g3f0\"\n}], [\"path\", {\n  d: \"m20.1 3.9 1.4-1.4\",\n  key: \"1qn309\"\n}], [\"path\", {\n  d: \"M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z\",\n  key: \"1t2c92\"\n}], [\"path\", {\n  d: \"m9.6 14.4 4.8-4.8\",\n  key: \"6umqxw\"\n}]];\nconst Dumbbell = createLucideIcon(\"dumbbell\", __iconNode);\nexport { __iconNode, Dumbbell as default };\n//# sourceMappingURL=dumbbell.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}