{"ast": null, "code": "import React,{useState}from'react';import{useComments,formatCommentDate}from'../../hooks/useComments';import{Heart,MessageCircle,Shield,Trash2,Flag,AlertCircle,ArrowRight}from'lucide-react';import{shouldShowReplyButton,calculateIndentation,getDepthLimitMessage,getCommentDepthClasses,COMMENT_DEPTH_CONFIG}from'../../utils/commentDepth';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AdminCommentItem=_ref=>{let{comment,onReply,onLike,onUnlike,onDelete,onFlag,currentUserId,currentUserType,depth=0}=_ref;const[showReplyForm,setShowReplyForm]=useState(false);const[showActions,setShowActions]=useState(false);const[showDepthWarning,setShowDepthWarning]=useState(false);const hasUserReacted=comment.user_reaction!==undefined&&comment.user_reaction!==null;// Calculate depth-related properties\nconst canReply=shouldShowReplyButton(depth);const indentation=calculateIndentation(depth);const depthClasses=getCommentDepthClasses(depth);const depthLimitMessage=getDepthLimitMessage(depth);const isAtMaxDepth=depth>=COMMENT_DEPTH_CONFIG.MAX_DEPTH;const handleReactionToggle=()=>{if(hasUserReacted){onUnlike(comment.comment_id);}else{onLike(comment.comment_id);}};const handleDelete=()=>{if(window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')){onDelete(comment.comment_id);}};const handleReplyClick=()=>{if(isAtMaxDepth){setShowDepthWarning(true);setTimeout(()=>setShowDepthWarning(false),3000);}else{setShowReplyForm(true);}};const handleFlag=()=>{const reason=window.prompt('Please provide a reason for flagging this comment:');if(reason&&reason.trim()){onFlag(comment.comment_id,reason.trim());}};return/*#__PURE__*/_jsx(\"div\",{id:\"comment-\".concat(comment.comment_id),className:depthClasses.join(' '),style:{marginLeft:\"\".concat(indentation,\"px\"),marginBottom:'1rem',padding:'1rem',backgroundColor:depth>0?'#f9fafb':'white',borderRadius:'8px',border:'1px solid #e5e7eb',position:'relative'},onMouseEnter:()=>setShowActions(true),onMouseLeave:()=>setShowActions(false),children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'start',gap:'0.75rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{width:'2.5rem',height:'2.5rem',borderRadius:'50%',backgroundColor:comment.user_type==='admin'?'#3b82f6':'#22c55e',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'600',fontSize:'0.875rem',flexShrink:0,position:'relative'},children:[comment.author_name?comment.author_name.charAt(0).toUpperCase():'?',comment.user_type==='admin'&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',bottom:'-2px',right:'-2px',width:'1rem',height:'1rem',backgroundColor:'#facc15',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',border:'2px solid white'},children:/*#__PURE__*/_jsx(Shield,{size:8,color:\"white\"})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.5rem',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600',color:'#374151',fontSize:'0.875rem'},children:comment.author_name||'Anonymous'}),comment.user_type==='admin'&&/*#__PURE__*/_jsxs(\"span\",{style:{backgroundColor:'#3b82f6',color:'white',fontSize:'0.75rem',fontWeight:'500',padding:'0.125rem 0.5rem',borderRadius:'0.375rem',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Shield,{size:10}),\"Admin\"]}),comment.is_flagged&&/*#__PURE__*/_jsxs(\"span\",{style:{backgroundColor:'#ef4444',color:'white',fontSize:'0.75rem',fontWeight:'500',padding:'0.125rem 0.5rem',borderRadius:'0.375rem',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Flag,{size:10}),\"Flagged\"]}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#6b7280',fontSize:'0.75rem'},children:formatCommentDate(comment.created_at)})]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#374151',fontSize:'0.875rem',lineHeight:'1.5',marginBottom:'0.75rem',wordBreak:'break-word'},children:comment.comment_text}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.75rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleReactionToggle,style:{display:'flex',alignItems:'center',gap:'0.25rem',background:'none',border:'none',color:hasUserReacted?'#ef4444':'#6b7280',cursor:'pointer',padding:'0.25rem 0.5rem',borderRadius:'0.375rem',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.backgroundColor='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor='transparent';},children:[/*#__PURE__*/_jsx(Heart,{size:14,fill:hasUserReacted?'#ef4444':'none'}),/*#__PURE__*/_jsx(\"span\",{children:comment.reaction_count||0})]}),canReply?/*#__PURE__*/_jsxs(\"button\",{onClick:handleReplyClick,style:{display:'flex',alignItems:'center',gap:'0.25rem',background:'none',border:'none',color:'#6b7280',cursor:'pointer',padding:'0.25rem 0.5rem',borderRadius:'0.375rem',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.backgroundColor='#f3f4f6';e.currentTarget.style.color='#374151';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor='transparent';e.currentTarget.style.color='#6b7280';},children:[/*#__PURE__*/_jsx(MessageCircle,{size:14}),\"Reply\"]}):/*#__PURE__*/// Show depth limit message for max depth comments\n_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.75rem',color:'#f59e0b',fontStyle:'italic'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:12}),/*#__PURE__*/_jsx(\"span\",{children:\"Reply depth limit reached\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{// Scroll to top-level comment for continuing thread\nconst rootElement=document.getElementById(\"comment-\".concat(comment.comment_id));if(rootElement){rootElement.scrollIntoView({behavior:'smooth',block:'center'});}},style:{background:'none',border:'none',cursor:'pointer',color:'#3b82f6',fontSize:'0.75rem',textDecoration:'underline',padding:'0'},children:/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[\"Continue thread\",/*#__PURE__*/_jsx(ArrowRight,{size:10})]})})]}),currentUserType==='admin'&&showActions&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleFlag,style:{display:'flex',alignItems:'center',gap:'0.25rem',background:'none',border:'none',color:'#f59e0b',cursor:'pointer',padding:'0.25rem 0.5rem',borderRadius:'0.375rem',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.backgroundColor='#fef3c7';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor='transparent';},children:[/*#__PURE__*/_jsx(Flag,{size:14}),\"Flag\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleDelete,style:{display:'flex',alignItems:'center',gap:'0.25rem',background:'none',border:'none',color:'#ef4444',cursor:'pointer',padding:'0.25rem 0.5rem',borderRadius:'0.375rem',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.backgroundColor='#fef2f2';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor='transparent';},children:[/*#__PURE__*/_jsx(Trash2,{size:14}),\"Delete\"]})]})]}),showReplyForm&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'0.75rem'},children:/*#__PURE__*/_jsx(AdminCommentForm,{announcementId:comment.announcement_id,parentCommentId:comment.comment_id,onSubmit:()=>setShowReplyForm(false),onCancel:()=>setShowReplyForm(false),placeholder:\"Write a reply...\"})}),showDepthWarning&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'0.5rem',padding:'0.5rem',backgroundColor:'#fef3c7',border:'1px solid #f59e0b',borderRadius:'4px',fontSize:'0.75rem',color:'#92400e',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(AlertCircle,{size:14}),/*#__PURE__*/_jsx(\"span\",{children:depthLimitMessage})]}),comment.replies&&comment.replies.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem'},children:comment.replies.map(reply=>/*#__PURE__*/_jsx(AdminCommentItem,{comment:reply,onReply:onReply,onLike:onLike,onUnlike:onUnlike,onDelete:onDelete,onFlag:onFlag,currentUserId:currentUserId,currentUserType:currentUserType,depth:depth+1},reply.comment_id))})]})]})});};// Admin Comment Form Component will be added in the next chunk\nconst AdminCommentForm=_ref2=>{let{announcementId,parentCommentId,onSubmit,onCancel,placeholder=\"Write a comment...\"}=_ref2;const[commentText,setCommentText]=useState('');const[isAnonymous,setIsAnonymous]=useState(false);const[isSubmitting,setIsSubmitting]=useState(false);const{createComment}=useComments(announcementId,'admin');// Admin service\nconst handleSubmit=async e=>{e.preventDefault();if(!commentText.trim()||isSubmitting)return;try{setIsSubmitting(true);const commentData={announcement_id:announcementId,comment_text:commentText.trim(),parent_comment_id:parentCommentId,is_anonymous:isAnonymous};await createComment(commentData);setCommentText('');onSubmit();}catch(error){console.error('Error submitting comment:',error);}finally{setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,style:{padding:'1rem',backgroundColor:'#f8fafc',borderRadius:'8px',border:'1px solid #e2e8f0'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.75rem'},children:/*#__PURE__*/_jsx(\"textarea\",{value:commentText,onChange:e=>setCommentText(e.target.value),placeholder:placeholder,rows:3,style:{width:'100%',padding:'0.75rem',border:'1px solid #d1d5db',borderRadius:'6px',fontSize:'0.875rem',outline:'none',resize:'vertical',fontFamily:'inherit'},onFocus:e=>{e.currentTarget.style.borderColor='#3b82f6';e.currentTarget.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)';},onBlur:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='none';}})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',gap:'0.75rem'},children:[/*#__PURE__*/_jsxs(\"label\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.875rem',color:'#6b7280',cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isAnonymous,onChange:e=>setIsAnonymous(e.target.checked),style:{width:'1rem',height:'1rem',accentColor:'#3b82f6'}}),\"Post as Anonymous Admin\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem'},children:[onCancel&&/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,style:{padding:'0.5rem 1rem',border:'1px solid #d1d5db',borderRadius:'6px',backgroundColor:'white',color:'#6b7280',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.backgroundColor='#f9fafb';e.currentTarget.style.borderColor='#9ca3af';},onMouseLeave:e=>{e.currentTarget.style.backgroundColor='white';e.currentTarget.style.borderColor='#d1d5db';},children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:!commentText.trim()||isSubmitting,style:{padding:'0.5rem 1rem',border:'none',borderRadius:'6px',backgroundColor:!commentText.trim()||isSubmitting?'#9ca3af':'#3b82f6',color:'white',fontSize:'0.875rem',fontWeight:'500',cursor:!commentText.trim()||isSubmitting?'not-allowed':'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{if(!isSubmitting&&commentText.trim()){e.currentTarget.style.backgroundColor='#2563eb';}},onMouseLeave:e=>{if(!isSubmitting&&commentText.trim()){e.currentTarget.style.backgroundColor='#3b82f6';}},children:isSubmitting?'Posting...':'Post Comment'})]})]})]});};// Main AdminCommentSection Component\nconst AdminCommentSection=_ref3=>{let{announcementId,allowComments=true,currentUserId,currentUserType='admin'}=_ref3;const{comments,loading,error,refresh,likeComment,unlikeComment,deleteComment,flagComment}=useComments(announcementId,'admin');// Explicitly use admin service\nconst handleReply=parentId=>{// This could trigger a scroll to the reply form or other UI feedback\nconsole.log('Reply to comment:',parentId);};const handleDelete=async commentId=>{try{await deleteComment(commentId);await refresh();}catch(error){console.error('Error deleting comment:',error);}};const handleFlag=async(commentId,reason)=>{try{await flagComment(commentId,reason);await refresh();}catch(error){console.error('Error flagging comment:',error);}};if(!allowComments){return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem',textAlign:'center',color:'#6b7280',fontSize:'0.875rem',fontStyle:'italic',backgroundColor:'#f9fafb',borderRadius:'8px',border:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:20,style:{marginBottom:'0.5rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"div\",{children:\"Comments are disabled for this announcement.\"})]});}return/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(Shield,{size:20,color:\"#3b82f6\"}),/*#__PURE__*/_jsxs(\"h3\",{style:{fontSize:'1.125rem',fontWeight:'600',color:'#374151',margin:0},children:[\"Admin Comments (\",comments.length,\")\"]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.75rem',backgroundColor:'#fef2f2',border:'1px solid #fecaca',color:'#dc2626',borderRadius:'6px',marginBottom:'1rem',fontSize:'0.875rem',display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Flag,{size:16}),error]}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(AdminCommentForm,{announcementId:announcementId,onSubmit:refresh,placeholder:\"Share your admin insights...\"})}),loading?/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',padding:'2rem'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'1.5rem',height:'1.5rem',border:'2px solid #e5e7eb',borderTop:'2px solid #3b82f6',borderRadius:'50%',animation:'spin 1s linear infinite'}})}):comments.length===0?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#6b7280',fontSize:'0.875rem',backgroundColor:'#f8fafc',borderRadius:'8px',border:'1px solid #e2e8f0'},children:[/*#__PURE__*/_jsx(MessageCircle,{size:24,style:{marginBottom:'0.5rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"div\",{children:\"No comments yet. Be the first admin to share insights!\"})]}):/*#__PURE__*/_jsx(\"div\",{children:comments.map(comment=>/*#__PURE__*/_jsx(AdminCommentItem,{comment:comment,onReply:handleReply,onLike:likeComment,onUnlike:unlikeComment,onDelete:handleDelete,onFlag:handleFlag,currentUserId:currentUserId,currentUserType:currentUserType},comment.comment_id))})]});};export default AdminCommentSection;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}