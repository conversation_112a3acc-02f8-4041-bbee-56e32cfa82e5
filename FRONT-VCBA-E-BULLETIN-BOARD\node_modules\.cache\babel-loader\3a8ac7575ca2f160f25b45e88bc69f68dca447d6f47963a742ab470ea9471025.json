{"ast": null, "code": "// Central export for all services\nexport { default as AuthService } from './auth.service';\nexport { AdminAuthService } from './admin-auth.service';\nexport { httpClient, tokenManager, ApiError, setupResponseInterceptor } from './api.service';\nexport { studentService } from './studentService';\nexport { default as announcementService, adminAnnouncementService } from './announcementService';\nexport { default as commentService } from './commentService';\nexport { default as calendarService } from './calendarService';\nexport { default as notificationService, adminNotificationService } from './notificationService';", "map": {"version": 3, "names": ["default", "AuthService", "AdminAuthService", "httpClient", "tokenManager", "ApiError", "setupResponseInterceptor", "studentService", "announcementService", "adminAnnouncementService", "commentService", "calendarService", "notificationService", "adminNotificationService"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/index.ts"], "sourcesContent": ["// Central export for all services\nexport { default as AuthService } from './auth.service';\nexport { AdminAuthService } from './admin-auth.service';\nexport { httpClient, tokenManager, ApiError, setupResponseInterceptor } from './api.service';\nexport { studentService } from './studentService';\nexport { default as announcementService, adminAnnouncementService } from './announcementService';\nexport { default as commentService } from './commentService';\nexport { default as calendarService } from './calendarService';\nexport { default as notificationService, adminNotificationService } from './notificationService';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,WAAW,QAAQ,gBAAgB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,eAAe;AAC5F,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASP,OAAO,IAAIQ,mBAAmB,EAAEC,wBAAwB,QAAQ,uBAAuB;AAChG,SAAST,OAAO,IAAIU,cAAc,QAAQ,kBAAkB;AAC5D,SAASV,OAAO,IAAIW,eAAe,QAAQ,mBAAmB;AAC9D,SAASX,OAAO,IAAIY,mBAAmB,EAAEC,wBAAwB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}