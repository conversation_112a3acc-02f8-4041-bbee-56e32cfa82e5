{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"6\",\n  key: \"1vlfrh\"\n}]];\nconst CircleSmall = createLucideIcon(\"circle-small\", __iconNode);\nexport { __iconNode, CircleSmall as default };\n//# sourceMappingURL=circle-small.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}