{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"8\",\n  key: \"46899m\"\n}], [\"path\", {\n  d: \"M12 2v7.5\",\n  key: \"1e5rl5\"\n}], [\"path\", {\n  d: \"m19 5-5.23 5.23\",\n  key: \"1ezxxf\"\n}], [\"path\", {\n  d: \"M22 12h-7.5\",\n  key: \"le1719\"\n}], [\"path\", {\n  d: \"m19 19-5.23-5.23\",\n  key: \"p3fmgn\"\n}], [\"path\", {\n  d: \"M12 14.5V22\",\n  key: \"dgcmos\"\n}], [\"path\", {\n  d: \"M10.23 13.77 5 19\",\n  key: \"qwopd4\"\n}], [\"path\", {\n  d: \"M9.5 12H2\",\n  key: \"r7bup8\"\n}], [\"path\", {\n  d: \"M10.23 10.23 5 5\",\n  key: \"k2y7lj\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"ix0uyj\"\n}]];\nconst ShipWheel = createLucideIcon(\"ship-wheel\", __iconNode);\nexport { __iconNode, ShipWheel as default };\n//# sourceMappingURL=ship-wheel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}