{"ast": null, "code": "// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, ADMIN_USER_DATA_KEY, ADMIN_REFRESH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\n// Admin-specific token manager\nclass AdminTokenManager {\n  getToken() {\n    return localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n  setToken(token) {\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  }\n  removeToken() {\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      Authorization: `Bearer ${token}`\n    } : {};\n  }\n}\nconst adminTokenManager = new AdminTokenManager();\nexport class AdminAuthService {\n  /**\n   * Custom request method with admin token\n   */\n  static async request(method, endpoint, data, retryCount = 0) {\n    const token = adminTokenManager.getToken();\n    const headers = {};\n\n    // Only set Content-Type for non-FormData requests\n    if (!(data instanceof FormData)) {\n      headers['Content-Type'] = 'application/json';\n    }\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n      console.log('🔍 DEBUG - Authorization header set:', `Bearer ${token.substring(0, 20)}...`);\n    } else {\n      console.error('❌ DEBUG - No token found, request will fail with 401');\n    }\n    const config = {\n      method,\n      headers,\n      credentials: 'include'\n    };\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      // Handle FormData differently - don't stringify it\n      config.body = data instanceof FormData ? data : JSON.stringify(data);\n    }\n    const url = `${API_BASE_URL}${endpoint}`;\n    try {\n      console.log(`🔄 Admin API Request: ${method} ${url}`);\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        var _responseData$error;\n        if (response.status === 401) {\n          // Token expired or invalid - clear auth and redirect\n          adminTokenManager.removeToken();\n          throw new Error('Authentication required');\n        }\n        const responseData = await response.json().catch(() => ({\n          message: 'Request failed'\n        }));\n        const errorMessage = (responseData === null || responseData === void 0 ? void 0 : responseData.message) || (responseData === null || responseData === void 0 ? void 0 : (_responseData$error = responseData.error) === null || _responseData$error === void 0 ? void 0 : _responseData$error.message) || `HTTP ${response.status}`;\n        throw new Error(errorMessage);\n      }\n      const responseData = await response.json();\n      console.log(`✅ Admin API Success: ${method} ${endpoint}`);\n      return responseData;\n    } catch (error) {\n      console.error(`❌ Admin API request failed: ${method} ${endpoint}`, error);\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error instanceof TypeError || error instanceof Error && error.message.includes('Failed to fetch'))) {\n        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/3)...`);\n        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff\n        return this.request(method, endpoint, data, retryCount + 1);\n      }\n\n      // Enhanced error information for connection issues\n      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n        throw new Error(`❌ Network connection failed. Please check:\\n1. Backend server is running on ${API_BASE_URL}\\n2. CORS is properly configured\\n3. No firewall blocking the connection`);\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * Get current authenticated admin user\n   */\n  static async getCurrentUser() {\n    try {\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n      const response = await this.request('GET', API_ENDPOINTS.AUTH.PROFILE);\n      if (response && response.user) {\n        return response.user;\n      }\n      return null;\n    } catch (error) {\n      console.error('Failed to get current admin user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login admin user\n   */\n  static async login(credentials) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, {\n        ...credentials,\n        userType: 'admin'\n      });\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user;\n        const transformedUser = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          profilePicture: rawUser.profile_picture,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at\n        };\n\n        // Verify the user is actually an admin\n        if (transformedUser.role !== 'admin') {\n          throw new Error('Access denied: Admin privileges required');\n        }\n\n        // Store tokens and transformed user data in admin-specific keys\n        adminTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(ADMIN_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedUser));\n        return {\n          success: true,\n          message: response.message || 'Admin login successful',\n          data: {\n            ...response.data,\n            user: transformedUser\n          }\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Admin login failed');\n    } catch (error) {\n      console.error('AdminAuthService.login error:', error);\n      throw new Error(error.message || 'Admin login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.ADMIN_REGISTER, data);\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'Admin registration initiated successfully',\n          data: response.data\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Admin registration failed');\n    } catch (error) {\n      console.error('AdminAuthService.registerAdmin error:', error);\n      throw new Error(error.message || 'Admin registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.VERIFY_OTP, data);\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data\n        };\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'OTP verification failed');\n    } catch (error) {\n      console.error('AdminAuthService.verifyOtp error:', error);\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email) {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.RESEND_OTP, {\n        email\n      });\n      if (response && response.success) {\n        return response;\n      }\n      throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to resend OTP');\n    } catch (error) {\n      console.error('AdminAuthService.resendOtp error:', error);\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Logout admin user\n   */\n  static async logout() {\n    try {\n      console.log('🚪 AdminAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AdminAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ AdminAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 AdminAuthService - Clearing admin local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear admin local storage and tokens\n   */\n  static clearLocalStorage() {\n    console.log('🧹 AdminAuthService - Clearing admin authentication data');\n    adminTokenManager.removeToken();\n    localStorage.removeItem(ADMIN_USER_DATA_KEY);\n    localStorage.removeItem(ADMIN_REFRESH_TOKEN_KEY);\n    console.log('✅ AdminAuthService - Admin authentication data cleared');\n  }\n\n  /**\n   * Check if admin is authenticated\n   */\n  static isAuthenticated() {\n    const token = adminTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'admin');\n  }\n\n  /**\n   * Get stored admin user data\n   */\n  static getStoredUser() {\n    try {\n      const userData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'admin' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored admin user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get admin user role\n   */\n  static getUserRole() {\n    const user = this.getStoredUser();\n    return (user === null || user === void 0 ? void 0 : user.role) === 'admin' ? 'admin' : null;\n  }\n\n  /**\n   * HTTP GET method with admin authentication\n   */\n  static async get(endpoint) {\n    return this.request('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with admin authentication\n   */\n  static async post(endpoint, data) {\n    return this.request('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with admin authentication\n   */\n  static async put(endpoint, data) {\n    return this.request('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with admin authentication\n   */\n  static async delete(endpoint) {\n    return this.request('DELETE', endpoint);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  static async uploadProfilePicture(file) {\n    console.log('📤 AdminAuthService - Uploading profile picture:', file.name);\n    try {\n      const formData = new FormData();\n      formData.append('profilePicture', file);\n\n      // Use the existing request method which handles authentication properly\n      const result = await this.request('POST', '/api/admin/profile/picture', formData);\n      console.log('✅ AdminAuthService - Profile picture uploaded successfully');\n\n      // Transform the admin data\n      const rawAdmin = result.data.admin;\n      const transformedAdmin = {\n        id: rawAdmin.admin_id,\n        email: rawAdmin.email,\n        role: 'admin',\n        firstName: rawAdmin.first_name,\n        lastName: rawAdmin.last_name,\n        middleName: rawAdmin.middle_name,\n        suffix: rawAdmin.suffix,\n        phoneNumber: rawAdmin.phone_number,\n        department: rawAdmin.department,\n        position: rawAdmin.position,\n        grade_level: rawAdmin.grade_level,\n        profilePicture: rawAdmin.profile_picture,\n        isActive: Boolean(rawAdmin.is_active),\n        lastLogin: rawAdmin.last_login,\n        createdAt: rawAdmin.account_created_at,\n        updatedAt: rawAdmin.account_updated_at\n      };\n\n      // Update stored user data\n      localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n      return {\n        admin: transformedAdmin,\n        profilePicture: result.data.profilePicture\n      };\n    } catch (error) {\n      console.error('❌ AdminAuthService - Profile picture upload failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Remove profile picture\n   */\n  static async removeProfilePicture() {\n    console.log('🗑️ AdminAuthService - Removing profile picture');\n    const response = await this.request('DELETE', '/api/admin/profile/picture');\n    if (!response.success) {\n      var _response$error;\n      throw new Error(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Failed to remove profile picture');\n    }\n    console.log('✅ AdminAuthService - Profile picture removed successfully');\n\n    // Transform the admin data\n    const rawAdmin = response.data.admin;\n    const transformedAdmin = {\n      id: rawAdmin.admin_id,\n      email: rawAdmin.email,\n      role: 'admin',\n      firstName: rawAdmin.first_name,\n      lastName: rawAdmin.last_name,\n      middleName: rawAdmin.middle_name,\n      suffix: rawAdmin.suffix,\n      phoneNumber: rawAdmin.phone_number,\n      department: rawAdmin.department,\n      position: rawAdmin.position,\n      grade_level: rawAdmin.grade_level,\n      profilePicture: rawAdmin.profile_picture,\n      isActive: Boolean(rawAdmin.is_active),\n      lastLogin: rawAdmin.last_login,\n      createdAt: rawAdmin.account_created_at,\n      updatedAt: rawAdmin.account_updated_at\n    };\n\n    // Update stored user data\n    localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n    return transformedAdmin;\n  }\n}\nexport default AdminAuthService;", "map": {"version": 3, "names": ["API_ENDPOINTS", "ADMIN_USER_DATA_KEY", "ADMIN_REFRESH_TOKEN_KEY", "ADMIN_AUTH_TOKEN_KEY", "API_BASE_URL", "AdminTokenManager", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "removeToken", "removeItem", "getAuthHeaders", "Authorization", "adminTokenManager", "AdminAuthService", "request", "method", "endpoint", "data", "retryCount", "headers", "FormData", "console", "log", "substring", "error", "config", "credentials", "body", "JSON", "stringify", "url", "response", "fetch", "ok", "_responseData$error", "status", "Error", "responseData", "json", "catch", "message", "errorMessage", "TypeError", "includes", "Promise", "resolve", "setTimeout", "getCurrentUser", "AUTH", "PROFILE", "user", "login", "LOGIN", "userType", "success", "rawUser", "transformedUser", "id", "admin_id", "student_id", "email", "role", "firstName", "first_name", "lastName", "last_name", "middleName", "middle_name", "suffix", "phoneNumber", "phone_number", "phone", "department", "position", "grade_level", "studentNumber", "student_number", "profilePicture", "profile_picture", "isActive", "Boolean", "is_active", "lastLogin", "last_login", "createdAt", "account_created_at", "created_at", "updatedAt", "account_updated_at", "updated_at", "accessToken", "refreshToken", "registerAdmin", "ADMIN_REGISTER", "verifyOtp", "VERIFY_OTP", "resendOtp", "RESEND_OTP", "logout", "LOGOUT", "warn", "clearLocalStorage", "isAuthenticated", "userData", "getStoredUser", "parse", "getUserRole", "get", "post", "put", "delete", "uploadProfilePicture", "file", "name", "formData", "append", "result", "rawAdmin", "admin", "transformedAdmin", "removeProfilePicture", "_response$error"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/admin-auth.service.ts"], "sourcesContent": ["// import { httpClient } from './api.service'; // Not needed - using custom fetch\nimport { API_ENDPOINTS, ADMIN_USER_DATA_KEY, ADMIN_REFRESH_TOKEN_KEY, ADMIN_AUTH_TOKEN_KEY, API_BASE_URL } from '../config/constants';\nimport {\n  LoginCredentials,\n  AdminRegistrationData,\n  OtpVerificationData,\n  AuthResponse,\n  RegistrationResponse,\n  OtpVerificationResponse,\n  User,\n  ApiResponse,\n} from '../types';\n\n// Admin-specific token manager\nclass AdminTokenManager {\n  getToken(): string | null {\n    return localStorage.getItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n\n  setToken(token: string): void {\n    localStorage.setItem(ADMIN_AUTH_TOKEN_KEY, token);\n  }\n\n  removeToken(): void {\n    localStorage.removeItem(ADMIN_AUTH_TOKEN_KEY);\n  }\n\n  getAuthHeaders(): Record<string, string> {\n    const token = this.getToken();\n    return token ? { Authorization: `Bearer ${token}` } : {};\n  }\n}\n\nconst adminTokenManager = new AdminTokenManager();\n\nexport class AdminAuthService {\n  /**\n   * Custom request method with admin token\n   */\n  public static async request<T>(\n    method: string,\n    endpoint: string,\n    data?: any,\n    retryCount: number = 0\n  ): Promise<any> {\n    const token = adminTokenManager.getToken();\n    const headers: Record<string, string> = {};\n\n    // Only set Content-Type for non-FormData requests\n    if (!(data instanceof FormData)) {\n      headers['Content-Type'] = 'application/json';\n    }\n\n    if (token) {\n      headers['Authorization'] = `Bearer ${token}`;\n      console.log('🔍 DEBUG - Authorization header set:', `Bearer ${token.substring(0, 20)}...`);\n    } else {\n      console.error('❌ DEBUG - No token found, request will fail with 401');\n    }\n\n    const config: RequestInit = {\n      method,\n      headers,\n      credentials: 'include',\n    };\n\n    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {\n      // Handle FormData differently - don't stringify it\n      config.body = data instanceof FormData ? data : JSON.stringify(data);\n    }\n\n    const url = `${API_BASE_URL}${endpoint}`;\n\n    try {\n      console.log(`🔄 Admin API Request: ${method} ${url}`);\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Token expired or invalid - clear auth and redirect\n          adminTokenManager.removeToken();\n          throw new Error('Authentication required');\n        }\n\n        const responseData = await response.json().catch(() => ({ message: 'Request failed' }));\n        const errorMessage = responseData?.message || responseData?.error?.message || `HTTP ${response.status}`;\n        throw new Error(errorMessage);\n      }\n\n      const responseData = await response.json();\n      console.log(`✅ Admin API Success: ${method} ${endpoint}`);\n      return responseData;\n    } catch (error) {\n      console.error(`❌ Admin API request failed: ${method} ${endpoint}`, error);\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error instanceof TypeError || (error instanceof Error && error.message.includes('Failed to fetch')))) {\n        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/3)...`);\n        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff\n        return this.request<T>(method, endpoint, data, retryCount + 1);\n      }\n\n      // Enhanced error information for connection issues\n      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n        throw new Error(`❌ Network connection failed. Please check:\\n1. Backend server is running on ${API_BASE_URL}\\n2. CORS is properly configured\\n3. No firewall blocking the connection`);\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Get current authenticated admin user\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = adminTokenManager.getToken();\n      if (!token) {\n        return null;\n      }\n\n      const response = await this.request<{ user: User }>('GET', API_ENDPOINTS.AUTH.PROFILE);\n\n      if (response && response.user) {\n        return response.user;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Failed to get current admin user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Login admin user\n   */\n  static async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.LOGIN, { ...credentials, userType: 'admin' });\n\n      if (response && response.success && response.data) {\n        // Transform raw database user data to frontend format\n        const rawUser = response.data.user as any;\n        const transformedUser: User = {\n          id: rawUser.admin_id || rawUser.student_id,\n          email: rawUser.email,\n          role: rawUser.admin_id ? 'admin' : 'student',\n          firstName: rawUser.first_name,\n          lastName: rawUser.last_name,\n          middleName: rawUser.middle_name,\n          suffix: rawUser.suffix,\n          phoneNumber: rawUser.phone_number || rawUser.phone,\n          department: rawUser.department,\n          position: rawUser.position,\n          grade_level: rawUser.grade_level,\n          studentNumber: rawUser.student_number,\n          profilePicture: rawUser.profile_picture,\n          isActive: Boolean(rawUser.is_active),\n          lastLogin: rawUser.last_login,\n          createdAt: rawUser.account_created_at || rawUser.created_at,\n          updatedAt: rawUser.account_updated_at || rawUser.updated_at,\n        };\n\n        // Verify the user is actually an admin\n        if (transformedUser.role !== 'admin') {\n          throw new Error('Access denied: Admin privileges required');\n        }\n\n        // Store tokens and transformed user data in admin-specific keys\n        adminTokenManager.setToken(response.data.accessToken);\n        if (response.data.refreshToken) {\n          localStorage.setItem(ADMIN_REFRESH_TOKEN_KEY, response.data.refreshToken);\n        }\n        localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedUser));\n\n        return {\n          success: true,\n          message: response.message || 'Admin login successful',\n          data: {\n            ...response.data,\n            user: transformedUser,\n          },\n        };\n      }\n\n      throw new Error(response?.message || 'Admin login failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.login error:', error);\n      throw new Error(error.message || 'Admin login failed');\n    }\n  }\n\n  /**\n   * Register admin user\n   */\n  static async registerAdmin(data: AdminRegistrationData): Promise<RegistrationResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.ADMIN_REGISTER, data);\n\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'Admin registration initiated successfully',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response?.message || 'Admin registration failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.registerAdmin error:', error);\n      throw new Error(error.message || 'Admin registration failed');\n    }\n  }\n\n  /**\n   * Verify OTP for admin registration\n   */\n  static async verifyOtp(data: OtpVerificationData): Promise<OtpVerificationResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.VERIFY_OTP, data);\n\n      if (response && response.success) {\n        return {\n          success: true,\n          message: response.message || 'OTP verification successful',\n          data: response.data,\n        };\n      }\n\n      throw new Error(response?.message || 'OTP verification failed');\n    } catch (error: any) {\n      console.error('AdminAuthService.verifyOtp error:', error);\n      throw new Error(error.message || 'OTP verification failed');\n    }\n  }\n\n  /**\n   * Resend OTP for admin registration\n   */\n  static async resendOtp(email: string): Promise<ApiResponse> {\n    try {\n      const response = await this.request('POST', API_ENDPOINTS.AUTH.RESEND_OTP, { email });\n\n      if (response && response.success) {\n        return response;\n      }\n\n      throw new Error(response?.message || 'Failed to resend OTP');\n    } catch (error: any) {\n      console.error('AdminAuthService.resendOtp error:', error);\n      throw new Error(error.message || 'Failed to resend OTP');\n    }\n  }\n\n  /**\n   * Logout admin user\n   */\n  static async logout(): Promise<void> {\n    try {\n      console.log('🚪 AdminAuthService - Calling server logout endpoint');\n      await this.request('POST', API_ENDPOINTS.AUTH.LOGOUT);\n      console.log('✅ AdminAuthService - Server logout successful');\n    } catch (error) {\n      console.warn('⚠️ AdminAuthService - Server logout failed, continuing with local logout:', error);\n    } finally {\n      console.log('🧹 AdminAuthService - Clearing admin local storage');\n      this.clearLocalStorage();\n    }\n  }\n\n  /**\n   * Clear admin local storage and tokens\n   */\n  static clearLocalStorage(): void {\n    console.log('🧹 AdminAuthService - Clearing admin authentication data');\n    adminTokenManager.removeToken();\n    localStorage.removeItem(ADMIN_USER_DATA_KEY);\n    localStorage.removeItem(ADMIN_REFRESH_TOKEN_KEY);\n    console.log('✅ AdminAuthService - Admin authentication data cleared');\n  }\n\n  /**\n   * Check if admin is authenticated\n   */\n  static isAuthenticated(): boolean {\n    const token = adminTokenManager.getToken();\n    const userData = this.getStoredUser();\n    return !!(token && userData && userData.role === 'admin');\n  }\n\n  /**\n   * Get stored admin user data\n   */\n  static getStoredUser(): User | null {\n    try {\n      const userData = localStorage.getItem(ADMIN_USER_DATA_KEY);\n      const user = userData ? JSON.parse(userData) : null;\n      return user && user.role === 'admin' ? user : null;\n    } catch (error) {\n      console.error('Error parsing stored admin user data:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get admin user role\n   */\n  static getUserRole(): 'admin' | null {\n    const user = this.getStoredUser();\n    return user?.role === 'admin' ? 'admin' : null;\n  }\n\n  /**\n   * HTTP GET method with admin authentication\n   */\n  static async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('GET', endpoint);\n  }\n\n  /**\n   * HTTP POST method with admin authentication\n   */\n  static async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('POST', endpoint, data);\n  }\n\n  /**\n   * HTTP PUT method with admin authentication\n   */\n  static async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('PUT', endpoint, data);\n  }\n\n  /**\n   * HTTP DELETE method with admin authentication\n   */\n  static async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<ApiResponse<T>>('DELETE', endpoint);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<{ admin: User; profilePicture: string }> {\n    console.log('📤 AdminAuthService - Uploading profile picture:', file.name);\n\n    try {\n      const formData = new FormData();\n      formData.append('profilePicture', file);\n\n      // Use the existing request method which handles authentication properly\n      const result = await this.request('POST', '/api/admin/profile/picture', formData);\n\n      console.log('✅ AdminAuthService - Profile picture uploaded successfully');\n\n      // Transform the admin data\n      const rawAdmin = result.data.admin;\n      const transformedAdmin: User = {\n        id: rawAdmin.admin_id,\n        email: rawAdmin.email,\n        role: 'admin',\n        firstName: rawAdmin.first_name,\n        lastName: rawAdmin.last_name,\n        middleName: rawAdmin.middle_name,\n        suffix: rawAdmin.suffix,\n        phoneNumber: rawAdmin.phone_number,\n        department: rawAdmin.department,\n        position: rawAdmin.position,\n        grade_level: rawAdmin.grade_level,\n        profilePicture: rawAdmin.profile_picture,\n        isActive: Boolean(rawAdmin.is_active),\n        lastLogin: rawAdmin.last_login,\n        createdAt: rawAdmin.account_created_at,\n        updatedAt: rawAdmin.account_updated_at,\n      };\n\n      // Update stored user data\n      localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n\n      return {\n        admin: transformedAdmin,\n        profilePicture: result.data.profilePicture\n      };\n    } catch (error: any) {\n      console.error('❌ AdminAuthService - Profile picture upload failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Remove profile picture\n   */\n  static async removeProfilePicture(): Promise<User> {\n    console.log('🗑️ AdminAuthService - Removing profile picture');\n\n    const response = await this.request('DELETE', '/api/admin/profile/picture');\n\n    if (!response.success) {\n      throw new Error(response.error?.message || 'Failed to remove profile picture');\n    }\n\n    console.log('✅ AdminAuthService - Profile picture removed successfully');\n\n    // Transform the admin data\n    const rawAdmin = response.data.admin;\n    const transformedAdmin: User = {\n      id: rawAdmin.admin_id,\n      email: rawAdmin.email,\n      role: 'admin',\n      firstName: rawAdmin.first_name,\n      lastName: rawAdmin.last_name,\n      middleName: rawAdmin.middle_name,\n      suffix: rawAdmin.suffix,\n      phoneNumber: rawAdmin.phone_number,\n      department: rawAdmin.department,\n      position: rawAdmin.position,\n      grade_level: rawAdmin.grade_level,\n      profilePicture: rawAdmin.profile_picture,\n      isActive: Boolean(rawAdmin.is_active),\n      lastLogin: rawAdmin.last_login,\n      createdAt: rawAdmin.account_created_at,\n      updatedAt: rawAdmin.account_updated_at,\n    };\n\n    // Update stored user data\n    localStorage.setItem(ADMIN_USER_DATA_KEY, JSON.stringify(transformedAdmin));\n\n    return transformedAdmin;\n  }\n}\n\nexport default AdminAuthService;\n"], "mappings": "AAAA;AACA,SAASA,aAAa,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,YAAY,QAAQ,qBAAqB;AAYrI;AACA,MAAMC,iBAAiB,CAAC;EACtBC,QAAQA,CAAA,EAAkB;IACxB,OAAOC,YAAY,CAACC,OAAO,CAACL,oBAAoB,CAAC;EACnD;EAEAM,QAAQA,CAACC,KAAa,EAAQ;IAC5BH,YAAY,CAACI,OAAO,CAACR,oBAAoB,EAAEO,KAAK,CAAC;EACnD;EAEAE,WAAWA,CAAA,EAAS;IAClBL,YAAY,CAACM,UAAU,CAACV,oBAAoB,CAAC;EAC/C;EAEAW,cAAcA,CAAA,EAA2B;IACvC,MAAMJ,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;IAC7B,OAAOI,KAAK,GAAG;MAAEK,aAAa,EAAE,UAAUL,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;AACF;AAEA,MAAMM,iBAAiB,GAAG,IAAIX,iBAAiB,CAAC,CAAC;AAEjD,OAAO,MAAMY,gBAAgB,CAAC;EAC5B;AACF;AACA;EACE,aAAoBC,OAAOA,CACzBC,MAAc,EACdC,QAAgB,EAChBC,IAAU,EACVC,UAAkB,GAAG,CAAC,EACR;IACd,MAAMZ,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;IAC1C,MAAMiB,OAA+B,GAAG,CAAC,CAAC;;IAE1C;IACA,IAAI,EAAEF,IAAI,YAAYG,QAAQ,CAAC,EAAE;MAC/BD,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;IAC9C;IAEA,IAAIb,KAAK,EAAE;MACTa,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUb,KAAK,EAAE;MAC5Ce,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,UAAUhB,KAAK,CAACiB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IAC5F,CAAC,MAAM;MACLF,OAAO,CAACG,KAAK,CAAC,sDAAsD,CAAC;IACvE;IAEA,MAAMC,MAAmB,GAAG;MAC1BV,MAAM;MACNI,OAAO;MACPO,WAAW,EAAE;IACf,CAAC;IAED,IAAIT,IAAI,KAAKF,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,OAAO,CAAC,EAAE;MACzE;MACAU,MAAM,CAACE,IAAI,GAAGV,IAAI,YAAYG,QAAQ,GAAGH,IAAI,GAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC;IACtE;IAEA,MAAMa,GAAG,GAAG,GAAG9B,YAAY,GAAGgB,QAAQ,EAAE;IAExC,IAAI;MACFK,OAAO,CAACC,GAAG,CAAC,yBAAyBP,MAAM,IAAIe,GAAG,EAAE,CAAC;MACrD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,EAAEL,MAAM,CAAC;MAEzC,IAAI,CAACM,QAAQ,CAACE,EAAE,EAAE;QAAA,IAAAC,mBAAA;QAChB,IAAIH,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UAC3B;UACAvB,iBAAiB,CAACJ,WAAW,CAAC,CAAC;UAC/B,MAAM,IAAI4B,KAAK,CAAC,yBAAyB,CAAC;QAC5C;QAEA,MAAMC,YAAY,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO;UAAEC,OAAO,EAAE;QAAiB,CAAC,CAAC,CAAC;QACvF,MAAMC,YAAY,GAAG,CAAAJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,OAAO,MAAIH,YAAY,aAAZA,YAAY,wBAAAH,mBAAA,GAAZG,YAAY,CAAEb,KAAK,cAAAU,mBAAA,uBAAnBA,mBAAA,CAAqBM,OAAO,KAAI,QAAQT,QAAQ,CAACI,MAAM,EAAE;QACvG,MAAM,IAAIC,KAAK,CAACK,YAAY,CAAC;MAC/B;MAEA,MAAMJ,YAAY,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAC1CjB,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,IAAIC,QAAQ,EAAE,CAAC;MACzD,OAAOqB,YAAY;IACrB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+BT,MAAM,IAAIC,QAAQ,EAAE,EAAEQ,KAAK,CAAC;;MAEzE;MACA,IAAIN,UAAU,GAAG,CAAC,KAAKM,KAAK,YAAYkB,SAAS,IAAKlB,KAAK,YAAYY,KAAK,IAAIZ,KAAK,CAACgB,OAAO,CAACG,QAAQ,CAAC,iBAAiB,CAAE,CAAC,EAAE;QAC3HtB,OAAO,CAACC,GAAG,CAAC,gCAAgCJ,UAAU,GAAG,CAAC,QAAQ,CAAC;QACnE,MAAM,IAAI0B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,IAAI3B,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,OAAO,IAAI,CAACJ,OAAO,CAAIC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC;MAChE;;MAEA;MACA,IAAIM,KAAK,YAAYkB,SAAS,IAAIlB,KAAK,CAACgB,OAAO,CAACG,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC3E,MAAM,IAAIP,KAAK,CAAC,+EAA+EpC,YAAY,0EAA0E,CAAC;MACxL;MAEA,MAAMwB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAauB,cAAcA,CAAA,EAAyB;IAClD,IAAI;MACF,MAAMzC,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;MAC1C,IAAI,CAACI,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MAEA,MAAMyB,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAiB,KAAK,EAAElB,aAAa,CAACoD,IAAI,CAACC,OAAO,CAAC;MAEtF,IAAIlB,QAAQ,IAAIA,QAAQ,CAACmB,IAAI,EAAE;QAC7B,OAAOnB,QAAQ,CAACmB,IAAI;MACtB;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAa2B,KAAKA,CAACzB,WAA6B,EAAyB;IACvE,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACoD,IAAI,CAACI,KAAK,EAAE;QAAE,GAAG1B,WAAW;QAAE2B,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAE5G,IAAItB,QAAQ,IAAIA,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACd,IAAI,EAAE;QACjD;QACA,MAAMsC,OAAO,GAAGxB,QAAQ,CAACd,IAAI,CAACiC,IAAW;QACzC,MAAMM,eAAqB,GAAG;UAC5BC,EAAE,EAAEF,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACI,UAAU;UAC1CC,KAAK,EAAEL,OAAO,CAACK,KAAK;UACpBC,IAAI,EAAEN,OAAO,CAACG,QAAQ,GAAG,OAAO,GAAG,SAAS;UAC5CI,SAAS,EAAEP,OAAO,CAACQ,UAAU;UAC7BC,QAAQ,EAAET,OAAO,CAACU,SAAS;UAC3BC,UAAU,EAAEX,OAAO,CAACY,WAAW;UAC/BC,MAAM,EAAEb,OAAO,CAACa,MAAM;UACtBC,WAAW,EAAEd,OAAO,CAACe,YAAY,IAAIf,OAAO,CAACgB,KAAK;UAClDC,UAAU,EAAEjB,OAAO,CAACiB,UAAU;UAC9BC,QAAQ,EAAElB,OAAO,CAACkB,QAAQ;UAC1BC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;UAChCC,aAAa,EAAEpB,OAAO,CAACqB,cAAc;UACrCC,cAAc,EAAEtB,OAAO,CAACuB,eAAe;UACvCC,QAAQ,EAAEC,OAAO,CAACzB,OAAO,CAAC0B,SAAS,CAAC;UACpCC,SAAS,EAAE3B,OAAO,CAAC4B,UAAU;UAC7BC,SAAS,EAAE7B,OAAO,CAAC8B,kBAAkB,IAAI9B,OAAO,CAAC+B,UAAU;UAC3DC,SAAS,EAAEhC,OAAO,CAACiC,kBAAkB,IAAIjC,OAAO,CAACkC;QACnD,CAAC;;QAED;QACA,IAAIjC,eAAe,CAACK,IAAI,KAAK,OAAO,EAAE;UACpC,MAAM,IAAIzB,KAAK,CAAC,0CAA0C,CAAC;QAC7D;;QAEA;QACAxB,iBAAiB,CAACP,QAAQ,CAAC0B,QAAQ,CAACd,IAAI,CAACyE,WAAW,CAAC;QACrD,IAAI3D,QAAQ,CAACd,IAAI,CAAC0E,YAAY,EAAE;UAC9BxF,YAAY,CAACI,OAAO,CAACT,uBAAuB,EAAEiC,QAAQ,CAACd,IAAI,CAAC0E,YAAY,CAAC;QAC3E;QACAxF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE+B,IAAI,CAACC,SAAS,CAAC2B,eAAe,CAAC,CAAC;QAE1E,OAAO;UACLF,OAAO,EAAE,IAAI;UACbd,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,wBAAwB;UACrDvB,IAAI,EAAE;YACJ,GAAGc,QAAQ,CAACd,IAAI;YAChBiC,IAAI,EAAEM;UACR;QACF,CAAC;MACH;MAEA,MAAM,IAAIpB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,oBAAoB,CAAC;IAC5D,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAIY,KAAK,CAACZ,KAAK,CAACgB,OAAO,IAAI,oBAAoB,CAAC;IACxD;EACF;;EAEA;AACF;AACA;EACE,aAAaoD,aAAaA,CAAC3E,IAA2B,EAAiC;IACrF,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACoD,IAAI,CAAC6C,cAAc,EAAE5E,IAAI,CAAC;MAEpF,IAAIc,QAAQ,IAAIA,QAAQ,CAACuB,OAAO,EAAE;QAChC,OAAO;UACLA,OAAO,EAAE,IAAI;UACbd,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,2CAA2C;UACxEvB,IAAI,EAAEc,QAAQ,CAACd;QACjB,CAAC;MACH;MAEA,MAAM,IAAImB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,2BAA2B,CAAC;IACnE,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBH,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAM,IAAIY,KAAK,CAACZ,KAAK,CAACgB,OAAO,IAAI,2BAA2B,CAAC;IAC/D;EACF;;EAEA;AACF;AACA;EACE,aAAasD,SAASA,CAAC7E,IAAyB,EAAoC;IAClF,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACoD,IAAI,CAAC+C,UAAU,EAAE9E,IAAI,CAAC;MAEhF,IAAIc,QAAQ,IAAIA,QAAQ,CAACuB,OAAO,EAAE;QAChC,OAAO;UACLA,OAAO,EAAE,IAAI;UACbd,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI,6BAA6B;UAC1DvB,IAAI,EAAEc,QAAQ,CAACd;QACjB,CAAC;MACH;MAEA,MAAM,IAAImB,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,yBAAyB,CAAC;IACjE,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAIY,KAAK,CAACZ,KAAK,CAACgB,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF;;EAEA;AACF;AACA;EACE,aAAawD,SAASA,CAACpC,KAAa,EAAwB;IAC1D,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACoD,IAAI,CAACiD,UAAU,EAAE;QAAErC;MAAM,CAAC,CAAC;MAErF,IAAI7B,QAAQ,IAAIA,QAAQ,CAACuB,OAAO,EAAE;QAChC,OAAOvB,QAAQ;MACjB;MAEA,MAAM,IAAIK,KAAK,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,sBAAsB,CAAC;IAC9D,CAAC,CAAC,OAAOhB,KAAU,EAAE;MACnBH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAIY,KAAK,CAACZ,KAAK,CAACgB,OAAO,IAAI,sBAAsB,CAAC;IAC1D;EACF;;EAEA;AACF;AACA;EACE,aAAa0D,MAAMA,CAAA,EAAkB;IACnC,IAAI;MACF7E,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,MAAM,IAAI,CAACR,OAAO,CAAC,MAAM,EAAElB,aAAa,CAACoD,IAAI,CAACmD,MAAM,CAAC;MACrD9E,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdH,OAAO,CAAC+E,IAAI,CAAC,2EAA2E,EAAE5E,KAAK,CAAC;IAClG,CAAC,SAAS;MACRH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAAC+E,iBAAiB,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;EACE,OAAOA,iBAAiBA,CAAA,EAAS;IAC/BhF,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvEV,iBAAiB,CAACJ,WAAW,CAAC,CAAC;IAC/BL,YAAY,CAACM,UAAU,CAACZ,mBAAmB,CAAC;IAC5CM,YAAY,CAACM,UAAU,CAACX,uBAAuB,CAAC;IAChDuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EACvE;;EAEA;AACF;AACA;EACE,OAAOgF,eAAeA,CAAA,EAAY;IAChC,MAAMhG,KAAK,GAAGM,iBAAiB,CAACV,QAAQ,CAAC,CAAC;IAC1C,MAAMqG,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,OAAO,CAAC,EAAElG,KAAK,IAAIiG,QAAQ,IAAIA,QAAQ,CAAC1C,IAAI,KAAK,OAAO,CAAC;EAC3D;;EAEA;AACF;AACA;EACE,OAAO2C,aAAaA,CAAA,EAAgB;IAClC,IAAI;MACF,MAAMD,QAAQ,GAAGpG,YAAY,CAACC,OAAO,CAACP,mBAAmB,CAAC;MAC1D,MAAMqD,IAAI,GAAGqD,QAAQ,GAAG3E,IAAI,CAAC6E,KAAK,CAACF,QAAQ,CAAC,GAAG,IAAI;MACnD,OAAOrD,IAAI,IAAIA,IAAI,CAACW,IAAI,KAAK,OAAO,GAAGX,IAAI,GAAG,IAAI;IACpD,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE,OAAOkF,WAAWA,CAAA,EAAmB;IACnC,MAAMxD,IAAI,GAAG,IAAI,CAACsD,aAAa,CAAC,CAAC;IACjC,OAAO,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,IAAI,MAAK,OAAO,GAAG,OAAO,GAAG,IAAI;EAChD;;EAEA;AACF;AACA;EACE,aAAa8C,GAAGA,CAAI3F,QAAgB,EAA2B;IAC7D,OAAO,IAAI,CAACF,OAAO,CAAiB,KAAK,EAAEE,QAAQ,CAAC;EACtD;;EAEA;AACF;AACA;EACE,aAAa4F,IAAIA,CAAI5F,QAAgB,EAAEC,IAAU,EAA2B;IAC1E,OAAO,IAAI,CAACH,OAAO,CAAiB,MAAM,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,aAAa4F,GAAGA,CAAI7F,QAAgB,EAAEC,IAAU,EAA2B;IACzE,OAAO,IAAI,CAACH,OAAO,CAAiB,KAAK,EAAEE,QAAQ,EAAEC,IAAI,CAAC;EAC5D;;EAEA;AACF;AACA;EACE,aAAa6F,MAAMA,CAAI9F,QAAgB,EAA2B;IAChE,OAAO,IAAI,CAACF,OAAO,CAAiB,QAAQ,EAAEE,QAAQ,CAAC;EACzD;;EAEA;AACF;AACA;EACE,aAAa+F,oBAAoBA,CAACC,IAAU,EAAoD;IAC9F3F,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE0F,IAAI,CAACC,IAAI,CAAC;IAE1E,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAI9F,QAAQ,CAAC,CAAC;MAC/B8F,QAAQ,CAACC,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;;MAEvC;MACA,MAAMI,MAAM,GAAG,MAAM,IAAI,CAACtG,OAAO,CAAC,MAAM,EAAE,4BAA4B,EAAEoG,QAAQ,CAAC;MAEjF7F,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;MAEzE;MACA,MAAM+F,QAAQ,GAAGD,MAAM,CAACnG,IAAI,CAACqG,KAAK;MAClC,MAAMC,gBAAsB,GAAG;QAC7B9D,EAAE,EAAE4D,QAAQ,CAAC3D,QAAQ;QACrBE,KAAK,EAAEyD,QAAQ,CAACzD,KAAK;QACrBC,IAAI,EAAE,OAAO;QACbC,SAAS,EAAEuD,QAAQ,CAACtD,UAAU;QAC9BC,QAAQ,EAAEqD,QAAQ,CAACpD,SAAS;QAC5BC,UAAU,EAAEmD,QAAQ,CAAClD,WAAW;QAChCC,MAAM,EAAEiD,QAAQ,CAACjD,MAAM;QACvBC,WAAW,EAAEgD,QAAQ,CAAC/C,YAAY;QAClCE,UAAU,EAAE6C,QAAQ,CAAC7C,UAAU;QAC/BC,QAAQ,EAAE4C,QAAQ,CAAC5C,QAAQ;QAC3BC,WAAW,EAAE2C,QAAQ,CAAC3C,WAAW;QACjCG,cAAc,EAAEwC,QAAQ,CAACvC,eAAe;QACxCC,QAAQ,EAAEC,OAAO,CAACqC,QAAQ,CAACpC,SAAS,CAAC;QACrCC,SAAS,EAAEmC,QAAQ,CAAClC,UAAU;QAC9BC,SAAS,EAAEiC,QAAQ,CAAChC,kBAAkB;QACtCE,SAAS,EAAE8B,QAAQ,CAAC7B;MACtB,CAAC;;MAED;MACArF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE+B,IAAI,CAACC,SAAS,CAAC0F,gBAAgB,CAAC,CAAC;MAE3E,OAAO;QACLD,KAAK,EAAEC,gBAAgB;QACvB1C,cAAc,EAAEuC,MAAM,CAACnG,IAAI,CAAC4D;MAC9B,CAAC;IACH,CAAC,CAAC,OAAOrD,KAAU,EAAE;MACnBH,OAAO,CAACG,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,aAAagG,oBAAoBA,CAAA,EAAkB;IACjDnG,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAE9D,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAACjB,OAAO,CAAC,QAAQ,EAAE,4BAA4B,CAAC;IAE3E,IAAI,CAACiB,QAAQ,CAACuB,OAAO,EAAE;MAAA,IAAAmE,eAAA;MACrB,MAAM,IAAIrF,KAAK,CAAC,EAAAqF,eAAA,GAAA1F,QAAQ,CAACP,KAAK,cAAAiG,eAAA,uBAAdA,eAAA,CAAgBjF,OAAO,KAAI,kCAAkC,CAAC;IAChF;IAEAnB,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;IAExE;IACA,MAAM+F,QAAQ,GAAGtF,QAAQ,CAACd,IAAI,CAACqG,KAAK;IACpC,MAAMC,gBAAsB,GAAG;MAC7B9D,EAAE,EAAE4D,QAAQ,CAAC3D,QAAQ;MACrBE,KAAK,EAAEyD,QAAQ,CAACzD,KAAK;MACrBC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAEuD,QAAQ,CAACtD,UAAU;MAC9BC,QAAQ,EAAEqD,QAAQ,CAACpD,SAAS;MAC5BC,UAAU,EAAEmD,QAAQ,CAAClD,WAAW;MAChCC,MAAM,EAAEiD,QAAQ,CAACjD,MAAM;MACvBC,WAAW,EAAEgD,QAAQ,CAAC/C,YAAY;MAClCE,UAAU,EAAE6C,QAAQ,CAAC7C,UAAU;MAC/BC,QAAQ,EAAE4C,QAAQ,CAAC5C,QAAQ;MAC3BC,WAAW,EAAE2C,QAAQ,CAAC3C,WAAW;MACjCG,cAAc,EAAEwC,QAAQ,CAACvC,eAAe;MACxCC,QAAQ,EAAEC,OAAO,CAACqC,QAAQ,CAACpC,SAAS,CAAC;MACrCC,SAAS,EAAEmC,QAAQ,CAAClC,UAAU;MAC9BC,SAAS,EAAEiC,QAAQ,CAAChC,kBAAkB;MACtCE,SAAS,EAAE8B,QAAQ,CAAC7B;IACtB,CAAC;;IAED;IACArF,YAAY,CAACI,OAAO,CAACV,mBAAmB,EAAE+B,IAAI,CAACC,SAAS,CAAC0F,gBAAgB,CAAC,CAAC;IAE3E,OAAOA,gBAAgB;EACzB;AACF;AAEA,eAAe1G,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}