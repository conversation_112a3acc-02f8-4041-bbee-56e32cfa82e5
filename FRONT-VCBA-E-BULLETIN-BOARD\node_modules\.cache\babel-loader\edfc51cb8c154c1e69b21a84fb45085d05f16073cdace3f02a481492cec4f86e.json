{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 7v4\",\n  key: \"xawao1\"\n}], [\"path\", {\n  d: \"M7.998 9.003a5 5 0 1 0 8-.005\",\n  key: \"1pek45\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst CirclePower = createLucideIcon(\"circle-power\", __iconNode);\nexport { __iconNode, CirclePower as default };\n//# sourceMappingURL=circle-power.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}