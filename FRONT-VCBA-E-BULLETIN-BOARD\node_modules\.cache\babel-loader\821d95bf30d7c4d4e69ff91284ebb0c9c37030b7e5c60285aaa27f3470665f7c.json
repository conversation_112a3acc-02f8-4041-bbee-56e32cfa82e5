{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 16 4 4 4-4\",\n  key: \"1co6wj\"\n}], [\"path\", {\n  d: \"M7 20V4\",\n  key: \"1yoxec\"\n}], [\"path\", {\n  d: \"m21 8-4-4-4 4\",\n  key: \"1c9v7m\"\n}], [\"path\", {\n  d: \"M17 4v16\",\n  key: \"7dpous\"\n}]];\nconst ArrowDownUp = createLucideIcon(\"arrow-down-up\", __iconNode);\nexport { __iconNode, ArrowDownUp as default };\n//# sourceMappingURL=arrow-down-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}