{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20.34 17.52a10 10 0 1 0-2.82 2.82\",\n  key: \"fydyku\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"17f5cg\"\n}], [\"path\", {\n  d: \"m13.41 13.41 4.18 4.18\",\n  key: \"1gqbwc\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}]];\nconst Radius = createLucideIcon(\"radius\", __iconNode);\nexport { __iconNode, Radius as default };\n//# sourceMappingURL=radius.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}