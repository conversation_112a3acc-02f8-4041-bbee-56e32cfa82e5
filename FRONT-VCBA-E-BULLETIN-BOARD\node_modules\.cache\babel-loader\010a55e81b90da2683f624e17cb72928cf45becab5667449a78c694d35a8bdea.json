{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\AdminCommentSection.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport { Heart, MessageCircle, Shield, Trash2, Flag, AlertCircle, ArrowRight } from 'lucide-react';\nimport { shouldShowReplyButton, calculateIndentation, getDepthLimitMessage, getCommentDepthClasses, COMMENT_DEPTH_CONFIG } from '../../utils/commentDepth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminCommentItem = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  onDelete,\n  onFlag,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  _s();\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showActions, setShowActions] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      onDelete(comment.comment_id);\n    }\n  };\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n  const handleFlag = () => {\n    const reason = window.prompt('Please provide a reason for flagging this comment:');\n    if (reason && reason.trim()) {\n      onFlag(comment.comment_id, reason.trim());\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: `comment-${comment.comment_id}`,\n    className: depthClasses.join(' '),\n    style: {\n      marginLeft: `${indentation}px`,\n      marginBottom: '1rem',\n      padding: '1rem',\n      backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n      borderRadius: '8px',\n      border: '1px solid #e5e7eb',\n      position: 'relative'\n    },\n    onMouseEnter: () => setShowActions(true),\n    onMouseLeave: () => setShowActions(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'start',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '2.5rem',\n          height: '2.5rem',\n          borderRadius: '50%',\n          backgroundColor: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: '600',\n          fontSize: '0.875rem',\n          flexShrink: 0,\n          position: 'relative'\n        },\n        children: [comment.author_name ? comment.author_name.charAt(0).toUpperCase() : '?', comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '-2px',\n            right: '-2px',\n            width: '1rem',\n            height: '1rem',\n            backgroundColor: '#facc15',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Shield, {\n            size: 8,\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            },\n            children: comment.author_name || 'Anonymous'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), comment.user_type === 'admin' && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              fontSize: '0.75rem',\n              fontWeight: '500',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '0.375rem',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), \"Admin\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), comment.is_flagged && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#ef4444',\n              color: 'white',\n              fontSize: '0.75rem',\n              fontWeight: '500',\n              padding: '0.125rem 0.5rem',\n              borderRadius: '0.375rem',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Flag, {\n              size: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), \"Flagged\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            },\n            children: formatCommentDate(comment.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            marginBottom: '0.75rem',\n            wordBreak: 'break-word'\n          },\n          children: comment.comment_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReactionToggle,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              color: hasUserReacted ? '#ef4444' : '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '0.375rem',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#f3f4f6';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n            },\n            children: [/*#__PURE__*/_jsxDEV(Heart, {\n              size: 14,\n              fill: hasUserReacted ? '#ef4444' : 'none'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: comment.reaction_count || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), canReply ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleReplyClick,\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.25rem',\n              background: 'none',\n              border: 'none',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem 0.5rem',\n              borderRadius: '0.375rem',\n              transition: 'all 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = '#f3f4f6';\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              size: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), \"Reply\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Show depth limit message for max depth comments\n          _jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.75rem',\n              color: '#f59e0b',\n              fontStyle: 'italic'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Reply depth limit reached\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                // Scroll to top-level comment for continuing thread\n                const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                if (rootElement) {\n                  rootElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center'\n                  });\n                }\n              },\n              style: {\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                color: '#3b82f6',\n                fontSize: '0.75rem',\n                textDecoration: 'underline',\n                padding: '0'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [\"Continue thread\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), currentUserType === 'admin' && showActions && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFlag,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: '#f59e0b',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.backgroundColor = '#fef3c7';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Flag, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), \"Flag\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDelete,\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: '#ef4444',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.backgroundColor = '#fef2f2';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), showReplyForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.75rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(AdminCommentForm, {\n            announcementId: comment.announcement_id,\n            parentCommentId: comment.comment_id,\n            onSubmit: () => setShowReplyForm(false),\n            onCancel: () => setShowReplyForm(false),\n            placeholder: \"Write a reply...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), showDepthWarning && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.5rem',\n            backgroundColor: '#fef3c7',\n            border: '1px solid #f59e0b',\n            borderRadius: '4px',\n            fontSize: '0.75rem',\n            color: '#92400e',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: depthLimitMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), comment.replies && comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem'\n          },\n          children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(AdminCommentItem, {\n            comment: reply,\n            onReply: onReply,\n            onLike: onLike,\n            onUnlike: onUnlike,\n            onDelete: onDelete,\n            onFlag: onFlag,\n            currentUserId: currentUserId,\n            currentUserType: currentUserType,\n            depth: depth + 1\n          }, reply.comment_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n\n// Admin Comment Form Component will be added in the next chunk\n_s(AdminCommentItem, \"OYfzjSRSx7720jmObxR/xvaWehw=\");\n_c = AdminCommentItem;\nconst AdminCommentForm = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  _s2();\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    createComment\n  } = useComments(announcementId, 'admin'); // Admin service\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!commentText.trim() || isSubmitting) return;\n    try {\n      setIsSubmitting(true);\n      const commentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        parent_comment_id: parentCommentId,\n        is_anonymous: isAnonymous\n      };\n      await createComment(commentData);\n      setCommentText('');\n      onSubmit();\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    style: {\n      padding: '1rem',\n      backgroundColor: '#f8fafc',\n      borderRadius: '8px',\n      border: '1px solid #e2e8f0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: commentText,\n        onChange: e => setCommentText(e.target.value),\n        placeholder: placeholder,\n        rows: 3,\n        style: {\n          width: '100%',\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          fontSize: '0.875rem',\n          outline: 'none',\n          resize: 'vertical',\n          fontFamily: 'inherit'\n        },\n        onFocus: e => {\n          e.currentTarget.style.borderColor = '#3b82f6';\n          e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n        },\n        onBlur: e => {\n          e.currentTarget.style.borderColor = '#d1d5db';\n          e.currentTarget.style.boxShadow = 'none';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '0.75rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '0.875rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: isAnonymous,\n          onChange: e => setIsAnonymous(e.target.checked),\n          style: {\n            width: '1rem',\n            height: '1rem',\n            accentColor: '#3b82f6'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), \"Post as Anonymous Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '0.5rem'\n        },\n        children: [onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          style: {\n            padding: '0.5rem 1rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '6px',\n            backgroundColor: 'white',\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.backgroundColor = '#f9fafb';\n            e.currentTarget.style.borderColor = '#9ca3af';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.backgroundColor = 'white';\n            e.currentTarget.style.borderColor = '#d1d5db';\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !commentText.trim() || isSubmitting,\n          style: {\n            padding: '0.5rem 1rem',\n            border: 'none',\n            borderRadius: '6px',\n            backgroundColor: !commentText.trim() || isSubmitting ? '#9ca3af' : '#3b82f6',\n            color: 'white',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => {\n            if (!isSubmitting && commentText.trim()) {\n              e.currentTarget.style.backgroundColor = '#2563eb';\n            }\n          },\n          onMouseLeave: e => {\n            if (!isSubmitting && commentText.trim()) {\n              e.currentTarget.style.backgroundColor = '#3b82f6';\n            }\n          },\n          children: isSubmitting ? 'Posting...' : 'Post Comment'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 470,\n    columnNumber: 5\n  }, this);\n};\n\n// Main AdminCommentSection Component\n_s2(AdminCommentForm, \"BbE2Nc5wJlPq9At3ZSo/OwA4M08=\", false, function () {\n  return [useComments];\n});\n_c2 = AdminCommentForm;\nconst AdminCommentSection = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'admin'\n}) => {\n  _s3();\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment,\n    deleteComment,\n    flagComment\n  } = useComments(announcementId, 'admin'); // Explicitly use admin service\n\n  const handleReply = parentId => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n  const handleDelete = async commentId => {\n    try {\n      await deleteComment(commentId);\n      await refresh();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n    }\n  };\n  const handleFlag = async (commentId, reason) => {\n    try {\n      await flagComment(commentId, reason);\n      await refresh();\n    } catch (error) {\n      console.error('Error flagging comment:', error);\n    }\n  };\n  if (!allowComments) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic',\n        backgroundColor: '#f9fafb',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n        size: 20,\n        style: {\n          marginBottom: '0.5rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Comments are disabled for this announcement.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Shield, {\n        size: 20,\n        color: \"#3b82f6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151',\n          margin: 0\n        },\n        children: [\"Admin Comments (\", comments.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '0.75rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '6px',\n        marginBottom: '1rem',\n        fontSize: '0.875rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Flag, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminCommentForm, {\n        announcementId: announcementId,\n        onSubmit: refresh,\n        placeholder: \"Share your admin insights...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '1.5rem',\n          height: '1.5rem',\n          border: '2px solid #e5e7eb',\n          borderTop: '2px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 9\n    }, this) : comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        backgroundColor: '#f8fafc',\n        borderRadius: '8px',\n        border: '1px solid #e2e8f0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n        size: 24,\n        style: {\n          marginBottom: '0.5rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No comments yet. Be the first admin to share insights!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      children: comments.map(comment => /*#__PURE__*/_jsxDEV(AdminCommentItem, {\n        comment: comment,\n        onReply: handleReply,\n        onLike: likeComment,\n        onUnlike: unlikeComment,\n        onDelete: handleDelete,\n        onFlag: handleFlag,\n        currentUserId: currentUserId,\n        currentUserType: currentUserType\n      }, comment.comment_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 652,\n    columnNumber: 5\n  }, this);\n};\n_s3(AdminCommentSection, \"QXmdCaMyDgdlX99Xi+oikqU+5xQ=\", false, function () {\n  return [useComments];\n});\n_c3 = AdminCommentSection;\nexport default AdminCommentSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AdminCommentItem\");\n$RefreshReg$(_c2, \"AdminCommentForm\");\n$RefreshReg$(_c3, \"AdminCommentSection\");", "map": {"version": 3, "names": ["React", "useState", "useComments", "formatCommentDate", "Heart", "MessageCircle", "Shield", "Trash2", "Flag", "AlertCircle", "ArrowRight", "shouldShowReplyButton", "calculateIndentation", "getDepthLimitMessage", "getCommentDepthClasses", "COMMENT_DEPTH_CONFIG", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminCommentItem", "comment", "onReply", "onLike", "onUnlike", "onDelete", "onFlag", "currentUserId", "currentUserType", "depth", "_s", "showReplyForm", "setShowReplyForm", "showActions", "setShowActions", "showDepthWarning", "setShowDepthWarning", "hasUserReacted", "user_reaction", "undefined", "canReply", "indentation", "depthClasses", "depthLimitMessage", "isAtMaxDepth", "MAX_DEPTH", "handleReactionToggle", "comment_id", "handleDelete", "window", "confirm", "handleReplyClick", "setTimeout", "handleFlag", "reason", "prompt", "trim", "id", "className", "join", "style", "marginLeft", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "position", "onMouseEnter", "onMouseLeave", "children", "display", "alignItems", "gap", "width", "height", "user_type", "justifyContent", "color", "fontWeight", "fontSize", "flexShrink", "author_name", "char<PERSON>t", "toUpperCase", "bottom", "right", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "min<PERSON><PERSON><PERSON>", "flexWrap", "is_flagged", "created_at", "lineHeight", "wordBreak", "comment_text", "onClick", "background", "cursor", "transition", "e", "currentTarget", "fill", "reaction_count", "fontStyle", "rootElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "textDecoration", "marginTop", "AdminCommentForm", "announcementId", "announcement_id", "parentCommentId", "onSubmit", "onCancel", "placeholder", "replies", "length", "map", "reply", "_c", "_s2", "commentText", "setCommentText", "isAnonymous", "setIsAnonymous", "isSubmitting", "setIsSubmitting", "createComment", "handleSubmit", "preventDefault", "commentData", "parent_comment_id", "is_anonymous", "error", "console", "value", "onChange", "target", "rows", "outline", "resize", "fontFamily", "onFocus", "borderColor", "boxShadow", "onBlur", "type", "checked", "accentColor", "disabled", "_c2", "AdminCommentSection", "allowComments", "_s3", "comments", "loading", "refresh", "likeComment", "unlikeComment", "deleteComment", "flagComment", "handleReply", "parentId", "log", "commentId", "textAlign", "opacity", "margin", "borderTop", "animation", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/AdminCommentSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useComments, formatCommentDate } from '../../hooks/useComments';\nimport type { Comment, CreateCommentData } from '../../services/commentService';\nimport { Heart, MessageCircle, Shield, Trash2, Flag, AlertCircle, ArrowRight } from 'lucide-react';\nimport ProfileAvatar from '../common/ProfileAvatar';\nimport {\n  shouldShowReplyButton,\n  calculateIndentation,\n  getDepthLimitMessage,\n  getCommentDepthClasses,\n  COMMENT_DEPTH_CONFIG\n} from '../../utils/commentDepth';\n\ninterface AdminCommentSectionProps {\n  announcementId: number;\n  allowComments?: boolean;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n}\n\ninterface AdminCommentItemProps {\n  comment: Comment;\n  onReply: (parentId: number) => void;\n  onLike: (id: number) => void;\n  onUnlike: (id: number) => void;\n  onDelete: (id: number) => void;\n  onFlag: (id: number, reason: string) => void;\n  currentUserId?: number;\n  currentUserType?: 'admin' | 'student';\n  depth?: number;\n}\n\nconst AdminCommentItem: React.FC<AdminCommentItemProps> = ({\n  comment,\n  onReply,\n  onLike,\n  onUnlike,\n  onDelete,\n  onFlag,\n  currentUserId,\n  currentUserType,\n  depth = 0\n}) => {\n  const [showReplyForm, setShowReplyForm] = useState(false);\n  const [showActions, setShowActions] = useState(false);\n  const [showDepthWarning, setShowDepthWarning] = useState(false);\n  const hasUserReacted = comment.user_reaction !== undefined && comment.user_reaction !== null;\n\n  // Calculate depth-related properties\n  const canReply = shouldShowReplyButton(depth);\n  const indentation = calculateIndentation(depth);\n  const depthClasses = getCommentDepthClasses(depth);\n  const depthLimitMessage = getDepthLimitMessage(depth);\n  const isAtMaxDepth = depth >= COMMENT_DEPTH_CONFIG.MAX_DEPTH;\n\n  const handleReactionToggle = () => {\n    if (hasUserReacted) {\n      onUnlike(comment.comment_id);\n    } else {\n      onLike(comment.comment_id);\n    }\n  };\n\n  const handleDelete = () => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      onDelete(comment.comment_id);\n    }\n  };\n\n  const handleReplyClick = () => {\n    if (isAtMaxDepth) {\n      setShowDepthWarning(true);\n      setTimeout(() => setShowDepthWarning(false), 3000);\n    } else {\n      setShowReplyForm(true);\n    }\n  };\n\n  const handleFlag = () => {\n    const reason = window.prompt('Please provide a reason for flagging this comment:');\n    if (reason && reason.trim()) {\n      onFlag(comment.comment_id, reason.trim());\n    }\n  };\n\n  return (\n    <div\n      id={`comment-${comment.comment_id}`}\n      className={depthClasses.join(' ')}\n      style={{\n        marginLeft: `${indentation}px`,\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: depth > 0 ? '#f9fafb' : 'white',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb',\n        position: 'relative'\n      }}\n    onMouseEnter={() => setShowActions(true)}\n    onMouseLeave={() => setShowActions(false)}\n    >\n      <div style={{ display: 'flex', alignItems: 'start', gap: '0.75rem' }}>\n        {/* Avatar */}\n        <div style={{\n          width: '2.5rem',\n          height: '2.5rem',\n          borderRadius: '50%',\n          backgroundColor: comment.user_type === 'admin' ? '#3b82f6' : '#22c55e',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: '600',\n          fontSize: '0.875rem',\n          flexShrink: 0,\n          position: 'relative'\n        }}>\n          {comment.author_name ? comment.author_name.charAt(0).toUpperCase() : '?'}\n          {comment.user_type === 'admin' && (\n            <div style={{\n              position: 'absolute',\n              bottom: '-2px',\n              right: '-2px',\n              width: '1rem',\n              height: '1rem',\n              backgroundColor: '#facc15',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              border: '2px solid white'\n            }}>\n              <Shield size={8} color=\"white\" />\n            </div>\n          )}\n        </div>\n\n        <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Comment Header */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            marginBottom: '0.5rem',\n            flexWrap: 'wrap'\n          }}>\n            <span style={{\n              fontWeight: '600',\n              color: '#374151',\n              fontSize: '0.875rem'\n            }}>\n              {comment.author_name || 'Anonymous'}\n            </span>\n            \n            {comment.user_type === 'admin' && (\n              <span style={{\n                backgroundColor: '#3b82f6',\n                color: 'white',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '0.375rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              }}>\n                <Shield size={10} />\n                Admin\n              </span>\n            )}\n\n            {comment.is_flagged && (\n              <span style={{\n                backgroundColor: '#ef4444',\n                color: 'white',\n                fontSize: '0.75rem',\n                fontWeight: '500',\n                padding: '0.125rem 0.5rem',\n                borderRadius: '0.375rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              }}>\n                <Flag size={10} />\n                Flagged\n              </span>\n            )}\n\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem'\n            }}>\n              {formatCommentDate(comment.created_at)}\n            </span>\n          </div>\n\n          {/* Comment Content */}\n          <div style={{\n            color: '#374151',\n            fontSize: '0.875rem',\n            lineHeight: '1.5',\n            marginBottom: '0.75rem',\n            wordBreak: 'break-word'\n          }}>\n            {comment.comment_text}\n          </div>\n\n          {/* Comment Actions */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '0.75rem'\n          }}>\n            {/* Like Button */}\n            <button\n              onClick={handleReactionToggle}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem',\n                background: 'none',\n                border: 'none',\n                color: hasUserReacted ? '#ef4444' : '#6b7280',\n                cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n              }}\n            >\n              <Heart\n                size={14}\n                fill={hasUserReacted ? '#ef4444' : 'none'}\n              />\n              <span>{comment.reaction_count || 0}</span>\n            </button>\n\n            {/* Reply Button with Depth Limiting */}\n            {canReply ? (\n              <button\n                onClick={handleReplyClick}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem',\n                  background: 'none',\n                  border: 'none',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '0.375rem',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n                e.currentTarget.style.color = '#374151';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'transparent';\n                e.currentTarget.style.color = '#6b7280';\n              }}\n            >\n              <MessageCircle size={14} />\n              Reply\n            </button>\n            ) : (\n              // Show depth limit message for max depth comments\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.75rem',\n                color: '#f59e0b',\n                fontStyle: 'italic'\n              }}>\n                <AlertCircle size={12} />\n                <span>Reply depth limit reached</span>\n                <button\n                  onClick={() => {\n                    // Scroll to top-level comment for continuing thread\n                    const rootElement = document.getElementById(`comment-${comment.comment_id}`);\n                    if (rootElement) {\n                      rootElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                    }\n                  }}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#3b82f6',\n                    fontSize: '0.75rem',\n                    textDecoration: 'underline',\n                    padding: '0'\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                    Continue thread\n                    <ArrowRight size={10} />\n                  </span>\n                </button>\n              </div>\n            )}\n\n            {/* Admin Actions */}\n            {currentUserType === 'admin' && showActions && (\n              <>\n                <button\n                  onClick={handleFlag}\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    background: 'none',\n                    border: 'none',\n                    color: '#f59e0b',\n                    cursor: 'pointer',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '0.375rem',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#fef3c7';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  }}\n                >\n                  <Flag size={14} />\n                  Flag\n                </button>\n\n                <button\n                  onClick={handleDelete}\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    background: 'none',\n                    border: 'none',\n                    color: '#ef4444',\n                    cursor: 'pointer',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '0.375rem',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.backgroundColor = '#fef2f2';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  }}\n                >\n                  <Trash2 size={14} />\n                  Delete\n                </button>\n              </>\n            )}\n          </div>\n\n          {/* Reply Form */}\n          {showReplyForm && (\n            <div style={{ marginTop: '0.75rem' }}>\n              <AdminCommentForm\n                announcementId={comment.announcement_id}\n                parentCommentId={comment.comment_id}\n                onSubmit={() => setShowReplyForm(false)}\n                onCancel={() => setShowReplyForm(false)}\n                placeholder=\"Write a reply...\"\n              />\n            </div>\n          )}\n\n          {/* Depth Warning Message */}\n          {showDepthWarning && (\n            <div style={{\n              marginTop: '0.5rem',\n              padding: '0.5rem',\n              backgroundColor: '#fef3c7',\n              border: '1px solid #f59e0b',\n              borderRadius: '4px',\n              fontSize: '0.75rem',\n              color: '#92400e',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <AlertCircle size={14} />\n              <span>{depthLimitMessage}</span>\n            </div>\n          )}\n\n          {/* Replies */}\n          {comment.replies && comment.replies.length > 0 && (\n            <div style={{ marginTop: '1rem' }}>\n              {comment.replies.map((reply) => (\n                <AdminCommentItem\n                  key={reply.comment_id}\n                  comment={reply}\n                  onReply={onReply}\n                  onLike={onLike}\n                  onUnlike={onUnlike}\n                  onDelete={onDelete}\n                  onFlag={onFlag}\n                  currentUserId={currentUserId}\n                  currentUserType={currentUserType}\n                  depth={depth + 1}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Admin Comment Form Component will be added in the next chunk\ninterface AdminCommentFormProps {\n  announcementId: number;\n  parentCommentId?: number;\n  onSubmit: () => void;\n  onCancel?: () => void;\n  placeholder?: string;\n}\n\nconst AdminCommentForm: React.FC<AdminCommentFormProps> = ({\n  announcementId,\n  parentCommentId,\n  onSubmit,\n  onCancel,\n  placeholder = \"Write a comment...\"\n}) => {\n  const [commentText, setCommentText] = useState('');\n  const [isAnonymous, setIsAnonymous] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { createComment } = useComments(announcementId, 'admin'); // Admin service\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!commentText.trim() || isSubmitting) return;\n\n    try {\n      setIsSubmitting(true);\n\n      const commentData: CreateCommentData = {\n        announcement_id: announcementId,\n        comment_text: commentText.trim(),\n        parent_comment_id: parentCommentId,\n        is_anonymous: isAnonymous\n      };\n\n      await createComment(commentData);\n      setCommentText('');\n      onSubmit();\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} style={{\n      padding: '1rem',\n      backgroundColor: '#f8fafc',\n      borderRadius: '8px',\n      border: '1px solid #e2e8f0'\n    }}>\n      <div style={{ marginBottom: '0.75rem' }}>\n        <textarea\n          value={commentText}\n          onChange={(e) => setCommentText(e.target.value)}\n          placeholder={placeholder}\n          rows={3}\n          style={{\n            width: '100%',\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            outline: 'none',\n            resize: 'vertical',\n            fontFamily: 'inherit'\n          }}\n          onFocus={(e) => {\n            e.currentTarget.style.borderColor = '#3b82f6';\n            e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n          }}\n          onBlur={(e) => {\n            e.currentTarget.style.borderColor = '#d1d5db';\n            e.currentTarget.style.boxShadow = 'none';\n          }}\n        />\n      </div>\n\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: '0.75rem'\n      }}>\n        <label style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '0.875rem',\n          color: '#6b7280',\n          cursor: 'pointer'\n        }}>\n          <input\n            type=\"checkbox\"\n            checked={isAnonymous}\n            onChange={(e) => setIsAnonymous(e.target.checked)}\n            style={{\n              width: '1rem',\n              height: '1rem',\n              accentColor: '#3b82f6'\n            }}\n          />\n          Post as Anonymous Admin\n        </label>\n\n        <div style={{ display: 'flex', gap: '0.5rem' }}>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              style={{\n                padding: '0.5rem 1rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                backgroundColor: 'white',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = '#f9fafb';\n                e.currentTarget.style.borderColor = '#9ca3af';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'white';\n                e.currentTarget.style.borderColor = '#d1d5db';\n              }}\n            >\n              Cancel\n            </button>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={!commentText.trim() || isSubmitting}\n            style={{\n              padding: '0.5rem 1rem',\n              border: 'none',\n              borderRadius: '6px',\n              backgroundColor: !commentText.trim() || isSubmitting ? '#9ca3af' : '#3b82f6',\n              color: 'white',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              cursor: !commentText.trim() || isSubmitting ? 'not-allowed' : 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              if (!isSubmitting && commentText.trim()) {\n                e.currentTarget.style.backgroundColor = '#2563eb';\n              }\n            }}\n            onMouseLeave={(e) => {\n              if (!isSubmitting && commentText.trim()) {\n                e.currentTarget.style.backgroundColor = '#3b82f6';\n              }\n            }}\n          >\n            {isSubmitting ? 'Posting...' : 'Post Comment'}\n          </button>\n        </div>\n      </div>\n    </form>\n  );\n};\n\n// Main AdminCommentSection Component\nconst AdminCommentSection: React.FC<AdminCommentSectionProps> = ({\n  announcementId,\n  allowComments = true,\n  currentUserId,\n  currentUserType = 'admin'\n}) => {\n  const {\n    comments,\n    loading,\n    error,\n    refresh,\n    likeComment,\n    unlikeComment,\n    deleteComment,\n    flagComment\n  } = useComments(announcementId, 'admin'); // Explicitly use admin service\n\n  const handleReply = (parentId: number) => {\n    // This could trigger a scroll to the reply form or other UI feedback\n    console.log('Reply to comment:', parentId);\n  };\n\n  const handleDelete = async (commentId: number) => {\n    try {\n      await deleteComment(commentId);\n      await refresh();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n    }\n  };\n\n  const handleFlag = async (commentId: number, reason: string) => {\n    try {\n      await flagComment(commentId, reason);\n      await refresh();\n    } catch (error) {\n      console.error('Error flagging comment:', error);\n    }\n  };\n\n  if (!allowComments) {\n    return (\n      <div style={{\n        padding: '1rem',\n        textAlign: 'center',\n        color: '#6b7280',\n        fontSize: '0.875rem',\n        fontStyle: 'italic',\n        backgroundColor: '#f9fafb',\n        borderRadius: '8px',\n        border: '1px solid #e5e7eb'\n      }}>\n        <MessageCircle size={20} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n        <div>Comments are disabled for this announcement.</div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ marginTop: '1.5rem' }}>\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem',\n        marginBottom: '1rem'\n      }}>\n        <Shield size={20} color=\"#3b82f6\" />\n        <h3 style={{\n          fontSize: '1.125rem',\n          fontWeight: '600',\n          color: '#374151',\n          margin: 0\n        }}>\n          Admin Comments ({comments.length})\n        </h3>\n      </div>\n\n      {error && (\n        <div style={{\n          padding: '0.75rem',\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          color: '#dc2626',\n          borderRadius: '6px',\n          marginBottom: '1rem',\n          fontSize: '0.875rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <Flag size={16} />\n          {error}\n        </div>\n      )}\n\n      {/* Comment Form */}\n      <div style={{ marginBottom: '1.5rem' }}>\n        <AdminCommentForm\n          announcementId={announcementId}\n          onSubmit={refresh}\n          placeholder=\"Share your admin insights...\"\n        />\n      </div>\n\n      {/* Comments List */}\n      {loading ? (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          padding: '2rem'\n        }}>\n          <div style={{\n            width: '1.5rem',\n            height: '1.5rem',\n            border: '2px solid #e5e7eb',\n            borderTop: '2px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></div>\n        </div>\n      ) : comments.length === 0 ? (\n        <div style={{\n          textAlign: 'center',\n          padding: '2rem',\n          color: '#6b7280',\n          fontSize: '0.875rem',\n          backgroundColor: '#f8fafc',\n          borderRadius: '8px',\n          border: '1px solid #e2e8f0'\n        }}>\n          <MessageCircle size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n          <div>No comments yet. Be the first admin to share insights!</div>\n        </div>\n      ) : (\n        <div>\n          {comments.map((comment) => (\n            <AdminCommentItem\n              key={comment.comment_id}\n              comment={comment}\n              onReply={handleReply}\n              onLike={likeComment}\n              onUnlike={unlikeComment}\n              onDelete={handleDelete}\n              onFlag={handleFlag}\n              currentUserId={currentUserId}\n              currentUserType={currentUserType}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminCommentSection;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,yBAAyB;AAExE,SAASC,KAAK,EAAEC,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAElG,SACEC,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,QACf,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqBlC,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,aAAa;EACbC,eAAe;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMoC,cAAc,GAAGhB,OAAO,CAACiB,aAAa,KAAKC,SAAS,IAAIlB,OAAO,CAACiB,aAAa,KAAK,IAAI;;EAE5F;EACA,MAAME,QAAQ,GAAG7B,qBAAqB,CAACkB,KAAK,CAAC;EAC7C,MAAMY,WAAW,GAAG7B,oBAAoB,CAACiB,KAAK,CAAC;EAC/C,MAAMa,YAAY,GAAG5B,sBAAsB,CAACe,KAAK,CAAC;EAClD,MAAMc,iBAAiB,GAAG9B,oBAAoB,CAACgB,KAAK,CAAC;EACrD,MAAMe,YAAY,GAAGf,KAAK,IAAId,oBAAoB,CAAC8B,SAAS;EAE5D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIT,cAAc,EAAE;MAClBb,QAAQ,CAACH,OAAO,CAAC0B,UAAU,CAAC;IAC9B,CAAC,MAAM;MACLxB,MAAM,CAACF,OAAO,CAAC0B,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,CAACC,OAAO,CAAC,6EAA6E,CAAC,EAAE;MACjGzB,QAAQ,CAACJ,OAAO,CAAC0B,UAAU,CAAC;IAC9B;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,YAAY,EAAE;MAChBR,mBAAmB,CAAC,IAAI,CAAC;MACzBgB,UAAU,CAAC,MAAMhB,mBAAmB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM;MACLJ,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,MAAM,GAAGL,MAAM,CAACM,MAAM,CAAC,oDAAoD,CAAC;IAClF,IAAID,MAAM,IAAIA,MAAM,CAACE,IAAI,CAAC,CAAC,EAAE;MAC3B9B,MAAM,CAACL,OAAO,CAAC0B,UAAU,EAAEO,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,oBACEvC,OAAA;IACEwC,EAAE,EAAE,WAAWpC,OAAO,CAAC0B,UAAU,EAAG;IACpCW,SAAS,EAAEhB,YAAY,CAACiB,IAAI,CAAC,GAAG,CAAE;IAClCC,KAAK,EAAE;MACLC,UAAU,EAAE,GAAGpB,WAAW,IAAI;MAC9BqB,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAEnC,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;MAChDoC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE;IACZ,CAAE;IACJC,YAAY,EAAEA,CAAA,KAAMlC,cAAc,CAAC,IAAI,CAAE;IACzCmC,YAAY,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK,CAAE;IAAAoC,QAAA,eAExCrD,OAAA;MAAK2C,KAAK,EAAE;QAAEW,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAE;MAAAH,QAAA,gBAEnErD,OAAA;QAAK2C,KAAK,EAAE;UACVc,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBV,YAAY,EAAE,KAAK;UACnBD,eAAe,EAAE3C,OAAO,CAACuD,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;UACtEL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBK,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,CAAC;UACbd,QAAQ,EAAE;QACZ,CAAE;QAAAG,QAAA,GACCjD,OAAO,CAAC6D,WAAW,GAAG7D,OAAO,CAAC6D,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,GAAG,EACvE/D,OAAO,CAACuD,SAAS,KAAK,OAAO,iBAC5B3D,OAAA;UAAK2C,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpBkB,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACbZ,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdX,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnBM,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBK,cAAc,EAAE,QAAQ;YACxBX,MAAM,EAAE;UACV,CAAE;UAAAI,QAAA,eACArD,OAAA,CAACX,MAAM;YAACiF,IAAI,EAAE,CAAE;YAACT,KAAK,EAAC;UAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1E,OAAA;QAAK2C,KAAK,EAAE;UAAEgC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAEnCrD,OAAA;UAAK2C,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbX,YAAY,EAAE,QAAQ;YACtBgC,QAAQ,EAAE;UACZ,CAAE;UAAAxB,QAAA,gBACArD,OAAA;YAAM2C,KAAK,EAAE;cACXmB,UAAU,EAAE,KAAK;cACjBD,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE;YACZ,CAAE;YAAAV,QAAA,EACCjD,OAAO,CAAC6D,WAAW,IAAI;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EAENtE,OAAO,CAACuD,SAAS,KAAK,OAAO,iBAC5B3D,OAAA;YAAM2C,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1Bc,KAAK,EAAE,OAAO;cACdE,QAAQ,EAAE,SAAS;cACnBD,UAAU,EAAE,KAAK;cACjBhB,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,UAAU;cACxBM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAH,QAAA,gBACArD,OAAA,CAACX,MAAM;cAACiF,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EAEAtE,OAAO,CAAC0E,UAAU,iBACjB9E,OAAA;YAAM2C,KAAK,EAAE;cACXI,eAAe,EAAE,SAAS;cAC1Bc,KAAK,EAAE,OAAO;cACdE,QAAQ,EAAE,SAAS;cACnBD,UAAU,EAAE,KAAK;cACjBhB,OAAO,EAAE,iBAAiB;cAC1BE,YAAY,EAAE,UAAU;cACxBM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAH,QAAA,gBACArD,OAAA,CAACT,IAAI;cAAC+E,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,eAED1E,OAAA;YAAM2C,KAAK,EAAE;cACXkB,KAAK,EAAE,SAAS;cAChBE,QAAQ,EAAE;YACZ,CAAE;YAAAV,QAAA,EACCnE,iBAAiB,CAACkB,OAAO,CAAC2E,UAAU;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN1E,OAAA;UAAK2C,KAAK,EAAE;YACVkB,KAAK,EAAE,SAAS;YAChBE,QAAQ,EAAE,UAAU;YACpBiB,UAAU,EAAE,KAAK;YACjBnC,YAAY,EAAE,SAAS;YACvBoC,SAAS,EAAE;UACb,CAAE;UAAA5B,QAAA,EACCjD,OAAO,CAAC8E;QAAY;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGN1E,OAAA;UAAK2C,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,MAAM;YACXO,QAAQ,EAAE;UACZ,CAAE;UAAAV,QAAA,gBAEArD,OAAA;YACEmF,OAAO,EAAEtD,oBAAqB;YAC9Bc,KAAK,EAAE;cACLW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACd4B,UAAU,EAAE,MAAM;cAClBnC,MAAM,EAAE,MAAM;cACdY,KAAK,EAAEzC,cAAc,GAAG,SAAS,GAAG,SAAS;cAC7CiE,MAAM,EAAE,SAAS;cACjBvC,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,UAAU;cACxBsC,UAAU,EAAE;YACd,CAAE;YACFnC,YAAY,EAAGoC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD,CAAE;YACFK,YAAY,EAAGmC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,aAAa;YACvD,CAAE;YAAAM,QAAA,gBAEFrD,OAAA,CAACb,KAAK;cACJmF,IAAI,EAAE,EAAG;cACTmB,IAAI,EAAErE,cAAc,GAAG,SAAS,GAAG;YAAO;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACF1E,OAAA;cAAAqD,QAAA,EAAOjD,OAAO,CAACsF,cAAc,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EAGRnD,QAAQ,gBACPvB,OAAA;YACEmF,OAAO,EAAEjD,gBAAiB;YAC1BS,KAAK,EAAE;cACLW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,SAAS;cACd4B,UAAU,EAAE,MAAM;cAClBnC,MAAM,EAAE,MAAM;cACdY,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAE,SAAS;cACnBvC,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,UAAU;cACxBsC,UAAU,EAAE;YACd,CAAE;YACFnC,YAAY,EAAGoC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;cACjDwC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACkB,KAAK,GAAG,SAAS;YACzC,CAAE;YACFT,YAAY,EAAGmC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,aAAa;cACrDwC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACkB,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAR,QAAA,gBAEFrD,OAAA,CAACZ,aAAa;cAACkF,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;UAAA;UAEP;UACA1E,OAAA;YAAK2C,KAAK,EAAE;cACVW,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,QAAQ;cACbO,QAAQ,EAAE,SAAS;cACnBF,KAAK,EAAE,SAAS;cAChB8B,SAAS,EAAE;YACb,CAAE;YAAAtC,QAAA,gBACArD,OAAA,CAACR,WAAW;cAAC8E,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzB1E,OAAA;cAAAqD,QAAA,EAAM;YAAyB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtC1E,OAAA;cACEmF,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMS,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAW1F,OAAO,CAAC0B,UAAU,EAAE,CAAC;gBAC5E,IAAI8D,WAAW,EAAE;kBACfA,WAAW,CAACG,cAAc,CAAC;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAS,CAAC,CAAC;gBACrE;cACF,CAAE;cACFtD,KAAK,EAAE;gBACLyC,UAAU,EAAE,MAAM;gBAClBnC,MAAM,EAAE,MAAM;gBACdoC,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE,SAAS;gBAChBE,QAAQ,EAAE,SAAS;gBACnBmC,cAAc,EAAE,WAAW;gBAC3BpD,OAAO,EAAE;cACX,CAAE;cAAAO,QAAA,eAEFrD,OAAA;gBAAM2C,KAAK,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAU,CAAE;gBAAAH,QAAA,GAAC,iBAEtE,eAAArD,OAAA,CAACP,UAAU;kBAAC6E,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA/D,eAAe,KAAK,OAAO,IAAIK,WAAW,iBACzChB,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cACEmF,OAAO,EAAE/C,UAAW;cACpBO,KAAK,EAAE;gBACLW,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,SAAS;gBACd4B,UAAU,EAAE,MAAM;gBAClBnC,MAAM,EAAE,MAAM;gBACdY,KAAK,EAAE,SAAS;gBAChBwB,MAAM,EAAE,SAAS;gBACjBvC,OAAO,EAAE,gBAAgB;gBACzBE,YAAY,EAAE,UAAU;gBACxBsC,UAAU,EAAE;cACd,CAAE;cACFnC,YAAY,EAAGoC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;cACnD,CAAE;cACFK,YAAY,EAAGmC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,aAAa;cACvD,CAAE;cAAAM,QAAA,gBAEFrD,OAAA,CAACT,IAAI;gBAAC+E,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1E,OAAA;cACEmF,OAAO,EAAEpD,YAAa;cACtBY,KAAK,EAAE;gBACLW,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,SAAS;gBACd4B,UAAU,EAAE,MAAM;gBAClBnC,MAAM,EAAE,MAAM;gBACdY,KAAK,EAAE,SAAS;gBAChBwB,MAAM,EAAE,SAAS;gBACjBvC,OAAO,EAAE,gBAAgB;gBACzBE,YAAY,EAAE,UAAU;gBACxBsC,UAAU,EAAE;cACd,CAAE;cACFnC,YAAY,EAAGoC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;cACnD,CAAE;cACFK,YAAY,EAAGmC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,aAAa;cACvD,CAAE;cAAAM,QAAA,gBAEFrD,OAAA,CAACV,MAAM;gBAACgF,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL5D,aAAa,iBACZd,OAAA;UAAK2C,KAAK,EAAE;YAAEwD,SAAS,EAAE;UAAU,CAAE;UAAA9C,QAAA,eACnCrD,OAAA,CAACoG,gBAAgB;YACfC,cAAc,EAAEjG,OAAO,CAACkG,eAAgB;YACxCC,eAAe,EAAEnG,OAAO,CAAC0B,UAAW;YACpC0E,QAAQ,EAAEA,CAAA,KAAMzF,gBAAgB,CAAC,KAAK,CAAE;YACxC0F,QAAQ,EAAEA,CAAA,KAAM1F,gBAAgB,CAAC,KAAK,CAAE;YACxC2F,WAAW,EAAC;UAAkB;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAxD,gBAAgB,iBACflB,OAAA;UAAK2C,KAAK,EAAE;YACVwD,SAAS,EAAE,QAAQ;YACnBrD,OAAO,EAAE,QAAQ;YACjBC,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBe,QAAQ,EAAE,SAAS;YACnBF,KAAK,EAAE,SAAS;YAChBP,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAH,QAAA,gBACArD,OAAA,CAACR,WAAW;YAAC8E,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB1E,OAAA;YAAAqD,QAAA,EAAO3B;UAAiB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN,EAGAtE,OAAO,CAACuG,OAAO,IAAIvG,OAAO,CAACuG,OAAO,CAACC,MAAM,GAAG,CAAC,iBAC5C5G,OAAA;UAAK2C,KAAK,EAAE;YAAEwD,SAAS,EAAE;UAAO,CAAE;UAAA9C,QAAA,EAC/BjD,OAAO,CAACuG,OAAO,CAACE,GAAG,CAAEC,KAAK,iBACzB9G,OAAA,CAACG,gBAAgB;YAEfC,OAAO,EAAE0G,KAAM;YACfzG,OAAO,EAAEA,OAAQ;YACjBC,MAAM,EAAEA,MAAO;YACfC,QAAQ,EAAEA,QAAS;YACnBC,QAAQ,EAAEA,QAAS;YACnBC,MAAM,EAAEA,MAAO;YACfC,aAAa,EAAEA,aAAc;YAC7BC,eAAe,EAAEA,eAAgB;YACjCC,KAAK,EAAEA,KAAK,GAAG;UAAE,GATZkG,KAAK,CAAChF,UAAU;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUtB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA7D,EAAA,CAtYMV,gBAAiD;AAAA4G,EAAA,GAAjD5G,gBAAiD;AA+YvD,MAAMiG,gBAAiD,GAAGA,CAAC;EACzDC,cAAc;EACdE,eAAe;EACfC,QAAQ;EACRC,QAAQ;EACRC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAM,GAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmI,WAAW,EAAEC,cAAc,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqI,YAAY,EAAEC,eAAe,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEuI;EAAc,CAAC,GAAGtI,WAAW,CAACoH,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;;EAEhE,MAAMmB,YAAY,GAAG,MAAOjC,CAAkB,IAAK;IACjDA,CAAC,CAACkC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,WAAW,CAAC1E,IAAI,CAAC,CAAC,IAAI8E,YAAY,EAAE;IAEzC,IAAI;MACFC,eAAe,CAAC,IAAI,CAAC;MAErB,MAAMI,WAA8B,GAAG;QACrCpB,eAAe,EAAED,cAAc;QAC/BnB,YAAY,EAAE+B,WAAW,CAAC1E,IAAI,CAAC,CAAC;QAChCoF,iBAAiB,EAAEpB,eAAe;QAClCqB,YAAY,EAAET;MAChB,CAAC;MAED,MAAMI,aAAa,CAACG,WAAW,CAAC;MAChCR,cAAc,CAAC,EAAE,CAAC;MAClBV,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRP,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEtH,OAAA;IAAMwG,QAAQ,EAAEgB,YAAa;IAAC7E,KAAK,EAAE;MACnCG,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE;IACV,CAAE;IAAAI,QAAA,gBACArD,OAAA;MAAK2C,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAU,CAAE;MAAAQ,QAAA,eACtCrD,OAAA;QACE+H,KAAK,EAAEd,WAAY;QACnBe,QAAQ,EAAGzC,CAAC,IAAK2B,cAAc,CAAC3B,CAAC,CAAC0C,MAAM,CAACF,KAAK,CAAE;QAChDrB,WAAW,EAAEA,WAAY;QACzBwB,IAAI,EAAE,CAAE;QACRvF,KAAK,EAAE;UACLc,KAAK,EAAE,MAAM;UACbX,OAAO,EAAE,SAAS;UAClBG,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,KAAK;UACnBe,QAAQ,EAAE,UAAU;UACpBoE,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,UAAU;UAClBC,UAAU,EAAE;QACd,CAAE;QACFC,OAAO,EAAG/C,CAAC,IAAK;UACdA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC4F,WAAW,GAAG,SAAS;UAC7ChD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC6F,SAAS,GAAG,mCAAmC;QACvE,CAAE;QACFC,MAAM,EAAGlD,CAAC,IAAK;UACbA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC4F,WAAW,GAAG,SAAS;UAC7ChD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC6F,SAAS,GAAG,MAAM;QAC1C;MAAE;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1E,OAAA;MAAK2C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBK,cAAc,EAAE,eAAe;QAC/BJ,GAAG,EAAE;MACP,CAAE;MAAAH,QAAA,gBACArD,OAAA;QAAO2C,KAAK,EAAE;UACZW,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,QAAQ;UACbO,QAAQ,EAAE,UAAU;UACpBF,KAAK,EAAE,SAAS;UAChBwB,MAAM,EAAE;QACV,CAAE;QAAAhC,QAAA,gBACArD,OAAA;UACE0I,IAAI,EAAC,UAAU;UACfC,OAAO,EAAExB,WAAY;UACrBa,QAAQ,EAAGzC,CAAC,IAAK6B,cAAc,CAAC7B,CAAC,CAAC0C,MAAM,CAACU,OAAO,CAAE;UAClDhG,KAAK,EAAE;YACLc,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdkF,WAAW,EAAE;UACf;QAAE;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,2BAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAER1E,OAAA;QAAK2C,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,GAC5CoD,QAAQ,iBACPzG,OAAA;UACE0I,IAAI,EAAC,QAAQ;UACbvD,OAAO,EAAEsB,QAAS;UAClB9D,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBG,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,OAAO;YACxBc,KAAK,EAAE,SAAS;YAChBE,QAAQ,EAAE,UAAU;YACpBD,UAAU,EAAE,KAAK;YACjBuB,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE;UACd,CAAE;UACFnC,YAAY,EAAGoC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;YACjDwC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC4F,WAAW,GAAG,SAAS;UAC/C,CAAE;UACFnF,YAAY,EAAGmC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,OAAO;YAC/CwC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC4F,WAAW,GAAG,SAAS;UAC/C,CAAE;UAAAlF,QAAA,EACH;QAED;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED1E,OAAA;UACE0I,IAAI,EAAC,QAAQ;UACbG,QAAQ,EAAE,CAAC5B,WAAW,CAAC1E,IAAI,CAAC,CAAC,IAAI8E,YAAa;UAC9C1E,KAAK,EAAE;YACLG,OAAO,EAAE,aAAa;YACtBG,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAE,CAACkE,WAAW,CAAC1E,IAAI,CAAC,CAAC,IAAI8E,YAAY,GAAG,SAAS,GAAG,SAAS;YAC5ExD,KAAK,EAAE,OAAO;YACdE,QAAQ,EAAE,UAAU;YACpBD,UAAU,EAAE,KAAK;YACjBuB,MAAM,EAAE,CAAC4B,WAAW,CAAC1E,IAAI,CAAC,CAAC,IAAI8E,YAAY,GAAG,aAAa,GAAG,SAAS;YACvE/B,UAAU,EAAE;UACd,CAAE;UACFnC,YAAY,EAAGoC,CAAC,IAAK;YACnB,IAAI,CAAC8B,YAAY,IAAIJ,WAAW,CAAC1E,IAAI,CAAC,CAAC,EAAE;cACvCgD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD;UACF,CAAE;UACFK,YAAY,EAAGmC,CAAC,IAAK;YACnB,IAAI,CAAC8B,YAAY,IAAIJ,WAAW,CAAC1E,IAAI,CAAC,CAAC,EAAE;cACvCgD,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACI,eAAe,GAAG,SAAS;YACnD;UACF,CAAE;UAAAM,QAAA,EAEDgE,YAAY,GAAG,YAAY,GAAG;QAAc;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;;AAED;AAAAsC,GAAA,CAhKMZ,gBAAiD;EAAA,QAU3BnH,WAAW;AAAA;AAAA6J,GAAA,GAVjC1C,gBAAiD;AAiKvD,MAAM2C,mBAAuD,GAAGA,CAAC;EAC/D1C,cAAc;EACd2C,aAAa,GAAG,IAAI;EACpBtI,aAAa;EACbC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAsI,GAAA;EACJ,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPtB,KAAK;IACLuB,OAAO;IACPC,WAAW;IACXC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGvK,WAAW,CAACoH,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;;EAE1C,MAAMoD,WAAW,GAAIC,QAAgB,IAAK;IACxC;IACA5B,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,EAAED,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAM3H,YAAY,GAAG,MAAO6H,SAAiB,IAAK;IAChD,IAAI;MACF,MAAML,aAAa,CAACK,SAAS,CAAC;MAC9B,MAAMR,OAAO,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMzF,UAAU,GAAG,MAAAA,CAAOwH,SAAiB,EAAEvH,MAAc,KAAK;IAC9D,IAAI;MACF,MAAMmH,WAAW,CAACI,SAAS,EAAEvH,MAAM,CAAC;MACpC,MAAM+G,OAAO,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,IAAI,CAACmB,aAAa,EAAE;IAClB,oBACEhJ,OAAA;MAAK2C,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACf+G,SAAS,EAAE,QAAQ;QACnBhG,KAAK,EAAE,SAAS;QAChBE,QAAQ,EAAE,UAAU;QACpB4B,SAAS,EAAE,QAAQ;QACnB5C,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAI,QAAA,gBACArD,OAAA,CAACZ,aAAa;QAACkF,IAAI,EAAE,EAAG;QAAC3B,KAAK,EAAE;UAAEE,YAAY,EAAE,QAAQ;UAAEiH,OAAO,EAAE;QAAI;MAAE;QAAAvF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5E1E,OAAA;QAAAqD,QAAA,EAAK;MAA4C;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,oBACE1E,OAAA;IAAK2C,KAAK,EAAE;MAAEwD,SAAS,EAAE;IAAS,CAAE;IAAA9C,QAAA,gBAClCrD,OAAA;MAAK2C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,QAAQ;QACbX,YAAY,EAAE;MAChB,CAAE;MAAAQ,QAAA,gBACArD,OAAA,CAACX,MAAM;QAACiF,IAAI,EAAE,EAAG;QAACT,KAAK,EAAC;MAAS;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpC1E,OAAA;QAAI2C,KAAK,EAAE;UACToB,QAAQ,EAAE,UAAU;UACpBD,UAAU,EAAE,KAAK;UACjBD,KAAK,EAAE,SAAS;UAChBkG,MAAM,EAAE;QACV,CAAE;QAAA1G,QAAA,GAAC,kBACe,EAAC6F,QAAQ,CAACtC,MAAM,EAAC,GACnC;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAELmD,KAAK,iBACJ7H,OAAA;MAAK2C,KAAK,EAAE;QACVG,OAAO,EAAE,SAAS;QAClBC,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BY,KAAK,EAAE,SAAS;QAChBb,YAAY,EAAE,KAAK;QACnBH,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE,UAAU;QACpBT,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAH,QAAA,gBACArD,OAAA,CAACT,IAAI;QAAC+E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjBmD,KAAK;IAAA;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1E,OAAA;MAAK2C,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAS,CAAE;MAAAQ,QAAA,eACrCrD,OAAA,CAACoG,gBAAgB;QACfC,cAAc,EAAEA,cAAe;QAC/BG,QAAQ,EAAE4C,OAAQ;QAClB1C,WAAW,EAAC;MAA8B;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLyE,OAAO,gBACNnJ,OAAA;MAAK2C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfM,cAAc,EAAE,QAAQ;QACxBd,OAAO,EAAE;MACX,CAAE;MAAAO,QAAA,eACArD,OAAA;QAAK2C,KAAK,EAAE;UACVc,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBT,MAAM,EAAE,mBAAmB;UAC3B+G,SAAS,EAAE,mBAAmB;UAC9BhH,YAAY,EAAE,KAAK;UACnBiH,SAAS,EAAE;QACb;MAAE;QAAA1F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJwE,QAAQ,CAACtC,MAAM,KAAK,CAAC,gBACvB5G,OAAA;MAAK2C,KAAK,EAAE;QACVkH,SAAS,EAAE,QAAQ;QACnB/G,OAAO,EAAE,MAAM;QACfe,KAAK,EAAE,SAAS;QAChBE,QAAQ,EAAE,UAAU;QACpBhB,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAI,QAAA,gBACArD,OAAA,CAACZ,aAAa;QAACkF,IAAI,EAAE,EAAG;QAAC3B,KAAK,EAAE;UAAEE,YAAY,EAAE,QAAQ;UAAEiH,OAAO,EAAE;QAAI;MAAE;QAAAvF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5E1E,OAAA;QAAAqD,QAAA,EAAK;MAAsD;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,gBAEN1E,OAAA;MAAAqD,QAAA,EACG6F,QAAQ,CAACrC,GAAG,CAAEzG,OAAO,iBACpBJ,OAAA,CAACG,gBAAgB;QAEfC,OAAO,EAAEA,OAAQ;QACjBC,OAAO,EAAEoJ,WAAY;QACrBnJ,MAAM,EAAE+I,WAAY;QACpB9I,QAAQ,EAAE+I,aAAc;QACxB9I,QAAQ,EAAEuB,YAAa;QACvBtB,MAAM,EAAE2B,UAAW;QACnB1B,aAAa,EAAEA,aAAc;QAC7BC,eAAe,EAAEA;MAAgB,GAR5BP,OAAO,CAAC0B,UAAU;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASxB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACuE,GAAA,CAxJIF,mBAAuD;EAAA,QAevD9J,WAAW;AAAA;AAAAiL,GAAA,GAfXnB,mBAAuD;AA0J7D,eAAeA,mBAAmB;AAAC,IAAAhC,EAAA,EAAA+B,GAAA,EAAAoB,GAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}