{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v10\",\n  key: \"mnfbl\"\n}], [\"path\", {\n  d: \"m8.5 4 7 4\",\n  key: \"m1xjk3\"\n}], [\"path\", {\n  d: \"m8.5 8 7-4\",\n  key: \"t0m5j6\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"5\",\n  key: \"qbz8iq\"\n}]];\nconst NonBinary = createLucideIcon(\"non-binary\", __iconNode);\nexport { __iconNode, NonBinary as default };\n//# sourceMappingURL=non-binary.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}