{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 14 4 9l5-5\",\n  key: \"102s5s\"\n}], [\"path\", {\n  d: \"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11\",\n  key: \"f3b9sd\"\n}]];\nconst Undo2 = createLucideIcon(\"undo-2\", __iconNode);\nexport { __iconNode, Undo2 as default };\n//# sourceMappingURL=undo-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}