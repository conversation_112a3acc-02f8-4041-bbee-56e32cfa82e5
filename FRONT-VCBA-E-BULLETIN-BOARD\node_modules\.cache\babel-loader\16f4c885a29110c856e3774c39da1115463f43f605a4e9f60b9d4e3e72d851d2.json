{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.7 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v4.1\",\n  key: \"1bw5m7\"\n}], [\"path\", {\n  d: \"m21 21-1.9-1.9\",\n  key: \"1g2n9r\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"18b49y\"\n}]];\nconst FolderSearch = createLucideIcon(\"folder-search\", __iconNode);\nexport { __iconNode, FolderSearch as default };\n//# sourceMappingURL=folder-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}