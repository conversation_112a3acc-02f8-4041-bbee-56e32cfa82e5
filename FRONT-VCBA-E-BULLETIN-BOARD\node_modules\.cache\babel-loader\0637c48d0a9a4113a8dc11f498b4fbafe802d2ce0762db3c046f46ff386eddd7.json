{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n  key: \"yt0hxn\"\n}], [\"polyline\", {\n  points: \"7.5 4.21 12 6.81 16.5 4.21\",\n  key: \"fabo96\"\n}], [\"polyline\", {\n  points: \"7.5 19.79 7.5 14.6 3 12\",\n  key: \"z377f1\"\n}], [\"polyline\", {\n  points: \"21 12 16.5 14.6 16.5 19.79\",\n  key: \"9nrev1\"\n}], [\"polyline\", {\n  points: \"3.27 6.96 12 12.01 20.73 6.96\",\n  key: \"1180pa\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22.08\",\n  y2: \"12\",\n  key: \"3z3uq6\"\n}]];\nconst Codesandbox = createLucideIcon(\"codesandbox\", __iconNode);\nexport { __iconNode, Codesandbox as default };\n//# sourceMappingURL=codesandbox.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}