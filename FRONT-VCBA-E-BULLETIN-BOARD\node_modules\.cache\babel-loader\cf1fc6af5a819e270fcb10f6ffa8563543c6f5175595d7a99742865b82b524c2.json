{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l4-2\",\n  key: \"1r2kuh\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst Clock2 = createLucideIcon(\"clock-2\", __iconNode);\nexport { __iconNode, Clock2 as default };\n//# sourceMappingURL=clock-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}