{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v5\",\n  key: \"1p9q5i\"\n}], [\"path\", {\n  d: \"M14 6a6 6 0 0 1 6 6v3\",\n  key: \"1hnv84\"\n}], [\"path\", {\n  d: \"M4 15v-3a6 6 0 0 1 6-6\",\n  key: \"9ciidu\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"15\",\n  width: \"20\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"g3x8cw\"\n}]];\nconst HardHat = createLucideIcon(\"hard-hat\", __iconNode);\nexport { __iconNode, HardHat as default };\n//# sourceMappingURL=hard-hat.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}