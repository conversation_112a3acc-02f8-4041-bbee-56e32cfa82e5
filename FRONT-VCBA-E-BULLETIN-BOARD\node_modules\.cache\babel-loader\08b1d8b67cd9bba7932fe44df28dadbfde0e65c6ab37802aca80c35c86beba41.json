{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 20-1.25-2.5L6 18\",\n  key: \"18frcb\"\n}], [\"path\", {\n  d: \"M10 4 8.75 6.5 6 6\",\n  key: \"7mghy3\"\n}], [\"path\", {\n  d: \"m14 20 1.25-2.5L18 18\",\n  key: \"1chtki\"\n}], [\"path\", {\n  d: \"m14 4 1.25 2.5L18 6\",\n  key: \"1b4wsy\"\n}], [\"path\", {\n  d: \"m17 21-3-6h-4\",\n  key: \"15hhxa\"\n}], [\"path\", {\n  d: \"m17 3-3 6 1.5 3\",\n  key: \"11697g\"\n}], [\"path\", {\n  d: \"M2 12h6.5L10 9\",\n  key: \"kv9z4n\"\n}], [\"path\", {\n  d: \"m20 10-1.5 2 1.5 2\",\n  key: \"1swlpi\"\n}], [\"path\", {\n  d: \"M22 12h-6.5L14 15\",\n  key: \"1mxi28\"\n}], [\"path\", {\n  d: \"m4 10 1.5 2L4 14\",\n  key: \"k9enpj\"\n}], [\"path\", {\n  d: \"m7 21 3-6-1.5-3\",\n  key: \"j8hb9u\"\n}], [\"path\", {\n  d: \"m7 3 3 6h4\",\n  key: \"1otusx\"\n}]];\nconst Snowflake = createLucideIcon(\"snowflake\", __iconNode);\nexport { __iconNode, Snowflake as default };\n//# sourceMappingURL=snowflake.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}