{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\",\n  key: \"1pljnt\"\n}], [\"path\", {\n  d: \"m9.2 22 3-7\",\n  key: \"sb5f6j\"\n}], [\"path\", {\n  d: \"m9 13-3 7\",\n  key: \"500co5\"\n}], [\"path\", {\n  d: \"m17 13-3 7\",\n  key: \"8t2fiy\"\n}]];\nconst CloudRainWind = createLucideIcon(\"cloud-rain-wind\", __iconNode);\nexport { __iconNode, CloudRainWind as default };\n//# sourceMappingURL=cloud-rain-wind.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}