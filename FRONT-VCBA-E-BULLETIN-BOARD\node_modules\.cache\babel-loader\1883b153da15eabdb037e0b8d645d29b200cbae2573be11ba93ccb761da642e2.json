{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13v8l-4-4\",\n  key: \"1f5nwf\"\n}], [\"path\", {\n  d: \"m12 21 4-4\",\n  key: \"1lfcce\"\n}], [\"path\", {\n  d: \"M4.393 15.269A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.436 8.284\",\n  key: \"ui1hmy\"\n}]];\nconst CloudDownload = createLucideIcon(\"cloud-download\", __iconNode);\nexport { __iconNode, CloudDownload as default };\n//# sourceMappingURL=cloud-download.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}