{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3V2\",\n  key: \"ar7q03\"\n}], [\"path\", {\n  d: \"m15.4 17.4 3.2-2.8a2 2 0 1 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2l-1.302-1.464A1 1 0 0 0 6.151 19H5\",\n  key: \"n2g93r\"\n}], [\"path\", {\n  d: \"M2 14h12a2 2 0 0 1 0 4h-2\",\n  key: \"1o2jem\"\n}], [\"path\", {\n  d: \"M4 10h16\",\n  key: \"img6z1\"\n}], [\"path\", {\n  d: \"M5 10a7 7 0 0 1 14 0\",\n  key: \"1ega1o\"\n}], [\"path\", {\n  d: \"M5 14v6a1 1 0 0 1-1 1H2\",\n  key: \"1hescx\"\n}]];\nconst HandPlatter = createLucideIcon(\"hand-platter\", __iconNode);\nexport { __iconNode, HandPlatter as default };\n//# sourceMappingURL=hand-platter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}