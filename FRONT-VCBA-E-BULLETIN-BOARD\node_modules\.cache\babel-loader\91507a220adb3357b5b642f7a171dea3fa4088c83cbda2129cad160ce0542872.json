{"ast": null, "code": "import React,{useState}from'react';import{useStudentAuth}from'../../contexts/StudentAuthContext';import{User,Bell,Lock}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentSettings=()=>{const{user}=useStudentAuth();const[activeTab,setActiveTab]=useState('profile');const tabs=[{key:'profile',label:'Profile',icon:User},{key:'notifications',label:'Notifications',icon:Bell},{key:'privacy',label:'Privacy',icon:Lock}];const renderProfileSettings=()=>{var _user$firstName,_user$lastName;return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',border:'1px solid rgba(255, 255, 255, 0.2)',backdropFilter:'blur(10px)'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#1e40af',fontSize:'1.25rem',fontWeight:'600'},children:\"Profile Picture\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'2rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{width:'100px',height:'100px',borderRadius:'50%',background:'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'700',fontSize:'2rem'},children:[user===null||user===void 0?void 0:(_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0),user===null||user===void 0?void 0:(_user$lastName=user.lastName)===null||_user$lastName===void 0?void 0:_user$lastName.charAt(0)]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"button\",{style:{background:'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 1.5rem',fontWeight:'600',cursor:'pointer',marginRight:'1rem'},children:\"Upload New Photo\"}),/*#__PURE__*/_jsx(\"button\",{style:{background:'none',border:'1px solid #e0f2fe',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:'pointer',color:'#6b7280'},children:\"Remove\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'16px',padding:'2rem',border:'1px solid rgba(255, 255, 255, 0.2)',backdropFilter:'blur(10px)'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#1e40af',fontSize:'1.25rem',fontWeight:'600'},children:\"Personal Information\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"First Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.firstName,style:{width:'100%',padding:'0.75rem',border:'1px solid #e0f2fe',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Last Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:user===null||user===void 0?void 0:user.lastName,style:{width:'100%',padding:'0.75rem',border:'1px solid #e0f2fe',borderRadius:'8px',fontSize:'1rem',outline:'none'}})]}),/*#__PURE__*/_jsxs(\"div\",{style:{gridColumn:'1 / -1'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",defaultValue:user===null||user===void 0?void 0:user.email,disabled:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #e0f2fe',borderRadius:'8px',fontSize:'1rem',outline:'none',background:'#f8fafc',color:'#6b7280'}}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',margin:'0.5rem 0 0 0'},children:\"Email address cannot be changed. Contact admin for assistance.\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Student ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:\"VCBA-2025-001\",disabled:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #e0f2fe',borderRadius:'8px',fontSize:'1rem',outline:'none',background:'#f8fafc',color:'#6b7280'}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'0.5rem',color:'#374151',fontWeight:'500'},children:\"Course\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",defaultValue:\"BS Business Administration\",disabled:true,style:{width:'100%',padding:'0.75rem',border:'1px solid #e0f2fe',borderRadius:'8px',fontSize:'1rem',outline:'none',background:'#f8fafc',color:'#6b7280'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'2rem',display:'flex',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"button\",{style:{background:'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',color:'white',border:'none',borderRadius:'8px',padding:'0.75rem 2rem',fontWeight:'600',cursor:'pointer'},children:\"Save Changes\"}),/*#__PURE__*/_jsx(\"button\",{style:{background:'none',border:'1px solid #e0f2fe',borderRadius:'8px',padding:'0.75rem 2rem',cursor:'pointer',color:'#6b7280'},children:\"Cancel\"})]})]})]});};const renderNotificationSettings=()=>/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e0f2fe'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#1e40af',fontSize:'1.25rem',fontWeight:'600'},children:\"Notification Preferences\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Email Notifications\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Receive announcements via email\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#3b82f6',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Push Notifications\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Get instant notifications on your device\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#3b82f6',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Alert Notifications\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Receive urgent alerts and important notices\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#3b82f6',transition:'0.4s',borderRadius:'34px'}})]})]})]})]});const renderPrivacySettings=()=>/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'16px',padding:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e0f2fe'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 1.5rem 0',color:'#1e40af',fontSize:'1.25rem',fontWeight:'600'},children:\"Privacy Settings\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Profile Visibility\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Allow other students to see your profile\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",defaultChecked:true,style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#3b82f6',transition:'0.4s',borderRadius:'34px'}})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#374151',marginBottom:'0.25rem'},children:\"Activity Status\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#6b7280',fontSize:'0.875rem'},children:\"Show when you're online\"})]}),/*#__PURE__*/_jsxs(\"label\",{style:{position:'relative',display:'inline-block',width:'60px',height:'34px'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:{opacity:0,width:0,height:0}}),/*#__PURE__*/_jsx(\"span\",{style:{position:'absolute',cursor:'pointer',top:0,left:0,right:0,bottom:0,background:'#ccc',transition:'0.4s',borderRadius:'34px'}})]})]})]})]});const renderContent=()=>{switch(activeTab){case'profile':return renderProfileSettings();case'notifications':return renderNotificationSettings();case'privacy':return renderPrivacySettings();default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%',padding:'2rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{background:'white',borderRadius:'16px',padding:'1.5rem',marginBottom:'2rem',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',border:'1px solid #e0f2fe'},children:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',gap:'1rem',flexWrap:'wrap'},children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.key),style:{background:activeTab===tab.key?'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)':'transparent',color:activeTab===tab.key?'white':'#6b7280',border:activeTab===tab.key?'none':'1px solid #e0f2fe',borderRadius:'8px',padding:'0.75rem 1.5rem',cursor:'pointer',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.5rem',transition:'all 0.2s ease'},children:[/*#__PURE__*/_jsx(tab.icon,{size:18,color:activeTab===tab.key?'#22c55e':'#6b7280'}),tab.label]},tab.key))})}),renderContent()]});};export default StudentSettings;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}