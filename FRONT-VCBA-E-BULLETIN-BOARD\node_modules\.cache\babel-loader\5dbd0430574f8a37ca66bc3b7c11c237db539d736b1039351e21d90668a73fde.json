{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M9 3v18\",\n  key: \"fh3hqa\"\n}]];\nconst PanelLeft = createLucideIcon(\"panel-left\", __iconNode);\nexport { __iconNode, PanelLeft as default };\n//# sourceMappingURL=panel-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}