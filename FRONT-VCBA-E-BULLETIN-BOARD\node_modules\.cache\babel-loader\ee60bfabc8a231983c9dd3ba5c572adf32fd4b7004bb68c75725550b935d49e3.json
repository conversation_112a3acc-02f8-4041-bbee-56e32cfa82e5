{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 9.5 8 12l2 2.5\",\n  key: \"3mjy60\"\n}], [\"path\", {\n  d: \"m14 9.5 2 2.5-2 2.5\",\n  key: \"1bir2l\"\n}], [\"path\", {\n  d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22z\",\n  key: \"k85zhp\"\n}]];\nconst MessageCircleCode = createLucideIcon(\"message-circle-code\", __iconNode);\nexport { __iconNode, MessageCircleCode as default };\n//# sourceMappingURL=message-circle-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}