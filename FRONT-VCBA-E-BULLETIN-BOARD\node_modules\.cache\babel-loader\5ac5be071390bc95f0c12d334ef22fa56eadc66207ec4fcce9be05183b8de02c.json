{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M8 15h5\",\n  key: \"vxg57a\"\n}], [\"path\", {\n  d: \"M8 11h5a2 2 0 1 0 0-4h-3v10\",\n  key: \"1usi5u\"\n}]];\nconst ReceiptRussianRuble = createLucideIcon(\"receipt-russian-ruble\", __iconNode);\nexport { __iconNode, ReceiptRussianRuble as default };\n//# sourceMappingURL=receipt-russian-ruble.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}