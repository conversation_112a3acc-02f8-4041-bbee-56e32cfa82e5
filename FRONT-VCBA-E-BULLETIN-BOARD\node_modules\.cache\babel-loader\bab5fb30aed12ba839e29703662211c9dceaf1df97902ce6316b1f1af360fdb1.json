{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 21v-8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8\",\n  key: \"1w3rig\"\n}], [\"path\", {\n  d: \"M4 16s.5-1 2-1 2.5 2 4 2 2.5-2 4-2 2.5 2 4 2 2-1 2-1\",\n  key: \"n2jgmb\"\n}], [\"path\", {\n  d: \"M2 21h20\",\n  key: \"1nyx9w\"\n}], [\"path\", {\n  d: \"M7 8v3\",\n  key: \"1qtyvj\"\n}], [\"path\", {\n  d: \"M12 8v3\",\n  key: \"hwp4zt\"\n}], [\"path\", {\n  d: \"M17 8v3\",\n  key: \"1i6e5u\"\n}], [\"path\", {\n  d: \"M7 4h.01\",\n  key: \"1bh4kh\"\n}], [\"path\", {\n  d: \"M12 4h.01\",\n  key: \"1ujb9j\"\n}], [\"path\", {\n  d: \"M17 4h.01\",\n  key: \"1upcoc\"\n}]];\nconst Cake = createLucideIcon(\"cake\", __iconNode);\nexport { __iconNode, Cake as default };\n//# sourceMappingURL=cake.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}