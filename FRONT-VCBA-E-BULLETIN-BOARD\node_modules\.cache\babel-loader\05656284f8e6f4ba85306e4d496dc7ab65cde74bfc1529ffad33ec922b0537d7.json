{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10v7.9\",\n  key: \"m8g9tt\"\n}], [\"path\", {\n  d: \"M11.802 6.145a5 5 0 0 1 6.053 6.053\",\n  key: \"dn87i3\"\n}], [\"path\", {\n  d: \"M14 6.1v2.243\",\n  key: \"1kzysn\"\n}], [\"path\", {\n  d: \"m15.5 15.571-.964.964a5 5 0 0 1-7.071 0 5 5 0 0 1 0-7.07l.964-.965\",\n  key: \"3sxy18\"\n}], [\"path\", {\n  d: \"M16 7V3a1 1 0 0 1 1.707-.707 2.5 2.5 0 0 0 2.152.717 1 1 0 0 1 1.131 1.131 2.5 2.5 0 0 0 .717 2.152A1 1 0 0 1 21 8h-4\",\n  key: \"gpb6xx\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8 17v4a1 1 0 0 1-1.707.707 2.5 2.5 0 0 0-2.152-.717 1 1 0 0 1-1.131-1.131 2.5 2.5 0 0 0-.717-2.152A1 1 0 0 1 3 16h4\",\n  key: \"qexcha\"\n}]];\nconst CandyOff = createLucideIcon(\"candy-off\", __iconNode);\nexport { __iconNode, CandyOff as default };\n//# sourceMappingURL=candy-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}