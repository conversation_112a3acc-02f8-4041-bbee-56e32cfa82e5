{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { calendarService } from '../services';\n// Hook for managing calendar events\nexport const useCalendar = initialDate => {\n  _s();\n  const [events, setEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [currentDate, setCurrentDate] = useState(initialDate || new Date());\n  const [view, setView] = useState('month');\n  const [calendarData, setCalendarData] = useState({\n    events: {}\n  });\n  const fetchCalendarData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const currentYear = currentDate.getFullYear();\n      // Fetch events for multiple years (current year ± 2 years)\n      const yearsToFetch = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1, currentYear + 2];\n\n      // console.log(`📅 Fetching calendar data for years: ${yearsToFetch.join(', ')}...`);\n\n      // Fetch events for all years in parallel\n      const responses = await Promise.all(yearsToFetch.map(year => calendarService.getCalendarView(year, undefined)));\n\n      // Merge all successful responses\n      const mergedEvents = {};\n      let totalEvents = 0;\n      responses.forEach((response, index) => {\n        if (response.success && response.data) {\n          // Merge events from this year into the combined events object\n          Object.entries(response.data.events).forEach(([date, dayEvents]) => {\n            if (!mergedEvents[date]) {\n              mergedEvents[date] = [];\n            }\n            mergedEvents[date].push(...dayEvents);\n            totalEvents += dayEvents.length;\n          });\n        } else {\n          console.warn(`⚠️ Failed to fetch data for year ${yearsToFetch[index]}:`, response.message);\n        }\n      });\n\n      // Set the merged calendar data\n      setCalendarData({\n        events: mergedEvents\n      });\n\n      // Convert grouped events to flat array for easier manipulation\n      const flatEvents = Object.values(mergedEvents).flat();\n      setEvents(flatEvents);\n\n      // console.log(`✅ Calendar data loaded for years ${yearsToFetch.join(', ')}: ${totalEvents} events`);\n\n      if (totalEvents === 0) {\n        setError('No calendar data found for the requested years');\n      }\n    } catch (err) {\n      // console.error('❌ Error fetching calendar data:', err);\n\n      let errorMessage = 'An error occurred while fetching calendar data';\n      if (err.message.includes('Network connection failed')) {\n        errorMessage = 'Unable to connect to server. Please check your connection and try again.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentDate.getFullYear()]); // Only depend on year since we fetch entire year\n\n  const refresh = useCallback(async () => {\n    await fetchCalendarData();\n  }, [fetchCalendarData]);\n  const createEvent = useCallback(async data => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.createEvent(data);\n      if (response.success) {\n        // Refresh calendar data to get the new event\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to create event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while creating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const updateEvent = useCallback(async (id, data) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.updateEvent(id, data);\n      if (response.success && response.data) {\n        // Refresh the calendar data to ensure multi-day events are properly handled\n        // This is more reliable than trying to manually update the complex calendar state\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to update event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while updating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const deleteEvent = useCallback(async id => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.deleteEvent(id);\n      if (response.success) {\n        // Refresh the calendar data to ensure consistency\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to delete event');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while deleting event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n  const getEventsForDate = useCallback(date => {\n    // Format date manually to avoid timezone issues\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const dateKey = `${year}-${month}-${day}`;\n    return calendarData.events[dateKey] || [];\n  }, [calendarData]);\n  const getEventsForDateRange = useCallback(async (startDate, endDate) => {\n    try {\n      // Format dates manually to avoid timezone issues\n      const formatDate = date => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n      const start = formatDate(startDate);\n      const end = formatDate(endDate);\n      const response = await calendarService.getEventsByDateRange(start, end);\n      if (response.success && response.data) {\n        return response.data.events;\n      } else {\n        throw new Error(response.message || 'Failed to fetch events for date range');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching events for date range');\n      return [];\n    }\n  }, []);\n  useEffect(() => {\n    fetchCalendarData();\n  }, [fetchCalendarData]);\n  return {\n    events,\n    loading,\n    error,\n    currentDate,\n    view,\n    calendarData,\n    setCurrentDate,\n    setView,\n    refresh,\n    createEvent,\n    updateEvent,\n    deleteEvent,\n    getEventsForDate,\n    getEventsForDateRange\n  };\n};\n\n// Hook for managing calendar categories\n_s(useCalendar, \"2vA0Qb3Hd7A09pRA30+K+g24wJk=\");\nexport const useCalendarCategories = () => {\n  _s2();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await calendarService.getCategoriesWithSubcategories();\n      if (response.success && response.data) {\n        setCategories(response.data.categories || []);\n      } else {\n        setError(response.message || 'Failed to fetch categories');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Utility functions for calendar operations\n_s2(useCalendarCategories, \"Df8QI5+iDpsUtY9TPlsWgTiZI8A=\");\nexport const getCalendarDays = (year, month) => {\n  const firstDay = new Date(year, month, 1);\n  const lastDay = new Date(year, month + 1, 0);\n  const startDate = new Date(firstDay);\n  const endDate = new Date(lastDay);\n\n  // Adjust to start from Sunday (or Monday based on preference)\n  startDate.setDate(startDate.getDate() - startDate.getDay());\n\n  // Adjust to end on Saturday (or Sunday based on preference)\n  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n  const days = [];\n  const currentDate = new Date(startDate);\n  while (currentDate <= endDate) {\n    days.push(new Date(currentDate));\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n  return days;\n};\nexport const isToday = date => {\n  const today = new Date();\n  return date.toDateString() === today.toDateString();\n};\nexport const isSameMonth = (date, month, year) => {\n  return date.getMonth() === month && date.getFullYear() === year;\n};\nexport const formatDateForDisplay = date => {\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n};\nexport const formatTimeForDisplay = date => {\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nexport const getMonthName = month => {\n  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n  return monthNames[month];\n};\nexport const getDayName = day => {\n  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n  return dayNames[day];\n};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "calendarService", "useCalendar", "initialDate", "_s", "events", "setEvents", "loading", "setLoading", "error", "setError", "currentDate", "setCurrentDate", "Date", "view", "<PERSON><PERSON><PERSON><PERSON>", "calendarData", "setCalendarData", "fetchCalendarData", "undefined", "currentYear", "getFullYear", "years<PERSON>oFetch", "responses", "Promise", "all", "map", "year", "getCalendarView", "mergedEvents", "totalEvents", "for<PERSON>ach", "response", "index", "success", "data", "Object", "entries", "date", "dayEvents", "push", "length", "console", "warn", "message", "flatEvents", "values", "flat", "err", "errorMessage", "includes", "refresh", "createEvent", "Error", "updateEvent", "id", "deleteEvent", "getEventsForDate", "month", "String", "getMonth", "padStart", "day", "getDate", "<PERSON><PERSON><PERSON>", "getEventsForDateRange", "startDate", "endDate", "formatDate", "start", "end", "getEventsByDateRange", "useCalendarCategories", "_s2", "categories", "setCategories", "fetchCategories", "getCategoriesWithSubcategories", "getCalendarDays", "firstDay", "lastDay", "setDate", "getDay", "days", "isToday", "today", "toDateString", "isSameMonth", "formatDateForDisplay", "toLocaleDateString", "weekday", "formatTimeForDisplay", "toLocaleTimeString", "hour", "minute", "getMonthName", "monthNames", "getDayName", "dayNames"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useCalendar.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { calendarService } from '../services';\nimport type {\n  CalendarEvent,\n  CreateEventData,\n  UpdateEventData,\n  EventFilters,\n  UseCalendarReturn,\n  CalendarViewResponse\n} from '../types/calendar.types';\n\n// Hook for managing calendar events\nexport const useCalendar = (initialDate?: Date): UseCalendarReturn => {\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [currentDate, setCurrentDate] = useState(initialDate || new Date());\n  const [view, setView] = useState<'month' | 'week' | 'day'>('month');\n  const [calendarData, setCalendarData] = useState<CalendarViewResponse>({ events: {} });\n\n  const fetchCalendarData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const currentYear = currentDate.getFullYear();\n      // Fetch events for multiple years (current year ± 2 years)\n      const yearsToFetch = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1, currentYear + 2];\n\n      // console.log(`📅 Fetching calendar data for years: ${yearsToFetch.join(', ')}...`);\n\n      // Fetch events for all years in parallel\n      const responses = await Promise.all(\n        yearsToFetch.map(year => calendarService.getCalendarView(year, undefined))\n      );\n\n      // Merge all successful responses\n      const mergedEvents: { [key: string]: any[] } = {};\n      let totalEvents = 0;\n\n      responses.forEach((response, index) => {\n        if (response.success && response.data) {\n          // Merge events from this year into the combined events object\n          Object.entries(response.data.events).forEach(([date, dayEvents]) => {\n            if (!mergedEvents[date]) {\n              mergedEvents[date] = [];\n            }\n            mergedEvents[date].push(...(dayEvents as any[]));\n            totalEvents += (dayEvents as any[]).length;\n          });\n        } else {\n          console.warn(`⚠️ Failed to fetch data for year ${yearsToFetch[index]}:`, response.message);\n        }\n      });\n\n      // Set the merged calendar data\n      setCalendarData({ events: mergedEvents });\n\n      // Convert grouped events to flat array for easier manipulation\n      const flatEvents = Object.values(mergedEvents).flat();\n      setEvents(flatEvents);\n\n      // console.log(`✅ Calendar data loaded for years ${yearsToFetch.join(', ')}: ${totalEvents} events`);\n\n      if (totalEvents === 0) {\n        setError('No calendar data found for the requested years');\n      }\n    } catch (err: any) {\n      // console.error('❌ Error fetching calendar data:', err);\n\n      let errorMessage = 'An error occurred while fetching calendar data';\n      if (err.message.includes('Network connection failed')) {\n        errorMessage = 'Unable to connect to server. Please check your connection and try again.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentDate.getFullYear()]); // Only depend on year since we fetch entire year\n\n  const refresh = useCallback(async () => {\n    await fetchCalendarData();\n  }, [fetchCalendarData]);\n\n  const createEvent = useCallback(async (data: CreateEventData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.createEvent(data);\n      \n      if (response.success) {\n        // Refresh calendar data to get the new event\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to create event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const updateEvent = useCallback(async (id: number, data: UpdateEventData) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.updateEvent(id, data);\n      \n      if (response.success && response.data) {\n        // Refresh the calendar data to ensure multi-day events are properly handled\n        // This is more reliable than trying to manually update the complex calendar state\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to update event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while updating event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const deleteEvent = useCallback(async (id: number) => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      \n      const response = await calendarService.deleteEvent(id);\n      \n      if (response.success) {\n        // Refresh the calendar data to ensure consistency\n        await fetchCalendarData();\n      } else {\n        throw new Error(response.message || 'Failed to delete event');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while deleting event');\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchCalendarData]);\n\n  const getEventsForDate = useCallback((date: Date): CalendarEvent[] => {\n    // Format date manually to avoid timezone issues\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const dateKey = `${year}-${month}-${day}`;\n    return calendarData.events[dateKey] || [];\n  }, [calendarData]);\n\n  const getEventsForDateRange = useCallback(async (startDate: Date, endDate: Date): Promise<CalendarEvent[]> => {\n    try {\n      // Format dates manually to avoid timezone issues\n      const formatDate = (date: Date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const start = formatDate(startDate);\n      const end = formatDate(endDate);\n\n      const response = await calendarService.getEventsByDateRange(start, end);\n\n      if (response.success && response.data) {\n        return response.data.events;\n      } else {\n        throw new Error(response.message || 'Failed to fetch events for date range');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching events for date range');\n      return [];\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchCalendarData();\n  }, [fetchCalendarData]);\n\n  return {\n    events,\n    loading,\n    error,\n    currentDate,\n    view,\n    calendarData,\n    setCurrentDate,\n    setView,\n    refresh,\n    createEvent,\n    updateEvent,\n    deleteEvent,\n    getEventsForDate,\n    getEventsForDateRange\n  };\n};\n\n// Hook for managing calendar categories\nexport const useCalendarCategories = () => {\n  const [categories, setCategories] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const fetchCategories = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await calendarService.getCategoriesWithSubcategories();\n\n      if (response.success && response.data) {\n        setCategories(response.data.categories || []);\n      } else {\n        setError(response.message || 'Failed to fetch categories');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while fetching categories');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  const refresh = useCallback(async () => {\n    await fetchCategories();\n  }, [fetchCategories]);\n\n  return {\n    categories,\n    loading,\n    error,\n    refresh\n  };\n};\n\n// Utility functions for calendar operations\nexport const getCalendarDays = (year: number, month: number): Date[] => {\n  const firstDay = new Date(year, month, 1);\n  const lastDay = new Date(year, month + 1, 0);\n  const startDate = new Date(firstDay);\n  const endDate = new Date(lastDay);\n  \n  // Adjust to start from Sunday (or Monday based on preference)\n  startDate.setDate(startDate.getDate() - startDate.getDay());\n  \n  // Adjust to end on Saturday (or Sunday based on preference)\n  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));\n  \n  const days: Date[] = [];\n  const currentDate = new Date(startDate);\n  \n  while (currentDate <= endDate) {\n    days.push(new Date(currentDate));\n    currentDate.setDate(currentDate.getDate() + 1);\n  }\n  \n  return days;\n};\n\nexport const isToday = (date: Date): boolean => {\n  const today = new Date();\n  return date.toDateString() === today.toDateString();\n};\n\nexport const isSameMonth = (date: Date, month: number, year: number): boolean => {\n  return date.getMonth() === month && date.getFullYear() === year;\n};\n\nexport const formatDateForDisplay = (date: Date): string => {\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n};\n\nexport const formatTimeForDisplay = (date: Date): string => {\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\nexport const getMonthName = (month: number): string => {\n  const monthNames = [\n    'January', 'February', 'March', 'April', 'May', 'June',\n    'July', 'August', 'September', 'October', 'November', 'December'\n  ];\n  return monthNames[month];\n};\n\nexport const getDayName = (day: number): string => {\n  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n  return dayNames[day];\n};\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,eAAe,QAAQ,aAAa;AAU7C;AACA,OAAO,MAAMC,WAAW,GAAIC,WAAkB,IAAwB;EAAAC,EAAA;EACpE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAkB,EAAE,CAAC;EACzD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAACK,WAAW,IAAI,IAAIU,IAAI,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAA2B,OAAO,CAAC;EACnE,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAuB;IAAEO,MAAM,EAAE,CAAC;EAAE,CAAC,CAAC;EAEtF,MAAMa,iBAAiB,GAAGlB,WAAW,CAAC,YAAY;IAChD,IAAI;MACFQ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMC,WAAW,GAAGT,WAAW,CAACU,WAAW,CAAC,CAAC;MAC7C;MACA,MAAMC,YAAY,GAAG,CAACF,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG,CAAC,EAAEA,WAAW,EAAEA,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG,CAAC,CAAC;;MAEtG;;MAEA;MACA,MAAMG,SAAS,GAAG,MAAMC,OAAO,CAACC,GAAG,CACjCH,YAAY,CAACI,GAAG,CAACC,IAAI,IAAI1B,eAAe,CAAC2B,eAAe,CAACD,IAAI,EAAER,SAAS,CAAC,CAC3E,CAAC;;MAED;MACA,MAAMU,YAAsC,GAAG,CAAC,CAAC;MACjD,IAAIC,WAAW,GAAG,CAAC;MAEnBP,SAAS,CAACQ,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACrC,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;UACrC;UACAC,MAAM,CAACC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAAC9B,MAAM,CAAC,CAAC0B,OAAO,CAAC,CAAC,CAACO,IAAI,EAAEC,SAAS,CAAC,KAAK;YAClE,IAAI,CAACV,YAAY,CAACS,IAAI,CAAC,EAAE;cACvBT,YAAY,CAACS,IAAI,CAAC,GAAG,EAAE;YACzB;YACAT,YAAY,CAACS,IAAI,CAAC,CAACE,IAAI,CAAC,GAAID,SAAmB,CAAC;YAChDT,WAAW,IAAKS,SAAS,CAAWE,MAAM;UAC5C,CAAC,CAAC;QACJ,CAAC,MAAM;UACLC,OAAO,CAACC,IAAI,CAAC,oCAAoCrB,YAAY,CAACW,KAAK,CAAC,GAAG,EAAED,QAAQ,CAACY,OAAO,CAAC;QAC5F;MACF,CAAC,CAAC;;MAEF;MACA3B,eAAe,CAAC;QAAEZ,MAAM,EAAEwB;MAAa,CAAC,CAAC;;MAEzC;MACA,MAAMgB,UAAU,GAAGT,MAAM,CAACU,MAAM,CAACjB,YAAY,CAAC,CAACkB,IAAI,CAAC,CAAC;MACrDzC,SAAS,CAACuC,UAAU,CAAC;;MAErB;;MAEA,IAAIf,WAAW,KAAK,CAAC,EAAE;QACrBpB,QAAQ,CAAC,gDAAgD,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOsC,GAAQ,EAAE;MACjB;;MAEA,IAAIC,YAAY,GAAG,gDAAgD;MACnE,IAAID,GAAG,CAACJ,OAAO,CAACM,QAAQ,CAAC,2BAA2B,CAAC,EAAE;QACrDD,YAAY,GAAG,0EAA0E;MAC3F,CAAC,MAAM,IAAID,GAAG,CAACJ,OAAO,EAAE;QACtBK,YAAY,GAAGD,GAAG,CAACJ,OAAO;MAC5B;MAEAlC,QAAQ,CAACuC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACG,WAAW,CAACU,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC,MAAM8B,OAAO,GAAGnD,WAAW,CAAC,YAAY;IACtC,MAAMkB,iBAAiB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMkC,WAAW,GAAGpD,WAAW,CAAC,MAAOmC,IAAqB,IAAK;IAC/D,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMa,QAAQ,GAAG,MAAM/B,eAAe,CAACmD,WAAW,CAACjB,IAAI,CAAC;MAExD,IAAIH,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMhB,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAImC,KAAK,CAACrB,QAAQ,CAACY,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACJ,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMI,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMoC,WAAW,GAAGtD,WAAW,CAAC,OAAOuD,EAAU,EAAEpB,IAAqB,KAAK;IAC3E,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMa,QAAQ,GAAG,MAAM/B,eAAe,CAACqD,WAAW,CAACC,EAAE,EAAEpB,IAAI,CAAC;MAE5D,IAAIH,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC;QACA;QACA,MAAMjB,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAImC,KAAK,CAACrB,QAAQ,CAACY,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACJ,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMI,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMsC,WAAW,GAAGxD,WAAW,CAAC,MAAOuD,EAAU,IAAK;IACpD,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMa,QAAQ,GAAG,MAAM/B,eAAe,CAACuD,WAAW,CAACD,EAAE,CAAC;MAEtD,IAAIvB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMhB,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAImC,KAAK,CAACrB,QAAQ,CAACY,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACJ,OAAO,IAAI,wCAAwC,CAAC;MACjE,MAAMI,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACU,iBAAiB,CAAC,CAAC;EAEvB,MAAMuC,gBAAgB,GAAGzD,WAAW,CAAEsC,IAAU,IAAsB;IACpE;IACA,MAAMX,IAAI,GAAGW,IAAI,CAACjB,WAAW,CAAC,CAAC;IAC/B,MAAMqC,KAAK,GAAGC,MAAM,CAACrB,IAAI,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACrB,IAAI,CAACyB,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMG,OAAO,GAAG,GAAGrC,IAAI,IAAI+B,KAAK,IAAII,GAAG,EAAE;IACzC,OAAO9C,YAAY,CAACX,MAAM,CAAC2D,OAAO,CAAC,IAAI,EAAE;EAC3C,CAAC,EAAE,CAAChD,YAAY,CAAC,CAAC;EAElB,MAAMiD,qBAAqB,GAAGjE,WAAW,CAAC,OAAOkE,SAAe,EAAEC,OAAa,KAA+B;IAC5G,IAAI;MACF;MACA,MAAMC,UAAU,GAAI9B,IAAU,IAAK;QACjC,MAAMX,IAAI,GAAGW,IAAI,CAACjB,WAAW,CAAC,CAAC;QAC/B,MAAMqC,KAAK,GAAGC,MAAM,CAACrB,IAAI,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACrB,IAAI,CAACyB,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,OAAO,GAAGlC,IAAI,IAAI+B,KAAK,IAAII,GAAG,EAAE;MAClC,CAAC;MAED,MAAMO,KAAK,GAAGD,UAAU,CAACF,SAAS,CAAC;MACnC,MAAMI,GAAG,GAAGF,UAAU,CAACD,OAAO,CAAC;MAE/B,MAAMnC,QAAQ,GAAG,MAAM/B,eAAe,CAACsE,oBAAoB,CAACF,KAAK,EAAEC,GAAG,CAAC;MAEvE,IAAItC,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC,OAAOH,QAAQ,CAACG,IAAI,CAAC9B,MAAM;MAC7B,CAAC,MAAM;QACL,MAAM,IAAIgD,KAAK,CAACrB,QAAQ,CAACY,OAAO,IAAI,uCAAuC,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACJ,OAAO,IAAI,wDAAwD,CAAC;MACjF,OAAO,EAAE;IACX;EACF,CAAC,EAAE,EAAE,CAAC;EAEN7C,SAAS,CAAC,MAAM;IACdmB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,OAAO;IACLb,MAAM;IACNE,OAAO;IACPE,KAAK;IACLE,WAAW;IACXG,IAAI;IACJE,YAAY;IACZJ,cAAc;IACdG,OAAO;IACPoC,OAAO;IACPC,WAAW;IACXE,WAAW;IACXE,WAAW;IACXC,gBAAgB;IAChBQ;EACF,CAAC;AACH,CAAC;;AAED;AAAA7D,EAAA,CApMaF,WAAW;AAqMxB,OAAO,MAAMsE,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAqB,CAAC;EAExD,MAAM8E,eAAe,GAAG5E,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFQ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACS,SAAS,CAAC;MAEnB,MAAMa,QAAQ,GAAG,MAAM/B,eAAe,CAAC4E,8BAA8B,CAAC,CAAC;MAEvE,IAAI7C,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCwC,aAAa,CAAC3C,QAAQ,CAACG,IAAI,CAACuC,UAAU,IAAI,EAAE,CAAC;MAC/C,CAAC,MAAM;QACLhE,QAAQ,CAACsB,QAAQ,CAACY,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOI,GAAQ,EAAE;MACjBtC,QAAQ,CAACsC,GAAG,CAACJ,OAAO,IAAI,6CAA6C,CAAC;IACxE,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENT,SAAS,CAAC,MAAM;IACd6E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMzB,OAAO,GAAGnD,WAAW,CAAC,YAAY;IACtC,MAAM4E,eAAe,CAAC,CAAC;EACzB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,OAAO;IACLF,UAAU;IACVnE,OAAO;IACPE,KAAK;IACL0C;EACF,CAAC;AACH,CAAC;;AAED;AAAAsB,GAAA,CAxCaD,qBAAqB;AAyClC,OAAO,MAAMM,eAAe,GAAGA,CAACnD,IAAY,EAAE+B,KAAa,KAAa;EACtE,MAAMqB,QAAQ,GAAG,IAAIlE,IAAI,CAACc,IAAI,EAAE+B,KAAK,EAAE,CAAC,CAAC;EACzC,MAAMsB,OAAO,GAAG,IAAInE,IAAI,CAACc,IAAI,EAAE+B,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5C,MAAMQ,SAAS,GAAG,IAAIrD,IAAI,CAACkE,QAAQ,CAAC;EACpC,MAAMZ,OAAO,GAAG,IAAItD,IAAI,CAACmE,OAAO,CAAC;;EAEjC;EACAd,SAAS,CAACe,OAAO,CAACf,SAAS,CAACH,OAAO,CAAC,CAAC,GAAGG,SAAS,CAACgB,MAAM,CAAC,CAAC,CAAC;;EAE3D;EACAf,OAAO,CAACc,OAAO,CAACd,OAAO,CAACJ,OAAO,CAAC,CAAC,IAAI,CAAC,GAAGI,OAAO,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;EAE3D,MAAMC,IAAY,GAAG,EAAE;EACvB,MAAMxE,WAAW,GAAG,IAAIE,IAAI,CAACqD,SAAS,CAAC;EAEvC,OAAOvD,WAAW,IAAIwD,OAAO,EAAE;IAC7BgB,IAAI,CAAC3C,IAAI,CAAC,IAAI3B,IAAI,CAACF,WAAW,CAAC,CAAC;IAChCA,WAAW,CAACsE,OAAO,CAACtE,WAAW,CAACoD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EAChD;EAEA,OAAOoB,IAAI;AACb,CAAC;AAED,OAAO,MAAMC,OAAO,GAAI9C,IAAU,IAAc;EAC9C,MAAM+C,KAAK,GAAG,IAAIxE,IAAI,CAAC,CAAC;EACxB,OAAOyB,IAAI,CAACgD,YAAY,CAAC,CAAC,KAAKD,KAAK,CAACC,YAAY,CAAC,CAAC;AACrD,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAACjD,IAAU,EAAEoB,KAAa,EAAE/B,IAAY,KAAc;EAC/E,OAAOW,IAAI,CAACsB,QAAQ,CAAC,CAAC,KAAKF,KAAK,IAAIpB,IAAI,CAACjB,WAAW,CAAC,CAAC,KAAKM,IAAI;AACjE,CAAC;AAED,OAAO,MAAM6D,oBAAoB,GAAIlD,IAAU,IAAa;EAC1D,OAAOA,IAAI,CAACmD,kBAAkB,CAAC,OAAO,EAAE;IACtCC,OAAO,EAAE,MAAM;IACf/D,IAAI,EAAE,SAAS;IACf+B,KAAK,EAAE,MAAM;IACbI,GAAG,EAAE;EACP,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM6B,oBAAoB,GAAIrD,IAAU,IAAa;EAC1D,OAAOA,IAAI,CAACsD,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,YAAY,GAAIrC,KAAa,IAAa;EACrD,MAAMsC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;EACD,OAAOA,UAAU,CAACtC,KAAK,CAAC;AAC1B,CAAC;AAED,OAAO,MAAMuC,UAAU,GAAInC,GAAW,IAAa;EACjD,MAAMoC,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;EAC/F,OAAOA,QAAQ,CAACpC,GAAG,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}