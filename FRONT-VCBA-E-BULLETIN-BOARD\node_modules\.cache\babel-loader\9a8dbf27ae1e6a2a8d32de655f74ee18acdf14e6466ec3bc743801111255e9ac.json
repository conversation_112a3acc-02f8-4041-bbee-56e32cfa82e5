{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n  key: \"13o1zl\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}]];\nconst Globe = createLucideIcon(\"globe\", __iconNode);\nexport { __iconNode, Globe as default };\n//# sourceMappingURL=globe.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}