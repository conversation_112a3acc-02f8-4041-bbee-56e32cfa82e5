{"ast": null, "code": "/**\n * Form utility functions for consistent form handling across the application\n */\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (formData, files = [], options = {}) => {\n  const {\n    skipScheduledDate = true,\n    fileFieldName = 'images',\n    isUpdate = false\n  } = options;\n  console.log('🔧 createFormData called with isUpdate:', isUpdate);\n  console.log('🔧 formData:', formData);\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip empty values for updates to avoid \"No valid fields to update\" error\n    if (isUpdate && (value === '' || value === null || value === undefined)) {\n      return;\n    }\n    if (key === 'category_id' && typeof value === 'string') {\n      const parsedValue = parseInt(value);\n      if (!isNaN(parsedValue) && parsedValue > 0) {\n        formDataToSubmit.append(key, parsedValue.toString());\n      } else if (!isUpdate) {\n        // For creation, category_id is required\n        throw new Error('Category is required');\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and is a valid number\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue) && parsedValue > 0) {\n          formDataToSubmit.append(key, parsedValue.toString());\n        }\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n    } else if (value !== null && value !== undefined && value !== '') {\n      formDataToSubmit.append(key, value.toString());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach(file => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (formData, rules = {}) => {\n  const errors = {};\n  const {\n    required = [],\n    maxLength = {},\n    custom = {}\n  } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || typeof value === 'string' && !value.trim()) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: {\n    title: 255\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if ((formData === null || formData === void 0 ? void 0 : formData.status) === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes, decimals = 1) => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (file, options = {}) => {\n  const {\n    maxSize = 5 * 1024 * 1024,\n    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']\n  } = options;\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["createFormData", "formData", "files", "options", "skipScheduledDate", "fileFieldName", "isUpdate", "console", "log", "formDataToSubmit", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "parsedValue", "parseInt", "isNaN", "append", "toString", "Error", "trim", "status", "length", "file", "validate<PERSON><PERSON><PERSON><PERSON>s", "rules", "errors", "required", "max<PERSON><PERSON><PERSON>", "custom", "field", "replace", "max", "validator", "error", "announcementValidationRules", "title", "scheduled_publish_at", "formatFileSize", "bytes", "decimals", "k", "dm", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "validateFile", "maxSize", "allowedTypes", "includes", "type", "join", "size"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["/**\n * Form utility functions for consistent form handling across the application\n */\n\nexport interface FormField {\n  [key: string]: string | number | boolean | null | undefined;\n}\n\n/**\n * Creates FormData from form fields and files\n * @param formData - Object containing form field values\n * @param files - Array of files to append\n * @param options - Configuration options\n */\nexport const createFormData = (\n  formData: FormField,\n  files: File[] = [],\n  options: {\n    skipScheduledDate?: boolean;\n    fileFieldName?: string;\n    isUpdate?: boolean;\n  } = {}\n): FormData => {\n  const { skipScheduledDate = true, fileFieldName = 'images', isUpdate = false } = options;\n  console.log('🔧 createFormData called with isUpdate:', isUpdate);\n  console.log('🔧 formData:', formData);\n  const formDataToSubmit = new FormData();\n\n  // Add all form fields\n  Object.entries(formData).forEach(([key, value]) => {\n    // Skip empty values for updates to avoid \"No valid fields to update\" error\n    if (isUpdate && (value === '' || value === null || value === undefined)) {\n      return;\n    }\n\n    if (key === 'category_id' && typeof value === 'string') {\n      const parsedValue = parseInt(value);\n      if (!isNaN(parsedValue) && parsedValue > 0) {\n        formDataToSubmit.append(key, parsedValue.toString());\n      } else if (!isUpdate) {\n        // For creation, category_id is required\n        throw new Error('Category is required');\n      }\n    } else if (key === 'subcategory_id' && typeof value === 'string') {\n      // Only append subcategory_id if it's not empty and is a valid number\n      if (value.trim() !== '') {\n        const parsedValue = parseInt(value);\n        if (!isNaN(parsedValue) && parsedValue > 0) {\n          formDataToSubmit.append(key, parsedValue.toString());\n        }\n      }\n    } else if (key === 'scheduled_publish_at' && skipScheduledDate && formData.status !== 'scheduled') {\n      // Skip scheduled_publish_at if not scheduling\n      return;\n    } else if (typeof value === 'boolean') {\n      formDataToSubmit.append(key, value ? '1' : '0');\n    } else if (value !== null && value !== undefined && value !== '') {\n      formDataToSubmit.append(key, value.toString());\n    }\n  });\n\n  // Add files\n  if (files.length > 0) {\n    files.forEach((file) => {\n      formDataToSubmit.append(fileFieldName, file);\n    });\n  }\n\n  return formDataToSubmit;\n};\n\n/**\n * Validates common form fields\n * @param formData - Form data to validate\n * @param rules - Validation rules\n */\nexport const validateFormFields = (\n  formData: FormField,\n  rules: {\n    required?: string[];\n    maxLength?: { [key: string]: number };\n    custom?: { [key: string]: (value: any) => string | null };\n  } = {}\n): Record<string, string> => {\n  const errors: Record<string, string> = {};\n  const { required = [], maxLength = {}, custom = {} } = rules;\n\n  // Check required fields\n  required.forEach(field => {\n    const value = formData[field];\n    if (!value || (typeof value === 'string' && !value.trim())) {\n      errors[field] = `${field.replace('_', ' ')} is required`;\n    }\n  });\n\n  // Check max length\n  Object.entries(maxLength).forEach(([field, max]) => {\n    const value = formData[field];\n    if (typeof value === 'string' && value.length > max) {\n      errors[field] = `${field.replace('_', ' ')} must be less than ${max} characters`;\n    }\n  });\n\n  // Apply custom validation\n  Object.entries(custom).forEach(([field, validator]) => {\n    const value = formData[field];\n    const error = validator(value);\n    if (error) {\n      errors[field] = error;\n    }\n  });\n\n  return errors;\n};\n\n/**\n * Common validation rules for announcements\n */\nexport const announcementValidationRules = {\n  required: ['title', 'content', 'category_id'],\n  maxLength: { title: 255 },\n  custom: {\n    scheduled_publish_at: (value: any, formData?: FormField) => {\n      if (formData?.status === 'scheduled' && !value) {\n        return 'Scheduled publish date is required for scheduled announcements';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Formats file size for display\n * @param bytes - File size in bytes\n * @param decimals - Number of decimal places\n */\nexport const formatFileSize = (bytes: number, decimals: number = 1): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n};\n\n/**\n * Validates file type and size\n * @param file - File to validate\n * @param options - Validation options\n */\nexport const validateFile = (\n  file: File,\n  options: {\n    maxSize?: number;\n    allowedTypes?: string[];\n  } = {}\n): string | null => {\n  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] } = options;\n\n  if (!allowedTypes.includes(file.type)) {\n    return `File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`;\n  }\n\n  if (file.size > maxSize) {\n    return `File size ${formatFileSize(file.size)} exceeds maximum allowed size of ${formatFileSize(maxSize)}`;\n  }\n\n  return null;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAC5BC,QAAmB,EACnBC,KAAa,GAAG,EAAE,EAClBC,OAIC,GAAG,CAAC,CAAC,KACO;EACb,MAAM;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,aAAa,GAAG,QAAQ;IAAEC,QAAQ,GAAG;EAAM,CAAC,GAAGH,OAAO;EACxFI,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEF,QAAQ,CAAC;EAChEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEP,QAAQ,CAAC;EACrC,MAAMQ,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAEvC;EACAC,MAAM,CAACC,OAAO,CAACX,QAAQ,CAAC,CAACY,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD;IACA,IAAIT,QAAQ,KAAKS,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;MACvE;IACF;IAEA,IAAIF,GAAG,KAAK,aAAa,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MACtD,MAAME,WAAW,GAAGC,QAAQ,CAACH,KAAK,CAAC;MACnC,IAAI,CAACI,KAAK,CAACF,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;QAC1CR,gBAAgB,CAACW,MAAM,CAACN,GAAG,EAAEG,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;MACtD,CAAC,MAAM,IAAI,CAACf,QAAQ,EAAE;QACpB;QACA,MAAM,IAAIgB,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,MAAM,IAAIR,GAAG,KAAK,gBAAgB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAIA,KAAK,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,MAAMN,WAAW,GAAGC,QAAQ,CAACH,KAAK,CAAC;QACnC,IAAI,CAACI,KAAK,CAACF,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;UAC1CR,gBAAgB,CAACW,MAAM,CAACN,GAAG,EAAEG,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QACtD;MACF;IACF,CAAC,MAAM,IAAIP,GAAG,KAAK,sBAAsB,IAAIV,iBAAiB,IAAIH,QAAQ,CAACuB,MAAM,KAAK,WAAW,EAAE;MACjG;MACA;IACF,CAAC,MAAM,IAAI,OAAOT,KAAK,KAAK,SAAS,EAAE;MACrCN,gBAAgB,CAACW,MAAM,CAACN,GAAG,EAAEC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;IACjD,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MAChEN,gBAAgB,CAACW,MAAM,CAACN,GAAG,EAAEC,KAAK,CAACM,QAAQ,CAAC,CAAC,CAAC;IAChD;EACF,CAAC,CAAC;;EAEF;EACA,IAAInB,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;IACpBvB,KAAK,CAACW,OAAO,CAAEa,IAAI,IAAK;MACtBjB,gBAAgB,CAACW,MAAM,CAACf,aAAa,EAAEqB,IAAI,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA,OAAOjB,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkB,kBAAkB,GAAGA,CAChC1B,QAAmB,EACnB2B,KAIC,GAAG,CAAC,CAAC,KACqB;EAC3B,MAAMC,MAA8B,GAAG,CAAC,CAAC;EACzC,MAAM;IAAEC,QAAQ,GAAG,EAAE;IAAEC,SAAS,GAAG,CAAC,CAAC;IAAEC,MAAM,GAAG,CAAC;EAAE,CAAC,GAAGJ,KAAK;;EAE5D;EACAE,QAAQ,CAACjB,OAAO,CAACoB,KAAK,IAAI;IACxB,MAAMlB,KAAK,GAAGd,QAAQ,CAACgC,KAAK,CAAC;IAC7B,IAAI,CAAClB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACQ,IAAI,CAAC,CAAE,EAAE;MAC1DM,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc;IAC1D;EACF,CAAC,CAAC;;EAEF;EACAvB,MAAM,CAACC,OAAO,CAACmB,SAAS,CAAC,CAAClB,OAAO,CAAC,CAAC,CAACoB,KAAK,EAAEE,GAAG,CAAC,KAAK;IAClD,MAAMpB,KAAK,GAAGd,QAAQ,CAACgC,KAAK,CAAC;IAC7B,IAAI,OAAOlB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACU,MAAM,GAAGU,GAAG,EAAE;MACnDN,MAAM,CAACI,KAAK,CAAC,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,sBAAsBC,GAAG,aAAa;IAClF;EACF,CAAC,CAAC;;EAEF;EACAxB,MAAM,CAACC,OAAO,CAACoB,MAAM,CAAC,CAACnB,OAAO,CAAC,CAAC,CAACoB,KAAK,EAAEG,SAAS,CAAC,KAAK;IACrD,MAAMrB,KAAK,GAAGd,QAAQ,CAACgC,KAAK,CAAC;IAC7B,MAAMI,KAAK,GAAGD,SAAS,CAACrB,KAAK,CAAC;IAC9B,IAAIsB,KAAK,EAAE;MACTR,MAAM,CAACI,KAAK,CAAC,GAAGI,KAAK;IACvB;EACF,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,2BAA2B,GAAG;EACzCR,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;EAC7CC,SAAS,EAAE;IAAEQ,KAAK,EAAE;EAAI,CAAC;EACzBP,MAAM,EAAE;IACNQ,oBAAoB,EAAEA,CAACzB,KAAU,EAAEd,QAAoB,KAAK;MAC1D,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,MAAM,MAAK,WAAW,IAAI,CAACT,KAAK,EAAE;QAC9C,OAAO,gEAAgE;MACzE;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,cAAc,GAAGA,CAACC,KAAa,EAAEC,QAAgB,GAAG,CAAC,KAAa;EAC7E,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAME,CAAC,GAAG,IAAI;EACd,MAAMC,EAAE,GAAGF,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;EACtC,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACxC,GAAG,CAACkC,KAAK,CAAC,GAAGM,IAAI,CAACxC,GAAG,CAACoC,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACR,KAAK,GAAGM,IAAI,CAACG,GAAG,CAACP,CAAC,EAAEG,CAAC,CAAC,EAAEK,OAAO,CAACP,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAGA,CAC1B3B,IAAU,EACVvB,OAGC,GAAG,CAAC,CAAC,KACY;EAClB,MAAM;IAAEmD,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAAEC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAE,CAAC,GAAGpD,OAAO;EAEpH,IAAI,CAACoD,YAAY,CAACC,QAAQ,CAAC9B,IAAI,CAAC+B,IAAI,CAAC,EAAE;IACrC,OAAO,aAAa/B,IAAI,CAAC+B,IAAI,qCAAqCF,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;EAC7F;EAEA,IAAIhC,IAAI,CAACiC,IAAI,GAAGL,OAAO,EAAE;IACvB,OAAO,aAAab,cAAc,CAACf,IAAI,CAACiC,IAAI,CAAC,oCAAoClB,cAAc,CAACa,OAAO,CAAC,EAAE;EAC5G;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}