{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"line\", {\n  x1: \"21.17\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"a0cw5f\"\n}], [\"line\", {\n  x1: \"3.95\",\n  x2: \"8.54\",\n  y1: \"6.06\",\n  y2: \"14\",\n  key: \"1kftof\"\n}], [\"line\", {\n  x1: \"10.88\",\n  x2: \"15.46\",\n  y1: \"21.94\",\n  y2: \"14\",\n  key: \"1ymyh8\"\n}]];\nconst Chrome = createLucideIcon(\"chrome\", __iconNode);\nexport { __iconNode, Chrome as default };\n//# sourceMappingURL=chrome.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}