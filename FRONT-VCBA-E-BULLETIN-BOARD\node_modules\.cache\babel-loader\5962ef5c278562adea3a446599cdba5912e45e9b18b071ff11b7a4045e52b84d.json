{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 6v6h4l-7 7-7-7h4V6h6z\",\n  key: \"1thax2\"\n}]];\nconst ArrowBigDown = createLucideIcon(\"arrow-big-down\", __iconNode);\nexport { __iconNode, ArrowBigDown as default };\n//# sourceMappingURL=arrow-big-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}