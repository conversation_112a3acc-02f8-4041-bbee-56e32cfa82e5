{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1\",\n  key: \"10bnsj\"\n}], [\"path\", {\n  d: \"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9\",\n  key: \"1eqmu1\"\n}], [\"path\", {\n  d: \"M21 21v-2h-4\",\n  key: \"14zm7j\"\n}], [\"path\", {\n  d: \"M3 5h4V3\",\n  key: \"z442eg\"\n}], [\"path\", {\n  d: \"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3\",\n  key: \"ebdjd7\"\n}]];\nconst Cable = createLucideIcon(\"cable\", __iconNode);\nexport { __iconNode, Cable as default };\n//# sourceMappingURL=cable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}