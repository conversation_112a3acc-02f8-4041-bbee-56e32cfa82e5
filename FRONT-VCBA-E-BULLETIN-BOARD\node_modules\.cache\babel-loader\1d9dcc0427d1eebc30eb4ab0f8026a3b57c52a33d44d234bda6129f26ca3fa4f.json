{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAdminAuth}from'../../contexts/AdminAuthContext';import{useStudentAuth}from'../../contexts/StudentAuthContext';import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";// Admin Public Route Component\nconst AdminPublicRoute=_ref=>{let{children,restricted=false}=_ref;const{isAuthenticated,user,isLoading}=useAdminAuth();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"})});}// If route is restricted and user is authenticated, redirect to dashboard\nif(restricted&&isAuthenticated&&user){return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Student Public Route Component\nconst StudentPublicRoute=_ref2=>{let{children,restricted=false}=_ref2;const{isAuthenticated,user,isLoading}=useStudentAuth();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"})});}// If route is restricted and user is authenticated, redirect to dashboard\nif(restricted&&isAuthenticated&&user){return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};// Main Public Route Component\nconst PublicRoute=props=>{const location=useLocation();// Determine which public route component to use based on path\nif(location.pathname.startsWith('/admin')){return/*#__PURE__*/_jsx(AdminPublicRoute,_objectSpread({},props));}else if(location.pathname.startsWith('/student')){return/*#__PURE__*/_jsx(StudentPublicRoute,_objectSpread({},props));}// For other routes, just render children\nreturn/*#__PURE__*/_jsx(_Fragment,{children:props.children});};export default PublicRoute;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}