{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17V3\",\n  key: \"1cwfxf\"\n}], [\"path\", {\n  d: \"m6 11 6 6 6-6\",\n  key: \"12ii2o\"\n}], [\"path\", {\n  d: \"M19 21H5\",\n  key: \"150jfl\"\n}]];\nconst ArrowDownToLine = createLucideIcon(\"arrow-down-to-line\", __iconNode);\nexport { __iconNode, ArrowDownToLine as default };\n//# sourceMappingURL=arrow-down-to-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}