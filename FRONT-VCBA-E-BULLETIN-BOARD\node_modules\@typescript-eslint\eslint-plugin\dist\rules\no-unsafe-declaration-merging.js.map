{"version": 3, "file": "no-unsafe-declaration-merging.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-declaration-merging.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAE1D,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,KAAK;SAC5B;QACD,QAAQ,EAAE;YACR,aAAa,EACX,4DAA4D;SAC/D;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,sBAAsB,CAC7B,KAAY,EACZ,IAAyB,EACzB,UAA0B;YAE1B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;aACR;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;gBACpB,OAAO;aACR;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE;gBAClD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,eAAe;iBAC3B,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAI,CAAC,EAAE,EAAE;oBACX,gFAAgF;oBAChF,qEAAqE;oBACrE,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;oBAC9C,IAAI,YAAY,IAAI,IAAI,EAAE;wBACxB,OAAO;qBACR;oBAED,sBAAsB,CACpB,YAAY,EACZ,IAAI,CAAC,EAAE,EACP,sBAAc,CAAC,sBAAsB,CACtC,CAAC;iBACH;YACH,CAAC;YACD,sBAAsB,CAAC,IAAI;gBACzB,sBAAsB,CACpB,OAAO,CAAC,QAAQ,EAAE,EAClB,IAAI,CAAC,EAAE,EACP,sBAAc,CAAC,gBAAgB,CAChC,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}