{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M21 17V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h11Z\",\n  key: \"kg77oy\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"M15 22v-4a2 2 0 0 1 2-2h4\",\n  key: \"1gnbqr\"\n}]];\nconst CalendarFold = createLucideIcon(\"calendar-fold\", __iconNode);\nexport { __iconNode, CalendarFold as default };\n//# sourceMappingURL=calendar-fold.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}