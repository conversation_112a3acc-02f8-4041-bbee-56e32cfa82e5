{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"4\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"1wwnby\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"14\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1fe6j6\"\n}], [\"path\", {\n  d: \"M4 2v20\",\n  key: \"gtpd5x\"\n}], [\"path\", {\n  d: \"M14 2v20\",\n  key: \"tg6bpw\"\n}]];\nconst AlignHorizontalDistributeStart = createLucideIcon(\"align-horizontal-distribute-start\", __iconNode);\nexport { __iconNode, AlignHorizontalDistributeStart as default };\n//# sourceMappingURL=align-horizontal-distribute-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}