{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 18 6-6-6-6\",\n  key: \"mthhwq\"\n}]];\nconst ChevronRight = createLucideIcon(\"chevron-right\", __iconNode);\nexport { __iconNode, ChevronRight as default };\n//# sourceMappingURL=chevron-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}