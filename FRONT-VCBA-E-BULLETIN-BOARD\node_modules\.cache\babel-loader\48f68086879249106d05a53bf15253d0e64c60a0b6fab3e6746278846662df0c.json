{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/common/ProfilePictureUpload';\nimport StudentProfileService from '../../services/studentProfileService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentSettings = () => {\n  _s();\n  const {\n    user\n  } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const tabs = [{\n    key: 'profile',\n    label: 'Profile',\n    icon: User\n  }, {\n    key: 'notifications',\n    label: 'Notifications',\n    icon: Bell\n  }, {\n    key: 'privacy',\n    label: 'Privacy',\n    icon: Lock\n  }];\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n\n  // Handle profile picture selection\n  const handleProfilePictureSelect = file => {\n    setSelectedFile(file);\n    setProfilePictureError(null);\n  };\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async () => {\n    if (!selectedFile) return;\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n    try {\n      const response = await StudentProfileService.uploadProfilePicture(selectedFile);\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        setSelectedFile(null);\n        // You might want to refresh the user context here\n      } else {\n        var _response$error;\n        setProfilePictureError(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || 'Failed to upload profile picture');\n      }\n    } catch (error) {\n      setProfilePictureError(error.message || 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n    try {\n      const response = await StudentProfileService.deleteProfilePicture();\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // You might want to refresh the user context here\n      } else {\n        var _response$error2;\n        setProfilePictureError(((_response$error2 = response.error) === null || _response$error2 === void 0 ? void 0 : _response$error2.message) || 'Failed to remove profile picture');\n      }\n    } catch (error) {\n      setProfilePictureError(error.message || 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          borderRadius: '16px',\n          padding: '2rem',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(10px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#1e40af',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Profile Picture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n          currentImageUrl: user === null || user === void 0 ? void 0 : user.profilePictureUrl,\n          onImageSelect: handleProfilePictureSelect,\n          onUpload: handleProfilePictureUpload,\n          onRemove: handleProfilePictureRemove,\n          loading: profilePictureLoading,\n          userInitials: `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`,\n          gradientColors: ['#3b82f6', '#fbbf24']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 9\n        }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem',\n            backgroundColor: 'rgba(240, 253, 244, 0.9)',\n            border: '1px solid rgba(187, 247, 208, 0.8)',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            color: '#15803d',\n            fontSize: '0.875rem',\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16,\n            style: {\n              marginRight: '8px',\n              flexShrink: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), successMessage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), profilePictureError && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem',\n            backgroundColor: 'rgba(254, 242, 242, 0.9)',\n            border: '1px solid rgba(254, 202, 202, 0.8)',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            color: '#dc2626',\n            fontSize: '0.875rem'\n          },\n          children: profilePictureError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          borderRadius: '16px',\n          padding: '2rem',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(10px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#1e40af',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Personal Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.firstName,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.lastName,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridColumn: '1 / -1'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              defaultValue: user === null || user === void 0 ? void 0 : user.email,\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                margin: '0.5rem 0 0 0'\n              },\n              children: \"Email address cannot be changed. Contact admin for assistance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Student ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"VCBA-2025-001\",\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                color: '#374151',\n                fontWeight: '500'\n              },\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              defaultValue: \"BS Business Administration\",\n              disabled: true,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '2rem',\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 2rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            },\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: 'none',\n              border: '1px solid #e0f2fe',\n              borderRadius: '8px',\n              padding: '0.75rem 2rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 5\n    }, this);\n  };\n  const renderNotificationSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Notification Preferences\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Email Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive announcements via email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Push Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Get instant notifications on your device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Alert Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Receive urgent alerts and important notices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n  const renderPrivacySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      },\n      children: \"Privacy Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Profile Visibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Allow other students to see your profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            defaultChecked: true,\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.25rem'\n            },\n            children: \"Activity Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.875rem'\n            },\n            children: \"Show when you're online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block',\n            width: '60px',\n            height: '34px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            style: {\n              opacity: 0,\n              width: 0,\n              height: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 419,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)' : 'transparent',\n            color: activeTab === tab.key ? 'white' : '#6b7280',\n            border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem',\n            cursor: 'pointer',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 18,\n            color: activeTab === tab.key ? '#22c55e' : '#6b7280'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), renderContent()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentSettings, \"3p2LfGkLozTl42edp2ytAptDCcw=\", false, function () {\n  return [useStudentAuth];\n});\n_c = StudentSettings;\nexport default StudentSettings;\nvar _c;\n$RefreshReg$(_c, \"StudentSettings\");", "map": {"version": 3, "names": ["React", "useState", "useStudentAuth", "User", "Bell", "Lock", "CheckCircle", "ProfilePictureUpload", "StudentProfileService", "jsxDEV", "_jsxDEV", "StudentSettings", "_s", "user", "activeTab", "setActiveTab", "profilePictureLoading", "setProfilePictureLoading", "profilePictureError", "setProfilePictureError", "successMessage", "setSuccessMessage", "selectedFile", "setSelectedFile", "tabs", "key", "label", "icon", "useEffect", "timer", "setTimeout", "clearTimeout", "handleProfilePictureSelect", "file", "handleProfilePictureUpload", "response", "uploadProfilePicture", "success", "_response$error", "error", "message", "handleProfilePictureRemove", "deleteProfilePicture", "_response$error2", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "background", "borderRadius", "padding", "border", "<PERSON><PERSON>ilter", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentImageUrl", "profilePictureUrl", "onImageSelect", "onUpload", "onRemove", "loading", "userInitials", "firstName", "char<PERSON>t", "lastName", "gradientColors", "marginTop", "backgroundColor", "alignItems", "size", "marginRight", "flexShrink", "gridTemplateColumns", "marginBottom", "type", "defaultValue", "width", "outline", "gridColumn", "email", "disabled", "cursor", "renderNotificationSettings", "boxShadow", "justifyContent", "position", "height", "defaultChecked", "opacity", "top", "left", "right", "bottom", "transition", "renderPrivacySettings", "renderContent", "flexWrap", "map", "tab", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentSettings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useStudentAuth } from '../../contexts/StudentAuthContext';\nimport { User, Bell, Lock, CheckCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/common/ProfilePictureUpload';\nimport StudentProfileService from '../../services/studentProfileService';\n\nconst StudentSettings: React.FC = () => {\n  const { user } = useStudentAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'privacy'>('profile');\n  const [profilePictureLoading, setProfilePictureLoading] = useState(false);\n  const [profilePictureError, setProfilePictureError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  const tabs = [\n    { key: 'profile', label: 'Profile', icon: User },\n    { key: 'notifications', label: 'Notifications', icon: Bell },\n    { key: 'privacy', label: 'Privacy', icon: Lock }\n  ];\n\n  // Clear messages after 5 seconds\n  React.useEffect(() => {\n    if (successMessage || profilePictureError) {\n      const timer = setTimeout(() => {\n        setSuccessMessage(null);\n        setProfilePictureError(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, profilePictureError]);\n\n  // Handle profile picture selection\n  const handleProfilePictureSelect = (file: File | null) => {\n    setSelectedFile(file);\n    setProfilePictureError(null);\n  };\n\n  // Handle profile picture upload\n  const handleProfilePictureUpload = async () => {\n    if (!selectedFile) return;\n\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n\n    try {\n      const response = await StudentProfileService.uploadProfilePicture(selectedFile);\n      if (response.success) {\n        setSuccessMessage('Profile picture uploaded successfully!');\n        setSelectedFile(null);\n        // You might want to refresh the user context here\n      } else {\n        setProfilePictureError(response.error?.message || 'Failed to upload profile picture');\n      }\n    } catch (error: any) {\n      setProfilePictureError(error.message || 'Failed to upload profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  // Handle profile picture removal\n  const handleProfilePictureRemove = async () => {\n    setProfilePictureLoading(true);\n    setProfilePictureError(null);\n    setSuccessMessage(null);\n\n    try {\n      const response = await StudentProfileService.deleteProfilePicture();\n      if (response.success) {\n        setSuccessMessage('Profile picture removed successfully!');\n        // You might want to refresh the user context here\n      } else {\n        setProfilePictureError(response.error?.message || 'Failed to remove profile picture');\n      }\n    } catch (error: any) {\n      setProfilePictureError(error.message || 'Failed to remove profile picture');\n    } finally {\n      setProfilePictureLoading(false);\n    }\n  };\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Picture */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Profile Picture\n        </h3>\n\n        <ProfilePictureUpload\n          currentImageUrl={user?.profilePictureUrl}\n          onImageSelect={handleProfilePictureSelect}\n          onUpload={handleProfilePictureUpload}\n          onRemove={handleProfilePictureRemove}\n          loading={profilePictureLoading}\n          userInitials={`${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`}\n          gradientColors={['#3b82f6', '#fbbf24']}\n        />\n\n        {/* Success/Error Messages */}\n        {successMessage && (\n          <div style={{\n            marginTop: '1rem',\n            backgroundColor: 'rgba(240, 253, 244, 0.9)',\n            border: '1px solid rgba(187, 247, 208, 0.8)',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            color: '#15803d',\n            fontSize: '0.875rem',\n            display: 'flex',\n            alignItems: 'center'\n          }}>\n            <CheckCircle size={16} style={{ marginRight: '8px', flexShrink: 0 }} />\n            {successMessage}\n          </div>\n        )}\n\n        {profilePictureError && (\n          <div style={{\n            marginTop: '1rem',\n            backgroundColor: 'rgba(254, 242, 242, 0.9)',\n            border: '1px solid rgba(254, 202, 202, 0.8)',\n            borderRadius: '6px',\n            padding: '0.75rem',\n            color: '#dc2626',\n            fontSize: '0.875rem'\n          }}>\n            {profilePictureError}\n          </div>\n        )}\n      </div>\n\n      {/* Personal Information */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.1)',\n        borderRadius: '16px',\n        padding: '2rem',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#1e40af',\n          fontSize: '1.25rem',\n          fontWeight: '600'\n        }}>\n          Personal Information\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>\n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              First Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.firstName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Last Name\n            </label>\n            <input\n              type=\"text\"\n              defaultValue={user?.lastName}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n          \n          <div style={{ gridColumn: '1 / -1' }}>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              defaultValue={user?.email}\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n            <p style={{\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              margin: '0.5rem 0 0 0'\n            }}>\n              Email address cannot be changed. Contact admin for assistance.\n            </p>\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Student ID\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"VCBA-2025-001\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n          \n          <div>\n            <label style={{\n              display: 'block',\n              marginBottom: '0.5rem',\n              color: '#374151',\n              fontWeight: '500'\n            }}>\n              Course\n            </label>\n            <input\n              type=\"text\"\n              defaultValue=\"BS Business Administration\"\n              disabled\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #e0f2fe',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none',\n                background: '#f8fafc',\n                color: '#6b7280'\n              }}\n            />\n          </div>\n        </div>\n        \n        <div style={{ marginTop: '2rem', display: 'flex', gap: '1rem' }}>\n          <button style={{\n            background: 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          }}>\n            Save Changes\n          </button>\n          <button style={{\n            background: 'none',\n            border: '1px solid #e0f2fe',\n            borderRadius: '8px',\n            padding: '0.75rem 2rem',\n            cursor: 'pointer',\n            color: '#6b7280'\n          }}>\n            Cancel\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Notification Preferences\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Email Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive announcements via email\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Push Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Get instant notifications on your device\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Alert Notifications\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Receive urgent alerts and important notices\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPrivacySettings = () => (\n    <div style={{\n      background: 'white',\n      borderRadius: '16px',\n      padding: '2rem',\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n      border: '1px solid #e0f2fe'\n    }}>\n      <h3 style={{\n        margin: '0 0 1.5rem 0',\n        color: '#1e40af',\n        fontSize: '1.25rem',\n        fontWeight: '600'\n      }}>\n        Privacy Settings\n      </h3>\n      \n      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Profile Visibility\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Allow other students to see your profile\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#3b82f6',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n        \n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: '500', color: '#374151', marginBottom: '0.25rem' }}>\n              Activity Status\n            </div>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Show when you're online\n            </div>\n          </div>\n          <label style={{ position: 'relative', display: 'inline-block', width: '60px', height: '34px' }}>\n            <input type=\"checkbox\" style={{ opacity: 0, width: 0, height: 0 }} />\n            <span style={{\n              position: 'absolute',\n              cursor: 'pointer',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: '#ccc',\n              transition: '0.4s',\n              borderRadius: '34px'\n            }} />\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'privacy':\n        return renderPrivacySettings();\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div style={{ width: '100%', padding: '2rem' }}>\n      {/* Tabs */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e0f2fe'\n      }}>\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {tabs.map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                background: activeTab === tab.key \n                  ? 'linear-gradient(135deg, #3b82f6 0%, #fbbf24 100%)'\n                  : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#6b7280',\n                border: activeTab === tab.key ? 'none' : '1px solid #e0f2fe',\n                borderRadius: '8px',\n                padding: '0.75rem 1.5rem',\n                cursor: 'pointer',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              <tab.icon size={18} color={activeTab === tab.key ? '#22c55e' : '#6b7280'} />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Content */}\n      {renderContent()}\n    </div>\n  );\n};\n\nexport default StudentSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,QAAQ,cAAc;AAC5D,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,qBAAqB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGX,cAAc,CAAC,CAAC;EACjC,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAA0C,SAAS,CAAC;EAC9F,MAAM,CAACe,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACiB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAc,IAAI,CAAC;EAEnE,MAAMuB,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAExB;EAAK,CAAC,EAChD;IAAEsB,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAEvB;EAAK,CAAC,EAC5D;IAAEqB,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEtB;EAAK,CAAC,CACjD;;EAED;EACAL,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIR,cAAc,IAAIF,mBAAmB,EAAE;MACzC,MAAMW,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BT,iBAAiB,CAAC,IAAI,CAAC;QACvBF,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMY,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACT,cAAc,EAAEF,mBAAmB,CAAC,CAAC;;EAEzC;EACA,MAAMc,0BAA0B,GAAIC,IAAiB,IAAK;IACxDV,eAAe,CAACU,IAAI,CAAC;IACrBd,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMe,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAACZ,YAAY,EAAE;IAEnBL,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM3B,qBAAqB,CAAC4B,oBAAoB,CAACd,YAAY,CAAC;MAC/E,IAAIa,QAAQ,CAACE,OAAO,EAAE;QACpBhB,iBAAiB,CAAC,wCAAwC,CAAC;QAC3DE,eAAe,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM;QAAA,IAAAe,eAAA;QACLnB,sBAAsB,CAAC,EAAAmB,eAAA,GAAAH,QAAQ,CAACI,KAAK,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,OAAO,KAAI,kCAAkC,CAAC;MACvF;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBpB,sBAAsB,CAACoB,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7E,CAAC,SAAS;MACRvB,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMwB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7CxB,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAM3B,qBAAqB,CAACkC,oBAAoB,CAAC,CAAC;MACnE,IAAIP,QAAQ,CAACE,OAAO,EAAE;QACpBhB,iBAAiB,CAAC,uCAAuC,CAAC;QAC1D;MACF,CAAC,MAAM;QAAA,IAAAsB,gBAAA;QACLxB,sBAAsB,CAAC,EAAAwB,gBAAA,GAAAR,QAAQ,CAACI,KAAK,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBH,OAAO,KAAI,kCAAkC,CAAC;MACvF;IACF,CAAC,CAAC,OAAOD,KAAU,EAAE;MACnBpB,sBAAsB,CAACoB,KAAK,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7E,CAAC,SAAS;MACRvB,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5BpC,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpEzC,OAAA;QAAKqC,KAAK,EAAE;UACVK,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE;QAClB,CAAE;QAAAL,QAAA,gBACAzC,OAAA;UAAIqC,KAAK,EAAE;YACTU,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtD,OAAA,CAACH,oBAAoB;UACnB0D,eAAe,EAAEpD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,iBAAkB;UACzCC,aAAa,EAAEnC,0BAA2B;UAC1CoC,QAAQ,EAAElC,0BAA2B;UACrCmC,QAAQ,EAAE5B,0BAA2B;UACrC6B,OAAO,EAAEtD,qBAAsB;UAC/BuD,YAAY,EAAE,GAAG,CAAA1D,IAAI,aAAJA,IAAI,wBAAAgC,eAAA,GAAJhC,IAAI,CAAE2D,SAAS,cAAA3B,eAAA,uBAAfA,eAAA,CAAiB4B,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA5D,IAAI,aAAJA,IAAI,wBAAAiC,cAAA,GAAJjC,IAAI,CAAE6D,QAAQ,cAAA5B,cAAA,uBAAdA,cAAA,CAAgB2B,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAG;UACtFE,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EAGD5C,cAAc,iBACbV,OAAA;UAAKqC,KAAK,EAAE;YACV6B,SAAS,EAAE,MAAM;YACjBC,eAAe,EAAE,0BAA0B;YAC3CtB,MAAM,EAAE,oCAAoC;YAC5CF,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,SAAS;YAClBI,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,UAAU;YACpBX,OAAO,EAAE,MAAM;YACf8B,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,gBACAzC,OAAA,CAACJ,WAAW;YAACyE,IAAI,EAAE,EAAG;YAAChC,KAAK,EAAE;cAAEiC,WAAW,EAAE,KAAK;cAAEC,UAAU,EAAE;YAAE;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtE5C,cAAc;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACN,EAEA9C,mBAAmB,iBAClBR,OAAA;UAAKqC,KAAK,EAAE;YACV6B,SAAS,EAAE,MAAM;YACjBC,eAAe,EAAE,0BAA0B;YAC3CtB,MAAM,EAAE,oCAAoC;YAC5CF,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,SAAS;YAClBI,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,EACCjC;QAAmB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtD,OAAA;QAAKqC,KAAK,EAAE;UACVK,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE;QAClB,CAAE;QAAAL,QAAA,gBACAzC,OAAA;UAAIqC,KAAK,EAAE;YACTU,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtD,OAAA;UAAKqC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEkC,mBAAmB,EAAE,SAAS;YAAEhC,GAAG,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAC7EzC,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChBmC,YAAY,EAAE,QAAQ;gBACtBzB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE0E,IAAI,EAAC,MAAM;cACXC,YAAY,EAAExE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,SAAU;cAC9BzB,KAAK,EAAE;gBACLuC,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChB4B,OAAO,EAAE;cACX;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChBmC,YAAY,EAAE,QAAQ;gBACtBzB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE0E,IAAI,EAAC,MAAM;cACXC,YAAY,EAAExE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,QAAS;cAC7B3B,KAAK,EAAE;gBACLuC,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChB4B,OAAO,EAAE;cACX;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAKqC,KAAK,EAAE;cAAEyC,UAAU,EAAE;YAAS,CAAE;YAAArC,QAAA,gBACnCzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChBmC,YAAY,EAAE,QAAQ;gBACtBzB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE0E,IAAI,EAAC,OAAO;cACZC,YAAY,EAAExE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,KAAM;cAC1BC,QAAQ;cACR3C,KAAK,EAAE;gBACLuC,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChB4B,OAAO,EAAE,MAAM;gBACfnC,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFtD,OAAA;cAAGqC,KAAK,EAAE;gBACRY,QAAQ,EAAE,SAAS;gBACnBD,KAAK,EAAE,SAAS;gBAChBD,MAAM,EAAE;cACV,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChBmC,YAAY,EAAE,QAAQ;gBACtBzB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE0E,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,eAAe;cAC5BK,QAAQ;cACR3C,KAAK,EAAE;gBACLuC,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChB4B,OAAO,EAAE,MAAM;gBACfnC,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOqC,KAAK,EAAE;gBACZC,OAAO,EAAE,OAAO;gBAChBmC,YAAY,EAAE,QAAQ;gBACtBzB,KAAK,EAAE,SAAS;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE0E,IAAI,EAAC,MAAM;cACXC,YAAY,EAAC,4BAA4B;cACzCK,QAAQ;cACR3C,KAAK,EAAE;gBACLuC,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBM,QAAQ,EAAE,MAAM;gBAChB4B,OAAO,EAAE,MAAM;gBACfnC,UAAU,EAAE,SAAS;gBACrBM,KAAK,EAAE;cACT;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAKqC,KAAK,EAAE;YAAE6B,SAAS,EAAE,MAAM;YAAE5B,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAC,QAAA,gBAC9DzC,OAAA;YAAQqC,KAAK,EAAE;cACbK,UAAU,EAAE,mDAAmD;cAC/DM,KAAK,EAAE,OAAO;cACdH,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,cAAc;cACvBM,UAAU,EAAE,KAAK;cACjB+B,MAAM,EAAE;YACV,CAAE;YAAAxC,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtD,OAAA;YAAQqC,KAAK,EAAE;cACbK,UAAU,EAAE,MAAM;cAClBG,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,cAAc;cACvBqC,MAAM,EAAE,SAAS;cACjBjC,KAAK,EAAE;YACT,CAAE;YAAAP,QAAA,EAAC;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAM4B,0BAA0B,GAAGA,CAAA,kBACjClF,OAAA;IAAKqC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfuC,SAAS,EAAE,gCAAgC;MAC3CtC,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACAzC,OAAA;MAAIqC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELtD,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtEzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,eAAe;UAAEhB,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEyB,YAAY,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEgD,QAAQ,EAAE,UAAU;YAAE/C,OAAO,EAAE,cAAc;YAAEsC,KAAK,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FzC,OAAA;YAAO0E,IAAI,EAAC,UAAU;YAACa,cAAc;YAAClD,KAAK,EAAE;cAAEmD,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXgD,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTlD,UAAU,EAAE,SAAS;cACrBmD,UAAU,EAAE,MAAM;cAClBlD,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,eAAe;UAAEhB,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEyB,YAAY,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEgD,QAAQ,EAAE,UAAU;YAAE/C,OAAO,EAAE,cAAc;YAAEsC,KAAK,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FzC,OAAA;YAAO0E,IAAI,EAAC,UAAU;YAACa,cAAc;YAAClD,KAAK,EAAE;cAAEmD,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXgD,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTlD,UAAU,EAAE,SAAS;cACrBmD,UAAU,EAAE,MAAM;cAClBlD,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,eAAe;UAAEhB,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEyB,YAAY,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEgD,QAAQ,EAAE,UAAU;YAAE/C,OAAO,EAAE,cAAc;YAAEsC,KAAK,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FzC,OAAA;YAAO0E,IAAI,EAAC,UAAU;YAACa,cAAc;YAAClD,KAAK,EAAE;cAAEmD,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXgD,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTlD,UAAU,EAAE,SAAS;cACrBmD,UAAU,EAAE,MAAM;cAClBlD,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMwC,qBAAqB,GAAGA,CAAA,kBAC5B9F,OAAA;IAAKqC,KAAK,EAAE;MACVK,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfuC,SAAS,EAAE,gCAAgC;MAC3CtC,MAAM,EAAE;IACV,CAAE;IAAAJ,QAAA,gBACAzC,OAAA;MAAIqC,KAAK,EAAE;QACTU,MAAM,EAAE,cAAc;QACtBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,EAAC;IAEH;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELtD,OAAA;MAAKqC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACtEzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,eAAe;UAAEhB,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEyB,YAAY,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEgD,QAAQ,EAAE,UAAU;YAAE/C,OAAO,EAAE,cAAc;YAAEsC,KAAK,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FzC,OAAA;YAAO0E,IAAI,EAAC,UAAU;YAACa,cAAc;YAAClD,KAAK,EAAE;cAAEmD,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFtD,OAAA;YAAMqC,KAAK,EAAE;cACXgD,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTlD,UAAU,EAAE,SAAS;cACrBmD,UAAU,EAAE,MAAM;cAClBlD,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtD,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,eAAe;UAAEhB,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACrFzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAKqC,KAAK,EAAE;cAAEa,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE,SAAS;cAAEyB,YAAY,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAE9E;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtD,OAAA;YAAKqC,KAAK,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,EAAC;UAExD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAOqC,KAAK,EAAE;YAAEgD,QAAQ,EAAE,UAAU;YAAE/C,OAAO,EAAE,cAAc;YAAEsC,KAAK,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAAA7C,QAAA,gBAC7FzC,OAAA;YAAO0E,IAAI,EAAC,UAAU;YAACrC,KAAK,EAAE;cAAEmD,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAE;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEtD,OAAA;YAAMqC,KAAK,EAAE;cACXgD,QAAQ,EAAE,UAAU;cACpBJ,MAAM,EAAE,SAAS;cACjBQ,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTlD,UAAU,EAAE,MAAM;cAClBmD,UAAU,EAAE,MAAM;cAClBlD,YAAY,EAAE;YAChB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQ3F,SAAS;MACf,KAAK,SAAS;QACZ,OAAO8B,qBAAqB,CAAC,CAAC;MAChC,KAAK,eAAe;QAClB,OAAOgD,0BAA0B,CAAC,CAAC;MACrC,KAAK,SAAS;QACZ,OAAOY,qBAAqB,CAAC,CAAC;MAChC;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE9F,OAAA;IAAKqC,KAAK,EAAE;MAAEuC,KAAK,EAAE,MAAM;MAAEhC,OAAO,EAAE;IAAO,CAAE;IAAAH,QAAA,gBAE7CzC,OAAA;MAAKqC,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjB6B,YAAY,EAAE,MAAM;QACpBU,SAAS,EAAE,gCAAgC;QAC3CtC,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,eACAzC,OAAA;QAAKqC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEwD,QAAQ,EAAE;QAAO,CAAE;QAAAvD,QAAA,EAC5D3B,IAAI,CAACmF,GAAG,CAACC,GAAG,iBACXlG,OAAA;UAEEmG,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC6F,GAAG,CAACnF,GAAU,CAAE;UAC5CsB,KAAK,EAAE;YACLK,UAAU,EAAEtC,SAAS,KAAK8F,GAAG,CAACnF,GAAG,GAC7B,mDAAmD,GACnD,aAAa;YACjBiC,KAAK,EAAE5C,SAAS,KAAK8F,GAAG,CAACnF,GAAG,GAAG,OAAO,GAAG,SAAS;YAClD8B,MAAM,EAAEzC,SAAS,KAAK8F,GAAG,CAACnF,GAAG,GAAG,MAAM,GAAG,mBAAmB;YAC5D4B,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,gBAAgB;YACzBqC,MAAM,EAAE,SAAS;YACjB/B,UAAU,EAAE,KAAK;YACjBZ,OAAO,EAAE,MAAM;YACf8B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE,QAAQ;YACbqD,UAAU,EAAE;UACd,CAAE;UAAApD,QAAA,gBAEFzC,OAAA,CAACkG,GAAG,CAACjF,IAAI;YAACoD,IAAI,EAAE,EAAG;YAACrB,KAAK,EAAE5C,SAAS,KAAK8F,GAAG,CAACnF,GAAG,GAAG,SAAS,GAAG;UAAU;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC3E4C,GAAG,CAAClF,KAAK;QAAA,GAnBLkF,GAAG,CAACnF,GAAG;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLyC,aAAa,CAAC,CAAC;EAAA;IAAA5C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACpD,EAAA,CA1hBID,eAAyB;EAAA,QACZT,cAAc;AAAA;AAAA4G,EAAA,GAD3BnG,eAAyB;AA4hB/B,eAAeA,eAAe;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}