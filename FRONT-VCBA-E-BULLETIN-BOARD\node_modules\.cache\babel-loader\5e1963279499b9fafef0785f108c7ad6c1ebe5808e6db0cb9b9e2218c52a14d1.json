{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 20 5-5 5 5\",\n  key: \"13a0gw\"\n}], [\"path\", {\n  d: \"m7 4 5 5 5-5\",\n  key: \"1kwcof\"\n}]];\nconst ChevronsDownUp = createLucideIcon(\"chevrons-down-up\", __iconNode);\nexport { __iconNode, ChevronsDownUp as default };\n//# sourceMappingURL=chevrons-down-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}