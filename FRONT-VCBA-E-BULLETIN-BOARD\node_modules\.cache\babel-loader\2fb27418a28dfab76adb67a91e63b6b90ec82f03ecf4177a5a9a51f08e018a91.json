{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 13a2 2 0 0 0 2-2V7a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0V4a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0v-4a2 2 0 0 1 2-2\",\n  key: \"57tc96\"\n}]];\nconst AudioWaveform = createLucideIcon(\"audio-waveform\", __iconNode);\nexport { __iconNode, AudioWaveform as default };\n//# sourceMappingURL=audio-waveform.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}