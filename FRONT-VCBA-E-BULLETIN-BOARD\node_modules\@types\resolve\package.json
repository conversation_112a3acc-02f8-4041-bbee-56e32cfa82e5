{"name": "@types/resolve", "version": "1.17.1", "description": "TypeScript definitions for resolve", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marionebl", "githubUsername": "marion<PERSON>l"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/resolve"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8f762c2673effeafe8ebe80dcb01f2dfc7cceef8151e5529e2a2ee9dba2a44e2", "typeScriptVersion": "2.9"}