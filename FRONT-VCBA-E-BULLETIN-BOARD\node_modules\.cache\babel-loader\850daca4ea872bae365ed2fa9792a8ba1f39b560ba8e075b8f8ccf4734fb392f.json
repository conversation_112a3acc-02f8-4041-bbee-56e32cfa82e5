{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\",\n  key: \"jecpp\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"i6l2r4\"\n}]];\nconst Briefcase = createLucideIcon(\"briefcase\", __iconNode);\nexport { __iconNode, Briefcase as default };\n//# sourceMappingURL=briefcase.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}