{"ast": null, "code": "import _objectSpread from\"D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{useAnnouncements,useCategories}from'../../hooks/useAnnouncements';import{useNotificationTarget}from'../../hooks/useNotificationNavigation';import{useStudentAuth}from'../../contexts/StudentAuthContext';import CommentSection from'../../components/student/CommentSection';import StudentNotificationBell from'../../components/student/NotificationBell';import ImageLightbox from'../../components/common/ImageLightbox';import{getImageUrl,API_BASE_URL}from'../../config/constants';import'../../styles/notificationHighlight.css';import{Home,Search,Pin,Calendar,MessageSquare,Heart,Filter,MapPin,BookOpen,Users,PartyPopper,AlertTriangle,Clock,Trophy,Briefcase,GraduationCap,Flag,Coffee,Plane,ChevronDown,User,LogOut}from'lucide-react';// Custom hook for CORS-safe image loading\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const useImageLoader=imagePath=>{const[imageUrl,setImageUrl]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);useEffect(()=>{if(!imagePath){setImageUrl(null);return;}const loadImage=async()=>{setLoading(true);setError(null);try{const fullUrl=getImageUrl(imagePath);if(!fullUrl){throw new Error('Invalid image path');}console.log('🔄 Fetching image via CORS-safe method:',fullUrl);// Fetch image as blob to bypass CORS restrictions\nconst response=await fetch(fullUrl,{method:'GET',headers:{'Origin':window.location.origin},mode:'cors'});if(!response.ok){throw new Error(\"HTTP \".concat(response.status,\": \").concat(response.statusText));}const blob=await response.blob();const objectUrl=URL.createObjectURL(blob);setImageUrl(objectUrl);console.log('[SUCCESS] Image loaded successfully via fetch');}catch(err){console.error('[ERROR] Image fetch failed:',err);setError(err instanceof Error?err.message:'Failed to load image');}finally{setLoading(false);}};loadImage();// Cleanup object URL on unmount\nreturn()=>{if(imageUrl&&imageUrl.startsWith('blob:')){URL.revokeObjectURL(imageUrl);}};},[imagePath]);return{imageUrl,loading,error};};// Reusable CORS-safe image component\nconst ImageDisplay=_ref=>{let{imagePath,alt,style,className,onLoad,onMouseEnter,onMouseLeave}=_ref;const{imageUrl,loading,error}=useImageLoader(imagePath);if(loading){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\u23F3\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem'},children:\"Loading...\"})]})});}if(error||!imageUrl){return/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({},style),{},{display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'#f8fafc',color:'#64748b',border:'2px dashed #cbd5e1'}),className:className,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'0.5rem',fontSize:'1.5rem'},children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',fontWeight:'500'},children:\"Image unavailable\"}),error&&/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',marginTop:'0.25rem',color:'#9ca3af'},children:error})]})});}return/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:alt,style:style,className:className,onLoad:e=>{console.log('[SUCCESS] Image rendered successfully via CORS-safe method');onLoad===null||onLoad===void 0?void 0:onLoad(e);},onMouseEnter:onMouseEnter,onMouseLeave:onMouseLeave});};// Facebook-style image gallery component\nconst FacebookImageGallery=_ref2=>{let{images,altPrefix,maxVisible=4,onImageClick}=_ref2;if(!images||images.length===0)return null;const visibleImages=images.slice(0,maxVisible);const remainingCount=images.length-maxVisible;const getImageStyle=(index,total)=>{const baseStyle={width:'100%',height:'100%',objectFit:'cover',cursor:'pointer',transition:'transform 0.2s ease, filter 0.2s ease',borderRadius:index===0&&total===1?'12px':'8px'};return baseStyle;};const getContainerStyle=(index,total)=>{const baseStyle={position:'relative',overflow:'hidden',backgroundColor:'#f3f4f6'};if(total===1){return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'400px',borderRadius:'12px'});}if(total===2){return _objectSpread(_objectSpread({},baseStyle),{},{width:'50%',height:'300px',borderRadius:'8px'});}if(total===3){if(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'60%',height:'300px',borderRadius:'8px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'148px',borderRadius:'8px'});}}// 4+ images\nif(index===0){return _objectSpread(_objectSpread({},baseStyle),{},{width:'60%',height:'300px',borderRadius:'8px'});}else{return _objectSpread(_objectSpread({},baseStyle),{},{width:'100%',height:'96px',borderRadius:'8px'});}};const renderOverlay=(index,count)=>{if(index===maxVisible-1&&count>0){return/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'1.5rem',fontWeight:'600',borderRadius:'8px'},children:[\"+\",count]});}return null;};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'4px',width:'100%',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(0,visibleImages.length),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:visibleImages[0],alt:\"\".concat(altPrefix,\" - Image 1\"),style:getImageStyle(0,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(0)})]}),visibleImages.length>1&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'4px',width:visibleImages.length===2?'50%':'40%'},children:visibleImages.slice(1).map((image,idx)=>{const actualIndex=idx+1;return/*#__PURE__*/_jsxs(\"div\",{style:getContainerStyle(actualIndex,visibleImages.length),children:[/*#__PURE__*/_jsx(ImageDisplay,{imagePath:image,alt:\"\".concat(altPrefix,\" - Image \").concat(actualIndex+1),style:getImageStyle(actualIndex,visibleImages.length),onMouseEnter:e=>{e.currentTarget.style.transform='scale(1.02)';},onMouseLeave:e=>{e.currentTarget.style.transform='scale(1)';}}),renderOverlay(actualIndex,remainingCount),onImageClick&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,cursor:'pointer'},onClick:()=>onImageClick(actualIndex)})]},actualIndex);})})]});};const StudentNewsfeed=()=>{const navigate=useNavigate();// Handle notification-triggered navigation\nconst{isFromNotification,notificationId,scrollTarget}=useNotificationTarget();// Category styling function\nconst getCategoryStyle=categoryName=>{const styles={'ACADEMIC':{background:'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',icon:BookOpen},'GENERAL':{background:'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',icon:Users},'EVENTS':{background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',icon:PartyPopper},'EMERGENCY':{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',icon:AlertTriangle},'SPORTS':{background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',icon:Trophy},'DEADLINES':{background:'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',icon:Clock}};return styles[categoryName]||styles['GENERAL'];};// Holiday type styling function\nconst getHolidayTypeStyle=holidayTypeName=>{const styles={'NATIONAL HOLIDAY':{background:'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',icon:Flag},'SCHOOL EVENT':{background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',icon:GraduationCap},'ACADEMIC BREAK':{background:'linear-gradient(135deg, #10b981 0%, #059669 100%)',icon:Coffee},'SPORTS EVENT':{background:'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',icon:Trophy},'FIELD TRIP':{background:'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',icon:Plane},'MEETING':{background:'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',icon:Briefcase}};return styles[holidayTypeName]||styles['SCHOOL EVENT'];};// Filter states\nconst[filterCategory,setFilterCategory]=useState('');const[filterGradeLevel,setFilterGradeLevel]=useState('');const[searchTerm,setSearchTerm]=useState('');// UI states\nconst[showComments,setShowComments]=useState(null);const[selectedPinnedPost,setSelectedPinnedPost]=useState(null);const[showUserDropdown,setShowUserDropdown]=useState(false);// Lightbox states\nconst[lightboxOpen,setLightboxOpen]=useState(false);const[lightboxImages,setLightboxImages]=useState([]);const[lightboxInitialIndex,setLightboxInitialIndex]=useState(0);// Data states\nconst[calendarEvents,setCalendarEvents]=useState([]);const[calendarLoading,setCalendarLoading]=useState(false);const[calendarError,setCalendarError]=useState();const[pinnedAnnouncements,setPinnedAnnouncements]=useState([]);const{categories}=useCategories();// Get student user context\nconst{user:studentUser,logout}=useStudentAuth();// Use the announcements hook for proper state management\nconst{announcements,loading,error,likeAnnouncement,unlikeAnnouncement,refresh:refreshAnnouncements}=useAnnouncements({status:'published',page:1,limit:50,sort_by:'created_at',sort_order:'DESC'});// Grade levels for dropdown (11-12)\nconst gradeLevels=Array.from({length:2},(_,i)=>i+11);// [11, 12]\n// Open lightbox function\nconst openLightbox=(imageUrls,initialIndex)=>{setLightboxImages(imageUrls);setLightboxInitialIndex(initialIndex);setLightboxOpen(true);};// Update pinned announcements when announcements change\nuseEffect(()=>{const pinned=announcements.filter(ann=>ann.is_pinned===1);setPinnedAnnouncements(pinned);},[announcements]);// Fetch calendar events\nconst fetchCalendarEvents=async()=>{try{setCalendarLoading(true);setCalendarError(undefined);const response=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar?limit=50&sort_by=event_date&sort_order=ASC\"));const data=await response.json();if(data.success&&data.data){const eventsData=data.data.events||data.data||[];// Fetch images for each event\nconst eventsWithImages=await Promise.all(eventsData.map(async event=>{try{const imageResponse=await fetch(\"\".concat(API_BASE_URL,\"/api/calendar/\").concat(event.calendar_id,\"/images\"));const imageData=await imageResponse.json();if(imageData.success&&imageData.data){event.images=imageData.data.attachments||[];}else{event.images=[];}}catch(imgErr){console.warn(\"Failed to fetch images for event \".concat(event.calendar_id,\":\"),imgErr);event.images=[];}return event;}));setCalendarEvents(eventsWithImages);}else{setCalendarError('Failed to load calendar events');}}catch(err){console.error('Error fetching calendar events:',err);setCalendarError(err.message||'Failed to load calendar events');}finally{setCalendarLoading(false);}};// Initial data fetch\nuseEffect(()=>{fetchCalendarEvents();},[]);// Handle like/unlike functionality\nconst handleLikeToggle=async announcement=>{try{console.log('[DEBUG] Student toggling reaction for announcement:',announcement.announcement_id);console.log('[DEBUG] Current user_reaction:',announcement.user_reaction);console.log('[DEBUG] Student user context:',{id:studentUser===null||studentUser===void 0?void 0:studentUser.id,role:'student'});if(announcement.user_reaction){// Unlike the announcement\nconsole.log('[DEBUG] Student removing reaction...');await unlikeAnnouncement(announcement.announcement_id);}else{// Like the announcement\nconsole.log('[DEBUG] Student adding reaction...');await likeAnnouncement(announcement.announcement_id,1);}console.log('[SUCCESS] Student reaction toggled successfully');}catch(error){console.error('[ERROR] Error toggling student like:',error);}};// Handle logout\nconst handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);// Force redirect even if logout fails\nwindow.location.href='/student/login';}};// Close dropdown when clicking outside\nuseEffect(()=>{const handleClickOutside=event=>{if(showUserDropdown){const target=event.target;if(!target.closest('[data-dropdown=\"user-dropdown\"]')){setShowUserDropdown(false);}}};document.addEventListener('mousedown',handleClickOutside);return()=>document.removeEventListener('mousedown',handleClickOutside);},[showUserDropdown]);// Filter announcements based on search term, category, and grade level\nconst filteredAnnouncements=announcements.filter(announcement=>{var _announcement$grade_l;const matchesSearch=!searchTerm||announcement.title.toLowerCase().includes(searchTerm.toLowerCase())||announcement.content.toLowerCase().includes(searchTerm.toLowerCase());const matchesCategory=!filterCategory||announcement.category_id.toString()===filterCategory;// Grade level filtering - show announcements for selected grade or all grades (null)\nconst matchesGradeLevel=!filterGradeLevel||((_announcement$grade_l=announcement.grade_level)===null||_announcement$grade_l===void 0?void 0:_announcement$grade_l.toString())===filterGradeLevel||announcement.grade_level===null;// Show all-grade announcements regardless of filter\nreturn matchesSearch&&matchesCategory&&matchesGradeLevel;});// Filter calendar events with date-based filtering\nconst filteredCalendarEvents=calendarEvents.filter(event=>{const matchesSearch=!searchTerm||event.title.toLowerCase().includes(searchTerm.toLowerCase())||event.description&&event.description.toLowerCase().includes(searchTerm.toLowerCase());// Only show events that are published and on or after today's date\nconst today=new Date();today.setHours(0,0,0,0);// Reset time to start of day\nconst eventDate=new Date(event.event_date);eventDate.setHours(0,0,0,0);// Reset time to start of day\nconst isEventDateValid=eventDate>=today;const isPublished=event.is_published===1;return matchesSearch&&isEventDateValid&&isPublished;});// Combine and sort all content by date (most recent first)\nconst displayAnnouncements=filteredAnnouncements;const displayEvents=filteredCalendarEvents;// Create combined content array for better chronological display\nconst combinedContent=[...displayAnnouncements.map(item=>_objectSpread(_objectSpread({},item),{},{type:'announcement',sortDate:new Date(item.created_at)})),...displayEvents.map(item=>_objectSpread(_objectSpread({},item),{},{type:'event',sortDate:new Date(item.event_date)}))].sort((a,b)=>b.sortDate.getTime()-a.sortDate.getTime());return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"style\",{children:\"\\n          html {\\n            scroll-behavior: smooth;\\n          }\\n\\n          @keyframes spin {\\n            0% { transform: rotate(0deg); }\\n            100% { transform: rotate(360deg); }\\n          }\\n\\n          @media (max-width: 768px) {\\n            .mobile-hide { display: none !important; }\\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\\n            .mobile-stack {\\n              flex-direction: column !important;\\n              gap: 0.75rem !important;\\n            }\\n            .mobile-grid { grid-template-columns: 1fr !important; }\\n          }\\n\\n          @media (max-width: 480px) {\\n            .mobile-small-padding { padding: 0.75rem !important; }\\n            .mobile-small-text { font-size: 0.8rem !important; }\\n            .mobile-compact-header {\\n              padding: 0.75rem 1rem !important;\\n            }\\n            .mobile-compact-title {\\n              font-size: 1.25rem !important;\\n            }\\n            .mobile-compact-search {\\n              max-width: 200px !important;\\n            }\\n          }\\n        \"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',minHeight:'100vh',background:'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',scrollBehavior:'smooth'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-full\",style:{flex:1,display:'flex',flexDirection:'column',minHeight:'100vh',width:'100%'},children:[/*#__PURE__*/_jsx(\"header\",{style:{background:'white',borderBottom:'1px solid #e5e7eb',position:'sticky',top:0,zIndex:100,boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%',padding:'0 2rem',height:'72px',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1.5rem',minWidth:'300px'},children:[/*#__PURE__*/_jsx(\"img\",{src:\"/logo/vcba1.png\",alt:\"VCBA Logo\",style:{width:'48px',height:'48px',objectFit:'contain'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{margin:0,fontSize:'1.5rem',fontWeight:'700',color:'#111827',lineHeight:'1.2'},children:\"VCBA E-Bulletin Board\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'#6b7280'},children:\"Student Newsfeed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,maxWidth:'500px',margin:'0 2rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(Search,{size:20,style:{position:'absolute',left:'1rem',top:'50%',transform:'translateY(-50%)',color:'#9ca3af'}}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search post\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{width:'100%',height:'44px',padding:'0 1rem 0 3rem',border:'1px solid #d1d5db',borderRadius:'12px',background:'#f9fafb',color:'#374151',fontSize:'0.875rem',outline:'none',transition:'all 0.2s ease'},onFocus:e=>{e.target.style.borderColor='#3b82f6';e.target.style.background='white';e.target.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)';},onBlur:e=>{e.target.style.borderColor='#d1d5db';e.target.style.background='#f9fafb';e.target.style.boxShadow='none';}})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',minWidth:'400px',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.5rem',background:'#f9fafb',borderRadius:'12px',border:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsxs(\"select\",{value:filterCategory,onChange:e=>setFilterCategory(e.target.value),style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'white',color:'#374151',fontSize:'0.875rem',outline:'none',cursor:'pointer',minWidth:'110px'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.category_id.toString(),children:category.name},category.category_id))]}),/*#__PURE__*/_jsxs(\"select\",{value:filterGradeLevel,onChange:e=>setFilterGradeLevel(e.target.value),style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'white',color:'#374151',fontSize:'0.875rem',outline:'none',cursor:'pointer',minWidth:'100px'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Grades\"}),gradeLevels.map(grade=>/*#__PURE__*/_jsxs(\"option\",{value:grade.toString(),children:[\"Grade \",grade]},grade))]}),(searchTerm||filterCategory||filterGradeLevel)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setSearchTerm('');setFilterCategory('');setFilterGradeLevel('');},style:{padding:'0.5rem 0.75rem',border:'none',borderRadius:'8px',background:'#ef4444',color:'white',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#dc2626';},onMouseLeave:e=>{e.currentTarget.style.background='#ef4444';},children:\"Clear\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(StudentNotificationBell,{}),/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},\"data-dropdown\":\"user-dropdown\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowUserDropdown(!showUserDropdown),style:{display:'flex',alignItems:'center',gap:'0.5rem',padding:'0.75rem 1rem',background:'white',border:'1px solid #d1d5db',borderRadius:'12px',color:'#374151',fontSize:'0.875rem',fontWeight:'500',cursor:'pointer',transition:'all 0.2s ease',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)'},onMouseEnter:e=>{e.currentTarget.style.borderColor='#3b82f6';e.currentTarget.style.boxShadow='0 2px 8px rgba(59, 130, 246, 0.1)';},onMouseLeave:e=>{e.currentTarget.style.borderColor='#d1d5db';e.currentTarget.style.boxShadow='0 1px 3px rgba(0, 0, 0, 0.1)';},children:[/*#__PURE__*/_jsx(User,{size:16}),/*#__PURE__*/_jsx(\"span\",{children:(studentUser===null||studentUser===void 0?void 0:studentUser.firstName)||'Student'}),/*#__PURE__*/_jsx(ChevronDown,{size:14,style:{transform:showUserDropdown?'rotate(180deg)':'rotate(0deg)',transition:'transform 0.2s ease'}})]}),showUserDropdown&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',top:'100%',right:0,marginTop:'0.5rem',background:'white',border:'1px solid #e5e7eb',borderRadius:'12px',boxShadow:'0 10px 25px rgba(0, 0, 0, 0.15)',minWidth:'200px',zIndex:1000,overflow:'hidden'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.75rem 1rem',borderBottom:'1px solid #f3f4f6',background:'#f9fafb'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',fontWeight:'600',color:'#111827'},children:[studentUser===null||studentUser===void 0?void 0:studentUser.firstName,\" \",studentUser===null||studentUser===void 0?void 0:studentUser.lastName]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.75rem',color:'#6b7280'},children:studentUser===null||studentUser===void 0?void 0:studentUser.email})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'0.5rem 0'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{navigate('/student/dashboard');setShowUserDropdown(false);},style:{width:'100%',display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem 1rem',background:'transparent',border:'none',color:'#374151',fontSize:'0.875rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#f3f4f6';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(Home,{size:16}),\"Dashboard\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{handleLogout();setShowUserDropdown(false);},style:{width:'100%',display:'flex',alignItems:'center',gap:'0.75rem',padding:'0.75rem 1rem',background:'transparent',border:'none',color:'#ef4444',fontSize:'0.875rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='#fef2f2';},onMouseLeave:e=>{e.currentTarget.style.background='transparent';},children:[/*#__PURE__*/_jsx(LogOut,{size:16}),\"Logout\"]})]})]})]})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',gap:'1.5rem',padding:'1.5rem 2rem',background:'transparent',alignItems:'flex-start'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'280px',flexShrink:0},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'14px',border:'1px solid #e5e7eb',overflow:'hidden',position:'sticky',top:'80px',boxShadow:'0 4px 6px -1px rgba(0, 0, 0, 0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1rem 1.25rem 0.75rem',borderBottom:'1px solid #f3f4f6',background:'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'0.5rem'},children:[/*#__PURE__*/_jsx(Pin,{size:20,style:{color:'white'}}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.125rem',fontWeight:'600',color:'white'},children:\"Important Updates\"})]}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem',color:'rgba(255, 255, 255, 0.8)'},children:\"Don't miss these announcements\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem'},children:pinnedAnnouncements.length>0?/*#__PURE__*/_jsxs(_Fragment,{children:[pinnedAnnouncements.slice(0,3).map((announcement,index)=>{// Handle alert announcements with special styling\nlet colors;if(announcement.is_alert){colors=['#fee2e2','#fecaca','#ef4444'];// Red alert colors\n}else{const categoryName=(announcement.category_name||'GENERAL').toUpperCase();// Create gradient background based on category\nconst gradientColors={'ACADEMIC':['#dbeafe','#bfdbfe','#3b82f6'],'EVENTS':['#fef3c7','#fde68a','#f59e0b'],'EMERGENCY':['#fee2e2','#fecaca','#ef4444'],'SPORTS':['#dcfce7','#bbf7d0','#22c55e'],'DEADLINES':['#ede9fe','#ddd6fe','#8b5cf6'],'GENERAL':['#f3f4f6','#e5e7eb','#6b7280']};colors=gradientColors[categoryName]||gradientColors['GENERAL'];}return/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',background:\"linear-gradient(135deg, \".concat(colors[0],\" 0%, \").concat(colors[1],\" 100%)\"),borderRadius:'10px',border:\"1px solid \".concat(colors[2]),marginBottom:'0.75rem',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow=\"0 8px 25px \".concat(colors[2],\"30\");},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='none';},onClick:()=>setSelectedPinnedPost(announcement),children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'10px',height:'10px',background:colors[2],borderRadius:'50%',marginTop:'0.5rem',flexShrink:0,boxShadow:\"0 0 0 3px \".concat(colors[2],\"30\")}}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 0.5rem 0',fontSize:'0.875rem',fontWeight:'600',color:colors[2],lineHeight:'1.4'},children:announcement.title}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 0.5rem 0',fontSize:'0.8rem',color:colors[2],lineHeight:'1.4'},children:announcement.content.length>60?\"\".concat(announcement.content.substring(0,60),\"...\"):announcement.content}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',fontSize:'0.75rem',color:colors[2]},children:[/*#__PURE__*/_jsx(Calendar,{size:12}),/*#__PURE__*/_jsx(\"span\",{children:new Date(announcement.created_at).toLocaleDateString()})]})]})]})},announcement.announcement_id);}),pinnedAnnouncements.length>3&&/*#__PURE__*/_jsxs(\"button\",{style:{width:'100%',padding:'0.75rem',border:'1px solid #e5e7eb',borderRadius:'12px',background:'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',color:'#3b82f6',fontSize:'0.875rem',fontWeight:'600',cursor:'pointer',transition:'all 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.background='linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';e.currentTarget.style.borderColor='#3b82f6';e.currentTarget.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.currentTarget.style.background='linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';e.currentTarget.style.borderColor='#e5e7eb';e.currentTarget.style.transform='translateY(0)';},children:[\"\\uD83D\\uDCCC View All \",pinnedAnnouncements.length,\" Important Updates\"]})]}):/*#__PURE__*/_jsxs(\"div\",{style:{padding:'2rem 1rem',textAlign:'center',color:'#6b7280'},children:[/*#__PURE__*/_jsx(Pin,{size:24,style:{marginBottom:'0.5rem',opacity:0.5}}),/*#__PURE__*/_jsx(\"p\",{style:{margin:0,fontSize:'0.875rem'},children:\"No pinned posts available\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,minWidth:0},children:[(loading||calendarLoading)&&/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',padding:'3rem'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:'2.5rem',height:'2.5rem',border:'3px solid #e5e7eb',borderTop:'3px solid #3b82f6',borderRadius:'50%',animation:'spin 1s linear infinite'}})}),(error||calendarError)&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(239, 68, 68, 0.1)',border:'1px solid rgba(239, 68, 68, 0.2)',borderRadius:'12px',padding:'1rem',marginBottom:'1.5rem',color:'#dc2626'},children:[error&&/*#__PURE__*/_jsxs(\"div\",{children:[\"Announcements: \",error]}),calendarError&&/*#__PURE__*/_jsxs(\"div\",{children:[\"Calendar: \",calendarError]})]}),!loading&&!calendarLoading&&displayAnnouncements.length===0&&displayEvents.length===0&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.8)',borderRadius:'16px',padding:'3rem',textAlign:'center',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(Filter,{size:48,color:\"#9ca3af\"})}),/*#__PURE__*/_jsx(\"h3\",{style:{color:'#374151',fontSize:'1.25rem',fontWeight:'600',margin:'0 0 0.5rem 0'},children:\"No updates found\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#6b7280',margin:0},children:searchTerm||filterCategory||filterGradeLevel?'Try adjusting your filters to see more content.':'Check back later for new updates.'})]}),!loading&&!calendarLoading&&(displayAnnouncements.length>0||displayEvents.length>0)&&/*#__PURE__*/_jsxs(\"div\",{style:{maxWidth:'1200px',margin:'0 auto',display:'flex',flexDirection:'column',gap:'1.5rem'},children:[displayEvents.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:displayEvents.map(event=>/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow='0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',marginBottom:'1rem'},children:[(()=>{const holidayTypeName=(event.holiday_type_name||'SCHOOL EVENT').toUpperCase();const holidayStyle=getHolidayTypeStyle(holidayTypeName);const IconComponent=holidayStyle.icon;return/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'50px',borderRadius:'12px',background:holidayStyle.background,display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(IconComponent,{size:20,color:\"white\"})});})(),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.25rem'},children:(()=>{const holidayTypeName=(event.holiday_type_name||'SCHOOL EVENT').toUpperCase();const holidayStyle=getHolidayTypeStyle(holidayTypeName);const IconComponent=holidayStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:holidayStyle.background,color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),holidayTypeName]});})()}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#9ca3af',fontSize:'0.8rem'},children:new Date(event.event_date).toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'})})]})]}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.75rem 0',color:'#1f2937',fontSize:'1.25rem',fontWeight:'700',lineHeight:'1.3'},children:event.title}),(()=>{// Get event images if they exist\nconst eventImageUrls=[];if(event.images&&event.images.length>0){event.images.forEach(img=>{if(img.file_path){// Convert file_path to full URL\nconst imageUrl=getImageUrl(img.file_path);if(imageUrl){eventImageUrls.push(imageUrl);}}});}return eventImageUrls.length>0?/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:eventImageUrls.filter(Boolean),altPrefix:event.title,maxVisible:4,onImageClick:index=>{const filteredImages=eventImageUrls.filter(Boolean);openLightbox(filteredImages,index);}})}):null;})(),event.description&&/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 1rem 0',color:'#4b5563',fontSize:'0.95rem',lineHeight:'1.6'},children:event.description}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',paddingTop:'1rem',borderTop:'1px solid rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',color:'#6b7280',fontSize:'0.875rem'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(MapPin,{size:14,color:\"#6b7280\"}),\"School Event\"]}),event.end_date&&event.end_date!==event.event_date&&/*#__PURE__*/_jsxs(\"span\",{children:[\"Until \",new Date(event.end_date).toLocaleDateString()]})]})})]},\"event-\".concat(event.calendar_id)))}),displayAnnouncements.length>0&&/*#__PURE__*/_jsx(_Fragment,{children:displayAnnouncements.map(announcement=>/*#__PURE__*/_jsxs(\"div\",{id:\"announcement-\".concat(announcement.announcement_id),className:isFromNotification&&scrollTarget===\"announcement-\".concat(announcement.announcement_id)?'notification-highlight announcement':'',style:{background:'rgba(255, 255, 255, 0.95)',borderRadius:'16px',padding:'1.5rem',border:announcement.is_alert?'2px solid rgba(239, 68, 68, 0.3)':'1px solid rgba(0, 0, 0, 0.1)',backdropFilter:'blur(10px)',boxShadow:announcement.is_alert?'0 4px 20px rgba(239, 68, 68, 0.15)':'0 4px 20px rgba(0, 0, 0, 0.08)',transition:'transform 0.2s ease, box-shadow 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-2px)';e.currentTarget.style.boxShadow=announcement.is_alert?'0 8px 30px rgba(239, 68, 68, 0.25)':'0 8px 30px rgba(0, 0, 0, 0.12)';},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow=announcement.is_alert?'0 4px 20px rgba(239, 68, 68, 0.15)':'0 4px 20px rgba(0, 0, 0, 0.08)';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',marginBottom:'1rem'},children:[(()=>{if(announcement.is_alert){return/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'50px',background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',borderRadius:'12px',display:'flex',alignItems:'center',justifyContent:'center',flexShrink:0},children:/*#__PURE__*/_jsx(AlertTriangle,{size:20,color:\"white\"})});}else{const categoryName=(announcement.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'50px',borderRadius:'12px',background:categoryStyle.background,display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(IconComponent,{size:20,color:\"white\"})});}})(),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem',marginBottom:'0.25rem'},children:[(()=>{if(announcement.is_alert){return/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'12px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:12,color:\"white\"}),\"Alert\"]});}else{const categoryName=(announcement.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:categoryStyle.background,color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),categoryName]});}})(),announcement.is_pinned&&/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Pin,{size:12,color:\"white\"}),\"PINNED\"]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#9ca3af',fontSize:'0.8rem'},children:[\"By \",announcement.author_name,\" \\u2022 \",announcement.published_at?new Date(announcement.published_at).toLocaleDateString('en-US',{weekday:'short',year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'}):'Unknown date']})]})]}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 0.75rem 0',color:'#1f2937',fontSize:'1.25rem',fontWeight:'700',lineHeight:'1.3'},children:announcement.title}),(()=>{// Get images from multiple sources\nconst imageUrls=[];// Add images from attachments (new multiple image system)\nif(announcement.attachments&&announcement.attachments.length>0){announcement.attachments.forEach(img=>{if(img.file_path){// Use getImageUrl to construct the full URL\nconst fullUrl=getImageUrl(img.file_path);if(fullUrl){imageUrls.push(fullUrl);}}});}// Fallback to legacy single image\nif(imageUrls.length===0&&(announcement.image_url||announcement.image_path)){const legacyUrl=getImageUrl(announcement.image_url||announcement.image_path);if(legacyUrl){imageUrls.push(legacyUrl);}}return imageUrls.length>0?/*#__PURE__*/_jsx(FacebookImageGallery,{images:imageUrls.filter(Boolean),altPrefix:announcement.title,maxVisible:4,onImageClick:index=>{const filteredImages=imageUrls.filter(Boolean);openLightbox(filteredImages,index);}}):null;})(),/*#__PURE__*/_jsx(\"div\",{style:{margin:'0 0 1.5rem 0',color:'#4b5563',fontSize:'0.95rem',lineHeight:'1.6'},dangerouslySetInnerHTML:{__html:announcement.content}}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',paddingTop:'1rem',borderTop:'1px solid rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleLikeToggle(announcement),style:{background:announcement.user_reaction?'rgba(239, 68, 68, 0.1)':'transparent',color:announcement.user_reaction?'#dc2626':'#6b7280',border:'none',borderRadius:'8px',padding:'0.5rem 1rem',fontSize:'0.875rem',fontWeight:'600',cursor:'pointer',display:'flex',alignItems:'center',gap:'0.5rem',transition:'all 0.2s ease'},onMouseEnter:e=>{if(!announcement.user_reaction){e.currentTarget.style.background='rgba(239, 68, 68, 0.1)';e.currentTarget.style.color='#dc2626';}},onMouseLeave:e=>{if(!announcement.user_reaction){e.currentTarget.style.background='transparent';e.currentTarget.style.color='#6b7280';}},children:[/*#__PURE__*/_jsx(Heart,{size:16,color:announcement.user_reaction?\"#dc2626\":\"#9ca3af\",fill:announcement.user_reaction?\"#dc2626\":\"none\"}),/*#__PURE__*/_jsx(\"span\",{children:announcement.reaction_count||0})]}),announcement.allow_comments&&/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowComments(showComments===announcement.announcement_id?null:announcement.announcement_id),style:{background:showComments===announcement.announcement_id?'rgba(59, 130, 246, 0.1)':'transparent',color:showComments===announcement.announcement_id?'#3b82f6':'#6b7280',border:'none',borderRadius:'8px',padding:'0.5rem 1rem',fontSize:'0.875rem',fontWeight:'600',cursor:'pointer',display:'flex',alignItems:'center',gap:'0.5rem',transition:'all 0.2s ease'},onMouseEnter:e=>{if(showComments!==announcement.announcement_id){e.currentTarget.style.background='rgba(59, 130, 246, 0.1)';e.currentTarget.style.color='#3b82f6';}},onMouseLeave:e=>{if(showComments!==announcement.announcement_id){e.currentTarget.style.background='transparent';e.currentTarget.style.color='#6b7280';}},children:[/*#__PURE__*/_jsx(MessageSquare,{size:16,color:\"#6b7280\"}),/*#__PURE__*/_jsx(\"span\",{children:announcement.comment_count||0})]})]})}),showComments===announcement.announcement_id&&announcement.allow_comments&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'1rem',paddingTop:'1rem',borderTop:'1px solid rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsx(CommentSection,{announcementId:announcement.announcement_id,allowComments:announcement.allow_comments,currentUserId:studentUser===null||studentUser===void 0?void 0:studentUser.id,currentUserType:\"student\"})})]},\"announcement-\".concat(announcement.announcement_id)))})]})]})]})]}),selectedPinnedPost&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000,padding:'2rem'},onClick:()=>setSelectedPinnedPost(null),children:/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'white',borderRadius:'16px',maxWidth:'600px',width:'100%',maxHeight:'80vh',overflow:'auto',boxShadow:'0 20px 60px rgba(0, 0, 0, 0.3)'},onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem',borderBottom:'1px solid #e5e7eb',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(Pin,{size:20,style:{color:'#22c55e'}}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,fontSize:'1.25rem',fontWeight:'600',color:'#111827'},children:\"Pinned Post\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedPinnedPost(null),style:{background:'none',border:'none',fontSize:'1.5rem',color:'#6b7280',cursor:'pointer',padding:'0.25rem',borderRadius:'4px',transition:'color 0.2s ease'},onMouseEnter:e=>{e.currentTarget.style.color='#374151';},onMouseLeave:e=>{e.currentTarget.style.color='#6b7280';},children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.75rem',marginBottom:'1rem'},children:[(()=>{if(selectedPinnedPost.is_alert){return/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(AlertTriangle,{size:12,color:\"white\"}),\"Alert\"]});}else{const categoryName=(selectedPinnedPost.category_name||'GENERAL').toUpperCase();const categoryStyle=getCategoryStyle(categoryName);const IconComponent=categoryStyle.icon;return/*#__PURE__*/_jsxs(\"span\",{style:{background:categoryStyle.background,color:'white',fontSize:'0.75rem',fontWeight:'600',padding:'0.25rem 0.75rem',borderRadius:'20px',textTransform:'uppercase',letterSpacing:'0.5px',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(IconComponent,{size:12,color:\"white\"}),categoryName]});}})(),/*#__PURE__*/_jsxs(\"span\",{style:{background:'linear-gradient(135deg, #facc15 0%, #eab308 100%)',color:'white',padding:'0.25rem 0.75rem',borderRadius:'12px',fontSize:'0.75rem',fontWeight:'600',display:'flex',alignItems:'center',gap:'0.25rem'},children:[/*#__PURE__*/_jsx(Pin,{size:12}),\"PINNED\"]})]}),/*#__PURE__*/_jsx(\"h2\",{style:{margin:'0 0 1rem 0',fontSize:'1.5rem',fontWeight:'700',color:'#111827',lineHeight:'1.3'},children:selectedPinnedPost.title}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#4b5563',fontSize:'1rem',lineHeight:'1.6',marginBottom:'1.5rem'},children:selectedPinnedPost.content}),selectedPinnedPost.attachments&&selectedPinnedPost.attachments.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(FacebookImageGallery,{images:selectedPinnedPost.attachments.map(img=>getImageUrl(img.file_path)).filter(Boolean),altPrefix:selectedPinnedPost.title,onImageClick:index=>{const imageUrls=selectedPinnedPost.attachments.map(img=>getImageUrl(img.file_path)).filter(Boolean);openLightbox(imageUrls,index);}})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem',fontSize:'0.875rem',color:'#6b7280',paddingTop:'1rem',borderTop:'1px solid #e5e7eb'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'0.5rem'},children:[/*#__PURE__*/_jsx(Calendar,{size:16}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Published: \",new Date(selectedPinnedPost.created_at).toLocaleDateString()]})]}),selectedPinnedPost.author_name&&/*#__PURE__*/_jsxs(\"div\",{children:[\"By: \",selectedPinnedPost.author_name]})]})]})]})}),/*#__PURE__*/_jsx(ImageLightbox,{images:lightboxImages,initialIndex:lightboxInitialIndex,isOpen:lightboxOpen,onClose:()=>setLightboxOpen(false),altPrefix:\"Image\"})]})]});};export default StudentNewsfeed;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}