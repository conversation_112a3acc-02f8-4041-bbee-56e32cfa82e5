{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"14\",\n  y: \"17\",\n  rx: \"1\",\n  key: \"19aais\"\n}], [\"path\", {\n  d: \"M10 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v2.5\",\n  key: \"1w6v7t\"\n}], [\"path\", {\n  d: \"M20 17v-2a2 2 0 1 0-4 0v2\",\n  key: \"pwaxnr\"\n}]];\nconst FolderLock = createLucideIcon(\"folder-lock\", __iconNode);\nexport { __iconNode, FolderLock as default };\n//# sourceMappingURL=folder-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}