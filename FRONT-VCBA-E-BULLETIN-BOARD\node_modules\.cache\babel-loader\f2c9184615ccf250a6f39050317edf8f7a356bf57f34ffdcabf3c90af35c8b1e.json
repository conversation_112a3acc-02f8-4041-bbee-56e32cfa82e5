{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useEffect, useCallback } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { notificationNavigationService } from '../services/notificationNavigationService';\n\n/**\n * Professional React Hook for Notification Navigation\n * Handles notification click behavior and deep linking\n * Following Google/OpenAI React best practices\n */\n\nexport const useNotificationNavigation = options => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    userRole,\n    onNavigationStart,\n    onNavigationComplete,\n    onNavigationError\n  } = options;\n\n  // Initialize navigation service\n  useEffect(() => {\n    notificationNavigationService.initialize(navigate, userRole);\n  }, [navigate, userRole]);\n\n  // Handle URL parameters for deep linking\n  useEffect(() => {\n    const handleDeepLink = async () => {\n      const urlParams = new URLSearchParams(location.search);\n      const notificationId = urlParams.get('notification');\n      const focusType = urlParams.get('focus');\n      const targetId = urlParams.get('id');\n      const commentId = urlParams.get('comment');\n      if (notificationId || focusType && targetId) {\n        console.log('🔗 Deep link detected:', {\n          notificationId,\n          focusType,\n          targetId,\n          commentId\n        });\n\n        // Handle highlighting and scrolling for deep links\n        setTimeout(() => {\n          handleDeepLinkHighlighting(focusType, targetId, commentId);\n        }, 500); // Allow page to render first\n      }\n    };\n    handleDeepLink();\n  }, [location]);\n\n  // Handle deep link highlighting and scrolling\n  const handleDeepLinkHighlighting = useCallback((focusType, targetId, commentId) => {\n    try {\n      let targetElement = null;\n\n      // Find target element based on focus type\n      if (focusType === 'announcement' && targetId) {\n        targetElement = document.getElementById(`announcement-${targetId}`);\n      } else if (focusType === 'comment' && commentId) {\n        targetElement = document.getElementById(`comment-${commentId}`);\n      } else if (focusType === 'event' && targetId) {\n        targetElement = document.getElementById(`event-${targetId}`);\n      }\n      if (targetElement) {\n        // Smooth scroll to target\n        targetElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add highlight effect\n        targetElement.classList.add('notification-highlight');\n\n        // Remove highlight after animation\n        setTimeout(() => {\n          var _targetElement;\n          (_targetElement = targetElement) === null || _targetElement === void 0 ? void 0 : _targetElement.classList.remove('notification-highlight');\n        }, 3000);\n        console.log('✨ Highlighted target element:', targetElement.id);\n      } else {\n        console.warn('🎯 Target element not found for deep link');\n      }\n    } catch (error) {\n      console.error('Error handling deep link highlighting:', error);\n    }\n  }, []);\n\n  // Main notification click handler\n  const handleNotificationClick = useCallback(async notification => {\n    try {\n      console.log('🔔 Handling notification click:', notification);\n\n      // Trigger navigation start callback\n      onNavigationStart === null || onNavigationStart === void 0 ? void 0 : onNavigationStart(notification);\n\n      // Use navigation service to handle the click\n      const success = await notificationNavigationService.handleNotificationClick(notification, {\n        markAsRead: true,\n        highlightTarget: true,\n        scrollBehavior: 'smooth'\n      });\n\n      // Trigger navigation complete callback\n      onNavigationComplete === null || onNavigationComplete === void 0 ? void 0 : onNavigationComplete(notification, success);\n      if (!success) {\n        throw new Error('Navigation failed');\n      }\n    } catch (error) {\n      console.error('Error in notification click handler:', error);\n      onNavigationError === null || onNavigationError === void 0 ? void 0 : onNavigationError(error, notification);\n\n      // Fallback navigation\n      const fallbackRoute = userRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n      navigate(fallbackRoute);\n    }\n  }, [navigate, userRole, onNavigationStart, onNavigationComplete, onNavigationError]);\n  return {\n    handleNotificationClick,\n    isNavigating: false,\n    // Could be enhanced with loading state\n    lastNavigatedNotification: null // Could be enhanced with state tracking\n  };\n};\n\n// Define the navigation state interface\n_s(useNotificationNavigation, \"OZbQqLRLQjDYISoAh4uQEBeDfPA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n/**\n * Hook for handling notification-triggered page behavior\n * Use this in pages that can be targets of notification navigation\n */\nexport const useNotificationTarget = () => {\n  _s2();\n  const location = useLocation();\n  useEffect(() => {\n    // Check if we arrived here from a notification\n    const state = location.state;\n    if (state !== null && state !== void 0 && state.fromNotification) {\n      console.log('📍 Page loaded from notification:', state);\n\n      // Handle highlighting and scrolling\n      if (state.scrollTo && state.highlightTarget) {\n        setTimeout(() => {\n          const targetElement = document.getElementById(state.scrollTo);\n          if (targetElement) {\n            // Scroll to target\n            targetElement.scrollIntoView({\n              behavior: state.scrollBehavior || 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n\n            // Add highlight effect\n            targetElement.classList.add('notification-highlight');\n\n            // Remove highlight after animation\n            setTimeout(() => {\n              targetElement.classList.remove('notification-highlight');\n            }, 3000);\n            console.log('✨ Auto-highlighted notification target:', state.scrollTo);\n          }\n        }, 300); // Allow page to render\n      }\n    }\n  }, [location]);\n  const state = location.state;\n  return {\n    isFromNotification: (state === null || state === void 0 ? void 0 : state.fromNotification) || false,\n    notificationId: (state === null || state === void 0 ? void 0 : state.notificationId) || null,\n    scrollTarget: (state === null || state === void 0 ? void 0 : state.scrollTo) || null\n  };\n};\n\n/**\n * Hook for managing notification read status\n */\n_s2(useNotificationTarget, \"BXcZrDMM76mmm4zA8/QV5UbMNXE=\", false, function () {\n  return [useLocation];\n});\nexport const useNotificationReadStatus = () => {\n  _s3();\n  const markAsRead = useCallback(async (notificationId, userRole) => {\n    try {\n      // Import notification service dynamically\n      const {\n        studentNotificationService,\n        adminNotificationService\n      } = await import('../services/notificationService');\n      const service = userRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAsRead(notificationId);\n      console.log('✅ Marked notification as read:', notificationId);\n      return true;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      return false;\n    }\n  }, []);\n  const markAllAsRead = useCallback(async userRole => {\n    try {\n      // Import notification service dynamically\n      const {\n        studentNotificationService,\n        adminNotificationService\n      } = await import('../services/notificationService');\n      const service = userRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAllAsRead();\n      console.log('✅ Marked all notifications as read');\n      return true;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      return false;\n    }\n  }, []);\n  return {\n    markAsRead,\n    markAllAsRead\n  };\n};\n_s3(useNotificationReadStatus, \"t3H3GdrEZr/5lET9hMg09AcG628=\");\nexport default useNotificationNavigation;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useNavigate", "useLocation", "notificationNavigationService", "useNotificationNavigation", "options", "_s", "navigate", "location", "userRole", "onNavigationStart", "onNavigationComplete", "onNavigationError", "initialize", "handleDeepLink", "urlParams", "URLSearchParams", "search", "notificationId", "get", "focusType", "targetId", "commentId", "console", "log", "setTimeout", "handleDeepLinkHighlighting", "targetElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "inline", "classList", "add", "_targetElement", "remove", "id", "warn", "error", "handleNotificationClick", "notification", "success", "mark<PERSON><PERSON><PERSON>", "highlightTarget", "scroll<PERSON>eh<PERSON>or", "Error", "fallback<PERSON><PERSON><PERSON>", "isNavigating", "lastNavigatedNotification", "useNotificationTarget", "_s2", "state", "fromNotification", "scrollTo", "isFromNotification", "scrollTarget", "useNotificationReadStatus", "_s3", "studentNotificationService", "adminNotificationService", "service", "markAllAsRead"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useNotificationNavigation.ts"], "sourcesContent": ["import { useEffect, useCallback } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { notificationNavigationService } from '../services/notificationNavigationService';\nimport { Notification } from '../services/notificationService';\n\n/**\n * Professional React Hook for Notification Navigation\n * Handles notification click behavior and deep linking\n * Following Google/OpenAI React best practices\n */\n\ninterface UseNotificationNavigationOptions {\n  userRole: 'admin' | 'student';\n  onNavigationStart?: (notification: Notification) => void;\n  onNavigationComplete?: (notification: Notification, success: boolean) => void;\n  onNavigationError?: (error: Error, notification: Notification) => void;\n}\n\ninterface UseNotificationNavigationReturn {\n  handleNotificationClick: (notification: Notification) => Promise<void>;\n  isNavigating: boolean;\n  lastNavigatedNotification: Notification | null;\n}\n\nexport const useNotificationNavigation = (\n  options: UseNotificationNavigationOptions\n): UseNotificationNavigationReturn => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { userRole, onNavigationStart, onNavigationComplete, onNavigationError } = options;\n\n  // Initialize navigation service\n  useEffect(() => {\n    notificationNavigationService.initialize(navigate, userRole);\n  }, [navigate, userRole]);\n\n  // Handle URL parameters for deep linking\n  useEffect(() => {\n    const handleDeepLink = async () => {\n      const urlParams = new URLSearchParams(location.search);\n      const notificationId = urlParams.get('notification');\n      const focusType = urlParams.get('focus');\n      const targetId = urlParams.get('id');\n      const commentId = urlParams.get('comment');\n\n      if (notificationId || (focusType && targetId)) {\n        console.log('🔗 Deep link detected:', { notificationId, focusType, targetId, commentId });\n        \n        // Handle highlighting and scrolling for deep links\n        setTimeout(() => {\n          handleDeepLinkHighlighting(focusType, targetId, commentId);\n        }, 500); // Allow page to render first\n      }\n    };\n\n    handleDeepLink();\n  }, [location]);\n\n  // Handle deep link highlighting and scrolling\n  const handleDeepLinkHighlighting = useCallback((\n    focusType: string | null,\n    targetId: string | null,\n    commentId: string | null\n  ) => {\n    try {\n      let targetElement: HTMLElement | null = null;\n\n      // Find target element based on focus type\n      if (focusType === 'announcement' && targetId) {\n        targetElement = document.getElementById(`announcement-${targetId}`);\n      } else if (focusType === 'comment' && commentId) {\n        targetElement = document.getElementById(`comment-${commentId}`);\n      } else if (focusType === 'event' && targetId) {\n        targetElement = document.getElementById(`event-${targetId}`);\n      }\n\n      if (targetElement) {\n        // Smooth scroll to target\n        targetElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add highlight effect\n        targetElement.classList.add('notification-highlight');\n        \n        // Remove highlight after animation\n        setTimeout(() => {\n          targetElement?.classList.remove('notification-highlight');\n        }, 3000);\n\n        console.log('✨ Highlighted target element:', targetElement.id);\n      } else {\n        console.warn('🎯 Target element not found for deep link');\n      }\n    } catch (error) {\n      console.error('Error handling deep link highlighting:', error);\n    }\n  }, []);\n\n  // Main notification click handler\n  const handleNotificationClick = useCallback(async (notification: Notification) => {\n    try {\n      console.log('🔔 Handling notification click:', notification);\n      \n      // Trigger navigation start callback\n      onNavigationStart?.(notification);\n\n      // Use navigation service to handle the click\n      const success = await notificationNavigationService.handleNotificationClick(\n        notification,\n        {\n          markAsRead: true,\n          highlightTarget: true,\n          scrollBehavior: 'smooth'\n        }\n      );\n\n      // Trigger navigation complete callback\n      onNavigationComplete?.(notification, success);\n\n      if (!success) {\n        throw new Error('Navigation failed');\n      }\n\n    } catch (error) {\n      console.error('Error in notification click handler:', error);\n      onNavigationError?.(error as Error, notification);\n      \n      // Fallback navigation\n      const fallbackRoute = userRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n      navigate(fallbackRoute);\n    }\n  }, [navigate, userRole, onNavigationStart, onNavigationComplete, onNavigationError]);\n\n  return {\n    handleNotificationClick,\n    isNavigating: false, // Could be enhanced with loading state\n    lastNavigatedNotification: null // Could be enhanced with state tracking\n  };\n};\n\n// Define the navigation state interface\ninterface NavigationState {\n  fromNotification?: boolean;\n  notificationId?: number;\n  highlightTarget?: boolean;\n  scrollTo?: string;\n  scrollBehavior?: ScrollBehavior;\n}\n\n/**\n * Hook for handling notification-triggered page behavior\n * Use this in pages that can be targets of notification navigation\n */\nexport const useNotificationTarget = () => {\n  const location = useLocation();\n\n  useEffect(() => {\n    // Check if we arrived here from a notification\n    const state = location.state as NavigationState;\n\n    if (state?.fromNotification) {\n      console.log('📍 Page loaded from notification:', state);\n\n      // Handle highlighting and scrolling\n      if (state.scrollTo && state.highlightTarget) {\n        setTimeout(() => {\n          const targetElement = document.getElementById(state.scrollTo!);\n\n          if (targetElement) {\n            // Scroll to target\n            targetElement.scrollIntoView({\n              behavior: (state.scrollBehavior || 'smooth') as ScrollBehavior,\n              block: 'center',\n              inline: 'nearest'\n            });\n\n            // Add highlight effect\n            targetElement.classList.add('notification-highlight');\n\n            // Remove highlight after animation\n            setTimeout(() => {\n              targetElement.classList.remove('notification-highlight');\n            }, 3000);\n\n            console.log('✨ Auto-highlighted notification target:', state.scrollTo);\n          }\n        }, 300); // Allow page to render\n      }\n    }\n  }, [location]);\n\n  const state = location.state as NavigationState;\n\n  return {\n    isFromNotification: state?.fromNotification || false,\n    notificationId: state?.notificationId || null,\n    scrollTarget: state?.scrollTo || null\n  };\n};\n\n/**\n * Hook for managing notification read status\n */\nexport const useNotificationReadStatus = () => {\n  const markAsRead = useCallback(async (notificationId: number, userRole: 'admin' | 'student') => {\n    try {\n      // Import notification service dynamically\n      const { studentNotificationService, adminNotificationService } = await import('../services/notificationService');\n      \n      const service = userRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAsRead(notificationId);\n      \n      console.log('✅ Marked notification as read:', notificationId);\n      return true;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      return false;\n    }\n  }, []);\n\n  const markAllAsRead = useCallback(async (userRole: 'admin' | 'student') => {\n    try {\n      // Import notification service dynamically\n      const { studentNotificationService, adminNotificationService } = await import('../services/notificationService');\n      \n      const service = userRole === 'admin' ? adminNotificationService : studentNotificationService;\n      await service.markAllAsRead();\n      \n      console.log('✅ Marked all notifications as read');\n      return true;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      return false;\n    }\n  }, []);\n\n  return {\n    markAsRead,\n    markAllAsRead\n  };\n};\n\nexport default useNotificationNavigation;\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,6BAA6B,QAAQ,2CAA2C;;AAGzF;AACA;AACA;AACA;AACA;;AAeA,OAAO,MAAMC,yBAAyB,GACpCC,OAAyC,IACL;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO,QAAQ;IAAEC,iBAAiB;IAAEC,oBAAoB;IAAEC;EAAkB,CAAC,GAAGP,OAAO;;EAExF;EACAN,SAAS,CAAC,MAAM;IACdI,6BAA6B,CAACU,UAAU,CAACN,QAAQ,EAAEE,QAAQ,CAAC;EAC9D,CAAC,EAAE,CAACF,QAAQ,EAAEE,QAAQ,CAAC,CAAC;;EAExB;EACAV,SAAS,CAAC,MAAM;IACd,MAAMe,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACR,QAAQ,CAACS,MAAM,CAAC;MACtD,MAAMC,cAAc,GAAGH,SAAS,CAACI,GAAG,CAAC,cAAc,CAAC;MACpD,MAAMC,SAAS,GAAGL,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;MACxC,MAAME,QAAQ,GAAGN,SAAS,CAACI,GAAG,CAAC,IAAI,CAAC;MACpC,MAAMG,SAAS,GAAGP,SAAS,CAACI,GAAG,CAAC,SAAS,CAAC;MAE1C,IAAID,cAAc,IAAKE,SAAS,IAAIC,QAAS,EAAE;QAC7CE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;UAAEN,cAAc;UAAEE,SAAS;UAAEC,QAAQ;UAAEC;QAAU,CAAC,CAAC;;QAEzF;QACAG,UAAU,CAAC,MAAM;UACfC,0BAA0B,CAACN,SAAS,EAAEC,QAAQ,EAAEC,SAAS,CAAC;QAC5D,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX;IACF,CAAC;IAEDR,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkB,0BAA0B,GAAG1B,WAAW,CAAC,CAC7CoB,SAAwB,EACxBC,QAAuB,EACvBC,SAAwB,KACrB;IACH,IAAI;MACF,IAAIK,aAAiC,GAAG,IAAI;;MAE5C;MACA,IAAIP,SAAS,KAAK,cAAc,IAAIC,QAAQ,EAAE;QAC5CM,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgBR,QAAQ,EAAE,CAAC;MACrE,CAAC,MAAM,IAAID,SAAS,KAAK,SAAS,IAAIE,SAAS,EAAE;QAC/CK,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWP,SAAS,EAAE,CAAC;MACjE,CAAC,MAAM,IAAIF,SAAS,KAAK,OAAO,IAAIC,QAAQ,EAAE;QAC5CM,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAASR,QAAQ,EAAE,CAAC;MAC9D;MAEA,IAAIM,aAAa,EAAE;QACjB;QACAA,aAAa,CAACG,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAN,aAAa,CAACO,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;;QAErD;QACAV,UAAU,CAAC,MAAM;UAAA,IAAAW,cAAA;UACf,CAAAA,cAAA,GAAAT,aAAa,cAAAS,cAAA,uBAAbA,cAAA,CAAeF,SAAS,CAACG,MAAM,CAAC,wBAAwB,CAAC;QAC3D,CAAC,EAAE,IAAI,CAAC;QAERd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEG,aAAa,CAACW,EAAE,CAAC;MAChE,CAAC,MAAM;QACLf,OAAO,CAACgB,IAAI,CAAC,2CAA2C,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,uBAAuB,GAAGzC,WAAW,CAAC,MAAO0C,YAA0B,IAAK;IAChF,IAAI;MACFnB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkB,YAAY,CAAC;;MAE5D;MACAhC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGgC,YAAY,CAAC;;MAEjC;MACA,MAAMC,OAAO,GAAG,MAAMxC,6BAA6B,CAACsC,uBAAuB,CACzEC,YAAY,EACZ;QACEE,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,IAAI;QACrBC,cAAc,EAAE;MAClB,CACF,CAAC;;MAED;MACAnC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAG+B,YAAY,EAAEC,OAAO,CAAC;MAE7C,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAII,KAAK,CAAC,mBAAmB,CAAC;MACtC;IAEF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D5B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAG4B,KAAK,EAAWE,YAAY,CAAC;;MAEjD;MACA,MAAMM,aAAa,GAAGvC,QAAQ,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;MACtFF,QAAQ,CAACyC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACzC,QAAQ,EAAEE,QAAQ,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,iBAAiB,CAAC,CAAC;EAEpF,OAAO;IACL6B,uBAAuB;IACvBQ,YAAY,EAAE,KAAK;IAAE;IACrBC,yBAAyB,EAAE,IAAI,CAAC;EAClC,CAAC;AACH,CAAC;;AAED;AAAA5C,EAAA,CAvHaF,yBAAyB;EAAA,QAGnBH,WAAW,EACXC,WAAW;AAAA;AA4H9B;AACA;AACA;AACA;AACA,OAAO,MAAMiD,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzC,MAAM5C,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACd;IACA,MAAMsD,KAAK,GAAG7C,QAAQ,CAAC6C,KAAwB;IAE/C,IAAIA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,gBAAgB,EAAE;MAC3B/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE6B,KAAK,CAAC;;MAEvD;MACA,IAAIA,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACR,eAAe,EAAE;QAC3CpB,UAAU,CAAC,MAAM;UACf,MAAME,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAACwB,KAAK,CAACE,QAAS,CAAC;UAE9D,IAAI5B,aAAa,EAAE;YACjB;YACAA,aAAa,CAACG,cAAc,CAAC;cAC3BC,QAAQ,EAAGsB,KAAK,CAACP,cAAc,IAAI,QAA2B;cAC9Dd,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;;YAEF;YACAN,aAAa,CAACO,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;;YAErD;YACAV,UAAU,CAAC,MAAM;cACfE,aAAa,CAACO,SAAS,CAACG,MAAM,CAAC,wBAAwB,CAAC;YAC1D,CAAC,EAAE,IAAI,CAAC;YAERd,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE6B,KAAK,CAACE,QAAQ,CAAC;UACxE;QACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX;IACF;EACF,CAAC,EAAE,CAAC/C,QAAQ,CAAC,CAAC;EAEd,MAAM6C,KAAK,GAAG7C,QAAQ,CAAC6C,KAAwB;EAE/C,OAAO;IACLG,kBAAkB,EAAE,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,gBAAgB,KAAI,KAAK;IACpDpC,cAAc,EAAE,CAAAmC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEnC,cAAc,KAAI,IAAI;IAC7CuC,YAAY,EAAE,CAAAJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,QAAQ,KAAI;EACnC,CAAC;AACH,CAAC;;AAED;AACA;AACA;AAFAH,GAAA,CA/CaD,qBAAqB;EAAA,QACfjD,WAAW;AAAA;AAiD9B,OAAO,MAAMwD,yBAAyB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7C,MAAMf,UAAU,GAAG5C,WAAW,CAAC,OAAOkB,cAAsB,EAAET,QAA6B,KAAK;IAC9F,IAAI;MACF;MACA,MAAM;QAAEmD,0BAA0B;QAAEC;MAAyB,CAAC,GAAG,MAAM,MAAM,CAAC,iCAAiC,CAAC;MAEhH,MAAMC,OAAO,GAAGrD,QAAQ,KAAK,OAAO,GAAGoD,wBAAwB,GAAGD,0BAA0B;MAC5F,MAAME,OAAO,CAAClB,UAAU,CAAC1B,cAAc,CAAC;MAExCK,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEN,cAAc,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuB,aAAa,GAAG/D,WAAW,CAAC,MAAOS,QAA6B,IAAK;IACzE,IAAI;MACF;MACA,MAAM;QAAEmD,0BAA0B;QAAEC;MAAyB,CAAC,GAAG,MAAM,MAAM,CAAC,iCAAiC,CAAC;MAEhH,MAAMC,OAAO,GAAGrD,QAAQ,KAAK,OAAO,GAAGoD,wBAAwB,GAAGD,0BAA0B;MAC5F,MAAME,OAAO,CAACC,aAAa,CAAC,CAAC;MAE7BxC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLI,UAAU;IACVmB;EACF,CAAC;AACH,CAAC;AAACJ,GAAA,CArCWD,yBAAyB;AAuCtC,eAAetD,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}