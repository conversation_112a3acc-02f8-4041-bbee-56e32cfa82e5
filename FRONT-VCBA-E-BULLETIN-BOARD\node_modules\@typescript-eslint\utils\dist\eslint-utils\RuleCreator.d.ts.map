{"version": 3, "file": "RuleCreator.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAI3B,MAAM,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACpE,MAAM,MAAM,mBAAmB,CAAC,WAAW,SAAS,MAAM,IAAI;IAC5D,IAAI,EAAE,uBAAuB,CAAC;CAC/B,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC;AAE5C,MAAM,WAAW,oBAAoB,CACnC,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,EAC1B,aAAa,SAAS,YAAY;IAElC,MAAM,EAAE,CACN,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EACrD,kBAAkB,EAAE,QAAQ,CAAC,QAAQ,CAAC,KACnC,aAAa,CAAC;IACnB,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;CACpC;AAED,MAAM,WAAW,YAAY,CAC3B,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,EAC1B,aAAa,SAAS,YAAY,CAClC,SAAQ,oBAAoB,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC;IAClE,IAAI,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;CACjC;AAED,MAAM,WAAW,mBAAmB,CAClC,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,EAC1B,aAAa,SAAS,YAAY,CAClC,SAAQ,oBAAoB,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC;IAClE,IAAI,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACvC,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;;;GAKG;AACH,wBAAgB,WAAW,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,0QAyBnE;yBAzBe,WAAW;;;AA2B3B;;;;;GAKG;AACH,iBAAS,UAAU,CACjB,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,EAC1B,aAAa,SAAS,YAAY,GAAG,YAAY,EACjD,EACA,MAAM,EACN,cAAc,EACd,IAAI,GACL,EAAE,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,GAAG,UAAU,CAC1E,WAAW,EACX,QAAQ,EACR,aAAa,CACd,CAWA"}