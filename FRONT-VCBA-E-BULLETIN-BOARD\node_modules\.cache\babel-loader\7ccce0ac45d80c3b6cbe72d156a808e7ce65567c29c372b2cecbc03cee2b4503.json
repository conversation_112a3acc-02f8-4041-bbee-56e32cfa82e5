{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 21V3\",\n  key: \"1bzk4w\"\n}], [\"path\", {\n  d: \"M2 5h18a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2.26\",\n  key: \"1d64pi\"\n}], [\"path\", {\n  d: \"M7 17v3a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-3\",\n  key: \"5hbqbf\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"qt15rb\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"ssideg\"\n}]];\nconst Gpu = createLucideIcon(\"gpu\", __iconNode);\nexport { __iconNode, Gpu as default };\n//# sourceMappingURL=gpu.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}