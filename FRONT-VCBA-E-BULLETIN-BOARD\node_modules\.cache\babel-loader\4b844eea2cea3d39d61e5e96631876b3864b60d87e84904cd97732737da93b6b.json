{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.116 4.104A1 1 0 0 1 6.11 3h11.78a1 1 0 0 1 .994 1.105L17.19 20.21A2 2 0 0 1 15.2 22H8.8a2 2 0 0 1-2-1.79z\",\n  key: \"p55z4y\"\n}], [\"path\", {\n  d: \"M6 12a5 5 0 0 1 6 0 5 5 0 0 0 6 0\",\n  key: \"mjntcy\"\n}]];\nconst GlassWater = createLucideIcon(\"glass-water\", __iconNode);\nexport { __iconNode, GlassWater as default };\n//# sourceMappingURL=glass-water.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}