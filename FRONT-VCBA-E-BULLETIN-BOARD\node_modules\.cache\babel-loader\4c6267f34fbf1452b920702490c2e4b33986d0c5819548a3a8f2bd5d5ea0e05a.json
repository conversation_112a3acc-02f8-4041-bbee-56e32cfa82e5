{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22a2 2 0 0 1-2-2\",\n  key: \"i7yj1i\"\n}], [\"path\", {\n  d: \"M14 2a2 2 0 0 1 2 2\",\n  key: \"170a0m\"\n}], [\"path\", {\n  d: \"M16 22h-2\",\n  key: \"18d249\"\n}], [\"path\", {\n  d: \"M2 10V8\",\n  key: \"7yj4fe\"\n}], [\"path\", {\n  d: \"M2 4a2 2 0 0 1 2-2\",\n  key: \"ddgnws\"\n}], [\"path\", {\n  d: \"M20 8a2 2 0 0 1 2 2\",\n  key: \"1770vt\"\n}], [\"path\", {\n  d: \"M22 14v2\",\n  key: \"iot8ja\"\n}], [\"path\", {\n  d: \"M22 20a2 2 0 0 1-2 2\",\n  key: \"qj8q6g\"\n}], [\"path\", {\n  d: \"M4 16a2 2 0 0 1-2-2\",\n  key: \"1dnafg\"\n}], [\"path\", {\n  d: \"M8 10a2 2 0 0 1 2-2h5a1 1 0 0 1 1 1v5a2 2 0 0 1-2 2H9a1 1 0 0 1-1-1z\",\n  key: \"ci6f0b\"\n}], [\"path\", {\n  d: \"M8 2h2\",\n  key: \"1gmkwm\"\n}]];\nconst SquaresIntersect = createLucideIcon(\"squares-intersect\", __iconNode);\nexport { __iconNode, SquaresIntersect as default };\n//# sourceMappingURL=squares-intersect.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}