{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Calendar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\nimport { calendarService } from '../../services/calendarService';\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon, AlertTriangle, MessageCircle } from 'lucide-react';\nimport CalendarEventLikeButton from '../../components/common/CalendarEventLikeButton';\n// Removed calendar attachment imports since this feature is not yet implemented\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Calendar = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  // Add CSS animation for spinning refresh icon\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes spin {\n        from { transform: rotate(0deg); }\n        to { transform: rotate(360deg); }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n  const [currentDate, setCurrentDate] = useState(() => new Date());\n  const [selectedDate, setSelectedDate] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [editingEvent, setEditingEvent] = useState(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [saving, setSaving] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n\n  // Event attachments state - disabled until backend implementation is complete\n  // const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\n\n  // Use the calendar hook\n  const {\n    events,\n    loading,\n    error,\n    createEvent,\n    updateEvent,\n    getEventsForDate,\n    refresh\n  } = useCalendar(currentDate);\n  const {\n    categories\n  } = useCalendarCategories();\n\n  // Clear messages after 5 seconds\n  useEffect(() => {\n    if (successMessage || errorMessage || error) {\n      const timer = setTimeout(() => {\n        setSuccessMessage('');\n        setErrorMessage('');\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [successMessage, errorMessage, error]);\n  const handleCreateEvent = useCallback(date => {\n    setEditingEvent(null);\n    setSelectedDate(date || null);\n    setShowModal(true);\n  }, []);\n  const handleEditEvent = useCallback(event => {\n    setEditingEvent(event);\n    setShowModal(true);\n  }, []);\n  const handleSaveEvent = useCallback(async (data, applyPendingDeletes, onComplete) => {\n    setSaving(true);\n    try {\n      if (editingEvent) {\n        await updateEvent(editingEvent.calendar_id, data);\n\n        // Apply pending image deletions AFTER successful update\n        if (applyPendingDeletes) {\n          console.log('🗑️ Applying pending image deletions after successful update');\n          await applyPendingDeletes();\n        }\n        setSuccessMessage('Event updated successfully! Calendar refreshed.');\n      } else {\n        await createEvent(data);\n        setSuccessMessage('Event created successfully! Calendar refreshed.');\n      }\n\n      // Execute completion callback for additional operations\n      if (onComplete) {\n        await onComplete();\n      }\n\n      // Force refresh the calendar to ensure immediate update\n      console.log('🔄 Refreshing calendar to show updated events...');\n      setRefreshing(true);\n      await refresh();\n      setRefreshing(false);\n\n      // Small delay to ensure smooth UI transition\n      setTimeout(() => {\n        setShowModal(false);\n        setEditingEvent(null);\n        setSelectedDate(null);\n        setSaving(false);\n      }, 100);\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to save event');\n      setSaving(false);\n    }\n  }, [editingEvent, updateEvent, createEvent, refresh]);\n  const handleCloseModal = useCallback(() => {\n    setShowModal(false);\n    setEditingEvent(null);\n    setSelectedDate(null);\n  }, []);\n\n  // Event management functions\n  const handlePublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.publishEvent(eventId);\n      setSuccessMessage('Event published successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to publish event');\n    }\n  }, [refresh]);\n  const handleUnpublishEvent = useCallback(async eventId => {\n    try {\n      await calendarService.unpublishEvent(eventId);\n      setSuccessMessage('Event unpublished successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to unpublish event');\n    }\n  }, [refresh]);\n  const handleDeleteEvent = useCallback(async eventId => {\n    // Use window.confirm to avoid ESLint error\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await calendarService.softDeleteEvent(eventId);\n      setSuccessMessage('Event deleted successfully');\n      refresh(); // Refresh calendar data\n    } catch (error) {\n      setErrorMessage(error.message || 'Failed to delete event');\n    }\n  }, [refresh]);\n  const navigateMonth = useCallback(direction => {\n    const newDate = new Date(currentDate);\n    if (direction === 'prev') {\n      newDate.setMonth(currentDate.getMonth() - 1);\n    } else {\n      newDate.setMonth(currentDate.getMonth() + 1);\n    }\n    setCurrentDate(newDate);\n  }, [currentDate]);\n  const goToToday = useCallback(() => {\n    setCurrentDate(new Date());\n  }, []);\n  const handleDateClick = useCallback(date => {\n    setSelectedDate(date);\n    handleCreateEvent(date);\n  }, [handleCreateEvent]);\n  const getEventTypeColor = useCallback(event => {\n    // Use category color if available, otherwise subcategory color, otherwise default\n    return event.category_color || event.subcategory_color || '#22c55e';\n  }, []);\n\n  // Helper function to format event duration\n  const getEventDuration = useCallback(event => {\n    if (!event.end_date || event.end_date === event.event_date) {\n      return 'Single day event';\n    }\n    const startDate = new Date(event.event_date);\n    const endDate = new Date(event.end_date);\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\n\n    return `${diffDays} day event`;\n  }, []);\n\n  // Helper function to get first two words from event title for calendar chip display\n  const getEventChipTitle = useCallback(title => {\n    const words = title.trim().split(/\\s+/);\n    return words.slice(0, 2).join(' ');\n  }, []);\n\n  // Memoize calendar days to prevent infinite re-renders\n  const days = useMemo(() => {\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\n\n  // Get unique events for the event list (deduplicate multi-day events)\n  const uniqueEvents = useMemo(() => {\n    const eventMap = new Map();\n    events.forEach(event => {\n      // Use calendar_id as the unique identifier\n      if (!eventMap.has(event.calendar_id)) {\n        eventMap.set(event.calendar_id, event);\n      }\n    });\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\n      // Sort by event_date, then by title\n      const dateA = new Date(a.event_date);\n      const dateB = new Date(b.event_date);\n      if (dateA.getTime() !== dateB.getTime()) {\n        return dateA.getTime() - dateB.getTime();\n      }\n      return a.title.localeCompare(b.title);\n    });\n\n    // Debug: Log deduplication results\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\n    return uniqueEventsList;\n  }, [events]);\n\n  // Filter events based on search term and holiday type\n  const filteredEvents = useMemo(() => {\n    return uniqueEvents.filter(event => {\n      var _event$description, _event$category_id;\n      const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_event$description = event.description) === null || _event$description === void 0 ? void 0 : _event$description.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesCategory = !selectedCategory || ((_event$category_id = event.category_id) === null || _event$category_id === void 0 ? void 0 : _event$category_id.toString()) === selectedCategory;\n      return matchesSearch && matchesCategory;\n    });\n  }, [uniqueEvents, searchTerm, selectedCategory]);\n\n  // Pagination calculations\n  const totalItems = filteredEvents.length;\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\n\n  // Reset to first page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, selectedCategory, itemsPerPage]);\n\n  // Fetch attachments for visible events - disabled until backend implementation is complete\n  // useEffect(() => {\n  //   const fetchAttachments = async () => {\n  //     if (!paginatedEvents || paginatedEvents.length === 0) return;\n  //     // Attachment functionality will be implemented later\n  //   };\n  //   fetchAttachments();\n  // }, [paginatedEvents]);\n\n  // Component to display event images - disabled until backend implementation is complete\n  const EventImages = ({\n    eventId\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '60px',\n        height: '60px',\n        backgroundColor: '#f3f4f6',\n        borderRadius: '6px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#9ca3af',\n        fontSize: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(ImageIcon, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Component to display individual image thumbnail - disabled until backend implementation is complete\n  // const EventImageThumbnail: React.FC<{ attachment: any }> = ({ attachment }) => {\n  //   // This component will be implemented when the backend supports file attachments\n  //   return null;\n  // };\n\n  // Pagination component - Always visible\n  const PaginationControls = () => {\n    // Always show pagination controls, even for single page\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\n\n    const getPageNumbers = () => {\n      const pages = [];\n      const maxVisiblePages = 5;\n      if (effectiveTotalPages <= maxVisiblePages) {\n        for (let i = 1; i <= effectiveTotalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        if (effectiveCurrentPage <= 3) {\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\n        } else {\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\n        }\n      }\n      return pages;\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '2rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        border: '1px solid #e5e7eb',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"Show:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: itemsPerPage,\n          onChange: e => setItemsPerPage(Number(e.target.value)),\n          style: {\n            padding: '0.25rem 0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            fontSize: '0.875rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5,\n            children: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10,\n            children: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 20,\n            children: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 50,\n            children: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: \"per page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [\"Showing \", Math.max(totalItems > 0 ? startIndex + 1 : 0, 0), \"-\", Math.min(endIndex, totalItems), \" of \", totalItems, \" events\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.25rem',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(1),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"First\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.max(1, prev - 1)),\n          disabled: effectiveCurrentPage === 1,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), getPageNumbers().map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => typeof page === 'number' && setCurrentPage(page),\n          disabled: page === '...',\n          style: {\n            padding: '0.5rem 0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\n            color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\n            cursor: page === '...' ? 'default' : 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: page === effectiveCurrentPage ? '600' : '400'\n          },\n          children: page\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1)),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(effectiveTotalPages),\n          disabled: effectiveCurrentPage === effectiveTotalPages,\n          style: {\n            padding: '0.5rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '4px',\n            backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\n            color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\n            cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\n            fontSize: '0.875rem'\n          },\n          children: \"Last\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    \"data-calendar-component\": \"main\",\n    children: [successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        color: '#166534',\n        borderRadius: '8px'\n      },\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 9\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem',\n        padding: '1rem',\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        color: '#dc2626',\n        borderRadius: '8px'\n      },\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '8px',\n        padding: '1rem',\n        marginBottom: '1rem',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n        border: '1px solid #e8f5e8',\n        position: 'relative',\n        zIndex: 10\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '1.25rem',\n              fontWeight: '600',\n              color: '#2d5016',\n              margin: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                size: 16,\n                color: \"#1e40af\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), \"School Calendar\", refreshing && /*#__PURE__*/_jsxDEV(RefreshCw, {\n                size: 14,\n                color: \"#22c55e\",\n                style: {\n                  animation: 'spin 1s linear infinite',\n                  marginLeft: '0.25rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280',\n              margin: '0 0 0 0.5rem',\n              fontSize: '0.875rem'\n            },\n            children: [getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('prev'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToToday,\n            style: {\n              padding: '0.25rem 0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigateMonth('next'),\n            style: {\n              padding: '0.25rem',\n              color: '#6b7280',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              fontSize: '1rem',\n              transition: 'color 0.2s ease'\n            },\n            onMouseOver: e => e.currentTarget.style.color = '#374151',\n            onMouseOut: e => e.currentTarget.style.color = '#6b7280',\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCreateEvent(),\n            style: {\n              display: 'inline-flex',\n              alignItems: 'center',\n              padding: '0.5rem 1rem',\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n            },\n            onMouseOver: e => {\n              e.currentTarget.style.transform = 'translateY(-1px)';\n              e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.transform = 'translateY(0)';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: \"+ Add Event\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)',\n          backgroundColor: '#f9fafb',\n          borderBottom: '1px solid #e5e7eb'\n        },\n        children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            textAlign: 'center',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#374151'\n          },\n          children: day\n        }, day, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(7, 1fr)'\n        },\n        children: days.map((date, index) => {\n          const dayEvents = getEventsForDate(date);\n          const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\n          const isTodayDate = isToday(date);\n\n          // Create unique key based on date to prevent React key conflicts\n          // Use date timestamp as key since it's unique and stable across re-renders\n          const dateKey = `calendar-day-${date.getTime()}`;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              minHeight: '120px',\n              padding: '0.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb',\n              // Remove right border on last column\n              cursor: 'pointer',\n              backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\n              color: !isCurrentMonth ? '#9ca3af' : '#374151',\n              transition: 'all 0.2s ease',\n              position: 'relative'\n            },\n            onClick: () => handleDateClick(date),\n            onMouseOver: e => {\n              if (isCurrentMonth) {\n                e.currentTarget.style.backgroundColor = '#f3f4f6';\n                e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\n              }\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                marginBottom: '0.25rem',\n                color: isTodayDate ? '#2563eb' : 'inherit'\n              },\n              children: date.getDate()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.25rem'\n              },\n              children: [dayEvents.slice(0, 3).map(event => {\n                // Determine styling for multi-day events\n                const isMultiDay = event.isMultiDay;\n                const isStart = event.isEventStart;\n                const isEnd = event.isEventEnd;\n                const isContinuation = isMultiDay && !isStart && !isEnd;\n                const eventColor = getEventTypeColor(event);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.75rem',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: isMultiDay ? isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px' : '6px',\n                    backgroundColor: eventColor + (isContinuation ? '25' : '15'),\n                    border: `1px solid ${eventColor}`,\n                    borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap',\n                    cursor: 'pointer',\n                    position: 'relative',\n                    color: '#374151',\n                    fontWeight: '500',\n                    transition: 'all 0.2s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleEditEvent(event);\n                  },\n                  onMouseOver: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + '30';\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\n                  },\n                  onMouseOut: e => {\n                    e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  title: isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title,\n                  children: [isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25B6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 27\n                  }, this), isContinuation && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25AC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 27\n                  }, this), isEnd && isMultiDay && !isStart && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem'\n                    },\n                    children: \"\\u25C0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      flex: 1,\n                      overflow: 'hidden',\n                      textOverflow: 'ellipsis'\n                    },\n                    children: getEventChipTitle(event.title)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this), isStart && isMultiDay && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: eventColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.7rem',\n                      opacity: 0.7\n                    },\n                    children: \"\\u2192\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 27\n                  }, this)]\n                }, `event-${event.calendar_id}-${date.getTime()}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this);\n              }), dayEvents.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280'\n                },\n                children: [\"+\", dayEvents.length - 3, \" more\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this)]\n          }, dateKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        margin: '2rem 0',\n        padding: '1.5rem',\n        backgroundColor: '#f8fafc'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1.5rem',\n          alignItems: 'end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Search Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              size: 16,\n              color: \"#9ca3af\",\n              style: {\n                position: 'absolute',\n                left: '0.75rem',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                pointerEvents: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search events...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem',\n                outline: 'none',\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\n              },\n              onFocus: e => {\n                e.currentTarget.style.borderColor = '#22c55e';\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n              },\n              onBlur: e => {\n                e.currentTarget.style.borderColor = '#d1d5db';\n                e.currentTarget.style.boxShadow = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem',\n              outline: 'none',\n              backgroundColor: 'white',\n              transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\n              minHeight: '2.5rem',\n              lineHeight: '1.2'\n            },\n            onFocus: e => {\n              e.currentTarget.style.borderColor = '#22c55e';\n              e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n            },\n            onBlur: e => {\n              e.currentTarget.style.borderColor = '#d1d5db';\n              e.currentTarget.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.category_id,\n              children: category.name\n            }, category.category_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refresh,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              backgroundColor: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              fontWeight: '500',\n              fontSize: '0.875rem',\n              transition: 'background-color 0.2s ease',\n              height: '2.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            onMouseOver: e => e.currentTarget.style.backgroundColor = '#e5e7eb',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = '#f3f4f6',\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#2d5016',\n            margin: 0\n          },\n          children: [\"Events for \", getMonthName(currentDate.getMonth()), \" \", currentDate.getFullYear()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: [filteredEvents.length, \" event\", filteredEvents.length !== 1 ? 's' : '', \" found\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 24,\n          style: {\n            marginBottom: '1rem',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 11\n      }, this) : filteredEvents.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No events found for this month\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleCreateEvent(),\n          style: {\n            marginTop: '1rem',\n            padding: '0.75rem 1.5rem',\n            backgroundColor: '#22c55e',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            fontWeight: '500'\n          },\n          children: \"Create First Event\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '1rem'\n        },\n        children: paginatedEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: '1px solid #e5e7eb',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            backgroundColor: 'white',\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: '0 0 0.5rem 0',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [event.title, event.is_alert && /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 25\n                  }, this), event.event_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 23\n                }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2192 \", event.end_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: '#f3f4f6',\n                    color: '#374151',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  },\n                  children: getEventDuration(event)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    backgroundColor: getEventTypeColor(event),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem'\n                  },\n                  children: event.category_name || 'Uncategorized'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 21\n              }, this), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  margin: '0.5rem 0 0 0',\n                  lineHeight: '1.5'\n                },\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  marginTop: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CalendarEventLikeButton, {\n                  eventId: event.calendar_id,\n                  initialLiked: event.user_has_reacted || false,\n                  initialCount: event.reaction_count || 0,\n                  size: \"small\",\n                  onLikeChange: (liked, newCount) => {\n                    // Update the event in the local state if needed\n                    console.log(`Event ${event.calendar_id} like changed:`, {\n                      liked,\n                      newCount\n                    });\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1098,\n                  columnNumber: 23\n                }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 27\n                  }, this), event.comment_count || 0, \" comments\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1109,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem',\n                marginLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEditEvent(event),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Edit event\",\n                children: /*#__PURE__*/_jsxDEV(Edit, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => event.is_published ? handleUnpublishEvent(event.calendar_id) : handlePublishEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: event.is_published ? '#fef3c7' : '#dcfce7',\n                  color: event.is_published ? '#d97706' : '#16a34a',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: event.is_published ? 'Unpublish event' : 'Publish event',\n                children: event.is_published ? /*#__PURE__*/_jsxDEV(Clock, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 54\n                }, this) : /*#__PURE__*/_jsxDEV(Send, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 76\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDeleteEvent(event.calendar_id),\n                style: {\n                  padding: '0.5rem',\n                  backgroundColor: '#fef2f2',\n                  color: '#dc2626',\n                  border: 'none',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                title: \"Delete event\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1182,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f3f4f6',\n              paddingTop: '1rem',\n              marginTop: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Event Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(EventImages, {\n              eventId: event.calendar_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 17\n          }, this)]\n        }, `unique-event-${event.calendar_id}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1009,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 945,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaginationControls, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CalendarEventModal, {\n      isOpen: showModal,\n      onClose: handleCloseModal,\n      onSave: handleSaveEvent,\n      event: editingEvent,\n      selectedDate: selectedDate,\n      loading: saving || loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 461,\n    columnNumber: 5\n  }, this);\n}, \"cJqX6HReh5h++M8bJ3JJq9dVpoA=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n})), \"cJqX6HReh5h++M8bJ3JJq9dVpoA=\", false, function () {\n  return [useCalendar, useCalendarCategories];\n});\n_c2 = Calendar;\nCalendar.displayName = 'Calendar';\nexport default Calendar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Calendar$React.memo\");\n$RefreshReg$(_c2, \"Calendar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useCalendar", "useCalendarCategories", "getCalendarDays", "isToday", "isSameMonth", "getMonthName", "CalendarEventModal", "calendarService", "Calendar", "CalendarIcon", "Search", "RefreshCw", "Trash2", "Edit", "Send", "Clock", "Image", "ImageIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageCircle", "CalendarEventLikeButton", "jsxDEV", "_jsxDEV", "_s", "memo", "_c", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "currentDate", "setCurrentDate", "Date", "selectedDate", "setSelectedDate", "showModal", "setShowModal", "editingEvent", "setEditingEvent", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "saving", "setSaving", "refreshing", "setRefreshing", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "events", "loading", "error", "createEvent", "updateEvent", "getEventsForDate", "refresh", "categories", "timer", "setTimeout", "clearTimeout", "handleCreateEvent", "date", "handleEditEvent", "event", "handleSaveEvent", "data", "applyPendingDeletes", "onComplete", "calendar_id", "console", "log", "message", "handleCloseModal", "handlePublishEvent", "eventId", "publishEvent", "handleUnpublishEvent", "unpublishEvent", "handleDeleteEvent", "window", "confirm", "softDeleteEvent", "navigateMonth", "direction", "newDate", "setMonth", "getMonth", "goToToday", "handleDateClick", "getEventTypeColor", "category_color", "subcategory_color", "getEventDuration", "end_date", "event_date", "startDate", "endDate", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "getEventChipTitle", "title", "words", "trim", "split", "slice", "join", "days", "getFullYear", "uniqueEvents", "eventMap", "Map", "for<PERSON>ach", "has", "set", "uniqueEventsList", "Array", "from", "values", "sort", "a", "b", "dateA", "dateB", "localeCompare", "length", "filteredEvents", "filter", "_event$description", "_event$category_id", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "category_id", "toString", "totalItems", "totalPages", "startIndex", "endIndex", "paginatedEvents", "EventImages", "width", "height", "backgroundColor", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontSize", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "PaginationControls", "effectiveTotalPages", "max", "effectiveCurrentPage", "getPageNumbers", "pages", "maxVisiblePages", "i", "push", "marginTop", "marginBottom", "padding", "border", "boxShadow", "gap", "value", "onChange", "e", "Number", "target", "min", "flexWrap", "onClick", "disabled", "cursor", "prev", "map", "page", "index", "fontWeight", "max<PERSON><PERSON><PERSON>", "margin", "background", "position", "zIndex", "animation", "marginLeft", "transition", "onMouseOver", "currentTarget", "onMouseOut", "transform", "overflow", "gridTemplateColumns", "borderBottom", "day", "textAlign", "dayEvents", "isCurrentMonth", "isTodayDate", "<PERSON><PERSON><PERSON>", "minHeight", "borderRight", "getDate", "flexDirection", "isMultiDay", "isStart", "isEventStart", "isEnd", "isEventEnd", "isContinuation", "eventColor", "borderLeft", "textOverflow", "whiteSpace", "stopPropagation", "originalStartDate", "originalEndDate", "flex", "opacity", "left", "top", "pointerEvents", "type", "placeholder", "outline", "onFocus", "borderColor", "onBlur", "lineHeight", "category", "name", "is_alert", "className", "category_name", "initialLiked", "user_has_reacted", "initialCount", "reaction_count", "onLikeChange", "liked", "newCount", "allow_comments", "comment_count", "is_published", "borderTop", "paddingTop", "isOpen", "onClose", "onSave", "_c2", "displayName", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Calendar.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { useCalendar, useCalendarCategories, getCalendarDays, isToday, isSameMonth, getMonthName } from '../../hooks/useCalendar';\r\nimport CalendarEventModal from '../../components/admin/modals/CalendarEventModal';\r\nimport type { CalendarEvent, CreateEventData, UpdateEventData } from '../../types/calendar.types';\r\nimport { calendarService } from '../../services/calendarService';\r\nimport { Calendar as CalendarIcon, Search, RefreshCw, Trash2, Edit, Send, Clock, Image as ImageIcon, AlertTriangle, MessageCircle, Heart } from 'lucide-react';\r\nimport CalendarEventLikeButton from '../../components/common/CalendarEventLikeButton';\r\n// Removed calendar attachment imports since this feature is not yet implemented\r\n\r\nconst Calendar: React.FC = React.memo(() => {\r\n  // Add CSS animation for spinning refresh icon\r\n  React.useEffect(() => {\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      @keyframes spin {\r\n        from { transform: rotate(0deg); }\r\n        to { transform: rotate(360deg); }\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n    return () => {\r\n      document.head.removeChild(style);\r\n    };\r\n  }, []);\r\n  const [currentDate, setCurrentDate] = useState(() => new Date());\r\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [saving, setSaving] = useState(false);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(10);\r\n\r\n  // Event attachments state - disabled until backend implementation is complete\r\n  // const [eventAttachments, setEventAttachments] = useState<Record<number, CalendarAttachment[]>>({});\r\n\r\n  // Use the calendar hook\r\n  const {\r\n    events,\r\n    loading,\r\n    error,\r\n    createEvent,\r\n    updateEvent,\r\n    getEventsForDate,\r\n    refresh\r\n  } = useCalendar(currentDate);\r\n\r\n  const { categories } = useCalendarCategories();\r\n\r\n  // Clear messages after 5 seconds\r\n  useEffect(() => {\r\n    if (successMessage || errorMessage || error) {\r\n      const timer = setTimeout(() => {\r\n        setSuccessMessage('');\r\n        setErrorMessage('');\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [successMessage, errorMessage, error]);\r\n\r\n  const handleCreateEvent = useCallback((date?: Date) => {\r\n    setEditingEvent(null);\r\n    setSelectedDate(date || null);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n  const handleEditEvent = useCallback((event: CalendarEvent) => {\r\n    setEditingEvent(event);\r\n    setShowModal(true);\r\n  }, []);\r\n\r\n\r\n\r\n  const handleSaveEvent = useCallback(async (\r\n    data: CreateEventData | UpdateEventData,\r\n    applyPendingDeletes?: () => Promise<void>,\r\n    onComplete?: () => Promise<void>\r\n  ) => {\r\n    setSaving(true);\r\n    try {\r\n      if (editingEvent) {\r\n        await updateEvent(editingEvent.calendar_id, data as UpdateEventData);\r\n\r\n        // Apply pending image deletions AFTER successful update\r\n        if (applyPendingDeletes) {\r\n          console.log('🗑️ Applying pending image deletions after successful update');\r\n          await applyPendingDeletes();\r\n        }\r\n\r\n        setSuccessMessage('Event updated successfully! Calendar refreshed.');\r\n      } else {\r\n        await createEvent(data as CreateEventData);\r\n        setSuccessMessage('Event created successfully! Calendar refreshed.');\r\n      }\r\n\r\n      // Execute completion callback for additional operations\r\n      if (onComplete) {\r\n        await onComplete();\r\n      }\r\n\r\n      // Force refresh the calendar to ensure immediate update\r\n      console.log('🔄 Refreshing calendar to show updated events...');\r\n      setRefreshing(true);\r\n      await refresh();\r\n      setRefreshing(false);\r\n\r\n      // Small delay to ensure smooth UI transition\r\n      setTimeout(() => {\r\n        setShowModal(false);\r\n        setEditingEvent(null);\r\n        setSelectedDate(null);\r\n        setSaving(false);\r\n      }, 100);\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to save event');\r\n      setSaving(false);\r\n    }\r\n  }, [editingEvent, updateEvent, createEvent, refresh]);\r\n\r\n  const handleCloseModal = useCallback(() => {\r\n    setShowModal(false);\r\n    setEditingEvent(null);\r\n    setSelectedDate(null);\r\n  }, []);\r\n\r\n  // Event management functions\r\n  const handlePublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.publishEvent(eventId);\r\n      setSuccessMessage('Event published successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to publish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleUnpublishEvent = useCallback(async (eventId: number) => {\r\n    try {\r\n      await calendarService.unpublishEvent(eventId);\r\n      setSuccessMessage('Event unpublished successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to unpublish event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const handleDeleteEvent = useCallback(async (eventId: number) => {\r\n    // Use window.confirm to avoid ESLint error\r\n    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await calendarService.softDeleteEvent(eventId);\r\n      setSuccessMessage('Event deleted successfully');\r\n      refresh(); // Refresh calendar data\r\n    } catch (error: any) {\r\n      setErrorMessage(error.message || 'Failed to delete event');\r\n    }\r\n  }, [refresh]);\r\n\r\n  const navigateMonth = useCallback((direction: 'prev' | 'next') => {\r\n    const newDate = new Date(currentDate);\r\n    if (direction === 'prev') {\r\n      newDate.setMonth(currentDate.getMonth() - 1);\r\n    } else {\r\n      newDate.setMonth(currentDate.getMonth() + 1);\r\n    }\r\n    setCurrentDate(newDate);\r\n  }, [currentDate]);\r\n\r\n  const goToToday = useCallback(() => {\r\n    setCurrentDate(new Date());\r\n  }, []);\r\n\r\n  const handleDateClick = useCallback((date: Date) => {\r\n    setSelectedDate(date);\r\n    handleCreateEvent(date);\r\n  }, [handleCreateEvent]);\r\n\r\n  const getEventTypeColor = useCallback((event: CalendarEvent) => {\r\n    // Use category color if available, otherwise subcategory color, otherwise default\r\n    return event.category_color || event.subcategory_color || '#22c55e';\r\n  }, []);\r\n\r\n  // Helper function to format event duration\r\n  const getEventDuration = useCallback((event: CalendarEvent) => {\r\n    if (!event.end_date || event.end_date === event.event_date) {\r\n      return 'Single day event';\r\n    }\r\n\r\n    const startDate = new Date(event.event_date);\r\n    const endDate = new Date(event.end_date);\r\n    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days\r\n\r\n    return `${diffDays} day event`;\r\n  }, []);\r\n\r\n  // Helper function to get first two words from event title for calendar chip display\r\n  const getEventChipTitle = useCallback((title: string) => {\r\n    const words = title.trim().split(/\\s+/);\r\n    return words.slice(0, 2).join(' ');\r\n  }, []);\r\n\r\n  // Memoize calendar days to prevent infinite re-renders\r\n  const days = useMemo(() => {\r\n    return getCalendarDays(currentDate.getFullYear(), currentDate.getMonth());\r\n  }, [currentDate.getFullYear(), currentDate.getMonth()]);\r\n\r\n  // Get unique events for the event list (deduplicate multi-day events)\r\n  const uniqueEvents = useMemo(() => {\r\n    const eventMap = new Map();\r\n\r\n    events.forEach(event => {\r\n      // Use calendar_id as the unique identifier\r\n      if (!eventMap.has(event.calendar_id)) {\r\n        eventMap.set(event.calendar_id, event);\r\n      }\r\n    });\r\n\r\n    const uniqueEventsList = Array.from(eventMap.values()).sort((a, b) => {\r\n      // Sort by event_date, then by title\r\n      const dateA = new Date(a.event_date);\r\n      const dateB = new Date(b.event_date);\r\n      if (dateA.getTime() !== dateB.getTime()) {\r\n        return dateA.getTime() - dateB.getTime();\r\n      }\r\n      return a.title.localeCompare(b.title);\r\n    });\r\n\r\n    // Debug: Log deduplication results\r\n    console.log(`📊 Event deduplication: ${events.length} total events → ${uniqueEventsList.length} unique events`);\r\n\r\n    return uniqueEventsList;\r\n  }, [events]);\r\n\r\n  // Filter events based on search term and holiday type\r\n  const filteredEvents = useMemo(() => {\r\n    return uniqueEvents.filter(event => {\r\n      const matchesSearch = !searchTerm ||\r\n        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        event.description?.toLowerCase().includes(searchTerm.toLowerCase());\r\n\r\n      const matchesCategory = !selectedCategory ||\r\n        event.category_id?.toString() === selectedCategory;\r\n\r\n      return matchesSearch && matchesCategory;\r\n    });\r\n  }, [uniqueEvents, searchTerm, selectedCategory]);\r\n\r\n  // Pagination calculations\r\n  const totalItems = filteredEvents.length;\r\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);\r\n\r\n  // Reset to first page when filters change\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [searchTerm, selectedCategory, itemsPerPage]);\r\n\r\n  // Fetch attachments for visible events - disabled until backend implementation is complete\r\n  // useEffect(() => {\r\n  //   const fetchAttachments = async () => {\r\n  //     if (!paginatedEvents || paginatedEvents.length === 0) return;\r\n  //     // Attachment functionality will be implemented later\r\n  //   };\r\n  //   fetchAttachments();\r\n  // }, [paginatedEvents]);\r\n\r\n  // Component to display event images - disabled until backend implementation is complete\r\n  const EventImages: React.FC<{ eventId: number }> = ({ eventId }) => {\r\n    return (\r\n      <div style={{\r\n        width: '60px',\r\n        height: '60px',\r\n        backgroundColor: '#f3f4f6',\r\n        borderRadius: '6px',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        color: '#9ca3af',\r\n        fontSize: '0.75rem'\r\n      }}>\r\n        <ImageIcon size={20} />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Component to display individual image thumbnail - disabled until backend implementation is complete\r\n  // const EventImageThumbnail: React.FC<{ attachment: any }> = ({ attachment }) => {\r\n  //   // This component will be implemented when the backend supports file attachments\r\n  //   return null;\r\n  // };\r\n\r\n  // Pagination component - Always visible\r\n  const PaginationControls = () => {\r\n    // Always show pagination controls, even for single page\r\n    const effectiveTotalPages = Math.max(totalPages, 1); // Ensure at least 1 page\r\n    const effectiveCurrentPage = Math.max(currentPage, 1); // Ensure at least page 1\r\n\r\n    const getPageNumbers = () => {\r\n      const pages = [];\r\n      const maxVisiblePages = 5;\r\n\r\n      if (effectiveTotalPages <= maxVisiblePages) {\r\n        for (let i = 1; i <= effectiveTotalPages; i++) {\r\n          pages.push(i);\r\n        }\r\n      } else {\r\n        if (effectiveCurrentPage <= 3) {\r\n          pages.push(1, 2, 3, 4, '...', effectiveTotalPages);\r\n        } else if (effectiveCurrentPage >= effectiveTotalPages - 2) {\r\n          pages.push(1, '...', effectiveTotalPages - 3, effectiveTotalPages - 2, effectiveTotalPages - 1, effectiveTotalPages);\r\n        } else {\r\n          pages.push(1, '...', effectiveCurrentPage - 1, effectiveCurrentPage, effectiveCurrentPage + 1, '...', effectiveTotalPages);\r\n        }\r\n      }\r\n\r\n      return pages;\r\n    };\r\n\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginTop: '2rem',\r\n        marginBottom: '2rem',\r\n        padding: '1.5rem',\r\n        backgroundColor: 'white',\r\n        borderRadius: '12px',\r\n        border: '1px solid #e5e7eb',\r\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'\r\n      }}>\r\n        {/* Items per page selector */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>Show:</span>\r\n          <select\r\n            value={itemsPerPage}\r\n            onChange={(e) => setItemsPerPage(Number(e.target.value))}\r\n            style={{\r\n              padding: '0.25rem 0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            <option value={5}>5</option>\r\n            <option value={10}>10</option>\r\n            <option value={20}>20</option>\r\n            <option value={50}>50</option>\r\n          </select>\r\n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>per page</span>\r\n        </div>\r\n\r\n        {/* Page info */}\r\n        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\r\n          Showing {Math.max(totalItems > 0 ? startIndex + 1 : 0, 0)}-{Math.min(endIndex, totalItems)} of {totalItems} events\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>\r\n          <button\r\n            onClick={() => setCurrentPage(1)}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            First\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\r\n            disabled={effectiveCurrentPage === 1}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === 1 ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === 1 ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === 1 ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Previous\r\n          </button>\r\n\r\n          {getPageNumbers().map((page, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => typeof page === 'number' && setCurrentPage(page)}\r\n              disabled={page === '...'}\r\n              style={{\r\n                padding: '0.5rem 0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '4px',\r\n                backgroundColor: page === effectiveCurrentPage ? '#3b82f6' : page === '...' ? 'transparent' : 'white',\r\n                color: page === effectiveCurrentPage ? 'white' : page === '...' ? '#9ca3af' : '#374151',\r\n                cursor: page === '...' ? 'default' : 'pointer',\r\n                fontSize: '0.875rem',\r\n                fontWeight: page === effectiveCurrentPage ? '600' : '400'\r\n              }}\r\n            >\r\n              {page}\r\n            </button>\r\n          ))}\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(prev => Math.min(effectiveTotalPages, prev + 1))}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Next\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setCurrentPage(effectiveTotalPages)}\r\n            disabled={effectiveCurrentPage === effectiveTotalPages}\r\n            style={{\r\n              padding: '0.5rem',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '4px',\r\n              backgroundColor: effectiveCurrentPage === effectiveTotalPages ? '#f3f4f6' : 'white',\r\n              color: effectiveCurrentPage === effectiveTotalPages ? '#9ca3af' : '#374151',\r\n              cursor: effectiveCurrentPage === effectiveTotalPages ? 'not-allowed' : 'pointer',\r\n              fontSize: '0.875rem'\r\n            }}\r\n          >\r\n            Last\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }} data-calendar-component=\"main\">\r\n      {/* Success/Error Messages */}\r\n      {successMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#f0fdf4',\r\n          border: '1px solid #bbf7d0',\r\n          color: '#166534',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {successMessage}\r\n        </div>\r\n      )}\r\n      {errorMessage && (\r\n        <div style={{\r\n          marginBottom: '1rem',\r\n          padding: '1rem',\r\n          backgroundColor: '#fef2f2',\r\n          border: '1px solid #fecaca',\r\n          color: '#dc2626',\r\n          borderRadius: '8px'\r\n        }}>\r\n          {errorMessage}\r\n        </div>\r\n      )}\r\n\r\n      {/* Calendar Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '8px',\r\n        padding: '1rem',\r\n        marginBottom: '1rem',\r\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\r\n        border: '1px solid #e8f5e8',\r\n        position: 'relative',\r\n        zIndex: 10\r\n      }}>\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '0.5rem' }}>\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n            <h1 style={{\r\n              fontSize: '1.25rem',\r\n              fontWeight: '600',\r\n              color: '#2d5016',\r\n              margin: 0\r\n            }}>\r\n              <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\r\n                <CalendarIcon size={16} color=\"#1e40af\" />\r\n                School Calendar\r\n                {refreshing && (\r\n                  <RefreshCw\r\n                    size={14}\r\n                    color=\"#22c55e\"\r\n                    style={{\r\n                      animation: 'spin 1s linear infinite',\r\n                      marginLeft: '0.25rem'\r\n                    }}\r\n                  />\r\n                )}\r\n              </span>\r\n            </h1>\r\n            <p style={{\r\n              color: '#6b7280',\r\n              margin: '0 0 0 0.5rem',\r\n              fontSize: '0.875rem'\r\n            }}>\r\n              {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\r\n            <button\r\n              onClick={() => navigateMonth('prev')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              ←\r\n            </button>\r\n\r\n            <button\r\n              onClick={goToToday}\r\n              style={{\r\n                padding: '0.25rem 0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              Today\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => navigateMonth('next')}\r\n              style={{\r\n                padding: '0.25rem',\r\n                color: '#6b7280',\r\n                background: 'none',\r\n                border: 'none',\r\n                cursor: 'pointer',\r\n                fontSize: '1rem',\r\n                transition: 'color 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.color = '#374151'}\r\n              onMouseOut={(e) => e.currentTarget.style.color = '#6b7280'}\r\n            >\r\n              →\r\n            </button>\r\n\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                display: 'inline-flex',\r\n                alignItems: 'center',\r\n                padding: '0.5rem 1rem',\r\n                background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\r\n              }}\r\n              onMouseOver={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(-1px)';\r\n                e.currentTarget.style.boxShadow = '0 2px 6px rgba(34, 197, 94, 0.2)';\r\n              }}\r\n              onMouseOut={(e) => {\r\n                e.currentTarget.style.transform = 'translateY(0)';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              + Add Event\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Calendar Grid */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        overflow: 'hidden'\r\n      }}>\r\n        {/* Calendar Header */}\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(7, 1fr)',\r\n          backgroundColor: '#f9fafb',\r\n          borderBottom: '1px solid #e5e7eb'\r\n        }}>\r\n          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (\r\n            <div key={day} style={{\r\n              padding: '1rem',\r\n              textAlign: 'center',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151'\r\n            }}>\r\n              {day}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Calendar Body */}\r\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)' }}>\r\n          {days.map((date, index) => {\r\n            const dayEvents = getEventsForDate(date);\r\n            const isCurrentMonth = isSameMonth(date, currentDate.getMonth(), currentDate.getFullYear());\r\n            const isTodayDate = isToday(date);\r\n\r\n            // Create unique key based on date to prevent React key conflicts\r\n            // Use date timestamp as key since it's unique and stable across re-renders\r\n            const dateKey = `calendar-day-${date.getTime()}`;\r\n\r\n            return (\r\n              <div\r\n                key={dateKey}\r\n                style={{\r\n                  minHeight: '120px',\r\n                  padding: '0.5rem',\r\n                  borderBottom: '1px solid #e5e7eb',\r\n                  borderRight: index % 7 === 6 ? 'none' : '1px solid #e5e7eb', // Remove right border on last column\r\n                  cursor: 'pointer',\r\n                  backgroundColor: !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white',\r\n                  color: !isCurrentMonth ? '#9ca3af' : '#374151',\r\n                  transition: 'all 0.2s ease',\r\n                  position: 'relative'\r\n                }}\r\n                onClick={() => handleDateClick(date)}\r\n                onMouseOver={(e) => {\r\n                  if (isCurrentMonth) {\r\n                    e.currentTarget.style.backgroundColor = '#f3f4f6';\r\n                    e.currentTarget.style.boxShadow = 'inset 0 0 0 1px #22c55e20';\r\n                  }\r\n                }}\r\n                onMouseOut={(e) => {\r\n                  e.currentTarget.style.backgroundColor = !isCurrentMonth ? '#f9fafb' : isTodayDate ? '#eff6ff' : 'white';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              >\r\n                <div style={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: '500',\r\n                  marginBottom: '0.25rem',\r\n                  color: isTodayDate ? '#2563eb' : 'inherit'\r\n                }}>\r\n                  {date.getDate()}\r\n                </div>\r\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>\r\n                  {dayEvents.slice(0, 3).map((event) => {\r\n                    // Determine styling for multi-day events\r\n                    const isMultiDay = event.isMultiDay;\r\n                    const isStart = event.isEventStart;\r\n                    const isEnd = event.isEventEnd;\r\n                    const isContinuation = isMultiDay && !isStart && !isEnd;\r\n                    const eventColor = getEventTypeColor(event);\r\n\r\n                    return (\r\n                      <div\r\n                        key={`event-${event.calendar_id}-${date.getTime()}`}\r\n                        style={{\r\n                          fontSize: '0.75rem',\r\n                          padding: '0.25rem 0.5rem',\r\n                          borderRadius: isMultiDay ? (isStart ? '6px 2px 2px 6px' : isEnd ? '2px 6px 6px 2px' : '2px') : '6px',\r\n                          backgroundColor: eventColor + (isContinuation ? '25' : '15'),\r\n                          border: `1px solid ${eventColor}`,\r\n                          borderLeft: isStart || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          borderRight: isEnd || !isMultiDay ? `4px solid ${eventColor}` : `1px solid ${eventColor}`,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis',\r\n                          whiteSpace: 'nowrap',\r\n                          cursor: 'pointer',\r\n                          position: 'relative',\r\n                          color: '#374151',\r\n                          fontWeight: '500',\r\n                          transition: 'all 0.2s ease',\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: '0.25rem'\r\n                        }}\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleEditEvent(event);\r\n                        }}\r\n                        onMouseOver={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + '30';\r\n                          e.currentTarget.style.transform = 'translateY(-1px)';\r\n                          e.currentTarget.style.boxShadow = `0 2px 8px ${eventColor}40`;\r\n                        }}\r\n                        onMouseOut={(e) => {\r\n                          e.currentTarget.style.backgroundColor = eventColor + (isContinuation ? '25' : '15');\r\n                          e.currentTarget.style.transform = 'translateY(0)';\r\n                          e.currentTarget.style.boxShadow = 'none';\r\n                        }}\r\n                        title={isMultiDay ? `${event.title} (${event.originalStartDate} to ${event.originalEndDate})` : event.title}\r\n                      >\r\n                        {/* Start indicator */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▶\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Continuation indicator */}\r\n                        {isContinuation && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ▬\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* End indicator */}\r\n                        {isEnd && isMultiDay && !isStart && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem'\r\n                          }}>\r\n                            ◀\r\n                          </span>\r\n                        )}\r\n\r\n                        {/* Event title */}\r\n                        <span style={{\r\n                          flex: 1,\r\n                          overflow: 'hidden',\r\n                          textOverflow: 'ellipsis'\r\n                        }}>\r\n                          {getEventChipTitle(event.title)}\r\n                        </span>\r\n\r\n                        {/* End arrow for start day */}\r\n                        {isStart && isMultiDay && (\r\n                          <span style={{\r\n                            color: eventColor,\r\n                            fontWeight: 'bold',\r\n                            fontSize: '0.7rem',\r\n                            opacity: 0.7\r\n                          }}>\r\n                            →\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {dayEvents.length > 3 && (\r\n                    <div style={{\r\n                      fontSize: '0.75rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      +{dayEvents.length - 3} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Filters */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        margin: '2rem 0',\r\n        padding: '1.5rem',\r\n        backgroundColor: '#f8fafc'\r\n      }}>\r\n        <div style={{\r\n          display: 'grid',\r\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\r\n          gap: '1.5rem',\r\n          alignItems: 'end'\r\n        }}>\r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Search Events\r\n            </label>\r\n            <div style={{ position: 'relative' }}>\r\n              <Search\r\n                size={16}\r\n                color=\"#9ca3af\"\r\n                style={{\r\n                  position: 'absolute',\r\n                  left: '0.75rem',\r\n                  top: '50%',\r\n                  transform: 'translateY(-50%)',\r\n                  pointerEvents: 'none'\r\n                }}\r\n              />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                style={{\r\n                  width: '100%',\r\n                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',\r\n                  border: '1px solid #d1d5db',\r\n                  borderRadius: '8px',\r\n                  fontSize: '0.875rem',\r\n                  outline: 'none',\r\n                  transition: 'border-color 0.2s ease, box-shadow 0.2s ease'\r\n                }}\r\n                onFocus={(e) => {\r\n                  e.currentTarget.style.borderColor = '#22c55e';\r\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n                }}\r\n                onBlur={(e) => {\r\n                  e.currentTarget.style.borderColor = '#d1d5db';\r\n                  e.currentTarget.style.boxShadow = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n          \r\n          <div>\r\n            <label style={{\r\n              display: 'block',\r\n              fontSize: '0.875rem',\r\n              fontWeight: '500',\r\n              color: '#374151',\r\n              marginBottom: '0.5rem'\r\n            }}>\r\n              Category\r\n            </label>\r\n            <select\r\n              value={selectedCategory}\r\n              onChange={(e) => setSelectedCategory(e.target.value)}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                border: '1px solid #d1d5db',\r\n                borderRadius: '8px',\r\n                fontSize: '0.875rem',\r\n                outline: 'none',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease, box-shadow 0.2s ease',\r\n                minHeight: '2.5rem',\r\n                lineHeight: '1.2'\r\n              }}\r\n              onFocus={(e) => {\r\n                e.currentTarget.style.borderColor = '#22c55e';\r\n                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\r\n              }}\r\n              onBlur={(e) => {\r\n                e.currentTarget.style.borderColor = '#d1d5db';\r\n                e.currentTarget.style.boxShadow = 'none';\r\n              }}\r\n            >\r\n              <option value=\"\">All Categories</option>\r\n              {categories.map((category: any) => (\r\n                <option key={category.category_id} value={category.category_id}>\r\n                  {category.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n          \r\n          <div>\r\n            <button\r\n              onClick={refresh}\r\n              style={{\r\n                width: '100%',\r\n                padding: '0.75rem',\r\n                backgroundColor: '#f3f4f6',\r\n                color: '#374151',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500',\r\n                fontSize: '0.875rem',\r\n                transition: 'background-color 0.2s ease',\r\n                height: '2.5rem',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                gap: '0.5rem'\r\n              }}\r\n              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}\r\n              onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}\r\n            >\r\n              <RefreshCw size={16} />\r\n              Refresh\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Event List Section */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderRadius: '16px',\r\n        padding: '2rem',\r\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n        border: '1px solid #e8f5e8',\r\n        marginBottom: '1rem'\r\n      }}>\r\n        <div style={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          marginBottom: '1.5rem'\r\n        }}>\r\n          <h2 style={{\r\n            fontSize: '1.5rem',\r\n            fontWeight: '600',\r\n            color: '#2d5016',\r\n            margin: 0\r\n          }}>\r\n            Events for {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}\r\n          </h2>\r\n          <div style={{\r\n            fontSize: '0.875rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found\r\n          </div>\r\n        </div>\r\n\r\n        {loading ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <RefreshCw size={24} style={{ marginBottom: '1rem', animation: 'spin 1s linear infinite' }} />\r\n            <p>Loading events...</p>\r\n          </div>\r\n        ) : filteredEvents.length === 0 ? (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '3rem',\r\n            color: '#6b7280'\r\n          }}>\r\n            <CalendarIcon size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\r\n            <p>No events found for this month</p>\r\n            <button\r\n              onClick={() => handleCreateEvent()}\r\n              style={{\r\n                marginTop: '1rem',\r\n                padding: '0.75rem 1.5rem',\r\n                backgroundColor: '#22c55e',\r\n                color: 'white',\r\n                border: 'none',\r\n                borderRadius: '8px',\r\n                cursor: 'pointer',\r\n                fontWeight: '500'\r\n              }}\r\n            >\r\n              Create First Event\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div style={{\r\n            display: 'grid',\r\n            gap: '1rem'\r\n          }}>\r\n            {paginatedEvents.map((event) => (\r\n              <div\r\n                key={`unique-event-${event.calendar_id}`}\r\n                style={{\r\n                  border: '1px solid #e5e7eb',\r\n                  borderRadius: '12px',\r\n                  padding: '1.5rem',\r\n                  backgroundColor: 'white',\r\n                  transition: 'all 0.2s ease'\r\n                }}\r\n              >\r\n                <div style={{\r\n                  display: 'flex',\r\n                  justifyContent: 'space-between',\r\n                  alignItems: 'flex-start',\r\n                  marginBottom: '1rem'\r\n                }}>\r\n                  <div style={{ flex: 1 }}>\r\n                    <h4 style={{\r\n                      fontSize: '1.125rem',\r\n                      fontWeight: '600',\r\n                      color: '#374151',\r\n                      margin: '0 0 0.5rem 0',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '0.5rem'\r\n                    }}>\r\n                      {event.title}\r\n                      {(event as any).is_alert && (\r\n                        <AlertTriangle size={16} color=\"#ef4444\" />\r\n                      )}\r\n                    </h4>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '1rem',\r\n                      fontSize: '0.875rem',\r\n                      color: '#6b7280'\r\n                    }}>\r\n                      <span className=\"flex items-center gap-2\">\r\n                        <CalendarIcon className=\"h-4 w-4\" />\r\n                        {event.event_date}\r\n                      </span>\r\n                      {event.end_date && event.end_date !== event.event_date && (\r\n                        <span>→ {event.end_date}</span>\r\n                      )}\r\n                      <span style={{\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem',\r\n                        fontWeight: '500'\r\n                      }}>\r\n                        {getEventDuration(event)}\r\n                      </span>\r\n                      <span style={{\r\n                        backgroundColor: getEventTypeColor(event),\r\n                        color: 'white',\r\n                        padding: '0.25rem 0.5rem',\r\n                        borderRadius: '4px',\r\n                        fontSize: '0.75rem'\r\n                      }}>\r\n                        {event.category_name || 'Uncategorized'}\r\n                      </span>\r\n\r\n                    </div>\r\n                    {event.description && (\r\n                      <p style={{\r\n                        fontSize: '0.875rem',\r\n                        color: '#6b7280',\r\n                        margin: '0.5rem 0 0 0',\r\n                        lineHeight: '1.5'\r\n                      }}>\r\n                        {event.description}\r\n                      </p>\r\n                    )}\r\n\r\n                    {/* Reaction and Comment Counts */}\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '1rem',\r\n                      marginTop: '0.75rem'\r\n                    }}>\r\n                      <CalendarEventLikeButton\r\n                        eventId={event.calendar_id}\r\n                        initialLiked={(event as any).user_has_reacted || false}\r\n                        initialCount={(event as any).reaction_count || 0}\r\n                        size=\"small\"\r\n                        onLikeChange={(liked: boolean, newCount: number) => {\r\n                          // Update the event in the local state if needed\r\n                          console.log(`Event ${event.calendar_id} like changed:`, { liked, newCount });\r\n                        }}\r\n                      />\r\n                      {(event as any).allow_comments && (\r\n                        <span style={{\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: '0.25rem',\r\n                          fontSize: '0.875rem',\r\n                          color: '#6b7280'\r\n                        }}>\r\n                          <MessageCircle size={14} />\r\n                          {(event as any).comment_count || 0} comments\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div style={{\r\n                    display: 'flex',\r\n                    gap: '0.5rem',\r\n                    marginLeft: '1rem'\r\n                  }}>\r\n                    <button\r\n                      onClick={() => handleEditEvent(event)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#f3f4f6',\r\n                        color: '#374151',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Edit event\"\r\n                    >\r\n                      <Edit size={16} />\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => (event as any).is_published\r\n                        ? handleUnpublishEvent(event.calendar_id)\r\n                        : handlePublishEvent(event.calendar_id)\r\n                      }\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: (event as any).is_published ? '#fef3c7' : '#dcfce7',\r\n                        color: (event as any).is_published ? '#d97706' : '#16a34a',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title={(event as any).is_published ? 'Unpublish event' : 'Publish event'}\r\n                    >\r\n                      {(event as any).is_published ? <Clock size={16} /> : <Send size={16} />}\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => handleDeleteEvent(event.calendar_id)}\r\n                      style={{\r\n                        padding: '0.5rem',\r\n                        backgroundColor: '#fef2f2',\r\n                        color: '#dc2626',\r\n                        border: 'none',\r\n                        borderRadius: '6px',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}\r\n                      title=\"Delete event\"\r\n                    >\r\n                      <Trash2 size={16} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div style={{\r\n                  borderTop: '1px solid #f3f4f6',\r\n                  paddingTop: '1rem',\r\n                  marginTop: '1rem'\r\n                }}>\r\n                  <div style={{\r\n                    fontSize: '0.875rem',\r\n                    fontWeight: '500',\r\n                    color: '#374151',\r\n                    marginBottom: '0.5rem'\r\n                  }}>\r\n                    Event Images\r\n                  </div>\r\n                  <EventImages eventId={event.calendar_id} />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination Controls - Outside the event list container */}\r\n      <PaginationControls />\r\n\r\n      {/* Calendar Event Modal */}\r\n      <CalendarEventModal\r\n        isOpen={showModal}\r\n        onClose={handleCloseModal}\r\n        onSave={handleSaveEvent}\r\n        event={editingEvent}\r\n        selectedDate={selectedDate}\r\n        loading={saving || loading}\r\n      />\r\n    </div>\r\n  );\r\n});\r\n\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport default Calendar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,QAAQ,yBAAyB;AACjI,OAAOC,kBAAkB,MAAM,kDAAkD;AAEjF,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,IAAIC,SAAS,EAAEC,aAAa,EAAEC,aAAa,QAAe,cAAc;AAC9J,OAAOC,uBAAuB,MAAM,iDAAiD;AACrF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMd,QAAkB,gBAAAe,EAAA,cAAG5B,KAAK,CAAC6B,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC1C;EACA5B,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAM6B,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAChC,OAAO,MAAM;MACXC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,MAAM,IAAIuC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkD,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA;;EAEA;EACA,MAAM;IACJ8D,MAAM;IACNC,OAAO;IACPC,KAAK;IACLC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC;EACF,CAAC,GAAGhE,WAAW,CAACiC,WAAW,CAAC;EAE5B,MAAM;IAAEgC;EAAW,CAAC,GAAGhE,qBAAqB,CAAC,CAAC;;EAE9C;EACAJ,SAAS,CAAC,MAAM;IACd,IAAI6C,cAAc,IAAIE,YAAY,IAAIgB,KAAK,EAAE;MAC3C,MAAMM,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BxB,iBAAiB,CAAC,EAAE,CAAC;QACrBE,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMuB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACxB,cAAc,EAAEE,YAAY,EAAEgB,KAAK,CAAC,CAAC;EAEzC,MAAMS,iBAAiB,GAAGtE,WAAW,CAAEuE,IAAW,IAAK;IACrD7B,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAACiC,IAAI,IAAI,IAAI,CAAC;IAC7B/B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgC,eAAe,GAAGxE,WAAW,CAAEyE,KAAoB,IAAK;IAC5D/B,eAAe,CAAC+B,KAAK,CAAC;IACtBjC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMkC,eAAe,GAAG1E,WAAW,CAAC,OAClC2E,IAAuC,EACvCC,mBAAyC,EACzCC,UAAgC,KAC7B;IACH7B,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,IAAIP,YAAY,EAAE;QAChB,MAAMsB,WAAW,CAACtB,YAAY,CAACqC,WAAW,EAAEH,IAAuB,CAAC;;QAEpE;QACA,IAAIC,mBAAmB,EAAE;UACvBG,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3E,MAAMJ,mBAAmB,CAAC,CAAC;QAC7B;QAEAhC,iBAAiB,CAAC,iDAAiD,CAAC;MACtE,CAAC,MAAM;QACL,MAAMkB,WAAW,CAACa,IAAuB,CAAC;QAC1C/B,iBAAiB,CAAC,iDAAiD,CAAC;MACtE;;MAEA;MACA,IAAIiC,UAAU,EAAE;QACd,MAAMA,UAAU,CAAC,CAAC;MACpB;;MAEA;MACAE,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D9B,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMe,OAAO,CAAC,CAAC;MACff,aAAa,CAAC,KAAK,CAAC;;MAEpB;MACAkB,UAAU,CAAC,MAAM;QACf5B,YAAY,CAAC,KAAK,CAAC;QACnBE,eAAe,CAAC,IAAI,CAAC;QACrBJ,eAAe,CAAC,IAAI,CAAC;QACrBU,SAAS,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOa,KAAU,EAAE;MACnBf,eAAe,CAACe,KAAK,CAACoB,OAAO,IAAI,sBAAsB,CAAC;MACxDjC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,EAAE,CAACP,YAAY,EAAEsB,WAAW,EAAED,WAAW,EAAEG,OAAO,CAAC,CAAC;EAErD,MAAMiB,gBAAgB,GAAGlF,WAAW,CAAC,MAAM;IACzCwC,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,IAAI,CAAC;IACrBJ,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6C,kBAAkB,GAAGnF,WAAW,CAAC,MAAOoF,OAAe,IAAK;IAChE,IAAI;MACF,MAAM5E,eAAe,CAAC6E,YAAY,CAACD,OAAO,CAAC;MAC3CxC,iBAAiB,CAAC,8BAA8B,CAAC;MACjDqB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBf,eAAe,CAACe,KAAK,CAACoB,OAAO,IAAI,yBAAyB,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMqB,oBAAoB,GAAGtF,WAAW,CAAC,MAAOoF,OAAe,IAAK;IAClE,IAAI;MACF,MAAM5E,eAAe,CAAC+E,cAAc,CAACH,OAAO,CAAC;MAC7CxC,iBAAiB,CAAC,gCAAgC,CAAC;MACnDqB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBf,eAAe,CAACe,KAAK,CAACoB,OAAO,IAAI,2BAA2B,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMuB,iBAAiB,GAAGxF,WAAW,CAAC,MAAOoF,OAAe,IAAK;IAC/D;IACA,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,2EAA2E,CAAC,EAAE;MAChG;IACF;IAEA,IAAI;MACF,MAAMlF,eAAe,CAACmF,eAAe,CAACP,OAAO,CAAC;MAC9CxC,iBAAiB,CAAC,4BAA4B,CAAC;MAC/CqB,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBf,eAAe,CAACe,KAAK,CAACoB,OAAO,IAAI,wBAAwB,CAAC;IAC5D;EACF,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAM2B,aAAa,GAAG5F,WAAW,CAAE6F,SAA0B,IAAK;IAChE,MAAMC,OAAO,GAAG,IAAI1D,IAAI,CAACF,WAAW,CAAC;IACrC,IAAI2D,SAAS,KAAK,MAAM,EAAE;MACxBC,OAAO,CAACC,QAAQ,CAAC7D,WAAW,CAAC8D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLF,OAAO,CAACC,QAAQ,CAAC7D,WAAW,CAAC8D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA7D,cAAc,CAAC2D,OAAO,CAAC;EACzB,CAAC,EAAE,CAAC5D,WAAW,CAAC,CAAC;EAEjB,MAAM+D,SAAS,GAAGjG,WAAW,CAAC,MAAM;IAClCmC,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8D,eAAe,GAAGlG,WAAW,CAAEuE,IAAU,IAAK;IAClDjC,eAAe,CAACiC,IAAI,CAAC;IACrBD,iBAAiB,CAACC,IAAI,CAAC;EACzB,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAM6B,iBAAiB,GAAGnG,WAAW,CAAEyE,KAAoB,IAAK;IAC9D;IACA,OAAOA,KAAK,CAAC2B,cAAc,IAAI3B,KAAK,CAAC4B,iBAAiB,IAAI,SAAS;EACrE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAGtG,WAAW,CAAEyE,KAAoB,IAAK;IAC7D,IAAI,CAACA,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,EAAE;MAC1D,OAAO,kBAAkB;IAC3B;IAEA,MAAMC,SAAS,GAAG,IAAIrE,IAAI,CAACqC,KAAK,CAAC+B,UAAU,CAAC;IAC5C,MAAME,OAAO,GAAG,IAAItE,IAAI,CAACqC,KAAK,CAAC8B,QAAQ,CAAC;IACxC,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAGL,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;IAClE,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAElE,OAAO,GAAGI,QAAQ,YAAY;EAChC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,iBAAiB,GAAGjH,WAAW,CAAEkH,KAAa,IAAK;IACvD,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;IACvC,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,IAAI,GAAGzH,OAAO,CAAC,MAAM;IACzB,OAAOI,eAAe,CAAC+B,WAAW,CAACuF,WAAW,CAAC,CAAC,EAAEvF,WAAW,CAAC8D,QAAQ,CAAC,CAAC,CAAC;EAC3E,CAAC,EAAE,CAAC9D,WAAW,CAACuF,WAAW,CAAC,CAAC,EAAEvF,WAAW,CAAC8D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM0B,YAAY,GAAG3H,OAAO,CAAC,MAAM;IACjC,MAAM4H,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE1BjE,MAAM,CAACkE,OAAO,CAACpD,KAAK,IAAI;MACtB;MACA,IAAI,CAACkD,QAAQ,CAACG,GAAG,CAACrD,KAAK,CAACK,WAAW,CAAC,EAAE;QACpC6C,QAAQ,CAACI,GAAG,CAACtD,KAAK,CAACK,WAAW,EAAEL,KAAK,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,MAAMuD,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACP,QAAQ,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpE;MACA,MAAMC,KAAK,GAAG,IAAInG,IAAI,CAACiG,CAAC,CAAC7B,UAAU,CAAC;MACpC,MAAMgC,KAAK,GAAG,IAAIpG,IAAI,CAACkG,CAAC,CAAC9B,UAAU,CAAC;MACpC,IAAI+B,KAAK,CAACzB,OAAO,CAAC,CAAC,KAAK0B,KAAK,CAAC1B,OAAO,CAAC,CAAC,EAAE;QACvC,OAAOyB,KAAK,CAACzB,OAAO,CAAC,CAAC,GAAG0B,KAAK,CAAC1B,OAAO,CAAC,CAAC;MAC1C;MACA,OAAOuB,CAAC,CAACnB,KAAK,CAACuB,aAAa,CAACH,CAAC,CAACpB,KAAK,CAAC;IACvC,CAAC,CAAC;;IAEF;IACAnC,OAAO,CAACC,GAAG,CAAC,2BAA2BrB,MAAM,CAAC+E,MAAM,mBAAmBV,gBAAgB,CAACU,MAAM,gBAAgB,CAAC;IAE/G,OAAOV,gBAAgB;EACzB,CAAC,EAAE,CAACrE,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMgF,cAAc,GAAG5I,OAAO,CAAC,MAAM;IACnC,OAAO2H,YAAY,CAACkB,MAAM,CAACnE,KAAK,IAAI;MAAA,IAAAoE,kBAAA,EAAAC,kBAAA;MAClC,MAAMC,aAAa,GAAG,CAAC5F,UAAU,IAC/BsB,KAAK,CAACyC,KAAK,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9F,UAAU,CAAC6F,WAAW,CAAC,CAAC,CAAC,MAAAH,kBAAA,GAC5DpE,KAAK,CAACyE,WAAW,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9F,UAAU,CAAC6F,WAAW,CAAC,CAAC,CAAC;MAErE,MAAMG,eAAe,GAAG,CAAC9F,gBAAgB,IACvC,EAAAyF,kBAAA,GAAArE,KAAK,CAAC2E,WAAW,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBO,QAAQ,CAAC,CAAC,MAAKhG,gBAAgB;MAEpD,OAAO0F,aAAa,IAAII,eAAe;IACzC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,YAAY,EAAEvE,UAAU,EAAEE,gBAAgB,CAAC,CAAC;;EAEhD;EACA,MAAMiG,UAAU,GAAGX,cAAc,CAACD,MAAM;EACxC,MAAMa,UAAU,GAAG3C,IAAI,CAACI,IAAI,CAACsC,UAAU,GAAG7F,YAAY,CAAC;EACvD,MAAM+F,UAAU,GAAG,CAACjG,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMgG,QAAQ,GAAGD,UAAU,GAAG/F,YAAY;EAC1C,MAAMiG,eAAe,GAAGf,cAAc,CAACrB,KAAK,CAACkC,UAAU,EAAEC,QAAQ,CAAC;;EAElE;EACA3J,SAAS,CAAC,MAAM;IACd0D,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,UAAU,EAAEE,gBAAgB,EAAEI,YAAY,CAAC,CAAC;;EAEhD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMkG,WAA0C,GAAGA,CAAC;IAAEvE;EAAQ,CAAC,KAAK;IAClE,oBACE7D,OAAA;MAAKI,KAAK,EAAE;QACViI,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,eACA9I,OAAA,CAACL,SAAS;QAACoJ,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAEV,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,mBAAmB,GAAGhE,IAAI,CAACiE,GAAG,CAACtB,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMuB,oBAAoB,GAAGlE,IAAI,CAACiE,GAAG,CAACtH,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvD,MAAMwH,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,KAAK,GAAG,EAAE;MAChB,MAAMC,eAAe,GAAG,CAAC;MAEzB,IAAIL,mBAAmB,IAAIK,eAAe,EAAE;QAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,mBAAmB,EAAEM,CAAC,EAAE,EAAE;UAC7CF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACL,IAAIJ,oBAAoB,IAAI,CAAC,EAAE;UAC7BE,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAEP,mBAAmB,CAAC;QACpD,CAAC,MAAM,IAAIE,oBAAoB,IAAIF,mBAAmB,GAAG,CAAC,EAAE;UAC1DI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEP,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,GAAG,CAAC,EAAEA,mBAAmB,CAAC;QACtH,CAAC,MAAM;UACLI,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,KAAK,EAAEL,oBAAoB,GAAG,CAAC,EAAEA,oBAAoB,EAAEA,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAEF,mBAAmB,CAAC;QAC5H;MACF;MAEA,OAAOI,KAAK;IACd,CAAC;IAED,oBACEzJ,OAAA;MAAKI,KAAK,EAAE;QACVqI,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBmB,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,QAAQ;QACjBxB,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBwB,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAnB,QAAA,gBAEA9I,OAAA;QAAKI,KAAK,EAAE;UAAEqI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnE9I,OAAA;UAAMI,KAAK,EAAE;YAAEyI,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrEnJ,OAAA;UACEmK,KAAK,EAAEjI,YAAa;UACpBkI,QAAQ,EAAGC,CAAC,IAAKlI,eAAe,CAACmI,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;UACzD/J,KAAK,EAAE;YACL2J,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBK,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,gBAEF9I,OAAA;YAAQmK,KAAK,EAAE,CAAE;YAAArB,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5BnJ,OAAA;YAAQmK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BnJ,OAAA;YAAQmK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BnJ,OAAA;YAAQmK,KAAK,EAAE,EAAG;YAAArB,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACTnJ,OAAA;UAAMI,KAAK,EAAE;YAAEyI,QAAQ,EAAE,UAAU;YAAED,KAAK,EAAE;UAAU,CAAE;UAAAE,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAGNnJ,OAAA;QAAKI,KAAK,EAAE;UAAEyI,QAAQ,EAAE,UAAU;UAAED,KAAK,EAAE;QAAU,CAAE;QAAAE,QAAA,GAAC,UAC9C,EAACzD,IAAI,CAACiE,GAAG,CAACvB,UAAU,GAAG,CAAC,GAAGE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAC,GAAC,EAAC5C,IAAI,CAACmF,GAAG,CAACtC,QAAQ,EAAEH,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,SAC7G;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNnJ,OAAA;QAAKI,KAAK,EAAE;UAAEqI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE,SAAS;UAAEO,QAAQ,EAAE;QAAO,CAAE;QAAA3B,QAAA,gBACtF9I,OAAA;UACE0K,OAAO,EAAEA,CAAA,KAAMzI,cAAc,CAAC,CAAC,CAAE;UACjC0I,QAAQ,EAAEpB,oBAAoB,KAAK,CAAE;UACrCnJ,KAAK,EAAE;YACL2J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjEX,KAAK,EAAEW,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDqB,MAAM,EAAErB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9DV,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnJ,OAAA;UACE0K,OAAO,EAAEA,CAAA,KAAMzI,cAAc,CAAC4I,IAAI,IAAIxF,IAAI,CAACiE,GAAG,CAAC,CAAC,EAAEuB,IAAI,GAAG,CAAC,CAAC,CAAE;UAC7DF,QAAQ,EAAEpB,oBAAoB,KAAK,CAAE;UACrCnJ,KAAK,EAAE;YACL2J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;YACjEX,KAAK,EAAEW,oBAAoB,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACzDqB,MAAM,EAAErB,oBAAoB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YAC9DV,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERK,cAAc,CAAC,CAAC,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChChL,OAAA;UAEE0K,OAAO,EAAEA,CAAA,KAAM,OAAOK,IAAI,KAAK,QAAQ,IAAI9I,cAAc,CAAC8I,IAAI,CAAE;UAChEJ,QAAQ,EAAEI,IAAI,KAAK,KAAM;UACzB3K,KAAK,EAAE;YACL2J,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEwC,IAAI,KAAKxB,oBAAoB,GAAG,SAAS,GAAGwB,IAAI,KAAK,KAAK,GAAG,aAAa,GAAG,OAAO;YACrGnC,KAAK,EAAEmC,IAAI,KAAKxB,oBAAoB,GAAG,OAAO,GAAGwB,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YACvFH,MAAM,EAAEG,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;YAC9ClC,QAAQ,EAAE,UAAU;YACpBoC,UAAU,EAAEF,IAAI,KAAKxB,oBAAoB,GAAG,KAAK,GAAG;UACtD,CAAE;UAAAT,QAAA,EAEDiC;QAAI,GAdAC,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeJ,CACT,CAAC,eAEFnJ,OAAA;UACE0K,OAAO,EAAEA,CAAA,KAAMzI,cAAc,CAAC4I,IAAI,IAAIxF,IAAI,CAACmF,GAAG,CAACnB,mBAAmB,EAAEwB,IAAI,GAAG,CAAC,CAAC,CAAE;UAC/EF,QAAQ,EAAEpB,oBAAoB,KAAKF,mBAAoB;UACvDjJ,KAAK,EAAE;YACL2J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnFT,KAAK,EAAEW,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EuB,MAAM,EAAErB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChFR,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnJ,OAAA;UACE0K,OAAO,EAAEA,CAAA,KAAMzI,cAAc,CAACoH,mBAAmB,CAAE;UACnDsB,QAAQ,EAAEpB,oBAAoB,KAAKF,mBAAoB;UACvDjJ,KAAK,EAAE;YACL2J,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEgB,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,OAAO;YACnFT,KAAK,EAAEW,oBAAoB,KAAKF,mBAAmB,GAAG,SAAS,GAAG,SAAS;YAC3EuB,MAAM,EAAErB,oBAAoB,KAAKF,mBAAmB,GAAG,aAAa,GAAG,SAAS;YAChFR,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEnJ,OAAA;IAAKI,KAAK,EAAE;MAAE8K,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAC,2BAAwB,MAAM;IAAArC,QAAA,GAEjF1H,cAAc,iBACbpB,OAAA;MAAKI,KAAK,EAAE;QACV0J,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfxB,eAAe,EAAE,SAAS;QAC1ByB,MAAM,EAAE,mBAAmB;QAC3BpB,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACC1H;IAAc;MAAA4H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACN,EACA7H,YAAY,iBACXtB,OAAA;MAAKI,KAAK,EAAE;QACV0J,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfxB,eAAe,EAAE,SAAS;QAC1ByB,MAAM,EAAE,mBAAmB;QAC3BpB,KAAK,EAAE,SAAS;QAChBJ,YAAY,EAAE;MAChB,CAAE;MAAAM,QAAA,EACCxH;IAAY;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDnJ,OAAA;MAAKI,KAAK,EAAE;QACVgL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,KAAK;QACnBuB,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,MAAM;QACpBG,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BqB,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE;MACV,CAAE;MAAAxC,QAAA,eACA9I,OAAA;QAAKI,KAAK,EAAE;UAAEqI,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACpG9I,OAAA;UAAKI,KAAK,EAAE;YAAEqI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwB,GAAG,EAAE;UAAU,CAAE;UAAApB,QAAA,gBACpE9I,OAAA;YAAII,KAAK,EAAE;cACTyI,QAAQ,EAAE,SAAS;cACnBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBuC,MAAM,EAAE;YACV,CAAE;YAAArC,QAAA,eACA9I,OAAA;cAAMI,KAAK,EAAE;gBAAEqI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEwB,GAAG,EAAE;cAAU,CAAE;cAAApB,QAAA,gBACrE9I,OAAA,CAACb,YAAY;gBAAC4J,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE1C,EAACzH,UAAU,iBACT1B,OAAA,CAACX,SAAS;gBACR0J,IAAI,EAAE,EAAG;gBACTH,KAAK,EAAC,SAAS;gBACfxI,KAAK,EAAE;kBACLmL,SAAS,EAAE,yBAAyB;kBACpCC,UAAU,EAAE;gBACd;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLnJ,OAAA;YAAGI,KAAK,EAAE;cACRwI,KAAK,EAAE,SAAS;cAChBuC,MAAM,EAAE,cAAc;cACtBtC,QAAQ,EAAE;YACZ,CAAE;YAAAC,QAAA,GACC/J,YAAY,CAAC4B,WAAW,CAAC8D,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAC9D,WAAW,CAACuF,WAAW,CAAC,CAAC;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENnJ,OAAA;UAAKI,KAAK,EAAE;YAAEqI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwB,GAAG,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACnE9I,OAAA;YACE0K,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAAC,MAAM,CAAE;YACrCjE,KAAK,EAAE;cACL2J,OAAO,EAAE,SAAS;cAClBnB,KAAK,EAAE,SAAS;cAChBwC,UAAU,EAAE,MAAM;cAClBpB,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB/B,QAAQ,EAAE,MAAM;cAChB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACwI,KAAK,GAAG,SAAU;YAC5DgD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACwI,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnJ,OAAA;YACE0K,OAAO,EAAEhG,SAAU;YACnBtE,KAAK,EAAE;cACL2J,OAAO,EAAE,iBAAiB;cAC1BxB,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,SAAU;YACtEqD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,SAAU;YAAAO,QAAA,EACtE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnJ,OAAA;YACE0K,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAAC,MAAM,CAAE;YACrCjE,KAAK,EAAE;cACL2J,OAAO,EAAE,SAAS;cAClBnB,KAAK,EAAE,SAAS;cAChBwC,UAAU,EAAE,MAAM;cAClBpB,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB/B,QAAQ,EAAE,MAAM;cAChB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACwI,KAAK,GAAG,SAAU;YAC5DgD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACwI,KAAK,GAAG,SAAU;YAAAE,QAAA,EAC5D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnJ,OAAA;YACE0K,OAAO,EAAEA,CAAA,KAAM3H,iBAAiB,CAAC,CAAE;YACnC3C,KAAK,EAAE;cACLqI,OAAO,EAAE,aAAa;cACtBC,UAAU,EAAE,QAAQ;cACpBqB,OAAO,EAAE,aAAa;cACtBqB,UAAU,EAAE,mDAAmD;cAC/DxC,KAAK,EAAE,OAAO;cACdoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGrB,CAAC,IAAK;cAClBA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACyL,SAAS,GAAG,kBAAkB;cACpDxB,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACF2B,UAAU,EAAGvB,CAAC,IAAK;cACjBA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACyL,SAAS,GAAG,eAAe;cACjDxB,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnJ,OAAA;MAAKI,KAAK,EAAE;QACVgL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpByB,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3B8B,QAAQ,EAAE;MACZ,CAAE;MAAAhD,QAAA,gBAEA9I,OAAA;QAAKI,KAAK,EAAE;UACVqI,OAAO,EAAE,MAAM;UACfsD,mBAAmB,EAAE,gBAAgB;UACrCxD,eAAe,EAAE,SAAS;UAC1ByD,YAAY,EAAE;QAChB,CAAE;QAAAlD,QAAA,EACC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACgC,GAAG,CAAEmB,GAAG,iBACzDjM,OAAA;UAAeI,KAAK,EAAE;YACpB2J,OAAO,EAAE,MAAM;YACfmC,SAAS,EAAE,QAAQ;YACnBrD,QAAQ,EAAE,UAAU;YACpBoC,UAAU,EAAE,KAAK;YACjBrC,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,EACCmD;QAAG,GAPIA,GAAG;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNnJ,OAAA;QAAKI,KAAK,EAAE;UAAEqI,OAAO,EAAE,MAAM;UAAEsD,mBAAmB,EAAE;QAAiB,CAAE;QAAAjD,QAAA,EACpE7C,IAAI,CAAC6E,GAAG,CAAC,CAAC9H,IAAI,EAAEgI,KAAK,KAAK;UACzB,MAAMmB,SAAS,GAAG1J,gBAAgB,CAACO,IAAI,CAAC;UACxC,MAAMoJ,cAAc,GAAGtN,WAAW,CAACkE,IAAI,EAAErC,WAAW,CAAC8D,QAAQ,CAAC,CAAC,EAAE9D,WAAW,CAACuF,WAAW,CAAC,CAAC,CAAC;UAC3F,MAAMmG,WAAW,GAAGxN,OAAO,CAACmE,IAAI,CAAC;;UAEjC;UACA;UACA,MAAMsJ,OAAO,GAAG,gBAAgBtJ,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;UAEhD,oBACEvF,OAAA;YAEEI,KAAK,EAAE;cACLmM,SAAS,EAAE,OAAO;cAClBxC,OAAO,EAAE,QAAQ;cACjBiC,YAAY,EAAE,mBAAmB;cACjCQ,WAAW,EAAExB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,mBAAmB;cAAE;cAC7DJ,MAAM,EAAE,SAAS;cACjBrC,eAAe,EAAE,CAAC6D,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cAChFzD,KAAK,EAAE,CAACwD,cAAc,GAAG,SAAS,GAAG,SAAS;cAC9CX,UAAU,EAAE,eAAe;cAC3BJ,QAAQ,EAAE;YACZ,CAAE;YACFX,OAAO,EAAEA,CAAA,KAAM/F,eAAe,CAAC3B,IAAI,CAAE;YACrC0I,WAAW,EAAGrB,CAAC,IAAK;cAClB,IAAI+B,cAAc,EAAE;gBAClB/B,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,SAAS;gBACjD8B,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,2BAA2B;cAC/D;YACF,CAAE;YACF2B,UAAU,EAAGvB,CAAC,IAAK;cACjBA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,CAAC6D,cAAc,GAAG,SAAS,GAAGC,WAAW,GAAG,SAAS,GAAG,OAAO;cACvGhC,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,gBAEF9I,OAAA;cAAKI,KAAK,EAAE;gBACVyI,QAAQ,EAAE,UAAU;gBACpBoC,UAAU,EAAE,KAAK;gBACjBnB,YAAY,EAAE,SAAS;gBACvBlB,KAAK,EAAEyD,WAAW,GAAG,SAAS,GAAG;cACnC,CAAE;cAAAvD,QAAA,EACC9F,IAAI,CAACyJ,OAAO,CAAC;YAAC;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACNnJ,OAAA;cAAKI,KAAK,EAAE;gBAAEqI,OAAO,EAAE,MAAM;gBAAEiE,aAAa,EAAE,QAAQ;gBAAExC,GAAG,EAAE;cAAU,CAAE;cAAApB,QAAA,GACtEqD,SAAS,CAACpG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC+E,GAAG,CAAE5H,KAAK,IAAK;gBACpC;gBACA,MAAMyJ,UAAU,GAAGzJ,KAAK,CAACyJ,UAAU;gBACnC,MAAMC,OAAO,GAAG1J,KAAK,CAAC2J,YAAY;gBAClC,MAAMC,KAAK,GAAG5J,KAAK,CAAC6J,UAAU;gBAC9B,MAAMC,cAAc,GAAGL,UAAU,IAAI,CAACC,OAAO,IAAI,CAACE,KAAK;gBACvD,MAAMG,UAAU,GAAGrI,iBAAiB,CAAC1B,KAAK,CAAC;gBAE3C,oBACElD,OAAA;kBAEEI,KAAK,EAAE;oBACLyI,QAAQ,EAAE,SAAS;oBACnBkB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAEmE,UAAU,GAAIC,OAAO,GAAG,iBAAiB,GAAGE,KAAK,GAAG,iBAAiB,GAAG,KAAK,GAAI,KAAK;oBACpGvE,eAAe,EAAE0E,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBAC5DhD,MAAM,EAAE,aAAaiD,UAAU,EAAE;oBACjCC,UAAU,EAAEN,OAAO,IAAI,CAACD,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBAC1FT,WAAW,EAAEM,KAAK,IAAI,CAACH,UAAU,GAAG,aAAaM,UAAU,EAAE,GAAG,aAAaA,UAAU,EAAE;oBACzFnB,QAAQ,EAAE,QAAQ;oBAClBqB,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE,QAAQ;oBACpBxC,MAAM,EAAE,SAAS;oBACjBS,QAAQ,EAAE,UAAU;oBACpBzC,KAAK,EAAE,SAAS;oBAChBqC,UAAU,EAAE,KAAK;oBACjBQ,UAAU,EAAE,eAAe;oBAC3BhD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBwB,GAAG,EAAE;kBACP,CAAE;kBACFQ,OAAO,EAAGL,CAAC,IAAK;oBACdA,CAAC,CAACgD,eAAe,CAAC,CAAC;oBACnBpK,eAAe,CAACC,KAAK,CAAC;kBACxB,CAAE;kBACFwI,WAAW,EAAGrB,CAAC,IAAK;oBAClBA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG0E,UAAU,GAAG,IAAI;oBACzD5C,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACyL,SAAS,GAAG,kBAAkB;oBACpDxB,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,aAAagD,UAAU,IAAI;kBAC/D,CAAE;kBACFrB,UAAU,EAAGvB,CAAC,IAAK;oBACjBA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG0E,UAAU,IAAID,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;oBACnF3C,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACyL,SAAS,GAAG,eAAe;oBACjDxB,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBACFtE,KAAK,EAAEgH,UAAU,GAAG,GAAGzJ,KAAK,CAACyC,KAAK,KAAKzC,KAAK,CAACoK,iBAAiB,OAAOpK,KAAK,CAACqK,eAAe,GAAG,GAAGrK,KAAK,CAACyC,KAAM;kBAAAmD,QAAA,GAG3G8D,OAAO,IAAID,UAAU,iBACpB3M,OAAA;oBAAMI,KAAK,EAAE;sBACXwI,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGA6D,cAAc,iBACbhN,OAAA;oBAAMI,KAAK,EAAE;sBACXwI,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,EAGA2D,KAAK,IAAIH,UAAU,IAAI,CAACC,OAAO,iBAC9B5M,OAAA;oBAAMI,KAAK,EAAE;sBACXwI,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE;oBACZ,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eAGDnJ,OAAA;oBAAMI,KAAK,EAAE;sBACXoN,IAAI,EAAE,CAAC;sBACP1B,QAAQ,EAAE,QAAQ;sBAClBqB,YAAY,EAAE;oBAChB,CAAE;oBAAArE,QAAA,EACCpD,iBAAiB,CAACxC,KAAK,CAACyC,KAAK;kBAAC;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,EAGNyD,OAAO,IAAID,UAAU,iBACpB3M,OAAA;oBAAMI,KAAK,EAAE;sBACXwI,KAAK,EAAEqE,UAAU;sBACjBhC,UAAU,EAAE,MAAM;sBAClBpC,QAAQ,EAAE,QAAQ;sBAClB4E,OAAO,EAAE;oBACX,CAAE;oBAAA3E,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA,GAzFI,SAASjG,KAAK,CAACK,WAAW,IAAIP,IAAI,CAACuC,OAAO,CAAC,CAAC,EAAE;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0FhD,CAAC;cAEV,CAAC,CAAC,EACDgD,SAAS,CAAChF,MAAM,GAAG,CAAC,iBACnBnH,OAAA;gBAAKI,KAAK,EAAE;kBACVyI,QAAQ,EAAE,SAAS;kBACnBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,GAAC,GACA,EAACqD,SAAS,CAAChF,MAAM,GAAG,CAAC,EAAC,OACzB;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAhJDmD,OAAO;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiJT,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnJ,OAAA;MAAKI,KAAK,EAAE;QACVgL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpByB,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BmB,MAAM,EAAE,QAAQ;QAChBpB,OAAO,EAAE,QAAQ;QACjBxB,eAAe,EAAE;MACnB,CAAE;MAAAO,QAAA,eACA9I,OAAA;QAAKI,KAAK,EAAE;UACVqI,OAAO,EAAE,MAAM;UACfsD,mBAAmB,EAAE,sCAAsC;UAC3D7B,GAAG,EAAE,QAAQ;UACbxB,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBACA9I,OAAA;UAAA8I,QAAA,gBACE9I,OAAA;YAAOI,KAAK,EAAE;cACZqI,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBkB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnJ,OAAA;YAAKI,KAAK,EAAE;cAAEiL,QAAQ,EAAE;YAAW,CAAE;YAAAvC,QAAA,gBACnC9I,OAAA,CAACZ,MAAM;cACL2J,IAAI,EAAE,EAAG;cACTH,KAAK,EAAC,SAAS;cACfxI,KAAK,EAAE;gBACLiL,QAAQ,EAAE,UAAU;gBACpBqC,IAAI,EAAE,SAAS;gBACfC,GAAG,EAAE,KAAK;gBACV9B,SAAS,EAAE,kBAAkB;gBAC7B+B,aAAa,EAAE;cACjB;YAAE;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnJ,OAAA;cACE6N,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kBAAkB;cAC9B3D,KAAK,EAAEvI,UAAW;cAClBwI,QAAQ,EAAGC,CAAC,IAAKxI,aAAa,CAACwI,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cAC/C/J,KAAK,EAAE;gBACLiI,KAAK,EAAE,MAAM;gBACb0B,OAAO,EAAE,gCAAgC;gBACzCC,MAAM,EAAE,mBAAmB;gBAC3BxB,YAAY,EAAE,KAAK;gBACnBK,QAAQ,EAAE,UAAU;gBACpBkF,OAAO,EAAE,MAAM;gBACftC,UAAU,EAAE;cACd,CAAE;cACFuC,OAAO,EAAG3D,CAAC,IAAK;gBACdA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6N,WAAW,GAAG,SAAS;gBAC7C5D,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,kCAAkC;cACtE,CAAE;cACFiE,MAAM,EAAG7D,CAAC,IAAK;gBACbA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6N,WAAW,GAAG,SAAS;gBAC7C5D,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,MAAM;cAC1C;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnJ,OAAA;UAAA8I,QAAA,gBACE9I,OAAA;YAAOI,KAAK,EAAE;cACZqI,OAAO,EAAE,OAAO;cAChBI,QAAQ,EAAE,UAAU;cACpBoC,UAAU,EAAE,KAAK;cACjBrC,KAAK,EAAE,SAAS;cAChBkB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnJ,OAAA;YACEmK,KAAK,EAAErI,gBAAiB;YACxBsI,QAAQ,EAAGC,CAAC,IAAKtI,mBAAmB,CAACsI,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;YACrD/J,KAAK,EAAE;cACLiI,KAAK,EAAE,MAAM;cACb0B,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE,mBAAmB;cAC3BxB,YAAY,EAAE,KAAK;cACnBK,QAAQ,EAAE,UAAU;cACpBkF,OAAO,EAAE,MAAM;cACfxF,eAAe,EAAE,OAAO;cACxBkD,UAAU,EAAE,8CAA8C;cAC1Dc,SAAS,EAAE,QAAQ;cACnB4B,UAAU,EAAE;YACd,CAAE;YACFH,OAAO,EAAG3D,CAAC,IAAK;cACdA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6N,WAAW,GAAG,SAAS;cAC7C5D,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,kCAAkC;YACtE,CAAE;YACFiE,MAAM,EAAG7D,CAAC,IAAK;cACbA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6N,WAAW,GAAG,SAAS;cAC7C5D,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAAC6J,SAAS,GAAG,MAAM;YAC1C,CAAE;YAAAnB,QAAA,gBAEF9I,OAAA;cAAQmK,KAAK,EAAC,EAAE;cAAArB,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvCxG,UAAU,CAACmI,GAAG,CAAEsD,QAAa,iBAC5BpO,OAAA;cAAmCmK,KAAK,EAAEiE,QAAQ,CAACvG,WAAY;cAAAiB,QAAA,EAC5DsF,QAAQ,CAACC;YAAI,GADHD,QAAQ,CAACvG,WAAW;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnJ,OAAA;UAAA8I,QAAA,eACE9I,OAAA;YACE0K,OAAO,EAAEhI,OAAQ;YACjBtC,KAAK,EAAE;cACLiI,KAAK,EAAE,MAAM;cACb0B,OAAO,EAAE,SAAS;cAClBxB,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE,MAAM;cACdxB,YAAY,EAAE,KAAK;cACnBoC,MAAM,EAAE,SAAS;cACjBK,UAAU,EAAE,KAAK;cACjBpC,QAAQ,EAAE,UAAU;cACpB4C,UAAU,EAAE,4BAA4B;cACxCnD,MAAM,EAAE,QAAQ;cAChBG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBuB,GAAG,EAAE;YACP,CAAE;YACFwB,WAAW,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,SAAU;YACtEqD,UAAU,EAAGvB,CAAC,IAAKA,CAAC,CAACsB,aAAa,CAACvL,KAAK,CAACmI,eAAe,GAAG,SAAU;YAAAO,QAAA,gBAErE9I,OAAA,CAACX,SAAS;cAAC0J,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnJ,OAAA;MAAKI,KAAK,EAAE;QACVgL,UAAU,EAAE,OAAO;QACnB5C,YAAY,EAAE,MAAM;QACpBuB,OAAO,EAAE,MAAM;QACfE,SAAS,EAAE,gCAAgC;QAC3CD,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE;MAChB,CAAE;MAAAhB,QAAA,gBACA9I,OAAA;QAAKI,KAAK,EAAE;UACVqI,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBoB,YAAY,EAAE;QAChB,CAAE;QAAAhB,QAAA,gBACA9I,OAAA;UAAII,KAAK,EAAE;YACTyI,QAAQ,EAAE,QAAQ;YAClBoC,UAAU,EAAE,KAAK;YACjBrC,KAAK,EAAE,SAAS;YAChBuC,MAAM,EAAE;UACV,CAAE;UAAArC,QAAA,GAAC,aACU,EAAC/J,YAAY,CAAC4B,WAAW,CAAC8D,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAC,EAAC9D,WAAW,CAACuF,WAAW,CAAC,CAAC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACLnJ,OAAA;UAAKI,KAAK,EAAE;YACVyI,QAAQ,EAAE,UAAU;YACpBD,KAAK,EAAE;UACT,CAAE;UAAAE,QAAA,GACC1B,cAAc,CAACD,MAAM,EAAC,QAAM,EAACC,cAAc,CAACD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QACvE;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9G,OAAO,gBACNrC,OAAA;QAAKI,KAAK,EAAE;UACV8L,SAAS,EAAE,QAAQ;UACnBnC,OAAO,EAAE,MAAM;UACfnB,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACA9I,OAAA,CAACX,SAAS;UAAC0J,IAAI,EAAE,EAAG;UAAC3I,KAAK,EAAE;YAAE0J,YAAY,EAAE,MAAM;YAAEyB,SAAS,EAAE;UAA0B;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9FnJ,OAAA;UAAA8I,QAAA,EAAG;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJ/B,cAAc,CAACD,MAAM,KAAK,CAAC,gBAC7BnH,OAAA;QAAKI,KAAK,EAAE;UACV8L,SAAS,EAAE,QAAQ;UACnBnC,OAAO,EAAE,MAAM;UACfnB,KAAK,EAAE;QACT,CAAE;QAAAE,QAAA,gBACA9I,OAAA,CAACb,YAAY;UAAC4J,IAAI,EAAE,EAAG;UAAC3I,KAAK,EAAE;YAAE0J,YAAY,EAAE,MAAM;YAAE2D,OAAO,EAAE;UAAI;QAAE;UAAAzE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzEnJ,OAAA;UAAA8I,QAAA,EAAG;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrCnJ,OAAA;UACE0K,OAAO,EAAEA,CAAA,KAAM3H,iBAAiB,CAAC,CAAE;UACnC3C,KAAK,EAAE;YACLyJ,SAAS,EAAE,MAAM;YACjBE,OAAO,EAAE,gBAAgB;YACzBxB,eAAe,EAAE,SAAS;YAC1BK,KAAK,EAAE,OAAO;YACdoB,MAAM,EAAE,MAAM;YACdxB,YAAY,EAAE,KAAK;YACnBoC,MAAM,EAAE,SAAS;YACjBK,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENnJ,OAAA;QAAKI,KAAK,EAAE;UACVqI,OAAO,EAAE,MAAM;UACfyB,GAAG,EAAE;QACP,CAAE;QAAApB,QAAA,EACCX,eAAe,CAAC2C,GAAG,CAAE5H,KAAK,iBACzBlD,OAAA;UAEEI,KAAK,EAAE;YACL4J,MAAM,EAAE,mBAAmB;YAC3BxB,YAAY,EAAE,MAAM;YACpBuB,OAAO,EAAE,QAAQ;YACjBxB,eAAe,EAAE,OAAO;YACxBkD,UAAU,EAAE;UACd,CAAE;UAAA3C,QAAA,gBAEF9I,OAAA;YAAKI,KAAK,EAAE;cACVqI,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBoB,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,gBACA9I,OAAA;cAAKI,KAAK,EAAE;gBAAEoN,IAAI,EAAE;cAAE,CAAE;cAAA1E,QAAA,gBACtB9I,OAAA;gBAAII,KAAK,EAAE;kBACTyI,QAAQ,EAAE,UAAU;kBACpBoC,UAAU,EAAE,KAAK;kBACjBrC,KAAK,EAAE,SAAS;kBAChBuC,MAAM,EAAE,cAAc;kBACtB1C,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE;gBACP,CAAE;gBAAApB,QAAA,GACC5F,KAAK,CAACyC,KAAK,EACVzC,KAAK,CAASoL,QAAQ,iBACtBtO,OAAA,CAACJ,aAAa;kBAACmJ,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnJ,OAAA;gBAAKI,KAAK,EAAE;kBACVqI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE,MAAM;kBACXrB,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,gBACA9I,OAAA;kBAAMuO,SAAS,EAAC,yBAAyB;kBAAAzF,QAAA,gBACvC9I,OAAA,CAACb,YAAY;oBAACoP,SAAS,EAAC;kBAAS;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCjG,KAAK,CAAC+B,UAAU;gBAAA;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNjG,KAAK,CAAC8B,QAAQ,IAAI9B,KAAK,CAAC8B,QAAQ,KAAK9B,KAAK,CAAC+B,UAAU,iBACpDjF,OAAA;kBAAA8I,QAAA,GAAM,SAAE,EAAC5F,KAAK,CAAC8B,QAAQ;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/B,eACDnJ,OAAA;kBAAMI,KAAK,EAAE;oBACXmI,eAAe,EAAE,SAAS;oBAC1BK,KAAK,EAAE,SAAS;oBAChBmB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE,SAAS;oBACnBoC,UAAU,EAAE;kBACd,CAAE;kBAAAnC,QAAA,EACC/D,gBAAgB,CAAC7B,KAAK;gBAAC;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACPnJ,OAAA;kBAAMI,KAAK,EAAE;oBACXmI,eAAe,EAAE3D,iBAAiB,CAAC1B,KAAK,CAAC;oBACzC0F,KAAK,EAAE,OAAO;oBACdmB,OAAO,EAAE,gBAAgB;oBACzBvB,YAAY,EAAE,KAAK;oBACnBK,QAAQ,EAAE;kBACZ,CAAE;kBAAAC,QAAA,EACC5F,KAAK,CAACsL,aAAa,IAAI;gBAAe;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEJ,CAAC,EACLjG,KAAK,CAACyE,WAAW,iBAChB3H,OAAA;gBAAGI,KAAK,EAAE;kBACRyI,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBuC,MAAM,EAAE,cAAc;kBACtBgD,UAAU,EAAE;gBACd,CAAE;gBAAArF,QAAA,EACC5F,KAAK,CAACyE;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACJ,eAGDnJ,OAAA;gBAAKI,KAAK,EAAE;kBACVqI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,GAAG,EAAE,MAAM;kBACXL,SAAS,EAAE;gBACb,CAAE;gBAAAf,QAAA,gBACA9I,OAAA,CAACF,uBAAuB;kBACtB+D,OAAO,EAAEX,KAAK,CAACK,WAAY;kBAC3BkL,YAAY,EAAGvL,KAAK,CAASwL,gBAAgB,IAAI,KAAM;kBACvDC,YAAY,EAAGzL,KAAK,CAAS0L,cAAc,IAAI,CAAE;kBACjD7F,IAAI,EAAC,OAAO;kBACZ8F,YAAY,EAAEA,CAACC,KAAc,EAAEC,QAAgB,KAAK;oBAClD;oBACAvL,OAAO,CAACC,GAAG,CAAC,SAASP,KAAK,CAACK,WAAW,gBAAgB,EAAE;sBAAEuL,KAAK;sBAAEC;oBAAS,CAAC,CAAC;kBAC9E;gBAAE;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACAjG,KAAK,CAAS8L,cAAc,iBAC5BhP,OAAA;kBAAMI,KAAK,EAAE;oBACXqI,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBwB,GAAG,EAAE,SAAS;oBACdrB,QAAQ,EAAE,UAAU;oBACpBD,KAAK,EAAE;kBACT,CAAE;kBAAAE,QAAA,gBACA9I,OAAA,CAACH,aAAa;oBAACkJ,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBjG,KAAK,CAAS+L,aAAa,IAAI,CAAC,EAAC,WACrC;gBAAA;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnJ,OAAA;cAAKI,KAAK,EAAE;gBACVqI,OAAO,EAAE,MAAM;gBACfyB,GAAG,EAAE,QAAQ;gBACbsB,UAAU,EAAE;cACd,CAAE;cAAA1C,QAAA,gBACA9I,OAAA;gBACE0K,OAAO,EAAEA,CAAA,KAAMzH,eAAe,CAACC,KAAK,CAAE;gBACtC9C,KAAK,EAAE;kBACL2J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBoB,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFhD,KAAK,EAAC,YAAY;gBAAAmD,QAAA,eAElB9I,OAAA,CAACT,IAAI;kBAACwJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAETnJ,OAAA;gBACE0K,OAAO,EAAEA,CAAA,KAAOxH,KAAK,CAASgM,YAAY,GACtCnL,oBAAoB,CAACb,KAAK,CAACK,WAAW,CAAC,GACvCK,kBAAkB,CAACV,KAAK,CAACK,WAAW,CACvC;gBACDnD,KAAK,EAAE;kBACL2J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAGrF,KAAK,CAASgM,YAAY,GAAG,SAAS,GAAG,SAAS;kBACpEtG,KAAK,EAAG1F,KAAK,CAASgM,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC1DlF,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFhD,KAAK,EAAGzC,KAAK,CAASgM,YAAY,GAAG,iBAAiB,GAAG,eAAgB;gBAAApG,QAAA,EAEvE5F,KAAK,CAASgM,YAAY,gBAAGlP,OAAA,CAACP,KAAK;kBAACsJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGnJ,OAAA,CAACR,IAAI;kBAACuJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAETnJ,OAAA;gBACE0K,OAAO,EAAEA,CAAA,KAAMzG,iBAAiB,CAACf,KAAK,CAACK,WAAW,CAAE;gBACpDnD,KAAK,EAAE;kBACL2J,OAAO,EAAE,QAAQ;kBACjBxB,eAAe,EAAE,SAAS;kBAC1BK,KAAK,EAAE,SAAS;kBAChBoB,MAAM,EAAE,MAAM;kBACdxB,YAAY,EAAE,KAAK;kBACnBoC,MAAM,EAAE,SAAS;kBACjBnC,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBACFhD,KAAK,EAAC,cAAc;gBAAAmD,QAAA,eAEpB9I,OAAA,CAACV,MAAM;kBAACyJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnJ,OAAA;YAAKI,KAAK,EAAE;cACV+O,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClBvF,SAAS,EAAE;YACb,CAAE;YAAAf,QAAA,gBACA9I,OAAA;cAAKI,KAAK,EAAE;gBACVyI,QAAQ,EAAE,UAAU;gBACpBoC,UAAU,EAAE,KAAK;gBACjBrC,KAAK,EAAE,SAAS;gBAChBkB,YAAY,EAAE;cAChB,CAAE;cAAAhB,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnJ,OAAA,CAACoI,WAAW;cAACvE,OAAO,EAAEX,KAAK,CAACK;YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA,GA1LD,gBAAgBjG,KAAK,CAACK,WAAW,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2LrC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnJ,OAAA,CAACoJ,kBAAkB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtBnJ,OAAA,CAAChB,kBAAkB;MACjBqQ,MAAM,EAAErO,SAAU;MAClBsO,OAAO,EAAE3L,gBAAiB;MAC1B4L,MAAM,EAAEpM,eAAgB;MACxBD,KAAK,EAAEhC,YAAa;MACpBJ,YAAY,EAAEA,YAAa;MAC3BuB,OAAO,EAAEb,MAAM,IAAIa;IAAQ;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;EAAA,QAlpCKzK,WAAW,EAEQC,qBAAqB;AAAA,EAgpC7C,CAAC;EAAA,QAlpCID,WAAW,EAEQC,qBAAqB;AAAA,EAgpC5C;AAAC6Q,GAAA,GA5rCGtQ,QAAkB;AA8rCxBA,QAAQ,CAACuQ,WAAW,GAAG,UAAU;AAEjC,eAAevQ,QAAQ;AAAC,IAAAiB,EAAA,EAAAqP,GAAA;AAAAE,YAAA,CAAAvP,EAAA;AAAAuP,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}