{"ast": null, "code": "import { AdminAuthService } from './admin-auth.service';\nclass AdminProfileService {\n  /**\n   * Get current admin profile\n   */\n  async getProfile() {\n    return AdminAuthService.get('/admin/profile');\n  }\n\n  /**\n   * Update admin profile\n   */\n  async updateProfile(data) {\n    return AdminAuthService.put('/admin/profile', data);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  async uploadProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('image', file);\n    return AdminAuthService.post('/admin/profile/picture', formData);\n  }\n\n  /**\n   * Update profile picture\n   */\n  async updateProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('image', file);\n    return AdminAuthService.put('/admin/profile/picture', formData);\n  }\n\n  /**\n   * Delete profile picture\n   */\n  async deleteProfilePicture() {\n    return AdminAuthService.delete('/admin/profile/picture');\n  }\n\n  /**\n   * Get dashboard statistics\n   */\n  async getDashboardStats() {\n    return AdminAuthService.get('/admin/dashboard/stats');\n  }\n\n  /**\n   * Validate profile picture file\n   */\n  validateProfilePicture(file) {\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n    if (!file) {\n      return {\n        isValid: false,\n        error: 'No file provided'\n      };\n    }\n    if (file.size > maxSize) {\n      return {\n        isValid: false,\n        error: `File size too large. Maximum size is ${(maxSize / (1024 * 1024)).toFixed(1)}MB`\n      };\n    }\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        isValid: false,\n        error: `Invalid file type. Allowed types: ${allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ')}`\n      };\n    }\n    return {\n      isValid: true\n    };\n  }\n\n  /**\n   * Generate default avatar URL\n   */\n  getDefaultAvatarUrl(firstName = '', lastName = '') {\n    const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=3b82f6&color=ffffff&size=300`;\n  }\n\n  /**\n   * Get profile picture with fallback\n   */\n  getProfilePictureWithFallback(profilePictureUrl, firstName = '', lastName = '') {\n    const defaultAvatar = this.getDefaultAvatarUrl(firstName, lastName);\n    return {\n      url: profilePictureUrl || defaultAvatar,\n      hasCustomPicture: !!profilePictureUrl,\n      defaultAvatar\n    };\n  }\n\n  /**\n   * Format admin profile data\n   */\n  formatProfileData(admin) {\n    return {\n      ...admin,\n      full_name: [admin.first_name, admin.middle_name, admin.last_name, admin.suffix].filter(Boolean).join(' '),\n      profile_picture_info: this.getProfilePictureWithFallback(admin.profile_picture_url, admin.first_name, admin.last_name)\n    };\n  }\n}\nexport const adminProfileService = new AdminProfileService();\nexport default adminProfileService;", "map": {"version": 3, "names": ["AdminAuthService", "AdminProfileService", "getProfile", "get", "updateProfile", "data", "put", "uploadProfilePicture", "file", "formData", "FormData", "append", "post", "updateProfilePicture", "deleteProfilePicture", "delete", "getDashboardStats", "validateProfilePicture", "maxSize", "allowedTypes", "<PERSON><PERSON><PERSON><PERSON>", "error", "size", "toFixed", "includes", "type", "map", "t", "split", "toUpperCase", "join", "getDefaultAvatarUrl", "firstName", "lastName", "initials", "char<PERSON>t", "encodeURIComponent", "getProfilePictureWithFallback", "profilePictureUrl", "defaultAvatar", "url", "hasCustomPicture", "formatProfileData", "admin", "full_name", "first_name", "middle_name", "last_name", "suffix", "filter", "Boolean", "profile_picture_info", "profile_picture_url", "adminProfileService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/adminProfileService.ts"], "sourcesContent": ["import { AdminAuthService } from './admin-auth.service';\nimport { API_ENDPOINTS } from '../config/constants';\nimport { ApiResponse } from '../types';\n\nexport interface AdminProfile {\n  admin_id: number;\n  email: string;\n  first_name: string;\n  middle_name?: string;\n  last_name: string;\n  suffix?: string;\n  full_name: string;\n  phone_number?: string;\n  department?: string;\n  position?: string;\n  grade_level?: number;\n  bio?: string;\n  profile_picture?: string;\n  profile_picture_url?: string;\n  profile_picture_info?: {\n    url: string;\n    hasCustomPicture: boolean;\n    defaultAvatar: string;\n  };\n  is_active: boolean;\n  last_login?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface UpdateProfileData {\n  first_name?: string;\n  middle_name?: string;\n  last_name?: string;\n  suffix?: string;\n  phone_number?: string;\n  department?: string;\n  position?: string;\n  grade_level?: number;\n  bio?: string;\n}\n\nexport interface ProfilePictureResponse {\n  profile_picture: {\n    url: string;\n    hasCustomPicture: boolean;\n    defaultAvatar: string;\n  };\n  admin: AdminProfile;\n}\n\nclass AdminProfileService {\n  /**\n   * Get current admin profile\n   */\n  async getProfile(): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    return AdminAuthService.get<{ admin: AdminProfile }>('/admin/profile');\n  }\n\n  /**\n   * Update admin profile\n   */\n  async updateProfile(data: UpdateProfileData): Promise<ApiResponse<{ admin: AdminProfile }>> {\n    return AdminAuthService.put<{ admin: AdminProfile }>('/admin/profile', data);\n  }\n\n  /**\n   * Upload profile picture\n   */\n  async uploadProfilePicture(file: File): Promise<ApiResponse<ProfilePictureResponse>> {\n    const formData = new FormData();\n    formData.append('image', file);\n\n    return AdminAuthService.post<ProfilePictureResponse>('/admin/profile/picture', formData);\n  }\n\n  /**\n   * Update profile picture\n   */\n  async updateProfilePicture(file: File): Promise<ApiResponse<ProfilePictureResponse>> {\n    const formData = new FormData();\n    formData.append('image', file);\n\n    return AdminAuthService.put<ProfilePictureResponse>('/admin/profile/picture', formData);\n  }\n\n  /**\n   * Delete profile picture\n   */\n  async deleteProfilePicture(): Promise<ApiResponse<ProfilePictureResponse>> {\n    return AdminAuthService.delete<ProfilePictureResponse>('/admin/profile/picture');\n  }\n\n  /**\n   * Get dashboard statistics\n   */\n  async getDashboardStats(): Promise<ApiResponse<any>> {\n    return AdminAuthService.get<any>('/admin/dashboard/stats');\n  }\n\n  /**\n   * Validate profile picture file\n   */\n  validateProfilePicture(file: File): { isValid: boolean; error?: string } {\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n\n    if (!file) {\n      return { isValid: false, error: 'No file provided' };\n    }\n\n    if (file.size > maxSize) {\n      return { \n        isValid: false, \n        error: `File size too large. Maximum size is ${(maxSize / (1024 * 1024)).toFixed(1)}MB` \n      };\n    }\n\n    if (!allowedTypes.includes(file.type)) {\n      return { \n        isValid: false, \n        error: `Invalid file type. Allowed types: ${allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ')}` \n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Generate default avatar URL\n   */\n  getDefaultAvatarUrl(firstName: string = '', lastName: string = ''): string {\n    const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=3b82f6&color=ffffff&size=300`;\n  }\n\n  /**\n   * Get profile picture with fallback\n   */\n  getProfilePictureWithFallback(\n    profilePictureUrl?: string | null, \n    firstName: string = '', \n    lastName: string = ''\n  ): { url: string; hasCustomPicture: boolean; defaultAvatar: string } {\n    const defaultAvatar = this.getDefaultAvatarUrl(firstName, lastName);\n    \n    return {\n      url: profilePictureUrl || defaultAvatar,\n      hasCustomPicture: !!profilePictureUrl,\n      defaultAvatar\n    };\n  }\n\n  /**\n   * Format admin profile data\n   */\n  formatProfileData(admin: AdminProfile): AdminProfile {\n    return {\n      ...admin,\n      full_name: [admin.first_name, admin.middle_name, admin.last_name, admin.suffix]\n        .filter(Boolean)\n        .join(' '),\n      profile_picture_info: this.getProfilePictureWithFallback(\n        admin.profile_picture_url,\n        admin.first_name,\n        admin.last_name\n      )\n    };\n  }\n}\n\nexport const adminProfileService = new AdminProfileService();\nexport default adminProfileService;\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AAmDvD,MAAMC,mBAAmB,CAAC;EACxB;AACF;AACA;EACE,MAAMC,UAAUA,CAAA,EAAkD;IAChE,OAAOF,gBAAgB,CAACG,GAAG,CAA0B,gBAAgB,CAAC;EACxE;;EAEA;AACF;AACA;EACE,MAAMC,aAAaA,CAACC,IAAuB,EAAiD;IAC1F,OAAOL,gBAAgB,CAACM,GAAG,CAA0B,gBAAgB,EAAED,IAAI,CAAC;EAC9E;;EAEA;AACF;AACA;EACE,MAAME,oBAAoBA,CAACC,IAAU,EAAgD;IACnF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAOR,gBAAgB,CAACY,IAAI,CAAyB,wBAAwB,EAAEH,QAAQ,CAAC;EAC1F;;EAEA;AACF;AACA;EACE,MAAMI,oBAAoBA,CAACL,IAAU,EAAgD;IACnF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAOR,gBAAgB,CAACM,GAAG,CAAyB,wBAAwB,EAAEG,QAAQ,CAAC;EACzF;;EAEA;AACF;AACA;EACE,MAAMK,oBAAoBA,CAAA,EAAiD;IACzE,OAAOd,gBAAgB,CAACe,MAAM,CAAyB,wBAAwB,CAAC;EAClF;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAAA,EAA8B;IACnD,OAAOhB,gBAAgB,CAACG,GAAG,CAAM,wBAAwB,CAAC;EAC5D;;EAEA;AACF;AACA;EACEc,sBAAsBA,CAACT,IAAU,EAAwC;IACvE,MAAMU,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAExF,IAAI,CAACX,IAAI,EAAE;MACT,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAmB,CAAC;IACtD;IAEA,IAAIb,IAAI,CAACc,IAAI,GAAGJ,OAAO,EAAE;MACvB,OAAO;QACLE,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,wCAAwC,CAACH,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC;MACrF,CAAC;IACH;IAEA,IAAI,CAACJ,YAAY,CAACK,QAAQ,CAAChB,IAAI,CAACiB,IAAI,CAAC,EAAE;MACrC,OAAO;QACLL,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,qCAAqCF,YAAY,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAC7G,CAAC;IACH;IAEA,OAAO;MAAEV,OAAO,EAAE;IAAK,CAAC;EAC1B;;EAEA;AACF;AACA;EACEW,mBAAmBA,CAACC,SAAiB,GAAG,EAAE,EAAEC,QAAgB,GAAG,EAAE,EAAU;IACzE,MAAMC,QAAQ,GAAG,GAAGF,SAAS,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE,CAACN,WAAW,CAAC,CAAC;IAC5E,OAAO,oCAAoCO,kBAAkB,CAACF,QAAQ,CAAC,0CAA0C;EACnH;;EAEA;AACF;AACA;EACEG,6BAA6BA,CAC3BC,iBAAiC,EACjCN,SAAiB,GAAG,EAAE,EACtBC,QAAgB,GAAG,EAAE,EAC8C;IACnE,MAAMM,aAAa,GAAG,IAAI,CAACR,mBAAmB,CAACC,SAAS,EAAEC,QAAQ,CAAC;IAEnE,OAAO;MACLO,GAAG,EAAEF,iBAAiB,IAAIC,aAAa;MACvCE,gBAAgB,EAAE,CAAC,CAACH,iBAAiB;MACrCC;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEG,iBAAiBA,CAACC,KAAmB,EAAgB;IACnD,OAAO;MACL,GAAGA,KAAK;MACRC,SAAS,EAAE,CAACD,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,WAAW,EAAEH,KAAK,CAACI,SAAS,EAAEJ,KAAK,CAACK,MAAM,CAAC,CAC5EC,MAAM,CAACC,OAAO,CAAC,CACfpB,IAAI,CAAC,GAAG,CAAC;MACZqB,oBAAoB,EAAE,IAAI,CAACd,6BAA6B,CACtDM,KAAK,CAACS,mBAAmB,EACzBT,KAAK,CAACE,UAAU,EAChBF,KAAK,CAACI,SACR;IACF,CAAC;EACH;AACF;AAEA,OAAO,MAAMM,mBAAmB,GAAG,IAAIpD,mBAAmB,CAAC,CAAC;AAC5D,eAAeoD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}