{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z\",\n  key: \"vktsd0\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"kqv944\"\n}]];\nconst Tag = createLucideIcon(\"tag\", __iconNode);\nexport { __iconNode, Tag as default };\n//# sourceMappingURL=tag.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}