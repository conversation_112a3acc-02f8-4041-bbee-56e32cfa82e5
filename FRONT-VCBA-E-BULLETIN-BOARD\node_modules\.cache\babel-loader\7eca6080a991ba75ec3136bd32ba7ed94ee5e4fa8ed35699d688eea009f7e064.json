{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 4H3\",\n  key: \"1hwok0\"\n}], [\"path\", {\n  d: \"M18 8H6\",\n  key: \"41n648\"\n}], [\"path\", {\n  d: \"M19 12H9\",\n  key: \"1g4lpz\"\n}], [\"path\", {\n  d: \"M16 16h-6\",\n  key: \"1j5d54\"\n}], [\"path\", {\n  d: \"M11 20H9\",\n  key: \"39obr8\"\n}]];\nconst Tornado = createLucideIcon(\"tornado\", __iconNode);\nexport { __iconNode, Tornado as default };\n//# sourceMappingURL=tornado.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}