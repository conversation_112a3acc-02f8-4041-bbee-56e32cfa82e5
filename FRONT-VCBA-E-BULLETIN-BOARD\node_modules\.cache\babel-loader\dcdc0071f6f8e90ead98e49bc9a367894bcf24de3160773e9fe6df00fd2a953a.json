{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"12\",\n  key: \"1pkeuh\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12.01\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"4dfq90\"\n}]];\nconst CircleAlert = createLucideIcon(\"circle-alert\", __iconNode);\nexport { __iconNode, CircleAlert as default };\n//# sourceMappingURL=circle-alert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}