{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14.531 12.469 6.619 20.38a1 1 0 1 1-3-3l7.912-7.912\",\n  key: \"we99rg\"\n}], [\"path\", {\n  d: \"M15.686 4.314A12.5 12.5 0 0 0 5.461 2.958 1 1 0 0 0 5.58 4.71a22 22 0 0 1 6.318 3.393\",\n  key: \"1w6hck\"\n}], [\"path\", {\n  d: \"M17.7 3.7a1 1 0 0 0-1.4 0l-4.6 4.6a1 1 0 0 0 0 1.4l2.6 2.6a1 1 0 0 0 1.4 0l4.6-4.6a1 1 0 0 0 0-1.4z\",\n  key: \"15hgfx\"\n}], [\"path\", {\n  d: \"M19.686 8.314a12.501 12.501 0 0 1 1.356 10.225 1 1 0 0 1-1.751-.119 22 22 0 0 0-3.393-6.319\",\n  key: \"452b4h\"\n}]];\nconst Pickaxe = createLucideIcon(\"pickaxe\", __iconNode);\nexport { __iconNode, Pickaxe as default };\n//# sourceMappingURL=pickaxe.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}