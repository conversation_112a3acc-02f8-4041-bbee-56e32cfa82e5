{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 3a2 2 0 0 0-2 2\",\n  key: \"y57alp\"\n}], [\"path\", {\n  d: \"M19 3a2 2 0 0 1 2 2\",\n  key: \"18rm91\"\n}], [\"path\", {\n  d: \"M21 19a2 2 0 0 1-2 2\",\n  key: \"1j7049\"\n}], [\"path\", {\n  d: \"M5 21a2 2 0 0 1-2-2\",\n  key: \"sbafld\"\n}], [\"path\", {\n  d: \"M9 3h1\",\n  key: \"1yesri\"\n}], [\"path\", {\n  d: \"M9 21h1\",\n  key: \"15o7lz\"\n}], [\"path\", {\n  d: \"M14 3h1\",\n  key: \"1ec4yj\"\n}], [\"path\", {\n  d: \"M14 21h1\",\n  key: \"v9vybs\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}], [\"path\", {\n  d: \"M21 9v1\",\n  key: \"mxsmne\"\n}], [\"path\", {\n  d: \"M3 14v1\",\n  key: \"vnatye\"\n}], [\"path\", {\n  d: \"M21 14v1\",\n  key: \"169vum\"\n}]];\nconst SquareDashed = createLucideIcon(\"square-dashed\", __iconNode);\nexport { __iconNode, SquareDashed as default };\n//# sourceMappingURL=square-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}