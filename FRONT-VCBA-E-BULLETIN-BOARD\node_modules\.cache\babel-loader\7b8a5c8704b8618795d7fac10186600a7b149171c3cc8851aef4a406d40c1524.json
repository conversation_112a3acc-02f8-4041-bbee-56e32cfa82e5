{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"4\",\n  x: \"8\",\n  y: \"2\",\n  rx: \"1\",\n  ry: \"1\",\n  key: \"tgr4d6\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\",\n  key: \"116196\"\n}], [\"path\", {\n  d: \"m15 11-6 6\",\n  key: \"1toa9n\"\n}], [\"path\", {\n  d: \"m9 11 6 6\",\n  key: \"wlibny\"\n}]];\nconst ClipboardX = createLucideIcon(\"clipboard-x\", __iconNode);\nexport { __iconNode, ClipboardX as default };\n//# sourceMappingURL=clipboard-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}