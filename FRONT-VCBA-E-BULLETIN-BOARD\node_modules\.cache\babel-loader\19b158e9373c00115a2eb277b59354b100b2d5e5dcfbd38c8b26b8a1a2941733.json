{"ast": null, "code": "import{useState,useEffect,useCallback}from'react';import{calendarService}from'../services';// Hook for managing calendar events\nexport const useCalendar=initialDate=>{const[events,setEvents]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const[currentDate,setCurrentDate]=useState(initialDate||new Date());const[view,setView]=useState('month');const[calendarData,setCalendarData]=useState({events:{}});const fetchCalendarData=useCallback(async()=>{try{setLoading(true);setError(undefined);const year=currentDate.getFullYear();const month=view==='month'?currentDate.getMonth()+1:undefined;// console.log(`📅 Fetching calendar data for ${year}${month ? `-${month}` : ''}...`);\nconst response=await calendarService.getCalendarView(year,month);if(response.success&&response.data){setCalendarData(response.data);// Convert grouped events to flat array for easier manipulation\nconst flatEvents=Object.values(response.data.events).flat();setEvents(flatEvents);// console.log(`✅ Calendar data loaded: ${flatEvents.length} events`);\n}else{const errorMsg=response.message||'Failed to fetch calendar data';// console.warn('⚠️ Calendar response not successful:', response);\nsetError(errorMsg);}}catch(err){// console.error('❌ Error fetching calendar data:', err);\nlet errorMessage='An error occurred while fetching calendar data';if(err.message.includes('Network connection failed')){errorMessage='Unable to connect to server. Please check your connection and try again.';}else if(err.message){errorMessage=err.message;}setError(errorMessage);}finally{setLoading(false);}},[currentDate,view]);const refresh=useCallback(async()=>{await fetchCalendarData();},[fetchCalendarData]);const createEvent=useCallback(async data=>{try{setLoading(true);setError(undefined);const response=await calendarService.createEvent(data);if(response.success){// Refresh calendar data to get the new event\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to create event');}}catch(err){setError(err.message||'An error occurred while creating event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const updateEvent=useCallback(async(id,data)=>{try{setLoading(true);setError(undefined);const response=await calendarService.updateEvent(id,data);if(response.success&&response.data){// Refresh the calendar data to ensure multi-day events are properly handled\n// This is more reliable than trying to manually update the complex calendar state\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to update event');}}catch(err){setError(err.message||'An error occurred while updating event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const deleteEvent=useCallback(async id=>{try{setLoading(true);setError(undefined);const response=await calendarService.deleteEvent(id);if(response.success){// Refresh the calendar data to ensure consistency\nawait fetchCalendarData();}else{throw new Error(response.message||'Failed to delete event');}}catch(err){setError(err.message||'An error occurred while deleting event');throw err;}finally{setLoading(false);}},[fetchCalendarData]);const getEventsForDate=useCallback(date=>{// Format date manually to avoid timezone issues\nconst year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const dateKey=\"\".concat(year,\"-\").concat(month,\"-\").concat(day);return calendarData.events[dateKey]||[];},[calendarData]);const getEventsForDateRange=useCallback(async(startDate,endDate)=>{try{// Format dates manually to avoid timezone issues\nconst formatDate=date=>{const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day);};const start=formatDate(startDate);const end=formatDate(endDate);const response=await calendarService.getEventsByDateRange(start,end);if(response.success&&response.data){return response.data.events;}else{throw new Error(response.message||'Failed to fetch events for date range');}}catch(err){setError(err.message||'An error occurred while fetching events for date range');return[];}},[]);useEffect(()=>{fetchCalendarData();},[fetchCalendarData]);return{events,loading,error,currentDate,view,calendarData,setCurrentDate,setView,refresh,createEvent,updateEvent,deleteEvent,getEventsForDate,getEventsForDateRange};};// Hook for managing holiday types\nexport const useHolidayTypes=()=>{const[holidayTypes,setHolidayTypes]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState();const fetchHolidayTypes=useCallback(async()=>{try{setLoading(true);setError(undefined);const response=await calendarService.getHolidayTypes();if(response.success&&response.data){setHolidayTypes(response.data.holidayTypes);}else{setError(response.message||'Failed to fetch holiday types');}}catch(err){setError(err.message||'An error occurred while fetching holiday types');}finally{setLoading(false);}},[]);const refresh=useCallback(async()=>{await fetchHolidayTypes();},[fetchHolidayTypes]);useEffect(()=>{fetchHolidayTypes();},[fetchHolidayTypes]);return{holidayTypes,loading,error,refresh};};// Utility functions for calendar operations\nexport const getCalendarDays=(year,month)=>{const firstDay=new Date(year,month,1);const lastDay=new Date(year,month+1,0);const startDate=new Date(firstDay);const endDate=new Date(lastDay);// Adjust to start from Sunday (or Monday based on preference)\nstartDate.setDate(startDate.getDate()-startDate.getDay());// Adjust to end on Saturday (or Sunday based on preference)\nendDate.setDate(endDate.getDate()+(6-endDate.getDay()));const days=[];const currentDate=new Date(startDate);while(currentDate<=endDate){days.push(new Date(currentDate));currentDate.setDate(currentDate.getDate()+1);}return days;};export const isToday=date=>{const today=new Date();return date.toDateString()===today.toDateString();};export const isSameMonth=(date,month,year)=>{return date.getMonth()===month&&date.getFullYear()===year;};export const formatDateForDisplay=date=>{return date.toLocaleDateString('en-US',{weekday:'long',year:'numeric',month:'long',day:'numeric'});};export const formatTimeForDisplay=date=>{return date.toLocaleTimeString('en-US',{hour:'2-digit',minute:'2-digit'});};export const getMonthName=month=>{const monthNames=['January','February','March','April','May','June','July','August','September','October','November','December'];return monthNames[month];};export const getDayName=day=>{const dayNames=['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];return dayNames[day];};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}