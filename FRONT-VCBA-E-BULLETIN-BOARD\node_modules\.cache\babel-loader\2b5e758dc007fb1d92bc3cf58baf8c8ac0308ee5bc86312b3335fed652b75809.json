{"ast": null, "code": "// Form utilities for announcement management\n\n// Validation rules for announcements\nexport const announcementValidationRules = {\n  title: {\n    required: true,\n    minLength: 1,\n    maxLength: 255\n  },\n  content: {\n    required: true,\n    minLength: 1,\n    maxLength: 10000\n  },\n  category_id: {\n    required: true,\n    type: 'integer',\n    min: 1\n  },\n  subcategory_id: {\n    required: false,\n    type: 'integer',\n    min: 1\n  },\n  status: {\n    required: false,\n    enum: ['draft', 'scheduled', 'published', 'archived']\n  },\n  custom: {\n    scheduled_publish_at: (value, formData) => {\n      if (!value || value.trim() === '') {\n        return null; // Optional field\n      }\n      const date = new Date(value);\n      if (isNaN(date.getTime())) {\n        return 'Please enter a valid date and time';\n      }\n      if (date <= new Date()) {\n        return 'Scheduled publish date must be in the future';\n      }\n      return null;\n    }\n  }\n};\n\n// Validate form fields\nexport const validateFormFields = (formData, validationRules) => {\n  const errors = {};\n  const rules = validationRules || announcementValidationRules;\n\n  // Title validation\n  if (!formData.title || formData.title.trim().length === 0) {\n    errors.title = 'Title is required';\n  } else if (formData.title.trim().length > 255) {\n    errors.title = 'Title must be 255 characters or less';\n  }\n\n  // Content validation\n  if (!formData.content || formData.content.trim().length === 0) {\n    errors.content = 'Content is required';\n  } else if (formData.content.trim().length > 10000) {\n    errors.content = 'Content must be 10000 characters or less';\n  }\n\n  // Category validation\n  if (!formData.category_id || formData.category_id.trim() === '') {\n    errors.category_id = 'Category is required';\n  } else {\n    const categoryId = parseInt(formData.category_id);\n    if (isNaN(categoryId) || categoryId < 1) {\n      errors.category_id = 'Please select a valid category';\n    }\n  }\n\n  // Subcategory validation (optional)\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (isNaN(subcategoryId) || subcategoryId < 1) {\n      errors.subcategory_id = 'Please select a valid subcategory';\n    }\n  }\n\n  // Status validation\n  const validStatuses = ['draft', 'scheduled', 'published', 'archived'];\n  if (formData.status && !validStatuses.includes(formData.status)) {\n    errors.status = 'Invalid status selected';\n  }\n\n  // Custom validations\n  if (rules.custom) {\n    Object.keys(rules.custom).forEach(fieldName => {\n      const validator = rules.custom[fieldName];\n      if (typeof validator === 'function') {\n        const fieldValue = formData[fieldName];\n        const validationError = validator(fieldValue, formData);\n        if (validationError) {\n          errors[fieldName] = validationError;\n        }\n      }\n    });\n  }\n  return errors;\n};\n\n// Create FormData object for API submission\nexport const createFormData = (formData, selectedImages = []) => {\n  const form = new FormData();\n\n  // Add text fields with proper data type conversion\n  form.append('title', formData.title.trim());\n  form.append('content', formData.content.trim());\n\n  // Convert string IDs to integers for backend validation\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    form.append('category_id', categoryId.toString());\n  }\n\n  // Only add subcategory_id if it's a valid positive integer\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      form.append('subcategory_id', subcategoryId.toString());\n    }\n  }\n\n  // Add status (default to 'draft' if not provided)\n  form.append('status', formData.status || 'draft');\n\n  // Convert boolean values to strings for FormData\n  form.append('is_pinned', formData.is_pinned ? '1' : '0');\n  form.append('is_alert', formData.is_alert ? '1' : '0');\n  form.append('allow_comments', formData.allow_comments ? '1' : '0');\n  form.append('allow_sharing', formData.allow_sharing ? '1' : '0');\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    form.append('scheduled_publish_at', formData.scheduled_publish_at);\n  }\n\n  // Add image files\n  selectedImages.forEach(image => {\n    form.append('images', image);\n  });\n  return form;\n};\n\n// Create JSON data object for API submission (alternative to FormData)\nexport const createJsonData = formData => {\n  const data = {\n    title: formData.title.trim(),\n    content: formData.content.trim(),\n    status: formData.status || 'draft',\n    is_pinned: formData.is_pinned,\n    is_alert: formData.is_alert,\n    allow_comments: formData.allow_comments,\n    allow_sharing: formData.allow_sharing\n  };\n\n  // Convert string IDs to integers\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    data.category_id = categoryId;\n  }\n\n  // Only add subcategory_id if it's a valid positive integer\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      data.subcategory_id = subcategoryId;\n    }\n  }\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    data.scheduled_publish_at = formData.scheduled_publish_at;\n  }\n  return data;\n};\n\n// Helper function to convert form data for updates (only include changed fields)\nexport const createUpdateData = formData => {\n  const updateData = {};\n\n  // Only include fields that have values and might have changed\n  if (formData.title && formData.title.trim() !== '') {\n    updateData.title = formData.title.trim();\n  }\n  if (formData.content && formData.content.trim() !== '') {\n    updateData.content = formData.content.trim();\n  }\n\n  // Convert and validate category_id\n  const categoryId = parseInt(formData.category_id);\n  if (!isNaN(categoryId) && categoryId > 0) {\n    updateData.category_id = categoryId;\n  }\n\n  // Convert and validate subcategory_id (can be null/undefined)\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\n    const subcategoryId = parseInt(formData.subcategory_id);\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\n      updateData.subcategory_id = subcategoryId;\n    }\n  } else {\n    // Explicitly set to null if empty (to clear existing subcategory)\n    updateData.subcategory_id = null;\n  }\n\n  // Always include these fields as they have default values\n  updateData.status = formData.status || 'draft';\n  updateData.is_pinned = formData.is_pinned;\n  updateData.is_alert = formData.is_alert;\n  updateData.allow_comments = formData.allow_comments;\n  updateData.allow_sharing = formData.allow_sharing;\n\n  // Add scheduled publish date if provided\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\n    updateData.scheduled_publish_at = formData.scheduled_publish_at;\n  }\n  return updateData;\n};\n\n// File validation function for image uploads\nexport const validateFile = file => {\n  // Check file size (5MB limit)\n  const maxSize = 5 * 1024 * 1024; // 5MB in bytes\n  if (file.size > maxSize) {\n    return {\n      isValid: false,\n      error: 'File size must be less than 5MB'\n    };\n  }\n\n  // Check file type\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      isValid: false,\n      error: 'Only JPEG, PNG, GIF, and WebP images are allowed'\n    };\n  }\n  return {\n    isValid: true\n  };\n};\n\n// Format file size for display\nexport const formatFileSize = bytes => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};", "map": {"version": 3, "names": ["announcementValidationRules", "title", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "content", "category_id", "type", "min", "subcategory_id", "status", "enum", "custom", "scheduled_publish_at", "value", "formData", "trim", "date", "Date", "isNaN", "getTime", "validate<PERSON><PERSON><PERSON><PERSON>s", "validationRules", "errors", "rules", "length", "categoryId", "parseInt", "subcategoryId", "validStatuses", "includes", "Object", "keys", "for<PERSON>ach", "fieldName", "validator", "fieldValue", "validationError", "createFormData", "selectedImages", "form", "FormData", "append", "toString", "is_pinned", "is_alert", "allow_comments", "allow_sharing", "image", "createJsonData", "data", "createUpdateData", "updateData", "validateFile", "file", "maxSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "error", "allowedTypes", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/formUtils.ts"], "sourcesContent": ["// Form utilities for announcement management\r\nexport interface AnnouncementFormData {\r\n  title: string;\r\n  content: string;\r\n  category_id: string;\r\n  subcategory_id: string;\r\n  status: 'draft' | 'published' | 'scheduled' | 'archived';\r\n  is_pinned: boolean;\r\n  is_alert: boolean;\r\n  allow_comments: boolean;\r\n  allow_sharing: boolean;\r\n  scheduled_publish_at: string;\r\n}\r\n\r\n// Validation rules for announcements\r\nexport const announcementValidationRules = {\r\n  title: {\r\n    required: true,\r\n    minLength: 1,\r\n    maxLength: 255\r\n  },\r\n  content: {\r\n    required: true,\r\n    minLength: 1,\r\n    maxLength: 10000\r\n  },\r\n  category_id: {\r\n    required: true,\r\n    type: 'integer',\r\n    min: 1\r\n  },\r\n  subcategory_id: {\r\n    required: false,\r\n    type: 'integer',\r\n    min: 1\r\n  },\r\n  status: {\r\n    required: false,\r\n    enum: ['draft', 'scheduled', 'published', 'archived']\r\n  },\r\n  custom: {\r\n    scheduled_publish_at: (value: any, formData?: any) => {\r\n      if (!value || value.trim() === '') {\r\n        return null; // Optional field\r\n      }\r\n\r\n      const date = new Date(value);\r\n      if (isNaN(date.getTime())) {\r\n        return 'Please enter a valid date and time';\r\n      }\r\n\r\n      if (date <= new Date()) {\r\n        return 'Scheduled publish date must be in the future';\r\n      }\r\n\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Validate form fields\r\nexport const validateFormFields = (formData: AnnouncementFormData, validationRules?: any): { [key: string]: string } => {\r\n  const errors: { [key: string]: string } = {};\r\n  const rules = validationRules || announcementValidationRules;\r\n\r\n  // Title validation\r\n  if (!formData.title || formData.title.trim().length === 0) {\r\n    errors.title = 'Title is required';\r\n  } else if (formData.title.trim().length > 255) {\r\n    errors.title = 'Title must be 255 characters or less';\r\n  }\r\n\r\n  // Content validation\r\n  if (!formData.content || formData.content.trim().length === 0) {\r\n    errors.content = 'Content is required';\r\n  } else if (formData.content.trim().length > 10000) {\r\n    errors.content = 'Content must be 10000 characters or less';\r\n  }\r\n\r\n  // Category validation\r\n  if (!formData.category_id || formData.category_id.trim() === '') {\r\n    errors.category_id = 'Category is required';\r\n  } else {\r\n    const categoryId = parseInt(formData.category_id);\r\n    if (isNaN(categoryId) || categoryId < 1) {\r\n      errors.category_id = 'Please select a valid category';\r\n    }\r\n  }\r\n\r\n  // Subcategory validation (optional)\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (isNaN(subcategoryId) || subcategoryId < 1) {\r\n      errors.subcategory_id = 'Please select a valid subcategory';\r\n    }\r\n  }\r\n\r\n  // Status validation\r\n  const validStatuses = ['draft', 'scheduled', 'published', 'archived'];\r\n  if (formData.status && !validStatuses.includes(formData.status)) {\r\n    errors.status = 'Invalid status selected';\r\n  }\r\n\r\n  // Custom validations\r\n  if (rules.custom) {\r\n    Object.keys(rules.custom).forEach(fieldName => {\r\n      const validator = rules.custom[fieldName];\r\n      if (typeof validator === 'function') {\r\n        const fieldValue = (formData as any)[fieldName];\r\n        const validationError = validator(fieldValue, formData);\r\n        if (validationError) {\r\n          errors[fieldName] = validationError;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  return errors;\r\n};\r\n\r\n// Create FormData object for API submission\r\nexport const createFormData = (formData: AnnouncementFormData, selectedImages: File[] = []): FormData => {\r\n  const form = new FormData();\r\n\r\n  // Add text fields with proper data type conversion\r\n  form.append('title', formData.title.trim());\r\n  form.append('content', formData.content.trim());\r\n\r\n  // Convert string IDs to integers for backend validation\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    form.append('category_id', categoryId.toString());\r\n  }\r\n\r\n  // Only add subcategory_id if it's a valid positive integer\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      form.append('subcategory_id', subcategoryId.toString());\r\n    }\r\n  }\r\n\r\n  // Add status (default to 'draft' if not provided)\r\n  form.append('status', formData.status || 'draft');\r\n\r\n  // Convert boolean values to strings for FormData\r\n  form.append('is_pinned', formData.is_pinned ? '1' : '0');\r\n  form.append('is_alert', formData.is_alert ? '1' : '0');\r\n  form.append('allow_comments', formData.allow_comments ? '1' : '0');\r\n  form.append('allow_sharing', formData.allow_sharing ? '1' : '0');\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    form.append('scheduled_publish_at', formData.scheduled_publish_at);\r\n  }\r\n\r\n  // Add image files\r\n  selectedImages.forEach((image) => {\r\n    form.append('images', image);\r\n  });\r\n\r\n  return form;\r\n};\r\n\r\n// Create JSON data object for API submission (alternative to FormData)\r\nexport const createJsonData = (formData: AnnouncementFormData): any => {\r\n  const data: any = {\r\n    title: formData.title.trim(),\r\n    content: formData.content.trim(),\r\n    status: formData.status || 'draft',\r\n    is_pinned: formData.is_pinned,\r\n    is_alert: formData.is_alert,\r\n    allow_comments: formData.allow_comments,\r\n    allow_sharing: formData.allow_sharing\r\n  };\r\n\r\n  // Convert string IDs to integers\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    data.category_id = categoryId;\r\n  }\r\n\r\n  // Only add subcategory_id if it's a valid positive integer\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      data.subcategory_id = subcategoryId;\r\n    }\r\n  }\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    data.scheduled_publish_at = formData.scheduled_publish_at;\r\n  }\r\n\r\n  return data;\r\n};\r\n\r\n// Helper function to convert form data for updates (only include changed fields)\r\nexport const createUpdateData = (formData: AnnouncementFormData): any => {\r\n  const updateData: any = {};\r\n\r\n  // Only include fields that have values and might have changed\r\n  if (formData.title && formData.title.trim() !== '') {\r\n    updateData.title = formData.title.trim();\r\n  }\r\n\r\n  if (formData.content && formData.content.trim() !== '') {\r\n    updateData.content = formData.content.trim();\r\n  }\r\n\r\n  // Convert and validate category_id\r\n  const categoryId = parseInt(formData.category_id);\r\n  if (!isNaN(categoryId) && categoryId > 0) {\r\n    updateData.category_id = categoryId;\r\n  }\r\n\r\n  // Convert and validate subcategory_id (can be null/undefined)\r\n  if (formData.subcategory_id && formData.subcategory_id.trim() !== '') {\r\n    const subcategoryId = parseInt(formData.subcategory_id);\r\n    if (!isNaN(subcategoryId) && subcategoryId > 0) {\r\n      updateData.subcategory_id = subcategoryId;\r\n    }\r\n  } else {\r\n    // Explicitly set to null if empty (to clear existing subcategory)\r\n    updateData.subcategory_id = null;\r\n  }\r\n\r\n  // Always include these fields as they have default values\r\n  updateData.status = formData.status || 'draft';\r\n  updateData.is_pinned = formData.is_pinned;\r\n  updateData.is_alert = formData.is_alert;\r\n  updateData.allow_comments = formData.allow_comments;\r\n  updateData.allow_sharing = formData.allow_sharing;\r\n\r\n  // Add scheduled publish date if provided\r\n  if (formData.scheduled_publish_at && formData.scheduled_publish_at.trim() !== '') {\r\n    updateData.scheduled_publish_at = formData.scheduled_publish_at;\r\n  }\r\n\r\n  return updateData;\r\n};\r\n\r\n// File validation function for image uploads\r\nexport const validateFile = (file: File): { isValid: boolean; error?: string } => {\r\n  // Check file size (5MB limit)\r\n  const maxSize = 5 * 1024 * 1024; // 5MB in bytes\r\n  if (file.size > maxSize) {\r\n    return { isValid: false, error: 'File size must be less than 5MB' };\r\n  }\r\n\r\n  // Check file type\r\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { isValid: false, error: 'Only JPEG, PNG, GIF, and WebP images are allowed' };\r\n  }\r\n\r\n  return { isValid: true };\r\n};\r\n\r\n// Format file size for display\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n};"], "mappings": "AAAA;;AAcA;AACA,OAAO,MAAMA,2BAA2B,GAAG;EACzCC,KAAK,EAAE;IACLC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC;EACDC,OAAO,EAAE;IACPH,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC;EACDE,WAAW,EAAE;IACXJ,QAAQ,EAAE,IAAI;IACdK,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE;EACP,CAAC;EACDC,cAAc,EAAE;IACdP,QAAQ,EAAE,KAAK;IACfK,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE;EACP,CAAC;EACDE,MAAM,EAAE;IACNR,QAAQ,EAAE,KAAK;IACfS,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU;EACtD,CAAC;EACDC,MAAM,EAAE;IACNC,oBAAoB,EAAEA,CAACC,KAAU,EAAEC,QAAc,KAAK;MACpD,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC,OAAO,IAAI,CAAC,CAAC;MACf;MAEA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACJ,KAAK,CAAC;MAC5B,IAAIK,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,oCAAoC;MAC7C;MAEA,IAAIH,IAAI,IAAI,IAAIC,IAAI,CAAC,CAAC,EAAE;QACtB,OAAO,8CAA8C;MACvD;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,kBAAkB,GAAGA,CAACN,QAA8B,EAAEO,eAAqB,KAAgC;EACtH,MAAMC,MAAiC,GAAG,CAAC,CAAC;EAC5C,MAAMC,KAAK,GAAGF,eAAe,IAAItB,2BAA2B;;EAE5D;EACA,IAAI,CAACe,QAAQ,CAACd,KAAK,IAAIc,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IACzDF,MAAM,CAACtB,KAAK,GAAG,mBAAmB;EACpC,CAAC,MAAM,IAAIc,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC,CAACS,MAAM,GAAG,GAAG,EAAE;IAC7CF,MAAM,CAACtB,KAAK,GAAG,sCAAsC;EACvD;;EAEA;EACA,IAAI,CAACc,QAAQ,CAACV,OAAO,IAAIU,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;IAC7DF,MAAM,CAAClB,OAAO,GAAG,qBAAqB;EACxC,CAAC,MAAM,IAAIU,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC,CAACS,MAAM,GAAG,KAAK,EAAE;IACjDF,MAAM,CAAClB,OAAO,GAAG,0CAA0C;EAC7D;;EAEA;EACA,IAAI,CAACU,QAAQ,CAACT,WAAW,IAAIS,QAAQ,CAACT,WAAW,CAACU,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC/DO,MAAM,CAACjB,WAAW,GAAG,sBAAsB;EAC7C,CAAC,MAAM;IACL,MAAMoB,UAAU,GAAGC,QAAQ,CAACZ,QAAQ,CAACT,WAAW,CAAC;IACjD,IAAIa,KAAK,CAACO,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;MACvCH,MAAM,CAACjB,WAAW,GAAG,gCAAgC;IACvD;EACF;;EAEA;EACA,IAAIS,QAAQ,CAACN,cAAc,IAAIM,QAAQ,CAACN,cAAc,CAACO,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMY,aAAa,GAAGD,QAAQ,CAACZ,QAAQ,CAACN,cAAc,CAAC;IACvD,IAAIU,KAAK,CAACS,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC7CL,MAAM,CAACd,cAAc,GAAG,mCAAmC;IAC7D;EACF;;EAEA;EACA,MAAMoB,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;EACrE,IAAId,QAAQ,CAACL,MAAM,IAAI,CAACmB,aAAa,CAACC,QAAQ,CAACf,QAAQ,CAACL,MAAM,CAAC,EAAE;IAC/Da,MAAM,CAACb,MAAM,GAAG,yBAAyB;EAC3C;;EAEA;EACA,IAAIc,KAAK,CAACZ,MAAM,EAAE;IAChBmB,MAAM,CAACC,IAAI,CAACR,KAAK,CAACZ,MAAM,CAAC,CAACqB,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAMC,SAAS,GAAGX,KAAK,CAACZ,MAAM,CAACsB,SAAS,CAAC;MACzC,IAAI,OAAOC,SAAS,KAAK,UAAU,EAAE;QACnC,MAAMC,UAAU,GAAIrB,QAAQ,CAASmB,SAAS,CAAC;QAC/C,MAAMG,eAAe,GAAGF,SAAS,CAACC,UAAU,EAAErB,QAAQ,CAAC;QACvD,IAAIsB,eAAe,EAAE;UACnBd,MAAM,CAACW,SAAS,CAAC,GAAGG,eAAe;QACrC;MACF;IACF,CAAC,CAAC;EACJ;EAEA,OAAOd,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAMe,cAAc,GAAGA,CAACvB,QAA8B,EAAEwB,cAAsB,GAAG,EAAE,KAAe;EACvG,MAAMC,IAAI,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE3B;EACAD,IAAI,CAACE,MAAM,CAAC,OAAO,EAAE3B,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC,CAAC;EAC3CwB,IAAI,CAACE,MAAM,CAAC,SAAS,EAAE3B,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMU,UAAU,GAAGC,QAAQ,CAACZ,QAAQ,CAACT,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACO,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxCc,IAAI,CAACE,MAAM,CAAC,aAAa,EAAEhB,UAAU,CAACiB,QAAQ,CAAC,CAAC,CAAC;EACnD;;EAEA;EACA,IAAI5B,QAAQ,CAACN,cAAc,IAAIM,QAAQ,CAACN,cAAc,CAACO,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMY,aAAa,GAAGD,QAAQ,CAACZ,QAAQ,CAACN,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACS,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CY,IAAI,CAACE,MAAM,CAAC,gBAAgB,EAAEd,aAAa,CAACe,QAAQ,CAAC,CAAC,CAAC;IACzD;EACF;;EAEA;EACAH,IAAI,CAACE,MAAM,CAAC,QAAQ,EAAE3B,QAAQ,CAACL,MAAM,IAAI,OAAO,CAAC;;EAEjD;EACA8B,IAAI,CAACE,MAAM,CAAC,WAAW,EAAE3B,QAAQ,CAAC6B,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC;EACxDJ,IAAI,CAACE,MAAM,CAAC,UAAU,EAAE3B,QAAQ,CAAC8B,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;EACtDL,IAAI,CAACE,MAAM,CAAC,gBAAgB,EAAE3B,QAAQ,CAAC+B,cAAc,GAAG,GAAG,GAAG,GAAG,CAAC;EAClEN,IAAI,CAACE,MAAM,CAAC,eAAe,EAAE3B,QAAQ,CAACgC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;;EAEhE;EACA,IAAIhC,QAAQ,CAACF,oBAAoB,IAAIE,QAAQ,CAACF,oBAAoB,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFwB,IAAI,CAACE,MAAM,CAAC,sBAAsB,EAAE3B,QAAQ,CAACF,oBAAoB,CAAC;EACpE;;EAEA;EACA0B,cAAc,CAACN,OAAO,CAAEe,KAAK,IAAK;IAChCR,IAAI,CAACE,MAAM,CAAC,QAAQ,EAAEM,KAAK,CAAC;EAC9B,CAAC,CAAC;EAEF,OAAOR,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAIlC,QAA8B,IAAU;EACrE,MAAMmC,IAAS,GAAG;IAChBjD,KAAK,EAAEc,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC;IAC5BX,OAAO,EAAEU,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC;IAChCN,MAAM,EAAEK,QAAQ,CAACL,MAAM,IAAI,OAAO;IAClCkC,SAAS,EAAE7B,QAAQ,CAAC6B,SAAS;IAC7BC,QAAQ,EAAE9B,QAAQ,CAAC8B,QAAQ;IAC3BC,cAAc,EAAE/B,QAAQ,CAAC+B,cAAc;IACvCC,aAAa,EAAEhC,QAAQ,CAACgC;EAC1B,CAAC;;EAED;EACA,MAAMrB,UAAU,GAAGC,QAAQ,CAACZ,QAAQ,CAACT,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACO,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxCwB,IAAI,CAAC5C,WAAW,GAAGoB,UAAU;EAC/B;;EAEA;EACA,IAAIX,QAAQ,CAACN,cAAc,IAAIM,QAAQ,CAACN,cAAc,CAACO,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMY,aAAa,GAAGD,QAAQ,CAACZ,QAAQ,CAACN,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACS,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CsB,IAAI,CAACzC,cAAc,GAAGmB,aAAa;IACrC;EACF;;EAEA;EACA,IAAIb,QAAQ,CAACF,oBAAoB,IAAIE,QAAQ,CAACF,oBAAoB,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFkC,IAAI,CAACrC,oBAAoB,GAAGE,QAAQ,CAACF,oBAAoB;EAC3D;EAEA,OAAOqC,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAIpC,QAA8B,IAAU;EACvE,MAAMqC,UAAe,GAAG,CAAC,CAAC;;EAE1B;EACA,IAAIrC,QAAQ,CAACd,KAAK,IAAIc,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAClDoC,UAAU,CAACnD,KAAK,GAAGc,QAAQ,CAACd,KAAK,CAACe,IAAI,CAAC,CAAC;EAC1C;EAEA,IAAID,QAAQ,CAACV,OAAO,IAAIU,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACtDoC,UAAU,CAAC/C,OAAO,GAAGU,QAAQ,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC;EAC9C;;EAEA;EACA,MAAMU,UAAU,GAAGC,QAAQ,CAACZ,QAAQ,CAACT,WAAW,CAAC;EACjD,IAAI,CAACa,KAAK,CAACO,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACxC0B,UAAU,CAAC9C,WAAW,GAAGoB,UAAU;EACrC;;EAEA;EACA,IAAIX,QAAQ,CAACN,cAAc,IAAIM,QAAQ,CAACN,cAAc,CAACO,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACpE,MAAMY,aAAa,GAAGD,QAAQ,CAACZ,QAAQ,CAACN,cAAc,CAAC;IACvD,IAAI,CAACU,KAAK,CAACS,aAAa,CAAC,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC9CwB,UAAU,CAAC3C,cAAc,GAAGmB,aAAa;IAC3C;EACF,CAAC,MAAM;IACL;IACAwB,UAAU,CAAC3C,cAAc,GAAG,IAAI;EAClC;;EAEA;EACA2C,UAAU,CAAC1C,MAAM,GAAGK,QAAQ,CAACL,MAAM,IAAI,OAAO;EAC9C0C,UAAU,CAACR,SAAS,GAAG7B,QAAQ,CAAC6B,SAAS;EACzCQ,UAAU,CAACP,QAAQ,GAAG9B,QAAQ,CAAC8B,QAAQ;EACvCO,UAAU,CAACN,cAAc,GAAG/B,QAAQ,CAAC+B,cAAc;EACnDM,UAAU,CAACL,aAAa,GAAGhC,QAAQ,CAACgC,aAAa;;EAEjD;EACA,IAAIhC,QAAQ,CAACF,oBAAoB,IAAIE,QAAQ,CAACF,oBAAoB,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChFoC,UAAU,CAACvC,oBAAoB,GAAGE,QAAQ,CAACF,oBAAoB;EACjE;EAEA,OAAOuC,UAAU;AACnB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAIC,IAAU,IAA2C;EAChF;EACA,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;EACjC,IAAID,IAAI,CAACE,IAAI,GAAGD,OAAO,EAAE;IACvB,OAAO;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAkC,CAAC;EACrE;;EAEA;EACA,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACxF,IAAI,CAACA,YAAY,CAAC7B,QAAQ,CAACwB,IAAI,CAAC/C,IAAI,CAAC,EAAE;IACrC,OAAO;MAAEkD,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAmD,CAAC;EACtF;EAEA,OAAO;IAAED,OAAO,EAAE;EAAK,CAAC;AAC1B,CAAC;;AAED;AACA,OAAO,MAAMG,cAAc,GAAIC,KAAa,IAAa;EACvD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAMC,CAAC,GAAG,IAAI;EACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;EAEnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;AACzE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}