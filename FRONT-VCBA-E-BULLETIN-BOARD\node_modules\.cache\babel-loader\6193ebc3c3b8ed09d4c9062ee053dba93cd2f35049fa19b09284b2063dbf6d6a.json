{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\ErrorBoundary\\\\ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n  render() {\n    if (this.state.hasError) {\n      // Render custom fallback UI if provided\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-boundary\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-boundary__container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-boundary__icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"48\",\n              height: \"48\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"15\",\n                y1: \"9\",\n                x2: \"9\",\n                y2: \"15\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"9\",\n                y1: \"9\",\n                x2: \"15\",\n                y2: \"15\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"We're sorry, but something unexpected happened. Please try refreshing the page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            className: \"error-boundary__button\",\n            children: \"Refresh Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "onError", "render", "fallback", "className", "children", "width", "height", "viewBox", "fill", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x1", "y1", "x2", "y2", "onClick", "window", "location", "reload"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/ErrorBoundary/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log the error\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Render custom fallback UI if provided\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default fallback UI\n      return (\n        <div className=\"error-boundary\">\n          <div className=\"error-boundary__container\">\n            <div className=\"error-boundary__icon\">\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n              </svg>\n            </div>\n            <h2>Something went wrong</h2>\n            <p>\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"error-boundary__button\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa/D,MAAMC,aAAa,SAASH,SAAS,CAAe;EAClDI,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOC,wBAAwBA,CAACC,KAAY,EAAS;IACnD;IACA,OAAO;MAAEF,QAAQ,EAAE,IAAI;MAAEE;IAAM,CAAC;EAClC;EAEAC,iBAAiBA,CAACD,KAAY,EAAEE,SAAoB,EAAE;IACpD;IACAC,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;;IAEjE;IACA,IAAI,IAAI,CAACN,KAAK,CAACQ,OAAO,EAAE;MACtB,IAAI,CAACR,KAAK,CAACQ,OAAO,CAACJ,KAAK,EAAEE,SAAS,CAAC;IACtC;EACF;EAEAG,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB;MACA,IAAI,IAAI,CAACF,KAAK,CAACU,QAAQ,EAAE;QACvB,OAAO,IAAI,CAACV,KAAK,CAACU,QAAQ;MAC5B;;MAEA;MACA,oBACEb,OAAA;QAAKc,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7Bf,OAAA;UAAKc,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCf,OAAA;YAAKc,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCf,OAAA;cAAKgB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAJ,QAAA,gBACzDf,OAAA;gBAAQoB,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACtE5B,OAAA;gBAAM6B,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACT,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC3E5B,OAAA;gBAAM6B,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACT,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5B,OAAA;YAAAe,QAAA,EAAI;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7B5B,OAAA;YAAAe,QAAA,EAAG;UAEH;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ5B,OAAA;YACEiC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCtB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACnC;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACzB,KAAK,CAACY,QAAQ;EAC5B;AACF;AAEA,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}