{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}], [\"path\", {\n  d: \"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2\",\n  key: \"80xlxr\"\n}], [\"path\", {\n  d: \"M5 10v2a7 7 0 0 0 12 5\",\n  key: \"p2k8kg\"\n}], [\"path\", {\n  d: \"M15 9.34V5a3 3 0 0 0-5.68-1.33\",\n  key: \"1gzdoj\"\n}], [\"path\", {\n  d: \"M9 9v3a3 3 0 0 0 5.12 2.12\",\n  key: \"r2i35w\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"19\",\n  y2: \"22\",\n  key: \"x3vr5v\"\n}]];\nconst MicOff = createLucideIcon(\"mic-off\", __iconNode);\nexport { __iconNode, MicOff as default };\n//# sourceMappingURL=mic-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}