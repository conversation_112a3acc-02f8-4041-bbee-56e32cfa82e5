{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22V8\",\n  key: \"qkxhtm\"\n}], [\"path\", {\n  d: \"M5 12H2a10 10 0 0 0 20 0h-3\",\n  key: \"1hv3nh\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"rqqgnr\"\n}]];\nconst Anchor = createLucideIcon(\"anchor\", __iconNode);\nexport { __iconNode, Anchor as default };\n//# sourceMappingURL=anchor.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}