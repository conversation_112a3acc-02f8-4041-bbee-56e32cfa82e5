{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\AdminAuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Helper function to map backend user data to frontend format (outside component to prevent re-creation)\nconst mapUserData = backendUser => {\n  return {\n    ...backendUser,\n    profilePicture: backendUser.profile_picture || null\n  };\n};\n\n// Admin Auth context interface\n\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AdminAuthContext = /*#__PURE__*/createContext(undefined);\nconst AdminAuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 AdminAuth - Checking authentication status');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n\n      // First check local storage for authentication data\n      const storedUser = AdminAuthService.getStoredUser();\n      const hasToken = AdminAuthService.isAuthenticated();\n\n      // console.log('🔍 AdminAuth - Local auth data:', {\n      //   hasUser: !!storedUser,\n      //   hasToken: !!hasToken,\n      //   userRole: storedUser?.role,\n      //   userEmail: storedUser?.email\n      // });\n\n      // If we have local data and it's an admin user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'admin') {\n        console.log('✅ AdminAuth - Admin user authenticated from local storage:', storedUser.email);\n        const mappedUser = mapUserData(storedUser);\n        dispatch({\n          type: 'AUTH_SUCCESS',\n          payload: mappedUser\n        });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ AdminAuth - No admin user found in local storage or wrong role');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    } catch (error) {\n      console.error('❌ AdminAuth - Auth check failed:', error.message);\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n\n      // Use AdminAuthService for login\n      const response = await AdminAuthService.login(credentials);\n\n      // Verify the user is actually an admin\n      if (response.data.user.role !== 'admin') {\n        throw new Error('Access denied: Admin privileges required');\n      }\n      console.log('✅ AdminAuth - Admin login successful:', response.data.user.email);\n      const mappedUser = mapUserData(response.data.user);\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: mappedUser\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Admin login failed. Please try again.';\n      console.error('❌ AdminAuth - Login failed:', errorMessage);\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      console.log('🚪 AdminAuth - Starting admin logout process');\n      await AdminAuthService.logout();\n      console.log('✅ AdminAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ AdminAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 AdminAuth - Clearing admin state');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n\n      // Redirect to admin login\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Register function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await AdminAuthService.registerAdmin(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      return response;\n    } catch (error) {\n      const errorMessage = error.message || 'Admin registration failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await AdminAuthService.verifyOtp(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async email => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await AdminAuthService.resendOtp(email);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Refresh user data\n  const refreshUser = useCallback(async () => {\n    try {\n      var _response$data;\n      console.log('🔄 AdminAuth - Refreshing admin user data');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n      const response = await AdminAuthService.getProfile();\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.admin) {\n        console.log('✅ AdminAuth - User data refreshed successfully');\n        const mappedUser = mapUserData(response.data.admin);\n        dispatch({\n          type: 'AUTH_SUCCESS',\n          payload: mappedUser\n        });\n      } else {\n        throw new Error('Failed to refresh user data');\n      }\n    } catch (error) {\n      console.error('❌ AdminAuth - Failed to refresh user data:', error);\n      // Don't logout on refresh failure, just log the error\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    }\n  }, []);\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 AdminAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 AdminAuth - Unauthorized request detected, logging out admin');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AdminAuth - Component mounted, checking admin auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    refreshUser,\n    userType: 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AdminAuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use admin auth context\n_s(AdminAuthProvider, \"jLpwENcvQkI6vdgeELfUnDllGiY=\");\n_c = AdminAuthProvider;\nexport const useAdminAuth = () => {\n  _s2();\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n};\n_s2(useAdminAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport { AdminAuthProvider };\nexport default AdminAuthContext;\nvar _c;\n$RefreshReg$(_c, \"AdminAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "useEffect", "AdminAuthService", "jsxDEV", "_jsxDEV", "mapUserData", "backendUser", "profilePicture", "profile_picture", "initialState", "user", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AdminAuthContext", "undefined", "AdminAuth<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "clearError", "checkAuthStatus", "console", "log", "storedUser", "getStoredUser", "hasToken", "role", "email", "mappedUser", "message", "login", "credentials", "response", "data", "Error", "errorMessage", "logout", "window", "location", "href", "register", "registerAdmin", "verifyOtp", "resendOtp", "refreshUser", "_response$data", "getProfile", "success", "admin", "value", "userType", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAdminAuth", "_s2", "context", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/AdminAuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport {\n  AuthState,\n  LoginCredentials,\n  AdminRegistrationData,\n  RegistrationResponse,\n  OtpVerificationData,\n  User\n} from '../types/auth.types';\n\n// Helper function to map backend user data to frontend format (outside component to prevent re-creation)\nconst mapUserData = (backendUser: any): User => {\n  return {\n    ...backendUser,\n    profilePicture: backendUser.profile_picture || null,\n  };\n};\n\n// Admin Auth context interface\ninterface AdminAuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n  register: (data: AdminRegistrationData) => Promise<RegistrationResponse>;\n  verifyOtp: (data: OtpVerificationData) => Promise<void>;\n  resendOtp: (email: string) => Promise<void>;\n  clearError: () => void;\n  checkAuthStatus: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n  userType: 'admin';\n}\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: any }\n  | { type: 'AUTH_ERROR'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'CLEAR_ERROR' };\n\n// Reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return { ...state, isLoading: true, error: null };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'CLEAR_ERROR':\n      return { ...state, error: null };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);\n\n\n\nconst AdminAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 AdminAuth - Checking authentication status');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      // First check local storage for authentication data\n      const storedUser = AdminAuthService.getStoredUser();\n      const hasToken = AdminAuthService.isAuthenticated();\n\n      // console.log('🔍 AdminAuth - Local auth data:', {\n      //   hasUser: !!storedUser,\n      //   hasToken: !!hasToken,\n      //   userRole: storedUser?.role,\n      //   userEmail: storedUser?.email\n      // });\n\n      // If we have local data and it's an admin user, authenticate immediately\n      if (storedUser && hasToken && storedUser.role === 'admin') {\n        console.log('✅ AdminAuth - Admin user authenticated from local storage:', storedUser.email);\n        const mappedUser = mapUserData(storedUser);\n        dispatch({ type: 'AUTH_SUCCESS', payload: mappedUser });\n        return;\n      }\n\n      // If no local data or wrong role, logout\n      console.log('❌ AdminAuth - No admin user found in local storage or wrong role');\n      dispatch({ type: 'AUTH_LOGOUT' });\n\n    } catch (error: any) {\n      console.error('❌ AdminAuth - Auth check failed:', error.message);\n      dispatch({ type: 'AUTH_LOGOUT' });\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      // Use AdminAuthService for login\n      const response = await AdminAuthService.login(credentials);\n      \n      // Verify the user is actually an admin\n      if (response.data.user.role !== 'admin') {\n        throw new Error('Access denied: Admin privileges required');\n      }\n\n      console.log('✅ AdminAuth - Admin login successful:', response.data.user.email);\n      const mappedUser = mapUserData(response.data.user);\n      dispatch({ type: 'AUTH_SUCCESS', payload: mappedUser });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Admin login failed. Please try again.';\n      console.error('❌ AdminAuth - Login failed:', errorMessage);\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      console.log('🚪 AdminAuth - Starting admin logout process');\n      await AdminAuthService.logout();\n      console.log('✅ AdminAuth - Server logout successful');\n    } catch (error) {\n      console.error('❌ AdminAuth - Logout error:', error);\n    } finally {\n      console.log('🧹 AdminAuth - Clearing admin state');\n      dispatch({ type: 'AUTH_LOGOUT' });\n      \n      // Redirect to admin login\n      window.location.href = '/admin/login';\n    }\n  };\n\n  // Register function\n  const register = async (data: AdminRegistrationData): Promise<RegistrationResponse> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await AdminAuthService.registerAdmin(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n      return response;\n    } catch (error: any) {\n      const errorMessage = error.message || 'Admin registration failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async (data: OtpVerificationData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await AdminAuthService.verifyOtp(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async (email: string): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await AdminAuthService.resendOtp(email);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Refresh user data\n  const refreshUser = useCallback(async (): Promise<void> => {\n    try {\n      console.log('🔄 AdminAuth - Refreshing admin user data');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      const response = await AdminAuthService.getProfile();\n\n      if (response.success && response.data?.admin) {\n        console.log('✅ AdminAuth - User data refreshed successfully');\n        const mappedUser = mapUserData(response.data.admin);\n        dispatch({ type: 'AUTH_SUCCESS', payload: mappedUser });\n      } else {\n        throw new Error('Failed to refresh user data');\n      }\n    } catch (error: any) {\n      console.error('❌ AdminAuth - Failed to refresh user data:', error);\n      // Don't logout on refresh failure, just log the error\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  }, []);\n\n  // Setup response interceptor for handling unauthorized requests - DISABLED\n  useEffect(() => {\n    console.log('🔧 AdminAuth - Response interceptor DISABLED to prevent automatic logouts');\n    // setupResponseInterceptor(() => {\n    //   console.log('🚨 AdminAuth - Unauthorized request detected, logging out admin');\n    //   dispatch({ type: 'AUTH_LOGOUT' });\n    // });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AdminAuth - Component mounted, checking admin auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value: AdminAuthContextType = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    refreshUser,\n    userType: 'admin',\n  };\n\n  return (\n    <AdminAuthContext.Provider value={value}>\n      {children}\n    </AdminAuthContext.Provider>\n  );\n};\n\n// Hook to use admin auth context\nexport const useAdminAuth = (): AdminAuthContextType => {\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n};\n\nexport { AdminAuthProvider };\nexport default AdminAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC5F,SAASC,gBAAgB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUlE;AACA,MAAMC,WAAW,GAAIC,WAAgB,IAAW;EAC9C,OAAO;IACL,GAAGA,WAAW;IACdC,cAAc,EAAED,WAAW,CAACE,eAAe,IAAI;EACjD,CAAC;AACH,CAAC;;AAED;;AAaA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;;AASA;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QAAE,GAAGF,KAAK;QAAEH,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAK,CAAC;IACnD,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAEM,MAAM,CAACE,OAAO;QACpBP,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,YAAY;MACf,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGE,KAAK;QAAEH,SAAS,EAAEI,MAAM,CAACE;MAAQ,CAAC;IAChD,KAAK,aAAa;MAChB,OAAO;QAAE,GAAGH,KAAK;QAAEF,KAAK,EAAE;MAAK,CAAC;IAClC;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,gBAAgB,gBAAGtB,aAAa,CAAmCuB,SAAS,CAAC;AAInF,MAAMC,iBAA0D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGzB,UAAU,CAACe,WAAW,EAAEL,YAAY,CAAC;;EAE/D;EACA,MAAMgB,UAAU,GAAGzB,WAAW,CAAC,MAAM;IACnCwB,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,eAAe,GAAG1B,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF2B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DJ,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;;MAEhD;MACA,MAAMW,UAAU,GAAG3B,gBAAgB,CAAC4B,aAAa,CAAC,CAAC;MACnD,MAAMC,QAAQ,GAAG7B,gBAAgB,CAACS,eAAe,CAAC,CAAC;;MAEnD;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAIkB,UAAU,IAAIE,QAAQ,IAAIF,UAAU,CAACG,IAAI,KAAK,OAAO,EAAE;QACzDL,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEC,UAAU,CAACI,KAAK,CAAC;QAC3F,MAAMC,UAAU,GAAG7B,WAAW,CAACwB,UAAU,CAAC;QAC1CL,QAAQ,CAAC;UAAEP,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAEgB;QAAW,CAAC,CAAC;QACvD;MACF;;MAEA;MACAP,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/EJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IAEnC,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBc,OAAO,CAACd,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAACsB,OAAO,CAAC;MAChEX,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmB,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFb,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;;MAEhC;MACA,MAAMqB,QAAQ,GAAG,MAAMpC,gBAAgB,CAACkC,KAAK,CAACC,WAAW,CAAC;;MAE1D;MACA,IAAIC,QAAQ,CAACC,IAAI,CAAC7B,IAAI,CAACsB,IAAI,KAAK,OAAO,EAAE;QACvC,MAAM,IAAIQ,KAAK,CAAC,0CAA0C,CAAC;MAC7D;MAEAb,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEU,QAAQ,CAACC,IAAI,CAAC7B,IAAI,CAACuB,KAAK,CAAC;MAC9E,MAAMC,UAAU,GAAG7B,WAAW,CAACiC,QAAQ,CAACC,IAAI,CAAC7B,IAAI,CAAC;MAClDc,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEgB;MAAW,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOrB,KAAU,EAAE;MACnB,MAAM4B,YAAY,GAAG5B,KAAK,CAACsB,OAAO,IAAI,uCAAuC;MAC7ER,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAE4B,YAAY,CAAC;MAC1DjB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM5B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM6B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACFf,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,MAAM1B,gBAAgB,CAACwC,MAAM,CAAC,CAAC;MAC/Bf,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRc,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDJ,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;;MAEjC;MACA0B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;IACvC;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOP,IAA2B,IAAoC;IACrF,IAAI;MACFf,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMqB,QAAQ,GAAG,MAAMpC,gBAAgB,CAAC6C,aAAa,CAACR,IAAI,CAAC;MAE3Df,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjD,OAAOoB,QAAQ;IACjB,CAAC,CAAC,OAAOzB,KAAU,EAAE;MACnB,MAAM4B,YAAY,GAAG5B,KAAK,CAACsB,OAAO,IAAI,8CAA8C;MACpFX,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM5B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAG,MAAOT,IAAyB,IAAoB;IACpE,IAAI;MACFf,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMf,gBAAgB,CAAC8C,SAAS,CAACT,IAAI,CAAC;MAEtCf,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM4B,YAAY,GAAG5B,KAAK,CAACsB,OAAO,IAAI,4CAA4C;MAClFX,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM5B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoC,SAAS,GAAG,MAAOhB,KAAa,IAAoB;IACxD,IAAI;MACFT,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMf,gBAAgB,CAAC+C,SAAS,CAAChB,KAAK,CAAC;MAEvCT,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM4B,YAAY,GAAG5B,KAAK,CAACsB,OAAO,IAAI,yCAAyC;MAC/EX,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEuB;MAAa,CAAC,CAAC;MACvD,MAAM5B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAGlD,WAAW,CAAC,YAA2B;IACzD,IAAI;MAAA,IAAAmD,cAAA;MACFxB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDJ,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhD,MAAMoB,QAAQ,GAAG,MAAMpC,gBAAgB,CAACkD,UAAU,CAAC,CAAC;MAEpD,IAAId,QAAQ,CAACe,OAAO,KAAAF,cAAA,GAAIb,QAAQ,CAACC,IAAI,cAAAY,cAAA,eAAbA,cAAA,CAAeG,KAAK,EAAE;QAC5C3B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,MAAMM,UAAU,GAAG7B,WAAW,CAACiC,QAAQ,CAACC,IAAI,CAACe,KAAK,CAAC;QACnD9B,QAAQ,CAAC;UAAEP,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAEgB;QAAW,CAAC,CAAC;MACzD,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF,CAAC,CAAC,OAAO3B,KAAU,EAAE;MACnBc,OAAO,CAACd,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE;MACAW,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjB,SAAS,CAAC,MAAM;IACd0B,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;IACxF;IACA;IACA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3B,SAAS,CAAC,MAAM;IACd0B,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3EF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAM6B,KAA2B,GAAG;IAClC,GAAGxC,KAAK;IACRqB,KAAK;IACLM,MAAM;IACNI,QAAQ;IACRE,SAAS;IACTC,SAAS;IACTxB,UAAU;IACVC,eAAe;IACfwB,WAAW;IACXM,QAAQ,EAAE;EACZ,CAAC;EAED,oBACEpD,OAAA,CAACe,gBAAgB,CAACsC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EACrCA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;;AAED;AAAAtC,EAAA,CA5LMF,iBAA0D;AAAAyC,EAAA,GAA1DzC,iBAA0D;AA6LhE,OAAO,MAAM0C,YAAY,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EACtD,MAAMC,OAAO,GAAGnE,UAAU,CAACqB,gBAAgB,CAAC;EAC5C,IAAI8C,OAAO,KAAK7C,SAAS,EAAE;IACzB,MAAM,IAAIoB,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EACA,OAAOyB,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAQzB,SAAS1C,iBAAiB;AAC1B,eAAeF,gBAAgB;AAAC,IAAA2C,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}