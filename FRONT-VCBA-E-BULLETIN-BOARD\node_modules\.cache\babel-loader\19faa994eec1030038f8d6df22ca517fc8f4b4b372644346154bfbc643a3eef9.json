{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v8\",\n  key: \"d4bbey\"\n}], [\"path\", {\n  d: \"M12.8 21.6A2 2 0 1 0 14 18H2\",\n  key: \"19kp1d\"\n}], [\"path\", {\n  d: \"M17.5 10a2.5 2.5 0 1 1 2 4H2\",\n  key: \"19kpjc\"\n}], [\"path\", {\n  d: \"m6 6 4 4 4-4\",\n  key: \"k13n16\"\n}]];\nconst WindArrowDown = createLucideIcon(\"wind-arrow-down\", __iconNode);\nexport { __iconNode, WindArrowDown as default };\n//# sourceMappingURL=wind-arrow-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}