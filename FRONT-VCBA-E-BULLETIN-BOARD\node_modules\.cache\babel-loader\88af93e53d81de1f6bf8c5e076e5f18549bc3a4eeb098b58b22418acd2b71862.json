{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 17 5-5-5-5\",\n  key: \"1bji2h\"\n}], [\"path\", {\n  d: \"M21 12H9\",\n  key: \"dn1m92\"\n}], [\"path\", {\n  d: \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\",\n  key: \"1uf3rs\"\n}]];\nconst LogOut = createLucideIcon(\"log-out\", __iconNode);\nexport { __iconNode, LogOut as default };\n//# sourceMappingURL=log-out.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}