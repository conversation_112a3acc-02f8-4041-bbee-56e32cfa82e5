{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}], [\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"m6.8 15-3.5 2\",\n  key: \"hjy98k\"\n}], [\"path\", {\n  d: \"m20.7 7-3.5 2\",\n  key: \"f08gto\"\n}], [\"path\", {\n  d: \"M6.8 9 3.3 7\",\n  key: \"1aevh4\"\n}], [\"path\", {\n  d: \"m20.7 17-3.5-2\",\n  key: \"1liqo3\"\n}], [\"path\", {\n  d: \"m9 22 3-8 3 8\",\n  key: \"wees03\"\n}], [\"path\", {\n  d: \"M8 22h8\",\n  key: \"rmew8v\"\n}], [\"path\", {\n  d: \"M18 18.7a9 9 0 1 0-12 0\",\n  key: \"dhzg4g\"\n}]];\nconst FerrisWheel = createLucideIcon(\"ferris-wheel\", __iconNode);\nexport { __iconNode, FerrisWheel as default };\n//# sourceMappingURL=ferris-wheel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}