{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback, useEffect } from 'react';\nimport { calendarService } from '../services/calendarService';\nexport const useCalendarImageUpload = ({\n  calendarId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [existingImages, setExistingImages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pendingDeletes, setPendingDeletes] = useState([]);\n\n  // Clear existing images when calendarId becomes null (for new events)\n  useEffect(() => {\n    if (!calendarId) {\n      // console.log('🧹 useCalendarImageUpload - Clearing existing images for new event');\n      setExistingImages([]);\n      setPendingDeletes([]);\n      setError(null);\n    }\n  }, [calendarId]);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Clear pending deletes\n  const clearPendingDeletes = useCallback(() => {\n    setPendingDeletes([]);\n  }, []);\n\n  // Clear all image-related state (for new events)\n  const clearAllImageState = useCallback(() => {\n    // console.log('🧹 useCalendarImageUpload - Clearing all image state');\n    setExistingImages([]);\n    setPendingDeletes([]);\n    setError(null);\n    setLoading(false);\n  }, []);\n\n  // Fetch existing images for the calendar event\n  const refreshImages = useCallback(async () => {\n    if (!calendarId) {\n      setExistingImages([]);\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // console.log(`📸 Fetching images for calendar event ${calendarId}`);\n      const response = await calendarService.getEventAttachments(calendarId);\n      if (response.success && response.data) {\n        const images = response.data.attachments || [];\n        // console.log(`✅ Found ${images.length} images for calendar event ${calendarId}`);\n\n        // Add file_url for display using the same approach as announcements\n        const imagesWithUrls = images.map(img => {\n          // Use the same getImageUrl function that announcements use\n          const {\n            getImageUrl\n          } = require('../config/constants');\n          const file_url = getImageUrl(img.file_path);\n          return {\n            ...img,\n            file_url\n          };\n        });\n        setExistingImages(imagesWithUrls);\n      } else {\n        throw new Error(response.message || 'Failed to fetch calendar images');\n      }\n    } catch (err) {\n      console.error('❌ Error fetching calendar images:', err);\n      const errorMessage = err.message || 'Failed to fetch calendar images';\n      setError(errorMessage);\n      // Call onError if it exists, but don't include it in dependencies to prevent infinite loop\n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId]); // Remove onError from dependencies to prevent infinite loop\n\n  // Upload new images\n  const uploadImages = useCallback(async files => {\n    if (!calendarId) {\n      throw new Error('Calendar ID is required for image upload');\n    }\n    if (!files || files.length === 0) {\n      throw new Error('No files selected for upload');\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      console.log(`📤 Uploading ${files.length} images for calendar event ${calendarId}`);\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('images', file);\n      });\n      const response = await calendarService.uploadEventAttachments(calendarId, formData);\n      if (response.success) {\n        console.log(`✅ Successfully uploaded ${files.length} images`);\n        if (onSuccess) {\n          onSuccess(`Successfully uploaded ${files.length} image(s)`);\n        }\n        await refreshImages(); // Refresh to show new images\n      } else {\n        throw new Error(response.message || 'Failed to upload images');\n      }\n    } catch (err) {\n      console.error('❌ Error uploading calendar images:', err);\n      const errorMessage = err.message || 'Failed to upload images';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId, refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Delete image\n  const deleteImage = useCallback(async attachmentId => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log(`🗑️ Deleting calendar image ${attachmentId}`);\n      const response = await calendarService.deleteEventAttachment(attachmentId);\n      if (response.success) {\n        console.log(`✅ Successfully deleted calendar image ${attachmentId}`);\n        if (onSuccess) {\n          onSuccess('Image deleted successfully');\n        }\n        await refreshImages(); // Refresh to remove deleted image\n      } else {\n        throw new Error(response.message || 'Failed to delete image');\n      }\n    } catch (err) {\n      console.error('❌ Error deleting calendar image:', err);\n      const errorMessage = err.message || 'Failed to delete image';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Set primary image\n  const setPrimaryImage = useCallback(async attachmentId => {\n    if (!calendarId) {\n      throw new Error('Calendar ID is required');\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      console.log(`⭐ Setting calendar image ${attachmentId} as primary`);\n      const response = await calendarService.setPrimaryAttachment(calendarId, attachmentId);\n      if (response.success) {\n        console.log(`✅ Successfully set calendar image ${attachmentId} as primary`);\n        if (onSuccess) {\n          onSuccess('Primary image updated successfully');\n        }\n        await refreshImages(); // Refresh to show updated primary status\n      } else {\n        throw new Error(response.message || 'Failed to set primary image');\n      }\n    } catch (err) {\n      console.error('❌ Error setting primary calendar image:', err);\n      const errorMessage = err.message || 'Failed to set primary image';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId, refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Mark image for deletion (pending)\n  const markForDeletion = useCallback(attachmentId => {\n    console.log('🏷️ Marking calendar image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      if (!prev.includes(attachmentId)) {\n        const newPending = [...prev, attachmentId];\n        console.log('📋 Pending deletes updated:', newPending);\n        return newPending;\n      }\n      return prev;\n    });\n  }, []);\n\n  // Unmark image for deletion\n  const unmarkForDeletion = useCallback(attachmentId => {\n    console.log('🔄 Unmarking calendar image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      const newPending = prev.filter(id => id !== attachmentId);\n      console.log('📋 Updated pending deletes:', newPending);\n      return newPending;\n    });\n  }, []);\n\n  // Apply pending deletions\n  const applyPendingDeletes = useCallback(async () => {\n    if (pendingDeletes.length === 0) return;\n    try {\n      console.log(`🗑️ Applying ${pendingDeletes.length} pending calendar image deletions`);\n\n      // Delete images directly without calling deleteImage to avoid dependency issues\n      for (const attachmentId of pendingDeletes) {\n        try {\n          setLoading(true);\n          console.log(`🗑️ Deleting calendar image ${attachmentId}`);\n          const response = await calendarService.deleteEventAttachment(attachmentId);\n          if (response.success) {\n            console.log(`✅ Successfully deleted calendar image ${attachmentId}`);\n          } else {\n            throw new Error(response.message || 'Failed to delete image');\n          }\n        } catch (err) {\n          console.error('❌ Error deleting calendar image:', err);\n          throw err;\n        }\n      }\n      setPendingDeletes([]);\n      // Call refreshImages directly without including it in dependencies\n      refreshImages();\n      console.log('✅ All pending calendar image deletions applied');\n    } catch (err) {\n      console.error('❌ Error applying pending calendar image deletions:', err);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [pendingDeletes]); // Remove refreshImages from dependencies to prevent infinite loop\n\n  // Fetch images when calendarId changes\n  useEffect(() => {\n    if (calendarId) {\n      refreshImages();\n    }\n  }, [calendarId]); // Remove refreshImages from dependency array to prevent infinite loop\n\n  return {\n    existingImages,\n    loading,\n    error,\n    uploadImages,\n    deleteImage,\n    setPrimaryImage,\n    refreshImages,\n    clearError,\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    clearAllImageState\n  };\n};\n_s(useCalendarImageUpload, \"vWYrJL14yKm5Yx+sOCrYTCj8Lf8=\");", "map": {"version": 3, "names": ["useState", "useCallback", "useEffect", "calendarService", "useCalendarImageUpload", "calendarId", "onSuccess", "onError", "_s", "existingImages", "setExistingImages", "loading", "setLoading", "error", "setError", "pendingDeletes", "setPendingDeletes", "clearError", "clearPendingDeletes", "clearAllImageState", "refreshImages", "response", "getEventAttachments", "success", "data", "images", "attachments", "imagesWithUrls", "map", "img", "getImageUrl", "require", "file_url", "file_path", "Error", "message", "err", "console", "errorMessage", "uploadImages", "files", "length", "log", "formData", "FormData", "for<PERSON>ach", "file", "append", "uploadEventAttachments", "deleteImage", "attachmentId", "deleteEventAttachment", "setPrimaryImage", "setPrimaryAttachment", "markForDeletion", "prev", "includes", "newPending", "unmarkForDeletion", "filter", "id", "applyPendingDeletes"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/hooks/useCalendarImageUpload.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { calendarService } from '../services/calendarService';\n\nexport interface CalendarAttachment {\n  attachment_id: number;\n  calendar_id: number;\n  file_name: string;\n  file_path: string;\n  file_url?: string;\n  file_type: 'image' | 'video' | 'document';\n  file_size: number;\n  mime_type: string;\n  display_order: number;\n  is_primary: boolean;\n  uploaded_at: string;\n  deleted_at?: string | null;\n}\n\ninterface UseCalendarImageUploadProps {\n  calendarId?: number;\n  onSuccess?: (message: string) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UseCalendarImageUploadReturn {\n  existingImages: CalendarAttachment[];\n  loading: boolean;\n  error: string | null;\n  uploadImages: (files: File[]) => Promise<void>;\n  deleteImage: (attachmentId: number) => Promise<void>;\n  setPrimaryImage: (attachmentId: number) => Promise<void>;\n  refreshImages: () => Promise<void>;\n  clearError: () => void;\n  // Pending operations for soft deletion\n  pendingDeletes: number[];\n  markForDeletion: (attachmentId: number) => void;\n  unmarkForDeletion: (attachmentId: number) => void;\n  applyPendingDeletes: () => Promise<void>;\n  clearPendingDeletes: () => void;\n  // Clear all image state\n  clearAllImageState: () => void;\n}\n\nexport const useCalendarImageUpload = ({\n  calendarId,\n  onSuccess,\n  onError\n}: UseCalendarImageUploadProps): UseCalendarImageUploadReturn => {\n  const [existingImages, setExistingImages] = useState<CalendarAttachment[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [pendingDeletes, setPendingDeletes] = useState<number[]>([]);\n\n  // Clear existing images when calendarId becomes null (for new events)\n  useEffect(() => {\n    if (!calendarId) {\n      // console.log('🧹 useCalendarImageUpload - Clearing existing images for new event');\n      setExistingImages([]);\n      setPendingDeletes([]);\n      setError(null);\n    }\n  }, [calendarId]);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Clear pending deletes\n  const clearPendingDeletes = useCallback(() => {\n    setPendingDeletes([]);\n  }, []);\n\n  // Clear all image-related state (for new events)\n  const clearAllImageState = useCallback(() => {\n    // console.log('🧹 useCalendarImageUpload - Clearing all image state');\n    setExistingImages([]);\n    setPendingDeletes([]);\n    setError(null);\n    setLoading(false);\n  }, []);\n\n  // Fetch existing images for the calendar event\n  const refreshImages = useCallback(async () => {\n    if (!calendarId) {\n      setExistingImages([]);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // console.log(`📸 Fetching images for calendar event ${calendarId}`);\n      const response = await calendarService.getEventAttachments(calendarId);\n\n      if (response.success && response.data) {\n        const images = response.data.attachments || [];\n        // console.log(`✅ Found ${images.length} images for calendar event ${calendarId}`);\n\n        // Add file_url for display using the same approach as announcements\n        const imagesWithUrls = images.map((img: CalendarAttachment) => {\n          // Use the same getImageUrl function that announcements use\n          const { getImageUrl } = require('../config/constants');\n          const file_url = getImageUrl(img.file_path);\n\n          return {\n            ...img,\n            file_url\n          };\n        });\n\n        setExistingImages(imagesWithUrls);\n      } else {\n        throw new Error(response.message || 'Failed to fetch calendar images');\n      }\n    } catch (err: any) {\n      console.error('❌ Error fetching calendar images:', err);\n      const errorMessage = err.message || 'Failed to fetch calendar images';\n      setError(errorMessage);\n      // Call onError if it exists, but don't include it in dependencies to prevent infinite loop\n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId]); // Remove onError from dependencies to prevent infinite loop\n\n  // Upload new images\n  const uploadImages = useCallback(async (files: File[]) => {\n    if (!calendarId) {\n      throw new Error('Calendar ID is required for image upload');\n    }\n\n    if (!files || files.length === 0) {\n      throw new Error('No files selected for upload');\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log(`📤 Uploading ${files.length} images for calendar event ${calendarId}`);\n\n      const formData = new FormData();\n      files.forEach((file) => {\n        formData.append('images', file);\n      });\n\n      const response = await calendarService.uploadEventAttachments(calendarId, formData);\n\n      if (response.success) {\n        console.log(`✅ Successfully uploaded ${files.length} images`);\n        if (onSuccess) {\n          onSuccess(`Successfully uploaded ${files.length} image(s)`);\n        }\n        await refreshImages(); // Refresh to show new images\n      } else {\n        throw new Error(response.message || 'Failed to upload images');\n      }\n    } catch (err: any) {\n      console.error('❌ Error uploading calendar images:', err);\n      const errorMessage = err.message || 'Failed to upload images';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId, refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Delete image\n  const deleteImage = useCallback(async (attachmentId: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log(`🗑️ Deleting calendar image ${attachmentId}`);\n      const response = await calendarService.deleteEventAttachment(attachmentId);\n\n      if (response.success) {\n        console.log(`✅ Successfully deleted calendar image ${attachmentId}`);\n        if (onSuccess) {\n          onSuccess('Image deleted successfully');\n        }\n        await refreshImages(); // Refresh to remove deleted image\n      } else {\n        throw new Error(response.message || 'Failed to delete image');\n      }\n    } catch (err: any) {\n      console.error('❌ Error deleting calendar image:', err);\n      const errorMessage = err.message || 'Failed to delete image';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Set primary image\n  const setPrimaryImage = useCallback(async (attachmentId: number) => {\n    if (!calendarId) {\n      throw new Error('Calendar ID is required');\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log(`⭐ Setting calendar image ${attachmentId} as primary`);\n      const response = await calendarService.setPrimaryAttachment(calendarId, attachmentId);\n\n      if (response.success) {\n        console.log(`✅ Successfully set calendar image ${attachmentId} as primary`);\n        if (onSuccess) {\n          onSuccess('Primary image updated successfully');\n        }\n        await refreshImages(); // Refresh to show updated primary status\n      } else {\n        throw new Error(response.message || 'Failed to set primary image');\n      }\n    } catch (err: any) {\n      console.error('❌ Error setting primary calendar image:', err);\n      const errorMessage = err.message || 'Failed to set primary image';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [calendarId, refreshImages]); // Remove onSuccess and onError from dependencies\n\n  // Mark image for deletion (pending)\n  const markForDeletion = useCallback((attachmentId: number) => {\n    console.log('🏷️ Marking calendar image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      if (!prev.includes(attachmentId)) {\n        const newPending = [...prev, attachmentId];\n        console.log('📋 Pending deletes updated:', newPending);\n        return newPending;\n      }\n      return prev;\n    });\n  }, []);\n\n  // Unmark image for deletion\n  const unmarkForDeletion = useCallback((attachmentId: number) => {\n    console.log('🔄 Unmarking calendar image for deletion:', attachmentId);\n    setPendingDeletes(prev => {\n      const newPending = prev.filter(id => id !== attachmentId);\n      console.log('📋 Updated pending deletes:', newPending);\n      return newPending;\n    });\n  }, []);\n\n  // Apply pending deletions\n  const applyPendingDeletes = useCallback(async () => {\n    if (pendingDeletes.length === 0) return;\n\n    try {\n      console.log(`🗑️ Applying ${pendingDeletes.length} pending calendar image deletions`);\n\n      // Delete images directly without calling deleteImage to avoid dependency issues\n      for (const attachmentId of pendingDeletes) {\n        try {\n          setLoading(true);\n          console.log(`🗑️ Deleting calendar image ${attachmentId}`);\n          const response = await calendarService.deleteEventAttachment(attachmentId);\n\n          if (response.success) {\n            console.log(`✅ Successfully deleted calendar image ${attachmentId}`);\n          } else {\n            throw new Error(response.message || 'Failed to delete image');\n          }\n        } catch (err: any) {\n          console.error('❌ Error deleting calendar image:', err);\n          throw err;\n        }\n      }\n\n      setPendingDeletes([]);\n      // Call refreshImages directly without including it in dependencies\n      refreshImages();\n      console.log('✅ All pending calendar image deletions applied');\n    } catch (err: any) {\n      console.error('❌ Error applying pending calendar image deletions:', err);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [pendingDeletes]); // Remove refreshImages from dependencies to prevent infinite loop\n\n  // Fetch images when calendarId changes\n  useEffect(() => {\n    if (calendarId) {\n      refreshImages();\n    }\n  }, [calendarId]); // Remove refreshImages from dependency array to prevent infinite loop\n\n  return {\n    existingImages,\n    loading,\n    error,\n    uploadImages,\n    deleteImage,\n    setPrimaryImage,\n    refreshImages,\n    clearError,\n    pendingDeletes,\n    markForDeletion,\n    unmarkForDeletion,\n    applyPendingDeletes,\n    clearPendingDeletes,\n    clearAllImageState\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,eAAe,QAAQ,6BAA6B;AA0C7D,OAAO,MAAMC,sBAAsB,GAAGA,CAAC;EACrCC,UAAU;EACVC,SAAS;EACTC;AAC2B,CAAC,KAAmC;EAAAC,EAAA;EAC/D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAuB,EAAE,CAAC;EAC9E,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;;EAElE;EACAE,SAAS,CAAC,MAAM;IACd,IAAI,CAACG,UAAU,EAAE;MACf;MACAK,iBAAiB,CAAC,EAAE,CAAC;MACrBM,iBAAiB,CAAC,EAAE,CAAC;MACrBF,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,CAACT,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMY,UAAU,GAAGhB,WAAW,CAAC,MAAM;IACnCa,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,mBAAmB,GAAGjB,WAAW,CAAC,MAAM;IAC5Ce,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,kBAAkB,GAAGlB,WAAW,CAAC,MAAM;IAC3C;IACAS,iBAAiB,CAAC,EAAE,CAAC;IACrBM,iBAAiB,CAAC,EAAE,CAAC;IACrBF,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,aAAa,GAAGnB,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACI,UAAU,EAAE;MACfK,iBAAiB,CAAC,EAAE,CAAC;MACrB;IACF;IAEA,IAAI;MACFE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMO,QAAQ,GAAG,MAAMlB,eAAe,CAACmB,mBAAmB,CAACjB,UAAU,CAAC;MAEtE,IAAIgB,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC,MAAMC,MAAM,GAAGJ,QAAQ,CAACG,IAAI,CAACE,WAAW,IAAI,EAAE;QAC9C;;QAEA;QACA,MAAMC,cAAc,GAAGF,MAAM,CAACG,GAAG,CAAEC,GAAuB,IAAK;UAC7D;UACA,MAAM;YAAEC;UAAY,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;UACtD,MAAMC,QAAQ,GAAGF,WAAW,CAACD,GAAG,CAACI,SAAS,CAAC;UAE3C,OAAO;YACL,GAAGJ,GAAG;YACNG;UACF,CAAC;QACH,CAAC,CAAC;QAEFtB,iBAAiB,CAACiB,cAAc,CAAC;MACnC,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAACb,QAAQ,CAACc,OAAO,IAAI,iCAAiC,CAAC;MACxE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACxB,KAAK,CAAC,mCAAmC,EAAEuB,GAAG,CAAC;MACvD,MAAME,YAAY,GAAGF,GAAG,CAACD,OAAO,IAAI,iCAAiC;MACrErB,QAAQ,CAACwB,YAAY,CAAC;MACtB;MACA,IAAI/B,OAAO,EAAE;QACXA,OAAO,CAAC+B,YAAY,CAAC;MACvB;IACF,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA,MAAMkC,YAAY,GAAGtC,WAAW,CAAC,MAAOuC,KAAa,IAAK;IACxD,IAAI,CAACnC,UAAU,EAAE;MACf,MAAM,IAAI6B,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIP,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEduB,OAAO,CAACK,GAAG,CAAC,gBAAgBF,KAAK,CAACC,MAAM,8BAA8BpC,UAAU,EAAE,CAAC;MAEnF,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BJ,KAAK,CAACK,OAAO,CAAEC,IAAI,IAAK;QACtBH,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;MACjC,CAAC,CAAC;MAEF,MAAMzB,QAAQ,GAAG,MAAMlB,eAAe,CAAC6C,sBAAsB,CAAC3C,UAAU,EAAEsC,QAAQ,CAAC;MAEnF,IAAItB,QAAQ,CAACE,OAAO,EAAE;QACpBc,OAAO,CAACK,GAAG,CAAC,2BAA2BF,KAAK,CAACC,MAAM,SAAS,CAAC;QAC7D,IAAInC,SAAS,EAAE;UACbA,SAAS,CAAC,yBAAyBkC,KAAK,CAACC,MAAM,WAAW,CAAC;QAC7D;QACA,MAAMrB,aAAa,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAACb,QAAQ,CAACc,OAAO,IAAI,yBAAyB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACxB,KAAK,CAAC,oCAAoC,EAAEuB,GAAG,CAAC;MACxD,MAAME,YAAY,GAAGF,GAAG,CAACD,OAAO,IAAI,yBAAyB;MAC7DrB,QAAQ,CAACwB,YAAY,CAAC;MACtB,IAAI/B,OAAO,EAAE;QACXA,OAAO,CAAC+B,YAAY,CAAC;MACvB;MACA,MAAMF,GAAG;IACX,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,UAAU,EAAEe,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAM6B,WAAW,GAAGhD,WAAW,CAAC,MAAOiD,YAAoB,IAAK;IAC9D,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEduB,OAAO,CAACK,GAAG,CAAC,+BAA+BQ,YAAY,EAAE,CAAC;MAC1D,MAAM7B,QAAQ,GAAG,MAAMlB,eAAe,CAACgD,qBAAqB,CAACD,YAAY,CAAC;MAE1E,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QACpBc,OAAO,CAACK,GAAG,CAAC,yCAAyCQ,YAAY,EAAE,CAAC;QACpE,IAAI5C,SAAS,EAAE;UACbA,SAAS,CAAC,4BAA4B,CAAC;QACzC;QACA,MAAMc,aAAa,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAACb,QAAQ,CAACc,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACxB,KAAK,CAAC,kCAAkC,EAAEuB,GAAG,CAAC;MACtD,MAAME,YAAY,GAAGF,GAAG,CAACD,OAAO,IAAI,wBAAwB;MAC5DrB,QAAQ,CAACwB,YAAY,CAAC;MACtB,IAAI/B,OAAO,EAAE;QACXA,OAAO,CAAC+B,YAAY,CAAC;MACvB;MACA,MAAMF,GAAG;IACX,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACQ,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB;EACA,MAAMgC,eAAe,GAAGnD,WAAW,CAAC,MAAOiD,YAAoB,IAAK;IAClE,IAAI,CAAC7C,UAAU,EAAE;MACf,MAAM,IAAI6B,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEduB,OAAO,CAACK,GAAG,CAAC,4BAA4BQ,YAAY,aAAa,CAAC;MAClE,MAAM7B,QAAQ,GAAG,MAAMlB,eAAe,CAACkD,oBAAoB,CAAChD,UAAU,EAAE6C,YAAY,CAAC;MAErF,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QACpBc,OAAO,CAACK,GAAG,CAAC,qCAAqCQ,YAAY,aAAa,CAAC;QAC3E,IAAI5C,SAAS,EAAE;UACbA,SAAS,CAAC,oCAAoC,CAAC;QACjD;QACA,MAAMc,aAAa,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAACb,QAAQ,CAACc,OAAO,IAAI,6BAA6B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACxB,KAAK,CAAC,yCAAyC,EAAEuB,GAAG,CAAC;MAC7D,MAAME,YAAY,GAAGF,GAAG,CAACD,OAAO,IAAI,6BAA6B;MACjErB,QAAQ,CAACwB,YAAY,CAAC;MACtB,IAAI/B,OAAO,EAAE;QACXA,OAAO,CAAC+B,YAAY,CAAC;MACvB;MACA,MAAMF,GAAG;IACX,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,UAAU,EAAEe,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAMkC,eAAe,GAAGrD,WAAW,CAAEiD,YAAoB,IAAK;IAC5Db,OAAO,CAACK,GAAG,CAAC,0CAA0C,EAAEQ,YAAY,CAAC;IACrElC,iBAAiB,CAACuC,IAAI,IAAI;MACxB,IAAI,CAACA,IAAI,CAACC,QAAQ,CAACN,YAAY,CAAC,EAAE;QAChC,MAAMO,UAAU,GAAG,CAAC,GAAGF,IAAI,EAAEL,YAAY,CAAC;QAC1Cb,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAEe,UAAU,CAAC;QACtD,OAAOA,UAAU;MACnB;MACA,OAAOF,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,iBAAiB,GAAGzD,WAAW,CAAEiD,YAAoB,IAAK;IAC9Db,OAAO,CAACK,GAAG,CAAC,2CAA2C,EAAEQ,YAAY,CAAC;IACtElC,iBAAiB,CAACuC,IAAI,IAAI;MACxB,MAAME,UAAU,GAAGF,IAAI,CAACI,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKV,YAAY,CAAC;MACzDb,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAEe,UAAU,CAAC;MACtD,OAAOA,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,mBAAmB,GAAG5D,WAAW,CAAC,YAAY;IAClD,IAAIc,cAAc,CAAC0B,MAAM,KAAK,CAAC,EAAE;IAEjC,IAAI;MACFJ,OAAO,CAACK,GAAG,CAAC,gBAAgB3B,cAAc,CAAC0B,MAAM,mCAAmC,CAAC;;MAErF;MACA,KAAK,MAAMS,YAAY,IAAInC,cAAc,EAAE;QACzC,IAAI;UACFH,UAAU,CAAC,IAAI,CAAC;UAChByB,OAAO,CAACK,GAAG,CAAC,+BAA+BQ,YAAY,EAAE,CAAC;UAC1D,MAAM7B,QAAQ,GAAG,MAAMlB,eAAe,CAACgD,qBAAqB,CAACD,YAAY,CAAC;UAE1E,IAAI7B,QAAQ,CAACE,OAAO,EAAE;YACpBc,OAAO,CAACK,GAAG,CAAC,yCAAyCQ,YAAY,EAAE,CAAC;UACtE,CAAC,MAAM;YACL,MAAM,IAAIhB,KAAK,CAACb,QAAQ,CAACc,OAAO,IAAI,wBAAwB,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;UACjBC,OAAO,CAACxB,KAAK,CAAC,kCAAkC,EAAEuB,GAAG,CAAC;UACtD,MAAMA,GAAG;QACX;MACF;MAEApB,iBAAiB,CAAC,EAAE,CAAC;MACrB;MACAI,aAAa,CAAC,CAAC;MACfiB,OAAO,CAACK,GAAG,CAAC,gDAAgD,CAAC;IAC/D,CAAC,CAAC,OAAON,GAAQ,EAAE;MACjBC,OAAO,CAACxB,KAAK,CAAC,oDAAoD,EAAEuB,GAAG,CAAC;MACxE,MAAMA,GAAG;IACX,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACG,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEtB;EACAb,SAAS,CAAC,MAAM;IACd,IAAIG,UAAU,EAAE;MACde,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACf,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB,OAAO;IACLI,cAAc;IACdE,OAAO;IACPE,KAAK;IACL0B,YAAY;IACZU,WAAW;IACXG,eAAe;IACfhC,aAAa;IACbH,UAAU;IACVF,cAAc;IACduC,eAAe;IACfI,iBAAiB;IACjBG,mBAAmB;IACnB3C,mBAAmB;IACnBC;EACF,CAAC;AACH,CAAC;AAACX,EAAA,CAxRWJ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}