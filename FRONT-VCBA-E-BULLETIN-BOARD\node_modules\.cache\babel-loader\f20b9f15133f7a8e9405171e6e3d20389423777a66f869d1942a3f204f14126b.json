{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z\",\n  key: \"oel41y\"\n}], [\"path\", {\n  d: \"m14.5 9.5-5 5\",\n  key: \"17q4r4\"\n}], [\"path\", {\n  d: \"m9.5 9.5 5 5\",\n  key: \"18nt4w\"\n}]];\nconst ShieldX = createLucideIcon(\"shield-x\", __iconNode);\nexport { __iconNode, ShieldX as default };\n//# sourceMappingURL=shield-x.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}