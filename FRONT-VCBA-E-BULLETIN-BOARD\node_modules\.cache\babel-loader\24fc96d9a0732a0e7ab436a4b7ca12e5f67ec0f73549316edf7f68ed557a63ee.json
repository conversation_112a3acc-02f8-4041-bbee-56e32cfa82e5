{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 6-6 6 6 6\",\n  key: \"7v63n9\"\n}], [\"path\", {\n  d: \"M3 12h14\",\n  key: \"13k4hi\"\n}], [\"path\", {\n  d: \"M21 19V5\",\n  key: \"b4bplr\"\n}]];\nconst ArrowLeftFromLine = createLucideIcon(\"arrow-left-from-line\", __iconNode);\nexport { __iconNode, ArrowLeftFromLine as default };\n//# sourceMappingURL=arrow-left-from-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}