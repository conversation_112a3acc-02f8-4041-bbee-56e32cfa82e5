{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n  key: \"wmoenq\"\n}], [\"path\", {\n  d: \"M12 9v4\",\n  key: \"juzpu7\"\n}], [\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}]];\nconst TriangleAlert = createLucideIcon(\"triangle-alert\", __iconNode);\nexport { __iconNode, TriangleAlert as default };\n//# sourceMappingURL=triangle-alert.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}