{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\",\n  key: \"j76jl0\"\n}], [\"path\", {\n  d: \"M22 10v6\",\n  key: \"1lu8f3\"\n}], [\"path\", {\n  d: \"M6 12.5V16a6 3 0 0 0 12 0v-3.5\",\n  key: \"1r8lef\"\n}]];\nconst GraduationCap = createLucideIcon(\"graduation-cap\", __iconNode);\nexport { __iconNode, GraduationCap as default };\n//# sourceMappingURL=graduation-cap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}