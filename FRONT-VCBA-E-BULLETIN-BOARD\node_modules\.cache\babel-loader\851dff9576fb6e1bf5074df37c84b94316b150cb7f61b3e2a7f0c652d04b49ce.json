{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"7\",\n  height: \"13\",\n  x: \"3\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"1fjrkv\"\n}], [\"path\", {\n  d: \"m15 2-3 3-3-3\",\n  key: \"1uh6eb\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"13\",\n  x: \"14\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"w3fjg8\"\n}]];\nconst BetweenVerticalStart = createLucideIcon(\"between-vertical-start\", __iconNode);\nexport { __iconNode, BetweenVerticalStart as default };\n//# sourceMappingURL=between-vertical-start.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}