export = ReactRefreshLoader;
/**
 * A simple Webpack loader to inject react-refresh HMR code into modules.
 *
 * [Reference for Loader API](https://webpack.js.org/api/loaders/)
 * @this {import('webpack').LoaderContext<import('./types').ReactRefreshLoaderOptions>}
 * @param {string} source The original module source code.
 * @param {import('source-map').RawSourceMap} [inputSourceMap] The source map of the module.
 * @param {*} [meta] The loader metadata passed in.
 * @returns {void}
 */
declare function ReactRefreshLoader(
  this: import('webpack').LoaderContext<import('./types').ReactRefreshLoaderOptions>,
  source: string,
  inputSourceMap?: import('source-map').RawSourceMap | undefined,
  meta?: any
): void;
