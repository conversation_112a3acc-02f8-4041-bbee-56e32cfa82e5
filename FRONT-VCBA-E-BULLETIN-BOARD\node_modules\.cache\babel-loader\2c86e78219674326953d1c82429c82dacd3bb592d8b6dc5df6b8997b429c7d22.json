{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 18H3\",\n  key: \"n3j2dh\"\n}], [\"path\", {\n  d: \"m15 18 2 2 4-4\",\n  key: \"1szwhi\"\n}], [\"path\", {\n  d: \"M16 12H3\",\n  key: \"1a2rj7\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}]];\nconst ListCheck = createLucideIcon(\"list-check\", __iconNode);\nexport { __iconNode, ListCheck as default };\n//# sourceMappingURL=list-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}