{"ast": null, "code": "import { StudentAuthService } from './student-auth.service';\nexport class StudentProfileService {\n  /**\n   * Get student profile\n   */\n  static async getProfile() {\n    return StudentAuthService.get('/api/student/profile');\n  }\n\n  /**\n   * Update student profile\n   */\n  static async updateProfile(data) {\n    return StudentAuthService.put('/api/student/profile', data);\n  }\n\n  /**\n   * Upload student profile picture\n   */\n  static async uploadProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return StudentAuthService.post('/api/student/profile/picture', formData);\n  }\n\n  /**\n   * Update student profile picture\n   */\n  static async updateProfilePicture(file) {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    return StudentAuthService.put('/api/student/profile/picture', formData);\n  }\n\n  /**\n   * Delete student profile picture\n   */\n  static async deleteProfilePicture() {\n    return StudentAuthService.delete('/api/student/profile/picture');\n  }\n}\nexport default StudentProfileService;", "map": {"version": 3, "names": ["StudentAuthService", "StudentProfileService", "getProfile", "get", "updateProfile", "data", "put", "uploadProfilePicture", "file", "formData", "FormData", "append", "post", "updateProfilePicture", "deleteProfilePicture", "delete"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/studentProfileService.ts"], "sourcesContent": ["import { StudentAuthService } from './student-auth.service';\nimport { ApiResponse } from '../types';\n\nexport interface StudentProfile {\n  student_id: number;\n  email: string;\n  student_number: string;\n  first_name: string;\n  middle_name?: string;\n  last_name: string;\n  suffix?: string;\n  phone_number?: string;\n  grade_level: number;\n  section: string;\n  parent_guardian_name?: string;\n  parent_guardian_phone?: string;\n  address?: string;\n  profile_picture?: string;\n  profile_picture_url?: string;\n  is_active: boolean;\n  last_login?: string;\n  created_by: number;\n  account_created_at: string;\n  account_updated_at: string;\n  profile_created_at: string;\n  profile_updated_at: string;\n}\n\nexport interface UpdateStudentProfileData {\n  first_name?: string;\n  middle_name?: string;\n  last_name?: string;\n  suffix?: string;\n  phone_number?: string;\n  grade_level?: number;\n  section?: string;\n  parent_guardian_name?: string;\n  parent_guardian_phone?: string;\n  address?: string;\n  date_of_birth?: string;\n  gender?: 'male' | 'female' | 'other';\n  city?: string;\n  state?: string;\n  postal_code?: string;\n  country?: string;\n  emergency_contact_name?: string;\n  emergency_contact_phone?: string;\n  emergency_contact_relationship?: string;\n  bio?: string;\n}\n\nexport class StudentProfileService {\n  /**\n   * Get student profile\n   */\n  static async getProfile(): Promise<ApiResponse<{ student: StudentProfile }>> {\n    return StudentAuthService.get<{ student: StudentProfile }>('/api/student/profile');\n  }\n\n  /**\n   * Update student profile\n   */\n  static async updateProfile(data: UpdateStudentProfileData): Promise<ApiResponse<{ student: StudentProfile }>> {\n    return StudentAuthService.put<{ student: StudentProfile }>('/api/student/profile', data);\n  }\n\n  /**\n   * Upload student profile picture\n   */\n  static async uploadProfilePicture(file: File): Promise<ApiResponse<{ student: StudentProfile }>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    \n    return StudentAuthService.post<{ student: StudentProfile }>('/api/student/profile/picture', formData);\n  }\n\n  /**\n   * Update student profile picture\n   */\n  static async updateProfilePicture(file: File): Promise<ApiResponse<{ student: StudentProfile }>> {\n    const formData = new FormData();\n    formData.append('profilePicture', file);\n    \n    return StudentAuthService.put<{ student: StudentProfile }>('/api/student/profile/picture', formData);\n  }\n\n  /**\n   * Delete student profile picture\n   */\n  static async deleteProfilePicture(): Promise<ApiResponse<{ student: StudentProfile }>> {\n    return StudentAuthService.delete<{ student: StudentProfile }>('/api/student/profile/picture');\n  }\n}\n\nexport default StudentProfileService;\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,wBAAwB;AAmD3D,OAAO,MAAMC,qBAAqB,CAAC;EACjC;AACF;AACA;EACE,aAAaC,UAAUA,CAAA,EAAsD;IAC3E,OAAOF,kBAAkB,CAACG,GAAG,CAA8B,sBAAsB,CAAC;EACpF;;EAEA;AACF;AACA;EACE,aAAaC,aAAaA,CAACC,IAA8B,EAAqD;IAC5G,OAAOL,kBAAkB,CAACM,GAAG,CAA8B,sBAAsB,EAAED,IAAI,CAAC;EAC1F;;EAEA;AACF;AACA;EACE,aAAaE,oBAAoBA,CAACC,IAAU,EAAqD;IAC/F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOR,kBAAkB,CAACY,IAAI,CAA8B,8BAA8B,EAAEH,QAAQ,CAAC;EACvG;;EAEA;AACF;AACA;EACE,aAAaI,oBAAoBA,CAACL,IAAU,EAAqD;IAC/F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,IAAI,CAAC;IAEvC,OAAOR,kBAAkB,CAACM,GAAG,CAA8B,8BAA8B,EAAEG,QAAQ,CAAC;EACtG;;EAEA;AACF;AACA;EACE,aAAaK,oBAAoBA,CAAA,EAAsD;IACrF,OAAOd,kBAAkB,CAACe,MAAM,CAA8B,8BAA8B,CAAC;EAC/F;AACF;AAEA,eAAed,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}