{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\",\n  key: \"1m0v6g\"\n}], [\"path\", {\n  d: \"M14 15H9v-5\",\n  key: \"pi4jk9\"\n}], [\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M21 3 9 15\",\n  key: \"15kdhq\"\n}]];\nconst Scaling = createLucideIcon(\"scaling\", __iconNode);\nexport { __iconNode, Scaling as default };\n//# sourceMappingURL=scaling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}