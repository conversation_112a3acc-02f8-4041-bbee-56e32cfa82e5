{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 10-6.157 6.162a2 2 0 0 0-.5.833l-1.322 4.36a.5.5 0 0 0 .622.624l4.358-1.323a2 2 0 0 0 .83-.5L14 13.982\",\n  key: \"bjo8r8\"\n}], [\"path\", {\n  d: \"m12.829 7.172 4.359-4.346a1 1 0 1 1 3.986 3.986l-4.353 4.353\",\n  key: \"16h5ne\"\n}], [\"path\", {\n  d: \"m15 5 4 4\",\n  key: \"1mk7zo\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst PencilOff = createLucideIcon(\"pencil-off\", __iconNode);\nexport { __iconNode, PencilOff as default };\n//# sourceMappingURL=pencil-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}