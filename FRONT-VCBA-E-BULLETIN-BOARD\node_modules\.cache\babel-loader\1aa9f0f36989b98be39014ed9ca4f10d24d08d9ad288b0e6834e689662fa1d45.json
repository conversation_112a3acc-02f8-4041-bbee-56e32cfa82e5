{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"5\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1qnov2\"\n}], [\"path\", {\n  d: \"M5 9v6\",\n  key: \"158jrl\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"104gr9\"\n}], [\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"108a5v\"\n}], [\"path\", {\n  d: \"M16 15.7A9 9 0 0 0 19 9\",\n  key: \"1e3vqb\"\n}]];\nconst GitGraph = createLucideIcon(\"git-graph\", __iconNode);\nexport { __iconNode, GitGraph as default };\n//# sourceMappingURL=git-graph.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}