{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m7 6 5 5 5-5\",\n  key: \"1lc07p\"\n}], [\"path\", {\n  d: \"m7 13 5 5 5-5\",\n  key: \"1d48rs\"\n}]];\nconst ChevronsDown = createLucideIcon(\"chevrons-down\", __iconNode);\nexport { __iconNode, ChevronsDown as default };\n//# sourceMappingURL=chevrons-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}