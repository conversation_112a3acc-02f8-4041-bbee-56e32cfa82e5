{"ast": null, "code": "import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\nclass CalendarReactionService {\n  // No need to store client in constructor - get it dynamically each time\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n    console.log('🔍 CalendarReactionService - Auth detection:', {\n      hasStudentToken: !!studentToken,\n      hasStudentUser: !!studentUser,\n      hasAdminToken: !!adminToken,\n      hasAdminUser: !!adminUser,\n      studentTokenPrefix: studentToken ? studentToken.substring(0, 10) + '...' : null,\n      adminTokenPrefix: adminToken ? adminToken.substring(0, 10) + '...' : null\n    });\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId) {\n    try {\n      const client = this.getAuthenticatedClient();\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: client === adminHttpClient ? 'ADMIN' : client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n      const response = await client.post(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          added: false\n        }\n      };\n    } catch (error) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event (following announcement pattern)\n  async unlikeEvent(eventId) {\n    try {\n      console.log('💔 CalendarReactionService - Unliking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n      const response = await this.client.delete(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || {\n          removed: false\n        }\n      };\n    } catch (error) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId, currentlyLiked) {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;", "map": {"version": 3, "names": ["httpClient", "adminHttpClient", "studentHttpClient", "CalendarReactionService", "getAuthenticatedClient", "studentToken", "localStorage", "getItem", "studentUser", "adminToken", "adminUser", "console", "log", "hasStudentToken", "hasStudentUser", "hasAdminToken", "hasAdminUser", "studentTokenPrefix", "substring", "adminTokenPrefix", "likeEvent", "eventId", "client", "clientType", "response", "post", "success", "message", "data", "added", "error", "Error", "unlikeEvent", "delete", "removed", "toggleLike", "currentlyLiked", "calendarReactionService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/calendarReactionService.ts"], "sourcesContent": ["import { httpClient, adminHttpClient, studentHttpClient } from './api.service';\n\nexport interface CalendarReactionData {\n  added?: boolean;\n  removed?: boolean;\n}\n\nexport interface CalendarReactionResponse {\n  success: boolean;\n  message: string;\n  data: CalendarReactionData;\n}\n\nclass CalendarReactionService {\n  // No need to store client in constructor - get it dynamically each time\n\n  /**\n   * Get the appropriate HTTP client based on current authentication\n   * Following the same pattern as AnnouncementService\n   */\n  private getAuthenticatedClient() {\n    const studentToken = localStorage.getItem('studentToken');\n    const studentUser = localStorage.getItem('studentUser');\n    const adminToken = localStorage.getItem('adminToken');\n    const adminUser = localStorage.getItem('adminUser');\n\n    console.log('🔍 CalendarReactionService - Auth detection:', {\n      hasStudentToken: !!studentToken,\n      hasStudentUser: !!studentUser,\n      hasAdminToken: !!adminToken,\n      hasAdminUser: !!adminUser,\n      studentTokenPrefix: studentToken ? studentToken.substring(0, 10) + '...' : null,\n      adminTokenPrefix: adminToken ? adminToken.substring(0, 10) + '...' : null\n    });\n\n    // Prefer student authentication if available\n    if (studentToken && studentUser) {\n      console.log('🎓 CalendarReactionService - Using STUDENT authentication');\n      return studentHttpClient;\n    } else if (adminToken && adminUser) {\n      console.log('👨‍💼 CalendarReactionService - Using ADMIN authentication');\n      return adminHttpClient;\n    } else {\n      console.log('🔧 CalendarReactionService - Using DEFAULT authentication');\n      return httpClient;\n    }\n  }\n\n  // Like a calendar event (following announcement pattern)\n  async likeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      const client = this.getAuthenticatedClient();\n\n      console.log('❤️ CalendarReactionService - Liking calendar event:', {\n        eventId,\n        clientType: client === adminHttpClient ? 'ADMIN' : client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n\n      const response = await client.post(`/api/calendar/${eventId}/like`, {});\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { added: false }\n      };\n    } catch (error: any) {\n      console.error('Error liking calendar event:', error);\n      throw new Error(error.message || 'Failed to like calendar event');\n    }\n  }\n\n  // Unlike a calendar event (following announcement pattern)\n  async unlikeEvent(eventId: number): Promise<CalendarReactionResponse> {\n    try {\n      console.log('💔 CalendarReactionService - Unliking calendar event:', {\n        eventId,\n        clientType: this.client === adminHttpClient ? 'ADMIN' : this.client === studentHttpClient ? 'STUDENT' : 'DEFAULT'\n      });\n\n      const response = await this.client.delete(`/api/calendar/${eventId}/like`);\n      return {\n        success: response.success,\n        message: response.message,\n        data: response.data || { removed: false }\n      };\n    } catch (error: any) {\n      console.error('Error unliking calendar event:', error);\n      throw new Error(error.message || 'Failed to unlike calendar event');\n    }\n  }\n\n  // Toggle like/unlike for a calendar event\n  async toggleLike(eventId: number, currentlyLiked: boolean): Promise<CalendarReactionResponse> {\n    if (currentlyLiked) {\n      return this.unlikeEvent(eventId);\n    } else {\n      return this.likeEvent(eventId);\n    }\n  }\n}\n\nexport const calendarReactionService = new CalendarReactionService();\nexport default calendarReactionService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,eAAe;AAa9E,MAAMC,uBAAuB,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;EACUC,sBAAsBA,CAAA,EAAG;IAC/B,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAEnDI,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DC,eAAe,EAAE,CAAC,CAACR,YAAY;MAC/BS,cAAc,EAAE,CAAC,CAACN,WAAW;MAC7BO,aAAa,EAAE,CAAC,CAACN,UAAU;MAC3BO,YAAY,EAAE,CAAC,CAACN,SAAS;MACzBO,kBAAkB,EAAEZ,YAAY,GAAGA,YAAY,CAACa,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI;MAC/EC,gBAAgB,EAAEV,UAAU,GAAGA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;IACvE,CAAC,CAAC;;IAEF;IACA,IAAIb,YAAY,IAAIG,WAAW,EAAE;MAC/BG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOV,iBAAiB;IAC1B,CAAC,MAAM,IAAIO,UAAU,IAAIC,SAAS,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,OAAOX,eAAe;IACxB,CAAC,MAAM;MACLU,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE,OAAOZ,UAAU;IACnB;EACF;;EAEA;EACA,MAAMoB,SAASA,CAACC,OAAe,EAAqC;IAClE,IAAI;MACF,MAAMC,MAAM,GAAG,IAAI,CAAClB,sBAAsB,CAAC,CAAC;MAE5CO,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QACjES,OAAO;QACPE,UAAU,EAAED,MAAM,KAAKrB,eAAe,GAAG,OAAO,GAAGqB,MAAM,KAAKpB,iBAAiB,GAAG,SAAS,GAAG;MAChG,CAAC,CAAC;MAEF,MAAMsB,QAAQ,GAAG,MAAMF,MAAM,CAACG,IAAI,CAAC,iBAAiBJ,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;MACvE,OAAO;QACLK,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEC,KAAK,EAAE;QAAM;MACxC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBnB,OAAO,CAACmB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,+BAA+B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMK,WAAWA,CAACX,OAAe,EAAqC;IACpE,IAAI;MACFV,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;QACnES,OAAO;QACPE,UAAU,EAAE,IAAI,CAACD,MAAM,KAAKrB,eAAe,GAAG,OAAO,GAAG,IAAI,CAACqB,MAAM,KAAKpB,iBAAiB,GAAG,SAAS,GAAG;MAC1G,CAAC,CAAC;MAEF,MAAMsB,QAAQ,GAAG,MAAM,IAAI,CAACF,MAAM,CAACW,MAAM,CAAC,iBAAiBZ,OAAO,OAAO,CAAC;MAC1E,OAAO;QACLK,OAAO,EAAEF,QAAQ,CAACE,OAAO;QACzBC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;UAAEM,OAAO,EAAE;QAAM;MAC1C,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBnB,OAAO,CAACmB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACH,OAAO,IAAI,iCAAiC,CAAC;IACrE;EACF;;EAEA;EACA,MAAMQ,UAAUA,CAACd,OAAe,EAAEe,cAAuB,EAAqC;IAC5F,IAAIA,cAAc,EAAE;MAClB,OAAO,IAAI,CAACJ,WAAW,CAACX,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC;IAChC;EACF;AACF;AAEA,OAAO,MAAMgB,uBAAuB,GAAG,IAAIlC,uBAAuB,CAAC,CAAC;AACpE,eAAekC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}