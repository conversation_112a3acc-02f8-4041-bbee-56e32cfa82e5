{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M16 12h.01\",\n  key: \"1l6xoz\"\n}], [\"path\", {\n  d: \"m17 7 5 5-5 5\",\n  key: \"1xlxn0\"\n}], [\"path\", {\n  d: \"m7 7-5 5 5 5\",\n  key: \"19njba\"\n}], [\"path\", {\n  d: \"M8 12h.01\",\n  key: \"czm47f\"\n}]];\nconst ChevronsLeftRightEllipsis = createLucideIcon(\"chevrons-left-right-ellipsis\", __iconNode);\nexport { __iconNode, ChevronsLeftRightEllipsis as default };\n//# sourceMappingURL=chevrons-left-right-ellipsis.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}