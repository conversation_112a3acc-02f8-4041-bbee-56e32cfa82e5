{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20 6 9 17l-5-5\",\n  key: \"1gmf2c\"\n}]];\nconst Check = createLucideIcon(\"check\", __iconNode);\nexport { __iconNode, Check as default };\n//# sourceMappingURL=check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}