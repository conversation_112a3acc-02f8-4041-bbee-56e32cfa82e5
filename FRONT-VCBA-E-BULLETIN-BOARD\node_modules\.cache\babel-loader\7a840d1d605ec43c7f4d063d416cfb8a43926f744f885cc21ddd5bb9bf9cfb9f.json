{"ast": null, "code": "import _objectSpread from\"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useCallback,useEffect}from'react';import{adminAnnouncementService}from'../services/announcementService';export const useMultipleImageUpload=_ref=>{let{announcementId,onSuccess,onError}=_ref;const[existingImages,setExistingImages]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[pendingDeletes,setPendingDeletes]=useState([]);// Clear existing images when announcementId becomes null (for new announcements)\nuseEffect(()=>{if(!announcementId){console.log('🧹 useMultipleImageUpload - Clearing existing images for new announcement');setExistingImages([]);setPendingDeletes([]);setError(null);}},[announcementId]);// Use the imported service instance\n// Clear error\nconst clearError=useCallback(()=>{setError(null);},[]);// Clear pending deletes\nconst clearPendingDeletes=useCallback(()=>{setPendingDeletes([]);},[]);// Clear all image-related state (for new announcements)\nconst clearAllImageState=useCallback(()=>{console.log('🧹 useMultipleImageUpload - Clearing all image state');setExistingImages([]);setPendingDeletes([]);setError(null);setLoading(false);},[]);// Mark image for deletion (don't delete immediately)\nconst markForDeletion=useCallback(attachmentId=>{console.log('🔴 Marking image for deletion:',attachmentId);setPendingDeletes(prev=>{if(!prev.includes(attachmentId)){const newPending=[...prev,attachmentId];console.log('🔴 Updated pending deletes:',newPending);return newPending;}return prev;});},[]);// Unmark image for deletion\nconst unmarkForDeletion=useCallback(attachmentId=>{console.log('🟢 Unmarking image for deletion:',attachmentId);setPendingDeletes(prev=>{const newPending=prev.filter(id=>id!==attachmentId);console.log('🟢 Updated pending deletes:',newPending);return newPending;});},[]);// Handle API errors\nconst handleError=useCallback((err,defaultMessage)=>{var _err$response,_err$response$data;const errorMessage=(err===null||err===void 0?void 0:(_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||(err===null||err===void 0?void 0:err.message)||defaultMessage;setError(errorMessage);onError===null||onError===void 0?void 0:onError(errorMessage);},[onError]);// Refresh images from server\nconst refreshImages=useCallback(async()=>{if(!announcementId)return;try{var _response$data;setLoading(true);setError(null);const response=await adminAnnouncementService.getAnnouncementImages(announcementId);if(response.success&&(_response$data=response.data)!==null&&_response$data!==void 0&&_response$data.images){setExistingImages(response.data.images);}else{setExistingImages([]);}}catch(err){handleError(err,'Failed to load images');}finally{setLoading(false);}},[announcementId,handleError]);// Upload multiple images\nconst uploadImages=useCallback(async files=>{if(!announcementId||files.length===0)return;try{setLoading(true);setError(null);const formData=new FormData();files.forEach(file=>{formData.append('images',file);});const response=await adminAnnouncementService.addAnnouncementImages(announcementId,formData);if(response.success){var _response$data2;// Directly update state with new images instead of making another API call\nif((_response$data2=response.data)!==null&&_response$data2!==void 0&&_response$data2.images){setExistingImages(response.data.images);}else{// Fallback to refresh if response doesn't include images\nawait refreshImages();}onSuccess===null||onSuccess===void 0?void 0:onSuccess(\"Successfully uploaded \".concat(files.length,\" image\").concat(files.length>1?'s':''));}else{throw new Error(response.message||'Upload failed');}}catch(err){handleError(err,'Failed to upload images');}finally{setLoading(false);}},[announcementId,onSuccess,handleError,refreshImages]);// Apply pending deletes (called when user clicks Update)\nconst applyPendingDeletes=useCallback(async()=>{if(!announcementId||pendingDeletes.length===0){return;}const deletesToApply=[...pendingDeletes];// Capture current state\ntry{setLoading(true);setError(null);// Batch delete all pending images\nconst deletePromises=deletesToApply.map(attachmentId=>adminAnnouncementService.deleteAnnouncementImage(announcementId,attachmentId));const results=await Promise.allSettled(deletePromises);// Check for any failures\nconst failures=results.filter(result=>result.status==='rejected');if(failures.length>0){throw new Error(\"Failed to delete \".concat(failures.length,\" image(s)\"));}// Update state optimistically - remove deleted images from existingImages\nsetExistingImages(prev=>prev.filter(img=>!deletesToApply.includes(img.attachment_id)));// Clear pending deletes\nsetPendingDeletes([]);onSuccess===null||onSuccess===void 0?void 0:onSuccess(\"Successfully deleted \".concat(deletesToApply.length,\" image(s)\"));}catch(err){console.error('❌ Error applying pending deletes:',err);handleError(err,'Failed to delete images');// Refresh images to ensure consistency\nawait refreshImages();}finally{setLoading(false);}},[announcementId,pendingDeletes,onSuccess,handleError,refreshImages]);// Delete an image (immediate deletion - kept for backward compatibility)\nconst deleteImage=useCallback(async attachmentId=>{if(!announcementId)return;try{setLoading(true);setError(null);const response=await adminAnnouncementService.deleteAnnouncementImage(announcementId,attachmentId);if(response.success){// Remove from local state immediately for better UX\nsetExistingImages(prev=>prev.filter(img=>img.attachment_id!==attachmentId));onSuccess===null||onSuccess===void 0?void 0:onSuccess('Image deleted successfully');}else{throw new Error(response.message||'Delete failed');}}catch(err){var _refreshResponse$data;handleError(err,'Failed to delete image');// Refresh images to ensure consistency\nconst refreshResponse=await adminAnnouncementService.getAnnouncementImages(announcementId);if(refreshResponse.success&&(_refreshResponse$data=refreshResponse.data)!==null&&_refreshResponse$data!==void 0&&_refreshResponse$data.images){setExistingImages(refreshResponse.data.images);}}finally{setLoading(false);}},[announcementId,onSuccess,handleError]);// Update image display order\nconst updateImageOrder=useCallback(async imageOrder=>{if(!announcementId)return;try{setLoading(true);setError(null);const response=await adminAnnouncementService.updateImageOrder(announcementId,imageOrder);if(response.success){// Update local state to reflect new order\nsetExistingImages(prev=>{const updated=[...prev];imageOrder.forEach(_ref2=>{let{attachment_id,display_order}=_ref2;const index=updated.findIndex(img=>img.attachment_id===attachment_id);if(index!==-1){updated[index]=_objectSpread(_objectSpread({},updated[index]),{},{display_order});}});return updated.sort((a,b)=>a.display_order-b.display_order);});onSuccess===null||onSuccess===void 0?void 0:onSuccess('Image order updated successfully');}else{throw new Error(response.message||'Update failed');}}catch(err){var _refreshResponse$data2;handleError(err,'Failed to update image order');// Refresh images to ensure consistency\nconst refreshResponse=await adminAnnouncementService.getAnnouncementImages(announcementId);if(refreshResponse.success&&(_refreshResponse$data2=refreshResponse.data)!==null&&_refreshResponse$data2!==void 0&&_refreshResponse$data2.images){setExistingImages(refreshResponse.data.images);}}finally{setLoading(false);}},[announcementId,onSuccess,handleError]);// Set primary image\nconst setPrimaryImage=useCallback(async attachmentId=>{if(!announcementId)return;try{setLoading(true);setError(null);const response=await adminAnnouncementService.setPrimaryImage(announcementId,attachmentId);if(response.success){// Update local state to reflect new primary image\nsetExistingImages(prev=>prev.map(img=>_objectSpread(_objectSpread({},img),{},{is_primary:img.attachment_id===attachmentId})));onSuccess===null||onSuccess===void 0?void 0:onSuccess('Primary image updated successfully');}else{throw new Error(response.message||'Update failed');}}catch(err){var _refreshResponse$data3;handleError(err,'Failed to set primary image');// Refresh images to ensure consistency\nconst refreshResponse=await adminAnnouncementService.getAnnouncementImages(announcementId);if(refreshResponse.success&&(_refreshResponse$data3=refreshResponse.data)!==null&&_refreshResponse$data3!==void 0&&_refreshResponse$data3.images){setExistingImages(refreshResponse.data.images);}}finally{setLoading(false);}},[announcementId,onSuccess,handleError]);return{existingImages,loading,error,uploadImages,deleteImage,updateImageOrder,setPrimaryImage,refreshImages,clearError,// New pending operations\npendingDeletes,markForDeletion,unmarkForDeletion,applyPendingDeletes,clearPendingDeletes,// Clear all image state\nclearAllImageState};};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}