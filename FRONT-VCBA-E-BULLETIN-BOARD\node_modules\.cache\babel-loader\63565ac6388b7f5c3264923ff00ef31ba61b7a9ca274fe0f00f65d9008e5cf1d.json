{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17.5 12c0 4.4-3.6 8-8 8A4.5 4.5 0 0 1 5 15.5c0-6 8-4 8-8.5a3 3 0 1 0-6 0c0 3 2.5 8.5 12 13\",\n  key: \"1o9ehi\"\n}], [\"path\", {\n  d: \"M16 12h3\",\n  key: \"4uvgyw\"\n}]];\nconst Ampersand = createLucideIcon(\"ampersand\", __iconNode);\nexport { __iconNode, Ampersand as default };\n//# sourceMappingURL=ampersand.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}