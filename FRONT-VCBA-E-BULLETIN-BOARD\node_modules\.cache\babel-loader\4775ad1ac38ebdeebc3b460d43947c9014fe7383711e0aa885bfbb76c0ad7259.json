{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\layout\\\\StudentLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport StudentSidebar from './StudentSidebar';\nimport StudentHeader from './StudentHeader';\nimport '../student.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      height: '100vh',\n      width: '100vw',\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(StudentSidebar, {\n      isOpen: sidebarOpen,\n      onToggle: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        marginLeft: sidebarOpen ? '280px' : '80px',\n        transition: 'margin-left 0.3s ease',\n        height: '100vh',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(StudentHeader, {\n        onToggleSidebar: toggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        style: {\n          flex: 1,\n          background: 'transparent',\n          overflow: 'hidden',\n          height: 'calc(100vh - 80px)',\n          // Subtract header height\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflow: 'auto',\n            padding: '2rem'\n          },\n          children: children || /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 26\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentLayout, \"fX/xBG9dqPCXDXt8dvZeCgFoxdw=\");\n_c = StudentLayout;\nexport default StudentLayout;\nvar _c;\n$RefreshReg$(_c, \"StudentLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "StudentSidebar", "StudentHeader", "jsxDEV", "_jsxDEV", "StudentLayout", "children", "_s", "sidebarOpen", "setSidebarOpen", "toggleSidebar", "style", "display", "height", "width", "position", "top", "left", "background", "overflow", "isOpen", "onToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "flexDirection", "marginLeft", "transition", "onToggleSidebar", "padding", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/layout/StudentLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport StudentSidebar from './StudentSidebar';\nimport StudentHeader from './StudentHeader';\nimport '../student.css';\n\ninterface StudentLayoutProps {\n  children?: React.ReactNode;\n}\n\nconst StudentLayout: React.FC<StudentLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      height: '100vh',\n      width: '100vw',\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n      overflow: 'hidden'\n    }}>\n      {/* Sidebar */}\n      <StudentSidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />\n\n      {/* Main Content Area */}\n      <div style={{\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        marginLeft: sidebarOpen ? '280px' : '80px',\n        transition: 'margin-left 0.3s ease',\n        height: '100vh',\n        overflow: 'hidden'\n      }}>\n        {/* Header */}\n        <StudentHeader onToggleSidebar={toggleSidebar} />\n\n        {/* Page Content */}\n        <main style={{\n          flex: 1,\n          background: 'transparent',\n          overflow: 'hidden',\n          height: 'calc(100vh - 80px)', // Subtract header height\n          position: 'relative'\n        }}>\n          <div style={{\n            height: '100%',\n            overflow: 'auto',\n            padding: '2rem'\n          }}>\n            {children || <Outlet />}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMxB,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEJ,OAAA;IAAKO,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAE,mDAAmD;MAC/DC,QAAQ,EAAE;IACZ,CAAE;IAAAb,QAAA,gBAEAF,OAAA,CAACH,cAAc;MAACmB,MAAM,EAAEZ,WAAY;MAACa,QAAQ,EAAEX;IAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhErB,OAAA;MAAKO,KAAK,EAAE;QACVe,IAAI,EAAE,CAAC;QACPd,OAAO,EAAE,MAAM;QACfe,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAEpB,WAAW,GAAG,OAAO,GAAG,MAAM;QAC1CqB,UAAU,EAAE,uBAAuB;QACnChB,MAAM,EAAE,OAAO;QACfM,QAAQ,EAAE;MACZ,CAAE;MAAAb,QAAA,gBAEAF,OAAA,CAACF,aAAa;QAAC4B,eAAe,EAAEpB;MAAc;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjDrB,OAAA;QAAMO,KAAK,EAAE;UACXe,IAAI,EAAE,CAAC;UACPR,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,QAAQ;UAClBN,MAAM,EAAE,oBAAoB;UAAE;UAC9BE,QAAQ,EAAE;QACZ,CAAE;QAAAT,QAAA,eACAF,OAAA;UAAKO,KAAK,EAAE;YACVE,MAAM,EAAE,MAAM;YACdM,QAAQ,EAAE,MAAM;YAChBY,OAAO,EAAE;UACX,CAAE;UAAAzB,QAAA,EACCA,QAAQ,iBAAIF,OAAA,CAACJ,MAAM;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CArDIF,aAA2C;AAAA2B,EAAA,GAA3C3B,aAA2C;AAuDjD,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}