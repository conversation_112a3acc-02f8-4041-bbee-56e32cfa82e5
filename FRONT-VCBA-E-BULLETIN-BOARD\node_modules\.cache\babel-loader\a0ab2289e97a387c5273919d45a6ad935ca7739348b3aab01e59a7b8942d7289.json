{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 6v6l1.56.78\",\n  key: \"14ed3g\"\n}], [\"path\", {\n  d: \"M13.227 21.925a10 10 0 1 1 8.767-9.588\",\n  key: \"jwkls1\"\n}], [\"path\", {\n  d: \"m14 18 4-4 4 4\",\n  key: \"ftkppy\"\n}], [\"path\", {\n  d: \"M18 22v-8\",\n  key: \"su0gjh\"\n}]];\nconst ClockArrowUp = createLucideIcon(\"clock-arrow-up\", __iconNode);\nexport { __iconNode, ClockArrowUp as default };\n//# sourceMappingURL=clock-arrow-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}