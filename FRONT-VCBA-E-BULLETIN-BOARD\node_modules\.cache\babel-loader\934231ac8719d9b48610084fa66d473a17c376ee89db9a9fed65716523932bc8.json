{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5\",\n  key: \"1couwa\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z\",\n  key: \"1y4qbx\"\n}]];\nconst FilePen = createLucideIcon(\"file-pen\", __iconNode);\nexport { __iconNode, FilePen as default };\n//# sourceMappingURL=file-pen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}