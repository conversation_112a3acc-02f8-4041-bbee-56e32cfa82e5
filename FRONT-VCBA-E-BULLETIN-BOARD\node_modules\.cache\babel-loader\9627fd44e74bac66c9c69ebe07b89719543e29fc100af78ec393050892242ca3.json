{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"4\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"1wwnby\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"14\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1fe6j6\"\n}], [\"path\", {\n  d: \"M17 22v-5\",\n  key: \"4b6g73\"\n}], [\"path\", {\n  d: \"M17 7V2\",\n  key: \"hnrr36\"\n}], [\"path\", {\n  d: \"M7 22v-3\",\n  key: \"1r4jpn\"\n}], [\"path\", {\n  d: \"M7 5V2\",\n  key: \"liy1u9\"\n}]];\nconst AlignHorizontalDistributeCenter = createLucideIcon(\"align-horizontal-distribute-center\", __iconNode);\nexport { __iconNode, AlignHorizontalDistributeCenter as default };\n//# sourceMappingURL=align-horizontal-distribute-center.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}