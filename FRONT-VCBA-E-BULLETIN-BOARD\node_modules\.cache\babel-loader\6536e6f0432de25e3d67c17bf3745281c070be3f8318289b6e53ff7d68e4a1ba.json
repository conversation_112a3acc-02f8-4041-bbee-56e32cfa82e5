{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m9 7-5 5 5 5\",\n  key: \"j5w590\"\n}], [\"path\", {\n  d: \"m15 7 5 5-5 5\",\n  key: \"1bl6da\"\n}]];\nconst ChevronsLeftRight = createLucideIcon(\"chevrons-left-right\", __iconNode);\nexport { __iconNode, ChevronsLeftRight as default };\n//# sourceMappingURL=chevrons-left-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}