{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 17c5 0 8-2.69 8-6H4c0 3.31 3 6 8 6m-4 4h8m-4-3v3M5.14 11a3.5 3.5 0 1 1 6.71 0\",\n  key: \"1uxfcu\"\n}], [\"path\", {\n  d: \"M12.14 11a3.5 3.5 0 1 1 6.71 0\",\n  key: \"4k3m1s\"\n}], [\"path\", {\n  d: \"M15.5 6.5a3.5 3.5 0 1 0-7 0\",\n  key: \"zmuahr\"\n}]];\nconst IceCreamBowl = createLucideIcon(\"ice-cream-bowl\", __iconNode);\nexport { __iconNode, IceCreamBowl as default };\n//# sourceMappingURL=ice-cream-bowl.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}