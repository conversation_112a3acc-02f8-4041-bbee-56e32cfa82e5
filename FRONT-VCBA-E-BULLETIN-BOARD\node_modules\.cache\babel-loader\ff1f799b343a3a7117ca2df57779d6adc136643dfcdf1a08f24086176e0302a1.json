{"ast": null, "code": "import _objectSpread from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"className\", \"children\", \"iconNode\"];\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\nconst Icon = forwardRef((_ref, ref) => {\n  let {\n      color = \"currentColor\",\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = \"\",\n      children,\n      iconNode\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return createElement(\"svg\", _objectSpread(_objectSpread(_objectSpread({\n    ref\n  }, defaultAttributes), {}, {\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n    className: mergeClasses(\"lucide\", className)\n  }, !children && !hasA11yProp(rest) && {\n    \"aria-hidden\": \"true\"\n  }), rest), [...iconNode.map(_ref2 => {\n    let [tag, attrs] = _ref2;\n    return createElement(tag, attrs);\n  }), ...(Array.isArray(children) ? children : [children])]);\n});\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}