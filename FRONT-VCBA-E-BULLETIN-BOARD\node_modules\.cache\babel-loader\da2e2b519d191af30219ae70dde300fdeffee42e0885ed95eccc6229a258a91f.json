{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10H6\",\n  key: \"1bsnug\"\n}], [\"path\", {\n  d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\",\n  key: \"wrbu53\"\n}], [\"path\", {\n  d: \"M19 18h2a1 1 0 0 0 1-1v-3.28a1 1 0 0 0-.684-.948l-1.923-.641a1 1 0 0 1-.578-.502l-1.539-3.076A1 1 0 0 0 16.382 8H14\",\n  key: \"lrkjwd\"\n}], [\"path\", {\n  d: \"M8 8v4\",\n  key: \"1fwk8c\"\n}], [\"path\", {\n  d: \"M9 18h6\",\n  key: \"x1upvd\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"332jqn\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}]];\nconst Ambulance = createLucideIcon(\"ambulance\", __iconNode);\nexport { __iconNode, Ambulance as default };\n//# sourceMappingURL=ambulance.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}