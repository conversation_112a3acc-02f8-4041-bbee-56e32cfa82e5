{"ast": null, "code": "import React,{Component}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";class ErrorBoundary extends Component{constructor(props){super(props);this.state={hasError:false};}static getDerivedStateFromError(error){// Update state so the next render will show the fallback UI\nreturn{hasError:true,error};}componentDidCatch(error,errorInfo){// Log the error\nconsole.error('ErrorBoundary caught an error:',error,errorInfo);// Call the onError callback if provided\nif(this.props.onError){this.props.onError(error,errorInfo);}}render(){if(this.state.hasError){// Render custom fallback UI if provided\nif(this.props.fallback){return this.props.fallback;}// Default fallback UI\nreturn/*#__PURE__*/_jsx(\"div\",{className:\"error-boundary\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"error-boundary__container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-boundary__icon\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"48\",height:\"48\",viewBox:\"0 0 24 24\",fill:\"none\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"15\",y1:\"9\",x2:\"9\",y2:\"15\",stroke:\"currentColor\",strokeWidth:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"9\",y1:\"9\",x2:\"15\",y2:\"15\",stroke:\"currentColor\",strokeWidth:\"2\"})]})}),/*#__PURE__*/_jsx(\"h2\",{children:\"Something went wrong\"}),/*#__PURE__*/_jsx(\"p\",{children:\"We're sorry, but something unexpected happened. Please try refreshing the page.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.reload(),className:\"error-boundary__button\",children:\"Refresh Page\"})]})});}return this.props.children;}}export default ErrorBoundary;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}