{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 3.34V5a3 3 0 0 0 3 3\",\n  key: \"w732o8\"\n}], [\"path\", {\n  d: \"M11 21.95V18a2 2 0 0 0-2-2 2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05\",\n  key: \"f02343\"\n}], [\"path\", {\n  d: \"M21.54 15H17a2 2 0 0 0-2 2v4.54\",\n  key: \"1djwo0\"\n}], [\"path\", {\n  d: \"M12 2a10 10 0 1 0 9.54 13\",\n  key: \"zjsr6q\"\n}], [\"path\", {\n  d: \"M20 6V4a2 2 0 1 0-4 0v2\",\n  key: \"1of5e8\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"14\",\n  y: \"6\",\n  rx: \"1\",\n  key: \"1fmf51\"\n}]];\nconst EarthLock = createLucideIcon(\"earth-lock\", __iconNode);\nexport { __iconNode, EarthLock as default };\n//# sourceMappingURL=earth-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}