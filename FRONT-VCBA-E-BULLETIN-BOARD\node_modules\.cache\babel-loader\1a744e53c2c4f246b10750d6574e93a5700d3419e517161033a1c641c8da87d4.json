{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"15\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"u2pros\"\n}], [\"path\", {\n  d: \"M20.9 19.8A2 2 0 0 0 22 18V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2h5.1\",\n  key: \"1jj40k\"\n}], [\"path\", {\n  d: \"M15 11v-1\",\n  key: \"cntcp\"\n}], [\"path\", {\n  d: \"M15 17v-2\",\n  key: \"1279jj\"\n}]];\nconst FolderArchive = createLucideIcon(\"folder-archive\", __iconNode);\nexport { __iconNode, FolderArchive as default };\n//# sourceMappingURL=folder-archive.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}