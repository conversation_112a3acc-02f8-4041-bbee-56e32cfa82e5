{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\CalendarImageUpload.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Star, Image as ImageIcon, AlertCircle } from 'lucide-react';\nimport { getImageUrl } from '../../config/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Custom hook for CORS-safe image loading\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n      } catch (err) {\n        console.error('❌ Failed to load image via CORS-safe method:', err);\n        setError(err.message || 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup function to revoke object URL\n    return () => {\n      if (imageUrl) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Component for displaying individual calendar images with CORS-safe loading\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst CalendarImageDisplay = ({\n  image,\n  isMarkedForDeletion,\n  onToggleDeletion,\n  onSetPrimary\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(image.file_path);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      aspectRatio: '1',\n      borderRadius: '8px',\n      overflow: 'hidden',\n      border: image.is_primary ? '2px solid #22c55e' : '1px solid #e5e7eb',\n      opacity: isMarkedForDeletion ? 0.5 : 1,\n      filter: isMarkedForDeletion ? 'grayscale(100%)' : 'none',\n      backgroundColor: '#f9fafb'\n    },\n    children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        inset: 0,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280',\n        fontSize: '0.75rem',\n        textAlign: 'center',\n        padding: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '4px'\n        },\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this), (error || loading) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        inset: 0,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        fontSize: '0.75rem',\n        textAlign: 'center',\n        padding: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '1.5rem',\n          marginBottom: '4px'\n        },\n        children: \"\\u23F3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          wordBreak: 'break-all'\n        },\n        children: [\"Image loading...\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 29\n        }, this), image.file_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this), imageUrl && !loading && !error && /*#__PURE__*/_jsxDEV(\"img\", {\n      src: imageUrl,\n      alt: image.file_name,\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '4px',\n        right: '4px',\n        display: 'flex',\n        gap: '4px'\n      },\n      children: [!image.is_primary && !isMarkedForDeletion && onSetPrimary && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: e => {\n          e.preventDefault();\n          e.stopPropagation();\n          onSetPrimary(image.attachment_id);\n        },\n        style: {\n          backgroundColor: 'rgba(34, 197, 94, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          padding: '4px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        title: \"Set as primary\",\n        children: /*#__PURE__*/_jsxDEV(Star, {\n          size: 12\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: e => {\n          e.preventDefault();\n          e.stopPropagation();\n          onToggleDeletion(image.attachment_id);\n        },\n        style: {\n          backgroundColor: isMarkedForDeletion ? 'rgba(34, 197, 94, 0.7)' : 'rgba(239, 68, 68, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          padding: '4px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        title: isMarkedForDeletion ? 'Undo delete' : 'Mark for deletion',\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 12\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), isMarkedForDeletion && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        inset: 0,\n        backgroundColor: 'rgba(239, 68, 68, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: '#dc2626',\n        fontSize: '0.875rem',\n        fontWeight: '500'\n      },\n      children: \"Will be deleted\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s2(CalendarImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = CalendarImageDisplay;\nconst CalendarImageUpload = ({\n  onImagesChange,\n  existingImages = [],\n  onSetPrimary,\n  maxImages = 10,\n  maxFileSize = 5 * 1024 * 1024,\n  // 5MB\n  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  pendingDeletes = [],\n  onMarkForDeletion,\n  onUnmarkForDeletion\n}) => {\n  _s3();\n  const [images, setImages] = useState([]);\n  const [dragActive, setDragActive] = useState(false);\n  const [error, setError] = useState(null);\n  const fileInputRef = useRef(null);\n  const dragCounter = useRef(0);\n\n  // Validate file\n  const validateFile = useCallback(file => {\n    if (!acceptedTypes.includes(file.type)) {\n      return `Invalid file type. Accepted types: ${acceptedTypes.join(', ')}`;\n    }\n    if (file.size > maxFileSize) {\n      return `File size too large. Maximum size: ${(maxFileSize / (1024 * 1024)).toFixed(1)}MB`;\n    }\n    return null;\n  }, [acceptedTypes, maxFileSize]);\n\n  // Process files\n  const processFiles = useCallback(fileList => {\n    const files = Array.from(fileList);\n    const totalImages = images.length + existingImages.length;\n    if (totalImages + files.length > maxImages) {\n      setError(`Maximum ${maxImages} images allowed. You can add ${maxImages - totalImages} more.`);\n      return;\n    }\n    const validFiles = [];\n    const errors = [];\n    files.forEach(file => {\n      const validationError = validateFile(file);\n      if (validationError) {\n        errors.push(`${file.name}: ${validationError}`);\n      } else {\n        validFiles.push(file);\n      }\n    });\n    if (errors.length > 0) {\n      setError(errors.join('\\n'));\n      return;\n    }\n    setError(null);\n    const newImages = validFiles.map(file => ({\n      id: `${Date.now()}-${Math.random()}`,\n      file,\n      preview: URL.createObjectURL(file)\n    }));\n    setImages(prev => {\n      const updated = [...prev, ...newImages];\n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [images.length, existingImages.length, maxImages, validateFile, onImagesChange]);\n\n  // Handle file input change\n  const handleFileChange = useCallback(e => {\n    if (e.target.files && e.target.files.length > 0) {\n      processFiles(e.target.files);\n    }\n  }, [processFiles]);\n\n  // Handle drag events\n  const handleDragEnter = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setDragActive(true);\n    }\n  }, []);\n  const handleDragLeave = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setDragActive(false);\n    }\n  }, []);\n  const handleDragOver = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n  const handleDrop = useCallback(e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    dragCounter.current = 0;\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n      processFiles(e.dataTransfer.files);\n    }\n  }, [processFiles]);\n\n  // Remove new image\n  const removeImage = useCallback(imageId => {\n    setImages(prev => {\n      const updated = prev.filter(img => img.id !== imageId);\n      // Clean up object URL\n      const removedImage = prev.find(img => img.id === imageId);\n      if (removedImage) {\n        URL.revokeObjectURL(removedImage.preview);\n      }\n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [onImagesChange]);\n\n  // Toggle existing image for deletion\n  const toggleExistingImageDeletion = useCallback(attachmentId => {\n    const isMarkedForDeletion = pendingDeletes.includes(attachmentId);\n    if (isMarkedForDeletion) {\n      console.log('🔄 Unmarking image for deletion:', attachmentId);\n      onUnmarkForDeletion === null || onUnmarkForDeletion === void 0 ? void 0 : onUnmarkForDeletion(attachmentId);\n    } else {\n      console.log('🏷️ Marking image for deletion:', attachmentId);\n      onMarkForDeletion === null || onMarkForDeletion === void 0 ? void 0 : onMarkForDeletion(attachmentId);\n    }\n  }, [pendingDeletes, onMarkForDeletion, onUnmarkForDeletion]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback(attachmentId => {\n    if (onSetPrimary) {\n      onSetPrimary(attachmentId);\n    }\n  }, [onSetPrimary]);\n\n  // Clear error after 5 seconds\n  React.useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => setError(null), 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [error]);\n\n  // Clean up object URLs on unmount\n  React.useEffect(() => {\n    return () => {\n      images.forEach(img => URL.revokeObjectURL(img.preview));\n    };\n  }, []);\n  const totalImages = images.length + existingImages.length;\n  const canAddMore = totalImages < maxImages && !disabled;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `calendar-image-upload ${className}`,\n    children: [canAddMore && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `upload-area ${dragActive ? 'drag-active' : ''} ${disabled ? 'disabled' : ''}`,\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDragOver: handleDragOver,\n      onDrop: handleDrop,\n      onClick: () => {\n        var _fileInputRef$current;\n        return !disabled && ((_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click());\n      },\n      style: {\n        border: '2px dashed #d1d5db',\n        borderRadius: '8px',\n        padding: '2rem',\n        textAlign: 'center',\n        cursor: disabled ? 'not-allowed' : 'pointer',\n        backgroundColor: dragActive ? '#f3f4f6' : disabled ? '#f9fafb' : 'white',\n        borderColor: dragActive ? '#22c55e' : error ? '#ef4444' : '#d1d5db',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ref: fileInputRef,\n        type: \"file\",\n        multiple: true,\n        accept: acceptedTypes.join(','),\n        onChange: handleFileChange,\n        style: {\n          display: 'none'\n        },\n        disabled: disabled\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Upload, {\n        size: 48,\n        style: {\n          color: dragActive ? '#22c55e' : disabled ? '#9ca3af' : '#6b7280',\n          marginBottom: '1rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: disabled ? '#9ca3af' : '#374151',\n          fontSize: '1rem',\n          fontWeight: '500',\n          marginBottom: '0.5rem'\n        },\n        children: dragActive ? 'Drop images here' : 'Click to upload or drag and drop'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: [\"PNG, JPG, GIF, WebP up to \", (maxFileSize / (1024 * 1024)).toFixed(1), \"MB\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this), totalImages, \"/\", maxImages, \" images\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '6px',\n        padding: '0.75rem',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        size: 16,\n        style: {\n          color: '#ef4444',\n          marginTop: '0.125rem',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#dc2626',\n          fontSize: '0.875rem',\n          whiteSpace: 'pre-line'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 9\n    }, this), (existingImages.length > 0 || images.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',\n        gap: '1rem'\n      },\n      children: [existingImages.map(image => {\n        const isMarkedForDeletion = pendingDeletes.includes(image.attachment_id);\n        return /*#__PURE__*/_jsxDEV(CalendarImageDisplay, {\n          image: image,\n          isMarkedForDeletion: isMarkedForDeletion,\n          onToggleDeletion: toggleExistingImageDeletion,\n          onSetPrimary: onSetPrimary\n        }, `existing-${image.attachment_id}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 15\n        }, this);\n      }), images.map(image => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          aspectRatio: '1',\n          borderRadius: '8px',\n          overflow: 'hidden',\n          border: '1px solid #e5e7eb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.preview,\n          alt: \"Preview\",\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => removeImage(image.id),\n          style: {\n            position: 'absolute',\n            top: '4px',\n            right: '4px',\n            backgroundColor: 'rgba(239, 68, 68, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            padding: '4px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          title: \"Remove image\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 15\n        }, this)]\n      }, image.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 9\n    }, this), existingImages.length === 0 && images.length === 0 && !canAddMore && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ImageIcon, {\n        size: 48,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No images uploaded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 410,\n    columnNumber: 5\n  }, this);\n};\n_s3(CalendarImageUpload, \"YUV81KoUL5tEQGdloPq99xsMNIU=\");\n_c2 = CalendarImageUpload;\nexport default CalendarImageUpload;\nvar _c, _c2;\n$RefreshReg$(_c, \"CalendarImageDisplay\");\n$RefreshReg$(_c2, \"CalendarImageUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useEffect", "Upload", "X", "Star", "Image", "ImageIcon", "AlertCircle", "getImageUrl", "jsxDEV", "_jsxDEV", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "console", "message", "revokeObjectURL", "CalendarImageDisplay", "image", "isMarkedForDeletion", "onToggleDeletion", "onSetPrimary", "_s2", "file_path", "style", "position", "aspectRatio", "borderRadius", "overflow", "border", "is_primary", "opacity", "filter", "backgroundColor", "children", "inset", "display", "flexDirection", "alignItems", "justifyContent", "color", "fontSize", "textAlign", "padding", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "marginBottom", "wordBreak", "file_name", "src", "alt", "width", "height", "objectFit", "top", "right", "gap", "onClick", "e", "preventDefault", "stopPropagation", "attachment_id", "cursor", "title", "fontWeight", "_c", "CalendarImageUpload", "onImagesChange", "existingImages", "maxImages", "maxFileSize", "acceptedTypes", "className", "disabled", "pendingDeletes", "onMarkForDeletion", "onUnmarkForDeletion", "_s3", "images", "setImages", "dragActive", "setDragActive", "fileInputRef", "dragCounter", "validateFile", "file", "includes", "type", "join", "toFixed", "processFiles", "fileList", "files", "Array", "from", "totalImages", "length", "validFiles", "errors", "for<PERSON>ach", "validationError", "push", "name", "newImages", "map", "id", "Date", "now", "Math", "random", "preview", "prev", "updated", "img", "handleFileChange", "target", "handleDragEnter", "current", "dataTransfer", "items", "handleDragLeave", "handleDragOver", "handleDrop", "removeImage", "imageId", "removedImage", "find", "toggleExistingImageDeletion", "attachmentId", "log", "setPrimaryImage", "timer", "setTimeout", "clearTimeout", "canAddMore", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "_fileInputRef$current", "click", "borderColor", "ref", "multiple", "accept", "onChange", "flexShrink", "whiteSpace", "gridTemplateColumns", "_c2", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/CalendarImageUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Upload, X, Star, Image as ImageIcon, AlertCircle } from 'lucide-react';\nimport type { CalendarAttachment } from '../../hooks/useCalendarImageUpload';\nimport { getImageUrl } from '../../config/constants';\n\ninterface ImageFile {\n  id: string;\n  file: File;\n  preview: string;\n}\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n      } catch (err: any) {\n        console.error('❌ Failed to load image via CORS-safe method:', err);\n        setError(err.message || 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup function to revoke object URL\n    return () => {\n      if (imageUrl) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Component for displaying individual calendar images with CORS-safe loading\nconst CalendarImageDisplay: React.FC<{\n  image: CalendarAttachment;\n  isMarkedForDeletion: boolean;\n  onToggleDeletion: (attachmentId: number) => void;\n  onSetPrimary?: (attachmentId: number) => void;\n}> = ({ image, isMarkedForDeletion, onToggleDeletion, onSetPrimary }) => {\n  const { imageUrl, loading, error } = useImageLoader(image.file_path);\n\n  return (\n    <div\n      style={{\n        position: 'relative',\n        aspectRatio: '1',\n        borderRadius: '8px',\n        overflow: 'hidden',\n        border: image.is_primary ? '2px solid #22c55e' : '1px solid #e5e7eb',\n        opacity: isMarkedForDeletion ? 0.5 : 1,\n        filter: isMarkedForDeletion ? 'grayscale(100%)' : 'none',\n        backgroundColor: '#f9fafb'\n      }}\n    >\n      {/* Loading state */}\n      {loading && (\n        <div style={{\n          position: 'absolute',\n          inset: 0,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f3f4f6',\n          color: '#6b7280',\n          fontSize: '0.75rem',\n          textAlign: 'center',\n          padding: '8px'\n        }}>\n          <ImageIcon size={24} />\n          <div style={{ marginTop: '4px' }}>Loading...</div>\n        </div>\n      )}\n\n      {/* Loading/Error state */}\n      {(error || loading) && (\n        <div style={{\n          position: 'absolute',\n          inset: 0,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f8fafc',\n          color: '#64748b',\n          fontSize: '0.75rem',\n          textAlign: 'center',\n          padding: '8px'\n        }}>\n          <div style={{ fontSize: '1.5rem', marginBottom: '4px' }}>⏳</div>\n          <div style={{ wordBreak: 'break-all' }}>\n            Image loading...<br/>{image.file_name}\n          </div>\n        </div>\n      )}\n\n      {/* Image */}\n      {imageUrl && !loading && !error && (\n        <img\n          src={imageUrl}\n          alt={image.file_name}\n          style={{\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover'\n          }}\n        />\n      )}\n\n      {/* Controls */}\n      <div style={{\n        position: 'absolute',\n        top: '4px',\n        right: '4px',\n        display: 'flex',\n        gap: '4px'\n      }}>\n        {/* Set Primary Button */}\n        {!image.is_primary && !isMarkedForDeletion && onSetPrimary && (\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              e.stopPropagation();\n              onSetPrimary(image.attachment_id);\n            }}\n            style={{\n              backgroundColor: 'rgba(34, 197, 94, 0.7)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              padding: '4px',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n            title=\"Set as primary\"\n          >\n            <Star size={12} />\n          </button>\n        )}\n\n        {/* Delete/Undelete Button */}\n        <button\n          onClick={(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            onToggleDeletion(image.attachment_id);\n          }}\n          style={{\n            backgroundColor: isMarkedForDeletion ? 'rgba(34, 197, 94, 0.7)' : 'rgba(239, 68, 68, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            padding: '4px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}\n          title={isMarkedForDeletion ? 'Undo delete' : 'Mark for deletion'}\n        >\n          <X size={12} />\n        </button>\n      </div>\n\n\n\n      {/* Deletion Overlay */}\n      {isMarkedForDeletion && (\n        <div style={{\n          position: 'absolute',\n          inset: 0,\n          backgroundColor: 'rgba(239, 68, 68, 0.1)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#dc2626',\n          fontSize: '0.875rem',\n          fontWeight: '500'\n        }}>\n          Will be deleted\n        </div>\n      )}\n    </div>\n  );\n};\n\ninterface CalendarImageUploadProps {\n  onImagesChange: (files: File[]) => void;\n  existingImages?: CalendarAttachment[];\n  onSetPrimary?: (attachmentId: number) => void;\n  maxImages?: number;\n  maxFileSize?: number; // in bytes\n  acceptedTypes?: string[];\n  className?: string;\n  disabled?: boolean;\n  // Pending deletion props\n  pendingDeletes?: number[];\n  onMarkForDeletion?: (attachmentId: number) => void;\n  onUnmarkForDeletion?: (attachmentId: number) => void;\n}\n\nconst CalendarImageUpload: React.FC<CalendarImageUploadProps> = ({\n  onImagesChange,\n  existingImages = [],\n  onSetPrimary,\n  maxImages = 10,\n  maxFileSize = 5 * 1024 * 1024, // 5MB\n  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  className = '',\n  disabled = false,\n  pendingDeletes = [],\n  onMarkForDeletion,\n  onUnmarkForDeletion\n}) => {\n  const [images, setImages] = useState<ImageFile[]>([]);\n  const [dragActive, setDragActive] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const dragCounter = useRef(0);\n\n  // Validate file\n  const validateFile = useCallback((file: File): string | null => {\n    if (!acceptedTypes.includes(file.type)) {\n      return `Invalid file type. Accepted types: ${acceptedTypes.join(', ')}`;\n    }\n    if (file.size > maxFileSize) {\n      return `File size too large. Maximum size: ${(maxFileSize / (1024 * 1024)).toFixed(1)}MB`;\n    }\n    return null;\n  }, [acceptedTypes, maxFileSize]);\n\n  // Process files\n  const processFiles = useCallback((fileList: FileList) => {\n    const files = Array.from(fileList);\n    const totalImages = images.length + existingImages.length;\n    \n    if (totalImages + files.length > maxImages) {\n      setError(`Maximum ${maxImages} images allowed. You can add ${maxImages - totalImages} more.`);\n      return;\n    }\n\n    const validFiles: File[] = [];\n    const errors: string[] = [];\n\n    files.forEach(file => {\n      const validationError = validateFile(file);\n      if (validationError) {\n        errors.push(`${file.name}: ${validationError}`);\n      } else {\n        validFiles.push(file);\n      }\n    });\n\n    if (errors.length > 0) {\n      setError(errors.join('\\n'));\n      return;\n    }\n\n    setError(null);\n\n    const newImages: ImageFile[] = validFiles.map(file => ({\n      id: `${Date.now()}-${Math.random()}`,\n      file,\n      preview: URL.createObjectURL(file)\n    }));\n\n    setImages(prev => {\n      const updated = [...prev, ...newImages];\n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [images.length, existingImages.length, maxImages, validateFile, onImagesChange]);\n\n  // Handle file input change\n  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files.length > 0) {\n      processFiles(e.target.files);\n    }\n  }, [processFiles]);\n\n  // Handle drag events\n  const handleDragEnter = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current++;\n    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n      setDragActive(true);\n    }\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    dragCounter.current--;\n    if (dragCounter.current === 0) {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    dragCounter.current = 0;\n\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n      processFiles(e.dataTransfer.files);\n    }\n  }, [processFiles]);\n\n  // Remove new image\n  const removeImage = useCallback((imageId: string) => {\n    setImages(prev => {\n      const updated = prev.filter(img => img.id !== imageId);\n      // Clean up object URL\n      const removedImage = prev.find(img => img.id === imageId);\n      if (removedImage) {\n        URL.revokeObjectURL(removedImage.preview);\n      }\n      \n      onImagesChange(updated.map(img => img.file));\n      return updated;\n    });\n  }, [onImagesChange]);\n\n  // Toggle existing image for deletion\n  const toggleExistingImageDeletion = useCallback((attachmentId: number) => {\n    const isMarkedForDeletion = pendingDeletes.includes(attachmentId);\n\n    if (isMarkedForDeletion) {\n      console.log('🔄 Unmarking image for deletion:', attachmentId);\n      onUnmarkForDeletion?.(attachmentId);\n    } else {\n      console.log('🏷️ Marking image for deletion:', attachmentId);\n      onMarkForDeletion?.(attachmentId);\n    }\n  }, [pendingDeletes, onMarkForDeletion, onUnmarkForDeletion]);\n\n  // Set primary image\n  const setPrimaryImage = useCallback((attachmentId: number) => {\n    if (onSetPrimary) {\n      onSetPrimary(attachmentId);\n    }\n  }, [onSetPrimary]);\n\n  // Clear error after 5 seconds\n  React.useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => setError(null), 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [error]);\n\n  // Clean up object URLs on unmount\n  React.useEffect(() => {\n    return () => {\n      images.forEach(img => URL.revokeObjectURL(img.preview));\n    };\n  }, []);\n\n  const totalImages = images.length + existingImages.length;\n  const canAddMore = totalImages < maxImages && !disabled;\n\n  return (\n    <div className={`calendar-image-upload ${className}`}>\n      {/* Upload Area */}\n      {canAddMore && (\n        <div\n          className={`upload-area ${dragActive ? 'drag-active' : ''} ${disabled ? 'disabled' : ''}`}\n          onDragEnter={handleDragEnter}\n          onDragLeave={handleDragLeave}\n          onDragOver={handleDragOver}\n          onDrop={handleDrop}\n          onClick={() => !disabled && fileInputRef.current?.click()}\n          style={{\n            border: '2px dashed #d1d5db',\n            borderRadius: '8px',\n            padding: '2rem',\n            textAlign: 'center',\n            cursor: disabled ? 'not-allowed' : 'pointer',\n            backgroundColor: dragActive ? '#f3f4f6' : disabled ? '#f9fafb' : 'white',\n            borderColor: dragActive ? '#22c55e' : error ? '#ef4444' : '#d1d5db',\n            marginBottom: '1rem'\n          }}\n        >\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            multiple\n            accept={acceptedTypes.join(',')}\n            onChange={handleFileChange}\n            style={{ display: 'none' }}\n            disabled={disabled}\n          />\n          \n          <Upload \n            size={48} \n            style={{ \n              color: dragActive ? '#22c55e' : disabled ? '#9ca3af' : '#6b7280',\n              marginBottom: '1rem'\n            }} \n          />\n          \n          <p style={{ \n            color: disabled ? '#9ca3af' : '#374151',\n            fontSize: '1rem',\n            fontWeight: '500',\n            marginBottom: '0.5rem'\n          }}>\n            {dragActive ? 'Drop images here' : 'Click to upload or drag and drop'}\n          </p>\n          \n          <p style={{ \n            color: '#6b7280',\n            fontSize: '0.875rem'\n          }}>\n            PNG, JPG, GIF, WebP up to {(maxFileSize / (1024 * 1024)).toFixed(1)}MB\n            <br />\n            {totalImages}/{maxImages} images\n          </p>\n        </div>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div style={{\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '6px',\n          padding: '0.75rem',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'flex-start',\n          gap: '0.5rem'\n        }}>\n          <AlertCircle size={16} style={{ color: '#ef4444', marginTop: '0.125rem', flexShrink: 0 }} />\n          <div style={{ color: '#dc2626', fontSize: '0.875rem', whiteSpace: 'pre-line' }}>\n            {error}\n          </div>\n        </div>\n      )}\n\n      {/* Image Grid */}\n      {(existingImages.length > 0 || images.length > 0) && (\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',\n          gap: '1rem'\n        }}>\n          {/* Existing Images */}\n          {existingImages.map((image) => {\n            const isMarkedForDeletion = pendingDeletes.includes(image.attachment_id);\n\n            return (\n              <CalendarImageDisplay\n                key={`existing-${image.attachment_id}`}\n                image={image}\n                isMarkedForDeletion={isMarkedForDeletion}\n                onToggleDeletion={toggleExistingImageDeletion}\n                onSetPrimary={onSetPrimary}\n              />\n            );\n          })}\n\n          {/* New Images */}\n          {images.map((image) => (\n            <div\n              key={image.id}\n              style={{\n                position: 'relative',\n                aspectRatio: '1',\n                borderRadius: '8px',\n                overflow: 'hidden',\n                border: '1px solid #e5e7eb'\n              }}\n            >\n              <img\n                src={image.preview}\n                alt=\"Preview\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n              />\n              \n              <button\n                onClick={() => removeImage(image.id)}\n                style={{\n                  position: 'absolute',\n                  top: '4px',\n                  right: '4px',\n                  backgroundColor: 'rgba(239, 68, 68, 0.7)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  padding: '4px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}\n                title=\"Remove image\"\n              >\n                <X size={12} />\n              </button>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Empty State */}\n      {existingImages.length === 0 && images.length === 0 && !canAddMore && (\n        <div style={{\n          textAlign: 'center',\n          padding: '2rem',\n          color: '#6b7280'\n        }}>\n          <ImageIcon size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n          <p>No images uploaded</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CalendarImageUpload;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,MAAM,EAAEC,CAAC,EAAEC,IAAI,EAAEC,KAAK,IAAIC,SAAS,EAAEC,WAAW,QAAQ,cAAc;AAE/E,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQrD;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAEvDG,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGb,WAAW,CAACI,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;;QAEA;QACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,OAAO,EAAE;UACpCI,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIT,KAAK,CAAC,QAAQC,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CnB,WAAW,CAACoB,SAAS,CAAC;MACxB,CAAC,CAAC,OAAOG,GAAQ,EAAE;QACjBC,OAAO,CAACrB,KAAK,CAAC,8CAA8C,EAAEoB,GAAG,CAAC;QAClEnB,QAAQ,CAACmB,GAAG,CAACE,OAAO,IAAI,sBAAsB,CAAC;MACjD,CAAC,SAAS;QACRvB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,EAAE;QACZsB,GAAG,CAACK,eAAe,CAAC3B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA1DMF,cAAc;AA2DpB,MAAM+B,oBAKJ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,mBAAmB;EAAEC,gBAAgB;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EACvE,MAAM;IAAEjC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACgC,KAAK,CAACK,SAAS,CAAC;EAEpE,oBACEtC,OAAA;IACEuC,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAEX,KAAK,CAACY,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;MACpEC,OAAO,EAAEZ,mBAAmB,GAAG,GAAG,GAAG,CAAC;MACtCa,MAAM,EAAEb,mBAAmB,GAAG,iBAAiB,GAAG,MAAM;MACxDc,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,GAGD3C,OAAO,iBACNN,OAAA;MAAKuC,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBU,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBN,eAAe,EAAE,SAAS;QAC1BO,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE;MACX,CAAE;MAAAT,QAAA,gBACAjD,OAAA,CAACJ,SAAS;QAAC+D,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvB/D,OAAA;QAAKuC,KAAK,EAAE;UAAEyB,SAAS,EAAE;QAAM,CAAE;QAAAf,QAAA,EAAC;MAAU;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACN,EAGA,CAACvD,KAAK,IAAIF,OAAO,kBAChBN,OAAA;MAAKuC,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBU,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBN,eAAe,EAAE,SAAS;QAC1BO,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,SAAS;QACnBC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE;MACX,CAAE;MAAAT,QAAA,gBACAjD,OAAA;QAAKuC,KAAK,EAAE;UAAEiB,QAAQ,EAAE,QAAQ;UAAES,YAAY,EAAE;QAAM,CAAE;QAAAhB,QAAA,EAAC;MAAC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChE/D,OAAA;QAAKuC,KAAK,EAAE;UAAE2B,SAAS,EAAE;QAAY,CAAE;QAAAjB,QAAA,GAAC,kBACtB,eAAAjD,OAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAAC9B,KAAK,CAACkC,SAAS;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA3D,QAAQ,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,iBAC7BR,OAAA;MACEoE,GAAG,EAAEhE,QAAS;MACdiE,GAAG,EAAEpC,KAAK,CAACkC,SAAU;MACrB5B,KAAK,EAAE;QACL+B,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE;MACb;IAAE;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eAGD/D,OAAA;MAAKuC,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBiC,GAAG,EAAE,KAAK;QACVC,KAAK,EAAE,KAAK;QACZvB,OAAO,EAAE,MAAM;QACfwB,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,GAEC,CAAChB,KAAK,CAACY,UAAU,IAAI,CAACX,mBAAmB,IAAIE,YAAY,iBACxDpC,OAAA;QACE4E,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnB3C,YAAY,CAACH,KAAK,CAAC+C,aAAa,CAAC;QACnC,CAAE;QACFzC,KAAK,EAAE;UACLS,eAAe,EAAE,wBAAwB;UACzCO,KAAK,EAAE,OAAO;UACdX,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnBgB,OAAO,EAAE,KAAK;UACduB,MAAM,EAAE,SAAS;UACjB9B,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QACF4B,KAAK,EAAC,gBAAgB;QAAAjC,QAAA,eAEtBjD,OAAA,CAACN,IAAI;UAACiE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACT,eAGD/D,OAAA;QACE4E,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnB5C,gBAAgB,CAACF,KAAK,CAAC+C,aAAa,CAAC;QACvC,CAAE;QACFzC,KAAK,EAAE;UACLS,eAAe,EAAEd,mBAAmB,GAAG,wBAAwB,GAAG,wBAAwB;UAC1FqB,KAAK,EAAE,OAAO;UACdX,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnBgB,OAAO,EAAE,KAAK;UACduB,MAAM,EAAE,SAAS;UACjB9B,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QACF4B,KAAK,EAAEhD,mBAAmB,GAAG,aAAa,GAAG,mBAAoB;QAAAe,QAAA,eAEjEjD,OAAA,CAACP,CAAC;UAACkE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAKL7B,mBAAmB,iBAClBlC,OAAA;MAAKuC,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBU,KAAK,EAAE,CAAC;QACRF,eAAe,EAAE,wBAAwB;QACzCG,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,UAAU;QACpB2B,UAAU,EAAE;MACd,CAAE;MAAAlC,QAAA,EAAC;IAEH;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1B,GAAA,CAzJIL,oBAKJ;EAAA,QACqC/B,cAAc;AAAA;AAAAmF,EAAA,GAN/CpD,oBAKJ;AAqKF,MAAMqD,mBAAuD,GAAGA,CAAC;EAC/DC,cAAc;EACdC,cAAc,GAAG,EAAE;EACnBnD,YAAY;EACZoD,SAAS,GAAG,EAAE;EACdC,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EAC/BC,aAAa,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACtEC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG,KAAK;EAChBC,cAAc,GAAG,EAAE;EACnBC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9G,QAAQ,CAAc,EAAE,CAAC;EACrD,MAAM,CAAC+G,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMiH,YAAY,GAAGhH,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMiH,WAAW,GAAGjH,MAAM,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAMkH,YAAY,GAAGjH,WAAW,CAAEkH,IAAU,IAAoB;IAC9D,IAAI,CAACd,aAAa,CAACe,QAAQ,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACtC,OAAO,sCAAsChB,aAAa,CAACiB,IAAI,CAAC,IAAI,CAAC,EAAE;IACzE;IACA,IAAIH,IAAI,CAAC7C,IAAI,GAAG8B,WAAW,EAAE;MAC3B,OAAO,sCAAsC,CAACA,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEmB,OAAO,CAAC,CAAC,CAAC,IAAI;IAC3F;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAAClB,aAAa,EAAED,WAAW,CAAC,CAAC;;EAEhC;EACA,MAAMoB,YAAY,GAAGvH,WAAW,CAAEwH,QAAkB,IAAK;IACvD,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC;IAClC,MAAMI,WAAW,GAAGjB,MAAM,CAACkB,MAAM,GAAG5B,cAAc,CAAC4B,MAAM;IAEzD,IAAID,WAAW,GAAGH,KAAK,CAACI,MAAM,GAAG3B,SAAS,EAAE;MAC1C/E,QAAQ,CAAC,WAAW+E,SAAS,gCAAgCA,SAAS,GAAG0B,WAAW,QAAQ,CAAC;MAC7F;IACF;IAEA,MAAME,UAAkB,GAAG,EAAE;IAC7B,MAAMC,MAAgB,GAAG,EAAE;IAE3BN,KAAK,CAACO,OAAO,CAACd,IAAI,IAAI;MACpB,MAAMe,eAAe,GAAGhB,YAAY,CAACC,IAAI,CAAC;MAC1C,IAAIe,eAAe,EAAE;QACnBF,MAAM,CAACG,IAAI,CAAC,GAAGhB,IAAI,CAACiB,IAAI,KAAKF,eAAe,EAAE,CAAC;MACjD,CAAC,MAAM;QACLH,UAAU,CAACI,IAAI,CAAChB,IAAI,CAAC;MACvB;IACF,CAAC,CAAC;IAEF,IAAIa,MAAM,CAACF,MAAM,GAAG,CAAC,EAAE;MACrB1G,QAAQ,CAAC4G,MAAM,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3B;IACF;IAEAlG,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMiH,SAAsB,GAAGN,UAAU,CAACO,GAAG,CAACnB,IAAI,KAAK;MACrDoB,EAAE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;MACpCxB,IAAI;MACJyB,OAAO,EAAEvG,GAAG,CAACC,eAAe,CAAC6E,IAAI;IACnC,CAAC,CAAC,CAAC;IAEHN,SAAS,CAACgC,IAAI,IAAI;MAChB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,EAAE,GAAGR,SAAS,CAAC;MACvCpC,cAAc,CAAC6C,OAAO,CAACR,GAAG,CAACS,GAAG,IAAIA,GAAG,CAAC5B,IAAI,CAAC,CAAC;MAC5C,OAAO2B,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClC,MAAM,CAACkB,MAAM,EAAE5B,cAAc,CAAC4B,MAAM,EAAE3B,SAAS,EAAEe,YAAY,EAAEjB,cAAc,CAAC,CAAC;;EAEnF;EACA,MAAM+C,gBAAgB,GAAG/I,WAAW,CAAEuF,CAAsC,IAAK;IAC/E,IAAIA,CAAC,CAACyD,MAAM,CAACvB,KAAK,IAAIlC,CAAC,CAACyD,MAAM,CAACvB,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MAC/CN,YAAY,CAAChC,CAAC,CAACyD,MAAM,CAACvB,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0B,eAAe,GAAGjJ,WAAW,CAAEuF,CAAkB,IAAK;IAC1DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBuB,WAAW,CAACkC,OAAO,EAAE;IACrB,IAAI3D,CAAC,CAAC4D,YAAY,CAACC,KAAK,IAAI7D,CAAC,CAAC4D,YAAY,CAACC,KAAK,CAACvB,MAAM,GAAG,CAAC,EAAE;MAC3Df,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuC,eAAe,GAAGrJ,WAAW,CAAEuF,CAAkB,IAAK;IAC1DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBuB,WAAW,CAACkC,OAAO,EAAE;IACrB,IAAIlC,WAAW,CAACkC,OAAO,KAAK,CAAC,EAAE;MAC7BpC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwC,cAAc,GAAGtJ,WAAW,CAAEuF,CAAkB,IAAK;IACzDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8D,UAAU,GAAGvJ,WAAW,CAAEuF,CAAkB,IAAK;IACrDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBqB,aAAa,CAAC,KAAK,CAAC;IACpBE,WAAW,CAACkC,OAAO,GAAG,CAAC;IAEvB,IAAI3D,CAAC,CAAC4D,YAAY,CAAC1B,KAAK,IAAIlC,CAAC,CAAC4D,YAAY,CAAC1B,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MAC3DN,YAAY,CAAChC,CAAC,CAAC4D,YAAY,CAAC1B,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMiC,WAAW,GAAGxJ,WAAW,CAAEyJ,OAAe,IAAK;IACnD7C,SAAS,CAACgC,IAAI,IAAI;MAChB,MAAMC,OAAO,GAAGD,IAAI,CAACnF,MAAM,CAACqF,GAAG,IAAIA,GAAG,CAACR,EAAE,KAAKmB,OAAO,CAAC;MACtD;MACA,MAAMC,YAAY,GAAGd,IAAI,CAACe,IAAI,CAACb,GAAG,IAAIA,GAAG,CAACR,EAAE,KAAKmB,OAAO,CAAC;MACzD,IAAIC,YAAY,EAAE;QAChBtH,GAAG,CAACK,eAAe,CAACiH,YAAY,CAACf,OAAO,CAAC;MAC3C;MAEA3C,cAAc,CAAC6C,OAAO,CAACR,GAAG,CAACS,GAAG,IAAIA,GAAG,CAAC5B,IAAI,CAAC,CAAC;MAC5C,OAAO2B,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7C,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM4D,2BAA2B,GAAG5J,WAAW,CAAE6J,YAAoB,IAAK;IACxE,MAAMjH,mBAAmB,GAAG2D,cAAc,CAACY,QAAQ,CAAC0C,YAAY,CAAC;IAEjE,IAAIjH,mBAAmB,EAAE;MACvBL,OAAO,CAACuH,GAAG,CAAC,kCAAkC,EAAED,YAAY,CAAC;MAC7DpD,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAGoD,YAAY,CAAC;IACrC,CAAC,MAAM;MACLtH,OAAO,CAACuH,GAAG,CAAC,iCAAiC,EAAED,YAAY,CAAC;MAC5DrD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGqD,YAAY,CAAC;IACnC;EACF,CAAC,EAAE,CAACtD,cAAc,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAMsD,eAAe,GAAG/J,WAAW,CAAE6J,YAAoB,IAAK;IAC5D,IAAI/G,YAAY,EAAE;MAChBA,YAAY,CAAC+G,YAAY,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC/G,YAAY,CAAC,CAAC;;EAElB;EACAjD,KAAK,CAACI,SAAS,CAAC,MAAM;IACpB,IAAIiB,KAAK,EAAE;MACT,MAAM8I,KAAK,GAAGC,UAAU,CAAC,MAAM9I,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MACpD,OAAO,MAAM+I,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC9I,KAAK,CAAC,CAAC;;EAEX;EACArB,KAAK,CAACI,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX0G,MAAM,CAACqB,OAAO,CAACc,GAAG,IAAI1G,GAAG,CAACK,eAAe,CAACqG,GAAG,CAACH,OAAO,CAAC,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMf,WAAW,GAAGjB,MAAM,CAACkB,MAAM,GAAG5B,cAAc,CAAC4B,MAAM;EACzD,MAAMsC,UAAU,GAAGvC,WAAW,GAAG1B,SAAS,IAAI,CAACI,QAAQ;EAEvD,oBACE5F,OAAA;IAAK2F,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAAA1C,QAAA,GAElDwG,UAAU,iBACTzJ,OAAA;MACE2F,SAAS,EAAE,eAAeQ,UAAU,GAAG,aAAa,GAAG,EAAE,IAAIP,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;MAC1F8D,WAAW,EAAEnB,eAAgB;MAC7BoB,WAAW,EAAEhB,eAAgB;MAC7BiB,UAAU,EAAEhB,cAAe;MAC3BiB,MAAM,EAAEhB,UAAW;MACnBjE,OAAO,EAAEA,CAAA;QAAA,IAAAkF,qBAAA;QAAA,OAAM,CAAClE,QAAQ,MAAAkE,qBAAA,GAAIzD,YAAY,CAACmC,OAAO,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;MAAA,CAAC;MAC1DxH,KAAK,EAAE;QACLK,MAAM,EAAE,oBAAoB;QAC5BF,YAAY,EAAE,KAAK;QACnBgB,OAAO,EAAE,MAAM;QACfD,SAAS,EAAE,QAAQ;QACnBwB,MAAM,EAAEW,QAAQ,GAAG,aAAa,GAAG,SAAS;QAC5C5C,eAAe,EAAEmD,UAAU,GAAG,SAAS,GAAGP,QAAQ,GAAG,SAAS,GAAG,OAAO;QACxEoE,WAAW,EAAE7D,UAAU,GAAG,SAAS,GAAG3F,KAAK,GAAG,SAAS,GAAG,SAAS;QACnEyD,YAAY,EAAE;MAChB,CAAE;MAAAhB,QAAA,gBAEFjD,OAAA;QACEiK,GAAG,EAAE5D,YAAa;QAClBK,IAAI,EAAC,MAAM;QACXwD,QAAQ;QACRC,MAAM,EAAEzE,aAAa,CAACiB,IAAI,CAAC,GAAG,CAAE;QAChCyD,QAAQ,EAAE/B,gBAAiB;QAC3B9F,KAAK,EAAE;UAAEY,OAAO,EAAE;QAAO,CAAE;QAC3ByC,QAAQ,EAAEA;MAAS;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEF/D,OAAA,CAACR,MAAM;QACLmE,IAAI,EAAE,EAAG;QACTpB,KAAK,EAAE;UACLgB,KAAK,EAAE4C,UAAU,GAAG,SAAS,GAAGP,QAAQ,GAAG,SAAS,GAAG,SAAS;UAChE3B,YAAY,EAAE;QAChB;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEF/D,OAAA;QAAGuC,KAAK,EAAE;UACRgB,KAAK,EAAEqC,QAAQ,GAAG,SAAS,GAAG,SAAS;UACvCpC,QAAQ,EAAE,MAAM;UAChB2B,UAAU,EAAE,KAAK;UACjBlB,YAAY,EAAE;QAChB,CAAE;QAAAhB,QAAA,EACCkD,UAAU,GAAG,kBAAkB,GAAG;MAAkC;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eAEJ/D,OAAA;QAAGuC,KAAK,EAAE;UACRgB,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE;QACZ,CAAE;QAAAP,QAAA,GAAC,4BACyB,EAAC,CAACwC,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEmB,OAAO,CAAC,CAAC,CAAC,EAAC,IACpE,eAAA5G,OAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACLmD,WAAW,EAAC,GAAC,EAAC1B,SAAS,EAAC,SAC3B;MAAA;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGAvD,KAAK,iBACJR,OAAA;MAAKuC,KAAK,EAAE;QACVS,eAAe,EAAE,SAAS;QAC1BJ,MAAM,EAAE,mBAAmB;QAC3BF,YAAY,EAAE,KAAK;QACnBgB,OAAO,EAAE,SAAS;QAClBO,YAAY,EAAE,MAAM;QACpBd,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,YAAY;QACxBsB,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,gBACAjD,OAAA,CAACH,WAAW;QAAC8D,IAAI,EAAE,EAAG;QAACpB,KAAK,EAAE;UAAEgB,KAAK,EAAE,SAAS;UAAES,SAAS,EAAE,UAAU;UAAEqG,UAAU,EAAE;QAAE;MAAE;QAAAzG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5F/D,OAAA;QAAKuC,KAAK,EAAE;UAAEgB,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,UAAU;UAAE8G,UAAU,EAAE;QAAW,CAAE;QAAArH,QAAA,EAC5EzC;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAACwB,cAAc,CAAC4B,MAAM,GAAG,CAAC,IAAIlB,MAAM,CAACkB,MAAM,GAAG,CAAC,kBAC9CnH,OAAA;MAAKuC,KAAK,EAAE;QACVY,OAAO,EAAE,MAAM;QACfoH,mBAAmB,EAAE,uCAAuC;QAC5D5F,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,GAECsC,cAAc,CAACoC,GAAG,CAAE1F,KAAK,IAAK;QAC7B,MAAMC,mBAAmB,GAAG2D,cAAc,CAACY,QAAQ,CAACxE,KAAK,CAAC+C,aAAa,CAAC;QAExE,oBACEhF,OAAA,CAACgC,oBAAoB;UAEnBC,KAAK,EAAEA,KAAM;UACbC,mBAAmB,EAAEA,mBAAoB;UACzCC,gBAAgB,EAAE+G,2BAA4B;UAC9C9G,YAAY,EAAEA;QAAa,GAJtB,YAAYH,KAAK,CAAC+C,aAAa,EAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKvC,CAAC;MAEN,CAAC,CAAC,EAGDkC,MAAM,CAAC0B,GAAG,CAAE1F,KAAK,iBAChBjC,OAAA;QAEEuC,KAAK,EAAE;UACLC,QAAQ,EAAE,UAAU;UACpBC,WAAW,EAAE,GAAG;UAChBC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;QACV,CAAE;QAAAK,QAAA,gBAEFjD,OAAA;UACEoE,GAAG,EAAEnC,KAAK,CAACgG,OAAQ;UACnB5D,GAAG,EAAC,SAAS;UACb9B,KAAK,EAAE;YACL+B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE;UACb;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF/D,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAMkE,WAAW,CAAC7G,KAAK,CAAC2F,EAAE,CAAE;UACrCrF,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBiC,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE,KAAK;YACZ1B,eAAe,EAAE,wBAAwB;YACzCO,KAAK,EAAE,OAAO;YACdX,MAAM,EAAE,MAAM;YACdF,YAAY,EAAE,KAAK;YACnBgB,OAAO,EAAE,KAAK;YACduB,MAAM,EAAE,SAAS;YACjB9B,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UACF4B,KAAK,EAAC,cAAc;UAAAjC,QAAA,eAEpBjD,OAAA,CAACP,CAAC;YAACkE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA,GAtCJ9B,KAAK,CAAC2F,EAAE;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCV,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAwB,cAAc,CAAC4B,MAAM,KAAK,CAAC,IAAIlB,MAAM,CAACkB,MAAM,KAAK,CAAC,IAAI,CAACsC,UAAU,iBAChEzJ,OAAA;MAAKuC,KAAK,EAAE;QACVkB,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,MAAM;QACfH,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,gBACAjD,OAAA,CAACJ,SAAS;QAAC+D,IAAI,EAAE,EAAG;QAACpB,KAAK,EAAE;UAAE0B,YAAY,EAAE,MAAM;UAAEnB,OAAO,EAAE;QAAI;MAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtE/D,OAAA;QAAAiD,QAAA,EAAG;MAAkB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiC,GAAA,CAxUIX,mBAAuD;AAAAmF,GAAA,GAAvDnF,mBAAuD;AA0U7D,eAAeA,mBAAmB;AAAC,IAAAD,EAAA,EAAAoF,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}