{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\NewsFeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect, useContext, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\n\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminAuthContext from '../../contexts/AdminAuthContext';\nimport StudentAuthContext from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart,\n// Edit, // Not used in unified component\nUsers, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane, ChevronDown, User, LogOut } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = (imagePath, userRole) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const currentBlobUrl = useRef(null);\n  useEffect(() => {\n    // Cleanup previous blob URL if it exists\n    if (currentBlobUrl.current) {\n      URL.revokeObjectURL(currentBlobUrl.current);\n      currentBlobUrl.current = null;\n    }\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n\n        // Store the blob URL for cleanup\n        currentBlobUrl.current = objectUrl;\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup function\n    return () => {\n      if (currentBlobUrl.current) {\n        URL.revokeObjectURL(currentBlobUrl.current);\n        currentBlobUrl.current = null;\n      }\n    };\n  }, [imagePath, userRole]); // Fixed: removed imageUrl to prevent infinite loop\n\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"hHzKct3MDLbHRCNtj2pkZxlcZjY=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath, userRole);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  userRole,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = () => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(),\n        userRole: userRole,\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(),\n            userRole: userRole,\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n\n// Props interface for the unified NewsFeed component\n_c2 = ImageGallery;\n// Main unified NewsFeed Component\nconst NewsFeed = ({\n  userRole\n}) => {\n  _s3();\n  var _currentUser$firstNam2, _currentUser$lastName2;\n  const navigate = useNavigate();\n\n  // Safely get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useContext(AdminAuthContext);\n  const studentAuth = useContext(StudentAuthContext);\n\n  // Determine current user role and context\n  const currentRole = userRole || (adminAuth !== null && adminAuth !== void 0 && adminAuth.isAuthenticated ? 'admin' : studentAuth !== null && studentAuth !== void 0 && studentAuth.isAuthenticated ? 'student' : null);\n  const currentUser = currentRole === 'admin' ? adminAuth === null || adminAuth === void 0 ? void 0 : adminAuth.user : studentAuth === null || studentAuth === void 0 ? void 0 : studentAuth.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth === null || adminAuth === void 0 ? void 0 : adminAuth.logout : studentAuth === null || studentAuth === void 0 ? void 0 : studentAuth.logout;\n\n  // All hooks must be called before any early returns\n  const {\n    categories\n  } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const {\n    isFromNotification,\n    scrollTarget\n  } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [showCalendarComments, setShowCalendarComments] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = useCallback(async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const authToken = currentRole === 'admin' ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY) : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${authToken}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  }, [currentRole]);\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter(ann => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (showUserDropdown) {\n        const target = event.target;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#ef4444',\n            marginBottom: '1rem'\n          },\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            marginBottom: '1.5rem'\n          },\n          children: \"Please log in to access the newsfeed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          style: {\n            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            padding: '0.75rem 1.5rem',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async announcement => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId, currentlyLiked) => {\n    const client = currentRole === 'admin' ? adminHttpClient : studentHttpClient;\n    const endpoint = `/api/calendar/${eventId}/like`;\n    console.log(`[DEBUG] ${currentRole} making direct API call to:`, endpoint);\n    console.log(`[DEBUG] Using ${currentRole} HTTP client`);\n    if (currentlyLiked) {\n      // Unlike the event\n      return await client.delete(endpoint);\n    } else {\n      // Like the event\n      return await client.post(endpoint, {});\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async event => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, {\n        id: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n        role: currentRole\n      });\n      const response = await toggleCalendarReaction(event.calendar_id, event.user_has_reacted || false);\n      if (response.success) {\n        const newReactionState = !event.user_has_reacted;\n        const newReactionCount = event.user_has_reacted ? Math.max(0, (event.reaction_count || 0) - 1) : (event.reaction_count || 0) + 1;\n        console.log(`[DEBUG] ${currentRole} updating local state:`, {\n          eventId: event.calendar_id,\n          oldReactionState: event.user_has_reacted,\n          newReactionState,\n          oldCount: event.reaction_count,\n          newCount: newReactionCount,\n          userId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n          userRole: currentRole\n        });\n\n        // Update the local state\n        setCalendarEvents(prevEvents => prevEvents.map(e => e.calendar_id === event.calendar_id ? {\n          ...e,\n          user_has_reacted: newReactionState,\n          reaction_count: newReactionCount\n        } : e));\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      if (currentLogout) {\n        await currentLogout();\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' + String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' + String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = event.is_active !== 0;\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search post\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 19\n                }, this), categories.filter(category =>\n                // Hide holiday categories from dropdown\n                !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)).map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(StudentNotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 67\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                \"data-dropdown\": \"user-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowUserDropdown(!showUserDropdown),\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    padding: '0.75rem 1rem',\n                    background: 'white',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: [currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getImageUrl(currentUser.profilePicture) || '',\n                    alt: `${currentUser.firstName} ${currentUser.lastName}`,\n                    style: {\n                      width: '24px',\n                      height: '24px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '1px solid #e5e7eb'\n                    },\n                    onError: e => {\n                      const target = e.target;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        const userIcon = parent.querySelector('.user-icon');\n                        if (userIcon) {\n                          userIcon.style.display = 'block';\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1068,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(User, {\n                    size: 16,\n                    className: \"user-icon\",\n                    style: {\n                      display: currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? 'none' : 'block'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentRole === 'admin' ? 'Admin' : 'Student')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1098,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                    size: 14,\n                    style: {\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1099,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 19\n                }, this), showUserDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '100%',\n                    right: 0,\n                    marginTop: '0.5rem',\n                    background: 'white',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '12px',\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                    minWidth: '200px',\n                    zIndex: 1000,\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.75rem 1rem',\n                      borderBottom: '1px solid #f3f4f6',\n                      background: '#f9fafb'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [currentUser !== null && currentUser !== void 0 && currentUser.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: getImageUrl(currentUser.profilePicture) || '',\n                        alt: `${currentUser.firstName} ${currentUser.lastName}`,\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '50%',\n                          objectFit: 'cover',\n                          border: '2px solid #e5e7eb',\n                          flexShrink: 0\n                        },\n                        onError: e => {\n                          const target = e.target;\n                          target.style.display = 'none';\n                          const parent = target.parentElement;\n                          if (parent) {\n                            var _currentUser$firstNam, _currentUser$lastName;\n                            parent.innerHTML = `\n                                    <div style=\"\n                                      width: 40px;\n                                      height: 40px;\n                                      border-radius: 50%;\n                                      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                                      display: flex;\n                                      align-items: center;\n                                      justify-content: center;\n                                      color: white;\n                                      font-weight: 600;\n                                      font-size: 1rem;\n                                      flex-shrink: 0;\n                                    \">\n                                      ${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam = currentUser.firstName) === null || _currentUser$firstNam === void 0 ? void 0 : _currentUser$firstNam.charAt(0)) || ''}${(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName = currentUser.lastName) === null || _currentUser$lastName === void 0 ? void 0 : _currentUser$lastName.charAt(0)) || ''}\n                                    </div>\n                                  `;\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1133,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '40px',\n                          height: '40px',\n                          borderRadius: '50%',\n                          background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: 'white',\n                          fontWeight: '600',\n                          fontSize: '1rem',\n                          flexShrink: 0\n                        },\n                        children: [(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$firstNam2 = currentUser.firstName) === null || _currentUser$firstNam2 === void 0 ? void 0 : _currentUser$firstNam2.charAt(0)) || '', (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$lastName2 = currentUser.lastName) === null || _currentUser$lastName2 === void 0 ? void 0 : _currentUser$lastName2.charAt(0)) || '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1170,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827'\n                          },\n                          children: [currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName, \" \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.lastName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1188,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '0.75rem',\n                            color: '#6b7280'\n                          },\n                          children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1195,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1187,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '0.5rem 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                        navigate(dashboardPath);\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#374151',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#f3f4f6';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1232,\n                        columnNumber: 27\n                      }, this), \"Dashboard\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1206,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        handleLogout();\n                        setShowUserDropdown(false);\n                      },\n                      style: {\n                        width: '100%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        padding: '0.75rem 1rem',\n                        background: 'transparent',\n                        border: 'none',\n                        color: '#ef4444',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = '#fef2f2';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'transparent';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1261,\n                        columnNumber: 27\n                      }, this), \"Logout\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1236,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 830,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map(announcement => {\n                  // Handle alert announcements with special styling\n                  const isAlert = announcement.is_alert;\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: isAlert ? '#fef2f2' : '#f8fafc',\n                      borderRadius: '12px',\n                      border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                      e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                      e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: isAlert ? '#ef4444' : categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1362,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1371,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1380,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1397,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1398,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1390,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1370,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1357,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1336,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1471,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1457,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1451,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                refreshAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1484,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1565,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1555,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1575,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1551,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1673,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1663,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.category_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1702,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1689,\n                          columnNumber: 33\n                        }, this);\n                      })(), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px',\n                          fontSize: '0.75rem',\n                          color: '#3b82f6'\n                        },\n                        children: [event.created_by_picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getImageUrl(event.created_by_picture) || '',\n                          alt: event.created_by_name,\n                          style: {\n                            width: '16px',\n                            height: '16px',\n                            borderRadius: '50%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            const target = e.target;\n                            target.style.display = 'none';\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1721,\n                          columnNumber: 35\n                        }, this) : /*#__PURE__*/_jsxDEV(User, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1736,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontWeight: '500'\n                          },\n                          children: event.created_by_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1738,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1710,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1751,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1744,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1677,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1761,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1676,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1657,\n                  columnNumber: 23\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = eventImageUrls.filter(Boolean);\n                        openLightboxWithUrls(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1792,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1791,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1807,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1834,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1827,\n                    columnNumber: 25\n                  }, this), event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.category_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1849,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1843,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1818,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCalendarLikeToggle(event),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: event.user_has_reacted ? '#ef4444' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Heart, {\n                      size: 18,\n                      fill: event.user_has_reacted ? '#ef4444' : 'none'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1895,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.reaction_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1899,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1872,\n                    columnNumber: 25\n                  }, this), event.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowCalendarComments(showCalendarComments === event.calendar_id ? null : event.calendar_id),\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      background: 'none',\n                      border: 'none',\n                      color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                      cursor: 'pointer',\n                      padding: '0.5rem',\n                      borderRadius: '8px',\n                      transition: 'all 0.2s ease',\n                      fontSize: '0.875rem',\n                      fontWeight: '500'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'none';\n                      e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1931,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.comment_count || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1932,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1904,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1863,\n                  columnNumber: 23\n                }, this), showCalendarComments === event.calendar_id && event.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1945,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    calendarId: event.calendar_id,\n                    allowComments: event.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1952,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1939,\n                  columnNumber: 25\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1636,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                id: `announcement-${announcement.announcement_id}`,\n                className: isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : '',\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2018,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2003,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2043,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2033,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2062,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2052,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2092,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2079,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2115,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2102,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2123,\n                        columnNumber: 31\n                      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          background: 'rgba(34, 197, 94, 0.1)',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px',\n                          fontSize: '0.75rem',\n                          color: '#16a34a'\n                        },\n                        children: [announcement.author_picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getImageUrl(announcement.author_picture) || '',\n                          alt: announcement.author_name,\n                          style: {\n                            width: '16px',\n                            height: '16px',\n                            borderRadius: '50%',\n                            objectFit: 'cover'\n                          },\n                          onError: e => {\n                            const target = e.target;\n                            target.style.display = 'none';\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2148,\n                          columnNumber: 35\n                        }, this) : /*#__PURE__*/_jsxDEV(User, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2163,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontWeight: '500'\n                          },\n                          children: announcement.author_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2165,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2137,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2171,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2069,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2184,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2068,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2024,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  userRole: currentRole,\n                  onImageClick: index => {\n                    openLightbox(announcement.attachments || [], index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2198,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2209,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2257,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2261,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2234,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2293,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2294,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2266,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2228,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2314,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || announcement.author_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2315,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2309,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2318,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2302,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2219,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: currentRole === 'admin' ? /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2340,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(CommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    currentUserType: \"student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2347,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2334,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1970,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1627,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2417,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2395,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              if (selectedPinnedPost.is_alert) {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2464,\n                    columnNumber: 25\n                  }, this), \"Alert\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2451,\n                  columnNumber: 23\n                }, this);\n              } else {\n                const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                const categoryStyle = getCategoryStyle(categoryName);\n                const IconComponent = categoryStyle.icon;\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: categoryStyle.background,\n                    color: 'white',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '20px',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                    size: 12,\n                    color: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2487,\n                    columnNumber: 25\n                  }, this), categoryName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2474,\n                  columnNumber: 23\n                }, this);\n              }\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2505,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2510,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              userRole: currentRole,\n              onImageClick: index => {\n                openLightbox(selectedPinnedPost.attachments, index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2523,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2522,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2552,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2561,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2543,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2441,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2383,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2368,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      isOpen: lightboxOpen,\n      onClose: () => setLightboxOpen(false),\n      altPrefix: \"Announcement Image\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2572,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 809,\n    columnNumber: 5\n  }, this);\n};\n_s3(NewsFeed, \"y8fPKTk+uoNrOOlXtCjiYGfZ6Ao=\", false, function () {\n  return [useNavigate, useCategories, useAnnouncements, useNotificationTarget];\n});\n_c3 = NewsFeed;\nexport default NewsFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"NewsFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useCallback", "useRef", "useNavigate", "adminHttpClient", "studentHttpClient", "useCategories", "useAnnouncements", "useNotificationTarget", "AdminAuthContext", "StudentAuthContext", "AdminCommentSection", "CommentSection", "NotificationBell", "StudentNotificationBell", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "ADMIN_AUTH_TOKEN_KEY", "STUDENT_AUTH_TOKEN_KEY", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "ChevronDown", "User", "LogOut", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "userRole", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "currentBlobUrl", "current", "URL", "revokeObjectURL", "loadImage", "fullUrl", "Error", "console", "log", "authToken", "localStorage", "getItem", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "createObjectURL", "err", "message", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "position", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "transition", "gap", "file_path", "currentTarget", "transform", "top", "left", "right", "bottom", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "_c2", "NewsFeed", "_s3", "_currentUser$firstNam2", "_currentUser$lastName2", "navigate", "adminAuth", "studentAuth", "currentRole", "isAuthenticated", "currentUser", "user", "currentLogout", "logout", "categories", "announcements", "likeAnnouncement", "unlikeAnnouncement", "refresh", "refreshAnnouncements", "page", "limit", "sort_by", "sort_order", "isFromNotification", "scrollTarget", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "showCalendarComments", "setShowCalendarComments", "selectedPinnedPost", "setSelectedPinnedPost", "showUserDropdown", "setShowUserDropdown", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "fetchCalendarEvents", "undefined", "data", "json", "success", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "pinned", "filter", "ann", "is_pinned", "handleClickOutside", "target", "closest", "document", "addEventListener", "removeEventListener", "minHeight", "background", "padding", "max<PERSON><PERSON><PERSON>", "openLightbox", "initialIndex", "imageUrls", "img", "Boolean", "openLightboxWithUrls", "getCategoryStyle", "categoryName", "styles", "icon", "getHolidayTypeStyle", "holidayTypeName", "handleLikeToggle", "announcement", "announcement_id", "user_reaction", "id", "role", "toggleCalendarReaction", "eventId", "currentlyLiked", "client", "endpoint", "delete", "post", "handleCalendarLikeToggle", "user_has_reacted", "newReactionState", "newReactionCount", "reaction_count", "oldReactionState", "oldCount", "newCount", "userId", "prevEvents", "handleLogout", "redirectPath", "href", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "todayDateString", "getFullYear", "String", "getMonth", "padStart", "getDate", "eventStartDate", "event_date", "eventStartDateString", "eventEndDateString", "end_date", "endDate", "isEventActive", "isActive", "is_active", "displayAnnouncements", "displayEvents", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "size", "type", "placeholder", "value", "onChange", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "profilePicture", "firstName", "lastName", "onError", "parent", "parentElement", "userIcon", "querySelector", "flexShrink", "_currentUser$firstNam", "_currentUser$lastName", "innerHTML", "char<PERSON>t", "email", "dashboardPath", "<PERSON><PERSON><PERSON><PERSON>", "is_alert", "category_name", "toUpperCase", "categoryStyle", "substring", "created_at", "toLocaleDateString", "opacity", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "created_by_name", "created_by_picture", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "push", "maxVisible", "filteredImages", "paddingTop", "fill", "allow_comments", "comment_count", "calendarId", "allowComments", "currentUserId", "currentUserType", "flexWrap", "author_name", "author_picture", "posted_by_name", "announcementId", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/NewsFeed.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\n// import { announcementService } from '../../services'; // Not used in unified component\nimport { calendarReactionService } from '../../services/calendarReactionService';\nimport { adminHttpClient, studentHttpClient } from '../../services/api.service';\nimport { useCategories, useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useNotificationTarget } from '../../hooks/useNotificationNavigation';\nimport AdminAuthContext from '../../contexts/AdminAuthContext';\nimport StudentAuthContext from '../../contexts/StudentAuthContext';\nimport AdminCommentSection from '../admin/AdminCommentSection';\nimport CommentSection from '../student/CommentSection';\nimport NotificationBell from '../admin/NotificationBell';\nimport StudentNotificationBell from '../student/NotificationBell';\nimport FacebookImageGallery from './FacebookImageGallery';\nimport ImageLightbox from './ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL, ADMIN_AUTH_TOKEN_KEY, STUDENT_AUTH_TOKEN_KEY } from '../../config/constants';\nimport '../../styles/notificationHighlight.css';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  // Edit, // Not used in unified component\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane,\n  ChevronDown,\n  User,\n  LogOut\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading (role-aware)\nconst useImageLoader = (imagePath: string | null, userRole?: 'admin' | 'student') => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const currentBlobUrl = useRef<string | null>(null);\n\n  useEffect(() => {\n    // Cleanup previous blob URL if it exists\n    if (currentBlobUrl.current) {\n      URL.revokeObjectURL(currentBlobUrl.current);\n      currentBlobUrl.current = null;\n    }\n\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Get the appropriate token based on user role\n        const authToken = userRole === 'admin'\n          ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n          : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${authToken}`,\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n\n        // Store the blob URL for cleanup\n        currentBlobUrl.current = objectUrl;\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup function\n    return () => {\n      if (currentBlobUrl.current) {\n        URL.revokeObjectURL(currentBlobUrl.current);\n        currentBlobUrl.current = null;\n      }\n    };\n  }, [imagePath, userRole]); // Fixed: removed imageUrl to prevent infinite loop\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  userRole?: 'admin' | 'student';\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  userRole,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath, userRole);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  userRole?: 'admin' | 'student';\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, userRole, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle()}\n          userRole={userRole}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle()}\n                  userRole={userRole}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Props interface for the unified NewsFeed component\ninterface NewsFeedProps {\n  userRole?: 'admin' | 'student'; // Optional prop to override role detection\n}\n\n// Main unified NewsFeed Component\nconst NewsFeed: React.FC<NewsFeedProps> = ({ userRole }) => {\n  const navigate = useNavigate();\n\n  // Safely get authentication contexts for both roles (must be called before any conditional logic)\n  const adminAuth = useContext(AdminAuthContext);\n  const studentAuth = useContext(StudentAuthContext);\n\n  // Determine current user role and context\n  const currentRole = userRole ||\n    (adminAuth?.isAuthenticated ? 'admin' :\n     studentAuth?.isAuthenticated ? 'student' : null);\n\n  const currentUser = currentRole === 'admin' ? adminAuth?.user : studentAuth?.user;\n  const currentLogout = currentRole === 'admin' ? adminAuth?.logout : studentAuth?.logout;\n\n  // All hooks must be called before any early returns\n  const { categories } = useCategories();\n\n  // Use the announcements hook for proper state management with role-based service\n  const {\n    announcements,\n    loading,\n    error,\n    likeAnnouncement,\n    unlikeAnnouncement,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 50,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, currentRole === 'admin'); // true for admin service, false for student service\n\n  // Handle notification-triggered navigation\n  const { isFromNotification, scrollTarget } = useNotificationTarget();\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [showCalendarComments, setShowCalendarComments] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  // Note: recentStudents and studentLoading state can be added later if needed\n\n  // Fetch calendar events function\n  const fetchCalendarEvents = useCallback(async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const authToken = currentRole === 'admin'\n        ? localStorage.getItem(ADMIN_AUTH_TOKEN_KEY)\n        : localStorage.getItem(STUDENT_AUTH_TOKEN_KEY);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${authToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${authToken}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  }, [currentRole]);\n\n  // Update pinned announcements when announcements change\n  useEffect(() => {\n    const pinned = announcements.filter((ann: any) => ann.is_pinned === 1);\n    setPinnedAnnouncements(pinned);\n  }, [announcements]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchCalendarEvents();\n    // Note: fetchRecentStudents functionality can be added later if needed for admin dashboard\n  }, [currentRole, fetchCalendarEvents]); // Re-fetch when role changes\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (showUserDropdown) {\n        const target = event.target as Element;\n        if (!target.closest('[data-dropdown=\"user-dropdown\"]')) {\n          setShowUserDropdown(false);\n        }\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, [showUserDropdown]);\n\n  // Early return if no valid role is detected (after all hooks)\n  if (!currentRole) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)'\n      }}>\n        <div style={{\n          background: 'white',\n          padding: '2rem',\n          borderRadius: '16px',\n          border: '1px solid #e5e7eb',\n          textAlign: 'center',\n          maxWidth: '400px'\n        }}>\n          <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>Authentication Required</h2>\n          <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>\n            Please log in to access the newsfeed.\n          </p>\n          <button\n            onClick={() => navigate('/')}\n            style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              color: 'white',\n              border: 'none',\n              borderRadius: '12px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Note: Duplicate useEffect and function removed - already defined above\n\n  // Handle like/unlike functionality (role-aware)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for announcement:`, announcement.announcement_id);\n      console.log('[DEBUG] Current user_reaction:', announcement.user_reaction);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      if (announcement.user_reaction) {\n        // Unlike the announcement\n        console.log(`[DEBUG] ${currentRole} removing reaction...`);\n        await unlikeAnnouncement(announcement.announcement_id);\n      } else {\n        // Like the announcement\n        console.log(`[DEBUG] ${currentRole} adding reaction...`);\n        await likeAnnouncement(announcement.announcement_id, 1);\n      }\n\n      console.log(`[SUCCESS] ${currentRole} reaction toggled successfully`);\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling like:`, error);\n    }\n  };\n\n  // Role-aware calendar reaction function\n  const toggleCalendarReaction = async (eventId: number, currentlyLiked: boolean) => {\n    const client = currentRole === 'admin' ? adminHttpClient : studentHttpClient;\n    const endpoint = `/api/calendar/${eventId}/like`;\n\n    console.log(`[DEBUG] ${currentRole} making direct API call to:`, endpoint);\n    console.log(`[DEBUG] Using ${currentRole} HTTP client`);\n\n    if (currentlyLiked) {\n      // Unlike the event\n      return await client.delete(endpoint);\n    } else {\n      // Like the event\n      return await client.post(endpoint, {});\n    }\n  };\n\n  // Handle calendar event like/unlike functionality\n  const handleCalendarLikeToggle = async (event: any) => {\n    try {\n      console.log(`[DEBUG] ${currentRole} toggling reaction for calendar event:`, event.calendar_id);\n      console.log('[DEBUG] Current user_has_reacted:', event.user_has_reacted);\n      console.log(`[DEBUG] ${currentRole} user context:`, { id: currentUser?.id, role: currentRole });\n\n      const response = await toggleCalendarReaction(event.calendar_id, event.user_has_reacted || false);\n\n      if (response.success) {\n        const newReactionState = !event.user_has_reacted;\n        const newReactionCount = event.user_has_reacted\n          ? Math.max(0, (event.reaction_count || 0) - 1)\n          : (event.reaction_count || 0) + 1;\n\n        console.log(`[DEBUG] ${currentRole} updating local state:`, {\n          eventId: event.calendar_id,\n          oldReactionState: event.user_has_reacted,\n          newReactionState,\n          oldCount: event.reaction_count,\n          newCount: newReactionCount,\n          userId: currentUser?.id,\n          userRole: currentRole\n        });\n\n        // Update the local state\n        setCalendarEvents(prevEvents =>\n          prevEvents.map(e =>\n            e.calendar_id === event.calendar_id\n              ? {\n                  ...e,\n                  user_has_reacted: newReactionState,\n                  reaction_count: newReactionCount\n                }\n              : e\n          )\n        );\n        console.log(`[SUCCESS] ${currentRole} calendar event reaction toggled successfully`);\n      }\n    } catch (error) {\n      console.error(`[ERROR] ${currentRole} error toggling calendar like:`, error);\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      if (currentLogout) {\n        await currentLogout();\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Force redirect even if logout fails\n      const redirectPath = currentRole === 'admin' ? '/admin/login' : '/student/login';\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Note: Duplicate useEffect for dropdown removed - already defined above\n\n\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Show events that are currently active (between start and end date)\n    // Use local dates to avoid timezone issues (same logic as StudentNewsfeed)\n    const today = new Date();\n    const todayDateString = today.getFullYear() + '-' +\n      String(today.getMonth() + 1).padStart(2, '0') + '-' +\n      String(today.getDate()).padStart(2, '0');\n\n    const eventStartDate = new Date(event.event_date);\n    const eventStartDateString = eventStartDate.getFullYear() + '-' +\n      String(eventStartDate.getMonth() + 1).padStart(2, '0') + '-' +\n      String(eventStartDate.getDate()).padStart(2, '0');\n\n    // If event has an end date, use it; otherwise, show for the event date only\n    const eventEndDateString = event.end_date ? (() => {\n      const endDate = new Date(event.end_date);\n      return endDate.getFullYear() + '-' +\n        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +\n        String(endDate.getDate()).padStart(2, '0');\n    })() : eventStartDateString;\n\n    // Event is active if today is between start and end date (inclusive)\n    const isEventActive = todayDateString >= eventStartDateString && todayDateString <= eventEndDateString;\n\n    // For admins, show both published and unpublished events (but only currently active events)\n    const isActive = (event as any).is_active !== 0;\n\n    return matchesSearch && isEventActive && isActive;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n\n\n  // Create combined content array for better chronological display (for future use)\n  // const combinedContent = [\n  //   ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n  //   ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  // ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                 {/* ill comment this for now and uncomment it soon */}\n                  {/* {currentRole === 'admin' ? 'Admin Newsfeed' : 'Student Newsfeed'} */}\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories\n                    .filter(category =>\n                      // Hide holiday categories from dropdown\n                      !['Philippine Holidays', 'International Holidays', 'Religious Holidays'].includes(category.name)\n                    )\n                    .map(category => (\n                      <option key={category.category_id} value={category.category_id.toString()}>\n                        {category.name}\n                      </option>\n                    ))\n                  }\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Right Side Actions */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                {/* Notification Bell - Role-aware */}\n                {currentRole === 'admin' ? <NotificationBell /> : <StudentNotificationBell />}\n\n                {/* User Dropdown */}\n                <div style={{ position: 'relative' }} data-dropdown=\"user-dropdown\">\n                  <button\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      padding: '0.75rem 1rem',\n                      background: 'white',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '12px',\n                      color: '#374151',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = '#22c55e';\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#d1d5db';\n                      e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    {/* Profile Picture */}\n                    {currentUser?.profilePicture ? (\n                      <img\n                        src={getImageUrl(currentUser.profilePicture) || ''}\n                        alt={`${currentUser.firstName} ${currentUser.lastName}`}\n                        style={{\n                          width: '24px',\n                          height: '24px',\n                          borderRadius: '50%',\n                          objectFit: 'cover',\n                          border: '1px solid #e5e7eb'\n                        }}\n                        onError={(e) => {\n                          const target = e.target as HTMLImageElement;\n                          target.style.display = 'none';\n                          const parent = target.parentElement;\n                          if (parent) {\n                            const userIcon = parent.querySelector('.user-icon');\n                            if (userIcon) {\n                              (userIcon as HTMLElement).style.display = 'block';\n                            }\n                          }\n                        }}\n                      />\n                    ) : null}\n                    <User\n                      size={16}\n                      className=\"user-icon\"\n                      style={{\n                        display: currentUser?.profilePicture ? 'none' : 'block'\n                      }}\n                    />\n                    <span>{currentUser?.firstName || (currentRole === 'admin' ? 'Admin' : 'Student')}</span>\n                    <ChevronDown size={14} style={{\n                      transform: showUserDropdown ? 'rotate(180deg)' : 'rotate(0deg)',\n                      transition: 'transform 0.2s ease'\n                    }} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {showUserDropdown && (\n                    <div style={{\n                      position: 'absolute',\n                      top: '100%',\n                      right: 0,\n                      marginTop: '0.5rem',\n                      background: 'white',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',\n                      minWidth: '200px',\n                      zIndex: 1000,\n                      overflow: 'hidden'\n                    }}>\n                      <div style={{\n                        padding: '0.75rem 1rem',\n                        borderBottom: '1px solid #f3f4f6',\n                        background: '#f9fafb'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          marginBottom: '0.5rem'\n                        }}>\n                          {/* Profile Picture */}\n                          {currentUser?.profilePicture ? (\n                            <img\n                              src={getImageUrl(currentUser.profilePicture) || ''}\n                              alt={`${currentUser.firstName} ${currentUser.lastName}`}\n                              style={{\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '50%',\n                                objectFit: 'cover',\n                                border: '2px solid #e5e7eb',\n                                flexShrink: 0\n                              }}\n                              onError={(e) => {\n                                const target = e.target as HTMLImageElement;\n                                target.style.display = 'none';\n                                const parent = target.parentElement;\n                                if (parent) {\n                                  parent.innerHTML = `\n                                    <div style=\"\n                                      width: 40px;\n                                      height: 40px;\n                                      border-radius: 50%;\n                                      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\n                                      display: flex;\n                                      align-items: center;\n                                      justify-content: center;\n                                      color: white;\n                                      font-weight: 600;\n                                      font-size: 1rem;\n                                      flex-shrink: 0;\n                                    \">\n                                      ${currentUser?.firstName?.charAt(0) || ''}${currentUser?.lastName?.charAt(0) || ''}\n                                    </div>\n                                  `;\n                                }\n                              }}\n                            />\n                          ) : (\n                            <div style={{\n                              width: '40px',\n                              height: '40px',\n                              borderRadius: '50%',\n                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              color: 'white',\n                              fontWeight: '600',\n                              fontSize: '1rem',\n                              flexShrink: 0\n                            }}>\n                              {currentUser?.firstName?.charAt(0) || ''}{currentUser?.lastName?.charAt(0) || ''}\n                            </div>\n                          )}\n\n                          <div style={{ flex: 1 }}>\n                            <div style={{\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              color: '#111827'\n                            }}>\n                              {currentUser?.firstName} {currentUser?.lastName}\n                            </div>\n                            <div style={{\n                              fontSize: '0.75rem',\n                              color: '#6b7280'\n                            }}>\n                              {currentUser?.email}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div style={{ padding: '0.5rem 0' }}>\n                        <button\n                          onClick={() => {\n                            const dashboardPath = currentRole === 'admin' ? '/admin/dashboard' : '/student/dashboard';\n                            navigate(dashboardPath);\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#374151',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f3f4f6';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LayoutDashboard size={16} />\n                          Dashboard\n                        </button>\n\n                        <button\n                          onClick={() => {\n                            handleLogout();\n                            setShowUserDropdown(false);\n                          }}\n                          style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            padding: '0.75rem 1rem',\n                            background: 'transparent',\n                            border: 'none',\n                            color: '#ef4444',\n                            fontSize: '0.875rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#fef2f2';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'transparent';\n                          }}\n                        >\n                          <LogOut size={16} />\n                          Logout\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement) => {\n                      // Handle alert announcements with special styling\n                      const isAlert = announcement.is_alert;\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: isAlert ? '#fef2f2' : '#f8fafc',\n                            borderRadius: '12px',\n                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  refreshAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) - Commented out for now */}\n          {/* Future feature: Recent student registrations for admin dashboard */}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.category_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            {/* Author Information - Show for all users */}\n                            {event.created_by_name && (\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px',\n                                fontSize: '0.75rem',\n                                color: '#3b82f6'\n                              }}>\n                                {event.created_by_picture ? (\n                                  <img\n                                    src={getImageUrl(event.created_by_picture) || ''}\n                                    alt={event.created_by_name}\n                                    style={{\n                                      width: '16px',\n                                      height: '16px',\n                                      borderRadius: '50%',\n                                      objectFit: 'cover'\n                                    }}\n                                    onError={(e) => {\n                                      const target = e.target as HTMLImageElement;\n                                      target.style.display = 'none';\n                                    }}\n                                  />\n                                ) : (\n                                  <User size={12} />\n                                )}\n                                <span style={{ fontWeight: '500' }}>\n                                  {event.created_by_name}\n                                </span>\n                              </div>\n                            )}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightboxWithUrls(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.category_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.category_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Interaction Section */}\n                      <div style={{\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem'\n                      }}>\n                        {/* Like Button */}\n                        <button\n                          onClick={() => handleCalendarLikeToggle(event)}\n                          style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            background: 'none',\n                            border: 'none',\n                            color: (event as any).user_has_reacted ? '#ef4444' : '#6b7280',\n                            cursor: 'pointer',\n                            padding: '0.5rem',\n                            borderRadius: '8px',\n                            transition: 'all 0.2s ease',\n                            fontSize: '0.875rem',\n                            fontWeight: '500'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = 'none';\n                          }}\n                        >\n                          <Heart\n                            size={18}\n                            fill={(event as any).user_has_reacted ? '#ef4444' : 'none'}\n                          />\n                          <span>{(event as any).reaction_count || 0}</span>\n                        </button>\n\n                        {/* Comments Button */}\n                        {(event as any).allow_comments && (\n                          <button\n                            onClick={() => setShowCalendarComments(\n                              showCalendarComments === event.calendar_id ? null : event.calendar_id\n                            )}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#374151';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                              e.currentTarget.style.color = showCalendarComments === event.calendar_id ? '#22c55e' : '#6b7280';\n                            }}\n                          >\n                            <MessageSquare size={18} />\n                            <span>{(event as any).comment_count || 0}</span>\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Calendar Event Comments Section */}\n                      {showCalendarComments === event.calendar_id && (event as any).allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              calendarId={event.calendar_id}\n                              allowComments={(event as any).allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      id={`announcement-${announcement.announcement_id}`}\n                      className={isFromNotification && scrollTarget === `announcement-${announcement.announcement_id}` ? 'notification-highlight announcement' : ''}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            {/* Author Information - Show for all users */}\n                            {announcement.author_name && (\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'rgba(34, 197, 94, 0.1)',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px',\n                                fontSize: '0.75rem',\n                                color: '#16a34a'\n                              }}>\n                                {announcement.author_picture ? (\n                                  <img\n                                    src={getImageUrl(announcement.author_picture) || ''}\n                                    alt={announcement.author_name}\n                                    style={{\n                                      width: '16px',\n                                      height: '16px',\n                                      borderRadius: '50%',\n                                      objectFit: 'cover'\n                                    }}\n                                    onError={(e) => {\n                                      const target = e.target as HTMLImageElement;\n                                      target.style.display = 'none';\n                                    }}\n                                  />\n                                ) : (\n                                  <User size={12} />\n                                )}\n                                <span style={{ fontWeight: '500' }}>\n                                  {announcement.author_name}\n                                </span>\n                              </div>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          userRole={currentRole}\n                          onImageClick={(index) => {\n                            openLightbox(announcement.attachments || [], index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {(announcement as any).posted_by_name || announcement.author_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {currentRole === 'admin' ? (\n                            <AdminCommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"admin\"\n                            />\n                          ) : (\n                            <CommentSection\n                              announcementId={announcement.announcement_id}\n                              allowComments={announcement.allow_comments}\n                              currentUserId={currentUser?.id}\n                              currentUserType=\"student\"\n                            />\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    userRole={currentRole}\n                    onImageClick={(index) => {\n                      openLightbox(selectedPinnedPost.attachments, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Announcement Image\"\n      />\n    </div>\n  );\n};\n\nexport default NewsFeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACnF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C;;AAEA,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC/E,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,8BAA8B;AAC9E,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,uBAAuB,MAAM,6BAA6B;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAG3C,SAASC,WAAW,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,wBAAwB;AAChH,OAAO,wCAAwC;AAC/C,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK;AACL;AACAC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAwB,EAAEC,QAA8B,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM0D,cAAc,GAAGtD,MAAM,CAAgB,IAAI,CAAC;EAElDH,SAAS,CAAC,MAAM;IACd;IACA,IAAIyD,cAAc,CAACC,OAAO,EAAE;MAC1BC,GAAG,CAACC,eAAe,CAACH,cAAc,CAACC,OAAO,CAAC;MAC3CD,cAAc,CAACC,OAAO,GAAG,IAAI;IAC/B;IAEA,IAAI,CAACV,SAAS,EAAE;MACdI,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMS,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAMM,OAAO,GAAG5C,WAAW,CAAC8B,SAAS,CAAC;QACtC,IAAI,CAACc,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,SAAS,GAAGjB,QAAQ,KAAK,OAAO,GAClCkB,YAAY,CAACC,OAAO,CAAChD,oBAAoB,CAAC,GAC1C+C,YAAY,CAACC,OAAO,CAAC/C,sBAAsB,CAAC;;QAEhD;QACA,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAACR,OAAO,EAAE;UACpCS,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUN,SAAS,EAAE;YACtC,QAAQ,EAAEO,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAId,KAAK,CAAC,QAAQM,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGtB,GAAG,CAACuB,eAAe,CAACF,IAAI,CAAC;;QAE3C;QACAvB,cAAc,CAACC,OAAO,GAAGuB,SAAS;QAClC7B,WAAW,CAAC6B,SAAS,CAAC;QAEtBjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOkB,GAAG,EAAE;QACZnB,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAE4B,GAAG,CAAC;QAC3C3B,QAAQ,CAAC2B,GAAG,YAAYpB,KAAK,GAAGoB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACR9B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIJ,cAAc,CAACC,OAAO,EAAE;QAC1BC,GAAG,CAACC,eAAe,CAACH,cAAc,CAACC,OAAO,CAAC;QAC3CD,cAAc,CAACC,OAAO,GAAG,IAAI;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACV,SAAS,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE3B,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CAhFMH,cAAc;AA4FpB,MAAMsC,YAAyC,GAAGA,CAAC;EACjDrC,SAAS;EACTsC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTvC,QAAQ;EACRwC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAEzC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGR,cAAc,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAExE,IAAII,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBtD,OAAA;QAAK2C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCtD,OAAA;UAAK2C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE7D,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlD,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEP,OAAA;MAAK2C,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBtD,OAAA;QAAK2C,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCtD,OAAA;UAAK2C,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrE7D,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/ElD,KAAK,iBACJX,OAAA;UAAK2C,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzE3C;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7D,OAAA;IACEiE,GAAG,EAAE1D,QAAS;IACdmC,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CAtEMP,YAAyC;EAAA,QAURtC,cAAc;AAAA;AAAAgE,EAAA,GAV/C1B,YAAyC;AA8E/C,MAAM2B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEjE,QAAQ;EAAEkE;AAAa,CAAC,KAAK;EACnG,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEd,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIR,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAA2B;IAC/C,OAAO;MACLF,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACE1F,OAAA;IAAK2C,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf0C,GAAG,EAAE,KAAK;MACVL,KAAK,EAAE,MAAM;MACb9B,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAtD,OAAA;MAAK2C,KAAK,EAAEmC,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAAlB,QAAA,gBACrDtD,OAAA,CAACyC,YAAY;QACXrC,SAAS,EAAEqE,aAAa,CAAC,CAAC,CAAC,CAACmB,SAAU;QACtClD,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAE6C,aAAa,CAAC,CAAE;QACvBnF,QAAQ,EAAEA,QAAS;QACnByC,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;QACjD,CAAE;QACF/C,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDU,YAAY,iBACXvE,OAAA;QACE2C,KAAK,EAAE;UACLuC,QAAQ,EAAE,UAAU;UACpBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAAC;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLY,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBxE,OAAA;MAAK2C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE3B,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DmB,GAAG,EAAE,KAAK;QACVL,KAAK,EAAEb,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAlB,QAAA,EACCmB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK/B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE3E,OAAA;UAEE2C,KAAK,EAAE;YACL,GAAGmC,iBAAiB,CAAC0B,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAC;YACvDU,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBAEFtD,OAAA,CAACyC,YAAY;YACXrC,SAAS,EAAEkG,KAAK,CAACV,SAAU;YAC3BlD,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAE6C,aAAa,CAAC,CAAE;YACvBnF,QAAQ,EAAEA,QAAS;YACnByC,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;YACjD,CAAE;YACF/C,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD4C,MAAM,IAAI9B,cAAc,GAAG,CAAC,iBAC3B3E,OAAA;YAAK2C,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT9C,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdI,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,GACA,EAACqB,cAAc;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAU,YAAY,iBACXvE,OAAA;YACE2C,KAAK,EAAE;cACLuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACiC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAnDI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA6C,GAAA,GAzJMtC,YAAyC;AA8J/C;AACA,MAAMuC,QAAiC,GAAGA,CAAC;EAAEtG;AAAS,CAAC,KAAK;EAAAuG,GAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA;EAC1D,MAAMC,QAAQ,GAAGvJ,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwJ,SAAS,GAAG3J,UAAU,CAACS,gBAAgB,CAAC;EAC9C,MAAMmJ,WAAW,GAAG5J,UAAU,CAACU,kBAAkB,CAAC;;EAElD;EACA,MAAMmJ,WAAW,GAAG7G,QAAQ,KACzB2G,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEG,eAAe,GAAG,OAAO,GACpCF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC;EAEnD,MAAMC,WAAW,GAAGF,WAAW,KAAK,OAAO,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,IAAI,GAAGJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI;EACjF,MAAMC,aAAa,GAAGJ,WAAW,KAAK,OAAO,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,MAAM,GAAGN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,MAAM;;EAEvF;EACA,MAAM;IAAEC;EAAW,CAAC,GAAG7J,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM;IACJ8J,aAAa;IACbhH,OAAO;IACPE,KAAK;IACL+G,gBAAgB;IAChBC,kBAAkB;IAClBC,OAAO,EAAEC;EACX,CAAC,GAAGjK,gBAAgB,CAAC;IACnBsE,MAAM,EAAE,WAAW;IACnB4F,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAEf,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAM;IAAEgB,kBAAkB;IAAEC;EAAa,CAAC,GAAGtK,qBAAqB,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACuK,cAAc,EAAEC,iBAAiB,CAAC,GAAGlL,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmL,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpL,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACqL,UAAU,EAAEC,aAAa,CAAC,GAAGtL,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACuL,YAAY,EAAEC,eAAe,CAAC,GAAGxL,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACyL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1L,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAAC2L,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5L,QAAQ,CAAa,IAAI,CAAC;EAC9E,MAAM,CAAC6L,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9L,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC+L,YAAY,EAAEC,eAAe,CAAC,GAAGhM,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiM,cAAc,EAAEC,iBAAiB,CAAC,GAAGlM,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACmM,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpM,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACqM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtM,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACuM,cAAc,EAAEC,iBAAiB,CAAC,GAAGxM,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACyM,eAAe,EAAEC,kBAAkB,CAAC,GAAG1M,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2M,aAAa,EAAEC,gBAAgB,CAAC,GAAG5M,QAAQ,CAAqB,CAAC;EACxE;;EAEA;EACA,MAAM6M,mBAAmB,GAAG1M,WAAW,CAAC,YAAY;IAClD,IAAI;MACFuM,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACE,SAAS,CAAC;MAE3B,MAAM3I,SAAS,GAAG4F,WAAW,KAAK,OAAO,GACrC3F,YAAY,CAACC,OAAO,CAAChD,oBAAoB,CAAC,GAC1C+C,YAAY,CAACC,OAAO,CAAC/C,sBAAsB,CAAC;MAEhD,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnD,YAAY,0DAA0D,EAAE;QACtGqD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUN,SAAS,EAAE;UACtC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAM4I,IAAI,GAAG,MAAMzI,QAAQ,CAAC0I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,UAAU,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMK,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAAChE,GAAG,CAAC,MAAOqE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMjJ,KAAK,CAAC,GAAGnD,YAAY,iBAAiBmM,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FhJ,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUN,SAAS,EAAE;gBACtC,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMuJ,SAAS,GAAG,MAAMF,aAAa,CAACR,IAAI,CAAC,CAAC;YAE5C,IAAIU,SAAS,CAACT,OAAO,IAAIS,SAAS,CAACX,IAAI,EAAE;cACvCQ,KAAK,CAACrG,MAAM,GAAGwG,SAAS,CAACX,IAAI,CAACY,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACrG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO0G,MAAM,EAAE;YACf3J,OAAO,CAAC4J,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACrG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOqG,KAAK;QACd,CAAC,CACH,CAAC;QAEDf,iBAAiB,CAACY,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLR,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOxH,GAAQ,EAAE;MACjBnB,OAAO,CAACT,KAAK,CAAC,iCAAiC,EAAE4B,GAAG,CAAC;MACrDwH,gBAAgB,CAACxH,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRqH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAAC3C,WAAW,CAAC,CAAC;;EAEjB;EACA9J,SAAS,CAAC,MAAM;IACd,MAAM6N,MAAM,GAAGxD,aAAa,CAACyD,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;IACtE3B,sBAAsB,CAACwB,MAAM,CAAC;EAChC,CAAC,EAAE,CAACxD,aAAa,CAAC,CAAC;;EAEnB;EACArK,SAAS,CAAC,MAAM;IACd4M,mBAAmB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC9C,WAAW,EAAE8C,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA5M,SAAS,CAAC,MAAM;IACd,MAAMiO,kBAAkB,GAAIX,KAAiB,IAAK;MAChD,IAAI1B,gBAAgB,EAAE;QACpB,MAAMsC,MAAM,GAAGZ,KAAK,CAACY,MAAiB;QACtC,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;UACtDtC,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;IAEDuC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACrC,gBAAgB,CAAC,CAAC;;EAEtB;EACA,IAAI,CAAC9B,WAAW,EAAE;IAChB,oBACElH,OAAA;MAAK2C,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpByI,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE;MACd,CAAE;MAAAtI,QAAA,eACAtD,OAAA;QAAK2C,KAAK,EAAE;UACViJ,UAAU,EAAE,OAAO;UACnBC,OAAO,EAAE,MAAM;UACfzG,YAAY,EAAE,MAAM;UACpBtB,MAAM,EAAE,mBAAmB;UAC3BP,SAAS,EAAE,QAAQ;UACnBuI,QAAQ,EAAE;QACZ,CAAE;QAAAxI,QAAA,gBACAtD,OAAA;UAAI2C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF7D,OAAA;UAAG2C,KAAK,EAAE;YAAEU,KAAK,EAAE,SAAS;YAAEG,YAAY,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7D,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAMY,QAAQ,CAAC,GAAG,CAAE;UAC7BpE,KAAK,EAAE;YACLiJ,UAAU,EAAE,mDAAmD;YAC/DvI,KAAK,EAAE,OAAO;YACdS,MAAM,EAAE,MAAM;YACdsB,YAAY,EAAE,MAAM;YACpByG,OAAO,EAAE,gBAAgB;YACzBpI,QAAQ,EAAE,UAAU;YACpBM,UAAU,EAAE,KAAK;YACjBsB,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMkI,YAAY,GAAGA,CAAC1H,MAAgC,EAAE2H,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAG5H,MAAM,CAACgC,GAAG,CAAC6F,GAAG,IAAI5N,WAAW,CAAC4N,GAAG,CAACtG,SAAS,CAAC,CAAC,CAACsF,MAAM,CAACiB,OAAO,CAAa;IAC3F9C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiD,oBAAoB,GAAGA,CAACH,SAAmB,EAAED,YAAoB,KAAK;IAC1E3C,iBAAiB,CAAC4C,SAAS,CAAC;IAC5B1C,uBAAuB,CAACyC,YAAY,CAAC;IACrC7C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEtN;MACR,CAAC;MACD,SAAS,EAAE;QACT0M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAExN;MACR,CAAC;MACD,QAAQ,EAAE;QACR4M,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAErN;MACR,CAAC;MACD,WAAW,EAAE;QACXyM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEpN;MACR,CAAC;MACD,QAAQ,EAAE;QACRwM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAElN;MACR,CAAC;MACD,WAAW,EAAE;QACXsM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEnN;MACR;IACF,CAAC;IAED,OAAOkN,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMH,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBX,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE/M;MACR,CAAC;MACD,cAAc,EAAE;QACdmM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEhN;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBoM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE9M;MACR,CAAC;MACD,cAAc,EAAE;QACdkM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAElN;MACR,CAAC;MACD,YAAY,EAAE;QACZsM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAE7M;MACR,CAAC;MACD,SAAS,EAAE;QACTiM,UAAU,EAAE,mDAAmD;QAC/DY,IAAI,EAAEjN;MACR;IACF,CAAC;IAED,OAAOgN,MAAM,CAACG,eAAe,CAAwB,IAAIH,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;;EAEA;EACA,MAAMI,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACFxL,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,sCAAsC,EAAE0F,YAAY,CAACC,eAAe,CAAC;MACvGzL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuL,YAAY,CAACE,aAAa,CAAC;MACzE1L,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,gBAAgB,EAAE;QAAE6F,EAAE,EAAE3F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAE;QAAEC,IAAI,EAAE9F;MAAY,CAAC,CAAC;MAE/F,IAAI0F,YAAY,CAACE,aAAa,EAAE;QAC9B;QACA1L,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,uBAAuB,CAAC;QAC1D,MAAMS,kBAAkB,CAACiF,YAAY,CAACC,eAAe,CAAC;MACxD,CAAC,MAAM;QACL;QACAzL,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,qBAAqB,CAAC;QACxD,MAAMQ,gBAAgB,CAACkF,YAAY,CAACC,eAAe,EAAE,CAAC,CAAC;MACzD;MAEAzL,OAAO,CAACC,GAAG,CAAC,aAAa6F,WAAW,gCAAgC,CAAC;IACvE,CAAC,CAAC,OAAOvG,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,WAAWuG,WAAW,uBAAuB,EAAEvG,KAAK,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMsM,sBAAsB,GAAG,MAAAA,CAAOC,OAAe,EAAEC,cAAuB,KAAK;IACjF,MAAMC,MAAM,GAAGlG,WAAW,KAAK,OAAO,GAAGzJ,eAAe,GAAGC,iBAAiB;IAC5E,MAAM2P,QAAQ,GAAG,iBAAiBH,OAAO,OAAO;IAEhD9L,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,6BAA6B,EAAEmG,QAAQ,CAAC;IAC1EjM,OAAO,CAACC,GAAG,CAAC,iBAAiB6F,WAAW,cAAc,CAAC;IAEvD,IAAIiG,cAAc,EAAE;MAClB;MACA,OAAO,MAAMC,MAAM,CAACE,MAAM,CAACD,QAAQ,CAAC;IACtC,CAAC,MAAM;MACL;MACA,OAAO,MAAMD,MAAM,CAACG,IAAI,CAACF,QAAQ,EAAE,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMG,wBAAwB,GAAG,MAAO9C,KAAU,IAAK;IACrD,IAAI;MACFtJ,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,wCAAwC,EAAEwD,KAAK,CAACE,WAAW,CAAC;MAC9FxJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqJ,KAAK,CAAC+C,gBAAgB,CAAC;MACxErM,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,gBAAgB,EAAE;QAAE6F,EAAE,EAAE3F,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAE;QAAEC,IAAI,EAAE9F;MAAY,CAAC,CAAC;MAE/F,MAAMzF,QAAQ,GAAG,MAAMwL,sBAAsB,CAACvC,KAAK,CAACE,WAAW,EAAEF,KAAK,CAAC+C,gBAAgB,IAAI,KAAK,CAAC;MAEjG,IAAIhM,QAAQ,CAAC2I,OAAO,EAAE;QACpB,MAAMsD,gBAAgB,GAAG,CAAChD,KAAK,CAAC+C,gBAAgB;QAChD,MAAME,gBAAgB,GAAGjD,KAAK,CAAC+C,gBAAgB,GAC3C7I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC6F,KAAK,CAACkD,cAAc,IAAI,CAAC,IAAI,CAAC,CAAC,GAC5C,CAAClD,KAAK,CAACkD,cAAc,IAAI,CAAC,IAAI,CAAC;QAEnCxM,OAAO,CAACC,GAAG,CAAC,WAAW6F,WAAW,wBAAwB,EAAE;UAC1DgG,OAAO,EAAExC,KAAK,CAACE,WAAW;UAC1BiD,gBAAgB,EAAEnD,KAAK,CAAC+C,gBAAgB;UACxCC,gBAAgB;UAChBI,QAAQ,EAAEpD,KAAK,CAACkD,cAAc;UAC9BG,QAAQ,EAAEJ,gBAAgB;UAC1BK,MAAM,EAAE5G,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAE;UACvB1M,QAAQ,EAAE6G;QACZ,CAAC,CAAC;;QAEF;QACAyC,iBAAiB,CAACsE,UAAU,IAC1BA,UAAU,CAAC5H,GAAG,CAACnC,CAAC,IACdA,CAAC,CAAC0G,WAAW,KAAKF,KAAK,CAACE,WAAW,GAC/B;UACE,GAAG1G,CAAC;UACJuJ,gBAAgB,EAAEC,gBAAgB;UAClCE,cAAc,EAAED;QAClB,CAAC,GACDzJ,CACN,CACF,CAAC;QACD9C,OAAO,CAACC,GAAG,CAAC,aAAa6F,WAAW,+CAA+C,CAAC;MACtF;IACF,CAAC,CAAC,OAAOvG,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,WAAWuG,WAAW,gCAAgC,EAAEvG,KAAK,CAAC;IAC9E;EACF,CAAC;;EAED;EACA,MAAMuN,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI5G,aAAa,EAAE;QACjB,MAAMA,aAAa,CAAC,CAAC;MACvB;IACF,CAAC,CAAC,OAAO3G,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACA,MAAMwN,YAAY,GAAGjH,WAAW,KAAK,OAAO,GAAG,cAAc,GAAG,gBAAgB;MAChFrF,MAAM,CAACC,QAAQ,CAACsM,IAAI,GAAGD,YAAY;IACrC;EACF,CAAC;;EAED;;EAIA;EACA,MAAME,qBAAqB,GAAG5G,aAAa,CAACyD,MAAM,CAAC0B,YAAY,IAAI;IAAA,IAAA0B,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAAChG,UAAU,IAC/BoE,YAAY,CAAC6B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnG,UAAU,CAACkG,WAAW,CAAC,CAAC,CAAC,IACnE9B,YAAY,CAACgC,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnG,UAAU,CAACkG,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAMG,eAAe,GAAG,CAACzG,cAAc,IACrC,EAAAkG,qBAAA,GAAA1B,YAAY,CAACkC,WAAW,cAAAR,qBAAA,uBAAxBA,qBAAA,CAA0BS,QAAQ,CAAC,CAAC,MAAK3G,cAAc;IAEzD,MAAM4G,iBAAiB,GAAG,CAAC1G,gBAAgB,IACzC,EAAAiG,qBAAA,GAAA3B,YAAY,CAACqC,WAAW,cAAAV,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAKzG,gBAAgB;IAE3D,OAAOkG,aAAa,IAAIK,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGxF,cAAc,CAACwB,MAAM,CAACR,KAAK,IAAI;IAC5D,MAAM8D,aAAa,GAAG,CAAChG,UAAU,IAC/BkC,KAAK,CAAC+D,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnG,UAAU,CAACkG,WAAW,CAAC,CAAC,CAAC,IAC3DhE,KAAK,CAACyE,WAAW,IAAIzE,KAAK,CAACyE,WAAW,CAACT,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnG,UAAU,CAACkG,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA;IACA,MAAMU,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,GAAG,GAC/CC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACnDF,MAAM,CAACJ,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1C,MAAME,cAAc,GAAG,IAAIP,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC;IACjD,MAAMC,oBAAoB,GAAGF,cAAc,CAACL,WAAW,CAAC,CAAC,GAAG,GAAG,GAC7DC,MAAM,CAACI,cAAc,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAC5DF,MAAM,CAACI,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMK,kBAAkB,GAAGrF,KAAK,CAACsF,QAAQ,GAAG,CAAC,MAAM;MACjD,MAAMC,OAAO,GAAG,IAAIZ,IAAI,CAAC3E,KAAK,CAACsF,QAAQ,CAAC;MACxC,OAAOC,OAAO,CAACV,WAAW,CAAC,CAAC,GAAG,GAAG,GAChCC,MAAM,CAACS,OAAO,CAACR,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GACrDF,MAAM,CAACS,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9C,CAAC,EAAE,CAAC,GAAGI,oBAAoB;;IAE3B;IACA,MAAMI,aAAa,GAAGZ,eAAe,IAAIQ,oBAAoB,IAAIR,eAAe,IAAIS,kBAAkB;;IAEtG;IACA,MAAMI,QAAQ,GAAIzF,KAAK,CAAS0F,SAAS,KAAK,CAAC;IAE/C,OAAO5B,aAAa,IAAI0B,aAAa,IAAIC,QAAQ;EACnD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGhC,qBAAqB;EAClD,MAAMiC,aAAa,GAAGpB,sBAAsB;;EAI5C;EACA;EACA;EACA;EACA;;EAEA,oBACElP,OAAA;IAAK2C,KAAK,EAAE;MACVgJ,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/D1G,QAAQ,EAAE;IACZ,CAAE;IAAA5B,QAAA,gBAEAtD,OAAA;MAAK2C,KAAK,EAAE;QACVuC,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTqK,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAA9M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL7D,OAAA;MAAK2C,KAAK,EAAE;QAAEuC,QAAQ,EAAE,UAAU;QAAEuL,MAAM,EAAE;MAAE,CAAE;MAAAnN,QAAA,gBAE9CtD,OAAA;QAAQ2C,KAAK,EAAE;UACbiJ,UAAU,EAAE,OAAO;UACnB8E,YAAY,EAAE,mBAAmB;UACjCxL,QAAQ,EAAE,QAAQ;UAClBa,GAAG,EAAE,CAAC;UACN0K,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAArN,QAAA,eACAtD,OAAA;UAAK2C,KAAK,EAAE;YACVkJ,OAAO,EAAE,QAAQ;YACjBtG,MAAM,EAAE,MAAM;YACdtC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBAEAtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,QAAQ;cACbiL,QAAQ,EAAE;YACZ,CAAE;YAAAtN,QAAA,gBACAtD,OAAA;cACEiE,GAAG,EAAC,iBAAiB;cACrBvB,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACL2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7D,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAI2C,KAAK,EAAE;kBACTkO,MAAM,EAAE,CAAC;kBACTpN,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChByN,UAAU,EAAE;gBACd,CAAE;gBAAAxN,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRkO,MAAM,EAAE,CAAC;kBACTpN,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE,SAAS;kBAChByN,UAAU,EAAE;gBACd;cAAE;gBAAApN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAK2C,KAAK,EAAE;cACVoO,IAAI,EAAE,CAAC;cACPjF,QAAQ,EAAE,OAAO;cACjB+E,MAAM,EAAE;YACV,CAAE;YAAAvN,QAAA,eACAtD,OAAA;cAAK2C,KAAK,EAAE;gBAAEuC,QAAQ,EAAE;cAAW,CAAE;cAAA5B,QAAA,gBACnCtD,OAAA,CAACrB,MAAM;gBACLqS,IAAI,EAAE,EAAG;gBACTrO,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBc,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVD,SAAS,EAAE,kBAAkB;kBAC7BzC,KAAK,EAAE;gBACT;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7D,OAAA;gBACEiR,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,aAAa;gBACzBC,KAAK,EAAE3I,UAAW;gBAClB4I,QAAQ,EAAGlN,CAAC,IAAKuE,aAAa,CAACvE,CAAC,CAACoH,MAAM,CAAC6F,KAAK,CAAE;gBAC/CxO,KAAK,EAAE;kBACL2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdsG,OAAO,EAAE,eAAe;kBACxB/H,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,MAAM;kBACpBwG,UAAU,EAAE,SAAS;kBACrBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB4N,OAAO,EAAE,MAAM;kBACf3L,UAAU,EAAE;gBACd,CAAE;gBACF4L,OAAO,EAAGpN,CAAC,IAAK;kBACdA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;kBAC7CrN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,OAAO;kBAC1C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFa,MAAM,EAAGtN,CAAC,IAAK;kBACbA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;kBAC7CrN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;kBAC5C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAAjN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXiL,QAAQ,EAAE,OAAO;cACjBzN,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAGAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbkG,OAAO,EAAE,QAAQ;gBACjBD,UAAU,EAAE,SAAS;gBACrBxG,YAAY,EAAE,MAAM;gBACpBtB,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACAtD,OAAA;gBACEmR,KAAK,EAAE/I,cAAe;gBACtBgJ,QAAQ,EAAGlN,CAAC,IAAKmE,iBAAiB,CAACnE,CAAC,CAACoH,MAAM,CAAC6F,KAAK,CAAE;gBACnDxO,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,OAAO;kBACnBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB4N,OAAO,EAAE,MAAM;kBACfhM,MAAM,EAAE,SAAS;kBACjBuL,QAAQ,EAAE;gBACZ,CAAE;gBAAAtN,QAAA,gBAEFtD,OAAA;kBAAQmR,KAAK,EAAC,EAAE;kBAAA7N,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvC2D,UAAU,CACR0D,MAAM,CAACuG,QAAQ;gBACd;gBACA,CAAC,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,oBAAoB,CAAC,CAAC9C,QAAQ,CAAC8C,QAAQ,CAACC,IAAI,CACjG,CAAC,CACArL,GAAG,CAACoL,QAAQ,iBACXzR,OAAA;kBAAmCmR,KAAK,EAAEM,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAAzL,QAAA,EACvEmO,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC3C,WAAW;kBAAApL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eAET7D,OAAA;gBACEmR,KAAK,EAAE7I,gBAAiB;gBACxB8I,QAAQ,EAAGlN,CAAC,IAAKqE,mBAAmB,CAACrE,CAAC,CAACoH,MAAM,CAAC6F,KAAK,CAAE;gBACrDxO,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,OAAO;kBACnBvI,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpB4N,OAAO,EAAE,MAAM;kBACfhM,MAAM,EAAE,SAAS;kBACjBuL,QAAQ,EAAE;gBACZ,CAAE;gBAAAtN,QAAA,gBAEFtD,OAAA;kBAAQmR,KAAK,EAAC,EAAE;kBAAA7N,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7D,OAAA;kBAAQmR,KAAK,EAAC,IAAI;kBAAA7N,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7D,OAAA;kBAAQmR,KAAK,EAAC,IAAI;kBAAA7N,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAAC2E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtI,OAAA;gBACEmG,OAAO,EAAEA,CAAA,KAAM;kBACbsC,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACF5F,KAAK,EAAE;kBACLkJ,OAAO,EAAE,gBAAgB;kBACzB/H,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBwG,UAAU,EAAE,SAAS;kBACrBvI,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBsB,MAAM,EAAE,SAAS;kBACjBK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACF7I,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAAtI,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7D,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,GAEC4D,WAAW,KAAK,OAAO,gBAAGlH,OAAA,CAAC9B,gBAAgB;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG7D,OAAA,CAAC7B,uBAAuB;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG7E7D,OAAA;gBAAK2C,KAAK,EAAE;kBAAEuC,QAAQ,EAAE;gBAAW,CAAE;gBAAC,iBAAc,eAAe;gBAAA5B,QAAA,gBACjEtD,OAAA;kBACEmG,OAAO,EAAEA,CAAA,KAAM8C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBACtDrG,KAAK,EAAE;oBACLM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbkG,OAAO,EAAE,cAAc;oBACvBD,UAAU,EAAE,OAAO;oBACnB9H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpB/B,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE,eAAe;oBAC3BiL,SAAS,EAAE;kBACb,CAAE;kBACF7N,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;oBAC7CrN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,kCAAkC;kBACtE,CAAE;kBACF5N,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;oBAC7CrN,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,8BAA8B;kBAClE,CAAE;kBAAArN,QAAA,GAGD8D,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuK,cAAc,gBAC1B3R,OAAA;oBACEiE,GAAG,EAAE3F,WAAW,CAAC8I,WAAW,CAACuK,cAAc,CAAC,IAAI,EAAG;oBACnDjP,GAAG,EAAE,GAAG0E,WAAW,CAACwK,SAAS,IAAIxK,WAAW,CAACyK,QAAQ,EAAG;oBACxDlP,KAAK,EAAE;sBACL2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdH,YAAY,EAAE,KAAK;sBACnBK,SAAS,EAAE,OAAO;sBAClB3B,MAAM,EAAE;oBACV,CAAE;oBACFgO,OAAO,EAAG5N,CAAC,IAAK;sBACd,MAAMoH,MAAM,GAAGpH,CAAC,CAACoH,MAA0B;sBAC3CA,MAAM,CAAC3I,KAAK,CAACM,OAAO,GAAG,MAAM;sBAC7B,MAAM8O,MAAM,GAAGzG,MAAM,CAAC0G,aAAa;sBACnC,IAAID,MAAM,EAAE;wBACV,MAAME,QAAQ,GAAGF,MAAM,CAACG,aAAa,CAAC,YAAY,CAAC;wBACnD,IAAID,QAAQ,EAAE;0BACXA,QAAQ,CAAiBtP,KAAK,CAACM,OAAO,GAAG,OAAO;wBACnD;sBACF;oBACF;kBAAE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACA,IAAI,eACR7D,OAAA,CAACH,IAAI;oBACHmR,IAAI,EAAE,EAAG;oBACTpO,SAAS,EAAC,WAAW;oBACrBD,KAAK,EAAE;sBACLM,OAAO,EAAEmE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuK,cAAc,GAAG,MAAM,GAAG;oBAClD;kBAAE;oBAAAjO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF7D,OAAA;oBAAAsD,QAAA,EAAO,CAAA8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwK,SAAS,MAAK1K,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;kBAAC;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF7D,OAAA,CAACJ,WAAW;oBAACoR,IAAI,EAAE,EAAG;oBAACrO,KAAK,EAAE;sBAC5BmD,SAAS,EAAEkD,gBAAgB,GAAG,gBAAgB,GAAG,cAAc;sBAC/DtD,UAAU,EAAE;oBACd;kBAAE;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGRmF,gBAAgB,iBACfhJ,OAAA;kBAAK2C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,CAAC;oBACRjC,SAAS,EAAE,QAAQ;oBACnB4H,UAAU,EAAE,OAAO;oBACnB9H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,MAAM;oBACpBuL,SAAS,EAAE,iCAAiC;oBAC5CC,QAAQ,EAAE,OAAO;oBACjBH,MAAM,EAAE,IAAI;oBACZtL,QAAQ,EAAE;kBACZ,CAAE;kBAAA7B,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVkJ,OAAO,EAAE,cAAc;sBACvB6E,YAAY,EAAE,mBAAmB;sBACjC9E,UAAU,EAAE;oBACd,CAAE;oBAAAtI,QAAA,eACAtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GAEC8D,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuK,cAAc,gBAC1B3R,OAAA;wBACEiE,GAAG,EAAE3F,WAAW,CAAC8I,WAAW,CAACuK,cAAc,CAAC,IAAI,EAAG;wBACnDjP,GAAG,EAAE,GAAG0E,WAAW,CAACwK,SAAS,IAAIxK,WAAW,CAACyK,QAAQ,EAAG;wBACxDlP,KAAK,EAAE;0BACL2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdH,YAAY,EAAE,KAAK;0BACnBK,SAAS,EAAE,OAAO;0BAClB3B,MAAM,EAAE,mBAAmB;0BAC3BqO,UAAU,EAAE;wBACd,CAAE;wBACFL,OAAO,EAAG5N,CAAC,IAAK;0BACd,MAAMoH,MAAM,GAAGpH,CAAC,CAACoH,MAA0B;0BAC3CA,MAAM,CAAC3I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC7B,MAAM8O,MAAM,GAAGzG,MAAM,CAAC0G,aAAa;0BACnC,IAAID,MAAM,EAAE;4BAAA,IAAAK,qBAAA,EAAAC,qBAAA;4BACVN,MAAM,CAACO,SAAS,GAAG;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,CAAAlL,WAAW,aAAXA,WAAW,wBAAAgL,qBAAA,GAAXhL,WAAW,CAAEwK,SAAS,cAAAQ,qBAAA,uBAAtBA,qBAAA,CAAwBG,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAAnL,WAAW,aAAXA,WAAW,wBAAAiL,qBAAA,GAAXjL,WAAW,CAAEyK,QAAQ,cAAAQ,qBAAA,uBAArBA,qBAAA,CAAuBE,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;AACxH;AACA,mCAAmC;0BACH;wBACF;sBAAE;wBAAA7O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEF7D,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdH,YAAY,EAAE,KAAK;0BACnBwG,UAAU,EAAE,mDAAmD;0BAC/D3I,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBE,KAAK,EAAE,OAAO;0BACdU,UAAU,EAAE,KAAK;0BACjBN,QAAQ,EAAE,MAAM;0BAChB0O,UAAU,EAAE;wBACd,CAAE;wBAAA7O,QAAA,GACC,CAAA8D,WAAW,aAAXA,WAAW,wBAAAP,sBAAA,GAAXO,WAAW,CAAEwK,SAAS,cAAA/K,sBAAA,uBAAtBA,sBAAA,CAAwB0L,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAE,CAAAnL,WAAW,aAAXA,WAAW,wBAAAN,sBAAA,GAAXM,WAAW,CAAEyK,QAAQ,cAAA/K,sBAAA,uBAArBA,sBAAA,CAAuByL,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;sBAAA;wBAAA7O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BAAEoO,IAAI,EAAE;wBAAE,CAAE;wBAAAzN,QAAA,gBACtBtD,OAAA;0BAAK2C,KAAK,EAAE;4BACVc,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,GACC8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwK,SAAS,EAAC,GAAC,EAACxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyK,QAAQ;wBAAA;0BAAAnO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C,CAAC,eACN7D,OAAA;0BAAK2C,KAAK,EAAE;4BACVc,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,EACC8D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoL;wBAAK;0BAAA9O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEkJ,OAAO,EAAE;oBAAW,CAAE;oBAAAvI,QAAA,gBAClCtD,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAMsM,aAAa,GAAGvL,WAAW,KAAK,OAAO,GAAG,kBAAkB,GAAG,oBAAoB;wBACzFH,QAAQ,CAAC0L,aAAa,CAAC;wBACvBxJ,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFtG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdkG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAtI,QAAA,gBAEFtD,OAAA,CAACf,eAAe;wBAAC+R,IAAI,EAAE;sBAAG;wBAAAtN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAE/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAET7D,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAM;wBACb+H,YAAY,CAAC,CAAC;wBACdjF,mBAAmB,CAAC,KAAK,CAAC;sBAC5B,CAAE;sBACFtG,KAAK,EAAE;wBACL2C,KAAK,EAAE,MAAM;wBACbrC,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdkG,OAAO,EAAE,cAAc;wBACvBD,UAAU,EAAE,aAAa;wBACzB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,UAAU;wBACpB4B,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;sBAC9C,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,aAAa;sBAClD,CAAE;sBAAAtI,QAAA,gBAEFtD,OAAA,CAACF,MAAM;wBAACkR,IAAI,EAAE;sBAAG;wBAAAtN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT7D,OAAA;QAAK2C,KAAK,EAAE;UACVkJ,OAAO,EAAE,MAAM;UACf5I,OAAO,EAAE,MAAM;UACf0C,GAAG,EAAE,MAAM;UACXzC,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEAtD,OAAA;UAAK2C,KAAK,EAAE;YACV2C,KAAK,EAAE,OAAO;YACd6M,UAAU,EAAE;UACd,CAAE;UAAA7O,QAAA,eACAtD,OAAA;YAAK2C,KAAK,EAAE;cACViJ,UAAU,EAAE,OAAO;cACnBxG,YAAY,EAAE,MAAM;cACpBtB,MAAM,EAAE,mBAAmB;cAC3BqB,QAAQ,EAAE,QAAQ;cAClBD,QAAQ,EAAE,QAAQ;cAClBa,GAAG,EAAE;YACP,CAAE;YAAAzC,QAAA,gBAEAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVkJ,OAAO,EAAE,oBAAoB;gBAC7B6E,YAAY,EAAE;cAChB,CAAE;cAAApN,QAAA,gBACAtD,OAAA;gBAAK2C,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE,SAAS;kBACdnC,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,gBACAtD,OAAA,CAACpB,GAAG;kBAACoS,IAAI,EAAE,EAAG;kBAACrO,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C7D,OAAA;kBAAI2C,KAAK,EAAE;oBACTkO,MAAM,EAAE,CAAC;oBACTpN,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRkO,MAAM,EAAE,CAAC;kBACTpN,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN7D,OAAA;cAAK2C,KAAK,EAAE;gBAAEkJ,OAAO,EAAE;cAAO,CAAE;cAAAvI,QAAA,EAC7BkG,mBAAmB,CAAChF,MAAM,GAAG,CAAC,gBAC7BxE,OAAA,CAAAE,SAAA;gBAAAoD,QAAA,GACGkG,mBAAmB,CAAC9E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAEuG,YAAY,IAAK;kBACrD;kBACA,MAAM8F,OAAO,GAAG9F,YAAY,CAAC+F,QAAQ;kBACrC,MAAMrG,YAAY,GAAG,CAACM,YAAY,CAACgG,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAGzG,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACEtM,OAAA;oBAEE2C,KAAK,EAAE;sBACLkJ,OAAO,EAAE,MAAM;sBACfD,UAAU,EAAE8G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAC3CtN,YAAY,EAAE,MAAM;sBACpBtB,MAAM,EAAE4O,OAAO,GAAG,mBAAmB,GAAG,mBAAmB;sBAC3DlP,YAAY,EAAE,MAAM;sBACpB6B,MAAM,EAAE,SAAS;sBACjBK,UAAU,EAAE;oBACd,CAAE;oBACF5C,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG8G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClExO,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAGmB,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACF3P,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG8G,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClExO,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAGmB,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACFvM,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC6D,YAAY,CAAE;oBAAAtJ,QAAA,eAEnDtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtD,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbqG,UAAU,EAAE8G,OAAO,GAAG,SAAS,GAAII,aAAa,CAAClH,UAAU,CAAC+C,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAU;0BACxGvJ,YAAY,EAAE,KAAK;0BACnBpB,SAAS,EAAE,QAAQ;0BACnBmO,UAAU,EAAE;wBACd;sBAAE;wBAAAzO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL7D,OAAA;wBAAK2C,KAAK,EAAE;0BAAEoO,IAAI,EAAE;wBAAE,CAAE;wBAAAzN,QAAA,gBACtBtD,OAAA;0BAAI2C,KAAK,EAAE;4BACTkO,MAAM,EAAE,cAAc;4BACtBpN,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE,SAAS;4BAChByN,UAAU,EAAE;0BACd,CAAE;0BAAAxN,QAAA,EACCsJ,YAAY,CAAC6B;wBAAK;0BAAA/K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL7D,OAAA;0BAAG2C,KAAK,EAAE;4BACRkO,MAAM,EAAE,cAAc;4BACtBpN,QAAQ,EAAE,QAAQ;4BAClBJ,KAAK,EAAE,SAAS;4BAChByN,UAAU,EAAE;0BACd,CAAE;0BAAAxN,QAAA,EACCsJ,YAAY,CAACgC,OAAO,CAACpK,MAAM,GAAG,EAAE,GAC7B,GAAGoI,YAAY,CAACgC,OAAO,CAACmE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7CnG,YAAY,CAACgC;wBAAO;0BAAAlL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ7D,OAAA;0BAAK2C,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE,QAAQ;4BACblC,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,gBACAtD,OAAA,CAACnB,QAAQ;4BAACmS,IAAI,EAAE;0BAAG;4BAAAtN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB7D,OAAA;4BAAAsD,QAAA,EAAO,IAAI+L,IAAI,CAACzC,YAAY,CAACoG,UAAU,CAAC,CAACC,kBAAkB,CAAC;0BAAC;4BAAAvP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhED+I,YAAY,CAACC,eAAe;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAED2F,mBAAmB,CAAChF,MAAM,GAAG,CAAC,iBAC7BxE,OAAA;kBAAQ2C,KAAK,EAAE;oBACb2C,KAAK,EAAE,MAAM;oBACbuG,OAAO,EAAE,SAAS;oBAClB/H,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,KAAK;oBACnBwG,UAAU,EAAE,OAAO;oBACnBvI,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE;kBACd,CAAE;kBACF5C,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,SAAS;oBAC5C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACFxO,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,OAAO;oBAC1C1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAAC4O,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAAjO,QAAA,GAAC,WACQ,EAACkG,mBAAmB,CAAChF,MAAM,EAAC,eACvC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH7D,OAAA;gBAAK2C,KAAK,EAAE;kBACVkJ,OAAO,EAAE,WAAW;kBACpBtI,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,gBACAtD,OAAA,CAACpB,GAAG;kBAACoS,IAAI,EAAE,EAAG;kBAACrO,KAAK,EAAE;oBAAEa,YAAY,EAAE,QAAQ;oBAAE0P,OAAO,EAAE;kBAAI;gBAAE;kBAAAxP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE7D,OAAA;kBAAG2C,KAAK,EAAE;oBAAEkO,MAAM,EAAE,CAAC;oBAAEpN,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,EAAC;gBAE/C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YAAEoO,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAAtN,QAAA,GAEpC,CAAC7C,OAAO,IAAImJ,eAAe,kBAC1B5J,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpByI,SAAS,EAAE;YACb,CAAE;YAAArI,QAAA,eACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBlD,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA;gBAAK2C,KAAK,EAAE;kBACV2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,MAAM,EAAE,kCAAkC;kBAC1CqP,SAAS,EAAE,mBAAmB;kBAC9B/N,YAAY,EAAE,KAAK;kBACnBgO,SAAS,EAAE;gBACb;cAAE;gBAAA1P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT7D,OAAA;gBAAG2C,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,MAAM;kBAChBM,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAClD,KAAK,IAAImJ,aAAa,KAAK,CAACrJ,OAAO,IAAI,CAACmJ,eAAe,iBACvD5J,OAAA;YAAK2C,KAAK,EAAE;cACVkJ,OAAO,EAAE,MAAM;cACfD,UAAU,EAAE,wBAAwB;cACpC9H,MAAM,EAAE,kCAAkC;cAC1CsB,YAAY,EAAE,MAAM;cACpB7B,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdqG,UAAU,EAAE,wBAAwB;gBACpCxG,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB0N,MAAM,EAAE;cACV,CAAE;cAAAvN,QAAA,eACAtD,OAAA,CAAClB,aAAa;gBAACkS,IAAI,EAAE,EAAG;gBAAC3N,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN7D,OAAA;cAAI2C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChBwN,MAAM,EAAE,cAAc;gBACtBpN,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7D,OAAA;cAAG2C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChBwN,MAAM,EAAE,cAAc;gBACtBpN,QAAQ,EAAE;cACZ,CAAE;cAAAH,QAAA,EACC3C,KAAK,IAAImJ;YAAa;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ7D,OAAA;cACEmG,OAAO,EAAEA,CAAA,KAAM;gBACb0B,oBAAoB,CAAC,CAAC;gBACtBmC,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFrH,KAAK,EAAE;gBACLiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpByG,OAAO,EAAE,gBAAgB;gBACzBpI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACF5N,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAArN,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAACpD,OAAO,IAAI,CAACmJ,eAAe,IAAI,CAACjJ,KAAK,IAAI,CAACmJ,aAAa,IACxDuG,oBAAoB,CAAC7L,MAAM,KAAK,CAAC,IAAI8L,aAAa,CAAC9L,MAAM,KAAK,CAAC,iBAC9DxE,OAAA;YAAK2C,KAAK,EAAE;cACVkJ,OAAO,EAAE,WAAW;cACpBtI,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdqG,UAAU,EAAE,mDAAmD;gBAC/DxG,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB0N,MAAM,EAAE;cACV,CAAE;cAAAvN,QAAA,eACAtD,OAAA,CAACtB,SAAS;gBAACsS,IAAI,EAAE,EAAG;gBAAC3N,KAAK,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN7D,OAAA;cAAI2C,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChBwN,MAAM,EAAE,YAAY;gBACpBpN,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7D,OAAA;cAAG2C,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChBwN,MAAM,EAAE,YAAY;gBACpBpN,QAAQ,EAAE,MAAM;gBAChBqN,UAAU,EAAE,KAAK;gBACjBhF,QAAQ,EAAE,OAAO;gBACjBuH,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAhQ,QAAA,EACCkF,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAAC2E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtI,OAAA;cACEmG,OAAO,EAAEA,CAAA,KAAM;gBACbsC,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACF5F,KAAK,EAAE;gBACLiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpByG,OAAO,EAAE,gBAAgB;gBACzBpI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACF5N,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAArN,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAMA,CAACpD,OAAO,IAAI,CAACmJ,eAAe,KAAKyG,oBAAoB,CAAC7L,MAAM,GAAG,CAAC,IAAI8L,aAAa,CAAC9L,MAAM,GAAG,CAAC,CAAC,iBAC5FxE,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAECgN,aAAa,CAAC9L,MAAM,GAAG,CAAC,iBACvBxE,OAAA,CAAAE,SAAA;cAAAoD,QAAA,EACGgN,aAAa,CAACjK,GAAG,CAACqE,KAAK,iBACtB1K,OAAA;gBAEE2C,KAAK,EAAE;kBACLiJ,UAAU,EAAE,2BAA2B;kBACvCxG,YAAY,EAAE,MAAM;kBACpByG,OAAO,EAAE,QAAQ;kBACjB/H,MAAM,EAAE,8BAA8B;kBACtCyP,cAAc,EAAE,YAAY;kBAC5B5C,SAAS,EAAE,gCAAgC;kBAC3CjL,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACF5N,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAArN,QAAA,gBAGFtD,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACV2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdqG,UAAU,EAAE,mDAAmD;sBAC/DxG,YAAY,EAAE,MAAM;sBACpBnC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBgP,UAAU,EAAE;oBACd,CAAE;oBAAA7O,QAAA,eACAtD,OAAA,CAACnB,QAAQ;sBAACmS,IAAI,EAAE,EAAG;sBAAC3N,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEoO,IAAI,EAAE;oBAAE,CAAE;oBAAAzN,QAAA,gBACtBtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMoJ,eAAe,GAAGhC,KAAK,CAACkI,aAAa,IAAI,cAAc;wBAC7D,MAAMY,YAAY,GAAG/G,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAM+G,aAAa,GAAGD,YAAY,CAAChH,IAAI;wBAEvC,oBACExM,OAAA;0BAAM2C,KAAK,EAAE;4BACXiJ,UAAU,EAAE4H,YAAY,CAAC5H,UAAU;4BACnCvI,KAAK,EAAE,OAAO;4BACdI,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjB8H,OAAO,EAAE,iBAAiB;4BAC1BzG,YAAY,EAAE,MAAM;4BACpBsO,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtB1Q,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACAtD,OAAA,CAACyT,aAAa;4BAACzC,IAAI,EAAE,EAAG;4BAAC3N,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxC6I,eAAe;wBAAA;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,EAGH6G,KAAK,CAACkJ,eAAe,iBACpB5T,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,QAAQ;0BACbiG,UAAU,EAAE,yBAAyB;0BACrCC,OAAO,EAAE,iBAAiB;0BAC1BzG,YAAY,EAAE,MAAM;0BACpB3B,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACCoH,KAAK,CAACmJ,kBAAkB,gBACvB7T,OAAA;0BACEiE,GAAG,EAAE3F,WAAW,CAACoM,KAAK,CAACmJ,kBAAkB,CAAC,IAAI,EAAG;0BACjDnR,GAAG,EAAEgI,KAAK,CAACkJ,eAAgB;0BAC3BjR,KAAK,EAAE;4BACL2C,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdH,YAAY,EAAE,KAAK;4BACnBK,SAAS,EAAE;0BACb,CAAE;0BACFqM,OAAO,EAAG5N,CAAC,IAAK;4BACd,MAAMoH,MAAM,GAAGpH,CAAC,CAACoH,MAA0B;4BAC3CA,MAAM,CAAC3I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC/B;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7D,OAAA,CAACH,IAAI;0BAACmR,IAAI,EAAE;wBAAG;0BAAAtN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAClB,eACD7D,OAAA;0BAAM2C,KAAK,EAAE;4BAAEoB,UAAU,EAAE;0BAAM,CAAE;0BAAAT,QAAA,EAChCoH,KAAK,CAACkJ;wBAAe;0BAAAlQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,SAAS;0BACdtC,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,gBACAtD,OAAA,CAACnB,QAAQ;0BAACmS,IAAI,EAAE;wBAAG;0BAAAtN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAIwL,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACoD,kBAAkB,CAAC,OAAO,EAAE;0BACtDa,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAAvQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN7D,OAAA;sBAAI2C,KAAK,EAAE;wBACTkO,MAAM,EAAE,cAAc;wBACtBpN,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChByN,UAAU,EAAE;sBACd,CAAE;sBAAAxN,QAAA,EACCoH,KAAK,CAAC+D;oBAAK;sBAAA/K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAAC,MAAM;kBACN;kBACA,MAAMqQ,cAAwB,GAAG,EAAE;kBAEnC,IAAKxJ,KAAK,CAASrG,MAAM,IAAKqG,KAAK,CAASrG,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DkG,KAAK,CAASrG,MAAM,CAAC8P,OAAO,CAAEjI,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACtG,SAAS,EAAE;wBACjB;wBACA,MAAMrF,QAAQ,GAAGjC,WAAW,CAAC4N,GAAG,CAACtG,SAAS,CAAC;wBAC3C,IAAIrF,QAAQ,EAAE;0BACZ2T,cAAc,CAACE,IAAI,CAAC7T,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAO2T,cAAc,CAAC1P,MAAM,GAAG,CAAC,gBAC9BxE,OAAA;oBAAK2C,KAAK,EAAE;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAAF,QAAA,eACnCtD,OAAA,CAAC5B,oBAAoB;sBACnBiG,MAAM,EAAE6P,cAAc,CAAChJ,MAAM,CAACiB,OAAO,CAAc;sBACnD7H,SAAS,EAAEoG,KAAK,CAAC+D,KAAM;sBACvB4F,UAAU,EAAE,CAAE;sBACd9P,YAAY,EAAGQ,KAAK,IAAK;wBACvB,MAAMuP,cAAc,GAAGJ,cAAc,CAAChJ,MAAM,CAACiB,OAAO,CAAa;wBACjEC,oBAAoB,CAACkI,cAAc,EAAEvP,KAAK,CAAC;sBAC7C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,EAGH6G,KAAK,CAACyE,WAAW,iBAChBnP,OAAA;kBAAK2C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnBqN,UAAU,EAAE,KAAK;oBACjBtN,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCoH,KAAK,CAACyE;gBAAW;kBAAAzL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbkG,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,0BAA0B;oBACtCxG,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE;kBACZ,CAAE;kBAAAH,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAtD,OAAA,CAACnB,QAAQ;sBAACmS,IAAI,EAAE;oBAAG;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB7D,OAAA;sBAAAsD,QAAA,EACGoH,KAAK,CAACsF,QAAQ,IAAItF,KAAK,CAACsF,QAAQ,KAAKtF,KAAK,CAACmF,UAAU,GAClD,GAAG,IAAIR,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACoD,kBAAkB,CAAC,CAAC,MAAM,IAAI5D,IAAI,CAAC3E,KAAK,CAACsF,QAAQ,CAAC,CAACiD,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAI5D,IAAI,CAAC3E,KAAK,CAACmF,UAAU,CAAC,CAACoD,kBAAkB,CAAC;oBAAC;sBAAAvP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAEL6G,KAAK,CAACkI,aAAa,iBAClB5S,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,eACAtD,OAAA;sBAAM2C,KAAK,EAAE;wBACXkJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAE,yBAAyB;wBACrCxG,YAAY,EAAE,KAAK;wBACnB3B,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCoH,KAAK,CAACkI;oBAAa;sBAAAlP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGN7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjBuQ,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE,8BAA8B;oBACzClQ,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBAEAtD,OAAA;oBACEmG,OAAO,EAAEA,CAAA,KAAMqH,wBAAwB,CAAC9C,KAAK,CAAE;oBAC/C/H,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbiG,UAAU,EAAE,MAAM;sBAClB9H,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAGqH,KAAK,CAAS+C,gBAAgB,GAAG,SAAS,GAAG,SAAS;sBAC9DpI,MAAM,EAAE,SAAS;sBACjBwG,OAAO,EAAE,QAAQ;sBACjBzG,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;oBAC1D,CAAE;oBACF7I,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;oBAC3C,CAAE;oBAAAtI,QAAA,gBAEFtD,OAAA,CAACjB,KAAK;sBACJiS,IAAI,EAAE,EAAG;sBACTwD,IAAI,EAAG9J,KAAK,CAAS+C,gBAAgB,GAAG,SAAS,GAAG;oBAAO;sBAAA/J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACF7D,OAAA;sBAAAsD,QAAA,EAAQoH,KAAK,CAASkD,cAAc,IAAI;oBAAC;sBAAAlK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,EAGP6G,KAAK,CAAS+J,cAAc,iBAC5BzU,OAAA;oBACEmG,OAAO,EAAEA,CAAA,KAAM0C,uBAAuB,CACpCD,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,IAAI,GAAGF,KAAK,CAACE,WAC5D,CAAE;oBACFjI,KAAK,EAAE;sBACLM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbiG,UAAU,EAAE,MAAM;sBAClB9H,MAAM,EAAE,MAAM;sBACdT,KAAK,EAAEuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;sBACzEvF,MAAM,EAAE,SAAS;sBACjBwG,OAAO,EAAE,QAAQ;sBACjBzG,YAAY,EAAE,KAAK;sBACnBM,UAAU,EAAE,eAAe;sBAC3BjC,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE;oBACd,CAAE;oBACFjB,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;sBACxD1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBACF7H,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;sBACzC1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGuF,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBAClG,CAAE;oBAAAtH,QAAA,gBAEFtD,OAAA,CAAClB,aAAa;sBAACkS,IAAI,EAAE;oBAAG;sBAAAtN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B7D,OAAA;sBAAAsD,QAAA,EAAQoH,KAAK,CAASgK,aAAa,IAAI;oBAAC;sBAAAhR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGL+E,oBAAoB,KAAK8B,KAAK,CAACE,WAAW,IAAKF,KAAK,CAAS+J,cAAc,iBAC1EzU,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjBuQ,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE;kBACb,CAAE;kBAAA7P,QAAA,EACC4D,WAAW,KAAK,OAAO,gBACtBlH,OAAA,CAAChC,mBAAmB;oBAClB2W,UAAU,EAAEjK,KAAK,CAACE,WAAY;oBAC9BgK,aAAa,EAAGlK,KAAK,CAAS+J,cAAe;oBAC7CI,aAAa,EAAEzN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/B+H,eAAe,EAAC;kBAAO;oBAAApR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF7D,OAAA,CAAC/B,cAAc;oBACb0W,UAAU,EAAEjK,KAAK,CAACE,WAAY;oBAC9BgK,aAAa,EAAGlK,KAAK,CAAS+J,cAAe;oBAC7CI,aAAa,EAAEzN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/B+H,eAAe,EAAC;kBAAS;oBAAApR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GAnUI,SAAS6G,KAAK,CAACE,WAAW,EAAE;gBAAAlH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoU9B,CACN;YAAC,gBACF,CACH,EAGAwM,oBAAoB,CAAC7L,MAAM,GAAG,CAAC,iBAC9BxE,OAAA,CAAAE,SAAA;cAAAoD,QAAA,EACG+M,oBAAoB,CAAChK,GAAG,CAACuG,YAAY,iBACpC5M,OAAA;gBAEE+M,EAAE,EAAE,gBAAgBH,YAAY,CAACC,eAAe,EAAG;gBACnDjK,SAAS,EAAEsF,kBAAkB,IAAIC,YAAY,KAAK,gBAAgByE,YAAY,CAACC,eAAe,EAAE,GAAG,qCAAqC,GAAG,EAAG;gBAC9IlK,KAAK,EAAE;kBACLiJ,UAAU,EAAE,2BAA2B;kBACvCxG,YAAY,EAAE,MAAM;kBACpByG,OAAO,EAAE,QAAQ;kBACjB/H,MAAM,EAAE8I,YAAY,CAACxB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClCmI,cAAc,EAAE,YAAY;kBAC5B5C,SAAS,EAAE/D,YAAY,CAACxB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpC1F,UAAU,EAAE,2CAA2C;kBACvDR,QAAQ,EAAE;gBACZ,CAAE;gBACFpC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG/D,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFrI,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgO,SAAS,GAAG/D,YAAY,CAACxB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAA9H,QAAA,GAGDsJ,YAAY,CAACxB,SAAS,iBACrBpL,OAAA;kBAAK2C,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACb2F,UAAU,EAAE,mDAAmD;oBAC/DvI,KAAK,EAAE,OAAO;oBACdwI,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBd,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,SAAS;oBACdgL,SAAS,EAAE;kBACb,CAAE;kBAAArN,QAAA,gBACAtD,OAAA,CAACpB,GAAG;oBAACoS,IAAI,EAAE;kBAAG;oBAAAtN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,GACC,CAAC,MAAM;oBACN,IAAIsJ,YAAY,CAAC+F,QAAQ,EAAE;sBACzB,oBACE3S,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdqG,UAAU,EAAE,mDAAmD;0BAC/DxG,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBgP,UAAU,EAAE;wBACd,CAAE;wBAAA7O,QAAA,eACAtD,OAAA,CAACZ,aAAa;0BAAC4R,IAAI,EAAE,EAAG;0BAAC3N,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAMyI,YAAY,GAAG,CAACM,YAAY,CAACgG,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAGzG,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAMmH,aAAa,GAAGX,aAAa,CAACtG,IAAI;sBAExC,oBACExM,OAAA;wBAAK2C,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdqG,UAAU,EAAEkH,aAAa,CAAClH,UAAU;0BACpCxG,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBgP,UAAU,EAAE;wBACd,CAAE;wBAAA7O,QAAA,eACAtD,OAAA,CAACyT,aAAa;0BAACzC,IAAI,EAAE,EAAG;0BAAC3N,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ7D,OAAA;oBAAK2C,KAAK,EAAE;sBAAEoO,IAAI,EAAE;oBAAE,CAAE;oBAAAzN,QAAA,gBACtBtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE,QAAQ;wBACtBuR,QAAQ,EAAE;sBACZ,CAAE;sBAAAzR,QAAA,GACC,CAAC,MAAM;wBACN,IAAIsJ,YAAY,CAAC+F,QAAQ,EAAE;0BACzB,oBACE3S,OAAA;4BAAM2C,KAAK,EAAE;8BACXiJ,UAAU,EAAE,mDAAmD;8BAC/DvI,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjB8H,OAAO,EAAE,iBAAiB;8BAC1BzG,YAAY,EAAE,MAAM;8BACpBsO,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB1Q,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtD,OAAA,CAACZ,aAAa;8BAAC4R,IAAI,EAAE,EAAG;8BAAC3N,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAMyI,YAAY,GAAG,CAACM,YAAY,CAACgG,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAGzG,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMmH,aAAa,GAAGX,aAAa,CAACtG,IAAI;0BAExC,oBACExM,OAAA;4BAAM2C,KAAK,EAAE;8BACXiJ,UAAU,EAAEkH,aAAa,CAAClH,UAAU;8BACpCvI,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjB8H,OAAO,EAAE,iBAAiB;8BAC1BzG,YAAY,EAAE,MAAM;8BACpBsO,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB1Q,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtD,OAAA,CAACyT,aAAa;8BAACzC,IAAI,EAAE,EAAG;8BAAC3N,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCyI,YAAY;0BAAA;4BAAA5I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEH+I,YAAY,CAACqC,WAAW,iBACvBjP,OAAA;wBAAM2C,KAAK,EAAE;0BACXiJ,UAAU,EAAE,yBAAyB;0BACrCvI,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,SAAS;0BACnBM,UAAU,EAAE,KAAK;0BACjB8H,OAAO,EAAE,iBAAiB;0BAC1BzG,YAAY,EAAE;wBAChB,CAAE;wBAAA9B,QAAA,GAAC,QACK,EAACsJ,YAAY,CAACqC,WAAW;sBAAA;wBAAAvL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,EAGA+I,YAAY,CAACoI,WAAW,iBACvBhV,OAAA;wBAAK2C,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,QAAQ;0BACbiG,UAAU,EAAE,wBAAwB;0BACpCC,OAAO,EAAE,iBAAiB;0BAC1BzG,YAAY,EAAE,MAAM;0BACpB3B,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,GACCsJ,YAAY,CAACqI,cAAc,gBAC1BjV,OAAA;0BACEiE,GAAG,EAAE3F,WAAW,CAACsO,YAAY,CAACqI,cAAc,CAAC,IAAI,EAAG;0BACpDvS,GAAG,EAAEkK,YAAY,CAACoI,WAAY;0BAC9BrS,KAAK,EAAE;4BACL2C,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdH,YAAY,EAAE,KAAK;4BACnBK,SAAS,EAAE;0BACb,CAAE;0BACFqM,OAAO,EAAG5N,CAAC,IAAK;4BACd,MAAMoH,MAAM,GAAGpH,CAAC,CAACoH,MAA0B;4BAC3CA,MAAM,CAAC3I,KAAK,CAACM,OAAO,GAAG,MAAM;0BAC/B;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEF7D,OAAA,CAACH,IAAI;0BAACmR,IAAI,EAAE;wBAAG;0BAAAtN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAClB,eACD7D,OAAA;0BAAM2C,KAAK,EAAE;4BAAEoB,UAAU,EAAE;0BAAM,CAAE;0BAAAT,QAAA,EAChCsJ,YAAY,CAACoI;wBAAW;0BAAAtR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,eAED7D,OAAA;wBAAK2C,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAI+L,IAAI,CAACzC,YAAY,CAACoG,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BAC7Da,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAAvQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN7D,OAAA;sBAAI2C,KAAK,EAAE;wBACTkO,MAAM,EAAE,cAAc;wBACtBpN,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChByN,UAAU,EAAE;sBACd,CAAE;sBAAAxN,QAAA,EACCsJ,YAAY,CAAC6B;oBAAK;sBAAA/K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL+I,YAAY,CAAC9B,WAAW,IAAI8B,YAAY,CAAC9B,WAAW,CAACtG,MAAM,GAAG,CAAC,iBAC9DxE,OAAA,CAACoE,YAAY;kBACXC,MAAM,EAAEuI,YAAY,CAAC9B,WAAY;kBACjCxG,SAAS,EAAEsI,YAAY,CAAC6B,KAAM;kBAC9BpO,QAAQ,EAAE6G,WAAY;kBACtB3C,YAAY,EAAGQ,KAAK,IAAK;oBACvBgH,YAAY,CAACa,YAAY,CAAC9B,WAAW,IAAI,EAAE,EAAE/F,KAAK,CAAC;kBACrD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnBqN,UAAU,EAAE,KAAK;oBACjBtN,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCsJ,YAAY,CAACgC;gBAAO;kBAAAlL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGN7D,OAAA;kBAAK2C,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B0I,OAAO,EAAE,MAAM;oBACfD,UAAU,EAAE,qBAAqB;oBACjCxG,YAAY,EAAE,MAAM;oBACpB5B,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAtD,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEAtD,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAMwG,gBAAgB,CAACC,YAAY,CAAE;sBAC9CjK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbiG,UAAU,EAAE,MAAM;wBAClB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEuJ,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDzH,MAAM,EAAE,SAAS;wBACjBwG,OAAO,EAAE,QAAQ;wBACjBzG,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACF7I,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAAtI,QAAA,gBAEFtD,OAAA,CAACjB,KAAK;wBACJiS,IAAI,EAAE,EAAG;wBACTwD,IAAI,EAAE5H,YAAY,CAACE,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAApJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF7D,OAAA;wBAAAsD,QAAA,EAAOsJ,YAAY,CAACgB,cAAc,IAAI;sBAAC;wBAAAlK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGR+I,YAAY,CAAC6H,cAAc,iBAC1BzU,OAAA;sBACEmG,OAAO,EAAEA,CAAA,KAAMwC,eAAe,CAC5BD,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,IAAI,GAAGD,YAAY,CAACC,eACtE,CAAE;sBACFlK,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbiG,UAAU,EAAE,MAAM;wBAClB9H,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEqF,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5ExH,MAAM,EAAE,SAAS;wBACjBwG,OAAO,EAAE,QAAQ;wBACjBzG,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,qBAAqB;wBACxD1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFN,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACiJ,UAAU,GAAG,MAAM;wBACzC1H,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGqF,YAAY,KAAKkE,YAAY,CAACC,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAAvJ,QAAA,gBAEFtD,OAAA,CAAClB,aAAa;wBAACkS,IAAI,EAAE;sBAAG;wBAAAtN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B7D,OAAA;wBAAAsD,QAAA,EAAOsJ,YAAY,CAAC8H,aAAa,IAAI;sBAAC;wBAAAhR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGE,CAAC,eAGN7D,OAAA;oBAAK2C,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,MAAM;sBACXlC,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAtD,OAAA;sBAAK2C,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtD,OAAA,CAAChB,KAAK;wBAACgS,IAAI,EAAE;sBAAG;wBAAAtN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB7D,OAAA;wBAAAsD,QAAA,GAAM,YAAU,EAAEsJ,YAAY,CAASsI,cAAc,IAAItI,YAAY,CAACoI,WAAW,IAAI,OAAO;sBAAA;wBAAAtR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjG,CAAC,eAEN7D,OAAA;sBAAK2C,KAAK,EAAE;wBACVkJ,OAAO,EAAE,gBAAgB;wBACzBD,UAAU,EAAEgB,YAAY,CAAC1K,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BmB,KAAK,EAAEuJ,YAAY,CAAC1K,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClEkD,YAAY,EAAE,KAAK;wBACnBrB,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCsJ,YAAY,CAAC1K;oBAAM;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL6E,YAAY,KAAKkE,YAAY,CAACC,eAAe,IAAID,YAAY,CAAC6H,cAAc,iBAC3EzU,OAAA;kBAAK2C,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjBuQ,UAAU,EAAE,MAAM;oBAClBpB,SAAS,EAAE;kBACb,CAAE;kBAAA7P,QAAA,EACC4D,WAAW,KAAK,OAAO,gBACtBlH,OAAA,CAAChC,mBAAmB;oBAClBmX,cAAc,EAAEvI,YAAY,CAACC,eAAgB;oBAC7C+H,aAAa,EAAEhI,YAAY,CAAC6H,cAAe;oBAC3CI,aAAa,EAAEzN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/B+H,eAAe,EAAC;kBAAO;oBAAApR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,gBAEF7D,OAAA,CAAC/B,cAAc;oBACbkX,cAAc,EAAEvI,YAAY,CAACC,eAAgB;oBAC7C+H,aAAa,EAAEhI,YAAY,CAAC6H,cAAe;oBAC3CI,aAAa,EAAEzN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2F,EAAG;oBAC/B+H,eAAe,EAAC;kBAAS;oBAAApR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GAhYI,gBAAgB+I,YAAY,CAACC,eAAe,EAAE;gBAAAnJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiYhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLiF,kBAAkB,iBACjB9I,OAAA;MAAK2C,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBsN,MAAM,EAAE,IAAI;QACZ5E,OAAO,EAAE;MACX,CAAE;MACF1F,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC,IAAI,CAAE;MAAAzF,QAAA,eAEzCtD,OAAA;QAAK2C,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpB0G,QAAQ,EAAE,OAAO;UACjBxG,KAAK,EAAE,MAAM;UACb8P,SAAS,EAAE,MAAM;UACjBjQ,QAAQ,EAAE,MAAM;UAChBwL,SAAS,EAAE;QACb,CAAE;QACFxK,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACmR,eAAe,CAAC,CAAE;QAAA/R,QAAA,gBAGlCtD,OAAA;UAAK2C,KAAK,EAAE;YACVkJ,OAAO,EAAE,QAAQ;YACjB6E,YAAY,EAAE,mBAAmB;YACjCzN,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACAtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACAtD,OAAA,CAACpB,GAAG;cAACoS,IAAI,EAAE,EAAG;cAACrO,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C7D,OAAA;cAAI2C,KAAK,EAAE;gBACTkO,MAAM,EAAE,CAAC;gBACTpN,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN7D,OAAA;YACEmG,OAAO,EAAEA,CAAA,KAAM4C,qBAAqB,CAAC,IAAI,CAAE;YAC3CpG,KAAK,EAAE;cACLiJ,UAAU,EAAE,MAAM;cAClB9H,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClBJ,KAAK,EAAE,SAAS;cAChBgC,MAAM,EAAE,SAAS;cACjBwG,OAAO,EAAE,SAAS;cAClBzG,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UAAK2C,KAAK,EAAE;YAAEkJ,OAAO,EAAE;UAAS,CAAE;UAAAvI,QAAA,gBAChCtD,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,SAAS;cACdnC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,GACC,CAAC,MAAM;cACN,IAAIwF,kBAAkB,CAAC6J,QAAQ,EAAE;gBAC/B,oBACE3S,OAAA;kBAAM2C,KAAK,EAAE;oBACXiJ,UAAU,EAAE,mDAAmD;oBAC/DvI,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjB8H,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpBsO,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtB1Q,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtD,OAAA,CAACZ,aAAa;oBAAC4R,IAAI,EAAE,EAAG;oBAAC3N,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAEX,CAAC,MAAM;gBACL,MAAMyI,YAAY,GAAG,CAACxD,kBAAkB,CAAC8J,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;gBAClF,MAAMC,aAAa,GAAGzG,gBAAgB,CAACC,YAAY,CAAC;gBACpD,MAAMmH,aAAa,GAAGX,aAAa,CAACtG,IAAI;gBAExC,oBACExM,OAAA;kBAAM2C,KAAK,EAAE;oBACXiJ,UAAU,EAAEkH,aAAa,CAAClH,UAAU;oBACpCvI,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjB8H,OAAO,EAAE,iBAAiB;oBAC1BzG,YAAY,EAAE,MAAM;oBACpBsO,aAAa,EAAE,WAAW;oBAC1BC,aAAa,EAAE,OAAO;oBACtB1Q,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtD,OAAA,CAACyT,aAAa;oBAACzC,IAAI,EAAE,EAAG;oBAAC3N,KAAK,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxCyI,YAAY;gBAAA;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAEX;YACF,CAAC,EAAE,CAAC,eAEJ7D,OAAA;cAAM2C,KAAK,EAAE;gBACXiJ,UAAU,EAAE,mDAAmD;gBAC/DvI,KAAK,EAAE,OAAO;gBACdwI,OAAO,EAAE,iBAAiB;gBAC1BzG,YAAY,EAAE,MAAM;gBACpB3B,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBd,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA,CAACpB,GAAG;gBAACoS,IAAI,EAAE;cAAG;gBAAAtN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN7D,OAAA;YAAI2C,KAAK,EAAE;cACTkO,MAAM,EAAE,YAAY;cACpBpN,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE,SAAS;cAChByN,UAAU,EAAE;YACd,CAAE;YAAAxN,QAAA,EACCwF,kBAAkB,CAAC2F;UAAK;YAAA/K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EAGJiF,kBAAkB,CAACgC,WAAW,IAAIhC,kBAAkB,CAACgC,WAAW,CAACtG,MAAM,GAAG,CAAC,iBAC1ExE,OAAA;YAAK2C,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,eACrCtD,OAAA,CAACoE,YAAY;cACXC,MAAM,EAAEyE,kBAAkB,CAACgC,WAAY;cACvCxG,SAAS,EAAEwE,kBAAkB,CAAC2F,KAAM;cACpCpO,QAAQ,EAAE6G,WAAY;cACtB3C,YAAY,EAAGQ,KAAK,IAAK;gBACvBgH,YAAY,CAACjD,kBAAkB,CAACgC,WAAW,EAAE/F,KAAK,CAAC;cACrD;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED7D,OAAA;YAAK2C,KAAK,EAAE;cACVU,KAAK,EAAE,SAAS;cAChBI,QAAQ,EAAE,MAAM;cAChBqN,UAAU,EAAE,KAAK;cACjBtN,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACCwF,kBAAkB,CAAC8F;UAAO;YAAAlL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEN7D,OAAA;YAAK2C,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXlC,QAAQ,EAAE,UAAU;cACpBJ,KAAK,EAAE,SAAS;cAChBkR,UAAU,EAAE,MAAM;cAClBpB,SAAS,EAAE;YACb,CAAE;YAAA7P,QAAA,gBACAtD,OAAA;cAAK2C,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtD,OAAA,CAACnB,QAAQ;gBAACmS,IAAI,EAAE;cAAG;gBAAAtN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB7D,OAAA;gBAAAsD,QAAA,GAAM,aAAW,EAAC,IAAI+L,IAAI,CAACvG,kBAAkB,CAACkK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAvP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACLiF,kBAAkB,CAACkM,WAAW,iBAC7BhV,OAAA;cAAAsD,QAAA,GAAK,MACC,EAACwF,kBAAkB,CAACkM,WAAW;YAAA;cAAAtR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7D,OAAA,CAAC3B,aAAa;MACZgG,MAAM,EAAE+E,cAAe;MACvB4C,YAAY,EAAE1C,oBAAqB;MACnCgM,MAAM,EAAEpM,YAAa;MACrBqM,OAAO,EAAEA,CAAA,KAAMpM,eAAe,CAAC,KAAK,CAAE;MACtC7E,SAAS,EAAC;IAAoB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC+C,GAAA,CA9pEID,QAAiC;EAAA,QACpBnJ,WAAW,EAeLG,aAAa,EAUhCC,gBAAgB,EASyBC,qBAAqB;AAAA;AAAA2X,GAAA,GAnC9D7O,QAAiC;AAgqEvC,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAAuC,GAAA,EAAA8O,GAAA;AAAAC,YAAA,CAAAtR,EAAA;AAAAsR,YAAA,CAAA/O,GAAA;AAAA+O,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}