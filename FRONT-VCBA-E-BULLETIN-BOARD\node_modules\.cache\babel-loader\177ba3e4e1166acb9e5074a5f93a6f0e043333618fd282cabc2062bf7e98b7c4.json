{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 3H5\",\n  key: \"1236rx\"\n}], [\"path\", {\n  d: \"M12 21V7\",\n  key: \"gj6g52\"\n}], [\"path\", {\n  d: \"m6 15 6 6 6-6\",\n  key: \"h15q88\"\n}]];\nconst ArrowDownFromLine = createLucideIcon(\"arrow-down-from-line\", __iconNode);\nexport { __iconNode, ArrowDownFromLine as default };\n//# sourceMappingURL=arrow-down-from-line.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}