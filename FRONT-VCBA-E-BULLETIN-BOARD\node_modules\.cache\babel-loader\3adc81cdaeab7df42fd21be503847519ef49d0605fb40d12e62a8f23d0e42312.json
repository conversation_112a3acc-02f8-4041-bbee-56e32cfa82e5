{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z\",\n  key: \"3c2336\"\n}], [\"path\", {\n  d: \"m15 9-6 6\",\n  key: \"1uzhvr\"\n}], [\"path\", {\n  d: \"M9 9h.01\",\n  key: \"1q5me6\"\n}], [\"path\", {\n  d: \"M15 15h.01\",\n  key: \"lqbp3k\"\n}]];\nconst BadgePercent = createLucideIcon(\"badge-percent\", __iconNode);\nexport { __iconNode, BadgePercent as default };\n//# sourceMappingURL=badge-percent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}