!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryDom={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var r={},n={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>"["+(38+e)+";5;"+t+"m"},r=function(e){return void 0===e&&(e=0),(t,r,n)=>"["+(38+e)+";2;"+t+";"+r+";"+n+"m"};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,a]of Object.entries(r))n[t]={open:"["+a[0]+"m",close:"["+a[1]+"m"},r[t]=n[t],e.set(a[0],a[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(n);var a={};Object.defineProperty(a,"__esModule",{value:!0}),a.printIteratorEntries=function(e,t,r,n,a,o,l){void 0===l&&(l=": ");let i="",u=e.next();if(!u.done){i+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){i+=s+o(u.value[0],t,s,n,a)+l+o(u.value[1],t,s,n,a),u=e.next(),u.done?t.min||(i+=","):i+=","+t.spacingInner}i+=t.spacingOuter+r}return i},a.printIteratorValues=function(e,t,r,n,a,o){let l="",i=e.next();if(!i.done){l+=t.spacingOuter;const u=r+t.indent;for(;!i.done;)l+=u+o(i.value,t,u,n,a),i=e.next(),i.done?t.min||(l+=","):l+=","+t.spacingInner;l+=t.spacingOuter+r}return l},a.printListItems=function(e,t,r,n,a,o){let l="";if(e.length){l+=t.spacingOuter;const i=r+t.indent;for(let r=0;r<e.length;r++)l+=i,r in e&&(l+=o(e[r],t,i,n,a)),r<e.length-1?l+=","+t.spacingInner:t.min||(l+=",");l+=t.spacingOuter+r}return l},a.printObjectProperties=function(e,t,r,n,a,l){let i="";const u=o(e,t.compareKeys);if(u.length){i+=t.spacingOuter;const o=r+t.indent;for(let r=0;r<u.length;r++){const s=u[r];i+=o+l(s,t,o,n,a)+": "+l(e[s],t,o,n,a),r<u.length-1?i+=","+t.spacingInner:t.min||(i+=",")}i+=t.spacingOuter+r}return i};const o=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var l={};Object.defineProperty(l,"__esModule",{value:!0}),l.test=l.serialize=l.default=void 0;var i=a,u="undefined"!=typeof globalThis?globalThis:void 0!==u?u:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),s=u["jest-symbol-do-not-touch"]||u.Symbol;const d="function"==typeof s&&s.for?s.for("jest.asymmetricMatcher"):1267621,c=" ",p=(e,t,r,n,a,o)=>{const l=e.toString();return"ArrayContaining"===l||"ArrayNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+c+"["+(0,i.printListItems)(e.sample,t,r,n,a,o)+"]":"ObjectContaining"===l||"ObjectNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+c+"{"+(0,i.printObjectProperties)(e.sample,t,r,n,a,o)+"}":"StringMatching"===l||"StringNotMatching"===l||"StringContaining"===l||"StringNotContaining"===l?l+c+o(e.sample,t,r,n,a):e.toAsymmetricMatcher()};l.serialize=p;const m=e=>e&&e.$$typeof===d;l.test=m;var f={serialize:p,test:m};l.default=f;var b={};Object.defineProperty(b,"__esModule",{value:!0}),b.test=b.serialize=b.default=void 0;var v=h((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),y=h(n.exports);function h(e){return e&&e.__esModule?e:{default:e}}const g=e=>"string"==typeof e&&!!e.match((0,v.default)());b.test=g;const C=(e,t,r,n,a,o)=>o(e.replace((0,v.default)(),(e=>{switch(e){case y.default.red.close:case y.default.green.close:case y.default.cyan.close:case y.default.gray.close:case y.default.white.close:case y.default.yellow.close:case y.default.bgRed.close:case y.default.bgGreen.close:case y.default.bgYellow.close:case y.default.inverse.close:case y.default.dim.close:case y.default.bold.close:case y.default.reset.open:case y.default.reset.close:return"</>";case y.default.red.open:return"<red>";case y.default.green.open:return"<green>";case y.default.cyan.open:return"<cyan>";case y.default.gray.open:return"<gray>";case y.default.white.open:return"<white>";case y.default.yellow.open:return"<yellow>";case y.default.bgRed.open:return"<bgRed>";case y.default.bgGreen.open:return"<bgGreen>";case y.default.bgYellow.open:return"<bgYellow>";case y.default.inverse.open:return"<inverse>";case y.default.dim.open:return"<dim>";case y.default.bold.open:return"<bold>";default:return""}})),t,r,n,a);b.serialize=C;var q={serialize:C,test:g};b.default=q;var P={};Object.defineProperty(P,"__esModule",{value:!0}),P.test=P.serialize=P.default=void 0;var E=a;const w=["DOMStringMap","NamedNodeMap"],x=/^(HTML\w*Collection|NodeList)$/,R=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==w.indexOf(t)||x.test(t));var t};P.test=R;const O=(e,t,r,n,a,o)=>{const l=e.constructor.name;return++n>t.maxDepth?"["+l+"]":(t.min?"":l+" ")+(-1!==w.indexOf(l)?"{"+(0,E.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,a,o)+"}":"["+(0,E.printListItems)(Array.from(e),t,r,n,a,o)+"]")};P.serialize=O;var T={serialize:O,test:R};P.default=T;var _={},M={},A={};Object.defineProperty(A,"__esModule",{value:!0}),A.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(M,"__esModule",{value:!0}),M.printText=M.printProps=M.printElementAsLeaf=M.printElement=M.printComment=M.printChildren=void 0;var j,S=(j=A)&&j.__esModule?j:{default:j};M.printProps=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")};M.printChildren=(e,t,r,n,a,o)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?I(e,t):o(e,t,r,n,a)))).join("");const I=(e,t)=>{const r=t.colors.content;return r.open+(0,S.default)(e)+r.close};M.printText=I;M.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,S.default)(e)+"--\x3e"+r.close};M.printElement=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close};M.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(_,"__esModule",{value:!0}),_.test=_.serialize=_.default=void 0;var B=M;const N=/^((HTML|SVG)\w*)?Element$/,F=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(N.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function k(e){return 11===e.nodeType}_.test=F;const L=(e,t,r,n,a,o)=>{if(function(e){return 3===e.nodeType}(e))return(0,B.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,B.printComment)(e.data,t);const l=k(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,B.printElementAsLeaf)(l,t):(0,B.printElement)(l,(0,B.printProps)(k(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),k(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,a,o),(0,B.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,a,o),t,r)};_.serialize=L;var U={serialize:L,test:F};_.default=U;var H={};Object.defineProperty(H,"__esModule",{value:!0}),H.test=H.serialize=H.default=void 0;var D=a;const z="@@__IMMUTABLE_ORDERED__@@",V=e=>"Immutable."+e,W=e=>"["+e+"]",$=" ";const G=(e,t,r,n,a,o,l)=>++n>t.maxDepth?W(V(l)):V(l)+$+"["+(0,D.printIteratorValues)(e.values(),t,r,n,a,o)+"]",Q=(e,t,r,n,a,o)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,a,o,l)=>++n>t.maxDepth?W(V(l)):V(l)+$+"{"+(0,D.printIteratorEntries)(e.entries(),t,r,n,a,o)+"}")(e,t,r,n,a,o,e[z]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?G(e,t,r,n,a,o,"List"):e["@@__IMMUTABLE_SET__@@"]?G(e,t,r,n,a,o,e[z]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?G(e,t,r,n,a,o,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,a,o)=>{const l=V("Seq");return++n>t.maxDepth?W(l):e["@@__IMMUTABLE_KEYED__@@"]?l+$+"{"+(e._iter||e._object?(0,D.printIteratorEntries)(e.entries(),t,r,n,a,o):"…")+"}":l+$+"["+(e._iter||e._array||e._collection||e._iterable?(0,D.printIteratorValues)(e.values(),t,r,n,a,o):"…")+"]"})(e,t,r,n,a,o):((e,t,r,n,a,o)=>{const l=V(e._name||"Record");return++n>t.maxDepth?W(l):l+$+"{"+(0,D.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,a,o)+"}"})(e,t,r,n,a,o);H.serialize=Q;const X=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);H.test=X;var J={serialize:Q,test:X};H.default=J;var K,Y={},Z={exports:{}},ee={};!function(e){e.exports=function(){if(K)return ee;K=1;var e=60103,t=60106,r=60107,n=60108,a=60114,o=60109,l=60110,i=60112,u=60113,s=60120,d=60115,c=60116,p=60121,m=60122,f=60117,b=60129,v=60131;if("function"==typeof Symbol&&Symbol.for){var y=Symbol.for;e=y("react.element"),t=y("react.portal"),r=y("react.fragment"),n=y("react.strict_mode"),a=y("react.profiler"),o=y("react.provider"),l=y("react.context"),i=y("react.forward_ref"),u=y("react.suspense"),s=y("react.suspense_list"),d=y("react.memo"),c=y("react.lazy"),p=y("react.block"),m=y("react.server.block"),f=y("react.fundamental"),b=y("react.debug_trace_mode"),v=y("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var m=p.$$typeof;switch(m){case e:switch(p=p.type){case r:case a:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case l:case i:case c:case d:case o:return p;default:return m}}case t:return m}}}var g=o,C=e,q=i,P=r,E=c,w=d,x=t,R=a,O=n,T=u;return ee.ContextConsumer=l,ee.ContextProvider=g,ee.Element=C,ee.ForwardRef=q,ee.Fragment=P,ee.Lazy=E,ee.Memo=w,ee.Portal=x,ee.Profiler=R,ee.StrictMode=O,ee.Suspense=T,ee.isAsyncMode=function(){return!1},ee.isConcurrentMode=function(){return!1},ee.isContextConsumer=function(e){return h(e)===l},ee.isContextProvider=function(e){return h(e)===o},ee.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},ee.isForwardRef=function(e){return h(e)===i},ee.isFragment=function(e){return h(e)===r},ee.isLazy=function(e){return h(e)===c},ee.isMemo=function(e){return h(e)===d},ee.isPortal=function(e){return h(e)===t},ee.isProfiler=function(e){return h(e)===a},ee.isStrictMode=function(e){return h(e)===n},ee.isSuspense=function(e){return h(e)===u},ee.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===a||e===b||e===n||e===u||e===s||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===c||e.$$typeof===d||e.$$typeof===o||e.$$typeof===l||e.$$typeof===i||e.$$typeof===f||e.$$typeof===p||e[0]===m)},ee.typeOf=h,ee}()}(Z),Object.defineProperty(Y,"__esModule",{value:!0}),Y.test=Y.serialize=Y.default=void 0;var te=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=ne(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(Z.exports),re=M;function ne(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(ne=function(e){return e?r:t})(e)}const ae=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{ae(e,t)})):null!=e&&!1!==e&&t.push(e),t},oe=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(te.isFragment(e))return"React.Fragment";if(te.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(te.isContextProvider(e))return"Context.Provider";if(te.isContextConsumer(e))return"Context.Consumer";if(te.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(te.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},le=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,re.printElementAsLeaf)(oe(e),t):(0,re.printElement)(oe(e),(0,re.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,a,o),(0,re.printChildren)(ae(e.props.children),t,r+t.indent,n,a,o),t,r);Y.serialize=le;const ie=e=>null!=e&&te.isElement(e);Y.test=ie;var ue={serialize:le,test:ie};Y.default=ue;var se={};Object.defineProperty(se,"__esModule",{value:!0}),se.test=se.serialize=se.default=void 0;var de=M,ce="undefined"!=typeof globalThis?globalThis:void 0!==ce?ce:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),pe=ce["jest-symbol-do-not-touch"]||ce.Symbol;const me="function"==typeof pe&&pe.for?pe.for("react.test.json"):245830487,fe=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,de.printElementAsLeaf)(e.type,t):(0,de.printElement)(e.type,e.props?(0,de.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,a,o):"",e.children?(0,de.printChildren)(e.children,t,r+t.indent,n,a,o):"",t,r);se.serialize=fe;const be=e=>e&&e.$$typeof===me;se.test=be;var ve={serialize:fe,test:be};se.default=ve,Object.defineProperty(r,"__esModule",{value:!0});var ye=r.default=r.DEFAULT_OPTIONS=void 0,he=r.format=rt,ge=r.plugins=void 0,Ce=_e(n.exports),qe=a,Pe=_e(l),Ee=_e(b),we=_e(P),xe=_e(_),Re=_e(H),Oe=_e(Y),Te=_e(se);function _e(e){return e&&e.__esModule?e:{default:e}}const Me=Object.prototype.toString,Ae=Date.prototype.toISOString,je=Error.prototype.toString,Se=RegExp.prototype.toString,Ie=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",Be=e=>"undefined"!=typeof window&&e===window,Ne=/^Symbol\((.*)\)(.*)$/,Fe=/\n/gi;class ke extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function Le(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Ue(e){return String(e).replace(Ne,"Symbol($1)")}function He(e){return"["+je.call(e)+"]"}function De(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;if("number"===a)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===a)return function(e){return String(e+"n")}(e);if("string"===a)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===a)return Le(e,t);if("symbol"===a)return Ue(e);const o=Me.call(e);return"[object WeakMap]"===o?"WeakMap {}":"[object WeakSet]"===o?"WeakSet {}":"[object Function]"===o||"[object GeneratorFunction]"===o?Le(e,t):"[object Symbol]"===o?Ue(e):"[object Date]"===o?isNaN(+e)?"Date { NaN }":Ae.call(e):"[object Error]"===o?He(e):"[object RegExp]"===o?r?Se.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):Se.call(e):e instanceof Error?He(e):null}function ze(e,t,r,n,a,o){if(-1!==a.indexOf(e))return"[Circular]";(a=a.slice()).push(e);const l=++n>t.maxDepth,i=t.min;if(t.callToJSON&&!l&&e.toJSON&&"function"==typeof e.toJSON&&!o)return $e(e.toJSON(),t,r,n,a,!0);const u=Me.call(e);return"[object Arguments]"===u?l?"[Arguments]":(i?"":"Arguments ")+"["+(0,qe.printListItems)(e,t,r,n,a,$e)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?l?"["+e.constructor.name+"]":(i?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,qe.printListItems)(e,t,r,n,a,$e)+"]":"[object Map]"===u?l?"[Map]":"Map {"+(0,qe.printIteratorEntries)(e.entries(),t,r,n,a,$e," => ")+"}":"[object Set]"===u?l?"[Set]":"Set {"+(0,qe.printIteratorValues)(e.values(),t,r,n,a,$e)+"}":l||Be(e)?"["+Ie(e)+"]":(i?"":t.printBasicPrototype||"Object"!==Ie(e)?Ie(e)+" ":"")+"{"+(0,qe.printObjectProperties)(e,t,r,n,a,$e)+"}"}function Ve(e,t,r,n,a,o){let l;try{l=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,a,o,$e):e.print(t,(e=>$e(e,r,n,a,o)),(e=>{const t=n+r.indent;return t+e.replace(Fe,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new ke(e.message,e.stack)}if("string"!=typeof l)throw new Error('pretty-format: Plugin must return type "string" but instead returned "'+typeof l+'".');return l}function We(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new ke(e.message,e.stack)}return null}function $e(e,t,r,n,a,o){const l=We(t.plugins,e);if(null!==l)return Ve(l,e,t,r,n,a);const i=De(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==i?i:ze(e,t,r,n,a,o)}const Ge={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},Qe=Object.keys(Ge),Xe={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:Ge};var Je=r.DEFAULT_OPTIONS=Xe;const Ke=e=>Qe.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:Ge[r],a=n&&Ce.default[n];if(!a||"string"!=typeof a.close||"string"!=typeof a.open)throw new Error('pretty-format: Option "theme" has a key "'+r+'" whose value "'+n+'" is undefined in ansi-styles.');return t[r]=a,t}),Object.create(null)),Ye=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:Xe.printFunctionName,Ze=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:Xe.escapeRegex,et=e=>e&&void 0!==e.escapeString?e.escapeString:Xe.escapeString,tt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:Xe.callToJSON,colors:e&&e.highlight?Ke(e):Qe.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:Xe.compareKeys,escapeRegex:Ze(e),escapeString:et(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:Xe.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:Xe.maxDepth,min:e&&void 0!==e.min?e.min:Xe.min,plugins:e&&void 0!==e.plugins?e.plugins:Xe.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:Ye(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function rt(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!Xe.hasOwnProperty(e))throw new Error('pretty-format: Unknown option "'+e+'".')})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error('pretty-format: Option "theme" must be of type "object" but instead received "'+typeof e.theme+'".')}}(t),t.plugins)){const r=We(t.plugins,e);if(null!==r)return Ve(r,e,tt(t),"",0,[])}const r=De(e,Ye(t),Ze(t),et(t));return null!==r?r:ze(e,tt(t),"",0,[])}const nt={AsymmetricMatcher:Pe.default,ConvertAnsi:Ee.default,DOMCollection:we.default,DOMElement:xe.default,Immutable:Re.default,ReactElement:Oe.default,ReactTestComponent:Te.default};ge=r.plugins=nt;var at=rt;ye=r.default=at;var ot=t({__proto__:null,get DEFAULT_OPTIONS(){return Je},format:he,get plugins(){return ge},get default(){return ye}},[r]);function lt(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const it=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")},ut=(e,t,r,n,a,o)=>e.map((e=>{const l="string"==typeof e?st(e,t):o(e,t,r,n,a);return""===l&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+l})).join(""),st=(e,t)=>{const r=t.colors.content;return r.open+lt(e)+r.close},dt=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+lt(e)+"--\x3e"+r.close},ct=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close},pt=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},mt=3,ft=8,bt=11,vt=/^((HTML|SVG)\w*)?Element$/,yt=e=>{const{tagName:t}=e;return Boolean("string"==typeof t&&t.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is"))},ht=e=>{const t=e.constructor.name,{nodeType:r}=e;return 1===r&&(vt.test(t)||yt(e))||r===mt&&"Text"===t||r===ft&&"Comment"===t||r===bt&&"DocumentFragment"===t};function gt(e){return e.nodeType===bt}function Ct(e){return{test:e=>{var t;return((null==e||null==(t=e.constructor)?void 0:t.name)||yt(e))&&ht(e)},serialize:(t,r,n,a,o,l)=>{if(function(e){return e.nodeType===mt}(t))return st(t.data,r);if(function(e){return e.nodeType===ft}(t))return dt(t.data,r);const i=gt(t)?"DocumentFragment":t.tagName.toLowerCase();return++a>r.maxDepth?pt(i,r):ct(i,it(gt(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),gt(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,a,o,l),ut(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,a,o,l),r,n)}}}let qt=null,Pt=null,Et=null;try{const e=module&&module.require;Pt=e.call(module,"fs").readFileSync,Et=e.call(module,"@babel/code-frame").codeFrameColumns,qt=e.call(module,"chalk")}catch{}function wt(){if(!Pt||!Et)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),a=n.split(":"),[o,l,i]=[a[0],parseInt(a[1],10),parseInt(a[2],10)];let u="";try{u=Pt(o,"utf-8")}catch{return""}const s=Et(u,{start:{line:l,column:i}},{highlightCode:!0,linesBelow:0});return qt.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const xt=3;function Rt(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Ot(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Tt(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function _t(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const Mt=()=>{if("undefined"==typeof process)return!1;let e;try{var t;const r=null==(t=process.env)?void 0:t.COLORS;r&&(e=JSON.parse(r))}catch{}return"boolean"==typeof e?e:void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:At}=ge,jt=1,St=8;function It(e){return e.nodeType!==St&&(e.nodeType!==jt||!e.matches(kt().defaultIgnore))}function Bt(e,t,r){if(void 0===r&&(r={}),e||(e=Ot().body),"number"!=typeof t&&("undefined"!=typeof process&&process.env,t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:a=It,...o}=r,l=he(e,{plugins:[Ct(a),At],printFunctionName:!1,highlight:Mt(),...o});return void 0!==t&&e.outerHTML.length>t?l.slice(0,t)+"...":l}const Nt=function(){const e=wt();e?console.log(Bt(...arguments)+"\n\n"+e):console.log(Bt(...arguments))};let Ft={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=Bt(t),n=new Error([e,"Ignored nodes: comments, "+Ft.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function kt(){return Ft}const Lt=["button","meter","output","progress","select","textarea","input"];function Ut(e){return Lt.includes(e.nodeName.toLowerCase())?"":e.nodeType===xt?e.textContent:Array.from(e.childNodes).map((e=>Ut(e))).join("")}function Ht(e){let t;return t="label"===e.tagName.toLowerCase()?Ut(e):e.value||e.textContent,t}function Dt(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function zt(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const a=t.getAttribute("aria-labelledby"),o=a?a.split(" "):[];return o.length?o.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Ht(r),formControl:null}:{content:"",formControl:null}})):Array.from(Dt(t)).map((e=>({content:Ht(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function Vt(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Wt(e,t,r,n){if("string"!=typeof e)return!1;Vt(r);const a=n(e);return"string"==typeof r||"number"==typeof r?a.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(a,t):Xt(r,a)}function $t(e,t,r,n){if("string"!=typeof e)return!1;Vt(r);const a=n(e);return r instanceof Function?r(a,t):r instanceof RegExp?Xt(r,a):a===String(r)}function Gt(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function Qt(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Gt({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function Xt(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Jt(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===xt&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}var Kt=Object.prototype.toString;function Yt(e){return"function"==typeof e||"[object Function]"===Kt.call(e)}var Zt=Math.pow(2,53)-1;function er(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Zt)}function tr(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!Yt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var a,o=er(n.length),l=Yt(r)?Object(new r(o)):new Array(o),i=0;i<o;)a=n[i],l[i]=t?t(a,i):a,i+=1;return l.length=o,l}function rr(e){return rr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rr(e)}function nr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ar(n.key),n)}}function ar(e){var t=function(e,t){if("object"!==rr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===rr(t)?t:String(t)}var or=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=ar(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&nr(t.prototype,r),n&&nr(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),lr="undefined"==typeof Set?Set:or;function ir(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var ur={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},sr={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function dr(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=sr[t])&&void 0!==n&&n.has(r))}))}(e,t)}function cr(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=ur[ir(e)];if(void 0!==t)return t;switch(ir(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||dr(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||dr(e,r||""))return r}return t}function pr(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function mr(e){return pr(e)&&"caption"===ir(e)}function fr(e){return pr(e)&&"input"===ir(e)}function br(e){return pr(e)&&"legend"===ir(e)}function vr(e){return function(e){return pr(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===ir(e)}function yr(e,t){if(pr(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function hr(e,t){return!!pr(e)&&-1!==t.indexOf(cr(e))}function gr(e,t){if(!pr(e))return!1;if("range"===t)return hr(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Cr(e,t){var r=tr(e.querySelectorAll(t));return yr(e,"aria-owns").forEach((function(e){r.push.apply(r,tr(e.querySelectorAll(t)))})),r}function qr(e){return pr(t=e)&&"select"===ir(t)?e.selectedOptions||Cr(e,"[selected]"):Cr(e,'[aria-selected="true"]');var t}function Pr(e){return fr(e)||pr(t=e)&&"textarea"===ir(t)?e.value:e.textContent||"";var t}function Er(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function wr(e){var t=ir(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function xr(e){if(wr(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&pr(e)){var r=xr(e);null!==r&&(t=r)}})),t}function Rr(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):xr(e)}function Or(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new lr,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),a=t.compute,o=void 0===a?"name":a,l=t.computedStyleSupportsPseudoElements,i=void 0===l?void 0!==t.getComputedStyle:l,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,d=t.hidden,c=void 0!==d&&d;function p(e,t){var r,n,a="";if(pr(e)&&i){var o=Er(s(e,"::before"));a="".concat(o," ").concat(a)}if((function(e){return pr(e)&&"slot"===ir(e)}(e)?0===(n=(r=e).assignedNodes()).length?tr(r.childNodes):n:tr(e.childNodes).concat(yr(e,"aria-owns"))).forEach((function(e){var r=b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(pr(e)?s(e).getPropertyValue("display"):"inline")?" ":"";a+="".concat(n).concat(r).concat(n)})),pr(e)&&i){var l=Er(s(e,"::after"));a="".concat(a," ").concat(l)}return a.trim()}function m(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function f(e){if(!pr(e))return null;if(function(e){return pr(e)&&"fieldset"===ir(e)}(e)){r.add(e);for(var t=tr(e.childNodes),n=0;n<t.length;n+=1){var a=t[n];if(br(a))return b(a,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return pr(e)&&"table"===ir(e)}(e)){r.add(e);for(var o=tr(e.childNodes),l=0;l<o.length;l+=1){var i=o[l];if(mr(i))return b(i,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return pr(e)&&"svg"===ir(e)}(e)){r.add(e);for(var u=tr(e.childNodes),s=0;s<u.length;s+=1){var d=u[s];if(vr(d))return d.textContent}return null}if("img"===ir(e)||"area"===ir(e)){var c=m(e,"alt");if(null!==c)return c}else if(function(e){return pr(e)&&"optgroup"===ir(e)}(e)){var f=m(e,"label");if(null!==f)return f}}if(fr(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var v=m(e,"value");if(null!==v)return v;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var y,h,g=null===(h=(y=e).labels)?h:void 0!==h?tr(h):wr(y)?tr(y.ownerDocument.querySelectorAll("label")).filter((function(e){return Rr(e)===y})):null;if(null!==g&&0!==g.length)return r.add(e),tr(g).map((function(e){return b(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(fr(e)&&"image"===e.type){var C=m(e,"alt");if(null!==C)return C;var q=m(e,"title");return null!==q?q:"Submit Query"}if(hr(e,["button"])){var P=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==P)return P}return null}function b(e,t){if(r.has(e))return"";if(!c&&function(e,t){if(!pr(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=pr(e)?e.getAttributeNode("aria-labelledby"):null,a=null===n||r.has(n)?[]:yr(e,"aria-labelledby");if("name"===o&&!t.isReferenced&&a.length>0)return r.add(n),a.map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var l,i=t.recursion&&(hr(l=e,["button","combobox","listbox","textbox"])||gr(l,"range"))&&"name"===o;if(!i){var u=(pr(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===o)return r.add(e),u;if(!function(e){return hr(e,["none","presentation"])}(e)){var d=f(e);if(null!==d)return r.add(e),d}}if(hr(e,["menu"]))return r.add(e),"";if(i||t.isEmbeddedInLabel||t.isReferenced){if(hr(e,["combobox","listbox"])){r.add(e);var v=qr(e);return 0===v.length?fr(e)?e.value:"":tr(v).map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(gr(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(hr(e,["textbox"]))return r.add(e),Pr(e)}if(function(e){return hr(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||pr(e)&&t.isReferenced||function(e){return mr(e)}(e)){var y=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==y)return r.add(e),y}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return pr(e)?m(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return b(e,{isEmbeddedInLabel:!1,isReferenced:"description"===o,recursion:!1}).trim().replace(/\s\s+/g," ")}function Tr(e){return Tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr(e)}function _r(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Mr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_r(Object(r),!0).forEach((function(t){Ar(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_r(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ar(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Tr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Tr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Tr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=yr(e,"aria-describedby").map((function(e){return Or(e,Mr(Mr({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function Sr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return hr(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":Or(e,t)}var Ir={},Br={},Nr={},Fr={};Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.default=void 0;var kr=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};Fr.default=kr,Object.defineProperty(Nr,"__esModule",{value:!0}),Nr.default=function(e,t){"function"==typeof Symbol&&"symbol"===Ur(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:Lr.default.bind(t)});return e};var Lr=function(e){return e&&e.__esModule?e:{default:e}}(Fr);function Ur(e){return Ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ur(e)}Object.defineProperty(Br,"__esModule",{value:!0}),Br.default=void 0;var Hr=function(e){return e&&e.__esModule?e:{default:e}}(Nr);function Dr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||zr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zr(e,t){if(e){if("string"==typeof e)return Vr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Vr(e,t):void 0}}function Vr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Wr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-braillelabel",{type:"string"}],["aria-brailleroledescription",{type:"string"}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-description",{type:"string"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],$r={entries:function(){return Wr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=zr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(Wr);try{for(n.s();!(t=n.n()).done;){var a=Dr(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Wr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Wr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!$r.get(e)},keys:function(){return Wr.map((function(e){return Dr(e,1)[0]}))},values:function(){return Wr.map((function(e){return Dr(e,2)[1]}))}},Gr=(0,Hr.default)($r,$r.entries());Br.default=Gr;var Qr={};Object.defineProperty(Qr,"__esModule",{value:!0}),Qr.default=void 0;var Xr=function(e){return e&&e.__esModule?e:{default:e}}(Nr);function Jr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||Kr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kr(e,t){if(e){if("string"==typeof e)return Yr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Yr(e,t):void 0}}function Yr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Zr=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],en={entries:function(){return Zr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Kr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(Zr);try{for(n.s();!(t=n.n()).done;){var a=Jr(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Zr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Zr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!en.get(e)},keys:function(){return Zr.map((function(e){return Jr(e,1)[0]}))},values:function(){return Zr.map((function(e){return Jr(e,2)[1]}))}},tn=(0,Xr.default)(en,en.entries());Qr.default=tn;var rn={},nn={},an={};Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var on={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};an.default=on;var ln={};Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=void 0;var un={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};ln.default=un;var sn={};Object.defineProperty(sn,"__esModule",{value:!0}),sn.default=void 0;var dn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};sn.default=dn;var cn={};Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};cn.default=pn;var mn={};Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var fn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};mn.default=fn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var vn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};bn.default=vn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var hn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};yn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Cn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};gn.default=Cn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var Pn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};qn.default=Pn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var wn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};En.default=wn;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var Rn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};xn.default=Rn;var On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var Tn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};On.default=Tn,Object.defineProperty(nn,"__esModule",{value:!0}),nn.default=void 0;var _n=Hn(an),Mn=Hn(ln),An=Hn(sn),jn=Hn(cn),Sn=Hn(mn),In=Hn(bn),Bn=Hn(yn),Nn=Hn(gn),Fn=Hn(qn),kn=Hn(En),Ln=Hn(xn),Un=Hn(On);function Hn(e){return e&&e.__esModule?e:{default:e}}var Dn=[["command",_n.default],["composite",Mn.default],["input",An.default],["landmark",jn.default],["range",Sn.default],["roletype",In.default],["section",Bn.default],["sectionhead",Nn.default],["select",Fn.default],["structure",kn.default],["widget",Ln.default],["window",Un.default]];nn.default=Dn;var zn={},Vn={};Object.defineProperty(Vn,"__esModule",{value:!0}),Vn.default=void 0;var Wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Vn.default=Wn;var $n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Gn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};$n.default=Gn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Qn.default=Xn;var Jn={};Object.defineProperty(Jn,"__esModule",{value:!0}),Jn.default=void 0;var Kn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Jn.default=Kn;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0}),Yn.default=void 0;var Zn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Yn.default=Zn;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"blockquote"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};ra.default=na;var aa={};Object.defineProperty(aa,"__esModule",{value:!0}),aa.default=void 0;var oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"caption"},module:"HTML"}],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};aa.default=oa;var la={};Object.defineProperty(la,"__esModule",{value:!0}),la.default=void 0;var ia={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["ancestor table element has table role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};la.default=ia;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};ua.default=sa;var da={};Object.defineProperty(da,"__esModule",{value:!0}),da.default=void 0;var ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"code"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};da.default=ca;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var ma={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"col"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"colgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};pa.default=ma;var fa={};Object.defineProperty(fa,"__esModule",{value:!0}),fa.default=void 0;var ba={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],constraints:["the multiple attribute is not set and the size attribute does not have a value greater than 1"],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};fa.default=ba;var va={};Object.defineProperty(va,"__esModule",{value:!0}),va.default=void 0;var ya={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-label"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};va.default=ya;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ha.default=ga;var Ca={};Object.defineProperty(Ca,"__esModule",{value:!0}),Ca.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ca.default=qa;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ea={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"del"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Pa.default=Ea;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};wa.default=xa;var Ra={};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.default=void 0;var Oa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Ra.default=Oa;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var _a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"html"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ta.default=_a;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0}),Ma.default=void 0;var Aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"em"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ma.default=Aa;var ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};ja.default=Sa;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0}),Ia.default=void 0;var Ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ia.default=Ba;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var Fa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Na.default=Fa;var ka={};Object.defineProperty(ka,"__esModule",{value:!0}),ka.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"a"},module:"HTML"},{concept:{name:"area"},module:"HTML"},{concept:{name:"aside"},module:"HTML"},{concept:{name:"b"},module:"HTML"},{concept:{name:"bdo"},module:"HTML"},{concept:{name:"body"},module:"HTML"},{concept:{name:"data"},module:"HTML"},{concept:{name:"div"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"footer"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"header"},module:"HTML"},{concept:{name:"hgroup"},module:"HTML"},{concept:{name:"i"},module:"HTML"},{concept:{name:"pre"},module:"HTML"},{concept:{name:"q"},module:"HTML"},{concept:{name:"samp"},module:"HTML"},{concept:{name:"section"},module:"HTML"},{concept:{name:"small"},module:"HTML"},{concept:{name:"span"},module:"HTML"},{concept:{name:"u"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ka.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Ha={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};Ua.default=Ha;var Da={};Object.defineProperty(Da,"__esModule",{value:!0}),Da.default=void 0;var za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{constraints:["ancestor table element has grid role","ancestor table element has treegrid role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};Da.default=za;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var Wa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"},{concept:{name:"address"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Va.default=Wa;var $a={};Object.defineProperty($a,"__esModule",{value:!0}),$a.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};$a.default=Ga;var Qa={};Object.defineProperty(Qa,"__esModule",{value:!0}),Qa.default=void 0;var Xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qa.default=Xa;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"ins"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ja.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"href"}],name:"area"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Ya.default=Za;var eo={};Object.defineProperty(eo,"__esModule",{value:!0}),eo.default=void 0;var to={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};eo.default=to;var ro={};Object.defineProperty(ro,"__esModule",{value:!0}),ro.default=void 0;var no={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"}],constraints:["the size attribute value is greater than 1"],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ro.default=no;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol","direct descendant of ul","direct descendant of menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ao.default=oo;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};lo.default=io;var uo={};Object.defineProperty(uo,"__esModule",{value:!0}),uo.default=void 0;var so={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};uo.default=so;var co={};Object.defineProperty(co,"__esModule",{value:!0}),co.default=void 0;var po={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:[],props:{"aria-braillelabel":null,"aria-brailleroledescription":null,"aria-description":null},relatedConcepts:[{concept:{name:"mark"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};co.default=po;var mo={};Object.defineProperty(mo,"__esModule",{value:!0}),mo.default=void 0;var fo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};mo.default=fo;var bo={};Object.defineProperty(bo,"__esModule",{value:!0}),bo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bo.default=vo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var ho={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};yo.default=ho;var go={};Object.defineProperty(go,"__esModule",{value:!0}),go.default=void 0;var Co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};go.default=Co;var qo={};Object.defineProperty(qo,"__esModule",{value:!0}),qo.default=void 0;var Po={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};qo.default=Po;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Eo.default=wo;var xo={};Object.defineProperty(xo,"__esModule",{value:!0}),xo.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};xo.default=Ro;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var To={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{name:"meter"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};Oo.default=To;var _o={};Object.defineProperty(_o,"__esModule",{value:!0}),_o.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};_o.default=Mo;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var jo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Ao.default=jo;var So={};Object.defineProperty(So,"__esModule",{value:!0}),So.default=void 0;var Io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};So.default=Io;var Bo={};Object.defineProperty(Bo,"__esModule",{value:!0}),Bo.default=void 0;var No={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};Bo.default=No;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.default=void 0;var ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"p"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fo.default=ko;var Lo={};Object.defineProperty(Lo,"__esModule",{value:!0}),Lo.default=void 0;var Uo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{attributes:[{name:"alt",value:""}],name:"img"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Lo.default=Uo;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var Do={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};Ho.default=Do;var zo={};Object.defineProperty(zo,"__esModule",{value:!0}),zo.default=void 0;var Vo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};zo.default=Vo;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var $o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Wo.default=$o;var Go={};Object.defineProperty(Go,"__esModule",{value:!0}),Go.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Go.default=Qo;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Jo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Xo.default=Jo;var Ko={};Object.defineProperty(Ko,"__esModule",{value:!0}),Ko.default=void 0;var Yo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};Ko.default=Yo;var Zo={};Object.defineProperty(Zo,"__esModule",{value:!0}),Zo.default=void 0;var el={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Zo.default=el;var tl={};Object.defineProperty(tl,"__esModule",{value:!0}),tl.default=void 0;var rl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};tl.default=rl;var nl={};Object.defineProperty(nl,"__esModule",{value:!0}),nl.default=void 0;var al={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};nl.default=al;var ol={};Object.defineProperty(ol,"__esModule",{value:!0}),ol.default=void 0;var ll={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};ol.default=ll;var il={};Object.defineProperty(il,"__esModule",{value:!0}),il.default=void 0;var ul={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};il.default=ul;var sl={};Object.defineProperty(sl,"__esModule",{value:!0}),sl.default=void 0;var dl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};sl.default=dl;var cl={};Object.defineProperty(cl,"__esModule",{value:!0}),cl.default=void 0;var pl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};cl.default=pl;var ml={};Object.defineProperty(ml,"__esModule",{value:!0}),ml.default=void 0;var fl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ml.default=fl;var bl={};Object.defineProperty(bl,"__esModule",{value:!0}),bl.default=void 0;var vl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"strong"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bl.default=vl;var yl={};Object.defineProperty(yl,"__esModule",{value:!0}),yl.default=void 0;var hl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sub"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};yl.default=hl;var gl={};Object.defineProperty(gl,"__esModule",{value:!0}),gl.default=void 0;var Cl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};gl.default=Cl;var ql={};Object.defineProperty(ql,"__esModule",{value:!0}),ql.default=void 0;var Pl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};ql.default=Pl;var El={};Object.defineProperty(El,"__esModule",{value:!0}),El.default=void 0;var wl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};El.default=wl;var xl={};Object.defineProperty(xl,"__esModule",{value:!0}),xl.default=void 0;var Rl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};xl.default=Rl;var Ol={};Object.defineProperty(Ol,"__esModule",{value:!0}),Ol.default=void 0;var Tl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Ol.default=Tl;var _l={};Object.defineProperty(_l,"__esModule",{value:!0}),_l.default=void 0;var Ml={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_l.default=Ml;var Al={};Object.defineProperty(Al,"__esModule",{value:!0}),Al.default=void 0;var jl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Al.default=jl;var Sl={};Object.defineProperty(Sl,"__esModule",{value:!0}),Sl.default=void 0;var Il={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};Sl.default=Il;var Bl={};Object.defineProperty(Bl,"__esModule",{value:!0}),Bl.default=void 0;var Nl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"time"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Bl.default=Nl;var Fl={};Object.defineProperty(Fl,"__esModule",{value:!0}),Fl.default=void 0;var kl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};Fl.default=kl;var Ll={};Object.defineProperty(Ll,"__esModule",{value:!0}),Ll.default=void 0;var Ul={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};Ll.default=Ul;var Hl={};Object.defineProperty(Hl,"__esModule",{value:!0}),Hl.default=void 0;var Dl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Hl.default=Dl;var zl={};Object.defineProperty(zl,"__esModule",{value:!0}),zl.default=void 0;var Vl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};zl.default=Vl;var Wl={};Object.defineProperty(Wl,"__esModule",{value:!0}),Wl.default=void 0;var $l={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};Wl.default=$l;var Gl={};Object.defineProperty(Gl,"__esModule",{value:!0}),Gl.default=void 0;var Ql={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};Gl.default=Ql,Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Xl=wu(Vn),Jl=wu($n),Kl=wu(Qn),Yl=wu(Jn),Zl=wu(Yn),ei=wu(ea),ti=wu(ra),ri=wu(aa),ni=wu(la),ai=wu(ua),oi=wu(da),li=wu(pa),ii=wu(fa),ui=wu(va),si=wu(ha),di=wu(Ca),ci=wu(Pa),pi=wu(wa),mi=wu(Ra),fi=wu(Ta),bi=wu(Ma),vi=wu(ja),yi=wu(Ia),hi=wu(Na),gi=wu(ka),Ci=wu(Ua),qi=wu(Da),Pi=wu(Va),Ei=wu($a),wi=wu(Qa),xi=wu(Ja),Ri=wu(Ya),Oi=wu(eo),Ti=wu(ro),_i=wu(ao),Mi=wu(lo),Ai=wu(uo),ji=wu(co),Si=wu(mo),Ii=wu(bo),Bi=wu(yo),Ni=wu(go),Fi=wu(qo),ki=wu(Eo),Li=wu(xo),Ui=wu(Oo),Hi=wu(_o),Di=wu(Ao),zi=wu(So),Vi=wu(Bo),Wi=wu(Fo),$i=wu(Lo),Gi=wu(Ho),Qi=wu(zo),Xi=wu(Wo),Ji=wu(Go),Ki=wu(Xo),Yi=wu(Ko),Zi=wu(Zo),eu=wu(tl),tu=wu(nl),ru=wu(ol),nu=wu(il),au=wu(sl),ou=wu(cl),lu=wu(ml),iu=wu(bl),uu=wu(yl),su=wu(gl),du=wu(ql),cu=wu(El),pu=wu(xl),mu=wu(Ol),fu=wu(_l),bu=wu(Al),vu=wu(Sl),yu=wu(Bl),hu=wu(Fl),gu=wu(Ll),Cu=wu(Hl),qu=wu(zl),Pu=wu(Wl),Eu=wu(Gl);function wu(e){return e&&e.__esModule?e:{default:e}}var xu=[["alert",Xl.default],["alertdialog",Jl.default],["application",Kl.default],["article",Yl.default],["banner",Zl.default],["blockquote",ei.default],["button",ti.default],["caption",ri.default],["cell",ni.default],["checkbox",ai.default],["code",oi.default],["columnheader",li.default],["combobox",ii.default],["complementary",ui.default],["contentinfo",si.default],["definition",di.default],["deletion",ci.default],["dialog",pi.default],["directory",mi.default],["document",fi.default],["emphasis",bi.default],["feed",vi.default],["figure",yi.default],["form",hi.default],["generic",gi.default],["grid",Ci.default],["gridcell",qi.default],["group",Pi.default],["heading",Ei.default],["img",wi.default],["insertion",xi.default],["link",Ri.default],["list",Oi.default],["listbox",Ti.default],["listitem",_i.default],["log",Mi.default],["main",Ai.default],["mark",ji.default],["marquee",Si.default],["math",Ii.default],["menu",Bi.default],["menubar",Ni.default],["menuitem",Fi.default],["menuitemcheckbox",ki.default],["menuitemradio",Li.default],["meter",Ui.default],["navigation",Hi.default],["none",Di.default],["note",zi.default],["option",Vi.default],["paragraph",Wi.default],["presentation",$i.default],["progressbar",Gi.default],["radio",Qi.default],["radiogroup",Xi.default],["region",Ji.default],["row",Ki.default],["rowgroup",Yi.default],["rowheader",Zi.default],["scrollbar",eu.default],["search",tu.default],["searchbox",ru.default],["separator",nu.default],["slider",au.default],["spinbutton",ou.default],["status",lu.default],["strong",iu.default],["subscript",uu.default],["superscript",su.default],["switch",du.default],["tab",cu.default],["table",pu.default],["tablist",mu.default],["tabpanel",fu.default],["term",bu.default],["textbox",vu.default],["time",yu.default],["timer",hu.default],["toolbar",gu.default],["tooltip",Cu.default],["tree",qu.default],["treegrid",Pu.default],["treeitem",Eu.default]];zn.default=xu;var Ru={},Ou={};Object.defineProperty(Ou,"__esModule",{value:!0}),Ou.default=void 0;var Tu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ou.default=Tu;var _u={};Object.defineProperty(_u,"__esModule",{value:!0}),_u.default=void 0;var Mu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};_u.default=Mu;var Au={};Object.defineProperty(Au,"__esModule",{value:!0}),Au.default=void 0;var ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Au.default=ju;var Su={};Object.defineProperty(Su,"__esModule",{value:!0}),Su.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Su.default=Iu;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var Nu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Bu.default=Nu;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Fu.default=ku;var Lu={};Object.defineProperty(Lu,"__esModule",{value:!0}),Lu.default=void 0;var Uu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Lu.default=Uu;var Hu={};Object.defineProperty(Hu,"__esModule",{value:!0}),Hu.default=void 0;var Du={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Hu.default=Du;var zu={};Object.defineProperty(zu,"__esModule",{value:!0}),zu.default=void 0;var Vu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};zu.default=Vu;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var $u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wu.default=$u;var Gu={};Object.defineProperty(Gu,"__esModule",{value:!0}),Gu.default=void 0;var Qu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Gu.default=Qu;var Xu={};Object.defineProperty(Xu,"__esModule",{value:!0}),Xu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Xu.default=Ju;var Ku={};Object.defineProperty(Ku,"__esModule",{value:!0}),Ku.default=void 0;var Yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ku.default=Yu;var Zu={};Object.defineProperty(Zu,"__esModule",{value:!0}),Zu.default=void 0;var es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Zu.default=es;var ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ts.default=rs;var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.default=void 0;var as={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};ns.default=as;var os={};Object.defineProperty(os,"__esModule",{value:!0}),os.default=void 0;var ls={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};os.default=ls;var is={};Object.defineProperty(is,"__esModule",{value:!0}),is.default=void 0;var us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};is.default=us;var ss={};Object.defineProperty(ss,"__esModule",{value:!0}),ss.default=void 0;var ds={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ss.default=ds;var cs={};Object.defineProperty(cs,"__esModule",{value:!0}),cs.default=void 0;var ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};cs.default=ps;var ms={};Object.defineProperty(ms,"__esModule",{value:!0}),ms.default=void 0;var fs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ms.default=fs;var bs={};Object.defineProperty(bs,"__esModule",{value:!0}),bs.default=void 0;var vs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bs.default=vs;var ys={};Object.defineProperty(ys,"__esModule",{value:!0}),ys.default=void 0;var hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ys.default=hs;var gs={};Object.defineProperty(gs,"__esModule",{value:!0}),gs.default=void 0;var Cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};gs.default=Cs;var qs={};Object.defineProperty(qs,"__esModule",{value:!0}),qs.default=void 0;var Ps={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};qs.default=Ps;var Es={};Object.defineProperty(Es,"__esModule",{value:!0}),Es.default=void 0;var ws={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Es.default=ws;var xs={};Object.defineProperty(xs,"__esModule",{value:!0}),xs.default=void 0;var Rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};xs.default=Rs;var Os={};Object.defineProperty(Os,"__esModule",{value:!0}),Os.default=void 0;var Ts={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Os.default=Ts;var _s={};Object.defineProperty(_s,"__esModule",{value:!0}),_s.default=void 0;var Ms={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};_s.default=Ms;var As={};Object.defineProperty(As,"__esModule",{value:!0}),As.default=void 0;var js={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};As.default=js;var Ss={};Object.defineProperty(Ss,"__esModule",{value:!0}),Ss.default=void 0;var Is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Ss.default=Is;var Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.default=void 0;var Ns={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bs.default=Ns;var Fs={};Object.defineProperty(Fs,"__esModule",{value:!0}),Fs.default=void 0;var ks={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Fs.default=ks;var Ls={};Object.defineProperty(Ls,"__esModule",{value:!0}),Ls.default=void 0;var Us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ls.default=Us;var Hs={};Object.defineProperty(Hs,"__esModule",{value:!0}),Hs.default=void 0;var Ds={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};Hs.default=Ds;var zs={};Object.defineProperty(zs,"__esModule",{value:!0}),zs.default=void 0;var Vs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};zs.default=Vs;var Ws={};Object.defineProperty(Ws,"__esModule",{value:!0}),Ws.default=void 0;var $s={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};Ws.default=$s;var Gs={};Object.defineProperty(Gs,"__esModule",{value:!0}),Gs.default=void 0;var Qs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Gs.default=Qs;var Xs={};Object.defineProperty(Xs,"__esModule",{value:!0}),Xs.default=void 0;var Js={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Xs.default=Js,Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var Ks=Nd(Ou),Ys=Nd(_u),Zs=Nd(Au),ed=Nd(Su),td=Nd(Bu),rd=Nd(Fu),nd=Nd(Lu),ad=Nd(Hu),od=Nd(zu),ld=Nd(Wu),id=Nd(Gu),ud=Nd(Xu),sd=Nd(Ku),dd=Nd(Zu),cd=Nd(ts),pd=Nd(ns),md=Nd(os),fd=Nd(is),bd=Nd(ss),vd=Nd(cs),yd=Nd(ms),hd=Nd(bs),gd=Nd(ys),Cd=Nd(gs),qd=Nd(qs),Pd=Nd(Es),Ed=Nd(xs),wd=Nd(Os),xd=Nd(_s),Rd=Nd(As),Od=Nd(Ss),Td=Nd(Bs),_d=Nd(Fs),Md=Nd(Ls),Ad=Nd(Hs),jd=Nd(zs),Sd=Nd(Ws),Id=Nd(Gs),Bd=Nd(Xs);function Nd(e){return e&&e.__esModule?e:{default:e}}var Fd=[["doc-abstract",Ks.default],["doc-acknowledgments",Ys.default],["doc-afterword",Zs.default],["doc-appendix",ed.default],["doc-backlink",td.default],["doc-biblioentry",rd.default],["doc-bibliography",nd.default],["doc-biblioref",ad.default],["doc-chapter",od.default],["doc-colophon",ld.default],["doc-conclusion",id.default],["doc-cover",ud.default],["doc-credit",sd.default],["doc-credits",dd.default],["doc-dedication",cd.default],["doc-endnote",pd.default],["doc-endnotes",md.default],["doc-epigraph",fd.default],["doc-epilogue",bd.default],["doc-errata",vd.default],["doc-example",yd.default],["doc-footnote",hd.default],["doc-foreword",gd.default],["doc-glossary",Cd.default],["doc-glossref",qd.default],["doc-index",Pd.default],["doc-introduction",Ed.default],["doc-noteref",wd.default],["doc-notice",xd.default],["doc-pagebreak",Rd.default],["doc-pagelist",Od.default],["doc-part",Td.default],["doc-preface",_d.default],["doc-prologue",Md.default],["doc-pullquote",Ad.default],["doc-qna",jd.default],["doc-subtitle",Sd.default],["doc-tip",Id.default],["doc-toc",Bd.default]];Ru.default=Fd;var kd={},Ld={};Object.defineProperty(Ld,"__esModule",{value:!0}),Ld.default=void 0;var Ud={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Ld.default=Ud;var Hd={};Object.defineProperty(Hd,"__esModule",{value:!0}),Hd.default=void 0;var Dd={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};Hd.default=Dd;var zd={};Object.defineProperty(zd,"__esModule",{value:!0}),zd.default=void 0;var Vd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};zd.default=Vd,Object.defineProperty(kd,"__esModule",{value:!0}),kd.default=void 0;var Wd=Qd(Ld),$d=Qd(Hd),Gd=Qd(zd);function Qd(e){return e&&e.__esModule?e:{default:e}}var Xd=[["graphics-document",Wd.default],["graphics-object",$d.default],["graphics-symbol",Gd.default]];kd.default=Xd,Object.defineProperty(rn,"__esModule",{value:!0}),rn.default=void 0;var Jd=tc(nn),Kd=tc(zn),Yd=tc(Ru),Zd=tc(kd),ec=tc(Nr);function tc(e){return e&&e.__esModule?e:{default:e}}function rc(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nc(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=oc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}function ac(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||oc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oc(e,t){if(e){if("string"==typeof e)return lc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?lc(e,t):void 0}}function lc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ic=[].concat(Jd.default,Kd.default,Yd.default,Zd.default);ic.forEach((function(e){var t,r=ac(e,2)[1],n=nc(r.superClass);try{for(n.s();!(t=n.n()).done;){var a,o=nc(t.value);try{var l=function(){var e=a.value,t=ic.find((function(t){return ac(t,1)[0]===e}));if(t)for(var n=t[1],o=0,l=Object.keys(n.props);o<l.length;o++){var i=l[o];Object.prototype.hasOwnProperty.call(r.props,i)||Object.assign(r.props,rc({},i,n.props[i]))}};for(o.s();!(a=o.n()).done;)l()}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}));var uc={entries:function(){return ic},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=nc(ic);try{for(n.s();!(t=n.n()).done;){var a=ac(t.value,2),o=a[0],l=a[1];e.call(r,l,o,ic)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=ic.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!uc.get(e)},keys:function(){return ic.map((function(e){return ac(e,1)[0]}))},values:function(){return ic.map((function(e){return ac(e,2)[1]}))}},sc=(0,ec.default)(uc,uc.entries());rn.default=sc;var dc={},cc={},pc=Object.prototype.hasOwnProperty;cc.dequal=function e(t,r){var n,a;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((a=t.length)===r.length)for(;a--&&e(t[a],r[a]););return-1===a}if(!n||"object"==typeof t){for(n in a=0,t){if(pc.call(t,n)&&++a&&!pc.call(r,n))return!1;if(!(n in r)||!e(t[n],r[n]))return!1}return Object.keys(r).length===a}}return t!=t&&r!=r},Object.defineProperty(dc,"__esModule",{value:!0}),dc.default=void 0;var mc=cc,fc=vc(Nr),bc=vc(rn);function vc(e){return e&&e.__esModule?e:{default:e}}function yc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||hc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hc(e,t){if(e){if("string"==typeof e)return gc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gc(e,t):void 0}}function gc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Cc=[],qc=bc.default.keys(),Pc=0;Pc<qc.length;Pc++){var Ec=qc[Pc],wc=bc.default.get(Ec);if(wc)for(var xc=[].concat(wc.baseConcepts,wc.relatedConcepts),Rc=0;Rc<xc.length;Rc++){var Oc=xc[Rc];"HTML"===Oc.module&&function(){var e=Oc.concept;if(e){var t,r=Cc.find((function(t){return(0,mc.dequal)(t,e)}));t=r?r[1]:[];for(var n=!0,a=0;a<t.length;a++)if(t[a]===Ec){n=!1;break}n&&t.push(Ec),Cc.push([e,t])}}()}}var Tc={entries:function(){return Cc},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=hc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(Cc);try{for(n.s();!(t=n.n()).done;){var a=yc(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Cc)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Cc.find((function(t){return e.name===t[0].name&&(0,mc.dequal)(e.attributes,t[0].attributes)}));return t&&t[1]},has:function(e){return!!Tc.get(e)},keys:function(){return Cc.map((function(e){return yc(e,1)[0]}))},values:function(){return Cc.map((function(e){return yc(e,2)[1]}))}},_c=(0,fc.default)(Tc,Tc.entries());dc.default=_c;var Mc={};Object.defineProperty(Mc,"__esModule",{value:!0}),Mc.default=void 0;var Ac=Sc(Nr),jc=Sc(rn);function Sc(e){return e&&e.__esModule?e:{default:e}}function Ic(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||Bc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bc(e,t){if(e){if("string"==typeof e)return Nc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Nc(e,t):void 0}}function Nc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Fc=[],kc=jc.default.keys(),Lc=0;Lc<kc.length;Lc++){var Uc=kc[Lc],Hc=jc.default.get(Uc),Dc=[];if(Hc){for(var zc=[].concat(Hc.baseConcepts,Hc.relatedConcepts),Vc=0;Vc<zc.length;Vc++){var Wc=zc[Vc];if("HTML"===Wc.module){var $c=Wc.concept;null!=$c&&Dc.push($c)}}Dc.length>0&&Fc.push([Uc,Dc])}}var Gc={entries:function(){return Fc},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Bc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(Fc);try{for(n.s();!(t=n.n()).done;){var a=Ic(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Fc)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Fc.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Gc.get(e)},keys:function(){return Fc.map((function(e){return Ic(e,1)[0]}))},values:function(){return Fc.map((function(e){return Ic(e,2)[1]}))}},Qc=(0,Ac.default)(Gc,Gc.entries());Mc.default=Qc,Object.defineProperty(Ir,"__esModule",{value:!0});var Xc=Ir.roles=up=Ir.roleElements=Ir.elementRoles=Ir.dom=Ir.aria=void 0,Jc=tp(Br),Kc=tp(Qr),Yc=tp(rn),Zc=tp(dc),ep=tp(Mc);function tp(e){return e&&e.__esModule?e:{default:e}}var rp=Jc.default;Ir.aria=rp;var np=Kc.default;Ir.dom=np;var ap=Yc.default;Xc=Ir.roles=ap;var op=Zc.default,lp=Ir.elementRoles=op,ip=ep.default,up=Ir.roleElements=ip;const sp=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;const a=-1!==n.indexOf("undefined"),o=-1!==n.indexOf("set");return void 0!==r?"["+t+'="'+r+'"]':a?":not(["+t+"])":o?"["+t+"]:not(["+t+'=""])':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[a,o]of e.entries())n=[...n,{match:r(a),roles:Array.from(o),specificity:t(a)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(lp);function dp(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function cp(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=dp}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function pp(e){for(const{match:t,roles:r}of sp)if(t(e))return[...r];return[]}function mp(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===cp(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):pp(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function fp(e,t){let{hidden:r,includeDescription:n}=t;const a=mp(e,{hidden:r});return Object.entries(a).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const a="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+Sr(e,{computedStyleSupportsPseudoElements:kt().computedStyleSupportsPseudoElements})+'":\n',r=Bt(e.cloneNode(!1));if(n){return""+t+('Description "'+jr(e,{computedStyleSupportsPseudoElements:kt().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+a})).join("\n")}function bp(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const vp=Gt();function yp(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function hp(e,t,r,n){let{variant:a,name:o}=n,l="";const i={},u=[["Role","TestId"].includes(e)?r:yp(r)];o&&(i.name=yp(o)),"Role"===e&&cp(t)&&(i.hidden=!0,l="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(i).length>0&&u.push(i);const s=a+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:a,warning:l,toString(){l&&console.warn(l);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function gp(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function Cp(e,t,r){var n,a;if(void 0===t&&(t="get"),e.matches(kt().defaultIgnore))return;const o=null!=(n=e.getAttribute("role"))?n:null==(a=pp(e))?void 0:a[0];if("generic"!==o&&gp("Role",r,o))return hp("Role",e,o,{variant:t,name:Sr(e,{computedStyleSupportsPseudoElements:kt().computedStyleSupportsPseudoElements})});const l=zt(document,e).map((e=>e.content)).join(" ");if(gp("LabelText",r,l))return hp("LabelText",e,l,{variant:t});const i=e.getAttribute("placeholder");if(gp("PlaceholderText",r,i))return hp("PlaceholderText",e,i,{variant:t});const u=vp(Jt(e));if(gp("Text",r,u))return hp("Text",e,u,{variant:t});if(gp("DisplayValue",r,e.value))return hp("DisplayValue",e,vp(e.value),{variant:t});const s=e.getAttribute("alt");if(gp("AltText",r,s))return hp("AltText",e,s,{variant:t});const d=e.getAttribute("title");if(gp("Title",r,d))return hp("Title",e,d,{variant:t});const c=e.getAttribute(kt().testIdAttribute);return gp("TestId",r,c)?hp("TestId",e,c,{variant:t}):void 0}function qp(e,t){e.stack=t.stack.replace(t.message,e.message)}function Pp(e,t){let{container:r=Ot(),timeout:n=kt().asyncUtilTimeout,showOriginalStackTrace:a=kt().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:i=e=>(Object.defineProperty(e,"message",{value:kt().getElementError(e.message,r).message}),e),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let d,c,p,m=!1,f="idle";const b=setTimeout((function(){let e;d?(e=d,a||"TestingLibraryElementError"!==e.name||qp(e,o)):(e=new Error("Timed out in waitFor."),a||qp(e,o)),y(i(e),null)}),n),v=Rt();if(v){const{unstable_advanceTimersWrapper:e}=kt();for(g();!m;){if(!Rt()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||qp(e,o),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(l)})),m)break;g()}}else{try{_t(r)}catch(e){return void s(e)}c=setInterval(h,l);const{MutationObserver:e}=Tt(r);p=new e(h),p.observe(r,u),g()}function y(e,r){m=!0,clearTimeout(b),v||(clearInterval(c),p.disconnect()),e?s(e):t(r)}function h(){if(Rt()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||qp(e,o),s(e)}return g()}function g(){if("pending"!==f)try{const t=function(e){try{return Ft._disableExpensiveErrorDiagnostics=!0,e()}finally{Ft._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(f="pending",t.then((e=>{f="resolved",y(null,e)}),(e=>{f="rejected",d=e}))):y(null,t)}catch(e){d=e}}}))}function Ep(e,t){const r=new Error("STACK_TRACE_MESSAGE");return kt().asyncWrapper((()=>Pp(e,{stackTraceError:r,...t})))}function wp(e,t){return kt().getElementError(e,t)}function xp(e,t){return wp(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Rp(e,t,r,n){let{exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===n?{}:n;const u=a?$t:Wt,s=Qt({collapseWhitespace:o,trim:l,normalizer:i});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function Op(e,t,r,n){const a=Rp(e,t,r,n);if(a.length>1)throw xp("Found multiple elements by ["+e+"="+r+"]",t);return a[0]||null}function Tp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(l.length>1){const e=l.map((e=>wp(null,e).message)).join("\n\n");throw xp(t(r,...a)+"\n\nHere are the matching elements:\n\n"+e,r)}return l[0]||null}}function _p(e,t){return kt().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function Mp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(!l.length)throw kt().getElementError(t(r,...a),r);return l}}function Ap(e){return(t,r,n,a)=>Ep((()=>e(t,r,n)),{container:t,...a})}const jp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=kt().throwSuggestions}={}]=o.slice(-1);if(i&&u){const e=Cp(i,r);if(e&&!t.endsWith(e.queryName))throw _p(e.toString(),n)}return i},Sp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=kt().throwSuggestions}={}]=o.slice(-1);if(i.length&&u){const e=[...new Set(i.map((e=>{var t;return null==(t=Cp(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(Cp(i[0],r).queryName))throw _p(e[0],n)}return i};function Ip(e,t,r){const n=jp(Tp(e,t),e.name,"query"),a=Mp(e,r),o=Tp(a,t),l=jp(o,e.name,"get");return[n,Sp(a,e.name.replace("query","get"),"getAll"),l,Ap(Sp(a,e.name,"findAll")),Ap(jp(o,e.name,"find"))]}var Bp=Object.freeze({__proto__:null,getElementError:wp,wrapAllByQueryWithSuggestion:Sp,wrapSingleQueryWithSuggestion:jp,getMultipleElementsFoundError:xp,queryAllByAttribute:Rp,queryByAttribute:Op,makeSingleQuery:Tp,makeGetAllQuery:Mp,makeFindQuery:Ap,buildQueries:Ip});const Np=function(e,t,r){let{exact:n=!0,trim:a,collapseWhitespace:o,normalizer:l}=void 0===r?{}:r;const i=n?$t:Wt,u=Qt({collapseWhitespace:o,trim:a,normalizer:l}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Ht(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return i(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},Fp=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===r?{}:r;_t(e);const u=a?$t:Wt,s=Qt({collapseWhitespace:o,trim:l,normalizer:i}),d=Array.from(e.querySelectorAll("*")).filter((e=>Dt(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,a)=>{const o=zt(e,a,{selector:n});o.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const l=o.filter((e=>Boolean(e.content))).map((e=>e.content));return u(l.join(" "),a,t,s)&&r.push(a),l.length>1&&l.forEach(((e,n)=>{u(e,a,t,s)&&r.push(a);const o=[...l];o.splice(n,1),o.length>1&&u(o.join(" "),a,t,s)&&r.push(a)})),r}),[]).concat(Rp("aria-label",e,t,{exact:a,normalizer:s}));return Array.from(new Set(d)).filter((e=>e.matches(n)))},kp=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];const o=Fp(e,t,...n);if(!o.length){const r=Np(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?kt().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):kt().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw kt().getElementError("Unable to find a label with the text of: "+t,e)}return o};const Lp=(e,t)=>"Found multiple elements with the text of: "+t,Up=jp(Tp(Fp,Lp),Fp.name,"query"),Hp=Tp(kp,Lp),Dp=Ap(Sp(kp,kp.name,"findAll")),zp=Ap(jp(Hp,kp.name,"find")),Vp=Sp(kp,kp.name,"getAll"),Wp=jp(Hp,kp.name,"get"),$p=Sp(Fp,Fp.name,"queryAll"),Gp=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return _t(t[0]),Rp("placeholder",...t)},Qp=Sp(Gp,Gp.name,"queryAll"),[Xp,Jp,Kp,Yp,Zp]=Ip(Gp,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),em=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,ignore:i=kt().defaultIgnore,normalizer:u}=void 0===r?{}:r;_t(e);const s=a?$t:Wt,d=Qt({collapseWhitespace:o,trim:l,normalizer:u});let c=[];return"function"==typeof e.matches&&e.matches(n)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(n))].filter((e=>!i||!e.matches(i))).filter((e=>s(Jt(e),e,t,d)))},tm=Sp(em,em.name,"queryAll"),[rm,nm,am,om,lm]=Ip(em,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:a,normalizer:o,selector:l}=r,i=Qt({collapseWhitespace:n,trim:a,normalizer:o})(t.toString());return"Unable to find an element with the text: "+(i!==t.toString()?i+" (normalized from '"+t+"')":t)+("*"!==(null!=l?l:"*")?", which matches selector '"+l+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),im=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;_t(e);const i=n?$t:Wt,u=Qt({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>i(Jt(e),e,t,u)))}return i(e.value,e,t,u)}))},um=Sp(im,im.name,"queryAll"),[sm,dm,cm,pm,mm]=Ip(im,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),fm=/^(img|input|area|.+-.+)$/i,bm=function(e,t,r){return void 0===r&&(r={}),_t(e),Rp("alt",e,t,r).filter((e=>fm.test(e.tagName)))},vm=Sp(bm,bm.name,"queryAll"),[ym,hm,gm,Cm,qm]=Ip(bm,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),Pm=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;_t(e);const i=n?$t:Wt,u=Qt({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>i(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&i(Jt(e),e,t,u)))},Em=Sp(Pm,Pm.name,"queryAll"),[wm,xm,Rm,Om,Tm]=Ip(Pm,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),_m=function(e,t,r){let{hidden:n=kt().defaultHidden,name:a,description:o,queryFallbacks:l=!1,selected:i,busy:u,checked:s,pressed:d,current:c,level:p,expanded:m,value:{now:f,min:b,max:v,text:y}={}}=void 0===r?{}:r;var h,g,C,q,P,E,w,x,R,O;if((_t(e),void 0!==i)&&void 0===(null==(h=Xc.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=Xc.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(C=Xc.get(t))?void 0:C.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(q=Xc.get(t))?void 0:q.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(P=Xc.get(t))?void 0:P.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==f&&void 0===(null==(E=Xc.get(t))?void 0:E.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(w=Xc.get(t))?void 0:w.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==b&&void 0===(null==(x=Xc.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(R=Xc.get(t))?void 0:R.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(O=Xc.get(t))?void 0:O.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const T=new WeakMap;function _(e){return T.has(e)||T.set(e,dp(e)),T.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r=null!=(t=up.get(e))?t:new Set,n=new Set(Array.from(r).map((e=>{let{name:t}=e;return t})));return['*[role~="'+e+'"]'].concat(Array.from(n)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(l)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return pp(e).some((e=>e===t))})).filter((e=>{if(void 0!==i)return i===function(e){return"OPTION"===e.tagName?e.selected:bp(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:bp(e,"aria-checked")}(e);if(void 0!==d)return d===function(e){return bp(e,"aria-pressed")}(e);if(void 0!==c)return c===function(e){var t,r;return null!=(t=null!=(r=bp(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==m)return m===function(e){return bp(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==f||void 0!==v||void 0!==b||void 0!==y){let r=!0;var t;if(void 0!==f&&r&&(r=f===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==v&&r&&(r=v===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==y)r&&(r=$t(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,y,(e=>e)));return r}return!0})).filter((e=>void 0===a||$t(Sr(e,{computedStyleSupportsPseudoElements:kt().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>void 0===o||$t(jr(e,{computedStyleSupportsPseudoElements:kt().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>!1!==n||!1===cp(e,{isSubtreeInaccessible:_})))};const Mm=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},Am=Sp(_m,_m.name,"queryAll"),[jm,Sm,Im,Bm,Nm]=Ip(_m,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+Mm(n)}),(function(e,t,r){let{hidden:n=kt().defaultHidden,name:a,description:o}=void 0===r?{}:r;if(kt()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+Mm(a);let l,i="";Array.from(e.children).forEach((e=>{i+=fp(e,{hidden:n,includeDescription:void 0!==o})})),l=0===i.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+i.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===a?"":"string"==typeof a?' and name "'+a+'"':" and name `"+a+"`";let s="";return s=void 0===o?"":"string"==typeof o?' and description "'+o+'"':" and description `"+o+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+l).trim()})),Fm=()=>kt().testIdAttribute,km=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return _t(t[0]),Rp(Fm(),...t)},Lm=Sp(km,km.name,"queryAll"),[Um,Hm,Dm,zm,Vm]=Ip(km,((e,t)=>"Found multiple elements by: ["+Fm()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+Fm()+'="'+t+'"]'));var Wm=Object.freeze({__proto__:null,queryAllByLabelText:$p,queryByLabelText:Up,getAllByLabelText:Vp,getByLabelText:Wp,findAllByLabelText:Dp,findByLabelText:zp,queryByPlaceholderText:Xp,queryAllByPlaceholderText:Qp,getByPlaceholderText:Kp,getAllByPlaceholderText:Jp,findAllByPlaceholderText:Yp,findByPlaceholderText:Zp,queryByText:rm,queryAllByText:tm,getByText:am,getAllByText:nm,findAllByText:om,findByText:lm,queryByDisplayValue:sm,queryAllByDisplayValue:um,getByDisplayValue:cm,getAllByDisplayValue:dm,findAllByDisplayValue:pm,findByDisplayValue:mm,queryByAltText:ym,queryAllByAltText:vm,getByAltText:gm,getAllByAltText:hm,findAllByAltText:Cm,findByAltText:qm,queryByTitle:wm,queryAllByTitle:Em,getByTitle:Rm,getAllByTitle:xm,findAllByTitle:Om,findByTitle:Tm,queryByRole:jm,queryAllByRole:Am,getAllByRole:Sm,getByRole:Im,findAllByRole:Bm,findByRole:Nm,queryByTestId:Um,queryAllByTestId:Lm,getByTestId:Dm,getAllByTestId:Hm,findAllByTestId:zm,findByTestId:Vm});function $m(e,t,r){return void 0===t&&(t=Wm),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const a=t[n];return r[n]=a.bind(null,e),r}),r)}const Gm=e=>!e||Array.isArray(e)&&!e.length;function Qm(e){if(Gm(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const Xm={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},pageHide:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},pageShow:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}}},Jm={doubleClick:"dblClick"};function Km(e,t){return kt().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function Ym(e,t,r,n){let{EventType:a="Event",defaultInit:o={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...r},{target:{value:i,files:u,...s}={}}=l;void 0!==i&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:a}=Object.getOwnPropertyDescriptor(n,"value")||{};if(a&&r!==a)a.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,i),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const d=Tt(t),c=d[a]||d.Event;let p;if("function"==typeof c)p=new c(e,l);else{p=d.document.createEvent(a);const{bubbles:t,cancelable:r,detail:n,...o}=l;p.initEvent(e,t,r,n),Object.keys(o).forEach((e=>{p[e]=o[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=l[e];"object"==typeof t&&("function"==typeof d.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new d.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}Object.keys(Xm).forEach((e=>{const{EventType:t,defaultInit:r}=Xm[e],n=e.toLowerCase();Ym[e]=(e,a)=>Ym(n,e,a,{EventType:t,defaultInit:r}),Km[e]=(t,r)=>Km(t,Ym[e](t,r))})),Object.keys(Jm).forEach((e=>{const t=Jm[e];Km[e]=function(){return Km[t](...arguments)}}));var Zm={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function a(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return a(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,a=t.length;n<a;n++){var l=t.charCodeAt(n);r[2*n]=l>>>8,r[2*n+1]=l%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,a=r.length;n<a;n++)r[n]=256*t[2*n]+t[2*n+1];var l=[];return r.forEach((function(t){l.push(e(t))})),o.decompress(l.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return a(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,a,o,l={},i={},u="",s="",d="",c=2,p=3,m=2,f=[],b=0,v=0;for(o=0;o<e.length;o+=1)if(u=e.charAt(o),Object.prototype.hasOwnProperty.call(l,u)||(l[u]=p++,i[u]=!0),s=d+u,Object.prototype.hasOwnProperty.call(l,s))d=s;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++),l[s]=p++,d=String(u)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++)}for(a=2,n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;for(;;){if(b<<=1,v==t-1){f.push(r(b));break}v++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var a,o,l,i,u,s,d,c=[],p=4,m=4,f=3,b="",v=[],y={val:n(0),position:r,index:1};for(a=0;a<3;a+=1)c[a]=a;for(l=0,u=Math.pow(2,2),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 2:return""}for(c[3]=d,o=d,v.push(d);;){if(y.index>t)return"";for(l=0,u=Math.pow(2,f),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(d=l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[d])b=c[d];else{if(d!==m)return null;b=o+o.charAt(0)}v.push(b),c[m++]=o+b.charAt(0),o=b,0==--p&&(p=Math.pow(2,f),f++)}}};return o}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(Zm);var ef=Zm.exports;function tf(e){return"https://testing-playground.com/#markup="+(t=e,ef.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}const rf={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>Nt(e,t,r))):Nt(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Ot().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=tf(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},nf="undefined"!=typeof document&&document.body?$m(document.body,Wm,rf):Object.keys(Wm).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),rf);e.buildQueries=Ip,e.configure=function(e){"function"==typeof e&&(e=e(Ft)),Ft={...Ft,...e}},e.createEvent=Ym,e.findAllByAltText=Cm,e.findAllByDisplayValue=pm,e.findAllByLabelText=Dp,e.findAllByPlaceholderText=Yp,e.findAllByRole=Bm,e.findAllByTestId=zm,e.findAllByText=om,e.findAllByTitle=Om,e.findByAltText=qm,e.findByDisplayValue=mm,e.findByLabelText=zp,e.findByPlaceholderText=Zp,e.findByRole=Nm,e.findByTestId=Vm,e.findByText=lm,e.findByTitle=Tm,e.fireEvent=Km,e.getAllByAltText=hm,e.getAllByDisplayValue=dm,e.getAllByLabelText=Vp,e.getAllByPlaceholderText=Jp,e.getAllByRole=Sm,e.getAllByTestId=Hm,e.getAllByText=nm,e.getAllByTitle=xm,e.getByAltText=gm,e.getByDisplayValue=cm,e.getByLabelText=Wp,e.getByPlaceholderText=Kp,e.getByRole=Im,e.getByTestId=Dm,e.getByText=am,e.getByTitle=Rm,e.getConfig=kt,e.getDefaultNormalizer=Gt,e.getElementError=wp,e.getMultipleElementsFoundError=xp,e.getNodeText=Jt,e.getQueriesForElement=$m,e.getRoles=mp,e.getSuggestedQuery=Cp,e.isInaccessible=cp,e.logDOM=Nt,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(fp(e,{hidden:r}))},e.makeFindQuery=Ap,e.makeGetAllQuery=Mp,e.makeSingleQuery=Tp,e.prettyDOM=Bt,e.prettyFormat=ot,e.queries=Wm,e.queryAllByAltText=vm,e.queryAllByAttribute=Rp,e.queryAllByDisplayValue=um,e.queryAllByLabelText=$p,e.queryAllByPlaceholderText=Qp,e.queryAllByRole=Am,e.queryAllByTestId=Lm,e.queryAllByText=tm,e.queryAllByTitle=Em,e.queryByAltText=ym,e.queryByAttribute=Op,e.queryByDisplayValue=sm,e.queryByLabelText=Up,e.queryByPlaceholderText=Xp,e.queryByRole=jm,e.queryByTestId=Um,e.queryByText=rm,e.queryByTitle=wm,e.queryHelpers=Bp,e.screen=nf,e.waitFor=Ep,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){Qm(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return Qm(e()),Ep((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!Gm(t))throw r}),t)},e.within=$m,e.wrapAllByQueryWithSuggestion=Sp,e.wrapSingleQueryWithSuggestion=jp,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=dom.umd.min.js.map
