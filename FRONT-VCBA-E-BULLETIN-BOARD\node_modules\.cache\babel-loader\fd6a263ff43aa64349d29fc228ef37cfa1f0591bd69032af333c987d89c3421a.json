{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"7\",\n  key: \"p2h5vp\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"15\",\n  r: \"7\",\n  key: \"19ennj\"\n}]];\nconst Blend = createLucideIcon(\"blend\", __iconNode);\nexport { __iconNode, Blend as default };\n//# sourceMappingURL=blend.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}