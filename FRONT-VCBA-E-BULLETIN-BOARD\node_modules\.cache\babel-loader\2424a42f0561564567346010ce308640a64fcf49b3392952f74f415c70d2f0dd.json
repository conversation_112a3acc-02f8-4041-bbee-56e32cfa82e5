{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12H3\",\n  key: \"1a2rj7\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M21 12h.01\",\n  key: \"msek7k\"\n}], [\"path\", {\n  d: \"M21 18h.01\",\n  key: \"1e8rq1\"\n}], [\"path\", {\n  d: \"M21 6h.01\",\n  key: \"1koanj\"\n}]];\nconst TableOfContents = createLucideIcon(\"table-of-contents\", __iconNode);\nexport { __iconNode, TableOfContents as default };\n//# sourceMappingURL=table-of-contents.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}