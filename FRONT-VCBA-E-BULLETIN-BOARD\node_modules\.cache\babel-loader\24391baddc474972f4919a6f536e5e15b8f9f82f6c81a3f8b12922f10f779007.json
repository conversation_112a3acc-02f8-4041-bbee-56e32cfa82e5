{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.188 8.5A6 6 0 0 1 16 4a1 1 0 0 0 6 6 6 6 0 0 1-3 5.197\",\n  key: \"erj67n\"\n}], [\"path\", {\n  d: \"M13 16a3 3 0 1 1 0 6H7a5 5 0 1 1 4.9-6Z\",\n  key: \"p44pc9\"\n}]];\nconst CloudMoon = createLucideIcon(\"cloud-moon\", __iconNode);\nexport { __iconNode, CloudMoon as default };\n//# sourceMappingURL=cloud-moon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}